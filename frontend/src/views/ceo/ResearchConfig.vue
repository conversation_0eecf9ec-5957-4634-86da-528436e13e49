<template>
  <div class="max-w-4xl mx-auto p-6">
    <PageHeader
      title="Configurazione Ricerca"
      subtitle="Configura gli argomenti di ricerca AI per il tuo settore"
    />
    
    <!-- Company Profile Section -->
    <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Profilo Aziendale</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Settore</label>
          <select 
            v-model="selectedIndustry"
            @change="loadResearchTopics"
            class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="Software/Technology">Software/Technology</option>
            <option value="Manufacturing">Manufacturing</option>
            <option value="Services">Services</option>
            <option value="Consulting">Consulting</option>
            <option value="Technical Documentation">Technical Documentation</option>
            <option value="Sports Association">Sports Association</option>
            <option value="Cultural Association">Cultural Association</option>
          </select>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Dimensione Aziendale</label>
          <select 
            v-model="companyProfile.size"
            class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="startup">Startup (1-10)</option>
            <option value="small">Piccola (11-50)</option>
            <option value="medium">Media (51-250)</option>
            <option value="large">Grande (250+)</option>
          </select>
        </div>
        
        <div class="md:col-span-2">
          <label class="block text-sm font-medium text-gray-700 mb-2">Mission Aziendale</label>
          <textarea
            v-model="companyProfile.mission"
            rows="3"
            class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Descrivi la mission e gli obiettivi della tua azienda..."
          ></textarea>
        </div>
        
        <div class="md:col-span-2">
          <label class="block text-sm font-medium text-gray-700 mb-2">Vision Aziendale</label>
          <textarea
            v-model="companyProfile.vision"
            rows="2"
            class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Descrivi la vision della tua azienda..."
          ></textarea>
        </div>
      </div>
      
      <div class="mt-4">
        <button 
          @click="saveProfile"
          class="btn-primary"
          :disabled="saving"
        >
          {{ saving ? 'Salvataggio...' : 'Salva Profilo' }}
        </button>
      </div>
    </div>
    
    <!-- Research Topics Configuration -->
    <div class="bg-white rounded-lg shadow-sm p-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-900">Argomenti di Ricerca Disponibili</h3>
        <span class="text-sm text-gray-500">Settore: {{ selectedIndustry }}</span>
      </div>
      
      <div v-if="loading" class="text-center py-8">
        <HeroIcon name="arrow-path" size="lg" class="mx-auto text-gray-400 animate-spin mb-3" />
        <p class="text-gray-500">Caricamento configurazione ricerca...</p>
      </div>
      
      <div v-else-if="researchTopics.length > 0" class="space-y-4">
        <div
          v-for="topic in researchTopics"
          :key="topic.id"
          class="border border-gray-200 rounded-lg p-4"
        >
          <div class="flex items-center justify-between mb-3">
            <h4 class="font-medium text-gray-900">{{ topic.title }}</h4>
            <div class="flex items-center gap-2">
              <span 
                class="px-2 py-1 text-xs rounded-full"
                :class="getPriorityClass(topic.priority)"
              >
                {{ topic.priority.toUpperCase() }}
              </span>
              <button
                @click="toggleTopic(topic)"
                class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                :class="topic.enabled ? 'bg-blue-600' : 'bg-gray-200'"
              >
                <span
                  class="pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"
                  :class="topic.enabled ? 'translate-x-5' : 'translate-x-0'"
                ></span>
              </button>
            </div>
          </div>
          <p class="text-sm text-gray-600 mb-3">
            {{ topic.description }}
          </p>
          <div class="text-xs text-gray-500">
            <strong>Query di esempio:</strong>
            <div class="mt-1 flex flex-wrap gap-1">
              <span
                v-for="query in topic.sample_queries.slice(0, 3)"
                :key="query"
                class="inline-block bg-gray-100 rounded px-2 py-1"
              >
                {{ query }}
              </span>
              <span
                v-if="topic.sample_queries.length > 3"
                class="inline-block text-gray-400"
              >
                +{{ topic.sample_queries.length - 3 }} altri
              </span>
            </div>
          </div>
        </div>
      </div>
      
      <div v-else class="text-center py-8 text-gray-500">
        <HeroIcon name="exclamation-triangle" size="lg" class="mx-auto mb-3" />
        <p>Nessun argomento di ricerca disponibile per questo settore.</p>
      </div>
      
      <div class="mt-6 p-4 bg-blue-50 rounded-lg">
        <div class="flex items-start gap-3">
          <HeroIcon name="information-circle" size="sm" class="text-blue-600 mt-0.5" />
          <div>
            <h4 class="font-medium text-blue-900">Configurazione Argomenti di Ricerca</h4>
            <p class="text-sm text-blue-700 mt-1">
              Gli argomenti di ricerca sono configurati automaticamente in base alla selezione del settore. 
              Attiva/disattiva gli argomenti per personalizzare le capacità del tuo assistente AI.
            </p>
          </div>
        </div>
      </div>
      
      <div class="mt-4 flex justify-end">
        <button 
          @click="saveResearchConfig"
          class="btn-secondary"
          :disabled="saving"
        >
          {{ saving ? 'Salvataggio...' : 'Salva Configurazione Ricerca' }}
        </button>
      </div>
    </div>
    
    <!-- AI Settings -->
    <div class="bg-white rounded-lg shadow-sm p-6 mt-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Impostazioni Assistente AI</h3>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Stile di Risposta</label>
          <select 
            v-model="aiSettings.response_style"
            class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="executive">Esecutivo (conciso, strategico)</option>
            <option value="detailed">Dettagliato (analisi approfondita)</option>
            <option value="conversational">Colloquiale (amichevole, informale)</option>
          </select>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Livello di Dettaglio</label>
          <select 
            v-model="aiSettings.detail_level"
            class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="strategic">Panoramica Strategica</option>
            <option value="tactical">Dettagli Tattici</option>
            <option value="operational">Focus Operativo</option>
          </select>
        </div>
        
        <div class="md:col-span-2">
          <label class="flex items-center">
            <input
              v-model="aiSettings.include_action_items"
              type="checkbox"
              class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
            />
            <span class="ml-2 text-sm text-gray-700">Includi elementi d'azione nelle risposte</span>
          </label>
        </div>
      </div>
      
      <div class="mt-4">
        <button 
          @click="saveAISettings"
          class="btn-secondary"
          :disabled="saving"
        >
          {{ saving ? 'Salvataggio...' : 'Salva Impostazioni AI' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import PageHeader from '@/components/design-system/PageHeader.vue'
import HeroIcon from '@/components/icons/HeroIcon.vue'
import { useToast } from '@/composables/useToast'
import api from '@/utils/api'

export default {
  name: 'ResearchConfig',
  components: {
    PageHeader,
    HeroIcon
  },
  setup() {
    const { showToast } = useToast()
    
    // Reactive data
    const loading = ref(false)
    const saving = ref(false)
    const selectedIndustry = ref('Software/Technology')
    const researchConfig = ref(null)
    const researchTopics = ref([])
    
    const companyProfile = ref({
      name: '',
      industry: 'Software/Technology',
      size: 'small',
      mission: '',
      vision: ''
    })
    
    const aiSettings = ref({
      response_style: 'executive',
      detail_level: 'strategic',
      include_action_items: true,
      confidence_threshold: 0.7,
      max_response_length: 1500
    })
    
    // Load configuration files
    const loadConfiguration = async () => {
      loading.value = true
      try {
        const response = await api.get('/api/ceo/config')
        if (response.data.success) {
          const { company, research_config } = response.data.data
          
          // Set company profile from API response
          companyProfile.value = {
            name: company.name || '',
            industry: company.industry || 'Software/Technology',
            size: 'small', // Default, can be configured
            mission: company.mission || '',
            vision: company.vision || ''
          }
          
          selectedIndustry.value = companyProfile.value.industry
          
          // Set AI settings from config or user preferences
          if (research_config.ai_settings) {
            aiSettings.value = { ...aiSettings.value, ...research_config.ai_settings }
          }
          
          // Override with user-specific preferences if available
          const userPrefs = response.data.data.user_preferences
          if (userPrefs) {
            if (userPrefs.ai_settings) {
              aiSettings.value = { ...aiSettings.value, ...userPrefs.ai_settings }
            }
            if (userPrefs.industry) {
              selectedIndustry.value = userPrefs.industry
            }
          }
          
          researchConfig.value = research_config
          await loadResearchTopics()
          
          // Apply user-specific topic preferences
          if (userPrefs && userPrefs.topics) {
            userPrefs.topics.forEach(userTopic => {
              const currentTopic = researchTopics.value.find(t => t.id === userTopic.id)
              if (currentTopic) {
                currentTopic.enabled = userTopic.enabled
              }
            })
          }
        } else {
          throw new Error('Failed to load config')
        }
        
      } catch (error) {
        console.error('Error loading configuration:', error)
        showToast('Error loading configuration', 'error')
        
        // Use defaults
        researchConfig.value = {
          research_categories: {},
          ai_settings: aiSettings.value
        }
      } finally {
        loading.value = false
      }
    }
    
    // Load research topics for selected industry
    const loadResearchTopics = () => {
      if (!researchConfig.value?.research_categories) {
        researchTopics.value = []
        return
      }
      
      const industryData = researchConfig.value.research_categories[selectedIndustry.value]
      if (industryData && industryData.topics) {
        researchTopics.value = [...industryData.topics]
      } else {
        researchTopics.value = []
      }
    }
    
    // Toggle topic enabled state
    const toggleTopic = (topic) => {
      topic.enabled = !topic.enabled
      showToast(`${topic.title} ${topic.enabled ? 'abilitato' : 'disabilitato'}`, 'success')
    }
    
    // Get priority class for styling
    const getPriorityClass = (priority) => {
      const classes = {
        high: 'bg-red-100 text-red-800',
        medium: 'bg-yellow-100 text-yellow-800',
        low: 'bg-gray-100 text-gray-800'
      }
      return classes[priority] || classes.medium
    }
    
    // Save company profile
    const saveProfile = async () => {
      saving.value = true
      try {
        // In a real app, this would save to the backend
        // For now, we'll just update localStorage and show success
        const profileData = {
          ...companyProfile.value,
          industry: selectedIndustry.value,
          updated_at: new Date().toISOString()
        }
        
        localStorage.setItem('ceo-company-profile', JSON.stringify(profileData))
        
        // Reload research topics if industry changed
        await loadResearchTopics()
        
        showToast('Profilo aziendale salvato con successo', 'success')
      } catch (error) {
        console.error('Error saving profile:', error)
        showToast('Errore nel salvataggio del profilo', 'error')
      } finally {
        saving.value = false
      }
    }
    
    // Save research configuration
    const saveResearchConfig = async () => {
      saving.value = true
      try {
        const configData = {
          research_preferences: {
            industry: selectedIndustry.value,
            topics: researchTopics.value,
            updated_at: new Date().toISOString()
          }
        }
        
        const response = await api.post('/api/ceo/config', configData)
        
        if (response.data.success) {
          showToast('Configurazione ricerca salvata con successo', 'success')
          // Also save to localStorage as backup
          localStorage.setItem('ceo-research-config', JSON.stringify(configData.research_preferences))
        } else {
          throw new Error('Server response unsuccessful')
        }
      } catch (error) {
        console.error('Error saving research config:', error)
        showToast('Errore nel salvataggio della configurazione ricerca', 'error')
        
        // Fallback to localStorage if server fails
        try {
          const fallbackData = {
            industry: selectedIndustry.value,
            topics: researchTopics.value,
            updated_at: new Date().toISOString()
          }
          localStorage.setItem('ceo-research-config', JSON.stringify(fallbackData))
          showToast('Configurazione salvata localmente come fallback', 'warning')
        } catch (fallbackError) {
          console.error('Fallback save failed:', fallbackError)
        }
      } finally {
        saving.value = false
      }
    }
    
    // Save AI settings
    const saveAISettings = async () => {
      saving.value = true
      try {
        const response = await api.post('/api/ceo/config', {
          ai_settings: aiSettings.value
        })
        
        if (response.data.success) {
          showToast('Impostazioni AI salvate con successo', 'success')
          // Also save to localStorage as backup
          localStorage.setItem('ceo-ai-settings', JSON.stringify(aiSettings.value))
        } else {
          throw new Error('Server response unsuccessful')
        }
      } catch (error) {
        console.error('Error saving AI settings:', error)
        showToast('Errore nel salvataggio delle impostazioni AI', 'error')
        
        // Fallback to localStorage if server fails
        try {
          localStorage.setItem('ceo-ai-settings', JSON.stringify(aiSettings.value))
          showToast('Impostazioni AI salvate localmente come fallback', 'warning')
        } catch (fallbackError) {
          console.error('Fallback save failed:', fallbackError)
        }
      } finally {
        saving.value = false
      }
    }
    
    // Load saved settings from localStorage
    const loadSavedSettings = () => {
      try {
        // Load saved company profile
        const savedProfile = localStorage.getItem('ceo-company-profile')
        if (savedProfile) {
          const profile = JSON.parse(savedProfile)
          companyProfile.value = { ...companyProfile.value, ...profile }
          selectedIndustry.value = profile.industry || selectedIndustry.value
        }
        
        // Load saved AI settings
        const savedAI = localStorage.getItem('ceo-ai-settings')
        if (savedAI) {
          aiSettings.value = { ...aiSettings.value, ...JSON.parse(savedAI) }
        }
        
        // Load saved research config
        const savedResearch = localStorage.getItem('ceo-research-config')
        if (savedResearch) {
          const research = JSON.parse(savedResearch)
          if (research.topics && research.industry === selectedIndustry.value) {
            // Merge with default config to preserve structure
            research.topics.forEach(savedTopic => {
              const currentTopic = researchTopics.value.find(t => t.id === savedTopic.id)
              if (currentTopic) {
                currentTopic.enabled = savedTopic.enabled
              }
            })
          }
        }
      } catch (error) {
        console.error('Error loading saved settings:', error)
      }
    }
    
    // Mount lifecycle
    onMounted(async () => {
      await loadConfiguration()
      loadSavedSettings()
    })
    
    return {
      loading,
      saving,
      selectedIndustry,
      researchTopics,
      companyProfile,
      aiSettings,
      loadResearchTopics,
      toggleTopic,
      getPriorityClass,
      saveProfile,
      saveResearchConfig,
      saveAISettings
    }
  }
}
</script>

<style scoped>
.btn-primary {
  @apply bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-secondary {
  @apply bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed;
}
</style>