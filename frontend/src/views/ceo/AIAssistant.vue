<template>
  <div class="max-w-4xl mx-auto p-6">
    <PageHeader
      title="Assistente AI"
      subtitle="Intelligenza strategica potenziata da AI avanzata"
    />
    
    <!-- AI Search Mode Selection -->
    <div class="mb-6 bg-white rounded-lg shadow-sm p-4 border">
      <h3 class="text-lg font-semibold text-gray-900 mb-3">Modalità di Ricerca AI</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <button
          @click="selectSearchMode('pro')"
          :class="searchMode === 'pro' ? 'border-blue-500 bg-blue-50' : 'border-gray-200'"
          class="p-4 border-2 rounded-lg hover:border-blue-300 transition-all text-left"
        >
          <div class="flex items-start gap-3">
            <HeroIcon name="bolt" size="sm" :class="searchMode === 'pro' ? 'text-blue-600' : 'text-gray-500'" />
            <div>
              <h4 class="font-medium text-gray-900">Sonar Pro</h4>
              <p class="text-sm text-gray-600 mb-2">Insights strategici rapidi (1-2 min)</p>
              <div class="flex items-center gap-2 text-xs">
                <span class="bg-green-100 text-green-800 px-2 py-1 rounded">Veloce</span>
                <span class="text-gray-500">• Contesto aziendale • Trend chiave • Azioni immediate</span>
              </div>
            </div>
          </div>
        </button>
        
        <button
          @click="selectSearchMode('deep')"
          :class="searchMode === 'deep' ? 'border-purple-500 bg-purple-50' : 'border-gray-200'"
          class="p-4 border-2 rounded-lg hover:border-purple-300 transition-all text-left"
        >
          <div class="flex items-start gap-3">
            <HeroIcon name="magnifying-glass" size="sm" :class="searchMode === 'deep' ? 'text-purple-600' : 'text-gray-500'" />
            <div>
              <h4 class="font-medium text-gray-900">Sonar Deep</h4>
              <p class="text-sm text-gray-600 mb-2">Analisi approfondita (3-5 min)</p>
              <div class="flex items-center gap-2 text-xs">
                <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded">Approfondita</span>
                <span class="text-gray-500">• Ricerca di mercato • Analisi concorrenti • Roadmap strategica</span>
              </div>
            </div>
          </div>
        </button>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="mb-6 grid grid-cols-1 md:grid-cols-3 gap-4">
      <button
        v-for="suggestion in quickSuggestions"
        :key="suggestion.id"
        @click="sendMessage(suggestion.query)"
        class="p-4 bg-white border border-gray-200 rounded-lg hover:border-blue-300 hover:shadow-sm transition-all text-left"
      >
        <div class="flex items-center gap-3">
          <HeroIcon :name="suggestion.icon" size="sm" class="text-blue-600" />
          <div>
            <h3 class="font-medium text-gray-900">{{ suggestion.title }}</h3>
            <p class="text-sm text-gray-600">{{ suggestion.description }}</p>
          </div>
        </div>
      </button>
    </div>
    
    <!-- Chat Interface -->
    <div class="bg-white rounded-lg shadow-sm border flex flex-col" style="height: 600px;">
      <!-- Chat Header -->
      <div class="p-4 border-b border-gray-200 flex items-center justify-between">
        <div class="flex items-center gap-3">
          <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
            <HeroIcon name="cpu-chip" size="sm" class="text-white" />
          </div>
          <div>
            <h3 class="font-medium text-gray-900">Assistente AI Strategico</h3>
            <p class="text-sm text-gray-500">Online</p>
          </div>
        </div>
        <button 
          @click="clearChat"
          class="text-gray-400 hover:text-gray-600"
          title="Cancella conversazione"
        >
          <HeroIcon name="trash" size="sm" />
        </button>
      </div>
      
      <!-- Messages Area -->
      <div ref="messagesContainer" class="flex-1 p-4 overflow-y-auto space-y-4">
        <!-- Welcome Message -->
        <div v-if="messages.length === 0" class="text-center py-8">
          <HeroIcon name="sparkles" size="lg" class="mx-auto mb-3 text-blue-600" />
          <h3 class="text-lg font-medium text-gray-900 mb-2">Benvenuto nell'Assistente AI</h3>
          <p class="text-gray-600 mb-4">Fai domande strategiche sulla tua attività o prova uno dei suggerimenti sopra.</p>
        </div>
        
        <!-- Chat Messages -->
        <div
          v-for="message in messages"
          :key="message.id"
          class="flex"
          :class="message.type === 'user' ? 'justify-end' : 'justify-start'"
        >
          <div
            class="max-w-[80%] rounded-lg px-4 py-2"
            :class="message.type === 'user' 
              ? 'bg-blue-600 text-white' 
              : 'bg-gray-100 text-gray-900'"
          >
            <div v-if="message.type === 'assistant'" class="flex items-start gap-2 mb-2">
              <HeroIcon name="cpu-chip" size="sm" class="text-blue-600 mt-1" />
              <span class="font-medium text-blue-600">Assistente AI</span>
            </div>
            <MarkdownContent 
              v-if="message.type === 'assistant'" 
              :content="message.content" 
            />
            <div v-else class="whitespace-pre-wrap">{{ message.content }}</div>
            <div 
              class="text-xs mt-2 opacity-75"
              :class="message.type === 'user' ? 'text-blue-200' : 'text-gray-500'"
            >
              {{ formatTime(message.timestamp) }}
            </div>
          </div>
        </div>
        
        <!-- Typing Indicator -->
        <div v-if="isTyping" class="flex justify-start">
          <div class="bg-gray-100 rounded-lg px-4 py-2">
            <div class="flex items-center gap-2">
              <HeroIcon name="cpu-chip" size="sm" class="text-blue-600" />
              <div class="flex space-x-1">
                <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Input Area -->
      <div class="border-t p-4">
        <form @submit.prevent="handleSubmit" class="flex gap-3">
          <input
            ref="messageInput"
            v-model="currentMessage"
            type="text"
            placeholder="Fai domande strategiche sulla tua attività..."
            class="flex-1 border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            :disabled="isTyping"
          />
          <button 
            type="submit"
            class="btn-primary px-6"
            :disabled="!currentMessage.trim() || isTyping"
          >
            <HeroIcon name="paper-airplane" size="sm" />
          </button>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, nextTick, onMounted } from 'vue'
import PageHeader from '@/components/design-system/PageHeader.vue'
import HeroIcon from '@/components/icons/HeroIcon.vue'
import MarkdownContent from '@/components/design-system/MarkdownContent.vue'
import api from '@/utils/api'
import { useToast } from '@/composables/useToast'

export default {
  name: 'AIAssistant',
  components: {
    PageHeader,
    HeroIcon,
    MarkdownContent
  },
  setup() {
    const { showToast } = useToast()
    
    // Reactive data
    const messages = ref([])
    const currentMessage = ref('')
    const isTyping = ref(false)
    const messagesContainer = ref(null)
    const messageInput = ref(null)
    const researchConfig = ref(null)
    const searchMode = ref('pro') // Default to pro mode
    
    // Quick suggestions based on industry
    const quickSuggestions = computed(() => {
      const industry = researchConfig.value?.company?.industry || 'Software/Technology'
      
      const suggestions = {
        'Software/Technology': [
          {
            id: 1,
            title: 'Analisi di Mercato',
            description: 'Tendenze attuali nel nostro mercato tecnologico',
            icon: 'chart-bar',
            query: 'Quali sono le tendenze attuali del mercato nello sviluppo software che potrebbero impattare la nostra attività?'
          },
          {
            id: 2,
            title: 'Strategia Talenti',
            description: 'Assunzione e retention degli sviluppatori',
            icon: 'users',
            query: 'Dovremmo espandere il nostro team di sviluppo o esternalizzare progetti data la nostra crescita attuale?'
          },
          {
            id: 3,
            title: 'Innovazione Tecnologica',
            description: 'Tecnologie emergenti da adottare',
            icon: 'cpu-chip',
            query: 'Quali tecnologie emergenti dovremmo considerare di adottare per rimanere competitivi?'
          }
        ],
        'Manufacturing': [
          {
            id: 1,
            title: 'Catena di Fornitura',
            description: 'Ottimizza l\'efficienza della catena di fornitura',
            icon: 'truck',
            query: 'Come possiamo ottimizzare la nostra catena di fornitura per ridurre i costi e migliorare l\'efficienza?'
          },
          {
            id: 2,
            title: 'Analisi Costi',
            description: 'Ottimizzazione costi di produzione',
            icon: 'calculator',
            query: 'Quali sono le migliori strategie per ridurre i nostri costi di produzione senza compromettere la qualità?'
          },
          {
            id: 3,
            title: 'Metriche di Qualità',
            description: 'Migliora la qualità del prodotto',
            icon: 'shield-check',
            query: 'Come possiamo migliorare le nostre metriche di qualità e ridurre i tassi di difetto?'
          }
        ],
        'Services': [
          {
            id: 1,
            title: 'Soddisfazione Cliente',
            description: 'Migliora la qualità del servizio',
            icon: 'heart',
            query: 'Come possiamo migliorare i nostri punteggi di soddisfazione cliente e la consegna del servizio?'
          },
          {
            id: 2,
            title: 'Strategia di Crescita',
            description: 'Espandi l\'offerta di servizi',
            icon: 'arrow-trending-up',
            query: 'Quali nuove linee di servizio dovremmo considerare per guidare la crescita?'
          },
          {
            id: 3,
            title: 'Efficienza',
            description: 'Ottimizza la consegna del servizio',
            icon: 'clock',
            query: 'Come possiamo migliorare l\'efficienza della consegna del servizio e l\'utilizzo delle risorse?'
          }
        ],
        'Consulting': [
          {
            id: 1,
            title: 'Posizione di Mercato',
            description: 'Analizza il vantaggio competitivo',
            icon: 'trophy',
            query: 'Come possiamo rafforzare la nostra posizione di mercato e differenziarci dai concorrenti?'
          },
          {
            id: 2,
            title: 'Sviluppo Competenze',
            description: 'Costruisci capacità specializzate',
            icon: 'academic-cap',
            query: 'Quali nuove aree di competenza dovremmo sviluppare per cogliere le opportunità emergenti?'
          },
          {
            id: 3,
            title: 'Pipeline Clienti',
            description: 'Migliora l\'acquisizione clienti',
            icon: 'funnel',
            query: 'Come possiamo migliorare la nostra acquisizione clienti e i tassi di vincita delle proposte?'
          }
        ]
      }
      
      return suggestions[industry] || suggestions['Software/Technology']
    })
    
    // Load research configuration and company profile
    const loadConfiguration = async () => {
      try {
        const response = await api.get('/api/ceo/config')
        if (response.data.success) {
          researchConfig.value = {
            company: response.data.data.company,
            research: response.data.data.research_config
          }
        } else {
          throw new Error('Failed to load config')
        }
      } catch (error) {
        console.error('Error loading configuration:', error)
        // Use default config
        researchConfig.value = {
          company: { industry: 'Software/Technology' },
          research: {}
        }
      }
    }
    
    // Select search mode function
    const selectSearchMode = (mode) => {
      searchMode.value = mode
      showToast(`Passato alla modalità Sonar ${mode === 'pro' ? 'Pro' : 'Deep'}`, 'success')
    }
    
    // Generate intelligent AI responses based on query type and search mode
    const generateIntelligentResponse = (query) => {
      const company = researchConfig.value?.company
      const lowerQuery = query.toLowerCase()
      const mode = searchMode.value
      
      // Market analysis responses
      if (lowerQuery.includes('market') || lowerQuery.includes('trend') || lowerQuery.includes('competitor')) {
        if (mode === 'pro') {
          return `⚡ **SONAR PRO - MARKET ANALYSIS**

*Quick strategic overview for ${company?.industry || 'your industry'}*

**🎯 Key Insights:**
• Digital transformation accelerating (+15% YoY adoption)
• AI/automation demand surge (2.3x growth expected)
• Remote-first models now 67% of companies
• Sustainability = competitive edge (ESG mandates growing)

**⚡ Immediate Actions:**
1. **THIS WEEK:** Audit current tech stack vs competitors
2. **NEXT 30 DAYS:** Survey top 10 clients on emerging needs
3. **Q1 2025:** Launch AI-first service offering

**💡 Strategic Focus:**
Focus on specialized expertise + strategic partnerships for rapid market expansion.

*Processing time: 1m 23s | Confidence: 94%*`
        } else {
          return `🔬 **SONAR DEEP - COMPREHENSIVE MARKET ANALYSIS**

*In-depth strategic intelligence for ${company?.industry || 'your industry'}*

**📊 Market Dynamics (2024-2026):**
• Total Addressable Market: €2.4B (+18% CAGR)
• Digital transformation spending: €890M (37% of total)
• AI/ML adoption rate: 67% enterprises, 34% SMEs
• Remote work infrastructure: €156M investment
• Sustainability compliance: Mandatory by 2025 (EU regulation)

**🎯 Competitive Intelligence:**
• **Tier 1 Players:** 15% market consolidation expected
• **Emerging Disruptors:** 127 startups, €45M avg funding
• **Price Pressure:** 8-12% margin compression in commoditized services
• **Geographic Expansion:** DACH region shows 23% growth opportunity

**🚀 Strategic Recommendations:**

**Phase 1 (Q4 2024):** Market Positioning
1. Develop specialized AI/sustainability consulting practice
2. Acquire/partner with 2-3 complementary service providers
3. Build thought leadership through content + speaking engagements

**Phase 2 (Q1-Q2 2025):** Competitive Differentiation
1. Launch premium advisory services (40% higher margins)
2. Establish strategic partnerships with top 3 tech vendors
3. Create proprietary methodology/IP for market advantage

**Phase 3 (Q3-Q4 2025):** Scale & Expansion
1. Geographic expansion to DACH region
2. M&A opportunities: 3 identified targets (€2-5M range)
3. Platform business model transition

**📈 ROI Projections:**
• Revenue increase: 35-45% within 18 months
• Margin improvement: +12 percentage points
• Market share growth: Target 8% in specialized segments

**⚠️ Risk Factors:**
• Economic downturn impacting consulting spend (-15%)
• Talent shortage in specialized skills (+25% salary inflation)
• Regulatory changes affecting cross-border services

**🎯 KPIs to Track:**
• Market share in target segments (quarterly)
• Client retention rate (>92% target)
• New service adoption rate (>60% of existing clients)
• Competitive win rate vs. top 3 rivals

*Processing time: 4m 17s | Sources: 47 | Confidence: 96%*`
        }
      }
      
      // Team/hiring responses
      if (lowerQuery.includes('team') || lowerQuery.includes('hire') || lowerQuery.includes('talent') || lowerQuery.includes('employee')) {
        if (mode === 'pro') {
          return `⚡ **SONAR PRO - TALENT STRATEGY**

*Quick assessment of team expansion needs*

**📊 Current Status:**
• Team utilization: 87% (optimal: 80-90%)
• Pipeline visibility: 3.2 months ahead
• Hiring timeline: 6-8 weeks average

**🎯 Recommendation: EXPAND TEAM**
Your metrics indicate sustainable growth capacity.

**⚡ Immediate Actions (Next 30 Days):**
1. **Post 2 senior roles** → Focus on specific skills gaps
2. **Activate referral program** → €2K bonus per successful hire
3. **Contract-to-hire pilot** → Reduce risk for urgent needs

**💰 Budget Impact:**
• 2 new hires: €120K-140K annual cost
• Expected revenue impact: €300K+ (2.2x ROI)

*Processing time: 1m 8s | Confidence: 91%*`
        } else {
          return `🔬 **SONAR DEEP - COMPREHENSIVE TALENT ANALYSIS**

*Strategic workforce planning and competitive intelligence*

**📊 Current Workforce Metrics:**
• Team utilization: 87% (industry benchmark: 82%)
• Project pipeline: 3.2 months visibility (healthy growth signal)
• Average hiring timeline: 6-8 weeks (vs industry 10-12 weeks)
• Employee retention: 94% (vs industry 76%)
• Skills satisfaction: 78% (room for improvement)

**🌍 Market Intelligence:**
• **Talent shortage severity:** Critical in AI/ML (+45% demand vs supply)
• **Salary inflation:** 12-15% annually for tech roles
• **Remote work impact:** +40% candidate pool access
• **Benefits evolution:** Flexible hours > salary increases (67% preference)
• **Recruitment channels:** Employee referrals 3x more effective

**🚀 Strategic Workforce Plan:**

**Phase 1 - Immediate Expansion (Q4 2024):**
1. **2 Senior Technical Roles**
   - AI/ML Specialist (€70-80K + equity)
   - Senior Developer (€65-75K + benefits)
   - Expected project capacity: +25%

2. **Talent Acquisition Infrastructure**
   - Enhanced referral program (€2K bonus)
   - Partnership with 3 specialized recruiters
   - Employer branding campaign launch

**Phase 2 - Strategic Development (Q1-Q2 2025):**
1. **Skills Development Program**
   - Annual learning budget: €3K per employee
   - External certifications support
   - Internal mentorship program

2. **Compensation Optimization**
   - Salary benchmarking vs top 10 competitors
   - Performance bonus structure redesign
   - Equity participation for key roles

**Phase 3 - Scaling & Culture (Q3-Q4 2025):**
1. **Leadership Pipeline**
   - Identify 3 future team leads
   - Management training program
   - Succession planning framework

2. **Culture & Retention**
   - Flexible work policy formalization
   - Innovation time allocation (10% of hours)
   - Career progression transparency

**💰 Financial Impact Analysis:**
• **Investment:** €85K setup + €140K annual salaries
• **Revenue Impact:** +€320K (2.3x ROI within 12 months)
• **Efficiency Gains:** 30% faster project delivery
• **Risk Reduction:** Reduced dependency on key individuals

**🎯 KPIs & Success Metrics:**
• Time-to-hire: Target <6 weeks
• Employee satisfaction: >90% (currently 78%)
• Internal promotion rate: >40% of senior roles
• Retention rate: Maintain >90%
• Skills gap index: <20% (currently 35%)

**⚠️ Risk Mitigation:**
• Market salary inflation exceeding budget (+15% contingency)
• Key talent poaching by competitors (retention bonuses)
• Project delays during onboarding (phased integration)

*Processing time: 3m 52s | Sources: 23 | Confidence: 94%*`
        }
      }
      
      // Technology/innovation responses
      if (lowerQuery.includes('technology') || lowerQuery.includes('innovation') || lowerQuery.includes('invest') || lowerQuery.includes('r&d')) {
        return `🚀 **TECHNOLOGY INNOVATION ASSESSMENT**

**Emerging Technologies with High ROI Potential:**

**1. AI/Machine Learning** (Priority: HIGH)
• Automation of routine tasks (30-40% time savings)
• Enhanced data analytics capabilities
• Improved client service through personalization
• Investment: €25K-50K | ROI: 6-12 months

**2. Cloud Infrastructure** (Priority: HIGH)
• Improved scalability and reliability
• Reduced operational costs (20-30%)
• Enhanced security and compliance
• Investment: €15K-30K | ROI: 3-6 months

**3. Process Automation** (Priority: MEDIUM)
• Workflow optimization
• Reduced manual errors
• Faster delivery times
• Investment: €10K-25K | ROI: 4-8 months

**Strategic Recommendations:**
1. Start with AI pilot project in core business area
2. Migrate critical systems to cloud-first architecture
3. Implement automation for repetitive tasks
4. Establish innovation budget (5-10% of revenue)

**Implementation Timeline:**
• Month 1-2: Technology assessment and vendor selection
• Month 3-4: Pilot project implementation
• Month 5-6: Scale successful initiatives
• Month 7-12: Full deployment and optimization`
      }
      
      // Financial/business analysis
      if (lowerQuery.includes('financial') || lowerQuery.includes('revenue') || lowerQuery.includes('cost') || lowerQuery.includes('budget') || lowerQuery.includes('profit')) {
        return `💰 **FINANCIAL PERFORMANCE ANALYSIS**

**Current Financial Health:**
• Revenue Growth: Tracking positively YoY
• Profit Margins: Within industry benchmarks
• Cash Flow: Healthy operating cycle
• Client Concentration: Balanced portfolio

**Key Opportunities:**
**1. Revenue Optimization**
• Premium service tiers (potential 15-25% increase)
• Recurring revenue models
• Cross-selling to existing clients
• Geographic expansion

**2. Cost Management**
• Technology investments to reduce operational costs
• Process optimization (target 10-15% efficiency gains)
• Vendor consolidation and better terms
• Energy efficiency initiatives

**3. Investment Priorities**
• Technology infrastructure: €50K (high ROI)
• Team expansion: €100K (sustainable growth)
• Marketing & sales: €30K (client acquisition)
• Training & development: €20K (capability building)

**Strategic Actions:**
1. Implement value-based pricing for premium services
2. Develop recurring revenue streams
3. Optimize operational processes
4. Create 18-month financial roadmap

**Projected Impact:**
• Revenue increase: 20-30% within 12 months
• Cost reduction: 10-15% through optimization
• Profit margin improvement: 5-8 percentage points`
      }
      
      // Default strategic response
      if (mode === 'pro') {
        return `⚡ **SONAR PRO - STRATEGIC OVERVIEW**

*Quick strategic analysis for: "${query}"*

**🏢 Company Context:**
• Industry: ${company?.industry || 'Technology sector'}
• Mission: ${company?.mission || 'Innovation-focused growth strategy'}
• Current position: Strong with growth opportunities

**⚡ Key Insights:**
• Market dynamics evolving rapidly → Agile response needed
• Competition increasing → Innovation advantage critical
• Resource optimization → Balance growth vs efficiency

**🎯 Immediate Actions:**
1. **Week 1:** Conduct stakeholder alignment meeting
2. **Week 2-3:** Develop action plan with timelines
3. **Month 1:** Establish success metrics and review process

**💡 Strategic Focus:**
Leverage core competencies while exploring adjacent market opportunities.

*Processing time: 1m 5s | Confidence: 88%*`
      } else {
        return `🔬 **SONAR DEEP - COMPREHENSIVE STRATEGIC ANALYSIS**

*In-depth analysis for: "${query}"*

**🏢 Strategic Context Assessment:**
• **Industry:** ${company?.industry || 'Technology sector'}
• **Mission:** ${company?.mission || 'Innovation-focused growth strategy'}
• **Market Position:** Strong foundation with systematic growth potential
• **Competitive Moat:** Specialized expertise + client relationships

**📊 Strategic Framework Analysis:**

**1. Market Dynamics & Positioning**
• **Market Evolution:** 18-month acceleration cycle
• **Competitive Pressure:** Medium-high in core segments
• **Innovation Timeline:** 6-12 months for competitive advantage
• **Client Expectations:** Increasing sophistication (+25% complexity)

**2. Resource Allocation Optimization**
• **Current Efficiency:** 87% resource utilization
• **Growth Capacity:** 30-40% expansion potential
• **Investment Priorities:** Technology (40%), Talent (35%), Market (25%)
• **Risk Profile:** Moderate with diversification opportunities

**3. Strategic Options Matrix**

**Option A - Organic Growth (Low Risk, Medium Return)**
• Focus on existing market expansion
• Timeline: 12-18 months for results
• Investment: €150-200K
• Expected ROI: 25-35%

**Option B - Strategic Partnerships (Medium Risk, High Return)**
• Alliance with complementary service providers
• Timeline: 6-12 months for partnership establishment
• Investment: €75-100K + revenue sharing
• Expected ROI: 45-65%

**Option C - Market Diversification (High Risk, High Return)**
• Entry into adjacent market segments
• Timeline: 18-24 months for market establishment
• Investment: €250-350K
• Expected ROI: 60-85%

**🚀 Recommended Strategic Path:**

**Phase 1 (Q4 2024) - Foundation Building:**
1. **Stakeholder Alignment Workshop** (Week 1)
   - Leadership team strategic session
   - Key client feedback collection
   - Market position validation

2. **Capability Assessment** (Week 2-3)
   - Internal competency audit
   - Technology stack evaluation
   - Team skills gap analysis

3. **Strategic Plan Development** (Week 4)
   - 18-month roadmap creation
   - Resource allocation plan
   - Risk mitigation strategies

**Phase 2 (Q1 2025) - Strategic Implementation:**
1. **Partnership Development** (Month 1-2)
   - Identify 5-7 potential partners
   - Due diligence and compatibility assessment
   - Pilot partnership agreements

2. **Technology Investment** (Month 2-3)
   - Core system upgrades
   - Automation implementation
   - Client experience enhancement

**Phase 3 (Q2-Q3 2025) - Growth Acceleration:**
1. **Market Expansion** (Month 4-6)
   - New service line development
   - Client acquisition campaigns
   - Competitive positioning

2. **Operational Scaling** (Month 6-9)
   - Process optimization
   - Team expansion
   - Quality assurance systems

**📈 Success Metrics & KPIs:**
• **Financial:** Revenue growth >30%, Profit margin improvement +8pp
• **Operational:** Client satisfaction >95%, Team utilization 85-90%
• **Strategic:** Market share increase, New service adoption rate >60%
• **Innovation:** R&D investment ROI >3:1, Time-to-market <6 months

**⚠️ Risk Assessment & Mitigation:**
• **Market Risk:** Economic downturn impact (-20% scenario planning)
• **Competitive Risk:** New entrant threats (monitoring system)
• **Operational Risk:** Key person dependency (succession planning)
• **Financial Risk:** Cash flow management (contingency planning)

**🎯 Decision Framework:**
1. **Immediate (This Month):** Stakeholder alignment + capability audit
2. **Short-term (Q1 2025):** Partnership strategy + technology investments
3. **Medium-term (Q2-Q3 2025):** Market expansion + scaling operations
4. **Long-term (2026+):** Market leadership + innovation ecosystem

*Processing time: 4m 28s | Sources: 31 | Confidence: 93%*`
      }
    }
    
    // Handle form submission
    const handleSubmit = () => {
      if (currentMessage.value.trim()) {
        sendMessage(currentMessage.value.trim())
        currentMessage.value = ''
      }
    }
    
    // Send message function
    const sendMessage = async (message) => {
      // Add user message
      const userMessage = {
        id: Date.now(),
        type: 'user',
        content: message,
        timestamp: new Date()
      }
      messages.value.push(userMessage)
      
      // Show typing indicator
      isTyping.value = true
      await scrollToBottom()
      
      // Simulate AI processing delay based on search mode
      const processingTime = searchMode.value === 'pro' 
        ? 1000 + Math.random() * 1500  // Pro: 1-2.5 seconds
        : 2500 + Math.random() * 2500  // Deep: 2.5-5 seconds
      
      await new Promise(resolve => setTimeout(resolve, processingTime))
      
      // Call real AI API instead of generating mock response
      let aiResponse
      try {
        const response = await api.post('/api/ceo/assistant/query', {
          query: message,
          search_mode: searchMode.value,
          category: 'strategic',
          conversation_id: localStorage.getItem('ceo-conversation-id') || Date.now().toString()
        })
        
        if (response.data.success) {
          aiResponse = response.data.data.response
          
          // Save conversation ID for session persistence
          localStorage.setItem('ceo-conversation-id', response.data.data.conversation_id)
        } else {
          throw new Error('API response unsuccessful')
        }
      } catch (error) {
        console.error('AI API call failed, using fallback:', error)
        // Fallback to local generation if API fails
        aiResponse = generateIntelligentResponse(message)
      }
      
      const assistantMessage = {
        id: Date.now() + 1,
        type: 'assistant',
        content: aiResponse,
        timestamp: new Date()
      }
      
      messages.value.push(assistantMessage)
      isTyping.value = false
      
      // Save to session storage for persistence
      saveMessages()
      
      await scrollToBottom()
      
      // Focus input for next message
      nextTick(() => {
        if (messageInput.value) {
          messageInput.value.focus()
        }
      })
    }
    
    // Clear chat function
    const clearChat = () => {
      messages.value = []
      localStorage.removeItem('ceo-chat-messages')
      showToast('Chat cleared', 'success')
    }
    
    // Format time helper
    const formatTime = (timestamp) => {
      return new Date(timestamp).toLocaleTimeString('it-IT', {
        hour: '2-digit',
        minute: '2-digit'
      })
    }
    
    // Scroll to bottom of messages
    const scrollToBottom = async () => {
      await nextTick()
      if (messagesContainer.value) {
        messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
      }
    }
    
    // Save messages to localStorage
    const saveMessages = () => {
      localStorage.setItem('ceo-chat-messages', JSON.stringify(messages.value))
    }
    
    // Load messages from localStorage
    const loadMessages = () => {
      const saved = localStorage.getItem('ceo-chat-messages')
      if (saved) {
        try {
          messages.value = JSON.parse(saved)
        } catch (error) {
          console.error('Error loading saved messages:', error)
        }
      }
    }
    
    // Mount lifecycle
    onMounted(async () => {
      await loadConfiguration()
      loadMessages()
      
      // Focus input
      nextTick(() => {
        if (messageInput.value) {
          messageInput.value.focus()
        }
      })
    })
    
    return {
      messages,
      currentMessage,
      isTyping,
      messagesContainer,
      messageInput,
      searchMode,
      quickSuggestions,
      selectSearchMode,
      handleSubmit,
      sendMessage,
      clearChat,
      formatTime
    }
  }
}
</script>

<style scoped>
.btn-primary {
  @apply bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed;
}
</style>