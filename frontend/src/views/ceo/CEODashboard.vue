<template>
  <DashboardTemplate
    :title="dashboardTitle"
    :subtitle="dashboardSubtitle"
    :stats="statsData"
    @refresh="loadDashboardData"
  >
    <template #header-actions>
      <div class="flex space-x-2">
        <router-link 
          to="/app/ceo/assistant"
          class="btn-primary flex items-center gap-2"
        >
          <HeroIcon name="cpu-chip" size="sm" />
          Assistente AI
        </router-link>
        
        <router-link 
          to="/app/ceo/insights"
          class="btn-secondary flex items-center gap-2"
        >
          <HeroIcon name="document-text" size="sm" />
          Archivio Insights
        </router-link>
      </div>
    </template>

    <template #widget>
      <!-- Main Dashboard Content -->
      <div class="space-y-6">
        
        <!-- Company Overview -->
        <div v-if="companyProfile" class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200">
          <div class="flex items-start justify-between">
            <div>
              <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ companyProfile.name }}</h3>
              <p class="text-sm text-gray-600 mb-3">{{ companyProfile.mission }}</p>
              <div class="flex items-center gap-4 text-sm text-gray-500">
                <span class="flex items-center gap-1">
                  <HeroIcon name="building-office" size="sm" />
                  {{ companyProfile.industry }}
                </span>
                <span class="flex items-center gap-1">
                  <HeroIcon name="users" size="sm" />
                  {{ companyProfile.team_size }}
                </span>
                <span class="flex items-center gap-1">
                  <HeroIcon name="calendar" size="sm" />
                  Fondata nel {{ companyProfile.founded }}
                </span>
              </div>
            </div>
            <router-link 
              to="/app/ceo/config"
              class="text-blue-600 hover:text-blue-700 text-sm font-medium"
            >
              Configura
            </router-link>
          </div>
        </div>
        
        <!-- Strategic Overview Cards -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <!-- Financial Summary Card -->
          <div class="bg-white rounded-lg shadow-sm p-6">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold text-gray-900">Panoramica Finanziaria</h3>
              <HeroIcon name="banknotes" size="sm" class="text-green-600" />
            </div>
            <div class="space-y-3">
              <div class="flex justify-between">
                <span class="text-sm text-gray-600">Progetti YTD</span>
                <span class="font-medium">{{ financialData.total_projects_ytd || 0 }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-sm text-gray-600">Budget Medio</span>
                <span class="font-medium">€{{ formatNumber(financialData.avg_project_budget) }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-sm text-gray-600">Contratti Attivi</span>
                <span class="font-medium">€{{ formatNumber(financialData.active_contracts_value) }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-sm text-gray-600">Proposte Mese</span>
                <span class="font-medium">{{ financialData.proposals_this_month || 0 }}</span>
              </div>
            </div>
          </div>

          <!-- Operational KPIs Card -->
          <div class="bg-white rounded-lg shadow-sm p-6">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold text-gray-900">KPI Operativi</h3>
              <HeroIcon name="chart-bar" size="sm" class="text-blue-600" />
            </div>
            <div class="space-y-3">
              <div class="flex justify-between">
                <span class="text-sm text-gray-600">Completamento Progetti</span>
                <span class="font-medium">{{ operationalData.project_completion_rate || 0 }}%</span>
              </div>
              <div class="flex justify-between">
                <span class="text-sm text-gray-600">Utilizzo Team</span>
                <span class="font-medium">{{ operationalData.team_utilization_rate || 0 }}%</span>
              </div>
              <div class="flex justify-between">
                <span class="text-sm text-gray-600">Clienti Attivi</span>
                <span class="font-medium">{{ operationalData.active_clients || 0 }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-sm text-gray-600">Ore Questo Mese</span>
                <span class="font-medium">{{ formatNumber(operationalData.total_timesheet_hours_month) }}h</span>
              </div>
            </div>
          </div>

          <!-- Team Insights Card -->
          <div class="bg-white rounded-lg shadow-sm p-6">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold text-gray-900">Performance del Team</h3>
              <HeroIcon name="users" size="sm" class="text-purple-600" />
            </div>
            <div class="space-y-3">
              <div class="flex justify-between">
                <span class="text-sm text-gray-600">Dipendenti Attivi</span>
                <span class="font-medium">{{ teamData.active_employees || 0 }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-sm text-gray-600">Copertura Valutazioni</span>
                <span class="font-medium">{{ teamData.performance_review_coverage || 0 }}%</span>
              </div>
              <div class="flex justify-between">
                <span class="text-sm text-gray-600">Completamento Task</span>
                <span class="font-medium">{{ teamData.task_completion_rate || 0 }}%</span>
              </div>
              <div class="flex justify-between">
                <span class="text-sm text-gray-600">Task Completati</span>
                <span class="font-medium">{{ teamData.completed_tasks_month || 0 }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- AI Insights Section -->
        <div class="bg-white rounded-lg shadow-sm">
          <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h3 class="text-lg font-semibold text-gray-900">Insights Strategici Recenti</h3>
            <router-link 
              to="/app/ceo/insights" 
              class="text-blue-600 hover:text-blue-800 font-medium"
            >
              Vedi Tutti →
            </router-link>
          </div>
          <div class="p-6">
            <div v-if="recentInsights.length === 0" class="text-center py-8">
              <HeroIcon name="light-bulb" size="lg" class="mx-auto text-gray-400 mb-3" />
              <p class="text-gray-500 mb-4">Nessun insight generato ancora</p>
              <button 
                @click="startQuickResearch"
                class="btn-primary"
              >
                Genera Primi Insights
              </button>
            </div>
            <div v-else class="space-y-4">
              <div 
                v-for="insight in recentInsights" 
                :key="insight.id"
                class="border border-gray-200 rounded-lg p-4 hover:border-blue-300 transition-colors"
              >
                <div class="flex items-start justify-between mb-2">
                  <h4 class="font-medium text-gray-900">{{ insight.title }}</h4>
                  <span :class="getPriorityClass(insight.priority)" class="px-2 py-1 text-xs rounded-full">
                    {{ insight.priority }}
                  </span>
                </div>
                <div 
                  class="text-sm text-gray-600 mb-3 markdown-preview-dashboard line-clamp-3"
                  v-html="renderMarkdownPreview(insight.summary || insight.content)"
                ></div>
                <div class="flex items-center justify-between text-xs text-gray-500">
                  <span>{{ insight.insight_type }}</span>
                  <span>{{ formatDate(insight.created_at || insight.timestamp) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Active Research Sessions -->
        <div v-if="activeResearch.length > 0" class="bg-white rounded-lg shadow-sm">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Sessioni di Ricerca Attive</h3>
          </div>
          <div class="p-6">
            <div class="space-y-4">
              <div 
                v-for="session in activeResearch" 
                :key="session.id"
                class="border border-gray-200 rounded-lg p-4"
              >
                <div class="flex items-center justify-between mb-2">
                  <h4 class="font-medium text-gray-900">{{ session.title }}</h4>
                  <span class="px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full">
                    {{ session.status }}
                  </span>
                </div>
                <div class="flex items-center justify-between text-sm text-gray-600">
                  <span>{{ session.category }}</span>
                  <span>{{ session.query_count }} query</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Sample Data Helper (show if no data) -->
        <div v-if="showSampleDataHelper" class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200 mb-6">
          <div class="flex items-start gap-4">
            <HeroIcon name="information-circle" size="sm" class="text-blue-600 mt-1" />
            <div class="flex-1">
              <h3 class="text-lg font-semibold text-blue-900 mb-2">Iniziare con la Dashboard CEO</h3>
              <p class="text-blue-700 mb-4">
                Sembra che tu abbia appena iniziato! La Dashboard CEO funziona meglio con alcuni dati aziendali. 
                Ecco cosa puoi fare:
              </p>
              <div class="space-y-2 text-sm text-blue-600">
                <div class="flex items-center gap-2">
                  <span class="w-2 h-2 bg-blue-600 rounded-full"></span>
                  <span><strong>Aggiungi Clienti:</strong> Vai su CRM → Clienti per aggiungere i tuoi contatti aziendali</span>
                </div>
                <div class="flex items-center gap-2">
                  <span class="w-2 h-2 bg-blue-600 rounded-full"></span>
                  <span><strong>Crea Progetti:</strong> Imposta i tuoi progetti in corso nella Gestione Progetti</span>
                </div>
                <div class="flex items-center gap-2">
                  <span class="w-2 h-2 bg-blue-600 rounded-full"></span>
                  <span><strong>Usa Assistente AI:</strong> Inizia a fare domande strategiche per generare insights</span>
                </div>
              </div>
              <div class="mt-4 flex gap-3">
                <router-link to="/app/crm/clients" class="btn-secondary text-sm">
                  Aggiungi Clienti
                </router-link>
                <router-link to="/app/projects" class="btn-secondary text-sm">
                  Crea Progetti
                </router-link>
                <router-link to="/app/ceo/assistant" class="btn-primary text-sm">
                  Prova Assistente AI
                </router-link>
              </div>
            </div>
          </div>
        </div>

        <!-- Critical Alerts -->
        <div v-if="criticalAlerts.length > 0" class="bg-white rounded-lg shadow-sm border-l-4 border-red-500">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
              <HeroIcon name="exclamation-triangle" size="sm" class="text-red-500" />
              Avvisi Critici
            </h3>
          </div>
          <div class="p-6">
            <div class="space-y-3">
              <div 
                v-for="alert in criticalAlerts" 
                :key="alert.title"
                class="flex items-center justify-between p-3 bg-red-50 rounded-lg"
              >
                <div>
                  <h4 class="font-medium text-red-900">{{ alert.title }}</h4>
                  <p class="text-sm text-red-700">{{ alert.message }}</p>
                </div>
                <router-link 
                  v-if="alert.action_url"
                  :to="alert.action_url"
                  class="btn-secondary text-sm"
                >
                  Visualizza
                </router-link>
              </div>
            </div>
          </div>
        </div>

      </div>
    </template>
  </DashboardTemplate>
</template>

<script>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import DashboardTemplate from '@/components/design-system/templates/DashboardTemplate.vue'
import HeroIcon from '@/components/icons/HeroIcon.vue'
import { useToast } from '@/composables/useToast'
import api from '@/utils/api'
import { marked } from 'marked'

export default {
  name: 'CEODashboard',
  components: {
    DashboardTemplate,
    HeroIcon
  },
  setup() {
    const router = useRouter()
    const { showToast } = useToast()
    
    // Reactive data
    const loading = ref(false)
    const dashboardData = ref({})
    const financialData = ref({})
    const operationalData = ref({})
    const teamData = ref({})
    const recentInsights = ref([])
    const activeResearch = ref([])
    const criticalAlerts = ref([])
    const companyProfile = ref(null)
    
    // Computed dashboard title and subtitle based on company profile
    const dashboardTitle = computed(() => {
      return companyProfile.value ? 
        `${companyProfile.value.name} - CEO Dashboard` : 
        'Human CEO Strategic Dashboard'
    })
    
    const dashboardSubtitle = computed(() => {
      return companyProfile.value?.vision || 
        'Strategic Command Center for Executive Decision Making'
    })
    
    // Computed stats for DashboardTemplate
    const statsData = computed(() => [
      {
        title: 'Progetti Attivi',
        value: financialData.value.total_projects_ytd || 0,
        icon: 'folder-open',
        color: 'green',
        trend: null
      },
      {
        title: 'Utilizzo Team',
        value: `${operationalData.value.team_utilization_rate || 0}%`,
        icon: 'users',
        color: 'purple',
        trend: null
      },
      {
        title: 'Clienti Attivi',
        value: operationalData.value.active_clients || 0,
        icon: 'building-office',
        color: 'blue',
        trend: null
      },
      {
        title: 'Insights in Sospeso',
        value: recentInsights.value.length || 0,
        icon: 'light-bulb',
        color: 'yellow',
        trend: null
      }
    ])
    
    // Show sample data helper if there's very little data
    const showSampleDataHelper = computed(() => {
      const totalProjects = financialData.value.total_projects_ytd || 0
      const totalClients = operationalData.value.active_clients || 0
      const totalInsights = recentInsights.value.length || 0
      
      // Show helper if very little data across all categories
      return totalProjects < 2 && totalClients < 2 && totalInsights < 1
    })
    
    // Methods
    // Load company profile from API
    const loadCompanyProfile = async () => {
      try {
        const response = await api.get('/api/ceo/config')
        if (response.data.success) {
          companyProfile.value = response.data.data.company
        } else {
          throw new Error('Failed to load config')
        }
      } catch (error) {
        console.error('Error loading company profile:', error)
        // Use fallback
        companyProfile.value = {
          name: 'Your Company',
          industry: 'Technology',
          mission: 'Driving innovation and growth',
          vision: 'Strategic excellence through technology',
          team_size: '25+ professionisti',
          founded: '2021'
        }
      }
    }
    
    const loadDashboardData = async () => {
      loading.value = true
      try {
        const response = await api.get('/api/ceo/dashboard-data')
        
        if (response.data.success) {
          dashboardData.value = response.data.data
          financialData.value = response.data.data.financial_summary || {}
          operationalData.value = response.data.data.operational_kpis || {}
          teamData.value = response.data.data.team_insights || {}
          recentInsights.value = response.data.data.recent_insights || []
          activeResearch.value = response.data.data.active_research || []
          criticalAlerts.value = response.data.data.critical_alerts || []
        }
      } catch (error) {
        console.error('Error loading CEO dashboard data:', error)
        showToast('Error loading dashboard data', 'error')
      } finally {
        loading.value = false
      }
      
      // If no insights from backend, show demo insights
      if (recentInsights.value.length === 0) {
        recentInsights.value = getDemoInsights()
      }
    }
    
    const loadInsightsFromLocalStorage = () => {
      try {
        // Load chat messages and convert to insights
        const chatMessages = localStorage.getItem('ceo-chat-messages')
        
        if (chatMessages) {
          const messages = JSON.parse(chatMessages)
          const insights = convertMessagesToInsights(messages)
          
          // Merge with backend insights if any exist
          const existingIds = new Set(recentInsights.value.map(i => i.id))
          const localInsights = insights.filter(i => !existingIds.has(i.id))
          
          // Combine and limit to 5 most recent
          const combined = [...recentInsights.value, ...localInsights]
            .sort((a, b) => new Date(b.timestamp || b.created_at) - new Date(a.timestamp || a.created_at))
            .slice(0, 5)
          
          recentInsights.value = combined
        }
      } catch (error) {
        console.error('Error loading insights from localStorage:', error)
      }
    }
    
    const convertMessagesToInsights = (messages) => {
      const sessions = []
      let currentSession = null
      let sessionId = Date.now()
      
      for (const message of messages) {
        if (message.type === 'user') {
          // Start new session
          currentSession = {
            id: sessionId++,
            title: generateInsightTitle(message.content),
            priority: 'medium',
            timestamp: message.timestamp,
            created_at: message.timestamp, // Add created_at for compatibility
            content: '',
            summary: '',
            insight_type: categorizeQuery(message.content),
            status: 'new' // Add status field
          }
        } else if (message.type === 'assistant' && currentSession) {
          // Complete session with AI response
          currentSession.content = message.content
          currentSession.summary = message.content.substring(0, 150) + '...'
          sessions.push(currentSession)
          currentSession = null
        }
      }
      
      return sessions.reverse() // Most recent first
    }
    
    const generateInsightTitle = (query) => {
      if (query.length > 60) {
        return query.substring(0, 60) + '...'
      }
      return query
    }
    
    const categorizeQuery = (query) => {
      const lowerQuery = query.toLowerCase()
      if (lowerQuery.includes('market') || lowerQuery.includes('competitor')) return 'market_analysis'
      if (lowerQuery.includes('team') || lowerQuery.includes('hire')) return 'talent_strategy'
      if (lowerQuery.includes('financial') || lowerQuery.includes('revenue')) return 'financial_analysis'
      if (lowerQuery.includes('technology') || lowerQuery.includes('innovation')) return 'technology_innovation'
      return 'strategic_planning'
    }
    
    const renderMarkdownPreview = (content) => {
      // Create a limited preview with markdown rendering for dashboard
      const previewLength = 120 // Shorter for dashboard
      let truncatedContent = content
      
      // If content is longer than preview length, truncate it smartly
      if (content.length > previewLength) {
        truncatedContent = content.substring(0, previewLength)
        // Try to break at a word boundary
        const lastSpace = truncatedContent.lastIndexOf(' ')
        if (lastSpace > previewLength * 0.8) {
          truncatedContent = truncatedContent.substring(0, lastSpace)
        }
        truncatedContent += '...'
      }
      
      // Configure marked for preview (simpler output)
      marked.setOptions({
        breaks: true,
        gfm: true
      })
      
      return marked(truncatedContent)
    }
    
    const startQuickResearch = () => {
      router.push('/app/ceo/assistant')
    }
    
    const formatNumber = (value) => {
      if (!value) return '0'
      return new Intl.NumberFormat('it-IT').format(value)
    }
    
    const formatDate = (dateString) => {
      if (!dateString) return ''
      return new Date(dateString).toLocaleDateString('it-IT', {
        day: 'numeric',
        month: 'short',
        hour: '2-digit',
        minute: '2-digit'
      })
    }
    
    const getPriorityClass = (priority) => {
      const classes = {
        'critical': 'bg-red-100 text-red-800',
        'high': 'bg-orange-100 text-orange-800',
        'medium': 'bg-yellow-100 text-yellow-800',
        'low': 'bg-green-100 text-green-800'
      }
      return classes[priority] || classes.medium
    }
    
    const getDemoInsights = () => {
      return [
        {
          id: 1,
          title: 'Market Analysis Q1 2024',
          priority: 'high',
          created_at: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
          content: '## Market Trends\n\nDigital transformation is accelerating across all sectors with increased demand for AI/automation solutions.',
          summary: 'Digital transformation accelerating with AI/automation demand increasing. Remote-first models becoming standard.',
          insight_type: 'market_analysis',
          status: 'new'
        },
        {
          id: 2,
          title: 'Team Expansion Strategy',
          priority: 'medium',
          created_at: new Date(Date.now() - *********).toISOString(), // 2 days ago
          content: '## Talent Strategy\n\nBased on current utilization rates (85-90%), team expansion is recommended to support growth.',
          summary: 'Team utilization at optimal range. Recommend hiring 2 senior positions within 30 days.',
          insight_type: 'talent_strategy',
          status: 'new'
        }
      ]
    }
    
    // Lifecycle
    onMounted(async () => {
      await loadCompanyProfile()
      loadDashboardData()
    })
    
    return {
      loading,
      companyProfile,
      dashboardTitle,
      dashboardSubtitle,
      statsData,
      showSampleDataHelper,
      financialData,
      operationalData,
      teamData,
      recentInsights,
      activeResearch,
      criticalAlerts,
      loadDashboardData,
      startQuickResearch,
      renderMarkdownPreview,
      formatNumber,
      formatDate,
      getPriorityClass
    }
  }
}
</script>

<style scoped>
.btn-primary {
  @apply bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors;
}

.btn-secondary {
  @apply bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors;
}

.markdown-preview-dashboard {
  line-height: 1.4;
}

.markdown-preview-dashboard h1,
.markdown-preview-dashboard h2,
.markdown-preview-dashboard h3,
.markdown-preview-dashboard h4,
.markdown-preview-dashboard h5,
.markdown-preview-dashboard h6 {
  font-weight: 600;
  margin: 0.25rem 0 0.125rem 0;
  color: #374151;
}

.markdown-preview-dashboard h1 { font-size: 0.9rem; }
.markdown-preview-dashboard h2 { font-size: 0.875rem; }
.markdown-preview-dashboard h3 { font-size: 0.875rem; }

.markdown-preview-dashboard strong {
  font-weight: 600;
  color: #374151;
}

.markdown-preview-dashboard em {
  font-style: italic;
  color: #4b5563;
}

.markdown-preview-dashboard ul,
.markdown-preview-dashboard ol {
  padding-left: 1rem;
  margin: 0.125rem 0;
}

.markdown-preview-dashboard li {
  margin: 0.125rem 0;
}

.markdown-preview-dashboard p {
  margin: 0.125rem 0;
}

.markdown-preview-dashboard code {
  background-color: #f3f4f6;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: ui-monospace, monospace;
  font-size: 0.75rem;
}
</style>