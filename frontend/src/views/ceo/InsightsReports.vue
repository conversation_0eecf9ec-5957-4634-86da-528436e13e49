<template>
  <div class="max-w-6xl mx-auto p-6">
    <PageHeader
      title="Insights e Report"
      subtitle="Archivio insights strategici e reportistica"
    />
    
    <!-- Filter Bar -->
    <div class="bg-white rounded-lg shadow-sm p-4 mb-6">
      <div class="flex flex-wrap gap-4">
        <select 
          v-model="selectedType"
          class="border border-gray-300 rounded-lg px-3 py-2"
        >
          <option value="">Tutti i Tipi</option>
          <option value="market_analysis">Analisi di Mercato</option>
          <option value="talent_strategy">Strategia Talenti</option>
          <option value="financial_analysis">Analisi Finanziaria</option>
          <option value="technology_innovation">Innovazione Tecnologica</option>
          <option value="strategic_planning">Pianificazione Strategica</option>
        </select>
        
        <select 
          v-model="selectedPriority"
          class="border border-gray-300 rounded-lg px-3 py-2"
        >
          <option value=""><PERSON>tte le Priorità</option>
          <option value="critical">Critica</option>
          <option value="high">Alta</option>
          <option value="medium">Media</option>
          <option value="low">Bassa</option>
        </select>
        
        <input
          v-model="searchQuery"
          type="text"
          placeholder="Cerca insights..."
          class="border border-gray-300 rounded-lg px-3 py-2 flex-1 min-w-64"
        />
        
        <button 
          @click="clearFilters"
          class="btn-secondary"
          v-if="searchQuery || selectedType || selectedPriority"
        >
          <HeroIcon name="x-mark" size="sm" />
          Cancella
        </button>
      </div>
    </div>
    
    <!-- Insights List -->
    <div class="space-y-4">
      <!-- Loading State -->
      <div v-if="loading" class="bg-white rounded-lg shadow-sm p-8 text-center">
        <HeroIcon name="arrow-path" size="lg" class="mx-auto text-gray-400 animate-spin mb-3" />
        <p class="text-gray-500">Caricamento insights...</p>
      </div>
      
      <!-- Insights Cards -->
      <div v-else-if="filteredInsights.length > 0" class="space-y-4">
        <div
          v-for="insight in filteredInsights"
          :key="insight.id"
          class="bg-white rounded-lg shadow-sm border border-gray-200 hover:border-gray-300 transition-colors"
        >
          <div class="p-6">
            <!-- Header -->
            <div class="flex items-start justify-between mb-4">
              <div class="flex-1">
                <div class="flex items-center gap-3 mb-2">
                  <HeroIcon :name="getInsightIcon(insight.insight_type || insight.type)" size="sm" :class="getInsightIconColor(insight.insight_type || insight.type)" />
                  <h3 class="text-lg font-semibold text-gray-900">{{ insight.title }}</h3>
                  <span 
                    class="px-2 py-1 text-xs rounded-full"
                    :class="getPriorityClass(insight.priority)"
                  >
                    {{ insight.priority.toUpperCase() }}
                  </span>
                </div>
                <p class="text-sm text-gray-600">{{ insight.category }}</p>
              </div>
              <div class="text-right">
                <p class="text-sm text-gray-500">{{ formatDate(insight.timestamp) }}</p>
                <p class="text-xs text-gray-400">{{ formatTime(insight.timestamp) }}</p>
              </div>
            </div>
            
            <!-- Content Preview -->
            <div class="mb-4">
              <div 
                class="text-gray-700 line-clamp-3 markdown-preview"
                v-html="renderMarkdownPreview(insight.summary || insight.content)"
              ></div>
            </div>
            
            <!-- Tags -->
            <div v-if="insight.tags && insight.tags.length > 0" class="flex flex-wrap gap-2 mb-4">
              <span
                v-for="tag in insight.tags"
                :key="tag"
                class="inline-block bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded"
              >
                {{ tag }}
              </span>
            </div>
            
            <!-- Actions -->
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-2">
                <button
                  @click="viewInsight(insight)"
                  class="text-blue-600 hover:text-blue-700 text-sm font-medium"
                >
                  Vedi Dettagli
                </button>
                <button
                  @click="exportInsight(insight)"
                  class="text-gray-600 hover:text-gray-700 text-sm"
                >
                  Esporta
                </button>
                <button
                  @click="deleteInsight(insight)"
                  class="text-red-600 hover:text-red-700 text-sm"
                  title="Elimina insight"
                >
                  Elimina
                </button>
              </div>
              <div class="flex items-center gap-2 text-xs text-gray-500">
                <span>{{ insight.query_count || 1 }} query</span>
                <span>•</span>
                <span>{{ insight.session_duration || '2m' }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Empty State -->
      <div v-else class="bg-white rounded-lg shadow-sm p-8 text-center">
        <HeroIcon name="document-text" size="lg" class="mx-auto text-gray-400 mb-3" />
        <h3 class="text-lg font-medium text-gray-900 mb-2">Nessun Insight Ancora</h3>
        <p class="text-gray-600 mb-4">
          Gli insights strategici appariranno qui dopo aver eseguito sessioni di ricerca AI
        </p>
        <router-link to="/app/ceo/assistant" class="btn-primary">
          Avvia Assistente AI
        </router-link>
      </div>
    </div>
    
    <!-- Insight Detail Modal -->
    <div
      v-if="selectedInsight"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
      @click="closeInsightModal"
    >
      <div
        class="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] flex flex-col"
        @click.stop
      >
        <!-- Modal Header -->
        <div class="flex items-center justify-between p-6 border-b border-gray-200 flex-shrink-0">
          <div class="flex items-center gap-3">
            <HeroIcon :name="getInsightIcon(selectedInsight.insight_type || selectedInsight.type)" size="sm" :class="getInsightIconColor(selectedInsight.insight_type || selectedInsight.type)" />
            <div>
              <h2 class="text-xl font-semibold text-gray-900">{{ selectedInsight.title }}</h2>
              <p class="text-sm text-gray-600">{{ selectedInsight.category }} • {{ formatDate(selectedInsight.timestamp) }}</p>
            </div>
          </div>
          <button
            @click="closeInsightModal"
            class="text-gray-400 hover:text-gray-600"
          >
            <HeroIcon name="x-mark" size="md" />
          </button>
        </div>
        
        <!-- Modal Content -->
        <div class="p-6 overflow-y-auto flex-1 min-h-0">
          <div class="prose max-w-none">
            <div v-html="renderMarkdown(selectedInsight.content)" class="markdown-content"></div>
          </div>
        </div>
        
        <!-- Modal Footer -->
        <div class="flex items-center justify-between p-6 border-t border-gray-200 flex-shrink-0">
          <div class="flex items-center gap-4">
            <span 
              class="px-3 py-1 text-sm rounded-full"
              :class="getPriorityClass(selectedInsight.priority)"
            >
              {{ selectedInsight.priority.toUpperCase() }} Priority
            </span>
            <span class="text-sm text-gray-500">
              {{ selectedInsight.query_count || 1 }} query • {{ selectedInsight.session_duration || '2m' }}
            </span>
          </div>
          <div class="flex gap-2">
            <button
              @click="exportInsight(selectedInsight)"
              class="btn-secondary"
            >
              Esporta PDF
            </button>
            <button
              @click="deleteInsight(selectedInsight)"
              class="btn-danger"
            >
              Elimina
            </button>
            <button
              @click="closeInsightModal"
              class="btn-primary"
            >
              Chiudi
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import PageHeader from '@/components/design-system/PageHeader.vue'
import HeroIcon from '@/components/icons/HeroIcon.vue'
import { useToast } from '@/composables/useToast'
import { marked } from 'marked'
import jsPDF from 'jspdf'

export default {
  name: 'InsightsReports',
  components: {
    PageHeader,
    HeroIcon
  },
  setup() {
    const { showToast } = useToast()
    
    // Reactive data
    const loading = ref(false)
    const insights = ref([])
    const selectedInsight = ref(null)
    const searchQuery = ref('')
    const selectedType = ref('')
    const selectedPriority = ref('')
    
    // Load insights from localStorage (chat sessions converted to insights)
    const loadInsights = () => {
      loading.value = true
      try {
        // Load chat messages and convert to insights
        const chatMessages = localStorage.getItem('ceo-chat-messages')
        if (chatMessages) {
          const messages = JSON.parse(chatMessages)
          const sessions = convertMessagesToInsights(messages)
          insights.value = sessions
        } else {
          // Load mock insights for demo
          insights.value = generateMockInsights()
        }
      } catch (error) {
        console.error('Error loading insights:', error)
        insights.value = generateMockInsights()
      } finally {
        loading.value = false
      }
    }
    
    // Convert chat messages to insights
    const convertMessagesToInsights = (messages) => {
      const sessions = []
      let currentSession = null
      let sessionId = 1
      
      for (const message of messages) {
        if (message.type === 'user') {
          // Start new session
          currentSession = {
            id: sessionId++,
            title: generateInsightTitle(message.content),
            type: categorizeQuery(message.content),
            category: 'Strategic Analysis',
            priority: 'medium',
            timestamp: message.timestamp,
            content: '',
            summary: '',
            query_count: 1,
            session_duration: '2-3m',
            tags: generateTags(message.content)
          }
        } else if (message.type === 'assistant' && currentSession) {
          // Complete session with AI response
          currentSession.content = message.content
          currentSession.summary = message.content.substring(0, 200) + '...'
          sessions.push(currentSession)
          currentSession = null
        }
      }
      
      return sessions.reverse() // Most recent first
    }
    
    // Generate insight title from query
    const generateInsightTitle = (query) => {
      if (query.length > 50) {
        return query.substring(0, 50) + '...'
      }
      return query
    }
    
    // Categorize query type
    const categorizeQuery = (query) => {
      const lowerQuery = query.toLowerCase()
      if (lowerQuery.includes('market') || lowerQuery.includes('competitor') || lowerQuery.includes('mercato') || lowerQuery.includes('concorrenti')) return 'market_analysis'
      if (lowerQuery.includes('team') || lowerQuery.includes('hire') || lowerQuery.includes('assumere') || lowerQuery.includes('talenti')) return 'talent_strategy'
      if (lowerQuery.includes('financial') || lowerQuery.includes('revenue') || lowerQuery.includes('finanziario') || lowerQuery.includes('ricavi')) return 'financial_analysis'
      if (lowerQuery.includes('technology') || lowerQuery.includes('innovation') || lowerQuery.includes('tecnologia') || lowerQuery.includes('innovazione')) return 'technology_innovation'
      return 'strategic_planning'
    }
    
    // Generate tags from query
    const generateTags = (query) => {
      const tags = []
      const lowerQuery = query.toLowerCase()
      
      if (lowerQuery.includes('market') || lowerQuery.includes('mercato')) tags.push('Ricerca di Mercato')
      if (lowerQuery.includes('team') || lowerQuery.includes('hire') || lowerQuery.includes('assumere') || lowerQuery.includes('talenti')) tags.push('Risorse Umane')
      if (lowerQuery.includes('revenue') || lowerQuery.includes('financial') || lowerQuery.includes('ricavi') || lowerQuery.includes('finanziario')) tags.push('Analisi Finanziaria')
      if (lowerQuery.includes('technology') || lowerQuery.includes('tecnologia')) tags.push('Tecnologia')
      if (lowerQuery.includes('strategy') || lowerQuery.includes('strategia')) tags.push('Strategia')
      if (lowerQuery.includes('growth') || lowerQuery.includes('crescita')) tags.push('Crescita')
      
      return tags.length > 0 ? tags : ['Strategia Generale']
    }
    
    // Generate mock insights for demo
    const generateMockInsights = () => {
      return [
        {
          id: 1,
          title: 'Analisi di Mercato per Q1 2024',
          type: 'market_analysis',
          category: 'Intelligence di Mercato',
          priority: 'high',
          timestamp: new Date(Date.now() - 86400000), // 1 day ago
          content: `📊 **MARKET ANALYSIS**\n\nBased on current market intelligence for Software/Technology:\n\n**Key Trends:**\n• Digital transformation accelerating across all sectors\n• Increased demand for AI/automation solutions\n• Remote-first business models becoming standard\n• Sustainability becoming a competitive advantage\n\n**Competitive Landscape:**\n• Market consolidation creating opportunities for specialized players\n• New entrants focusing on niche markets\n• Price competition increasing in commoditized services\n\n**Recommendations:**\n1. Focus on differentiation through specialized expertise\n2. Invest in technology that enhances client value\n3. Consider strategic partnerships for market expansion\n4. Monitor emerging competitors in adjacent markets`,
          summary: 'Comprehensive market analysis showing accelerating digital transformation and opportunities for specialized technology companies...',
          query_count: 3,
          session_duration: '5m',
          tags: ['Market Research', 'Technology', 'Strategy']
        },
        {
          id: 2,
          title: 'Analisi Strategia di Espansione del Team',
          type: 'talent_strategy',
          category: 'Risorse Umane',
          priority: 'medium',
          timestamp: new Date(Date.now() - 172800000), // 2 days ago
          content: `👥 **TALENT STRATEGY ANALYSIS**\n\nBased on your current business metrics:\n\n**Current Situation:**\n• Team utilization: 85-90% (optimal range)\n• Project pipeline: 3-4 months visibility\n• Average hiring timeline: 6-8 weeks\n• Employee retention: Above industry average\n\n**Market Conditions:**\n• Talent shortage in key technical skills\n• Remote work increasing candidate pool\n• Salary inflation 8-12% annually\n• Benefits becoming more important than salary\n\n**Recommendation:**\n**EXPAND TEAM** - Your metrics indicate sustainable growth\n\n**Action Plan:**\n1. **Immediate** (next 30 days):\n   - Post job openings for 2 senior positions\n   - Activate employee referral program\n   - Consider contract-to-hire for urgent needs`,
          summary: 'Analysis recommends team expansion based on current utilization rates and strong project pipeline...',
          query_count: 2,
          session_duration: '3m',
          tags: ['Human Resources', 'Growth', 'Strategy']
        }
      ]
    }
    
    // Computed filtered insights
    const filteredInsights = computed(() => {
      let filtered = insights.value
      
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase()
        filtered = filtered.filter(insight =>
          insight.title.toLowerCase().includes(query) ||
          insight.content.toLowerCase().includes(query) ||
          (insight.category && insight.category.toLowerCase().includes(query)) ||
          (insight.tags && insight.tags.some(tag => tag.toLowerCase().includes(query)))
        )
      }
      
      if (selectedType.value) {
        filtered = filtered.filter(insight => 
          insight.insight_type === selectedType.value || insight.type === selectedType.value
        )
      }
      
      if (selectedPriority.value) {
        filtered = filtered.filter(insight => insight.priority === selectedPriority.value)
      }
      
      return filtered
    })
    
    // Helper functions
    const getInsightIcon = (type) => {
      const icons = {
        market_analysis: 'chart-bar',
        talent_strategy: 'users',
        financial_analysis: 'banknotes',
        technology_innovation: 'cpu-chip',
        strategic_planning: 'light-bulb'
      }
      return icons[type] || 'document-text'
    }
    
    const getInsightIconColor = (type) => {
      const colors = {
        market_analysis: 'text-blue-600',
        talent_strategy: 'text-purple-600',
        financial_analysis: 'text-green-600',
        technology_innovation: 'text-orange-600',
        strategic_planning: 'text-yellow-600'
      }
      return colors[type] || 'text-gray-600'
    }
    
    const getPriorityClass = (priority) => {
      const classes = {
        critical: 'bg-red-100 text-red-800',
        high: 'bg-orange-100 text-orange-800',
        medium: 'bg-yellow-100 text-yellow-800',
        low: 'bg-gray-100 text-gray-800'
      }
      return classes[priority] || classes.medium
    }
    
    const getContentPreview = (content) => {
      // Strip markdown formatting for preview
      const plainText = content.replace(/\*\*(.*?)\*\*/g, '$1')
                             .replace(/\*(.*?)\*/g, '$1')
                             .replace(/#{1,6}\s+(.*)/g, '$1')
                             .replace(/```[\s\S]*?```/g, '[Code Block]')
                             .replace(/`([^`]+)`/g, '$1')
      return plainText.substring(0, 150) + '...'
    }
    
    const renderMarkdown = (content) => {
      // Configure marked options
      marked.setOptions({
        breaks: true,
        gfm: true
      })
      return marked(content)
    }
    
    const renderMarkdownPreview = (content) => {
      // Create a limited preview with markdown rendering
      const previewLength = 200
      let truncatedContent = content
      
      // If content is longer than preview length, truncate it smartly
      if (content.length > previewLength) {
        truncatedContent = content.substring(0, previewLength)
        // Try to break at a word boundary
        const lastSpace = truncatedContent.lastIndexOf(' ')
        if (lastSpace > previewLength * 0.8) {
          truncatedContent = truncatedContent.substring(0, lastSpace)
        }
        truncatedContent += '...'
      }
      
      // Configure marked for preview (simpler output)
      marked.setOptions({
        breaks: true,
        gfm: true
      })
      
      return marked(truncatedContent)
    }
    
    const formatDate = (timestamp) => {
      return new Date(timestamp).toLocaleDateString('it-IT', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    }
    
    const formatTime = (timestamp) => {
      return new Date(timestamp).toLocaleTimeString('it-IT', {
        hour: '2-digit',
        minute: '2-digit'
      })
    }
    
    // Actions
    const viewInsight = (insight) => {
      selectedInsight.value = insight
    }
    
    const closeInsightModal = () => {
      selectedInsight.value = null
    }
    
    const exportInsight = (insight) => {
      try {
        // Create new PDF document
        const doc = new jsPDF()
        
        // Set margins and line height
        const margin = 20
        const lineHeight = 7
        const pageWidth = doc.internal.pageSize.width
        const pageHeight = doc.internal.pageSize.height
        const maxWidth = pageWidth - (margin * 2)
        let currentY = margin
        
        // Helper function to add text with automatic line breaks
        const addText = (text, fontSize = 12, fontStyle = 'normal', color = [0, 0, 0]) => {
          doc.setFontSize(fontSize)
          doc.setFont('helvetica', fontStyle)
          doc.setTextColor(color[0], color[1], color[2])
          
          const lines = doc.splitTextToSize(text, maxWidth)
          
          for (let i = 0; i < lines.length; i++) {
            // Check if we need a new page
            if (currentY + lineHeight > pageHeight - margin) {
              doc.addPage()
              currentY = margin
            }
            
            doc.text(lines[i], margin, currentY)
            currentY += lineHeight
          }
          
          currentY += 3 // Extra spacing after text block
        }
        
        // Helper function to process markdown content
        const processMarkdownToPDF = (content) => {
          // Convert markdown to plain text with some formatting
          const lines = content.split('\n')
          
          for (const line of lines) {
            if (line.trim() === '') {
              currentY += 3 // Empty line spacing
              continue
            }
            
            // Headers
            if (line.startsWith('### ')) {
              addText(line.replace('### ', ''), 14, 'bold', [0, 0, 0])
            } else if (line.startsWith('## ')) {
              addText(line.replace('## ', ''), 16, 'bold', [0, 0, 0])
            } else if (line.startsWith('# ')) {
              addText(line.replace('# ', ''), 18, 'bold', [0, 0, 0])
            }
            // Bold text (simple replacement)
            else if (line.includes('**')) {
              const text = line.replace(/\*\*(.*?)\*\*/g, '$1')
              addText(text, 12, 'bold', [0, 0, 0])
            }
            // Lists
            else if (line.startsWith('• ') || line.startsWith('- ') || /^\d+\./.test(line)) {
              addText(line, 11, 'normal', [0, 0, 0])
            }
            // Regular text
            else {
              // Remove remaining markdown
              const cleanText = line.replace(/\*\*(.*?)\*\*/g, '$1')
                                   .replace(/\*(.*?)\*/g, '$1')
                                   .replace(/`([^`]+)`/g, '$1')
              if (cleanText.trim()) {
                addText(cleanText, 12, 'normal', [0, 0, 0])
              }
            }
          }
        }
        
        // PDF Header
        addText('STRATEGIC INSIGHT REPORT', 20, 'bold', [52, 58, 64])
        currentY += 5
        
        // Insight Title
        addText(insight.title, 16, 'bold', [0, 0, 0])
        currentY += 3
        
        // Metadata
        addText(`Category: ${insight.category}`, 11, 'normal', [107, 114, 128])
        addText(`Priority: ${insight.priority.toUpperCase()}`, 11, 'normal', [107, 114, 128])
        addText(`Date: ${formatDate(insight.timestamp)}`, 11, 'normal', [107, 114, 128])
        
        if (insight.tags && insight.tags.length > 0) {
          addText(`Tags: ${insight.tags.join(', ')}`, 11, 'normal', [107, 114, 128])
        }
        
        currentY += 8
        
        // Separator line
        doc.setDrawColor(229, 231, 235)
        doc.line(margin, currentY, pageWidth - margin, currentY)
        currentY += 10
        
        // Content
        addText('CONTENT', 14, 'bold', [52, 58, 64])
        currentY += 3
        
        processMarkdownToPDF(insight.content)
        
        // Footer
        currentY += 10
        doc.setDrawColor(229, 231, 235)
        doc.line(margin, currentY, pageWidth - margin, currentY)
        currentY += 8
        
        addText('Generated by DatPortal AI Assistant', 10, 'italic', [107, 114, 128])
        addText(`Export Date: ${new Date().toLocaleDateString('it-IT')}`, 10, 'italic', [107, 114, 128])
        
        // Save the PDF
        const filename = `insight-${insight.id}-${insight.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.pdf`
        doc.save(filename)
        
        showToast('PDF esportato con successo', 'success')
        
      } catch (error) {
        console.error('Error exporting PDF:', error)
        showToast('Errore nell\'esportazione PDF', 'error')
      }
    }
    
    const deleteInsight = (insight) => {
      if (confirm(`Sei sicuro di voler eliminare "${insight.title}"? Questa azione non può essere annullata.`)) {
        // Remove from insights array
        const index = insights.value.findIndex(i => i.id === insight.id)
        if (index !== -1) {
          insights.value.splice(index, 1)
          
          // Update localStorage (remove from chat messages if needed)
          try {
            const chatMessages = localStorage.getItem('ceo-chat-messages')
            if (chatMessages) {
              const messages = JSON.parse(chatMessages)
              // Filter out messages that correspond to this insight
              const filteredMessages = messages.filter(msg => {
                // Remove messages that match this insight's timestamp or content
                return !(msg.timestamp === insight.timestamp || 
                        (msg.content && msg.content.includes(insight.title)))
              })
              localStorage.setItem('ceo-chat-messages', JSON.stringify(filteredMessages))
            }
          } catch (error) {
            console.error('Error updating localStorage:', error)
          }
          
          // Close modal if this insight was selected
          if (selectedInsight.value && selectedInsight.value.id === insight.id) {
            closeInsightModal()
          }
          
          showToast('Insight eliminato con successo', 'success')
        }
      }
    }
    
    const clearFilters = () => {
      searchQuery.value = ''
      selectedType.value = ''
      selectedPriority.value = ''
    }
    
    // Lifecycle
    onMounted(() => {
      loadInsights()
    })
    
    return {
      loading,
      insights,
      filteredInsights,
      selectedInsight,
      searchQuery,
      selectedType,
      selectedPriority,
      getInsightIcon,
      getInsightIconColor,
      getPriorityClass,
      getContentPreview,
      renderMarkdown,
      renderMarkdownPreview,
      formatDate,
      formatTime,
      viewInsight,
      closeInsightModal,
      exportInsight,
      deleteInsight,
      clearFilters
    }
  }
}
</script>

<style scoped>
.btn-primary {
  @apply bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2;
}

.btn-secondary {
  @apply bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors;
}

.btn-danger {
  @apply bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.markdown-content {
  line-height: 1.6;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  font-weight: 600;
  margin-top: 1.5rem;
  margin-bottom: 0.5rem;
}

.markdown-content h1 { font-size: 1.875rem; }
.markdown-content h2 { font-size: 1.5rem; }
.markdown-content h3 { font-size: 1.25rem; }

.markdown-content strong {
  font-weight: 600;
  color: #1f2937;
}

.markdown-content em {
  font-style: italic;
  color: #374151;
}

.markdown-content ul,
.markdown-content ol {
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

.markdown-content li {
  margin: 0.25rem 0;
}

.markdown-content p {
  margin: 0.75rem 0;
}

.markdown-content code {
  background-color: #f3f4f6;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: ui-monospace, monospace;
  font-size: 0.875rem;
}

.markdown-content pre {
  background-color: #f3f4f6;
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1rem 0;
}

.markdown-content blockquote {
  border-left: 4px solid #d1d5db;
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: #6b7280;
}

.markdown-preview {
  line-height: 1.5;
}

.markdown-preview h1,
.markdown-preview h2,
.markdown-preview h3,
.markdown-preview h4,
.markdown-preview h5,
.markdown-preview h6 {
  font-weight: 600;
  margin: 0.5rem 0 0.25rem 0;
  color: #1f2937;
}

.markdown-preview h1 { font-size: 1.1rem; }
.markdown-preview h2 { font-size: 1.05rem; }
.markdown-preview h3 { font-size: 1rem; }

.markdown-preview strong {
  font-weight: 600;
  color: #1f2937;
}

.markdown-preview em {
  font-style: italic;
  color: #374151;
}

.markdown-preview ul,
.markdown-preview ol {
  padding-left: 1.25rem;
  margin: 0.25rem 0;
}

.markdown-preview li {
  margin: 0.125rem 0;
}

.markdown-preview p {
  margin: 0.25rem 0;
}

.markdown-preview code {
  background-color: #f3f4f6;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: ui-monospace, monospace;
  font-size: 0.8rem;
}
</style>