<template>
  <div class="max-w-4xl mx-auto">
    <div v-if="loading" class="flex items-center justify-center h-64">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
    </div>

    <div v-else-if="project" class="bg-white shadow rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200">
        <h1 class="text-xl font-semibold text-gray-900">Modifica Progetto</h1>
        <p class="mt-1 text-sm text-gray-600">{{ project.name }}</p>
      </div>

      <form @submit.prevent="saveProject" class="p-6 space-y-6">
        <!-- Nome Progetto -->
        <div>
          <label for="name" class="block text-sm font-medium text-gray-700">Nome Progetto *</label>
          <input
            id="name"
            v-model="form.name"
            type="text"
            required
            data-testid="project-name"
            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
          />
        </div>

        <!-- Descrizione -->
        <div>
          <label for="description" class="block text-sm font-medium text-gray-700">Descrizione</label>
          <textarea
            id="description"
            v-model="form.description"
            rows="3"
            data-testid="project-description"
            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
          ></textarea>
        </div>

        <!-- Cliente -->
        <div>
          <label for="client_id" class="block text-sm font-medium text-gray-700">Cliente</label>
          <select
            id="client_id"
            v-model="form.client_id"
            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
          >
            <option value="">Seleziona cliente</option>
            <option v-for="client in clients" :key="client.id" :value="client.id">
              {{ client.name }}
            </option>
          </select>
        </div>

        <!-- Date -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label for="start_date" class="block text-sm font-medium text-gray-700">Data Inizio</label>
            <input
              id="start_date"
              v-model="form.start_date"
              type="date"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            />
          </div>
          <div>
            <label for="end_date" class="block text-sm font-medium text-gray-700">Data Fine</label>
            <input
              id="end_date"
              v-model="form.end_date"
              type="date"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            />
          </div>
        </div>

        <!-- Tipo Progetto e Budget -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label for="project_type" class="block text-sm font-medium text-gray-700">Tipo Progetto</label>
            <select
              id="project_type"
              v-model="form.project_type"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            >
              <option value="service">🔧 Servizio</option>
              <option value="license">📄 Licenza</option>
              <option value="consulting">💼 Consulenza</option>
              <option value="product">📦 Prodotto</option>
              <option value="rd">🔬 R&D</option>
              <option value="internal">🏢 Interno</option>
            </select>
          </div>
          <div>
            <label for="budget" class="block text-sm font-medium text-gray-700">Budget (€)</label>
            <input
              id="budget"
              v-model="form.budget"
              type="number"
              step="0.01"
              min="0"
              data-testid="project-budget"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            />
          </div>
        </div>

        <!-- Status e Fatturabile -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label for="status" class="block text-sm font-medium text-gray-700">Stato</label>
            <select
              id="status"
              v-model="form.status"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            >
              <option value="planning">📋 Pianificazione</option>
              <option value="active">🚀 Attivo</option>
              <option value="on-hold">⏸️ In Pausa</option>
              <option value="completed">✅ Completato</option>
            </select>
          </div>
          <div class="flex items-center">
            <input
              id="is_billable"
              v-model="form.is_billable"
              type="checkbox"
              class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
            <label for="is_billable" class="ml-2 block text-sm text-gray-900">
              Progetto fatturabile
            </label>
          </div>
        </div>

        <!-- Azioni -->
        <div class="flex justify-between pt-6 border-t border-gray-200">
          <button
            type="button"
            @click="deleteProject"
            class="px-4 py-2 border border-red-300 rounded-md shadow-sm text-sm font-medium text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          >
            Elimina Progetto
          </button>
          
          <div class="flex space-x-3">
            <button
              type="button"
              @click="$router.go(-1)"
              data-testid="cancel-button"
              class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Annulla
            </button>
            <button
              type="submit"
              :disabled="saving"
              data-testid="save-button"
              class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
            >
              {{ saving ? 'Salvataggio...' : 'Salva Modifiche' }}
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// State
const loading = ref(true)
const saving = ref(false)
const project = ref(null)
const clients = ref([])
const form = ref({
  name: '',
  description: '',
  client_id: '',
  start_date: '',
  end_date: '',
  project_type: 'service',
  budget: '',
  status: 'planning',
  is_billable: true
})

// Methods
const loadProject = async () => {
  try {
    const response = await fetch(`/api/projects/${route.params.id}`, {
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      }
    })
    
    if (!response.ok) {
      throw new Error('Progetto non trovato')
    }
    
    const result = await response.json()
    project.value = result.data.project
    
    // Popola il form
    form.value = {
      name: project.value.name || '',
      description: project.value.description || '',
      client_id: project.value.client_id || '',
      start_date: project.value.start_date || '',
      end_date: project.value.end_date || '',
      project_type: project.value.project_type || 'service',
      budget: project.value.budget || '',
      status: project.value.status || 'planning',
      is_billable: project.value.is_billable !== false
    }
  } catch (error) {
    console.error('Error loading project:', error)
    router.push('/app/projects')
  } finally {
    loading.value = false
  }
}

const loadClients = async () => {
  try {
    const response = await fetch('/api/clients', {
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      }
    })
    
    if (response.ok) {
      const result = await response.json()
      clients.value = result.data?.clients || []
    }
  } catch (error) {
    console.error('Error loading clients:', error)
  }
}

const saveProject = async () => {
  saving.value = true
  
  try {
    const response = await fetch(`/api/projects/${route.params.id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      },
      body: JSON.stringify(form.value)
    })

    if (!response.ok) {
      throw new Error('Errore nel salvataggio del progetto')
    }

    // Redirect al progetto
    router.push(`/app/projects/${route.params.id}`)
  } catch (error) {
    console.error('Error saving project:', error)
    alert('Errore nel salvataggio del progetto')
  } finally {
    saving.value = false
  }
}

const deleteProject = async () => {
  if (!confirm('Sei sicuro di voler eliminare questo progetto? Questa azione non può essere annullata.')) {
    return
  }
  
  try {
    const response = await fetch(`/api/projects/${route.params.id}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      }
    })

    if (!response.ok) {
      throw new Error('Errore nell\'eliminazione del progetto')
    }

    router.push('/app/projects')
  } catch (error) {
    console.error('Error deleting project:', error)
    alert('Errore nell\'eliminazione del progetto')
  }
}

// Lifecycle
onMounted(() => {
  loadProject()
  loadClients()
})
</script>
