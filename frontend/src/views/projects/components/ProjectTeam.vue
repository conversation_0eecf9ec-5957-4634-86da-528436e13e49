<template>
  <div class="space-y-6">
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">
            Team del Progetto
          </h3>
          <div class="flex items-center">
            <button
              @click="runAIAnalysis"
              :disabled="analyzingWithAI"
              class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
            >
              <HeroIcon name="sparkles" size="sm" class="mr-2" />
              {{ analyzingWithAI ? 'Analisi…' : 'Analisi AI' }}
            </button>
            <button
              @click="showAddMemberModal = true"
              data-testid="add-team-member-button"
              class="ml-3 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <HeroIcon name="plus" size="sm" class="mr-2" />
              Aggiungi Membro
            </button>
          </div>
        </div>
      </div>

      <!-- Team Statistics -->
      <div class="p-6 border-b border-gray-200 dark:border-gray-700">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div class="text-center">
            <div class="text-2xl font-bold text-primary-600 dark:text-primary-400">{{ teamMembers.length }}</div>
            <div class="text-sm text-gray-500 dark:text-gray-400">Membri Totali</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-green-600">{{ totalHoursWorked }}h</div>
            <div class="text-sm text-gray-500 dark:text-gray-400">Ore Totali</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-blue-600">{{ averageHoursPerMember }}h</div>
            <div class="text-sm text-gray-500 dark:text-gray-400">Media per Membro</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-purple-600">{{ activeMembersCount }}</div>
            <div class="text-sm text-gray-500 dark:text-gray-400">Membri Attivi</div>
          </div>
        </div>
      </div>

      <!-- AI Insights - NUOVA POSIZIONE -->
      <div v-if="aiInsights" class="p-6 border-b border-gray-200 dark:border-gray-700 bg-indigo-50 dark:bg-indigo-900/20">
        <h4 class="text-md font-medium text-gray-900 dark:text-white mb-4 flex items-center">
          <HeroIcon name="light-bulb" size="md" class="text-indigo-600 mr-2" />
          Suggerimenti AI per l'allocazione del team
        </h4>
        <ul class="space-y-2">
          <li v-for="rec in aiInsights.recommended_allocations" :key="rec.user_id" class="flex items-center justify-between p-2 rounded-md bg-white dark:bg-gray-800 shadow-sm">
            <span>{{ rec.user_name }} – {{ rec.role }} ({{ rec.allocation }}%)</span>
            <button @click="applyAIRecommendation(rec)" class="text-sm text-white bg-indigo-600 hover:bg-indigo-700 px-3 py-1 rounded-md">Applica</button>
          </li>
        </ul>
      </div>

      <!-- Team Members List -->
      <div class="p-6">
        <div class="space-y-4">
          <div
            v-for="member in teamMembers"
            :key="member.id"
            class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-all duration-200"
          >
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-4">
                <div class="flex-shrink-0">
                  <img
                    v-if="member.profile_image"
                    :src="member.profile_image"
                    :alt="member.full_name"
                    class="w-12 h-12 rounded-full"
                  >
                  <div v-else class="w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                    <span class="text-sm font-medium text-gray-600 dark:text-gray-300">{{ getInitials(member.full_name) }}</span>
                  </div>
                </div>
                <div class="flex-1">
                  <div class="flex items-center space-x-2">
                    <h4 class="text-lg font-medium text-gray-900 dark:text-white">{{ member.full_name }}</h4>
                    <span
                      v-if="member.id === project?.manager_id"
                      class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
                    >
                      Project Manager
                    </span>
                  </div>
                  <p class="text-sm text-gray-600 dark:text-gray-400">{{ member.role || 'Team Member' }}</p>
                  <p class="text-xs text-gray-500 dark:text-gray-500">{{ member.email }}</p>
                  <p v-if="member.allocation_percentage" class="text-xs text-blue-600 dark:text-blue-400 mt-1">
                    Allocazione: {{ member.allocation_percentage }}%
                  </p>
                </div>
              </div>

              <div class="flex items-center space-x-4">
                <!-- Member stats -->
                <div class="text-right">
                  <div class="text-sm font-medium text-gray-900 dark:text-white">{{ formatHours(member.hours_worked || 0) }}h</div>
                  <div class="text-xs text-gray-500 dark:text-gray-400">ore lavorate</div>
                </div>
                <div class="text-right">
                  <div class="text-sm font-medium text-gray-900 dark:text-white">{{ getAssignedTasksCount(member.id) }}</div>
                  <div class="text-xs text-gray-500 dark:text-gray-400">task assegnati</div>
                </div>
                <div class="text-right">
                  <div class="text-sm font-medium text-gray-900 dark:text-white">{{ getCompletedTasksCount(member.id) }}</div>
                  <div class="text-xs text-gray-500 dark:text-gray-400">completati</div>
                </div>

                <!-- Actions -->
                <div class="flex items-center space-x-2">
                  <button
                    @click="editMember(member)"
                    class="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                    title="Modifica membro"
                  >
                    <HeroIcon name="pencil" size="sm" />
                  </button>
                  <button
                    v-if="member.id !== project?.manager_id"
                    @click="removeMember(member)"
                    data-testid="remove-team-member-button"
                    class="p-1 text-gray-400 hover:text-red-600"
                    title="Rimuovi dal progetto"
                  >
                    <HeroIcon name="trash" size="sm" />
                  </button>
                </div>
              </div>
            </div>

            <!-- Productivity meter -->
            <div class="mt-4">
              <div class="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-1">
                <span>Produttività</span>
                <span>{{ getProductivityPercentage(member.id) }}%</span>
              </div>
              <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  class="h-2 rounded-full transition-all duration-300"
                  :class="getProductivityColor(member.id)"
                  :style="{ width: getProductivityPercentage(member.id) + '%' }"
                ></div>
              </div>
            </div>
          </div>

          <!-- Empty state -->
          <div v-if="teamMembers.length === 0" class="text-center py-8">
            <HeroIcon name="users" size="xl" class="mx-auto text-gray-400" />
            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Nessun membro del team</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Inizia aggiungendo membri al progetto.</p>
            <div class="mt-6">
              <button
                @click="showAddMemberModal = true"
                class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
              >
                <HeroIcon name="plus" size="md" class="-ml-1 mr-2" />
                Aggiungi primo membro
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Add Member Modal -->
    <div v-if="showAddMemberModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" @click="closeAddMemberModal">
      <div class="relative top-20 mx-auto p-5 border w-[500px] shadow-lg rounded-md bg-white dark:bg-gray-800" @click.stop>
        <div class="mt-3">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Aggiungi Membro al Team
          </h3>

          <form @submit.prevent="addMember">
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Utente</label>
                <select
                  v-model="newMemberForm.user_id"
                  required
                  class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                >
                  <option value="">Seleziona utente</option>
                  <option
                    v-for="user in availableUsers"
                    :key="user.id"
                    :value="user.id"
                  >
                    {{ user.full_name }} ({{ user.email }})
                  </option>
                </select>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Ruolo</label>
                <select
                  v-model="newMemberForm.role"
                  required
                  class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                >
                  <option value="">Seleziona ruolo</option>
                  <option value="Team Member">Team Member</option>
                  <option value="Developer">Developer</option>
                  <option value="Designer">Designer</option>
                  <option value="QA Tester">QA Tester</option>
                  <option value="Business Analyst">Business Analyst</option>
                  <option value="Technical Lead">Technical Lead</option>
                </select>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Percentuale di Allocazione</label>
                <input
                  v-model="newMemberForm.allocation_percentage"
                  type="number"
                  min="0"
                  max="100"
                  required
                  class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                >
              </div>
            </div>

            <div class="flex justify-end space-x-3 mt-6">
              <button
                type="button"
                @click="closeAddMemberModal"
                class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md"
              >
                Annulla
              </button>
              <button
                type="submit"
                :disabled="adding"
                class="px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50"
              >
                {{ adding ? 'Aggiungendo...' : 'Aggiungi' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Edit Member Modal -->
    <div v-if="showEditMemberModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" @click="closeEditMemberModal">
      <div class="relative top-20 mx-auto p-5 border w-[500px] shadow-lg rounded-md bg-white dark:bg-gray-800" @click.stop>
        <div class="mt-3">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Modifica Membro
          </h3>

          <form @submit.prevent="updateMember">
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Ruolo</label>
                <select
                  v-model="editingMemberForm.role"
                  required
                  class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                >
                  <option value="">Seleziona ruolo</option>
                  <option value="Team Member">Team Member</option>
                  <option value="Developer">Developer</option>
                  <option value="Designer">Designer</option>
                  <option value="QA Tester">QA Tester</option>
                  <option value="Business Analyst">Business Analyst</option>
                  <option value="Technical Lead">Technical Lead</option>
                </select>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Percentuale di Allocazione</label>
                <input
                  v-model="editingMemberForm.allocation_percentage"
                  type="number"
                  min="0"
                  max="100"
                  required
                  class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                >
              </div>
            </div>

            <div class="flex justify-end space-x-3 mt-6">
              <button
                type="button"
                @click="closeEditMemberModal"
                class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md"
              >
                Annulla
              </button>
              <button
                type="submit"
                :disabled="adding"
                class="px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50"
              >
                {{ adding ? 'Aggiornando...' : 'Aggiorna' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- AI Insights -->
    <div v-if="aiInsights" class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
      <h4 class="text-md font-medium text-gray-900 dark:text-white mb-4 flex items-center">
        <HeroIcon name="light-bulb" size="md" class="text-indigo-600 mr-2" />
        Suggerimenti AI per l'allocazione del team
      </h4>
      <ul class="space-y-2">
        <li v-for="rec in aiInsights.recommended_allocations" :key="rec.user_id" class="flex items-center justify-between">
          <span>{{ rec.user_name }} – {{ rec.role }} ({{ rec.allocation }}%)</span>
          <button @click="applyAIRecommendation(rec)" class="text-sm text-white bg-primary-600 hover:bg-primary-700 px-3 py-1 rounded-md">Applica</button>
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useAuthStore } from '@/stores/auth'
import HeroIcon from '@/components/icons/HeroIcon.vue'

const props = defineProps({
  project: {
    type: Object,
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const authStore = useAuthStore()

// State
const showAddMemberModal = ref(false)
const showEditMemberModal = ref(false)
const analyzingWithAI = ref(false)
const aiInsights = ref(null)
const availableUsers = ref([])
const adding = ref(false)
const editingMemberForm = ref({
  id: null,
  role: '',
  allocation_percentage: 100
})
const newMemberForm = ref({
  user_id: '',
  role: '',
  allocation_percentage: 100
})

// Computed
const teamMembers = computed(() => {
  return props.project?.team_members || []
})

const totalHoursWorked = computed(() => {
  return teamMembers.value.reduce((total, member) => total + (member.hours_worked || 0), 0)
})

const averageHoursPerMember = computed(() => {
  if (teamMembers.value.length === 0) return 0
  return Math.round(totalHoursWorked.value / teamMembers.value.length)
})

const activeMembersCount = computed(() => {
  return teamMembers.value.filter(member => (member.hours_worked || 0) > 0).length
})

// Methods
const getInitials = (fullName) => {
  if (!fullName) return '??'
  return fullName
    .split(' ')
    .map(name => name.charAt(0).toUpperCase())
    .slice(0, 2)
    .join('')
}

const getAssignedTasksCount = (memberId) => {
  const tasks = props.project?.tasks || []
  return tasks.filter(task => task.assignee_id === memberId).length
}

const getCompletedTasksCount = (memberId) => {
  const tasks = props.project?.tasks || []
  return tasks.filter(task => task.assignee_id === memberId && task.status === 'done').length
}

const getProductivityPercentage = (memberId) => {
  const assigned = getAssignedTasksCount(memberId)
  const completed = getCompletedTasksCount(memberId)
  if (assigned === 0) return 0
  return Math.round((completed / assigned) * 100)
}

const getProductivityColor = (memberId) => {
  const percentage = getProductivityPercentage(memberId)
  if (percentage >= 80) return 'bg-green-600'
  if (percentage >= 60) return 'bg-yellow-600'
  if (percentage >= 40) return 'bg-orange-600'
  return 'bg-red-600'
}

const formatHours = (hours) => {
  if (!hours || hours === 0) return '0.00'
  return parseFloat(hours).toFixed(2)
}

const loadAvailableUsers = async () => {
  try {
    const response = await fetch('/api/personnel/users', {
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      }
    })

    if (response.ok) {
      const result = await response.json()
      // Filtra utenti già nel team
      const currentMemberIds = teamMembers.value.map(m => m.id)
      availableUsers.value = result.data?.users ? result.data.users.filter(user => !currentMemberIds.includes(user.id)) : []
    }
  } catch (error) {
    console.error('Errore nel caricamento utenti:', error)
    availableUsers.value = []
  }
}

const addMember = async () => {
  adding.value = true

  try {
    const response = await fetch(`/api/projects/${props.project.id}/team`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      },
      body: JSON.stringify({
        user_id: newMemberForm.value.user_id,
        role: newMemberForm.value.role,
        allocation_percentage: parseInt(newMemberForm.value.allocation_percentage, 10)
      })
    })

    if (response.ok) {
      // Ricarica progetto per aggiornare team
      emit('refresh')
      closeAddMemberModal()
    } else {
      const error = await response.json()
      alert(error.message || 'Errore nell\'aggiunta del membro')
    }
  } catch (error) {
    alert('Errore nell\'aggiunta del membro')
  } finally {
    adding.value = false
  }
}

const editMember = (member) => {
  editingMemberForm.value = {
    id: member.id,
    role: member.role || '',
    allocation_percentage: member.allocation_percentage || 100
  }
  showEditMemberModal.value = true
}

const updateMember = async () => {
  if (!editingMemberForm.value.id) return
  adding.value = true
  try {
    const response = await fetch(`/api/projects/${props.project.id}/team/${editingMemberForm.value.id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      },
      body: JSON.stringify({
        role: editingMemberForm.value.role,
        allocation_percentage: parseInt(editingMemberForm.value.allocation_percentage, 10)
      })
    })
    if (response.ok) {
      emit('refresh')
      showEditMemberModal.value = false
    } else {
      const error = await response.json()
      alert(error.message || 'Errore nella modifica del membro')
    }
  } catch (error) {
    alert('Errore nella modifica del membro')
  } finally {
    adding.value = false
  }
}

const removeMember = async (member) => {
  if (!confirm(`Rimuovere ${member.full_name} dal progetto?`)) return

  try {
    const response = await fetch(`/api/projects/${props.project.id}/team/${member.id}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      }
    })

    if (response.ok) {
      emit('refresh')
    } else {
      const error = await response.json()
      alert(error.message || 'Errore nella rimozione del membro')
    }
  } catch (error) {
    alert('Errore nella rimozione del membro')
  }
}

const closeAddMemberModal = () => {
  showAddMemberModal.value = false
  newMemberForm.value = {
    user_id: '',
    role: '',
    allocation_percentage: 100
  }
}

const closeEditMemberModal = () => {
  showEditMemberModal.value = false
  editingMemberForm.value = {
    id: null,
    role: '',
    allocation_percentage: 100
  }
}

// AI analysis
const runAIAnalysis = async () => {
  if (!props.project?.id) return
  analyzingWithAI.value = true
  try {
    const response = await fetch(`/api/ai-resources/analyze-allocation/${props.project.id}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      },
      body: JSON.stringify({ include_suggestions: true, analysis_depth: 'detailed' })
    })
    if (response.ok) {
      const result = await response.json()
      aiInsights.value = result.data?.analysis || null
    }
  } finally {
    analyzingWithAI.value = false
  }
}

const applyAIRecommendation = async (rec) => {
  try {
    const response = await fetch(`/api/projects/${props.project.id}/team`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      },
      body: JSON.stringify({
        user_id: rec.user_id,
        role: rec.role,
        allocation_percentage: parseInt(rec.allocation, 10)
      })
    })
    
    if (response.ok) {
      emit('refresh')
    } else {
      const error = await response.json()
      alert(error.message || 'Errore nell\'applicazione della raccomandazione')
    }
  } catch (e) { 
    console.error(e)
    alert('Errore nell\'applicazione della raccomandazione')
  }
}

// Events
const emit = defineEmits(['refresh'])

// Lifecycle
onMounted(() => {
  loadAvailableUsers()
})

// Watchers
watch(() => showAddMemberModal.value, (newVal) => {
  if (newVal) {
    loadAvailableUsers()
  }
})

watch(() => props.project?.team_members, () => {
  if (showAddMemberModal.value) {
    loadAvailableUsers()
  }
})

// Expose methods
defineExpose({
  refresh: () => {
    loadAvailableUsers()
    emit('refresh')
  }
})
</script>