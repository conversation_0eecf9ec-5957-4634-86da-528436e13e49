<template>
  <div class="h-full flex flex-col">
    <!-- <PERSON>er -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
        Assistente HR
      </h1>
      <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
        <PERSON><PERSON> con l'assistente HR per risolvere dubbi su procedure, benefit e normative aziendali
      </p>
    </div>

    <!-- Chat Component -->
    <div class="flex-1 min-h-0">
      <HRAssistantChatComponent />
    </div>
  </div>
</template>

<script setup>
import HRAssistantChatComponent from '@/components/communication/HRAssistantChat.vue'
</script>