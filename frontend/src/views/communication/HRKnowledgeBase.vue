<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
          Knowledge Base HR
        </h1>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Gestisci contenuti per l'assistente HR
        </p>
      </div>
      
      <button
        @click="showCreateModal = true"
        class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
      >
        <HeroIcon name="plus" class="h-4 w-4 mr-2" />
        Nuovo Contenuto
      </button>
    </div>

    <!-- Filters -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Categoria
          </label>
          <select
            v-model="filters.category"
            @change="loadKnowledgeBase"
            class="w-full rounded-lg border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="">Tutte le categorie</option>
            <option 
              v-for="category in categoriesList"
              :key="category.key"
              :value="category.key"
            >
              {{ category.label }}
            </option>
          </select>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Ricerca
          </label>
          <input
            v-model="filters.search"
            @input="debounceSearch"
            type="text"
            placeholder="Cerca nei contenuti..."
            class="w-full rounded-lg border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
        </div>
        
        <div class="flex items-end">
          <button
            @click="clearFilters"
            class="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"
          >
            Pulisci Filtri
          </button>
        </div>
      </div>
    </div>

    <!-- Knowledge Base Entries -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
      <div class="divide-y divide-gray-200 dark:divide-gray-700">
        <div
          v-for="entry in knowledgeBase"
          :key="entry.id"
          class="p-6 hover:bg-gray-50 dark:hover:bg-gray-700"
        >
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <div class="flex items-center space-x-3 mb-2">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                  {{ entry.title }}
                </h3>
                
                <CategoryBadge :category="entry.category" />
                
                <div v-if="entry.created_with_ai" class="flex items-center space-x-1">
                  <HeroIcon name="cpu-chip" class="h-4 w-4 text-purple-500" />
                  <span class="text-xs text-purple-600 dark:text-purple-400">
                    AI-Generated
                  </span>
                </div>
              </div>
              
              <p class="text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
                {{ truncateContent(entry.content) }}
              </p>
              
              <div class="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                <span>
                  <HeroIcon name="user" class="h-4 w-4 inline mr-1" />
                  {{ entry.creator?.full_name }}
                </span>
                <span>
                  <HeroIcon name="calendar" class="h-4 w-4 inline mr-1" />
                  {{ formatDate(entry.created_at) }}
                </span>
                <ConfidenceBadge 
                  v-if="entry.ai_confidence"
                  :confidence="entry.ai_confidence" 
                />
              </div>
              
              <!-- Tags -->
              <div v-if="entry.tags && entry.tags.length > 0" class="mt-2">
                <div class="flex flex-wrap gap-1">
                  <span
                    v-for="tag in entry.tags"
                    :key="tag"
                    class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300"
                  >
                    {{ tag }}
                  </span>
                </div>
              </div>
            </div>
            
            <div class="flex items-center space-x-2 ml-4">
              <button
                @click="editEntry(entry)"
                class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600"
                title="Modifica"
              >
                <HeroIcon name="pencil" class="h-4 w-4" />
              </button>
              
              <button
                @click="deleteEntry(entry)"
                class="p-2 text-red-400 hover:text-red-600 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20"
                title="Elimina"
              >
                <HeroIcon name="trash" class="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Empty State -->
      <div v-if="knowledgeBase.length === 0 && !loading" class="text-center py-12">
        <HeroIcon name="document-text" class="h-16 w-16 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
          Nessun contenuto trovato
        </h3>
        <p class="text-gray-500 dark:text-gray-400 mb-6">
          Inizia creando il primo contenuto per la knowledge base HR.
        </p>
        <button
          @click="showCreateModal = true"
          class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700"
        >
          <HeroIcon name="plus" class="h-4 w-4 mr-2" />
          Crea Primo Contenuto
        </button>
      </div>
      
      <!-- Loading -->
      <div v-if="loading" class="text-center py-12">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
        <p class="mt-2 text-gray-500 dark:text-gray-400">Caricamento...</p>
      </div>
    </div>

    <!-- Pagination -->
    <Pagination
      v-if="pagination && pagination.pages > 1"
      :current-page="pagination.page"
      :total-pages="pagination.pages"
      :total-items="pagination.total"
      @page-change="handlePageChange"
    />

    <!-- Create/Edit Modal -->
    <KnowledgeBaseModal
      v-if="showCreateModal || editingEntry"
      :entry="editingEntry"
      @close="closeModal"
      @save="handleSave"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useHRAssistantStore } from '@/stores/hrAssistant'
import HeroIcon from '@/components/icons/HeroIcon.vue'
import CategoryBadge from '@/components/common/CategoryBadge.vue'
import ConfidenceBadge from '@/components/common/ConfidenceBadge.vue'
import Pagination from '@/components/ui/Pagination.vue'
import KnowledgeBaseModal from '@/components/communication/KnowledgeBaseModal.vue'

const hrAssistantStore = useHRAssistantStore()

// Local state
const showCreateModal = ref(false)
const editingEntry = ref(null)
const filters = ref({
  category: '',
  search: '',
  page: 1
})
const pagination = ref(null)

// Computed - use reactive computed from store
const knowledgeBase = computed(() => hrAssistantStore.knowledgeBase)
const categoriesList = computed(() => hrAssistantStore.categoriesList)
const loading = computed(() => hrAssistantStore.loading)

// Methods
async function loadKnowledgeBase() {
  const result = await hrAssistantStore.loadKnowledgeBase(filters.value)
  if (result.success) {
    pagination.value = result.data.pagination
  }
}

function debounceSearch() {
  clearTimeout(debounceSearch.timer)
  debounceSearch.timer = setTimeout(() => {
    filters.value.page = 1
    loadKnowledgeBase()
  }, 500)
}

function clearFilters() {
  filters.value = {
    category: '',
    search: '',
    page: 1
  }
  loadKnowledgeBase()
}

function editEntry(entry) {
  editingEntry.value = entry
}

async function deleteEntry(entry) {
  if (!confirm(`Sei sicuro di voler eliminare "${entry.title}"?`)) return
  
  try {
    const result = await hrAssistantStore.deleteKnowledgeEntry(entry.id)
    if (result.success) {
      // Nessun reload manuale, lo store si aggiorna automaticamente
    }
  } catch (error) {
    console.error('Errore eliminazione:', error)
  }
}

function closeModal() {
  showCreateModal.value = false
  editingEntry.value = null
}

async function handleSave() {
  // Nessun reload manuale, lo store si aggiorna automaticamente
  closeModal()
}

function handlePageChange(page) {
  filters.value.page = page
  loadKnowledgeBase()
}

function truncateContent(content, limit = 150) {
  if (content.length <= limit) return content
  return content.substring(0, limit) + '...'
}

function formatDate(dateString) {
  return new Date(dateString).toLocaleDateString('it-IT', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

onMounted(() => {
  loadKnowledgeBase()
})
</script>