<template>
  <div v-if="isOpen" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="card w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
      <!-- Header -->
      <div class="flex items-center justify-between p-6 border-b border dark:border-gray-600">
        <div class="flex items-center space-x-3">
          <HeroIcon name="academic-cap" size="lg" class="text-blue-600" />
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
            Dettagli Contenuto HR
          </h3>
        </div>
        <div class="flex items-center space-x-2">
          <button
            v-if="canEdit"
            @click="$emit('edit', entry)"
            class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors"
          >
            <HeroIcon name="pencil" size="md" />
          </button>
          <button
            @click="closeModal"
            class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <HeroIcon name="x-mark" size="md" />
          </button>
        </div>
      </div>

      <!-- Content -->
      <div v-if="entry" class="p-6 space-y-6">
        <!-- Title and Meta -->
        <div class="space-y-4">
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
            {{ entry.title }}
          </h1>
          
          <!-- Meta Information -->
          <div class="flex flex-wrap items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
            <div class="flex items-center space-x-1">
              <HeroIcon name="user" size="sm" />
              <span>{{ entry.creator?.full_name }}</span>
            </div>
            <div class="flex items-center space-x-1">
              <HeroIcon name="calendar" size="sm" />
              <span>{{ formatDate(entry.created_at) }}</span>
            </div>
            <div v-if="entry.updated_at && entry.updated_at !== entry.created_at" class="flex items-center space-x-1">
              <HeroIcon name="clock" size="sm" />
              <span>Modificato: {{ formatDate(entry.updated_at) }}</span>
            </div>
          </div>

          <!-- Category and AI Badges -->
          <div class="flex flex-wrap gap-2">
            <CategoryBadge :category="entry.category" />
            
            <div v-if="entry.created_with_ai" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
              <HeroIcon name="cpu-chip" size="sm" class="mr-1" />
              Generato con AI
            </div>
            
            <ConfidenceBadge 
              v-if="entry.ai_confidence"
              :confidence="entry.ai_confidence" 
            />
          </div>
        </div>

        <!-- Content -->
        <div class="prose prose-gray dark:prose-invert max-w-none">
          <div class="whitespace-pre-wrap text-gray-900 dark:text-white leading-relaxed bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            {{ entry.content }}
          </div>
        </div>

        <!-- Tags -->
        <div v-if="entry.tags && entry.tags.length > 0" class="space-y-2">
          <h4 class="text-sm font-medium text-gray-900 dark:text-white">Tags</h4>
          <div class="flex flex-wrap gap-2">
            <span
              v-for="tag in entry.tags"
              :key="tag"
              class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
            >
              {{ tag }}
            </span>
          </div>
        </div>

        <!-- AI Generation Details -->
        <div v-if="entry.created_with_ai && entry.ai_sources" class="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-700 rounded-lg p-4">
          <div class="flex items-center space-x-2 mb-3">
            <HeroIcon name="cpu-chip" class="h-5 w-5 text-purple-600 dark:text-purple-400" />
            <h4 class="text-sm font-medium text-purple-900 dark:text-purple-300">
              Dettagli Generazione AI
            </h4>
          </div>
          
          <div class="space-y-2 text-sm">
            <div class="flex items-center justify-between">
              <span class="text-purple-700 dark:text-purple-300">Livello di confidenza:</span>
              <ConfidenceBadge :confidence="entry.ai_confidence" />
            </div>
            
            <div v-if="parsedAISources.length > 0">
              <span class="text-purple-700 dark:text-purple-300 block mb-2">Fonti utilizzate:</span>
              <ul class="list-disc list-inside space-y-1 text-purple-600 dark:text-purple-400">
                <li v-for="source in parsedAISources" :key="source">
                  {{ source }}
                </li>
              </ul>
            </div>
          </div>
        </div>

        <!-- Usage Stats -->
        <div class="pt-4 border-t border dark:border-gray-600">
          <div class="grid grid-cols-2 gap-4 text-sm text-gray-500 dark:text-gray-400">
            <div class="flex items-center space-x-2">
              <HeroIcon name="folder" size="sm" />
              <span>Categoria: {{ getCategoryLabel(entry.category) }}</span>
            </div>
            <div class="flex items-center space-x-2">
              <HeroIcon name="document-text" size="sm" />
              <span>{{ entry.content.length }} caratteri</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Loading State -->
      <div v-else class="p-6 text-center">
        <HeroIcon name="cog-6-tooth" size="lg" class="animate-spin text-gray-400 mx-auto mb-2" />
        <p class="text-gray-500 dark:text-gray-400">Caricamento...</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useHRAssistantStore } from '@/stores/hrAssistant'
import { usePermissions } from '@/composables/usePermissions'
import HeroIcon from '@/components/icons/HeroIcon.vue'
import CategoryBadge from '@/components/common/CategoryBadge.vue'
import ConfidenceBadge from '@/components/common/ConfidenceBadge.vue'

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  },
  entry: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['close', 'edit'])

const hrAssistantStore = useHRAssistantStore()
const { hasPermission } = usePermissions()

const canEdit = computed(() => {
  return hasPermission.value('manage_communications')
})

const parsedAISources = computed(() => {
  if (!props.entry?.ai_sources) return []
  try {
    return JSON.parse(props.entry.ai_sources)
  } catch {
    return []
  }
})

const closeModal = () => {
  emit('close')
}

const formatDate = (dateString) => {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleDateString('it-IT', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getCategoryLabel = (category) => {
  const categoryItem = hrAssistantStore.categoriesList.find(cat => cat.key === category)
  return categoryItem ? categoryItem.label : category
}
</script>