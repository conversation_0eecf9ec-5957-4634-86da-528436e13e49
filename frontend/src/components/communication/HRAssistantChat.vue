<template>
  <div class="flex flex-col h-full bg-white dark:bg-gray-900 rounded-lg shadow-lg">
    <!-- Header -->
    <div class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
      <div class="flex items-center space-x-3">
        <div class="flex-shrink-0">
          <HeroIcon 
            name="chat-bubble-left-right" 
            class="h-8 w-8 text-blue-600 dark:text-blue-400" 
          />
        </div>
        <div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
            Assistente HR
          </h3>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            Supporto per procedure e informazioni aziendali
          </p>
        </div>
      </div>
      
      <div class="flex items-center space-x-2">
        <button
          @click="startNewSession"
          class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800"
          title="Nuova conversazione"
        >
          <HeroIcon name="plus" class="h-5 w-5" />
        </button>
        
        <button
          @click="clearConversations"
          class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800"
          title="Cancella cronologia"
        >
          <HeroIcon name="trash" class="h-5 w-5" />
        </button>
      </div>
    </div>

    <!-- Messages Area -->
    <div 
      ref="messagesContainer"
      class="flex-1 overflow-y-auto p-4 space-y-4"
    >
      <!-- Welcome Message -->
      <div v-if="currentConversation.length === 0" class="text-center py-8">
        <HeroIcon 
          name="academic-cap" 
          class="h-16 w-16 text-gray-300 dark:text-gray-600 mx-auto mb-4" 
        />
        <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
          Ciao! Sono il tuo Assistente HR
        </h4>
        <p class="text-gray-500 dark:text-gray-400 mb-6 max-w-md mx-auto">
          Posso aiutarti con informazioni su contratti, ferie, permessi, benefit e molto altro.
        </p>
        
        <!-- Quick Actions -->
        <div class="grid grid-cols-2 md:grid-cols-3 gap-3 max-w-2xl mx-auto">
          <button
            v-for="category in categoriesList.slice(0, 6)"
            :key="category.key"
            @click="sendQuickMessage(`Vorrei informazioni su ${category.label.toLowerCase()}`)"
            class="flex items-center space-x-2 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            <HeroIcon :name="category.icon" class="h-5 w-5 text-blue-600 dark:text-blue-400" />
            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
              {{ category.label }}
            </span>
          </button>
        </div>
      </div>

      <!-- Conversation Messages -->
      <div
        v-for="message in currentConversation"
        :key="message.id"
        :class="message.type === 'user' ? 'flex justify-end' : 'flex justify-start'"
      >
        <!-- User Message -->
        <div 
          v-if="message.type === 'user'"
          class="max-w-xs lg:max-w-md bg-blue-600 text-white rounded-lg p-3"
        >
          {{ message.content }}
        </div>

        <!-- Bot Message -->
        <div
          v-else
          class="max-w-xs lg:max-w-2xl bg-gray-100 dark:bg-gray-800 rounded-lg p-4"
        >
          <!-- Bot Avatar & Category -->
          <div class="flex items-center space-x-2 mb-2">
            <HeroIcon 
              name="cpu-chip" 
              class="h-5 w-5 text-blue-600 dark:text-blue-400" 
            />
            <span class="text-xs font-medium text-gray-500 dark:text-gray-400">
              {{ getCategoryLabel(message.category) }}
            </span>
            <ConfidenceBadge 
              :confidence="message.confidence" 
              class="text-xs"
            />
          </div>

          <!-- Message Content -->
          <div 
            class="prose prose-sm dark:prose-invert max-w-none"
            v-html="formatMessageContent(message.content)"
          ></div>

          <!-- Suggested Actions -->
          <div 
            v-if="message.suggestedActions && message.suggestedActions.length > 0"
            class="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600"
          >
            <p class="text-xs font-medium text-gray-500 dark:text-gray-400 mb-2">
              Azioni suggerite:
            </p>
            <div class="space-y-1">
              <button
                v-for="(action, index) in message.suggestedActions"
                :key="index"
                @click="sendQuickMessage(action)"
                class="block w-full text-left text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 p-1 rounded hover:bg-blue-50 dark:hover:bg-blue-900/20"
              >
                • {{ action }}
              </button>
            </div>
          </div>

          <!-- Feedback -->
          <div 
            v-if="message.conversationId && !message.userFeedback"
            class="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600 flex items-center space-x-2"
          >
            <span class="text-xs text-gray-500 dark:text-gray-400">Utile?</span>
            <button
              @click="provideFeedback(message.conversationId, 'helpful')"
              class="p-1 text-green-600 hover:text-green-800 hover:bg-green-50 dark:hover:bg-green-900/20 rounded"
              title="Utile"
            >
              <HeroIcon name="hand-thumb-up" class="h-4 w-4" />
            </button>
            <button
              @click="provideFeedback(message.conversationId, 'not_helpful')"
              class="p-1 text-red-600 hover:text-red-800 hover:bg-red-50 dark:hover:bg-red-900/20 rounded"
              title="Non utile"
            >
              <HeroIcon name="hand-thumb-down" class="h-4 w-4" />
            </button>
          </div>

          <!-- Feedback Received -->
          <div 
            v-if="message.userFeedback"
            class="mt-2 text-xs text-gray-500 dark:text-gray-400"
          >
            <HeroIcon 
              :name="message.userFeedback === 'helpful' ? 'check-circle' : 'x-circle'" 
              :class="message.userFeedback === 'helpful' ? 'text-green-500' : 'text-red-500'"
              class="h-4 w-4 inline mr-1" 
            />
            Feedback ricevuto
          </div>
        </div>
      </div>

      <!-- Loading Message -->
      <div v-if="loading" class="flex justify-start">
        <div class="max-w-xs bg-gray-100 dark:bg-gray-800 rounded-lg p-4">
          <div class="flex items-center space-x-2">
            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            <span class="text-sm text-gray-500 dark:text-gray-400">Sto elaborando...</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Input Area -->
    <div class="border-t border-gray-200 dark:border-gray-700 p-4">
      <form @submit.prevent="sendMessage" class="flex space-x-3">
        <div class="flex-1 relative">
          <input
            v-model="newMessage"
            type="text"
            placeholder="Scrivi la tua domanda..."
            :disabled="loading"
            class="w-full px-4 py-2 pr-12 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
          />
          
          <!-- Character Count -->
          <div class="absolute right-3 top-2 text-xs text-gray-400">
            {{ newMessage.length }}/500
          </div>
        </div>
        
        <button
          type="submit"
          :disabled="!newMessage.trim() || loading || newMessage.length > 500"
          class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          <HeroIcon 
            name="paper-airplane" 
            class="h-5 w-5" 
          />
        </button>
      </form>
      
      <!-- Error Message -->
      <div v-if="error" class="mt-2 text-sm text-red-600 dark:text-red-400">
        {{ error }}
        <button 
          @click="clearError" 
          class="ml-2 text-xs underline hover:no-underline"
        >
          Chiudi
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, nextTick, onMounted } from 'vue'
import { useHRAssistantStore } from '@/stores/hrAssistant'
import HeroIcon from '@/components/icons/HeroIcon.vue'
import ConfidenceBadge from '@/components/common/ConfidenceBadge.vue'

const hrAssistantStore = useHRAssistantStore()

// Local state
const newMessage = ref('')
const messagesContainer = ref(null)

// Computed
const { 
  currentConversation, 
  categoriesList, 
  loading, 
  error 
} = hrAssistantStore

// Methods
async function sendMessage() {
  if (!newMessage.value.trim() || loading) return

  const message = newMessage.value.trim()
  newMessage.value = ''

  const result = await hrAssistantStore.sendMessage(message)
  
  if (result.success) {
    await nextTick()
    scrollToBottom()
  }
}

async function sendQuickMessage(message) {
  newMessage.value = message
  await sendMessage()
}

function getCategoryLabel(categoryKey) {
  const category = categoriesList.find(c => c.key === categoryKey)
  return category?.label || 'Generale'
}

function formatMessageContent(content) {
  // Basic markdown to HTML conversion
  return content
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/`(.*?)`/g, '<code class="bg-gray-200 dark:bg-gray-700 px-1 rounded">$1</code>')
    .replace(/\n/g, '<br>')
}

async function provideFeedback(conversationId, feedback) {
  await hrAssistantStore.provideFeedback(conversationId, feedback)
}

function startNewSession() {
  hrAssistantStore.startNewSession()
}

function clearConversations() {
  if (confirm('Sei sicuro di voler cancellare tutta la cronologia delle conversazioni?')) {
    hrAssistantStore.clearConversations()
  }
}

function clearError() {
  hrAssistantStore.clearError()
}

function scrollToBottom() {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

onMounted(() => {
  scrollToBottom()
})
</script>