<template>
  <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white dark:bg-gray-800">
      <!-- Header -->
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white">
          {{ isEdit ? 'Modifica Contenuto' : 'Nuovo Contenuto HR' }}
        </h3>
        <button
          @click="$emit('close')"
          class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
        >
          <HeroIcon name="x-mark" class="h-6 w-6" />
        </button>
      </div>

      <!-- Form -->
      <form @submit.prevent="submitForm" class="space-y-6">
        <!-- Title -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Titolo *
          </label>
          <input
            v-model="form.title"
            type="text"
            required
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            placeholder="Inserisci il titolo del contenuto"
          />
        </div>

        <!-- Category -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Categoria *
          </label>
          <select
            v-model="form.category"
            required
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="">Seleziona categoria</option>
            <option 
              v-for="category in categoriesList"
              :key="category.key"
              :value="category.key"
            >
              {{ category.label }}
            </option>
          </select>
        </div>

        <!-- AI Assistance Toggle -->
        <div v-if="!isEdit" class="flex items-center space-x-3">
          <input
            id="useAI"
            v-model="form.useAIAssistance"
            type="checkbox"
            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label for="useAI" class="text-sm font-medium text-gray-700 dark:text-gray-300">
            Usa assistenza AI per generare contenuto
          </label>
        </div>

        <!-- AI Requirements (when AI is enabled) -->
        <div v-if="form.useAIAssistance && !isEdit">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Requisiti specifici per l'AI
          </label>
          <textarea
            v-model="form.requirements"
            rows="3"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            placeholder="Descrivi cosa deve includere il contenuto (es: policy aziendale, CCNL, procedure specifiche...)"
          ></textarea>
        </div>

        <!-- Template Selection (when AI is enabled) -->
        <div v-if="form.useAIAssistance && !isEdit && templates.length > 0">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Template (opzionale)
          </label>
          <select
            v-model="form.templateId"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="">Nessun template</option>
            <option 
              v-for="template in categoryTemplates"
              :key="template.id"
              :value="template.id"
            >
              {{ template.name }}
            </option>
          </select>
        </div>

        <!-- Content -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Contenuto *
          </label>
          <textarea
            v-model="form.content"
            :required="!form.useAIAssistance"
            rows="10"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            :placeholder="form.useAIAssistance ? 'Il contenuto verrà generato automaticamente dall\'AI' : 'Inserisci il contenuto...'"
            :readonly="form.useAIAssistance && !isEdit"
          ></textarea>
        </div>

        <!-- Tags -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Tag (separati da virgola)
          </label>
          <input
            v-model="tagsInput"
            type="text"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            placeholder="tag1, tag2, tag3"
          />
        </div>

        <!-- AI Generation Result (when AI was used) -->
        <div v-if="aiGenerationResult" class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
          <div class="flex items-center space-x-2 mb-2">
            <HeroIcon name="cpu-chip" class="h-5 w-5 text-blue-600 dark:text-blue-400" />
            <span class="text-sm font-medium text-blue-900 dark:text-blue-300">
              Contenuto generato con AI
            </span>
            <ConfidenceBadge :confidence="aiGenerationResult.confidence" />
          </div>
          
          <div v-if="aiGenerationResult.sources && aiGenerationResult.sources.length > 0" class="text-xs text-blue-700 dark:text-blue-300">
            <p class="font-medium mb-1">Fonti utilizzate:</p>
            <ul class="list-disc list-inside space-y-1">
              <li v-for="source in aiGenerationResult.sources" :key="source">
                {{ source }}
              </li>
            </ul>
          </div>
        </div>

        <!-- Actions -->
        <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-600">
          <button
            type="button"
            @click="$emit('close')"
            class="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"
          >
            Annulla
          </button>
          
          <button
            type="submit"
            :disabled="loading || (!form.content && !form.useAIAssistance)"
            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <div v-if="loading" class="flex items-center">
              <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              {{ form.useAIAssistance && !isEdit ? 'Generando...' : 'Salvando...' }}
            </div>
            <span v-else>
              {{ isEdit ? 'Aggiorna' : 'Salva' }}
            </span>
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useHRAssistantStore } from '@/stores/hrAssistant'
import HeroIcon from '@/components/icons/HeroIcon.vue'
import ConfidenceBadge from '@/components/common/ConfidenceBadge.vue'

const props = defineProps({
  entry: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['close', 'save'])

const hrAssistantStore = useHRAssistantStore()

// Local state
const loading = ref(false)
const aiGenerationResult = ref(null)
const templates = ref([])
const form = ref({
  title: '',
  category: '',
  content: '',
  useAIAssistance: false,
  requirements: '',
  templateId: null,
  tags: []
})
const tagsInput = ref('')

// Computed
const isEdit = computed(() => !!props.entry)
const { categoriesList } = hrAssistantStore

const categoryTemplates = computed(() => {
  if (!form.value.category) return []
  return templates.value.filter(t => t.category === form.value.category)
})

// Watch category changes to load templates
watch(() => form.value.category, async (newCategory) => {
  if (newCategory && form.value.useAIAssistance) {
    await loadTemplates(newCategory)
  }
})

// Methods
async function loadTemplates(category = null) {
  const result = await hrAssistantStore.loadTemplates(category)
  if (result.success) {
    templates.value = result.data
  }
}

async function submitForm() {
  loading.value = true
  
  try {
    // Parse tags
    form.value.tags = tagsInput.value
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0)

    let result
    
    if (isEdit.value) {
      // Update existing entry
      result = await hrAssistantStore.updateKnowledgeEntry(props.entry.id, {
        title: form.value.title,
        category: form.value.category,
        content: form.value.content,
        tags: form.value.tags
      })
    } else {
      // Create new entry
      if (form.value.useAIAssistance) {
        // Generate with AI
        result = await hrAssistantStore.generateAIContent({
          title: form.value.title,
          category: form.value.category,
          content: form.value.content,
          requirements: form.value.requirements,
          templateId: form.value.templateId
        })
        
        if (result.success) {
          aiGenerationResult.value = result.data
          // Update form with AI-generated content
          form.value.content = result.data.content
        }
      } else {
        // Create manually
        result = await hrAssistantStore.createKnowledgeEntry({
          title: form.value.title,
          category: form.value.category,
          content: form.value.content,
          tags: form.value.tags
        })
      }
    }
    
    if (result.success) {
      emit('save')
    }
  } catch (error) {
    console.error('Error saving knowledge entry:', error)
  } finally {
    loading.value = false
  }
}

// Initialize form data
onMounted(async () => {
  if (isEdit.value) {
    form.value = {
      title: props.entry.title,
      category: props.entry.category,
      content: props.entry.content,
      useAIAssistance: false,
      requirements: '',
      templateId: null,
      tags: props.entry.tags || []
    }
    tagsInput.value = form.value.tags.join(', ')
  }
  
  // Load templates
  await loadTemplates()
})
</script>