<template>
  <div class="mt-5 flex-grow flex flex-col overflow-hidden">
    <nav class="flex-1 px-2 pb-4 space-y-1 overflow-y-auto sidebar-scroll">
      <SidebarNavItem
        v-if="showDashboard"
        :item="{
          name: 'Dashboard',
          path: '/app/dashboard',
          icon: 'dashboard'
        }"
        :is-collapsed="isCollapsed"
        @click="$emit('item-click')"
      />

      <SidebarNavItemCollapsible
        v-if="showPersonnel"
        :item="{
          name: 'Personale',
          icon: 'users',
          children: [
            { name: 'Gestione', path: '/app/personnel/admin', icon: 'admin', admin: true },
            { name: 'Inquadramenti', path: '/app/personnel/job-levels', icon: 'user-group' },
            { name: 'Performance', path: '/app/personnel/performance', icon: 'chart-line' }
          ]
        }"
        :is-collapsed="isCollapsed"
        @click="$emit('item-click')"
      />

      <SidebarNavItemCollapsible
        v-if="showTimesheet"
        :item="{
          name: 'Attività',
          icon: 'timesheet',
          children: [
            { name: 'Dashboard', path: '/app/timesheet/dashboard', icon: 'dashboard' },
            { name: 'Le Mie Ore', path: '/app/timesheet/entry', icon: 'clock' },
            { name: 'Progetti', path: '/app/projects', icon: 'projects' },
            { name: 'Reportistica', path: '/app/timesheet/analytics', icon: 'chart-line', admin: true },
            { name: 'Richieste', path: '/app/timesheet/requests', icon: 'calendar-plus' }
          ]
        }"
        :is-collapsed="isCollapsed"
        @click="$emit('item-click')"
      />

      <SidebarNavItemCollapsible
        v-if="showSales"
        :item="{
          name: 'Sales',
          icon: 'sales',
          children: [
            { name: 'Dashboard', path: '/app/crm/dashboard', icon: 'dashboard' },
            { name: 'Clienti', path: '/app/crm/clients', icon: 'clients' },
            { name: 'Contatti', path: '/app/crm/contacts', icon: 'contact' },
            { name: 'Contratti', path: '/app/crm/contracts', icon: 'contract' },
            { name: 'Pre-Fatture', path: '/app/invoicing/pre-invoices', icon: 'invoice' },
            { name: 'Proposte', path: '/app/crm/proposals', icon: 'proposal' }
          ]
        }"
        :is-collapsed="isCollapsed"
        @click="$emit('item-click')"
      />

      <SidebarNavItemCollapsible
        v-if="showBusinessIntelligence"
        :item="{
          name: 'Business Intelligence',
          icon: 'business-intelligence',
          children: [
            { name: 'Dashboard', path: '/app/business-intelligence/dashboard', icon: 'dashboard' },
            { name: 'Case Studies', path: '/app/business-intelligence/case-studies', icon: 'case-study' },
            { name: 'Competenze Core', path: '/app/business-intelligence/core-skills', icon: 'skills' },
            { name: 'Market Intelligence', path: '/app/business-intelligence/market-intel', icon: 'market' },
            { name: 'Offerta Tecnica', path: '/app/business-intelligence/technical-offer', icon: 'technical' },
            { name: 'Reportistica', path: '/app/business-intelligence/advanced-reports', icon: 'chart-bar' }
          ]
        }"
        :is-collapsed="isCollapsed"
        @click="$emit('item-click')"
      />

      <SidebarNavItemCollapsible
        v-if="showFunding"
        :item="{
          name: 'Bandi',
          icon: 'funding',
          children: [
            { name: 'Dashboard', path: '/app/funding/dashboard', icon: 'dashboard' },
            { name: 'Ricerca', path: '/app/funding/search', icon: 'search' },
            { name: 'Rendicontazione', path: '/app/funding/reporting', icon: 'reporting' }
          ]
        }"
        :is-collapsed="isCollapsed"
        @click="$emit('item-click')"
      />

      <SidebarNavItemCollapsible
        v-if="showCommunications"
        :item="{
          name: 'Comunicazioni',
          icon: 'communications',
          children: [
            { name: 'Dashboard', path: '/app/communications/dashboard', icon: 'dashboard' },
            { name: 'Forum', path: '/app/communications/forum', icon: 'chat-bubble-left' },
            { name: 'Sondaggi', path: '/app/communications/polls', icon: 'chart-bar' },
            { name: 'Messaggi', path: '/app/communications/messages', icon: 'envelope' },
            { name: 'Eventi', path: '/app/communications/events', icon: 'calendar-days' },
            { name: 'News', path: '/app/communications/news', icon: 'newspaper' },
            { name: 'Assistente HR', path: '/app/communications/hr-assistant', icon: 'cpu-chip' },
            { name: 'Knowledge Base HR', path: '/app/communications/hr-knowledge-base', icon: 'academic-cap', admin: true }
          ]
        }"
        :is-collapsed="isCollapsed"
        @click="$emit('item-click')"
      />

      <SidebarNavItemCollapsible
        v-if="showCompliance"
        :item="{
          name: 'Certificazioni',
          icon: 'certifications',
          children: [
            { name: 'Dashboard', path: '/app/certifications/dashboard', icon: 'dashboard' },
            { name: 'Gestione', path: '/app/certifications/list', icon: 'shield-check' },
            { name: 'Catalogo Standard', path: '/app/certifications/catalog', icon: 'book-open' },
            { name: 'Valuta Readiness', path: '/app/certifications/readiness', icon: 'clipboard-document-check' }
          ]
        }"
        :is-collapsed="isCollapsed"
        @click="$emit('item-click')"
      />

      <SidebarNavItemCollapsible
        v-if="showCeo"
        :item="{
          name: 'Human CEO',
          icon: 'user-circle',
          children: [
            { name: 'Strategic Dashboard', path: '/app/ceo/dashboard', icon: 'chart-bar' },
            { name: 'AI Assistant', path: '/app/ceo/assistant', icon: 'cpu-chip' },
            { name: 'Insights & Reports', path: '/app/ceo/insights', icon: 'document-text' },
            { name: 'Research Config', path: '/app/ceo/config', icon: 'cog-6-tooth' }
          ]
        }"
        :is-collapsed="isCollapsed"
        @click="$emit('item-click')"
      />

      <SidebarNavItem
        v-if="showAgents"
        :item="{
          name: 'Agenti',
          path: '#',
          icon: 'cpu-chip'
        }"
        :is-collapsed="isCollapsed"
        @click="$emit('item-click')"
      />

      <SidebarNavItemCollapsible
        v-if="showAdminSection"
        :item="{
          name: 'Amministrazione',
          icon: 'settings',
          children: [
            { name: 'Configurazioni', path: '/app/admin/settings', icon: 'settings' },
            { name: 'Gestione Utenti', path: '/app/admin/users', icon: 'user-management' },
            { name: 'Template KPI', path: '/app/admin/kpi-templates', icon: 'reports' }
          ]
        }"
        :is-collapsed="isCollapsed"
        @click="$emit('item-click')"
      />

      <SidebarNavItemCollapsible
        v-if="showDesignSystem"
        :item="{
          name: 'Design System',
          icon: 'sparkles',
          children: [
            { name: 'Dashboard Template', path: '/app/examples/dashboard', icon: 'chart-pie' },
            { name: 'Timesheet Grid', path: '/app/examples/timesheet-grid', icon: 'table-cells' },
            { name: 'Componenti Base', path: '/app/examples/components', icon: 'squares-2x2' },
            { name: 'FormBuilder', path: '/app/examples/form-builder', icon: 'document-text' },
            { name: 'ViewModeToggle', path: '/app/examples/view-mode-toggle', icon: 'squares-plus' },
            { name: 'KanbanView', path: '/app/examples/kanban', icon: 'view-columns' },
            { name: 'ProposalCard', path: '/app/examples/proposal-card', icon: 'identification' },
            { name: 'WizardContainer', path: '/app/examples/wizard-container', icon: 'arrows-right-left' }
          ]
        }"
        :is-collapsed="isCollapsed"
        @click="$emit('item-click')"
      />
    </nav>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { usePermissions } from '@/composables/usePermissions'
import SidebarNavItem from './SidebarNavItem.vue'
import SidebarNavItemCollapsible from './SidebarNavItemCollapsible.vue'

const props = defineProps({
  isCollapsed: {
    type: Boolean,
    default: false
  }
})

defineEmits(['item-click'])

const { hasPermission, isAdmin } = usePermissions()

// Controlli di visibilità basati sui permessi
const showDashboard = computed(() => hasPermission.value('view_dashboard'))
const showPersonnel = computed(() => hasPermission.value('view_personnel'))
const showProjects = computed(() => hasPermission.value('view_all_projects'))
const showTimesheet = computed(() => hasPermission.value('manage_timesheets') || hasPermission.value('view_all_projects'))
const showSales = computed(() => hasPermission.value('view_all_projects'))
const showBusinessIntelligence = computed(() => hasPermission.value('view_products') || hasPermission.value('view_all_projects'))
const showCommunications = computed(() => hasPermission.value('view_communications'))
const showCompliance = computed(() => hasPermission.value('view_compliance'))
const showCeo = computed(() => hasPermission.value('view_ceo'))
const showAgents = computed(() => hasPermission.value('view_agents'))
const showFunding = computed(() => hasPermission.value('view_funding') || hasPermission.value('view_reports'))
const showAdminSection = computed(() => hasPermission.value('admin_access'))
const showDesignSystem = computed(() => hasPermission.value('view_dashboard')) // Mostra a tutti gli utenti autenticati
</script>

<style scoped>
/* Custom scrollbar styling */
.sidebar-scroll {
  scrollbar-width: thin;
  scrollbar-color: rgb(209 213 219 / 0.8) transparent;
}

.sidebar-scroll::-webkit-scrollbar {
  width: 6px;
}

.sidebar-scroll::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-scroll::-webkit-scrollbar-thumb {
  background-color: rgb(209 213 219 / 0.8);
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.sidebar-scroll::-webkit-scrollbar-thumb:hover {
  background-color: rgb(156 163 175 / 0.9);
}

/* Dark mode */
.dark .sidebar-scroll {
  scrollbar-color: rgb(75 85 99 / 0.8) transparent;
}

.dark .sidebar-scroll::-webkit-scrollbar-thumb {
  background-color: rgb(75 85 99 / 0.8);
}

.dark .sidebar-scroll::-webkit-scrollbar-thumb:hover {
  background-color: rgb(107 114 128 / 0.9);
}
</style>