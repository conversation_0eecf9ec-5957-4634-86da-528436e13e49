/**
 * Icon Library - Mapping dei nomi comuni alle icone Heroicons
 * 
 * Questo sistema gestisce le icone in modo centralizzato per il progetto,
 * supportando la configurazione tenant-based tramite tenant_config.json
 * 
 * UTILIZZO:
 * 1. Platform Features: Le icone vengono lette direttamente da tenant_config.json
 *    dal campo company.platform_features[].icon
 * 
 * 2. Services: Le icone vengono mappate tramite getServiceIcon() utilizzando
 *    i nomi dei servizi da company.expertise[]
 * 
 * 3. Contact: Utilizza mapping standard (map-pin, phone, envelope, clock)
 * 
 * 4. Alias: Tutti gli altri nomi vengono risolti tramite iconAliases
 */

export const iconAliases = {
  // Actions
  'add': 'plus',
  'create': 'plus',
  'new': 'plus',
  'edit': 'pencil',
  'modify': 'pencil',
  'delete': 'trash',
  'remove': 'trash',
  'save': 'check',
  'cancel': 'x-mark',
  'close': 'x-mark',
  'search': 'magnifying-glass',
  'filter': 'funnel',
  'sort': 'bars-3',
  'refresh': 'arrow-path',
  'reload': 'arrow-path',
  'download': 'arrow-down-tray',
  'upload': 'arrow-up-tray',
  'export': 'arrow-up-tray',
  'import': 'arrow-down-tray',
  
  // Navigation
  'menu': 'bars-3',
  'hamburger': 'bars-3',
  'back': 'arrow-left',
  'forward': 'arrow-right',
  'next': 'chevron-right',
  'previous': 'chevron-left',
  'prev': 'chevron-left',
  'up': 'chevron-up',
  'down': 'chevron-down',
  'expand': 'chevron-down',
  'collapse': 'chevron-up',
  'external': 'arrow-top-right-on-square',
  'external-link': 'arrow-top-right-on-square',
  
  // Status & Feedback
  'success': 'check-circle',
  'error': 'x-circle',
  'warning': 'exclamation-triangle',
  'info': 'information-circle',
  'loading': 'arrow-path',
  'spinner': 'arrow-path',
  'pending': 'clock',
  'approved': 'check-circle',
  'rejected': 'x-circle',
  'draft': 'document',
  
  // Business Objects
  'user': 'user',
  'users': 'users',
  'user-circle': 'user-circle',
  'team': 'user-group',
  'profile': 'user-circle',
  'account': 'user-circle',
  'ban': 'no-symbol',
  'block': 'no-symbol',
  'disable': 'no-symbol',
  'client': 'home-modern',
  'company': 'home-modern',
  'project': 'folder',
  'projects': 'folder',
  'task': 'clipboard',
  'tasks': 'clipboard',
  'clipboard-list': 'clipboard',
  'document': 'document',
  'file': 'document',
  'folder': 'folder',
  'calendar': 'calendar',
  'date': 'calendar',
  'time': 'clock',
  'timer': 'clock',
  'timesheet': 'clipboard',
  'report': 'presentation-chart-bar',
  'analytics': 'chart-bar',
  'dashboard': 'squares-plus',
  'settings': 'wrench-screwdriver',
  'config': 'wrench-screwdriver',
  'cog-6-tooth': 'cog-6-tooth',
  'admin': 'shield-check',
  'shield-check': 'shield-check',
  'book-open': 'book-open',
  'clipboard-document-check': 'clipboard-document-check',
  'calendar-days': 'calendar-days',
  
  // Communication
  'email': 'envelope',
  'mail': 'envelope',
  'message': 'chat-bubble-left',
  'chat': 'chat-bubble-left',
  // chat-bubble-left-right is already a valid HeroIcon name
  'hr-assistant': 'user-circle',
  'assistant': 'user-circle',
  'notification': 'bell',
  'alert': 'bell',
  'phone': 'phone',
  'call': 'phone',
  
  // Finance & Business
  'money': 'banknotes',
  'payment': 'credit-card',
  'invoice': 'document-text',
  'bill': 'document-text',
  'expense': 'credit-card',
  'budget': 'calculator',
  'price': 'currency-euro',
  'cost': 'currency-euro',
  'revenue': 'arrow-trending-up',
  'profit': 'arrow-trending-up',
  'trending-up': 'arrow-trending-up',
  
  // Funding & Grants
  'funding': 'banknotes',
  'grants': 'gift',
  'opportunity': 'light-bulb',
  'application': 'document-plus',
  'lightning-bolt': 'bolt',
  'light-bulb': 'light-bulb',
  'reporting': 'presentation-chart-bar',
  
  // Technology & AI
  'database': 'circle-stack',
  'server': 'server',
  'cloud': 'cloud',
  'api': 'code-bracket',
  'code': 'code-bracket',
  'bug': 'bug-ant',
  'security': 'shield-check',
  'lock': 'lock-closed',
  'unlock': 'lock-open',
  'key': 'key',
  'ai': 'cpu-chip',
  'agents': 'cpu-chip',
  'artificial-intelligence': 'cpu-chip',
  'automation': 'cpu-chip',
  'bot': 'cpu-chip',
  'machine-learning': 'cpu-chip',
  'brain': 'cpu-chip', // Specifico per tenant_config.json
  
  // Education & Compliance
  'education': 'academic-cap',
  'certification': 'academic-cap',
  'certifications': 'academic-cap',
  'compliance': 'academic-cap',
  'training': 'academic-cap',
  'learning': 'academic-cap',
  'course': 'academic-cap',
  'degree': 'academic-cap',
  'qualification': 'academic-cap',
  'readiness': 'clipboard-document-check',
  'audit': 'calendar-days',
  'audits': 'calendar-days',
  'catalog': 'book-open',
  'standards': 'book-open',
  'renewal': 'arrow-path',
  'health': 'heart',
  'score': 'chart-bar',
  
  // Organization & Business Structure
  'organization': 'building-office-2',
  'office': 'building-office-2',
  'building': 'building-office-2',
  'department': 'building-office-2',
  'departments': 'building-office-2',
  'ceo': 'building-office-2',
  'executive': 'building-office-2',
  'management': 'building-office-2',
  'corporate': 'building-office-2',
  'headquarters': 'building-office-2',
  
  // Design & Creativity
  'design': 'sparkles',
  'creative': 'sparkles',
  'magic': 'sparkles',
  'inspiration': 'sparkles',
  'innovation': 'sparkles',
  'style': 'sparkles',
  'theme': 'sparkles',
  
  // Media & Content
  'image': 'photo',
  'photo': 'photo',
  'video': 'video-camera',
  'camera': 'camera',
  'microphone': 'microphone',
  'speaker': 'speaker-wave',
  'volume': 'speaker-wave',
  'play': 'play',
  'pause': 'pause',
  'stop': 'stop',
  
  // UI Elements
  'home': 'home',
  'star': 'star',
  'heart': 'heart',
  'bookmark': 'bookmark',
  'tag': 'tag',
  'label': 'tag',
  'link': 'link',
  'share': 'share',
  'copy': 'document-duplicate',
  'paste': 'clipboard',
  'cut': 'scissors',
  'print': 'printer',
  'view': 'eye',
  'hide': 'eye-slash',
  'visible': 'eye',
  'invisible': 'eye-slash',
  
  // Layout & Design
  'grid': 'squares-plus',
  'list': 'list-bullet',
  'table': 'table-cells',
  'card': 'rectangle-stack',
  'sidebar': 'bars-3',
  'fullscreen': 'rectangle-group',
  'minimize': 'minus',
  'kanban': 'view-columns',
  'columns': 'view-columns',
  'board': 'view-columns',
  'proposal': 'identification',
  'badge': 'identification',
  'id-card': 'identification',
  
  // Directions & Movement
  'move': 'hand-raised',
  'drag': 'hand-raised',
  'resize': 'hand-raised',
  'rotate': 'arrow-path',
  'flip': 'arrow-path',

  // Charts & Analytics
  'chart': 'chart-bar',
  'chart-pie': 'chart-pie',
  'pie-chart': 'chart-pie',
  'analytics-pie': 'chart-pie',
  
  // Grid & Layout Systems
  'squares-2x2': 'squares-2x2',
  'grid-2x2': 'squares-2x2',
  'components': 'squares-2x2',
  'components-base': 'squares-2x2',
  
  // Icone specifiche per tenant_config.json
  'briefcase': 'briefcase',
  
  // Location & Contact
  'location': 'map-pin',
  'address': 'map-pin',
  'map': 'map',
  'pin': 'map-pin',
  'marker': 'map-pin'
}

/**
 * Resolve icon name using aliases
 * @param {string} name - Icon name or alias
 * @returns {string} - Resolved Heroicons name
 */
export function resolveIconName(name) {
  // Convert to lowercase and replace underscores with hyphens
  const normalizedName = name.toLowerCase().replace(/_/g, '-')
  
  // Return alias if exists, otherwise return normalized name
  return iconAliases[normalizedName] || normalizedName
}

/**
 * Get icon for service category (compatible with backend service categories)
 * @param {string} category - Service category
 * @returns {string} - Icon name for HeroIcon component
 */
export function getServiceIcon(category) {
  const categoryIcons = {
    'Sviluppo Software': 'code-bracket',
    'Intelligenza Artificiale': 'cpu-chip',
    'Consulenza IT': 'computer-desktop',
    'Gestione Progetti Innovativi': 'briefcase',
    'Gestione Progetti': 'briefcase',
    'Supporto su Bandi e Finanziamenti': 'banknotes',
    'Finanziamenti': 'banknotes',
    'default': 'wrench-screwdriver'
  }
  
  return categoryIcons[category] || categoryIcons['default']
}

/**
 * Get icon for platform features (from tenant_config.json)
 * @param {string} iconName - Icon name from config
 * @returns {string} - Resolved icon name for HeroIcon component
 */
export function getPlatformFeatureIcon(iconName) {
  const featureIcons = {
    'briefcase': 'briefcase',
    'users': 'users',
    'chart': 'chart-bar',
    'brain': 'cpu-chip',
    'default': 'squares-plus'
  }
  
  return featureIcons[iconName] || featureIcons['default']
}

/**
 * Get all available icon names (aliases + direct names)
 * @returns {string[]} - Array of all available icon names
 */
export function getAvailableIcons() {
  return Object.keys(iconAliases)
}

/**
 * Check if an icon name is valid
 * @param {string} name - Icon name to check
 * @returns {boolean} - Whether the icon exists
 */
export function isValidIcon(name) {
  const resolvedName = resolveIconName(name)
  // This would need to be updated with actual Heroicons list
  // For now, we assume all resolved names are valid
  return true
} 