<template>
  <span 
    :class="[
      'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
      categoryClasses
    ]"
  >
    <HeroIcon 
      :name="categoryIcon" 
      class="h-3 w-3 mr-1" 
    />
    {{ categoryLabel }}
  </span>
</template>

<script setup>
import { computed } from 'vue'
import HeroIcon from '@/components/icons/HeroIcon.vue'

const props = defineProps({
  category: {
    type: String,
    required: true
  }
})

const categoryConfig = {
  contracts: {
    label: 'Contratti',
    icon: 'document-text',
    classes: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
  },
  onboarding: {
    label: 'Onboarding',
    icon: 'user-plus',
    classes: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
  },
  offboarding: {
    label: 'Offboarding',
    icon: 'user-minus',
    classes: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
  },
  leave: {
    label: 'Ferie',
    icon: 'calendar-days',
    classes: 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400'
  },
  permits: {
    label: 'Permessi',
    icon: 'clock',
    classes: 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400'
  },
  travel: {
    label: 'Trasferte',
    icon: 'map-pin',
    classes: 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400'
  },
  benefits: {
    label: 'Benefit',
    icon: 'gift',
    classes: 'bg-pink-100 text-pink-800 dark:bg-pink-900/20 dark:text-pink-400'
  },
  tools: {
    label: 'Strumenti',
    icon: 'computer-desktop',
    classes: 'bg-cyan-100 text-cyan-800 dark:bg-cyan-900/20 dark:text-cyan-400'
  },
  purchases: {
    label: 'Acquisti',
    icon: 'shopping-cart',
    classes: 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900/20 dark:text-emerald-400'
  },
  training: {
    label: 'Formazione',
    icon: 'academic-cap',
    classes: 'bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400'
  }
}

const categoryLabel = computed(() => {
  return categoryConfig[props.category]?.label || props.category
})

const categoryIcon = computed(() => {
  return categoryConfig[props.category]?.icon || 'tag'
})

const categoryClasses = computed(() => {
  return categoryConfig[props.category]?.classes || 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
})
</script>