<template>
  <span 
    :class="[
      'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
      confidenceClasses
    ]"
  >
    <HeroIcon 
      :name="confidenceIcon" 
      class="h-3 w-3 mr-1" 
    />
    {{ confidenceLabel }}
  </span>
</template>

<script setup>
import { computed } from 'vue'
import HeroIcon from '@/components/icons/HeroIcon.vue'

const props = defineProps({
  confidence: {
    type: String,
    required: true,
    validator: (value) => ['high', 'medium', 'low'].includes(value)
  }
})

const confidenceConfig = {
  high: {
    label: 'Alta',
    icon: 'check-circle',
    classes: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
  },
  medium: {
    label: 'Media',
    icon: 'exclamation-triangle',
    classes: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
  },
  low: {
    label: 'Bassa',
    icon: 'question-mark-circle',
    classes: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
  }
}

const confidenceLabel = computed(() => {
  return confidenceConfig[props.confidence]?.label || 'Sconosciuta'
})

const confidenceIcon = computed(() => {
  return confidenceConfig[props.confidence]?.icon || 'question-mark-circle'
})

const confidenceClasses = computed(() => {
  return confidenceConfig[props.confidence]?.classes || 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
})
</script>