import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '@/utils/api'

export const useHRAssistantStore = defineStore('hrAssistant', () => {
  // State
  const conversations = ref([])
  const currentSessionId = ref(null)
  const knowledgeBase = ref([])
  const templates = ref([])
  const analytics = ref({})
  const loading = ref(false)
  const error = ref(null)
  const lastFilters = ref({})

  // Getters
  const currentConversation = computed(() => {
    return conversations.value.filter(c => c.sessionId === currentSessionId.value)
  })

  const categoriesList = computed(() => [
    { key: 'contracts', label: 'Informazioni Contrattuali', icon: 'document-text' },
    { key: 'onboarding', label: 'Procedure di Onboarding', icon: 'user-plus' },
    { key: 'offboarding', label: 'Procedure di Offboarding', icon: 'user-minus' },
    { key: 'leave', label: 'Gestione Ferie', icon: 'calendar-days' },
    { key: 'permits', label: '<PERSON><PERSON>si e Congedi', icon: 'clock' },
    { key: 'travel', label: 'Trasferte e Rimborsi', icon: 'map-pin' },
    { key: 'benefits', label: 'Benefit e Welfare', icon: 'gift' },
    { key: 'tools', label: 'Strumenti Aziendali', icon: 'computer-desktop' },
    { key: 'purchases', label: 'Richiesta Acquisti', icon: 'shopping-cart' },
    { key: 'training', label: 'Formazione e Certificazioni', icon: 'academic-cap' }
  ])

  // Actions
  async function sendMessage(message) {
    loading.value = true
    error.value = null

    try {
      // Ensure session ID
      if (!currentSessionId.value) {
        currentSessionId.value = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      }

      // Add user message to conversation
      const userMessage = {
        id: `msg_${Date.now()}`,
        type: 'user',
        content: message,
        timestamp: new Date().toISOString(),
        sessionId: currentSessionId.value
      }
      conversations.value.push(userMessage)

      // Send to backend
      const response = await api.post('/api/communication/hr-assistant/chat', {
        message,
        session_id: currentSessionId.value
      })

      if (response.data.success) {
        // Add bot response to conversation
        const botMessage = {
          id: `msg_${Date.now() + 1}`,
          type: 'bot',
          content: response.data.data.response,
          category: response.data.data.category,
          confidence: response.data.data.confidence,
          suggestedActions: response.data.data.suggested_actions || [],
          timestamp: new Date().toISOString(),
          sessionId: currentSessionId.value,
          conversationId: response.data.data.conversation_id
        }
        conversations.value.push(botMessage)

        return { success: true, data: response.data.data }
      } else {
        error.value = response.data.message
        return { success: false, error: error.value }
      }
    } catch (err) {
      error.value = err.response?.data?.message || 'Errore di connessione'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  async function provideFeedback(conversationId, feedback) {
    try {
      const response = await api.post('/api/communication/hr-assistant/feedback', {
        conversation_id: conversationId,
        feedback // 'helpful', 'not_helpful', 'neutral'
      })

      if (response.data.success) {
        // Update conversation with feedback
        const message = conversations.value.find(c => c.conversationId === conversationId)
        if (message) {
          message.userFeedback = feedback
        }
        return { success: true }
      }
    } catch (err) {
      console.error('Feedback submission failed:', err)
      return { success: false }
    }
  }

  async function loadKnowledgeBase(filters = {}) {
    loading.value = true
    error.value = null
    
    // Salva i filtri per ricaricamenti futuri
    lastFilters.value = { ...filters }
    
    try {
      const params = new URLSearchParams()
      if (filters.category) params.append('category', filters.category)
      if (filters.search) params.append('search', filters.search)
      if (filters.page) params.append('page', filters.page)

      const response = await api.get(`/api/communication/hr-assistant/knowledge-base?${params}`)

      if (response.data.success) {
        knowledgeBase.value = response.data.data.entries || []
        return {
          success: true,
          data: response.data.data
        }
      } else {
        knowledgeBase.value = []
        return { success: false, error: response.data.message }
      }
    } catch (err) {
      error.value = err.response?.data?.message || 'Errore caricamento knowledge base'
      knowledgeBase.value = []
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  async function createKnowledgeEntry(entryData) {
    loading.value = true
    try {
      const response = await api.post('/api/communication/hr-assistant/knowledge-base', entryData)

      if (response.data.success) {
        // Refresh knowledge base con gli ultimi filtri usati
        await loadKnowledgeBase(lastFilters.value)
        return { success: true, data: response.data.data }
      } else {
        error.value = response.data.message
        return { success: false, error: error.value }
      }
    } catch (err) {
      error.value = err.response?.data?.message || 'Errore creazione entry'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  async function updateKnowledgeEntry(entryId, entryData) {
    loading.value = true
    try {
      const response = await api.put(`/api/communication/hr-assistant/knowledge-base/${entryId}`, entryData)

      if (response.data.success) {
        // Refresh knowledge base con gli ultimi filtri usati
        await loadKnowledgeBase(lastFilters.value)
        return { success: true, data: response.data.data }
      } else {
        error.value = response.data.message
        return { success: false, error: error.value }
      }
    } catch (err) {
      error.value = err.response?.data?.message || 'Errore aggiornamento entry'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  async function deleteKnowledgeEntry(entryId) {
    loading.value = true
    try {
      const response = await api.delete(`/api/communication/hr-assistant/knowledge-base/${entryId}`)

      if (response.data.success) {
        // Refresh knowledge base con gli ultimi filtri usati
        await loadKnowledgeBase(lastFilters.value)
        return { success: true }
      } else {
        error.value = response.data.message
        return { success: false, error: error.value }
      }
    } catch (err) {
      error.value = err.response?.data?.message || 'Errore eliminazione entry'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  async function generateAIContent(contentParams) {
    loading.value = true
    try {
      const response = await api.post('/api/communication/hr-assistant/knowledge-base', {
        category: contentParams.category,
        title: contentParams.title,
        content: contentParams.content || '',
        requirements: contentParams.requirements || '',
        template_id: contentParams.templateId || null,
        use_ai_assistance: true
      })

      if (response.data.success) {
        return { success: true, data: response.data.data }
      } else {
        error.value = response.data.message
        return { success: false, error: error.value }
      }
    } catch (err) {
      error.value = err.response?.data?.message || 'Errore generazione contenuto'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  async function loadTemplates(category = null) {
    loading.value = true
    try {
      const params = new URLSearchParams()
      if (category) params.append('category', category)

      const response = await api.get(`/api/communication/hr-assistant/templates?${params}`)

      if (response.data.success) {
        templates.value = response.data.data
        return { success: true, data: response.data.data }
      }
    } catch (err) {
      error.value = err.response?.data?.message || 'Errore caricamento template'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  async function loadAnalytics(days = 30) {
    loading.value = true
    try {
      const response = await api.get(`/api/communication/hr-assistant/analytics?days=${days}`)

      if (response.data.success) {
        analytics.value = response.data.data
        return { success: true, data: response.data.data }
      }
    } catch (err) {
      error.value = err.response?.data?.message || 'Errore caricamento analytics'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  function startNewSession() {
    currentSessionId.value = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    // Keep conversations for history but mark new session
  }

  function clearConversations() {
    conversations.value = []
    currentSessionId.value = null
  }

  function clearError() {
    error.value = null
  }

  return {
    // State
    conversations,
    currentSessionId,
    knowledgeBase,
    templates,
    analytics,
    loading,
    error,
    lastFilters,

    // Getters
    currentConversation,
    categoriesList,

    // Actions
    sendMessage,
    provideFeedback,
    loadKnowledgeBase,
    createKnowledgeEntry,
    updateKnowledgeEntry,
    deleteKnowledgeEntry,
    generateAIContent,
    loadTemplates,
    loadAnalytics,
    startNewSession,
    clearConversations,
    clearError
  }
})