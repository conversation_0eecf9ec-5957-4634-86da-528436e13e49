import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

// Layout components
import AppLayout from '@/components/layout/AppLayout.vue'
import PublicLayout from '@/components/layout/PublicLayout.vue'

// Public views (lazy loading)
const Home = () => import('@/views/public/Home.vue')
const About = () => import('@/views/public/About.vue')
const Contact = () => import('@/views/public/Contact.vue')
const Services = () => import('@/views/public/Services.vue')
const Privacy = () => import('@/views/public/Privacy.vue')
const CookiePolicy = () => import('@/views/public/CookiePolicy.vue')

// Auth views (lazy loading)
const Login = () => import('@/views/auth/Login.vue')
const Register = () => import('@/views/auth/Register.vue')

// Protected views (lazy loading)
const Dashboard = () => import('@/views/dashboard/Dashboard.vue')
const Projects = () => import('@/views/projects/Projects.vue')

const routes = [
  // Public routes
  {
    path: '/',
    component: PublicLayout,
    children: [
      { path: '', name: 'home', component: Home },
      { path: 'about', name: 'about', component: About },
      { path: 'contact', name: 'contact', component: Contact },
      { path: 'services', name: 'services', component: Services },
      { path: 'privacy', name: 'privacy', component: Privacy },
      { path: 'cookie-policy', name: 'cookie-policy', component: CookiePolicy }
    ]
  },

  // Auth routes
  {
    path: '/auth',
    component: PublicLayout,
    children: [
      { path: 'login', name: 'login', component: Login },
      { path: 'register', name: 'register', component: Register }
    ]
  },

  // Protected routes
  {
    path: '/app',
    component: AppLayout,
    meta: { requiresAuth: true },
    children: [
      { path: '', redirect: '/app/dashboard' },
      {
        path: 'dashboard',
        name: 'dashboard',
        component: Dashboard,
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_dashboard'
        }
      },
      {
        path: 'projects',
        name: 'projects',
        component: Projects,
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_all_projects'
        }
      },
      {
        path: 'projects/create',
        name: 'projects-create',
        component: () => import('@/views/projects/ProjectCreate.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'edit_project'
        }
      },
      {
        path: 'projects/:id',
        name: 'project-view',
        component: () => import('@/views/projects/ProjectView.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_all_projects'
        }
      },
      {
        path: 'projects/:id/edit',
        name: 'project-edit',
        component: () => import('@/views/projects/ProjectEdit.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'edit_project'
        }
      },
      // Timesheet routes
      {
        path: 'timesheet',
        redirect: '/app/timesheet/entry'
      },
      {
        path: 'timesheet/entry',
        name: 'timesheet-entry',
        component: () => import('@/views/timesheet/TimesheetEntry.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'manage_timesheets'
        }
      },
      {
        path: 'timesheet/requests',
        name: 'timesheet-requests',
        component: () => import('@/views/timesheet/TimesheetRequests.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'manage_timesheets'
        }
      },
      {
        path: 'timesheet/dashboard',
        name: 'timesheet-dashboard',
        component: () => import('@/views/timesheet/TimesheetDashboard.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_all_projects'
        }
      },

      {
        path: 'timesheet/analytics',
        name: 'timesheet-analytics',
        component: () => import('@/views/timesheet/TimesheetAnalytics.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'admin_access'
        }
      },

      // Communication routes
      {
        path: 'communications',
        redirect: '/app/communications/dashboard'
      },
      {
        path: 'communications/dashboard',
        name: 'communications-dashboard',
        component: () => import('@/views/communication/CommunicationDashboard.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_communications'
        }
      },
      {
        path: 'communications/forum',
        name: 'communications-forum',
        component: () => import('@/views/communication/ForumIndex.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_communications'
        }
      },
      {
        path: 'communications/forum/topics/:id',
        name: 'communications-topic',
        component: () => import('@/views/communication/TopicView.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_communications'
        }
      },
      {
        path: 'communications/polls',
        name: 'communications-polls',
        component: () => import('@/views/communication/PollsIndex.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_communications'
        }
      },
      {
        path: 'communications/polls/:id',
        name: 'communications-poll',
        component: () => import('@/views/communication/PollView.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_communications'
        }
      },
      {
        path: 'communications/messages',
        name: 'communications-messages',
        component: () => import('@/views/communication/MessagesIndex.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_communications'
        }
      },
      {
        path: 'communications/events',
        name: 'communications-events',
        component: () => import('@/views/communication/EventsIndex.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_communications'
        }
      },
      {
        path: 'communications/events/:id',
        name: 'communications-event-view',
        component: () => import('@/views/communication/EventView.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_communications'
        }
      },
      {
        path: 'communications/news',
        name: 'communications-news',
        component: () => import('@/views/communication/NewsIndex.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_communications'
        }
      },
      {
        path: 'communications/news/:id',
        name: 'communications-news-view',
        component: () => import('@/views/communication/NewsView.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_communications'
        }
      },

      // HR Assistant routes
      {
        path: 'communications/hr-assistant',
        name: 'hr-assistant-chat',
        component: () => import('@/views/communication/HRAssistantChat.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_communications'
        }
      },
      {
        path: 'communications/hr-knowledge-base',
        name: 'hr-knowledge-base',
        component: () => import('@/views/communication/HRKnowledgeBase.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_communications'
        }
      },

      // Design System Examples
      {
        path: 'examples/dashboard',
        name: 'dashboard-example',
        component: () => import('@/components/design-system/templates/examples/DashboardExample.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_dashboard'
        }
      },
      {
        path: 'examples/timesheet-grid',
        name: 'timesheet-grid-example',
        component: () => import('@/components/design-system/grids/examples/TimesheetGridExample.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_dashboard'
        }
      },
      {
        path: 'examples/components',
        name: 'components-example',
        component: () => import('@/components/design-system/examples/ComponentsExample.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_dashboard'
        }
      },
      {
        path: 'examples/form-builder',
        name: 'form-builder-example',
        component: () => import('@/components/design-system/examples/FormBuilderExample.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_dashboard'
        }
      },
      {
        path: 'examples/view-mode-toggle',
        name: 'view-mode-toggle-example',
        component: () => import('@/components/design-system/examples/ViewModeToggleExample.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_dashboard'
        }
      },
      {
        path: 'examples/kanban',
        name: 'kanban-example',
        component: () => import('@/components/design-system/examples/KanbanExample.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_dashboard'
        }
      },
      {
        path: 'examples/proposal-card',
        name: 'proposal-card-example',
        component: () => import('@/components/design-system/examples/ProposalCardExample.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_dashboard'
        }
      },
      {
        path: 'examples/icon-system',
        name: 'icon-system-example',
        component: () => import('@/components/design-system/examples/IconSystemExample.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_dashboard'
        }
      },
      {
        path: 'examples/wizard-container',
        name: 'wizard-container-example',
        component: () => import('@/components/design-system/examples/WizardContainerExample.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_dashboard'
        }
      },
      // Personnel routes
      {
        path: 'personnel',
        redirect: '/app/personnel/admin'
      },
      {
        path: 'personnel/orgchart',
        name: 'personnel-orgchart',
        component: () => import('@/views/personnel/PersonnelOrgChart.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_personnel_data'
        }
      },
      {
        path: 'personnel/skills',
        name: 'personnel-skills',
        component: () => import('@/views/personnel/SkillsMatrix.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_personnel_data'
        }
      },
      {
        path: 'personnel/job-levels',
        name: 'personnel-job-levels',
        component: () => import('@/views/personnel/JobLevels.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_personnel_data'
        }
      },
      {
        path: 'personnel/inquadramenti',
        redirect: '/app/personnel/job-levels'
      },
      {
        path: 'personnel/departments',
        redirect: '/app/personnel/admin'
      },
      {
        path: 'personnel/departments/create',
        name: 'department-create',
        component: () => import('@/views/personnel/DepartmentCreate.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'manage_users'
        }
      },
      {
        path: 'personnel/departments/:id',
        name: 'department-view',
        component: () => import('@/views/personnel/DepartmentView.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_personnel_data'
        }
      },
      {
        path: 'personnel/departments/:id/edit',
        name: 'department-edit',
        component: () => import('@/views/personnel/DepartmentEdit.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'manage_users'
        }
      },
      {
        path: 'personnel/allocation',
        name: 'personnel-allocation',
        component: () => import('@/views/personnel/PersonnelAllocation.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_personnel_data'
        }
      },
      {
        path: 'personnel/admin',
        name: 'personnel-admin',
        component: () => import('@/views/personnel/PersonnelAdmin.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'admin_access'
        }
      },
      {
        path: 'personnel/performance',
        name: 'personnel-performance',
        component: () => import('@/views/personnel/PersonnelPerformance.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_personnel_data'
        }
      },
      {
        path: 'personnel/:id',
        name: 'personnel-profile',
        component: () => import('@/views/personnel/PersonnelProfile.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_personnel_data'
        }
      },
      {
        path: 'admin',
        redirect: '/app/admin/users'
      },
      {
        path: 'admin/users',
        name: 'admin-users',
        component: () => import('@/views/admin/Admin.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'admin_access'
        }
      },
      {
        path: 'admin/settings',
        name: 'admin-settings',
        component: () => import('@/views/admin/AdminSettings.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'admin_access'
        }
      },
      {
        path: 'admin/kpi-templates',
        name: 'admin-kpi-templates',
        component: () => import('@/views/admin/KPITemplates.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'admin_access'
        }
      },
      {
        path: 'profile',
        name: 'profile',
        component: () => import('@/views/user/Profile.vue'),
        meta: {
          requiresAuth: true
        }
      },
      {
        path: 'settings',
        name: 'settings',
        component: () => import('@/views/user/Settings.vue'),
        meta: {
          requiresAuth: true
        }
      },
      
      // CRM routes
      {
        path: 'crm',
        redirect: '/app/crm/dashboard'
      },
      {
        path: 'crm/dashboard',
        name: 'crm-dashboard',
        component: () => import('@/views/crm/CRMDashboard.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_all_projects'
        }
      },
      {
        path: 'crm/clients',
        name: 'crm-clients',
        component: () => import('@/views/crm/clients/ClientsList.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_all_projects'
        }
      },
      {
        path: 'crm/clients/create',
        name: 'crm-clients-create',
        component: () => import('@/views/crm/clients/ClientForm.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'edit_project'
        }
      },
      {
        path: 'crm/clients/:id',
        name: 'crm-client-view',
        component: () => import('@/views/crm/clients/ClientView.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_all_projects'
        }
      },
      {
        path: 'crm/clients/:id/edit',
        name: 'crm-client-edit',
        component: () => import('@/views/crm/clients/ClientForm.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'edit_project'
        }
      },
      {
        path: 'crm/contacts',
        name: 'crm-contacts',
        component: () => import('@/views/crm/contacts/ContactsList.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_all_projects'
        }
      },
      {
        path: 'crm/proposals',
        name: 'crm-proposals',
        component: () => import('@/views/crm/proposals/ProposalsPipeline.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_all_projects'
        }
      },
      {
        path: 'crm/proposals/new',
        name: 'crm-proposals-new',
        component: () => import('@/views/crm/proposals/ProposalForm.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'edit_project'
        }
      },
      {
        path: 'crm/proposals/create',
        name: 'crm-proposals-create',
        component: () => import('@/views/crm/proposals/ProposalForm.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'edit_project'
        }
      },
      {
        path: 'crm/proposals/:id',
        name: 'crm-proposal-view',
        component: () => import('@/views/crm/proposals/ProposalView.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_all_projects'
        }
      },
      {
        path: 'crm/proposals/:id/edit',
        name: 'crm-proposal-edit',
        component: () => import('@/views/crm/proposals/ProposalForm.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'edit_project'
        }
      },
      {
        path: 'crm/contracts',
        name: 'crm-contracts',
        component: () => import('@/views/crm/contracts/ContractsList.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_all_projects'
        }
      },
      {
        path: 'crm/contracts/new',
        name: 'crm-contract-create',
        component: () => import('@/views/crm/contracts/ContractForm.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'manage_contracts'
        }
      },
      {
        path: 'crm/contracts/:id/edit',
        name: 'crm-contract-edit',
        component: () => import('@/views/crm/contracts/ContractForm.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'manage_contracts'
        }
      },
      {
        path: 'crm/contracts/:id',
        name: 'crm-contract-view',
        component: () => import('@/views/crm/contracts/ContractView.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_all_projects'
        }
      },
      
      // Invoicing routes
      {
        path: 'invoicing',
        redirect: '/app/invoicing/pre-invoices'
      },
      {
        path: 'invoicing/pre-invoices',
        name: 'invoicing-pre-invoices',
        component: () => import('@/views/invoicing/PreInvoicesList.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_all_invoices'
        }
      },
      {
        path: 'invoicing/pre-invoices/new',
        name: 'invoicing-pre-invoice-create',
        component: () => import('@/views/invoicing/PreInvoiceForm.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'manage_invoices'
        }
      },
      {
        path: 'invoicing/pre-invoices/:id',
        name: 'invoicing-pre-invoice-view',
        component: () => import('@/views/invoicing/PreInvoiceView.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_all_invoices'
        }
      },

      // Business Intelligence routes
      {
        path: 'business-intelligence',
        redirect: '/app/business-intelligence/dashboard'
      },
      {
        path: 'business-intelligence/dashboard',
        name: 'business-intelligence-dashboard',
        component: () => import('@/views/business-intelligence/BIDashboard.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_all_projects'
        }
      },
      {
        path: 'business-intelligence/case-studies',
        name: 'business-intelligence-case-studies',
        component: () => import('@/views/business-intelligence/CaseStudies.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_all_projects'
        }
      },
      {
        path: 'business-intelligence/core-skills',
        name: 'business-intelligence-core-skills',
        component: () => import('@/views/business-intelligence/CoreSkills.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_personnel_data'
        }
      },
      {
        path: 'business-intelligence/technical-offer',
        name: 'business-intelligence-technical-offer',
        component: () => import('@/views/business-intelligence/TechnicalOffer.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_all_projects'
        }
      },
      {
        path: 'business-intelligence/market-intel',
        name: 'business-intelligence-market-intel',
        component: () => import('@/views/business-intelligence/MarketIntelligence.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_all_projects'
        }
      },
      {
        path: 'business-intelligence/advanced-reports',
        name: 'business-intelligence-advanced-reports',
        component: () => import('@/views/business-intelligence/AdvancedReports.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_reports'
        }
      },
      
      // Certification routes
      {
        path: 'certifications',
        redirect: '/app/certifications/dashboard'
      },
      {
        path: 'certifications/dashboard',
        name: 'certifications-dashboard',
        component: () => import('@/views/certifications/CertificationsDashboard.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_compliance'
        }
      },
      {
        path: 'certifications/list',
        name: 'certifications-list',
        component: () => import('@/views/certifications/CertificationsList.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_compliance'
        }
      },
      {
        path: 'certifications/catalog',
        name: 'certifications-catalog',
        component: () => import('@/views/certifications/CertificationsCatalog.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_compliance'
        }
      },
      {
        path: 'certifications/create',
        name: 'certifications-create',
        component: () => import('@/views/certifications/CertificationCreate.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_compliance'
        }
      },
      {
        path: 'certifications/:id',
        name: 'certification-view',
        component: () => import('@/views/certifications/CertificationView.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_compliance'
        }
      },
      {
        path: 'certifications/:id/edit',
        name: 'certification-edit',
        component: () => import('@/views/certifications/CertificationEdit.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_compliance'
        }
      },
      {
        path: 'certifications/readiness',
        name: 'certifications-readiness',
        component: () => import('@/views/certifications/CertificationReadiness.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_compliance'
        }
      },
      
      // Human CEO routes
      {
        path: 'ceo',
        redirect: '/app/ceo/dashboard'
      },
      {
        path: 'ceo/dashboard',
        name: 'ceo-dashboard',
        component: () => import('@/views/ceo/CEODashboard.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_ceo'
        }
      },
      {
        path: 'ceo/assistant',
        name: 'ceo-assistant',
        component: () => import('@/views/ceo/AIAssistant.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_ceo'
        }
      },
      {
        path: 'ceo/insights',
        name: 'ceo-insights',
        component: () => import('@/views/ceo/InsightsReports.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_ceo'
        }
      },
      {
        path: 'ceo/config',
        name: 'ceo-config',
        component: () => import('@/views/ceo/ResearchConfig.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_ceo'
        }
      },
      
      // Funding routes
      {
        path: 'funding',
        redirect: '/app/funding/dashboard'
      },
      {
        path: 'funding/dashboard',
        name: 'funding-dashboard',
        component: () => import('@/views/funding/FundingDashboard.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_all_projects'
        }
      },
      {
        path: 'funding/search',
        name: 'funding-search',
        component: () => import('@/views/funding/FundingSearch.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_all_projects'
        }
      },
      {
        path: 'funding/opportunities/:id',
        name: 'funding-opportunity-view',
        component: () => import('@/views/funding/FundingOpportunityView.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_all_projects'
        }
      },
      {
        path: 'funding/applications/:id',
        name: 'funding-application-view',
        component: () => import('@/views/funding/FundingApplicationView.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_all_projects'
        }
      },
      {
        path: 'funding/reporting',
        name: 'funding-reporting',
        component: () => import('@/views/funding/FundingReporting.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_all_projects'
        }
      },
      {
        path: 'funding/applications/new',
        name: 'funding-application-form',
        component: () => import('@/views/funding/FundingApplicationForm.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_all_projects'
        }
      },
      {
        path: 'funding/applications/new/:opportunityId',
        name: 'funding-application-form-with-opportunity',
        component: () => import('@/views/funding/FundingApplicationForm.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_all_projects'
        }
      },
      {
        path: 'funding/expenses/new',
        name: 'funding-expense-form',
        component: () => import('@/views/funding/FundingExpenseForm.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_all_projects'
        }
      },
      {
        path: 'funding/expenses/:id',
        name: 'funding-expense-view',
        component: () => import('@/views/funding/FundingExpenseView.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_all_projects'
        }
      },
      {
        path: 'funding/expenses/:id/edit',
        name: 'funding-expense-edit',
        component: () => import('@/views/funding/FundingExpenseForm.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_all_projects'
        }
      },
      
      // Test routes for Design System
      {
        path: 'test/design-system',
        name: 'test-design-system',
        component: () => import('@/views/test/DesignSystemTest.vue'),
        meta: {
          requiresAuth: true
        }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// Navigation guard per autenticazione e autorizzazioni
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  if (to.meta.requiresAuth) {
    // Controlla l'autenticazione se non è già stata verificata
    if (!authStore.sessionChecked) {
      await authStore.initializeAuth()
    }

    if (!authStore.isAuthenticated) {
      next('/auth/login')
      return
    }

    // Controllo permessi se specificato nella route
    if (to.meta.requiredPermission) {
      if (!authStore.hasPermission(to.meta.requiredPermission)) {
        // Redirect a pagina di accesso negato o dashboard
        console.warn(`Accesso negato a ${to.path}: permesso '${to.meta.requiredPermission}' richiesto`)
        next('/app/dashboard')
        return
      }
    }
  }

  next()
})

export default router