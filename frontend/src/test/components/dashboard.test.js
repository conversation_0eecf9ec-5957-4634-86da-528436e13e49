import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mountComponent, mockApiResponse, mockUser, mockProject } from '../utils/test-helpers.js'
import Dashboard from '@/views/Dashboard.vue'
import { useDashboardStore } from '@/stores/dashboard'
import { useAuthStore } from '@/stores/auth'

// Mock stores
vi.mock('@/stores/dashboard', () => ({
  useDashboardStore: vi.fn()
}))

vi.mock('@/stores/auth', () => ({
  useAuthStore: vi.fn()
}))

// Mock API
global.fetch = vi.fn()

describe('Dashboard Components', () => {
  let mockDashboardStore
  let mockAuthStore

  beforeEach(() => {
    vi.clearAllMocks()
    
    // Setup mock dashboard store
    mockDashboardStore = {
      stats: {
        totalProjects: 10,
        activeProjects: 7,
        completedTasks: 45,
        pendingTasks: 12,
        totalHours: 320,
        thisWeekHours: 40
      },
      recentActivities: [
        {
          id: 1,
          type: 'project_created',
          message: 'New project "Test Project" created',
          timestamp: '2025-06-26T10:00:00Z',
          user: '<PERSON>'
        },
        {
          id: 2,
          type: 'task_completed',
          message: 'Task "Setup database" completed',
          timestamp: '2025-06-26T09:30:00Z',
          user: 'Jane Smith'
        }
      ],
      upcomingDeadlines: [
        {
          id: 1,
          title: 'Project Alpha Milestone',
          dueDate: '2025-06-30',
          type: 'project',
          priority: 'high'
        }
      ],
      loading: false,
      error: null,
      fetchDashboardData: vi.fn(),
      refreshStats: vi.fn()
    }
    
    // Setup mock auth store
    mockAuthStore = {
      user: mockUser,
      isAuthenticated: true,
      hasPermission: vi.fn(() => true)
    }
    
    useDashboardStore.mockReturnValue(mockDashboardStore)
    useAuthStore.mockReturnValue(mockAuthStore)
    
    fetch.mockClear()
  })

  describe('Dashboard Component', () => {
    it('should render dashboard with stats', () => {
      const wrapper = mountComponent(Dashboard)
      
      // Check if stats are displayed
      expect(wrapper.text()).toContain('10') // totalProjects
      expect(wrapper.text()).toContain('7')  // activeProjects
      expect(wrapper.text()).toContain('45') // completedTasks
      expect(wrapper.text()).toContain('320') // totalHours
    })

    it('should display welcome message with user name', () => {
      const wrapper = mountComponent(Dashboard)
      
      expect(wrapper.text()).toContain(`Welcome back, ${mockUser.first_name}`)
    })

    it('should show recent activities', () => {
      const wrapper = mountComponent(Dashboard)
      
      expect(wrapper.text()).toContain('New project "Test Project" created')
      expect(wrapper.text()).toContain('Task "Setup database" completed')
    })

    it('should show upcoming deadlines', () => {
      const wrapper = mountComponent(Dashboard)
      
      expect(wrapper.text()).toContain('Project Alpha Milestone')
      expect(wrapper.text()).toContain('2025-06-30')
    })

    it('should show loading state', () => {
      mockDashboardStore.loading = true
      
      const wrapper = mountComponent(Dashboard)
      
      expect(wrapper.find('[data-testid="loading-spinner"]').exists()).toBe(true)
    })

    it('should show error state', () => {
      mockDashboardStore.error = 'Failed to load dashboard data'
      
      const wrapper = mountComponent(Dashboard)
      
      expect(wrapper.text()).toContain('Failed to load dashboard data')
    })

    it('should call fetchDashboardData on mount', () => {
      mountComponent(Dashboard)
      
      expect(mockDashboardStore.fetchDashboardData).toHaveBeenCalled()
    })

    it('should refresh data when refresh button is clicked', async () => {
      const wrapper = mountComponent(Dashboard)
      
      const refreshButton = wrapper.find('[data-testid="refresh-button"]')
      if (refreshButton.exists()) {
        await refreshButton.trigger('click')
        expect(mockDashboardStore.refreshStats).toHaveBeenCalled()
      }
    })

    it('should hide admin sections for non-admin users', () => {
      mockAuthStore.hasPermission.mockReturnValue(false)
      
      const wrapper = mountComponent(Dashboard)
      
      expect(wrapper.find('[data-testid="admin-section"]').exists()).toBe(false)
    })

    it('should show admin sections for admin users', () => {
      mockAuthStore.hasPermission.mockReturnValue(true)
      
      const wrapper = mountComponent(Dashboard)
      
      expect(wrapper.find('[data-testid="admin-section"]').exists()).toBe(true)
    })
  })

  describe('Dashboard Store', () => {
    it('should fetch dashboard data successfully', async () => {
      const mockStats = {
        totalProjects: 15,
        activeProjects: 10,
        completedTasks: 50,
        pendingTasks: 8
      }
      
      fetch.mockResolvedValueOnce(mockApiResponse({
        success: true,
        data: mockStats
      }))

      const { useDashboardStore } = await import('@/stores/dashboard')
      const store = useDashboardStore()
      
      await store.fetchDashboardData()

      expect(store.stats).toEqual(mockStats)
      expect(store.loading).toBe(false)
      expect(store.error).toBe(null)
    })

    it('should handle fetch error', async () => {
      fetch.mockRejectedValueOnce(new Error('Network error'))

      const { useDashboardStore } = await import('@/stores/dashboard')
      const store = useDashboardStore()
      
      await store.fetchDashboardData()

      expect(store.loading).toBe(false)
      expect(store.error).toBe('Failed to load dashboard data')
    })

    it('should refresh stats', async () => {
      fetch.mockResolvedValueOnce(mockApiResponse({
        success: true,
        data: { totalProjects: 20 }
      }))

      const { useDashboardStore } = await import('@/stores/dashboard')
      const store = useDashboardStore()
      
      await store.refreshStats()

      expect(fetch).toHaveBeenCalledWith('/api/dashboard/stats')
    })
  })

  describe('Dashboard Widgets', () => {
    it('should render stats cards with correct values', () => {
      const wrapper = mountComponent(Dashboard)
      
      const statsCards = wrapper.findAll('[data-testid="stats-card"]')
      expect(statsCards.length).toBeGreaterThan(0)
      
      // Check if each stat is displayed
      expect(wrapper.text()).toContain('Total Projects')
      expect(wrapper.text()).toContain('Active Projects')
      expect(wrapper.text()).toContain('Completed Tasks')
    })

    it('should format numbers correctly', () => {
      mockDashboardStore.stats.totalHours = 1234.5
      
      const wrapper = mountComponent(Dashboard)
      
      // Should format large numbers with commas
      expect(wrapper.text()).toContain('1,234.5')
    })

    it('should show percentage changes when available', () => {
      mockDashboardStore.stats.projectsChange = 15.5
      
      const wrapper = mountComponent(Dashboard)
      
      expect(wrapper.text()).toContain('+15.5%')
    })
  })
})
