import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia } from 'pinia'
import ProjectTeam from '@/views/projects/components/ProjectTeam.vue'
import { useAuthStore } from '@/stores/auth'

// Mock the auth store
vi.mock('@/stores/auth', () => ({
  useAuthStore: vi.fn()
}))

describe('ProjectTeam Component', () => {
  let wrapper
  let mockAuthStore
  let mockProject

  beforeEach(() => {
    // Setup mock auth store
    mockAuthStore = {
      user: { id: 1, role: 'admin' },
      csrfToken: 'mock-csrf-token',
      hasPermission: vi.fn(() => true)
    }
    useAuthStore.mockReturnValue(mockAuthStore)

    // Mock project data
    mockProject = {
      id: 1,
      name: 'Test Project',
      team_members: [
        {
          id: 1,
          full_name: '<PERSON>',
          email: '<EMAIL>',
          role: 'Project Manager',
          allocation_percentage: 50,
          hours_worked: 40
        },
        {
          id: 2,
          full_name: '<PERSON>', 
          email: '<EMAIL>',
          role: 'Developer',
          allocation_percentage: 100,
          hours_worked: 80
        }
      ]
    }

    // Mock fetch globally
    global.fetch = vi.fn()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Component Rendering', () => {
    it('should render team members correctly', () => {
      wrapper = mount(ProjectTeam, {
        props: { project: mockProject },
        global: {
          plugins: [createPinia()],
          stubs: ['HeroIcon']
        }
      })

      // Test that team members are displayed
      expect(wrapper.text()).toContain('John Doe')
      expect(wrapper.text()).toContain('Jane Smith')
      expect(wrapper.text()).toContain('Project Manager')
      expect(wrapper.text()).toContain('Developer')
    })

    it('should display team statistics correctly', () => {
      wrapper = mount(ProjectTeam, {
        props: { project: mockProject },
        global: {
          plugins: [createPinia()],
          stubs: ['HeroIcon']
        }
      })

      // Test computed statistics
      const totalHours = wrapper.vm.totalHoursWorked
      const averageHours = wrapper.vm.averageHoursPerMember
      const activeMembers = wrapper.vm.activeMembersCount

      expect(totalHours).toBe(120) // 40 + 80
      expect(averageHours).toBe(60) // 120 / 2
      expect(activeMembers).toBe(2) // Both have hours > 0
    })

    it('should show empty state when no team members', () => {
      const emptyProject = { ...mockProject, team_members: [] }
      
      wrapper = mount(ProjectTeam, {
        props: { project: emptyProject },
        global: {
          plugins: [createPinia()],
          stubs: ['HeroIcon']
        }
      })

      expect(wrapper.text()).toContain('No team members')
      expect(wrapper.find('[data-testid="empty-team-state"]').exists()).toBe(true)
    })
  })

  describe('Props and Events', () => {
    it('should handle loading prop correctly', () => {
      wrapper = mount(ProjectTeam, {
        props: { 
          project: mockProject,
          loading: true 
        },
        global: {
          plugins: [createPinia()],
          stubs: ['HeroIcon']
        }
      })

      expect(wrapper.find('[data-testid="loading-spinner"]').exists()).toBe(true)
    })

    it('should emit refresh event when team changes', async () => {
      wrapper = mount(ProjectTeam, {
        props: { project: mockProject },
        global: {
          plugins: [createPinia()],
          stubs: ['HeroIcon']
        }
      })

      // Mock successful API response
      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ success: true })
      })

      // Trigger team member addition
      await wrapper.vm.addMember()

      // Check that refresh event was emitted
      expect(wrapper.emitted('refresh')).toBeTruthy()
    })
  })

  describe('User Interactions', () => {
    beforeEach(() => {
      wrapper = mount(ProjectTeam, {
        props: { project: mockProject },
        global: {
          plugins: [createPinia()],
          stubs: ['HeroIcon']
        }
      })
    })

    it('should open add member modal', async () => {
      const addButton = wrapper.find('[data-testid="add-member-button"]')
      await addButton.trigger('click')

      expect(wrapper.vm.showAddMemberModal).toBe(true)
      expect(wrapper.find('[data-testid="add-member-modal"]').exists()).toBe(true)
    })

    it('should handle form submission for adding member', async () => {
      // Mock API response
      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ success: true })
      })

      // Set form data
      wrapper.vm.newMemberForm = {
        user_id: '3',
        role: 'QA Tester',
        allocation_percentage: 75
      }

      // Submit form
      await wrapper.vm.addMember()

      // Verify API call
      expect(fetch).toHaveBeenCalledWith(`/api/projects/${mockProject.id}/team`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': 'mock-csrf-token'
        },
        body: JSON.stringify({
          user_id: '3',
          role: 'QA Tester',
          allocation_percentage: 75
        })
      })
    })

    it('should handle API errors gracefully', async () => {
      // Mock API error
      fetch.mockRejectedValueOnce(new Error('Network error'))

      // Spy on alert (in real app, you'd use a toast system)
      const alertSpy = vi.spyOn(window, 'alert').mockImplementation(() => {})

      await wrapper.vm.addMember()

      expect(alertSpy).toHaveBeenCalledWith('Errore nell\'aggiunta del membro')
      alertSpy.mockRestore()
    })
  })

  describe('Permissions and Access Control', () => {
    it('should hide add button for unauthorized users', () => {
      mockAuthStore.hasPermission.mockReturnValue(false)

      wrapper = mount(ProjectTeam, {
        props: { project: mockProject },
        global: {
          plugins: [createPinia()],
          stubs: ['HeroIcon']
        }
      })

      expect(wrapper.find('[data-testid="add-member-button"]').exists()).toBe(false)
    })

    it('should show admin actions for admin users', () => {
      mockAuthStore.user.role = 'admin'

      wrapper = mount(ProjectTeam, {
        props: { project: mockProject },
        global: {
          plugins: [createPinia()],
          stubs: ['HeroIcon']
        }
      })

      expect(wrapper.find('[data-testid="admin-actions"]').exists()).toBe(true)
    })
  })

  describe('Data Validation', () => {
    beforeEach(() => {
      wrapper = mount(ProjectTeam, {
        props: { project: mockProject },
        global: {
          plugins: [createPinia()],
          stubs: ['HeroIcon']
        }
      })
    })

    it('should validate form inputs', async () => {
      // Set invalid form data
      wrapper.vm.newMemberForm = {
        user_id: '',
        role: '',
        allocation_percentage: -10
      }

      await wrapper.vm.addMember()

      // Should not make API call with invalid data
      expect(fetch).not.toHaveBeenCalled()
    })

    it('should format allocation percentage correctly', () => {
      const member = mockProject.team_members[0]
      const formatted = wrapper.vm.formatAllocation(member.allocation_percentage)
      
      expect(formatted).toBe('50%')
    })
  })
})
