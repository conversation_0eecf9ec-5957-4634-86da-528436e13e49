import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import { useProjectsStore } from '@/stores/projects'
import Projects from '@/views/projects/Projects.vue'
import ProjectEdit from '@/views/projects/ProjectEdit.vue'

// Mock router
const mockRouter = {
  push: vi.fn(),
  replace: vi.fn()
}

vi.mock('vue-router', () => ({
  useRouter: () => mockRouter,
  useRoute: () => ({ params: { id: '1' } })
}))

describe('Project API Integration Tests', () => {
  let pinia
  let projectsStore

  beforeEach(() => {
    // Setup Pinia
    pinia = createPinia()
    setActivePinia(pinia)
    projectsStore = useProjectsStore()

    // Mock fetch globally
    global.fetch = vi.fn()
    
    // Clear all mocks
    vi.clearAllMocks()
  })

  describe('Projects List Integration', () => {
    it('should load projects from API and display them', async () => {
      // Mock API response
      const mockProjects = [
        {
          id: 1,
          name: 'Project Alpha',
          status: 'active',
          budget: 50000,
          client: { name: 'Client A' }
        },
        {
          id: 2,
          name: 'Project Beta',
          status: 'planning',
          budget: 30000,
          client: { name: 'Client B' }
        }
      ]

      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          data: { projects: mockProjects }
        })
      })

      // Mount component
      const wrapper = mount(Projects, {
        global: {
          plugins: [pinia],
          mocks: {
            $router: mockRouter
          }
        }
      })

      // Wait for API call to complete
      await wrapper.vm.$nextTick()
      await new Promise(resolve => setTimeout(resolve, 0))

      // Verify API was called
      expect(fetch).toHaveBeenCalledWith('/api/projects')

      // Verify store was updated
      expect(projectsStore.projects).toEqual(mockProjects)
      expect(projectsStore.loading).toBe(false)

      // Verify UI displays projects
      expect(wrapper.text()).toContain('Project Alpha')
      expect(wrapper.text()).toContain('Project Beta')
      expect(wrapper.text()).toContain('Client A')
      expect(wrapper.text()).toContain('Client B')
    })

    it('should handle API errors gracefully', async () => {
      // Mock API error
      fetch.mockRejectedValueOnce(new Error('Network error'))

      const wrapper = mount(Projects, {
        global: {
          plugins: [pinia],
          mocks: {
            $router: mockRouter
          }
        }
      })

      await wrapper.vm.$nextTick()
      await new Promise(resolve => setTimeout(resolve, 0))

      // Verify error state
      expect(projectsStore.error).toBeTruthy()
      expect(projectsStore.loading).toBe(false)
      expect(wrapper.text()).toContain('Error loading projects')
    })

    it('should filter projects based on search', async () => {
      const mockProjects = [
        { id: 1, name: 'Alpha Project', status: 'active' },
        { id: 2, name: 'Beta Project', status: 'planning' },
        { id: 3, name: 'Gamma Task', status: 'active' }
      ]

      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          data: { projects: mockProjects }
        })
      })

      const wrapper = mount(Projects, {
        global: {
          plugins: [pinia],
          mocks: {
            $router: mockRouter
          }
        }
      })

      await wrapper.vm.$nextTick()

      // Search for "Project"
      const searchInput = wrapper.find('[data-testid="search-input"]')
      await searchInput.setValue('Project')

      // Should show only projects with "Project" in name
      expect(wrapper.text()).toContain('Alpha Project')
      expect(wrapper.text()).toContain('Beta Project')
      expect(wrapper.text()).not.toContain('Gamma Task')
    })
  })

  describe('Project Creation Integration', () => {
    it('should create project via API and redirect', async () => {
      const newProject = {
        name: 'New Integration Project',
        description: 'Test project for integration',
        budget: 25000,
        client_id: 1
      }

      // Mock successful creation
      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          data: { ...newProject, id: 3 }
        })
      })

      const wrapper = mount(ProjectEdit, {
        global: {
          plugins: [pinia],
          mocks: {
            $router: mockRouter
          }
        }
      })

      // Fill form
      await wrapper.find('[data-testid="project-name"]').setValue(newProject.name)
      await wrapper.find('[data-testid="project-description"]').setValue(newProject.description)
      await wrapper.find('[data-testid="project-budget"]').setValue(newProject.budget.toString())

      // Submit form
      await wrapper.find('[data-testid="save-button"]').trigger('click')
      await wrapper.vm.$nextTick()

      // Verify API call
      expect(fetch).toHaveBeenCalledWith('/api/projects', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(newProject)
      })

      // Verify redirect
      expect(mockRouter.push).toHaveBeenCalledWith('/app/projects/3')
    })

    it('should handle validation errors from API', async () => {
      // Mock validation error response
      fetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        json: () => Promise.resolve({
          success: false,
          errors: {
            name: ['Project name is required'],
            budget: ['Budget must be positive']
          }
        })
      })

      const wrapper = mount(ProjectEdit, {
        global: {
          plugins: [pinia],
          mocks: {
            $router: mockRouter
          }
        }
      })

      // Submit empty form
      await wrapper.find('[data-testid="save-button"]').trigger('click')
      await wrapper.vm.$nextTick()

      // Verify validation errors are displayed
      expect(wrapper.text()).toContain('Project name is required')
      expect(wrapper.text()).toContain('Budget must be positive')
    })
  })

  describe('Real-time Data Updates', () => {
    it('should update UI when store data changes', async () => {
      const wrapper = mount(Projects, {
        global: {
          plugins: [pinia],
          mocks: {
            $router: mockRouter
          }
        }
      })

      // Initially no projects
      expect(wrapper.text()).toContain('No projects found')

      // Add project to store
      projectsStore.projects = [
        { id: 1, name: 'Dynamic Project', status: 'active' }
      ]

      await wrapper.vm.$nextTick()

      // UI should update
      expect(wrapper.text()).toContain('Dynamic Project')
      expect(wrapper.text()).not.toContain('No projects found')
    })

    it('should reflect loading states', async () => {
      const wrapper = mount(Projects, {
        global: {
          plugins: [pinia],
          mocks: {
            $router: mockRouter
          }
        }
      })

      // Set loading state
      projectsStore.loading = true
      await wrapper.vm.$nextTick()

      expect(wrapper.find('[data-testid="loading-spinner"]').exists()).toBe(true)

      // Clear loading state
      projectsStore.loading = false
      await wrapper.vm.$nextTick()

      expect(wrapper.find('[data-testid="loading-spinner"]').exists()).toBe(false)
    })
  })

  describe('Error Handling Integration', () => {
    it('should display API error messages', async () => {
      fetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        json: () => Promise.resolve({
          success: false,
          error: 'Internal server error'
        })
      })

      const wrapper = mount(Projects, {
        global: {
          plugins: [pinia],
          mocks: {
            $router: mockRouter
          }
        }
      })

      await wrapper.vm.$nextTick()
      await new Promise(resolve => setTimeout(resolve, 0))

      expect(wrapper.text()).toContain('Internal server error')
    })

    it('should retry failed requests', async () => {
      // First call fails
      fetch
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            data: { projects: [] }
          })
        })

      const wrapper = mount(Projects, {
        global: {
          plugins: [pinia],
          mocks: {
            $router: mockRouter
          }
        }
      })

      await wrapper.vm.$nextTick()

      // Click retry button
      await wrapper.find('[data-testid="retry-button"]').trigger('click')
      await wrapper.vm.$nextTick()

      // Should make second API call
      expect(fetch).toHaveBeenCalledTimes(2)
    })
  })

  describe('Pagination Integration', () => {
    it('should handle paginated API responses', async () => {
      const mockResponse = {
        success: true,
        data: {
          projects: [
            { id: 1, name: 'Project 1' },
            { id: 2, name: 'Project 2' }
          ],
          pagination: {
            page: 1,
            per_page: 10,
            total: 25,
            pages: 3
          }
        }
      }

      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      })

      const wrapper = mount(Projects, {
        global: {
          plugins: [pinia],
          mocks: {
            $router: mockRouter
          }
        }
      })

      await wrapper.vm.$nextTick()

      // Verify pagination info is displayed
      expect(wrapper.text()).toContain('Page 1 of 3')
      expect(wrapper.text()).toContain('25 total projects')

      // Test page navigation
      await wrapper.find('[data-testid="next-page"]').trigger('click')

      expect(fetch).toHaveBeenCalledWith('/api/projects?page=2')
    })
  })
})
