/**
 * Test utilities and helpers for Vue component testing
 */
import { mount, shallowMount } from '@vue/test-utils'
import { createPinia } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'

// Mock router for testing
export const createMockRouter = (routes = []) => {
  return createRouter({
    history: createWebHistory(),
    routes: [
      { path: '/', component: { template: '<div>Home</div>' } },
      { path: '/login', component: { template: '<div>Login</div>' } },
      { path: '/dashboard', component: { template: '<div>Dashboard</div>' } },
      ...routes
    ]
  })
}

// Mock store factory
export const createMockStore = () => {
  return createPinia()
}

// Default mount options
export const defaultMountOptions = {
  global: {
    plugins: [createMockStore(), createMockRouter()],
    stubs: {
      'router-link': true,
      'router-view': true,
      'HeroIcon': true,
      'transition': false,
      'teleport': true
    },
    mocks: {
      $t: (key) => key, // Mock i18n
      $route: {
        path: '/',
        params: {},
        query: {},
        meta: {}
      },
      $router: {
        push: vi.fn(),
        replace: vi.fn(),
        go: vi.fn(),
        back: vi.fn()
      }
    }
  }
}

// Enhanced mount helper
export const mountComponent = (component, options = {}) => {
  const mergedOptions = {
    ...defaultMountOptions,
    ...options,
    global: {
      ...defaultMountOptions.global,
      ...options.global
    }
  }
  
  return mount(component, mergedOptions)
}

// Shallow mount helper
export const shallowMountComponent = (component, options = {}) => {
  const mergedOptions = {
    ...defaultMountOptions,
    ...options,
    global: {
      ...defaultMountOptions.global,
      ...options.global
    }
  }
  
  return shallowMount(component, mergedOptions)
}

// Mock API responses
export const mockApiResponse = (data, status = 200) => {
  return Promise.resolve({
    ok: status >= 200 && status < 300,
    status,
    json: () => Promise.resolve(data),
    text: () => Promise.resolve(JSON.stringify(data))
  })
}

// Mock API error
export const mockApiError = (message = 'API Error', status = 500) => {
  return Promise.reject({
    ok: false,
    status,
    message,
    json: () => Promise.resolve({ error: message })
  })
}

// Wait for next tick and DOM updates
export const waitForUpdate = async (wrapper) => {
  await wrapper.vm.$nextTick()
  await new Promise(resolve => setTimeout(resolve, 0))
}

// Mock user data
export const mockUser = {
  id: 1,
  username: 'testuser',
  email: '<EMAIL>',
  first_name: 'Test',
  last_name: 'User',
  role: 'employee',
  department_id: 1,
  is_active: true
}

// Mock admin user
export const mockAdminUser = {
  ...mockUser,
  id: 2,
  username: 'admin',
  email: '<EMAIL>',
  role: 'admin'
}

// Mock project data
export const mockProject = {
  id: 1,
  name: 'Test Project',
  description: 'A test project',
  status: 'active',
  start_date: '2025-01-01',
  end_date: '2025-12-31',
  budget: 10000,
  client_id: 1,
  manager_id: 1
}

// Mock client data
export const mockClient = {
  id: 1,
  name: 'Test Client',
  email: '<EMAIL>',
  phone: '+1234567890',
  address: '123 Test St',
  status: 'active'
}

// Form validation helpers
export const fillForm = async (wrapper, formData) => {
  for (const [field, value] of Object.entries(formData)) {
    const input = wrapper.find(`[data-testid="${field}"]`)
    if (input.exists()) {
      await input.setValue(value)
    }
  }
  await waitForUpdate(wrapper)
}

// Event helpers
export const triggerEvent = async (wrapper, selector, event, payload = {}) => {
  const element = wrapper.find(selector)
  if (element.exists()) {
    await element.trigger(event, payload)
    await waitForUpdate(wrapper)
  }
}

// Local storage mock helpers
export const mockLocalStorage = () => {
  const store = {}
  return {
    getItem: vi.fn((key) => store[key] || null),
    setItem: vi.fn((key, value) => { store[key] = value }),
    removeItem: vi.fn((key) => { delete store[key] }),
    clear: vi.fn(() => { Object.keys(store).forEach(key => delete store[key]) })
  }
}

// Console helpers for testing
export const suppressConsoleErrors = () => {
  const originalError = console.error
  console.error = vi.fn()
  return () => { console.error = originalError }
}

export const suppressConsoleWarnings = () => {
  const originalWarn = console.warn
  console.warn = vi.fn()
  return () => { console.warn = originalWarn }
}
