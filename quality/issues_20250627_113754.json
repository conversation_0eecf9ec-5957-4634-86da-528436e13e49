{"total": 57, "p": 1, "ps": 100, "paging": {"pageIndex": 1, "pageSize": 100, "total": 57}, "effortTotal": 960, "issues": [{"key": "AZewkLCAhv2Xsi9SWyR0", "rule": "python:S5754", "severity": "CRITICAL", "component": "octopus-interfacer:app/controllers/export_controller.py", "project": "octopus-interfacer", "line": 96, "hash": "3e264f5fbab10735799511f21ca70842", "textRange": {"startLine": 96, "endLine": 96, "startOffset": 12, "endOffset": 18}, "flows": [], "status": "OPEN", "message": "Specify an exception class to catch or reraise the exception", "effort": "5min", "debt": "5min", "author": "<EMAIL>", "tags": ["bad-practice", "error-handling", "suspicious"], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLCAhv2Xsi9SWyR1", "rule": "python:S5754", "severity": "CRITICAL", "component": "octopus-interfacer:app/controllers/export_controller.py", "project": "octopus-interfacer", "line": 144, "hash": "3e264f5fbab10735799511f21ca70842", "textRange": {"startLine": 144, "endLine": 144, "startOffset": 16, "endOffset": 22}, "flows": [], "status": "OPEN", "message": "Specify an exception class to catch or reraise the exception", "effort": "5min", "debt": "5min", "author": "<EMAIL>", "tags": ["bad-practice", "error-handling", "suspicious"], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLBuhv2Xsi9SWyRx", "rule": "python:S1135", "severity": "INFO", "component": "octopus-interfacer:app/controllers/invoicing_controller.py", "project": "octopus-interfacer", "line": 153, "hash": "c9249f924c92e99e4d70dcce794bdc7a", "textRange": {"startLine": 153, "endLine": 153, "startOffset": 36, "endOffset": 76}, "flows": [], "status": "OPEN", "message": "Complete the task associated to this \"TODO\" comment.", "effort": "0min", "debt": "0min", "author": "<EMAIL>", "tags": ["cwe"], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLB8hv2Xsi9SWyRz", "rule": "python:S1192", "severity": "CRITICAL", "component": "octopus-interfacer:app/controllers/report_controller.py", "project": "octopus-interfacer", "line": 45, "hash": "2dab99584c7c51cb2142635147938d7d", "textRange": {"startLine": 45, "endLine": 45, "startOffset": 9, "endOffset": 34}, "flows": [{"locations": [{"component": "octopus-interfacer:app/controllers/report_controller.py", "textRange": {"startLine": 73, "endLine": 73, "startOffset": 9, "endOffset": 34}, "msg": "Duplication", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/controllers/report_controller.py", "textRange": {"startLine": 90, "endLine": 90, "startOffset": 9, "endOffset": 34}, "msg": "Duplication", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/controllers/report_controller.py", "textRange": {"startLine": 115, "endLine": 115, "startOffset": 9, "endOffset": 34}, "msg": "Duplication", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/controllers/report_controller.py", "textRange": {"startLine": 132, "endLine": 132, "startOffset": 9, "endOffset": 34}, "msg": "Duplication", "msgFormattings": []}]}], "status": "OPEN", "message": "Define a constant instead of duplicating this literal 'Token di autenticazione' 5 times.", "effort": "10min", "debt": "10min", "author": "<EMAIL>", "tags": ["design"], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLCJhv2Xsi9SWyR3", "rule": "python:S1192", "severity": "CRITICAL", "component": "octopus-interfacer:app/models/user.py", "project": "octopus-interfacer", "line": 22, "hash": "a306f8bba6dc65d0dfbceffa54d761b2", "textRange": {"startLine": 22, "endLine": 22, "startOffset": 90, "endOffset": 110}, "flows": [{"locations": [{"component": "octopus-interfacer:app/models/user.py", "textRange": {"startLine": 23, "endLine": 23, "startOffset": 81, "endOffset": 101}, "msg": "Duplication", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/models/user.py", "textRange": {"startLine": 24, "endLine": 24, "startOffset": 87, "endOffset": 107}, "msg": "Duplication", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/models/user.py", "textRange": {"startLine": 25, "endLine": 25, "startOffset": 86, "endOffset": 106}, "msg": "Duplication", "msgFormattings": []}]}], "status": "OPEN", "message": "Define a constant instead of duplicating this literal 'all, delete-orphan' 4 times.", "effort": "8min", "debt": "8min", "author": "<EMAIL>", "tags": ["design"], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLCWhv2Xsi9SWyR7", "rule": "python:S1481", "severity": "MINOR", "component": "octopus-interfacer:app/services/auth_service.py", "project": "octopus-interfacer", "line": 41, "hash": "d6bf0bef753edda80a3790bea5787f1b", "textRange": {"startLine": 41, "endLine": 41, "startOffset": 12, "endOffset": 24}, "flows": [], "status": "OPEN", "message": "Remove the unused local variable \"user_session\".", "effort": "5min", "debt": "5min", "author": "<EMAIL>", "tags": ["unused"], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLCYhv2Xsi9SWyR8", "rule": "python:S930", "severity": "BLOCKER", "component": "octopus-interfacer:app/services/chat_service.py", "project": "octopus-interfacer", "line": 67, "hash": "a7633227e21ecd130952b83b0884b18a", "textRange": {"startLine": 67, "endLine": 67, "startOffset": 23, "endOffset": 45}, "flows": [{"locations": [{"component": "octopus-interfacer:app/services/chat_service.py", "textRange": {"startLine": 67, "endLine": 67, "startOffset": 23, "endOffset": 45}, "msg": "Add 4 missing arguments; 'process_query_with_gpt' expects 7 positional arguments.", "msgFormattings": []}, {"component": "octopus-interfacer:utils/openai_utils.py", "textRange": {"startLine": 10, "endLine": 10, "startOffset": 4, "endOffset": 26}, "msg": "Function definition.", "msgFormattings": []}]}], "status": "OPEN", "message": "Add 4 missing arguments; 'process_query_with_gpt' expects 7 positional arguments.", "effort": "10min", "debt": "10min", "author": "<EMAIL>", "tags": ["cwe"], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "BUG", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLCYhv2Xsi9SWyR9", "rule": "python:S930", "severity": "BLOCKER", "component": "octopus-interfacer:app/services/chat_service.py", "project": "octopus-interfacer", "line": 68, "hash": "559336ab43b8f5ce01e4cd3b89be0e3c", "textRange": {"startLine": 68, "endLine": 68, "startOffset": 16, "endOffset": 29}, "flows": [{"locations": [{"component": "octopus-interfacer:app/services/chat_service.py", "textRange": {"startLine": 68, "endLine": 68, "startOffset": 16, "endOffset": 29}, "msg": "Remove this unexpected named argument 'query'.", "msgFormattings": []}, {"component": "octopus-interfacer:utils/openai_utils.py", "textRange": {"startLine": 10, "endLine": 10, "startOffset": 4, "endOffset": 26}, "msg": "Function definition.", "msgFormattings": []}]}], "status": "OPEN", "message": "Remove this unexpected named argument 'query'.", "effort": "10min", "debt": "10min", "author": "<EMAIL>", "tags": ["cwe"], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "BUG", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLCYhv2Xsi9SWyR-", "rule": "python:S930", "severity": "BLOCKER", "component": "octopus-interfacer:app/services/chat_service.py", "project": "octopus-interfacer", "line": 71, "hash": "fb07f48aeada25ff376e61aad7463322", "textRange": {"startLine": 71, "endLine": 71, "startOffset": 16, "endOffset": 53}, "flows": [{"locations": [{"component": "octopus-interfacer:app/services/chat_service.py", "textRange": {"startLine": 71, "endLine": 71, "startOffset": 16, "endOffset": 53}, "msg": "Remove this unexpected named argument 'filtered_endpoints'.", "msgFormattings": []}, {"component": "octopus-interfacer:utils/openai_utils.py", "textRange": {"startLine": 10, "endLine": 10, "startOffset": 4, "endOffset": 26}, "msg": "Function definition.", "msgFormattings": []}]}], "status": "OPEN", "message": "Remove this unexpected named argument 'filtered_endpoints'.", "effort": "10min", "debt": "10min", "author": "<EMAIL>", "tags": ["cwe"], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "BUG", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLCYhv2Xsi9SWyR_", "rule": "python:S930", "severity": "BLOCKER", "component": "octopus-interfacer:app/services/chat_service.py", "project": "octopus-interfacer", "line": 218, "hash": "a3de51f4d7c87df2ea84b474f73333f9", "textRange": {"startLine": 218, "endLine": 218, "startOffset": 32, "endOffset": 53}, "flows": [{"locations": [{"component": "octopus-interfacer:app/services/chat_service.py", "textRange": {"startLine": 218, "endLine": 218, "startOffset": 32, "endOffset": 53}, "msg": "Remove 1 unexpected arguments; 'get_swagger_endpoints' expects 1 positional arguments.", "msgFormattings": []}, {"component": "octopus-interfacer:utils/swagger_utils.py", "textRange": {"startLine": 8, "endLine": 8, "startOffset": 4, "endOffset": 25}, "msg": "Function definition.", "msgFormattings": []}]}], "status": "OPEN", "message": "Remove 1 unexpected arguments; 'get_swagger_endpoints' expects 1 positional arguments.", "effort": "10min", "debt": "10min", "author": "<EMAIL>", "tags": ["cwe"], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "BUG", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLCThv2Xsi9SWyR4", "rule": "python:S1186", "severity": "CRITICAL", "component": "octopus-interfacer:app/services/invoicing_service.py", "project": "octopus-interfacer", "line": 17, "hash": "6f7a4723dd0e8820f76fe17d4017882f", "textRange": {"startLine": 17, "endLine": 17, "startOffset": 8, "endOffset": 16}, "flows": [], "status": "OPEN", "message": "Add a nested comment explaining why this method is empty, or complete the implementation.", "effort": "5min", "debt": "5min", "author": "<EMAIL>", "tags": ["suspicious"], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLCThv2Xsi9SWyR5", "rule": "python:S3776", "severity": "CRITICAL", "component": "octopus-interfacer:app/services/invoicing_service.py", "project": "octopus-interfacer", "line": 20, "hash": "22c90e52353db8ebb01cff8b723b80a0", "textRange": {"startLine": 20, "endLine": 20, "startOffset": 8, "endOffset": 32}, "flows": [{"locations": [{"component": "octopus-interfacer:app/services/invoicing_service.py", "textRange": {"startLine": 38, "endLine": 38, "startOffset": 12, "endOffset": 14}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/invoicing_service.py", "textRange": {"startLine": 38, "endLine": 38, "startOffset": 32, "endOffset": 34}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/invoicing_service.py", "textRange": {"startLine": 45, "endLine": 45, "startOffset": 12, "endOffset": 14}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/invoicing_service.py", "textRange": {"startLine": 58, "endLine": 58, "startOffset": 12, "endOffset": 14}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/invoicing_service.py", "textRange": {"startLine": 72, "endLine": 72, "startOffset": 12, "endOffset": 14}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/invoicing_service.py", "textRange": {"startLine": 72, "endLine": 72, "startOffset": 45, "endOffset": 48}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/invoicing_service.py", "textRange": {"startLine": 73, "endLine": 73, "startOffset": 16, "endOffset": 19}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/invoicing_service.py", "textRange": {"startLine": 74, "endLine": 74, "startOffset": 20, "endOffset": 22}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/invoicing_service.py", "textRange": {"startLine": 80, "endLine": 80, "startOffset": 24, "endOffset": 26}, "msg": "+4 (incl 3 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/invoicing_service.py", "textRange": {"startLine": 88, "endLine": 88, "startOffset": 8, "endOffset": 14}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/invoicing_service.py", "textRange": {"startLine": 94, "endLine": 94, "startOffset": 8, "endOffset": 14}, "msg": "+1", "msgFormattings": []}]}], "status": "OPEN", "message": "Refactor this function to reduce its Cognitive Complexity from 17 to the 15 allowed.", "effort": "7min", "debt": "7min", "author": "<EMAIL>", "tags": ["brain-overload"], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLCThv2Xsi9SWyR6", "rule": "python:S1135", "severity": "INFO", "component": "octopus-interfacer:app/services/invoicing_service.py", "project": "octopus-interfacer", "line": 352, "hash": "0067c1d3af6f6c40779de41a648f0bc5", "textRange": {"startLine": 352, "endLine": 352, "startOffset": 16, "endOffset": 80}, "flows": [], "status": "OPEN", "message": "Complete the task associated to this \"TODO\" comment.", "effort": "0min", "debt": "0min", "author": "<EMAIL>", "tags": ["cwe"], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLCchv2Xsi9SWySB", "rule": "python:S1192", "severity": "CRITICAL", "component": "octopus-interfacer:app/services/report_service.py", "project": "octopus-interfacer", "line": 37, "hash": "b27ddfaaa012290d4f740f607b8f3b93", "textRange": {"startLine": 37, "endLine": 37, "startOffset": 31, "endOffset": 103}, "flows": [{"locations": [{"component": "octopus-interfacer:app/services/report_service.py", "textRange": {"startLine": 106, "endLine": 106, "startOffset": 31, "endOffset": 103}, "msg": "Duplication", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/report_service.py", "textRange": {"startLine": 177, "endLine": 177, "startOffset": 31, "endOffset": 103}, "msg": "Duplication", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/report_service.py", "textRange": {"startLine": 337, "endLine": 337, "startOffset": 31, "endOffset": 103}, "msg": "Duplication", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/report_service.py", "textRange": {"startLine": 400, "endLine": 400, "startOffset": 31, "endOffset": 103}, "msg": "Duplication", "msgFormattings": []}]}], "status": "OPEN", "message": "Define a constant instead of duplicating this literal 'Configurazione Octopus non trovata. Vai alla pagina di configurazione.' 5 times.", "effort": "10min", "debt": "10min", "author": "<EMAIL>", "tags": ["design"], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLCchv2Xsi9SWySA", "rule": "python:S1192", "severity": "CRITICAL", "component": "octopus-interfacer:app/services/report_service.py", "project": "octopus-interfacer", "line": 45, "hash": "fe0c1a94ef001e38d3a2fc41879733f5", "textRange": {"startLine": 45, "endLine": 45, "startOffset": 31, "endOffset": 96}, "flows": [{"locations": [{"component": "octopus-interfacer:app/services/report_service.py", "textRange": {"startLine": 114, "endLine": 114, "startOffset": 31, "endOffset": 96}, "msg": "Duplication", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/report_service.py", "textRange": {"startLine": 185, "endLine": 185, "startOffset": 31, "endOffset": 96}, "msg": "Duplication", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/report_service.py", "textRange": {"startLine": 345, "endLine": 345, "startOffset": 31, "endOffset": 96}, "msg": "Duplication", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/report_service.py", "textRange": {"startLine": 408, "endLine": 408, "startOffset": 31, "endOffset": 96}, "msg": "Duplication", "msgFormattings": []}]}], "status": "OPEN", "message": "Define a constant instead of duplicating this literal 'Token di autenticazione non valido. Aggiorna la configurazione.' 5 times.", "effort": "10min", "debt": "10min", "author": "<EMAIL>", "tags": ["design"], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLCchv2Xsi9SWySC", "rule": "python:S3776", "severity": "CRITICAL", "component": "octopus-interfacer:app/services/report_service.py", "project": "octopus-interfacer", "line": 169, "hash": "0c28711465bf4e050e5dcb7e53b2c9ff", "textRange": {"startLine": 169, "endLine": 169, "startOffset": 8, "endOffset": 17}, "flows": [{"locations": [{"component": "octopus-interfacer:app/services/report_service.py", "textRange": {"startLine": 174, "endLine": 174, "startOffset": 12, "endOffset": 14}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/report_service.py", "textRange": {"startLine": 182, "endLine": 182, "startOffset": 12, "endOffset": 14}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/report_service.py", "textRange": {"startLine": 202, "endLine": 202, "startOffset": 12, "endOffset": 14}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/report_service.py", "textRange": {"startLine": 223, "endLine": 223, "startOffset": 12, "endOffset": 14}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/report_service.py", "textRange": {"startLine": 223, "endLine": 223, "startOffset": 32, "endOffset": 35}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/report_service.py", "textRange": {"startLine": 225, "endLine": 225, "startOffset": 16, "endOffset": 19}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/report_service.py", "textRange": {"startLine": 226, "endLine": 226, "startOffset": 20, "endOffset": 22}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/report_service.py", "textRange": {"startLine": 226, "endLine": 226, "startOffset": 81, "endOffset": 84}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/report_service.py", "textRange": {"startLine": 229, "endLine": 229, "startOffset": 24, "endOffset": 26}, "msg": "+4 (incl 3 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/report_service.py", "textRange": {"startLine": 236, "endLine": 236, "startOffset": 28, "endOffset": 31}, "msg": "+5 (incl 4 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/report_service.py", "textRange": {"startLine": 241, "endLine": 241, "startOffset": 24, "endOffset": 28}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/report_service.py", "textRange": {"startLine": 248, "endLine": 248, "startOffset": 28, "endOffset": 31}, "msg": "+5 (incl 4 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/report_service.py", "textRange": {"startLine": 254, "endLine": 254, "startOffset": 12, "endOffset": 14}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/report_service.py", "textRange": {"startLine": 256, "endLine": 256, "startOffset": 12, "endOffset": 14}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/report_service.py", "textRange": {"startLine": 266, "endLine": 266, "startOffset": 12, "endOffset": 14}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/report_service.py", "textRange": {"startLine": 271, "endLine": 271, "startOffset": 16, "endOffset": 18}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/report_service.py", "textRange": {"startLine": 273, "endLine": 273, "startOffset": 20, "endOffset": 23}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/report_service.py", "textRange": {"startLine": 277, "endLine": 277, "startOffset": 20, "endOffset": 23}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/report_service.py", "textRange": {"startLine": 284, "endLine": 284, "startOffset": 20, "endOffset": 22}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/report_service.py", "textRange": {"startLine": 284, "endLine": 284, "startOffset": 47, "endOffset": 50}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/report_service.py", "textRange": {"startLine": 295, "endLine": 295, "startOffset": 12, "endOffset": 16}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/report_service.py", "textRange": {"startLine": 299, "endLine": 299, "startOffset": 16, "endOffset": 19}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/report_service.py", "textRange": {"startLine": 303, "endLine": 303, "startOffset": 12, "endOffset": 16}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/report_service.py", "textRange": {"startLine": 322, "endLine": 322, "startOffset": 8, "endOffset": 14}, "msg": "+1", "msgFormattings": []}]}], "status": "OPEN", "message": "Refactor this function to reduce its Cognitive Complexity from 46 to the 15 allowed.", "effort": "36min", "debt": "36min", "author": "<EMAIL>", "tags": ["brain-overload"], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLCchv2Xsi9SWySD", "rule": "python:S3776", "severity": "CRITICAL", "component": "octopus-interfacer:app/services/report_service.py", "project": "octopus-interfacer", "line": 392, "hash": "265be0d7da8d2f2b3313e67900ef636d", "textRange": {"startLine": 392, "endLine": 392, "startOffset": 8, "endOffset": 25}, "flows": [{"locations": [{"component": "octopus-interfacer:app/services/report_service.py", "textRange": {"startLine": 397, "endLine": 397, "startOffset": 12, "endOffset": 14}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/report_service.py", "textRange": {"startLine": 405, "endLine": 405, "startOffset": 12, "endOffset": 14}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/report_service.py", "textRange": {"startLine": 421, "endLine": 421, "startOffset": 12, "endOffset": 14}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/report_service.py", "textRange": {"startLine": 431, "endLine": 431, "startOffset": 12, "endOffset": 18}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/report_service.py", "textRange": {"startLine": 436, "endLine": 436, "startOffset": 12, "endOffset": 14}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/report_service.py", "textRange": {"startLine": 438, "endLine": 438, "startOffset": 16, "endOffset": 18}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/report_service.py", "textRange": {"startLine": 445, "endLine": 445, "startOffset": 20, "endOffset": 22}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/report_service.py", "textRange": {"startLine": 447, "endLine": 447, "startOffset": 24, "endOffset": 26}, "msg": "+4 (incl 3 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/report_service.py", "textRange": {"startLine": 450, "endLine": 450, "startOffset": 20, "endOffset": 24}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/report_service.py", "textRange": {"startLine": 455, "endLine": 455, "startOffset": 16, "endOffset": 19}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/report_service.py", "textRange": {"startLine": 456, "endLine": 456, "startOffset": 20, "endOffset": 22}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/report_service.py", "textRange": {"startLine": 462, "endLine": 462, "startOffset": 12, "endOffset": 14}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/report_service.py", "textRange": {"startLine": 463, "endLine": 463, "startOffset": 16, "endOffset": 18}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/report_service.py", "textRange": {"startLine": 463, "endLine": 463, "startOffset": 42, "endOffset": 45}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:app/services/report_service.py", "textRange": {"startLine": 480, "endLine": 480, "startOffset": 8, "endOffset": 14}, "msg": "+1", "msgFormattings": []}]}], "status": "OPEN", "message": "Refactor this function to reduce its Cognitive Complexity from 25 to the 15 allowed.", "effort": "15min", "debt": "15min", "author": "<EMAIL>", "tags": ["brain-overload"], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLDPhv2Xsi9SWySU", "rule": "css:S4666", "severity": "MAJOR", "component": "octopus-interfacer:static/css/styles.css", "project": "octopus-interfacer", "line": 1160, "hash": "f45023dcbe8f5fe389257359e0c43492", "textRange": {"startLine": 1160, "endLine": 1160, "startOffset": 0, "endOffset": 15}, "flows": [], "status": "OPEN", "message": "Unexpected duplicate selector \".feature-card\", first used at line 414", "effort": "1min", "debt": "1min", "author": "<EMAIL>", "tags": [], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLDPhv2Xsi9SWySV", "rule": "css:S4666", "severity": "MAJOR", "component": "octopus-interfacer:static/css/styles.css", "project": "octopus-interfacer", "line": 1181, "hash": "856b79a616bb6e450273b44c9f2a667e", "textRange": {"startLine": 1181, "endLine": 1181, "startOffset": 0, "endOffset": 16}, "flows": [], "status": "OPEN", "message": "Unexpected duplicate selector \".endpoint-item\", first used at line 888", "effort": "1min", "debt": "1min", "author": "<EMAIL>", "tags": [], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLDPhv2Xsi9SWySW", "rule": "css:S4666", "severity": "MAJOR", "component": "octopus-interfacer:static/css/styles.css", "project": "octopus-interfacer", "line": 1192, "hash": "97f35245962d6a73dfe001ef16ade1cd", "textRange": {"startLine": 1192, "endLine": 1192, "startOffset": 0, "endOffset": 18}, "flows": [], "status": "OPEN", "message": "Unexpected duplicate selector \".endpoint-method\", first used at line 900", "effort": "1min", "debt": "1min", "author": "<EMAIL>", "tags": [], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLDPhv2Xsi9SWySX", "rule": "css:S4666", "severity": "MAJOR", "component": "octopus-interfacer:static/css/styles.css", "project": "octopus-interfacer", "line": 1298, "hash": "3d313aa3f95d07a68488c7b106380268", "textRange": {"startLine": 1298, "endLine": 1298, "startOffset": 0, "endOffset": 14}, "flows": [], "status": "OPEN", "message": "Unexpected duplicate selector \".btn-outline\", first used at line 87", "effort": "1min", "debt": "1min", "author": "<EMAIL>", "tags": [], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLDPhv2Xsi9SWySY", "rule": "css:S4666", "severity": "MAJOR", "component": "octopus-interfacer:static/css/styles.css", "project": "octopus-interfacer", "line": 1312, "hash": "7294894c7a12523d680828463a497f52", "textRange": {"startLine": 1312, "endLine": 1312, "startOffset": 0, "endOffset": 20}, "flows": [], "status": "OPEN", "message": "Unexpected duplicate selector \".btn-outline:hover\", first used at line 93", "effort": "1min", "debt": "1min", "author": "<EMAIL>", "tags": [], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLDPhv2Xsi9SWySZ", "rule": "css:S4666", "severity": "MAJOR", "component": "octopus-interfacer:static/css/styles.css", "project": "octopus-interfacer", "line": 1603, "hash": "eac372888c7bafb82601d0c4f5e1e557", "textRange": {"startLine": 1603, "endLine": 1603, "startOffset": 0, "endOffset": 13}, "flows": [], "status": "OPEN", "message": "Unexpected duplicate selector \".form-group\", first used at line 194", "effort": "1min", "debt": "1min", "author": "<EMAIL>", "tags": [], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLDPhv2Xsi9SWySa", "rule": "css:S4666", "severity": "MAJOR", "component": "octopus-interfacer:static/css/styles.css", "project": "octopus-interfacer", "line": 1609, "hash": "f7892127be55552bab2580d4b900171a", "textRange": {"startLine": 1609, "endLine": 1609, "startOffset": 0, "endOffset": 13}, "flows": [], "status": "OPEN", "message": "Unexpected duplicate selector \".form-label\", first used at line 198", "effort": "1min", "debt": "1min", "author": "<EMAIL>", "tags": [], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLDPhv2Xsi9SWySb", "rule": "css:S4666", "severity": "MAJOR", "component": "octopus-interfacer:static/css/styles.css", "project": "octopus-interfacer", "line": 1812, "hash": "d4100538e6a49d48d5d49663525065f8", "textRange": {"startLine": 1812, "endLine": 1812, "startOffset": 0, "endOffset": 16}, "flows": [], "status": "OPEN", "message": "Unexpected duplicate selector \".input-wrapper\", first used at line 1645", "effort": "1min", "debt": "1min", "author": "<EMAIL>", "tags": [], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLDPhv2Xsi9SWySc", "rule": "css:S4666", "severity": "MAJOR", "component": "octopus-interfacer:static/css/styles.css", "project": "octopus-interfacer", "line": 1828, "hash": "5b3c9bb7568e6853f3cb40119946bb3b", "textRange": {"startLine": 1828, "endLine": 1828, "startOffset": 0, "endOffset": 18}, "flows": [], "status": "OPEN", "message": "Unexpected duplicate selector \".toggle-password\", first used at line 1633", "effort": "1min", "debt": "1min", "author": "<EMAIL>", "tags": [], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLDPhv2Xsi9SWySd", "rule": "css:S4666", "severity": "MAJOR", "component": "octopus-interfacer:static/css/styles.css", "project": "octopus-interfacer", "line": 1846, "hash": "6c56f5da81ed8357d6103ba38f8c47f5", "textRange": {"startLine": 1846, "endLine": 1846, "startOffset": 0, "endOffset": 8}, "flows": [], "status": "OPEN", "message": "Unexpected duplicate selector \".alert\", first used at line 1649", "effort": "1min", "debt": "1min", "author": "<EMAIL>", "tags": [], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLDPhv2Xsi9SWySe", "rule": "css:S4666", "severity": "MAJOR", "component": "octopus-interfacer:static/css/styles.css", "project": "octopus-interfacer", "line": 1862, "hash": "65f393fef99236af2078ca22785bca34", "textRange": {"startLine": 1862, "endLine": 1862, "startOffset": 0, "endOffset": 18}, "flows": [], "status": "OPEN", "message": "Unexpected duplicate selector \".login-container\", first used at line 1570", "effort": "1min", "debt": "1min", "author": "<EMAIL>", "tags": [], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLDPhv2Xsi9SWySf", "rule": "css:S4666", "severity": "MAJOR", "component": "octopus-interfacer:static/css/styles.css", "project": "octopus-interfacer", "line": 1871, "hash": "bf897ecd350e9020e657b1a682b3d8b9", "textRange": {"startLine": 1871, "endLine": 1871, "startOffset": 0, "endOffset": 13}, "flows": [], "status": "OPEN", "message": "Unexpected duplicate selector \".login-form\", first used at line 1597", "effort": "1min", "debt": "1min", "author": "<EMAIL>", "tags": [], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLDPhv2Xsi9SWySg", "rule": "css:S4666", "severity": "MAJOR", "component": "octopus-interfacer:static/css/styles.css", "project": "octopus-interfacer", "line": 1877, "hash": "eac372888c7bafb82601d0c4f5e1e557", "textRange": {"startLine": 1877, "endLine": 1877, "startOffset": 0, "endOffset": 13}, "flows": [], "status": "OPEN", "message": "Unexpected duplicate selector \".form-group\", first used at line 194", "effort": "1min", "debt": "1min", "author": "<EMAIL>", "tags": [], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLDPhv2Xsi9SWySh", "rule": "css:S4666", "severity": "MAJOR", "component": "octopus-interfacer:static/css/styles.css", "project": "octopus-interfacer", "line": 1883, "hash": "f7892127be55552bab2580d4b900171a", "textRange": {"startLine": 1883, "endLine": 1883, "startOffset": 0, "endOffset": 13}, "flows": [], "status": "OPEN", "message": "Unexpected duplicate selector \".form-label\", first used at line 198", "effort": "1min", "debt": "1min", "author": "<EMAIL>", "tags": [], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLDPhv2Xsi9SWySi", "rule": "css:S4666", "severity": "MAJOR", "component": "octopus-interfacer:static/css/styles.css", "project": "octopus-interfacer", "line": 1889, "hash": "d4d23dd690d6e420676eb52c82a26c73", "textRange": {"startLine": 1889, "endLine": 1889, "startOffset": 0, "endOffset": 13}, "flows": [], "status": "OPEN", "message": "Unexpected duplicate selector \".form-input\", first used at line 1615", "effort": "1min", "debt": "1min", "author": "<EMAIL>", "tags": [], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLDPhv2Xsi9SWySj", "rule": "css:S4666", "severity": "MAJOR", "component": "octopus-interfacer:static/css/styles.css", "project": "octopus-interfacer", "line": 1914, "hash": "47596bb4042617817eea6ca7a9573fc3", "textRange": {"startLine": 1914, "endLine": 1914, "startOffset": 0, "endOffset": 17}, "flows": [], "status": "OPEN", "message": "Unexpected duplicate selector \".docs-container\", first used at line 1332", "effort": "1min", "debt": "1min", "author": "<EMAIL>", "tags": [], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLDPhv2Xsi9SWySk", "rule": "css:S4666", "severity": "MAJOR", "component": "octopus-interfacer:static/css/styles.css", "project": "octopus-interfacer", "line": 1919, "hash": "2f87c58490cc0b28ad8b28cb6021d88e", "textRange": {"startLine": 1919, "endLine": 1919, "startOffset": 0, "endOffset": 21}, "flows": [], "status": "OPEN", "message": "Unexpected duplicate selector \".endpoint-list code\", first used at line 1389", "effort": "1min", "debt": "1min", "author": "<EMAIL>", "tags": [], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLDPhv2Xsi9SWySl", "rule": "css:S4666", "severity": "MAJOR", "component": "octopus-interfacer:static/css/styles.css", "project": "octopus-interfacer", "line": 1926, "hash": "e324e5747d90130a3d2b50ff2b012d3e", "textRange": {"startLine": 1926, "endLine": 1926, "startOffset": 0, "endOffset": 11}, "flows": [], "status": "OPEN", "message": "Unexpected duplicate selector \"pre, code\", first used at line 1282", "effort": "1min", "debt": "1min", "author": "<EMAIL>", "tags": [], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLC0hv2Xsi9SWySL", "rule": "javascript:S3776", "severity": "CRITICAL", "component": "octopus-interfacer:static/js/app.js", "project": "octopus-interfacer", "line": 10, "hash": "b9ddbb29bd76f11391c0c19d3772243f", "textRange": {"startLine": 10, "endLine": 10, "startOffset": 33, "endOffset": 35}, "flows": [{"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 54, "endLine": 54, "startOffset": 12, "endOffset": 14}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 59, "endLine": 59, "startOffset": 16, "endOffset": 18}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 68, "endLine": 68, "startOffset": 20, "endOffset": 22}, "msg": "+4 (incl. 3 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 78, "endLine": 78, "startOffset": 20, "endOffset": 22}, "msg": "+4 (incl. 3 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 95, "endLine": 95, "startOffset": 16, "endOffset": 18}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 98, "endLine": 98, "startOffset": 16, "endOffset": 18}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 108, "endLine": 108, "startOffset": 16, "endOffset": 18}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 111, "endLine": 111, "startOffset": 18, "endOffset": 22}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 108, "endLine": 108, "startOffset": 45, "endOffset": 47}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 135, "endLine": 135, "startOffset": 67, "endOffset": 69}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 136, "endLine": 136, "startOffset": 59, "endOffset": 61}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 137, "endLine": 137, "startOffset": 73, "endOffset": 75}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 139, "endLine": 139, "startOffset": 51, "endOffset": 53}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 146, "endLine": 146, "startOffset": 12, "endOffset": 14}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 151, "endLine": 151, "startOffset": 12, "endOffset": 14}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 151, "endLine": 151, "startOffset": 25, "endOffset": 27}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 158, "endLine": 158, "startOffset": 19, "endOffset": 21}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 162, "endLine": 162, "startOffset": 14, "endOffset": 18}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 158, "endLine": 158, "startOffset": 37, "endOffset": 39}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 189, "endLine": 189, "startOffset": 16, "endOffset": 18}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 193, "endLine": 193, "startOffset": 16, "endOffset": 18}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 193, "endLine": 193, "startOffset": 57, "endOffset": 59}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 202, "endLine": 202, "startOffset": 16, "endOffset": 18}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 208, "endLine": 208, "startOffset": 18, "endOffset": 22}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 214, "endLine": 214, "startOffset": 16, "endOffset": 18}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 226, "endLine": 226, "startOffset": 12, "endOffset": 14}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 233, "endLine": 233, "startOffset": 20, "endOffset": 22}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 237, "endLine": 237, "startOffset": 20, "endOffset": 22}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 237, "endLine": 237, "startOffset": 61, "endOffset": 63}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 246, "endLine": 246, "startOffset": 20, "endOffset": 22}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 251, "endLine": 251, "startOffset": 22, "endOffset": 26}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 257, "endLine": 257, "startOffset": 20, "endOffset": 22}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 267, "endLine": 267, "startOffset": 12, "endOffset": 14}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 267, "endLine": 267, "startOffset": 38, "endOffset": 40}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 275, "endLine": 275, "startOffset": 16, "endOffset": 18}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 275, "endLine": 275, "startOffset": 36, "endOffset": 38}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 280, "endLine": 280, "startOffset": 16, "endOffset": 18}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 280, "endLine": 280, "startOffset": 34, "endOffset": 36}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 285, "endLine": 285, "startOffset": 16, "endOffset": 18}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 290, "endLine": 290, "startOffset": 18, "endOffset": 22}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 287, "endLine": 287, "startOffset": 20, "endOffset": 22}, "msg": "+4 (incl. 3 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 287, "endLine": 287, "startOffset": 38, "endOffset": 40}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 292, "endLine": 292, "startOffset": 20, "endOffset": 22}, "msg": "+4 (incl. 3 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 292, "endLine": 292, "startOffset": 38, "endOffset": 40}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 298, "endLine": 298, "startOffset": 16, "endOffset": 18}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 298, "endLine": 298, "startOffset": 34, "endOffset": 36}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 312, "endLine": 312, "startOffset": 16, "endOffset": 18}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 328, "endLine": 328, "startOffset": 18, "endOffset": 22}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 315, "endLine": 315, "startOffset": 59, "endOffset": 61}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 318, "endLine": 318, "startOffset": 20, "endOffset": 22}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 321, "endLine": 321, "startOffset": 22, "endOffset": 26}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 324, "endLine": 324, "startOffset": 48, "endOffset": 50}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 332, "endLine": 332, "startOffset": 14, "endOffset": 19}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 343, "endLine": 343, "startOffset": 12, "endOffset": 14}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 349, "endLine": 349, "startOffset": 20, "endOffset": 22}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 352, "endLine": 352, "startOffset": 20, "endOffset": 22}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 352, "endLine": 352, "startOffset": 61, "endOffset": 63}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 361, "endLine": 361, "startOffset": 20, "endOffset": 22}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 367, "endLine": 367, "startOffset": 20, "endOffset": 22}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 372, "endLine": 372, "startOffset": 20, "endOffset": 22}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 382, "endLine": 382, "startOffset": 12, "endOffset": 14}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 399, "endLine": 399, "startOffset": 12, "endOffset": 14}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 417, "endLine": 417, "startOffset": 16, "endOffset": 18}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 421, "endLine": 421, "startOffset": 16, "endOffset": 18}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 421, "endLine": 421, "startOffset": 57, "endOffset": 59}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 431, "endLine": 431, "startOffset": 16, "endOffset": 18}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 459, "endLine": 459, "startOffset": 18, "endOffset": 22}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 442, "endLine": 442, "startOffset": 24, "endOffset": 26}, "msg": "+5 (incl. 4 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 448, "endLine": 448, "startOffset": 20, "endOffset": 22}, "msg": "+4 (incl. 3 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 455, "endLine": 455, "startOffset": 22, "endOffset": 26}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 452, "endLine": 452, "startOffset": 24, "endOffset": 26}, "msg": "+5 (incl. 4 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 463, "endLine": 463, "startOffset": 57, "endOffset": 59}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 468, "endLine": 468, "startOffset": 48, "endOffset": 50}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 473, "endLine": 473, "startOffset": 16, "endOffset": 18}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 502, "endLine": 502, "startOffset": 12, "endOffset": 14}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 508, "endLine": 508, "startOffset": 20, "endOffset": 22}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 512, "endLine": 512, "startOffset": 20, "endOffset": 22}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 512, "endLine": 512, "startOffset": 61, "endOffset": 63}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 520, "endLine": 520, "startOffset": 20, "endOffset": 22}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 524, "endLine": 524, "startOffset": 22, "endOffset": 26}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 529, "endLine": 529, "startOffset": 20, "endOffset": 22}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 550, "endLine": 550, "startOffset": 16, "endOffset": 18}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 554, "endLine": 554, "startOffset": 16, "endOffset": 18}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 554, "endLine": 554, "startOffset": 57, "endOffset": 59}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 564, "endLine": 564, "startOffset": 16, "endOffset": 18}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 568, "endLine": 568, "startOffset": 18, "endOffset": 22}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 574, "endLine": 574, "startOffset": 16, "endOffset": 18}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 593, "endLine": 593, "startOffset": 12, "endOffset": 14}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 612, "endLine": 612, "startOffset": 16, "endOffset": 18}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 619, "endLine": 619, "startOffset": 16, "endOffset": 18}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 634, "endLine": 634, "startOffset": 18, "endOffset": 22}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 623, "endLine": 623, "startOffset": 20, "endOffset": 22}, "msg": "+4 (incl. 3 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 626, "endLine": 626, "startOffset": 22, "endOffset": 26}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 623, "endLine": 623, "startOffset": 34, "endOffset": 36}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 662, "endLine": 662, "startOffset": 16, "endOffset": 18}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 670, "endLine": 670, "startOffset": 12, "endOffset": 14}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 677, "endLine": 677, "startOffset": 12, "endOffset": 14}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 688, "endLine": 688, "startOffset": 12, "endOffset": 14}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 693, "endLine": 693, "startOffset": 14, "endOffset": 19}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 699, "endLine": 699, "startOffset": 12, "endOffset": 14}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 701, "endLine": 701, "startOffset": 16, "endOffset": 18}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 706, "endLine": 706, "startOffset": 14, "endOffset": 19}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 712, "endLine": 712, "startOffset": 12, "endOffset": 14}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 715, "endLine": 715, "startOffset": 12, "endOffset": 14}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 715, "endLine": 715, "startOffset": 36, "endOffset": 38}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 723, "endLine": 723, "startOffset": 12, "endOffset": 14}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 736, "endLine": 736, "startOffset": 12, "endOffset": 14}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 746, "endLine": 746, "startOffset": 16, "endOffset": 18}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 750, "endLine": 750, "startOffset": 16, "endOffset": 18}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 750, "endLine": 750, "startOffset": 57, "endOffset": 59}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 760, "endLine": 760, "startOffset": 16, "endOffset": 18}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 763, "endLine": 763, "startOffset": 18, "endOffset": 22}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/app.js", "textRange": {"startLine": 769, "endLine": 769, "startOffset": 16, "endOffset": 18}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}], "status": "OPEN", "message": "Refactor this function to reduce its Cognitive Complexity from 244 to the 15 allowed.", "effort": "3h54min", "debt": "3h54min", "author": "<EMAIL>", "tags": ["brain-overload"], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLC0hv2Xsi9SWySM", "rule": "javascript:S1126", "severity": "MINOR", "component": "octopus-interfacer:static/js/app.js", "project": "octopus-interfacer", "line": 715, "hash": "8ec2642b6582cb55fd9750750ddf26c9", "textRange": {"startLine": 715, "endLine": 717, "startOffset": 12, "endOffset": 13}, "flows": [], "status": "OPEN", "message": "Replace this if-then-else flow by a single return statement.", "effort": "2min", "debt": "2min", "author": "<EMAIL>", "tags": ["clumsy"], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": true, "messageFormattings": []}, {"key": "AZewkLC-hv2Xsi9SWySN", "rule": "javascript:S3776", "severity": "CRITICAL", "component": "octopus-interfacer:static/js/report.js", "project": "octopus-interfacer", "line": 7, "hash": "c8869491e103e598f3b69c6413df072c", "textRange": {"startLine": 7, "endLine": 7, "startOffset": 32, "endOffset": 34}, "flows": [{"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 36, "endLine": 36, "startOffset": 16, "endOffset": 18}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 38, "endLine": 38, "startOffset": 20, "endOffset": 22}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 44, "endLine": 44, "startOffset": 22, "endOffset": 26}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 55, "endLine": 55, "startOffset": 16, "endOffset": 18}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 62, "endLine": 62, "startOffset": 53, "endOffset": 55}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 71, "endLine": 71, "startOffset": 20, "endOffset": 22}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 71, "endLine": 71, "startOffset": 29, "endOffset": 31}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 73, "endLine": 73, "startOffset": 67, "endOffset": 69}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 80, "endLine": 80, "startOffset": 14, "endOffset": 19}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 109, "endLine": 109, "startOffset": 16, "endOffset": 18}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 117, "endLine": 117, "startOffset": 12, "endOffset": 14}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 123, "endLine": 123, "startOffset": 50, "endOffset": 52}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 134, "endLine": 134, "startOffset": 12, "endOffset": 14}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 134, "endLine": 134, "startOffset": 33, "endOffset": 35}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 139, "endLine": 139, "startOffset": 16, "endOffset": 18}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 139, "endLine": 139, "startOffset": 37, "endOffset": 39}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 147, "endLine": 147, "startOffset": 56, "endOffset": 57}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 148, "endLine": 148, "startOffset": 120, "endOffset": 122}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 158, "endLine": 158, "startOffset": 16, "endOffset": 18}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 158, "endLine": 158, "startOffset": 49, "endOffset": 51}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 177, "endLine": 177, "startOffset": 16, "endOffset": 19}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 184, "endLine": 184, "startOffset": 24, "endOffset": 26}, "msg": "+4 (incl. 3 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 192, "endLine": 192, "startOffset": 83, "endOffset": 85}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 194, "endLine": 194, "startOffset": 28, "endOffset": 30}, "msg": "+4 (incl. 3 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 195, "endLine": 195, "startOffset": 105, "endOffset": 107}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 220, "endLine": 220, "startOffset": 91, "endOffset": 93}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 224, "endLine": 224, "startOffset": 60, "endOffset": 61}, "msg": "+4 (incl. 3 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 226, "endLine": 226, "startOffset": 65, "endOffset": 67}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 236, "endLine": 236, "startOffset": 72, "endOffset": 74}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 239, "endLine": 239, "startOffset": 93, "endOffset": 95}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 274, "endLine": 274, "startOffset": 87, "endOffset": 88}, "msg": "+4 (incl. 3 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 275, "endLine": 275, "startOffset": 83, "endOffset": 85}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 278, "endLine": 278, "startOffset": 87, "endOffset": 88}, "msg": "+4 (incl. 3 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 279, "endLine": 279, "startOffset": 83, "endOffset": 85}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 287, "endLine": 287, "startOffset": 63, "endOffset": 65}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 288, "endLine": 288, "startOffset": 83, "endOffset": 85}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 290, "endLine": 290, "startOffset": 28, "endOffset": 30}, "msg": "+4 (incl. 3 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 290, "endLine": 290, "startOffset": 47, "endOffset": 49}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 298, "endLine": 298, "startOffset": 88, "endOffset": 89}, "msg": "+4 (incl. 3 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 302, "endLine": 302, "startOffset": 83, "endOffset": 85}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 303, "endLine": 303, "startOffset": 76, "endOffset": 78}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 314, "endLine": 314, "startOffset": 61, "endOffset": 63}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 315, "endLine": 315, "startOffset": 58, "endOffset": 60}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 318, "endLine": 318, "startOffset": 72, "endOffset": 74}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 319, "endLine": 319, "startOffset": 75, "endOffset": 77}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 320, "endLine": 320, "startOffset": 77, "endOffset": 79}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 323, "endLine": 323, "startOffset": 75, "endOffset": 76}, "msg": "+4 (incl. 3 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 325, "endLine": 325, "startOffset": 90, "endOffset": 91}, "msg": "+4 (incl. 3 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 327, "endLine": 327, "startOffset": 78, "endOffset": 79}, "msg": "+4 (incl. 3 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 330, "endLine": 330, "startOffset": 26, "endOffset": 31}, "msg": "+4 (incl. 3 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 333, "endLine": 333, "startOffset": 96, "endOffset": 98}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 349, "endLine": 349, "startOffset": 20, "endOffset": 22}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 349, "endLine": 349, "startOffset": 43, "endOffset": 45}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 370, "endLine": 370, "startOffset": 16, "endOffset": 18}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 375, "endLine": 375, "startOffset": 14, "endOffset": 19}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 386, "endLine": 386, "startOffset": 173, "endOffset": 175}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 388, "endLine": 388, "startOffset": 16, "endOffset": 18}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 393, "endLine": 393, "startOffset": 16, "endOffset": 18}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 395, "endLine": 395, "startOffset": 24, "endOffset": 25}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 402, "endLine": 402, "startOffset": 16, "endOffset": 18}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 403, "endLine": 403, "startOffset": 50, "endOffset": 52}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 407, "endLine": 407, "startOffset": 49, "endOffset": 51}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 410, "endLine": 410, "startOffset": 14, "endOffset": 19}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 412, "endLine": 412, "startOffset": 43, "endOffset": 45}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 420, "endLine": 420, "startOffset": 39, "endOffset": 41}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 422, "endLine": 422, "startOffset": 16, "endOffset": 18}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 425, "endLine": 425, "startOffset": 18, "endOffset": 22}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 431, "endLine": 431, "startOffset": 16, "endOffset": 18}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 436, "endLine": 436, "startOffset": 16, "endOffset": 18}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 441, "endLine": 441, "startOffset": 16, "endOffset": 18}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 442, "endLine": 442, "startOffset": 49, "endOffset": 51}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 449, "endLine": 449, "startOffset": 16, "endOffset": 18}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 451, "endLine": 451, "startOffset": 20, "endOffset": 22}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 464, "endLine": 464, "startOffset": 22, "endOffset": 26}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 470, "endLine": 470, "startOffset": 35, "endOffset": 36}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 471, "endLine": 471, "startOffset": 14, "endOffset": 19}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 482, "endLine": 482, "startOffset": 16, "endOffset": 18}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 487, "endLine": 487, "startOffset": 16, "endOffset": 18}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 492, "endLine": 492, "startOffset": 16, "endOffset": 18}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 493, "endLine": 493, "startOffset": 49, "endOffset": 51}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 497, "endLine": 497, "startOffset": 16, "endOffset": 18}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 502, "endLine": 502, "startOffset": 18, "endOffset": 22}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 513, "endLine": 513, "startOffset": 14, "endOffset": 19}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 532, "endLine": 532, "startOffset": 16, "endOffset": 18}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 538, "endLine": 538, "startOffset": 16, "endOffset": 18}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 546, "endLine": 546, "startOffset": 16, "endOffset": 18}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 555, "endLine": 555, "startOffset": 35, "endOffset": 37}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 556, "endLine": 556, "startOffset": 14, "endOffset": 19}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 565, "endLine": 565, "startOffset": 12, "endOffset": 14}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 566, "endLine": 566, "startOffset": 50, "endOffset": 52}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 571, "endLine": 571, "startOffset": 12, "endOffset": 14}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 574, "endLine": 574, "startOffset": 12, "endOffset": 14}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 575, "endLine": 575, "startOffset": 70, "endOffset": 72}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 579, "endLine": 579, "startOffset": 12, "endOffset": 14}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 579, "endLine": 579, "startOffset": 29, "endOffset": 31}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 584, "endLine": 584, "startOffset": 20, "endOffset": 22}, "msg": "+4 (incl. 3 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 584, "endLine": 584, "startOffset": 52, "endOffset": 54}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 586, "endLine": 586, "startOffset": 62, "endOffset": 64}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 600, "endLine": 600, "startOffset": 12, "endOffset": 14}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 603, "endLine": 603, "startOffset": 12, "endOffset": 14}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 605, "endLine": 605, "startOffset": 62, "endOffset": 64}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 606, "endLine": 606, "startOffset": 62, "endOffset": 64}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 610, "endLine": 610, "startOffset": 12, "endOffset": 14}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 610, "endLine": 610, "startOffset": 29, "endOffset": 31}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 618, "endLine": 618, "startOffset": 20, "endOffset": 22}, "msg": "+4 (incl. 3 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 618, "endLine": 618, "startOffset": 54, "endOffset": 56}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 620, "endLine": 620, "startOffset": 68, "endOffset": 70}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 634, "endLine": 634, "startOffset": 12, "endOffset": 14}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 637, "endLine": 637, "startOffset": 12, "endOffset": 14}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 640, "endLine": 640, "startOffset": 62, "endOffset": 64}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 644, "endLine": 644, "startOffset": 12, "endOffset": 14}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 644, "endLine": 644, "startOffset": 29, "endOffset": 31}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 652, "endLine": 652, "startOffset": 20, "endOffset": 22}, "msg": "+4 (incl. 3 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 652, "endLine": 652, "startOffset": 52, "endOffset": 54}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 654, "endLine": 654, "startOffset": 69, "endOffset": 71}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 666, "endLine": 666, "startOffset": 12, "endOffset": 14}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 666, "endLine": 666, "startOffset": 23, "endOffset": 25}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 676, "endLine": 676, "startOffset": 12, "endOffset": 14}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 676, "endLine": 676, "startOffset": 23, "endOffset": 25}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 685, "endLine": 685, "startOffset": 12, "endOffset": 14}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 692, "endLine": 692, "startOffset": 12, "endOffset": 14}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 741, "endLine": 741, "startOffset": 16, "endOffset": 19}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 744, "endLine": 744, "startOffset": 20, "endOffset": 22}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 744, "endLine": 744, "startOffset": 50, "endOffset": 52}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 752, "endLine": 752, "startOffset": 65, "endOffset": 67}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 754, "endLine": 754, "startOffset": 57, "endOffset": 59}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 755, "endLine": 755, "startOffset": 58, "endOffset": 60}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 756, "endLine": 756, "startOffset": 51, "endOffset": 53}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 757, "endLine": 757, "startOffset": 50, "endOffset": 52}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 764, "endLine": 764, "startOffset": 20, "endOffset": 22}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 764, "endLine": 764, "startOffset": 44, "endOffset": 46}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 772, "endLine": 772, "startOffset": 28, "endOffset": 30}, "msg": "+4 (incl. 3 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 777, "endLine": 777, "startOffset": 99, "endOffset": 101}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 780, "endLine": 780, "startOffset": 32, "endOffset": 34}, "msg": "+5 (incl. 4 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 782, "endLine": 782, "startOffset": 34, "endOffset": 38}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 793, "endLine": 793, "startOffset": 28, "endOffset": 30}, "msg": "+4 (incl. 3 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 805, "endLine": 805, "startOffset": 30, "endOffset": 34}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 793, "endLine": 793, "startOffset": 45, "endOffset": 47}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 795, "endLine": 795, "startOffset": 83, "endOffset": 85}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 796, "endLine": 796, "startOffset": 88, "endOffset": 90}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 797, "endLine": 797, "startOffset": 88, "endOffset": 90}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 808, "endLine": 808, "startOffset": 99, "endOffset": 101}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 811, "endLine": 811, "startOffset": 32, "endOffset": 34}, "msg": "+5 (incl. 4 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 813, "endLine": 813, "startOffset": 34, "endOffset": 38}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 819, "endLine": 819, "startOffset": 26, "endOffset": 31}, "msg": "+4 (incl. 3 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 822, "endLine": 822, "startOffset": 95, "endOffset": 97}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 825, "endLine": 825, "startOffset": 28, "endOffset": 30}, "msg": "+5 (incl. 4 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 827, "endLine": 827, "startOffset": 30, "endOffset": 34}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 838, "endLine": 838, "startOffset": 75, "endOffset": 77}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 839, "endLine": 839, "startOffset": 61, "endOffset": 63}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 854, "endLine": 854, "startOffset": 53, "endOffset": 55}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 855, "endLine": 855, "startOffset": 51, "endOffset": 53}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 856, "endLine": 856, "startOffset": 48, "endOffset": 50}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 857, "endLine": 857, "startOffset": 57, "endOffset": 59}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 858, "endLine": 858, "startOffset": 83, "endOffset": 85}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 859, "endLine": 859, "startOffset": 43, "endOffset": 45}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 860, "endLine": 860, "startOffset": 49, "endOffset": 51}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 861, "endLine": 861, "startOffset": 87, "endOffset": 89}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 862, "endLine": 862, "startOffset": 89, "endOffset": 91}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 863, "endLine": 863, "startOffset": 54, "endOffset": 56}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 864, "endLine": 864, "startOffset": 59, "endOffset": 61}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 865, "endLine": 865, "startOffset": 63, "endOffset": 65}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 867, "endLine": 867, "startOffset": 45, "endOffset": 47}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 868, "endLine": 868, "startOffset": 37, "endOffset": 39}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 869, "endLine": 869, "startOffset": 43, "endOffset": 45}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 870, "endLine": 870, "startOffset": 56, "endOffset": 57}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 900, "endLine": 900, "startOffset": 102, "endOffset": 103}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 909, "endLine": 909, "startOffset": 14, "endOffset": 19}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 921, "endLine": 921, "startOffset": 12, "endOffset": 14}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 928, "endLine": 928, "startOffset": 12, "endOffset": 14}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 943, "endLine": 943, "startOffset": 16, "endOffset": 18}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 958, "endLine": 958, "startOffset": 28, "endOffset": 29}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 958, "endLine": 958, "startOffset": 60, "endOffset": 61}, "msg": "+4 (incl. 3 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 988, "endLine": 988, "startOffset": 20, "endOffset": 22}, "msg": "+3 (incl. 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 988, "endLine": 988, "startOffset": 44, "endOffset": 46}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 1009, "endLine": 1009, "startOffset": 98, "endOffset": 99}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:static/js/report.js", "textRange": {"startLine": 1018, "endLine": 1018, "startOffset": 14, "endOffset": 19}, "msg": "+2 (incl. 1 for nesting)", "msgFormattings": []}]}], "status": "OPEN", "message": "Refactor this function to reduce its Cognitive Complexity from 319 to the 15 allowed.", "effort": "5h9min", "debt": "5h9min", "author": "<EMAIL>", "tags": ["brain-overload"], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLC-hv2Xsi9SWySO", "rule": "javascript:S1481", "severity": "MINOR", "component": "octopus-interfacer:static/js/report.js", "project": "octopus-interfacer", "line": 236, "hash": "ed9592a303ca78466d3e7700bd4d21ae", "textRange": {"startLine": 236, "endLine": 236, "startOffset": 34, "endOffset": 50}, "flows": [], "status": "OPEN", "message": "Remove the declaration of the unused 'advancementValue' variable.", "effort": "5min", "debt": "5min", "author": "<EMAIL>", "tags": ["unused"], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLC-hv2Xsi9SWySP", "rule": "javascript:S1854", "severity": "MAJOR", "component": "octopus-interfacer:static/js/report.js", "project": "octopus-interfacer", "line": 236, "hash": "ed9592a303ca78466d3e7700bd4d21ae", "textRange": {"startLine": 236, "endLine": 236, "startOffset": 34, "endOffset": 50}, "flows": [], "status": "OPEN", "message": "Remove this useless assignment to variable \"advancementValue\".", "effort": "15min", "debt": "15min", "author": "<EMAIL>", "tags": ["cwe", "unused"], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLC-hv2Xsi9SWySQ", "rule": "javascript:S1481", "severity": "MINOR", "component": "octopus-interfacer:static/js/report.js", "project": "octopus-interfacer", "line": 749, "hash": "95a479f591c5d16066b438142f82beb6", "textRange": {"startLine": 749, "endLine": 749, "startOffset": 26, "endOffset": 42}, "flows": [], "status": "OPEN", "message": "Remove the declaration of the unused 'currentYearHours' variable.", "effort": "5min", "debt": "5min", "author": "<EMAIL>", "tags": ["unused"], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLC-hv2Xsi9SWySR", "rule": "javascript:S1854", "severity": "MAJOR", "component": "octopus-interfacer:static/js/report.js", "project": "octopus-interfacer", "line": 749, "hash": "95a479f591c5d16066b438142f82beb6", "textRange": {"startLine": 749, "endLine": 749, "startOffset": 26, "endOffset": 42}, "flows": [], "status": "OPEN", "message": "Remove this useless assignment to variable \"currentYearHours\".", "effort": "15min", "debt": "15min", "author": "<EMAIL>", "tags": ["cwe", "unused"], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLC-hv2Xsi9SWySS", "rule": "javascript:S1481", "severity": "MINOR", "component": "octopus-interfacer:static/js/report.js", "project": "octopus-interfacer", "line": 838, "hash": "130d400e06aa8a8867f60bdc1463d8ff", "textRange": {"startLine": 838, "endLine": 838, "startOffset": 26, "endOffset": 46}, "flows": [], "status": "OPEN", "message": "Remove the declaration of the unused 'advancementLaborCost' variable.", "effort": "5min", "debt": "5min", "author": "<EMAIL>", "tags": ["unused"], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLC_hv2Xsi9SWyST", "rule": "javascript:S1854", "severity": "MAJOR", "component": "octopus-interfacer:static/js/report.js", "project": "octopus-interfacer", "line": 838, "hash": "130d400e06aa8a8867f60bdc1463d8ff", "textRange": {"startLine": 838, "endLine": 838, "startOffset": 26, "endOffset": 46}, "flows": [], "status": "OPEN", "message": "Remove this useless assignment to variable \"advancementLaborCost\".", "effort": "15min", "debt": "15min", "author": "<EMAIL>", "tags": ["cwe", "unused"], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLDlhv2Xsi9SWySt", "rule": "Web:TableWithoutCaptionCheck", "severity": "MINOR", "component": "octopus-interfacer:templates/chat.html", "project": "octopus-interfacer", "line": 88, "hash": "4b1a0dde12800e7bf9b0ba0531ca6d50", "textRange": {"startLine": 88, "endLine": 88, "startOffset": 44, "endOffset": 70}, "flows": [], "status": "OPEN", "message": "Add a description to this table.", "effort": "5min", "debt": "5min", "author": "<EMAIL>", "tags": ["accessibility", "wcag2-a"], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "BUG", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLDfhv2Xsi9SWySr", "rule": "Web:TableWithoutCaptionCheck", "severity": "MINOR", "component": "octopus-interfacer:templates/index.html", "project": "octopus-interfacer", "line": 59, "hash": "4b1a0dde12800e7bf9b0ba0531ca6d50", "textRange": {"startLine": 59, "endLine": 59, "startOffset": 28, "endOffset": 54}, "flows": [], "status": "OPEN", "message": "Add a description to this table.", "effort": "5min", "debt": "5min", "author": "<EMAIL>", "tags": ["accessibility", "wcag2-a"], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "BUG", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLDbhv2Xsi9SWySq", "rule": "Web:TableWithoutCaptionCheck", "severity": "MINOR", "component": "octopus-interfacer:templates/invoicing.html", "project": "octopus-interfacer", "line": 122, "hash": "6a8430da617e4e07d63cd815ccdb49e3", "textRange": {"startLine": 122, "endLine": 122, "startOffset": 24, "endOffset": 57}, "flows": [], "status": "OPEN", "message": "Add a description to this table.", "effort": "5min", "debt": "5min", "author": "<EMAIL>", "tags": ["accessibility", "wcag2-a"], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "BUG", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLDshv2Xsi9SWySy", "rule": "Web:TableWithoutCaptionCheck", "severity": "MINOR", "component": "octopus-interfacer:templates/landing.html", "project": "octopus-interfacer", "line": 52, "hash": "8d98274f2a040bc976602128182ad2cd", "textRange": {"startLine": 52, "endLine": 52, "startOffset": 40, "endOffset": 85}, "flows": [], "status": "OPEN", "message": "Add a description to this table.", "effort": "5min", "debt": "5min", "author": "<EMAIL>", "tags": ["accessibility", "wcag2-a"], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "BUG", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLDohv2Xsi9SWySu", "rule": "Web:TableWithoutCaptionCheck", "severity": "MINOR", "component": "octopus-interfacer:templates/report.html", "project": "octopus-interfacer", "line": 93, "hash": "c31e8fb25e4ef488da6e45fddc19e28a", "textRange": {"startLine": 93, "endLine": 93, "startOffset": 20, "endOffset": 87}, "flows": [], "status": "OPEN", "message": "Add a description to this table.", "effort": "5min", "debt": "5min", "author": "<EMAIL>", "tags": ["accessibility", "wcag2-a"], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "BUG", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLDohv2Xsi9SWySv", "rule": "Web:S5256", "severity": "MAJOR", "component": "octopus-interfacer:templates/report.html", "project": "octopus-interfacer", "line": 93, "hash": "c31e8fb25e4ef488da6e45fddc19e28a", "textRange": {"startLine": 93, "endLine": 93, "startOffset": 20, "endOffset": 87}, "flows": [], "status": "OPEN", "message": "Add \"<th>\" headers to this \"<table>\".", "effort": "2min", "debt": "2min", "author": "<EMAIL>", "tags": ["accessibility", "wcag2-a"], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "BUG", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLDohv2Xsi9SWySw", "rule": "Web:TableWithoutCaptionCheck", "severity": "MINOR", "component": "octopus-interfacer:templates/report.html", "project": "octopus-interfacer", "line": 115, "hash": "664fd01c985171b6c86765cb35f79978", "textRange": {"startLine": 115, "endLine": 115, "startOffset": 8, "endOffset": 58}, "flows": [], "status": "OPEN", "message": "Add a description to this table.", "effort": "5min", "debt": "5min", "author": "<EMAIL>", "tags": ["accessibility", "wcag2-a"], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "BUG", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLCihv2Xsi9SWySF", "rule": "python:S3776", "severity": "CRITICAL", "component": "octopus-interfacer:utils/export_utils.py", "project": "octopus-interfacer", "line": 12, "hash": "dd378f759a0174430935cfd77eeb44c1", "textRange": {"startLine": 12, "endLine": 12, "startOffset": 4, "endOffset": 23}, "flows": [{"locations": [{"component": "octopus-interfacer:utils/export_utils.py", "textRange": {"startLine": 24, "endLine": 24, "startOffset": 8, "endOffset": 10}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/export_utils.py", "textRange": {"startLine": 26, "endLine": 26, "startOffset": 8, "endOffset": 12}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/export_utils.py", "textRange": {"startLine": 28, "endLine": 28, "startOffset": 8, "endOffset": 12}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/export_utils.py", "textRange": {"startLine": 32, "endLine": 32, "startOffset": 8, "endOffset": 10}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/export_utils.py", "textRange": {"startLine": 44, "endLine": 44, "startOffset": 8, "endOffset": 10}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/export_utils.py", "textRange": {"startLine": 46, "endLine": 46, "startOffset": 8, "endOffset": 12}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/export_utils.py", "textRange": {"startLine": 57, "endLine": 57, "startOffset": 8, "endOffset": 10}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/export_utils.py", "textRange": {"startLine": 57, "endLine": 57, "startOffset": 34, "endOffset": 37}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/export_utils.py", "textRange": {"startLine": 60, "endLine": 60, "startOffset": 12, "endOffset": 14}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/export_utils.py", "textRange": {"startLine": 63, "endLine": 63, "startOffset": 16, "endOffset": 19}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/export_utils.py", "textRange": {"startLine": 66, "endLine": 66, "startOffset": 8, "endOffset": 12}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/export_utils.py", "textRange": {"startLine": 68, "endLine": 68, "startOffset": 12, "endOffset": 14}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/export_utils.py", "textRange": {"startLine": 71, "endLine": 71, "startOffset": 16, "endOffset": 19}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/export_utils.py", "textRange": {"startLine": 72, "endLine": 72, "startOffset": 52, "endOffset": 54}, "msg": "+4 (incl 3 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/export_utils.py", "textRange": {"startLine": 74, "endLine": 74, "startOffset": 12, "endOffset": 16}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/export_utils.py", "textRange": {"startLine": 81, "endLine": 81, "startOffset": 8, "endOffset": 12}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/export_utils.py", "textRange": {"startLine": 85, "endLine": 85, "startOffset": 47, "endOffset": 49}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/export_utils.py", "textRange": {"startLine": 92, "endLine": 92, "startOffset": 8, "endOffset": 10}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/export_utils.py", "textRange": {"startLine": 97, "endLine": 97, "startOffset": 12, "endOffset": 15}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/export_utils.py", "textRange": {"startLine": 105, "endLine": 105, "startOffset": 12, "endOffset": 15}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/export_utils.py", "textRange": {"startLine": 108, "endLine": 108, "startOffset": 16, "endOffset": 19}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/export_utils.py", "textRange": {"startLine": 109, "endLine": 109, "startOffset": 20, "endOffset": 23}, "msg": "+4 (incl 3 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/export_utils.py", "textRange": {"startLine": 110, "endLine": 110, "startOffset": 24, "endOffset": 26}, "msg": "+5 (incl 4 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/export_utils.py", "textRange": {"startLine": 122, "endLine": 122, "startOffset": 4, "endOffset": 10}, "msg": "+1", "msgFormattings": []}]}], "status": "OPEN", "message": "Refactor this function to reduce its Cognitive Complexity from 47 to the 15 allowed.", "effort": "37min", "debt": "37min", "author": "<EMAIL>", "tags": ["brain-overload"], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLCihv2Xsi9SWySG", "rule": "python:S112", "severity": "MAJOR", "component": "octopus-interfacer:utils/export_utils.py", "project": "octopus-interfacer", "line": 125, "hash": "ad94a67cad77eef5a6d6e3392ef1ecc4", "textRange": {"startLine": 125, "endLine": 125, "startOffset": 14, "endOffset": 38}, "flows": [], "status": "OPEN", "message": "Replace this generic exception class with a more specific one.", "effort": "20min", "debt": "20min", "author": "<EMAIL>", "tags": ["cwe", "error-handling"], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLCrhv2Xsi9SWySK", "rule": "python:S3776", "severity": "CRITICAL", "component": "octopus-interfacer:utils/openai_utils.py", "project": "octopus-interfacer", "line": 10, "hash": "03f590e87861324f26077a8f53e8dbed", "textRange": {"startLine": 10, "endLine": 10, "startOffset": 4, "endOffset": 26}, "flows": [{"locations": [{"component": "octopus-interfacer:utils/openai_utils.py", "textRange": {"startLine": 22, "endLine": 22, "startOffset": 8, "endOffset": 11}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/openai_utils.py", "textRange": {"startLine": 24, "endLine": 24, "startOffset": 12, "endOffset": 14}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/openai_utils.py", "textRange": {"startLine": 24, "endLine": 24, "startOffset": 90, "endOffset": 93}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/openai_utils.py", "textRange": {"startLine": 24, "endLine": 24, "startOffset": 48, "endOffset": 50}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/openai_utils.py", "textRange": {"startLine": 32, "endLine": 32, "startOffset": 16, "endOffset": 19}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/openai_utils.py", "textRange": {"startLine": 33, "endLine": 33, "startOffset": 20, "endOffset": 22}, "msg": "+4 (incl 3 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/openai_utils.py", "textRange": {"startLine": 35, "endLine": 35, "startOffset": 24, "endOffset": 26}, "msg": "+5 (incl 4 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/openai_utils.py", "textRange": {"startLine": 35, "endLine": 35, "startOffset": 226, "endOffset": 229}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/openai_utils.py", "textRange": {"startLine": 40, "endLine": 40, "startOffset": 16, "endOffset": 18}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/openai_utils.py", "textRange": {"startLine": 40, "endLine": 40, "startOffset": 52, "endOffset": 54}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/openai_utils.py", "textRange": {"startLine": 44, "endLine": 44, "startOffset": 20, "endOffset": 23}, "msg": "+4 (incl 3 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/openai_utils.py", "textRange": {"startLine": 46, "endLine": 46, "startOffset": 24, "endOffset": 26}, "msg": "+5 (incl 4 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/openai_utils.py", "textRange": {"startLine": 46, "endLine": 46, "startOffset": 49, "endOffset": 52}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/openai_utils.py", "textRange": {"startLine": 46, "endLine": 46, "startOffset": 78, "endOffset": 80}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/openai_utils.py", "textRange": {"startLine": 48, "endLine": 48, "startOffset": 28, "endOffset": 30}, "msg": "+6 (incl 5 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/openai_utils.py", "textRange": {"startLine": 48, "endLine": 48, "startOffset": 230, "endOffset": 233}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/openai_utils.py", "textRange": {"startLine": 70, "endLine": 70, "startOffset": 8, "endOffset": 11}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/openai_utils.py", "textRange": {"startLine": 125, "endLine": 125, "startOffset": 12, "endOffset": 14}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/openai_utils.py", "textRange": {"startLine": 125, "endLine": 125, "startOffset": 29, "endOffset": 32}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/openai_utils.py", "textRange": {"startLine": 145, "endLine": 145, "startOffset": 12, "endOffset": 18}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/openai_utils.py", "textRange": {"startLine": 155, "endLine": 155, "startOffset": 12, "endOffset": 14}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/openai_utils.py", "textRange": {"startLine": 161, "endLine": 161, "startOffset": 16, "endOffset": 19}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/openai_utils.py", "textRange": {"startLine": 163, "endLine": 163, "startOffset": 20, "endOffset": 22}, "msg": "+3 (incl 2 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/openai_utils.py", "textRange": {"startLine": 164, "endLine": 164, "startOffset": 24, "endOffset": 26}, "msg": "+4 (incl 3 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/openai_utils.py", "textRange": {"startLine": 166, "endLine": 166, "startOffset": 20, "endOffset": 24}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/openai_utils.py", "textRange": {"startLine": 174, "endLine": 174, "startOffset": 16, "endOffset": 19}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/openai_utils.py", "textRange": {"startLine": 177, "endLine": 177, "startOffset": 66, "endOffset": 68}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/openai_utils.py", "textRange": {"startLine": 187, "endLine": 187, "startOffset": 16, "endOffset": 18}, "msg": "+2 (incl 1 for nesting)", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/openai_utils.py", "textRange": {"startLine": 189, "endLine": 189, "startOffset": 16, "endOffset": 20}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/openai_utils.py", "textRange": {"startLine": 201, "endLine": 201, "startOffset": 12, "endOffset": 14}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/openai_utils.py", "textRange": {"startLine": 201, "endLine": 201, "startOffset": 57, "endOffset": 60}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/openai_utils.py", "textRange": {"startLine": 223, "endLine": 223, "startOffset": 12, "endOffset": 14}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/openai_utils.py", "textRange": {"startLine": 261, "endLine": 261, "startOffset": 12, "endOffset": 16}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/openai_utils.py", "textRange": {"startLine": 273, "endLine": 273, "startOffset": 8, "endOffset": 14}, "msg": "+1", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/openai_utils.py", "textRange": {"startLine": 281, "endLine": 281, "startOffset": 4, "endOffset": 10}, "msg": "+1", "msgFormattings": []}]}], "status": "OPEN", "message": "Refactor this function to reduce its Cognitive Complexity from 67 to the 15 allowed.", "effort": "57min", "debt": "57min", "author": "<EMAIL>", "tags": ["brain-overload"], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLCnhv2Xsi9SWySH", "rule": "python:S1192", "severity": "CRITICAL", "component": "octopus-interfacer:utils/swagger_utils.py", "project": "octopus-interfacer", "line": 13, "hash": "75cf809c20abfc14336efcd4ef8f97bc", "textRange": {"startLine": 13, "endLine": 13, "startOffset": 52, "endOffset": 70}, "flows": [{"locations": [{"component": "octopus-interfacer:utils/swagger_utils.py", "textRange": {"startLine": 15, "endLine": 15, "startOffset": 61, "endOffset": 79}, "msg": "Duplication", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/swagger_utils.py", "textRange": {"startLine": 16, "endLine": 16, "startOffset": 84, "endOffset": 102}, "msg": "Duplication", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/swagger_utils.py", "textRange": {"startLine": 126, "endLine": 126, "startOffset": 7, "endOffset": 25}, "msg": "Duplication", "msgFormattings": []}]}, {"locations": [{"component": "octopus-interfacer:utils/swagger_utils.py", "textRange": {"startLine": 127, "endLine": 127, "startOffset": 34, "endOffset": 52}, "msg": "Duplication", "msgFormattings": []}]}], "status": "OPEN", "message": "Define a constant instead of duplicating this literal '/v1/swagger.json' 5 times.", "effort": "10min", "debt": "10min", "author": "<EMAIL>", "tags": ["design"], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLCnhv2Xsi9SWySI", "rule": "python:S112", "severity": "MAJOR", "component": "octopus-interfacer:utils/swagger_utils.py", "project": "octopus-interfacer", "line": 46, "hash": "445652263f10e639e5bc36bda60b15bf", "textRange": {"startLine": 46, "endLine": 46, "startOffset": 14, "endOffset": 70}, "flows": [], "status": "OPEN", "message": "Replace this generic exception class with a more specific one.", "effort": "20min", "debt": "20min", "author": "<EMAIL>", "tags": ["cwe", "error-handling"], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}, {"key": "AZewkLCnhv2Xsi9SWySJ", "rule": "python:S5754", "severity": "CRITICAL", "component": "octopus-interfacer:utils/swagger_utils.py", "project": "octopus-interfacer", "line": 115, "hash": "3e264f5fbab10735799511f21ca70842", "textRange": {"startLine": 115, "endLine": 115, "startOffset": 12, "endOffset": 18}, "flows": [], "status": "OPEN", "message": "Specify an exception class to catch or reraise the exception", "effort": "5min", "debt": "5min", "author": "<EMAIL>", "tags": ["bad-practice", "error-handling", "suspicious"], "creationDate": "2025-06-27T08:13:42+0000", "updateDate": "2025-06-27T08:45:45+0000", "type": "CODE_SMELL", "scope": "MAIN", "quickFixAvailable": false, "messageFormattings": []}], "components": [{"key": "octopus-interfacer:app/controllers/invoicing_controller.py", "enabled": true, "qualifier": "FIL", "name": "invoicing_controller.py", "longName": "app/controllers/invoicing_controller.py", "path": "app/controllers/invoicing_controller.py"}, {"key": "octopus-interfacer:static/css/styles.css", "enabled": true, "qualifier": "FIL", "name": "styles.css", "longName": "static/css/styles.css", "path": "static/css/styles.css"}, {"key": "octopus-interfacer:app/models/user.py", "enabled": true, "qualifier": "FIL", "name": "user.py", "longName": "app/models/user.py", "path": "app/models/user.py"}, {"key": "octopus-interfacer:app/controllers/report_controller.py", "enabled": true, "qualifier": "FIL", "name": "report_controller.py", "longName": "app/controllers/report_controller.py", "path": "app/controllers/report_controller.py"}, {"key": "octopus-interfacer:static/js/app.js", "enabled": true, "qualifier": "FIL", "name": "app.js", "longName": "static/js/app.js", "path": "static/js/app.js"}, {"key": "octopus-interfacer:static/js/report.js", "enabled": true, "qualifier": "FIL", "name": "report.js", "longName": "static/js/report.js", "path": "static/js/report.js"}, {"key": "octopus-interfacer:utils/swagger_utils.py", "enabled": true, "qualifier": "FIL", "name": "swagger_utils.py", "longName": "utils/swagger_utils.py", "path": "utils/swagger_utils.py"}, {"key": "octopus-interfacer:templates/report.html", "enabled": true, "qualifier": "FIL", "name": "report.html", "longName": "templates/report.html", "path": "templates/report.html"}, {"key": "octopus-interfacer:templates/chat.html", "enabled": true, "qualifier": "FIL", "name": "chat.html", "longName": "templates/chat.html", "path": "templates/chat.html"}, {"key": "octopus-interfacer:app/services/invoicing_service.py", "enabled": true, "qualifier": "FIL", "name": "invoicing_service.py", "longName": "app/services/invoicing_service.py", "path": "app/services/invoicing_service.py"}, {"key": "octopus-interfacer:templates/landing.html", "enabled": true, "qualifier": "FIL", "name": "landing.html", "longName": "templates/landing.html", "path": "templates/landing.html"}, {"key": "octopus-interfacer:utils/openai_utils.py", "enabled": true, "qualifier": "FIL", "name": "openai_utils.py", "longName": "utils/openai_utils.py", "path": "utils/openai_utils.py"}, {"key": "octopus-interfacer:utils/export_utils.py", "enabled": true, "qualifier": "FIL", "name": "export_utils.py", "longName": "utils/export_utils.py", "path": "utils/export_utils.py"}, {"key": "octopus-interfacer:templates/index.html", "enabled": true, "qualifier": "FIL", "name": "index.html", "longName": "templates/index.html", "path": "templates/index.html"}, {"key": "octopus-interfacer:templates/invoicing.html", "enabled": true, "qualifier": "FIL", "name": "invoicing.html", "longName": "templates/invoicing.html", "path": "templates/invoicing.html"}, {"key": "octopus-interfacer:app/services/report_service.py", "enabled": true, "qualifier": "FIL", "name": "report_service.py", "longName": "app/services/report_service.py", "path": "app/services/report_service.py"}, {"key": "octopus-interfacer", "enabled": true, "qualifier": "TRK", "name": "OctopusInterfacer", "longName": "OctopusInterfacer"}, {"key": "octopus-interfacer:app/services/chat_service.py", "enabled": true, "qualifier": "FIL", "name": "chat_service.py", "longName": "app/services/chat_service.py", "path": "app/services/chat_service.py"}, {"key": "octopus-interfacer:app/services/auth_service.py", "enabled": true, "qualifier": "FIL", "name": "auth_service.py", "longName": "app/services/auth_service.py", "path": "app/services/auth_service.py"}, {"key": "octopus-interfacer:app/controllers/export_controller.py", "enabled": true, "qualifier": "FIL", "name": "export_controller.py", "longName": "app/controllers/export_controller.py", "path": "app/controllers/export_controller.py"}], "facets": []}