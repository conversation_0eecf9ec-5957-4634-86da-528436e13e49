# Informazioni di base del progetto
sonar.projectKey=octopus-interfacer
sonar.projectName=OctopusInterfacer
sonar.projectVersion=2.0.0

# Percorsi
sonar.sources=.
sonar.python.coverage.reportPaths=coverage.xml
sonar.python.xunit.reportPath=test-results.xml

# Esclusioni
sonar.exclusions=venv/**,**/venv/**,**/__pycache__/**,**/node_modules/**,**/*.pyc,legacy_backup/**,instance/**,history/**,exports/**
sonar.python.version=3.11

# Encoding del sorgente
sonar.sourceEncoding=UTF-8

# Host SonarQube
sonar.host.url=http://localhost:9000
sonar.login=squ_1a1a6b2b057244a14e01977be76d15e6a220e462
