#!/bin/bash

# Script per analizzare il codice con SonarQube
# Uso: ./quality/sonar_analysis.sh [opzioni]

set -e  # Termina lo script se un comando fallisce

# Colori per output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Directory di base del progetto (una cartella sopra quality)
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
QUALITY_DIR="$PROJECT_DIR/quality"

# Configurazione di default
SONAR_HOST="http://localhost:9000"
SONAR_TOKEN=""
PROJECT_KEY="octopus-interfacer"
PROJECT_NAME="OctopusInterfacer"
PROJECT_VERSION="2.0.0"
SOURCES_DIR="."
EXCLUSIONS="venv/**,**/venv/**,**/__pycache__/**,**/node_modules/**,**/*.pyc,legacy_backup/**,instance/**,history/**,exports/**"
PYTHON_VERSION="3.11"
ACTION="analyze"  # Azione predefinita

# Funzione di aiuto
show_help() {
    echo -e "${BLUE}Analisi SonarQube per progetti Python${NC}"
    echo "Uso: $0 [opzioni]"
    echo ""
    echo "Azioni:"
    echo "  start              Avvia SonarQube con Docker"
    echo "  stop               Ferma SonarQube"
    echo "  status             Verifica lo stato di SonarQube"
    echo "  analyze            Esegue l'analisi (default)"
    echo "  setup-tests        Configura pytest e coverage"
    echo ""
    echo "Opzioni:"
    echo "  -h, --help                Mostra questo messaggio di aiuto"
    echo "  -u, --url URL             URL del server SonarQube (default: $SONAR_HOST)"
    echo "  -t, --token TOKEN         Token di autenticazione SonarQube"
    echo "  -k, --key KEY             Chiave del progetto (default: $PROJECT_KEY)"
    echo "  -n, --name NAME           Nome del progetto (default: $PROJECT_NAME)"
    echo "  -v, --version VERSION     Versione del progetto (default: $PROJECT_VERSION)"
    echo "  -s, --source DIR          Directory sorgente (default: $SOURCES_DIR)"
    echo "  -e, --exclusions PATTERN  Pattern di esclusione (default: $EXCLUSIONS)"
    echo "  -p, --python VERSION      Versione Python (default: $PYTHON_VERSION)"
    echo ""
    echo "Esempio:"
    echo "  $0 start                  # Avvia SonarQube"
    echo "  $0 analyze --token abcdef123456 --key my-project"
}

# Parsing dei parametri
while [[ $# -gt 0 ]]; do
    key="$1"
    case $key in
        start|stop|status|analyze|setup-tests)
            ACTION="$1"
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        -u|--url)
            SONAR_HOST="$2"
            shift 2
            ;;
        -t|--token)
            SONAR_TOKEN="$2"
            shift 2
            ;;
        -k|--key)
            PROJECT_KEY="$2"
            shift 2
            ;;
        -n|--name)
            PROJECT_NAME="$2"
            shift 2
            ;;
        -v|--version)
            PROJECT_VERSION="$2"
            shift 2
            ;;
        -s|--source)
            SOURCES_DIR="$2"
            shift 2
            ;;
        -e|--exclusions)
            EXCLUSIONS="$2"
            shift 2
            ;;
        -p|--python)
            PYTHON_VERSION="$2"
            shift 2
            ;;
        *)
            echo -e "${RED}Opzione non riconosciuta: $1${NC}"
            show_help
            exit 1
            ;;
    esac
done

# Avvia SonarQube con Docker
start_sonarqube() {
    echo -e "${BLUE}Avvio SonarQube con Docker...${NC}"
    
    cd "$QUALITY_DIR"
    if [ -f "docker-compose.sonarqube.yml" ]; then
        docker-compose -f docker-compose.sonarqube.yml up -d
        echo -e "${GREEN}✓ SonarQube avviato in background${NC}"
        echo -e "${YELLOW}Attendi circa 1-2 minuti per l'avvio completo...${NC}"
        echo -e "${BLUE}URL: http://localhost:9000${NC}"
        echo -e "${BLUE}Credenziali default: admin/admin${NC}"
    else
        echo -e "${RED}File docker-compose.sonarqube.yml non trovato in $QUALITY_DIR${NC}"
        exit 1
    fi
}

# Ferma SonarQube
stop_sonarqube() {
    echo -e "${BLUE}Arresto SonarQube...${NC}"
    
    cd "$QUALITY_DIR"
    if [ -f "docker-compose.sonarqube.yml" ]; then
        docker-compose -f docker-compose.sonarqube.yml down
        echo -e "${GREEN}✓ SonarQube arrestato${NC}"
    else
        echo -e "${RED}File docker-compose.sonarqube.yml non trovato in $QUALITY_DIR${NC}"
        exit 1
    fi
}

# Verifica lo stato di SonarQube
check_sonarqube_status() {
    echo -e "${BLUE}Verifica dello stato di SonarQube...${NC}"
    
    cd "$QUALITY_DIR"
    if [ -f "docker-compose.sonarqube.yml" ]; then
        docker-compose -f docker-compose.sonarqube.yml ps
    else
        echo -e "${RED}File docker-compose.sonarqube.yml non trovato in $QUALITY_DIR${NC}"
        exit 1
    fi
    
    # Verifica se SonarQube risponde
    if curl --silent --head "$SONAR_HOST" &> /dev/null; then
        echo -e "${GREEN}✓ SonarQube è raggiungibile all'indirizzo: $SONAR_HOST${NC}"
    else
        echo -e "${RED}✗ SonarQube non è raggiungibile all'indirizzo: $SONAR_HOST${NC}"
    fi
}

# Configura pytest e coverage
setup_tests() {
    echo -e "${BLUE}Configurazione di pytest e coverage...${NC}"
    
    cd "$PROJECT_DIR"
    
    # Verifica se pytest e coverage sono installati
    if ! pip show pytest &> /dev/null || ! pip show pytest-cov &> /dev/null; then
        echo -e "${YELLOW}Installazione di pytest e pytest-cov...${NC}"
        pip install pytest pytest-cov
    fi
    
    # Crea directory tests se non esiste
    if [ ! -d "tests" ]; then
        echo -e "${YELLOW}Creazione directory tests...${NC}"
        mkdir -p tests
    fi
    
    # Crea file conftest.py se non esiste
    if [ ! -f "tests/conftest.py" ]; then
        echo -e "${YELLOW}Creazione file conftest.py...${NC}"
        cat > tests/conftest.py << EOF
import pytest
import sys
import os

# Aggiungi la directory principale al path per l'import
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

@pytest.fixture
def app():
    from app import create_app
    app = create_app({"TESTING": True})
    yield app

@pytest.fixture
def client(app):
    return app.test_client()
EOF
    fi
    
    # Crea file .coveragerc se non esiste
    if [ ! -f ".coveragerc" ]; then
        echo -e "${YELLOW}Creazione file .coveragerc...${NC}"
        cat > .coveragerc << EOF
[run]
source = app,utils
omit = 
    */venv/*
    */__pycache__/*
    */tests/*
    */legacy_backup/*

[report]
exclude_lines =
    pragma: no cover
    def __repr__
    raise NotImplementedError
    if __name__ == .__main__.:
    pass
    raise ImportError
EOF
    fi
    
    # Crea file pytest.ini se non esiste
    if [ ! -f "pytest.ini" ]; then
        echo -e "${YELLOW}Creazione file pytest.ini...${NC}"
        cat > pytest.ini << EOF
[pytest]
testpaths = tests
python_files = test_*.py
python_functions = test_*
EOF
    fi
    
    # Crea un test di esempio
    if [ ! -f "tests/test_app.py" ]; then
        echo -e "${YELLOW}Creazione test di esempio...${NC}"
        cat > tests/test_app.py << EOF
def test_health_check(client):
    """Test che il health check funzioni correttamente."""
    response = client.get('/health')
    assert response.status_code == 200
    assert response.json['status'] == 'ok'
EOF
    fi
    
    echo -e "${GREEN}✓ Configurazione completata${NC}"
    echo -e "${BLUE}Puoi eseguire i test con: pytest${NC}"
    echo -e "${BLUE}Puoi generare report di coverage con: pytest --cov=app --cov-report=xml${NC}"
}

# Verifica requisiti
check_requirements() {
    echo -e "${BLUE}Verifica dei requisiti...${NC}"
    
    # Verifica SonarScanner
    if ! command -v sonar-scanner &> /dev/null; then
        echo -e "${RED}SonarScanner non trovato. Installalo seguendo le istruzioni su:${NC}"
        echo -e "${YELLOW}https://docs.sonarqube.org/latest/analysis/scan/sonarscanner/${NC}"
        exit 1
    fi
    
    # Verifica connessione a SonarQube
    if ! curl --silent --head "$SONAR_HOST" &> /dev/null; then
        echo -e "${RED}Impossibile connettersi al server SonarQube all'indirizzo: $SONAR_HOST${NC}"
        echo -e "${YELLOW}Verifica che il server sia in esecuzione e accessibile.${NC}"
        echo -e "${YELLOW}Puoi avviarlo con: $0 start${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✓ Tutti i requisiti soddisfatti${NC}"
}

# Crea file di configurazione SonarQube
create_sonar_config() {
    echo -e "${BLUE}Creazione file di configurazione SonarQube...${NC}"
    
    cd "$PROJECT_DIR"
    
    cat > "$QUALITY_DIR/sonar-project.properties" << EOF
# Informazioni di base del progetto
sonar.projectKey=$PROJECT_KEY
sonar.projectName=$PROJECT_NAME
sonar.projectVersion=$PROJECT_VERSION

# Percorsi
sonar.sources=$SOURCES_DIR
sonar.python.coverage.reportPaths=coverage.xml
sonar.python.xunit.reportPath=test-results.xml

# Esclusioni
sonar.exclusions=$EXCLUSIONS
sonar.python.version=$PYTHON_VERSION

# Encoding del sorgente
sonar.sourceEncoding=UTF-8

# Host SonarQube
sonar.host.url=$SONAR_HOST
EOF

    if [ ! -z "$SONAR_TOKEN" ]; then
        echo "sonar.login=$SONAR_TOKEN" >> "$QUALITY_DIR/sonar-project.properties"
    fi
    
    echo -e "${GREEN}✓ File sonar-project.properties creato${NC}"
}

# Esegui analisi
run_analysis() {
    echo -e "${BLUE}Avvio analisi del codice...${NC}"
    
    cd "$PROJECT_DIR"
    
    # Esegui SonarScanner
    sonar-scanner -Dproject.settings="$QUALITY_DIR/sonar-project.properties"
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ Analisi completata con successo${NC}"
        echo -e "${GREEN}Puoi visualizzare i risultati all'indirizzo: ${SONAR_HOST}/dashboard?id=${PROJECT_KEY}${NC}"
    else
        echo -e "${RED}✗ Analisi fallita${NC}"
        exit 1
    fi
}

# Esecuzione principale in base all'azione specificata
case "$ACTION" in
    start)
        start_sonarqube
        ;;
    stop)
        stop_sonarqube
        ;;
    status)
        check_sonarqube_status
        ;;
    analyze)
        echo -e "${BLUE}=== Analisi SonarQube per $PROJECT_NAME ===${NC}"
        check_requirements
        create_sonar_config
        run_analysis
        ;;
    setup-tests)
        setup_tests
        ;;
    *)
        echo -e "${RED}Azione non riconosciuta: $ACTION${NC}"
        show_help
        exit 1
        ;;
esac 