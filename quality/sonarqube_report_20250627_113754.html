<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SonarQube Report - octopus-interfacer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        h1, h2, h3 {
            color: #0066cc;
        }
        .metrics {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 30px;
        }
        .metric-card {
            background: #f5f5f5;
            border-radius: 5px;
            padding: 15px;
            min-width: 200px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            margin-top: 10px;
        }
        .rating {
            display: inline-block;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            color: white;
            text-align: center;
            line-height: 30px;
            font-weight: bold;
            margin-right: 10px;
        }
        .rating-a { background-color: #00AA00; }
        .rating-b { background-color: #80CC00; }
        .rating-c { background-color: #FFCC00; }
        .rating-d { background-color: #FF8800; }
        .rating-e { background-color: #FF0000; }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .severity {
            font-weight: bold;
        }
        .severity-BLOCKER { color: #ff0000; }
        .severity-CRITICAL { color: #ff4500; }
        .severity-MAJOR { color: #ff8c00; }
        .severity-MINOR { color: #ffcc00; }
        .severity-INFO { color: #5cb85c; }
        .timestamp {
            color: #666;
            font-style: italic;
            margin-top: 40px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>SonarQube Report</h1>
        <p>Progetto: <strong>octopus-interfacer</strong></p>
        <p>URL: <a href="http://localhost:9000/dashboard?id=octopus-interfacer" target="_blank">http://localhost:9000/dashboard?id=octopus-interfacer</a></p>
        
        <h2>Metriche Principali</h2>
        <div class="metrics" id="metrics">
            <!-- Le metriche verranno inserite qui via JavaScript -->
        </div>
        
        <h2>Problemi Principali</h2>
        <table id="issues">
            <thead>
                <tr>
                    <th>Severità</th>
                    <th>Tipo</th>
                    <th>Componente</th>
                    <th>Messaggio</th>
                </tr>
            </thead>
            <tbody>
                <!-- I problemi verranno inseriti qui via JavaScript -->
            </tbody>
        </table>
        
        <p class="timestamp">Report generato il: Fri Jun 27 11:37:54 CEST 2025</p>
    </div>

    <script>
        // Carica i dati dal report JSON
        fetch('sonarqube_report_20250627_113754.json')
            .then(response => response.json())
            .then(data => {
                const metricsContainer = document.getElementById('metrics');
                const component = data.component;
                
                if (component && component.measures) {
                    const measures = component.measures;
                    
                    // Funzione per ottenere il valore di una metrica
                    function getMeasure(key) {
                        const measure = measures.find(m => m.metric === key);
                        return measure ? measure.value : 'N/A';
                    }
                    
                    // Funzione per convertire il rating in lettera
                    function ratingToLetter(rating) {
                        const ratingNum = parseFloat(rating);
                        if (ratingNum === 1) return 'A';
                        if (ratingNum === 2) return 'B';
                        if (ratingNum === 3) return 'C';
                        if (ratingNum === 4) return 'D';
                        if (ratingNum === 5) return 'E';
                        return '?';
                    }
                    
                    // Aggiungi le metriche
                    const metrics = [
                        { name: 'Bugs', key: 'bugs' },
                        { name: 'Vulnerabilità', key: 'vulnerabilities' },
                        { name: 'Code Smells', key: 'code_smells' },
                        { name: 'Copertura', key: 'coverage', suffix: '%' },
                        { name: 'Duplicazioni', key: 'duplicated_lines_density', suffix: '%' },
                        { name: 'Linee di Codice', key: 'ncloc' },
                        { 
                            name: 'Affidabilità', 
                            key: 'reliability_rating',
                            format: value => {
                                const letter = ratingToLetter(value);
                                return `<span class="rating rating-${letter.toLowerCase()}">${letter}</span>`;
                            }
                        },
                        { 
                            name: 'Sicurezza', 
                            key: 'security_rating',
                            format: value => {
                                const letter = ratingToLetter(value);
                                return `<span class="rating rating-${letter.toLowerCase()}">${letter}</span>`;
                            }
                        },
                        { 
                            name: 'Manutenibilità', 
                            key: 'sqale_rating',
                            format: value => {
                                const letter = ratingToLetter(value);
                                return `<span class="rating rating-${letter.toLowerCase()}">${letter}</span>`;
                            }
                        }
                    ];
                    
                    metrics.forEach(metric => {
                        const value = getMeasure(metric.key);
                        const formattedValue = metric.format ? metric.format(value) : value + (metric.suffix || '');
                        
                        const metricCard = document.createElement('div');
                        metricCard.className = 'metric-card';
                        metricCard.innerHTML = `
                            <h3>${metric.name}</h3>
                            <div class="metric-value">${formattedValue}</div>
                        `;
                        
                        metricsContainer.appendChild(metricCard);
                    });
                }
            })
            .catch(error => console.error('Errore nel caricamento delle metriche:', error));
            
        // Carica i problemi
        fetch('issues_20250627_113754.json')
            .then(response => response.json())
            .then(data => {
                const issuesTable = document.getElementById('issues').getElementsByTagName('tbody')[0];
                
                if (data.issues && data.issues.length > 0) {
                    // Mostra solo i primi 20 problemi
                    const issues = data.issues.slice(0, 20);
                    
                    issues.forEach(issue => {
                        const row = document.createElement('tr');
                        
                        const severityCell = document.createElement('td');
                        severityCell.innerHTML = `<span class="severity severity-${issue.severity}">${issue.severity}</span>`;
                        
                        const typeCell = document.createElement('td');
                        typeCell.textContent = issue.type;
                        
                        const componentCell = document.createElement('td');
                        componentCell.textContent = issue.component.split(':').pop();
                        
                        const messageCell = document.createElement('td');
                        messageCell.textContent = issue.message;
                        
                        row.appendChild(severityCell);
                        row.appendChild(typeCell);
                        row.appendChild(componentCell);
                        row.appendChild(messageCell);
                        
                        issuesTable.appendChild(row);
                    });
                } else {
                    const row = document.createElement('tr');
                    const cell = document.createElement('td');
                    cell.colSpan = 4;
                    cell.textContent = 'Nessun problema trovato';
                    row.appendChild(cell);
                    issuesTable.appendChild(row);
                }
            })
            .catch(error => console.error('Errore nel caricamento dei problemi:', error));
    </script>
</body>
</html>
