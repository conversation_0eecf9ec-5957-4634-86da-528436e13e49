{"component": {"key": "octopus-interfacer", "name": "OctopusInterfacer", "qualifier": "TRK", "measures": [{"metric": "duplicated_lines_density", "value": "1.1", "bestValue": false}, {"metric": "sqale_index", "value": "888", "bestValue": false}, {"metric": "security_rating", "value": "1.0", "bestValue": true}, {"metric": "coverage", "value": "0.9", "bestValue": false}, {"metric": "reliability_rating", "value": "5.0", "bestValue": false}, {"metric": "ncloc", "value": "9621"}, {"metric": "sqale_rating", "value": "1.0", "bestValue": true}, {"metric": "bugs", "value": "11", "bestValue": false}, {"metric": "vulnerabilities", "value": "0", "bestValue": true}, {"metric": "code_smells", "value": "46", "bestValue": false}]}}