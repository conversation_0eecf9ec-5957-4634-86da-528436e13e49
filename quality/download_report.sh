#!/bin/bash

# Script per scaricare i risultati dell'analisi di SonarQube
# Uso: ./quality/download_report.sh [token]

set -e  # Termina lo script se un comando fallisce

# Colori per output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configurazione di default
SONAR_HOST="http://localhost:9000"
SONAR_TOKEN=""
PROJECT_KEY="octopus-interfacer"
OUTPUT_DIR="./quality/reports"

# Funzione di aiuto
show_help() {
    echo -e "${BLUE}Download report SonarQube${NC}"
    echo "Uso: $0 [opzioni]"
    echo ""
    echo "Opzioni:"
    echo "  -h, --help                Mostra questo messaggio di aiuto"
    echo "  -u, --url URL             URL del server SonarQube (default: $SONAR_HOST)"
    echo "  -t, --token TOKEN         Token di autenticazione SonarQube"
    echo "  -k, --key KEY             Chiave del progetto (default: $PROJECT_KEY)"
    echo "  -o, --output DIR          Directory di output (default: $OUTPUT_DIR)"
    echo ""
    echo "Esempio:"
    echo "  $0 -t abcdef123456 -o ./reports"
}

# Parsing dei parametri
while [[ $# -gt 0 ]]; do
    key="$1"
    case $key in
        -h|--help)
            show_help
            exit 0
            ;;
        -u|--url)
            SONAR_HOST="$2"
            shift 2
            ;;
        -t|--token)
            SONAR_TOKEN="$2"
            shift 2
            ;;
        -k|--key)
            PROJECT_KEY="$2"
            shift 2
            ;;
        -o|--output)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        *)
            echo -e "${RED}Opzione non riconosciuta: $1${NC}"
            show_help
            exit 1
            ;;
    esac
done

# Verifica requisiti
if [ -z "$SONAR_TOKEN" ]; then
    echo -e "${RED}Token SonarQube non specificato. Usa -t o --token.${NC}"
    show_help
    exit 1
fi

# Crea directory di output se non esiste
mkdir -p "$OUTPUT_DIR"

# Timestamp per il nome del file
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
REPORT_FILE="$OUTPUT_DIR/sonarqube_report_${TIMESTAMP}.json"
HTML_REPORT_FILE="$OUTPUT_DIR/sonarqube_report_${TIMESTAMP}.html"

echo -e "${BLUE}Download dei risultati dell'analisi SonarQube...${NC}"

# Scarica le metriche principali
echo -e "${YELLOW}Scaricamento delle metriche principali...${NC}"
curl -s -u "$SONAR_TOKEN:" "$SONAR_HOST/api/measures/component?component=$PROJECT_KEY&metricKeys=bugs,vulnerabilities,code_smells,coverage,duplicated_lines_density,ncloc,sqale_index,reliability_rating,security_rating,sqale_rating" > "$REPORT_FILE"

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ Report JSON salvato in $REPORT_FILE${NC}"
    
    # Crea un report HTML
    echo -e "${YELLOW}Creazione del report HTML...${NC}"
    
    # Scarica le issues
    ISSUES_FILE="$OUTPUT_DIR/issues_${TIMESTAMP}.json"
    curl -s -u "$SONAR_TOKEN:" "$SONAR_HOST/api/issues/search?componentKeys=$PROJECT_KEY&resolved=false" > "$ISSUES_FILE"
    
    cat > "$HTML_REPORT_FILE" << EOF
<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SonarQube Report - $PROJECT_KEY</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        h1, h2, h3 {
            color: #0066cc;
        }
        .metrics {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 30px;
        }
        .metric-card {
            background: #f5f5f5;
            border-radius: 5px;
            padding: 15px;
            min-width: 200px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            margin-top: 10px;
        }
        .rating {
            display: inline-block;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            color: white;
            text-align: center;
            line-height: 30px;
            font-weight: bold;
            margin-right: 10px;
        }
        .rating-a { background-color: #00AA00; }
        .rating-b { background-color: #80CC00; }
        .rating-c { background-color: #FFCC00; }
        .rating-d { background-color: #FF8800; }
        .rating-e { background-color: #FF0000; }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .severity {
            font-weight: bold;
        }
        .severity-BLOCKER { color: #ff0000; }
        .severity-CRITICAL { color: #ff4500; }
        .severity-MAJOR { color: #ff8c00; }
        .severity-MINOR { color: #ffcc00; }
        .severity-INFO { color: #5cb85c; }
        .timestamp {
            color: #666;
            font-style: italic;
            margin-top: 40px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>SonarQube Report</h1>
        <p>Progetto: <strong>$PROJECT_KEY</strong></p>
        <p>URL: <a href="$SONAR_HOST/dashboard?id=$PROJECT_KEY" target="_blank">$SONAR_HOST/dashboard?id=$PROJECT_KEY</a></p>
        
        <h2>Metriche Principali</h2>
        <div class="metrics" id="metrics">
            <!-- Le metriche verranno inserite qui via JavaScript -->
        </div>
        
        <h2>Problemi Principali</h2>
        <table id="issues">
            <thead>
                <tr>
                    <th>Severità</th>
                    <th>Tipo</th>
                    <th>Componente</th>
                    <th>Messaggio</th>
                </tr>
            </thead>
            <tbody>
                <!-- I problemi verranno inseriti qui via JavaScript -->
            </tbody>
        </table>
        
        <p class="timestamp">Report generato il: $(date)</p>
    </div>

    <script>
        // Carica i dati dal report JSON
        fetch('$(basename "$REPORT_FILE")')
            .then(response => response.json())
            .then(data => {
                const metricsContainer = document.getElementById('metrics');
                const component = data.component;
                
                if (component && component.measures) {
                    const measures = component.measures;
                    
                    // Funzione per ottenere il valore di una metrica
                    function getMeasure(key) {
                        const measure = measures.find(m => m.metric === key);
                        return measure ? measure.value : 'N/A';
                    }
                    
                    // Funzione per convertire il rating in lettera
                    function ratingToLetter(rating) {
                        const ratingNum = parseFloat(rating);
                        if (ratingNum === 1) return 'A';
                        if (ratingNum === 2) return 'B';
                        if (ratingNum === 3) return 'C';
                        if (ratingNum === 4) return 'D';
                        if (ratingNum === 5) return 'E';
                        return '?';
                    }
                    
                    // Aggiungi le metriche
                    const metrics = [
                        { name: 'Bugs', key: 'bugs' },
                        { name: 'Vulnerabilità', key: 'vulnerabilities' },
                        { name: 'Code Smells', key: 'code_smells' },
                        { name: 'Copertura', key: 'coverage', suffix: '%' },
                        { name: 'Duplicazioni', key: 'duplicated_lines_density', suffix: '%' },
                        { name: 'Linee di Codice', key: 'ncloc' },
                        { 
                            name: 'Affidabilità', 
                            key: 'reliability_rating',
                            format: value => {
                                const letter = ratingToLetter(value);
                                return \`<span class="rating rating-\${letter.toLowerCase()}">\${letter}</span>\`;
                            }
                        },
                        { 
                            name: 'Sicurezza', 
                            key: 'security_rating',
                            format: value => {
                                const letter = ratingToLetter(value);
                                return \`<span class="rating rating-\${letter.toLowerCase()}">\${letter}</span>\`;
                            }
                        },
                        { 
                            name: 'Manutenibilità', 
                            key: 'sqale_rating',
                            format: value => {
                                const letter = ratingToLetter(value);
                                return \`<span class="rating rating-\${letter.toLowerCase()}">\${letter}</span>\`;
                            }
                        }
                    ];
                    
                    metrics.forEach(metric => {
                        const value = getMeasure(metric.key);
                        const formattedValue = metric.format ? metric.format(value) : value + (metric.suffix || '');
                        
                        const metricCard = document.createElement('div');
                        metricCard.className = 'metric-card';
                        metricCard.innerHTML = \`
                            <h3>\${metric.name}</h3>
                            <div class="metric-value">\${formattedValue}</div>
                        \`;
                        
                        metricsContainer.appendChild(metricCard);
                    });
                }
            })
            .catch(error => console.error('Errore nel caricamento delle metriche:', error));
            
        // Carica i problemi
        fetch('$(basename "$ISSUES_FILE")')
            .then(response => response.json())
            .then(data => {
                const issuesTable = document.getElementById('issues').getElementsByTagName('tbody')[0];
                
                if (data.issues && data.issues.length > 0) {
                    // Mostra solo i primi 20 problemi
                    const issues = data.issues.slice(0, 20);
                    
                    issues.forEach(issue => {
                        const row = document.createElement('tr');
                        
                        const severityCell = document.createElement('td');
                        severityCell.innerHTML = \`<span class="severity severity-\${issue.severity}">\${issue.severity}</span>\`;
                        
                        const typeCell = document.createElement('td');
                        typeCell.textContent = issue.type;
                        
                        const componentCell = document.createElement('td');
                        componentCell.textContent = issue.component.split(':').pop();
                        
                        const messageCell = document.createElement('td');
                        messageCell.textContent = issue.message;
                        
                        row.appendChild(severityCell);
                        row.appendChild(typeCell);
                        row.appendChild(componentCell);
                        row.appendChild(messageCell);
                        
                        issuesTable.appendChild(row);
                    });
                } else {
                    const row = document.createElement('tr');
                    const cell = document.createElement('td');
                    cell.colSpan = 4;
                    cell.textContent = 'Nessun problema trovato';
                    row.appendChild(cell);
                    issuesTable.appendChild(row);
                }
            })
            .catch(error => console.error('Errore nel caricamento dei problemi:', error));
    </script>
</body>
</html>
EOF

    echo -e "${GREEN}✓ Report HTML salvato in $HTML_REPORT_FILE${NC}"
    echo -e "${BLUE}Puoi aprire il report HTML nel tuo browser per visualizzare i risultati.${NC}"
else
    echo -e "${RED}✗ Errore durante il download del report${NC}"
    exit 1
fi 