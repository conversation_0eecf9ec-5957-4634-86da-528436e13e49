# Controllo Qualità del Codice

Questa cartella contiene gli strumenti e le configurazioni per il controllo qualità del codice di OctopusInterfacer.

## SonarQube

SonarQube è uno strumento di analisi statica del codice che aiuta a identificare problemi di qualità, bug, vulnerabilità e code smells.

### Requisiti

- Docker e docker-compose
- SonarScanner (per eseguire l'analisi)

### Installazione di SonarScanner

#### macOS
```bash
brew install sonar-scanner
```

#### Linux
```bash
wget https://binaries.sonarsource.com/Distribution/sonar-scanner-cli/sonar-scanner-cli-4.8.0.2856-linux.zip
unzip sonar-scanner-cli-4.8.0.2856-linux.zip
mv sonar-scanner-4.8.0.2856-linux /opt/sonar-scanner
echo 'export PATH=$PATH:/opt/sonar-scanner/bin' >> ~/.bashrc
source ~/.bashrc
```

#### Windows
1. Scarica SonarScanner da [qui](https://docs.sonarqube.org/latest/analysis/scan/sonarscanner/)
2. Estrai il file zip
3. Aggiungi la cartella `bin` al PATH di sistema

### Utilizzo

Lo script `sonar_analysis.sh` fornisce diverse funzionalità:

#### Avviare SonarQube
```bash
./quality/sonar_analysis.sh start
```
SonarQube sarà disponibile all'indirizzo http://localhost:9000 (credenziali default: admin/admin)

#### Configurare i test
```bash
./quality/sonar_analysis.sh setup-tests
```
Questo comando configura pytest, coverage e crea un test di esempio.

#### Eseguire l'analisi
```bash
./quality/sonar_analysis.sh analyze
```

#### Verificare lo stato di SonarQube
```bash
./quality/sonar_analysis.sh status
```

#### Fermare SonarQube
```bash
./quality/sonar_analysis.sh stop
```

### Workflow completo

1. Avvia SonarQube: `./quality/sonar_analysis.sh start`
2. Configura i test: `./quality/sonar_analysis.sh setup-tests`
3. Esegui i test con coverage: `pytest --cov=app --cov-report=xml`
4. Esegui l'analisi: `./quality/sonar_analysis.sh analyze`
5. Visualizza i risultati su http://localhost:9000

### Configurazione

Il file `sonar-project.properties` contiene la configurazione per l'analisi SonarQube. Puoi modificarlo per personalizzare l'analisi.

### Token SonarQube

Dopo il primo accesso a SonarQube:
1. Vai su http://localhost:9000
2. Accedi con admin/admin (cambia la password se richiesto)
3. Vai su Account > Security
4. Genera un token
5. Usa il token con il comando: `./quality/sonar_analysis.sh analyze -t il-tuo-token` 