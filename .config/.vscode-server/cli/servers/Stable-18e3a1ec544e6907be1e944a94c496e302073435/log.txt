*
* Visual Studio Code Server
*
* By using the software, you agree to
* the Visual Studio Code Server License Terms (https://aka.ms/vscode-server-license) and
* the Microsoft Privacy Statement (https://privacy.microsoft.com/en-US/privacystatement).
*
Server bound to /tmp/code-************************************
Extension host agent listening on /tmp/code-************************************

[21:57:42] 




[21:57:42] Extension host agent started.
[21:57:42] [/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.2]: This extension is using the API proposal 'languageModelDataPart' that is not compatible with the current version of VS Code.
[21:57:42] [/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.2]: This extension is using the API proposal 'languageModelDataPart' that is not compatible with the current version of VS Code.
[21:57:42] [<unknown>][afea41e5][ManagementConnection] New connection established.
[21:57:42] [<unknown>][345e7279][ExtensionHostConnection] New connection established.
[21:57:42] [<unknown>][345e7279][ExtensionHostConnection] <23510> Launched Extension Host Process.
[21:57:43] [/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.2]: This extension is using the API proposal 'languageModelDataPart' that is not compatible with the current version of VS Code.
[21:57:43] ComputeTargetPlatform: linux-x64
[21:57:43] [/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.2]: This extension is using the API proposal 'languageModelDataPart' that is not compatible with the current version of VS Code.
[21:57:43] [/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.2]: This extension is using the API proposal 'languageModelDataPart' that is not compatible with the current version of VS Code.
[21:57:46] [/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.2]: This extension is using the API proposal 'languageModelDataPart' that is not compatible with the current version of VS Code.
[21:57:47] ComputeTargetPlatform: linux-x64
[21:57:47] [/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.2]: This extension is using the API proposal 'languageModelDataPart' that is not compatible with the current version of VS Code.
[21:57:47] [/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.2]: This extension is using the API proposal 'languageModelDataPart' that is not compatible with the current version of VS Code.
[21:57:47] [/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.2]: This extension is using the API proposal 'languageModelDataPart' that is not compatible with the current version of VS Code.
[21:57:47] [/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.2]: This extension is using the API proposal 'languageModelDataPart' that is not compatible with the current version of VS Code.
[21:57:48] [/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.2]: This extension is using the API proposal 'languageModelDataPart' that is not compatible with the current version of VS Code.
[21:57:48] [/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.2]: This extension is using the API proposal 'languageModelDataPart' that is not compatible with the current version of VS Code.
[21:57:49] Getting Manifest... augment.vscode-augment
[21:57:49] Getting Manifest... github.copilot
[21:57:49] Getting Manifest... github.copilot-chat
[21:57:49] Getting Manifest... stagewise.stagewise-vscode-extension
[21:57:49] Installing extension: github.copilot {
  productVersion: { version: '1.101.1', date: '2025-06-18T13:35:12.605Z' },
  pinned: false,
  operation: 3,
  isApplicationScoped: false,
  donotVerifySignature: false,
  context: { clientTargetPlatform: 'darwin-arm64' },
  profileLocation: Br {
    scheme: 'file',
    authority: '',
    path: '/home/<USER>/.vscode-server/extensions/extensions.json',
    query: '',
    fragment: '',
    _formatted: 'file:///home/<USER>/.vscode-server/extensions/extensions.json',
    _fsPath: '/home/<USER>/.vscode-server/extensions/extensions.json'
  }
}
[21:57:49] [/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.2]: This extension is using the API proposal 'languageModelDataPart' that is not compatible with the current version of VS Code.
[21:57:49] Installing extension: stagewise.stagewise-vscode-extension {
  productVersion: { version: '1.101.1', date: '2025-06-18T13:35:12.605Z' },
  pinned: false,
  operation: 3,
  isApplicationScoped: false,
  donotVerifySignature: false,
  context: { clientTargetPlatform: 'darwin-arm64' },
  profileLocation: Br {
    scheme: 'file',
    authority: '',
    path: '/home/<USER>/.vscode-server/extensions/extensions.json',
    query: '',
    fragment: '',
    _formatted: 'file:///home/<USER>/.vscode-server/extensions/extensions.json',
    _fsPath: '/home/<USER>/.vscode-server/extensions/extensions.json'
  }
}
[21:57:49] Installing extension: github.copilot-chat {
  productVersion: { version: '1.101.1', date: '2025-06-18T13:35:12.605Z' },
  pinned: false,
  operation: 3,
  isApplicationScoped: false,
  donotVerifySignature: false,
  context: { clientTargetPlatform: 'darwin-arm64' },
  profileLocation: Br {
    scheme: 'file',
    authority: '',
    path: '/home/<USER>/.vscode-server/extensions/extensions.json',
    query: '',
    fragment: '',
    _formatted: 'file:///home/<USER>/.vscode-server/extensions/extensions.json',
    _fsPath: '/home/<USER>/.vscode-server/extensions/extensions.json'
  }
}
[21:57:49] [/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.2]: This extension is using the API proposal 'languageModelDataPart' that is not compatible with the current version of VS Code.
[21:57:49] [/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.2]: This extension is using the API proposal 'languageModelDataPart' that is not compatible with the current version of VS Code.
[21:57:49] Installing extension: augment.vscode-augment {
  productVersion: { version: '1.101.1', date: '2025-06-18T13:35:12.605Z' },
  pinned: false,
  operation: 3,
  isApplicationScoped: false,
  donotVerifySignature: false,
  context: { clientTargetPlatform: 'darwin-arm64' },
  profileLocation: Br {
    scheme: 'file',
    authority: '',
    path: '/home/<USER>/.vscode-server/extensions/extensions.json',
    query: '',
    fragment: '',
    _formatted: 'file:///home/<USER>/.vscode-server/extensions/extensions.json',
    _fsPath: '/home/<USER>/.vscode-server/extensions/extensions.json'
  }
}
[21:57:49] [/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.2]: This extension is using the API proposal 'languageModelDataPart' that is not compatible with the current version of VS Code.
[21:57:49] [/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.2]: This extension is using the API proposal 'languageModelDataPart' that is not compatible with the current version of VS Code.
[21:57:49] [/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.2]: This extension is using the API proposal 'languageModelDataPart' that is not compatible with the current version of VS Code.
[21:57:52] Extension signature verification result for github.copilot: Success. Internal Code: 0. Executed: true. Duration: 2117ms.
[21:57:52] Extension signature verification result for github.copilot-chat: Success. Internal Code: 0. Executed: true. Duration: 2118ms.
[21:57:52] Extension signature verification result for augment.vscode-augment: Success. Internal Code: 0. Executed: true. Duration: 2061ms.
[21:57:52] Extension signature verification result for stagewise.stagewise-vscode-extension: Success. Internal Code: 0. Executed: true. Duration: 2204ms.
[21:57:52] Extracted extension to file:///home/<USER>/.vscode-server/extensions/stagewise.stagewise-vscode-extension-0.6.0: stagewise.stagewise-vscode-extension
[21:57:52] Renamed to /home/<USER>/.vscode-server/extensions/stagewise.stagewise-vscode-extension-0.6.0
[21:57:52] Marked extension as removed stagewise.stagewise-vscode-extension-0.4.1
[21:57:52] [/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.2]: This extension is using the API proposal 'languageModelDataPart' that is not compatible with the current version of VS Code.
[21:57:52] Extension installed successfully: stagewise.stagewise-vscode-extension file:///home/<USER>/.vscode-server/extensions/extensions.json
[21:57:52] [/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.2]: This extension is using the API proposal 'languageModelDataPart' that is not compatible with the current version of VS Code.
[21:57:52] Extracted extension to file:///home/<USER>/.vscode-server/extensions/github.copilot-chat-0.28.3: github.copilot-chat
[21:57:53] Renamed to /home/<USER>/.vscode-server/extensions/github.copilot-chat-0.28.3
[21:57:53] [/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.2]: This extension is using the API proposal 'languageModelDataPart' that is not compatible with the current version of VS Code.
[21:57:53] Extension installed successfully: github.copilot-chat file:///home/<USER>/.vscode-server/extensions/extensions.json
[21:57:53] Marked extension as removed github.copilot-chat-0.27.2
[21:57:53] Extracted extension to file:///home/<USER>/.vscode-server/extensions/github.copilot-1.338.0: github.copilot
[21:57:53] Renamed to /home/<USER>/.vscode-server/extensions/github.copilot-1.338.0
[21:57:53] Marked extension as removed github.copilot-1.326.0
[21:57:53] Extension installed successfully: github.copilot file:///home/<USER>/.vscode-server/extensions/extensions.json
[21:57:53] Extracted extension to file:///home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.487.1: augment.vscode-augment
[21:57:53] Renamed to /home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.487.1
[21:57:53] Marked extension as removed augment.vscode-augment-0.470.1
[21:57:53] Extension installed successfully: augment.vscode-augment file:///home/<USER>/.vscode-server/extensions/extensions.json
[21:57:59] [File Watcher ('parcel')] Unexpected error: inotify_add_watch on '/home/<USER>/workspace/.config/.vscode-server/data/logs/20250530T175437/exthost2/vscode.markdown-language-features' failed: No such file or directory (EUNKNOWN) (path: /home/<USER>/workspace)
[21:57:59] [File Watcher (universal)] inotify_add_watch on '/home/<USER>/workspace/.config/.vscode-server/data/logs/20250530T175437/exthost2/vscode.markdown-language-features' failed: No such file or directory
New EH opened, aborting shutdown
[22:02:42] New EH opened, aborting shutdown
[23:47:58] [<unknown>][afea41e5][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
[23:47:59] [<unknown>][345e7279][ExtensionHostConnection] <23510> Extension Host Process exited with code: 0, signal: null.
Last EH closed, waiting before shutting down
[23:47:59] Last EH closed, waiting before shutting down
