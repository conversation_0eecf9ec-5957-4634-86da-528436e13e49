*
* Visual Studio Code Server
*
* By using the software, you agree to
* the Visual Studio Code Server License Terms (https://aka.ms/vscode-server-license) and
* the Microsoft Privacy Statement (https://privacy.microsoft.com/en-US/privacystatement).
*
Server bound to /tmp/code-************************************
Extension host agent listening on /tmp/code-************************************

[18:37:18] 




[18:37:18] Extension host agent started.
[18:37:19] [<unknown>][c7d9c71a][ManagementConnection] New connection established.
[18:37:19] [<unknown>][5cee835d][ExtensionHostConnection] New connection established.
[18:37:19] [/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.2]: This extension is using the API proposal 'languageModelDataPart' that is not compatible with the current version of VS Code.
[18:37:19] [/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.2]: This extension is using the API proposal 'languageModelDataPart' that is not compatible with the current version of VS Code.
[18:37:19] [<unknown>][5cee835d][ExtensionHostConnection] <768> Launched Extension Host Process.
[18:37:19] Deleted marked for removal extension from disk stagewise.stagewise-vscode-extension /home/<USER>/.vscode-server/extensions/stagewise.stagewise-vscode-extension-0.4.1
[18:37:19] Deleted marked for removal extension from disk augment.vscode-augment /home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.470.1
[18:37:19] Deleted marked for removal extension from disk github.copilot-chat /home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.2
[18:37:19] ComputeTargetPlatform: linux-x64
[18:37:19] Deleted marked for removal extension from disk github.copilot /home/<USER>/.vscode-server/extensions/github.copilot-1.326.0
[18:37:25] ComputeTargetPlatform: linux-x64
New EH opened, aborting shutdown
[18:42:18] New EH opened, aborting shutdown
