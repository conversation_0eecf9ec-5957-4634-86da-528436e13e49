(()=>{var e={7545:(e,t,n)=>{"use strict";n.r(t),n.d(t,{Token:()=>h,any:()=>Me,backtick:()=>ne,comment:()=>W,createAtRule:()=>Ge,createProperty:()=>Je,createRule:()=>We,default:()=>Ye,formatting:()=>De,ident:()=>A,interpolation:()=>z,keyword:()=>Le,lexer:()=>Xe,parseMediaExpression:()=>qe,parsePropertyName:()=>Ue,parsePropertyValue:()=>Fe,parseSelector:()=>Be,selector:()=>Ne,string:()=>B,url:()=>Ae,value:()=>ze,variable:()=>Pe,whitespace:()=>J});var r=n(8769),o=n(6613);class i{constructor(e){this.type=e,this.children=[],this.parent=null}get firstChild(){return this.children[0]}get nextSibling(){const e=this.index();return-1!==e?this.parent.children[e+1]:null}get previousSibling(){const e=this.index();return-1!==e?this.parent.children[e-1]:null}index(){return this.parent?this.parent.children.indexOf(this):-1}add(e){return e&&(e.remove(),this.children.push(e),e.parent=this),this}remove(){if(this.parent){const e=this.index();-1!==e&&(this.parent.children.splice(e,1),this.parent=null)}return this}}class s extends i{constructor(){super("stylesheet"),this.comments=[]}get start(){const e=this.firstChild;return e&&e.start}get end(){const e=this.children[this.children.length-1];return e&&e.end}addComment(e){this.comments.push(e)}}function a(e){return function(e){let t;for(e=e.slice();t!==e.length;)t=e.length,c(e[0])&&e.shift(),c(d(e))&&e.pop();return e}(e)}function c(e){const t=e&&e.type;return"whitespace"===t||"comment"===t}function u(e,t){const n=e.pos;for(let r=0,o=t.length;r<o;r++)if(!e.eat(t.charCodeAt(r)))return e.pos=n,!1;return!0}function l(e,t){const n=e.pos;return!!e.eat(t)&&(e.start=n,!0)}function f(e,t){const n=e.pos;return!!e.eatWhile(t)&&(e.start=n,!0)}function d(e){return e[e.length-1]}function p(e){return e&&e.valueOf()}class h{constructor(e,t,n,r){this.stream=e,this.start=null!=n?n:e.start,this.end=null!=r?r:e.pos,this.type=t,this._props=null,this._value=null,this._items=null}get size(){return this._items?this._items.length:0}get items(){return this._items}clone(e,t){return new this.constructor(this.stream,this.type,null!=e?e:this.start,null!=t?t:this.end)}add(e){if(Array.isArray(e))for(let t=0,n=e.length;t<n;t++)this.add(e[t]);else e&&(this._items?this._items.push(e):this._items=[e]);return this}remove(e){if(this._items){const t=this._items.indexOf(e);-1!==t&&this._items.splice(t,1)}return this}item(e){const t=this.size;return this._items&&this._items[(t+e)%t]}limit(){return this.stream.limit(this.start,this.end)}slice(e,t){const n=this.clone(),r=this._items&&this._items.slice(e,t);return r&&r.length?(n.start=r[0].start,n.end=r[r.length-1].end,n.add(r)):r&&(n.start=n.end),n}property(e,t){return void 0!==t&&(this._props||(this._props={}),this._props[e]=t),this._props&&this._props[e]}toString(){return`${this.valueOf()} [${this.start}, ${this.end}] (${this.type})`}valueOf(){return null===this._value&&(this._value=this.stream.substring(this.start,this.end)),this._value}}const m=44,g=58,b=59,v=123,y=125,x=(new Map).set(m,"comma").set(g,"propertyDelimiter").set(b,"propertyTerminator").set(v,"ruleStart").set(y,"ruleEnd");var w=function(e,t){if(40===e.peek()){const r=e.pos;e.next();const o=[];let i,s=!1;for(;!e.eof()&&(n=e.peek())!==v&&n!==y&&!e.eat(41)&&(i=t(e),i);)S(i)&&(s=!0),o.push(i);return e.start=r,function(e,t,n){const r=new h(e,"arguments"),o=n?S:T;let i=[];for(let n,s=0,a=t.length;s<a;s++)n=t[s],o(n)?(r.add(k(e,i)||C(e,n.start)),i.length=0):i.push(n);return i.length&&r.add(k(e,i)),r}(e,o,s)}var n};function k(e,t){if((t=a(t)).length){const n=new h(e,"argument",t[0].start,d(t).end);for(let e=0;e<t.length;e++)n.add(t[e]);return n}}function C(e,t){const n=new h(e,"argument",t,t);return n.property("empty",!0),n}function T(e){return"comma"===e.property("type")}function S(e){return"propertyTerminator"===e.property("type")}const _=45,O=95;function A(e){return j(e)&&new h(e,"ident")}function j(e){const t=e.pos;return e.eat(_),e.eat(E)?(e.eatWhile(P),e.start=t,!0):(e.pos=t,!1)}function E(e){return e===O||e===_||(0,o.R5)(e)||e>=128}function P(e){return(0,o.Et)(e)||E(e)}function $(e,t,n,r,o){const i=e.pos;if(e.eat(n)){const n=r(e,i);if(n||o)return e.start=i,new h(e,t,i).add(n)}e.pos=i}const I=64;function R(e){return $(e,"at-keyword",I,A)}const M=35,N=64;function z(e,t){const n=e.pos;if(t=t||D,(e.eat(M)||e.eat(N))&&e.eat(v)){const r=new h(e,"interpolation",n);let o,i=1;for(;!e.eof();)if(e.eat(v))i++;else if(e.eat(y)){if(i--,!i)return r.end=e.pos,r}else{if(!(o=t(e)))break;r.add(o)}}e.pos=n}function L(e){const t=e.pos;return(e.eat(M)||e.eat(N))&&(0,o.Ji)(e,v,y)?(e.start=t,!0):(e.pos=t,!1)}function D(e){const t=e.pos;for(;!e.eof()&&e.peek()!==y;)U(e)||e.next();if(t!==e.pos)return new h(e,"expression",t)}function B(e){return U(e,!0)}function U(e,t){let n,r,i,s=e.peek();if((0,o.vG)(s)){e.start=e.pos,e.next();const o=s,a=e.pos;for(;!e.eof()&&(n=e.pos,!e.eat(o)&&!e.eat(F));)e.eat(92)?e.eat(F):t&&(i=z(e))&&(r?r.push(i):r=[i]),e.next();if(t){const t=new h(e,"string"),i=new h(e,"unquoted",a,n);return i.add(r),t.add(i),t.property("quote",o),t}return!0}return!1}function F(e){return 10===e||13===e}const q=42,V=47;var W=function(e){return function(e){if(G(e)){const t=new h(e,"comment");return t.property("type","single-line"),t}}(e)||function(e){if(Q(e)){const t=new h(e,"comment");return t.property("type","multiline"),t}}(e)};function H(e){return G(e)||Q(e)}function G(e){const t=e.pos;if(e.eat(V)&&e.eat(V)){for(e.start=t;!e.eof()&&10!==(n=e.next())&&13!==n;);return!0}var n;return e.pos=t,!1}function Q(e){const t=e.pos;if(e.eat(V)&&e.eat(q)){for(;!(e.eof()||e.next()===q&&e.eat(V)););return e.start=t,!0}return e.pos=t,!1}function J(e){return K(e)&&new h(e,"whitespace")}function K(e){return f(e,o.xC)}const X=91,Z=93;function Y(e){for(;!e.eof();)if(!K(e)&&!H(e))return!0}function ee(e){return 126===e||124===e||94===e||36===e||42===e||61===e}const te=96;function ne(e){if(re(e))return new h(e,"backtick")}function re(e){const t=e.pos;return!!(0,o.Ji)(e,te,te)&&(e.start=t,!0)}const oe=46,ie={43:"adjacentSibling",126:"generalSibling",62:"child",38:"nesting"};var se=function(e){if(43===(t=e.peek())||126===t||38===t||62===t){const t=e.pos,n=ie[e.next()],r=new h(e,"combinator",t);return r.property("type",n),r}var t};const ae=35;function ce(e){if(function(e){return f(e,ue)}(e))return new h(e,"hash-value")}function ue(e){return(0,o.Et)(e)||(0,o.R5)(e,65,70)||95===e||45===e||e>128}const le=35,fe=33,de=46;function pe(e){return 45===e||43===e}const he=33,me=42,ge=43,be=45,ve=47,ye=60,xe=61,we=62;function ke(e){return e===he||e===ye||e===xe||e===we}function Ce(e){return e===me||e===ge||e===be||e===ve||ke(e)}var Te=function(e){const t=e.pos;if(e.eatWhile(58)){const n=A(e);if(n)return new h(e,"pseudo",t).add(n)}e.pos=t},Se=function(e){return _e(e)&&new h(e,"unquoted")};function _e(e){return f(e,Oe)}function Oe(e){return!(isNaN(e)||(0,o.vG)(e)||(0,o.xC)(e)||40===e||41===e||92===e||function(e){return e>=0&&e<=8||11===e||e>=14&&e<=31||127===e}(e))}function Ae(e){const t=e.pos;if(u(e,"url(")){K(e);const n=B(e)||Se(e);return K(e),e.eat(41),new h(e,"url",t).add(n)}e.pos=t}function je(e){const t=e.pos;return u(e,"url(")?(K(e),U(e)||_e(e),K(e),e.eat(41),e.start=t,!0):(e.pos=t,!1)}const Ee=36;function Pe(e){return $(e,"variable",Ee,$e)}function $e(e){if(function(e){return f(e,Ie)}(e))return new h(e,"name")}function Ie(e){return e===Ee||P(e)}function Re(e){const t=Me(e)||w(e,Re);if(t&&"ident"===t.type){const n=w(e,Re);if(n)return new h(e,"function",t.start,n.end).add(t).add(n)}return t||function(e){e.start=e.pos;if(null!=e.next())return new h(e,"unknown")}(e)}function Me(e){return De(e)||Ae(e)||Ne(e)||ze(e)||function(e){if((t=e.peek())===m||t===g||t===b||t===v||t===y){const t=e.pos,n=x.get(e.next()),r=new h(e,"separator",t);return r.property("type",n),r}var t}(e)}function Ne(e){return z(e)||ne(e)||A(e)||R(e)||function(e){return $(e,"class",oe,A)}(e)||function(e){return $(e,"id",le,A)}(e)||Te(e)||function(e){const t=e.pos;if(e.eat(X)){Y(e);const n=A(e);Y(e);const r=function(e){return f(e,ee)&&new h(e,"operator")}(e);Y(e);const o=B(e)||A(e);return Y(e),e.eat(Z),new h(e,"attribute",t).add(n).add(r).add(o)}}(e)||se(e)}function ze(e){return Ae(e)||B(e)||z(e)||ne(e)||function(e){if(function(e){const t=e.pos;if(e.eat(pe),e.eatWhile(o.Et)){e.start=t;const n=e.pos;return e.eat(de)&&e.eatWhile(o.Et)||(e.pos=n),!0}return e.eat(de)&&e.eatWhile(o.Et)?(e.start=t,!0):(e.pos=t,!1)}(e)){const t=e.start,n=new h(e,"value"),r=function(e){return j(e)||function(e){return l(e,37)}(e)}(e)?new h(e,"unit"):null;return new h(e,"number",t).add(n).add(r)}}(e)||function(e){return $(e,"hash",ae,ce,!0)}(e)||Le(e)||function(e){return $(e,"important",fe,A)}(e)||function(e){return function(e){return l(e,ke)?(e.eatWhile(xe),!0):!!l(e,Ce)}(e)&&new h(e,"operator")}(e)}function Le(e){return ne(e)||Pe(e)||R(e)||A(e)}function De(e){return W(e)||J(e)}function Be(e){return Ve(e,"selector")}function Ue(e){const t="string"==typeof e?new r.A(e):e,n=[];for(;!t.eof();)n.push(Re(t));let o;if(1===n.length)o=n[0];else{o=new h(t,"property-name",t.start,t.end);for(let e=0,t=n.length;e<t;e++)o.add(n[e])}return o}function Fe(e){return Ve(e)}function qe(e){return Ve(e)}function Ve(e,t){t=t||"item";const n="string"==typeof e?new r.A(e):e,o=[],i=[],s=()=>{const e=a(i);if(e.length){const r=new h(n,t,e[0].start,d(e).end);for(let t=0;t<e.length;t++)r.add(e[t]);o.push(r)}i.length=0};let c;for(;!n.eof();)if(n.eat(44))s();else{if(!(c=Re(n)))throw n.error("Unexpected character");"comment"!==c.type&&i.push(c)}return s(),o}function We(e,t,n,r){if(!t.length)return null;const o=t[0];return o.end=d(t).end,new He(e,o,n,r)}class He extends i{constructor(e,t,n,r){super("rule"),this.stream=e,this.selectorToken=t,this.contentStartToken=n,this.contentEndToken=r||n,this._parsedSelector=null}get selector(){return p(this.selectorToken)}get parsedSelector(){return this._parsedSelector||(this._parsedSelector=Be(this.selectorToken.limit())),this._parsedSelector}get start(){return this.selectorToken&&this.selectorToken.start}get end(){const e=this.contentEndToken||this.contentStartToken||this.nameToken;return e&&e.end}}function Ge(e,t,n,r){if(!t.length)return null;let o,i=0;const s=t[i++];return i<t.length?(o=t[i++],o.type="expression",o.end=d(t).end):o=new h(e,"expression",s.end,s.end),new Qe(e,s,o,n,r)}class Qe extends i{constructor(e,t,n,r,o){super("at-rule"),this.stream=e,this.nameToken=t,this.expressionToken=n,this.contentStartToken=r,this.contentEndToken=o||r,this._parsedExpression=null}get name(){return p(this.nameToken&&this.nameToken.item(0))}get expression(){return p(this.expressionToken)}get parsedExpression(){return this._parsedExpression||(this._parsedExpression=qe(this.expressionToken.limit())),this._parsedExpression}get start(){return this.nameToken&&this.nameToken.start}get end(){const e=this.contentEndToken||this.contentStartToken||this.nameToken;return e&&e.end}}function Je(e,t,n){if(!t.length)return null;let r,o,i=0;const s=t[i++];return i<t.length&&(o=t[i++],o.type="value",o.end=d(t).end),s&&o&&(r=new h(e,"separator",s.end,o.start)),new Ke(e,s,o,r,n)}class Ke extends i{constructor(e,t,n,r,o){super("property"),this.stream=e,this.nameToken=t,this.valueToken=n,this._parsedName=null,this._parsedValue=null,this.separatorToken=r,this.terminatorToken=o}get name(){return p(this.nameToken)}get parsedName(){return this._parsedName||(this._parsedName=Ue(this.nameToken.limit())),this._parsedName}get value(){return p(this.valueToken)}get parsedValue(){return this._parsedValue||(this._parsedValue=Fe(this.valueToken.limit())),this._parsedValue}get separator(){return p(this.separatorToken)}get terminator(){return p(this.terminatorToken)}get start(){const e=this.nameToken||this.separatorToken||this.valueToken||this.terminatorToken;return e&&e.start}get end(){const e=this.terminatorToken||this.valueToken||this.separatorToken||this.nameToken;return e&&e.end}}function Xe(e,t){t=t||Re;const n="string"==typeof e?new r.A(e):e,o=[];let i;for(;!n.eof()&&(i=t(n));)o.push(i);return o}function Ze(e,t){if(e.eat(40)){let n,r=1;for(;!e.eof();)if(e.eat(41)){if(r--,!r)break}else if(e.eat(40))r++;else{if(je(e)||U(e))continue;if(n=W(e)){t.addComment(n);continue}e.next()}return!0}return!1}const Ye=function(e){const t="string"==typeof e?new r.A(e):e,n=new s;let o,i,a,c=n,u=[];const l=()=>{i&&(u.push(i),i=null)};for(;!t.eof();)if(!K(t))if(a=W(t))n.addComment(a);else if(t.start=t.pos,t.eatWhile(58))u.length||(i?l():i=new h(t,"preparse"));else if(t.eat(59))l(),c.add(Je(t,u,new h(t,"termintator"))),u.length=0;else if(t.eat(123))l(),u.length>0&&(o="at-keyword"===u[0].type?Ge(t,u,new h(t,"body-start")):We(t,u,new h(t,"body-start")),c.add(o),c=o,u.length=0);else if(t.eat(125))l(),c.add(Je(t,u)),"stylesheet"!==c.type&&(c.contentEndToken=new h(t,"body-end"),c=c.parent),u.length=0;else if(a=R(t))l(),u.push(a);else{if(!(je(t)||L(t)||re(t)||Ze(t,n)||U(t)||t.next()))throw new Error(`Unexpected end-of-stream at ${t.pos}`);i=i||new h(t,"preparse"),i.end=t.pos}for(i&&u.push(i),c.add(Je(t,u)),t.start=t.pos;c&&c!==n;)c.contentEndToken=new h(t,"body-end"),c=c.parent;return n}},1253:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>I,defaultOptions:()=>j,match:()=>E});var r=n(8769),o=n(6613);class i{constructor(e,t,n,r){this.stream=e,this.type=t,this.open=n,this.close=r,this.children=[],this.parent=null}get name(){return"tag"===this.type&&this.open?this.open&&this.open.name&&this.open.name.value:"#"+this.type}get attributes(){return this.open&&this.open.attributes}get start(){return this.open&&this.open.start}get end(){return this.close?this.close.end:this.open&&this.open.end}get firstChild(){return this.children[0]}get nextSibling(){const e=this.getIndex();return-1!==e?this.parent.children[e+1]:null}get previousSibling(){const e=this.getIndex();return-1!==e?this.parent.children[e-1]:null}getIndex(){return this.parent?this.parent.children.indexOf(this):-1}addChild(e){return this.removeChild(e),this.children.push(e),e.parent=this,this}removeChild(e){const t=this.children.indexOf(e);return-1!==t&&(this.children.splice(t,1),e.parent=null),this}}var s=function(e,t,n){return"function"==typeof t?function(e,t){const n=e.pos;if(e.eatWhile(t))return new a(e,n,e.pos);e.pos=n}(e,t):new a(e,t,n)};class a{constructor(e,t,n){this.stream=e,this.start=null!=t?t:e.start,this.end=null!=n?n:e.pos,this._value=null}get value(){if(null===this._value){const e=this.stream.start,t=this.stream.pos;this.stream.start=this.start,this.stream.pos=this.end,this._value=this.stream.current(),this.stream.start=e,this.stream.pos=t}return this._value}toString(){return this.value}valueOf(){return`${this.value} [${this.start}; ${this.end}]`}}const c={throws:!0};var u=function(e){const t=e.pos;if((0,o.Ji)(e,60,62,c)||(0,o.Ji)(e,91,93,c)||(0,o.Ji)(e,40,41,c)||(0,o.Ji)(e,123,125,c))return s(e,t)};const l=47,f=61,d=62;function p(e){return u(e)||s(e,m)}function h(e){const t=e.pos;if((0,o.vP)(e)){const n=e.pos;let r,o;e.pos=t,e.next(),r=e.start=e.pos,e.pos=n,e.backUp(1),o=e.pos;const i=s(e,r,o);return e.pos=n,i}return u(e)||function(e){return s(e,b)}(e)}function m(e){return e!==f&&!g(e)&&!(0,o.xC)(e)}function g(e){return e===d||e===l}function b(e){return!(isNaN(e)||(0,o.vG)(e)||(0,o.xC)(e)||g(e))}var v=function(e){const t=e.pos;if(e.eat(60)){const n={type:e.eat(47)?"close":"open"};if((n.name=function(e){return s(e,y)}(e))&&("close"!==n.type&&(n.attributes=function(e){const t=[];let n;for(;!e.eof();)if(e.eatWhile(o.xC),n={start:e.pos},n.name=p(e))e.eat(f)?n.value=h(e):n.boolean=!0,n.end=e.pos,t.push(n);else{if(g(e.peek()))break;e.next()}return t}(e),e.eatWhile(o.xC),n.selfClosing=e.eat(47)),e.eat(62)))return Object.assign(s(e,t),n)}return e.pos=t,null};function y(e){return(0,o.gA)(e)||58===e||46===e||45===e||95===e}function x(e,t){const n=e.pos;for(let r=0;r<t.length;r++)if(!e.eat(t[r]))return e.pos=n,!1;return e.start=n,!0}function w(e,t,n,r){const o=e.pos;if(x(e,t)){for(;!e.eof();){if(x(e,n))return!0;e.next()}return!!r||(e.pos=o,!1)}return e.pos=o,null}function k(e){return e.split("").map((e=>e.charCodeAt(0)))}const C=k("\x3c!--"),T=k("--\x3e");var S=function(e){const t=e.pos;if(w(e,C,T,!0)){const n=s(e,t);return n.type="comment",n}return null};const _=k("<![CDATA["),O=k("]]>");var A=function(e){const t=e.pos;if(w(e,_,O,!0)){const n=s(e,t);return n.type="cdata",n}return null};const j={xml:!1,special:["script","style"],empty:["img","meta","link","br","base","hr","area","wbr","col","embed","input","param","source","track"]};function E(e){if(60===e.peek())return S(e)||A(e)||v(e)}function P(e,t){const n=e.pos;for(;!e.eof();){if(x(e,t))return e.pos=e.start,v(e);e.next()}return e.pos=n,null}function $(e){return e[e.length-1]}const I=function(e,t){t=Object.assign({},j,t);const n="string"==typeof e?new r.A(e):e,o=new i(n,"root"),s=new Set(t.empty),a=t.special.reduce(((e,t)=>e.set(t,k(`</${t}>`))),new Map),c=(e,n)=>e.selfClosing||!t.xml&&s.has(n);let u,l,f,d=[o];for(;!n.eof();)if(u=E(n))if(f=(p=u).name?p.name.value.toLowerCase():`#${p.type}`,"open"===u.type)l=new i(n,"tag",u),$(d).addChild(l),a.has(f)?l.close=P(n,a.get(f)):c(u,f)||d.push(l);else if("close"===u.type){for(let e=d.length-1;e>0;e--)if(d[e].name.toLowerCase()===f){d[e].close=u,d=d.slice(0,e);break}}else $(d).addChild(new i(n,u.type,u));else n.next();var p;return o}},2915:(e,t,n)=>{"use strict";function r(e){return e>47&&e<58}function o(e){return 32===e||9===e||160===e}function i(e){return o(e)||10===e||13===e}n.r(t),n.d(t,{default:()=>_,extract:()=>x,parse:()=>u});class s{constructor(e,t,n){null==n&&"string"==typeof e&&(n=e.length),this.string=e,this.pos=this.start=t||0,this.end=n||0}eof(){return this.pos>=this.end}limit(e,t){return new s(this.string,e,t)}peek(){return this.string.charCodeAt(this.pos)}next(){if(this.pos<this.string.length)return this.string.charCodeAt(this.pos++)}eat(e){const t=this.peek(),n="function"==typeof e?e(t):t===e;return n&&this.next(),n}eatWhile(e){const t=this.pos;for(;!this.eof()&&this.eat(e););return this.pos!==t}backUp(e){this.pos-=e||1}current(){return this.substring(this.start,this.pos)}substring(e,t){return this.string.slice(e,t)}error(e,t=this.pos){return new a(`${e} at ${t+1}`,t,this.string)}}class a extends Error{constructor(e,t,n){super(e),this.pos=t,this.string=n}}const c=v("null",0);function u(e){const t="string"==typeof e?new s(e):e;let n,r=0,i=21;const a=[];for(;!t.eof();)t.eatWhile(o),t.start=t.pos,l(t)?(1&i||h("Unexpected number",t),a.push(f(t.current())),i=10):b(t.peek())?(n=t.next(),m(n)&&16&i?(g(n)&&a.push(d(n,r)),i=21):(2&i||h("Unexpected operator",t),a.push(p(n,r)),i=21)):t.eat(40)?(4&i||h('Unexpected "("',t),r+=10,i=53):t.eat(41)?(r-=10,32&i?a.push(c):8&i||h('Unexpected ")"',t),i=14):h("Unknown character",t);(r<0||r>=10)&&h('Unmatched "()"',t);const u=function(e){const t=[],n=[];let r=0;for(let o=0;o<e.length;o++){const i=e[o];if("num"===i.type)n.push(i);else{for(r+="op1"===i.type?1:2;t.length&&i.priority<=t[t.length-1].priority;)n.push(t.pop());t.push(i)}}return r+1===n.length+t.length?n.concat(t.reverse()):null}(a);return null===u&&h("Parity",t),u}function l(e){const t=e.pos;return!(!e.eat(46)||!e.eatWhile(r))||!(!e.eatWhile(r)||e.eat(46)&&!e.eatWhile(r))||(e.pos=t,!1)}function f(e,t){return v("num",parseFloat(e),t)}function d(e,t=0){return 45===e&&(t+=2),v("op1",e,t)}function p(e,t=0){return 42===e?t+=1:47!==e&&92!==e||(t+=2),v("op2",e,t)}function h(e,t){throw t&&(e+=` at column ${t.pos} of expression`),new Error(e)}function m(e){return function(e){return 43===e}(e)||g(e)}function g(e){return 45===e}function b(e){return 43===e||45===e||42===e||47===e||92===e}function v(e,t,n=0){return{type:e,value:t,priority:n}}const y={lookAhead:!0,whitespace:!0};function x(e,t=e.length,n){const r=Object.assign(Object.assign({},y),n),o={text:e,pos:t};let s;if(r.lookAhead&&41===C(o)){o.pos++;const t=e.length;for(;o.pos<t&&(s=C(o),41===s||r.whitespace&&i(s));)o.pos++}const a=o.pos;let c=0;for(;o.pos>=0;)if(!w(o)){if(s=k(o),41===s)c++;else if(40===s){if(!c)break;c--}else if(!(r.whitespace&&i(s)||m(s)||b(s)))break;o.pos--}if(o.pos!==a&&!c){for(;i(C(o));)o.pos++;return[o.pos,a]}return null}function w(e){if(r(k(e))){e.pos--;let t,n=!1;for(;e.pos>=0;){if(t=k(e),46===t){if(n)break;n=!0}else if(!r(t))break;e.pos--}return!0}return!1}function k(e){return e.text.charCodeAt(e.pos-1)}function C(e){return e.text.charCodeAt(e.pos)}const T={45:e=>-e},S={43:(e,t)=>e+t,45:(e,t)=>e-t,42:(e,t)=>e*t,47:(e,t)=>e/t,92:(e,t)=>Math.floor(e/t)},_=function(e){if(Array.isArray(e)||(e=u(e)),!e||!e.length)return null;const t=[];let n,r,o;for(let i=0,s=e.length;i<s;i++){const s=e[i];if("num"===s.type)t.push(s.value);else if("op2"===s.type)r=t.pop(),n=t.pop(),o=S[s.value],t.push(o(n,r));else{if("op1"!==s.type)throw new Error("Invalid expression");n=t.pop(),o=T[s.value],t.push(o(n))}}if(t.length>1)throw new Error("Invalid Expression (parity)");return t[0]}},6613:(e,t,n)=>{"use strict";n.d(t,{Et:()=>c,Ji:()=>p,R5:()=>u,gA:()=>l,vG:()=>a,vP:()=>s,xC:()=>f});const r=39,o=34,i={escape:92,throws:!1};var s=function(e,t){t=t?Object.assign({},i,t):i;const n=e.pos,r=e.peek();if(e.eat(a)){for(;!e.eof();)switch(e.next()){case r:return e.start=n,!0;case t.escape:e.next()}if(e.pos=n,t.throws)throw e.error("Unable to consume quoted string")}return!1};function a(e){return e===r||e===o}function c(e){return e>47&&e<58}function u(e,t,n){return n=n||90,(e&=-33)>=(t=t||65)&&e<=n}function l(e){return c(e)||u(e)}function f(e){return function(e){return 32===e||9===e||160===e}(e)||10===e||13===e}const d={escape:92,throws:!1};function p(e,t,n,r){r=r?Object.assign({},d,r):d;const o=e.pos;if(e.eat(t)){let i,a=1;for(;!e.eof();)if(!s(e,r))if(i=e.next(),i===t)a++;else if(i===n){if(a--,!a)return e.start=o,!0}else i===r.escape&&e.next();if(e.pos=o,r.throws)throw e.error(`Unable to find matching pair for ${String.fromCharCode(t)}`)}return!1}},8769:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});const r=class{constructor(e,t,n){null==n&&"string"==typeof e&&(n=e.length),this.string=e,this.pos=this.start=t||0,this.end=n}eof(){return this.pos>=this.end}limit(e,t){return new this.constructor(this.string,e,t)}peek(){return this.string.charCodeAt(this.pos)}next(){if(this.pos<this.string.length)return this.string.charCodeAt(this.pos++)}eat(e){const t=this.peek(),n="function"==typeof e?e(t):t===e;return n&&this.next(),n}eatWhile(e){const t=this.pos;for(;!this.eof()&&this.eat(e););return this.pos!==t}backUp(e){this.pos-=e||1}current(){return this.substring(this.start,this.pos)}substring(e,t){return this.string.slice(e,t)}error(e){const t=new Error(`${e} at char ${this.pos+1}`);return t.originalMessage=e,t.pos=this.pos,t.string=this.string,t}}},3547:(e,t,n)=>{"use strict";function r(e){return 32===e||9===e||11===e||12===e||160===e||5760===e||e>=8192&&e<=8203||8239===e||8287===e||12288===e||65279===e}function o(e){return 10===e||13===e||8232===e||8233===e}function i(e){return e>=48&&e<=57}var s;function a(e,t,n){void 0===n&&(n=s.DEFAULT);var a=function(e,t){void 0===t&&(t=!1);var n=e.length,s=0,a="",c=0,u=16,l=0,f=0,d=0,p=0,h=0;function m(t,n){for(var r=0,o=0;r<t||!n;){var i=e.charCodeAt(s);if(i>=48&&i<=57)o=16*o+i-48;else if(i>=65&&i<=70)o=16*o+i-65+10;else{if(!(i>=97&&i<=102))break;o=16*o+i-97+10}s++,r++}return r<t&&(o=-1),o}function g(){if(a="",h=0,c=s,f=l,p=d,s>=n)return c=n,u=17;var t=e.charCodeAt(s);if(r(t)){do{s++,a+=String.fromCharCode(t),t=e.charCodeAt(s)}while(r(t));return u=15}if(o(t))return s++,a+=String.fromCharCode(t),13===t&&10===e.charCodeAt(s)&&(s++,a+="\n"),l++,d=s,u=14;switch(t){case 123:return s++,u=1;case 125:return s++,u=2;case 91:return s++,u=3;case 93:return s++,u=4;case 58:return s++,u=6;case 44:return s++,u=5;case 34:return s++,a=function(){for(var t="",r=s;;){if(s>=n){t+=e.substring(r,s),h=2;break}var i=e.charCodeAt(s);if(34===i){t+=e.substring(r,s),s++;break}if(92!==i){if(i>=0&&i<=31){if(o(i)){t+=e.substring(r,s),h=2;break}h=6}s++}else{if(t+=e.substring(r,s),++s>=n){h=2;break}switch(e.charCodeAt(s++)){case 34:t+='"';break;case 92:t+="\\";break;case 47:t+="/";break;case 98:t+="\b";break;case 102:t+="\f";break;case 110:t+="\n";break;case 114:t+="\r";break;case 116:t+="\t";break;case 117:var a=m(4,!0);a>=0?t+=String.fromCharCode(a):h=4;break;default:h=5}r=s}}return t}(),u=10;case 47:var g=s-1;if(47===e.charCodeAt(s+1)){for(s+=2;s<n&&!o(e.charCodeAt(s));)s++;return a=e.substring(g,s),u=12}if(42===e.charCodeAt(s+1)){s+=2;for(var v=n-1,y=!1;s<v;){var x=e.charCodeAt(s);if(42===x&&47===e.charCodeAt(s+1)){s+=2,y=!0;break}s++,o(x)&&(13===x&&10===e.charCodeAt(s)&&s++,l++,d=s)}return y||(s++,h=1),a=e.substring(g,s),u=13}return a+=String.fromCharCode(t),s++,u=16;case 45:if(a+=String.fromCharCode(t),++s===n||!i(e.charCodeAt(s)))return u=16;case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return a+=function(){var t=s;if(48===e.charCodeAt(s))s++;else for(s++;s<e.length&&i(e.charCodeAt(s));)s++;if(s<e.length&&46===e.charCodeAt(s)){if(!(++s<e.length&&i(e.charCodeAt(s))))return h=3,e.substring(t,s);for(s++;s<e.length&&i(e.charCodeAt(s));)s++}var n=s;if(s<e.length&&(69===e.charCodeAt(s)||101===e.charCodeAt(s)))if((++s<e.length&&43===e.charCodeAt(s)||45===e.charCodeAt(s))&&s++,s<e.length&&i(e.charCodeAt(s))){for(s++;s<e.length&&i(e.charCodeAt(s));)s++;n=s}else h=3;return e.substring(t,n)}(),u=11;default:for(;s<n&&b(t);)s++,t=e.charCodeAt(s);if(c!==s){switch(a=e.substring(c,s)){case"true":return u=8;case"false":return u=9;case"null":return u=7}return u=16}return a+=String.fromCharCode(t),s++,u=16}}function b(e){if(r(e)||o(e))return!1;switch(e){case 125:case 93:case 123:case 91:case 34:case 58:case 44:case 47:return!1}return!0}return{setPosition:function(e){s=e,a="",c=0,u=16,h=0},getPosition:function(){return s},scan:t?function(){var e;do{e=g()}while(e>=12&&e<=15);return e}:g,getToken:function(){return u},getTokenValue:function(){return a},getTokenOffset:function(){return c},getTokenLength:function(){return s-c},getTokenStartLine:function(){return f},getTokenStartCharacter:function(){return c-p},getTokenError:function(){return h}}}(e,!1);function c(e){return e?function(){return e(a.getTokenOffset(),a.getTokenLength(),a.getTokenStartLine(),a.getTokenStartCharacter())}:function(){return!0}}function u(e){return e?function(t){return e(t,a.getTokenOffset(),a.getTokenLength(),a.getTokenStartLine(),a.getTokenStartCharacter())}:function(){return!0}}var l=c(t.onObjectBegin),f=u(t.onObjectProperty),d=c(t.onObjectEnd),p=c(t.onArrayBegin),h=c(t.onArrayEnd),m=u(t.onLiteralValue),g=u(t.onSeparator),b=c(t.onComment),v=u(t.onError),y=n&&n.disallowComments,x=n&&n.allowTrailingComma;function w(){for(;;){var e=a.scan();switch(a.getTokenError()){case 4:k(14);break;case 5:k(15);break;case 3:k(13);break;case 1:y||k(11);break;case 2:k(12);break;case 6:k(16)}switch(e){case 12:case 13:y?k(10):b();break;case 16:k(1);break;case 15:case 14:break;default:return e}}}function k(e,t,n){if(void 0===t&&(t=[]),void 0===n&&(n=[]),v(e),t.length+n.length>0)for(var r=a.getToken();17!==r;){if(-1!==t.indexOf(r)){w();break}if(-1!==n.indexOf(r))break;r=w()}}function C(e){var t=a.getTokenValue();return e?m(t):f(t),w(),!0}return w(),17===a.getToken()?!!n.allowEmptyContent||(k(4,[],[]),!1):function e(){switch(a.getToken()){case 3:return function(){p(),w();for(var t=!1;4!==a.getToken()&&17!==a.getToken();){if(5===a.getToken()){if(t||k(4,[],[]),g(","),w(),4===a.getToken()&&x)break}else t&&k(6,[],[]);e()||k(4,[],[4,5]),t=!0}return h(),4!==a.getToken()?k(8,[4],[]):w(),!0}();case 1:return function(){l(),w();for(var t=!1;2!==a.getToken()&&17!==a.getToken();){if(5===a.getToken()){if(t||k(4,[],[]),g(","),w(),2===a.getToken()&&x)break}else t&&k(6,[],[]);(10!==a.getToken()?(k(3,[],[2,5]),0):(C(!1),6===a.getToken()?(g(":"),w(),e()||k(4,[],[2,5])):k(5,[],[2,5]),1))||k(4,[],[2,5]),t=!0}return d(),2!==a.getToken()?k(7,[2],[]):w(),!0}();case 10:return C(!0);default:return function(){switch(a.getToken()){case 11:var e=a.getTokenValue(),t=Number(e);isNaN(t)&&(k(2),t=0),m(t);break;case 7:m(null);break;case 8:m(!0);break;case 9:m(!1);break;default:return!1}return w(),!0}()}}()?(17!==a.getToken()&&k(9,[],[]),!0):(k(4,[],[]),!1)}n.r(t),n.d(t,{FileType:()=>Fe,doComplete:()=>Hi,emmetSnippetField:()=>ts,expandAbbreviation:()=>ps,extractAbbreviation:()=>as,extractAbbreviationFromText:()=>cs,getDefaultSnippets:()=>is,getDefaultSyntax:()=>os,getEmmetMode:()=>xs,getExpandOptions:()=>ls,getSyntaxType:()=>rs,isAbbreviationValid:()=>us,isStyleSheet:()=>ns,parseAbbreviation:()=>ds,updateExtensionsPath:()=>hs}),function(e){e.DEFAULT={allowTrailingComma:!1}}(s||(s={}));var c,u,l,f,d,p,h,m,g,b,v,y,x,w,k,C,T,S,_,O,A,j,E,P,$,I,R,M,N,z,L,D,B,U,F,q,V,W,H,G,Q,J,K,X,Z,Y,ee,te,ne,re,oe,ie,se,ae,ce,ue,le,fe,de,pe,he,me,ge,be,ve,ye,xe,we,ke,Ce,Te,Se,_e,Oe,Ae,je,Ee,Pe,$e,Ie,Re,Me=function(e,t,n){void 0===t&&(t=[]),void 0===n&&(n=s.DEFAULT);var r=null,o=[],i=[];function c(e){Array.isArray(o)?o.push(e):null!==r&&(o[r]=e)}var u={onObjectBegin:function(){var e={};c(e),i.push(o),o=e,r=null},onObjectProperty:function(e){r=e},onObjectEnd:function(){o=i.pop()},onArrayBegin:function(){var e=[];c(e),i.push(o),o=e,r=null},onArrayEnd:function(){o=i.pop()},onLiteralValue:c,onError:function(e,n,r){t.push({error:e,offset:n,length:r})}};return a(e,u,n),o[0]},Ne=n(9023);!function(e){e.is=function(e){return"string"==typeof e}}(c||(c={})),function(e){e.is=function(e){return"string"==typeof e}}(u||(u={})),function(e){e.MIN_VALUE=-2147483648,e.MAX_VALUE=2147483647,e.is=function(t){return"number"==typeof t&&e.MIN_VALUE<=t&&t<=e.MAX_VALUE}}(l||(l={})),function(e){e.MIN_VALUE=0,e.MAX_VALUE=2147483647,e.is=function(t){return"number"==typeof t&&e.MIN_VALUE<=t&&t<=e.MAX_VALUE}}(f||(f={})),function(e){e.create=function(e,t){return e===Number.MAX_VALUE&&(e=f.MAX_VALUE),t===Number.MAX_VALUE&&(t=f.MAX_VALUE),{line:e,character:t}},e.is=function(e){let t=e;return Ie.objectLiteral(t)&&Ie.uinteger(t.line)&&Ie.uinteger(t.character)}}(d||(d={})),function(e){e.create=function(e,t,n,r){if(Ie.uinteger(e)&&Ie.uinteger(t)&&Ie.uinteger(n)&&Ie.uinteger(r))return{start:d.create(e,t),end:d.create(n,r)};if(d.is(e)&&d.is(t))return{start:e,end:t};throw new Error(`Range#create called with invalid arguments[${e}, ${t}, ${n}, ${r}]`)},e.is=function(e){let t=e;return Ie.objectLiteral(t)&&d.is(t.start)&&d.is(t.end)}}(p||(p={})),function(e){e.create=function(e,t){return{uri:e,range:t}},e.is=function(e){let t=e;return Ie.objectLiteral(t)&&p.is(t.range)&&(Ie.string(t.uri)||Ie.undefined(t.uri))}}(h||(h={})),function(e){e.create=function(e,t,n,r){return{targetUri:e,targetRange:t,targetSelectionRange:n,originSelectionRange:r}},e.is=function(e){let t=e;return Ie.objectLiteral(t)&&p.is(t.targetRange)&&Ie.string(t.targetUri)&&p.is(t.targetSelectionRange)&&(p.is(t.originSelectionRange)||Ie.undefined(t.originSelectionRange))}}(m||(m={})),function(e){e.create=function(e,t,n,r){return{red:e,green:t,blue:n,alpha:r}},e.is=function(e){const t=e;return Ie.objectLiteral(t)&&Ie.numberRange(t.red,0,1)&&Ie.numberRange(t.green,0,1)&&Ie.numberRange(t.blue,0,1)&&Ie.numberRange(t.alpha,0,1)}}(g||(g={})),function(e){e.create=function(e,t){return{range:e,color:t}},e.is=function(e){const t=e;return Ie.objectLiteral(t)&&p.is(t.range)&&g.is(t.color)}}(b||(b={})),function(e){e.create=function(e,t,n){return{label:e,textEdit:t,additionalTextEdits:n}},e.is=function(e){const t=e;return Ie.objectLiteral(t)&&Ie.string(t.label)&&(Ie.undefined(t.textEdit)||O.is(t))&&(Ie.undefined(t.additionalTextEdits)||Ie.typedArray(t.additionalTextEdits,O.is))}}(v||(v={})),function(e){e.Comment="comment",e.Imports="imports",e.Region="region"}(y||(y={})),function(e){e.create=function(e,t,n,r,o,i){const s={startLine:e,endLine:t};return Ie.defined(n)&&(s.startCharacter=n),Ie.defined(r)&&(s.endCharacter=r),Ie.defined(o)&&(s.kind=o),Ie.defined(i)&&(s.collapsedText=i),s},e.is=function(e){const t=e;return Ie.objectLiteral(t)&&Ie.uinteger(t.startLine)&&Ie.uinteger(t.startLine)&&(Ie.undefined(t.startCharacter)||Ie.uinteger(t.startCharacter))&&(Ie.undefined(t.endCharacter)||Ie.uinteger(t.endCharacter))&&(Ie.undefined(t.kind)||Ie.string(t.kind))}}(x||(x={})),function(e){e.create=function(e,t){return{location:e,message:t}},e.is=function(e){let t=e;return Ie.defined(t)&&h.is(t.location)&&Ie.string(t.message)}}(w||(w={})),function(e){e.Error=1,e.Warning=2,e.Information=3,e.Hint=4}(k||(k={})),function(e){e.Unnecessary=1,e.Deprecated=2}(C||(C={})),function(e){e.is=function(e){const t=e;return Ie.objectLiteral(t)&&Ie.string(t.href)}}(T||(T={})),function(e){e.create=function(e,t,n,r,o,i){let s={range:e,message:t};return Ie.defined(n)&&(s.severity=n),Ie.defined(r)&&(s.code=r),Ie.defined(o)&&(s.source=o),Ie.defined(i)&&(s.relatedInformation=i),s},e.is=function(e){var t;let n=e;return Ie.defined(n)&&p.is(n.range)&&Ie.string(n.message)&&(Ie.number(n.severity)||Ie.undefined(n.severity))&&(Ie.integer(n.code)||Ie.string(n.code)||Ie.undefined(n.code))&&(Ie.undefined(n.codeDescription)||Ie.string(null===(t=n.codeDescription)||void 0===t?void 0:t.href))&&(Ie.string(n.source)||Ie.undefined(n.source))&&(Ie.undefined(n.relatedInformation)||Ie.typedArray(n.relatedInformation,w.is))}}(S||(S={})),function(e){e.create=function(e,t,...n){let r={title:e,command:t};return Ie.defined(n)&&n.length>0&&(r.arguments=n),r},e.is=function(e){let t=e;return Ie.defined(t)&&Ie.string(t.title)&&Ie.string(t.command)}}(_||(_={})),function(e){e.replace=function(e,t){return{range:e,newText:t}},e.insert=function(e,t){return{range:{start:e,end:e},newText:t}},e.del=function(e){return{range:e,newText:""}},e.is=function(e){const t=e;return Ie.objectLiteral(t)&&Ie.string(t.newText)&&p.is(t.range)}}(O||(O={})),function(e){e.create=function(e,t,n){const r={label:e};return void 0!==t&&(r.needsConfirmation=t),void 0!==n&&(r.description=n),r},e.is=function(e){const t=e;return Ie.objectLiteral(t)&&Ie.string(t.label)&&(Ie.boolean(t.needsConfirmation)||void 0===t.needsConfirmation)&&(Ie.string(t.description)||void 0===t.description)}}(A||(A={})),function(e){e.is=function(e){const t=e;return Ie.string(t)}}(j||(j={})),function(e){e.replace=function(e,t,n){return{range:e,newText:t,annotationId:n}},e.insert=function(e,t,n){return{range:{start:e,end:e},newText:t,annotationId:n}},e.del=function(e,t){return{range:e,newText:"",annotationId:t}},e.is=function(e){const t=e;return O.is(t)&&(A.is(t.annotationId)||j.is(t.annotationId))}}(E||(E={})),function(e){e.create=function(e,t){return{textDocument:e,edits:t}},e.is=function(e){let t=e;return Ie.defined(t)&&L.is(t.textDocument)&&Array.isArray(t.edits)}}(P||(P={})),function(e){e.create=function(e,t,n){let r={kind:"create",uri:e};return void 0===t||void 0===t.overwrite&&void 0===t.ignoreIfExists||(r.options=t),void 0!==n&&(r.annotationId=n),r},e.is=function(e){let t=e;return t&&"create"===t.kind&&Ie.string(t.uri)&&(void 0===t.options||(void 0===t.options.overwrite||Ie.boolean(t.options.overwrite))&&(void 0===t.options.ignoreIfExists||Ie.boolean(t.options.ignoreIfExists)))&&(void 0===t.annotationId||j.is(t.annotationId))}}($||($={})),function(e){e.create=function(e,t,n,r){let o={kind:"rename",oldUri:e,newUri:t};return void 0===n||void 0===n.overwrite&&void 0===n.ignoreIfExists||(o.options=n),void 0!==r&&(o.annotationId=r),o},e.is=function(e){let t=e;return t&&"rename"===t.kind&&Ie.string(t.oldUri)&&Ie.string(t.newUri)&&(void 0===t.options||(void 0===t.options.overwrite||Ie.boolean(t.options.overwrite))&&(void 0===t.options.ignoreIfExists||Ie.boolean(t.options.ignoreIfExists)))&&(void 0===t.annotationId||j.is(t.annotationId))}}(I||(I={})),function(e){e.create=function(e,t,n){let r={kind:"delete",uri:e};return void 0===t||void 0===t.recursive&&void 0===t.ignoreIfNotExists||(r.options=t),void 0!==n&&(r.annotationId=n),r},e.is=function(e){let t=e;return t&&"delete"===t.kind&&Ie.string(t.uri)&&(void 0===t.options||(void 0===t.options.recursive||Ie.boolean(t.options.recursive))&&(void 0===t.options.ignoreIfNotExists||Ie.boolean(t.options.ignoreIfNotExists)))&&(void 0===t.annotationId||j.is(t.annotationId))}}(R||(R={})),function(e){e.is=function(e){let t=e;return t&&(void 0!==t.changes||void 0!==t.documentChanges)&&(void 0===t.documentChanges||t.documentChanges.every((e=>Ie.string(e.kind)?$.is(e)||I.is(e)||R.is(e):P.is(e))))}}(M||(M={})),function(e){e.create=function(e){return{uri:e}},e.is=function(e){let t=e;return Ie.defined(t)&&Ie.string(t.uri)}}(N||(N={})),function(e){e.create=function(e,t){return{uri:e,version:t}},e.is=function(e){let t=e;return Ie.defined(t)&&Ie.string(t.uri)&&Ie.integer(t.version)}}(z||(z={})),function(e){e.create=function(e,t){return{uri:e,version:t}},e.is=function(e){let t=e;return Ie.defined(t)&&Ie.string(t.uri)&&(null===t.version||Ie.integer(t.version))}}(L||(L={})),function(e){e.create=function(e,t,n,r){return{uri:e,languageId:t,version:n,text:r}},e.is=function(e){let t=e;return Ie.defined(t)&&Ie.string(t.uri)&&Ie.string(t.languageId)&&Ie.integer(t.version)&&Ie.string(t.text)}}(D||(D={})),function(e){e.PlainText="plaintext",e.Markdown="markdown",e.is=function(t){const n=t;return n===e.PlainText||n===e.Markdown}}(B||(B={})),function(e){e.is=function(e){const t=e;return Ie.objectLiteral(e)&&B.is(t.kind)&&Ie.string(t.value)}}(U||(U={})),function(e){e.Text=1,e.Method=2,e.Function=3,e.Constructor=4,e.Field=5,e.Variable=6,e.Class=7,e.Interface=8,e.Module=9,e.Property=10,e.Unit=11,e.Value=12,e.Enum=13,e.Keyword=14,e.Snippet=15,e.Color=16,e.File=17,e.Reference=18,e.Folder=19,e.EnumMember=20,e.Constant=21,e.Struct=22,e.Event=23,e.Operator=24,e.TypeParameter=25}(F||(F={})),function(e){e.PlainText=1,e.Snippet=2}(q||(q={})),function(e){e.Deprecated=1}(V||(V={})),function(e){e.create=function(e,t,n){return{newText:e,insert:t,replace:n}},e.is=function(e){const t=e;return t&&Ie.string(t.newText)&&p.is(t.insert)&&p.is(t.replace)}}(W||(W={})),function(e){e.asIs=1,e.adjustIndentation=2}(H||(H={})),function(e){e.is=function(e){const t=e;return t&&(Ie.string(t.detail)||void 0===t.detail)&&(Ie.string(t.description)||void 0===t.description)}}(G||(G={})),function(e){e.create=function(e){return{label:e}}}(Q||(Q={})),function(e){e.create=function(e,t){return{items:e||[],isIncomplete:!!t}}}(J||(J={})),function(e){e.fromPlainText=function(e){return e.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")},e.is=function(e){const t=e;return Ie.string(t)||Ie.objectLiteral(t)&&Ie.string(t.language)&&Ie.string(t.value)}}(K||(K={})),function(e){e.is=function(e){let t=e;return!!t&&Ie.objectLiteral(t)&&(U.is(t.contents)||K.is(t.contents)||Ie.typedArray(t.contents,K.is))&&(void 0===e.range||p.is(e.range))}}(X||(X={})),function(e){e.create=function(e,t){return t?{label:e,documentation:t}:{label:e}}}(Z||(Z={})),function(e){e.create=function(e,t,...n){let r={label:e};return Ie.defined(t)&&(r.documentation=t),Ie.defined(n)?r.parameters=n:r.parameters=[],r}}(Y||(Y={})),function(e){e.Text=1,e.Read=2,e.Write=3}(ee||(ee={})),function(e){e.create=function(e,t){let n={range:e};return Ie.number(t)&&(n.kind=t),n}}(te||(te={})),function(e){e.File=1,e.Module=2,e.Namespace=3,e.Package=4,e.Class=5,e.Method=6,e.Property=7,e.Field=8,e.Constructor=9,e.Enum=10,e.Interface=11,e.Function=12,e.Variable=13,e.Constant=14,e.String=15,e.Number=16,e.Boolean=17,e.Array=18,e.Object=19,e.Key=20,e.Null=21,e.EnumMember=22,e.Struct=23,e.Event=24,e.Operator=25,e.TypeParameter=26}(ne||(ne={})),function(e){e.Deprecated=1}(re||(re={})),function(e){e.create=function(e,t,n,r,o){let i={name:e,kind:t,location:{uri:r,range:n}};return o&&(i.containerName=o),i}}(oe||(oe={})),function(e){e.create=function(e,t,n,r){return void 0!==r?{name:e,kind:t,location:{uri:n,range:r}}:{name:e,kind:t,location:{uri:n}}}}(ie||(ie={})),function(e){e.create=function(e,t,n,r,o,i){let s={name:e,detail:t,kind:n,range:r,selectionRange:o};return void 0!==i&&(s.children=i),s},e.is=function(e){let t=e;return t&&Ie.string(t.name)&&Ie.number(t.kind)&&p.is(t.range)&&p.is(t.selectionRange)&&(void 0===t.detail||Ie.string(t.detail))&&(void 0===t.deprecated||Ie.boolean(t.deprecated))&&(void 0===t.children||Array.isArray(t.children))&&(void 0===t.tags||Array.isArray(t.tags))}}(se||(se={})),function(e){e.Empty="",e.QuickFix="quickfix",e.Refactor="refactor",e.RefactorExtract="refactor.extract",e.RefactorInline="refactor.inline",e.RefactorRewrite="refactor.rewrite",e.Source="source",e.SourceOrganizeImports="source.organizeImports",e.SourceFixAll="source.fixAll"}(ae||(ae={})),function(e){e.Invoked=1,e.Automatic=2}(ce||(ce={})),function(e){e.create=function(e,t,n){let r={diagnostics:e};return null!=t&&(r.only=t),null!=n&&(r.triggerKind=n),r},e.is=function(e){let t=e;return Ie.defined(t)&&Ie.typedArray(t.diagnostics,S.is)&&(void 0===t.only||Ie.typedArray(t.only,Ie.string))&&(void 0===t.triggerKind||t.triggerKind===ce.Invoked||t.triggerKind===ce.Automatic)}}(ue||(ue={})),function(e){e.create=function(e,t,n){let r={title:e},o=!0;return"string"==typeof t?(o=!1,r.kind=t):_.is(t)?r.command=t:r.edit=t,o&&void 0!==n&&(r.kind=n),r},e.is=function(e){let t=e;return t&&Ie.string(t.title)&&(void 0===t.diagnostics||Ie.typedArray(t.diagnostics,S.is))&&(void 0===t.kind||Ie.string(t.kind))&&(void 0!==t.edit||void 0!==t.command)&&(void 0===t.command||_.is(t.command))&&(void 0===t.isPreferred||Ie.boolean(t.isPreferred))&&(void 0===t.edit||M.is(t.edit))}}(le||(le={})),function(e){e.create=function(e,t){let n={range:e};return Ie.defined(t)&&(n.data=t),n},e.is=function(e){let t=e;return Ie.defined(t)&&p.is(t.range)&&(Ie.undefined(t.command)||_.is(t.command))}}(fe||(fe={})),function(e){e.create=function(e,t){return{tabSize:e,insertSpaces:t}},e.is=function(e){let t=e;return Ie.defined(t)&&Ie.uinteger(t.tabSize)&&Ie.boolean(t.insertSpaces)}}(de||(de={})),function(e){e.create=function(e,t,n){return{range:e,target:t,data:n}},e.is=function(e){let t=e;return Ie.defined(t)&&p.is(t.range)&&(Ie.undefined(t.target)||Ie.string(t.target))}}(pe||(pe={})),function(e){e.create=function(e,t){return{range:e,parent:t}},e.is=function(t){let n=t;return Ie.objectLiteral(n)&&p.is(n.range)&&(void 0===n.parent||e.is(n.parent))}}(he||(he={})),function(e){e.namespace="namespace",e.type="type",e.class="class",e.enum="enum",e.interface="interface",e.struct="struct",e.typeParameter="typeParameter",e.parameter="parameter",e.variable="variable",e.property="property",e.enumMember="enumMember",e.event="event",e.function="function",e.method="method",e.macro="macro",e.keyword="keyword",e.modifier="modifier",e.comment="comment",e.string="string",e.number="number",e.regexp="regexp",e.operator="operator",e.decorator="decorator"}(me||(me={})),function(e){e.declaration="declaration",e.definition="definition",e.readonly="readonly",e.static="static",e.deprecated="deprecated",e.abstract="abstract",e.async="async",e.modification="modification",e.documentation="documentation",e.defaultLibrary="defaultLibrary"}(ge||(ge={})),function(e){e.is=function(e){const t=e;return Ie.objectLiteral(t)&&(void 0===t.resultId||"string"==typeof t.resultId)&&Array.isArray(t.data)&&(0===t.data.length||"number"==typeof t.data[0])}}(be||(be={})),function(e){e.create=function(e,t){return{range:e,text:t}},e.is=function(e){const t=e;return null!=t&&p.is(t.range)&&Ie.string(t.text)}}(ve||(ve={})),function(e){e.create=function(e,t,n){return{range:e,variableName:t,caseSensitiveLookup:n}},e.is=function(e){const t=e;return null!=t&&p.is(t.range)&&Ie.boolean(t.caseSensitiveLookup)&&(Ie.string(t.variableName)||void 0===t.variableName)}}(ye||(ye={})),function(e){e.create=function(e,t){return{range:e,expression:t}},e.is=function(e){const t=e;return null!=t&&p.is(t.range)&&(Ie.string(t.expression)||void 0===t.expression)}}(xe||(xe={})),function(e){e.create=function(e,t){return{frameId:e,stoppedLocation:t}},e.is=function(e){const t=e;return Ie.defined(t)&&p.is(e.stoppedLocation)}}(we||(we={})),function(e){e.Type=1,e.Parameter=2,e.is=function(e){return 1===e||2===e}}(ke||(ke={})),function(e){e.create=function(e){return{value:e}},e.is=function(e){const t=e;return Ie.objectLiteral(t)&&(void 0===t.tooltip||Ie.string(t.tooltip)||U.is(t.tooltip))&&(void 0===t.location||h.is(t.location))&&(void 0===t.command||_.is(t.command))}}(Ce||(Ce={})),function(e){e.create=function(e,t,n){const r={position:e,label:t};return void 0!==n&&(r.kind=n),r},e.is=function(e){const t=e;return Ie.objectLiteral(t)&&d.is(t.position)&&(Ie.string(t.label)||Ie.typedArray(t.label,Ce.is))&&(void 0===t.kind||ke.is(t.kind))&&void 0===t.textEdits||Ie.typedArray(t.textEdits,O.is)&&(void 0===t.tooltip||Ie.string(t.tooltip)||U.is(t.tooltip))&&(void 0===t.paddingLeft||Ie.boolean(t.paddingLeft))&&(void 0===t.paddingRight||Ie.boolean(t.paddingRight))}}(Te||(Te={})),function(e){e.createSnippet=function(e){return{kind:"snippet",value:e}}}(Se||(Se={})),function(e){e.create=function(e,t,n,r){return{insertText:e,filterText:t,range:n,command:r}}}(_e||(_e={})),function(e){e.create=function(e){return{items:e}}}(Oe||(Oe={})),function(e){e.Invoked=0,e.Automatic=1}(Ae||(Ae={})),function(e){e.create=function(e,t){return{range:e,text:t}}}(je||(je={})),function(e){e.create=function(e,t){return{triggerKind:e,selectedCompletionInfo:t}}}(Ee||(Ee={})),function(e){e.is=function(e){const t=e;return Ie.objectLiteral(t)&&u.is(t.uri)&&Ie.string(t.name)}}(Pe||(Pe={})),function(e){function t(e,n){if(e.length<=1)return e;const r=e.length/2|0,o=e.slice(0,r),i=e.slice(r);t(o,n),t(i,n);let s=0,a=0,c=0;for(;s<o.length&&a<i.length;){let t=n(o[s],i[a]);e[c++]=t<=0?o[s++]:i[a++]}for(;s<o.length;)e[c++]=o[s++];for(;a<i.length;)e[c++]=i[a++];return e}e.create=function(e,t,n,r){return new ze(e,t,n,r)},e.is=function(e){let t=e;return!!(Ie.defined(t)&&Ie.string(t.uri)&&(Ie.undefined(t.languageId)||Ie.string(t.languageId))&&Ie.uinteger(t.lineCount)&&Ie.func(t.getText)&&Ie.func(t.positionAt)&&Ie.func(t.offsetAt))},e.applyEdits=function(e,n){let r=e.getText(),o=t(n,((e,t)=>{let n=e.range.start.line-t.range.start.line;return 0===n?e.range.start.character-t.range.start.character:n})),i=r.length;for(let t=o.length-1;t>=0;t--){let n=o[t],s=e.offsetAt(n.range.start),a=e.offsetAt(n.range.end);if(!(a<=i))throw new Error("Overlapping edit");r=r.substring(0,s)+n.newText+r.substring(a,r.length),i=s}return r}}($e||($e={}));class ze{constructor(e,t,n,r){this._uri=e,this._languageId=t,this._version=n,this._content=r,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){if(e){let t=this.offsetAt(e.start),n=this.offsetAt(e.end);return this._content.substring(t,n)}return this._content}update(e,t){this._content=e.text,this._version=t,this._lineOffsets=void 0}getLineOffsets(){if(void 0===this._lineOffsets){let e=[],t=this._content,n=!0;for(let r=0;r<t.length;r++){n&&(e.push(r),n=!1);let o=t.charAt(r);n="\r"===o||"\n"===o,"\r"===o&&r+1<t.length&&"\n"===t.charAt(r+1)&&r++}n&&t.length>0&&e.push(t.length),this._lineOffsets=e}return this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);let t=this.getLineOffsets(),n=0,r=t.length;if(0===r)return d.create(0,e);for(;n<r;){let o=Math.floor((n+r)/2);t[o]>e?r=o:n=o+1}let o=n-1;return d.create(o,e-t[o])}offsetAt(e){let t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;let n=t[e.line],r=e.line+1<t.length?t[e.line+1]:this._content.length;return Math.max(Math.min(n+e.character,r),n)}get lineCount(){return this.getLineOffsets().length}}!function(e){const t=Object.prototype.toString;e.defined=function(e){return void 0!==e},e.undefined=function(e){return void 0===e},e.boolean=function(e){return!0===e||!1===e},e.string=function(e){return"[object String]"===t.call(e)},e.number=function(e){return"[object Number]"===t.call(e)},e.numberRange=function(e,n,r){return"[object Number]"===t.call(e)&&n<=e&&e<=r},e.integer=function(e){return"[object Number]"===t.call(e)&&-2147483648<=e&&e<=2147483647},e.uinteger=function(e){return"[object Number]"===t.call(e)&&0<=e&&e<=2147483647},e.func=function(e){return"[object Function]"===t.call(e)},e.objectLiteral=function(e){return null!==e&&"object"==typeof e},e.typedArray=function(e,t){return Array.isArray(e)&&e.every(t)}}(Ie||(Ie={})),(()=>{var e={470:e=>{function t(e){if("string"!=typeof e)throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}function n(e,t){for(var n,r="",o=0,i=-1,s=0,a=0;a<=e.length;++a){if(a<e.length)n=e.charCodeAt(a);else{if(47===n)break;n=47}if(47===n){if(i===a-1||1===s);else if(i!==a-1&&2===s){if(r.length<2||2!==o||46!==r.charCodeAt(r.length-1)||46!==r.charCodeAt(r.length-2))if(r.length>2){var c=r.lastIndexOf("/");if(c!==r.length-1){-1===c?(r="",o=0):o=(r=r.slice(0,c)).length-1-r.lastIndexOf("/"),i=a,s=0;continue}}else if(2===r.length||1===r.length){r="",o=0,i=a,s=0;continue}t&&(r.length>0?r+="/..":r="..",o=2)}else r.length>0?r+="/"+e.slice(i+1,a):r=e.slice(i+1,a),o=a-i-1;i=a,s=0}else 46===n&&-1!==s?++s:s=-1}return r}var r={resolve:function(){for(var e,r="",o=!1,i=arguments.length-1;i>=-1&&!o;i--){var s;i>=0?s=arguments[i]:(void 0===e&&(e=process.cwd()),s=e),t(s),0!==s.length&&(r=s+"/"+r,o=47===s.charCodeAt(0))}return r=n(r,!o),o?r.length>0?"/"+r:"/":r.length>0?r:"."},normalize:function(e){if(t(e),0===e.length)return".";var r=47===e.charCodeAt(0),o=47===e.charCodeAt(e.length-1);return 0!==(e=n(e,!r)).length||r||(e="."),e.length>0&&o&&(e+="/"),r?"/"+e:e},isAbsolute:function(e){return t(e),e.length>0&&47===e.charCodeAt(0)},join:function(){if(0===arguments.length)return".";for(var e,n=0;n<arguments.length;++n){var o=arguments[n];t(o),o.length>0&&(void 0===e?e=o:e+="/"+o)}return void 0===e?".":r.normalize(e)},relative:function(e,n){if(t(e),t(n),e===n)return"";if((e=r.resolve(e))===(n=r.resolve(n)))return"";for(var o=1;o<e.length&&47===e.charCodeAt(o);++o);for(var i=e.length,s=i-o,a=1;a<n.length&&47===n.charCodeAt(a);++a);for(var c=n.length-a,u=s<c?s:c,l=-1,f=0;f<=u;++f){if(f===u){if(c>u){if(47===n.charCodeAt(a+f))return n.slice(a+f+1);if(0===f)return n.slice(a+f)}else s>u&&(47===e.charCodeAt(o+f)?l=f:0===f&&(l=0));break}var d=e.charCodeAt(o+f);if(d!==n.charCodeAt(a+f))break;47===d&&(l=f)}var p="";for(f=o+l+1;f<=i;++f)f!==i&&47!==e.charCodeAt(f)||(0===p.length?p+="..":p+="/..");return p.length>0?p+n.slice(a+l):(a+=l,47===n.charCodeAt(a)&&++a,n.slice(a))},_makeLong:function(e){return e},dirname:function(e){if(t(e),0===e.length)return".";for(var n=e.charCodeAt(0),r=47===n,o=-1,i=!0,s=e.length-1;s>=1;--s)if(47===(n=e.charCodeAt(s))){if(!i){o=s;break}}else i=!1;return-1===o?r?"/":".":r&&1===o?"//":e.slice(0,o)},basename:function(e,n){if(void 0!==n&&"string"!=typeof n)throw new TypeError('"ext" argument must be a string');t(e);var r,o=0,i=-1,s=!0;if(void 0!==n&&n.length>0&&n.length<=e.length){if(n.length===e.length&&n===e)return"";var a=n.length-1,c=-1;for(r=e.length-1;r>=0;--r){var u=e.charCodeAt(r);if(47===u){if(!s){o=r+1;break}}else-1===c&&(s=!1,c=r+1),a>=0&&(u===n.charCodeAt(a)?-1==--a&&(i=r):(a=-1,i=c))}return o===i?i=c:-1===i&&(i=e.length),e.slice(o,i)}for(r=e.length-1;r>=0;--r)if(47===e.charCodeAt(r)){if(!s){o=r+1;break}}else-1===i&&(s=!1,i=r+1);return-1===i?"":e.slice(o,i)},extname:function(e){t(e);for(var n=-1,r=0,o=-1,i=!0,s=0,a=e.length-1;a>=0;--a){var c=e.charCodeAt(a);if(47!==c)-1===o&&(i=!1,o=a+1),46===c?-1===n?n=a:1!==s&&(s=1):-1!==n&&(s=-1);else if(!i){r=a+1;break}}return-1===n||-1===o||0===s||1===s&&n===o-1&&n===r+1?"":e.slice(n,o)},format:function(e){if(null===e||"object"!=typeof e)throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof e);return function(e,t){var n=t.dir||t.root,r=t.base||(t.name||"")+(t.ext||"");return n?n===t.root?n+r:n+"/"+r:r}(0,e)},parse:function(e){t(e);var n={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return n;var r,o=e.charCodeAt(0),i=47===o;i?(n.root="/",r=1):r=0;for(var s=-1,a=0,c=-1,u=!0,l=e.length-1,f=0;l>=r;--l)if(47!==(o=e.charCodeAt(l)))-1===c&&(u=!1,c=l+1),46===o?-1===s?s=l:1!==f&&(f=1):-1!==s&&(f=-1);else if(!u){a=l+1;break}return-1===s||-1===c||0===f||1===f&&s===c-1&&s===a+1?-1!==c&&(n.base=n.name=0===a&&i?e.slice(1,c):e.slice(a,c)):(0===a&&i?(n.name=e.slice(1,s),n.base=e.slice(1,c)):(n.name=e.slice(a,s),n.base=e.slice(a,c)),n.ext=e.slice(s,c)),a>0?n.dir=e.slice(0,a-1):i&&(n.dir="/"),n},sep:"/",delimiter:":",win32:null,posix:null};r.posix=r,e.exports=r}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var i=t[r]={exports:{}};return e[r](i,i.exports,n),i.exports}n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};(()=>{let e;if(n.r(r),n.d(r,{URI:()=>l,Utils:()=>T}),"object"==typeof process)e="win32"===process.platform;else if("object"==typeof navigator){let t=navigator.userAgent;e=t.indexOf("Windows")>=0}const t=/^\w[\w\d+.-]*$/,o=/^\//,i=/^\/\//;function s(e,n){if(!e.scheme&&n)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${e.authority}", path: "${e.path}", query: "${e.query}", fragment: "${e.fragment}"}`);if(e.scheme&&!t.test(e.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(e.path)if(e.authority){if(!o.test(e.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(i.test(e.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}const a="",c="/",u=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/;class l{static isUri(e){return e instanceof l||!!e&&"string"==typeof e.authority&&"string"==typeof e.fragment&&"string"==typeof e.path&&"string"==typeof e.query&&"string"==typeof e.scheme&&"string"==typeof e.fsPath&&"function"==typeof e.with&&"function"==typeof e.toString}scheme;authority;path;query;fragment;constructor(e,t,n,r,o,i=!1){"object"==typeof e?(this.scheme=e.scheme||a,this.authority=e.authority||a,this.path=e.path||a,this.query=e.query||a,this.fragment=e.fragment||a):(this.scheme=function(e,t){return e||t?e:"file"}(e,i),this.authority=t||a,this.path=function(e,t){switch(e){case"https":case"http":case"file":t?t[0]!==c&&(t=c+t):t=c}return t}(this.scheme,n||a),this.query=r||a,this.fragment=o||a,s(this,i))}get fsPath(){return g(this,!1)}with(e){if(!e)return this;let{scheme:t,authority:n,path:r,query:o,fragment:i}=e;return void 0===t?t=this.scheme:null===t&&(t=a),void 0===n?n=this.authority:null===n&&(n=a),void 0===r?r=this.path:null===r&&(r=a),void 0===o?o=this.query:null===o&&(o=a),void 0===i?i=this.fragment:null===i&&(i=a),t===this.scheme&&n===this.authority&&r===this.path&&o===this.query&&i===this.fragment?this:new d(t,n,r,o,i)}static parse(e,t=!1){const n=u.exec(e);return n?new d(n[2]||a,x(n[4]||a),x(n[5]||a),x(n[7]||a),x(n[9]||a),t):new d(a,a,a,a,a)}static file(t){let n=a;if(e&&(t=t.replace(/\\/g,c)),t[0]===c&&t[1]===c){const e=t.indexOf(c,2);-1===e?(n=t.substring(2),t=c):(n=t.substring(2,e),t=t.substring(e)||c)}return new d("file",n,t,a,a)}static from(e){const t=new d(e.scheme,e.authority,e.path,e.query,e.fragment);return s(t,!0),t}toString(e=!1){return b(this,e)}toJSON(){return this}static revive(e){if(e){if(e instanceof l)return e;{const t=new d(e);return t._formatted=e.external,t._fsPath=e._sep===f?e.fsPath:null,t}}return e}}const f=e?1:void 0;class d extends l{_formatted=null;_fsPath=null;get fsPath(){return this._fsPath||(this._fsPath=g(this,!1)),this._fsPath}toString(e=!1){return e?b(this,!0):(this._formatted||(this._formatted=b(this,!1)),this._formatted)}toJSON(){const e={$mid:1};return this._fsPath&&(e.fsPath=this._fsPath,e._sep=f),this._formatted&&(e.external=this._formatted),this.path&&(e.path=this.path),this.scheme&&(e.scheme=this.scheme),this.authority&&(e.authority=this.authority),this.query&&(e.query=this.query),this.fragment&&(e.fragment=this.fragment),e}}const p={58:"%3A",47:"%2F",63:"%3F",35:"%23",91:"%5B",93:"%5D",64:"%40",33:"%21",36:"%24",38:"%26",39:"%27",40:"%28",41:"%29",42:"%2A",43:"%2B",44:"%2C",59:"%3B",61:"%3D",32:"%20"};function h(e,t,n){let r,o=-1;for(let i=0;i<e.length;i++){const s=e.charCodeAt(i);if(s>=97&&s<=122||s>=65&&s<=90||s>=48&&s<=57||45===s||46===s||95===s||126===s||t&&47===s||n&&91===s||n&&93===s||n&&58===s)-1!==o&&(r+=encodeURIComponent(e.substring(o,i)),o=-1),void 0!==r&&(r+=e.charAt(i));else{void 0===r&&(r=e.substr(0,i));const t=p[s];void 0!==t?(-1!==o&&(r+=encodeURIComponent(e.substring(o,i)),o=-1),r+=t):-1===o&&(o=i)}}return-1!==o&&(r+=encodeURIComponent(e.substring(o))),void 0!==r?r:e}function m(e){let t;for(let n=0;n<e.length;n++){const r=e.charCodeAt(n);35===r||63===r?(void 0===t&&(t=e.substr(0,n)),t+=p[r]):void 0!==t&&(t+=e[n])}return void 0!==t?t:e}function g(t,n){let r;return r=t.authority&&t.path.length>1&&"file"===t.scheme?`//${t.authority}${t.path}`:47===t.path.charCodeAt(0)&&(t.path.charCodeAt(1)>=65&&t.path.charCodeAt(1)<=90||t.path.charCodeAt(1)>=97&&t.path.charCodeAt(1)<=122)&&58===t.path.charCodeAt(2)?n?t.path.substr(1):t.path[1].toLowerCase()+t.path.substr(2):t.path,e&&(r=r.replace(/\//g,"\\")),r}function b(e,t){const n=t?m:h;let r="",{scheme:o,authority:i,path:s,query:a,fragment:u}=e;if(o&&(r+=o,r+=":"),(i||"file"===o)&&(r+=c,r+=c),i){let e=i.indexOf("@");if(-1!==e){const t=i.substr(0,e);i=i.substr(e+1),e=t.lastIndexOf(":"),-1===e?r+=n(t,!1,!1):(r+=n(t.substr(0,e),!1,!1),r+=":",r+=n(t.substr(e+1),!1,!0)),r+="@"}i=i.toLowerCase(),e=i.lastIndexOf(":"),-1===e?r+=n(i,!1,!0):(r+=n(i.substr(0,e),!1,!0),r+=i.substr(e))}if(s){if(s.length>=3&&47===s.charCodeAt(0)&&58===s.charCodeAt(2)){const e=s.charCodeAt(1);e>=65&&e<=90&&(s=`/${String.fromCharCode(e+32)}:${s.substr(3)}`)}else if(s.length>=2&&58===s.charCodeAt(1)){const e=s.charCodeAt(0);e>=65&&e<=90&&(s=`${String.fromCharCode(e+32)}:${s.substr(2)}`)}r+=n(s,!0,!1)}return a&&(r+="?",r+=n(a,!1,!1)),u&&(r+="#",r+=t?u:h(u,!1,!1)),r}function v(e){try{return decodeURIComponent(e)}catch{return e.length>3?e.substr(0,3)+v(e.substr(3)):e}}const y=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function x(e){return e.match(y)?e.replace(y,(e=>v(e))):e}var w=n(470);const k=w.posix||w,C="/";var T;!function(e){e.joinPath=function(e,...t){return e.with({path:k.join(e.path,...t)})},e.resolvePath=function(e,...t){let n=e.path,r=!1;n[0]!==C&&(n=C+n,r=!0);let o=k.resolve(n,...t);return r&&o[0]===C&&!e.authority&&(o=o.substring(1)),e.with({path:o})},e.dirname=function(e){if(0===e.path.length||e.path===C)return e;let t=k.dirname(e.path);return 1===t.length&&46===t.charCodeAt(0)&&(t=""),e.with({path:t})},e.basename=function(e){return k.basename(e.path)},e.extname=function(e){return k.extname(e.path)}}(T||(T={}))})(),Re=r})();const{URI:Le,Utils:De}=Re,Be={properties:["additive-symbols","align-content","align-items","justify-items","justify-self","justify-items","align-self","all","alt","animation","animation-delay","animation-direction","animation-duration","animation-fill-mode","animation-iteration-count","animation-name","animation-play-state","animation-timing-function","backface-visibility","background","background-attachment","background-blend-mode","background-clip","background-color","background-image","background-origin","background-position","background-position-x","background-position-y","background-repeat","background-size","behavior","block-size","border","border-block-end","border-block-start","border-block-end-color","border-block-start-color","border-block-end-style","border-block-start-style","border-block-end-width","border-block-start-width","border-bottom","border-bottom-color","border-bottom-left-radius","border-bottom-right-radius","border-bottom-style","border-bottom-width","border-collapse","border-color","border-image","border-image-outset","border-image-repeat","border-image-slice","border-image-source","border-image-width","border-inline-end","border-inline-start","border-inline-end-color","border-inline-start-color","border-inline-end-style","border-inline-start-style","border-inline-end-width","border-inline-start-width","border-left","border-left-color","border-left-style","border-left-width","border-radius","border-right","border-right-color","border-right-style","border-right-width","border-spacing","border-style","border-top","border-top-color","border-top-left-radius","border-top-right-radius","border-top-style","border-top-width","border-width","bottom","box-decoration-break","box-shadow","box-sizing","break-after","break-before","break-inside","caption-side","caret-color","clear","clip","clip-path","clip-rule","color","color-interpolation-filters","column-count","column-fill","column-gap","column-rule","column-rule-color","column-rule-style","column-rule-width","columns","column-span","column-width","contain","content","counter-increment","counter-reset","cursor","direction","display","empty-cells","enable-background","fallback","fill","fill-opacity","fill-rule","filter","flex","flex-basis","flex-direction","flex-flow","flex-grow","flex-shrink","flex-wrap","float","flood-color","flood-opacity","font","font-family","font-feature-settings","font-kerning","font-language-override","font-size","font-size-adjust","font-stretch","font-style","font-synthesis","font-variant","font-variant-alternates","font-variant-caps","font-variant-east-asian","font-variant-ligatures","font-variant-numeric","font-variant-position","font-weight","glyph-orientation-horizontal","glyph-orientation-vertical","grid-area","grid-auto-columns","grid-auto-flow","grid-auto-rows","grid-column","grid-column-end","grid-column-gap","grid-column-start","grid-gap","grid-row","grid-row-end","grid-row-gap","grid-row-start","grid-template","grid-template-areas","grid-template-columns","grid-template-rows","height","hyphens","image-orientation","image-rendering","ime-mode","inline-size","isolation","justify-content","kerning","left","letter-spacing","lighting-color","line-break","line-height","list-style","list-style-image","list-style-position","list-style-type","margin","margin-block-end","margin-block-start","margin-bottom","margin-inline-end","margin-inline-start","margin-left","margin-right","margin-top","marker","marker-end","marker-mid","marker-start","mask-type","max-block-size","max-height","max-inline-size","max-width","min-block-size","min-height","min-inline-size","min-width","mix-blend-mode","motion","motion-offset","motion-path","motion-rotation","-moz-animation","-moz-animation-delay","-moz-animation-direction","-moz-animation-duration","-moz-animation-iteration-count","-moz-animation-name","-moz-animation-play-state","-moz-animation-timing-function","-moz-appearance","-moz-backface-visibility","-moz-background-clip","-moz-background-inline-policy","-moz-background-origin","-moz-border-bottom-colors","-moz-border-image","-moz-border-left-colors","-moz-border-right-colors","-moz-border-top-colors","-moz-box-align","-moz-box-direction","-moz-box-flex","-moz-box-flexgroup","-moz-box-ordinal-group","-moz-box-orient","-moz-box-pack","-moz-box-sizing","-moz-column-count","-moz-column-gap","-moz-column-rule","-moz-column-rule-color","-moz-column-rule-style","-moz-column-rule-width","-moz-columns","-moz-column-width","-moz-font-feature-settings","-moz-hyphens","-moz-perspective","-moz-perspective-origin","-moz-text-align-last","-moz-text-decoration-color","-moz-text-decoration-line","-moz-text-decoration-style","-moz-text-size-adjust","-moz-transform","-moz-transform-origin","-moz-transition","-moz-transition-delay","-moz-transition-duration","-moz-transition-property","-moz-transition-timing-function","-moz-user-focus","-moz-user-select","-ms-accelerator","-ms-behavior","-ms-block-progression","-ms-content-zoom-chaining","-ms-content-zooming","-ms-content-zoom-limit","-ms-content-zoom-limit-max","-ms-content-zoom-limit-min","-ms-content-zoom-snap","-ms-content-zoom-snap-points","-ms-content-zoom-snap-type","-ms-filter","-ms-flex","-ms-flex-align","-ms-flex-direction","-ms-flex-flow","-ms-flex-item-align","-ms-flex-line-pack","-ms-flex-order","-ms-flex-pack","-ms-flex-wrap","-ms-flow-from","-ms-flow-into","-ms-grid-column","-ms-grid-column-align","-ms-grid-columns","-ms-grid-column-span","-ms-grid-layer","-ms-grid-row","-ms-grid-row-align","-ms-grid-rows","-ms-grid-row-span","-ms-high-contrast-adjust","-ms-hyphenate-limit-chars","-ms-hyphenate-limit-lines","-ms-hyphenate-limit-zone","-ms-hyphens","-ms-ime-mode","-ms-interpolation-mode","-ms-layout-grid","-ms-layout-grid-char","-ms-layout-grid-line","-ms-layout-grid-mode","-ms-layout-grid-type","-ms-line-break","-ms-overflow-style","-ms-perspective","-ms-perspective-origin","-ms-perspective-origin-x","-ms-perspective-origin-y","-ms-progress-appearance","-ms-scrollbar-3dlight-color","-ms-scrollbar-arrow-color","-ms-scrollbar-base-color","-ms-scrollbar-darkshadow-color","-ms-scrollbar-face-color","-ms-scrollbar-highlight-color","-ms-scrollbar-shadow-color","-ms-scrollbar-track-color","-ms-scroll-chaining","-ms-scroll-limit","-ms-scroll-limit-x-max","-ms-scroll-limit-x-min","-ms-scroll-limit-y-max","-ms-scroll-limit-y-min","-ms-scroll-rails","-ms-scroll-snap-points-x","-ms-scroll-snap-points-y","-ms-scroll-snap-type","-ms-scroll-snap-x","-ms-scroll-snap-y","-ms-scroll-translation","-ms-text-align-last","-ms-text-autospace","-ms-text-combine-horizontal","-ms-text-justify","-ms-text-kashida-space","-ms-text-overflow","-ms-text-size-adjust","-ms-text-underline-position","-ms-touch-action","-ms-touch-select","-ms-transform","-ms-transform-origin","-ms-transform-origin-x","-ms-transform-origin-y","-ms-transform-origin-z","-ms-user-select","-ms-word-break","-ms-word-wrap","-ms-wrap-flow","-ms-wrap-margin","-ms-wrap-through","-ms-writing-mode","-ms-zoom","-ms-zoom-animation","nav-down","nav-index","nav-left","nav-right","nav-up","negative","-o-animation","-o-animation-delay","-o-animation-direction","-o-animation-duration","-o-animation-fill-mode","-o-animation-iteration-count","-o-animation-name","-o-animation-play-state","-o-animation-timing-function","object-fit","object-position","-o-border-image","-o-object-fit","-o-object-position","opacity","order","orphans","-o-table-baseline","-o-tab-size","-o-text-overflow","-o-transform","-o-transform-origin","-o-transition","-o-transition-delay","-o-transition-duration","-o-transition-property","-o-transition-timing-function","offset-block-end","offset-block-start","offset-inline-end","offset-inline-start","outline","outline-color","outline-offset","outline-style","outline-width","overflow","overflow-wrap","overflow-x","overflow-y","pad","padding","padding-bottom","padding-block-end","padding-block-start","padding-inline-end","padding-inline-start","padding-left","padding-right","padding-top","page-break-after","page-break-before","page-break-inside","paint-order","perspective","perspective-origin","pointer-events","position","prefix","quotes","range","resize","right","ruby-align","ruby-overhang","ruby-position","ruby-span","scrollbar-3dlight-color","scrollbar-arrow-color","scrollbar-base-color","scrollbar-darkshadow-color","scrollbar-face-color","scrollbar-highlight-color","scrollbar-shadow-color","scrollbar-track-color","scroll-behavior","scroll-snap-coordinate","scroll-snap-destination","scroll-snap-points-x","scroll-snap-points-y","scroll-snap-type","shape-image-threshold","shape-margin","shape-outside","shape-rendering","size","src","stop-color","stop-opacity","stroke","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width","suffix","system","symbols","table-layout","tab-size","text-align","text-align-last","text-anchor","text-decoration","text-decoration-color","text-decoration-line","text-decoration-style","text-indent","text-justify","text-orientation","text-overflow","text-rendering","text-shadow","text-transform","text-underline-position","top","touch-action","transform","transform-origin","transform-style","transition","transition-delay","transition-duration","transition-property","transition-timing-function","unicode-bidi","unicode-range","user-select","vertical-align","visibility","-webkit-animation","-webkit-animation-delay","-webkit-animation-direction","-webkit-animation-duration","-webkit-animation-fill-mode","-webkit-animation-iteration-count","-webkit-animation-name","-webkit-animation-play-state","-webkit-animation-timing-function","-webkit-appearance","-webkit-backdrop-filter","-webkit-backface-visibility","-webkit-background-clip","-webkit-background-composite","-webkit-background-origin","-webkit-border-image","-webkit-box-align","-webkit-box-direction","-webkit-box-flex","-webkit-box-flex-group","-webkit-box-ordinal-group","-webkit-box-orient","-webkit-box-pack","-webkit-box-reflect","-webkit-box-sizing","-webkit-break-after","-webkit-break-before","-webkit-break-inside","-webkit-column-break-after","-webkit-column-break-before","-webkit-column-break-inside","-webkit-column-count","-webkit-column-gap","-webkit-column-rule","-webkit-column-rule-color","-webkit-column-rule-style","-webkit-column-rule-width","-webkit-columns","-webkit-column-span","-webkit-column-width","-webkit-filter","-webkit-flow-from","-webkit-flow-into","-webkit-font-feature-settings","-webkit-hyphens","-webkit-line-break","-webkit-margin-bottom-collapse","-webkit-margin-collapse","-webkit-margin-start","-webkit-margin-top-collapse","-webkit-mask-clip","-webkit-mask-image","-webkit-mask-origin","-webkit-mask-repeat","-webkit-mask-size","-webkit-nbsp-mode","-webkit-overflow-scrolling","-webkit-padding-start","-webkit-perspective","-webkit-perspective-origin","-webkit-region-fragment","-webkit-tap-highlight-color","-webkit-text-fill-color","-webkit-text-size-adjust","-webkit-text-stroke","-webkit-text-stroke-color","-webkit-text-stroke-width","-webkit-touch-callout","-webkit-transform","-webkit-transform-origin","-webkit-transform-origin-x","-webkit-transform-origin-y","-webkit-transform-origin-z","-webkit-transform-style","-webkit-transition","-webkit-transition-delay","-webkit-transition-duration","-webkit-transition-property","-webkit-transition-timing-function","-webkit-user-drag","-webkit-user-modify","-webkit-user-select","white-space","widows","width","will-change","word-break","word-spacing","word-wrap","writing-mode","z-index","zoom"]},Ue={tags:["body","head","html","address","blockquote","dd","div","section","article","aside","header","footer","nav","menu","dl","dt","fieldset","form","frame","frameset","h1","h2","h3","h4","h5","h6","iframe","noframes","object","ol","p","ul","applet","center","dir","hr","pre","a","abbr","acronym","area","b","base","basefont","bdo","big","br","button","caption","cite","code","col","colgroup","del","dfn","em","font","i","img","input","ins","isindex","kbd","label","legend","li","link","map","meta","noscript","optgroup","option","param","q","s","samp","script","select","small","span","strike","strong","style","sub","sup","table","tbody","td","textarea","tfoot","th","thead","title","tr","tt","u","var","canvas","main","figure","plaintext","figcaption","hgroup","details","summary","audio","bdi","data","datalist","dialog","embed","mark","math","meter","output","picture","portal","progress","rp","rt","ruby","search","slot","source","template","time","track","video","wbr"]};var Fe;!function(e){e[e.Unknown=0]="Unknown",e[e.File=1]="File",e[e.Directory=2]="Directory",e[e.SymbolicLink=64]="SymbolicLink"}(Fe||(Fe={}));const qe=new RegExp("^(/|//|\\\\\\\\|[A-Za-z]:(/|\\\\))"),Ve=".".charCodeAt(0);function We(e){const t=[];for(const n of e)0===n.length||1===n.length&&n.charCodeAt(0)===Ve||(2===n.length&&n.charCodeAt(0)===Ve&&n.charCodeAt(1)===Ve?t.pop():t.push(n));e.length>1&&0===e[e.length-1].length&&t.push("");let n=t.join("/");return 0===e[0].length&&(n="/"+n),n}function He(e,...t){const n=e.path.split("/");for(const e of t)n.push(...e.split("/"));return e.with({path:We(n)})}function Ge(e){return e>47&&e<58}function Qe(e,t,n){return n=n||90,(e&=-33)>=(t=t||65)&&e<=n}function Je(e){return Ge(e)||Ke(e)}function Ke(e){return 95===e||Qe(e)}function Xe(e){return function(e){return 32===e||9===e||160===e}(e)||10===e||13===e}function Ze(e){return 39===e||34===e}class Ye{constructor(e,t,n){null==n&&"string"==typeof e&&(n=e.length),this.string=e,this.pos=this.start=t||0,this.end=n||0}eof(){return this.pos>=this.end}limit(e,t){return new Ye(this.string,e,t)}peek(){return this.string.charCodeAt(this.pos)}next(){if(this.pos<this.string.length)return this.string.charCodeAt(this.pos++)}eat(e){const t=this.peek(),n="function"==typeof e?e(t):t===e;return n&&this.next(),n}eatWhile(e){const t=this.pos;for(;!this.eof()&&this.eat(e););return this.pos!==t}backUp(e){this.pos-=e||1}current(){return this.substring(this.start,this.pos)}substring(e,t){return this.string.slice(e,t)}error(e,t=this.pos){return new et(`${e} at ${t+1}`,t,this.string)}}class et extends Error{constructor(e,t,n){super(e),this.pos=t,this.string=n}}function tt(e){return e.tokens[e.pos]}function nt(e){return e.tokens[e.pos++]}function rt(e,t=e.start,n=e.pos){return e.tokens.slice(t,n)}function ot(e){return e.pos<e.size}function it(e,t){const n=tt(e);return!(!n||!t(n)||(e.pos++,0))}function st(e,t,n=tt(e)){n&&null!=n.start&&(t+=` at ${n.start}`);const r=new Error(t);return r.pos=n&&n.start,r}function at(e,t){const n={type:"TokenGroup",elements:[]};let r,o=n;const i=[];for(;ot(e)&&(r=ut(e,t)||ct(e,t));)if(o.elements.push(r),it(e,Pt))i.push(o),o=r;else{if(it(e,$t))continue;if(it(e,It))do{i.length&&(o=i.pop())}while(it(e,It))}return n}function ct(e,t){if(it(e,jt)){const n=at(e,t);return bt(nt(e),"group",!1)&&(n.repeat=function(e){return kt(tt(e))?e.tokens[e.pos++]:void 0}(e)),n}}function ut(e,t){let n;const r={type:"TokenElement",name:void 0,attributes:void 0,value:void 0,repeat:void 0,selfClose:!1,elements:[]};for(function(e,t){const n=e.pos;if(t.jsx&&it(e,Ct))for(;ot(e);){const{pos:t}=e;if(!it(e,St)||!it(e,Ct)){e.pos=t;break}}for(;ot(e)&&it(e,Tt););return e.pos!==n&&(e.start=n,!0)}(e,t)&&(r.name=rt(e));ot(e);)if(e.start=e.pos,r.repeat||Et(r)||!it(e,kt))if(!r.value&&mt(e))r.value=gt(e);else{if(!(n=ft(e,"id",t)||ft(e,"class",t)||lt(e))){!Et(r)&&it(e,Rt)&&(r.selfClose=!0,!r.repeat&&it(e,kt)&&(r.repeat=e.tokens[e.pos-1]));break}r.attributes?r.attributes=r.attributes.concat(n):r.attributes=Array.isArray(n)?n.slice():[n]}else r.repeat=e.tokens[e.pos-1];return Et(r)?void 0:r}function lt(e){if(it(e,_t)){const t=[];let n;for(;ot(e);)if(n=dt(e))t.push(n);else{if(it(e,Ot))break;if(!it(e,xt))throw st(e,`Unexpected "${tt(e).type}" token`)}return t}}function ft(e,t,n){if(vt(tt(e),t)){e.pos++;let o=1;for(;vt(tt(e),t);)e.pos++,o++;const i={name:[(r=t,{type:"Literal",value:r})]};return o>1&&(i.multiple=!0),n.jsx&&mt(e)?(i.value=gt(e),i.expression=!0):i.value=ht(e)?rt(e):void 0,i}var r}function dt(e){if(pt(e))return{value:rt(e)};if(ht(e,!0)){const t=rt(e);let n;return it(e,wt)&&(pt(e)||ht(e,!0))&&(n=rt(e)),{name:t,value:n}}}function pt(e){const t=e.pos,n=tt(e);if(yt(n)){for(e.pos++;ot(e);)if(yt(nt(e),n.single))return e.start=t,!0;throw st(e,"Unclosed quote",n)}return!1}function ht(e,t){const n=e.pos,r={attribute:0,expression:0,group:0};for(;ot(e);){const n=tt(e);if(r.expression)bt(n,"expression")&&(r[n.context]+=n.open?1:-1);else{if(yt(n)||vt(n)||xt(n)||kt(n))break;if(bt(n)){if(!t)break;if(n.open)r[n.context]++;else{if(!r[n.context])break;r[n.context]--}}}e.pos++}return n!==e.pos&&(e.start=n,!0)}function mt(e){const t=e.pos;if(it(e,At)){let n=0;for(;ot(e);){const t=nt(e);if(bt(t,"expression"))if(t.open)n++;else{if(!n)break;n--}}return e.start=t,!0}return!1}function gt(e){let t=e.start,n=e.pos;return bt(e.tokens[t],"expression",!0)&&t++,bt(e.tokens[n-1],"expression",!1)&&n--,rt(e,t,n)}function bt(e,t,n){return Boolean(e&&"Bracket"===e.type&&(!t||e.context===t)&&(null==n||e.open===n))}function vt(e,t){return Boolean(e&&"Operator"===e.type&&(!t||e.operator===t))}function yt(e,t){return Boolean(e&&"Quote"===e.type&&(null==t||e.single===t))}function xt(e){return Boolean(e&&"WhiteSpace"===e.type)}function wt(e){return vt(e,"equal")}function kt(e){return Boolean(e&&"Repeater"===e.type)}function Ct(e){if(function(e){return"Literal"===e.type}(e)){const t=e.value.charCodeAt(0);return t>=65&&t<=90}return!1}function Tt(e){return"Literal"===e.type||"RepeaterNumber"===e.type||"RepeaterPlaceholder"===e.type}function St(e){return vt(e,"class")}function _t(e){return bt(e,"attribute",!0)}function Ot(e){return bt(e,"attribute",!1)}function At(e){return bt(e,"expression",!0)}function jt(e){return bt(e,"group",!0)}function Et(e){return!e.name&&!e.value&&!e.attributes}function Pt(e){return vt(e,"child")}function $t(e){return vt(e,"sibling")}function It(e){return vt(e,"climb")}function Rt(e){return vt(e,"close")}var Mt;function Nt(e){return!!e.eat(Mt.Escape)&&(e.start=e.pos,e.eof()||e.pos++,!0)}function zt(e,t){return function(e,t){const n=e.pos;if((t.expression||t.attribute)&&e.eat(Mt.Dollar)&&e.eat(Mt.CurlyBracketOpen)){let t;e.start=e.pos;let r="";if(e.eatWhile(Ge)?(t=Number(e.current()),r=e.eat(Mt.Colon)?Lt(e):""):Qe(e.peek())&&(r=Lt(e)),e.eat(Mt.CurlyBracketClose))return{type:"Field",index:t,name:r,start:n,end:e.pos};throw e.error("Expecting }")}e.pos=n}(e,t)||function(e){const t=e.pos;if(e.eat(Mt.Dollar)&&e.eat(Mt.Hash))return{type:"RepeaterPlaceholder",value:void 0,start:t,end:e.pos};e.pos=t}(e)||function(e){const t=e.pos;if(e.eatWhile(Mt.Dollar)){const n=e.pos-t;let r=!1,o=1,i=0;if(e.eat(Mt.At)){for(;e.eat(Mt.Climb);)i++;r=e.eat(Mt.Dash),e.start=e.pos,e.eatWhile(Ge)&&(o=Number(e.current()))}return e.start=t,{type:"RepeaterNumber",size:n,reverse:r,base:o,parent:i,start:t,end:e.pos}}}(e)||function(e){const t=e.pos;if(e.eat(Mt.Asterisk)){e.start=e.pos;let n=1,r=!1;return e.eatWhile(Ge)?n=Number(e.current()):r=!0,{type:"Repeater",count:n,value:0,implicit:r,start:t,end:e.pos}}}(e)||function(e){const t=e.pos;if(e.eatWhile(Xe))return{type:"WhiteSpace",start:t,end:e.pos,value:e.substring(t,e.pos)}}(e)||function(e,t){const n=e.pos,r=t.expression;let o="";for(;!e.eof();){if(Nt(e)){o+=e.current();continue}const n=e.peek();if(n===Mt.Slash&&!t.quote&&!t.expression&&!t.attribute){const t=e.string.charCodeAt(e.pos-1),n=e.string.charCodeAt(e.pos+1);if(Ge(t)&&Ge(n)){o+=e.string[e.pos++];continue}}if(n===t.quote||n===Mt.Dollar||Dt(n,t))break;if(r){if(n===Mt.CurlyBracketOpen)t.expression++;else if(n===Mt.CurlyBracketClose){if(!(t.expression>r))break;t.expression--}}else if(!t.quote){if(!t.attribute&&!Wt(n))break;if(Bt(n,t)||Ut(n,t)||Ze(n)||Ft(n))break}o+=e.string[e.pos++]}if(n!==e.pos)return e.start=n,{type:"Literal",value:o,start:n,end:e.pos}}(e,t)||function(e){const t=qt(e.peek());if(t)return{type:"Operator",operator:t,start:e.pos++,end:e.pos}}(e)||function(e){const t=e.peek();if(Ze(t))return{type:"Quote",single:t===Mt.SingleQuote,start:e.pos++,end:e.pos}}(e)||function(e){const t=e.peek(),n=Ft(t);if(n)return{type:"Bracket",open:Vt(t),context:n,start:e.pos++,end:e.pos}}(e)}function Lt(e){const t=[];for(e.start=e.pos;!e.eof();)if(e.eat(Mt.CurlyBracketOpen))t.push(e.pos);else if(e.eat(Mt.CurlyBracketClose)){if(!t.length){e.pos--;break}t.pop()}else e.pos++;if(t.length)throw e.pos=t.pop(),e.error("Expecting }");return e.current()}function Dt(e,t){const n=qt(e);return!(!n||t.quote||t.expression||t.attribute&&"equal"!==n)}function Bt(e,t){return Xe(e)&&!t.expression}function Ut(e,t){return e===Mt.Asterisk&&!t.attribute&&!t.expression}function Ft(e){return e===Mt.RoundBracketOpen||e===Mt.RoundBracketClose?"group":e===Mt.SquareBracketOpen||e===Mt.SquareBracketClose?"attribute":e===Mt.CurlyBracketOpen||e===Mt.CurlyBracketClose?"expression":void 0}function qt(e){return(e===Mt.Child?"child":e===Mt.Sibling&&"sibling")||e===Mt.Climb&&"climb"||e===Mt.Dot&&"class"||e===Mt.Hash&&"id"||e===Mt.Slash&&"close"||e===Mt.Equals&&"equal"||void 0}function Vt(e){return e===Mt.CurlyBracketOpen||e===Mt.SquareBracketOpen||e===Mt.RoundBracketOpen}function Wt(e){return Je(e)||function(e){return 196===e||214==e||220===e||228===e||246===e||252===e}(e)||e===Mt.Dash||e===Mt.Colon||e===Mt.Excl}!function(e){e[e.CurlyBracketOpen=123]="CurlyBracketOpen",e[e.CurlyBracketClose=125]="CurlyBracketClose",e[e.Escape=92]="Escape",e[e.Equals=61]="Equals",e[e.SquareBracketOpen=91]="SquareBracketOpen",e[e.SquareBracketClose=93]="SquareBracketClose",e[e.Asterisk=42]="Asterisk",e[e.Hash=35]="Hash",e[e.Dollar=36]="Dollar",e[e.Dash=45]="Dash",e[e.Dot=46]="Dot",e[e.Slash=47]="Slash",e[e.Colon=58]="Colon",e[e.Excl=33]="Excl",e[e.At=64]="At",e[e.Underscore=95]="Underscore",e[e.RoundBracketOpen=40]="RoundBracketOpen",e[e.RoundBracketClose=41]="RoundBracketClose",e[e.Sibling=43]="Sibling",e[e.Child=62]="Child",e[e.Climb=94]="Climb",e[e.SingleQuote=39]="SingleQuote",e[e.DoubleQuote=34]="DoubleQuote"}(Mt||(Mt={}));const Ht={child:">",class:".",climb:"^",id:"#",equal:"=",close:"/",sibling:"+"},Gt={Literal:e=>e.value,Quote:e=>e.single?"'":'"',Bracket:e=>"attribute"===e.context?e.open?"[":"]":"expression"===e.context?e.open?"{":"}":e.open?"(":"}",Operator:e=>Ht[e.operator],Field:(e,t)=>null!=e.index?e.name?`\${${e.index}:${e.name}}`:`\${${e.index}`:e.name?t.getVariable(e.name):"",RepeaterPlaceholder(e,t){let n;for(let e=t.repeaters.length-1;e>=0;e--)if(t.repeaters[e].implicit){n=t.repeaters[e];break}return t.inserted=!0,t.getText(n&&n.value)},RepeaterNumber(e,t){let n=1;const r=t.repeaters.length-1,o=t.repeaters[r];if(o&&(n=e.reverse?e.base+o.count-o.value-1:e.base+o.value,e.parent)){const i=Math.max(0,r-e.parent);if(i!==r){const e=t.repeaters[i];n+=o.count*e.value}}let i=String(n);for(;i.length<e.size;)i="0"+i;return i},WhiteSpace:e=>e.value};function Qt(e,t){if(!Gt[e.type])throw new Error(`Unknown token ${e.type}`);return Gt[e.type](e,t)}const Jt=/^((https?:|ftp:|file:)?\/\/|(www|ftp)\.)[^ ]*$/,Kt=/^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,5}$/;function Xt(e,t){let n=[];if(e.repeat){const r=e.repeat,o=Object.assign({},r);let i;o.count=o.implicit&&Array.isArray(t.text)?t.cleanText.length:o.count||1,t.repeaters.push(o);for(let r=0;r<o.count;r++){if(o.value=r,e.repeat=o,i=rn(e)?Yt(e,t):Zt(e,t),o.implicit&&!t.inserted){const e=sn(i),n=e&&an(e);n&&cn(n,t.getText(o.value))}if(n=n.concat(i),--t.repeatGuard<=0)break}t.repeaters.pop(),e.repeat=r,o.implicit&&(t.inserted=!0)}else n=n.concat(rn(e)?Yt(e,t):Zt(e,t));return n}function Zt(e,t){let n=[];const r={type:"AbbreviationNode",name:e.name&&tn(e.name,t),value:e.value&&nn(e.value,t),attributes:void 0,children:n,repeat:e.repeat&&Object.assign({},e.repeat),selfClosing:e.selfClose};let o=[r];for(const r of e.elements)n=n.concat(Xt(r,t));if(e.attributes){r.attributes=[];for(const n of e.attributes)r.attributes.push(en(n,t))}return r.name||r.attributes||!r.value||r.value.some(on)?r.children=n:o=o.concat(n),o}function Yt(e,t){let n=[];for(const r of e.elements)n=n.concat(Xt(r,t));return e.repeat&&(n=function(e,t){for(const n of e)n.repeat||(n.repeat=Object.assign({},t));return e}(n,e.repeat)),n}function en(e,t){let n,r=!1,o=!1,i=e.expression?"expression":"raw";const s=e.name&&tn(e.name,t);if(s&&"!"===s[0]&&(r=!0),s&&"."===s[s.length-1]&&(o=!0),e.value){const r=e.value.slice();if(yt(r[0])){const e=r.shift();r.length&&sn(r).type===e.type&&r.pop(),i=e.single?"singleQuote":"doubleQuote"}else bt(r[0],"expression",!0)&&(i="expression",r.shift(),bt(sn(r),"expression",!1)&&r.pop());n=nn(r,t)}return{name:o||r?s.slice(r?1:0,o?-1:void 0):s,value:n,boolean:o,implied:r,valueType:i,multiple:e.multiple}}function tn(e,t){let n="";for(let r=0;r<e.length;r++)n+=Qt(e[r],t);return n}function nn(e,t){const n=[];let r="";for(let o,i=0;i<e.length;i++)o=e[i],on(o)?(r&&(n.push(r),r=""),n.push(o)):r+=Qt(o,t);return r&&n.push(r),n}function rn(e){return"TokenGroup"===e.type}function on(e){return"object"==typeof e&&"Field"===e.type&&null!=e.index}function sn(e){return e[e.length-1]}function an(e){return e.children.length?an(sn(e.children)):e}function cn(e,t){e.value?"string"==typeof sn(e.value)?e.value[e.value.length-1]+=t:e.value.push(t):e.value=[t]}function un(e,t){try{return function(e,t={}){let n,r=!1;t.text&&(n=Array.isArray(t.text)?t.text.filter((e=>e.trim())):t.text);const o={type:"Abbreviation",children:Yt(e,{inserted:!1,repeaters:[],text:t.text,cleanText:n,repeatGuard:t.maxRepeat||Number.POSITIVE_INFINITY,getText(e){var o;let i;if(r=!0,Array.isArray(t.text)){if(void 0!==e&&e>=0&&e<n.length)return n[e];i=void 0!==e?t.text[e]:t.text.join("\n")}else i=null!==(o=t.text)&&void 0!==o?o:"";return i},getVariable(e){const n=t.variables&&t.variables[e];return null!=n?n:e}})};if(null!=t.text&&!r){const e=an(sn(o.children));if(e){const n=Array.isArray(t.text)?t.text.join("\n"):t.text;cn(e,n),"a"===e.name&&t.href&&function(e,t){var n;let r="";Jt.test(t)?(r=t,/\w+:/.test(r)||r.startsWith("//")||(r=`http://${r}`)):Kt.test(t)&&(r=`mailto:${t}`);const o=null===(n=e.attributes)||void 0===n?void 0:n.find((e=>"href"===e.name));o?o.value||(o.value=[r]):(e.attributes||(e.attributes=[]),e.attributes.push({name:"href",value:[r],valueType:"doubleQuote"}))}(e,n)}}return o}(function(e,t={}){const n={tokens:r=e,start:0,pos:0,size:r.length};var r;const o=at(n,t);if(ot(n))throw st(n,"Unexpected character");return o}("string"==typeof e?function(e){const t=new Ye(e),n=[],r={group:0,attribute:0,expression:0,quote:0};let o,i=0;for(;!t.eof();){if(i=t.peek(),o=zt(t,r),!o)throw t.error("Unexpected character");n.push(o),"Quote"===o.type?r.quote=i===r.quote?0:i:"Bracket"===o.type&&(r[o.context]+=o.open?1:-1)}return n}(e):e,t),t)}catch(t){throw t instanceof et&&"string"==typeof e&&(t.message+=`\n${e}\n${"-".repeat(t.pos)}^`),t}}var ln,fn;function dn(e,t){return function(e){const t=e.pos;if(e.eat(fn.Dollar)&&e.eat(fn.CurlyBracketOpen)){let n;e.start=e.pos;let r="";if(e.eatWhile(Ge)?(n=Number(e.current()),r=e.eat(fn.Colon)?pn(e):""):Qe(e.peek())&&(r=pn(e)),e.eat(fn.CurlyBracketClose))return{type:"Field",index:n,name:r,start:t,end:e.pos};throw e.error("Expecting }")}e.pos=t}(e)||function(e){const t=e.pos;if(e.eat(fn.Dash)&&e.eat(fn.Dash))return e.start=t,e.eatWhile(yn),{type:"CustomProperty",value:e.current(),start:t,end:e.pos};e.pos=t}(e)||function(e){const t=e.pos;if(function(e){const t=e.pos;e.eat(fn.Dash);const n=e.pos,r=e.eatWhile(Ge),o=e.pos;if(e.eat(fn.Dot)){const t=e.eatWhile(Ge);r||t||(e.pos=o)}return e.pos===n&&(e.pos=t),e.pos!==t}(e)){e.start=t;const n=e.current();return e.start=e.pos,e.eat(fn.Percent)||e.eatWhile(Ke),{type:"NumberValue",value:Number(n),rawValue:n,unit:e.current(),start:t,end:e.pos}}}(e)||function(e){const t=e.pos;if(e.eat(fn.Hash)){const n=e.pos;let r="",o="";if(e.eatWhile(vn)?(r=e.substring(n,e.pos),o=mn(e)):e.eat(fn.Transparent)?(r="0",o=mn(e)||"0"):o=mn(e),r||o||e.eof()){const{r:n,g:i,b:s,a}=function(e,t){let n="0",r="0",o="0",i=Number(null!=t&&""!==t?t:1);if("t"===e)i=0;else switch(e.length){case 0:break;case 1:n=r=o=e+e;break;case 2:n=r=o=e;break;case 3:n=e[0]+e[0],r=e[1]+e[1],o=e[2]+e[2];break;default:n=(e+=e).slice(0,2),r=e.slice(2,4),o=e.slice(4,6)}return{r:parseInt(n,16),g:parseInt(r,16),b:parseInt(o,16),a:i}}(r,o);return{type:"ColorValue",r:n,g:i,b:s,a,raw:e.substring(t+1,e.pos),start:t,end:e.pos}}return hn(e,t)}e.pos=t}(e)||function(e){const t=e.peek(),n=e.pos;let r=!1;if(Ze(t)){for(e.pos++;!e.eof();){if(e.eat(t)){r=!0;break}e.pos++}return e.start=n,{type:"StringValue",value:e.substring(n+1,e.pos-(r?1:0)),quote:t===fn.SingleQuote?"single":"double",start:n,end:e.pos}}}(e)||function(e){const t=e.peek();if(function(e){return e===fn.RoundBracketOpen||e===fn.RoundBracketClose}(t))return{type:"Bracket",open:t===fn.RoundBracketOpen,start:e.pos++,end:e.pos}}(e)||gn(e)||function(e){const t=e.pos;if(e.eatWhile(Xe))return{type:"WhiteSpace",start:t,end:e.pos}}(e)||function(e,t){const n=e.pos;if(e.eat(bn)?e.eatWhile(n?yn:xn):e.eat(Ke)?e.eatWhile(t?xn:yn):(e.eat(fn.Dot),e.eatWhile(xn)),n!==e.pos)return e.start=n,hn(e,e.start=n)}(e,t)}function pn(e){const t=[];for(e.start=e.pos;!e.eof();)if(e.eat(fn.CurlyBracketOpen))t.push(e.pos);else if(e.eat(fn.CurlyBracketClose)){if(!t.length){e.pos--;break}t.pop()}else e.pos++;if(t.length)throw e.pos=t.pop(),e.error("Expecting }");return e.current()}function hn(e,t=e.start,n=e.pos){return{type:"Literal",value:e.substring(t,n),start:t,end:n}}function mn(e){const t=e.pos;return e.eat(fn.Dot)?(e.start=t,e.eatWhile(Ge)?e.current():"1"):""}function gn(e){const t=(n=e.peek())===fn.Sibling&&ln.Sibling||n===fn.Excl&&ln.Important||n===fn.Comma&&ln.ArgumentDelimiter||n===fn.Colon&&ln.PropertyDelimiter||n===fn.Dash&&ln.ValueDelimiter||void 0;var n;if(t)return{type:"Operator",operator:t,start:e.pos++,end:e.pos}}function bn(e){return e===fn.At||e===fn.Dollar}function vn(e){return Ge(e)||Qe(e,65,70)}function yn(e){return Je(e)||e===fn.Dash}function xn(e){return Ke(e)||e===fn.Percent||e===fn.Slash}function wn(e){return"ColorValue"===e.type||"NumberValue"===e.type&&!e.unit}function kn(e,t){let n=0,r=0;for(;t.length;){const e=(o=t)[o.length-1];if("Literal"!==e.type&&"NumberValue"!==e.type)break;n=e.start,r||(r=e.end),t.pop()}var o;n!==r&&t.push(hn(e,n,r))}function Cn(e){return e.tokens[e.pos]}function Tn(e){return e.pos<e.size}function Sn(e,t){return!!t(Cn(e))&&(e.pos++,!0)}function _n(e,t,n=Cn(e)){n&&null!=n.start&&(t+=` at ${n.start}`);const r=new Error(t);return r.pos=n&&n.start,r}function On(e,t){let n,r,o=!1;const i=[],s=Cn(e),a=!!t.value;for(a||!En(s)||function(e){const t=e.tokens[e.pos],n=e.tokens[e.pos+1];return t&&n&&En(t)&&"Bracket"===n.type}(e)||(e.pos++,n=s.value,Sn(e,Un)),a&&Sn(e,Rn);Tn(e);)if(Sn(e,Dn))o=!0;else if(r=An(e,a))i.push(r);else if(!Sn(e,Ln))break;if(n||i.length||o)return{name:n,value:i,important:o}}function An(e,t){const n=[];let r,o;for(;Tn(e);)if(r=Cn(e),Bn(r))e.pos++,En(r)&&(o=jn(e))?n.push({type:"FunctionCall",name:r.value,arguments:o}):n.push(r);else{if(!(Un(r)||t&&Rn(r)))break;e.pos++}return n.length?{type:"CSSValue",value:n}:void 0}function jn(e){const t=e.pos;if(Sn(e,$n)){const n=[];let r;for(;Tn(e)&&!Sn(e,In);)if(r=An(e,!0))n.push(r);else if(!Sn(e,Rn)&&!Sn(e,zn))throw _n(e,"Unexpected token");return e.start=t,n}}function En(e){return e&&"Literal"===e.type}function Pn(e,t){return e&&"Bracket"===e.type&&(null==t||e.open===t)}function $n(e){return Pn(e,!0)}function In(e){return Pn(e,!1)}function Rn(e){return e&&"WhiteSpace"===e.type}function Mn(e,t){return e&&"Operator"===e.type&&(!t||e.operator===t)}function Nn(e){return Mn(e,ln.Sibling)}function zn(e){return Mn(e,ln.ArgumentDelimiter)}function Ln(e){return zn(e)}function Dn(e){return Mn(e,ln.Important)}function Bn(e){return"StringValue"===e.type||"ColorValue"===e.type||"NumberValue"===e.type||"Literal"===e.type||"Field"===e.type||"CustomProperty"===e.type}function Un(e){return Mn(e,ln.PropertyDelimiter)||Mn(e,ln.ValueDelimiter)}function Fn(e,t){try{const n="string"==typeof e?function(e,t){let n,r=0;const o=new Ye(e),i=[];for(;!o.eof();){if(n=dn(o,0===r&&!t),!n)throw o.error("Unexpected character");if("Bracket"===n.type&&(!r&&n.open&&kn(o,i),r+=n.open?1:-1,r<0))throw o.error("Unexpected bracket",n.start);i.push(n),wn(n)&&(n=gn(o))&&i.push(n)}return i}(e,t&&t.value):e;return function(e,t={}){const n=function(e){return{tokens:e,start:0,pos:0,size:e.length}}(e),r=[];let o;for(;Tn(n);)if(o=On(n,t))r.push(o);else if(!Sn(n,Nn))throw _n(n,"Unexpected token");return r}(n,t)}catch(t){throw t instanceof et&&"string"==typeof e&&(t.message+=`\n${e}\n${"-".repeat(t.pos)}^`),t}}function qn(e,t,n){if(e&&t){e.length&&n&&Wn(e,n);for(const n of t)Wn(e,n);return e}const r=e||t;return r&&r.slice()}function Vn(e,t,n){return e.name=t.name,n.options["output.reverseAttributes"]||(e.value=t.value),e.implied||(e.implied=t.implied),e.boolean||(e.boolean=t.boolean),"expression"!==e.valueType&&(e.valueType=t.valueType),e}function Wn(e,t){const n=e.length-1;"string"==typeof e[n]&&"string"==typeof t?e[n]+=t:e.push(t)}function Hn(e,t){for(let n=0;n<e.children.length;n++){const r=e.children[n];if(t(r))return r;const o=Hn(r,t);if(o)return o}}function Gn(e){let t;for(;e.children.length;)t=e,e=e.children[e.children.length-1];return{parent:t,node:e}}function Qn(e){return"AbbreviationNode"===e.type}function Jn(e,t,n){let r=[];for(const n of e.children){const e=t(n);if(e){r=r.concat(e.children);const o=Gn(e);Qn(o.node)&&(o.node.children=o.node.children.concat(Jn(n,t)))}else r.push(n),n.children=Jn(n,t)}return e.children=r}!function(e){e.Sibling="+",e.Important="!",e.ArgumentDelimiter=",",e.ValueDelimiter="-",e.PropertyDelimiter=":"}(ln||(ln={})),function(e){e[e.Hash=35]="Hash",e[e.Dollar=36]="Dollar",e[e.Dash=45]="Dash",e[e.Dot=46]="Dot",e[e.Colon=58]="Colon",e[e.Comma=44]="Comma",e[e.Excl=33]="Excl",e[e.At=64]="At",e[e.Percent=37]="Percent",e[e.Underscore=95]="Underscore",e[e.RoundBracketOpen=40]="RoundBracketOpen",e[e.RoundBracketClose=41]="RoundBracketClose",e[e.CurlyBracketOpen=123]="CurlyBracketOpen",e[e.CurlyBracketClose=125]="CurlyBracketClose",e[e.Sibling=43]="Sibling",e[e.SingleQuote=39]="SingleQuote",e[e.DoubleQuote=34]="DoubleQuote",e[e.Transparent=116]="Transparent",e[e.Slash=47]="Slash"}(fn||(fn={}));const Kn="{",Xn="}";function Zn(e,t=0){return{options:e,value:"",level:t,offset:0,line:0,column:0}}function Yn(e,t){ar(e,(0,e.options["output.text"])(t,e.offset,e.line,e.column))}function er(e,t){const n=t.split(/\r\n|\r|\n/g);for(let t=0,r=n.length-1;t<=r;t++)Yn(e,n[t]),t!==r&&tr(e,!0)}function tr(e,t){const n=e.options["output.baseIndent"];Yn(e,e.options["output.newline"]+n),e.line++,e.column=n.length,t&&function(e,t=e.level){Yn(e,e.options["output.indent"].repeat(Math.max(t,0)))}(e,!0===t?e.level:t)}function nr(e,t,n){ar(e,(0,e.options["output.field"])(t,n,e.offset,e.line,e.column))}function rr(e,t){return cr(e,t.options["output.attributeCase"])}function or(e,t,n){return"expression"===e.valueType?n?Kn:Xn:"single"===t.options["output.attributeQuotes"]?"'":'"'}function ir(e,t){return e.boolean||t.options["output.booleanAttributes"].includes((e.name||"").toLowerCase())}function sr(e,t){return"string"==typeof e?t.options.inlineElements.includes(e.toLowerCase()):e.name?sr(e.name,t):Boolean(e.value&&!e.attributes)}function ar(e,t){e.value+=t,e.offset+=t.length,e.column+=t.length}function cr(e,t){return t?"upper"===t?e.toUpperCase():e.toLowerCase():e}const ur={p:"span",ul:"li",ol:"li",table:"tr",tr:"td",tbody:"tr",thead:"tr",tfoot:"tr",colgroup:"col",select:"option",optgroup:"option",audio:"source",video:"source",object:"param",map:"area"};function lr(e,t,n){const r=function(e){for(let t=e.length-1;t>=0;t--){const n=e[t];if(Qn(n))return n}}(t),o=n.context?n.context.name:"",i=((r?r.name:o)||"").toLowerCase();e.name=ur[i]||(sr(i,n)?"span":"div")}const fr={ru:{common:["далеко-далеко","за","словесными","горами","в стране","гласных","и согласных","живут","рыбные","тексты"],words:["вдали","от всех","они","буквенных","домах","на берегу","семантика","большого","языкового","океана","маленький","ручеек","даль","журчит","по всей","обеспечивает","ее","всеми","необходимыми","правилами","эта","парадигматическая","страна","которой","жаренные","предложения","залетают","прямо","рот","даже","всемогущая","пунктуация","не","имеет","власти","над","рыбными","текстами","ведущими","безорфографичный","образ","жизни","однажды","одна","маленькая","строчка","рыбного","текста","имени","lorem","ipsum","решила","выйти","большой","мир","грамматики","великий","оксмокс","предупреждал","о","злых","запятых","диких","знаках","вопроса","коварных","точках","запятой","но","текст","дал","сбить","себя","толку","он","собрал","семь","своих","заглавных","букв","подпоясал","инициал","за","пояс","пустился","дорогу","взобравшись","первую","вершину","курсивных","гор","бросил","последний","взгляд","назад","силуэт","своего","родного","города","буквоград","заголовок","деревни","алфавит","подзаголовок","своего","переулка","грустный","реторический","вопрос","скатился","его","щеке","продолжил","свой","путь","дороге","встретил","рукопись","она","предупредила","моей","все","переписывается","несколько","раз","единственное","что","меня","осталось","это","приставка","возвращайся","ты","лучше","свою","безопасную","страну","послушавшись","рукописи","наш","продолжил","свой","путь","вскоре","ему","повстречался","коварный","составитель","рекламных","текстов","напоивший","языком","речью","заманивший","свое","агентство","которое","использовало","снова","снова","своих","проектах","если","переписали","то","живет","там","до","сих","пор"]},sp:{common:["mujer","uno","dolor","más","de","poder","mismo","si"],words:["ejercicio","preferencia","perspicacia","laboral","paño","suntuoso","molde","namibia","planeador","mirar","demás","oficinista","excepción","odio","consecuencia","casi","auto","chicharra","velo","elixir","ataque","no","odio","temporal","cuórum","dignísimo","facilismo","letra","nihilista","expedición","alma","alveolar","aparte","león","animal","como","paria","belleza","modo","natividad","justo","ataque","séquito","pillo","sed","ex","y","voluminoso","temporalidad","verdades","racional","asunción","incidente","marejada","placenta","amanecer","fuga","previsor","presentación","lejos","necesariamente","sospechoso","adiposidad","quindío","pócima","voluble","débito","sintió","accesorio","falda","sapiencia","volutas","queso","permacultura","laudo","soluciones","entero","pan","litro","tonelada","culpa","libertario","mosca","dictado","reincidente","nascimiento","dolor","escolar","impedimento","mínima","mayores","repugnante","dulce","obcecado","montaña","enigma","total","deletéreo","décima","cábala","fotografía","dolores","molesto","olvido","paciencia","resiliencia","voluntad","molestias","magnífico","distinción","ovni","marejada","cerro","torre","y","abogada","manantial","corporal","agua","crepúsculo","ataque","desierto","laboriosamente","angustia","afortunado","alma","encefalograma","materialidad","cosas","o","renuncia","error","menos","conejo","abadía","analfabeto","remo","fugacidad","oficio","en","almácigo","vos","pan","represión","números","triste","refugiado","trote","inventor","corchea","repelente","magma","recusado","patrón","explícito","paloma","síndrome","inmune","autoinmune","comodidad","ley","vietnamita","demonio","tasmania","repeler","apéndice","arquitecto","columna","yugo","computador","mula","a","propósito","fantasía","alias","rayo","tenedor","deleznable","ventana","cara","anemia","corrupto"]},latin:{common:["lorem","ipsum","dolor","sit","amet","consectetur","adipisicing","elit"],words:["exercitationem","perferendis","perspiciatis","laborum","eveniet","sunt","iure","nam","nobis","eum","cum","officiis","excepturi","odio","consectetur","quasi","aut","quisquam","vel","eligendi","itaque","non","odit","tempore","quaerat","dignissimos","facilis","neque","nihil","expedita","vitae","vero","ipsum","nisi","animi","cumque","pariatur","velit","modi","natus","iusto","eaque","sequi","illo","sed","ex","et","voluptatibus","tempora","veritatis","ratione","assumenda","incidunt","nostrum","placeat","aliquid","fuga","provident","praesentium","rem","necessitatibus","suscipit","adipisci","quidem","possimus","voluptas","debitis","sint","accusantium","unde","sapiente","voluptate","qui","aspernatur","laudantium","soluta","amet","quo","aliquam","saepe","culpa","libero","ipsa","dicta","reiciendis","nesciunt","doloribus","autem","impedit","minima","maiores","repudiandae","ipsam","obcaecati","ullam","enim","totam","delectus","ducimus","quis","voluptates","dolores","molestiae","harum","dolorem","quia","voluptatem","molestias","magni","distinctio","omnis","illum","dolorum","voluptatum","ea","quas","quam","corporis","quae","blanditiis","atque","deserunt","laboriosam","earum","consequuntur","hic","cupiditate","quibusdam","accusamus","ut","rerum","error","minus","eius","ab","ad","nemo","fugit","officia","at","in","id","quos","reprehenderit","numquam","iste","fugiat","sit","inventore","beatae","repellendus","magnam","recusandae","quod","explicabo","doloremque","aperiam","consequatur","asperiores","commodi","optio","dolor","labore","temporibus","repellat","veniam","architecto","est","esse","mollitia","nulla","a","similique","eos","alias","dolore","tenetur","deleniti","porro","facere","maxime","corrupti"]}},dr=/^lorem([a-z]*)(\d*)(-\d*)?$/i;function pr(e,t){return Math.floor(Math.random()*(t-e)+e)}function hr(e,t){const n=e.length,r=Math.min(n,t),o=[];for(;o.length<r;){const t=e[pr(0,n)];o.includes(t)||o.push(t)}return o}function mr(e,t){var n;return e.length&&(e=[(n=e[0],n[0].toUpperCase()+n.slice(1))].concat(e.slice(1))),e.join(" ")+(t||"?!..."[pr(0,4)])}function gr(e){if(e.length<2)return e;const t=(e=e.slice()).length,n=/,$/;let r=0;r=t>3&&t<=6?pr(0,1):t>6&&t<=12?pr(0,2):pr(1,4);for(let o,i=0;i<r;i++)o=pr(0,t-2),n.test(e[o])||(e[o]+=",");return e}function br(e,t,n){const r=[];let o,i=0;for(n&&e.common&&(o=e.common.slice(0,t),i+=o.length,r.push(mr(gr(o),".")));i<t;)o=hr(e.words,Math.min(pr(2,30),t-i)),i+=o.length,r.push(mr(gr(o)));return r.join(" ")}function vr(e){return"select"!==e.name}const yr=/^(-+)([a-z0-9]+[a-z0-9-]*)/i,xr=/^(_+)([a-z0-9]+[a-z0-9-_]*)/i,wr=e=>/^[a-z]\-/i.test(e),kr=e=>/^[a-z]/i.test(e);function Cr(e){if(!e._bem){let t="";if(e.attributes)for(const n of e.attributes)if("class"===n.name&&n.value){t=jr(n.value);break}e._bem=Tr(t)}return e._bem}function Tr(e){const t=e?e.split(/\s+/):[];return{classNames:t,block:_r(t)}}function Sr(e,t=0,n){let r=Math.max(e.length-t,0);do{const t=e[r];if(t){const e=Cr(t);if(e.block)return e.block}}while(0<r--);if(n){const e=function(e){return e._bem||(e._bem=Tr(e.attributes&&e.attributes.class||"")),e._bem}(n);if(e.block)return e.block}return""}function _r(e){return Or(e,wr)||Or(e,kr)||void 0}function Or(e,t){for(const n of e){if(yr.test(n)||xr.test(n))break;if(t(n))return n}}function Ar(e,t){for(const n of e.attributes)if("class"===n.name){n.value=[t];break}}function jr(e){let t="";for(const n of e)t+="string"==typeof n?n:n.name;return t}function Er(e,t,n){return!!e&&n.indexOf(e)===t}function Pr(e){if("label"===e.name){const t=Hn(e,(e=>"input"===e.name||"textarea"===e.name));t&&(e.attributes&&(e.attributes=e.attributes.filter((e=>!("for"===e.name&&$r(e))))),t.attributes&&(t.attributes=t.attributes.filter((e=>!("id"===e.name&&$r(e))))))}}function $r(e){if(!e.value)return!0;if(1===e.value.length){const t=e.value[0];if(t&&"string"!=typeof t&&!t.name)return!0}return!1}function Ir(e,t,n){const r=(e,r,i)=>{const{parent:s,current:a}=n;n.parent=a,n.current=e,t(e,r,i,n,o),n.current=a,n.parent=s},o=(e,t,o)=>{n.ancestors.push(n.current),r(e,t,o),n.ancestors.pop()};e.children.forEach(r)}function Rr(e){return{current:null,parent:void 0,ancestors:[],config:e,field:1,out:Zn(e.options)}}const Mr=[{type:"Field",index:0,name:""}];function Nr(e){return!!e&&!e.name&&!e.attributes}function zr(e,t){return!!e&&sr(e,t)}function Lr(e){return"object"==typeof e&&"Field"===e.type}function Dr(e,t){const{out:n}=t;let r=-1;for(const o of e)"string"==typeof o?er(n,o):(nr(n,t.field+o.index,o.name),o.index>r&&(r=o.index));-1!==r&&(t.field+=r+1)}function Br(e){return!e.implied||"raw"!==e.valueType||!!e.value&&e.value.length>0}var Ur;function Fr(e){const t=[],n={pos:0,text:e};let r,o=n.pos,i=n.pos;for(;n.pos<n.text.length;)i=n.pos,(r=qr(n))?(o!==n.pos&&t.push(e.slice(o,i)),t.push(r),o=n.pos):n.pos++;return o!==n.pos&&t.push(e.slice(o)),t}function qr(e){if(Vr(e)===Ur.Start){const t=++e.pos;let n=t,r=t,o=1;for(;e.pos<e.text.length;){const i=Vr(e);if(Wr(i)){for(n=e.pos;Hr(Vr(e));)e.pos++;r=e.pos}else{if(i===Ur.Start)o++;else if(i===Ur.End&&0==--o)return{before:e.text.slice(t,n),after:e.text.slice(r,e.pos++),name:e.text.slice(n,r)};e.pos++}}}}function Vr(e,t=e.pos){return e.text.charCodeAt(t)}function Wr(e){return e>=65&&e<=90}function Hr(e){return Wr(e)||e>47&&e<58||e===Ur.Underscore||e===Ur.Dash}function Gr(e,t){const{comment:n}=t;if(!(n.enabled&&n.trigger&&e.name&&e.attributes))return!1;for(const t of e.attributes)if(t.name&&n.trigger.includes(t.name))return!0;return!1}function Qr(e,t,n){const r={},{out:o}=n;for(const t of e.attributes)t.name&&t.value&&(r[t.name.toUpperCase()]=t.value);for(const e of t)"string"==typeof e?er(o,e):r[e.name]&&(er(o,e.before),Dr(r[e.name],n),er(o,e.after))}!function(e){e[e.Start=91]="Start",e[e.End=93]="End",e[e.Underscore=95]="Underscore",e[e.Dash=45]="Dash"}(Ur||(Ur={}));const Jr=/^<([\w\-:]+)[\s>]/,Kr=new Set(["for","while","of","async","await","const","let","var","continue","break","debugger","do","export","import","in","instanceof","new","return","switch","this","throw","try","catch","typeof","void","with","yield"]);function Xr(e,t){const n=Rr(t);return n.comment=function(e){const{options:t}=e;return{enabled:t["comment.enabled"],trigger:t["comment.trigger"],before:t["comment.before"]?Fr(t["comment.before"]):void 0,after:t["comment.after"]?Fr(t["comment.after"]):void 0}}(t),Ir(e,Zr,n),n.out.value}function Zr(e,t,n,r,o){const{out:i,config:s}=r,a=to(e,t,n,r),c=function(e){const{config:t,parent:n}=e;return!n||Nr(n)||n.name&&t.options["output.formatSkip"].includes(n.name)?0:1}(r);if(i.level+=c,a&&tr(i,!0),e.name){const t=function(e,t){return cr(e,t.options["output.tagCase"])}(e.name,s);if(function(e,t){Gr(e,t)&&t.comment.before&&Qr(e,t.comment.before,t)}(e,r),er(i,`<${t}`),e.attributes)for(const t of e.attributes)Br(t)&&Yr(t,r);if(!e.selfClosing||e.children.length||e.value){if(er(i,">"),!eo(e,r,o)){if(e.value){const t=e.value.some(no)||function(e,t){if(e.length&&"string"==typeof e[0]){const n=Jr.exec(e[0]);if((null==n?void 0:n.length)&&!t.options.inlineElements.includes(n[1].toLowerCase()))return!0}return!1}(e.value,s);t&&tr(r.out,++i.level),Dr(e.value,r),t&&tr(r.out,--i.level)}if(e.children.forEach(o),!e.value&&!e.children.length){const t=s.options["output.formatLeafNode"]||s.options["output.formatForce"].includes(e.name);t&&tr(r.out,++i.level),Dr(Mr,r),t&&tr(r.out,--i.level)}}er(i,`</${t}>`),function(e,t){Gr(e,t)&&t.comment.after&&Qr(e,t.comment.after,t)}(e,r)}else er(i,`${function(e){switch(e.options["output.selfClosingStyle"]){case"xhtml":return" /";case"xml":return"/";default:return""}}(s)}>`)}else!eo(e,r,o)&&e.value&&(Dr(e.value,r),e.children.forEach(o));if(a&&t===n.length-1&&r.parent){const e=Nr(r.parent)?0:1;tr(i,i.level-e)}i.level-=c}function Yr(e,t){const{out:n,config:r}=t;if(e.name){const o=r.options["markup.attributes"],i=r.options["markup.valuePrefix"];let{name:s,value:a}=e,c=or(e,r,!0),u=or(e,r);o&&(s=ro(s,o,e.multiple)||s),s=rr(s,r),r.options["jsx.enabled"]&&e.multiple&&(c=Kn,u=Xn);const l=i?ro(e.name,i,e.multiple):null;if(l&&1===(null==a?void 0:a.length)&&"string"==typeof a[0]){const e=a[0];a=[oo(e)?`${l}.${e}`:`${l}['${e}']`],r.options["jsx.enabled"]&&(c=Kn,u=Xn)}ir(e,r)&&!a?r.options["output.compactBoolean"]||(a=[s]):a||(a=Mr),er(n," "+s),a?(er(n,"="+c),Dr(a,t),er(n,u)):"html"!==r.options["output.selfClosingStyle"]&&er(n,"="+c+u)}}function eo(e,t,n){if(e.value&&e.children.length){const r=e.value.findIndex(Lr);if(-1!==r){Dr(e.value.slice(0,r),t);const o=t.out.line;let i=r+1;return e.children.forEach(n),t.out.line!==o&&"string"==typeof e.value[i]&&er(t.out,e.value[i++].trimLeft()),Dr(e.value.slice(i),t),!0}}return!1}function to(e,t,n,r){const{config:o,parent:i}=r;if(!o.options["output.format"])return!1;if(0===t&&!i)return!1;if(i&&Nr(i)&&1===n.length)return!1;if(Nr(e)&&(Nr(n[t-1])||Nr(n[t+1])||e.value.some(no)||e.value.some(Lr)&&e.children.length))return!0;if(sr(e,o)){if(0===t){for(let e=0;e<n.length;e++)if(!sr(n[e],o))return!0}else if(!sr(n[t-1],o))return!0;if(o.options["output.inlineBreak"]){let e=1,r=t,i=t;for(;zr(n[--r],o);)e++;for(;zr(n[++i],o);)e++;if(e>=o.options["output.inlineBreak"])return!0}for(let t=0,n=e.children.length;t<n;t++)if(to(e.children[t],t,e.children,r))return!0;return!1}return!0}function no(e){return"string"==typeof e&&/\r|\n/.test(e)}function ro(e,t,n){return n&&t[`${e}*`]||t[e]}function oo(e){return!Kr.has(e)&&/^[a-zA-Z_$][\w_$]*$/.test(e)}function io(e,t,n){const r=Rr(t);return r.options=n||{},Ir(e,so,r),r.out.value}function so(e,t,n,r,o){const{out:i,options:s}=r,{primary:a,secondary:c}=function(e){const t=[],n=[];if(e.attributes)for(const r of e.attributes)ao(r)?t.push(r):n.push(r);return{primary:t,secondary:n}}(e),u=r.parent?1:0;i.level+=u,function(e,t,n,r){return!(!r.parent&&0===t)&&!Nr(e)}(e,t,0,r)&&tr(i,!0),!e.name||"div"===e.name&&a.length||er(i,(s.beforeName||"")+e.name+(s.afterName||"")),function(e,t){for(const n of e)n.value&&("class"===n.name?(er(t.out,"."),Dr(n.value.map((e=>"string"==typeof e?e.replace(/\s+/g,"."):e)),t)):(er(t.out,"#"),Dr(n.value,t)))}(a,r),function(e,t){if(e.length){const{out:n,config:r,options:o}=t;o.beforeAttribute&&er(n,o.beforeAttribute);for(let i=0;i<e.length;i++){const s=e[i];er(n,rr(s.name||"",r)),ir(s,r)&&!s.value?!r.options["output.compactBoolean"]&&o.booleanValue&&er(n,"="+o.booleanValue):(er(n,"="+or(s,r,!0)),Dr(s.value||Mr,t),er(n,or(s,r))),i!==e.length-1&&o.glueAttribute&&er(n,o.glueAttribute)}o.afterAttribute&&er(n,o.afterAttribute)}}(c.filter(Br),r),!e.selfClosing||e.value||e.children.length?(function(e,t){if(!e.value&&e.children.length)return;const n=e.value||Mr,r=function(e){const t=[];let n=[];for(const r of e)if("string"==typeof r){const e=r.split(/\r\n?|\n/g);for(n.push(e.shift()||"");e.length;)t.push(n),n=[e.shift()||""]}else n.push(r);return n.length&&t.push(n),t}(n),{out:o,options:i}=t;if(1===r.length)(e.name||e.attributes)&&Yn(o," "),Dr(n,t);else{const e=[];let n=0;for(const t of r){const r=co(t);e.push(r),r>n&&(n=r)}o.level++;for(let s=0;s<r.length;s++)tr(o,!0),i.beforeTextLine&&Yn(o,i.beforeTextLine),Dr(r[s],t),i.afterTextLine&&(Yn(o," ".repeat(n-e[s])),Yn(o,i.afterTextLine));o.level--}}(e,r),e.children.forEach(o)):r.options.selfClose&&er(i,r.options.selfClose),i.level-=u}function ao(e){return"class"===e.name||"id"===e.name}function co(e){let t=0;for(const n of e)t+="string"==typeof n?n.length:n.name.length;return t}const uo={html:Xr,haml:function(e,t){return io(e,t,{beforeName:"%",beforeAttribute:"(",afterAttribute:")",glueAttribute:" ",afterTextLine:" |",booleanValue:"true",selfClose:"/"})},slim:function(e,t){return io(e,t,{beforeAttribute:" ",glueAttribute:" ",beforeTextLine:"| ",selfClose:"/"})},pug:function(e,t){return io(e,t,{beforeAttribute:"(",afterAttribute:")",glueAttribute:", ",beforeTextLine:"| ",selfClose:"xml"===t.options["output.selfClosingStyle"]?"/":""})}};function lo(e,t){let n;if("string"==typeof e){const r=Object.assign({},t);t.options["jsx.enabled"]&&(r.jsx=!0),t.options["markup.href"]&&(r.href=!0),e=un(e,r),n=t.text,t.text=void 0}return e=function(e,t){const n=[],r=t.options["output.reverseAttributes"],{warn:o}=t,i=e=>{const s=e.name&&t.snippets[e.name];if(!s||n.includes(s))return null;let a;try{a=un(s,t)}catch(e){return null==o||o(`Unable to parse "${s}" snippet`,e),null}n.push(s),Jn(a,i),n.pop();for(const t of a.children){if(e.attributes){const n=t.attributes||[],o=e.attributes||[];t.attributes=r?o.concat(n):n.concat(o)}u=t,(c=e).selfClosing&&(u.selfClosing=!0),null!=c.value&&(u.value=c.value),c.repeat&&(u.repeat=c.repeat)}var c,u;return a};return Jn(e,i),e}(e,t),function(e,t,n){const r=[e],o=e=>{t(e,r,n),r.push(e),e.children.forEach(o),r.pop()};e.children.forEach(o)}(e,po,t),t.text=null!=n?n:t.text,e}function fo(e,t){return(uo[t.syntax]||Xr)(e,t)}function po(e,t,n){!function(e,t,n){!e.name&&e.attributes&&lr(e,t,n)}(e,t,n),function(e,t){if(!e.attributes)return;const n=[],r={};for(const o of e.attributes)if(o.name){const e=o.name;if(e in r){const n=r[e];"class"===e?n.value=qn(n.value,o.value," "):Vn(n,o,t)}else n.push(r[e]=Object.assign({},o))}else n.push(o);e.attributes=n}(e,n),function(e,t,n){let r;if(e.name&&(r=e.name.match(dr))){const o=fr[r[1]]||fr.latin,i=r[2]?Math.max(1,Number(r[2])):30,s=pr(i,r[3]?Math.max(i,Number(r[3].slice(1))):i),a=e.repeat||function(e){for(let t=e.length-1;t>=0;t--){const n=e[t];if("AbbreviationNode"===n.type&&n.repeat)return n.repeat}}(t);e.name=e.attributes=void 0,e.value=[br(o,s,!a||0===a.value)],e.repeat&&t.length>1&&lr(e,t,n)}}(e,t,n),"xsl"===n.syntax&&function(e){var t;"xsl:variable"!==(t=e.name)&&"xsl:with-param"!==t||!e.attributes||!e.children.length&&!e.value||(e.attributes=e.attributes.filter(vr))}(e),"markup"===n.type&&Pr(e),n.options["bem.enabled"]&&function(e,t,n){!function(e){const t=Cr(e),n=[];for(const e of t.classNames){const t=e.indexOf("_");t>0&&!e.startsWith("-")?(n.push(e.slice(0,t)),n.push(e.slice(t))):n.push(e)}n.length&&(t.classNames=n.filter(Er),t.block=_r(t.classNames),Ar(e,t.classNames.join(" ")))}(e),function(e,t,n){const r=Cr(e),o=[],{options:i}=n,s=t.slice(1).concat(e);for(let e of r.classNames){let t,r="";const a=e;(t=e.match(yr))&&(r=Sr(s,t[1].length,n.context)+i["bem.element"]+t[2],o.push(r),e=e.slice(t[0].length)),(t=e.match(xr))&&(r||(r=Sr(s,t[1].length),o.push(r)),o.push(`${r}${i["bem.modifier"]}${t[2]}`),e=e.slice(t[0].length)),e===a&&o.push(a)}const a=o.filter(Er);a.length&&Ar(e,a.join(" "))}(e,t,n)}(e,t,n)}var ho;!function(e){e.Raw="Raw",e.Property="Property"}(ho||(ho={}));const mo=/^([a-z-]+)(?:\s*:\s*([^\n\r;]+?);*)?$/,go={value:!0};function bo(e,t){const n=t.match(mo);if(n){const t={},r=n[2]?n[2].split("|").map(yo):[];for(const e of r)for(const n of e)wo(n,t);return{type:ho.Property,key:e,property:n[1],value:r,keywords:t,dependencies:[]}}return{type:ho.Raw,key:e,value:t}}function vo(e,t){return e.key===t.key?0:e.key<t.key?-1:1}function yo(e){return Fn(e.trim(),go)[0].value}function xo(e){return e.type===ho.Property}function wo(e,t){for(const n of e.value)if("Literal"===n.type)t[n.value]=n;else if("FunctionCall"===n.type)t[n.name]=n;else if("Field"===n.type){const e=n.name.trim();e&&(t[e]={type:"Literal",value:e})}}function ko(e,t,n=!1){if((e=e.toLowerCase())===(t=t.toLowerCase()))return 1;if(!e||!t||e.charCodeAt(0)!==t.charCodeAt(0))return 0;const r=e.length,o=t.length;if(!n&&r>o)return 0;const i=Math.min(r,o),s=Math.max(r,o);let a=1,c=1,u=s,l=0,f=0,d=!1,p=!1;for(;a<r;){for(l=e.charCodeAt(a),d=!1,p=!1;c<o;){if(f=t.charCodeAt(c),l===f){d=!0,u+=s-(p?a:c);break}p=45===f,c++}if(!d){if(!n)return 0;break}a++}const h=s-i;return u*(a/s)/(Co(s)-Co(h))}function Co(e){return e*(e+1)/2}function To(e,t){return e.r||e.g||e.b||e.a?1===e.a?function(e,t){const n=t&&_o(e.r)&&_o(e.g)&&_o(e.b)?Oo:Ao;return"#"+n(e.r)+n(e.g)+n(e.b)}(e,t):function(e){const t=[e.r,e.g,e.b];return 1!==e.a&&t.push(So(e.a,8)),`${3===t.length?"rgb":"rgba"}(${t.join(", ")})`}(e):"transparent"}function So(e,t=4){return e.toFixed(t).replace(/\.?0+$/,"")}function _o(e){return!(e%17)}function Oo(e){return(e>>4).toString(16)}function Ao(e){return function(e,t){for(;e.length<2;)e="0"+e;return e}(e.toString(16))}const jo={Global:"@@global",Section:"@@section",Property:"@@property",Value:"@@value"};function Eo(e,t){var n;const r=Zn(t.options),o=t.options["output.format"];(null===(n=t.context)||void 0===n?void 0:n.name)===jo.Section&&(e=e.filter((e=>e.snippet)));for(let n=0;n<e.length;n++)o&&0!==n&&tr(r,!0),Po(e[n],r,t);return r.value}function Po(e,t,n){const r=n.options["stylesheet.json"];if(e.name)er(t,(r?e.name.replace(/\-(\w)/g,((e,t)=>t.toUpperCase())):e.name)+n.options["stylesheet.between"]),e.value.length?function(e,t,n){const r=n.options["stylesheet.json"],o=r?function(e){if(1===e.value.length){const t=e.value[0];if(1===t.value.length&&"NumberValue"===t.value[0].type)return t.value[0]}}(e):null;if(!o||o.unit&&"px"!==o.unit){const o=function(e){return e.options["stylesheet.jsonDoubleQuotes"]?'"':"'"}(n);r&&Yn(t,o);for(let r=0;r<e.value.length;r++)0!==r&&Yn(t,", "),Io(e.value[r],t,n);r&&Yn(t,o)}else Yn(t,String(o.value))}(e,t,n):nr(t,0,""),r?Yn(t,","):($o(e,t,!0),Yn(t,n.options["stylesheet.after"]));else{for(const r of e.value)for(const e of r.value)Ro(e,t,n);$o(e,t,e.value.length>0)}}function $o(e,t,n){e.important&&(n&&Yn(t," "),Yn(t,"!important"))}function Io(e,t,n){for(let r=0,o=-1;r<e.value.length;r++){const i=e.value[r];0===r||"Field"===i.type&&i.start===o||Yn(t," "),Ro(i,t,n),o=i.end}}function Ro(e,t,n){if("ColorValue"===e.type)Yn(t,To(e,n.options["stylesheet.shortHex"]));else if("Literal"===e.type||"CustomProperty"===e.type)er(t,e.value);else if("NumberValue"===e.type)er(t,So(e.value,4)+e.unit);else if("StringValue"===e.type){const n="double"===e.quote?'"':"'";er(t,n+e.value+n)}else if("Field"===e.type)nr(t,e.index,e.name);else if("FunctionCall"===e.type){Yn(t,e.name+"(");for(let r=0;r<e.arguments.length;r++)r&&Yn(t,", "),Io(e.arguments[r],t,n);Yn(t,")")}}const Mo="lg";function No(e,t){var n;const r=(null===(n=t.cache)||void 0===n?void 0:n.stylesheetSnippets)||function(e){const t=[];for(const n of Object.keys(e))t.push(bo(n,e[n]));return function(e){e=e.slice().sort(vo);const t=[];let n;for(const r of e.filter(xo)){for(;t.length;){if(n=t[t.length-1],r.property.startsWith(n.property)&&45===r.property.charCodeAt(n.property.length)){n.dependencies.push(r),t.push(r);break}t.pop()}t.length||t.push(r)}return e}(t)}(t.snippets),o=[];t.cache&&(t.cache.stylesheetSnippets=r),"string"==typeof e&&(e=Fn(e,{value:Go(t)}));const i=function(e,t){if(t.context){if(t.context.name===jo.Section)return e.filter((e=>e.type===ho.Raw));if(t.context.name===jo.Property)return e.filter((e=>e.type===ho.Property))}return e}(r,t);for(const n of e){const e=zo(n,i,t);e&&o.push(e)}return o}function zo(e,t,n){if(!function(e,t){let n=null;const r=1===e.value.length?e.value[0]:null;if(r&&1===r.value.length){const e=r.value[0];"FunctionCall"===e.type&&e.name===Mo&&(n=e)}return!(!n&&e.name!==Mo)&&(n=n?Object.assign(Object.assign({},n),{name:"linear-gradient"}):{type:"FunctionCall",name:"linear-gradient",arguments:[Fo(Vo(0,""))]},t.context||(e.name="background-image"),e.value=[Fo(n)],!0)}(e,n)){const r=n.options["stylesheet.fuzzySearchMinScore"];if(Go(n)){const o=n.context.name,i=t.find((e=>e.type===ho.Property&&e.property===o));Lo(e,n,i,r),e.snippet=i}else if(e.name){const o=Do(e.name,t,r,!0);if(e.snippet=o,o){const t=o.type===ho.Property?function(e,t,n){const r=function(e,t){for(let n=0,r=0;n<e.length;n++){if(r=t.indexOf(e[n],r),-1===r)return e.slice(n);r++}return""}(e.name,t.key);if(r){if(e.value.length)return null;const o=Uo(r,n,t);if(!o)return null;e.value.push(Fo(o))}if(e.name=t.property,e.value.length)Lo(e,n,t);else if(t.value.length){const r=t.value[0];e.value=1===t.value.length||r.some(Wo)?r:r.map((e=>Ho(e,n)))}return e}(e,o,n):function(e,t){let n,r=0;const o=/\$\{(\d+)(:[^}]+)?\}/g,i=e.value[0],s=[];for(;n=o.exec(t.value);)r!==n.index&&s.push(qo(t.value.slice(r,n.index))),r=n.index+n[0].length,i&&i.value.length?s.push(i.value.shift()):s.push(Vo(Number(n[1]),n[2]?n[2].slice(1):""));const a=t.value.slice(r);return a&&s.push(qo(a)),e.name=void 0,e.value=[Fo(...s)],e}(e,o);if(t)e=t;else if(n.options["stylesheet.strictMatch"])return null}}}return(e.name||n.context)&&function(e,t){const n=t.options["stylesheet.unitAliases"],r=t.options["stylesheet.unitless"];for(const o of e.value)for(const i of o.value)"NumberValue"===i.type&&(i.unit?i.unit=n[i.unit]||i.unit:0===i.value||r.includes(e.name)||(i.unit=i.rawValue.includes(".")?t.options["stylesheet.floatUnit"]:t.options["stylesheet.intUnit"]))}(e,n),e}function Lo(e,t,n,r){for(const o of e.value){const e=[];for(const i of o.value)if("Literal"===i.type)e.push(Uo(i.value,t,n,r)||i);else if("FunctionCall"===i.type){const o=Uo(i.name,t,n,r);o&&"FunctionCall"===o.type?e.push(Object.assign(Object.assign({},o),{arguments:i.arguments.concat(o.arguments.slice(i.arguments.length))})):e.push(i)}else e.push(i);o.value=e}}function Do(e,t,n=0,r=!1){let o=null,i=0;for(const n of t){const t=ko(e,Bo(n),r);if(1===t)return n;t&&t>=i&&(i=t,o=n)}return i>=n?o:null}function Bo(e){return"string"==typeof e?e:e.key}function Uo(e,t,n,r){let o;if(n){if(o=Do(e,Object.keys(n.keywords),r))return n.keywords[o];for(const t of n.dependencies)if(o=Do(e,Object.keys(t.keywords),r))return t.keywords[o]}return(o=Do(e,t.options["stylesheet.keywords"],r))?qo(o):null}function Fo(...e){return{type:"CSSValue",value:e}}function qo(e){return{type:"Literal",value:e}}function Vo(e,t){return{type:"Field",index:e,name:t}}function Wo(e){for(const t of e.value)if("Field"===t.type||"FunctionCall"===t.type&&t.arguments.some(Wo))return!0;return!1}function Ho(e,t,n={index:1}){let r=[];for(const o of e.value)switch(o.type){case"ColorValue":r.push(Vo(n.index++,To(o,t.options["stylesheet.shortHex"])));break;case"Literal":r.push(Vo(n.index++,o.value));break;case"NumberValue":r.push(Vo(n.index++,`${o.value}${o.unit}`));break;case"StringValue":const e="single"===o.quote?"'":'"';r.push(Vo(n.index++,e+o.value+e));break;case"FunctionCall":r.push(Vo(n.index++,o.name),qo("("));for(let e=0,i=o.arguments.length;e<i;e++)r=r.concat(Ho(o.arguments[e],t,n).value),e!==i-1&&r.push(qo(", "));r.push(qo(")"));break;default:r.push(o)}return Object.assign(Object.assign({},e),{value:r})}function Go(e){return!(!e.context||e.context.name!==jo.Value&&e.context.name.startsWith("@@"))}const Qo={markup:"html",stylesheet:"css"},Jo={type:"markup",syntax:"html",variables:{lang:"en",locale:"en-US",charset:"UTF-8",indentation:"\t",newline:"\n"},snippets:{},options:{inlineElements:["a","abbr","acronym","applet","b","basefont","bdo","big","br","button","cite","code","del","dfn","em","font","i","iframe","img","input","ins","kbd","label","map","object","q","s","samp","select","small","span","strike","strong","sub","sup","textarea","tt","u","var"],"output.indent":"\t","output.baseIndent":"","output.newline":"\n","output.tagCase":"","output.attributeCase":"","output.attributeQuotes":"double","output.format":!0,"output.formatLeafNode":!1,"output.formatSkip":["html"],"output.formatForce":["body"],"output.inlineBreak":3,"output.compactBoolean":!1,"output.booleanAttributes":["contenteditable","seamless","async","autofocus","autoplay","checked","controls","defer","disabled","formnovalidate","hidden","ismap","loop","multiple","muted","novalidate","readonly","required","reversed","selected","typemustmatch"],"output.reverseAttributes":!1,"output.selfClosingStyle":"html","output.field":(e,t)=>t,"output.text":e=>e,"markup.href":!0,"comment.enabled":!1,"comment.trigger":["id","class"],"comment.before":"","comment.after":"\n\x3c!-- /[#ID][.CLASS] --\x3e","bem.enabled":!1,"bem.element":"__","bem.modifier":"_","jsx.enabled":!1,"stylesheet.keywords":["auto","inherit","unset","none"],"stylesheet.unitless":["z-index","line-height","opacity","font-weight","zoom","flex","flex-grow","flex-shrink"],"stylesheet.shortHex":!0,"stylesheet.between":": ","stylesheet.after":";","stylesheet.intUnit":"px","stylesheet.floatUnit":"em","stylesheet.unitAliases":{e:"em",p:"%",x:"ex",r:"rem"},"stylesheet.json":!1,"stylesheet.jsonDoubleQuotes":!1,"stylesheet.fuzzySearchMinScore":0,"stylesheet.strictMatch":!1}},Ko={markup:{snippets:Xo({a:"a[href]","a:blank":"a[href='http://${0}' target='_blank' rel='noopener noreferrer']","a:link":"a[href='http://${0}']","a:mail":"a[href='mailto:${0}']","a:tel":"a[href='tel:+${0}']",abbr:"abbr[title]","acr|acronym":"acronym[title]",base:"base[href]/",basefont:"basefont/",br:"br/",frame:"frame/",hr:"hr/",bdo:"bdo[dir]","bdo:r":"bdo[dir=rtl]","bdo:l":"bdo[dir=ltr]",col:"col/",link:"link[rel=stylesheet href]/","link:css":"link[href='${1:style}.css']","link:print":"link[href='${1:print}.css' media=print]","link:favicon":"link[rel='shortcut icon' type=image/x-icon href='${1:favicon.ico}']","link:mf|link:manifest":"link[rel='manifest' href='${1:manifest.json}']","link:touch":"link[rel=apple-touch-icon href='${1:favicon.png}']","link:rss":"link[rel=alternate type=application/rss+xml title=RSS href='${1:rss.xml}']","link:atom":"link[rel=alternate type=application/atom+xml title=Atom href='${1:atom.xml}']","link:im|link:import":"link[rel=import href='${1:component}.html']",meta:"meta/","meta:utf":"meta[http-equiv=Content-Type content='text/html;charset=UTF-8']","meta:vp":"meta[name=viewport content='width=${1:device-width}, initial-scale=${2:1.0}']","meta:compat":"meta[http-equiv=X-UA-Compatible content='${1:IE=7}']","meta:edge":"meta:compat[content='${1:ie=edge}']","meta:redirect":"meta[http-equiv=refresh content='0; url=${1:http://example.com}']","meta:refresh":"meta[http-equiv=refresh content='${1:5}']","meta:kw":"meta[name=keywords content]","meta:desc":"meta[name=description content]",style:"style",script:"script","script:src":"script[src]","script:module":"script[type=module src]",img:"img[src alt]/","img:s|img:srcset":"img[srcset src alt]","img:z|img:sizes":"img[sizes srcset src alt]",picture:"picture","src|source":"source/","src:sc|source:src":"source[src type]","src:s|source:srcset":"source[srcset]","src:t|source:type":"source[srcset type='${1:image/}']","src:z|source:sizes":"source[sizes srcset]","src:m|source:media":"source[media='(${1:min-width: })' srcset]","src:mt|source:media:type":"source:media[type='${2:image/}']","src:mz|source:media:sizes":"source:media[sizes srcset]","src:zt|source:sizes:type":"source[sizes srcset type='${1:image/}']",iframe:"iframe[src frameborder=0]",embed:"embed[src type]/",object:"object[data type]",param:"param[name value]/",map:"map[name]",area:"area[shape coords href alt]/","area:d":"area[shape=default]","area:c":"area[shape=circle]","area:r":"area[shape=rect]","area:p":"area[shape=poly]",form:"form[action]","form:get":"form[method=get]","form:post":"form[method=post]",label:"label[for]",input:"input[type=${1:text}]/",inp:"input[name=${1} id=${1}]","input:h|input:hidden":"input[type=hidden name]","input:t|input:text":"inp[type=text]","input:search":"inp[type=search]","input:email":"inp[type=email]","input:url":"inp[type=url]","input:p|input:password":"inp[type=password]","input:datetime":"inp[type=datetime]","input:date":"inp[type=date]","input:datetime-local":"inp[type=datetime-local]","input:month":"inp[type=month]","input:week":"inp[type=week]","input:time":"inp[type=time]","input:tel":"inp[type=tel]","input:number":"inp[type=number]","input:color":"inp[type=color]","input:c|input:checkbox":"inp[type=checkbox]","input:r|input:radio":"inp[type=radio]","input:range":"inp[type=range]","input:f|input:file":"inp[type=file]","input:s|input:submit":"input[type=submit value]","input:i|input:image":"input[type=image src alt]","input:b|input:btn|input:button":"input[type=button value]","input:reset":"input:button[type=reset]",isindex:"isindex/",select:"select[name=${1} id=${1}]","select:d|select:disabled":"select[disabled.]","opt|option":"option[value]",textarea:"textarea[name=${1} id=${1}]","tarea:c|textarea:cols":"textarea[name=${1} id=${1} cols=${2:30}]","tarea:r|textarea:rows":"textarea[name=${1} id=${1} rows=${3:10}]","tarea:cr|textarea:cols:rows":"textarea[name=${1} id=${1} cols=${2:30} rows=${3:10}]",marquee:"marquee[behavior direction]","menu:c|menu:context":"menu[type=context]","menu:t|menu:toolbar":"menu[type=toolbar]",video:"video[src]",audio:"audio[src]","html:xml":"html[xmlns=http://www.w3.org/1999/xhtml]",keygen:"keygen/",command:"command/","btn:s|button:s|button:submit":"button[type=submit]","btn:r|button:r|button:reset":"button[type=reset]","btn:b|button:b|button:button":"button[type=button]","btn:d|button:d|button:disabled":"button[disabled.]","fst:d|fset:d|fieldset:d|fieldset:disabled":"fieldset[disabled.]",bq:"blockquote",fig:"figure",figc:"figcaption",pic:"picture",ifr:"iframe",emb:"embed",obj:"object",cap:"caption",colg:"colgroup",fst:"fieldset",btn:"button",optg:"optgroup",tarea:"textarea",leg:"legend",sect:"section",art:"article",hdr:"header",ftr:"footer",adr:"address",dlg:"dialog",str:"strong",prog:"progress",mn:"main",tem:"template",fset:"fieldset",datal:"datalist",kg:"keygen",out:"output",det:"details",sum:"summary",cmd:"command",data:"data[value]",meter:"meter[value]",time:"time[datetime]","ri:d|ri:dpr":"img:s","ri:v|ri:viewport":"img:z","ri:a|ri:art":"pic>src:m+img","ri:t|ri:type":"pic>src:t+img","!!!":"{<!DOCTYPE html>}",doc:"html[lang=${lang}]>(head>meta[charset=${charset}]+meta:vp+title{${1:Document}})+body","!|html:5":"!!!+doc",c:"{\x3c!-- ${0} --\x3e}","cc:ie":"{\x3c!--[if IE]>${0}<![endif]--\x3e}","cc:noie":"{\x3c!--[if !IE]>\x3c!--\x3e${0}\x3c!--<![endif]--\x3e}"})},xhtml:{options:{"output.selfClosingStyle":"xhtml"}},xml:{options:{"output.selfClosingStyle":"xml"}},xsl:{snippets:Xo({"tm|tmatch":"xsl:template[match mode]","tn|tname":"xsl:template[name]",call:"xsl:call-template[name]",ap:"xsl:apply-templates[select mode]",api:"xsl:apply-imports",imp:"xsl:import[href]",inc:"xsl:include[href]",ch:"xsl:choose","wh|xsl:when":"xsl:when[test]",ot:"xsl:otherwise",if:"xsl:if[test]",par:"xsl:param[name]",pare:"xsl:param[name select]",var:"xsl:variable[name]",vare:"xsl:variable[name select]",wp:"xsl:with-param[name select]",key:"xsl:key[name match use]",elem:"xsl:element[name]",attr:"xsl:attribute[name]",attrs:"xsl:attribute-set[name]",cp:"xsl:copy[select]",co:"xsl:copy-of[select]",val:"xsl:value-of[select]","for|each":"xsl:for-each[select]",tex:"xsl:text",com:"xsl:comment",msg:"xsl:message[terminate=no]",fall:"xsl:fallback",num:"xsl:number[value]",nam:"namespace-alias[stylesheet-prefix result-prefix]",pres:"xsl:preserve-space[elements]",strip:"xsl:strip-space[elements]",proc:"xsl:processing-instruction[name]",sort:"xsl:sort[select order]",choose:"xsl:choose>xsl:when+xsl:otherwise",xsl:"!!!+xsl:stylesheet[version=1.0 xmlns:xsl=http://www.w3.org/1999/XSL/Transform]>{\n|}","!!!":'{<?xml version="1.0" encoding="UTF-8"?>}'}),options:{"output.selfClosingStyle":"xml"}},jsx:{options:{"jsx.enabled":!0,"markup.attributes":{class:"className","class*":"styleName",for:"htmlFor"},"markup.valuePrefix":{"class*":"styles"}}},vue:{options:{"markup.attributes":{"class*":":class"}}},svelte:{options:{"jsx.enabled":!0}},pug:{snippets:Xo({"!!!":"{doctype html}"})},stylesheet:{snippets:Xo({"@f":"@font-face {\n\tfont-family: ${1};\n\tsrc: url(${2});\n}","@ff":"@font-face {\n\tfont-family: '${1:FontName}';\n\tsrc: url('${2:FileName}.eot');\n\tsrc: url('${2:FileName}.eot?#iefix') format('embedded-opentype'),\n\t\t url('${2:FileName}.woff') format('woff'),\n\t\t url('${2:FileName}.ttf') format('truetype'),\n\t\t url('${2:FileName}.svg#${1:FontName}') format('svg');\n\tfont-style: ${3:normal};\n\tfont-weight: ${4:normal};\n}","@i|@import":"@import url(${0});","@kf":"@keyframes ${1:identifier} {\n\t${2}\n}","@m|@media":"@media ${1:screen} {\n\t${0}\n}",ac:"align-content:start|end|flex-start|flex-end|center|space-between|space-around|stretch|space-evenly",ai:"align-items:start|end|flex-start|flex-end|center|baseline|stretch",anim:"animation:${1:name} ${2:duration} ${3:timing-function} ${4:delay} ${5:iteration-count} ${6:direction} ${7:fill-mode}",animdel:"animation-delay:time",animdir:"animation-direction:normal|reverse|alternate|alternate-reverse",animdur:"animation-duration:${1:0}s",animfm:"animation-fill-mode:both|forwards|backwards",animic:"animation-iteration-count:1|infinite",animn:"animation-name",animps:"animation-play-state:running|paused",animtf:"animation-timing-function:linear|ease|ease-in|ease-out|ease-in-out|cubic-bezier(${1:0.1}, ${2:0.7}, ${3:1.0}, ${3:0.1})",ap:"appearance:none",as:"align-self:start|end|auto|flex-start|flex-end|center|baseline|stretch",b:"bottom",bd:"border:${1:1px} ${2:solid} ${3:#000}",bdb:"border-bottom:${1:1px} ${2:solid} ${3:#000}",bdbc:"border-bottom-color:${1:#000}",bdbi:"border-bottom-image:url(${0})",bdbk:"border-break:close",bdbli:"border-bottom-left-image:url(${0})|continue",bdblrs:"border-bottom-left-radius",bdbri:"border-bottom-right-image:url(${0})|continue",bdbrrs:"border-bottom-right-radius",bdbs:"border-bottom-style",bdbw:"border-bottom-width",bdc:"border-color:${1:#000}",bdci:"border-corner-image:url(${0})|continue",bdcl:"border-collapse:collapse|separate",bdf:"border-fit:repeat|clip|scale|stretch|overwrite|overflow|space",bdi:"border-image:url(${0})",bdl:"border-left:${1:1px} ${2:solid} ${3:#000}",bdlc:"border-left-color:${1:#000}",bdlen:"border-length",bdli:"border-left-image:url(${0})",bdls:"border-left-style",bdlw:"border-left-width",bdr:"border-right:${1:1px} ${2:solid} ${3:#000}",bdrc:"border-right-color:${1:#000}",bdri:"border-right-image:url(${0})",bdrs:"border-radius",bdrst:"border-right-style",bdrw:"border-right-width",bds:"border-style:none|hidden|dotted|dashed|solid|double|dot-dash|dot-dot-dash|wave|groove|ridge|inset|outset",bdsp:"border-spacing",bdt:"border-top:${1:1px} ${2:solid} ${3:#000}",bdtc:"border-top-color:${1:#000}",bdti:"border-top-image:url(${0})",bdtli:"border-top-left-image:url(${0})|continue",bdtlrs:"border-top-left-radius",bdtri:"border-top-right-image:url(${0})|continue",bdtrrs:"border-top-right-radius",bdts:"border-top-style",bdtw:"border-top-width",bdw:"border-width",bbs:"border-block-start",bbe:"border-block-end",bis:"border-inline-start",bie:"border-inline-end",bfv:"backface-visibility:hidden|visible",bg:"background:${1:#000}","bg:n":"background: none",bga:"background-attachment:fixed|scroll",bgbk:"background-break:bounding-box|each-box|continuous",bgc:"background-color:${1:#fff}",bgcp:"background-clip:padding-box|border-box|content-box|no-clip",bgi:"background-image:url(${0})",bgo:"background-origin:padding-box|border-box|content-box",bgp:"background-position:${1:0} ${2:0}",bgpx:"background-position-x",bgpy:"background-position-y",bgr:"background-repeat:no-repeat|repeat-x|repeat-y|space|round",bgsz:"background-size:contain|cover",bs:"block-size",bxsh:"box-shadow:${1:inset }${2:hoff} ${3:voff} ${4:blur} ${5:#000}|none",bxsz:"box-sizing:border-box|content-box|border-box",c:"color:${1:#000}",cg:"column-gap",cr:"color:rgb(${1:0}, ${2:0}, ${3:0})",cra:"color:rgba(${1:0}, ${2:0}, ${3:0}, ${4:.5})",cl:"clear:both|left|right|none",cm:"/* ${0} */",cnt:"content:'${0}'|normal|open-quote|no-open-quote|close-quote|no-close-quote|attr(${0})|counter(${0})|counters(${0})",coi:"counter-increment",colm:"columns",colmc:"column-count",colmf:"column-fill",colmg:"column-gap",colmr:"column-rule",colmrc:"column-rule-color",colmrs:"column-rule-style",colmrw:"column-rule-width",colms:"column-span",colmw:"column-width",cor:"counter-reset",cp:"clip:auto|rect(${1:top} ${2:right} ${3:bottom} ${4:left})",cps:"caption-side:top|bottom",cur:"cursor:pointer|auto|default|crosshair|hand|help|move|pointer|text",d:"display:block|none|flex|inline-flex|inline|inline-block|grid|inline-grid|subgrid|list-item|run-in|contents|table|inline-table|table-caption|table-column|table-column-group|table-header-group|table-footer-group|table-row|table-row-group|table-cell|ruby|ruby-base|ruby-base-group|ruby-text|ruby-text-group",ec:"empty-cells:show|hide",f:"font:${1:1em} ${2:sans-serif}",fd:"font-display:auto|block|swap|fallback|optional",fef:"font-effect:none|engrave|emboss|outline",fem:"font-emphasize",femp:"font-emphasize-position:before|after",fems:"font-emphasize-style:none|accent|dot|circle|disc",ff:"font-family:serif|sans-serif|cursive|fantasy|monospace",fft:'font-family:"Times New Roman", Times, Baskerville, Georgia, serif',ffa:'font-family:Arial, "Helvetica Neue", Helvetica, sans-serif',ffv:"font-family:Verdana, Geneva, sans-serif",fl:"float:left|right|none",fs:"font-style:italic|normal|oblique",fsm:"font-smoothing:antialiased|subpixel-antialiased|none",fst:"font-stretch:normal|ultra-condensed|extra-condensed|condensed|semi-condensed|semi-expanded|expanded|extra-expanded|ultra-expanded",fv:"font-variant:normal|small-caps",fvs:"font-variation-settings:normal|inherit|initial|unset",fw:"font-weight:normal|bold|bolder|lighter",fx:"flex",fxb:"flex-basis:fill|max-content|min-content|fit-content|content",fxd:"flex-direction:row|row-reverse|column|column-reverse",fxf:"flex-flow",fxg:"flex-grow",fxsh:"flex-shrink",fxw:"flex-wrap:nowrap|wrap|wrap-reverse",fsz:"font-size",fsza:"font-size-adjust",g:"gap",gtc:"grid-template-columns:repeat(${0})|minmax()",gtr:"grid-template-rows:repeat(${0})|minmax()",gta:"grid-template-areas",gt:"grid-template",gg:"grid-gap",gcg:"grid-column-gap",grg:"grid-row-gap",gac:"grid-auto-columns:auto|minmax()",gar:"grid-auto-rows:auto|minmax()",gaf:"grid-auto-flow:row|column|dense|inherit|initial|unset",gd:"grid",gc:"grid-column",gcs:"grid-column-start",gce:"grid-column-end",gr:"grid-row",grs:"grid-row-start",gre:"grid-row-end",ga:"grid-area",h:"height",is:"inline-size",jc:"justify-content:start|end|stretch|flex-start|flex-end|center|space-between|space-around|space-evenly",ji:"justify-items:start|end|center|stretch",js:"justify-self:start|end|center|stretch",l:"left",lg:"background-image:linear-gradient(${1})",lh:"line-height",lis:"list-style",lisi:"list-style-image",lisp:"list-style-position:inside|outside",list:"list-style-type:disc|circle|square|decimal|decimal-leading-zero|lower-roman|upper-roman",lts:"letter-spacing:normal",m:"margin",mah:"max-height",mar:"max-resolution",maw:"max-width",mb:"margin-bottom",mih:"min-height",mir:"min-resolution",miw:"min-width",ml:"margin-left",mr:"margin-right",mt:"margin-top",mbs:"margin-block-start",mbe:"margin-block-end",mis:"margin-inline-start",mie:"margin-inline-end",ol:"outline",olc:"outline-color:${1:#000}|invert",olo:"outline-offset",ols:"outline-style:none|dotted|dashed|solid|double|groove|ridge|inset|outset",olw:"outline-width:thin|medium|thick","op|opa":"opacity",ord:"order",ori:"orientation:landscape|portrait",orp:"orphans",ov:"overflow:hidden|visible|hidden|scroll|auto",ovs:"overflow-style:scrollbar|auto|scrollbar|panner|move|marquee",ovx:"overflow-x:hidden|visible|hidden|scroll|auto",ovy:"overflow-y:hidden|visible|hidden|scroll|auto",p:"padding",pb:"padding-bottom",pgba:"page-break-after:auto|always|left|right",pgbb:"page-break-before:auto|always|left|right",pgbi:"page-break-inside:auto|avoid",pl:"padding-left",pos:"position:relative|absolute|relative|fixed|static",pr:"padding-right",pt:"padding-top",pbs:"padding-block-start",pbe:"padding-block-end",pis:"padding-inline-start",pie:"padding-inline-end",spbs:"scroll-padding-block-start",spbe:"scroll-padding-block-end",spis:"scroll-padding-inline-start",spie:"scroll-padding-inline-end",q:"quotes",qen:"quotes:'\\201C' '\\201D' '\\2018' '\\2019'",qru:"quotes:'\\00AB' '\\00BB' '\\201E' '\\201C'",r:"right",rg:"row-gap",rsz:"resize:none|both|horizontal|vertical",t:"top",ta:"text-align:left|center|right|justify",tal:"text-align-last:left|center|right",tbl:"table-layout:fixed",td:"text-decoration:none|underline|overline|line-through",te:"text-emphasis:none|accent|dot|circle|disc|before|after",th:"text-height:auto|font-size|text-size|max-size",ti:"text-indent",tj:"text-justify:auto|inter-word|inter-ideograph|inter-cluster|distribute|kashida|tibetan",to:"text-outline:${1:0} ${2:0} ${3:#000}",tov:"text-overflow:ellipsis|clip",tr:"text-replace",trf:"transform:${1}|skewX(${1:angle})|skewY(${1:angle})|scale(${1:x}, ${2:y})|scaleX(${1:x})|scaleY(${1:y})|scaleZ(${1:z})|scale3d(${1:x}, ${2:y}, ${3:z})|rotate(${1:angle})|rotateX(${1:angle})|rotateY(${1:angle})|rotateZ(${1:angle})|translate(${1:x}, ${2:y})|translateX(${1:x})|translateY(${1:y})|translateZ(${1:z})|translate3d(${1:tx}, ${2:ty}, ${3:tz})",trfo:"transform-origin",trfs:"transform-style:preserve-3d",trs:"transition:${1:prop} ${2:time}",trsde:"transition-delay:${1:time}",trsdu:"transition-duration:${1:time}",trsp:"transition-property:${1:prop}",trstf:"transition-timing-function:${1:fn}",tsh:"text-shadow:${1:hoff} ${2:voff} ${3:blur} ${4:#000}",tt:"text-transform:uppercase|lowercase|capitalize|none",tw:"text-wrap:none|normal|unrestricted|suppress",us:"user-select:none",v:"visibility:hidden|visible|collapse",va:"vertical-align:top|super|text-top|middle|baseline|bottom|text-bottom|sub","w|wid":"width",whs:"white-space:nowrap|pre|pre-wrap|pre-line|normal",whsc:"white-space-collapse:normal|keep-all|loose|break-strict|break-all",wido:"widows",wm:"writing-mode:lr-tb|lr-tb|lr-bt|rl-tb|rl-bt|tb-rl|tb-lr|bt-lr|bt-rl",wob:"word-break:normal|keep-all|break-all",wos:"word-spacing",wow:"word-wrap:none|unrestricted|suppress|break-word|normal",z:"z-index",zom:"zoom:1"})},sass:{options:{"stylesheet.after":""}},stylus:{options:{"stylesheet.between":" ","stylesheet.after":""}}};function Xo(e){const t={};return Object.keys(e).forEach((n=>{for(const r of n.split("|"))t[r]=e[n]})),t}function Zo(e={},t={}){const n=e.type||"markup",r=e.syntax||Qo[n];return Object.assign(Object.assign(Object.assign({},Jo),e),{type:n,syntax:r,variables:Yo(n,r,"variables",e,t),snippets:Yo(n,r,"snippets",e,t),options:Yo(n,r,"options",e,t)})}function Yo(e,t,n,r,o={}){const i=Ko[e],s=o[e],a=Ko[t],c=o[t];return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},Jo[n]),i&&i[n]),a&&a[n]),s&&s[n]),c&&c[n]),r[n])}function ei(e,t=0){return{text:e,start:t,pos:e.length}}function ti(e){return e.pos===e.start}function ni(e,t=0){return e.text.charCodeAt(e.pos-1+t)}function ri(e){if(!ti(e))return e.text.charCodeAt(--e.pos)}function oi(e,t){if(ti(e))return!1;const n="function"==typeof t?t(ni(e)):t===ni(e);return n&&e.pos--,!!n}function ii(e,t){const n=e.pos;for(;oi(e,t););return e.pos<n}var si,ai;function ci(e){return e===si.SingleQuote||e===si.DoubleQuote}!function(e){e[e.SingleQuote=39]="SingleQuote",e[e.DoubleQuote=34]="DoubleQuote",e[e.Escape=92]="Escape"}(si||(si={})),function(e){e[e.SquareL=91]="SquareL",e[e.SquareR=93]="SquareR",e[e.RoundL=40]="RoundL",e[e.RoundR=41]="RoundR",e[e.CurlyL=123]="CurlyL",e[e.CurlyR=125]="CurlyR"}(ai||(ai={}));const ui={[ai.SquareL]:ai.SquareR,[ai.RoundL]:ai.RoundR,[ai.CurlyL]:ai.CurlyR};var li;function fi(e){const t=e.pos;if(!oi(e,li.AngleRight))return!1;let n=!1;for(oi(e,li.Slash);!ti(e);){if(ii(e,gi),hi(e)){if(oi(e,li.Slash)){n=oi(e,li.AngleLeft);break}if(oi(e,li.AngleLeft)){n=!0;break}if(oi(e,gi))continue;if(oi(e,li.Equals)){if(hi(e))continue;break}if(pi(e)){n=!0;break}break}if(!di(e))break}return e.pos=t,n}function di(e){return function(e){const t=e.pos;return!!(function(e){const t=e.pos,n=ri(e);if(ci(n))for(;!ti(e);)if(ri(e)===n&&ni(e)!==si.Escape)return!0;return e.pos=t,!1}(e)&&oi(e,li.Equals)&&hi(e))||(e.pos=t,!1)}(e)||pi(e)}function pi(e){const t=e.pos,n=[];for(;!ti(e);){const t=ni(e);if(yi(t))n.push(t);else if(vi(t)){if(n.pop()!==ui[t])break}else if(!bi(t))break;e.pos--}return!(t===e.pos||!oi(e,li.Equals)||!hi(e))||(e.pos=t,!1)}function hi(e){return ii(e,mi)}function mi(e){return e===li.Colon||e===li.Dash||function(e){return(e&=-33)>=65&&e<=90}(e)||function(e){return e>47&&e<58}(e)}function gi(e){return e===li.Space||e===li.Tab}function bi(e){return!isNaN(e)&&e!==li.Equals&&!gi(e)&&!ci(e)}function vi(e){return e===ai.CurlyL||e===ai.RoundL||e===ai.SquareL}function yi(e){return e===ai.CurlyR||e===ai.RoundR||e===ai.SquareR}!function(e){e[e.Tab=9]="Tab",e[e.Space=32]="Space",e[e.Dash=45]="Dash",e[e.Slash=47]="Slash",e[e.Colon=58]="Colon",e[e.Equals=61]="Equals",e[e.AngleLeft=60]="AngleLeft",e[e.AngleRight=62]="AngleRight"}(li||(li={}));const xi=e=>e.charCodeAt(0),wi="#.*:$-_!@%^+>/".split("").map(xi),ki={type:"markup",lookAhead:!0,prefix:""};function Ci(e,t=e.length,n={}){const r=Object.assign(Object.assign({},ki),n);let o;t=Math.min(e.length,Math.max(0,null==t?e.length:t)),r.lookAhead&&(t=function(e,t,n){for(ci(e.charCodeAt(t))&&t++;Ai(e.charCodeAt(t),n.type);)t++;return t}(e,t,r));const i=function(e,t,n){if(!n)return 0;const r=ei(e),o=n.split("").map(xi);let i;for(r.pos=t;!ti(r);)if(!Ti(r,ai.SquareR,ai.SquareL)&&!Ti(r,ai.CurlyR,ai.CurlyL)){if(i=r.pos,Si(r,o))return i;r.pos--}return-1}(e,t,r.prefix||"");if(-1===i)return;const s=ei(e,i);s.pos=t;const a=[];for(;!ti(s);){if(o=ni(s),a.includes(ai.CurlyR)){if(o===ai.CurlyR){a.push(o),s.pos--;continue}if(o!==ai.CurlyL){s.pos--;continue}}if(Ai(o,r.type))a.push(o);else if(Oi(o,r.type)){if(a.pop()!==ui[o])break}else{if(a.includes(ai.SquareR)||a.includes(ai.CurlyR)){s.pos--;continue}if(fi(s)||!_i(o))break}s.pos--}if(!a.length&&s.pos!==t){const r=e.slice(s.pos,t).replace(/^[*+>^]+/,"");return{abbreviation:r,location:t-r.length,start:n.prefix?i-n.prefix.length:t-r.length,end:t}}}function Ti(e,t,n){const r=e.pos;if(oi(e,t))for(;!ti(e);){if(oi(e,n))return!0;e.pos--}return e.pos=r,!1}function Si(e,t){const n=e.pos;let r=!1;for(let n=t.length-1;n>=0&&!ti(e)&&oi(e,t[n]);n--)r=0===n;return r||(e.pos=n),r}function _i(e){return e>64&&e<91||e>96&&e<123||e>47&&e<58||wi.includes(e)}function Oi(e,t){return e===ai.RoundL||"markup"===t&&(e===ai.SquareL||e===ai.CurlyL)}function Ai(e,t){return e===ai.RoundR||"markup"===t&&(e===ai.SquareR||e===ai.CurlyR)}function ji(e,t){const n=Zo(t);return"stylesheet"===n.type?function(e,t){return Eo(No(e,t),t)}(e,n):function(e,t){return fo(lo(e,t),t)}(e,n)}const Ei={markup:["html","xml","xsl","jsx","js","pug","slim","haml","vue"],stylesheet:["css","sass","scss","less","sss","stylus"]};var Pi=function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function s(e){try{c(r.next(e))}catch(e){i(e)}}function a(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}c((r=r.apply(e,t||[])).next())}))};let $i;try{$i=n(1398).l10n}catch(e){$i={t:e=>e}}const Ii=new Map;let Ri;const Mi=new Map,Ni=/^[a-z,A-Z,!,(,[,#,\.\{]/,zi=/^[a-z,A-Z,!,(,[,#,\.]/,Li=/^-?[a-z,A-Z,!,@,#]/,Di=/[a-z,A-Z\.]/,Bi=[...Ue.tags,"lorem"],Ui="bem",Fi="|",qi="t",Vi="c",Wi=3;function Hi(e,t,n,r){var o,i;if("never"===r.showExpandedAbbreviation||!xs(n,r.excludeLanguages))return;const s=ns(n);if(!s){if(!Ii.has(n)){const e=Object.assign(Object.assign({},is(n)),Zi[n]);Ii.set(n,Object.keys(e))}Ri=null!==(o=Ii.get(n))&&void 0!==o?o:[]}const a=as(e,t,{lookAhead:!s,type:s?"stylesheet":"markup"});if(!a)return;const{abbreviationRange:c,abbreviation:u,filter:l}=a,f=Xi(e,t).substr(0,t.character),d=function(e){if(e){const t=e.match(/[\w,:,-,\.]*$/);if(t)return t[0]}}(f);if(d===u&&f.endsWith(`<${u}`)&&Ei.markup.includes(n))return;const p=ls(n,r,l);let h,m="",g=[];const b=(e,t)=>{if(us(e,u)){try{m=ji(t,p),s&&"!important".startsWith(t)&&(m="!important")}catch(e){}m&&!function(e,t,n,r){var o,i;if(ns(e)&&r){const e=null!==(o=r["stylesheet.between"])&&void 0!==o?o:": ",s=null!==(i=r["stylesheet.after"])&&void 0!==i?i:";";let a=t.indexOf(e[0],Math.max(t.length-e.length,0));return a=a>=0?a:t.length,n===`${t.substring(0,a)}${e}\${0}${s}`||n.replace(/\s/g,"")===t.replace(/\s/g,"")+s}if("xml"===e&&Bi.some((e=>e.startsWith(t.toLowerCase()))))return!0;if(Bi.includes(t.toLowerCase())||Ri.includes(t))return!1;if(/[-,:]/.test(t)&&!/--|::/.test(t)&&!t.endsWith(":"))return!1;if(/^\.{2,}$/.test(t))return!0;if("."===t)return!1;const s=t.match(/^([a-z,A-Z,\d]*)\.$/);return s?!s[1]||!Ue.tags.includes(s[1]):("jsx"!==e||!/^([A-Z][A-Za-z0-9]*)+$/.test(t))&&n.toLowerCase()===`<${t.toLowerCase()}>\${1}</${t.toLowerCase()}>`}(e,t,m,p.options)&&(h=Q.create(t),h.textEdit=O.replace(c,Ji(Ki(m))),h.documentation=Qi(m),h.insertTextFormat=q.Snippet,h.detail=$i.t("Emmet Abbreviation"),h.label=u,h.label+=l?"|"+l.replace(",","|"):"",g=[h])}};if(ns(n)){if(b(n,u),u.length>4&&Be.properties.find((e=>e.startsWith(u))))return J.create([],!0);if(h&&m.length){h.textEdit=O.replace(c,Ji(Ki(m))),h.documentation=Qi(m),h.label=m.replace(/([^\\])\$\{\d+\}/g,"$1").replace(/\$\{\d+:([^\}]+)\}/g,"$1"),h.filterText=u;const e=Mi.has(n)?Mi.get(n):Mi.get("css");if(g=Gi(null!=e?e:[],u,u,c,p,"Emmet Custom Snippet",!1),!g.find((e=>{var t,n,r;return(null===(t=e.textEdit)||void 0===t?void 0:t.newText)&&(null===(n=e.textEdit)||void 0===n?void 0:n.newText)===(null===(r=null==h?void 0:h.textEdit)||void 0===r?void 0:r.newText)}))){const e=new RegExp(".*"+u.split("").map((e=>"$"===e||"+"===e?"\\"+e:e)).join(".*")+".*","i");(/\d/.test(u)||e.test(h.label))&&g.push(h)}}}else{b(n,u);let e=u;const t=u.match(/(>|\+)([\w:-]+)$/);if(t&&3===t.length&&(e=t[2]),"xml"!==n){const t=Gi(Bi,e,u,c,p,"Emmet Abbreviation");g=g.concat(t)}if(!0===r.showAbbreviationSuggestions){const t=Gi(Ri.filter((e=>!Bi.includes(e))),e,u,c,p,"Emmet Abbreviation");h&&t.length>0&&e!==u&&(h.sortText="0"+h.label,t.forEach((e=>{e.filterText=u,e.sortText="9"+u}))),g=g.concat(t)}"html"===n&&g.length>=2&&u.includes(":")&&(null===(i=null==h?void 0:h.textEdit)||void 0===i?void 0:i.newText)===`<${u}>\${0}</${u}>`&&(g=g.filter((e=>e.label!==u)))}return!0===r.showSuggestionsAsSnippets&&g.forEach((e=>e.kind=F.Snippet)),g.length?J.create(g,!0):void 0}function Gi(e,t,n,r,o,i,s=!0){if(!t||!e)return[];const a=[];return e.forEach((e=>{if(!e.startsWith(t.toLowerCase())||s&&e===t.toLowerCase())return;const c=n+e.substr(t.length);let u;try{u=ji(c,o)}catch(e){}if(!u)return;const l=Q.create(t+e.substr(t.length));l.documentation=Qi(u),l.detail=i,l.textEdit=O.replace(r,Ji(Ki(u))),l.insertTextFormat=q.Snippet,a.push(l)})),a}function Qi(e){return e.replace(/([^\\])\$\{\d+\}/g,"$1|").replace(/\$\{\d+:([^\}]+)\}/g,"$1")}function Ji(e){return e?e.replace(/([^\\])(\$)([^\{])/g,"$1\\$2$3"):e}function Ki(e){if(!e||!e.trim())return e;let t=-1,n=[],r=!1,o=!1,i=0;const s=e.length;try{for(;i<s&&!r;){if("$"!=e[i++]||"{"!=e[i++])continue;let a=-1,c=-1;for(;i<s&&/\d/.test(e[i]);)a=a<0?i:a,c=i+1,i++;if(-1===a||-1===c||i>=s||"}"!=e[i]&&":"!=e[i])continue;const u=e.substring(a,c);if(r="0"===u,r)break;let l=!1;if(":"==e[i++])for(;i<s;){if("}"==e[i]){l=!0;break}i++}Number(u)>Number(t)?(t=Number(u),n=[{numberStart:a,numberEnd:c}],o=!l):Number(u)===t&&n.push({numberStart:a,numberEnd:c})}}catch(e){}if(o&&!r)for(let t=0;t<n.length;t++){const r=n[t].numberStart,o=n[t].numberEnd;e=e.substr(0,r)+"0"+e.substr(o)}return e}function Xi(e,t){const n=e.offsetAt(t),r=e.getText();let o=0,i=r.length;for(let e=n-1;e>=0;e--)if("\n"===r[e]){o=e+1;break}for(let e=n;e<r.length;e++)if("\n"===r[e]){i=e;break}return r.substring(o,i)}let Zi={},Yi={},es={};const ts=(e,t)=>`\${${e}${t?":"+t:""}}`;function ns(e){return Ei.stylesheet.includes(e)}function rs(e){return ns(e)?"stylesheet":"markup"}function os(e){return ns(e)?"css":"html"}function is(e){const t=Zo({type:rs(e),syntax:e});return"xml"===e?{}:t.snippets}function ss(e,t){let n;for(let r=0;r<Wi;r++)if(e.endsWith(`${Fi}${Ui}`,t))t-=Ui.length+1,n=n?Ui+","+n:Ui;else if(e.endsWith(`${Fi}${Vi}`,t))t-=Vi.length+1,n=n?Vi+","+n:Vi;else{if(!e.endsWith(`${Fi}${qi}`,t))break;t-=qi.length+1,n=n?qi+","+n:qi}return{pos:t,filter:n}}function as(e,t,n){const r=Xi(e,t),o=r.substr(0,t.character),{pos:i,filter:s}=ss(o,t.character),a=s?s.length+1:0,c=Ci(r,i,n);if(c)return{abbreviationRange:p.create(t.line,c.location,t.line,c.location+c.abbreviation.length+a),abbreviation:c.abbreviation,filter:s}}function cs(e,t){if(!e)return;const{pos:n,filter:r}=ss(e,e.length),o=Ci(e,n,ns(t)||"stylesheet"===t?{syntax:"stylesheet",lookAhead:!1}:{lookAhead:!0});return o?{abbreviation:o.abbreviation,filter:r}:void 0}function us(e,t){if(!t)return!1;if(ns(e)){if(t.includes("#")){if(t.startsWith("#"))return/^#[\d,a-f,A-F]{1,6}$/.test(t);if(Bi.includes(t.substring(0,t.indexOf("#"))))return!1}return Li.test(t)}return t.startsWith("!")?!/[^!]/.test(t):!!(!/\(/.test(t)&&!/\)/.test(t)||/\{[^\}\{]*[\(\)]+[^\}\{]*\}(?:[>\+\*\^]|$)/.test(t)||/\(.*\)[>\+\*\^]/.test(t)||/\[[^\[\]\(\)]+=".*"\]/.test(t)||/[>\+\*\^]\(.*\)/.test(t))&&("jsx"===e?zi.test(t)&&Di.test(t):!/^{%|{#|{{/.test(t)&&Ni.test(t)&&Di.test(t))}function ls(e,t,n){var r,o,i,s,a,c,u,l,f,d;(t=null!=t?t:{}).preferences=null!==(r=t.preferences)&&void 0!==r?r:{};const p=t.preferences,h=ns(e)?e:"css",m=function(e,t){t||(t={});const n=Object.assign({},es,t)[e];if(!n||"string"==typeof n)return"xhtml"===n?{selfClosingStyle:"xhtml"}:{};const r={};for(const e in n)switch(e){case"tag_case":r.tagCase="lower"===n[e]||"upper"===n[e]?n[e]:"";break;case"attr_case":r.attributeCase="lower"===n[e]||"upper"===n[e]?n[e]:"";break;case"attr_quotes":r.attributeQuotes=n[e];break;case"tag_nl":r.format=!0!==n[e]&&!1!==n[e]||n[e];break;case"inline_break":r.inlineBreak=n[e];break;case"self_closing_tag":if(!0===n[e]){r.selfClosingStyle="xml";break}if(!1===n[e]){r.selfClosingStyle="html";break}r.selfClosingStyle=n[e];break;case"compact_bool":r.compactBooleanAttributes=n[e];break;default:r[e]=n[e]}return r}(e,null!==(o=t.syntaxProfiles)&&void 0!==o?o:{}),g=(m&&m.filters?m.filters.split(","):[]).map((e=>e.trim())),b=n&&n.split(",").some((e=>"bem"===e.trim()))||g.includes("bem"),v=n&&n.split(",").some((e=>"c"===e.trim()))||g.includes("c"),y=function(e,t){if(!t||"object"!=typeof t)return{};if(!ns(e)){const e={};for(const n in t)switch(n){case"filter.commentAfter":e.after=t[n];break;case"filter.commentBefore":e.before=t[n];break;case"filter.commentTrigger":e.trigger=t[n]}return{comment:e}}let n="number"==typeof(null==t?void 0:t["css.fuzzySearchMinScore"])?t["css.fuzzySearchMinScore"]:.3;n>1?n=1:n<0&&(n=0);const r={fuzzySearchMinScore:n};for(const n in t)switch(n){case"css.floatUnit":r.floatUnit=t[n];break;case"css.intUnit":r.intUnit=t[n];break;case"css.unitAliases":const o={};t[n].split(",").forEach((e=>{if(!e||!e.trim()||!e.includes(":"))return;const t=e.substr(0,e.indexOf(":")),n=e.substr(t.length+1);t.trim()&&n&&(o[t.trim()]=n)})),r.unitAliases=o;break;case`${e}.valueSeparator`:r.between=t[n];break;case`${e}.propertyEnd`:r.after=t[n]}return{stylesheet:r}}(e,t.preferences),x=(null==y?void 0:y.stylesheet)&&y.stylesheet.unitAliases||{},w={"output.formatSkip":["html"],"output.formatForce":["body"],"output.inlineBreak":0,"output.compactBoolean":!1,"output.reverseAttributes":!1,"output.field":ts,"markup.href":!0,"comment.enabled":!1,"comment.trigger":["id","class"],"comment.before":"","comment.after":"\n\x3c!-- /[#ID][.CLASS] --\x3e","bem.enabled":!1,"bem.element":"__","bem.modifier":"_","jsx.enabled":"jsx"===e,"stylesheet.shortHex":!0,"stylesheet.between":"stylus"===e?" ":": ","stylesheet.after":"sass"===e||"stylus"===e?"":";","stylesheet.intUnit":"px","stylesheet.floatUnit":"em","stylesheet.unitAliases":{e:"em",p:"%",x:"ex",r:"rem"},"stylesheet.fuzzySearchMinScore":.3};let k={"output.tagCase":m.tagCase,"output.attributeCase":m.attributeCase,"output.attributeQuotes":m.attributeQuotes,"output.format":null===(i=m.format)||void 0===i||i,"output.formatSkip":p["format.noIndentTags"],"output.formatForce":p["format.forceIndentationForTags"],"output.inlineBreak":null!==(s=m.inlineBreak)&&void 0!==s?s:p["output.inlineBreak"],"output.compactBoolean":null!==(a=m.compactBooleanAttributes)&&void 0!==a?a:p["profile.allowCompactBoolean"],"output.reverseAttributes":p["output.reverseAttributes"],"output.selfClosingStyle":null!==(u=null!==(c=m.selfClosingStyle)&&void 0!==c?c:p["output.selfClosingStyle"])&&void 0!==u?u:fs(e),"output.field":ts,"comment.enabled":v,"comment.trigger":p["filter.commentTrigger"],"comment.before":p["filter.commentBefore"],"comment.after":p["filter.commentAfter"],"bem.enabled":b,"bem.element":null!==(l=p["bem.elementSeparator"])&&void 0!==l?l:"__","bem.modifier":null!==(f=p["bem.modifierSeparator"])&&void 0!==f?f:"_","jsx.enabled":"jsx"===e,"stylesheet.shortHex":p["css.color.short"],"stylesheet.between":p[`${h}.valueSeparator`],"stylesheet.after":p[`${h}.propertyEnd`],"stylesheet.intUnit":p["css.intUnit"],"stylesheet.floatUnit":p["css.floatUnit"],"stylesheet.unitAliases":x,"stylesheet.fuzzySearchMinScore":p["css.fuzzySearchMinScore"]};if("jsx"===e){const e={class:"className","class*":"styleName",for:"htmlFor"},t={"class*":"styles"};m["markup.attributes"]&&(k["markup.attributes"]=Object.assign(Object.assign({},e),m["markup.attributes"])),m["markup.valuePrefix"]&&(k["markup.valuePrefix"]=Object.assign(Object.assign({},t),m["markup.valuePrefix"]))}if("vue"===e){const e={"class*":":class"},t={"class*":"$style"};m["markup.attributes"]&&(k["markup.attributes"]=Object.assign(Object.assign({},e),m["markup.attributes"])),m["markup.valuePrefix"]&&(k["markup.valuePrefix"]=Object.assign(Object.assign({},t),m["markup.valuePrefix"]))}const C={};[...Object.keys(w),...Object.keys(k)].forEach((e=>{var t;const n=e;C[n]=null!==(t=k[n])&&void 0!==t?t:w[n]}));const T=Object.assign(Object.assign({},w["stylesheet.unitAliases"]),k["stylesheet.unitAliases"]);C["stylesheet.unitAliases"]=T;const S=rs(e),_=(j=t.variables)?Object.assign({},Yi,j):Yi,O=os(e),A="stylesheet"===S?null!==(d=Zi[e])&&void 0!==d?d:Zi[O]:Zi[e];var j;return{type:S,options:C,variables:_,snippets:A,syntax:e,text:void 0,maxRepeat:1e3}}function fs(e){switch(e){case"xhtml":case"jsx":return"xhtml";case"xml":case"xsl":return"xml";default:return"html"}}function ds(e,t){const n=Zo(t);return"stylesheet"===t.type?No(e,n):lo(e,n)}function ps(e,t){let n;const r=Zo(t);return n="stylesheet"===t.type?"string"==typeof e?ji(e,r):Eo(e,r):"string"==typeof e?ji(e,r):fo(e,r),Ji(Ki(n))}function hs(e,t,n,r){return Pi(this,void 0,void 0,(function*(){if(ys(),!e.length)return;const o=[];for(let t of e)if("string"==typeof t){if(t=t.trim(),t.length&&"~"===t[0])r&&o.push(He(r,t.substring(1)));else if(i=t,qe.test(i))o.push(Le.file(t));else if(n)for(const e of n)o.push(He(e,t))}else console.warn("The following emmetExtensionsPath isn't a string: "+JSON.stringify(t));var i;for(const e of o){try{if((yield t.stat(e)).type!==Fe.Directory)continue}catch(e){continue}const n=He(e,"snippets.json"),r=He(e,"syntaxProfiles.json");let o;o="function"==typeof globalThis.TextDecoder?new globalThis.TextDecoder:new Ne.TextDecoder;let i="";try{const e=yield t.readFile(n);i=o.decode(e)}catch(e){}if(i.length)try{const e=ms(n,i);e.variables&&gs(e.variables),vs(e)}catch(e){throw ys(),e}let s="";try{const e=yield t.readFile(r);s=o.decode(e)}catch(e){}if(s.length)try{bs(ms(r,s))}catch(e){throw ys(),e}}}))}function ms(e,t){let n=[];const r=Me(t,n);if(n.length)throw new Error(`Found error ${function(e){switch(e){case 1:return"InvalidSymbol";case 2:return"InvalidNumberFormat";case 3:return"PropertyNameExpected";case 4:return"ValueExpected";case 5:return"ColonExpected";case 6:return"CommaExpected";case 7:return"CloseBraceExpected";case 8:return"CloseBracketExpected";case 9:return"EndOfFileExpected";case 10:return"InvalidCommentToken";case 11:return"UnexpectedEndOfComment";case 12:return"UnexpectedEndOfString";case 13:return"UnexpectedEndOfNumber";case 14:return"InvalidUnicode";case 15:return"InvalidEscapeCharacter";case 16:return"InvalidCharacter"}return"<unknown ParseErrorCode>"}(n[0].error)} while parsing the file ${e} at offset ${n[0].offset}`);return r}function gs(e){if("object"!=typeof e||!e)throw new Error($i.t("Invalid emmet.variables field. See https://code.visualstudio.com/docs/editor/emmet#_emmet-configuration for a valid example."));Yi=Object.assign({},Yi,e)}function bs(e){if("object"!=typeof e||!e)throw new Error($i.t("Invalid syntax profile. See https://code.visualstudio.com/docs/editor/emmet#_emmet-configuration for a valid example."));es=Object.assign({},es,e)}function vs(e){if("object"!=typeof e||!e)throw new Error($i.t("Invalid snippets file. See https://code.visualstudio.com/docs/editor/emmet#_using-custom-emmet-snippets for a valid example."));Object.keys(e).forEach((t=>{if(!e[t].snippets)return;const n=os(t);let r=e[t].snippets;if(e[n]&&e[n].snippets&&n!==t&&(r=Object.assign({},e[n].snippets,e[t].snippets)),ns(t)){const e=Mi.get(t),n=Object.assign([],e,Object.keys(r));Mi.set(t,n)}else for(const e in r)r.hasOwnProperty(e)&&r[e].startsWith("<")&&r[e].endsWith(">")&&(r[e]=`{${r[e]}}`);const o=Zi[t],i=function(e){const t={};return Object.keys(e).forEach((n=>{for(const r of n.split("|"))t[r]=e[n]})),t}(r),s=Object.assign({},o,i);Zi[t]=s}))}function ys(){Zi={},Ii.clear(),Mi.clear(),es={},Yi={}}function xs(e,t=[]){if(e&&!t.includes(e))return/\b(typescriptreact|javascriptreact|jsx-tags)\b/.test(e)?"jsx":"sass-indented"===e?"sass":"jade"===e?"pug":Ei.markup.includes(e)||Ei.stylesheet.includes(e)?e:void 0}},2647:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.detector=void 0;const r=n(2308),o=Object.keys(r.typeHandlers),i={56:"psd",66:"bmp",68:"dds",71:"gif",73:"tiff",77:"tiff",82:"webp",105:"icns",137:"png",255:"jpg"};t.detector=function(e){const t=e[0];if(t in i){const n=i[t];if(n&&r.typeHandlers[n].validate(e))return n}return o.find((t=>r.typeHandlers[t].validate(e)))}},5949:function(e,t,n){"use strict";var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function s(e){try{c(r.next(e))}catch(e){i(e)}}function a(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}c((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.types=t.setConcurrency=t.disableTypes=t.disableFS=t.imageSize=void 0;const o=n(9896),i=n(6928),s=n(9792),a=n(2308),c=n(2647),u=524288,l=new s.default({concurrency:100,autostart:!0}),f={disabledFS:!1,disabledTypes:[]};function d(e,t){const n=c.detector(e);if(void 0!==n){if(f.disabledTypes.indexOf(n)>-1)throw new TypeError("disabled file type: "+n);if(n in a.typeHandlers){const r=a.typeHandlers[n].calculate(e,t);if(void 0!==r)return r.type=n,r}}throw new TypeError("unsupported file type: "+n+" (file: "+t+")")}function p(e,t){if(Buffer.isBuffer(e))return d(e);if("string"!=typeof e||f.disabledFS)throw new TypeError("invalid invocation. input should be a Buffer");const n=i.resolve(e);if("function"!=typeof t){const e=function(e){const t=o.openSync(e,"r"),{size:n}=o.fstatSync(t);if(n<=0)throw o.closeSync(t),new Error("Empty file");const r=Math.min(n,u),i=Buffer.alloc(r);return o.readSync(t,i,0,r,0),o.closeSync(t),i}(n);return d(e,n)}l.push((()=>function(e){return r(this,void 0,void 0,(function*(){const t=yield o.promises.open(e,"r"),{size:n}=yield t.stat();if(n<=0)throw yield t.close(),new Error("Empty file");const r=Math.min(n,u),i=Buffer.alloc(r);return yield t.read(i,0,r,0),yield t.close(),i}))}(n).then((e=>process.nextTick(t,null,d(e,n)))).catch(t)))}e.exports=t=p,t.default=p,t.imageSize=p,t.disableFS=e=>{f.disabledFS=e},t.disableTypes=e=>{f.disabledTypes=e},t.setConcurrency=e=>{l.concurrency=e},t.types=Object.keys(a.typeHandlers)},7533:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.readUInt=void 0,t.readUInt=function(e,t,n,r){return n=n||0,e["readUInt"+t+(r?"BE":"LE")].call(e,n)}},2308:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.typeHandlers=void 0;const r=n(9648),o=n(941),i=n(7562),s=n(7857),a=n(4280),c=n(4270),u=n(4262),l=n(3879),f=n(3068),d=n(4782),p=n(8628),h=n(54),m=n(3564),g=n(7961),b=n(6004),v=n(2093);t.typeHandlers={bmp:r.BMP,cur:o.CUR,dds:i.DDS,gif:s.GIF,icns:a.ICNS,ico:c.ICO,j2c:u.J2C,jp2:l.JP2,jpg:f.JPG,ktx:d.KTX,png:p.PNG,pnm:h.PNM,psd:m.PSD,svg:g.SVG,tiff:b.TIFF,webp:v.WEBP}},9648:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.BMP=void 0,t.BMP={validate:e=>"BM"===e.toString("ascii",0,2),calculate:e=>({height:Math.abs(e.readInt32LE(22)),width:e.readUInt32LE(18)})}},941:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.CUR=void 0;const r=n(4270);t.CUR={validate:e=>0===e.readUInt16LE(0)&&2===e.readUInt16LE(2),calculate:e=>r.ICO.calculate(e)}},7562:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DDS=void 0,t.DDS={validate:e=>542327876===e.readUInt32LE(0),calculate:e=>({height:e.readUInt32LE(12),width:e.readUInt32LE(16)})}},7857:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.GIF=void 0;const n=/^GIF8[79]a/;t.GIF={validate(e){const t=e.toString("ascii",0,6);return n.test(t)},calculate:e=>({height:e.readUInt16LE(8),width:e.readUInt16LE(6)})}},4280:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ICNS=void 0;const n={ICON:32,"ICN#":32,"icm#":16,icm4:16,icm8:16,"ics#":16,ics4:16,ics8:16,is32:16,s8mk:16,icp4:16,icl4:32,icl8:32,il32:32,l8mk:32,icp5:32,ic11:32,ich4:48,ich8:48,ih32:48,h8mk:48,icp6:64,ic12:32,it32:128,t8mk:128,ic07:128,ic08:256,ic13:256,ic09:512,ic14:512,ic10:1024};function r(e,t){const n=t+4;return[e.toString("ascii",t,n),e.readUInt32BE(n)]}function o(e){const t=n[e];return{width:t,height:t,type:e}}t.ICNS={validate:e=>"icns"===e.toString("ascii",0,4),calculate(e){const t=e.length,n=e.readUInt32BE(4);let i=8,s=r(e,i),a=o(s[0]);if(i+=s[1],i===n)return a;const c={height:a.height,images:[a],width:a.width};for(;i<n&&i<t;)s=r(e,i),a=o(s[0]),i+=s[1],c.images.push(a);return c}}},4270:(e,t)=>{"use strict";function n(e,t){const n=e.readUInt8(t);return 0===n?256:n}function r(e,t){const r=6+16*t;return{height:n(e,r+1),width:n(e,r)}}Object.defineProperty(t,"__esModule",{value:!0}),t.ICO=void 0,t.ICO={validate:e=>0===e.readUInt16LE(0)&&1===e.readUInt16LE(2),calculate(e){const t=e.readUInt16LE(4),n=r(e,0);if(1===t)return n;const o=[n];for(let n=1;n<t;n+=1)o.push(r(e,n));return{height:n.height,images:o,width:n.width}}}},4262:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.J2C=void 0,t.J2C={validate:e=>"ff4fff51"===e.toString("hex",0,4),calculate:e=>({height:e.readUInt32BE(12),width:e.readUInt32BE(8)})}},3879:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.JP2=void 0;const n=e=>({height:e.readUInt32BE(4),width:e.readUInt32BE(8)});t.JP2={validate(e){const t=e.toString("hex",4,8),n=e.readUInt32BE(0);if("6a502020"!==t||n<1)return!1;const r=n+4,o=e.readUInt32BE(n);return"66747970"===e.slice(r,r+o).toString("hex",0,4)},calculate(e){const t=e.readUInt32BE(0);let r=t+4+e.readUInt16BE(t+2);switch(e.toString("hex",r,r+4)){case"72726571":return r=r+4+4+(e=>{const t=e.readUInt8(0);let n=1+2*t;return n=n+2+e.readUInt16BE(n)*(2+t),n+2+e.readUInt16BE(n)*(16+t)})(e.slice(r+4)),n(e.slice(r+8,r+24));case"6a703268":return n(e.slice(r+8,r+24));default:throw new TypeError("Unsupported header found: "+e.toString("ascii",r,r+4))}}}},3068:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.JPG=void 0;const r=n(7533);function o(e){return"45786966"===e.toString("hex",2,6)}function i(e,t){return{height:e.readUInt16BE(t),width:e.readUInt16BE(t+2)}}function s(e,t){const n=e.slice(2,t),o=n.toString("hex",6,8),i="4d4d"===o;if(i||"4949"===o)return function(e,t){const n=r.readUInt(e,16,14,t);for(let o=0;o<n;o++){const n=16+12*o,i=n+12;if(n>e.length)return;const s=e.slice(n,i);if(274===r.readUInt(s,16,0,t)){if(3!==r.readUInt(s,16,2,t))return;if(1!==r.readUInt(s,32,4,t))return;return r.readUInt(s,16,8,t)}}}(n,i)}function a(e,t){if(t>e.length)throw new TypeError("Corrupt JPG, exceeded buffer limits");if(255!==e[t])throw new TypeError("Invalid JPG, marker table corrupted")}t.JPG={validate:e=>"ffd8"===e.toString("hex",0,2),calculate(e){let t,n;for(e=e.slice(4);e.length;){const r=e.readUInt16BE(0);if(o(e)&&(t=s(e,r)),a(e,r),n=e[r+1],192===n||193===n||194===n){const n=i(e,r+5);return t?{height:n.height,orientation:t,width:n.width}:n}e=e.slice(r+2)}throw new TypeError("Invalid JPG, no size found")}}},4782:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.KTX=void 0,t.KTX={validate:e=>"KTX 11"===e.toString("ascii",1,7),calculate:e=>({height:e.readUInt32LE(40),width:e.readUInt32LE(36)})}},8628:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PNG=void 0;const n="CgBI";t.PNG={validate(e){if("PNG\r\n\n"===e.toString("ascii",1,8)){let t=e.toString("ascii",12,16);if(t===n&&(t=e.toString("ascii",28,32)),"IHDR"!==t)throw new TypeError("Invalid PNG");return!0}return!1},calculate:e=>e.toString("ascii",12,16)===n?{height:e.readUInt32BE(36),width:e.readUInt32BE(32)}:{height:e.readUInt32BE(20),width:e.readUInt32BE(16)}}},54:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PNM=void 0;const n={P1:"pbm/ascii",P2:"pgm/ascii",P3:"ppm/ascii",P4:"pbm",P5:"pgm",P6:"ppm",P7:"pam",PF:"pfm"},r=Object.keys(n),o={default:e=>{let t=[];for(;e.length>0;){const n=e.shift();if("#"!==n[0]){t=n.split(" ");break}}if(2===t.length)return{height:parseInt(t[1],10),width:parseInt(t[0],10)};throw new TypeError("Invalid PNM")},pam:e=>{const t={};for(;e.length>0;){const n=e.shift();if(n.length>16||n.charCodeAt(0)>128)continue;const[r,o]=n.split(" ");if(r&&o&&(t[r.toLowerCase()]=parseInt(o,10)),t.height&&t.width)break}if(t.height&&t.width)return{height:t.height,width:t.width};throw new TypeError("Invalid PAM")}};t.PNM={validate(e){const t=e.toString("ascii",0,2);return r.includes(t)},calculate(e){const t=e.toString("ascii",0,2),r=n[t],i=e.toString("ascii",3).split(/[\r\n]+/);return(o[r]||o.default)(i)}}},3564:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PSD=void 0,t.PSD={validate:e=>"8BPS"===e.toString("ascii",0,4),calculate:e=>({height:e.readUInt32BE(14),width:e.readUInt32BE(18)})}},7961:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SVG=void 0;const n=/<svg\s([^>"']|"[^"]*"|'[^']*')*>/,r={height:/\sheight=(['"])([^%]+?)\1/,root:n,viewbox:/\sviewBox=(['"])(.+?)\1/i,width:/\swidth=(['"])([^%]+?)\1/},o=2.54,i={in:96,cm:96/o,em:16,ex:8,m:96/o*100,mm:96/o/10,pc:96/72/12,pt:96/72,px:1},s=new RegExp(`^([0-9.]+(?:e\\d+)?)(${Object.keys(i).join("|")})?$`);function a(e){const t=s.exec(e);if(t)return Math.round(Number(t[1])*(i[t[2]]||1))}function c(e){const t=e.split(" ");return{height:a(t[3]),width:a(t[2])}}t.SVG={validate(e){const t=String(e);return n.test(t)},calculate(e){const t=e.toString("utf8").match(r.root);if(t){const e=function(e){const t=e.match(r.width),n=e.match(r.height),o=e.match(r.viewbox);return{height:n&&a(n[2]),viewbox:o&&c(o[2]),width:t&&a(t[2])}}(t[0]);if(e.width&&e.height)return function(e){return{height:e.height,width:e.width}}(e);if(e.viewbox)return function(e,t){const n=t.width/t.height;return e.width?{height:Math.floor(e.width/n),width:e.width}:e.height?{height:e.height,width:Math.floor(e.height*n)}:{height:t.height,width:t.width}}(e,e.viewbox)}throw new TypeError("Invalid SVG")}}},6004:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.TIFF=void 0;const r=n(9896),o=n(7533);function i(e,t){const n=o.readUInt(e,16,8,t);return(o.readUInt(e,16,10,t)<<16)+n}function s(e){if(e.length>24)return e.slice(12)}const a=["49492a00","4d4d002a"];t.TIFF={validate:e=>a.includes(e.toString("hex",0,4)),calculate(e,t){if(!t)throw new TypeError("Tiff doesn't support buffer");const n="BE"===function(e){const t=e.toString("ascii",0,2);return"II"===t?"LE":"MM"===t?"BE":void 0}(e),a=function(e,t,n){const i=o.readUInt(e,32,4,n);let s=1024;const a=r.statSync(t).size;i+s>a&&(s=a-i-10);const c=Buffer.alloc(s),u=r.openSync(t,"r");return r.readSync(u,c,0,s,i),r.closeSync(u),c.slice(2)}(e,t,n),c=function(e,t){const n={};let r=e;for(;r&&r.length;){const e=o.readUInt(r,16,0,t),a=o.readUInt(r,16,2,t),c=o.readUInt(r,32,4,t);if(0===e)break;1!==c||3!==a&&4!==a||(n[e]=i(r,t)),r=s(r)}return n}(a,n),u=c[256],l=c[257];if(!u||!l)throw new TypeError("Invalid Tiff. Missing tags");return{height:l,width:u}}}},2093:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.WEBP=void 0,t.WEBP={validate(e){const t="RIFF"===e.toString("ascii",0,4),n="WEBP"===e.toString("ascii",8,12),r="VP8"===e.toString("ascii",12,15);return t&&n&&r},calculate(e){const t=e.toString("ascii",12,16);if(e=e.slice(20,30),"VP8X"===t){const t=e[0];if(!(192&t||1&t))return function(e){return{height:1+e.readUIntLE(7,3),width:1+e.readUIntLE(4,3)}}(e);throw new TypeError("Invalid WebP")}if("VP8 "===t&&47!==e[0])return function(e){return{height:16383&e.readInt16LE(8),width:16383&e.readInt16LE(6)}}(e);const n=e.toString("hex",3,6);if("VP8L"===t&&"9d012a"!==n)return function(e){return{height:1+((15&e[4])<<10|e[3]<<2|(192&e[2])>>6),width:1+((63&e[2])<<8|e[1])}}(e);throw new TypeError("Invalid WebP")}}},2017:(e,t,n)=>{try{var r=n(9023);if("function"!=typeof r.inherits)throw"";e.exports=r.inherits}catch(t){e.exports=n(6698)}},6698:e=>{"function"==typeof Object.create?e.exports=function(e,t){t&&(e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}))}:e.exports=function(e,t){if(t){e.super_=t;var n=function(){};n.prototype=t.prototype,e.prototype=new n,e.prototype.constructor=e}}},9792:(e,t,n)=>{var r=n(2017),o=n(4434).EventEmitter;function i(e){if(!(this instanceof i))return new i(e);o.call(this),e=e||{},this.concurrency=e.concurrency||1/0,this.timeout=e.timeout||0,this.autostart=e.autostart||!1,this.results=e.results||null,this.pending=0,this.session=0,this.running=!1,this.jobs=[],this.timers={}}function s(){for(var e in this.timers){var t=this.timers[e];delete this.timers[e],clearTimeout(t)}}function a(e){var t=this;function n(e){t.end(e)}this.on("error",n),this.on("end",(function r(o){t.removeListener("error",n),t.removeListener("end",r),e(o,this.results)}))}function c(e){this.session++,this.running=!1,this.emit("end",e)}e.exports=i,e.exports.default=i,r(i,o),["pop","shift","indexOf","lastIndexOf"].forEach((function(e){i.prototype[e]=function(){return Array.prototype[e].apply(this.jobs,arguments)}})),i.prototype.slice=function(e,t){return this.jobs=this.jobs.slice(e,t),this},i.prototype.reverse=function(){return this.jobs.reverse(),this},["push","unshift","splice"].forEach((function(e){i.prototype[e]=function(){var t=Array.prototype[e].apply(this.jobs,arguments);return this.autostart&&this.start(),t}})),Object.defineProperty(i.prototype,"length",{get:function(){return this.pending+this.jobs.length}}),i.prototype.start=function(e){if(e&&a.call(this,e),this.running=!0,!(this.pending>=this.concurrency))if(0!==this.jobs.length){var t=this,n=this.jobs.shift(),r=!0,o=this.session,i=null,s=!1,u=null,l=n.hasOwnProperty("timeout")?n.timeout:this.timeout;l&&(i=setTimeout((function(){s=!0,t.listeners("timeout").length>0?t.emit("timeout",d,n):d()}),l),this.timers[i]=i),this.results&&(u=this.results.length,this.results[u]=null),this.pending++,t.emit("start",n);var f=n(d);f&&f.then&&"function"==typeof f.then&&f.then((function(e){return d(null,e)})).catch((function(e){return d(e||!0)})),this.running&&this.jobs.length>0&&this.start()}else 0===this.pending&&c.call(this);function d(e,a){r&&t.session===o&&(r=!1,t.pending--,null!==i&&(delete t.timers[i],clearTimeout(i)),e?t.emit("error",e,n):!1===s&&(null!==u&&(t.results[u]=Array.prototype.slice.call(arguments,1)),t.emit("success",a,n)),t.session===o&&(0===t.pending&&0===t.jobs.length?c.call(t):t.running&&t.start()))}},i.prototype.stop=function(){this.running=!1},i.prototype.end=function(e){s.call(this),this.jobs.length=0,this.pending=0,c.call(this,e)}},2698:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.wrapWithAbbreviation=async function(e){if(!(0,c.validate)(!1))return!1;const t=a.window.activeTextEditor,n=t.document;(e=e||{}).language||(e.language=n.languageId);const r=g(e)||"html",o=(0,u.getRootNode)(n,!0),i=(0,c.getEmmetHelper)(),s=Array.from(t.selections).sort(((e,t)=>e.start.compareTo(t.start))).map((e=>{let t=e;{let{start:e,end:r}=t;const i=n.offsetAt(e),s=n.getText(),u=(0,c.getHtmlFlatNode)(s,o,i,!0);if(u&&(0,c.isOffsetInsideOpenOrCloseTag)(u,i)){e=n.positionAt(u.start);const t=n.positionAt(u.end);r=t.isAfter(r)?t:r}const l=n.offsetAt(r),f=(0,c.getHtmlFlatNode)(s,o,l,!0);if(f&&(0,c.isOffsetInsideOpenOrCloseTag)(f,l)){const t=n.positionAt(f.start);e=t.isBefore(e)?t:e;const o=n.positionAt(f.end);r=o.isAfter(r)?o:r}t=new a.Range(e,r)}if(!t.isSingleLine&&0===t.end.character){const e=t.end.line-1;t=new a.Range(t.start,n.lineAt(e).range.end)}t.isEmpty&&(t=n.lineAt(t.start).range);const r=n.lineAt(t.start);return!r.isEmptyOrWhitespace&&r.firstNonWhitespaceCharacterIndex>t.start.character&&(t=t.with(new a.Position(t.start.line,r.firstNonWhitespaceCharacterIndex))),t})).reduce(((e,t)=>(e.length>0&&t.intersection(e[e.length-1])?e.push(t.union(e.pop())):e.push(t),e)),[]),l=t.selections;t.selections=s.map((e=>new a.Selection(e.start,e.end)));const f=s.map((e=>{let t;const r=n.getText(e),o=n.lineAt(e.start).text.match(/^(\s*)/),i=o?o[1]:"";return t=e.isSingleLine?[r]:r.split("\n"+i).map((e=>e.trimEnd())),t=t.map((e=>e.replace(/(\$\d)/g,"\\$1"))),{previewRange:e,originalRange:e,originalContent:r,textToWrapInPreview:t,baseIndent:i}})),{tabSize:d,insertSpaces:p}=t.options,b=p?" ".repeat(d):"\t";function v(){return t.edit((e=>{for(const t of f)e.replace(t.previewRange,t.originalContent),t.previewRange=t.originalRange}),{undoStopBefore:!1,undoStopAfter:!1})}let y=!1;async function x(e,n){const o=e&&e.trim()&&i.isAbbreviationValid(r,e)?i.extractAbbreviationFromText(e,r):void 0;if(!o)return y&&(y=!1,await v()),!1;const{abbreviation:s,filter:c}=o;if(n){const e=f.map((e=>({syntax:r,abbreviation:s,rangeToReplace:e.originalRange,textToWrap:e.textToWrapInPreview,filter:c,indent:b,baseIndent:e.baseIndent})));return y=!0,function(e){let n=new a.Range(0,0,0,0),r=new a.Range(0,0,0,0),o=0;return t.edit((t=>{for(let i=0;i<f.length;i++){const s=m(e[i])||"";if(!s)break;const c=f[i].previewRange,u=s.replace(/\$\{[\d]*\}/g,"|").replace(/\$\{[\d]*:([^}]*)\}/g,((e,t)=>t)).replace(/\\\$/g,"$");t.replace(c,u);const l=u.split("\n"),d=c.end.line-c.start.line+1,p=l.length-d,h=c.start.line+o;let g=c.start.character;const b=c.end.line+o+p;let v=l[l.length-1].length;i>0&&b===r.end.line?(g=r.end.character+(c.start.character-n.end.character),v+=g):i>0&&h===r.end.line?g=r.end.character+(c.start.character-n.end.character):1===l.length&&(v+=c.start.character),n=f[i].previewRange,r=new a.Range(h,g,b,v),f[i].previewRange=r,o+=p}}),{undoStopBefore:!1,undoStopAfter:!1})}(e)}const u=f.map((e=>({syntax:r,abbreviation:s,rangeToReplace:e.originalRange,textToWrap:e.textToWrapInPreview,filter:c,indent:b})));return y&&(y=!1,await v()),h(t,u,!1)}let w="";const k=a.l10n.t("Enter Abbreviation"),C=e&&e.abbreviation?e.abbreviation:await a.window.showInputBox({prompt:k,validateInput:async function(e){return e!==w&&(w=e,await x(e,!0)),""}}),T=await x(C,!1);return T||(t.selections=l),T},t.expandEmmetAbbreviation=function(e){if(!(0,c.validate)()||!a.window.activeTextEditor)return d();if(1===a.window.activeTextEditor.selections.length&&a.window.activeTextEditor.selection.isEmpty){const e=a.window.activeTextEditor.selection.anchor;if(0===e.character)return d();const t=e.translate(0,-1),n=a.window.activeTextEditor.document.getText(new a.Range(t,e));if(" "===n||"\t"===n)return d()}if((e=e||{}).language){if((a.workspace.getConfiguration("emmet").excludeLanguages?a.workspace.getConfiguration("emmet").excludeLanguages:[]).includes(a.window.activeTextEditor.document.languageId))return d()}else e.language=a.window.activeTextEditor.document.languageId;const t=g(e);if(!t)return d();const n=a.window.activeTextEditor;if(!0===a.workspace.getConfiguration("emmet").triggerExpansionOnTab&&n.selections.find((e=>!e.isEmpty)))return d();const r=[];let o,i=!0;const s=(0,c.getEmmetHelper)(),l=n.selections.slice(0);let f;function m(){if(f)return f;const e=!0===a.workspace.getConfiguration("emmet").optimizeStylesheetParsing;return f=1===n.selections.length&&(0,c.isStyleSheet)(n.document.languageId)&&e&&n.document.lineCount>1e3?(0,c.parsePartialStylesheet)(n.document,n.selection.isReversed?n.selection.anchor:n.selection.active):(0,u.getRootNode)(n.document,!0),f}return l.sort(((e,t)=>{const n=e.isReversed?e.anchor:e.active,r=t.isReversed?t.anchor:t.active;return-1*n.compareTo(r)})),l.forEach((e=>{const u=e.isReversed?e.anchor:e.active,[l,f,d]=((e,t,r,o)=>{r=e.validatePosition(r);let i=t,u=e.getText(i);if(!i.isEmpty){const e=s.extractAbbreviationFromText(u,o);return e?[i,e.abbreviation,e.filter]:[null,"",""]}const l=n.document.lineAt(r.line).text.substr(0,r.character);if("html"===o){const e=l.match(/<(\w+)$/);if(e)return u=e[1],i=new a.Range(r.translate(0,-(u.length+1)),r),[i,u,""]}const f=s.extractAbbreviation((0,c.toLSTextDocument)(n.document),r,{lookAhead:!1});if(!f)return[null,"",""];const{abbreviationRange:d,abbreviation:p,filter:h}=f;return[new a.Range(d.start.line,d.start.character,d.end.line,d.end.character),p,h]})(n.document,e,u,t);if(!l)return;if(!s.isAbbreviationValid(t,f))return;if((0,c.isStyleSheet)(t)&&f.endsWith(":"))return;const h=n.document.offsetAt(u);let g=(0,c.getFlatNode)(m(),h,!0),b=!0,v=t;if("html"===n.document.languageId)if((0,c.isStyleAttribute)(g,h))v="css",b=!1;else{const e=(0,c.getEmbeddedCssNodeIfAny)(n.document,g,u);e&&(g=(0,c.getFlatNode)(e,h,!0),v="css")}b&&!p(n.document,m(),g,v,h,l)||(o?i&&o!==f&&(i=!1):o=f,r.push({syntax:v,abbreviation:f,rangeToReplace:l,filter:d}))})),h(n,r,i).then((e=>e?Promise.resolve(void 0):d()))},t.isValidLocationForEmmetAbbreviation=p,t.getSyntaxFromArgs=g;const a=s(n(1398)),c=n(7937),u=n(6647),l=/[\u00a0]*[\d#\-\*\u2022]+\.?/,f=/^#[\da-fA-F]{0,6}$/;function d(){return!0===a.workspace.getConfiguration("emmet").triggerExpansionOnTab?a.commands.executeCommand("tab"):Promise.resolve(!0)}function p(e,t,n,r,o,i){if((0,c.isStyleSheet)(r)){if(t&&(t.comments||[]).some((e=>o>=e.start&&o<=e.end)))return!1;if(!n)return!0;const s=e.getText(new a.Range(i.start.line,i.start.character,i.end.line,i.end.character));if(s.startsWith("@"))return!0;if("sass"!==r&&"stylus"!==r&&"property"===n.type){if(n.parent&&"rule"!==n.parent.type&&"at-rule"!==n.parent.type)return!1;const e=n;if(e.terminatorToken&&e.separator&&o>=e.separatorToken.end&&o<=e.terminatorToken.start&&!s.includes(":"))return f.test(s)||"!"===s;if(!e.terminatorToken&&e.separator&&o>=e.separatorToken.end&&!s.includes(":"))return f.test(s)||"!"===s;if(f.test(s)||"!"===s)return!1}if("rule"!==n.type&&"at-rule"!==n.type)return!0;const c=n;if(o>c.contentStartToken.end)return!0;if(c.parent&&("rule"===c.parent.type||"at-rule"===c.parent.type)&&c.selectorToken){const t=e.positionAt(o),n=e.positionAt(c.selectorToken.start),r=e.positionAt(c.selectorToken.end);if(t.line!==r.line&&n.character===i.start.character&&n.line===i.start.line)return!0}return!1}const s="<",u=n;let l=0;if(u){if("script"===u.name){const e=(u.attributes||[]).filter((e=>"type"===e.name.toString()))[0],t=e?e.value.toString():"";return!!c.allowedMimeTypesInScriptTag.includes(t)||!(t&&"application/javascript"!==t&&"text/javascript"!==t||!g({language:"javascript"}))}if(!u.open||!u.close||!(u.open.end<=o&&o<=u.close.start))return!1;l=u.open.end;let e=u.firstChild;for(;e&&!(e.end>o);)l=e.end,e=e.nextSibling}const d=e.positionAt(l);let p=e.getText(new a.Range(d.line,d.character,i.start.line,i.start.character));if(p.length>500&&(p=p.substr(p.length-500)),!p.trim())return!0;let h=!0,m=!1,b=p.length-1;if(p[b]===s)return!1;for(;b>=0;){const e=p[b];if(b--,m||!/\s/.test(e))if("?"!==e||p[b]!==s){if(/\s/.test(e)&&p[b]===s)b--;else if(e===s||">"===e)if(b>=0&&"\\"===p[b])b--;else{if(">"===e){if(b>=0&&"="===p[b])continue;break}if(e===s){h=!m;break}}}else b--;else m=!0}return h}async function h(e,t,n){if(!t||0===t.length)return!1;let r=0;if(!n){t.sort(((e,t)=>t.rangeToReplace.start.compareTo(e.rangeToReplace.start)));for(const n of t){const t=m(n);t&&(await e.insertSnippet(new a.SnippetString(t),n.rangeToReplace,{undoStopBefore:!1,undoStopAfter:!1}),r++)}return r>0}const o=m(t[0]),i=t.map((e=>e.rangeToReplace));return!!o&&e.insertSnippet(new a.SnippetString(o),i)}function m(e){const t=(0,c.getEmmetHelper)(),n=t.getExpandOptions(e.syntax,(0,c.getEmmetConfiguration)(e.syntax),e.filter);let r;e.textToWrap&&(e.textToWrap=e.textToWrap.map((e=>e.replace(/\$\{/g,"\\${"))),e.filter&&e.filter.includes("t")&&(e.textToWrap=e.textToWrap.map((e=>e.replace(l,"").trim()))),n.text=e.textToWrap,n.options&&(e.rangeToReplace.isSingleLine||(n.options["output.inlineBreak"]=1),e.indent&&(n.options["output.indent"]=e.indent),e.baseIndent&&(n.options["output.baseIndent"]=e.baseIndent)));try{r=t.expandAbbreviation(e.abbreviation,n)}catch(e){a.window.showErrorMessage("Failed to expand abbreviation")}return r}function g(e){const t=(0,c.getMappingForIncludedLanguages)(),n=e.language,r=e.parentMode,o=a.workspace.getConfiguration("emmet").excludeLanguages?a.workspace.getConfiguration("emmet").excludeLanguages:[];if(o.includes(n))return;let i=(0,c.getEmmetMode)(t[n]??n,t,o);return i||(i=(0,c.getEmmetMode)(t[r]??r,t,o)),i}},5921:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.balanceOut=function(){d(!0)},t.balanceIn=function(){d(!1)};const a=s(n(1398)),c=n(7937),u=n(6647);let l=[],f=[];function d(e){if(!(0,c.validate)(!1)||!a.window.activeTextEditor)return;const t=a.window.activeTextEditor,n=t.document,r=(0,u.getRootNode)(n,!0);if(!r)return;const o=e?p:h;let i=t.selections.map((e=>o(n,r,e)));m(f,t.selections)?e?m(t.selections,i)||l.push(t.selections):l.length&&(i=l.pop()):l=e?[t.selections]:[],t.selections=i,f=t.selections}function p(e,t,n){const r=e.offsetAt(n.start),o=(0,c.getHtmlFlatNode)(e.getText(),t,r,!1);if(!o)return n;if(!o.open||!o.close)return(0,c.offsetRangeToSelection)(e,o.start,o.end);let i,s;return o.close.start<=r&&o.close.end>r?(i=(0,c.offsetRangeToSelection)(e,o.close.start,o.open.end),s=(0,c.offsetRangeToSelection)(e,o.close.end,o.open.start)):(i=(0,c.offsetRangeToSelection)(e,o.open.end,o.close.start),s=(0,c.offsetRangeToSelection)(e,o.open.start,o.close.end)),i.contains(n)&&!i.isEqual(n)?i:s.contains(n)&&!s.isEqual(n)?s:n}function h(e,t,n){const r=e.offsetAt(n.start),o=(0,c.getHtmlFlatNode)(e.getText(),t,r,!0);if(!o)return n;const i=e.offsetAt(n.start),s=e.offsetAt(n.end);if(o.open&&o.close){const t=i===o.start&&s===o.end,n=i>o.open.start&&i<o.open.end,r=i>o.close.start&&i<o.close.end;if(t||n||r)return(0,c.offsetRangeToSelection)(e,o.open.end,o.close.start)}if(!o.firstChild)return n;const a=o.firstChild;return i===a.start&&s===a.end&&a.open&&a.close?(0,c.offsetRangeToSelection)(e,a.open.end,a.close.start):(0,c.offsetRangeToSelection)(e,a.start,a.end)}function m(e,t){if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(!e[n].isEqual(t[n]))return!1;return!0}},4367:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DocumentStreamReader=void 0;class n{constructor(e,t,n){this.document=e,this.start=this.pos=t||0,this._sof=n?n[0]:0,this._eof=n?n[1]:e.getText().length}sof(){return this.pos<=this._sof}eof(){return this.pos>=this._eof}limit(e,t){return new n(this.document,e,[e,t])}peek(){return this.eof()?NaN:this.document.getText().charCodeAt(this.pos)}next(){if(this.eof())return NaN;const e=this.document.getText().charCodeAt(this.pos);return this.pos++,this.eof()&&(this.pos=this._eof),e}backUp(e){return this.pos-=e,this.pos<0&&(this.pos=0),this.peek()}current(){return this.substring(this.start,this.pos)}substring(e,t){return this.document.getText().substring(e,t)}error(e){return new Error(`${e} at offset ${this.pos}`)}eat(e){const t=this.peek(),n="function"==typeof e?e(t):t===e;return n&&this.next(),n}eatWhile(e){const t=this.pos;for(;!this.eof()&&this.eat(e););return this.pos!==t}}t.DocumentStreamReader=n},5315:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.DefaultCompletionItemProvider=void 0;const a=s(n(1398)),c=n(2698),u=n(7937),l=n(6647);t.DefaultCompletionItemProvider=class{provideCompletionItems(e,t,n,r){const o=this.provideCompletionItemsInternal(e,t,r);if(o)return o.then((e=>{if(!e||!e.items.length)return this.lastCompletionType=void 0,e;const t=e.items[0],n=t.documentation?t.documentation.toString():"";return n.startsWith("<")?this.lastCompletionType="html":n.indexOf(":")>0&&n.endsWith(";")?this.lastCompletionType="css":this.lastCompletionType=void 0,e}));this.lastCompletionType=void 0}provideCompletionItemsInternal(e,t,n){const r=a.workspace.getConfiguration("emmet"),o=r.excludeLanguages?r.excludeLanguages:[];if(o.includes(e.languageId))return;const i=(0,u.getMappingForIncludedLanguages)(),s=!!i[e.languageId],f=(0,u.getEmmetMode)(s?i[e.languageId]:e.languageId,i,o);if(!f||"never"===r.showExpandedAbbreviation||(s||"jsx"===f)&&"always"!==r.showExpandedAbbreviation)return;let d,p,h=f,m="html"===h||"jsx"===h||"xml"===h;const g=(0,u.toLSTextDocument)(e);t=e.validatePosition(t);const b=new a.Range(t.line,0,t.line,t.character);if(e.getText(b).trimStart().startsWith("//"))return;const v=(0,u.getEmmetHelper)();if("html"===h){if(n.triggerKind===a.CompletionTriggerKind.TriggerForIncompleteCompletions)switch(this.lastCompletionType){case"html":m=!1;break;case"css":m=!1,h="css"}if(m){const n=e.offsetAt(t),r=(0,l.getRootNode)(e,!0),o=(0,u.getHtmlFlatNode)(e.getText(),r,n,!1);if(o)if("script"===o.name){const e=o.attributes.find((e=>"type"===e.name.toString()));if(!e)return;{const t=e.value.toString();if("application/javascript"===t||"text/javascript"===t){if(!(0,c.getSyntaxFromArgs)({language:"javascript"}))return;m=!1}else u.allowedMimeTypesInScriptTag.includes(t)&&(m=!1)}}else if("style"===o.name)h="css",m=!1;else{const e=o.attributes.find((e=>"style"===e.name.toString()));e&&e.value.start<=n&&n<=e.value.end&&(h="css",m=!1)}}}const y=(0,u.isStyleSheet)(h)?{lookAhead:!1,syntax:"stylesheet"}:{lookAhead:!0,syntax:"markup"},x=v.extractAbbreviation(g,t,y);if(!x||!v.isAbbreviationValid(h,x.abbreviation))return;const w=e.offsetAt(t);if((0,u.isStyleSheet)(e.languageId)&&n.triggerKind!==a.CompletionTriggerKind.TriggerForIncompleteCompletions){if(m=!0,d=!0===a.workspace.getConfiguration("emmet").optimizeStylesheetParsing&&e.lineCount>1e3?(0,u.parsePartialStylesheet)(e,t):(0,l.getRootNode)(e,!0),!d)return;p=(0,u.getFlatNode)(d,w,!0)}if(!(0,u.isStyleSheet)(e.languageId)&&(0,u.isStyleSheet)(h)&&n.triggerKind!==a.CompletionTriggerKind.TriggerForIncompleteCompletions){if(m=!0,d=(0,l.getRootNode)(e,!0),!d)return;const n=(0,u.getFlatNode)(d,w,!0),r=(0,u.getEmbeddedCssNodeIfAny)(e,n,t);p=(0,u.getFlatNode)(r,w,!0)}if(m&&!(0,c.isValidLocationForEmmetAbbreviation)(e,d,p,h,w,(k=x.abbreviationRange,new a.Range(k.start.line,k.start.character,k.end.line,k.end.character))))return;var k;let C=Promise.resolve(!1);if(!(0,u.isStyleSheet)(h)&&("javascript"===e.languageId||"javascriptreact"===e.languageId||"typescript"===e.languageId||"typescriptreact"===e.languageId)){const t=x.abbreviation;C=t.startsWith("this.")||/\[[^\]=]*\]/.test(t)?Promise.resolve(!0):a.commands.executeCommand("vscode.executeDocumentSymbolProvider",e.uri).then((e=>!!e&&e.some((e=>t===e.name||t.startsWith(e.name+".")&&!/>|\*|\+/.test(t)))))}return C.then((n=>{if(n)return;const o=(0,u.getEmmetConfiguration)(h),i=v.doComplete((0,u.toLSTextDocument)(e),t,h,o),s=[];return i&&i.items&&i.items.forEach((e=>{const t=new a.CompletionItem(e.label);t.documentation=e.documentation,t.detail=e.detail,t.insertText=new a.SnippetString(e.textEdit.newText);const n=e.textEdit.range;t.range=new a.Range(n.start.line,n.start.character,n.end.line,n.end.character),t.filterText=e.filterText,t.sortText=e.sortText,!0===r.showSuggestionsAsSnippets&&(t.kind=a.CompletionItemKind.Snippet),s.push(t)})),new a.CompletionList(s,!0)}))}}},2727:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.fetchEditPoint=function(e){if(!(0,c.validate)()||!a.window.activeTextEditor)return;const t=a.window.activeTextEditor,n=[];t.selections.forEach((r=>{const o="next"===e?function(e,t){for(let n=e.anchor.line;n<t.document.lineCount;n++){const r=u(n,t,e.anchor,"next");if(r)return r}return e}(r,t):function(e,t){for(let n=e.anchor.line;n>=0;n--){const r=u(n,t,e.anchor,"prev");if(r)return r}return e}(r,t);n.push(o)})),t.selections=n,t.revealRange(t.selections[t.selections.length-1])};const a=s(n(1398)),c=n(7937);function u(e,t,n,r){const o=t.document.lineAt(e);let i=o.text;if(e!==n.line&&o.isEmptyOrWhitespace&&i.length)return new a.Selection(e,i.length,e,i.length);e===n.line&&"prev"===r&&(i=i.substr(0,n.character));const s="next"===r?i.indexOf('""',e===n.line?n.character:0):i.lastIndexOf('""'),c="next"===r?i.indexOf("><",e===n.line?n.character:0):i.lastIndexOf("><");let u=-1;return u=s>-1&&c>-1?"next"===r?Math.min(s,c):Math.max(s,c):s>-1?s:c,u>-1?new a.Selection(e,u+1,e,u+1):void 0}},1212:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.activateEmmetExtension=function(e){(0,w.migrateEmmetExtensionsPath)(),_(),(0,w.updateEmmetExtensionsPath)(),e.subscriptions.push(a.commands.registerCommand("editor.emmet.action.wrapWithAbbreviation",(e=>{(0,u.wrapWithAbbreviation)(e)}))),e.subscriptions.push(a.commands.registerCommand("emmet.expandAbbreviation",(e=>{(0,u.expandEmmetAbbreviation)(e)}))),e.subscriptions.push(a.commands.registerCommand("editor.emmet.action.removeTag",(()=>(0,l.removeTag)()))),e.subscriptions.push(a.commands.registerCommand("editor.emmet.action.updateTag",(e=>e&&"string"==typeof e?(0,f.updateTag)(e):(0,f.updateTag)(void 0)))),e.subscriptions.push(a.commands.registerCommand("editor.emmet.action.matchTag",(()=>{(0,d.matchTag)()}))),e.subscriptions.push(a.commands.registerCommand("editor.emmet.action.balanceOut",(()=>{(0,p.balanceOut)()}))),e.subscriptions.push(a.commands.registerCommand("editor.emmet.action.balanceIn",(()=>{(0,p.balanceIn)()}))),e.subscriptions.push(a.commands.registerCommand("editor.emmet.action.splitJoinTag",(()=>(0,h.splitJoinTag)()))),e.subscriptions.push(a.commands.registerCommand("editor.emmet.action.mergeLines",(()=>{(0,m.mergeLines)()}))),e.subscriptions.push(a.commands.registerCommand("editor.emmet.action.toggleComment",(()=>{(0,g.toggleComment)()}))),e.subscriptions.push(a.commands.registerCommand("editor.emmet.action.nextEditPoint",(()=>{(0,b.fetchEditPoint)("next")}))),e.subscriptions.push(a.commands.registerCommand("editor.emmet.action.prevEditPoint",(()=>{(0,b.fetchEditPoint)("prev")}))),e.subscriptions.push(a.commands.registerCommand("editor.emmet.action.selectNextItem",(()=>{(0,v.fetchSelectItem)("next")}))),e.subscriptions.push(a.commands.registerCommand("editor.emmet.action.selectPrevItem",(()=>{(0,v.fetchSelectItem)("prev")}))),e.subscriptions.push(a.commands.registerCommand("editor.emmet.action.evaluateMathExpression",(()=>{(0,y.evaluateMathExpression)()}))),e.subscriptions.push(a.commands.registerCommand("editor.emmet.action.incrementNumberByOneTenth",(()=>(0,x.incrementDecrement)(.1)))),e.subscriptions.push(a.commands.registerCommand("editor.emmet.action.incrementNumberByOne",(()=>(0,x.incrementDecrement)(1)))),e.subscriptions.push(a.commands.registerCommand("editor.emmet.action.incrementNumberByTen",(()=>(0,x.incrementDecrement)(10)))),e.subscriptions.push(a.commands.registerCommand("editor.emmet.action.decrementNumberByOneTenth",(()=>(0,x.incrementDecrement)(-.1)))),e.subscriptions.push(a.commands.registerCommand("editor.emmet.action.decrementNumberByOne",(()=>(0,x.incrementDecrement)(-1)))),e.subscriptions.push(a.commands.registerCommand("editor.emmet.action.decrementNumberByTen",(()=>(0,x.incrementDecrement)(-10)))),e.subscriptions.push(a.commands.registerCommand("editor.emmet.action.reflectCSSValue",(()=>(0,k.reflectCssValue)()))),e.subscriptions.push(a.commands.registerCommand("workbench.action.showEmmetCommands",(()=>{a.commands.executeCommand("workbench.action.quickOpen",">Emmet: ")}))),e.subscriptions.push(a.workspace.onDidChangeConfiguration((e=>{(e.affectsConfiguration("emmet.includeLanguages")||e.affectsConfiguration("emmet.useInlineCompletions"))&&_(),e.affectsConfiguration("emmet.extensionsPath")&&(0,w.updateEmmetExtensionsPath)()}))),e.subscriptions.push(a.workspace.onDidSaveTextDocument((e=>{const t=(0,w.getPathBaseName)(e.fileName);t.startsWith("snippets")&&t.endsWith(".json")&&(0,w.updateEmmetExtensionsPath)(!0)}))),e.subscriptions.push(a.workspace.onDidOpenTextDocument((e=>{const t=(0,w.getEmmetMode)(e.languageId,{},[])??"",n=(0,w.getSyntaxes)();(n.markup.includes(t)||n.stylesheet.includes(t))&&(0,C.addFileToParseCache)(e)}))),e.subscriptions.push(a.workspace.onDidCloseTextDocument((e=>{const t=(0,w.getEmmetMode)(e.languageId,{},[])??"",n=(0,w.getSyntaxes)();(n.markup.includes(t)||n.stylesheet.includes(t))&&(0,C.removeFileFromParseCache)(e)})))},t.deactivate=function(){O(),(0,C.clearParseCache)()};const a=s(n(1398)),c=n(5315),u=n(2698),l=n(5893),f=n(8306),d=n(3642),p=n(5921),h=n(5197),m=n(4754),g=n(6676),b=n(2727),v=n(5186),y=n(5774),x=n(8727),w=n(7937),k=n(7284),C=n(6647),T=new Map,S=[];function _(e){O();const t=new c.DefaultCompletionItemProvider,n={async provideInlineCompletionItems(e,n,r,o){const i=await t.provideCompletionItems(e,n,o,{triggerCharacter:void 0,triggerKind:a.CompletionTriggerKind.Invoke});if(!i)return;const s=i.items[0];if(!s)return;const c=s.range;return e.getText(c)===s.label?[{insertText:s.insertText,filterText:s.label,range:c}]:void 0}},r=a.workspace.getConfiguration("emmet").get("useInlineCompletions"),o=(0,w.getMappingForIncludedLanguages)();Object.keys(o).forEach((e=>{if(T.has(e)&&T.get(e)===o[e])return;if(r){const t=a.languages.registerInlineCompletionItemProvider({language:e,scheme:"*"},n);S.push(t)}const i=a.languages.registerCompletionItemProvider({language:e,scheme:"*"},t,...w.LANGUAGE_MODES[o[e]]);S.push(i),T.set(e,o[e])})),Object.keys(w.LANGUAGE_MODES).forEach((e=>{if(!T.has(e)){if(r){const t=a.languages.registerInlineCompletionItemProvider({language:e,scheme:"*"},n);S.push(t)}const o=a.languages.registerCompletionItemProvider({language:e,scheme:"*"},t,...w.LANGUAGE_MODES[e]);S.push(o),T.set(e,e)}}))}function O(){let e;for(T.clear();e=S.pop();)e.dispose()}},5774:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.evaluateMathExpression=function(){if(!a.window.activeTextEditor)return a.window.showInformationMessage("No editor is active"),Promise.resolve(!1);const e=a.window.activeTextEditor;return e.edit((t=>{e.selections.forEach((n=>{const r=n.isReversed?n.active:n.anchor,o=n.isReversed?n.anchor:n.active,i=e.document.getText(new a.Range(r,o));try{if(i){const e=String((0,c.default)(i));t.replace(new a.Range(r,o),e)}else{const r=e.document.getText(new a.Range(new a.Position(n.end.line,0),o)),i=(0,c.extract)(r);if(!i)throw new Error("Invalid extracted indices");const s=String((0,c.default)(r.substr(i[0],i[1]))),u=new a.Range(new a.Position(n.end.line,i[0]),new a.Position(n.end.line,i[1]));t.replace(u,s)}}catch(e){a.window.showErrorMessage("Could not evaluate expression"),console.warn("Math evaluation error",e)}}))}))};const a=s(n(1398)),c=s(n(2915))},2745:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.getImageSize=function(e){return e=e.replace(/^file:\/\//,""),d.test(e)?(t=e,new Promise(((e,n)=>{const r=new l.URL(t),o="https:"===r.protocol?u.get:c.get;if(!r.pathname)return n("Given url doesnt have pathname property");const i=r.pathname;o(r,(t=>{const r=[];let o=0;const s=n=>{try{const r=(0,f.imageSize)(Buffer.concat(n,o));t.removeListener("data",c),t.destroy(),e(p(a.basename(i),r))}catch(e){}},c=e=>{o+=e.length,r.push(e),s(r)};t.on("data",c).on("end",(()=>s(r))).once("error",(e=>{t.removeListener("data",c),n(e)}))})).once("error",n)}))):function(e){return new Promise(((t,n)=>{const r=e.match(/^data:.+?;base64,/);if(r)try{const n=Buffer.from(e.slice(r[0].length),"base64");return t(p("",(0,f.imageSize)(n)))}catch(e){return n(e)}(0,f.imageSize)(e,((r,o)=>{r?n(r):t(p(a.basename(e),o))}))}))}(e);var t};const a=s(n(6928)),c=s(n(8611)),u=s(n(5692)),l=n(7016),f=n(5949),d=/^https?:/;function p(e,t){const n=e.match(/@(\d+)x\./),r=n?+n[1]:1;if(t&&t.width&&t.height)return{realWidth:t.width,realHeight:t.height,width:Math.floor(t.width/r),height:Math.floor(t.height/r)}}},8727:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.incrementDecrement=function(e){if(!a.window.activeTextEditor)return void a.window.showInformationMessage("No editor is active");const t=a.window.activeTextEditor;return t.edit((n=>{t.selections.forEach((r=>{const o=l(t.document,r.isReversed?r.anchor:r.active);if(!o)return;const i=t.document.getText(o);f(i)&&n.replace(o,u(i,e))}))}))},t.update=u,t.locate=l;const a=s(n(1398)),c=/[0-9]/;function u(e,t){let n;const r=(n=e.match(/\.(\d+)$/))?n[1].length:1;let o=String((parseFloat(e)+t).toFixed(r)).replace(/\.0+$/,"");return(n=e.match(/^\-?(0\d+)/))&&(o=o.replace(/^(\-?)(\d+)/,((e,t,r)=>t+"0".repeat(Math.max(0,(n?n[1].length:0)-r.length))+r))),/^\-?\./.test(e)&&(o=o.replace(/^(\-?)0+/,"$1")),o}function l(e,t){const n=e.lineAt(t.line).text;let r,o=t.character,i=t.character,s=!1,u=!1;for(;o>0;){if(r=n[--o],"-"===r){u=!0;break}if("."!==r||s){if(!c.test(r)){o++;break}}else s=!0}for("-"!==n[i]||u||i++;i<n.length;)if(r=n[i++],"."===r&&!s&&c.test(n[i]))s=!0;else if(!c.test(r)){i--;break}if(o!==i&&f(n.slice(o,i)))return new a.Range(t.line,o,t.line,i)}function f(e){return!!e&&!isNaN(parseFloat(e))}},2361:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.locateFile=function(e,t){return/^\w+:/.test(t)?Promise.resolve(t):(t=a.normalize(t),u.test(t)?function(e,t){return new Promise(((n,r)=>{t=t.replace(u,"");const o=e=>{l(a.resolve(e,t)).then(n,(()=>{const n=a.dirname(e);if(!n||n===e)return r(`Unable to locate absolute file ${t}`);o(n)}))};o(e)}))}(e,t):function(e,t){return l(a.resolve(e,t))}(e,t))};const a=s(n(6928)),c=s(n(9896)),u="/"===a.sep?/^\/+/:/^\\+/;function l(e){return new Promise(((t,n)=>{c.stat(e,((r,o)=>r?n(r):o.isFile()?void t(e):n(new Error(`${e} is not a file`))))}))}},3642:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.matchTag=function(){if(!(0,c.validate)(!1)||!a.window.activeTextEditor)return;const e=a.window.activeTextEditor,t=e.document,n=(0,u.getRootNode)(t,!0);if(!n)return;const r=[];e.selections.forEach((e=>{const o=function(e,t,n){const r=e.offsetAt(n),o=(0,c.getHtmlFlatNode)(e.getText(),t,r,!0);if(!o)return;if(!o.open||!o.close||r>o.open.end&&r<o.close.start)return;const i=r<=o.open.end?o.close.start+2:o.start+1;return(0,c.offsetRangeToSelection)(e,i,i)}(t,n,e.start);o&&r.push(o)})),r.length&&(e.selections=r,e.revealRange(e.selections[r.length-1]))};const a=s(n(1398)),c=n(7937),u=n(6647)},4754:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.mergeLines=function(){if(!(0,c.validate)(!1)||!a.window.activeTextEditor)return;const e=a.window.activeTextEditor,t=(0,u.getRootNode)(e.document,!0);return t?e.edit((n=>{Array.from(e.selections).reverse().forEach((r=>{const o=function(e,t,n){let r,o;const i=e.offsetAt(t.start),s=e.offsetAt(t.end);if(t.isEmpty?r=o=(0,c.getFlatNode)(n,i,!0):(r=(0,c.getFlatNode)(n,i,!0),o=(0,c.getFlatNode)(n,s,!0)),!r||!o)return;const u=e.positionAt(r.start),l=u.line,f=u.character,d=e.positionAt(o.end).line;if(l===d)return;const p=(0,c.offsetRangeToVsRange)(e,r.start,o.end);let h=e.lineAt(l).text.substr(f);for(let t=l+1;t<=d;t++)h+=e.lineAt(t).text.trim();return new a.TextEdit(p,h)}(e.document,r,t);o&&n.replace(o.range,o.newText)}))})):void 0};const a=s(n(1398)),c=n(7937),u=n(6647)},4359:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.activate=function(e){e.subscriptions.push(a.commands.registerCommand("editor.emmet.action.updateImageSize",(()=>Promise.resolve().then((()=>s(n(530)))).then((e=>e.updateImageSize()))))),(0,l.setHomeDir)(a.Uri.file((0,c.homedir)())),(0,u.activateEmmetExtension)(e)};const a=s(n(1398)),c=n(857),u=n(1212),l=n(7937)},6647:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.getRootNode=function(e,t){const n=e.uri.toString(),r=a.get(n),c=e.version;if(t&&r&&c===r.key)return r.value;const u=((0,s.isStyleSheet)(e.languageId)?i.default:o.default)(e.getText());return t&&a.set(n,{key:c,value:u}),u},t.addFileToParseCache=function(e){const t=e.uri.toString();a.set(t,void 0)},t.removeFileFromParseCache=function(e){const t=e.uri.toString();a.delete(t)},t.clearParseCache=function(){a.clear()};const o=r(n(1253)),i=r(n(7545)),s=n(7937),a=new Map},7284:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.reflectCssValue=function(){const e=r.window.activeTextEditor;if(!e)return void r.window.showInformationMessage("No editor is active.");const t=(0,o.getCssPropertyFromDocument)(e,e.selection.active);return t?function(e,t){const n=t.parent;let r="";for(const e of i)if(t.name.startsWith(e)){r=e;break}const s=t.name.substr(r.length),a=t.value;return e.edit((t=>{i.forEach((i=>{if(i===r)return;const c=(0,o.getCssPropertyFromRule)(n,i+s);if(c){const n=(0,o.offsetRangeToVsRange)(e.document,c.valueToken.start,c.valueToken.end);t.replace(n,a)}}))}))}(e,t):void 0};const r=n(1398),o=n(7937),i=["-webkit-","-moz-","-ms-","-o-",""]},5893:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.removeTag=function(){if(!(0,u.validate)(!1)||!a.window.activeTextEditor)return;const e=a.window.activeTextEditor,t=e.document,n=(0,c.getRootNode)(t,!0);if(!n)return;const r=Array.from(e.selections).reverse().reduce(((t,r)=>t.concat(function(e,t,n){const r=e.offsetAt(n.start),o=(0,u.getHtmlFlatNode)(e.getText(),t,r,!0);if(!o)return[];let i,s;if(o.open&&(i=(0,u.offsetRangeToVsRange)(e,o.open.start,o.open.end)),o.close&&(s=(0,u.offsetRangeToVsRange)(e,o.close.start,o.close.end)),i&&s){const t=new a.Range(i.end.line,i.end.character,s.start.line,s.start.character),n=new a.Range(i.start.line,i.start.character,s.end.line,s.end.character);if(""===e.getText(t).trim()&&"pre"!==o.name)return[n]}const c=[];if(i&&(c.push(i),s)){const t=function(e,t,n){const r=t.start.line,o=n.start.line,i=e.lineAt(r).firstNonWhitespaceCharacterIndex,s=e.lineAt(o).firstNonWhitespaceCharacterIndex;let a;for(let t=r+1;t<o;t++){const n=e.lineAt(t);if(!n.isEmptyOrWhitespace){const e=n.firstNonWhitespaceCharacterIndex;a=a?Math.min(a,e):e}}let c=0;return a&&(c=a<i||a<s?0:Math.min(a-i,a-s)),c}(e,i,s);let n,r;for(let o=i.start.line+1;o<s.start.line;o++)e.lineAt(o).isEmptyOrWhitespace||(c.push(new a.Range(o,0,o,t)),void 0===n&&(n=o),r=o);l(e,s)&&r?c.push(new a.Range(r,e.lineAt(r).range.end.character,s.end.line,s.end.character)):c.push(s),l(e,i)&&n&&(c[1]=new a.Range(i.start.line,i.start.character,n,e.lineAt(n).firstNonWhitespaceCharacterIndex),c.shift())}return c}(e.document,n,r))),[]);return e.edit((e=>{r.forEach((t=>{e.delete(t)}))}))};const a=s(n(1398)),c=n(6647),u=n(7937);function l(e,t){if(t.start.line===t.end.line){const n=e.lineAt(t.start).text,r=e.getText(t);if(n.trim()===r)return!0}return!1}},5186:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.fetchSelectItem=function(e){if(!(0,c.validate)()||!a.window.activeTextEditor)return;const t=a.window.activeTextEditor,n=t.document,r=(0,f.getRootNode)(n,!0);if(!r)return;const o=[];t.selections.forEach((i=>{const s=i.isReversed?i.active:i.anchor,a=i.isReversed?i.anchor:i.active;let f;f=(0,c.isStyleSheet)(t.document.languageId)?"next"===e?(0,l.nextItemStylesheet)(n,s,a,r):(0,l.prevItemStylesheet)(n,s,a,r):"next"===e?(0,u.nextItemHTML)(n,s,a,r):(0,u.prevItemHTML)(n,s,a,r),o.push(f||i)})),t.selections=o,t.revealRange(t.selections[t.selections.length-1])};const a=s(n(1398)),c=n(7937),u=n(2703),l=n(4950),f=n(6647)},2703:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.nextItemHTML=function(e,t,n,i){const s=e.offsetAt(n);let a,c=(0,r.getHtmlFlatNode)(e.getText(),i,s,!1);if(c){if("comment"!==c.type){if(c.open&&s<=c.open.start+c.name.length)return o(e,c);if(c.open&&s<c.open.end){const n=e.offsetAt(t),o=function(e,t,n,o){if(o.attributes&&0!==o.attributes.length&&"comment"!==o.type)for(const i of o.attributes){if(n<i.start)return(0,r.offsetRangeToSelection)(e,i.start,i.end);if(!i.value||i.value.start===i.value.end)continue;if(t===i.start&&n===i.end||n<i.value.start)return(0,r.offsetRangeToSelection)(e,i.value.start,i.value.end);if(!i.value.toString().includes(" "))continue;let o;if(t===i.value.start&&n===i.value.end&&(o=-1),void 0===o&&n<i.end&&(o=e.positionAt(n).character-e.positionAt(i.value.start).character-1),void 0!==o){const[t,n]=(0,r.findNextWord)(i.value.toString(),o);if(void 0===t||void 0===n)return;if(t>=0&&n>=0){const o=i.value.start+t,s=i.value.start+n;return(0,r.offsetRangeToSelection)(e,o,s)}}}}(e,n,s,c);if(o)return o}for(a=c.firstChild;a&&(s>=a.end||"comment"===a.type);)a=a.nextSibling}for(;!a&&c;)c.nextSibling?"comment"!==c.nextSibling.type?a=c.nextSibling:c=c.nextSibling:c=c.parent;return a&&o(e,a)}},t.prevItemHTML=function(e,t,n,i){const s=e.offsetAt(t);let a,c=(0,r.getHtmlFlatNode)(e.getText(),i,s,!1);if(!c)return;const u=e.offsetAt(n);if(c.open&&"comment"!==c.type&&s-1>c.open.start)if(s<c.open.end||!c.firstChild||u<=c.firstChild.start)a=c;else{let e;for(a=c.firstChild;a.nextSibling&&s>=a.nextSibling.end;)a&&"comment"!==a.type&&(e=a),a=a.nextSibling;a=(0,r.getDeepestFlatNode)(a&&"comment"!==a.type?a:e)}for(;!a&&c;)c.previousSibling?"comment"!==c.previousSibling.type?a=(0,r.getDeepestFlatNode)(c.previousSibling):c=c.previousSibling:a=c.parent;if(!a)return;const l=function(e,t,n,o){if(o.attributes&&0!==o.attributes.length&&"comment"!==o.type)for(let i=o.attributes.length-1;i>=0;i--){const s=o.attributes[i];if(t<=s.start)continue;if(!s.value||s.value.start===s.value.end||t<s.value.start)return(0,r.offsetRangeToSelection)(e,s.start,s.end);if(t===s.value.start)return n>=s.value.end?(0,r.offsetRangeToSelection)(e,s.start,s.end):(0,r.offsetRangeToSelection)(e,s.value.start,s.value.end);const a=e.positionAt(t).character,c=e.positionAt(s.value.start).character,u=t>s.value.end?s.value.toString().length:a-c,[l,f]=(0,r.findPrevWord)(s.value.toString(),u);if(void 0===l||void 0===f)return;if(l>=0&&f>=0){const t=s.value.start+l,n=s.value.start+f;return(0,r.offsetRangeToSelection)(e,t,n)}}}(e,s,u,a);return l||o(e,a)};const r=n(7937);function o(e,t){if(t&&t.open){const n=t.open.start+1,o=n+t.name.length;return(0,r.offsetRangeToSelection)(e,n,o)}}},4950:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.nextItemStylesheet=function(e,t,n,r){const o=e.offsetAt(t),i=e.offsetAt(n);let s=(0,c.getFlatNode)(r,i,!0);if(s||(s=r),!s)return;if("property"===s.type&&o===s.start&&i===s.end)return l(e,s,o,i,!0,"next");if("property"===s.type&&o>=s.valueToken.start&&i<=s.valueToken.end){const t=l(e,s,o,i,!1,"next");if(t)return t}if("rule"===s.type&&i<s.selectorToken.end||"property"===s.type&&i<s.valueToken.end)return u(e,s);let a=s.firstChild;for(;a&&i>=a.end;)a=a.nextSibling;for(;!a&&s;)a=s.nextSibling,s=s.parent;return a?u(e,a):void 0},t.prevItemStylesheet=function(e,t,n,r){const o=e.offsetAt(t),i=e.offsetAt(n);let s=(0,c.getFlatNode)(r,o,!1);if(s||(s=r),!s)return;if("property"===s.type&&o===s.valueToken.start&&i===s.valueToken.end)return u(e,s);if("property"===s.type&&o>=s.valueToken.start&&i<=s.valueToken.end){const t=l(e,s,o,i,!1,"prev");if(t)return t}if("property"===s.type||!s.firstChild||"rule"===s.type&&o<=s.firstChild.start)return u(e,s);let a=s.firstChild;for(;a.nextSibling&&o>=a.nextSibling.end;)a=a.nextSibling;return a=(0,c.getDeepestFlatNode)(a),l(e,a,o,i,!1,"prev")};const a=s(n(1398)),c=n(7937);function u(e,t){if(!t)return;const n="rule"===t.type?t.selectorToken:t;return(0,c.offsetRangeToSelection)(e,n.start,n.end)}function l(e,t,n,r,o,i){if(!t||"property"!==t.type)return;const s=t,u=s.valueToken.stream.substring(s.valueToken.start,s.valueToken.end);if(o=o||"prev"===i&&n===s.valueToken.start&&r<s.valueToken.end)return(0,c.offsetRangeToSelection)(e,s.valueToken.start,s.valueToken.end);let l=-1;if("prev"===i){if(n===s.valueToken.start)return;const t=e.positionAt(n).character,r=e.positionAt(s.valueToken.start).character;l=n>s.valueToken.end?u.length:t-r}else if("next"===i){if(r===s.valueToken.end&&(n>s.valueToken.start||!u.includes(" ")))return;const t=e.positionAt(r).character,o=e.positionAt(s.valueToken.start).character;l=r===s.valueToken.end?-1:t-o-1}const[f,d]="prev"===i?(0,c.findPrevWord)(u,l):(0,c.findNextWord)(u,l);if(!f&&!d)return;const p=e.positionAt(s.valueToken.start),h=p.translate(0,f),m=p.translate(0,d);return new a.Selection(h,m)}},5197:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.splitJoinTag=function(){if(!(0,c.validate)(!1)||!a.window.activeTextEditor)return;const e=a.window.activeTextEditor,t=e.document,n=(0,u.getRootNode)(e.document,!0);return n?e.edit((r=>{Array.from(e.selections).reverse().forEach((e=>{const o=t.getText(),i=t.offsetAt(e.start),s=(0,c.getHtmlFlatNode)(o,n,i,!0);if(s){const e=function(e,t){let n,r;if(t.open&&t.close){const o=t.open.end-1,i=t.end;n=(0,c.offsetRangeToVsRange)(e,o,i),r="/>";const s=(0,c.getEmmetMode)(e.languageId,{},[])??"",a=(0,c.getEmmetConfiguration)(s);s&&a.syntaxProfiles[s]&&("xhtml"===a.syntaxProfiles[s].selfClosingStyle||"xhtml"===a.syntaxProfiles[s].self_closing_tag)&&(r=" "+r)}else{const o=e.getText().substring(t.start,t.end).match(/(\s*\/)?>$/),i=t.end,s=o?i-o[0].length:i;n=(0,c.offsetRangeToVsRange)(e,s,i),r=`></${t.name}>`}return new a.TextEdit(n,r)}(t,s);r.replace(e.range,e.newText)}}))})):void 0};const a=s(n(1398)),c=n(7937),u=n(6647)},6676:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t}),a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.toggleComment=function(){if(!(0,u.validate)()||!c.window.activeTextEditor)return;c.workspace.getConfiguration("editor.comments").get("insertSpace")?(d="/* ",p=" */",h="\x3c!-- ",m=" --\x3e"):(d="/*",p="*/",h="\x3c!--",m="--\x3e");const e=c.window.activeTextEditor,t=(0,f.getRootNode)(e.document,!0);return t?e.edit((n=>{const r=[];Array.from(e.selections).reverse().forEach((n=>{const o=(0,u.isStyleSheet)(e.document.languageId)?b(e.document,n,t):function(e,t,n){const r=t.isReversed?t.active:t.anchor,o=t.isReversed?t.anchor:t.active,i=e.offsetAt(r),s=e.offsetAt(o),a=e.getText(),f=(0,u.getHtmlFlatNode)(a,n,i,!0),d=(0,u.getHtmlFlatNode)(a,n,s,!0);if(!f||!d)return[];if((0,u.sameNodes)(f,d)&&"style"===f.name&&f.open&&f.close&&f.open.end<i&&f.close.start>s){const n=" ".repeat(f.open.end)+a.substring(f.open.end,f.close.start),r=(0,l.default)(n);return b(e,t,r)}const p=(0,u.getNodesInBetween)(f,d);let v=[];return p.forEach((t=>{v=v.concat(g(t,e))})),"comment"===f.type||(v.push(new c.TextEdit((0,u.offsetRangeToVsRange)(e,p[0].start,p[0].start),h)),v.push(new c.TextEdit((0,u.offsetRangeToVsRange)(e,p[p.length-1].end,p[p.length-1].end),m))),v}(e.document,n,t);o.length>0&&r.push(o)})),r.sort(((e,t)=>{const n=e[0].range.start.line-t[0].range.start.line;return 0===n?e[0].range.start.character-t[0].range.start.character:n}));let o=new c.Position(0,0);for(const e of r)e[0].range.end.isAfterOrEqual(o)&&e.forEach((e=>{n.replace(e.range,e.newText),o=e.range.end}))})):void 0};const c=s(n(1398)),u=n(7937),l=a(n(7545)),f=n(6647);let d,p,h,m;function g(e,t){let n=[];return"comment"===e.type?(n.push(new c.TextEdit((0,u.offsetRangeToVsRange)(t,e.start,e.start+h.length),"")),n.push(new c.TextEdit((0,u.offsetRangeToVsRange)(t,e.end-m.length,e.end),"")),n):(e.children.forEach((e=>{n=n.concat(g(e,t))})),n)}function b(e,t,n){const r=t.isReversed?t.active:t.anchor,o=t.isReversed?t.anchor:t.active;let i=e.offsetAt(r),s=e.offsetAt(o);const a=(0,u.getFlatNode)(n,i,!0),l=(0,u.getFlatNode)(n,s,!0);t.isEmpty?a&&(i=a.start,s=a.end,t=(0,u.offsetRangeToSelection)(e,i,s)):(i=function(e,t,n){for(const e of n.comments)if(e.start<=t&&t<=e.end)return t;if(!e)return t;if("property"===e.type)return e.start;const r=e;if(t<r.contentStartToken.end||!r.firstChild)return r.start;if(t<r.firstChild.start)return t;let o=r.firstChild;for(;o.nextSibling&&t>o.end;)o=o.nextSibling;return o.start}(a,i,n),s=function(e,t,n){for(const e of n.comments)if(e.start<=t&&t<=e.end)return t;if(!e)return t;if("property"===e.type)return e.end;const r=e;if(t===r.contentEndToken.end||!r.firstChild)return r.end;if(t>r.children[r.children.length-1].end)return t;let o=r.children[r.children.length-1];for(;o.previousSibling&&t<o.start;)o=o.previousSibling;return o.end}(l,s,n),t=(0,u.offsetRangeToSelection)(e,i,s));const f=[],h=[];return n.comments.forEach((n=>{const r=(0,u.offsetRangeToVsRange)(e,n.start,n.end);t.intersection(r)&&(f.push(r),h.push(new c.TextEdit((0,u.offsetRangeToVsRange)(e,n.start,n.start+d.length),"")),h.push(new c.TextEdit((0,u.offsetRangeToVsRange)(e,n.end-p.length,n.end),"")))})),h.length>0?h:[new c.TextEdit(new c.Range(t.start,t.start),d),new c.TextEdit(new c.Range(t.end,t.end),p)]}},530:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t}),a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.updateImageSize=function(){if(!(0,f.validate)()||!c.window.activeTextEditor)return;const e=c.window.activeTextEditor,t=Array.from(e.selections).reverse().map((t=>{const n=t.isReversed?t.active:t.anchor;return(0,f.isStyleSheet)(e.document.languageId)?function(e,t){return m(e,t,b)}(e,n):function(e,t){const n=g(e,t),r=n&&v(n);return r?(0,d.locateFile)(u.dirname(e.document.fileName),r).then(l.getImageSize).then((n=>{const o=g(e,t);return o&&v(o)===r?function(e,t,n,r){const o=e.document,i=x(t,"src");if(!i)return[];const s=x(t,"width"),a=x(t,"height"),u=function(e,t){const n=t.value?t.value.end:t.end,r=t.end;return n===r?"":e.document.getText().substring(n,r)}(e,i),l=t.attributes[t.attributes.length-1].end,d=[];let p="";return s?d.push(new c.TextEdit((0,f.offsetRangeToVsRange)(o,s.value.start,s.value.end),String(n))):p+=` width=${u}${n}${u}`,a?d.push(new c.TextEdit((0,f.offsetRangeToVsRange)(o,a.value.start,a.value.end),String(r))):p+=` height=${u}${r}${u}`,p&&d.push(new c.TextEdit((0,f.offsetRangeToVsRange)(o,l,l),p)),d}(e,o,n.width,n.height):[]})).catch((e=>(console.warn("Error while updating image size:",e),[]))):function(e,t){return m(e,t,(e=>{const n=e.document,r=(0,h.getRootNode)(n,!0),o=n.offsetAt(t),i=(0,f.getFlatNode)(r,o,!0);if(i&&"style"===i.name&&i.open&&i.close&&i.open.end<o&&i.close.start>o){const e=" ".repeat(i.open.end)+n.getText().substring(i.open.end,i.close.start),t=(0,p.default)(e),r=(0,f.getFlatNode)(t,o,!0);return r&&"property"===r.type?r:null}return null}))}(e,t)}(e,n)}));return Promise.all(t).then((t=>e.edit((e=>{t.forEach((t=>{t.forEach((t=>{e.replace(t.range,t.newText)}))}))}))))};const c=n(1398),u=s(n(6928)),l=n(2745),f=n(7937),d=n(2361),p=a(n(7545)),h=n(6647);function m(e,t,n){const r=n(e,t),o=r&&y(e,r,t);return o?(0,d.locateFile)(u.dirname(e.document.fileName),o).then(l.getImageSize).then((r=>{const i=n(e,t);return r&&i&&y(e,i,t)===o?function(e,t,n,r){const o=e.document,i=t.parent,s=(0,f.getCssPropertyFromRule)(i,"width"),a=(0,f.getCssPropertyFromRule)(i,"height"),u=t.separator||": ",l=function(e,t){let n;return(n=t.previousSibling||t.parent.contentStartToken)?e.document.getText().substring(n.end,t.start):(n=t.nextSibling||t.parent.contentEndToken)?e.document.getText().substring(t.end,n.start):""}(e,t),d=[];t.terminatorToken||d.push(new c.TextEdit((0,f.offsetRangeToVsRange)(o,t.end,t.end),";"));let p="";return s?d.push(new c.TextEdit((0,f.offsetRangeToVsRange)(o,s.valueToken.start,s.valueToken.end),`${n}px`)):p+=`${l}width${u}${n}px;`,a?d.push(new c.TextEdit((0,f.offsetRangeToVsRange)(o,a.valueToken.start,a.valueToken.end),`${r}px`)):p+=`${l}height${u}${r}px;`,p&&d.push(new c.TextEdit((0,f.offsetRangeToVsRange)(o,t.end,t.end),p)),d}(e,i,r.width,r.height):[]})).catch((e=>(console.warn("Error while updating image size:",e),[]))):Promise.reject(new Error("No valid image source"))}function g(e,t){const n=e.document,r=(0,h.getRootNode)(n,!0),o=n.offsetAt(t),i=(0,f.getFlatNode)(r,o,!0);return i&&"img"===i.name.toLowerCase()?i:null}function b(e,t){const n=e.document,r=(0,h.getRootNode)(n,!0),o=n.offsetAt(t),i=(0,f.getFlatNode)(r,o,!0);return i&&"property"===i.type?i:null}function v(e){const t=x(e,"src");if(t)return t.value.value}function y(e,t,n){if(!t)return;const r=function(e,t,n){const r=e.document.offsetAt(n);for(let e,n=0,o=t.parsedValue.length;n<o;n++)if((0,f.iterateCSSToken)(t.parsedValue[n],(t=>!("url"===t.type&&t.start<=r&&t.end>=r&&(e=t,1)))),e)return e}(e,t,n);if(!r)return;let o=r.item(0);return o&&"string"===o.type&&(o=o.item(0)),o&&o.valueOf()}function x(e,t){return t=t.toLowerCase(),e&&e.attributes.find((e=>e.name.toString().toLowerCase()===t))}},8306:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.updateTag=async function(e){if(!(0,c.validate)(!1)||!a.window.activeTextEditor)return;const t=a.window.activeTextEditor,n=t.document,r=(0,u.getRootNode)(n,!0);if(!r)return;const o=t.selections.reduceRight(((e,t)=>e.concat(function(e,t,n){const r=e.getText(),o=e.offsetAt(t.start),i=(0,c.getHtmlFlatNode)(r,n,o,!0);return i?function(e,t){const n=[];if(e.open){const r=t.positionAt(e.open.start);n.push({name:e.name,range:new a.Range(r.translate(0,1),r.translate(0,1).translate(0,e.name.length))})}if(e.close){const r=t.positionAt(e.close.start),o=t.positionAt(e.close.end);n.push({name:e.name,range:new a.Range(r.translate(0,2),o.translate(0,-1))})}return n}(i,e):[]}(n,t,r))),[]);if(!o.length)return;const i=o[0].name,s=o.every((e=>e.name===i));return!(void 0===e&&!(e=await a.window.showInputBox({prompt:"Enter Tag",value:s?i:void 0})))&&t.edit((t=>{o.forEach((n=>{t.replace(n.range,e)}))}))};const a=s(n(1398)),c=n(7937),u=n(6647)},7937:function(e,t,n){"use strict";var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t}),a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.allowedMimeTypesInScriptTag=t.LANGUAGE_MODES=void 0,t.setHomeDir=function(e){g=e},t.getEmmetHelper=b,t.updateEmmetExtensionsPath=function(e=!1){const t=b();let n=c.workspace.getConfiguration("emmet").get("extensionsPath");if(n||(n=[]),e||m!==n){m=n;const e=c.workspace.workspaceFolders?.length?c.workspace.workspaceFolders.map((e=>e.uri)):void 0,r=c.workspace.fs;t.updateExtensionsPath(n,r,e,g).catch((e=>{Array.isArray(n)&&n.length&&c.window.showErrorMessage(e.message)}))}},t.migrateEmmetExtensionsPath=function(){const e=c.workspace.getConfiguration().inspect("emmet.extensionsPath");"string"==typeof e?.globalValue?c.workspace.getConfiguration().update("emmet.extensionsPath",[e.globalValue],!0):null===e?.globalValue&&c.workspace.getConfiguration().update("emmet.extensionsPath",[],!0),"string"==typeof e?.workspaceValue?c.workspace.getConfiguration().update("emmet.extensionsPath",[e.workspaceValue],!1):null===e?.workspaceValue&&c.workspace.getConfiguration().update("emmet.extensionsPath",[],!1),"string"==typeof e?.workspaceFolderValue?c.workspace.getConfiguration().update("emmet.extensionsPath",[e.workspaceFolderValue]):null===e?.workspaceFolderValue&&c.workspace.getConfiguration().update("emmet.extensionsPath",[])},t.isStyleSheet=v,t.validate=function(e=!0){const t=c.window.activeTextEditor;return t?!(!e&&v(t.document.languageId)):(c.window.showInformationMessage("No editor is active"),!1)},t.getMappingForIncludedLanguages=function(){const e={},n=c.workspace.getConfiguration("emmet").get("includeLanguages"),r=Object.assign({},{handlebars:"html",php:"html"},n??{});return Object.keys(r).forEach((n=>{"string"==typeof r[n]&&t.LANGUAGE_MODES[r[n]]&&(e[n]=r[n])})),e},t.getEmmetMode=function(e,t,n){if(!e||n.includes(e))return;"jsx-tags"===e&&(e="javascriptreact"),t[e]&&(e=t[e]),/\b(typescriptreact|javascriptreact|jsx-tags)\b/.test(e)?e="jsx":"sass-indented"===e?e="sass":"jade"!==e&&"pug"!==e||(e="pug");const r={markup:["html","xml","xsl","jsx","js","pug","slim","haml"],stylesheet:["css","sass","scss","less","sss","stylus"]};return r.markup.includes(e)||r.stylesheet.includes(e)?e:void 0},t.parsePartialStylesheet=function(e,t){const n="css"===e.languageId,r=e.offsetAt(t);let o=0,i=e.getText().length;const s=r-5e3,a=s>0?s:o,u=new f.DocumentStreamReader(e,r);function d(){const t=e.positionAt(u.pos).line;if(!n&&g!==t){g=t;const n=e.lineAt(g).text.indexOf("//");n>-1&&(u.pos=e.offsetAt(new c.Position(g,n)))}}function p(){u.sof()||u.peek()!==w||(u.backUp(1)===k?u.pos=function(t){const n=e.getText().substring(0,t).lastIndexOf("/*");if(-1!==n)return n}(u.pos)??o:u.next())}function h(){if(u.eat(w))if(u.eat(w)&&!n){const t=e.positionAt(u.pos).line;u.pos=e.offsetAt(new c.Position(t+1,0))}else u.eat(k)&&(u.pos=function(t){let n=e.getText().substring(t).indexOf("*/");if(-1!==n)return n+=2+t,n}(u.pos)??i)}for(;!u.eof()&&!u.eat(y);)u.peek()===w?h():u.next();u.eof()||(i=u.pos),u.pos=r;let m=1,g=t.line,b=!1;for(;!b&&m>0&&!u.sof();){switch(d(),u.backUp(1)){case x:m--;break;case y:n?(u.next(),o=u.pos,b=!0):m++;break;case w:p()}(t.line-e.positionAt(u.pos).line>100||u.pos<=a)&&(b=!0)}g=e.positionAt(u.pos).line,m=0;let v=!1;for(;!b&&!u.sof()&&!v&&m>=0;){d();const e=u.backUp(1);if(!/\s/.test(String.fromCharCode(e))){switch(e){case w:p();break;case y:m++;break;case x:m--;break;default:m||(v=!0)}!u.sof()&&v&&(o=u.pos)}}try{const t=" ".repeat(o)+e.getText().substring(o,i);return(0,l.default)(t)}catch(e){return}},t.getFlatNode=C,t.getHtmlFlatNode=function e(t,n,r,o){let i=C(n,r,o);if(i){if("script"===i.name&&0===i.children.length){const n=T(t,i);n&&(i=e(n,i,r,o)??i)}else if("cdata"===i.type){i=e(S(t,i),i,r,o)??i}return i}},t.setupScriptNodeSubtree=T,t.setupCdataNodeSubtree=S,t.isOffsetInsideOpenOrCloseTag=function(e,t){const n=e;return!!(n.open&&t>n.open.start&&t<n.open.end||n.close&&t>n.close.start&&t<n.close.end)},t.offsetRangeToSelection=function(e,t,n){const r=e.positionAt(t),o=e.positionAt(n);return new c.Selection(r,o)},t.offsetRangeToVsRange=function(e,t,n){const r=e.positionAt(t),o=e.positionAt(n);return new c.Range(r,o)},t.getDeepestFlatNode=function e(t){if(!t||!t.children||0===t.children.length||!t.children.find((e=>"comment"!==e.type)))return t;for(let n=t.children.length-1;n>=0;n--)if("comment"!==t.children[n].type)return e(t.children[n])},t.findNextWord=function(e,t){let n,r,o=-1===t,i=!1,s=!1;for(;t<e.length-1;)if(t++,o){if(!o||i||" "!==e[t])if(i){if(" "===e[t]){r=t,s=!0;break}}else n=t,i=!0}else" "===e[t]&&(o=!0);return i&&!s&&(r=e.length),[n,r]},t.findPrevWord=function(e,t){let n,r,o=t===e.length,i=!1,s=!1;for(;t>-1;)if(t--,o){if(!o||s||" "!==e[t])if(s){if(" "===e[t]){n=t+1,i=!0;break}}else r=t+1,s=!0}else" "===e[t]&&(o=!0);return s&&!i&&(n=0),[n,r]},t.getNodesInBetween=function(e,t){if(_(e,t))return[e];if(!_(e.parent,t.parent)){if(t.start<e.start)return[t];if(t.start<e.end)return[e];for(;e.parent&&e.parent.end<t.start;)e=e.parent;for(;t.parent&&t.parent.start>e.start;)t=t.parent}const n=[];let r=e;const o=t.end;for(;r&&o>r.start;)n.push(r),r=r.nextSibling;return n},t.sameNodes=_,t.getEmmetConfiguration=function(e){const t=c.workspace.getConfiguration("emmet"),n=Object.assign({},t.syntaxProfiles||{}),r=Object.assign({},t.preferences||{});return"jsx"!==e&&"xml"!==e&&"xsl"!==e||(n[e]=n[e]||{},"object"!=typeof n[e]||n[e].hasOwnProperty("self_closing_tag")||n[e].hasOwnProperty("selfClosingStyle")||(n[e]={...n[e],selfClosingStyle:"jsx"===e?"xhtml":"xml"})),{preferences:r,showExpandedAbbreviation:t.showExpandedAbbreviation,showAbbreviationSuggestions:t.showAbbreviationSuggestions,syntaxProfiles:n,variables:t.variables,excludeLanguages:t.excludeLanguages,showSuggestionsAsSnippets:t.showSuggestionsAsSnippets}},t.iterateCSSToken=function e(t,n){for(let r=0,o=t.size;r<o;r++)if(!1===n(t.item(r))||!1===e(t.item(r),n))return!1;return!0},t.getCssPropertyFromRule=function(e,t){return e.children.find((e=>"property"===e.type&&e.name===t))},t.getCssPropertyFromDocument=function(e,t){const n=e.document,r=(0,p.getRootNode)(n,!0),o=n.offsetAt(t),i=C(r,o,!0);if(v(e.document.languageId))return i&&"property"===i.type?i:null;const s=i;if(s&&"style"===s.name&&s.open&&s.close&&s.open.end<o&&s.close.start>o){const e=" ".repeat(s.start)+n.getText().substring(s.start,s.end),t=C((0,l.default)(e),o,!0);return t&&"property"===t.type?t:null}return null},t.getEmbeddedCssNodeIfAny=function(e,t,n){if(!t)return;const r=t;if(r&&r.open&&r.close){const t=e.offsetAt(n);if(r.open.end<t&&t<=r.close.start&&"style"===r.name){const t=" ".repeat(r.open.end)+e.getText().substring(r.open.end,r.close.start);return(0,l.default)(t)}}},t.isStyleAttribute=function(e,t){if(!e)return!1;const n=e,r=(n.attributes||[]).findIndex((e=>"style"===e.name.toString()));if(-1===r)return!1;const o=n.attributes[r];return t>=o.value.start&&t<=o.value.end},t.isNumber=function(e){return"number"==typeof e},t.toLSTextDocument=function(e){return d.TextDocument.create(e.uri.toString(),e.languageId,e.version,e.getText())},t.getPathBaseName=function(e){const t=e.split("/").pop();return(t?t.split("\\").pop():"")??""},t.getSyntaxes=function(){return{markup:["html","xml","xsl","jsx","js","pug","slim","haml"],stylesheet:["css","sass","scss","less","sss","stylus"]}};const c=s(n(1398)),u=a(n(1253)),l=a(n(7545)),f=n(4367),d=n(5172),p=n(6647);let h,m,g;function b(){return h||(h=n(3547)),h}function v(e){return["css","scss","sass","less","stylus"].includes(e)}t.LANGUAGE_MODES={html:["!",".","}",":","*","$","]","/",">","0","1","2","3","4","5","6","7","8","9"],jade:["!",".","}",":","*","$","]","/",">","0","1","2","3","4","5","6","7","8","9"],slim:["!",".","}",":","*","$","]","/",">","0","1","2","3","4","5","6","7","8","9"],haml:["!",".","}",":","*","$","]","/",">","0","1","2","3","4","5","6","7","8","9"],xml:[".","}","*","$","]","/",">","0","1","2","3","4","5","6","7","8","9"],xsl:["!",".","}","*","$","/","]",">","0","1","2","3","4","5","6","7","8","9"],css:[":","!","-","0","1","2","3","4","5","6","7","8","9"],scss:[":","!","-","0","1","2","3","4","5","6","7","8","9"],sass:[":","!","0","1","2","3","4","5","6","7","8","9"],less:[":","!","-","0","1","2","3","4","5","6","7","8","9"],stylus:[":","!","0","1","2","3","4","5","6","7","8","9"],javascriptreact:["!",".","}","*","$","]","/",">","0","1","2","3","4","5","6","7","8","9"],typescriptreact:["!",".","}","*","$","]","/",">","0","1","2","3","4","5","6","7","8","9"]};const y=125,x=123,w=47,k=42;function C(e,t,n){if(e)return o(e.children);function r(e){if(!e)return;const r=e.start,i=e.end;if(r<t&&i>t||n&&r<=t&&i>=t)return o(e.children)??e;if("close"in e){const t=e;if(t.open&&!t.close)return o(t.children)}}function o(e){for(let t=0;t<e.length;t++){const n=r(e[t]);if(n)return n}}}function T(e,n){if("script"===n.name&&n.attributes&&n.attributes.some((e=>"type"===e.name.toString()&&t.allowedMimeTypesInScriptTag.includes(e.value.toString())))&&n.open){const t=" ".repeat(n.open.end),r=n.close?n.close.start:n.end,o=t+e.substring(n.open.end,r);return(0,u.default)(o).children.forEach((e=>{n.children.push(e),e.parent=n})),o}return""}function S(e,t){const n=t.start+9,r=t.end-3,o=" ".repeat(n)+e.substring(n,r);return(0,u.default)(o).children.forEach((e=>{t.children.push(e),e.parent=t})),o}function _(e,t){return!e&&!t||!(!e||!t)&&e.start===t.start&&e.end===t.end}t.allowedMimeTypesInScriptTag=["text/html","text/plain","text/x-template","text/template","text/ng-template"]},1398:e=>{"use strict";e.exports=require("vscode")},4434:e=>{"use strict";e.exports=require("events")},9896:e=>{"use strict";e.exports=require("fs")},8611:e=>{"use strict";e.exports=require("http")},5692:e=>{"use strict";e.exports=require("https")},857:e=>{"use strict";e.exports=require("os")},6928:e=>{"use strict";e.exports=require("path")},7016:e=>{"use strict";e.exports=require("url")},9023:e=>{"use strict";e.exports=require("util")},5172:(e,t,n)=>{"use strict";n.r(t),n.d(t,{TextDocument:()=>o});class r{constructor(e,t,n,r){this._uri=e,this._languageId=t,this._version=n,this._content=r,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){if(e){const t=this.offsetAt(e.start),n=this.offsetAt(e.end);return this._content.substring(t,n)}return this._content}update(e,t){for(const t of e)if(r.isIncremental(t)){const e=c(t.range),n=this.offsetAt(e.start),r=this.offsetAt(e.end);this._content=this._content.substring(0,n)+t.text+this._content.substring(r,this._content.length);const o=Math.max(e.start.line,0),i=Math.max(e.end.line,0);let a=this._lineOffsets;const u=s(t.text,!1,n);if(i-o===u.length)for(let e=0,t=u.length;e<t;e++)a[e+o+1]=u[e];else u.length<1e4?a.splice(o+1,i-o,...u):this._lineOffsets=a=a.slice(0,o+1).concat(u,a.slice(i+1));const l=t.text.length-(r-n);if(0!==l)for(let e=o+1+u.length,t=a.length;e<t;e++)a[e]=a[e]+l}else{if(!r.isFull(t))throw new Error("Unknown change event received");this._content=t.text,this._lineOffsets=void 0}this._version=t}getLineOffsets(){return void 0===this._lineOffsets&&(this._lineOffsets=s(this._content,!0)),this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);const t=this.getLineOffsets();let n=0,r=t.length;if(0===r)return{line:0,character:e};for(;n<r;){const o=Math.floor((n+r)/2);t[o]>e?r=o:n=o+1}const o=n-1;return{line:o,character:(e=this.ensureBeforeEOL(e,t[o]))-t[o]}}offsetAt(e){const t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;const n=t[e.line];if(e.character<=0)return n;const r=e.line+1<t.length?t[e.line+1]:this._content.length,o=Math.min(n+e.character,r);return this.ensureBeforeEOL(o,n)}ensureBeforeEOL(e,t){for(;e>t&&a(this._content.charCodeAt(e-1));)e--;return e}get lineCount(){return this.getLineOffsets().length}static isIncremental(e){const t=e;return null!=t&&"string"==typeof t.text&&void 0!==t.range&&(void 0===t.rangeLength||"number"==typeof t.rangeLength)}static isFull(e){const t=e;return null!=t&&"string"==typeof t.text&&void 0===t.range&&void 0===t.rangeLength}}var o;function i(e,t){if(e.length<=1)return e;const n=e.length/2|0,r=e.slice(0,n),o=e.slice(n);i(r,t),i(o,t);let s=0,a=0,c=0;for(;s<r.length&&a<o.length;){const n=t(r[s],o[a]);e[c++]=n<=0?r[s++]:o[a++]}for(;s<r.length;)e[c++]=r[s++];for(;a<o.length;)e[c++]=o[a++];return e}function s(e,t,n=0){const r=t?[n]:[];for(let t=0;t<e.length;t++){const o=e.charCodeAt(t);a(o)&&(13===o&&t+1<e.length&&10===e.charCodeAt(t+1)&&t++,r.push(n+t+1))}return r}function a(e){return 13===e||10===e}function c(e){const t=e.start,n=e.end;return t.line>n.line||t.line===n.line&&t.character>n.character?{start:n,end:t}:e}function u(e){const t=c(e.range);return t!==e.range?{newText:e.newText,range:t}:e}!function(e){e.create=function(e,t,n,o){return new r(e,t,n,o)},e.update=function(e,t,n){if(e instanceof r)return e.update(t,n),e;throw new Error("TextDocument.update: document must be created by TextDocument.create")},e.applyEdits=function(e,t){const n=e.getText(),r=i(t.map(u),((e,t)=>{const n=e.range.start.line-t.range.start.line;return 0===n?e.range.start.character-t.range.start.character:n}));let o=0;const s=[];for(const t of r){const r=e.offsetAt(t.range.start);if(r<o)throw new Error("Overlapping edit");r>o&&s.push(n.substring(o,r)),t.newText.length&&s.push(t.newText),o=e.offsetAt(t.range.end)}return s.push(n.substr(o)),s.join("")}}(o||(o={}))}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var i=t[r]={exports:{}};return e[r].call(i.exports,i,i.exports,n),i.exports}n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r=n(4359),o=exports;for(var i in r)o[i]=r[i];r.__esModule&&Object.defineProperty(o,"__esModule",{value:!0})})();
//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/2901c5ac6db8a986a5666c3af51ff804d05af0d4/extensions/emmet/dist/node/emmetNodeMain.js.map