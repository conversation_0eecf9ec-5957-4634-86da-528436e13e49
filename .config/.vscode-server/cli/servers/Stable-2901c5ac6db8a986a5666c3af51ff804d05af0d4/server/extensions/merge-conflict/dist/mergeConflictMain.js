/*! For license information please see mergeConflictMain.js.LICENSE.txt */
(()=>{"use strict";var e={956:(e,n,t)=>{t.r(n),t.d(n,{ActiveStatus:()=>x.f,AppInsightsCore:()=>h,BaseTelemetryPlugin:()=>_.s,DiagnosticLogger:()=>c.wq,EventLatency:()=>m,EventPersistence:()=>I,EventPropertyType:()=>y,EventsDiscardedReason:()=>E.x,FullVersionString:()=>f.xE,InternalAppInsightsCore:()=>s._,LoggingSeverity:()=>S,MinChannelPriorty:()=>C,NotificationManager:()=>T.h,PerfEvent:()=>a.Q6,PerfManager:()=>a.NS,ProcessTelemetryContext:()=>P.W0,SenderPostManager:()=>H.v,TraceLevel:()=>b,Undefined:()=>L.bA,ValueKind:()=>p,ValueSanitizer:()=>w,Version:()=>f.Rx,_InternalLogMessage:()=>c.WD,__getRegisteredEvents:()=>D.El,_appendHeader:()=>O.LU,_getAllResponseHeaders:()=>O.w3,_logInternalMessage:()=>c.Oc,_testHookMaxUnloadHooksCb:()=>B.d,_throwInternal:()=>c.ZP,_warnToConsole:()=>c.OG,addEventHandler:()=>D.So,addEventListeners:()=>D.lQ,addPageHideEventListener:()=>D.Fc,addPageShowEventListener:()=>D.oS,addPageUnloadEventListener:()=>D.ee,areCookiesSupported:()=>M.gi,arrForEach:()=>o.Iuo,arrIndexOf:()=>o.rDm,arrMap:()=>o.W$7,arrReduce:()=>o.KTd,attachEvent:()=>D.Q3,blockDynamicConversion:()=>z.V9,convertAllHeadersToMap:()=>O.IL,cookieAvailable:()=>M.gi,createCookieMgr:()=>M.xN,createDynamicConfig:()=>u.e,createEnumStyle:()=>g.H,createGuid:()=>f.gj,createProcessTelemetryContext:()=>P.i8,createTraceParent:()=>U.wk,createUniqueNamespace:()=>N.Z,createUnloadHandlerContainer:()=>F.P,dateNow:()=>o.f0d,detachEvent:()=>D.Ym,disallowsSameSiteNone:()=>M.It,doPerf:()=>a.r2,dumpObj:()=>o.mmD,eventOff:()=>D.ML,eventOn:()=>D.mB,extend:()=>f.X$,findW3cTraceParent:()=>U.ef,forceDynamicConversion:()=>z.Hf,formatErrorMessageXdr:()=>O.HU,formatErrorMessageXhr:()=>O.r4,formatTraceParent:()=>U.L0,generateW3CId:()=>k.cL,getCommonSchemaMetaData:()=>f.Go,getConsole:()=>A.U5,getCookieValue:()=>f.UM,getCrypto:()=>A.MY,getDocument:()=>o.YEm,getDynamicConfigHandler:()=>z.QA,getExceptionName:()=>O.lL,getFieldValueType:()=>f.cq,getGlobal:()=>o.mS$,getGlobalInst:()=>o.zS2,getHistory:()=>o.JKf,getIEVersion:()=>A.L0,getISOString:()=>O._u,getJSON:()=>A.hm,getLocation:()=>A.g$,getMsCrypto:()=>A.iN,getNavigator:()=>o.w3n,getPerformance:()=>o.FJj,getResponseText:()=>O.Lo,getSetValue:()=>O.c2,getTenantId:()=>f.EO,getTime:()=>f.WB,getWindow:()=>o.zkX,hasDocument:()=>o.Wtk,hasHistory:()=>o.twz,hasJSON:()=>A.Z,hasNavigator:()=>o.w9M,hasOwnProperty:()=>o.v0u,hasWindow:()=>o.Vdv,isArray:()=>o.cyL,isArrayValid:()=>f.wJ,isBeaconsSupported:()=>A.Uf,isBoolean:()=>o.Lmq,isChromium:()=>f.F2,isDate:()=>o.$PY,isDocumentObjectAvailable:()=>f.g8,isError:()=>o.bJ7,isFetchSupported:()=>A.R7,isFunction:()=>o.Tnt,isGreaterThanZero:()=>f.ei,isIE:()=>A.lT,isLatency:()=>f.Hh,isNotTruthy:()=>o.woc,isNullOrUndefined:()=>o.hXl,isNumber:()=>o.EtT,isObject:()=>o.Gvm,isReactNative:()=>A.lV,isSampledFlag:()=>U.N7,isString:()=>o.KgX,isTruthy:()=>o.zzB,isTypeof:()=>o.Edw,isUint8ArrayAvailable:()=>f.h3,isUndefined:()=>o.b07,isValidSpanId:()=>U.wN,isValidTraceId:()=>U.hX,isValidTraceParent:()=>U.mJ,isValueAssigned:()=>f.yD,isValueKind:()=>f.m0,isWindowObjectAvailable:()=>f.P$,isXhrSupported:()=>A.xk,mergeEvtNamespace:()=>D.Hm,newGuid:()=>k.aq,newId:()=>R.Si,normalizeJsName:()=>O.cH,objDefineAccessors:()=>o.raO,objForEachKey:()=>o.zav,objFreeze:()=>o.N6t,objKeys:()=>o.cGk,objSeal:()=>o.jsL,onConfigChange:()=>u.a,openXhr:()=>f.H$,optimizeObject:()=>O.hW,parseResponse:()=>X.x,parseTraceParent:()=>U.ZI,perfNow:()=>o.UUD,prependTransports:()=>O.jL,proxyAssign:()=>O.qz,proxyFunctionAs:()=>O.RF,proxyFunctions:()=>O.o$,random32:()=>R.VN,randomValue:()=>R.Z1,removeEventHandler:()=>D.zh,removeEventListeners:()=>D.Wg,removePageHideEventListener:()=>D.sq,removePageShowEventListener:()=>D.vF,removePageUnloadEventListener:()=>D.Ds,safeGetCookieMgr:()=>M.um,safeGetLogger:()=>c.y0,sanitizeProperty:()=>f.TC,setEnableEnvMocks:()=>A.cU,setProcessTelemetryTimings:()=>f.u9,setValue:()=>O.KY,strContains:()=>O.Ju,strEndsWith:()=>o.Cv9,strFunction:()=>L.hW,strObject:()=>L._1,strPrototype:()=>L.vR,strStartsWith:()=>o.tGl,strTrim:()=>o.EHq,strUndefined:()=>L.bA,throwError:()=>o.$8,toISOString:()=>O._u,useXDomainRequest:()=>A.PV});var r=t(659),i=t(8279),o=t(269),a=t(8156),u=t(9749),c=t(3775),s=t(2774),l=t(937),f=t(4822),d=t(1739),v=(0,o.ZHX)({endpointUrl:l.S,propertyStorageOverride:{isVal:function(e){return!e||e.getProperty&&e.setProperty||(0,o.$8)("Invalid property storage override passed."),!0}}}),h=function(e){function n(){var t=e.call(this)||this;return(0,i.A)(n,t,(function(e,n){e[d.mE]=function(t,r,i,s){(0,a.r2)(e,(function(){return"AppInsightsCore.initialize"}),(function(){try{n[d.mE]((0,u.e)(t,v,i||e[d.Uw],!1).cfg,r,i,s)}catch(n){var a=e[d.Uw],l=(0,o.mmD)(n);-1!==l[d.Sj]("channels")&&(l+="\n - Channels must be provided through config.channels only!"),(0,c.ZP)(a,1,514,"SDK Initialization Failed - no telemetry will be sent: "+l)}}),(function(){return{config:t,extensions:r,logger:i,notificationManager:s}}))},e.track=function(t){(0,a.r2)(e,(function(){return"AppInsightsCore.track"}),(function(){var r=t;if(r){r[d.dg]=r[d.dg]||{},r[d.dg].trackStart=(0,f.WB)(),(0,f.Hh)(r.latency)||(r.latency=1);var i=r.ext=r.ext||{};i.sdk=i.sdk||{},i.sdk.ver=f.xE;var o=r.baseData=r.baseData||{};o[l._0]=o[l._0]||{};var a=o[l._0];a[l.hj]=a[l.hj]||e.pluginVersionString||l.m5}n.track(r)}),(function(){return{item:t}}),!t.sync)},e[d.h4]=function(e){return n[d.h4](e||"InternalLog")}})),t}return(0,r.qU)(n,e),n.__ieDyn=1,n}(s._),g=t(4282),p=(0,g.H)({NotSet:0,Pii_DistinguishedName:1,Pii_GenericData:2,Pii_IPV4Address:3,Pii_IPv6Address:4,Pii_MailSubject:5,Pii_PhoneNumber:6,Pii_QueryString:7,Pii_SipAddress:8,Pii_SmtpAddress:9,Pii_Identity:10,Pii_Uri:11,Pii_Fqdn:12,Pii_IPV4AddressLegacy:13,Pii_IPv6ScrubLastHextets:14,Pii_DropValue:15,CustomerContent_GenericContent:32}),m=(0,g.H)({Normal:1,CostDeferred:2,RealTime:3,Immediate:4}),y=(0,g.H)({Unspecified:0,String:1,Int32:2,UInt32:3,Int64:4,UInt64:5,Double:6,Bool:7,Guid:8,DateTime:9}),I=(0,g.H)({Normal:1,Critical:2}),b=(0,g.H)({NONE:0,ERROR:1,WARNING:2,INFORMATION:3}),w=function(){function e(e){var n=this,t={},r=[],i=[];function a(e,n){var a,u=t[e];if(u&&(a=u[n]),!a&&null!==a){if((0,o.KgX)(e)&&(0,o.KgX)(n))if(i[d.oI]>0){for(var c=0;c<i[d.oI];c++)if(i[c][d.hF](e,n)){a={canHandle:!0,fieldHandler:i[c]};break}}else 0===r[d.oI]&&(a={canHandle:!0});if(!a&&null!==a)for(a=null,c=0;c<r[d.oI];c++)if(r[c][d.hF](e,n)){a={canHandle:!0,handler:r[c],fieldHandler:null};break}u||(u=t[e]={}),u[n]=a}return a}function u(e,n,t,r,i,a){if(e.handler)return e.handler.property(n,t,i,a);if(!(0,o.hXl)(i[d.QV])){if(!(4096&~r&&(0,f.m0)(i[d.QV])))return null;i[d.pF]=i[d.pF].toString()}return s(e.fieldHandler,n,t,r,i)}function c(e,n,t){return(0,f.yD)(t)?{value:t}:null}function s(e,t,r,i,a){if(a&&e){var u=e.getSanitizer(t,r,i,a[d.QV],a.propertyType);if(u)if(4===i){var l={},v=a[d.pF];(0,o.zav)(v,(function(n,i){var o=t+"."+r;if((0,f.yD)(i)){var a=c(0,0,i);(a=s(e,o,n,(0,f.cq)(i),a))&&(l[n]=a[d.pF])}})),a[d.pF]=l}else{var h={path:t,name:r,type:i,prop:a,sanitizer:n};a=u.call(n,h)}}return a}e&&i.push(e),n.clearCache=function(){t={}},n.addSanitizer=function(e){e&&((0,o.Nq2)(r,e)||r.push(e),t={})},n.addFieldSanitizer=function(e){e&&((0,o.Nq2)(i,e)||i.push(e),t={})},n[d.Rl]=function(e){if(e){var n=(0,o.rDm)(r,e);-1!==n&&(r.splice(n,1),t={}),(0,o.Iuo)(r,(function(n){n&&n[d.Rl]&&n[d.Rl](e)}))}},n[d.Mr]=function(e){if(e){var n=(0,o.rDm)(i,e);-1!==n&&(i.splice(n,1),t={}),(0,o.Iuo)(r,(function(n){n&&n[d.Mr]&&n[d.Mr](e)}))}},n.isEmpty=function(){return(0,o.R3R)(r)+(0,o.R3R)(i)===0},n[d.hF]=function(e,n){var t=a(e,n);return!!t&&t[d.nw]},n[d.pF]=function(e,n,t,r){var i=a(e,n);if(i&&i[d.nw]){if(!i||!i[d.nw])return null;if(i.handler)return i.handler[d.pF](e,n,t,r);if(!(0,o.KgX)(n)||(0,o.hXl)(t)||t===l.m5)return null;var s=null,v=(0,f.cq)(t);if(8192&~v)1!==v&&2!==v&&3!==v&&4096&~v?4===v&&(s=c(0,0,r?JSON.stringify(t):t)):s=c(0,0,t);else{var h=-8193&v;if(s=t,!(0,f.yD)(s[d.pF])||1!==h&&2!==h&&3!==h&&4096&~h)return null}if(s)return u(i,e,n,v,s,r)}return null},n.property=function(e,n,t,r){var i=a(e,n);if(!i||!i[d.nw])return null;if(!(0,o.KgX)(n)||(0,o.hXl)(t)||!(0,f.yD)(t[d.pF]))return null;var c=(0,f.cq)(t[d.pF]);return 0===c?null:u(i,e,n,c,t,r)}}return e.getFieldType=f.cq,e}(),C=100,S=(0,g.H)({DISABLED:0,CRITICAL:1,WARNING:2,DEBUG:3}),T=t(1356),_=t(8257),P=t(2317),E=t(3662),x=t(4875),D=t(6149),O=t(3673),k=t(9882),R=t(6535),A=t(7292),L=t(5664),M=t(5034),N=t(4276),F=t(836),U=t(1864),z=t(9147),H=t(856),X=t(1190),B=t(8969)},937:(e,n,t)=>{t.d(n,{S:()=>i,_0:()=>a,hj:()=>o,m5:()=>r});var r="",i="https://browser.events.data.microsoft.com/OneCollector/1.0/",o="version",a="properties"},4822:(e,n,t)=>{t.d(n,{EO:()=>w,F2:()=>L,Go:()=>_,H$:()=>M,Hh:()=>S,P$:()=>I,Rx:()=>l,TC:()=>T,UM:()=>P,WB:()=>D,X$:()=>x,cq:()=>A,ei:()=>N,g8:()=>y,gj:()=>E,h3:()=>C,m0:()=>O,u9:()=>R,wJ:()=>k,xE:()=>f,yD:()=>b});var r,i=t(269),o=t(7292),a=t(9882),u=t(5664),c=t(937),s=t(1739),l="4.3.4",f="1DS-Web-JS-"+l,d=u.Wy.hasOwnProperty,v="Microsoft_ApplicationInsights_BypassAjaxInstrumentation",h="withCredentials",g="timeout",p=((r={})[0]=0,r[2]=6,r[1]=1,r[3]=7,r[4098]=6,r[4097]=1,r[4099]=7,r),m=null,y=(0,i.Wtk)(),I=(0,i.Vdv)();function b(e){return!(e===c.m5||(0,i.hXl)(e))}function w(e){if(e){var n=(0,i.HzD)(e,"-");if(n>-1)return(0,i.ZWZ)(e,n)}return c.m5}function C(){return null===m&&(m=!(0,i.b07)(Uint8Array)&&!function(){var e=(0,i.w3n)();if(!(0,i.b07)(e)&&e.userAgent){var n=e.userAgent.toLowerCase();if((n[s.Sj]("safari")>=0||n[s.Sj]("firefox")>=0)&&n[s.Sj]("chrome")<0)return!0}return!1}()&&!(0,o.lV)()),m}function S(e){return!!(e&&(0,i.EtT)(e)&&e>=1&&e<=4)}function T(e,n,t){if(!n&&!b(n)||"string"!=typeof e)return null;var r=typeof n;if("string"===r||"number"===r||"boolean"===r||(0,i.cyL)(n))n={value:n};else if("object"!==r||d.call(n,"value")){if((0,i.hXl)(n[s.pF])||n[s.pF]===c.m5||!(0,i.KgX)(n[s.pF])&&!(0,i.EtT)(n[s.pF])&&!(0,i.Lmq)(n[s.pF])&&!(0,i.cyL)(n[s.pF]))return null}else n={value:t?JSON.stringify(n):n};if((0,i.cyL)(n[s.pF])&&!k(n[s.pF]))return null;if(!(0,i.hXl)(n[s.QV])){if((0,i.cyL)(n[s.pF])||!O(n[s.QV]))return null;n[s.pF]=n[s.pF].toString()}return n}function _(e,n,t){var r=-1;if(!(0,i.b07)(e))if(n>0&&(32===n?r=8192:n<=13&&(r=n<<5)),function(e){return e>=0&&e<=9}(t))-1===r&&(r=0),r|=t;else{var o=p[A(e)]||-1;-1!==r&&-1!==o?r|=o:6===o&&(r=o)}return r}function P(e,n,t){var r;return void 0===t&&(t=!0),e&&(r=e.get(n),t&&r&&decodeURIComponent&&(r=decodeURIComponent(r))),r||c.m5}function E(e){void 0===e&&(e="D");var n=(0,a.aq)();return"B"===e?n="{"+n+"}":"P"===e?n="("+n+")":"N"===e&&(n=n.replace(/-/g,c.m5)),n}function x(e,n,t,r,o){var a={},u=!1,c=0,l=arguments[s.oI],f=arguments;for((0,i.Lmq)(f[0])&&(u=f[0],c++);c<l;c++)e=f[c],(0,i.zav)(e,(function(e,n){u&&n&&(0,i.Gvm)(n)?(0,i.cyL)(n)?(a[e]=a[e]||[],(0,i.Iuo)(n,(function(n,t){n&&(0,i.Gvm)(n)?a[e][t]=x(!0,a[e][t],n):a[e][t]=n}))):a[e]=x(!0,a[e],n):a[e]=n}));return a}var D=i.UUD;function O(e){return 0===e||e>0&&e<=13||32===e}function k(e){return e[s.oI]>0}function R(e,n){var t=e;t[s.dg]=t[s.dg]||{},t[s.dg][s.Jg]=t[s.dg][s.Jg]||{},t[s.dg][s.Jg][n]=D()}function A(e){var n=0;if(null!=e){var t=typeof e;"string"===t?n=1:"number"===t?n=2:"boolean"===t?n=3:t===u._1&&(n=4,(0,i.cyL)(e)?(n=4096,e[s.oI]>0&&(n|=A(e[0]))):d.call(e,"value")&&(n=8192|A(e[s.pF])))}return n}function L(){return!!(0,i.zS2)("chrome")}function M(e,n,t,r,i,o){function a(e,n,t){try{e[n]=t}catch(e){}}void 0===r&&(r=!1),void 0===i&&(i=!1);var u=new XMLHttpRequest;return r&&a(u,v,r),t&&a(u,h,t),u.open(e,n,!i),t&&a(u,h,t),!i&&o&&a(u,g,o),u}function N(e){return e>0}},1739:(e,n,t)=>{t.d(n,{Jg:()=>f,Mr:()=>h,QV:()=>s,Rl:()=>v,Sj:()=>o,Uw:()=>i,dg:()=>a,h4:()=>u,hF:()=>d,mE:()=>r,nw:()=>g,oI:()=>l,pF:()=>c});var r="initialize",i="logger",o="indexOf",a="timings",u="pollInternalLogs",c="value",s="kind",l="length",f="processTelemetryStart",d="handleField",v="rmSanitizer",h="rmFieldSanitizer",g="canHandle"},8916:(e,n,t)=>{t.r(n),t.d(n,{BE_PROFILE:()=>o,NRT_PROFILE:()=>i,PostChannel:()=>Cn,RT_PROFILE:()=>r});var r="REAL_TIME",i="NEAR_REAL_TIME",o="BEST_EFFORT",a=t(659),u=t(8279),c=t(4822),s=t(269),l=t(8156),f=t(6149),d=t(4276),v=t(9749),h=t(2317),g=t(3673),p=t(3662),m=t(3775),y=t(8257),I=t(8205),b="",w="drop",C="requeue",S="no-cache, no-store",T="application/x-json-stream",_="cache-control",P="content-type",E="client-version",x="client-id",D="time-delta-to-apply-millis",O="upload-time",k="apikey",R="AuthMsaDeviceTicket",A="WebAuthToken",L="AuthXToken",M="msfpc",N="trace",F="user",U="allowRequestSending",z="firstRequestSent",H="shouldAddClockSkewHeaders",X="getClockSkewHeaderValue",B="setClockSkew",j="length",q="concat",V="iKey",K="count",W="events",Z="push",J="split",$="splice",G="toLowerCase",Q="hdrs",Y="useHdrs",ee="initialize",ne="setTimeoutOverride",te="clearTimeoutOverride",re="overrideEndpointUrl",ie="avoidOptions",oe="enableCompoundKey",ae="disableXhrSync",ue="disableFetchKeepAlive",ce="useSendBeacon",se="fetchCredentials",le="alwaysUseXhrOverride",fe="serializeOfflineEvt",de="getOfflineRequestDetails",ve="createPayload",he="createOneDSPayload",ge="payloadBlob",pe="headers",me="_thePayload",ye="urlString",Ie="batches",be="sendType",we="addHeader",Ce="canSendRequest",Se="sendQueuedRequests",Te="isCompletelyIdle",_e="setUnloading",Pe="resume",Ee="sendSynchronousBatch",xe="_transport",De="getWParam",Oe="isBeacon",ke="timings",Re="isTeardown",Ae="isSync",Le="data",Me="_sendReason",Ne="setKillSwitchTenants",Fe="_backOffTransmission",Ue="identifier",ze="eventsLimitInMem",He="autoFlushEventsLimit",Xe="baseData",Be="sendAttempt",je="latency",qe="sync";function Ve(e){var n=(e.ext||{}).intweb;return n&&(0,c.yD)(n[M])?n[M]:null}function Ke(e){for(var n=null,t=0;null===n&&t<e[j];t++)n=Ve(e[t]);return n}var We=function(){function e(n,t){var r=t?[][q](t):[],i=this,o=Ke(r);i[V]=function(){return n},i.Msfpc=function(){return o||b},i[K]=function(){return r[j]},i[W]=function(){return r},i.addEvent=function(e){return!!e&&(r[Z](e),o||(o=Ve(e)),!0)},i[J]=function(t,i){var a;if(t<r[j]){var u=r[j]-t;(0,s.hXl)(i)||(u=i<u?i:u),a=r[$](t,u),o=Ke(r)}return new e(n,a)}}return e.create=function(n,t){return new e(n,t)},e}(),Ze=t(7292),Je=t(856),$e=t(5664),Ge=function(){function e(){var n=!0,t=!0,r=!0,i="use-collector-delta",o=!1;(0,u.A)(e,this,(function(e){e[U]=function(){return n},e[z]=function(){r&&(r=!1,o||(n=!1))},e[H]=function(){return t},e[X]=function(){return i},e[B]=function(e){o||(e?(i=e,t=!0,o=!0):t=!1,n=!0)}}))}return e.__ieDyn=1,e}(),Qe=function(){function e(){var n={};(0,u.A)(e,this,(function(e){e[Ne]=function(e,t){if(e&&t)try{var r=(a=e[J](","),u=[],a&&(0,s.Iuo)(a,(function(e){u[Z]((0,s.EHq)(e))})),u);if("this-request-only"===t)return r;for(var i=1e3*parseInt(t,10),o=0;o<r[j];++o)n[r[o]]=(0,s.f0d)()+i}catch(e){return[]}var a,u;return[]},e.isTenantKilled=function(e){var t=n,r=(0,s.EHq)(e);return void 0!==t[r]&&t[r]>(0,s.f0d)()||(delete t[r],!1)}}))}return e.__ieDyn=1,e}();function Ye(e){var n,t=Math.floor(1200*Math.random())+2400;return n=Math.pow(2,e)*t,Math.min(n,6e5)}var en,nn=2e6,tn=Math.min(nn,65e3),rn="metadata",on="f",an=/\./,un=function(){function e(n,t,r,i,o,a){var f="data",d="baseData",v=!!i,h=!0,g=t,p={},m=!!a,y=o||c.Go;(0,u.A)(e,this,(function(e){function t(e,n,i,o,a,u,l){(0,s.zav)(e,(function(e,f){var d=null;if(f||(0,c.yD)(f)){var h=i,m=e,y=a,I=n;if(v&&!o&&an.test(e)){var b=e.split("."),w=b.length;if(w>1){y&&(y=y.slice());for(var C=0;C<w-1;C++){var S=b[C];I=I[S]=I[S]||{},h+="."+S,y&&y.push(S)}m=b[w-1]}}var T=o&&function(e,n){var t=p[e];return void 0===t&&(e.length>=7&&(t=(0,s.tGl)(e,"ext.metadata")||(0,s.tGl)(e,"ext.web")),p[e]=t),t}(h);if(d=!T&&g&&g.handleField(h,m)?g.value(h,m,f,r):(0,c.TC)(m,f,r)){var _=d.value;if(I[m]=_,u&&u(y,m,d),l&&"object"==typeof _&&!(0,s.cyL)(_)){var P=y;P&&(P=P.slice()).push(m),t(f,_,h+"."+m,o,P,u,l)}}}}))}e.createPayload=function(e,n,t,r,i,o){return{apiKeys:[],payloadBlob:b,overflow:null,sizeExceed:[],failedEvts:[],batches:[],numEvents:0,retryCnt:e,isTeardown:n,isSync:t,isBeacon:r,sendType:o,sendReason:i}},e.appendPayload=function(t,r,i){var o=t&&r&&!t.overflow;return o&&(0,l.r2)(n,(function(){return"Serializer:appendPayload"}),(function(){for(var n=r.events(),o=t.payloadBlob,a=t.numEvents,u=!1,c=[],l=[],f=t.isBeacon,d=f?65e3:3984588,v=f?tn:nn,h=0,g=0;h<n.length;){var p=n[h];if(p){if(a>=i){t.overflow=r.split(h);break}var m=e.getEventBlob(p);if(m&&m.length<=v){var y=m.length;if(o.length+y>d){t.overflow=r.split(h);break}o&&(o+="\n"),o+=m,++g>20&&((0,s.hKY)(o,0,1),g=0),u=!0,a++}else m?c.push(p):l.push(p),n.splice(h,1),h--}h++}if(c.length>0&&t.sizeExceed.push(We.create(r.iKey(),c)),l.length>0&&t.failedEvts.push(We.create(r.iKey(),l)),u){t.batches.push(r),t.payloadBlob=o,t.numEvents=a;var I=r.iKey();-1===(0,s.rDm)(t.apiKeys,I)&&t.apiKeys.push(I)}}),(function(){return{payload:t,theBatch:{iKey:r.iKey(),evts:r.events()},max:i}})),o},e.getEventBlob=function(e){try{return(0,l.r2)(n,(function(){return"Serializer.getEventBlob"}),(function(){var n={};n.name=e.name,n.time=e.time,n.ver=e.ver,n.iKey="o:"+(0,c.EO)(e.iKey);var r,i={};m||(r=function(e,n,t){!function(e,n,t,r,i){if(i&&n){var o=e(i.value,i.kind,i.propertyType);if(o>-1){var a=n[rn];a||(a=n[rn]={f:{}});var u=a[on];if(u||(u=a[on]={}),t)for(var c=0;c<t.length;c++){var l=t[c];u[l]||(u[l]={f:{}});var f=u[l][on];f||(f=u[l][on]={}),u=f}u=u[r]={},(0,s.cyL)(i.value)?u.a={t:o}:u.t=o}}}(y,i,e,n,t)});var o=e.ext;o&&(n.ext=i,(0,s.zav)(o,(function(e,n){t(n,i[e]={},"ext."+e,!0,null,null,!0)})));var a=n[f]={};a.baseType=e.baseType;var u=a[d]={};return t(e.baseData,u,d,!1,[d],r,h),t(e.data,a,f,!1,[],r,h),JSON.stringify(n)}),(function(){return{item:e}}))}catch(e){return null}}}))}return e.__ieDyn=1,e}();function cn(e,n){return{set:function(t,r){for(var i=[],o=2;o<arguments.length;o++)i[o-2]=arguments[o];return(0,s.vKV)([e,n],t,r,i)}}}var sn="sendAttempt",ln="?cors=true&"+P[G]()+"="+T,fn=((en={})[1]=C,en[100]=C,en[200]="sent",en[8004]=w,en[8003]=w,en),dn={},vn={};function hn(e,n,t){dn[e]=n,!1!==t&&(vn[n]=e)}function gn(e,n){var t=!1;if(e&&n){var r=(0,s.cGk)(e);if(r&&r[j]>0)for(var i=n[G](),o=0;o<r[j];o++){var a=r[o];if(a&&(0,s.v0u)(n,a)&&a[G]()===i){t=!0;break}}}return t}function pn(e,n,t,r){n&&t&&t[j]>0&&(r&&dn[n]?(e[Q][dn[n]]=t,e[Y]=!0):e.url+="&"+n+"="+t)}hn(R,R,!1),hn(E,E),hn(x,"Client-Id"),hn(k,k),hn(D,D),hn(O,O),hn(L,L);var mn=function(){function e(n,t,r,i){var o,a,f,d,h,p,y,I,C,R,A,L,N,F,G,Ue,ze,He,Xe,Be,je,qe,Ve,Ke,en,nn,tn,rn,on,an,dn,hn,mn=!1;(0,u.A)(e,this,(function(e){function u(e,n){try{return dn&&dn.getSenderInst(e,n)}catch(e){}return null}function yn(){try{return{enableSendPromise:!1,isOneDs:!0,disableCredentials:!1,fetchCredentials:hn,disableXhr:!1,disableBeacon:!mn,disableBeaconSync:!mn,disableFetchKeepAlive:je,timeWrapper:on,addNoResponse:Ve,senderOnCompleteCallBack:{xdrOnComplete:In,fetchOnComplete:bn,xhrOnComplete:wn,beaconOnRetry:Sn}}}catch(e){}return null}function In(e,n,t){var r=(0,g.Lo)(e);Cn(n,200,{},r),Mn(r)}function bn(e,n,t,r){var i={},o=e[pe];o&&o.forEach((function(e,n){i[n]=e})),function(e,t,r){Cn(n,e,t,r),Mn(r)}(e.status,i,t||b)}function wn(e,n,t){var r=(0,g.Lo)(e);Cn(n,e.status,(0,g.w3)(e,!0),r),Mn(r)}function Cn(e,n,t,r){try{e(n,t,r)}catch(e){(0,m.ZP)(y,2,518,(0,s.mmD)(e))}}function Sn(e,n,t){var r=200,i=e[me],o=e[ye]+(Ve?"&NoResponseBody=true":b);try{var a=(0,s.w3n)();if(i){var u=!!C.getPlugin("LocalStorage"),c=[],l=[];(0,s.Iuo)(i[Ie],(function(e){if(c&&e&&e[K]()>0)for(var n=e[W](),t=0;t<n[j];t++){if(!a.sendBeacon(o,F.getEventBlob(n[t]))){c[Z](e[J](t));break}l[Z](e[t])}else c[Z](e[J](0))})),l[j]>0&&(i.sentEvts=l),u||Nn(c,8003,i[be],!0)}else r=0}catch(e){(0,m.OG)(y,"Failed to send telemetry using sendBeacon API. Ex:"+(0,s.mmD)(e)),r=0}finally{Cn(n,r,{},b)}}function Tn(e){return 2===e||3===e}function _n(e){return ze&&Tn(e)&&(e=2),e}function Pn(){return!f&&h<t}function En(){var e=N;return N=[],e}function xn(e,n,t){var r=!1;return e&&e[j]>0&&!f&&I[n]&&F&&(r=0!==n||Pn()&&(t>0||d[U]())),r}function Dn(e){var n={};return e&&(0,s.Iuo)(e,(function(e,t){n[t]={iKey:e[V](),evts:e[W]()}})),n}function On(e,t,r,i,o){if(e&&0!==e[j])if(f)Nn(e,1,i);else{i=_n(i);try{var u=e,d=0!==i;(0,l.r2)(C,(function(){return"HttpManager:_sendBatches"}),(function(u){u&&(e=e.slice(0));for(var s=[],l=null,f=(0,c.WB)(),v=I[i]||(d?I[1]:I[0]),h=v&&v[xe],g=qe&&(ze||Tn(i)||3===h||v._isSync&&2===h);xn(e,i,t);){var p=e.shift();p&&p[K]()>0&&(a.isTenantKilled(p[V]())?s[Z](p):(l=l||F[ve](t,r,d,g,o,i),F.appendPayload(l,p,n)?null!==l.overflow&&(e=[l.overflow][q](e),l.overflow=null,An(l,f,(0,c.WB)(),o),f=(0,c.WB)(),l=null):(An(l,f,(0,c.WB)(),o),f=(0,c.WB)(),e=[p][q](e),l=null)))}l&&An(l,f,(0,c.WB)(),o),e[j]>0&&(N=e[q](N)),Nn(s,8004,i)}),(function(){return{batches:Dn(u),retryCount:t,isTeardown:r,isSynchronous:d,sendReason:o,useSendBeacon:Tn(i),sendType:i}}),!d)}catch(e){(0,m.ZP)(y,2,48,"Unexpected Exception sending batch: "+(0,s.mmD)(e))}}}function kn(e,n){var t={url:o,hdrs:{},useHdrs:!1};n?(t[Q]=(0,c.X$)(t[Q],L),t.useHdrs=(0,s.cGk)(t.hdrs)[j]>0):(0,s.zav)(L,(function(e,n){vn[e]?pn(t,vn[e],n,!1):(t[Q][e]=n,t[Y]=!0)})),pn(t,x,"NO_AUTH",n),pn(t,E,c.xE,n);var r=b;(0,s.Iuo)(e.apiKeys,(function(e){r[j]>0&&(r+=","),r+=e})),pn(t,k,r,n),pn(t,O,(0,s.f0d)().toString(),n);var i=function(e){for(var n=0;n<e.batches[j];n++){var t=e[Ie][n].Msfpc();if(t)return encodeURIComponent(t)}return b}(e);if((0,c.yD)(i)&&(t.url+="&ext.intweb.msfpc="+i),d[H]()&&pn(t,D,d[X](),n),C[De]){var a=C[De]();a>=0&&(t.url+="&w="+a)}for(var u=0;u<A[j];u++)t.url+="&"+A[u].name+"="+A[u].value;return t}function Rn(e,n,t){e[n]=e[n]||{},e[n][p.identifier]=t}function An(n,t,i,o){if(n&&n.payloadBlob&&n.payloadBlob[j]>0){var u=!!en,f=I[n.sendType];!Tn(n[be])&&n[Oe]&&2===n.sendReason&&(f=I[2]||I[3]||f);var v=He;(n.isBeacon||3===f[xe])&&(v=!1);var g=kn(n,v);v=v||g[Y];var b=(0,c.WB)();(0,l.r2)(C,(function(){return"HttpManager:_doPayloadSend"}),(function(){for(var I=0;I<n.batches[j];I++)for(var w=n[Ie][I][W](),E=0;E<w[j];E++){var x=w[E];if(G){var D=x[ke]=x[ke]||{};Rn(D,"sendEventStart",b),Rn(D,"serializationStart",t),Rn(D,"serializationCompleted",i)}x[sn]>0?x[sn]++:x[sn]=1}Nn(n[Ie],1e3+(o||0),n[be],!0);var O={data:n[ge],urlString:g.url,headers:g[Q],_thePayload:n,_sendReason:o,timeout:Xe,disableXhrSync:Be,disableFetchKeepAlive:je};v&&(gn(O[pe],_)||(O[pe][_]=S),gn(O[pe],P)||(O[pe][P]=T));var k=null;f&&(k=function(t){d[z]();var i=function(t,i){!function(n,t,i,o){var u,l=9e3,f=null,v=!1,g=!1;try{var m=!0;if(typeof n!==$e.bA){if(t){d[B](t["time-delta-millis"]);var y=t["kill-duration"]||t["kill-duration-seconds"];(0,s.Iuo)(a[Ne](t["kill-tokens"],y),(function(e){(0,s.Iuo)(i[Ie],(function(n){if(n[V]()===e){f=f||[];var t=n[J](0);i.numEvents-=t[K](),f[Z](t)}}))}))}if(200==n||204==n)return void(l=200);((u=n)>=300&&u<500&&429!=u||501==u||505==u||i.numEvents<=0)&&(m=!1),l=9e3+n%1e3}if(m){l=100;var I=i.retryCnt;0===i[be]&&(I<r?(v=!0,Ln((function(){0===i[be]&&h--,On(i[Ie],I+1,i[Re],ze?2:i[be],5)}),ze,Ye(I))):(g=!0,ze&&(l=8001)))}}finally{v||(d[B](),function(n,t,r,i){try{i&&p[Fe]();var o=n[Ie];200===t&&(o=n.sentEvts||n[Ie],i||n[Ae]||p._clearBackOff(),function(e){if(G){var n=(0,c.WB)();(0,s.Iuo)(e,(function(e){e&&e[K]()>0&&function(e,n){G&&(0,s.Iuo)(e,(function(e){Rn(e[ke]=e[ke]||{},"sendEventCompleted",n)}))}(e[W](),n)}))}}(o)),Nn(o,t,n[be],!0)}finally{0===n[be]&&(h--,5!==r&&e.sendQueuedRequests(n[be],r))}}(i,l,o,g)),Nn(f,8004,i[be])}}(t,i,n,o)},u=n[Re]||n[Ae];try{f.sendPOST(t,i,u),nn&&nn(O,t,u,n[Oe])}catch(e){(0,m.OG)(y,"Unexpected exception sending payload. Ex:"+(0,s.mmD)(e)),Cn(i,0,{})}}),(0,l.r2)(C,(function(){return"HttpManager:_doPayloadSend.sender"}),(function(){if(k)if(0===n[be]&&h++,u&&!n.isBeacon&&3!==f[xe]){var e={data:O[Le],urlString:O[ye],headers:(0,c.X$)({},O[pe]),timeout:O.timeout,disableXhrSync:O[ae],disableFetchKeepAlive:O[ue]},t=!1;(0,l.r2)(C,(function(){return"HttpManager:_doPayloadSend.sendHook"}),(function(){try{en(e,(function(e){t=!0,R||e[me]||(e[me]=e[me]||O[me],e[Me]=e[Me]||O[Me]),k(e)}),n.isSync||n[Re])}catch(e){t||k(O)}}))}else k(O)}))}),(function(){return{thePayload:n,serializationStart:t,serializationCompleted:i,sendReason:o}}),n[Ae])}n.sizeExceed&&n.sizeExceed[j]>0&&Nn(n.sizeExceed,8003,n[be]),n.failedEvts&&n.failedEvts[j]>0&&Nn(n.failedEvts,8002,n[be])}function Ln(e,n,t){n?e():on.set(e,t)}function Mn(e){var n=tn;try{for(var t=0;t<n[j];t++)try{n[t](e)}catch(e){(0,m.ZP)(y,1,519,"Response handler failed: "+e)}if(e){var r=JSON.parse(e);(0,c.yD)(r.webResult)&&(0,c.yD)(r.webResult[M])&&Ue.set("MSFPC",r.webResult[M],31536e3)}}catch(e){}}function Nn(e,n,t,r){if(e&&e[j]>0&&i){var o=i[(u=n,s=fn[u],(0,c.yD)(s)||(s="oth",u>=9e3&&u<=9999?s="rspFail":u>=8e3&&u<=8999?s=w:u>=1e3&&u<=1999&&(s="send")),s)];if(o){var a=0!==t;(0,l.r2)(C,(function(){return"HttpManager:_sendBatchesNotification"}),(function(){Ln((function(){try{o.call(i,e,n,a,t)}catch(e){(0,m.ZP)(y,1,74,"send request notification failed: "+e)}}),r||a,0)}),(function(){return{batches:Dn(e),reason:n,isSync:a,sendSync:r,sendType:t}}),!a)}}var u,s}!function(){var e;o=null,a=new Qe,f=!1,d=new Ge,mn=!1,h=0,p=null,y=null,I=null,C=null,R=!0,A=[],L={},N=[],F=null,G=!1,Ue=null,ze=!1,He=!1,Xe=e,Be=e,je=e,qe=e,Ve=e,Ke=[],en=e,nn=e,tn=[],rn=!1,on=cn(),an=!1,dn=null}(),e[ee]=function(e,n,t){rn||(C=n,Ue=n.getCookieMgr(),y=(p=t).diagLog(),(0,s.Yny)(Ke,(0,v.a)(e,(function(e){var r,i=e.cfg,a=e.cfg.extensionConfig[t.identifier];on=cn(a[ne],a[te]),(0,c.yD)(i.anonCookieName)?function(e,n,t){for(var r=0;r<e[j];r++)if(e[r].name===n)return void(e[r].value=t);e[Z]({name:n,value:t})}(A,"anoncknm",i.anonCookieName):function(e,n){for(var t=0;t<e[j];t++)if("anoncknm"===e[t].name)return void e[$](t,1)}(A),en=a.payloadPreprocessor,nn=a.payloadListener;var l=a.httpXHROverride,f=a[re]?a[re]:i.endpointUrl;o=f+ln,He=!!(0,s.b07)(a[ie])||!a[ie],G=!a.disableEventTimings;var d=a.valueSanitizer,v=a.stringifyObjects,h=!!i[oe];(0,s.b07)(a[oe])||(h=!!a[oe]),Xe=a.xhrTimeout,Be=!!a[ae],je=!!a[ue],Ve=!1!==a.addNoResponse,an=!!a.excludeCsMetaData,n.getPlugin("LocalStorage")&&(je=!0),mn=!(0,Ze.lV)(),F=new un(C,d,v,h,c.Go,an),(0,s.hXl)(a[ce])||(mn=!!a[ce]),a[se]&&(hn=a[se]);var p=yn();dn?dn.SetConfig(p):(dn=new Je.v)[ee](p,y);var b=l,w=a[le]?l:null,S=a[le]?l:null,T=[3,2];if(!l){R=!1;var _=[];(0,Ze.lV)()?(_=[2,1],T=[2,1,3]):_=[1,2,3],(l=u(_=(0,g.jL)(_,a.transports),!1))||(0,m.OG)(y,"No available transport to send events"),b=u(_,!0)}w||(w=u(T=(0,g.jL)(T,a.unloadTransports),!0)),qe=!R&&(mn&&(0,Ze.Uf)()||!je&&(0,Ze.R7)(!0)),(r={})[0]=l,r[1]=b||u([1,2,3],!0),r[2]=w||b||u([1],!0),r[3]=S||u([2,3],!0)||b||u([1],!0),I=r}))),rn=!0)},e.addResponseHandler=function(e){return tn[Z](e),{rm:function(){var n=tn.indexOf(e);n>=0&&tn[$](n,1)}}},e[fe]=function(e){try{if(F)return F.getEventBlob(e)}catch(e){}return b},e[de]=function(){try{return kn(F&&F[ve](0,!1,!1,!1,1,0),He)}catch(e){}return null},e[he]=function(e,t){try{var r=[];(0,s.Iuo)(e,(function(e){t&&(e=(0,g.hW)(e));var n=We.create(e[V],[e]);r[Z](n)}));for(var i=null;r[j]>0&&F;){var o=r.shift();o&&o[K]()>0&&(i=i||F[ve](0,!1,!1,!1,1,0),F.appendPayload(i,o,n))}var a=kn(i,He),u={data:i[ge],urlString:a.url,headers:a[Q],timeout:Xe,disableXhrSync:Be,disableFetchKeepAlive:je};return He&&(gn(u[pe],_)||(u[pe][_]=S),gn(u[pe],P)||(u[pe][P]=T)),u}catch(e){}return null},e._getDbgPlgTargets=function(){return[I[0],a,F,I,yn(),o]},e[we]=function(e,n){L[e]=n},e.removeHeader=function(e){delete L[e]},e[Ce]=function(){return Pn()&&d[U]()},e[Se]=function(e,n){(0,s.b07)(e)&&(e=0),ze&&(e=_n(e),n=2),xn(N,e,0)&&On(En(),0,!1,e,n||0)},e[Te]=function(){return!f&&0===h&&0===N[j]},e[_e]=function(e){ze=e},e.addBatch=function(e){if(e&&e[K]()>0){if(a.isTenantKilled(e[V]()))return!1;N[Z](e)}return!0},e.teardown=function(){N[j]>0&&On(En(),0,!0,2,2),(0,s.Iuo)(Ke,(function(e){e&&e.rm&&e.rm()})),Ke=[]},e.pause=function(){f=!0},e[Pe]=function(){f=!1,e[Se](0,4)},e[Ee]=function(e,n,t){e&&e[K]()>0&&((0,s.hXl)(n)&&(n=1),ze&&(n=_n(n),t=2),On([e],0,!1,n,t||0))}}))}return e.__ieDyn=1,e}(),yn=1e4,In="eventsDiscarded",bn=void 0,wn=(0,s.ZHX)({eventsLimitInMem:{isVal:c.ei,v:yn},immediateEventLimit:{isVal:c.ei,v:500},autoFlushEventsLimit:{isVal:c.ei,v:0},disableAutoBatchFlushLimit:!1,httpXHROverride:{isVal:function(e){return e&&e.sendPOST},v:bn},overrideInstrumentationKey:bn,overrideEndpointUrl:bn,disableTelemetry:!1,ignoreMc1Ms0CookieProcessing:!1,setTimeoutOverride:bn,clearTimeoutOverride:bn,payloadPreprocessor:bn,payloadListener:bn,disableEventTimings:bn,valueSanitizer:bn,stringifyObjects:bn,enableCompoundKey:bn,disableOptimizeObj:!1,fetchCredentials:bn,transports:bn,unloadTransports:bn,useSendBeacon:bn,disableFetchKeepAlive:bn,avoidOptions:!1,xhrTimeout:bn,disableXhrSync:bn,alwaysUseXhrOverride:!1,maxEventRetryAttempts:{isVal:s.EtT,v:6},maxUnloadEventRetryAttempts:{isVal:s.EtT,v:2},addNoResponse:bn,excludeCsMetaData:bn}),Cn=function(e){function n(){var t,a=e.call(this)||this;a.identifier="PostChannel",a.priority=1011,a.version="4.3.4";var y,b,w,C,S,T,_,P,E,x,D,O,k,L,M,U,z,H,X,B,G,Q,Y,re,ie,oe=!1,ae=[],ue=!1,ce=0,se=0,le={},ve=r;return(0,u.A)(n,a,(function(e,n){function a(){(0,f.Ds)(null,H),(0,f.sq)(null,H),(0,f.vF)(null,H)}function u(e){var n="";return e&&e[j]&&(0,s.Iuo)(e,(function(e){n&&(n+="\n"),n+=e})),n}function ge(e){var n="";try{ye(e),n=P[fe](e)}catch(e){}return n}function pe(e){"beforeunload"!==(e||(0,s.zkX)().event).type&&(M=!0,P[_e](M)),Ne(2,2)}function me(e){M=!1,P[_e](M)}function ye(e){e.ext&&e.ext[N]&&delete e.ext[N],e.ext&&e.ext[F]&&e.ext[F].id&&delete e.ext[F].id,L&&(e.ext=(0,g.hW)(e.ext),e[Xe]&&(e[Xe]=(0,g.hW)(e[Xe])),e[Le]&&(e[Le]=(0,g.hW)(e[Le])))}function be(e,n){if(e[Be]||(e[Be]=0),e[je]||(e[je]=1),ye(e),e[qe])if(T||ue)e[je]=3,e[qe]=!1;else if(P)return L&&(e=(0,g.hW)(e)),void P[Ee](We.create(e[V],[e]),!0===e[qe]?1:e[qe],3);var t=e[je],r=se,i=w;4===t&&(r=ce,i=b);var o=!1;if(r<i)o=!Ze(e,n);else{var a=1,u=20;4===t&&(a=4,u=1),o=!0,function(e,n,t,r){for(;t<=n;){var i=Ve(e,n,!0);if(i&&i[K]()>0){var o=i[J](0,r),a=o[K]();if(a>0)return 4===t?ce-=a:se-=a,on(In,[o],p.x.QueueFull),!0}t++}return Je(),!1}(e[V],e[je],a,u)&&(o=!Ze(e,n))}o&&rn(In,[e],p.x.QueueFull)}function xe(e,n,t){var r=$e(e,n,t);return P[Se](n,t),r}function Oe(){return se>0}function ke(){if(O>=0&&$e(O,0,k)&&P[Se](0,k),ce>0&&!S&&!ue){var e=le[ve][2];e>=0&&(S=Ae((function(){S=null,xe(4,0,1),ke()}),e))}var n=le[ve][1];!C&&!y&&n>=0&&!ue&&(Oe()?C=Ae((function(){C=null,xe(0===_?3:1,0,1),_++,_%=2,ke()}),n):_=0)}function Re(){t=null,oe=!1,ae=[],y=null,ue=!1,ce=0,b=500,se=0,w=yn,le={},ve=r,C=null,S=null,T=0,_=0,E={},x=0,Y=!1,D=0,O=-1,k=null,L=!0,M=!1,U=6,z=2,H=null,re=null,ie=!1,X=cn(),P=new mn(500,2,1,{requeue:nn,send:an,sent:un,drop:sn,rspFail:ln,oth:fn}),en(),E[4]={batches:[],iKeyMap:{}},E[3]={batches:[],iKeyMap:{}},E[2]={batches:[],iKeyMap:{}},E[1]={batches:[],iKeyMap:{}},dn()}function Ae(e,n){0===n&&T&&(n=1);var t=1e3;return T&&(t=Ye(T-1)),X.set(e,n*t)}function Me(){return null!==C&&(C.cancel(),C=null,_=0,!0)}function Ne(e,n){Me(),y&&(y.cancel(),y=null),ue||xe(1,e,n)}function Ve(e,n,t){var r=E[n];r||(r=E[n=1]);var i=r.iKeyMap[e];return!i&&t&&(i=We.create(e),r.batches[Z](i),r.iKeyMap[e]=i),i}function Ke(n,t){P[Ce]()&&!T&&(x>0&&se>x&&(t=!0),t&&null==y&&e.flush(n,(function(){}),20))}function Ze(e,n){L&&(e=(0,g.hW)(e));var t=e[je],r=Ve(e[V],t,!0);return!!r.addEvent(e)&&(4!==t?(se++,n&&0===e[Be]&&Ke(!e.sync,D>0&&r[K]()>=D)):ce++,!0)}function Je(){for(var e=0,n=0,t=function(t){var r=E[t];r&&r[Ie]&&(0,s.Iuo)(r[Ie],(function(r){4===t?e+=r[K]():n+=r[K]()}))},r=1;r<=4;r++)t(r);se=n,ce=e}function $e(n,t,r){var i=!1,o=0===t;return!o||P[Ce]()?(0,l.r2)(e.core,(function(){return"PostChannel._queueBatches"}),(function(){for(var e=[],t=4;t>=n;){var r=E[t];r&&r.batches&&r.batches[j]>0&&((0,s.Iuo)(r[Ie],(function(n){P.addBatch(n)?i=i||n&&n[K]()>0:e=e[q](n[W]()),4===t?ce-=n[K]():se-=n[K]()})),r[Ie]=[],r.iKeyMap={}),t--}e[j]>0&&rn(In,e,p.x.KillSwitch),i&&O>=n&&(O=-1,k=0)}),(function(){return{latency:n,sendType:t,sendReason:r}}),!o):(O=O>=0?Math.min(O,n):n,k=Math.max(k,r)),i}function Ge(e,n){xe(1,0,n),Je(),Qe((function(){e&&e(),ae[j]>0?y=Ae((function(){y=null,Ge(ae.shift(),n)}),0):(y=null,ke())}))}function Qe(e){P[Te]()?e():y=Ae((function(){y=null,Qe(e)}),.25)}function en(){(le={})[r]=[2,1,0],le[i]=[6,3,0],le[o]=[18,9,0]}function nn(n,t){var r=[],i=U;M&&(i=z),(0,s.Iuo)(n,(function(n){n&&n[K]()>0&&(0,s.Iuo)(n[W](),(function(n){n&&(n[qe]&&(n[je]=4,n[qe]=!1),n[Be]<i?((0,c.u9)(n,e[Ue]),be(n,!1)):r[Z](n))}))})),r[j]>0&&rn(In,r,p.x.NonRetryableStatus),M&&Ne(2,2)}function tn(n,t){var r=Q||{},i=r[n];if(i)try{i.apply(r,t)}catch(t){(0,m.ZP)(e.diagLog(),1,74,n+" notification failed: "+t)}}function rn(e,n){for(var t=[],r=2;r<arguments.length;r++)t[r-2]=arguments[r];n&&n[j]>0&&tn(e,[n][q](t))}function on(e,n){for(var t=[],r=2;r<arguments.length;r++)t[r-2]=arguments[r];n&&n[j]>0&&(0,s.Iuo)(n,(function(n){n&&n[K]()>0&&tn(e,[n.events()][q](t))}))}function an(e,n,t){e&&e[j]>0&&tn("eventsSendRequest",[n>=1e3&&n<=1999?n-1e3:0,!0!==t])}function un(e,n){on("eventsSent",e,n),ke()}function sn(e,n){on(In,e,n>=8e3&&n<=8999?n-8e3:p.x.Unknown)}function ln(e){on(In,e,p.x.NonRetryableStatus),ke()}function fn(e,n){on(In,e,p.x.Unknown),ke()}function dn(){D=G?0:Math.max(1500,w/6)}Re(),e._getDbgPlgTargets=function(){return[P,t]},e[ee]=function(r,i,o){(0,l.r2)(i,(function(){return"PostChannel:initialize"}),(function(){n[ee](r,i,o),Q=i.getNotifyMgr();try{H=(0,f.Hm)((0,d.Z)(e[Ue]),i.evtNamespace&&i.evtNamespace()),e._addHook((0,v.a)(r,(function(n){var r=n.cfg,o=(0,h.i8)(null,r,i);t=o.getExtCfg(e[Ue],wn),X=cn(t[ne],t[te]),L=!t.disableOptimizeObj&&(0,c.F2)(),B=t.ignoreMc1Ms0CookieProcessing,function(e){var n=e[De];e[De]=function(){var t=0;return B&&(t|=2),t|n.call(e)}}(i),w=t[ze],b=t.immediateEventLimit,x=t[He],U=t.maxEventRetryAttempts,z=t.maxUnloadEventRetryAttempts,G=t.disableAutoBatchFlushLimit,(0,s.$XS)(r.endpointUrl)?e.pause():ue&&e[Pe](),dn(),re=t.overrideInstrumentationKey,ie=!!t.disableTelemetry,Y&&a();var u=r.disablePageUnloadEvents||[];Y=(0,f.ee)(pe,u,H),Y=(0,f.Fc)(pe,u,H)||Y,Y=(0,f.oS)(me,r.disablePageShowEvents,H)||Y}))),P[ee](r,e.core,e)}catch(n){throw e.setInitialized(!1),n}}),(function(){return{theConfig:r,core:i,extensions:o}}))},e.processTelemetry=function(n,t){(0,c.u9)(n,e[Ue]),t=t||e._getTelCtx(t);var r=n;ie||oe||(re&&(r[V]=re),be(r,!0),M?Ne(2,2):ke()),e.processNext(r,t)},e.getOfflineSupport=function(){try{var e=P&&P[de]();if(P)return{getUrl:function(){return e?e.url:null},serialize:ge,batch:u,shouldProcess:function(e){return!ie},createPayload:function(e){return null},createOneDSPayload:function(e){if(P[he])return P[he](e,L)}}}catch(e){}return null},e._doTeardown=function(e,n){Ne(2,2),oe=!0,P.teardown(),a(),Re()},e.setEventQueueLimits=function(e,n){t[ze]=w=(0,c.ei)(e)?e:yn,t[He]=x=(0,c.ei)(n)?n:0,dn();var r=se>e;if(!r&&D>0)for(var i=1;!r&&i<=3;i++){var o=E[i];o&&o[Ie]&&(0,s.Iuo)(o[Ie],(function(e){e&&e[K]()>=D&&(r=!0)}))}Ke(!0,r)},e.pause=function(){Me(),ue=!0,P&&P.pause()},e[Pe]=function(){ue=!1,P&&P[Pe](),ke()},e._loadTransmitProfiles=function(e){Me(),en(),ve=r,ke(),(0,s.zav)(e,(function(e,n){var t=n[j];if(t>=2){var r=t>2?n[2]:0;if(n[$](0,t-2),n[1]<0&&(n[0]=-1),n[1]>0&&n[0]>0){var i=n[0]/n[1];n[0]=Math.ceil(i)*n[1]}r>=0&&n[1]>=0&&r>n[1]&&(r=n[1]),n[Z](r),le[e]=n}}))},e.flush=function(e,n,t){var r;if(void 0===e&&(e=!0),!ue)if(t=t||1,e)n||(r=(0,I.Qo)((function(e){n=e}))),null==y?(Me(),$e(1,0,t),y=Ae((function(){y=null,Ge(n,t)}),0)):ae[Z](n);else{var i=Me();xe(1,1,t),n&&n(),i&&ke()}return r},e.setMsaAuthTicket=function(e){P[we](R,e)},e.setAuthPluginHeader=function(e){P[we](A,e)},e.removeAuthPluginHeader=function(){P.removeHeader(A)},e.hasEvents=Oe,e._setTransmitProfile=function(e){ve!==e&&void 0!==le[e]&&(Me(),ve=e,ke())},(0,g.o$)(e,(function(){return P}),["addResponseHandler"]),e[Fe]=function(){T<4&&(T++,Me(),ke())},e._clearBackOff=function(){T&&(T=0,Me(),ke())}})),a}return(0,a.qU)(n,e),n.__ieDyn=1,n}(y.s)},4484:(e,n,t)=>{t.d(n,{F:()=>s,H:()=>c});var r=t(269),i=t(5025),o=t(5130),a=";",u="=";function c(e){if(!e)return{};var n=e[o.sY](a),t=(0,r.KTd)(n,(function(e,n){var t=n[o.sY](u);if(2===t[o.oI]){var r=t[0][o.OL](),i=t[1];e[r]=i}return e}),{});if((0,r.cGk)(t)[o.oI]>0){if(t.endpointsuffix){var c=t.location?t.location+".":"";t[o.zV]=t[o.zV]||"https://"+c+"dc."+t.endpointsuffix}t[o.zV]=t[o.zV]||i._G,(0,r.Cv9)(t[o.zV],"/")&&(t[o.zV]=t[o.zV].slice(0,-1))}return t}var s={parse:c}},5025:(e,n,t)=>{t.d(n,{R2:()=>s,_G:()=>u,jp:()=>o,ks:()=>l,tU:()=>i,wc:()=>c,xF:()=>r,ym:()=>a});var r="Microsoft_ApplicationInsights_BypassAjaxInstrumentation",i="sampleRate",o="ProcessLegacy",a="http.method",u="https://dc.services.visualstudio.com",c="/v2/track",s="not_specified",l="iKey"},7374:(e,n,t)=>{t.d(n,{eL:()=>i,iD:()=>a,uG:()=>o});var r=t(4282),i=(0,r.H)({LocalStorage:0,SessionStorage:1}),o=(0,r.H)({AI:0,AI_AND_W3C:1,W3C:2}),a=(0,r.H)({Normal:1,Critical:2})},87:(e,n,t)=>{t.d(n,{Dt:()=>a,Y8:()=>c,bb:()=>u,vv:()=>s});var r=t(269),i=t(5130),o="";function a(e,n){return void 0===n&&(n=!1),null==e?n:"true"===e.toString()[i.OL]()}function u(e){(isNaN(e)||e<0)&&(e=0),e=Math.round(e);var n=o+e%1e3,t=o+Math.floor(e/1e3)%60,r=o+Math.floor(e/6e4)%60,a=o+Math.floor(e/36e5)%24,u=Math.floor(e/864e5);return n=1===n[i.oI]?"00"+n:2===n[i.oI]?"0"+n:n,t=t[i.oI]<2?"0"+t:t,r=r[i.oI]<2?"0"+r:r,a=a[i.oI]<2?"0"+a:a,(u>0?u+".":o)+a+":"+r+":"+t+"."+n}function c(e,n){var t=null;return(0,r.Iuo)(e,(function(e){if(e.identifier===n)return t=e,-1})),t}function s(e,n,t,i,o){return!o&&(0,r.KgX)(e)&&("Script error."===e||"Script error"===e)}},8596:(e,n,t)=>{t.d(n,{o:()=>h});var r=t(659),i=t(3673);function o(e){var n="ai."+e+".";return function(e){return n+e}}var a=o("application"),u=o("device"),c=o("location"),s=o("operation"),l=o("session"),f=o("user"),d=o("cloud"),v=o("internal"),h=function(e){function n(){return e.call(this)||this}return(0,r.qU)(n,e),n}((0,i.SZ)({applicationVersion:a("ver"),applicationBuild:a("build"),applicationTypeId:a("typeId"),applicationId:a("applicationId"),applicationLayer:a("layer"),deviceId:u("id"),deviceIp:u("ip"),deviceLanguage:u("language"),deviceLocale:u("locale"),deviceModel:u("model"),deviceFriendlyName:u("friendlyName"),deviceNetwork:u("network"),deviceNetworkName:u("networkName"),deviceOEMName:u("oemName"),deviceOS:u("os"),deviceOSVersion:u("osVersion"),deviceRoleInstance:u("roleInstance"),deviceRoleName:u("roleName"),deviceScreenResolution:u("screenResolution"),deviceType:u("type"),deviceMachineName:u("machineName"),deviceVMName:u("vmName"),deviceBrowser:u("browser"),deviceBrowserVersion:u("browserVersion"),locationIp:c("ip"),locationCountry:c("country"),locationProvince:c("province"),locationCity:c("city"),operationId:s("id"),operationName:s("name"),operationParentId:s("parentId"),operationRootId:s("rootId"),operationSyntheticSource:s("syntheticSource"),operationCorrelationVector:s("correlationVector"),sessionId:l("id"),sessionIsFirst:l("isFirst"),sessionIsNew:l("isNew"),userAccountAcquisitionDate:f("accountAcquisitionDate"),userAccountId:f("accountId"),userAgent:f("userAgent"),userId:f("id"),userStoreRegion:f("storeRegion"),userAuthUserId:f("authUserId"),userAnonymousUserAcquisitionDate:f("anonUserAcquisitionDate"),userAuthenticatedUserAcquisitionDate:f("authUserAcquisitionDate"),cloudName:d("name"),cloudRole:d("role"),cloudRoleVer:d("roleVer"),cloudRoleInstance:d("roleInstance"),cloudEnvironment:d("environment"),cloudLocation:d("location"),cloudDeploymentUnit:d("deploymentUnit"),internalNodeName:v("nodeName"),internalSdkVersion:v("sdkVersion"),internalAgentVersion:v("agentVersion"),internalSnippet:v("snippet"),internalSdkSrc:v("sdkSrc")}))},9762:(e,n,t)=>{t.d(n,{O:()=>r});var r=(0,t(4282).H)({Verbose:0,Information:1,Warning:2,Error:3,Critical:4})},1575:(e,n,t)=>{t.d(n,{F:()=>i,O:()=>o});var r=t(8596),i={UserExt:"user",DeviceExt:"device",TraceExt:"trace",WebExt:"web",AppExt:"app",OSExt:"os",SessionExt:"ses",SDKExt:"sdk"},o=new r.o},5571:(e,n,t)=>{t.d(n,{G:()=>c});var r=t(6149),i=t(269),o=t(4276),a=t(5130);function u(e,n){(0,r.ML)(e,null,null,n)}function c(e){var n=(0,i.YEm)(),t=(0,i.w3n)(),c=!1,s=[],l=1;!t||(0,i.hXl)(t.onLine)||t.onLine||(l=2);var f=0,d=p(),v=(0,r.Hm)((0,o.Z)("OfflineListener"),e);try{if(g((0,i.zkX)())&&(c=!0),n){var h=n.body||n;h.ononline&&g(h)&&(c=!0)}}catch(e){c=!1}function g(e){var n=!1;return e&&(n=(0,r.mB)(e,"online",y,v))&&(0,r.mB)(e,"offline",I,v),n}function p(){return 2!==f&&2!==l}function m(){var e=p();d!==e&&(d=e,(0,i.Iuo)(s,(function(e){var n={isOnline:d,rState:l,uState:f};try{e(n)}catch(e){}})))}function y(){l=1,m()}function I(){l=2,m()}return{isOnline:function(){return d},isListening:function(){return c},unload:function(){var e=(0,i.zkX)();if(e&&c){if(u(e,v),n){var t=n.body||n;(0,i.b07)(t.ononline)||u(t,v)}c=!1}},addListener:function(e){return s[a.y5](e),{rm:function(){var n=s.indexOf(e);return n>-1?s.splice(n,1):void 0}}},setOnlineState:function(e){f=e,m()}}}},2910:(e,n,t)=>{t.d(n,{a:()=>r});var r=(0,t(4282).o)({requestContextHeader:[0,"Request-Context"],requestContextTargetKey:[1,"appId"],requestContextAppIdFormat:[2,"appId=cid-v1:"],requestIdHeader:[3,"Request-Id"],traceParentHeader:[4,"traceparent"],traceStateHeader:[5,"tracestate"],sdkContextHeader:[6,"Sdk-Context"],sdkContextHeaderAppIdRequest:[7,"appId"],requestContextHeaderLowerCase:[8,"request-context"]})},4658:(e,n,t)=>{t.d(n,{AN:()=>w,BW:()=>m,Dt:()=>T,Nu:()=>h,Se:()=>y,T9:()=>C,_M:()=>I,iw:()=>p,tm:()=>b,v7:()=>_,vH:()=>S,vh:()=>g});var r=t(269),i=t(3775),o=t(3673),a=t(7374),u=t(5130),c=void 0,s=void 0,l="";function f(){return m()?d(a.eL.LocalStorage):null}function d(e){try{if((0,r.hXl)((0,r.mS$)()))return null;var n=(new Date)[u.xE](),t=(0,r.zS2)(e===a.eL.LocalStorage?"localStorage":"sessionStorage"),i=l+n;t.setItem(i,n);var o=t.getItem(i)!==n;if(t[u.AZ](i),!o)return t}catch(e){}return null}function v(){return w()?d(a.eL.SessionStorage):null}function h(){c=!1,s=!1}function g(e){l=e||""}function p(){c=m(!0),s=w(!0)}function m(e){return(e||void 0===c)&&(c=!!d(a.eL.LocalStorage)),c}function y(e,n){var t=f();if(null!==t)try{return t.getItem(n)}catch(n){c=!1,(0,i.ZP)(e,2,1,"Browser failed read of local storage. "+(0,o.lL)(n),{exception:(0,r.mmD)(n)})}return null}function I(e,n,t){var a=f();if(null!==a)try{return a.setItem(n,t),!0}catch(n){c=!1,(0,i.ZP)(e,2,3,"Browser failed write to local storage. "+(0,o.lL)(n),{exception:(0,r.mmD)(n)})}return!1}function b(e,n){var t=f();if(null!==t)try{return t[u.AZ](n),!0}catch(n){c=!1,(0,i.ZP)(e,2,5,"Browser failed removal of local storage item. "+(0,o.lL)(n),{exception:(0,r.mmD)(n)})}return!1}function w(e){return(e||void 0===s)&&(s=!!d(a.eL.SessionStorage)),s}function C(){var e=[];return w()&&(0,r.zav)((0,r.zS2)("sessionStorage"),(function(n){e[u.y5](n)})),e}function S(e,n){var t=v();if(null!==t)try{return t.getItem(n)}catch(n){s=!1,(0,i.ZP)(e,2,2,"Browser failed read of session storage. "+(0,o.lL)(n),{exception:(0,r.mmD)(n)})}return null}function T(e,n,t){var a=v();if(null!==a)try{return a.setItem(n,t),!0}catch(n){s=!1,(0,i.ZP)(e,2,4,"Browser failed write to session storage. "+(0,o.lL)(n),{exception:(0,r.mmD)(n)})}return!1}function _(e,n){var t=v();if(null!==t)try{return t[u.AZ](n),!0}catch(n){s=!1,(0,i.ZP)(e,2,6,"Browser failed removal of session storage item. "+(0,o.lL)(n),{exception:(0,r.mmD)(n)})}return!1}},7358:(e,n,t)=>{t.d(n,{B:()=>r});var r=function(e,n){this.aiDataContract={baseType:1,baseData:1},this.baseType=e,this.baseData=n}},7975:(e,n,t)=>{t.d(n,{HQ:()=>g,Rr:()=>s,Vj:()=>h,Vk:()=>f,Vt:()=>d,_T:()=>p,lq:()=>c,pJ:()=>l,qW:()=>m,xP:()=>v,zx:()=>u});var r=t(269),i=t(3775),o=t(7292),a=t(5130);function u(e,n,t){var i=n[a.oI],o=c(e,n);if(o[a.oI]!==i){for(var u=0,s=o;void 0!==t[s];)u++,s=(0,r.P0f)(o,0,147)+m(u);o=s}return o}function c(e,n){var t;return n&&(n=(0,r.EHq)((0,r.oJg)(n)))[a.oI]>150&&(t=(0,r.P0f)(n,0,150),(0,i.ZP)(e,2,57,"name is too long.  It has been truncated to 150 characters.",{name:n},!0)),t||n}function s(e,n,t){var o;return void 0===t&&(t=1024),n&&(t=t||1024,(n=(0,r.EHq)((0,r.oJg)(n)))[a.oI]>t&&(o=(0,r.P0f)(n,0,t),(0,i.ZP)(e,2,61,"string value is too long. It has been truncated to "+t+" characters.",{value:n},!0))),o||n}function l(e,n){return p(e,n,2048,66)}function f(e,n){var t;return n&&n[a.oI]>32768&&(t=(0,r.P0f)(n,0,32768),(0,i.ZP)(e,2,56,"message is too long, it has been truncated to 32768 characters.",{message:n},!0)),t||n}function d(e,n){var t;if(n){var o=""+n;o[a.oI]>32768&&(t=(0,r.P0f)(o,0,32768),(0,i.ZP)(e,2,52,"exception is too long, it has been truncated to 32768 characters.",{exception:n},!0))}return t||n}function v(e,n){if(n){var t={};(0,r.zav)(n,(function(n,c){if((0,r.Gvm)(c)&&(0,o.Z)())try{c=(0,o.hm)()[a.Jj](c)}catch(n){(0,i.ZP)(e,2,49,"custom property is not valid",{exception:n},!0)}c=s(e,c,8192),n=u(e,n,t),t[n]=c})),n=t}return n}function h(e,n){if(n){var t={};(0,r.zav)(n,(function(n,r){n=u(e,n,t),t[n]=r})),n=t}return n}function g(e,n){return n?p(e,n,128,69)[a.xE]():n}function p(e,n,t,o){var u;return n&&(n=(0,r.EHq)((0,r.oJg)(n)))[a.oI]>t&&(u=(0,r.P0f)(n,0,t),(0,i.ZP)(e,2,o,"input is too long, it has been truncated to "+t+" characters.",{data:n},!0)),u||n}function m(e){var n="00"+e;return(0,r.hKY)(n,n[a.oI]-3)}},1062:(e,n,t)=>{t.d(n,{L:()=>u});var r=t(3673),i=t(5025),o=t(5130),a=t(7975),u=function(e,n,t){var u=this,c=this;c.ver=1,c.sampleRate=100,c.tags={},c[o.RS]=(0,a.Rr)(e,t)||i.R2,c.data=n,c.time=(0,r._u)(new Date),c.aiDataContract={time:1,iKey:1,name:1,sampleRate:function(){return 100===u.sampleRate?4:1},tags:1,data:1}}},3072:(e,n,t)=>{t.d(n,{J:()=>a});var r=t(5025),i=t(5130),o=t(7975),a=function(){function e(e,n,t,a){this.aiDataContract={ver:1,name:1,properties:0,measurements:0};var u=this;u.ver=2,u[i.RS]=(0,o.Rr)(e,n)||r.R2,u[i.$y]=(0,o.xP)(e,t),u[i.XA]=(0,o.Vj)(e,a)}return e.envelopeType="Microsoft.ApplicationInsights.{0}.Event",e.dataType="EventData",e}()},5397:(e,n,t)=>{t.d(n,{WJ:()=>w});var r=t(659),i=t(269),o=t(5025),a=t(5130),u=t(7975),c="error",s="stack",l="stackDetails",f="errorSrc",d="message",v="description";function h(e,n){var t=e;return t&&!(0,i.KgX)(t)&&(JSON&&JSON[a.Jj]?(t=JSON[a.Jj](e),!n||t&&"{}"!==t||(t=(0,i.Tnt)(e[a.xE])?e[a.xE]():""+e)):t=e+" - (Missing JSON.stringify)"),t||""}function g(e,n){var t=e;return e&&(t&&!(0,i.KgX)(t)&&(t=e[d]||e[v]||t),t&&!(0,i.KgX)(t)&&(t=h(t,!0)),e.filename&&(t=t+" @"+(e.filename||"")+":"+(e.lineno||"?")+":"+(e.colno||"?"))),n&&"String"!==n&&"Object"!==n&&"Error"!==n&&-1===(0,i.HzD)(t||"",n)&&(t=n+": "+t),t||""}function p(e){return e&&e.src&&(0,i.KgX)(e.src)&&e.obj&&(0,i.cyL)(e.obj)}function m(e){var n=e||"";(0,i.KgX)(n)||(n=(0,i.KgX)(n[s])?n[s]:""+n);var t=n[a.sY]("\n");return{src:n,obj:t}}function y(e){var n=null;if(e)try{if(e[s])n=m(e[s]);else if(e[c]&&e[c][s])n=m(e[c][s]);else if(e.exception&&e.exception[s])n=m(e.exception[s]);else if(p(e))n=e;else if(p(e[l]))n=e[l];else if((0,i.zkX)()&&(0,i.zkX)().opera&&e[d])n=function(e){for(var n=[],t=e[a.sY]("\n"),r=0;r<t[a.oI];r++){var i=t[r];t[r+1]&&(i+="@"+t[r+1],r++),n[a.y5](i)}return{src:e,obj:n}}(e[a.pM]);else if(e.reason&&e.reason[s])n=m(e.reason[s]);else if((0,i.KgX)(e))n=m(e);else{var t=e[d]||e[v]||"";(0,i.KgX)(e[f])&&(t&&(t+="\n"),t+=" from "+e[f]),t&&(n=m(t))}}catch(e){n=m(e)}return n||{src:"",obj:null}}function I(e){var n="";if(e&&!(n=e.typeName||e[a.RS]||""))try{var t=/function (.{1,200})\(/.exec(e.constructor[a.xE]());n=t&&t[a.oI]>1?t[1]:""}catch(e){}return n}function b(e){if(e)try{if(!(0,i.KgX)(e)){var n=I(e),t=h(e,!1);return t&&"{}"!==t||(e[c]&&(n=I(e=e[c])),t=h(e,!0)),0!==(0,i.HzD)(t,n)&&"String"!==n?n+":"+t:t}}catch(e){}return""+(e||"")}var w=function(){function e(e,n,t,r,o,c){this.aiDataContract={ver:1,exceptions:1,severityLevel:0,properties:0,measurements:0};var s=this;s.ver=2,function(e){try{if((0,i.Gvm)(e))return"ver"in e&&"exceptions"in e&&"properties"in e}catch(e){}return!1}(n)?(s[a.OK]=n[a.OK]||[],s[a.$y]=n[a.$y],s[a.XA]=n[a.XA],n[a.Ur]&&(s[a.Ur]=n[a.Ur]),n.id&&(s.id=n.id,n[a.$y].id=n.id),n[a.Fq]&&(s[a.Fq]=n[a.Fq]),(0,i.hXl)(n[a.r1])||(s[a.r1]=n[a.r1])):(t||(t={}),c&&(t.id=c),s[a.OK]=[new C(e,n,t)],s[a.$y]=(0,u.xP)(e,t),s[a.XA]=(0,u.Vj)(e,r),o&&(s[a.Ur]=o),c&&(s.id=c))}return e.CreateAutoException=function(e,n,t,r,i,o,u,c){var s,l=I(i||o||e);return(s={})[a.pM]=g(e,l),s.url=n,s.lineNumber=t,s.columnNumber=r,s.error=b(i||o||e),s.evt=b(o||e),s[a.qg]=l,s.stackDetails=y(u||i||o),s.errorSrc=c,s},e.CreateFromInterface=function(n,t,o,u){var c=t[a.OK]&&(0,i.W$7)(t[a.OK],(function(e){return C[a.vu](n,e)}));return new e(n,(0,r.Im)((0,r.Im)({},t),{exceptions:c}),o,u)},e.prototype.toInterface=function(){var e,n=this,t=n.exceptions,r=n.properties,o=n.measurements,u=n.severityLevel,c=n.problemGroup,s=n.id,l=n.isManual,f=t instanceof Array&&(0,i.W$7)(t,(function(e){return e.toInterface()}))||void 0;return(e={ver:"4.0"})[a.OK]=f,e.severityLevel=u,e.properties=r,e.measurements=o,e.problemGroup=c,e.id=s,e.isManual=l,e},e.CreateSimpleException=function(e,n,t,r,i,o){var u;return{exceptions:[(u={},u[a.lW]=!0,u.message=e,u.stack=i,u.typeName=n,u)]}},e.envelopeType="Microsoft.ApplicationInsights.{0}.Exception",e.dataType="ExceptionData",e.formatError=b,e}(),C=function(){function e(e,n,t){this.aiDataContract={id:0,outerId:0,typeName:1,message:1,hasFullStack:0,stack:0,parsedStack:2};var r=this;if(function(e){try{if((0,i.Gvm)(e))return"hasFullStack"in e&&"typeName"in e}catch(e){}return!1}(n))r[a.qg]=n[a.qg],r[a.pM]=n[a.pM],r[s]=n[s],r[a.on]=n[a.on]||[],r[a.lW]=n[a.lW];else{var f=n,d=f&&f.evt;(0,i.bJ7)(f)||(f=f[c]||d||f),r[a.qg]=(0,u.Rr)(e,I(f))||o.R2,r[a.pM]=(0,u.Vk)(e,g(n||f,r[a.qg]))||o.R2;var v=n[l]||y(n);r[a.on]=function(e){var n,t=e.obj;if(t&&t[a.oI]>0){n=[];var r=0,o=0;if((0,i.Iuo)(t,(function(e){var t=e[a.xE]();if(S.regex.test(t)){var i=new S(t,r++);o+=i[a.J$],n[a.y5](i)}})),o>32768)for(var u=0,c=n[a.oI]-1,s=0,l=u,f=c;u<c;){if((s+=n[u][a.J$]+n[c][a.J$])>32768){var d=f-l+1;n.splice(l,d);break}l=u,f=c,u++,c--}}return n}(v),(0,i.cyL)(r[a.on])&&(0,i.W$7)(r[a.on],(function(n){n[a.QE]=(0,u.Rr)(e,n[a.QE]),n[a.IE]=(0,u.Rr)(e,n[a.IE])})),r[s]=(0,u.Vt)(e,function(e){var n="";return e&&(e.obj?(0,i.Iuo)(e.obj,(function(e){n+=e+"\n"})):n=e.src||""),n}(v)),r.hasFullStack=(0,i.cyL)(r.parsedStack)&&r.parsedStack[a.oI]>0,t&&(t[a.qg]=t[a.qg]||r[a.qg])}}return e.prototype.toInterface=function(){var e,n=this,t=n[a.on]instanceof Array&&(0,i.W$7)(n[a.on],(function(e){return e.toInterface()}));return(e={id:n.id,outerId:n.outerId,typeName:n[a.qg],message:n[a.pM],hasFullStack:n[a.lW],stack:n[s]})[a.on]=t||void 0,e},e.CreateFromInterface=function(n,t){var o=t[a.on]instanceof Array&&(0,i.W$7)(t[a.on],(function(e){return S[a.vu](e)}))||t[a.on];return new e(n,(0,r.Im)((0,r.Im)({},t),{parsedStack:o}))},e}(),S=function(){function e(n,t){this.aiDataContract={level:1,method:1,assembly:0,fileName:0,line:0};var r=this;if(r[a.J$]=0,"string"==typeof n){var o=n;r[a.Av]=t,r[a.lx]="<no_method>",r[a.QE]=(0,i.EHq)(o),r[a.IE]="",r[a.h_]=0;var u=o.match(e.regex);u&&u[a.oI]>=5&&(r[a.lx]=(0,i.EHq)(u[2])||r[a.lx],r[a.IE]=(0,i.EHq)(u[4]),r[a.h_]=parseInt(u[5])||0)}else r[a.Av]=n[a.Av],r[a.lx]=n[a.lx],r[a.QE]=n[a.QE],r[a.IE]=n[a.IE],r[a.h_]=n[a.h_],r[a.J$]=0;r.sizeInBytes+=r.method[a.oI],r.sizeInBytes+=r.fileName[a.oI],r.sizeInBytes+=r.assembly[a.oI],r[a.J$]+=e.baseSize,r.sizeInBytes+=r.level.toString()[a.oI],r.sizeInBytes+=r.line.toString()[a.oI]}return e.CreateFromInterface=function(n){return new e(n,null)},e.prototype.toInterface=function(){var e=this;return{level:e[a.Av],method:e[a.lx],assembly:e[a.QE],fileName:e[a.IE],line:e[a.h_]}},e.regex=/^([\s]+at)?[\s]{0,50}([^\@\()]+?)[\s]{0,50}(\@|\()([^\(\n]+):([0-9]+):([0-9]+)(\)?)$/,e.baseSize=58,e}()},5014:(e,n,t)=>{t.d(n,{J:()=>u});var r=t(5025),i=t(5130),o=function(){this.aiDataContract={name:1,kind:0,value:1,count:0,min:0,max:0,stdDev:0},this.kind=0},a=t(7975),u=function(){function e(e,n,t,u,c,s,l,f,d){this.aiDataContract={ver:1,metrics:1,properties:0};var v=this;v.ver=2;var h=new o;h[i.F2]=u>0?u:void 0,h.max=isNaN(s)||null===s?void 0:s,h.min=isNaN(c)||null===c?void 0:c,h[i.RS]=(0,a.Rr)(e,n)||r.R2,h.value=t,h.stdDev=isNaN(l)||null===l?void 0:l,v.metrics=[h],v[i.$y]=(0,a.xP)(e,f),v[i.XA]=(0,a.Vj)(e,d)}return e.envelopeType="Microsoft.ApplicationInsights.{0}.Metric",e.dataType="MetricData",e}()},1448:(e,n,t)=>{t.d(n,{h:()=>u});var r=t(5025),i=t(87),o=t(5130),a=t(7975),u=function(){function e(e,n,t,u,c,s,l){this.aiDataContract={ver:1,name:0,url:0,duration:0,properties:0,measurements:0,id:0};var f=this;f.ver=2,f.id=(0,a.HQ)(e,l),f.url=(0,a.pJ)(e,t),f[o.RS]=(0,a.Rr)(e,n)||r.R2,isNaN(u)||(f[o.qd]=(0,i.bb)(u)),f[o.$y]=(0,a.xP)(e,c),f[o.XA]=(0,a.Vj)(e,s)}return e.envelopeType="Microsoft.ApplicationInsights.{0}.Pageview",e.dataType="PageviewData",e}()},4164:(e,n,t)=>{t.d(n,{H:()=>a});var r=t(5025),i=t(5130),o=t(7975),a=function(){function e(e,n,t,a,u,c,s){this.aiDataContract={ver:1,name:0,url:0,duration:0,perfTotal:0,networkConnect:0,sentRequest:0,receivedResponse:0,domProcessing:0,properties:0,measurements:0};var l=this;l.ver=2,l.url=(0,o.pJ)(e,t),l[i.RS]=(0,o.Rr)(e,n)||r.R2,l[i.$y]=(0,o.xP)(e,u),l[i.XA]=(0,o.Vj)(e,c),s&&(l.domProcessing=s.domProcessing,l[i.qd]=s[i.qd],l.networkConnect=s.networkConnect,l.perfTotal=s.perfTotal,l[i.fd]=s[i.fd],l.sentRequest=s.sentRequest)}return e.envelopeType="Microsoft.ApplicationInsights.{0}.PageviewPerformance",e.dataType="PageviewPerformanceData",e}()},1365:(e,n,t)=>{t.d(n,{A:()=>u});var r=t(87),i=t(2318),o=t(5130),a=t(7975),u=function(){function e(e,n,t,u,c,s,l,f,d,v,h,g){void 0===d&&(d="Ajax"),this.aiDataContract={id:1,ver:1,name:0,resultCode:0,duration:0,success:0,data:0,target:0,type:0,properties:0,measurements:0,kind:0,value:0,count:0,min:0,max:0,stdDev:0,dependencyKind:0,dependencySource:0,commandName:0,dependencyTypeName:0};var p=this;p.ver=2,p.id=n,p[o.qd]=(0,r.bb)(c),p.success=s,p.resultCode=l+"",p.type=(0,a.Rr)(e,d);var m=(0,i._U)(e,t,f,u);p.data=(0,a.pJ)(e,u)||m.data,p.target=(0,a.Rr)(e,m.target),v&&(p.target="".concat(p.target," | ").concat(v)),p[o.RS]=(0,a.Rr)(e,m[o.RS]),p[o.$y]=(0,a.xP)(e,h),p[o.XA]=(0,a.Vj)(e,g)}return e.envelopeType="Microsoft.ApplicationInsights.{0}.RemoteDependency",e.dataType="RemoteDependencyData",e}()},2445:(e,n,t)=>{t.d(n,{C:()=>a});var r=t(5025),i=t(5130),o=t(7975),a=function(){function e(e,n,t,a,u){this.aiDataContract={ver:1,message:1,severityLevel:0,properties:0};var c=this;c.ver=2,n=n||r.R2,c[i.pM]=(0,o.Vk)(e,n),c[i.$y]=(0,o.xP)(e,a),c[i.XA]=(0,o.Vj)(e,u),t&&(c[i.Ur]=t)}return e.envelopeType="Microsoft.ApplicationInsights.{0}.Message",e.dataType="MessageData",e}()},9354:(e,n,t)=>{t.d(n,{Gz:()=>l,M0:()=>v,PS:()=>d,cM:()=>c,k6:()=>f,wX:()=>s});var r=t(269),i=t(5130),o=(0,r.YEm)()||{},a=0,u=[null,null,null,null,null];function c(e){var n=a,t=u,r=t[n];return o.createElement?t[n]||(r=t[n]=o.createElement("a")):r={host:d(e,!0)},r.href=e,++n>=t[i.oI]&&(n=0),a=n,r}function s(e){var n,t=c(e);return t&&(n=t.href),n}function l(e){var n,t=c(e);return t&&(n=t[i.Ue]),n}function f(e,n){return e?e.toUpperCase()+" "+n:n}function d(e,n){var t=v(e,n)||"";if(t){var o=t.match(/(www\d{0,5}\.)?([^\/:]{1,256})(:\d{1,20})?/i);if(null!=o&&o[i.oI]>3&&(0,r.KgX)(o[2])&&o[2][i.oI]>0)return o[2]+(o[3]||"")}return t}function v(e,n){var t=null;if(e){var o=e.match(/(\w{1,150}):\/\/([^\/:]{1,256})(:\d{1,20})?/i);if(null!=o&&o[i.oI]>2&&(0,r.KgX)(o[2])&&o[2][i.oI]>0&&(t=o[2]||"",n&&o[i.oI]>2)){var a=(o[1]||"")[i.OL](),u=o[3]||"";("http"===a&&":80"===u||"https"===a&&":443"===u)&&(u=""),t+=u}}return t}},2318:(e,n,t)=>{t.d(n,{Ft:()=>w,Qu:()=>d,Rs:()=>g,Wt:()=>v,_U:()=>y,jj:()=>b,lt:()=>I,mD:()=>m,mp:()=>h,pg:()=>p});var r=t(269),i=t(1864),o=t(5025),a=t(2910),u=t(7975),c=t(9354),s=t(5130),l=[o._G+o.wc,"https://breeze.aimon.applicationinsights.io"+o.wc,"https://dc-int.services.visualstudio.com"+o.wc],f="cid-v1:";function d(e){return-1!==(0,r.rDm)(l,e[s.OL]())}function v(e){f=e}function h(){return f}function g(e,n,t){if(!n||e&&e.disableCorrelationHeaders)return!1;if(e&&e[s.Ol])for(var i=0;i<e.correlationHeaderExcludePatterns[s.oI];i++)if(e[s.Ol][i].test(n))return!1;var o=(0,c.cM)(n).host[s.OL]();if(!o||-1===(0,r.HzD)(o,":443")&&-1===(0,r.HzD)(o,":80")||(o=((0,c.M0)(n,!0)||"")[s.OL]()),(!e||!e.enableCorsCorrelation)&&o&&o!==t)return!1;var a,u=e&&e.correlationHeaderDomains;if(u&&((0,r.Iuo)(u,(function(e){var n=new RegExp(e.toLowerCase().replace(/\\/g,"\\\\").replace(/\./g,"\\.").replace(/\*/g,".*"));a=a||n.test(o)})),!a))return!1;var l=e&&e.correlationHeaderExcludedDomains;if(!l||0===l[s.oI])return!0;for(i=0;i<l[s.oI];i++)if(new RegExp(l[i].toLowerCase().replace(/\\/g,"\\\\").replace(/\./g,"\\.").replace(/\*/g,".*")).test(o))return!1;return o&&o[s.oI]>0}function p(e){if(e){var n=m(e,a.a[1]);if(n&&n!==f)return n}}function m(e,n){if(e)for(var t=e[s.sY](","),r=0;r<t[s.oI];++r){var i=t[r][s.sY]("=");if(2===i[s.oI]&&i[0]===n)return i[1]}}function y(e,n,t,r){var i,o=r,a=r;if(n&&n[s.oI]>0){var l=(0,c.cM)(n);if(i=l.host,!o)if(null!=l[s.Ue]){var f=0===l.pathname[s.oI]?"/":l[s.Ue];"/"!==f.charAt(0)&&(f="/"+f),a=l[s.Ue],o=(0,u.Rr)(e,t?t+" "+f:f)}else o=(0,u.Rr)(e,n)}else i=r,o=r;return{target:i,name:o,data:a}}function I(){var e=(0,r.FJj)();if(e&&e.now&&e.timing){var n=e.now()+e.timing.navigationStart;if(n>0)return n}return(0,r.f0d)()}function b(e,n){var t=null;return 0===e||0===n||(0,r.hXl)(e)||(0,r.hXl)(n)||(t=n-e),t}function w(e,n){var t=e||{};return{getName:function(){return t[s.RS]},setName:function(e){n&&n.setName(e),t[s.RS]=e},getTraceId:function(){return t.traceID},setTraceId:function(e){n&&n.setTraceId(e),(0,i.hX)(e)&&(t.traceID=e)},getSpanId:function(){return t.parentID},setSpanId:function(e){n&&n.setSpanId(e),(0,i.wN)(e)&&(t.parentID=e)},getTraceFlags:function(){return t.traceFlags},setTraceFlags:function(e){n&&n.setTraceFlags(e),t.traceFlags=e}}}},5130:(e,n,t)=>{t.d(n,{$e:()=>y,$y:()=>_,AZ:()=>s,Av:()=>N,C9:()=>m,Cx:()=>h,F2:()=>d,Fq:()=>O,IE:()=>L,J$:()=>E,Jj:()=>I,Jm:()=>v,OK:()=>S,OL:()=>o,Ol:()=>w,QE:()=>A,RS:()=>l,Ue:()=>b,Ur:()=>D,XA:()=>P,fd:()=>H,h_:()=>U,i9:()=>p,lW:()=>M,lx:()=>F,oI:()=>i,on:()=>T,pM:()=>f,qd:()=>z,qg:()=>x,r1:()=>k,sY:()=>r,up:()=>C,vu:()=>R,xE:()=>u,y5:()=>c,zV:()=>a,zw:()=>g});var r="split",i="length",o="toLowerCase",a="ingestionendpoint",u="toString",c="push",s="removeItem",l="name",f="message",d="count",v="preTriggerDate",h="disabled",g="interval",p="daysOfMonth",m="date",y="getUTCDate",I="stringify",b="pathname",w="correlationHeaderExcludePatterns",C="extensionConfig",S="exceptions",T="parsedStack",_="properties",P="measurements",E="sizeInBytes",x="typeName",D="severityLevel",O="problemGroup",k="isManual",R="CreateFromInterface",A="assembly",L="fileName",M="hasFullStack",N="level",F="method",U="line",z="duration",H="receivedResponse"},740:(e,n,t)=>{t.r(n),t.d(n,{AnalyticsPluginIdentifier:()=>H,BreezeChannelIdentifier:()=>z,ConfigurationManager:()=>_,ConnectionStringParser:()=>d.F,ContextTagKeys:()=>P.o,CtxTagKeys:()=>O.O,DEFAULT_BREEZE_ENDPOINT:()=>h._G,DEFAULT_BREEZE_PATH:()=>h.wc,Data:()=>S.B,DisabledPropertyName:()=>h.xF,DistributedTracingModes:()=>k.uG,Envelope:()=>g.L,Event:()=>p.J,EventPersistence:()=>k.iD,Exception:()=>m.WJ,Extensions:()=>O.F,HttpMethod:()=>h.ym,Metric:()=>y.J,PageView:()=>I.h,PageViewPerformance:()=>C.H,ProcessLegacy:()=>h.jp,PropertiesPluginIdentifier:()=>U,RemoteDependencyData:()=>b.A,RequestHeaders:()=>v.a,SampleRate:()=>h.tU,SeverityLevel:()=>T.O,TelemetryItemCreator:()=>D,ThrottleMgr:()=>f,Trace:()=>w.C,correlationIdCanIncludeCorrelationHeader:()=>r.Rs,correlationIdGetCorrelationContext:()=>r.pg,correlationIdGetCorrelationContextValue:()=>r.mD,correlationIdGetPrefix:()=>r.mp,correlationIdSetPrefix:()=>r.Wt,createDistributedTraceContextFromTrace:()=>r.Ft,createDomEvent:()=>M,createOfflineListener:()=>F.G,createTelemetryItem:()=>x,createTraceParent:()=>L.wk,dataSanitizeException:()=>E.Vt,dataSanitizeId:()=>E.HQ,dataSanitizeInput:()=>E._T,dataSanitizeKey:()=>E.lq,dataSanitizeKeyAndAddUniqueness:()=>E.zx,dataSanitizeMeasurements:()=>E.Vj,dataSanitizeMessage:()=>E.Vk,dataSanitizeProperties:()=>E.xP,dataSanitizeString:()=>E.Rr,dataSanitizeUrl:()=>E.pJ,dateTimeUtilsDuration:()=>r.jj,dateTimeUtilsNow:()=>r.lt,dsPadNumber:()=>E.qW,findAllScripts:()=>L.V5,findW3cTraceParent:()=>L.ef,formatTraceParent:()=>L.L0,getExtensionByName:()=>R.Y8,isBeaconApiSupported:()=>A.Uf,isCrossOriginError:()=>R.vv,isInternalApplicationInsightsEndpoint:()=>r.Qu,isSampledFlag:()=>L.N7,isValidSpanId:()=>L.wN,isValidTraceId:()=>L.hX,isValidTraceParent:()=>L.mJ,msToTimeSpan:()=>R.bb,parseConnectionString:()=>d.H,parseTraceParent:()=>L.ZI,strNotSpecified:()=>h.R2,stringToBoolOrDefault:()=>R.Dt,urlGetAbsoluteUrl:()=>N.wX,urlGetCompleteUrl:()=>N.k6,urlGetPathName:()=>N.Gz,urlParseFullHost:()=>N.M0,urlParseHost:()=>N.PS,urlParseUrl:()=>N.cM,utlCanUseLocalStorage:()=>s.BW,utlCanUseSessionStorage:()=>s.AN,utlDisableStorage:()=>s.Nu,utlEnableStorage:()=>s.iw,utlGetLocalStorage:()=>s.Se,utlGetSessionStorage:()=>s.vH,utlGetSessionStorageKeys:()=>s.T9,utlRemoveSessionStorage:()=>s.v7,utlRemoveStorage:()=>s.tm,utlSetLocalStorage:()=>s._M,utlSetSessionStorage:()=>s.Dt,utlSetStoragePrefix:()=>s.vh});var r=t(2318),i=t(269),o=t(3775),a=t(3673),u=t(9749),c=t(6535),s=t(4658),l=t(5130),f=function(e,n){var t,r,f,d,v,h,g,p=this,m=!1,y=!1;function I(e,n,i,o){if(m){var a=function(e){try{var n=b(e);return(0,c.Z1)(1e6)<=n.limit.samplingRate}catch(e){}return!1}(e);if(!a)return;var u=b(e),s=x(e),f=C(u,t,s),d=!1,h=0,g=D(e);try{f&&!g?(h=Math.min(u.limit.maxSendNumber,s[l.F2]+1),s[l.F2]=0,d=!0,v[e]=!0,s[l.Jm]=new Date):(v[e]=f,s[l.F2]+=1);var p=S(e);_(r,p,s);for(var y=0;y<h;y++)E(e,r,n,i)}catch(e){}return{isThrottled:d,throttleNum:h}}return o&&O(e)[l.y5]({msgID:e,message:n,severity:i}),null}function b(e){return f[e]||f[109]}function w(e,n){var t,r,o,a,u;try{var c=n||{},s={};s[l.Cx]=!!c[l.Cx];var d=c[l.zw]||{};y=(null==d?void 0:d.daysOfMonth)&&(null==d?void 0:d.daysOfMonth[l.oI])>0,s[l.zw]=(a=null===(o=(o=d)||{})||void 0===o?void 0:o.monthInterval,u=null==o?void 0:o.dayInterval,(0,i.hXl)(a)&&(0,i.hXl)(u)&&(o.monthInterval=3,y||(o[l.i9]=[28],y=!0)),o={monthInterval:null==o?void 0:o.monthInterval,dayInterval:null==o?void 0:o.dayInterval,daysOfMonth:null==o?void 0:o.daysOfMonth});var v={samplingRate:(null===(t=c.limit)||void 0===t?void 0:t.samplingRate)||100,maxSendNumber:(null===(r=c.limit)||void 0===r?void 0:r.maxSendNumber)||1};s.limit=v,f[e]=s}catch(e){}}function C(e,n,t){if(e&&!e[l.Cx]&&n&&(0,a.Gh)(t)){var r=T(),o=t[l.C9],u=e[l.zw],c=1;if(null==u?void 0:u.monthInterval){var s=12*(r.getUTCFullYear()-o.getUTCFullYear())+r.getUTCMonth()-o.getUTCMonth();c=P(u.monthInterval,0,s)}var f=1;if(y)f=(0,i.rDm)(u[l.i9],r[l.$e]());else if(null==u?void 0:u.dayInterval){var d=Math.floor((r.getTime()-o.getTime())/864e5);f=P(u.dayInterval,0,d)}return c>=0&&f>=0}return!1}function S(e,n){var t=(0,a.Gh)(n)?n:"";return e?"appInsightsThrottle"+t+"-"+e:null}function T(e){try{if(!e)return new Date;var n=new Date(e);if(!isNaN(n.getDate()))return n}catch(e){}return null}function _(e,n,t){try{return(0,s._M)(e,n,(0,i.EHq)(JSON[l.Jj](t)))}catch(e){}return!1}function P(e,n,t){return e<=0?1:t>=n&&(t-n)%e==0?Math.floor((t-n)/e)+1:-1}function E(e,n,t,r){(0,o.ZP)(n,r||1,e,t)}function x(e){try{var n=d[e];if(!n){var t=S(e,h);n=function(e,n,t){try{var r={date:T(),count:0};if(e){var i=JSON.parse(e);return{date:T(i[l.C9])||r[l.C9],count:i[l.F2]||r[l.F2],preTriggerDate:i.preTriggerDate?T(i[l.Jm]):void 0}}return _(n,t,r),r}catch(e){}return null}((0,s.Se)(r,t),r,t),d[e]=n}return d[e]}catch(e){}return null}function D(e){var n=v[e];if((0,i.hXl)(n)){n=!1;var t=x(e);t&&(n=function(e){try{if(e){var n=new Date;return e.getUTCFullYear()===n.getUTCFullYear()&&e.getUTCMonth()===n.getUTCMonth()&&e[l.$e]()===n[l.$e]()}}catch(e){}return!1}(t[l.Jm])),v[e]=n}return v[e]}function O(e){return g=g||{},(0,i.hXl)(g[e])&&(g[e]=[]),g[e]}r=(0,o.y0)(e),v={},d={},g={},f={},w(109),h=(0,a.Gh)(n)?n:"",e.addUnloadHook((0,u.a)(e.config,(function(e){var n=e.cfg;t=(0,s.BW)();var r=n.throttleMgrCfg||{};(0,i.zav)(r,(function(e,n){w(parseInt(e),n)}))}))),p._getDbgPlgTargets=function(){return[g]},p.getConfig=function(){return f},p.canThrottle=function(e){var n=x(e);return C(b(e),t,n)},p.isTriggered=function(e){return D(e)},p.isReady=function(){return m},p.flush=function(e){try{var n=O(e);if(n&&n[l.oI]>0){var t=n.slice(0);return g[e]=[],(0,i.Iuo)(t,(function(e){I(e.msgID,e[l.pM],e.severity,!1)})),!0}}catch(e){}return!1},p.flushAll=function(){try{if(g){var e=!0;return(0,i.zav)(g,(function(n){var t=p.flush(parseInt(n));e=e&&t})),e}}catch(e){}return!1},p.onReadyState=function(e,n){return void 0===n&&(n=!0),(m=!!(0,i.hXl)(e)||e)&&n?p.flushAll():null},p.sendMessage=function(e,n,t){return I(e,n,t,!0)}},d=t(4484),v=t(2910),h=t(5025),g=t(1062),p=t(3072),m=t(5397),y=t(5014),I=t(1448),b=t(1365),w=t(2445),C=t(4164),S=t(7358),T=t(9762),_=function(){function e(){}return e.getConfig=function(e,n,t,r){var o;return void 0===r&&(r=!1),o=t&&e[l.up]&&e[l.up][t]&&!(0,i.hXl)(e[l.up][t][n])?e[l.up][t][n]:e[n],(0,i.hXl)(o)?r:o},e}(),P=t(8596),E=t(7975);function x(e,n,t,r,o,u){var c;t=(0,E.Rr)(r,t)||h.R2,((0,i.hXl)(e)||(0,i.hXl)(n)||(0,i.hXl)(t))&&(0,i.$8)("Input doesn't contain all required fields");var s="";e[h.ks]&&(s=e[h.ks],delete e[h.ks]);var f=((c={})[l.RS]=t,c.time=(0,a._u)(new Date),c.iKey=s,c.ext=u||{},c.tags=[],c.data={},c.baseType=n,c.baseData=e,c);return(0,i.hXl)(o)||(0,i.zav)(o,(function(e,n){f.data[e]=n})),f}var D=function(){function e(){}return e.create=x,e}(),O=t(1575),k=t(7374),R=t(87),A=t(7292),L=t(1864);function M(e){var n=null;if((0,i.Tnt)(Event))n=new Event(e);else{var t=(0,i.YEm)();t&&t.createEvent&&(n=t.createEvent("Event")).initEvent(e,!0,!0)}return n}var N=t(9354),F=t(5571),U="AppInsightsPropertiesPlugin",z="AppInsightsChannelPlugin",H="ApplicationInsightsAnalytics"},2475:(e,n,t)=>{t.d(n,{DD:()=>c,Lx:()=>u,NU:()=>a});var r=t(269),i=t(6182);function o(e,n,t){return!e&&(0,r.hXl)(e)?n:(0,r.Lmq)(e)?e:"true"===(0,r.oJg)(e)[i.OL]()}function a(e){return{mrg:!0,v:e}}function u(e,n,t){return{fb:t,isVal:e,v:n}}function c(e,n){return{fb:n,set:o,v:!!e}}},991:(e,n,t)=>{t.d(n,{q:()=>c});var r=t(269),i=t(6182);function o(e){return e&&(0,r.Gvm)(e)&&(e.isVal||e.fb||(0,r.KhI)(e,"v")||(0,r.KhI)(e,"mrg")||(0,r.KhI)(e,"ref")||e.set)}function a(e,n,t){var o,a=t.dfVal||r.O9V;if(n&&t.fb){var u=t.fb;(0,r.cyL)(u)||(u=[u]);for(var c=0;c<u[i.oI];c++){var s=u[c],l=n[s];if(a(l)?o=l:e&&(a(l=e.cfg[s])&&(o=l),e.set(e.cfg,(0,r.oJg)(s),l)),a(o))break}}return!a(o)&&a(t.v)&&(o=t.v),o}function u(e,n,t){var c,s=t;return t&&o(t)&&(s=a(e,n,t)),s&&(o(s)&&(s=u(e,n,s)),(0,r.cyL)(s)?(c=[])[i.oI]=s[i.oI]:(0,r.QdQ)(s)&&(c={}),c&&((0,r.zav)(s,(function(t,r){r&&o(r)&&(r=u(e,n,r)),c[t]=r})),s=c)),s}function c(e,n,t,s){var l,f,d,v,h,g,p,m,y=s;o(y)?(l=y.isVal,f=y.set,g=y[i.XW],p=y[i.JQ],v=y.mrg,!(h=y.ref)&&(0,r.b07)(h)&&(h=!!v),d=a(e,n,y)):d=s,p&&e[i.JQ](n,t);var I=!0,b=n[t];!b&&(0,r.hXl)(b)||(m=b,I=!1,l&&m!==d&&!l(m)&&(m=d,I=!0),f&&(I=(m=f(m,d,n))===d)),I?m=d?u(e,n,d):d:((0,r.QdQ)(m)||(0,r.cyL)(d))&&v&&d&&((0,r.QdQ)(d)||(0,r.cyL)(d))&&(0,r.zav)(d,(function(n,t){c(e,m,n,t)})),e.set(n,t,m),h&&e.ref(n,t),g&&e[i.XW](n,t)}},9749:(e,n,t)=>{t.d(n,{e:()=>I,a:()=>b});var r,i=t(269),o=t(4276),a=t(6492),u=t(6182),c=t(991),s=t(9147),l=["push","pop","shift","unshift","splice"],f=function(e,n,t,r){e&&e[u.ih](3,108,"".concat(t," [").concat(n,"] failed - ")+(0,i.mmD)(r))};function d(e,n){var t=(0,i.kgX)(e,n);return t&&t.get}function v(e,n,t,r){if(n){var o=d(n,t);o&&o[e.prop]?n[t]=r:function(e,n,t,r){var o={n:t,h:[],trk:function(n){n&&n.fn&&(-1===(0,i.rDm)(o.h,n)&&o.h[u.y5](n),e.trk(n,o))},clr:function(e){var n=(0,i.rDm)(o.h,e);-1!==n&&o.h[u.Ic](n,1)}},c=!0,l=!1;function h(){c&&(l=l||(0,s.hF)(h,e,r),r&&!r[s.nM]&&l&&(r=g(e,r,t,"Converting")),c=!1);var n=e.act;return n&&o.trk(n),r}h[e.prop]={chng:function(){e.add(o)}},(0,i.vF1)(n,o.n,{g:h,s:function(p){if(r!==p){h[e.ro]&&!e.upd&&(0,s.If)("["+t+"] is read-only:"+(0,i.mmD)(n)),c&&(l=l||(0,s.hF)(h,e,r),c=!1);var m=l&&h[e.rf];if(l)if(m){(0,i.zav)(r,(function(e){r[e]=p?p[e]:a.HP}));try{(0,i.zav)(p,(function(n,t){v(e,r,n,t)})),p=r}catch(n){f((e.hdlr||{})[u.Uw],t,"Assigning",n),l=!1}}else r&&r[s.nM]&&(0,i.zav)(r,(function(n){var t=d(r,n);if(t){var i=t[e.prop];i&&i.chng()}}));if(p!==r){var y=p&&(0,s.hF)(h,e,p);!m&&y&&(p=g(e,p,t,"Converting")),r=p,l=y}e.add(o)}}})}(e,n,t,r)}return n}function h(e,n,t,r){if(n){var i=d(n,t),o=i&&!!i[e.prop],a=r&&r[0],c=r&&r[1],l=r&&r[2];if(!o){if(l)try{(0,s.V9)(n)}catch(n){f((e.hdlr||{})[u.Uw],t,"Blocking",n)}try{v(e,n,t,n[t]),i=d(n,t)}catch(n){f((e.hdlr||{})[u.Uw],t,"State",n)}}a&&(i[e.rf]=a),c&&(i[e.ro]=c),l&&(i[e.blkVal]=!0)}return n}function g(e,n,t,r){try{(0,i.zav)(n,(function(t,r){v(e,n,t,r)})),n[s.nM]||((0,i.UxO)(n,s.nM,{get:function(){return e[u.K0]}}),function(e,n,t){(0,i.cyL)(n)&&(0,i.Iuo)(l,(function(r){var i=n[r];n[r]=function(){for(var r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];var a=i[u.y9](this,r);return g(e,n,t,"Patching"),a}}))}(e,n,t))}catch(n){f((e.hdlr||{})[u.Uw],t,r,n)}return n}var p="[[ai_",m="]]";function y(e,n,t){var a,l=(0,s.QA)(n);if(l)return l;var d,y=(0,o.Z)("dyncfg",!0),I=n&&!1!==t?n:(0,s.Dy)(n),b=((a={uid:null,cfg:I})[u.Uw]=e,a[u.zs]=function(){d[u.zs]()},a.set=function(n,t,r){try{n=v(d,n,t,r)}catch(n){f(e,t,"Setting value",n)}return n[t]},a[u.h0]=function(e,n){return n&&(0,i.zav)(n,(function(n,t){(0,c.q)(b,e,n,t)})),e},a[u.x6]=function(e){return function(e,n){var t={fn:n,rm:function(){t.fn=null,e=null,n=null}};return(0,i.vF1)(t,"toJSON",{v:function(){return"WatcherHandler"+(t.fn?"":"[X]")}}),e.use(t,n),t}(d,e)},a.ref=function(e,n){var t;return h(d,e,n,(t={},t[0]=!0,t))[n]},a[u.XW]=function(e,n){var t;return h(d,e,n,(t={},t[1]=!0,t))[n]},a[u.JQ]=function(e,n){var t;return h(d,e,n,(t={},t[2]=!0,t))[n]},a._block=function(e,n){d.use(null,(function(t){var r=d.upd;try{(0,i.b07)(n)||(d.upd=n),e(t)}finally{d.upd=r}}))},a);return(0,i.vF1)(b,"uid",{c:!1,e:!1,w:!1,v:y}),g(d=function(e){var n,t,o=(0,i.jjc)(p+"get"+e.uid+m),a=(0,i.jjc)(p+"ro"+e.uid+m),c=(0,i.jjc)(p+"rf"+e.uid+m),s=(0,i.jjc)(p+"blkVal"+e.uid+m),l=(0,i.jjc)(p+"dtl"+e.uid+m),f=null,d=null;function v(n,r){var o=t.act;try{t.act=n,n&&n[l]&&((0,i.Iuo)(n[l],(function(e){e.clr(n)})),n[l]=[]),r({cfg:e.cfg,set:e.set.bind(e),setDf:e[u.h0].bind(e),ref:e.ref.bind(e),rdOnly:e[u.XW].bind(e)})}catch(n){var a=e[u.Uw];throw a&&a[u.ih](1,107,(0,i.mmD)(n)),n}finally{t.act=o||null}}function h(){if(f){var e=f;f=null,d&&d[u._w](),d=null;var n=[];if((0,i.Iuo)(e,(function(e){if(e&&(e[l]&&((0,i.Iuo)(e[l],(function(n){n.clr(e)})),e[l]=null),e.fn))try{v(e,e.fn)}catch(e){n[u.y5](e)}})),f)try{h()}catch(e){n[u.y5](e)}n[u.oI]>0&&function(e,n){r||(r=(0,i.aqQ)("AggregationError",(function(e,n){n[u.oI]>1&&(e.errors=n[1])})));var t="Watcher error(s): ";throw(0,i.Iuo)(n,(function(e,n){t+="\n".concat(n," > ").concat((0,i.mmD)(e))})),new r(t,n||[])}(0,n)}}return(n={prop:o,ro:a,rf:c})[u.JQ]=s,n[u.K0]=e,n.add=function(e){if(e&&e.h[u.oI]>0){f||(f=[]),d||(d=(0,i.dRz)((function(){d=null,h()}),0));for(var n=0;n<e.h[u.oI];n++){var t=e.h[n];t&&-1===(0,i.rDm)(f,t)&&f[u.y5](t)}}},n[u.zs]=h,n.use=v,n.trk=function(e,n){if(e){var t=e[l]=e[l]||[];-1===(0,i.rDm)(t,n)&&t[u.y5](n)}},t=n}(b),I,"config","Creating"),b}function I(e,n,t,r){var i=y(t,e||{},r);return n&&i[u.h0](i.cfg,n),i}function b(e,n,t){var r=e[s.nM]||e;return!r.cfg||r.cfg!==e&&r.cfg[s.nM]!==r?(function(e,n){e?(e[u.on](n),e[u.ih](2,108,n)):(0,s.If)(n)}(t,a.xW+(0,i.mmD)(e)),I(e,null,t)[u.x6](n)):r[u.x6](n)}},9147:(e,n,t)=>{t.d(n,{Dy:()=>c,Hf:()=>f,If:()=>v,QA:()=>s,V9:()=>l,hF:()=>d,nM:()=>o});var r=t(269),i=t(6182),o=(0,r.eCG)("[[ai_dynCfg_1]]"),a=(0,r.eCG)("[[ai_blkDynCfg_1]]"),u=(0,r.eCG)("[[ai_frcDynCfg_1]]");function c(e){var n;return e&&((0,r.cyL)(e)?(n=[])[i.oI]=e[i.oI]:(0,r.QdQ)(e)&&(n={}),n)?((0,r.zav)(e,(function(e,t){n[e]=c(t)})),n):e}function s(e){if(e){var n=e[o]||e;if(n.cfg&&(n.cfg===e||n.cfg[o]===n))return n}return null}function l(e){if(e&&((0,r.QdQ)(e)||(0,r.cyL)(e)))try{e[a]=!0}catch(e){}return e}function f(e){if(e)try{e[u]=!0}catch(e){}return e}function d(e,n,t){var i=!1;return t&&!e[n.blkVal]&&((i=t[u])||t[a]||(i=(0,r.QdQ)(t)||(0,r.cyL)(t))),i}function v(e){(0,r.zkd)("InvalidAccess:"+e)}},4282:(e,n,t)=>{t.d(n,{H:()=>i,o:()=>o});var r=t(269),i=r.WSA,o=r.fn0},3662:(e,n,t)=>{t.d(n,{x:()=>i});var r=t(4282),i=(0,r.H)({Unknown:0,NonRetryableStatus:1,InvalidEvent:2,SizeLimitExceeded:3,KillSwitch:4,QueueFull:5});(0,r.H)({Unknown:0,NonRetryableStatus:1,CleanStorage:2,MaxInStorageTimeExceeded:3})},4875:(e,n,t)=>{t.d(n,{f:()=>r});var r=(0,t(4282).H)({NONE:0,PENDING:3,INACTIVE:1,ACTIVE:2})},2774:(e,n,t)=>{t.d(n,{_:()=>A});var r,i=t(659),o=t(8279),a=t(8205),u=t(269),c=t(9749),s=t(4875),l=t(6182),f=t(4013),d=t(7847),v=t(5034),h=t(4276),g=t(7867),p=t(3775),m=t(3673),y=t(6492),I=t(1356),b=t(8156),w=t(2317),C=t(380),S=function(e){function n(){var t,r,i=e.call(this)||this;function a(){t=0,r=[]}return i.identifier="TelemetryInitializerPlugin",i.priority=199,a(),(0,o.A)(n,i,(function(e,n){e.addTelemetryInitializer=function(e){return function(e,n,t){var r={id:n,fn:t};return(0,u.Yny)(e,r),{remove:function(){(0,u.Iuo)(e,(function(n,t){if(n.id===r.id)return e[l.Ic](t,1),-1}))}}}(r,t++,e)},e[y.qT]=function(n,t){(function(e,n,t){for(var r=!1,i=e[l.oI],o=0;o<i;++o){var a=e[o];if(a)try{if(!1===a.fn[l.y9](null,[n])){r=!0;break}}catch(e){(0,p.ZP)(t,2,64,"Telemetry initializer failed: "+(0,m.lL)(e),{exception:(0,u.mmD)(e)},!0)}}return!r})(r,n,t?t[l.e4]():e[l.e4]())&&e[l.$5](n,t)},e[l.tn]=function(){a()}})),i}return(0,i.qU)(n,e),n.__ieDyn=1,n}(t(8257).s),T=t(836),_=t(8969),P="Plugins must provide initialize method",E="SDK is still unloading...",x=(0,u.ZHX)(((r={cookieCfg:{}})[y.jy]={rdOnly:!0,ref:!0,v:[]},r[y.LZ]={rdOnly:!0,ref:!0,v:[]},r[y.Bw]={ref:!0,v:{}},r[y.Yd]=y.HP,r.loggingLevelConsole=0,r.diagnosticLogInterval=y.HP,r));function D(e,n){return new b.NS(n)}function O(e,n){var t=!1;return(0,u.Iuo)(n,(function(n){if(n===e)return t=!0,-1})),t}function k(e,n,t,r){t&&(0,u.zav)(t,(function(t,i){r&&(0,u.QdQ)(i)&&(0,u.QdQ)(n[t])&&k(e,n[t],i,r),r&&(0,u.QdQ)(i)&&(0,u.QdQ)(n[t])?k(e,n[t],i,r):e.set(n,t,i)}))}function R(e,n){var t=null,r=-1;return(0,u.Iuo)(e,(function(e,i){if(e.w===n)return t=e,r=i,-1})),{i:r,l:t}}var A=function(){function e(){var n,t,r,A,L,M,N,F,U,z,H,X,B,j,q,V,K,W,Z,J,$,G,Q,Y,ee,ne,te,re,ie,oe,ae,ue;(0,o.A)(e,this,(function(e){function o(){ie=!0,(0,u.hXl)($)?(ne=s.f[l.Yq],(0,p.ZP)(r,1,112,"ikey can't be resolved from promises")):ne=s.f.ACTIVE,ce()}function ce(){t&&(e.releaseQueue(),e[l.h4]())}function se(e){return oe&&oe[l.XM]||ue||(e||r&&r.queue[l.oI]>0)&&(ae||(ae=!0,Ce(n[l.x6]((function(e){var n=e.cfg.diagnosticLogInterval;n&&n>0||(n=1e4);var t=!1;oe&&(t=oe[l.XM],oe[l._w]()),(oe=(0,u.AHH)(me,n)).unref(),oe[l.XM]=t})))),oe[l.XM]=!0),oe}function le(){var e={};Y=[];var n=function(n){n&&(0,u.Iuo)(n,(function(n){if(n[l.Ju]&&n[l.s]&&!e[n.identifier]){var t=n[l.Ju]+"="+n[l.s];Y[l.y5](t),e[n.identifier]=n}}))};n(X),H&&(0,u.Iuo)(H,(function(e){n(e)})),n(z)}function fe(){t=!1,(n=(0,c.e)({},x,e[l.Uw])).cfg[l.Bl]=1,(0,u.vF1)(e,"config",{g:function(){return n.cfg},s:function(n){e.updateCfg(n,!1)}}),(0,u.vF1)(e,"pluginVersionStringArr",{g:function(){return Y||le(),Y}}),(0,u.vF1)(e,"pluginVersionString",{g:function(){return ee||(Y||le(),ee=Y.join(";")),ee||y.m5}}),(0,u.vF1)(e,"logger",{g:function(){return r||(r=new p.wq(n.cfg),n[l.Uw]=r),r},s:function(e){n[l.Uw]=e,r!==e&&((0,f.K)(r,!1),r=e)}}),e[l.Uw]=new p.wq(n.cfg),Q=[];var i=e.config[y.jy]||[];i.splice(0,i[l.oI]),(0,u.Yny)(i,Q),j=new S,A=[],(0,f.K)(L,!1),L=null,M=null,N=null,(0,f.K)(F,!1),F=null,U=null,z=[],H=null,X=null,B=!1,q=null,V=(0,h.Z)("AIBaseCore",!0),K=(0,T.P)(),J=null,$=null,W=(0,_.w)(),G=[],ee=null,Y=null,ue=!1,oe=null,ae=!1,ne=0,te=null,re=null,ie=!1}function de(){var t=(0,w.i8)(ge(),n.cfg,e);return t[l.by](se),t}function ve(n){var t=function(e,n,t){var r,i=[],o=[],a={};return(0,u.Iuo)(t,(function(t){((0,u.hXl)(t)||(0,u.hXl)(t[l.mE]))&&(0,u.$8)(P);var r=t[y.Vo],c=t[l.Ju];t&&r&&((0,u.hXl)(a[r])?a[r]=c:(0,p.OG)(e,"Two extensions have same priority #"+r+" - "+a[r]+", "+c)),!r||r<n?i[l.y5](t):o[l.y5](t)})),(r={})[y.eT]=i,r[y.LZ]=o,r}(e[l.Uw],d.i,z);U=null,ee=null,Y=null,X=(H||[])[0]||[],X=(0,C.Xc)((0,u.Yny)(X,t[y.LZ]));var r=(0,u.Yny)((0,C.Xc)(t[y.eT]),X);Q=(0,u.N6t)(r);var i=e.config[y.jy]||[];i.splice(0,i[l.oI]),(0,u.Yny)(i,Q);var o=de();X&&X[l.oI]>0&&(0,C.pI)(o[l.$o](X),r),(0,C.pI)(o,r),n&&Ie(n)}function he(e){var n=null,t=null,r=[];return(0,u.Iuo)(Q,(function(n){if(n[l.Ju]===e&&n!==j)return t=n,-1;n.getChannel&&r[l.y5](n)})),!t&&r[l.oI]>0&&(0,u.Iuo)(r,(function(n){if(!(t=n.getChannel(e)))return-1})),t&&(n={plugin:t,setEnabled:function(e){(0,C.Cr)(t)[y.Hr]=!e},isEnabled:function(){var e=(0,C.Cr)(t);return!e[l.Ik]&&!e[y.Hr]},remove:function(e,n){var r;void 0===e&&(e=!0);var i=[t],o=((r={reason:1})[l.tI]=e,r);pe(i,o,(function(e){e&&ve({reason:32,removed:i}),n&&n(e)}))}}),n}function ge(){if(!U){var t=(Q||[]).slice();-1===(0,u.rDm)(t,j)&&t[l.y5](j),U=(0,w.PV)((0,C.Xc)(t),n.cfg,e)}return U}function pe(t,r,i){if(t&&t[l.oI]>0){var o=(0,w.PV)(t,n.cfg,e),a=(0,w.tS)(o,e);a[l.by]((function(){var e=!1,n=[];(0,u.Iuo)(z,(function(r,i){O(r,t)?e=!0:n[l.y5](r)})),z=n,ee=null,Y=null;var r=[];H&&((0,u.Iuo)(H,(function(n,i){var o=[];(0,u.Iuo)(n,(function(n){O(n,t)?e=!0:o[l.y5](n)})),r[l.y5](o)})),H=r),i&&i(e),se()})),a[l.$5](r)}else i(!1)}function me(){if(r&&r.queue){var n=r.queue.slice(0);r.queue[l.oI]=0,(0,u.Iuo)(n,(function(n){var t,r=((t={})[l.RS]=q||"InternalMessageId: "+n[l.JR],t[l.FI]=$,t[l.fA]=(0,m._u)(new Date),t.baseType=p.WD.dataType,t.baseData={message:n[l.pM]},t);e.track(r)}))}}function ye(e,n,t,r){var i=1,o=!1,a=null;function c(){i--,o&&0===i&&(a&&a[l._w](),a=null,n&&n(o),n=null)}return r=r||5e3,X&&X[l.oI]>0&&de()[l.$o](X).iterate((function(n){if(n.flush){i++;var o=!1;n.flush(e,(function(){o=!0,c()}),t)||o||(e&&null==a?a=(0,u.dRz)((function(){a=null,c()}),r):c())}})),o=!0,c(),!0}function Ie(n){var t=(0,w.nU)(ge(),e);t[l.by](se),e._updateHook&&!0===e._updateHook(t,n)||t[l.$5](n)}function be(n){var t=e[l.Uw];t?((0,p.ZP)(t,2,73,n),se()):(0,u.$8)(n)}function we(n){var t=e[l.RF]();t&&t[y.Yp]([n],2)}function Ce(e){W.add(e)}fe(),e._getDbgPlgTargets=function(){return[Q,A]},e[l.tZ]=function(){return t},e.activeStatus=function(){return ne},e._setPendingStatus=function(){ne=3},e[l.mE]=function(f,d,v,h){var I;B&&(0,u.$8)(E),e[l.tZ]()&&(0,u.$8)("Core cannot be initialized more than once"),n=(0,c.e)(f,x,v||e[l.Uw],!1),f=n.cfg,Ce(n[l.x6]((function(e){var n=e.cfg;if(3!==ne){re=n.initInMemoMaxSize||100;var i=n[l.sl],c=n.endpointUrl;if((0,u.hXl)(i)){$=null,ne=s.f[l.Yq];var d="Please provide instrumentation key";t?((0,p.ZP)(r,1,100,d),ce()):(0,u.$8)(d)}else{var v=[];if((0,u.$XS)(i)?(v[l.y5](i),$=null):$=i,(0,u.$XS)(c)?(v[l.y5](c),te=null):te=c,v[l.oI]){ie=!1,ne=3;var h=(0,m.Gh)(n.initTimeOut)?n.initTimeOut:5e4,g=(0,a.lh)(v);(0,u.dRz)((function(){ie||o()}),h),(0,a.Dv)(g,(function(e){try{if(ie)return;if(!e.rejected){var n=e[l.pF];if(n&&n[l.oI]){var t=n[0];if($=t&&t[l.pF],n[l.oI]>1){var r=n[1];te=r&&r[l.pF]}}$&&(f[l.sl]=$,f.endpointUrl=te)}o()}catch(e){ie||o()}}))}else o();var I=e.ref(e.cfg,y.Bw);(0,u.zav)(I,(function(n){e.ref(I,n)}))}}}))),Z=function(e,n,t,r){return n.add(e[l.x6]((function(e){var n=e.cfg.disableDbgExt;!0===n&&r&&(t[l.h3](r),r=null),t&&!r&&!0!==n&&(r=(0,g.M)(e.cfg),t[l.vR](r))}))),r}(n,W,(L=h)&&e[l.RF](),Z),Ce(n[l.x6]((function(n){if(n.cfg.enablePerfMgr){var t=n.cfg[y.Yd];I===t&&I||(t||(t=D),(0,m.c2)(n.cfg,y.Yd,t),I=t,N=null),M||N||!(0,u.Tnt)(t)||(N=t(e,e[l.RF]()))}else N=null,I=null}))),e[l.Uw]=v;var b=f[y.jy];if((z=[])[l.y5].apply(z,(0,i.vz)((0,i.vz)([],d,!1),b,!1)),H=f[y.LZ],ve(null),X&&0!==X[l.oI]||(0,u.$8)("No "+y.LZ+" available"),H&&H[l.oI]>1){var w=e[l.AP]("TeeChannelController");w&&w.plugin||(0,p.ZP)(r,1,28,"TeeChannel required")}!function(e,n,t){(0,u.Iuo)(n,(function(n){var r=(0,c.a)(e,n.w,t);delete n.w,n.rm=function(){r.rm()}}))}(f,G,r),G=null,t=!0,ne===s.f.ACTIVE&&ce()},e.getChannels=function(){var e=[];return X&&(0,u.Iuo)(X,(function(n){e[l.y5](n)})),(0,u.N6t)(e)},e.track=function(n){(0,b.r2)(e[y.kI](),(function(){return"AppInsightsCore:track"}),(function(){null===n&&(we(n),(0,u.$8)("Invalid telemetry item")),!n[l.RS]&&(0,u.hXl)(n[l.RS])&&(we(n),(0,u.$8)("telemetry name required")),n[l.FI]=n[l.FI]||$,n[l.fA]=n[l.fA]||(0,m._u)(new Date),n.ver=n.ver||"4.0",!B&&e[l.tZ]()&&ne===s.f.ACTIVE?de()[l.$5](n):ne!==s.f[l.Yq]&&A[l.oI]<=re&&A[l.y5](n)}),(function(){return{item:n}}),!n.sync)},e[l.DI]=de,e[l.RF]=function(){return L||(L=new I.h(n.cfg),e._notificationManager=L),L},e[l.vR]=function(n){e.getNotifyMgr()[l.vR](n)},e[l.h3]=function(e){L&&L[l.h3](e)},e.getCookieMgr=function(){return F||(F=(0,v.xN)(n.cfg,e[l.Uw])),F},e.setCookieMgr=function(e){F!==e&&((0,f.K)(F,!1),F=e)},e[y.kI]=function(){return M||N||(0,b.Z4)()},e.setPerfMgr=function(e){M=e},e.eventCnt=function(){return A[l.oI]},e.releaseQueue=function(){if(t&&A[l.oI]>0){var e=A;A=[],2===ne?(0,u.Iuo)(e,(function(e){e[l.FI]=e[l.FI]||$,de()[l.$5](e)})):(0,p.ZP)(r,2,20,"core init status is not active")}},e[l.h4]=function(e){return q=e||null,ue=!1,oe&&oe[l._w](),se(!0)},e[l.Di]=function(){ue=!0,oe&&oe[l._w](),me()},(0,m.o$)(e,(function(){return j}),["addTelemetryInitializer"]),e[l.M5]=function(n,i,o){var c;void 0===n&&(n=!0),t||(0,u.$8)("SDK is not initialized"),B&&(0,u.$8)(E);var s,d=((c={reason:50})[l.tI]=n,c.flushComplete=!1,c);n&&!i&&(s=(0,a.Qo)((function(e){i=e})));var v=(0,w.tS)(ge(),e);function h(n){d.flushComplete=n,B=!0,K.run(v,d),e[l.Di](),v[l.$5](d)}return v[l.by]((function(){W.run(e[l.Uw]),(0,f.k)([F,L,r],n,(function(){fe(),i&&i(d)}))}),e),me(),ye(n,h,6,o)||h(!1),s},e[l.AP]=he,e.addPlugin=function(e,n,t,r){if(!e)return r&&r(!1),void be(P);var i=he(e[l.Ju]);if(i&&!n)return r&&r(!1),void be("Plugin ["+e[l.Ju]+"] is already loaded!");var o={reason:16};function a(n){z[l.y5](e),o.added=[e],ve(o),r&&r(!0)}if(i){var u=[i.plugin];pe(u,{reason:2,isAsync:!!t},(function(e){e?(o.removed=u,o.reason|=32,a()):r&&r(!1)}))}else a()},e.updateCfg=function(t,r){var i;if(void 0===r&&(r=!0),e[l.tZ]()){i={reason:1,cfg:n.cfg,oldCfg:(0,u.zwS)({},n.cfg),newConfig:(0,u.zwS)({},t),merge:r},t=i.newConfig;var o=n.cfg;t[y.jy]=o[y.jy],t[y.LZ]=o[y.LZ]}n._block((function(e){var n=e.cfg;k(e,n,t,r),r||(0,u.zav)(n,(function(r){(0,u.KhI)(t,r)||e.set(n,r,y.HP)})),e[l.h0](n,x)}),!0),n[l.zs](),i&&Ie(i)},e.evtNamespace=function(){return V},e.flush=ye,e.getTraceCtx=function(e){return J||(J=(0,C.u7)()),J},e.setTraceCtx=function(e){J=e||null},e.addUnloadHook=Ce,(0,m.RF)(e,"addUnloadCb",(function(){return K}),"add"),e.onCfgChange=function(r){var i,o,a,s;return t?i=(0,c.a)(n.cfg,r,e[l.Uw]):((s=R(o=G,a=r).l)||(s={w:a,rm:function(){var e=R(o,a);-1!==e.i&&o[l.Ic](e.i,1)}},o[l.y5](s)),i=s),function(e){return(0,u.vF1)({rm:function(){e.rm()}},"toJSON",{v:function(){return"aicore::onCfgChange<"+JSON.stringify(e)+">"}})}(i)},e.getWParam=function(){return(0,u.Wtk)()||n.cfg.enableWParam?0:-1}}))}return e.__ieDyn=1,e}()},4013:(e,n,t)=>{t.d(n,{K:()=>a,k:()=>u});var r=t(8205),i=t(269),o=t(6182);function a(e,n){if(e&&e[o.M5])return e[o.M5](n)}function u(e,n,t){var o;return t||(o=(0,r.Qo)((function(e){t=e}))),e&&(0,i.R3R)(e)>0?(0,r.Dv)(a(e[0],n),(function(){u((0,i.KVm)(e,1),n,t)})):t(),o}},8257:(e,n,t)=>{t.d(n,{s:()=>p});var r,i=t(8279),o=t(269),a=t(9749),u=t(6182),c=t(3775),s=t(3673),l=t(6492),f=t(2317),d=t(836),v=t(8969),h="getPlugin",g=((r={})[l.Bw]={isVal:s.Gh,v:{}},r),p=function(){function e(){var n,t,r,p,m,y=this;function I(e){void 0===e&&(e=null);var n=e;if(!n){var i=t||(0,f.i8)(null,{},y[l.eT]);n=r&&r[h]?i[u.$o](null,r[h]):i[u.$o](null,r)}return n}function b(e,n,i){(0,a.e)(e,g,(0,c.y0)(n)),!i&&n&&(i=n[u.DI]()[u.uR]());var o=r;r&&r[h]&&(o=r[h]()),y[l.eT]=n,t=(0,f.i8)(i,e,n,o)}function w(){n=!1,y[l.eT]=null,t=null,r=null,m=(0,v.w)(),p=(0,d.P)()}w(),(0,i.A)(e,y,(function(e){e[u.mE]=function(e,t,r,i){b(e,t,i),n=!0},e[u.Ik]=function(n,t){var i,o=e[l.eT];if(o&&(!n||o===n[l.eT]())){var a,c=!1,s=n||(0,f.tS)(null,o,r&&r[h]?r[h]():r),d=t||((i={reason:0})[u.tI]=!1,i);return e[u.tn]&&!0===e[u.tn](s,d,v)?a=!0:v(),a}function v(){c||(c=!0,p.run(s,t),m.run(s[u.e4]()),!0===a&&s[u.$5](d),w())}},e[u.HC]=function(n,t){var i=e[l.eT];if(i&&(!n||i===n[l.eT]())){var o,a=!1,c=n||(0,f.nU)(null,i,r&&r[h]?r[h]():r),s=t||{reason:0};return e._doUpdate&&!0===e._doUpdate(c,s,d)?o=!0:d(),o}function d(){a||(a=!0,b(c.getCfg(),c.core(),c[u.uR]()))}},(0,s.RF)(e,"_addUnloadCb",(function(){return p}),"add"),(0,s.RF)(e,"_addHook",(function(){return m}),"add"),(0,o.vF1)(e,"_unloadHooks",{g:function(){return m}})})),y[u.e4]=function(e){return I(e)[u.e4]()},y[u.tZ]=function(){return n},y.setInitialized=function(e){n=e},y[u.YH]=function(e){r=e},y[u.$5]=function(e,n){n?n[u.$5](e):r&&(0,o.Tnt)(r[l.qT])&&r[l.qT](e,null)},y._getTelCtx=I}return e.__ieDyn=1,e}()},7847:(e,n,t)=>{t.d(n,{i:()=>r,x:()=>i});var r=500,i="Microsoft_ApplicationInsights_BypassAjaxInstrumentation"},5034:(e,n,t)=>{t.d(n,{It:()=>z,gi:()=>A,um:()=>k,xN:()=>R});var r,i,o,a=t(269),u=t(2475),c=t(9749),s=t(6182),l=t(3775),f=t(7292),d=t(3673),v=t(6492),h="toGMTString",g="toUTCString",p="cookie",m="expires",y="isCookieUseDisabled",I="disableCookiesUsage",b="_ckMgr",w=null,C=null,S=null,T={},_={},P=((r={cookieCfg:(0,u.NU)((i={},i[v.Fk]={fb:"cookieDomain",dfVal:d.Gh},i.path={fb:"cookiePath",dfVal:d.Gh},i.enabled=v.HP,i.ignoreCookies=v.HP,i.blockedCookies=v.HP,i)),cookieDomain:v.HP,cookiePath:v.HP})[I]=v.HP,r);function E(){!o&&(o=(0,a.nRs)((function(){return(0,a.YEm)()})))}function x(e){return!e||e.isEnabled()}function D(e,n){return!!(n&&e&&(0,a.cyL)(e.ignoreCookies))&&-1!==(0,a.rDm)(e.ignoreCookies,n)}function O(e,n){var t=n[s.XM];if((0,a.hXl)(t)){var r=void 0;(0,a.b07)(e[y])||(r=!e[y]),(0,a.b07)(e[I])||(r=!e[I]),t=r}return t}function k(e,n){var t;if(e)t=e.getCookieMgr();else if(n){var r=n.cookieCfg;t=r&&r[b]?r[b]:R(n)}return t||(t=function(e,n){var t=R[b]||_[b];return t||(t=R[b]=R(e,n),_[b]=t),t}(n,(e||{})[s.Uw])),t}function R(e,n){var t,r,i,o,u,l,p,y,I;e=(0,c.e)(e||_,null,n).cfg,u=(0,c.a)(e,(function(n){n[s.h0](n.cfg,P),r=n.ref(n.cfg,"cookieCfg"),i=r[v.QW]||"/",o=r[v.Fk],l=!1!==O(e,r),p=r.getCookie||F,y=r.setCookie||U,I=r.delCookie||U}),n);var w=((t={isEnabled:function(){var t=!1!==O(e,r)&&l&&A(n),i=_[b];return t&&i&&w!==i&&(t=x(i)),t},setEnabled:function(e){l=!1!==e,r[s.XM]=e},set:function(e,n,t,u,c){var l=!1;if(x(w)&&!function(e,n){return!!(n&&e&&(0,a.cyL)(e.blockedCookies)&&-1!==(0,a.rDm)(e.blockedCookies,n))||D(e,n)}(r,e)){var p={},I=(0,a.EHq)(n||v.m5),b=(0,a.HzD)(I,";");if(-1!==b&&(I=(0,a.EHq)((0,a.ZWZ)(n,b)),p=L((0,a.P0f)(n,b+1))),(0,d.KY)(p,v.Fk,u||o,a.zzB,a.b07),!(0,a.hXl)(t)){var S=(0,f.lT)();if((0,a.b07)(p[m])){var T=(0,a.f0d)()+1e3*t;if(T>0){var _=new Date;_.setTime(T),(0,d.KY)(p,m,M(_,S?h:g)||M(_,S?h:g)||v.m5,a.zzB)}}S||(0,d.KY)(p,"max-age",v.m5+t,null,a.b07)}var P=(0,f.g$)();P&&"https:"===P[s.Qg]&&((0,d.KY)(p,"secure",null,null,a.b07),null===C&&(C=!z(((0,a.w3n)()||{})[s.tX])),C&&(0,d.KY)(p,"SameSite","None",null,a.b07)),(0,d.KY)(p,v.QW,c||i,null,a.b07),y(e,N(I,p)),l=!0}return l},get:function(e){var n=v.m5;return x(w)&&!D(r,e)&&(n=p(e)),n},del:function(e,n){var t=!1;return x(w)&&(t=w.purge(e,n)),t},purge:function(e,t){var r,i=!1;if(A(n)){var o=((r={})[v.QW]=t||"/",r[m]="Thu, 01 Jan 1970 00:00:01 GMT",r);(0,f.lT)()||(o["max-age"]="0"),I(e,N(v.m5,o)),i=!0}return i}})[s.M5]=function(e){u&&u.rm(),u=null},t);return w[b]=w,w}function A(e){if(null===w){w=!1,!o&&E();try{var n=o.v||{};w=void 0!==n[p]}catch(n){(0,l.ZP)(e,2,68,"Cannot access document.cookie - "+(0,d.lL)(n),{exception:(0,a.mmD)(n)})}}return w}function L(e){var n={};if(e&&e[s.oI]){var t=(0,a.EHq)(e)[s.sY](";");(0,a.Iuo)(t,(function(e){if(e=(0,a.EHq)(e||v.m5)){var t=(0,a.HzD)(e,"=");-1===t?n[e]=null:n[(0,a.EHq)((0,a.ZWZ)(e,t))]=(0,a.EHq)((0,a.P0f)(e,t+1))}}))}return n}function M(e,n){return(0,a.Tnt)(e[n])?e[n]():null}function N(e,n){var t=e||v.m5;return(0,a.zav)(n,(function(e,n){t+="; "+e+((0,a.hXl)(n)?v.m5:"="+n)})),t}function F(e){var n=v.m5;if(!o&&E(),o.v){var t=o.v[p]||v.m5;S!==t&&(T=L(t),S=t),n=(0,a.EHq)(T[e]||v.m5)}return n}function U(e,n){!o&&E(),o.v&&(o.v[p]=e+"="+n)}function z(e){return!(!(0,a.KgX)(e)||!(0,d.Ju)(e,"CPU iPhone OS 12")&&!(0,d.Ju)(e,"iPad; CPU OS 12")&&!((0,d.Ju)(e,"Macintosh; Intel Mac OS X 10_14")&&(0,d.Ju)(e,"Version/")&&(0,d.Ju)(e,"Safari"))&&(!(0,d.Ju)(e,"Macintosh; Intel Mac OS X 10_14")||!(0,a.Cv9)(e,"AppleWebKit/605.1.15 (KHTML, like Gecko)"))&&!(0,d.Ju)(e,"Chrome/5")&&!(0,d.Ju)(e,"Chrome/6")&&(!(0,d.Ju)(e,"UnrealEngine")||(0,d.Ju)(e,"Chrome"))&&!(0,d.Ju)(e,"UCBrowser/12")&&!(0,d.Ju)(e,"UCBrowser/11"))}},9882:(e,n,t)=>{t.d(n,{aq:()=>a,cL:()=>u});var r=t(269),i=t(6492),o=t(6535);function a(){var e=u();return(0,r.P0f)(e,0,8)+"-"+(0,r.P0f)(e,8,12)+"-"+(0,r.P0f)(e,12,16)+"-"+(0,r.P0f)(e,16,20)+"-"+(0,r.P0f)(e,20)}function u(){for(var e,n=["0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f"],t=i.m5,a=0;a<4;a++)t+=n[15&(e=(0,o.VN)())]+n[e>>4&15]+n[e>>8&15]+n[e>>12&15]+n[e>>16&15]+n[e>>20&15]+n[e>>24&15]+n[e>>28&15];var u=n[8+(3&(0,o.VN)())|0];return(0,r.hKY)(t,0,8)+(0,r.hKY)(t,9,4)+"4"+(0,r.hKY)(t,13,3)+u+(0,r.hKY)(t,16,3)+(0,r.hKY)(t,19,12)}},4276:(e,n,t)=>{t.d(n,{T:()=>v,Z:()=>d});var r=t(269),i=t(6182),o=t(3673),a=t(6492),u=t(6535),c="3.3.4",s="."+(0,u.Si)(6),l=0;function f(e){return 1===e[i.re]||9===e[i.re]||!+e[i.re]}function d(e,n){return void 0===n&&(n=!1),(0,o.cH)(e+l+++(n?"."+c:a.m5)+s)}function v(e){var n={id:d("_aiData-"+(e||a.m5)+"."+c),accept:function(e){return f(e)},get:function(e,t,i,a){var u=e[n.id];return u?u[(0,o.cH)(t)]:(a&&(u=function(e,n){var t=n[e.id];if(!t){t={};try{f(n)&&(0,r.vF1)(n,e.id,{e:!1,v:t})}catch(e){}}return t}(n,e),u[(0,o.cH)(t)]=i),i)},kill:function(e,n){if(e&&e[n])try{delete e[n]}catch(e){}}};return n}},7867:(e,n,t)=>{t.d(n,{$:()=>l,M:()=>f});var r,i=t(269),o=t(6182),a=t(6492),u=[a.fc,a.Yp,a.dI,a.l0],c=null;function s(e,n){return function(){var t=arguments,r=l(n);if(r){var i=r.listener;i&&i[e]&&i[e][o.y9](i,t)}}}function l(e){var n,t=c;return t||!0===e.disableDbgExt||(t=c||((n=(0,i.zS2)("Microsoft"))&&(c=n.ApplicationInsights),c)),t?t.ChromeDbgExt:null}function f(e){if(!r){r={};for(var n=0;n<u[o.oI];n++)r[u[n]]=s(u[n],e)}return r}},3775:(e,n,t)=>{t.d(n,{OG:()=>w,Oc:()=>C,WD:()=>p,ZP:()=>b,wq:()=>y,y0:()=>m});var r,i=t(8279),o=t(269),a=t(9749),u=t(6182),c=t(7867),s=t(7292),l=t(6492),f="warnToConsole",d={loggingLevelConsole:0,loggingLevelTelemetry:1,maxMessageLimit:25,enableDebug:!1},v=((r={})[0]=null,r[1]="errorToConsole",r[2]=f,r[3]="debugToConsole",r);function h(e){return e?'"'+e[u.W7](/\"/g,l.m5)+'"':l.m5}function g(e,n){var t=(0,s.U5)();if(t){var r="log";t[e]&&(r=e),(0,o.Tnt)(t[r])&&t[r](n)}}var p=function(){function e(e,n,t,r){void 0===t&&(t=!1);var i=this;i[u.JR]=e,i[u.pM]=(t?"AI: ":"AI (Internal): ")+e;var o=l.m5;(0,s.Z)()&&(o=(0,s.hm)().stringify(r));var a=(n?" message:"+h(n):l.m5)+(r?" props:"+h(o):l.m5);i[u.pM]+=a}return e.dataType="MessageData",e}();function m(e,n){return(e||{})[u.Uw]||new y(n)}var y=function(){function e(n){this.identifier="DiagnosticLogger",this.queue=[];var t,r,s,l,h,m=0,y={};(0,i.A)(e,this,(function(e){function i(n,t){if(!(m>=s)){var i=!0,o="AITR_"+t[u.JR];if(y[o]?i=!1:y[o]=!0,i&&(n<=r&&(e.queue[u.y5](t),m++,I(1===n?"error":"warn",t)),m===s)){var a="Internal events throttle limit per PageView reached for this app.",c=new p(23,a,!1);e.queue[u.y5](c),1===n?e.errorToConsole(a):e[u.on](a)}}}function I(e,t){var r=(0,c.$)(n||{});r&&r[u.e4]&&r[u.e4](e,t)}h=function(n){return(0,a.a)((0,a.e)(n,d,e).cfg,(function(e){var n=e.cfg;t=n[u.Bl],r=n.loggingLevelTelemetry,s=n.maxMessageLimit,l=n.enableDebug}))}(n||{}),e.consoleLoggingLevel=function(){return t},e[u.ih]=function(n,r,a,c,s){void 0===s&&(s=!1);var d=new p(r,a,s,c);if(l)throw(0,o.mmD)(d);var h=v[n]||f;if((0,o.b07)(d[u.pM]))I("throw"+(1===n?"Critical":"Warning"),d);else{if(s){var g=+d[u.JR];!y[g]&&t>=n&&(e[h](d[u.pM]),y[g]=!0)}else t>=n&&e[h](d[u.pM]);i(n,d)}},e.debugToConsole=function(e){g("debug",e),I("warning",e)},e[u.on]=function(e){g("warn",e),I("warning",e)},e.errorToConsole=function(e){g("error",e),I("error",e)},e.resetInternalMessageCount=function(){m=0,y={}},e[u.sx]=i,e[u.M5]=function(e){h&&h.rm(),h=null}}))}return e.__ieDyn=1,e}();function I(e){return e||new y}function b(e,n,t,r,i,o){void 0===o&&(o=!1),I(e)[u.ih](n,t,r,i,o)}function w(e,n){I(e)[u.on](n)}function C(e,n,t){I(e)[u.sx](n,t)}},7292:(e,n,t)=>{t.d(n,{$Z:()=>z,Iu:()=>H,L0:()=>A,MY:()=>D,PV:()=>N,R7:()=>M,U5:()=>P,Uf:()=>L,Z:()=>E,cU:()=>T,g$:()=>_,hm:()=>x,iN:()=>O,lT:()=>R,lV:()=>k,xk:()=>F});var r=t(5664),i=t(269),o=t(6182),a=t(3673),u=t(6492),c="documentMode",s="location",l="console",f="JSON",d="crypto",v="msCrypto",h="ReactNative",g="msie",p="trident/",m="XMLHttpRequest",y=null,I=null,b=!1,w=null,C=null;function S(e,n){var t=!1;if(e){try{if(!(t=n in e)){var o=e[r.vR];o&&(t=n in o)}}catch(e){}if(!t)try{var a=new e;t=!(0,i.b07)(a[n])}catch(e){}}return t}function T(e){b=e}function _(e){if(e&&b){var n=(0,i.zS2)("__mockLocation");if(n)return n}return typeof location===r._1&&location?location:(0,i.zS2)(s)}function P(){return typeof console!==r.bA?console:(0,i.zS2)(l)}function E(){return Boolean(typeof JSON===r._1&&JSON||null!==(0,i.zS2)(f))}function x(){return E()?JSON||(0,i.zS2)(f):null}function D(){return(0,i.zS2)(d)}function O(){return(0,i.zS2)(v)}function k(){var e=(0,i.w3n)();return!(!e||!e.product)&&e.product===h}function R(){var e=(0,i.w3n)();if(e&&(e[o.tX]!==I||null===y)){var n=((I=e[o.tX])||u.m5)[o.OL]();y=(0,a.Ju)(n,g)||(0,a.Ju)(n,p)}return y}function A(e){if(void 0===e&&(e=null),!e){var n=(0,i.w3n)()||{};e=n?(n.userAgent||u.m5)[o.OL]():u.m5}var t=(e||u.m5)[o.OL]();if((0,a.Ju)(t,g)){var r=(0,i.YEm)()||{};return Math.max(parseInt(t[o.sY](g)[1]),r[c]||0)}if((0,a.Ju)(t,p)){var s=parseInt(t[o.sY](p)[1]);if(s)return s+4}return null}function L(e){return null!==C&&!1!==e||(C=(0,i.w9M)()&&Boolean((0,i.w3n)().sendBeacon)),C}function M(e){var n=!1;try{n=!!(0,i.zS2)("fetch");var t=(0,i.zS2)("Request");n&&e&&t&&(n=S(t,"keepalive"))}catch(e){}return n}function N(){return null===w&&(w=typeof XDomainRequest!==r.bA)&&F()&&(w=w&&!S((0,i.zS2)(m),"withCredentials")),w}function F(){var e=!1;try{e=!!(0,i.zS2)(m)}catch(e){}return e}function U(e,n){if(e)for(var t=0;t<e[o.oI];t++){var r=e[t];if(r[o.RS]&&r[o.RS]===n)return r}return{}}function z(e){var n=(0,i.YEm)();return n&&e?U(n.querySelectorAll("meta"),e).content:null}function H(e){var n,t=(0,i.FJj)();if(t){var r=t.getEntriesByType("navigation")||[];n=U((r[o.oI]>0?r[0]:{}).serverTiming,e).description}return n}},6149:(e,n,t)=>{t.d(n,{Ds:()=>B,El:()=>E,Fc:()=>j,Hm:()=>k,ML:()=>A,Q3:()=>L,So:()=>N,Wg:()=>H,Ym:()=>M,ee:()=>X,lQ:()=>z,mB:()=>R,oS:()=>V,sq:()=>q,vF:()=>K,zh:()=>F});var r=t(269),i=t(6182),o=t(4276),a=t(6492),u="on",c="attachEvent",s="addEventListener",l="detachEvent",f="removeEventListener",d="events",v="visibilitychange",h="pagehide",g="pageshow",p="unload",m="beforeunload",y=(0,o.Z)("aiEvtPageHide"),I=(0,o.Z)("aiEvtPageShow"),b=/\.[\.]+/g,w=/[\.]+$/,C=1,S=(0,o.T)("events"),T=/^([^.]*)(?:\.(.+)|)/;function _(e){return e&&e[i.W7]?e[i.W7](/^[\s\.]+|(?=[\s\.])[\.\s]+$/g,a.m5):e}function P(e,n){var t;if(n){var o=a.m5;(0,r.cyL)(n)?(o=a.m5,(0,r.Iuo)(n,(function(e){(e=_(e))&&("."!==e[0]&&(e="."+e),o+=e)}))):o=_(n),o&&("."!==o[0]&&(o="."+o),e=(e||a.m5)+o)}var u=T.exec(e||a.m5)||[];return(t={})[i.QM]=u[1],t.ns=(u[2]||a.m5).replace(b,".").replace(w,a.m5)[i.sY](".").sort().join("."),t}function E(e,n,t){var o=[],u=S.get(e,d,{},!1),c=P(n,t);return(0,r.zav)(u,(function(e,n){(0,r.Iuo)(n,(function(e){var n;c[i.QM]&&c[i.QM]!==e.evtName[i.QM]||c.ns&&c.ns!=c.ns||o[i.y5](((n={})[i.RS]=e.evtName[i.QM]+(e.evtName.ns?"."+e.evtName.ns:a.m5),n.handler=e[i.Yo],n))}))})),o}function x(e,n,t){void 0===t&&(t=!0);var r=S.get(e,d,{},t),i=r[n];return i||(i=r[n]=[]),i}function D(e,n,t,r){e&&n&&n[i.QM]&&(e[f]?e[f](n[i.QM],t,r):e[l]&&e[l](u+n[i.QM],t))}function O(e,n,t,r){for(var o=n[i.oI];o--;){var a=n[o];a&&(t.ns&&t.ns!==a.evtName.ns||r&&!r(a)||(D(e,a.evtName,a[i.Yo],a.capture),n[i.Ic](o,1)))}}function k(e,n){return n?P("xx",(0,r.cyL)(n)?[e].concat(n):[e,n]).ns[i.sY]("."):e}function R(e,n,t,r,o){var a;void 0===o&&(o=!1);var l=!1;if(e)try{var f=P(n,r);if(l=function(e,n,t,r){var o=!1;return e&&n&&n[i.QM]&&t&&(e[s]?(e[s](n[i.QM],t,r),o=!0):e[c]&&(e[c](u+n[i.QM],t),o=!0)),o}(e,f,t,o),l&&S.accept(e)){var d=((a={guid:C++,evtName:f})[i.Yo]=t,a.capture=o,a);x(e,f.type)[i.y5](d)}}catch(e){}return l}function A(e,n,t,o,a){if(void 0===a&&(a=!1),e)try{var u=P(n,o),c=!1;!function(e,n,t){if(n[i.QM])O(e,x(e,n[i.QM]),n,t);else{var o=S.get(e,d,{});(0,r.zav)(o,(function(r,i){O(e,i,n,t)})),0===(0,r.cGk)(o)[i.oI]&&S.kill(e,d)}}(e,u,(function(e){return!((!u.ns||t)&&e[i.Yo]!==t||(c=!0,0))})),c||D(e,u,t,a)}catch(e){}}function L(e,n,t,r){return void 0===r&&(r=!1),R(e,n,t,null,r)}function M(e,n,t,r){void 0===r&&(r=!1),A(e,n,t,null,r)}function N(e,n,t){var i=!1,o=(0,r.zkX)();o&&(i=R(o,e,n,t),i=R(o.body,e,n,t)||i);var a=(0,r.YEm)();return a&&(i=R(a,e,n,t)||i),i}function F(e,n,t){var i=(0,r.zkX)();i&&(A(i,e,n,t),A(i.body,e,n,t));var o=(0,r.YEm)();o&&A(o,e,n,t)}function U(e,n,t,o){var a=!1;return n&&e&&e[i.oI]>0&&(0,r.Iuo)(e,(function(e){e&&(t&&-1!==(0,r.rDm)(t,e)||(a=N(e,n,o)||a))})),a}function z(e,n,t,o){var a=!1;return n&&e&&(0,r.cyL)(e)&&!(a=U(e,n,t,o))&&t&&t[i.oI]>0&&(a=U(e,n,null,o)),a}function H(e,n,t){e&&(0,r.cyL)(e)&&(0,r.Iuo)(e,(function(e){e&&F(e,n,t)}))}function X(e,n,t){return z([m,p,h],e,n,t)}function B(e,n){H([m,p,h],e,n)}function j(e,n,t){var i=k(y,t),o=U([h],e,n,i);return n&&-1!==(0,r.rDm)(n,v)||(o=U([v],(function(n){var t=(0,r.YEm)();e&&t&&"hidden"===t.visibilityState&&e(n)}),n,i)||o),!o&&n&&(o=j(e,null,t)),o}function q(e,n){var t=k(y,n);H([h],e,t),H([v],null,t)}function V(e,n,t){var i=k(I,t),o=U([g],e,n,i);return!(o=U([v],(function(n){var t=(0,r.YEm)();e&&t&&"visible"===t.visibilityState&&e(n)}),n,i)||o)&&n&&(o=V(e,null,t)),o}function K(e,n){var t=k(I,n);H([g],e,t),H([v],null,t)}},3673:(e,n,t)=>{t.d(n,{CP:()=>S,Gh:()=>l,H$:()=>k,HU:()=>_,IL:()=>R,Ju:()=>d,KY:()=>g,LU:()=>A,Lo:()=>T,RF:()=>I,SZ:()=>w,_u:()=>v,c2:()=>p,cH:()=>f,hW:()=>C,jL:()=>E,lL:()=>h,o$:()=>b,qz:()=>y,r4:()=>P,w3:()=>F});var r=t(269),i=t(5664),o=t(6182),a=t(6492),u=/-([a-z])/g,c=/([^\w\d_$])/g,s=/^(\d+[\w\d_$])/;function l(e){return!(0,r.hXl)(e)}function f(e){var n=e;return n&&(0,r.KgX)(n)&&(n=(n=(n=n[o.W7](u,(function(e,n){return n.toUpperCase()})))[o.W7](c,"_"))[o.W7](s,(function(e,n){return"_"+n}))),n}function d(e,n){return!(!e||!n)&&-1!==(0,r.HzD)(e,n)}function v(e){return e&&e.toISOString()||""}function h(e){return(0,r.bJ7)(e)?e[o.RS]:a.m5}function g(e,n,t,r,i){var o=t;return e&&((o=e[n])===t||i&&!i(o)||r&&!r(t)||(o=t,e[n]=o)),o}function p(e,n,t){var i;return e?!(i=e[n])&&(0,r.hXl)(i)&&(i=(0,r.b07)(t)?{}:t,e[n]=i):i=(0,r.b07)(t)?{}:t,i}function m(e,n){var t=null,i=null;return(0,r.Tnt)(e)?t=e:i=e,function(){var e=arguments;if(t&&(i=t()),i)return i[n][o.y9](i,e)}}function y(e,n,t){if(e&&n&&(0,r.Gvm)(e)&&(0,r.Gvm)(n)){var i=function(i){if((0,r.KgX)(i)){var o=n[i];(0,r.Tnt)(o)?t&&!t(i,!0,n,e)||(e[i]=m(n,i)):t&&!t(i,!1,n,e)||((0,r.KhI)(e,i)&&delete e[i],(0,r.vF1)(e,i,{g:function(){return n[i]},s:function(e){n[i]=e}}))}};for(var o in n)i(o)}return e}function I(e,n,t,i,o){e&&n&&t&&(!1!==o||(0,r.b07)(e[n]))&&(e[n]=m(t,i))}function b(e,n,t,i){return e&&n&&(0,r.Gvm)(e)&&(0,r.cyL)(t)&&(0,r.Iuo)(t,(function(t){(0,r.KgX)(t)&&I(e,t,n,t,i)})),e}function w(e){return function(){var n=this;e&&(0,r.zav)(e,(function(e,t){n[e]=t}))}}function C(e){return e&&r.vE3&&(e=(0,i.s6)((0,r.vE3)({},e))),e}function S(e,n,t,i,a,u){var c=arguments,s=c[0]||{},l=c[o.oI],f=!1,d=1;for(l>0&&(0,r.Lmq)(s)&&(f=s,s=c[d]||{},d++),(0,r.Gvm)(s)||(s={});d<l;d++){var v=c[d],h=(0,r.cyL)(v),g=(0,r.Gvm)(v);for(var p in v)if(h&&p in v||g&&(0,r.KhI)(v,p)){var m=v[p],y=void 0;if(f&&m&&((y=(0,r.cyL)(m))||(0,r.QdQ)(m))){var I=s[p];y?(0,r.cyL)(I)||(I=[]):(0,r.QdQ)(I)||(I={}),m=S(f,I,m)}void 0!==m&&(s[p]=m)}}return s}function T(e){try{return e.responseText}catch(e){}return null}function _(e,n){return e?"XDomainRequest,Response:"+T(e)||0:n}function P(e,n){return e?"XMLHttpRequest,Status:"+e[o.cV]+",Response:"+T(e)||0:n}function E(e,n){return n&&((0,r.EtT)(n)?e=[n].concat(e):(0,r.cyL)(n)&&(e=n.concat(e))),e}Object.getPrototypeOf;var x="Microsoft_ApplicationInsights_BypassAjaxInstrumentation",D="withCredentials",O="timeout";function k(e,n,t,r,i,o){function a(e,n,t){try{e[n]=t}catch(e){}}void 0===r&&(r=!1),void 0===i&&(i=!1);var u=new XMLHttpRequest;return r&&a(u,x,r),t&&a(u,D,t),u.open(e,n,!i),t&&a(u,D,t),!i&&o&&a(u,O,o),u}function R(e){var n={};if((0,r.KgX)(e)){var t=(0,r.EHq)(e)[o.sY](/[\r\n]+/);(0,r.Iuo)(t,(function(e){if(e){var t=e.indexOf(": ");if(-1!==t){var i=(0,r.EHq)(e.substring(0,t))[o.OL](),a=(0,r.EHq)(e.substring(t+1));n[i]=a}else n[(0,r.EHq)(e)]=1}}))}return n}function A(e,n,t){if(!e[t]&&n&&n[o.Az]){var i=n[o.Az](t);i&&(e[t]=(0,r.EHq)(i))}return e}var L="kill-duration",M="kill-duration-seconds",N="time-delta-millis";function F(e,n){var t={};return e[o.wJ]?t=R(e[o.wJ]()):n&&(t=A(t,e,N),t=A(t,e,L),t=A(t,e,M)),t}},6492:(e,n,t)=>{t.d(n,{Bw:()=>s,Ev:()=>I,Fk:()=>w,HP:()=>r,Hr:()=>c,LZ:()=>o,QW:()=>C,Vj:()=>y,Vo:()=>d,Yd:()=>u,Yp:()=>h,dI:()=>g,eT:()=>a,fc:()=>v,jy:()=>l,kI:()=>b,l0:()=>p,m5:()=>i,qT:()=>f,s4:()=>m,xW:()=>S});var r=void 0,i="",o="channels",a="core",u="createPerfMgr",c="disabled",s="extensionConfig",l="extensions",f="processTelemetry",d="priority",v="eventsSent",h="eventsDiscarded",g="eventsSendRequest",p="perfEvent",m="offlineEventsStored",y="offlineBatchSent",I="offlineBatchDrop",b="getPerfMgr",w="domain",C="path",S="Not dynamic - "},1356:(e,n,t)=>{t.d(n,{h:()=>d});var r=t(8279),i=t(8205),o=t(269),a=t(9749),u=t(6182),c=t(6492),s={perfEvtsSendAll:!1};function l(e){e.h=null;var n=e.cb;e.cb=[],(0,o.Iuo)(n,(function(e){(0,o.gBW)(e.fn,[e.arg])}))}function f(e,n,t,r){(0,o.Iuo)(e,(function(e){e&&e[n]&&(t?(t.cb[u.y5]({fn:r,arg:e}),t.h=t.h||(0,o.dRz)(l,0,t)):(0,o.gBW)(r,[e]))}))}var d=function(){function e(n){var t,l;this.listeners=[];var d=[],v={h:null,cb:[]},h=(0,a.e)(n,s);l=h[u.x6]((function(e){t=!!e.cfg.perfEvtsSendAll})),(0,r.A)(e,this,(function(e){(0,o.vF1)(e,"listeners",{g:function(){return d}}),e[u.vR]=function(e){d[u.y5](e)},e[u.h3]=function(e){for(var n=(0,o.rDm)(d,e);n>-1;)d[u.Ic](n,1),n=(0,o.rDm)(d,e)},e[c.fc]=function(e){f(d,c.fc,v,(function(n){n[c.fc](e)}))},e[c.Yp]=function(e,n){f(d,c.Yp,v,(function(t){t[c.Yp](e,n)}))},e[c.dI]=function(e,n){f(d,c.dI,n?v:null,(function(t){t[c.dI](e,n)}))},e[c.l0]=function(e){e&&(!t&&e[u.Zu]()||f(d,c.l0,null,(function(n){e[u.tI]?(0,o.dRz)((function(){return n[c.l0](e)}),0):n[c.l0](e)})))},e[c.s4]=function(e){e&&e[u.oI]&&f(d,c.s4,v,(function(n){n[c.s4](e)}))},e[c.Vj]=function(e){e&&e[u.Cd]&&f(d,c.Vj,v,(function(n){n[c.Vj](e)}))},e[c.Ev]=function(e,n){if(e>0){var t=n||0;f(d,c.Ev,v,(function(n){n[c.Ev](e,t)}))}},e[u.M5]=function(e){var n,t=function(){l&&l.rm(),l=null,d=[],v.h&&v.h[u._w](),v.h=null,v.cb=[]};if(f(d,"unload",null,(function(t){var r=t[u.M5](e);r&&(n||(n=[]),n[u.y5](r))})),n)return(0,i.Qo)((function(e){return(0,i.Dv)((0,i.Xf)(n),(function(){t(),e()}))}));t()}}))}return e.__ieDyn=1,e}()},8156:(e,n,t)=>{t.d(n,{NS:()=>d,Q6:()=>f,Z4:()=>g,r2:()=>h});var r=t(8279),i=t(269),o=t(6182),a=t(6492),u="ctx",c="ParentContextKey",s="ChildrenContextKey",l=null,f=function(){function e(n,t,r){var a,l=this;l.start=(0,i.f0d)(),l[o.RS]=n,l[o.tI]=r,l[o.Zu]=function(){return!1},(0,i.Tnt)(t)&&(0,i.vF1)(l,"payload",{g:function(){return!a&&(0,i.Tnt)(t)&&(a=t(),t=null),a}}),l[o.O_]=function(n){return n?n===e[c]||n===e[s]?l[n]:(l[u]||{})[n]:null},l[o.e_]=function(n,t){n&&(n===e[c]?(l[n]||(l[o.Zu]=function(){return!0}),l[n]=t):n===e[s]?l[n]=t:(l[u]=l[u]||{})[n]=t)},l[o.Ru]=function(){var n=0,t=l[o.O_](e[s]);if((0,i.cyL)(t))for(var r=0;r<t[o.oI];r++){var a=t[r];a&&(n+=a[o.fA])}l[o.fA]=(0,i.f0d)()-l.start,l.exTime=l[o.fA]-n,l[o.Ru]=function(){}}}return e.ParentContextKey="parent",e.ChildrenContextKey="childEvts",e}(),d=function(){function e(n){this.ctx={},(0,r.A)(e,this,(function(e){e.create=function(e,n,t){return new f(e,n,t)},e.fire=function(e){e&&(e[o.Ru](),n&&(0,i.Tnt)(n[a.l0])&&n[a.l0](e))},e[o.e_]=function(n,t){n&&((e[u]=e[u]||{})[n]=t)},e[o.O_]=function(n){return(e[u]||{})[n]}}))}return e.__ieDyn=1,e}(),v="CoreUtils.doPerf";function h(e,n,t,r,i){if(e){var u=e;if(u[a.kI]&&(u=u[a.kI]()),u){var l=void 0,d=u[o.O_](v);try{if(l=u.create(n(),r,i)){if(d&&l[o.e_]&&(l[o.e_](f[c],d),d[o.O_]&&d[o.e_])){var h=d[o.O_](f[s]);h||(h=[],d[o.e_](f[s],h)),h[o.y5](l)}return u[o.e_](v,l),t(l)}}catch(e){l&&l[o.e_]&&l[o.e_]("exception",e)}finally{l&&u.fire(l),u[o.e_](v,d)}}}return t()}function g(){return l}},2317:(e,n,t)=>{t.d(n,{PV:()=>b,W0:()=>w,i8:()=>m,nU:()=>I,tS:()=>y});var r=t(269),i=t(991),o=t(9749),a=t(6182),u=t(3775),c=t(3673),s=t(6492),l=t(8156),f=t(380),d="TelemetryPluginChain",v="_hasRun",h="_getTelCtx",g=0;function p(e,n,t,c){var l=null,f=[];n||(n=(0,o.e)({},null,t[a.Uw])),null!==c&&(l=c?function(e,n,t){for(;e;){if(e[a.AP]()===t)return e;e=e[a.uR]()}return b([t],n.config||{},n)}(e,t,c):e);var d={_next:function(){var e=l;if(l=e?e[a.uR]():null,!e){var n=f;n&&n[a.oI]>0&&((0,r.Iuo)(n,(function(e){try{e.func.call(e.self,e.args)}catch(e){(0,u.ZP)(t[a.Uw],2,73,"Unexpected Exception during onComplete - "+(0,r.mmD)(e))}})),f=[])}return e},ctx:{core:function(){return t},diagLog:function(){return(0,u.y0)(t,n.cfg)},getCfg:function(){return n.cfg},getExtCfg:function(e,t){var o=v(e,!0);return t&&(0,r.zav)(t,(function(e,t){if((0,r.hXl)(o[e])){var a=n.cfg[e];!a&&(0,r.hXl)(a)||(o[e]=a)}(0,i.q)(n,o,e,t)})),n[a.h0](o,t)},getConfig:function(e,t,i){void 0===i&&(i=!1);var o,a=v(e,!1),u=n.cfg;return!a||!a[t]&&(0,r.hXl)(a[t])?!u[t]&&(0,r.hXl)(u[t])||(o=u[t]):o=a[t],o||!(0,r.hXl)(o)?o:i},hasNext:function(){return!!l},getNext:function(){return l},setNext:function(e){l=e},iterate:function(e){for(var n;n=d._next();){var t=n[a.AP]();t&&e(t)}},onComplete:function(e,n){for(var t=[],i=2;i<arguments.length;i++)t[i-2]=arguments[i];e&&f[a.y5]({func:e,self:(0,r.b07)(n)?d.ctx:n,args:t})}}};function v(e,t){var r=null,i=n.cfg;if(i&&e){var o=i[s.Bw];!o&&t&&(o={}),i[s.Bw]=o,(o=n.ref(i,s.Bw))&&(!(r=o[e])&&t&&(r={}),o[e]=r,r=n.ref(o,e))}return r}return d}function m(e,n,t,i){var u=(0,o.e)(n),c=p(e,u,t,i),l=c.ctx;return l[a.$5]=function(e){var n=c._next();return n&&n[s.qT](e,l),!n},l[a.$o]=function(e,n){return void 0===e&&(e=null),(0,r.cyL)(e)&&(e=b(e,u.cfg,t,n)),m(e||l[a.uR](),u.cfg,t,n)},l}function y(e,n,t){var i=(0,o.e)(n.config),u=p(e,i,n,t),c=u.ctx;return c[a.$5]=function(e){var n=u._next();return n&&n[a.M5](c,e),!n},c[a.$o]=function(e,t){return void 0===e&&(e=null),(0,r.cyL)(e)&&(e=b(e,i.cfg,n,t)),y(e||c[a.uR](),n,t)},c}function I(e,n,t){var i=(0,o.e)(n.config),u=p(e,i,n,t).ctx;return u[a.$5]=function(e){return u.iterate((function(n){(0,r.Tnt)(n[a.HC])&&n[a.HC](u,e)}))},u[a.$o]=function(e,t){return void 0===e&&(e=null),(0,r.cyL)(e)&&(e=b(e,i.cfg,n,t)),I(e||u[a.uR](),n,t)},u}function b(e,n,t,i){var o=null,c=!i;if((0,r.cyL)(e)&&e[a.oI]>0){var p=null;(0,r.Iuo)(e,(function(e){if(c||i!==e||(c=!0),c&&e&&(0,r.Tnt)(e[s.qT])){var y=function(e,n,t){var i,o=null,c=(0,r.Tnt)(e[s.qT]),p=(0,r.Tnt)(e[a.YH]),y={getPlugin:function(){return e},getNext:function(){return o},processTelemetry:function(i,u){I(u=u||function(){var i;return e&&(0,r.Tnt)(e[h])&&(i=e[h]()),i||(i=m(y,n,t)),i}(),(function(n){if(!e||!c)return!1;var t=(0,f.Cr)(e);return!t[a.Ik]&&!t[s.Hr]&&(p&&e[a.YH](o),e[s.qT](i,n),!0)}),"processTelemetry",(function(){return{item:i}}),!i.sync)||u[a.$5](i)},unload:function(n,t){I(n,(function(){var r=!1;if(e){var i=(0,f.Cr)(e),o=e[s.eT]||i[s.eT];!e||o&&o!==n.core()||i[a.Ik]||(i[s.eT]=null,i[a.Ik]=!0,i[a.tZ]=!1,e[a.Ik]&&!0===e[a.Ik](n,t)&&(r=!0))}return r}),"unload",(function(){}),t[a.tI])||n[a.$5](t)},update:function(n,t){I(n,(function(){var r=!1;if(e){var i=(0,f.Cr)(e),o=e[s.eT]||i[s.eT];!e||o&&o!==n.core()||i[a.Ik]||e[a.HC]&&!0===e[a.HC](n,t)&&(r=!0)}return r}),"update",(function(){}),!1)||n[a.$5](t)},_id:i=e?e[a.Ju]+"-"+e[s.Vo]+"-"+g++:"Unknown-0-"+g++,_setNext:function(e){o=e}};function I(n,t,c,f,h){var g=!1,p=e?e[a.Ju]:d,m=n[v];return m||(m=n[v]={}),n.setNext(o),e&&(0,l.r2)(n[s.eT](),(function(){return p+":"+c}),(function(){m[i]=!0;try{var e=o?o._id:s.m5;e&&(m[e]=!1),g=t(n)}catch(e){var l=!o||m[o._id];l&&(g=!0),o&&l||(0,u.ZP)(n[a.e4](),1,73,"Plugin ["+p+"] failed during "+c+" - "+(0,r.mmD)(e)+", run flags: "+(0,r.mmD)(m))}}),f,h),g}return(0,r.N6t)(y)}(e,n,t);o||(o=y),p&&p._setNext(y),p=y}}))}return i&&!o?b([i],n,t):o}var w=function(e,n,t,i){var o=m(e,n,t,i);(0,c.o$)(this,o,(0,r.cGk)(o))}},6535:(e,n,t)=>{t.d(n,{Si:()=>m,VN:()=>p,Z1:()=>g});var r=t(269),i=t(6182),o=t(7292),a=t(6492),u=4294967296,c=4294967295,s=123456789,l=987654321,f=!1,d=s,v=l;function h(){try{var e=2147483647&(0,r.f0d)();(n=(Math.random()*u^e)+e)<0&&(n>>>=0),d=s+n&c,v=l-n&c,f=!0}catch(e){}var n}function g(e){return e>0?Math.floor(p()/c*(e+1))>>>0:0}function p(e){var n=0,t=(0,o.MY)()||(0,o.iN)();return t&&t.getRandomValues&&(n=t.getRandomValues(new Uint32Array(1))[0]&c),0===n&&(0,o.lT)()&&(f||h(),n=function(e){var n=((v=36969*(65535&v)+(v>>16)&c)<<16)+(65535&(d=18e3*(65535&d)+(d>>16)&c))>>>0&c;return n>>>=0}()&c),0===n&&(n=Math.floor(u*Math.random()|0)),e||(n>>>=0),n}function m(e){void 0===e&&(e=22);for(var n=p()>>>0,t=0,r=a.m5;r[i.oI]<e;)t++,r+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(63&n),n>>>=6,5===t&&(n=(p()<<2&4294967295|3&n)>>>0,t=0);return r}},1190:(e,n,t)=>{t.d(n,{x:()=>u});var r=t(269),i=t(6182),o=t(3775),a=t(7292);function u(e,n){try{if(e&&""!==e){var t=(0,a.hm)().parse(e);if(t&&t[i.cp]&&t[i.cp]>=t.itemsAccepted&&t.itemsReceived-t.itemsAccepted===t.errors[i.oI])return t}}catch(t){(0,o.ZP)(n,1,43,"Cannot parse the response. "+(t[i.RS]||(0,r.mmD)(t)),{response:e})}return null}},856:(e,n,t)=>{t.d(n,{v:()=>h});var r=t(8279),i=t(8205),o=t(269),a=t(6182),u=t(7847),c=t(3775),s=t(7292),l=t(3673),f="",d="&NoResponseBody=true",v="POST",h=function(){function e(){var n,t,h,g,p,m,y,I,b,w,C,S,T,_,P=0;(0,r.A)(e,this,(function(e,r){var E=!0;function x(e,n){(0,c.ZP)(h,2,26,"Failed to send telemetry.",{message:e}),O(n,400,{})}function D(e){x("No endpoint url is provided for the batch",e)}function O(e,n,t,r){try{e&&e(n,t,r)}catch(e){}}function k(e,n){var t=(0,o.w3n)(),r=e[a.Vq];if(!r)return D(n),!0;r=e[a.Vq]+(T?d:f);var i=e[a.Cd],u=g?i:new Blob([i],{type:"text/plain;charset=UTF-8"});return t.sendBeacon(r,u)}function R(e,n,t){var r=e[a.Cd];try{if(r)if(k(e,n))O(n,200,{},f);else{var i=p&&p.beaconOnRetry;i&&(0,o.Tnt)(i)?i(e,n,k):(I&&I[a.L](e,n,!0),(0,c.ZP)(h,2,40,". Failed to send telemetry with Beacon API, retried with normal sender."))}}catch(e){g&&(0,c.OG)(h,"Failed to send telemetry using sendBeacon API. Ex:"+(0,o.mmD)(e)),O(n,g?0:400,{},f)}}function A(e,t,r){var u,c,s,d=e[a.c1]||{};!r&&n&&(u=(0,i.Qo)((function(e,n){c=e,s=n}))),g&&r&&e.disableXhrSync&&(r=!1);var h=e[a.Vq];if(!h)return D(t),void(c&&c(!1));var m=(0,l.H$)(v,h,E,!0,r,e[a.do]);function y(n){var r=p&&p.xhrOnComplete;if(r&&(0,o.Tnt)(r))r(n,t,e);else{var i=(0,l.Lo)(n);O(t,n[a.cV],(0,l.w3)(n,g),i)}}return g||m[a.yy]("Content-type","application/json"),(0,o.Iuo)((0,o.cGk)(d),(function(e){m[a.yy](e,d[e])})),m.onreadystatechange=function(){g||(y(m),4===m.readyState&&c&&c(!0))},m.onload=function(){g&&y(m)},m.onerror=function(e){O(t,g?m[a.cV]:400,(0,l.w3)(m,g),g?f:(0,l.r4)(m)),s&&s(e)},m.ontimeout=function(){O(t,g?m[a.cV]:500,(0,l.w3)(m,g),g?f:(0,l.r4)(m)),c&&c(!1)},m.send(e[a.Cd]),u}function L(e,t,r){var c,s,l,h,m=e[a.Vq],I=e[a.Cd],b=g?I:new Blob([I],{type:"application/json"}),w=new Headers,C=I[a.oI],S=!1,x=!1,k=e[a.c1]||{},R=((c={method:v,body:b})[u.x]=!0,c);e.headers&&(0,o.cGk)(e.headers)[a.oI]>0&&((0,o.Iuo)((0,o.cGk)(k),(function(e){w.append(e,k[e])})),R[a.c1]=w),y?R.credentials=y:E&&g&&(R.credentials="include"),r&&(R.keepalive=!0,P+=C,g?2===e._sendReason&&(S=!0,T&&(m+=d)):S=!0);var A=new Request(m,R);try{A[u.x]=!0}catch(e){}if(!r&&n&&(s=(0,i.Qo)((function(e,n){l=e,h=n}))),!m)return D(t),void(l&&l(!1));function L(e){O(t,g?0:400,{},g?f:e)}function M(e,n,r){var i=e[a.cV],u=p.fetchOnComplete;u&&(0,o.Tnt)(u)?u(e,t,r||f,n):O(t,i,{},r||f)}try{(0,i.Dv)(fetch(g?m:A,g?R:null),(function(n){if(r&&(P-=C,C=0),!x)if(x=!0,n.rejected)L(n.reason&&n.reason[a.pM]),h&&h(n.reason);else{var t=n[a.pF];try{g||t.ok?g&&!t.body?(M(t,null,f),l&&l(!0)):(0,i.Dv)(t.text(),(function(n){M(t,e,n[a.pF]),l&&l(!0)})):(L(t.statusText),l&&l(!1))}catch(e){L((0,o.mmD)(e)),h&&h(e)}}}))}catch(e){x||(L((0,o.mmD)(e)),h&&h(e))}return S&&!x&&(x=!0,O(t,200,{}),l&&l(!0)),g&&!x&&e[a.do]>0&&_&&_.set((function(){x||(x=!0,O(t,500,{}),l&&l(!0))}),e[a.do]),s}function M(e,n,t){var r=(0,o.zkX)(),i=new XDomainRequest,u=e[a.Cd];i.onload=function(){var t=(0,l.Lo)(i),r=p&&p.xdrOnComplete;r&&(0,o.Tnt)(r)?r(i,n,e):O(n,200,{},t)},i.onerror=function(){O(n,400,{},g?f:(0,l.HU)(i))},i.ontimeout=function(){O(n,500,{})},i.onprogress=function(){};var s=r&&r.location&&r.location[a.Qg]||"",d=e[a.Vq];if(d){if(!g&&0!==d.lastIndexOf(s,0)){var m="Cannot send XDomain request. The endpoint URL protocol doesn't match the hosting page protocol.";return(0,c.ZP)(h,2,40,". "+m),void x(m,n)}var y=g?d:d[a.W7](/^(https?:)/,"");i.open(v,y),e[a.do]&&(i[a.do]=e[a.do]),i.send(u),g&&t?_&&_.set((function(){i.send(u)}),0):i.send(u)}else D(n)}function N(){P=0,t=!1,n=!1,h=null,g=null,p=null,m=null,y=null,I=null,b=!1,w=!1,C=!1,S=!1,T=!1,_=null}N(),e[a.mE]=function(n,r){h=r,t&&(0,c.ZP)(h,1,28,"Sender is already initialized"),e.SetConfig(n),t=!0},e._getDbgPlgTargets=function(){return[t,g,m,n]},e.SetConfig=function(e){try{if(p=e.senderOnCompleteCallBack||{},m=!!e.disableCredentials,y=e.fetchCredentials,g=!!e.isOneDs,n=!!e.enableSendPromise,b=!!e.disableXhr,w=!!e.disableBeacon,C=!!e.disableBeaconSync,_=e.timeWrapper,T=!!e.addNoResponse,S=!!e.disableFetchKeepAlive,I={sendPOST:A},g||(E=!1),m){var t=(0,s.g$)();t&&t.protocol&&"file:"===t.protocol[a.OL]()&&(E=!1)}return!0}catch(e){}return!1},e.getSyncFetchPayload=function(){return P},e.getSenderInst=function(e,n){return e&&e[a.oI]?function(e,n){for(var t,r=0,i=null,o=0;null==i&&o<e[a.oI];)r=e[o],b||1!==r?2!==r||!(0,s.R7)(n)||n&&S?3!==r||!(0,s.Uf)()||(n?C:w)||(i=R):i=L:(0,s.PV)()?i=M:(0,s.xk)()&&(i=A),o++;return i?((t={_transport:r,_isSync:n})[a.L]=i,t):null}(e,n):null},e.getFallbackInst=function(){return I},e[a.tn]=function(e,n){N()}}))}return e.__ieDyn=1,e}()},380:(e,n,t)=>{t.d(n,{Cr:()=>s,Xc:()=>f,pI:()=>l,u7:()=>d});var r=t(269),i=t(6182),o=t(4276),a=t(6492),u=t(1864),c=(0,o.T)("plugin");function s(e){return c.get(e,"state",{},!0)}function l(e,n){for(var t,o=[],u=null,c=e[i.uR]();c;){var l=c[i.AP]();if(l){u&&u[i.YH]&&l[a.qT]&&u[i.YH](l);var f=!!(t=s(l))[i.tZ];l[i.tZ]&&(f=l[i.tZ]()),f||o[i.y5](l),u=l,c=c[i.uR]()}}(0,r.Iuo)(o,(function(r){var o=e[a.eT]();r[i.mE](e.getCfg(),o,n,e[i.uR]()),t=s(r),r[a.eT]||t[a.eT]||(t[a.eT]=o),t[i.tZ]=!0,delete t[i.Ik]}))}function f(e){return e.sort((function(e,n){var t=0;if(n){var r=n[a.qT];e[a.qT]?t=r?e[a.Vo]-n[a.Vo]:1:r&&(t=-1)}else t=e?1:-1;return t}))}function d(e){var n={};return{getName:function(){return n[i.RS]},setName:function(t){e&&e.setName(t),n[i.RS]=t},getTraceId:function(){return n[i.P5]},setTraceId:function(t){e&&e.setTraceId(t),(0,u.hX)(t)&&(n[i.P5]=t)},getSpanId:function(){return n[i.wi]},setSpanId:function(t){e&&e.setSpanId(t),(0,u.wN)(t)&&(n[i.wi]=t)},getTraceFlags:function(){return n[i.Rr]},setTraceFlags:function(t){e&&e.setTraceFlags(t),n[i.Rr]=t}}}},836:(e,n,t)=>{t.d(n,{P:()=>a});var r=t(269),i=t(6182),o=t(3775);function a(){var e=[];return{add:function(n){n&&e[i.y5](n)},run:function(n,t){(0,r.Iuo)(e,(function(e){try{e(n,t)}catch(e){(0,o.ZP)(n[i.e4](),2,73,"Unexpected error calling unload handler - "+(0,r.mmD)(e))}})),e=[]}}}},8969:(e,n,t)=>{t.d(n,{d:()=>c,w:()=>s});var r,i,o=t(269),a=t(6182),u=t(3775);function c(e,n){r=e,i=n}function s(){var e=[];return{run:function(n){var t=e;e=[],(0,o.Iuo)(t,(function(e){try{(e.rm||e.remove).call(e)}catch(e){(0,u.ZP)(n,2,73,"Unloading:"+(0,o.mmD)(e))}})),r&&t[a.oI]>r&&(i?i("doUnload",t):(0,u.ZP)(null,1,48,"Max unload hooks exceeded. An excessive number of unload hooks has been detected."))},add:function(n){n&&((0,o.Yny)(e,n),r&&e[a.oI]>r&&(i?i("Add",e):(0,u.ZP)(null,1,48,"Max unload hooks exceeded. An excessive number of unload hooks has been detected.")))}}}},1864:(e,n,t)=>{t.d(n,{L0:()=>S,N7:()=>C,V5:()=>_,ZI:()=>y,ef:()=>T,hX:()=>I,mJ:()=>w,wN:()=>b,wk:()=>m});var r=t(269),i=t(6182),o=t(9882),a=t(7292),u=t(6492),c=/^([\da-f]{2})-([\da-f]{32})-([\da-f]{16})-([\da-f]{2})(-[^\s]{1,64})?$/i,s="00",l="ff",f="00000000000000000000000000000000",d="0000000000000000",v=1;function h(e,n,t){return!(!e||e[i.oI]!==n||e===t||!e.match(/^[\da-f]*$/i))}function g(e,n,t){return h(e,n)?e:t}function p(e){(isNaN(e)||e<0||e>255)&&(e=1);for(var n=e.toString(16);n[i.oI]<2;)n="0"+n;return n}function m(e,n,t,a){var u;return(u={})[i.s]=h(a,2,l)?a:s,u[i.P5]=I(e)?e:(0,o.cL)(),u[i.wi]=b(n)?n:(0,r.ZWZ)((0,o.cL)(),16),u.traceFlags=t>=0&&t<=255?t:1,u}function y(e,n){var t;if(!e)return null;if((0,r.cyL)(e)&&(e=e[0]||""),!e||!(0,r.KgX)(e)||e[i.oI]>8192)return null;if(-1!==e.indexOf(",")){var o=e[i.sY](",");e=o[n>0&&o[i.oI]>n?n:0]}var a=c.exec((0,r.EHq)(e));return a&&a[1]!==l&&a[2]!==f&&a[3]!==d?((t={version:(a[1]||u.m5)[i.OL](),traceId:(a[2]||u.m5)[i.OL](),spanId:(a[3]||u.m5)[i.OL]()})[i.Rr]=parseInt(a[4],16),t):null}function I(e){return h(e,32,f)}function b(e){return h(e,16,d)}function w(e){return!!(e&&h(e[i.s],2,l)&&h(e[i.P5],32,f)&&h(e[i.wi],16,d)&&h(p(e[i.Rr]),2))}function C(e){return!!w(e)&&(e[i.Rr]&v)===v}function S(e){if(e){var n=p(e[i.Rr]);h(n,2)||(n="01");var t=e[i.s]||s;return"00"!==t&&"ff"!==t&&(t=s),"".concat(t.toLowerCase(),"-").concat(g(e.traceId,32,f).toLowerCase(),"-").concat(g(e.spanId,16,d).toLowerCase(),"-").concat(n.toLowerCase())}return""}function T(e){var n="traceparent",t=y((0,a.$Z)(n),e);return t||(t=y((0,a.Iu)(n),e)),t}function _(e){var n=e.getElementsByTagName("script"),t=[];return(0,r.Iuo)(n,(function(e){var n=e[i.NA]("src");if(n){var r=e[i.NA]("crossorigin"),o=!0===e.hasAttribute("async"),a=!0===e.hasAttribute("defer"),u=e[i.NA]("referrerpolicy"),c={url:n};r&&(c.crossOrigin=r),o&&(c.async=o),a&&(c.defer=a),u&&(c.referrerPolicy=u),t[i.y5](c)}})),t}},6182:(e,n,t)=>{t.d(n,{$5:()=>k,$o:()=>H,AP:()=>E,Az:()=>oe,Bl:()=>z,Cd:()=>ce,DI:()=>R,Di:()=>M,FI:()=>D,HC:()=>W,Ic:()=>g,Ik:()=>X,JQ:()=>i,JR:()=>B,Ju:()=>I,K0:()=>p,L:()=>he,M5:()=>N,NA:()=>we,OL:()=>r,O_:()=>se,P5:()=>ye,QM:()=>te,Qg:()=>$,RF:()=>P,RS:()=>x,Rr:()=>be,Ru:()=>fe,Uw:()=>d,Vq:()=>ve,W7:()=>ee,XM:()=>L,XW:()=>a,YH:()=>J,Yo:()=>re,Yq:()=>T,Zu:()=>ue,_w:()=>m,by:()=>F,c1:()=>ge,cV:()=>ie,cp:()=>de,do:()=>pe,e4:()=>V,e_:()=>le,fA:()=>O,h0:()=>l,h3:()=>b,h4:()=>A,ih:()=>s,mE:()=>y,oI:()=>o,on:()=>c,pF:()=>_,pM:()=>j,re:()=>Y,s:()=>U,sY:()=>Q,sl:()=>S,sx:()=>ne,tI:()=>q,tX:()=>G,tZ:()=>C,tn:()=>K,uR:()=>Z,vR:()=>w,wJ:()=>ae,wi:()=>Ie,x6:()=>f,y5:()=>h,y9:()=>v,yy:()=>me,zs:()=>u});var r="toLowerCase",i="blkVal",o="length",a="rdOnly",u="notify",c="warnToConsole",s="throwInternal",l="setDf",f="watch",d="logger",v="apply",h="push",g="splice",p="hdlr",m="cancel",y="initialize",I="identifier",b="removeNotificationListener",w="addNotificationListener",C="isInitialized",S="instrumentationKey",T="INACTIVE",_="value",P="getNotifyMgr",E="getPlugin",x="name",D="iKey",O="time",k="processNext",R="getProcessTelContext",A="pollInternalLogs",L="enabled",M="stopPollingInternalLogs",N="unload",F="onComplete",U="version",z="loggingLevelConsole",H="createNew",X="teardown",B="messageId",j="message",q="isAsync",V="diagLog",K="_doTeardown",W="update",Z="getNext",J="setNextPlugin",$="protocol",G="userAgent",Q="split",Y="nodeType",ee="replace",ne="logInternalMessage",te="type",re="handler",ie="status",oe="getResponseHeader",ae="getAllResponseHeaders",ue="isChildEvt",ce="data",se="getCtx",le="setCtx",fe="complete",de="itemsReceived",ve="urlString",he="sendPOST",ge="headers",pe="timeout",me="setRequestHeader",ye="traceId",Ie="spanId",be="traceFlags",we="getAttribute"},5664:(e,n,t)=>{t.d(n,{Wy:()=>c,_1:()=>i,bA:()=>o,hW:()=>r,s6:()=>u,vR:()=>a});var r="function",i="object",o="undefined",a="prototype",u=Object,c=u[a]},659:(e,n,t)=>{t.d(n,{Im:()=>a,qU:()=>c,vz:()=>s});var r=t(269),i=t(5664),o=(((0,r.mS$)()||{}).Symbol,((0,r.mS$)()||{}).Reflect,"hasOwnProperty"),a=r.vE3||function(e){for(var n,t=1,r=arguments.length;t<r;t++)for(var a in n=arguments[t])i.Wy[o].call(n,a)&&(e[a]=n[a]);return e},u=function(e,n){return u=i.s6.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,n){e.__proto__=n}||function(e,n){for(var t in n)n[o](t)&&(e[t]=n[t])},u(e,n)};function c(e,n){function t(){this.constructor=e}typeof n!==i.hW&&null!==n&&(0,r.zkd)("Class extends value "+String(n)+" is not a constructor or null"),u(e,n),e[i.vR]=null===n?(0,r.sSX)(n):(t[i.vR]=n[i.vR],new t)}function s(e,n){for(var t=0,r=n.length,i=e.length;t<r;t++,i++)e[i]=n[t];return e}},5927:(e,n,t)=>{t.r(n),t.d(n,{AppInsightsCore:()=>yn._,ApplicationInsights:()=>Dn,Sender:()=>pn,SeverityLevel:()=>Pn.O,arrForEach:()=>w.Iuo,isNullOrUndefined:()=>w.hXl,proxyFunctions:()=>b.o$,throwError:()=>w.$8});var r=t(8279),i=t(659),o=t(5025),a=t(3072),u=t(2445),c=t(1448),s=t(4164),l=t(5397),f=t(5014),d=t(1365),v=t(740),h=t(5571),g=t(4658),p=t(2318),m=t(2910),y=t(2475),I=t(3775),b=t(3673),w=t(269),C=t(6149),S=t(4276),T=t(9749),_=t(2317),P=t(4875),E=t(7292),x=t(856),D=t(4013),O=t(1190),k=t(8257),R=t(1575),A=t(7975),L=t(1062),M=t(7358),N="duration",F="tags",U="deviceType",z="data",H="name",X="traceID",B="length",j="stringify",q="measurements",V="dataType",K="envelopeType",W="toString",Z="_get",J="enqueue",$="count",G="eventsLimitInMem",Q="push",Y="item",ee="emitLineDelimitedJson",ne="clear",te="createNew",re="markAsSent",ie="clearSent",oe="bufferOverride",ae="BUFFER_KEY",ue="SENT_BUFFER_KEY",ce="concat",se="MAX_BUFFER_SIZE",le="triggerSend",fe="diagLog",de="initialize",ve="_sender",he="endpointUrl",ge="instrumentationKey",pe="customHeaders",me="maxBatchSizeInBytes",ye="onunloadDisableBeacon",Ie="isBeaconApiDisabled",be="alwaysUseXhrOverride",we="disableXhr",Ce="enableSessionStorageBuffer",Se="_buffer",Te="onunloadDisableFetch",_e="disableSendBeaconSplit",Pe="enableSendPromise",Ee="getSenderInst",xe="unloadTransports",De="convertUndefined",Oe="maxBatchInterval",ke="serialize",Re="_onError",Ae="_onPartialSuccess",Le="_onSuccess",Me="itemsReceived",Ne="itemsAccepted",Fe="oriPayload",Ue="baseType",ze="sampleRate",He="eventsSendRequest",Xe="getSamplingScore",Be="baseType",je="baseData",qe="properties",Ve="true";function Ke(e,n,t){return(0,b.KY)(e,n,t,w.zzB)}function We(e,n,t){(0,w.hXl)(e)||(0,w.zav)(e,(function(e,r){(0,w.EtT)(r)?t[e]=r:(0,w.KgX)(r)?n[e]=r:(0,E.Z)()&&(n[e]=(0,E.hm)()[j](r))}))}function Ze(e,n){(0,w.hXl)(e)||(0,w.zav)(e,(function(t,r){e[t]=r||n}))}function Je(e,n,t,r){var a=new L.L(e,r,n);Ke(a,"sampleRate",t[o.tU]),(t[je]||{}).startTime&&(a.time=(0,b._u)(t[je].startTime)),a.iKey=t.iKey;var u=t.iKey.replace(/-/g,"");return a[H]=a[H].replace("{0}",u),function(e,n,t){var r=t[F]=t[F]||{},o=n.ext=n.ext||{},a=n[F]=n[F]||[],u=o.user;u&&(Ke(r,R.O.userAuthUserId,u.authId),Ke(r,R.O.userId,u.id||u.localId));var c=o.app;c&&Ke(r,R.O.sessionId,c.sesId);var s=o.device;s&&(Ke(r,R.O.deviceId,s.id||s.localId),Ke(r,R.O[U],s.deviceClass),Ke(r,R.O.deviceIp,s.ip),Ke(r,R.O.deviceModel,s.model),Ke(r,R.O[U],s[U]));var l=n.ext.web;if(l){Ke(r,R.O.deviceLanguage,l.browserLang),Ke(r,R.O.deviceBrowserVersion,l.browserVer),Ke(r,R.O.deviceBrowser,l.browser);var f=t[z]=t[z]||{},d=f[je]=f[je]||{},v=d[qe]=d[qe]||{};Ke(v,"domain",l.domain),Ke(v,"isManual",l.isManual?Ve:null),Ke(v,"screenRes",l.screenRes),Ke(v,"userConsent",l.userConsent?Ve:null)}var h=o.os;h&&(Ke(r,R.O.deviceOS,h[H]),Ke(r,R.O.deviceOSVersion,h.osVer));var g=o.trace;g&&(Ke(r,R.O.operationParentId,g.parentID),Ke(r,R.O.operationName,(0,A.Rr)(e,g[H])),Ke(r,R.O.operationId,g[X]));for(var p={},m=a[B]-1;m>=0;m--){var y=a[m];(0,w.zav)(y,(function(e,n){p[e]=n})),a.splice(m,1)}(0,w.zav)(a,(function(e,n){p[e]=n}));var I=(0,i.Im)((0,i.Im)({},r),p);I[R.O.internalSdkVersion]||(I[R.O.internalSdkVersion]=(0,A.Rr)(e,"javascript:".concat(Ge.Version),64)),t[F]=(0,b.hW)(I)}(e,t,a),t[F]=t[F]||[],(0,b.hW)(a)}function $e(e,n){(0,w.hXl)(n[je])&&(0,I.ZP)(e,1,46,"telemetryItem.baseData cannot be null.")}var Ge={Version:"3.3.4"};function Qe(e,n,t){$e(e,n);var r={},i={};n[Be]!==a.J[V]&&(r.baseTypeSource=n[Be]),n[Be]===a.J[V]?(r=n[je][qe]||{},i=n[je][q]||{}):n[je]&&We(n[je],r,i),We(n[z],r,i),(0,w.hXl)(t)||Ze(r,t);var o=n[je][H],u=new a.J(e,o,r,i),c=new M.B(a.J[V],u);return Je(e,a.J[K],n,c)}var Ye,en,nn=function(){function e(n,t){var i=[],o=!1,a=t.maxRetryCnt;this[Z]=function(){return i},this._set=function(e){return i=e},(0,r.A)(e,this,(function(e){e[J]=function(r){e[$]()>=t[G]?o||((0,I.ZP)(n,2,105,"Maximum in-memory buffer size reached: "+e[$](),!0),o=!0):(r.cnt=r.cnt||0,!(0,w.hXl)(a)&&r.cnt>a||i[Q](r))},e[$]=function(){return i[B]},e.size=function(){for(var e=i[B],n=0;n<i[B];n++)e+=i[n].item[B];return t[ee]||(e+=2),e},e[ne]=function(){i=[],o=!1},e.getItems=function(){return i.slice(0)},e.batchPayloads=function(e){if(e&&e[B]>0){var n=[];return(0,w.Iuo)(e,(function(e){n[Q](e[Y])})),t[ee]?n.join("\n"):"["+n.join(",")+"]"}return null},e[te]=function(e,t,r){var o=i.slice(0);e=e||n,t=t||{};var a=r?new on(e,t):new tn(e,t);return(0,w.Iuo)(o,(function(e){a[J](e)})),a}}))}return e.__ieDyn=1,e}(),tn=function(e){function n(t,i){var o=e.call(this,t,i)||this;return(0,r.A)(n,o,(function(e,n){e[re]=function(e){n[ne]()},e[ie]=function(e){}})),o}return(0,i.qU)(n,e),n.__ieDyn=1,n}(nn),rn=["AI_buffer","AI_sentBuffer"],on=function(e){function n(t,i){var o=e.call(this,t,i)||this,a=!1,u=null==i?void 0:i.namePrefix,c=i[oe]||{getItem:g.vH,setItem:g.Dt},s=c.getItem,l=c.setItem,f=i.maxRetryCnt;return(0,r.A)(n,o,(function(e,r){var i=p(n[ae]),o=p(n[ue]),c=function(){var e=[];try{return(0,w.Iuo)(rn,(function(n){var t=C(n);if(e=e[ce](t),u){var r=C(u+"_"+n);e=e[ce](r)}})),e}catch(e){(0,I.ZP)(t,2,41,"Transfer events from previous buffers: "+(0,b.lL)(e)+". previous Buffer items can not be removed",{exception:(0,w.mmD)(e)})}return[]}(),d=o[ce](c),v=e._set(i[ce](d));function h(e,n){var t=[],r=[];return(0,w.Iuo)(e,(function(e){r[Q](e[Y])})),(0,w.Iuo)(n,(function(e){(0,w.Tnt)(e)||-1!==(0,w.rDm)(r,e[Y])||t[Q](e)})),t}function p(e){return m(u?u+"_"+e:e)}function m(e){try{var n=s(t,e);if(n){var r=(0,E.hm)().parse(n);if((0,w.KgX)(r)&&(r=(0,E.hm)().parse(r)),r&&(0,w.cyL)(r))return r}}catch(n){(0,I.ZP)(t,1,42," storage key: "+e+", "+(0,b.lL)(n),{exception:(0,w.mmD)(n)})}return[]}function y(e,n){var r=e;try{r=u?u+"_"+r:r;var i=JSON[j](n);l(t,r,i)}catch(e){l(t,r,JSON[j]([])),(0,I.ZP)(t,2,41," storage key: "+r+", "+(0,b.lL)(e)+". Buffer cleared",{exception:(0,w.mmD)(e)})}}function C(e){try{var n=m(e),r=[];return(0,w.Iuo)(n,(function(e){var n={item:e,cnt:0};r[Q](n)})),(0,g.v7)(t,e),r}catch(e){}return[]}v[B]>n[se]&&(v[B]=n[se]),y(n[ue],[]),y(n[ae],v),e[J]=function(i){e[$]()>=n[se]?a||((0,I.ZP)(t,2,67,"Maximum buffer size reached: "+e[$](),!0),a=!0):(i.cnt=i.cnt||0,!(0,w.hXl)(f)&&i.cnt>f||(r[J](i),y(n.BUFFER_KEY,e[Z]())))},e[ne]=function(){r[ne](),y(n.BUFFER_KEY,e[Z]()),y(n[ue],[]),a=!1},e[re]=function(r){y(n[ae],e._set(h(r,e[Z]())));var i=p(n[ue]);i instanceof Array&&r instanceof Array&&((i=i[ce](r))[B]>n[se]&&((0,I.ZP)(t,1,67,"Sent buffer reached its maximum size: "+i[B],!0),i[B]=n[se]),y(n[ue],i))},e[ie]=function(e){var t=p(n[ue]);t=h(e,t),y(n[ue],t)},e[te]=function(r,i,o){o=!!o;var a=e[Z]().slice(0),u=p(n[ue]).slice(0);r=r||t,i=i||{},e[ne]();var c=o?new n(r,i):new tn(r,i);return(0,w.Iuo)(a,(function(e){c[J](e)})),o&&c[re](u),c}})),o}var t;return(0,i.qU)(n,e),t=n,n.VERSION="_1",n.BUFFER_KEY="AI_buffer"+t.VERSION,n.SENT_BUFFER_KEY="AI_sentBuffer"+t.VERSION,n.MAX_BUFFER_SIZE=2e3,n}(nn),an=function(){function e(n){(0,r.A)(e,this,(function(e){function t(e,o){var a="__aiCircularRefCheck",u={};if(!e)return(0,I.ZP)(n,1,48,"cannot serialize object because it is null or undefined",{name:o},!0),u;if(e[a])return(0,I.ZP)(n,2,50,"Circular reference detected while serializing object",{name:o},!0),u;if(!e.aiDataContract){if("measurements"===o)u=i(e,"number",o);else if("properties"===o)u=i(e,"string",o);else if("tags"===o)u=i(e,"string",o);else if((0,w.cyL)(e))u=r(e,o);else{(0,I.ZP)(n,2,49,"Attempting to serialize an object which does not implement ISerializable",{name:o},!0);try{(0,E.hm)()[j](e),u=e}catch(e){(0,I.ZP)(n,1,48,e&&(0,w.Tnt)(e[W])?e[W]():"Error serializing object",null,!0)}}return u}return e[a]=!0,(0,w.zav)(e.aiDataContract,(function(i,a){var c=(0,w.Tnt)(a)?1&a():1&a,s=(0,w.Tnt)(a)?4&a():4&a,l=2&a,f=void 0!==e[i],d=(0,w.Gvm)(e[i])&&null!==e[i];if(!c||f||l){if(!s){var v;void 0!==(v=d?l?r(e[i],i):t(e[i],i):e[i])&&(u[i]=v)}}else(0,I.ZP)(n,1,24,"Missing required field specification. The field is required but not present on source",{field:i,name:o})})),delete e[a],u}function r(e,r){var i;if(e)if((0,w.cyL)(e)){i=[];for(var o=0;o<e[B];o++){var a=t(e[o],r+"["+o+"]");i[Q](a)}}else(0,I.ZP)(n,1,54,"This field was specified as an array in the contract but the item is not an array.\r\n",{name:r},!0);return i}function i(e,t,r){var i;return e&&(i={},(0,w.zav)(e,(function(e,o){if("string"===t)void 0===o?i[e]="undefined":null===o?i[e]="null":o[W]?i[e]=o[W]():i[e]="invalid field: toString() is not defined.";else if("number"===t)if(void 0===o)i[e]="undefined";else if(null===o)i[e]="null";else{var a=parseFloat(o);i[e]=a}else i[e]="invalid field: "+r+" is of unknown type.",(0,I.ZP)(n,1,i[e],null,!0)}))),i}e[ke]=function(e){var r=t(e,"root");try{return(0,E.hm)()[j](r)}catch(e){(0,I.ZP)(n,1,48,e&&(0,w.Tnt)(e[W])?e[W]():"Error serializing object",null,!0)}}}))}return e.__ieDyn=1,e}(),un=t(8596),cn=function(){function e(){}return e.prototype.getHashCodeScore=function(n){return this.getHashCode(n)/e.INT_MAX_VALUE*100},e.prototype.getHashCode=function(e){if(""===e)return 0;for(;e[B]<8;)e=e[ce](e);for(var n=5381,t=0;t<e[B];++t)n=(n<<5)+n+e.charCodeAt(t),n|=0;return Math.abs(n)},e.INT_MAX_VALUE=2147483647,e}(),sn=function(){var e=new cn,n=new un.o;this[Xe]=function(t){return t[F]&&t[F][n.userId]?e.getHashCodeScore(t[F][n.userId]):t.ext&&t.ext.user&&t.ext.user.id?e.getHashCodeScore(t.ext.user.id):t[F]&&t[F][n.operationId]?e.getHashCodeScore(t[F][n.operationId]):t.ext&&t.ext.telemetryTrace&&t.ext.telemetryTrace[X]?e.getHashCodeScore(t.ext.telemetryTrace[X]):100*Math.random()}},ln=function(){function e(e,n){this.INT_MAX_VALUE=2147483647;var t=n||(0,I.y0)(null);(e>100||e<0)&&(t.throwInternal(2,58,"Sampling rate is out of range (0..100). Sampling will be disabled, you may be sending too much data which may affect your AI service level.",{samplingRate:e},!0),e=100),this[ze]=e,this.samplingScoreGenerator=new sn}return e.prototype.isSampledIn=function(e){var n=this[ze];return null==n||n>=100||e.baseType===f.J[V]||this.samplingScoreGenerator[Xe](e)<n},e}(),fn=void 0;function dn(e){try{return e.responseText}catch(e){}return null}var vn,hn=(0,w.ZHX)(((Ye={endpointUrl:(0,y.Lx)(w.zzB,o._G+o.wc)})[ee]=(0,y.DD)(),Ye[Oe]=15e3,Ye[me]=102400,Ye.disableTelemetry=(0,y.DD)(),Ye[Ce]=(0,y.DD)(!0),Ye.isRetryDisabled=(0,y.DD)(),Ye[Ie]=(0,y.DD)(!0),Ye[_e]=(0,y.DD)(!0),Ye[we]=(0,y.DD)(),Ye[Te]=(0,y.DD)(),Ye[ye]=(0,y.DD)(),Ye[ge]=fn,Ye.namePrefix=fn,Ye.samplingPercentage=(0,y.Lx)((function(e){return!isNaN(e)&&e>0&&e<=100}),100),Ye[pe]=fn,Ye[De]=fn,Ye[G]=1e4,Ye[oe]=!1,Ye.httpXHROverride={isVal:function(e){return e&&e.sendPOST},v:fn},Ye[be]=(0,y.DD)(),Ye.transports=fn,Ye.retryCodes=fn,Ye.maxRetryCnt={isVal:w.EtT,v:10},Ye)),gn=((en={})[a.J.dataType]=Qe,en[u.C.dataType]=function(e,n,t){$e(e,n);var r=n[je].message,i=n[je].severityLevel,o=n[je][qe]||{},a=n[je][q]||{};We(n[z],o,a),(0,w.hXl)(t)||Ze(o,t);var c=new u.C(e,r,i,o,a),s=new M.B(u.C[V],c);return Je(e,u.C[K],n,s)},en[c.h.dataType]=function(e,n,t){var r;$e(e,n);var i=n[je];(0,w.hXl)(i)||(0,w.hXl)(i[qe])||(0,w.hXl)(i[qe][N])?(0,w.hXl)(n[z])||(0,w.hXl)(n[z][N])||(r=n[z][N],delete n[z][N]):(r=i[qe][N],delete i[qe][N]);var o,a=n[je];((n.ext||{}).trace||{})[X]&&(o=n.ext.trace[X]);var u=a.id||o,s=a[H],l=a.uri,f=a[qe]||{},d=a[q]||{};if((0,w.hXl)(a.refUri)||(f.refUri=a.refUri),(0,w.hXl)(a.pageType)||(f.pageType=a.pageType),(0,w.hXl)(a.isLoggedIn)||(f.isLoggedIn=a.isLoggedIn[W]()),!(0,w.hXl)(a[qe])){var v=a[qe];(0,w.zav)(v,(function(e,n){f[e]=n}))}We(n[z],f,d),(0,w.hXl)(t)||Ze(f,t);var h=new c.h(e,s,l,r,f,d,u),g=new M.B(c.h[V],h);return Je(e,c.h[K],n,g)},en[s.H.dataType]=function(e,n,t){$e(e,n);var r=n[je],i=r[H],o=r.uri||r.url,a=r[qe]||{},u=r[q]||{};We(n[z],a,u),(0,w.hXl)(t)||Ze(a,t);var c=new s.H(e,i,o,void 0,a,u,r),l=new M.B(s.H[V],c);return Je(e,s.H[K],n,l)},en[l.WJ.dataType]=function(e,n,t){$e(e,n);var r=n[je][q]||{},i=n[je][qe]||{};We(n[z],i,r),(0,w.hXl)(t)||Ze(i,t);var o=n[je],a=l.WJ.CreateFromInterface(e,o,i,r),u=new M.B(l.WJ[V],a);return Je(e,l.WJ[K],n,u)},en[f.J.dataType]=function(e,n,t){$e(e,n);var r=n[je],i=r[qe]||{},o=r[q]||{};We(n[z],i,o),(0,w.hXl)(t)||Ze(i,t);var a=new f.J(e,r[H],r.average,r.sampleCount,r.min,r.max,r.stdDev,i,o),u=new M.B(f.J[V],a);return Je(e,f.J[K],n,u)},en[d.A.dataType]=function(e,n,t){$e(e,n);var r=n[je][q]||{},i=n[je][qe]||{};We(n[z],i,r),(0,w.hXl)(t)||Ze(i,t);var a=n[je];if((0,w.hXl)(a))return(0,I.OG)(e,"Invalid input for dependency data"),null;var u=a[qe]&&a[qe][o.ym]?a[qe][o.ym]:"GET",c=new d.A(e,a.id,a.target,a[H],a[N],a.success,a.responseCode,u,a.type,a.correlationContext,i,r),s=new M.B(d.A[V],c);return Je(e,d.A[K],n,s)},en),pn=function(e){function n(){var t,i,a,u,c,s,l,f=e.call(this)||this;f.priority=1001,f.identifier=v.BreezeChannelIdentifier;var d,y,k,R,A,L,M,N,U,H,X,j,q,V,K,W,Z,G,ee,ae,ue,ce,se,Xe,Be,je,qe,Ve=0;return(0,r.A)(n,f,(function(e,r){function v(n,r){var i=dn(n);if(!n||i+""!="200"&&""!==i){var o=(0,O.x)(i);o&&o[Me]&&o[Me]>o[Ne]&&!V?e[Ae](r,o):e[Re](r,(0,b.HU)(n))}else t=0,e[Le](r,0)}function Ke(e,n,t){4===e.readyState&&un(e.status,n,e.responseURL,t,(0,b.r4)(e),dn(e)||e.response)}function We(e){try{if(e){var n=e[Fe];return n&&n[B]?n:null}}catch(e){}return null}function Ze(n,t){return!(X||(n?n.baseData&&!n[Ue]?(t&&(0,I.ZP)(t,1,70,"Cannot send telemetry without baseData and baseType"),1):(n[Ue]||(n[Ue]="EventData"),e[ve]?(r=n,e._sample.isSampledIn(r)?(n[o.tU]=e._sample[ze],0):(t&&(0,I.ZP)(t,2,33,"Telemetry item was sampled out and not sent",{SampleRate:e._sample[ze]}),1)):(t&&(0,I.ZP)(t,1,28,"Sender was not initialized"),1)):(t&&(0,I.ZP)(t,1,7,"Cannot send empty telemetry"),1)));var r}function Je(e,t){var r=e.iKey||j,i=n.constructEnvelope(e,r,t,q);if(i){var a=!1;if(e[F]&&e[F][o.jp]&&((0,w.Iuo)(e[F][o.jp],(function(e){try{e&&!1===e(i)&&(a=!0,(0,I.OG)(t,"Telemetry processor check returns false"))}catch(e){(0,I.ZP)(t,1,64,"One of telemetry initializers failed, telemetry item will not be sent: "+(0,b.lL)(e),{exception:(0,w.mmD)(e)},!0)}})),delete e[F][o.jp]),!a)return i}else(0,I.ZP)(t,1,47,"Unable to create an AppInsights envelope")}function $e(n){var t="",r=e[fe]();try{var i=Ze(n,r),o=null;i&&(o=Je(n,r)),o&&(t=c[ke](o))}catch(e){}return t}function Ge(e){var n="";return e&&e[B]&&(n="["+e.join(",")+"]"),n}function Qe(e){var n,t=nn();return(n={urlString:R})[z]=e,n.headers=t,n}function Ye(n,t,r,i){void 0===i&&(i=!0);var o=en(t),a=n&&n.sendPOST;return a&&o?(i&&e._buffer[re](t),a(o,(function(n,r,i){return function(n,t,r,i){200===t&&n?e._onSuccess(n,n[B]):i&&e[Re](n,i)}(t,n,0,i)}),!r)):null}function en(n){var t;if((0,w.cyL)(n)&&n[B]>0){var r=e[Se].batchPayloads(n),i=nn();return(t={})[z]=r,t.urlString=R,t.headers=i,t.disableXhrSync=ue,t.disableFetchKeepAlive=!ce,t[Fe]=n,t}return null}function nn(){try{var e=l||{};return(0,p.Qu)(R)&&(e[m.a[6]]=m.a[7]),e}catch(e){}return null}function rn(n){var t=n?n[B]:0;return e[Se].size()+t>L&&(y&&!y.isOnline()||e[le](!0,null,10),!0)}function un(n,r,i,o,a,u){var c=null;if(e._appId||(c=(0,O.x)(u))&&c.appId&&(e._appId=c.appId),(n<200||n>=300)&&0!==n){if((301===n||307===n||308===n)&&!cn(i))return void e[Re](r,a);if(y&&!y.isOnline())return void(V||(pn(r,10),(0,I.ZP)(e[fe](),2,40,". Offline - Response Code: ".concat(n,". Offline status: ").concat(!y.isOnline(),". Will retry to send ").concat(r.length," items."))));!V&&In(n)?(pn(r),(0,I.ZP)(e[fe](),2,40,". Response code "+n+". Will retry to send "+r[B]+" items.")):e[Re](r,a)}else cn(i),206===n?(c||(c=(0,O.x)(u)),c&&!V?e[Ae](r,c):e[Re](r,a)):(t=0,e[Le](r,o))}function cn(e){return!(s>=10||(0,w.hXl)(e)||""===e||e===R||(R=e,++s,0))}function sn(e,n){if(!d)return Ye(je&&je[Ee]([3],!0),e,n);d(e,!1)}function vn(e){try{if(e&&e[B])return(0,w.KgX)(e[0])}catch(e){}return null}function gn(n,t){var r=null;if((0,w.cyL)(n)){for(var i=n[B],o=0;o<n[B];o++)i+=n[o].item[B];return je.getSyncFetchPayload()+i<=65e3?r=2:(0,E.Uf)()?r=3:(r=1,(0,I.ZP)(e[fe](),2,40,". Failed to send telemetry with Beacon API, retried with xhrSender.")),Ye(je&&je[Ee]([r],!0),n,t)}return null}function pn(n,r){if(void 0===r&&(r=1),n&&0!==n[B]){var o=e[Se];o[ie](n),t++;for(var a=0,u=n;a<u.length;a++){var c=u[a];c.cnt=c.cnt||0,c.cnt++,o[J](c)}!function(e){var n;if(t<=1)n=10;else{var r=(Math.pow(2,t)-1)/2,o=Math.floor(Math.random()*r*10)+1;o*=e,n=Math.max(Math.min(o,3600),10)}var a=(0,w.f0d)()+1e3*n;i=a}(r),mn()}}function mn(){if(!u&&!a){var n=i?Math.max(0,i-(0,w.f0d)()):0,t=Math.max(K,n);u=(0,w.dRz)((function(){u=null,e[le](!0,null,1)}),t)}}function yn(){u&&u.cancel(),u=null,i=null}function In(e){return(0,w.hXl)(qe)?401===e||408===e||429===e||500===e||502===e||503===e||504===e:qe[B]&&qe.indexOf(e)>-1}function bn(){e[ve]=null,e[Se]=null,e._appId=null,e._sample=null,l={},y=null,t=0,i=null,a=!1,u=null,c=null,s=0,Ve=0,d=null,k=null,R=null,A=null,L=0,M=!1,H=null,X=!1,j=null,q=fn,V=!1,W=null,G=fn,ue=!1,ce=!1,Be=!1,se=null,Xe=null,je=null,(0,w.vF1)(e,"_senderConfig",{g:function(){return(0,b.CP)({},hn)}})}bn(),e.pause=function(){yn(),a=!0},e.resume=function(){a&&(a=!1,i=null,rn(),mn())},e.flush=function(n,t,r){if(void 0===n&&(n=!0),!a){yn();try{return e[le](n,null,r||1)}catch(n){(0,I.ZP)(e[fe](),1,22,"flush failed, telemetry will not be collected: "+(0,b.lL)(n),{exception:(0,w.mmD)(n)})}}},e.onunloadFlush=function(){if(!a)if(M||ae)try{return e[le](!0,sn,2)}catch(n){(0,I.ZP)(e[fe](),1,20,"failed to flush with beacon sender on page unload, telemetry will not be collected: "+(0,b.lL)(n),{exception:(0,w.mmD)(n)})}else e.flush(!1)},e.addHeader=function(e,n){l[e]=n},e[de]=function(n,o,a,u){e.isInitialized()&&(0,I.ZP)(e[fe](),1,28,"Sender is already initialized"),r[de](n,o,a,u);var m=e.identifier;c=new an(o.logger),t=0,i=null,e[ve]=null,s=0;var D=e[fe]();k=(0,C.Hm)((0,S.Z)("Sender"),o.evtNamespace&&o.evtNamespace()),y=(0,h.G)(k),e._addHook((0,T.a)(n,(function(n){var t=n.cfg;t.storagePrefix&&(0,g.vh)(t.storagePrefix);var r=(0,_.i8)(null,t,o).getExtCfg(m,hn),i=r[he];if(R&&i===R){var a=t[he];a&&a!==i&&(r[he]=a)}(0,w.$XS)(r[ge])&&(r[ge]=t[ge]),(0,w.vF1)(e,"_senderConfig",{g:function(){return r}}),A!==r[he]&&(R=A=r[he]),o.activeStatus()===P.f.PENDING?e.pause():o.activeStatus()===P.f.ACTIVE&&e.resume(),H&&H!==r[pe]&&(0,w.Iuo)(H,(function(e){delete l[e.header]})),L=r[me],M=(!1===r[ye]||!1===r[Ie])&&(0,E.Uf)(),N=!1===r[ye]&&(0,E.Uf)(),U=!1===r[Ie]&&(0,E.Uf)(),ae=r[be],ue=!!r[we],qe=r.retryCodes;var u=r[oe],c=!!r[Ce]&&(!!u||(0,g.AN)()),s=r.namePrefix,h=c!==W||c&&G!==s||c&&Z!==u;if(e[Se]){if(h)try{e._buffer=e._buffer[te](D,r,c)}catch(n){(0,I.ZP)(e[fe](),1,12,"failed to transfer telemetry to different buffer storage, telemetry will be lost: "+(0,b.lL)(n),{exception:(0,w.mmD)(n)})}rn()}else e[Se]=c?new on(D,r):new tn(D,r);G=s,W=c,Z=u,ce=!r[Te]&&(0,E.R7)(!0),Be=!!r[_e],e._sample=new ln(r.samplingPercentage,D),j=r[ge],(0,w.$XS)(j)||function(e,n){var t=n.disableInstrumentationKeyValidation;return!((0,w.hXl)(t)||!t)||new RegExp("^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$").test(e)}(j,t)||(0,I.ZP)(D,1,100,"Invalid Instrumentation key "+j),H=r[pe],(0,w.KgX)(R)&&!(0,p.Qu)(R)&&H&&H[B]>0?(0,w.Iuo)(H,(function(e){f.addHeader(e.header,e.value)})):H=null,ee=r[Pe];var y=function(){var n;try{var t={xdrOnComplete:function(e,n,t){var r=We(t);if(r)return v(e,r)},fetchOnComplete:function(e,n,t,r){var i=We(r);if(i)return un(e.status,i,e.url,i[B],e.statusText,t||"")},xhrOnComplete:function(e,n,t){var r=We(t);if(r)return Ke(e,r,r[B])},beaconOnRetry:function(n,t,r){return function(n,t,r){var i=n&&n[Fe];if(Be)Xe&&Xe(i,!0),(0,I.ZP)(e[fe](),2,40,". Failed to send telemetry with Beacon API, retried with normal sender.");else{for(var o=[],a=0;a<i[B];a++){var u=i[a],c=[u];r(en(c),t)?e._onSuccess(c,c[B]):o[Q](u)}o[B]>0&&(Xe&&Xe(o,!0),(0,I.ZP)(e[fe](),2,40,". Failed to send telemetry with Beacon API, retried with normal sender."))}}(n,t,r)}};return(n={})[Pe]=ee,n.isOneDs=!1,n.disableCredentials=!1,n[we]=ue,n.disableBeacon=!U,n.disableBeaconSync=!N,n.senderOnCompleteCallBack=t,n}catch(e){}return null}();je?je.SetConfig(y):(je=new x.v)[de](y,D);var C=r.httpXHROverride,S=null,T=null,O=(0,b.jL)([3,1,2],r.transports);S=je&&je[Ee](O,!1);var k=je&&je.getFallbackInst();se=function(e,n){return Ye(k,e,n)},Xe=function(e,n){return Ye(k,e,n,!1)},S=ae?C:S||C||k,e[ve]=function(e,n){return Ye(S,e,n)},ce&&(d=gn);var F=(0,b.jL)([3,1],r[xe]);ce||(F=F.filter((function(e){return 2!==e}))),T=je&&je[Ee](F,!0),T=ae?C:T||C,(ae||r[xe]||!d)&&T&&(d=function(e,n){return Ye(T,e,n)}),d||(d=se),X=r.disableTelemetry,q=r[De]||fn,V=r.isRetryDisabled,K=r[Oe]})))},e.processTelemetry=function(n,t){var r,i=(t=e._getTelCtx(t))[fe]();try{if(!Ze(n,i))return;var o=Je(n,i);if(!o)return;var a=c[ke](o),u=e[Se];rn(a);var s=((r={})[Y]=a,r.cnt=0,r);u[J](s),mn()}catch(e){(0,I.ZP)(i,2,12,"Failed adding telemetry to the sender's buffer, some telemetry will be lost: "+(0,b.lL)(e),{exception:(0,w.mmD)(e)})}e.processNext(n,t)},e.isCompletelyIdle=function(){return!a&&0===Ve&&0===e._buffer[$]()},e.getOfflineListener=function(){return y},e._xhrReadyStateChange=function(e,n,t){if(!vn(n))return Ke(e,n,t)},e[le]=function(n,t,r){var i;if(void 0===n&&(n=!0),!a)try{var o=e[Se];if(X)o[ne]();else{if(o[$]()>0){var u=o.getItems();!function(n,t){var r,i=(r="getNotifyMgr",e.core[r]?e.core[r]():e.core._notificationManager);if(i&&i[He])try{i[He](n,t)}catch(n){(0,I.ZP)(e[fe](),1,74,"send request notification failed: "+(0,b.lL)(n),{exception:(0,w.mmD)(n)})}}(r||0,n),i=t?t.call(e,u,n):e[ve](u,n)}new Date}yn()}catch(n){var c=(0,E.L0)();(!c||c>9)&&(0,I.ZP)(e[fe](),1,40,"Telemetry transmission failed, some telemetry will be lost: "+(0,b.lL)(n),{exception:(0,w.mmD)(n)})}return i},e.getOfflineSupport=function(){var e;return(e={getUrl:function(){return R},createPayload:Qe})[ke]=$e,e.batch=Ge,e.shouldProcess=function(e){return!!Ze(e)},e},e._doTeardown=function(n,t){e.onunloadFlush(),(0,D.K)(y,!1),bn()},e[Re]=function(n,t,r){if(!vn(n))return function(n,t,r){(0,I.ZP)(e[fe](),2,26,"Failed to send telemetry.",{message:t}),e._buffer&&e._buffer[ie](n)}(n,t)},e[Ae]=function(n,t){if(!vn(n))return function(n,t){for(var r=[],i=[],o=0,a=t.errors.reverse();o<a.length;o++){var u=a[o],c=n.splice(u.index,1)[0];In(u.statusCode)?i[Q](c):r[Q](c)}n[B]>0&&e[Le](n,t[Ne]),r[B]>0&&e[Re](r,(0,b.r4)(null,["partial success",t[Ne],"of",t.itemsReceived].join(" "))),i[B]>0&&(pn(i),(0,I.ZP)(e[fe](),2,40,"Partial success. Delivered: "+n[B]+", Failed: "+r[B]+". Will retry to send "+i[B]+" our of "+t[Me]+" items"))}(n,t)},e[Le]=function(n,t){if(!vn(n))return function(n,t){e._buffer&&e._buffer[ie](n)}(n)},e._xdrOnLoad=function(e,n){if(!vn(n))return v(e,n)}})),f}return(0,i.qU)(n,e),n.constructEnvelope=function(e,n,t,r){var o;return o=n===e.iKey||(0,w.hXl)(n)?e:(0,i.Im)((0,i.Im)({},e),{iKey:n}),(gn[o.baseType]||Qe)(t,o,r)},n}(k.s),mn=t(4484),yn=t(2774),In=t(8205),bn="instrumentationKey",wn="connectionString",Cn="instrumentationkey",Sn="endpointUrl",Tn="ingestionendpoint",_n="userOverrideEndpointUrl",Pn=t(9762),En=void 0,xn=((vn={diagnosticLogInterval:(0,y.Lx)((function(e){return e&&e>0}),1e4)})[wn]=En,vn[Sn]=En,vn[bn]=En,vn.extensionConfig={},vn),Dn=function(){function e(n){var t,i=new yn._;function a(e){e&&(e.baseData=e.baseData||{},e.baseType=e.baseType||"EventData"),i.track(e)}((0,w.hXl)(n)||(0,w.hXl)(n[bn])&&(0,w.hXl)(n[wn]))&&(0,w.$8)("Invalid input configuration"),(0,r.A)(e,this,(function(e){function r(){var e=(0,T.e)(n||{},xn);t=e.cfg,i.addUnloadHook((0,T.a)(e,(function(){var e=t[wn];if((0,w.$XS)(e)){var n=(0,In.Rf)((function(n,r){(0,In.Dv)(e,(function(e){var r=e.value,i=t[bn];!e.rejected&&r&&(t[wn]=r,i=(0,mn.H)(r)[Cn]||i),n(i)}))})),r=(0,In.Rf)((function(n,r){(0,In.Dv)(e,(function(e){var r=e.value,i=t[Sn];if(!e.rejected&&r){var a=(0,mn.H)(r)[Tn];i=a?a+o.wc:i}n(i)}))}));t[bn]=n,t[Sn]=t[_n]||r}if((0,w.KgX)(e)){var i=(0,mn.H)(e),a=i[Tn];t[Sn]=t[_n]?t[_n]:a+o.wc,t[bn]=i[Cn]||t[bn]}t[Sn]=t[_n]?t[_n]:t[Sn]}))),i.initialize(t,[new pn])}(0,w.vF1)(e,"config",{g:function(){return t}}),r(),e.initialize=r,e.track=a,(0,b.o$)(e,i,["flush","pollInternalLogs","stopPollingInternalLogs","unload","getPlugin","addPlugin","evtNamespace","addUnloadCb","onCfgChange","getTraceCtx","updateCfg","addTelemetryInitializer"])}))}return e.__ieDyn=1,e}()},8279:(e,n,t)=>{t.d(n,{A:()=>N});var r,i=t(269),o="constructor",a="prototype",u="function",c="_dynInstFuncs",s="_isDynProxy",l="_dynClass",f="_dynCls$",d="_dynInstChk",v=d,h="_dfOpts",g="_unknown_",p="__proto__",m="_dyn"+p,y="__dynProto$Gbl",I="_dynInstProto",b="useBaseInst",w="setInstFuncs",C=Object,S=C.getPrototypeOf,T=C.getOwnPropertyNames,_=(0,i.mS$)(),P=_[y]||(_[y]={o:(r={},r[w]=!0,r[b]=!0,r),n:1e3});function E(e){return e&&(e===C[a]||e===Array[a])}function x(e){return E(e)||e===Function[a]}function D(e){var n;if(e){if(S)return S(e);var t=e[p]||e[a]||(e[o]?e[o][a]:null);n=e[m]||t,(0,i.v0u)(e,m)||(delete e[I],n=e[m]=e[I]||e[m],e[I]=t)}return n}function O(e,n){var t=[];if(T)t=T(e);else for(var r in e)"string"==typeof r&&(0,i.v0u)(e,r)&&t.push(r);if(t&&t.length>0)for(var o=0;o<t.length;o++)n(t[o])}function k(e,n,t){return n!==o&&typeof e[n]===u&&(t||(0,i.v0u)(e,n))&&n!==p&&n!==a}function R(e){(0,i.zkd)("DynamicProto: "+e)}function A(e,n){for(var t=e.length-1;t>=0;t--)if(e[t]===n)return!0;return!1}function L(e,n,t,r,o){if(!E(e)){var a=t[c]=t[c]||(0,i.sSX)(null);if(!E(a)){var f=a[n]=a[n]||(0,i.sSX)(null);!1!==a[v]&&(a[v]=!!o),E(f)||O(t,(function(n){k(t,n,!1)&&t[n]!==r[n]&&(f[n]=t[n],delete t[n],(!(0,i.v0u)(e,n)||e[n]&&!e[n][s])&&(e[n]=function(e,n){var t=function(){var r=function(e,n,t,r){var o=null;if(e&&(0,i.v0u)(t,l)){var a=e[c]||(0,i.sSX)(null);if((o=(a[t[l]]||(0,i.sSX)(null))[n])||R("Missing ["+n+"] "+u),!o[d]&&!1!==a[v]){for(var s=!(0,i.v0u)(e,n),f=D(e),h=[];s&&f&&!x(f)&&!A(h,f);){var g=f[n];if(g){s=g===r;break}h.push(f),f=D(f)}try{s&&(e[n]=o),o[d]=1}catch(e){a[v]=!1}}}return o}(this,n,e,t)||function(e,n,t){var r=n[e];return r===t&&(r=D(n)[e]),typeof r!==u&&R("["+e+"] is not a "+u),r}(n,e,t);return r.apply(this,arguments)};return t[s]=1,t}(e,n)))}))}}}function M(e,n){return(0,i.v0u)(e,a)?e.name||n||g:((e||{})[o]||{}).name||n||g}function N(e,n,t,r){(0,i.v0u)(e,a)||R("theClass is an invalid class definition.");var o=e[a];(function(e,n){if(S){for(var t=[],r=D(n);r&&!x(r)&&!A(t,r);){if(r===e)return!0;t.push(r),r=D(r)}return!1}return!0})(o,n)||R("["+M(e)+"] not in hierarchy of ["+M(n)+"]");var u=null;(0,i.v0u)(o,l)?u=o[l]:(u=f+M(e,"_")+"$"+P.n,P.n++,o[l]=u);var d=N[h],g=!!d[b];g&&r&&void 0!==r[b]&&(g=!!r[b]);var p=function(e){var n=(0,i.sSX)(null);return O(e,(function(t){!n[t]&&k(e,t,!1)&&(n[t]=e[t])})),n}(n),m=function(e,n,t,r){function o(e,n,t){var i=n[t];if(i[s]&&r){var o=e[c]||{};!1!==o[v]&&(i=(o[n[l]]||{})[t]||i)}return function(){return i.apply(e,arguments)}}var a=(0,i.sSX)(null);O(t,(function(e){a[e]=o(n,t,e)}));for(var u=D(e),f=[];u&&!x(u)&&!A(f,u);)O(u,(function(e){!a[e]&&k(u,e,!S)&&(a[e]=o(n,u,e))})),f.push(u),u=D(u);return a}(o,n,p,g);t(n,m);var y=!!S&&!!d[w];y&&r&&(y=!!r[w]),L(o,u,n,p,!1!==y)}N[h]=P.o},8205:(e,n,t)=>{t.d(n,{Dv:()=>s,Qo:()=>A,Rf:()=>k,Xf:()=>L,lh:()=>R});var r,i,o,a=t(269),u="Promise",c="rejected";function s(e,n){return l(e,(function(e){return n?n({status:"fulfilled",rejected:!1,value:e}):e}),(function(e){return n?n({status:c,rejected:!0,reason:e}):e}))}function l(e,n,t,r){var i=e;try{if((0,a.$XS)(e))(n||t)&&(i=e.then(n,t));else try{n&&(i=n(e))}catch(e){if(!t)throw e;i=t(e)}}finally{r&&function(e,n){var t=e;n&&((0,a.$XS)(e)?t=e.finally?e.finally(n):e.then((function(e){return n(),e}),(function(e){throw n(),e})):n())}(i,r)}return i}var f,d=!1,v=["pending","resolving","resolved",c],h="dispatchEvent";function g(e){var n;return e&&e.createEvent&&(n=e.createEvent("Event")),!!n&&n.initEvent}var p,m,y,I,b="unhandledRejection",w=b.toLowerCase(),C=[],S=0,T=10;function _(e){return(0,a.Tnt)(e)?e.toString():(0,a.mmD)(e)}function P(e,n,t){var c,s,l=(0,a.KVm)(arguments,3),m=0,y=!1,I=[],P=S++,E=C.length>0?C[C.length-1]:void 0,x=!1,D=null;function O(n,t){try{return C.push(P),x=!0,D&&D.cancel(),D=null,e((function(e,r){I.push((function(){try{var i=2===m?n:t,o=(0,a.b07)(i)?c:(0,a.Tnt)(i)?i(c):i;(0,a.$XS)(o)?o.then(e,r):i?e(o):3===m?r(o):e(o)}catch(e){r(e)}})),y&&R()}),l)}finally{C.pop()}}function k(){return v[m]}function R(){if(I.length>0){var e=I.slice();I=[],x=!0,D&&D.cancel(),D=null,n(e)}}function A(e,n){return function(t){if(m===n){if(2===e&&(0,a.$XS)(t))return m=1,void t.then(A(2,1),A(3,1));m=e,y=!0,c=t,R(),x||3!==e||D||(D=(0,a.dRz)(L,T))}}}function L(){if(!x)if(x=!0,(0,a.Lln)())process.emit(b,c,s);else{var e=(0,a.zkX)()||(0,a.mS$)();!p&&(p=(0,a.GuU)((0,a.gBW)(a.zS2,[u+"RejectionEvent"]).v)),function(e,n,t,r){var i=(0,a.YEm)();!f&&(f=(0,a.GuU)(!!(0,a.gBW)(g,[i]).v));var o=f.v?i.createEvent("Event"):r?new Event(n):{};if(t&&t(o),f.v&&o.initEvent(n,!1,!0),o&&e[h])e[h](o);else{var u=e["on"+n];if(u)u(o);else{var c=(0,a.zS2)("console");c&&(c.error||c.log)(n,(0,a.mmD)(o))}}}(e,w,(function(e){return(0,a.vF1)(e,"promise",{g:function(){return s}}),e.reason=c,e}),!!p.v)}}return s={then:O,catch:function(e){return O(void 0,e)},finally:function(e){var n=e,t=e;return(0,a.Tnt)(e)&&(n=function(n){return e&&e(),n},t=function(n){throw e&&e(),n}),O(n,t)}},(0,a.UxO)(s,"state",{get:k}),d&&function(e,n,t,u){i=i||{toString:function(){return"[[PromiseResult]]"}},o=o||{toString:function(){return"[[PromiseIsHandled]]"}};var s={};s[r=r||{toString:function(){return"[[PromiseState]]"}}]={get:n},s[i]={get:function(){return(0,a.SZ2)(c)}},s[o]={get:function(){return x}},(0,a.isD)(e,s)}(s,k),(0,a.Lok)()&&(s[(0,a.Y0g)(11)]="IPromise"),s.toString=function(){return"IPromise"+(d?"["+P+((0,a.b07)(E)?"":":"+E)+"]":"")+" "+k()+(y?" - "+_(c):"")},function(){(0,a.Tnt)(t)||(0,a.zkd)(u+": executor is not a function - "+_(t));var e=A(3,0);try{t.call(s,A(2,0),e)}catch(n){e(n)}}(),s}function E(e){return function(n){var t=(0,a.KVm)(arguments,1);return e((function(e,t){try{var r=[],i=1;(0,a.DA8)(n,(function(n,o){n&&(i++,l(n,(function(n){r[o]=n,0==--i&&e(r)}),t))})),0==--i&&e(r)}catch(e){t(e)}}),t)}}function x(e){(0,a.Iuo)(e,(function(e){try{e()}catch(e){}}))}function D(e,n){return P(D,function(e){var n=(0,a.EtT)(e)?e:0;return function(e){(0,a.dRz)((function(){x(e)}),n)}}(n),e,n)}function O(e,n){!m&&(m=(0,a.GuU)((0,a.gBW)(a.zS2,[u]).v||null));var t=m.v;if(!t)return D(e);(0,a.Tnt)(e)||(0,a.zkd)(u+": executor is not a function - "+(0,a.mmD)(e));var r=0,i=new t((function(n,t){e((function(e){r=2,n(e)}),(function(e){r=3,t(e)}))}));return(0,a.UxO)(i,"state",{get:function(){return v[r]}}),i}function k(e){return P(k,x,e)}function R(e,n){return!y&&(t=k,y=(0,a.GuU)((function(e){var n=(0,a.KVm)(arguments,1);return t((function(n,t){var r=[],i=1;function o(e,t){i++,s(e,(function(e){e.rejected?r[t]={status:c,reason:e.reason}:r[t]={status:"fulfilled",value:e.value},0==--i&&n(r)}))}try{(0,a.cyL)(e)?(0,a.Iuo)(e,o):(0,a.xZI)(e)?(0,a.DA8)(e,o):(0,a.zkd)("Input is not an iterable"),0==--i&&n(r)}catch(e){t(e)}}),n)}))),y.v(e,n);var t}function A(e,n){return!I&&(I=(0,a.GuU)(O)),I.v.call(this,e,n)}var L=E(A);(0,a.Y0g)(11)},269:(e,n,t)=>{function r(e,n){return e||n}function i(e,n){return e[n]}t.d(n,{$8:()=>le,$PY:()=>X,$XS:()=>V,AHH:()=>dt,Cv9:()=>et,DA8:()=>bn,EHq:()=>Bn,Edw:()=>A,EtT:()=>B,FJj:()=>Qn,GuU:()=>Ke,Gvm:()=>z,HzD:()=>tt,Iuo:()=>Sn,JKf:()=>sn,KTd:()=>On,KVm:()=>Pn,KgX:()=>F,KhI:()=>$,Lln:()=>ln,Lmq:()=>j,Lok:()=>vn,N6t:()=>ye,Nq2:()=>Dn,O9V:()=>N,P0f:()=>Le,QdQ:()=>jn,R3R:()=>Gn,SZ2:()=>R,Tnt:()=>U,UUD:()=>Yn,UxO:()=>ne,Vdv:()=>en,W$7:()=>_n,WSA:()=>we,Wtk:()=>Qe,Y0g:()=>gn,YEm:()=>Ye,Yny:()=>Cn,ZHX:()=>me,ZWZ:()=>Fe,aqQ:()=>Mn,b07:()=>L,bJ7:()=>q,cGk:()=>pe,cyL:()=>H,dRz:()=>lt,eCG:()=>mn,f0d:()=>Nn,fn0:()=>Se,gBW:()=>D,hKY:()=>Me,hXl:()=>M,isD:()=>te,jjc:()=>pn,jsL:()=>Ie,kgX:()=>Z,mS$:()=>$e,mmD:()=>se,nRs:()=>Ve,oJg:()=>ue,rDm:()=>Tn,raO:()=>re,sSX:()=>kn,tGl:()=>rt,twz:()=>on,v0u:()=>J,vE3:()=>ge,vF1:()=>ie,vKV:()=>ft,w3n:()=>rn,w9M:()=>tn,woc:()=>K,xZI:()=>In,zS2:()=>Ge,zav:()=>Q,zkX:()=>nn,zkd:()=>fe,zwS:()=>Jn,zzB:()=>W});var o,a=void 0,u=null,c="",s="function",l="object",f="prototype",d="__proto__",v="undefined",h="constructor",g="Symbol",p="_polyfill",m="length",y="name",I="call",b="toString",w=r(Object),C=i(w,f),S=r(String),T=i(S,f),_=r(Math),P=r(Array),E=i(P,f),x=i(E,"slice");function D(e,n){try{return{v:e.apply(this,n)}}catch(e){return{e}}}function O(e){return function(n){return typeof n===e}}function k(e){var n="[object "+e+"]";return function(e){return!(!e||R(e)!==n)}}function R(e){return C[b].call(e)}function A(e,n){return typeof e===n}function L(e){return typeof e===v||e===v}function M(e){return e===u||L(e)}function N(e){return!!e||e!==a}var F=O("string"),U=O(s);function z(e){return!(!e&&M(e)||!e||typeof e!==l)}var H=i(P,"isArray"),X=k("Date"),B=O("number"),j=O("boolean"),q=k("Error");function V(e){return!!(e&&e.then&&U(e.then))}function K(e){return!e||!W(e)}function W(e){return!(!e||(n=function(){return!(e&&0+e)},t=!e,r=D(n),r.e?t:r.v));var n,t,r}var Z=i(w,"getOwnPropertyDescriptor");function J(e,n){return!!e&&C.hasOwnProperty[I](e,n)}var $=r(i(w,"hasOwn"),G);function G(e,n){return J(e,n)||!!Z(e,n)}function Q(e,n,t){if(e&&z(e))for(var r in e)if($(e,r)&&-1===n[I](t||e,r,e[r]))break}var Y={e:"enumerable",c:"configurable",v:"value",w:"writable",g:"get",s:"set"};function ee(e){var n={};if(n[Y.c]=!0,n[Y.e]=!0,e.l){n.get=function(){return e.l.v};var t=Z(e.l,"v");t&&t.set&&(n.set=function(n){e.l.v=n})}return Q(e,(function(e,t){n[Y[e]]=N(t)?t:n[Y[e]]})),n}var ne=i(w,"defineProperty"),te=i(w,"defineProperties");function re(e,n,t,r,i,o){var a={e:o,c:i};return t&&(a.g=t),r&&(a.s=r),ne(e,n,ee(a))}function ie(e,n,t){return ne(e,n,ee(t))}function oe(e,n,t,r,i){var o={};return Q(e,(function(e,r){ae(o,e,n?r:e,i),ae(o,r,t?r:e,i)})),r?r(o):o}function ae(e,n,t,r){ne(e,n,{value:t,enumerable:!0,writable:!!r})}var ue=r(S),ce="[object Error]";function se(e,n){var t=c,r=C[b][I](e);r===ce&&(e={stack:ue(e.stack),message:ue(e.message),name:ue(e.name)});try{t=((t=JSON.stringify(e,u,n?"number"==typeof n?n:4:a))?t.replace(/"(\w+)"\s*:\s{0,1}/g,"$1: "):u)||ue(e)}catch(e){t=" - "+se(e,n)}return r+": "+t}function le(e){throw new Error(e)}function fe(e){throw new TypeError(e)}var de=i(w,"freeze");function ve(e){return e}function he(e){return e[d]||u}var ge=i(w,"assign"),pe=i(w,"keys");function me(e){return de&&Q(e,(function(e,n){(H(n)||z(n))&&me(n)})),ye(e)}var ye=r(de,ve),Ie=r(i(w,"seal"),ve),be=r(i(w,"getPrototypeOf"),he);function we(e){return oe(e,1,0,ye)}function Ce(e){return oe(e,0,0,ye)}function Se(e){return function(e){var n={};return Q(e,(function(e,t){ae(n,e,t[1]),ae(n,t[0],t[1])})),ye(n)}(e)}var Te,_e=Ce({asyncIterator:0,hasInstance:1,isConcatSpreadable:2,iterator:3,match:4,matchAll:5,replace:6,search:7,species:8,split:9,toPrimitive:10,toStringTag:11,unscopables:12}),Pe="__tsUtils$gblCfg";function Ee(){var e;return typeof globalThis!==v&&(e=globalThis),e||typeof self===v||(e=self),e||typeof window===v||(e=window),e||typeof global===v||(e=global),e}function xe(){if(!Te){var e=D(Ee).v||{};Te=e[Pe]=e[Pe]||{}}return Te}var De=Oe;function Oe(e,n,t){var r=n?n[e]:u;return function(n){var i=(n?n[e]:u)||r;if(i||t){var o=arguments;return(i||t).apply(n,i?x[I](o,1):o)}fe('"'+ue(e)+'" not defined for '+se(n))}}function ke(e){return function(n){return n[e]}}var Re=i(_,"max"),Ae=De("slice",T),Le=De("substring",T),Me=Oe("substr",T,Ne);function Ne(e,n,t){return M(e)&&fe("Invalid "+se(e)),t<0?c:((n=n||0)<0&&(n=Re(n+e[m],0)),L(t)?Ae(e,n):Ae(e,n,n+t))}function Fe(e,n){return Le(e,0,n)}var Ue,ze,He,Xe="_urid";function Be(e){var n={description:ue(e),toString:function(){return g+"("+e+")"}};return n[p]=!0,n}function je(e){var n=function(){if(!Ue){var e=xe();Ue=e.gblSym=e.gblSym||{k:{},s:{}}}return Ue}();if(!$(n.k,e)){var t=Be(e),r=pe(n.s).length;t[Xe]=function(){return r+"_"+t[b]()},n.k[e]=t,n.s[t[Xe]()]=ue(e)}return n.k[e]}function qe(){He=xe()}function Ve(e){var n={};return!He&&qe(),n.b=He.lzy,ne(n,"v",{configurable:!0,get:function(){var t=e();return He.lzy||ne(n,"v",{value:t}),n.b=He.lzy,t}}),n}function Ke(e){return ne({toJSON:function(){return e}},"v",{value:e})}var We,Ze="window";function Je(e,n){var t;return function(){return!He&&qe(),t&&!He.lzy||(t=Ke(D(e,n).v)),t.v}}function $e(e){return!He&&qe(),We&&!1!==e&&!He.lzy||(We=Ke(D(Ee).v||u)),We.v}function Ge(e,n){var t;if((t=We&&!1!==n?We.v:$e(n))&&t[e])return t[e];if(e===Ze)try{return window}catch(e){}return u}function Qe(){return!!Ye()}var Ye=Je(Ge,["document"]);function en(){return!!nn()}var nn=Je(Ge,[Ze]);function tn(){return!!rn()}var rn=Je(Ge,["navigator"]);function on(){return!!sn()}var an,un,cn,sn=Je(Ge,["history"]),ln=Je((function(){return!!D((function(){return process&&(process.versions||{}).node})).v}));function fn(){return an=Ke(D(Ge,[g]).v)}function dn(e){var n=(He.lzy?0:an)||fn();return n.v?n.v[e]:a}function vn(){return!!hn()}function hn(){return!He&&qe(),((He.lzy?0:an)||fn()).v}function gn(e,n){var t=_e[e];!He&&qe();var r=(He.lzy?0:an)||fn();return r.v?r.v[t||e]:n?a:function(e){var n;!ze&&(ze={});var t=_e[e];return t&&(n=ze[t]=ze[t]||Be(g+"."+t)),n}(e)}function pn(e,n){!He&&qe();var t=(He.lzy?0:an)||fn();return t.v?t.v(e):n?u:Be(e)}function mn(e){return!He&&qe(),((un=(He.lzy?0:un)||Ke(D(dn,["for"]).v)).v||je)(e)}function yn(e){return!!e&&U(e.next)}function In(e){return!function(e){return e===u||!N(e)}(e)&&U(e[gn(3)])}function bn(e,n,t){if(e&&(yn(e)||(!cn&&(cn=Ke(gn(3))),e=e[cn.v]?e[cn.v]():u),yn(e))){var r=a,i=a;try{for(var o=0;!(i=e.next()).done&&-1!==n[I](t||e,i.value,o,e);)o++}catch(n){r={e:n},e.throw&&(i=u,e.throw(r))}finally{try{i&&!i.done&&e.return&&e.return(i)}finally{if(r)throw r.e}}}}function wn(e,n,t){return e.apply(n,t)}function Cn(e,n){return!L(n)&&e&&(H(n)?wn(e.push,e,n):yn(n)||In(n)?bn(n,(function(n){e.push(n)})):e.push(n)),e}function Sn(e,n,t){if(e)for(var r=e[m]>>>0,i=0;i<r&&(!(i in e)||-1!==n[I](t||e,e[i],i,e));i++);}var Tn=De("indexOf",E),_n=De("map",E);function Pn(e,n,t){return((e?e.slice:u)||x).apply(e,x[I](arguments,1))}function En(e,n,t){return-1!==Tn(e,n,t)}var xn,Dn=Oe("includes",E,En),On=De("reduce",E),kn=r(i(w,"create"),Rn);function Rn(e){if(!e)return{};var n=typeof e;function t(){}return n!==l&&n!==s&&fe("Prototype must be an Object or function: "+se(e)),t[f]=e,new t}function An(e,n){return(w.setPrototypeOf||function(e,n){var t;!xn&&(xn=Ke(((t={})[d]=[],t instanceof Array))),xn.v?e[d]=n:Q(n,(function(n,t){return e[n]=t}))})(e,n)}function Ln(e,n){n&&(e[y]=n)}function Mn(e,n,t){var r=t||Error,i=r[f][y],o=Error.captureStackTrace;return function(e,n,t){function r(){this[h]=n,D(ie,[this,y,{v:e,c:!0,e:!1}])}return D(ie,[n,y,{v:e,c:!0,e:!1}]),(n=An(n,t))[f]=t===u?kn(t):(r[f]=t[f],new r),n}(e,(function(){var t=this,a=arguments;try{D(Ln,[r,e]);var u=wn(r,t,x[I](a))||t;if(u!==t){var c=be(t);c!==be(u)&&An(u,c)}return o&&o(u,t[h]),n&&n(u,a),u}finally{D(Ln,[r,i])}}),r)}function Nn(){return(Date.now||Fn)()}function Fn(){return(new Date).getTime()}function Un(e){return function(n){return M(n)&&fe("strTrim called ["+se(n)+"]"),n&&n.replace&&(n=n.replace(e,c)),n}}var zn,Hn,Xn,Bn=Oe("trim",T,Un(/^\s+|(?=\s)\s+$/g));function jn(e){if(!e||typeof e!==l)return!1;Xn||(Xn=!en()||nn());var n=!1;if(e!==Xn){Hn||(zn=Function[f][b],Hn=zn[I](w));try{var t=be(e);(n=!t)||(J(t,h)&&(t=t[h]),n=!(!t||typeof t!==s||zn[I](t)!==Hn))}catch(e){}}return n}function qn(e){return e.value&&Zn(e),!0}var Vn=[function(e){var n=e.value;if(H(n)){var t=e.result=[];return t.length=n.length,e.copyTo(t,n),!0}return!1},Zn,function(e){return e.type===s},function(e){var n=e.value;return!!X(n)&&(e.result=new Date(n.getTime()),!0)}];function Kn(e,n,t,r){var i=t.handler,a=t.path?r?t.path.concat(r):t.path:[],c={handler:t.handler,src:t.src,path:a},s=typeof n,f=!1,d=n===u;d||(n&&s===l?f=jn(n):d=function(e){return!o&&(o=["string","number","boolean",v,"symbol","bigint"]),!(e===l||-1===o.indexOf(e))}(s));var h={type:s,isPrim:d,isPlain:f,value:n,result:n,path:a,origin:t.src,copy:function(n,r){return Kn(e,n,r?c:t,r)},copyTo:function(n,t){return Wn(e,n,t,c)}};return h.isPrim?i&&i[I](t,h)?h.result:n:function(e,n,r,o){var a;return Sn(e,(function(e){if(e.k===n)return a=e,-1})),a||(a={k:n,v:n},e.push(a),function(e){ie(h,"result",{g:function(){return e.v},s:function(n){e.v=n}});for(var n=0,r=i;!(r||(n<Vn.length?Vn[n++]:qn))[I](t,h);)r=u}(a)),a.v}(e,n)}function Wn(e,n,t,r){if(!M(t))for(var i in t)n[i]=Kn(e,t[i],r,i);return n}function Zn(e){var n=e.value;if(n&&e.isPlain){var t=e.result={};return e.copyTo(t,n),!0}return!1}function Jn(e,n,t,r,i,o,a){return function(e,n){return Sn(n,(function(n){!function(e,n,t){Wn([],e,n,{handler:void 0,src:n,path:[]})}(e,n)})),e}(Kn([],u=e,{handler:undefined,src:u})||{},x[I](arguments));var u}var $n,Gn=ke(m);function Qn(){return!He&&qe(),$n&&!He.lzy||($n=Ke(D(Ge,["performance"]).v)),$n.v}function Yn(){var e=Qn();return e&&e.now?e.now():Nn()}vn();var et=Oe("endsWith",T,nt);function nt(e,n,t){F(e)||fe("'"+se(e)+"' is not a string");var r=F(n)?n:ue(n),i=!L(t)&&t<e[m]?t:e[m];return Le(e,i-r[m],i)===r}var tt=De("indexOf",T),rt=Oe("startsWith",T,it);function it(e,n,t){F(e)||fe("'"+se(e)+"' is not a string");var r=F(n)?n:ue(n),i=t>0?t:0;return Le(e,i,i+r[m])===r}var ot="ref",at="unref",ut="hasRef",ct="enabled";function st(e,n,t){var r=H(n),i=r?n.length:0,o=(i>0?n[0]:r?a:n)||setTimeout,c=(i>1?n[1]:a)||clearTimeout,s=t[0];t[0]=function(){l.dn(),wn(s,a,x[I](arguments))};var l=function(e,n,t){var r,i=!0,o=e?n(u):u;function a(){return i=!1,o&&o[at]&&o[at](),r}function c(){o&&t(o),o=u}function s(){return o=n(o),i||a(),r}return(r={cancel:c,refresh:s})[ut]=function(){return o&&o[ut]?o[ut]():i},r[ot]=function(){return i=!0,o&&o[ot]&&o[ot](),r},r[at]=a,{h:r=ne(r,ct,{get:function(){return!!o},set:function(e){!e&&o&&c(),e&&!o&&s()}}),dn:function(){o=u}}}(e,(function(e){if(e){if(e.refresh)return e.refresh(),e;wn(c,a,[e])}return wn(o,a,t)}),(function(e){wn(c,a,[e])}));return l.h}function lt(e,n){return st(!0,a,x[I](arguments))}function ft(e,n,t){return st(!0,e,x[I](arguments,1))}function dt(e,n){return st(!1,a,x[I](arguments))}},5396:function(e,n,t){var r=this&&this.__createBinding||(Object.create?function(e,n,t,r){void 0===r&&(r=t);var i=Object.getOwnPropertyDescriptor(n,t);i&&!("get"in i?!n.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return n[t]}}),Object.defineProperty(e,r,i)}:function(e,n,t,r){void 0===r&&(r=t),e[r]=n[t]}),i=this&&this.__setModuleDefault||(Object.create?function(e,n){Object.defineProperty(e,"default",{enumerable:!0,value:n})}:function(e,n){e.default=n}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var n={};if(null!=e)for(var t in e)"default"!==t&&Object.prototype.hasOwnProperty.call(e,t)&&r(n,e,t);return i(n,e),n};Object.defineProperty(n,"__esModule",{value:!0}),n.oneDataSystemClientFactory=void 0,n.oneDataSystemClientFactory=async(e,n,r)=>{let i=await(async(e,n,r)=>{const i=await Promise.resolve().then((()=>o(t(956)))),a=await Promise.resolve().then((()=>o(t(8916)))),u=new i.AppInsightsCore,c=new a.PostChannel,s={instrumentationKey:e,endpointUrl:"https://mobile.events.data.microsoft.com/OneCollector/1.0",loggingLevelTelemetry:0,loggingLevelConsole:0,disableCookiesUsage:!0,disableDbgExt:!0,disableInstrumentationKeyValidation:!0,channels:[[c]]};if(r){s.extensionConfig={};const e={alwaysUseXhrOverride:!0,httpXHROverride:r};s.extensionConfig[c.identifier]=e}const l=n.workspace.getConfiguration("telemetry").get("internalTesting");return u.initialize(s,[]),u.addTelemetryInitializer((e=>{e.ext=e.ext??{},e.ext.web=e.ext.web??{},e.ext.web.consentDetails='{"GPC_DataSharingOptIn":false}',l&&(e.ext.utc=e.ext.utc??{},e.ext.utc.flags=8462029)})),u})(e,n,r);return{logEvent:(e,n)=>{try{i?.track({name:e,baseData:{name:e,properties:n?.properties,measurements:n?.measurements}})}catch(e){throw new Error("Failed to log event to app insights!\n"+e.message)}},flush:async()=>{try{return new Promise(((e,n)=>{i?i.flush(!0,(e=>{e||n("Failed to flush app 1DS!")})):e()}))}catch(e){throw new Error("Failed to flush 1DS!\n"+e.message)}},dispose:async()=>new Promise((e=>{i?i.unload(!1,(()=>{e(),i=void 0}),1e3):e()}))}}},2468:function(e,n,t){var r=this&&this.__createBinding||(Object.create?function(e,n,t,r){void 0===r&&(r=t);var i=Object.getOwnPropertyDescriptor(n,t);i&&!("get"in i?!n.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return n[t]}}),Object.defineProperty(e,r,i)}:function(e,n,t,r){void 0===r&&(r=t),e[r]=n[t]}),i=this&&this.__setModuleDefault||(Object.create?function(e,n){Object.defineProperty(e,"default",{enumerable:!0,value:n})}:function(e,n){e.default=n}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var n={};if(null!=e)for(var t in e)"default"!==t&&Object.prototype.hasOwnProperty.call(e,t)&&r(n,e,t);return i(n,e),n};Object.defineProperty(n,"__esModule",{value:!0}),n.appInsightsClientFactory=void 0;const a=t(740),u=t(8393);n.appInsightsClientFactory=async(e,n,r,i,c)=>{let s;try{const n=await Promise.resolve().then((()=>o(t(5927)))),r={};if(i){const e={alwaysUseXhrOverride:!0,httpXHROverride:i};r[a.BreezeChannelIdentifier]=e}let u;e.startsWith("InstrumentationKey=")||(u=e);const c=u?{instrumentationKey:u}:{connectionString:e};s=new n.ApplicationInsights({...c,disableAjaxTracking:!0,disableExceptionTracking:!0,disableFetchTracking:!0,disableCorrelationHeaders:!0,disableCookiesUsage:!0,autoTrackPageVisitTime:!1,emitLineDelimitedJson:!1,disableInstrumentationKeyValidation:!0,extensionConfig:r})}catch(e){return Promise.reject(e)}return{logEvent:(e,t)=>{const i={...t?.properties,...t?.measurements};c?.length&&u.TelemetryUtil.applyReplacements(i,c),s?.track({name:e,data:i,baseType:"EventData",ext:{user:{id:n,authId:n},app:{sesId:r}},baseData:{name:e,properties:t?.properties,measurements:t?.measurements}})},flush:async()=>{s?.flush(!1)},dispose:async()=>new Promise((e=>{s?.unload(!0,(()=>{e(),s=void 0}),1e3)}))}}},6548:(e,n)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.BaseTelemetryReporter=void 0,n.BaseTelemetryReporter=class{constructor(e,n,t){this.telemetrySender=e,this.vscodeAPI=n,this.userOptIn=!1,this.errorOptIn=!1,this.disposables=[],this._onDidChangeTelemetryLevel=new this.vscodeAPI.EventEmitter,this.onDidChangeTelemetryLevel=this._onDidChangeTelemetryLevel.event,this.telemetryLogger=this.vscodeAPI.env.createTelemetryLogger(this.telemetrySender,t),this.updateUserOptIn(),this.telemetryLogger.onDidChangeEnableStates((()=>{this.updateUserOptIn()}))}updateUserOptIn(){this.errorOptIn=this.telemetryLogger.isErrorsEnabled,this.userOptIn=this.telemetryLogger.isUsageEnabled,(this.telemetryLogger.isErrorsEnabled||this.telemetryLogger.isUsageEnabled)&&this.telemetrySender.instantiateSender(),this._onDidChangeTelemetryLevel.fire(this.telemetryLevel)}get telemetryLevel(){return this.errorOptIn&&this.userOptIn?"all":this.errorOptIn?"error":"off"}internalSendTelemetryEvent(e,n,t,r){r?this.telemetrySender.sendEventData(e,{properties:n,measurements:t}):this.telemetryLogger.logUsage(e,{properties:n,measurements:t})}sendTelemetryEvent(e,n,t){this.internalSendTelemetryEvent(e,n,t,!1)}sendRawTelemetryEvent(e,n,t){const r={...n};for(const e of Object.keys(r??{})){const n=r[e];"string"==typeof e&&void 0!==n&&(r[e]=new this.vscodeAPI.TelemetryTrustedValue("string"==typeof n?n:n.value))}this.sendTelemetryEvent(e,r,t)}sendDangerousTelemetryEvent(e,n,t){this.telemetrySender.instantiateSender(),this.internalSendTelemetryEvent(e,n,t,!0)}internalSendTelemetryErrorEvent(e,n,t,r){r?this.telemetrySender.sendEventData(e,{properties:n,measurements:t}):this.telemetryLogger.logError(e,{properties:n,measurements:t})}sendTelemetryErrorEvent(e,n,t){this.internalSendTelemetryErrorEvent(e,n,t,!1)}sendDangerousTelemetryErrorEvent(e,n,t){this.telemetrySender.instantiateSender(),this.internalSendTelemetryErrorEvent(e,n,t,!0)}async dispose(){return await this.telemetrySender.dispose(),this.telemetryLogger.dispose(),Promise.all(this.disposables.map((e=>e.dispose())))}}},2396:(e,n)=>{var t;Object.defineProperty(n,"__esModule",{value:!0}),n.BaseTelemetrySender=void 0,function(e){e[e.NOT_INSTANTIATED=0]="NOT_INSTANTIATED",e[e.INSTANTIATING=1]="INSTANTIATING",e[e.INSTANTIATED=2]="INSTANTIATED"}(t||(t={})),n.BaseTelemetrySender=class{constructor(e,n){this._instantiationStatus=t.NOT_INSTANTIATED,this._eventQueue=[],this._exceptionQueue=[],this._clientFactory=n,this._key=e}sendEventData(e,n){this._telemetryClient?this._telemetryClient.logEvent(e,n):this._instantiationStatus!==t.INSTANTIATED&&this._eventQueue.push({eventName:e,data:n})}sendErrorData(e,n){if(!this._telemetryClient)return void(this._instantiationStatus!==t.INSTANTIATED&&this._exceptionQueue.push({exception:e,data:n}));const r={stack:e.stack,message:e.message,name:e.name};if(n){const e=n.properties||n;n.properties={...e,...r}}else n={properties:r};this._telemetryClient.logEvent("unhandlederror",n)}async flush(){return this._telemetryClient?.flush()}async dispose(){this._telemetryClient&&(await this._telemetryClient.dispose(),this._telemetryClient=void 0)}_flushQueues(){this._eventQueue.forEach((({eventName:e,data:n})=>this.sendEventData(e,n))),this._eventQueue=[],this._exceptionQueue.forEach((({exception:e,data:n})=>this.sendErrorData(e,n))),this._exceptionQueue=[]}instantiateSender(){this._instantiationStatus===t.NOT_INSTANTIATED&&(this._instantiationStatus=t.INSTANTIATING,this._clientFactory(this._key).then((e=>{this._telemetryClient=e,this._instantiationStatus=t.INSTANTIATED,this._flushQueues()})).catch((e=>{console.error(e),this._instantiationStatus=t.INSTANTIATED})))}}},8393:(e,n)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.TelemetryUtil=void 0;class t{static applyReplacements(e,n){for(const t of Object.keys(e))for(const r of n)r.lookup.test(t)&&(void 0!==r.replacementString?e[t]=r.replacementString:delete e[t])}static shouldUseOneDataSystemSDK(e){return 74===e.length&&"-"===e[32]&&"-"===e[41]&&"-"===e[46]&&"-"===e[51]&&"-"===e[56]&&"-"===e[69]}static getAdditionalCommonProperties(e){return{"common.os":e.platform,"common.nodeArch":e.architecture,"common.platformversion":(e.release||"").replace(/^(\d+)(\.\d+)?(\.\d+)?(.*)/,"$1$2$3"),"common.telemetryclientversion":"0.9.8"}}static getInstance(){return t._instance||(t._instance=new t),t._instance}}n.TelemetryUtil=t},1170:function(e,n,t){var r=this&&this.__createBinding||(Object.create?function(e,n,t,r){void 0===r&&(r=t);var i=Object.getOwnPropertyDescriptor(n,t);i&&!("get"in i?!n.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return n[t]}}),Object.defineProperty(e,r,i)}:function(e,n,t,r){void 0===r&&(r=t),e[r]=n[t]}),i=this&&this.__setModuleDefault||(Object.create?function(e,n){Object.defineProperty(e,"default",{enumerable:!0,value:n})}:function(e,n){e.default=n}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var n={};if(null!=e)for(var t in e)"default"!==t&&Object.prototype.hasOwnProperty.call(e,t)&&r(n,e,t);return i(n,e),n};Object.defineProperty(n,"__esModule",{value:!0});const a=o(t(5692)),u=o(t(857)),c=o(t(1398)),s=t(5396),l=t(2468),f=t(6548),d=t(2396),v=t(8393);function h(){return{sendPOST:(e,n)=>{const t={method:"POST",headers:{...e.headers,"Content-Type":"application/json","Content-Length":Buffer.byteLength(e.data)}};try{const r=a.request(e.urlString,t,(e=>{e.on("data",(function(t){n(e.statusCode??200,e.headers,t.toString())})),e.on("error",(function(){n(0,{})}))}));r.write(e.data,(e=>{e&&n(0,{})})),r.end()}catch{n(0,{})}}}}class g extends f.BaseTelemetryReporter{constructor(e,n){let t=e=>(0,l.appInsightsClientFactory)(e,c.env.machineId,c.env.sessionId,h(),n);v.TelemetryUtil.shouldUseOneDataSystemSDK(e)&&(t=e=>(0,s.oneDataSystemClientFactory)(e,c,h()));const r={release:u.release(),platform:u.platform(),architecture:u.arch()},i=new d.BaseTelemetrySender(e,t);if(e&&0===e.indexOf("AIF-"))throw new Error("AIF keys are no longer supported. Please switch to 1DS keys for 1st party extensions");super(i,c,{additionalCommonProperties:v.TelemetryUtil.getAdditionalCommonProperties(r)})}}n.default=g},977:function(e,n,t){var r,i=this&&this.__createBinding||(Object.create?function(e,n,t,r){void 0===r&&(r=t);var i=Object.getOwnPropertyDescriptor(n,t);i&&!("get"in i?!n.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return n[t]}}),Object.defineProperty(e,r,i)}:function(e,n,t,r){void 0===r&&(r=t),e[r]=n[t]}),o=this&&this.__setModuleDefault||(Object.create?function(e,n){Object.defineProperty(e,"default",{enumerable:!0,value:n})}:function(e,n){e.default=n}),a=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var n=[];for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(n[n.length]=t);return n},r(e)},function(e){if(e&&e.__esModule)return e;var n={};if(null!=e)for(var t=r(e),a=0;a<t.length;a++)"default"!==t[a]&&i(n,e,t[a]);return o(n,e),n});Object.defineProperty(n,"__esModule",{value:!0});const u=a(t(1398));n.default=class{constructor(e){this.tracker=e.createTracker("codelens")}begin(e){this.config=e,this.config.enableCodeLens&&this.registerCodeLensProvider()}configurationUpdated(e){!1===e.enableCodeLens&&this.codeLensRegistrationHandle?(this.codeLensRegistrationHandle.dispose(),this.codeLensRegistrationHandle=null):!0!==e.enableCodeLens||this.codeLensRegistrationHandle||this.registerCodeLensProvider(),this.config=e}dispose(){this.codeLensRegistrationHandle&&(this.codeLensRegistrationHandle.dispose(),this.codeLensRegistrationHandle=null)}async provideCodeLenses(e,n){if(!this.config||!this.config.enableCodeLens)return null;const t=await this.tracker.getConflicts(e),r=t?.length??0;if(u.commands.executeCommand("setContext","mergeConflictsCount",r),!r)return null;const i=[];return t.forEach((n=>{const t={command:"merge-conflict.accept.current",title:u.l10n.t("Accept Current Change"),arguments:["known-conflict",n]},r={command:"merge-conflict.accept.incoming",title:u.l10n.t("Accept Incoming Change"),arguments:["known-conflict",n]},o={command:"merge-conflict.accept.both",title:u.l10n.t("Accept Both Changes"),arguments:["known-conflict",n]},a={command:"merge-conflict.compare",title:u.l10n.t("Compare Changes"),arguments:[n]},c=e.lineAt(n.range.start.line).range;i.push(new u.CodeLens(c,t),new u.CodeLens(c,r),new u.CodeLens(c,o),new u.CodeLens(c,a))})),i}registerCodeLensProvider(){this.codeLensRegistrationHandle=u.languages.registerCodeLensProvider([{scheme:"file"},{scheme:"vscode-vfs"},{scheme:"untitled"},{scheme:"vscode-userdata"}],this)}}},7146:function(e,n,t){var r,i=this&&this.__createBinding||(Object.create?function(e,n,t,r){void 0===r&&(r=t);var i=Object.getOwnPropertyDescriptor(n,t);i&&!("get"in i?!n.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return n[t]}}),Object.defineProperty(e,r,i)}:function(e,n,t,r){void 0===r&&(r=t),e[r]=n[t]}),o=this&&this.__setModuleDefault||(Object.create?function(e,n){Object.defineProperty(e,"default",{enumerable:!0,value:n})}:function(e,n){e.default=n}),a=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var n=[];for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(n[n.length]=t);return n},r(e)},function(e){if(e&&e.__esModule)return e;var n={};if(null!=e)for(var t=r(e),a=0;a<t.length;a++)"default"!==t[a]&&i(n,e,t[a]);return o(n,e),n}),u=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(n,"__esModule",{value:!0});const c=a(t(1398)),s=u(t(881));var l;!function(e){e[e.Forwards=0]="Forwards",e[e.Backwards=1]="Backwards"}(l||(l={})),n.default=class{constructor(e){this.disposables=[],this.tracker=e.createTracker("commands")}begin(){this.disposables.push(this.registerTextEditorCommand("merge-conflict.accept.current",this.acceptCurrent),this.registerTextEditorCommand("merge-conflict.accept.incoming",this.acceptIncoming),this.registerTextEditorCommand("merge-conflict.accept.selection",this.acceptSelection),this.registerTextEditorCommand("merge-conflict.accept.both",this.acceptBoth),this.registerTextEditorCommand("merge-conflict.accept.all-current",this.acceptAllCurrent,this.acceptAllCurrentResources),this.registerTextEditorCommand("merge-conflict.accept.all-incoming",this.acceptAllIncoming,this.acceptAllIncomingResources),this.registerTextEditorCommand("merge-conflict.accept.all-both",this.acceptAllBoth),this.registerTextEditorCommand("merge-conflict.next",this.navigateNext),this.registerTextEditorCommand("merge-conflict.previous",this.navigatePrevious),this.registerTextEditorCommand("merge-conflict.compare",this.compare))}registerTextEditorCommand(e,n,t){return c.commands.registerCommand(e,((...e)=>{if(t&&e.length&&e.every((e=>e&&e.resourceUri)))return t.call(this,e.map((e=>e.resourceUri)));const r=c.window.activeTextEditor;return r&&n.call(this,r,...e)}))}acceptCurrent(e,...n){return this.accept(0,e,...n)}acceptIncoming(e,...n){return this.accept(1,e,...n)}acceptBoth(e,...n){return this.accept(2,e,...n)}acceptAllCurrent(e){return this.acceptAll(0,e)}acceptAllIncoming(e){return this.acceptAll(1,e)}acceptAllCurrentResources(e){return this.acceptAllResources(0,e)}acceptAllIncomingResources(e){return this.acceptAllResources(1,e)}acceptAllBoth(e){return this.acceptAll(2,e)}async compare(e,n){if(!n&&!(n=await this.findConflictContainingSelection(e)))return void c.window.showWarningMessage(c.l10n.t("Editor cursor is not within a merge conflict"));const t=await this.tracker.getConflicts(e.document);if(!t)return void c.window.showWarningMessage(c.l10n.t("Editor cursor is not within a merge conflict"));const r=e.document.uri.scheme;let i=n.current.content;const o=t.map((e=>[e.current.content,e.range])),a=t.map((e=>[e.incoming.content,e.range])),u=e.document.uri.with({scheme:s.default.scheme,query:JSON.stringify({scheme:r,range:i,ranges:o})});i=n.incoming.content;const l=u.with({query:JSON.stringify({scheme:r,ranges:a})});let f=0;for(const e of t){if(e.range.isEqual(n.range))break;f+=e.range.end.line-e.range.start.line-(e.incoming.content.end.line-e.incoming.content.start.line)}const d=new c.Range(n.range.start.line-f,n.range.start.character,n.range.start.line-f,n.range.start.character),v=e.document.uri.path,h=v.substring(v.lastIndexOf("/")+1),g=c.l10n.t("{0}: Current Changes ↔ Incoming Changes",h),p=c.workspace.getConfiguration("merge-conflict").get("diffViewPosition"),m={viewColumn:"Beside"===p?c.ViewColumn.Beside:c.ViewColumn.Active,selection:d};"Below"===p&&await c.commands.executeCommand("workbench.action.newGroupBelow"),await c.commands.executeCommand("vscode.diff",u,l,g,m)}navigateNext(e){return this.navigate(e,l.Forwards)}navigatePrevious(e){return this.navigate(e,l.Backwards)}async acceptSelection(e){const n=await this.findConflictContainingSelection(e);if(!n)return void c.window.showWarningMessage(c.l10n.t("Editor cursor is not within a merge conflict"));let t,r=n.splitter;if(n.commonAncestors.length>0&&(r=n.commonAncestors[0].header),e.selection.active.isBefore(r.start))t=0;else{if(!e.selection.active.isAfter(n.splitter.end))return e.selection.active.isBefore(n.splitter.start)?void c.window.showWarningMessage(c.l10n.t('Editor cursor is within the common ancestors block, please move it to either the "current" or "incoming" block')):void c.window.showWarningMessage(c.l10n.t('Editor cursor is within the merge conflict splitter, please move it to either the "current" or "incoming" block'));t=1}this.tracker.forget(e.document),n.commitEdit(t,e)}dispose(){this.disposables.forEach((e=>e.dispose())),this.disposables=[]}async navigate(e,n){const t=await this.findConflictForNavigation(e,n);if(t)t.canNavigate?t.conflict&&(e.selection=new c.Selection(t.conflict.range.start,t.conflict.range.start),e.revealRange(t.conflict.range,c.TextEditorRevealType.Default)):c.window.showWarningMessage(c.l10n.t("No other merge conflicts within this file"));else{if(c.workspace.getConfiguration("merge-conflict").get("autoNavigateNextConflict.enabled"))return;c.window.showWarningMessage(c.l10n.t("No merge conflicts found in this file"))}}async accept(e,n,...t){let r;r="known-conflict"===t[0]?t[1]:await this.findConflictContainingSelection(n),r?(this.tracker.forget(n.document),r.commitEdit(e,n),c.workspace.getConfiguration("merge-conflict").get("autoNavigateNextConflict.enabled")&&this.navigateNext(n)):c.window.showWarningMessage(c.l10n.t("Editor cursor is not within a merge conflict"))}async acceptAll(e,n){const t=await this.tracker.getConflicts(n.document);t&&0!==t.length?(this.tracker.forget(n.document),await n.edit((r=>t.forEach((t=>{t.applyEdit(e,n.document,r)}))))):c.window.showWarningMessage(c.l10n.t("No merge conflicts found in this file"))}async acceptAllResources(e,n){const t=await Promise.all(n.map((e=>c.workspace.openTextDocument(e)))),r=new c.WorkspaceEdit;for(const n of t){const t=await this.tracker.getConflicts(n);t&&0!==t.length&&(this.tracker.forget(n),t.forEach((t=>{t.applyEdit(e,n,{replace:(e,t)=>r.replace(n.uri,e,t)})})))}c.workspace.applyEdit(r)}async findConflictContainingSelection(e,n){if(n||(n=await this.tracker.getConflicts(e.document)),!n||0===n.length)return null;for(const t of n)if(t.range.contains(e.selection.active))return t;return null}async findConflictForNavigation(e,n,t){if(t||(t=await this.tracker.getConflicts(e.document)),!t||0===t.length)return null;const r=e.selection.active;if(1===t.length)return t[0].range.contains(r)?{canNavigate:!1}:{canNavigate:!0,conflict:t[0]};let i,o,a;if(n===l.Forwards)i=e=>r.isBefore(e.range.start),o=()=>t[0],a=t;else{if(n!==l.Backwards)throw new Error(`Unsupported direction ${n}`);i=e=>r.isAfter(e.range.start),o=()=>t[t.length-1],a=t.slice().reverse()}for(const e of a)if(i(e)&&!e.range.contains(r))return{canNavigate:!0,conflict:e};return{canNavigate:!0,conflict:o()}}}},881:function(e,n,t){var r,i=this&&this.__createBinding||(Object.create?function(e,n,t,r){void 0===r&&(r=t);var i=Object.getOwnPropertyDescriptor(n,t);i&&!("get"in i?!n.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return n[t]}}),Object.defineProperty(e,r,i)}:function(e,n,t,r){void 0===r&&(r=t),e[r]=n[t]}),o=this&&this.__setModuleDefault||(Object.create?function(e,n){Object.defineProperty(e,"default",{enumerable:!0,value:n})}:function(e,n){e.default=n}),a=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var n=[];for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(n[n.length]=t);return n},r(e)},function(e){if(e&&e.__esModule)return e;var n={};if(null!=e)for(var t=r(e),a=0;a<t.length;a++)"default"!==t[a]&&i(n,e,t[a]);return o(n,e),n});Object.defineProperty(n,"__esModule",{value:!0});const u=a(t(1398));class c{constructor(e){this.context=e}begin(){this.context.subscriptions.push(u.workspace.registerTextDocumentContentProvider(c.scheme,this))}dispose(){}async provideTextDocumentContent(e){try{const{scheme:n,ranges:t}=JSON.parse(e.query),r=await u.workspace.openTextDocument(e.with({scheme:n,query:""}));let i="",o=new u.Position(0,0);t.forEach((e=>{const[n,t]=e,[a,c]=n,[s,l]=t;i+=r.getText(new u.Range(o.line,o.character,s.line,s.character)),i+=r.getText(new u.Range(a.line,a.character,c.line,c.character)),o=new u.Position(l.line,l.character)}));const a=r.lineAt(r.lineCount-1).range.end;return i+=r.getText(new u.Range(o.line,o.character,a.line,a.character)),i}catch(e){return await u.window.showErrorMessage("Unable to show comparison"),null}}}c.scheme="merge-conflict.conflict-diff",n.default=c},9465:(e,n)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.Delayer=void 0,n.Delayer=class{constructor(e){this.defaultDelay=e,this.timeout=null,this.completionPromise=null,this.onSuccess=null,this.task=null}trigger(e,n=this.defaultDelay){return this.task=e,n>=0&&this.cancelTimeout(),this.completionPromise||(this.completionPromise=new Promise((e=>{this.onSuccess=e})).then((()=>{this.completionPromise=null,this.onSuccess=null;const e=this.task();return this.task=null,e}))),(n>=0||null===this.timeout)&&(this.timeout=setTimeout((()=>{this.timeout=null,this.onSuccess(void 0)}),n>=0?n:this.defaultDelay)),this.completionPromise}forceDelivery(){if(!this.completionPromise)return null;this.cancelTimeout();const e=this.completionPromise;return this.onSuccess(void 0),e}isTriggered(){return null!==this.timeout}cancel(){this.cancelTimeout(),this.completionPromise=null}cancelTimeout(){null!==this.timeout&&(clearTimeout(this.timeout),this.timeout=null)}}},2676:(e,n)=>{Object.defineProperty(n,"__esModule",{value:!0}),n.DocumentMergeConflict=void 0,n.DocumentMergeConflict=class{constructor(e,n){this.telemetryReporter=n,this.applied=!1,this.range=e.range,this.current=e.current,this.incoming=e.incoming,this.commonAncestors=e.commonAncestors,this.splitter=e.splitter}commitEdit(e,n,t){return this.telemetryReporter.sendTelemetryEvent("mergeMarkers.accept",{resolution:function(e){switch(e){case 0:return"current";case 1:return"incoming";case 2:return"both"}}(e)}),t?(this.applyEdit(e,n.document,t),Promise.resolve(!0)):n.edit((t=>this.applyEdit(e,n.document,t)))}applyEdit(e,n,t){if(!this.applied)if(this.applied=!0,0===e){const e=n.getText(this.current.content);this.replaceRangeWithContent(e,t)}else if(1===e){const e=n.getText(this.incoming.content);this.replaceRangeWithContent(e,t)}else if(2===e){const e=n.getText(this.current.content),r=n.getText(this.incoming.content);t.replace(this.range,e.concat(r))}}replaceRangeWithContent(e,n){this.isNewlineOnly(e)?n.replace(this.range,""):n.replace(this.range,e)}isNewlineOnly(e){return"\n"===e||"\r\n"===e}}},2346:(e,n,t)=>{Object.defineProperty(n,"__esModule",{value:!0});const r=t(7356),i=t(9465);class o{constructor(e,n){this.origins=new Set,this.origins.add(n),this.delayTask=new i.Delayer(e)}addOrigin(e){this.origins.add(e)}hasOrigin(e){return this.origins.has(e)}}class a{constructor(e,n){this.parent=e,this.origin=n}getConflicts(e){return this.parent.getConflicts(e,this.origin)}isPending(e){return this.parent.isPending(e,this.origin)}forget(e){this.parent.forget(e)}}n.default=class{constructor(e){this.telemetryReporter=e,this.cache=new Map,this.delayExpireTime=0,this.seenDocumentsWithConflicts=new Set}getConflicts(e,n){const t=this.getCacheKey(e);if(!t)return Promise.resolve(this.getConflictsOrEmpty(e,[n]));let r=this.cache.get(t);return r?r.addOrigin(n):(r=new o(this.delayExpireTime,n),this.cache.set(t,r)),r.delayTask.trigger((()=>{const n=this.getConflictsOrEmpty(e,Array.from(r.origins));return this.cache?.delete(t),n}))}isPending(e,n){if(!e)return!1;const t=this.getCacheKey(e);if(!t)return!1;const r=this.cache.get(t);return!!r&&r.hasOrigin(n)}createTracker(e){return new a(this,e)}forget(e){const n=this.getCacheKey(e);n&&this.cache.delete(n)}dispose(){this.cache.clear()}getConflictsOrEmpty(e,n){if(!r.MergeConflictParser.containsConflict(e))return[];const t=r.MergeConflictParser.scanDocument(e,this.telemetryReporter),i=e.uri.toString();return this.seenDocumentsWithConflicts.has(i)||(this.seenDocumentsWithConflicts.add(i),this.telemetryReporter.sendTelemetryEvent("mergeMarkers.documentWithConflictMarkersOpened",{},{conflictCount:t.length})),t}getCacheKey(e){return e.uri?e.uri.toString():null}}},7282:function(e,n,t){var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(n,"__esModule",{value:!0}),n.activate=function(e){const n=new i.default(e);n.begin(),e.subscriptions.push(n)},n.deactivate=function(){};const i=r(t(5781))},7356:function(e,n,t){var r,i=this&&this.__createBinding||(Object.create?function(e,n,t,r){void 0===r&&(r=t);var i=Object.getOwnPropertyDescriptor(n,t);i&&!("get"in i?!n.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return n[t]}}),Object.defineProperty(e,r,i)}:function(e,n,t,r){void 0===r&&(r=t),e[r]=n[t]}),o=this&&this.__setModuleDefault||(Object.create?function(e,n){Object.defineProperty(e,"default",{enumerable:!0,value:n})}:function(e,n){e.default=n}),a=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var n=[];for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(n[n.length]=t);return n},r(e)},function(e){if(e&&e.__esModule)return e;var n={};if(null!=e)for(var t=r(e),a=0;a<t.length;a++)"default"!==t[a]&&i(n,e,t[a]);return o(n,e),n});Object.defineProperty(n,"__esModule",{value:!0}),n.MergeConflictParser=void 0;const u=a(t(1398)),c=t(2676),s="<<<<<<<",l=">>>>>>>";class f{static scanDocument(e,n){let t=null;const r=[];for(let n=0;n<e.lineCount;n++){const i=e.lineAt(n);if(i&&!i.isEmptyOrWhitespace)if(i.text.startsWith(s)){if(null!==t){t=null;break}t={startHeader:i,commonAncestors:[]}}else if(t&&!t.splitter&&i.text.startsWith("|||||||"))t.commonAncestors.push(i);else if(t&&!t.splitter&&"======="===i.text)t.splitter=i;else if(t&&i.text.startsWith(l)){t.endFooter=i;const n=f.scanItemTolMergeConflictDescriptor(e,t);null!==n&&r.push(n),t=null}}return r.filter(Boolean).map((e=>new c.DocumentMergeConflict(e,n)))}static scanItemTolMergeConflictDescriptor(e,n){if(!n.startHeader||!n.splitter||!n.endFooter)return null;const t=n.commonAncestors[0]||n.splitter;return{current:{header:n.startHeader.range,decoratorContent:new u.Range(n.startHeader.rangeIncludingLineBreak.end,f.shiftBackOneCharacter(e,t.range.start,n.startHeader.rangeIncludingLineBreak.end)),content:new u.Range(n.startHeader.rangeIncludingLineBreak.end,t.range.start),name:n.startHeader.text.substring(8)},commonAncestors:n.commonAncestors.map(((t,r,i)=>{const o=i[r+1]||n.splitter;return{header:t.range,decoratorContent:new u.Range(t.rangeIncludingLineBreak.end,f.shiftBackOneCharacter(e,o.range.start,t.rangeIncludingLineBreak.end)),content:new u.Range(t.rangeIncludingLineBreak.end,o.range.start),name:t.text.substring(8)}})),splitter:n.splitter.range,incoming:{header:n.endFooter.range,decoratorContent:new u.Range(n.splitter.rangeIncludingLineBreak.end,f.shiftBackOneCharacter(e,n.endFooter.range.start,n.splitter.rangeIncludingLineBreak.end)),content:new u.Range(n.splitter.rangeIncludingLineBreak.end,n.endFooter.range.start),name:n.endFooter.text.substring(8)},range:new u.Range(n.startHeader.range.start,n.endFooter.rangeIncludingLineBreak.end)}}static containsConflict(e){if(!e)return!1;const n=e.getText();return n.includes(s)&&n.includes(l)}static shiftBackOneCharacter(e,n,t){if(n.isEqual(t))return n;let r=n.line,i=n.character-1;return i<0&&(r--,i=e.lineAt(r).range.end.character),new u.Position(r,i)}}n.MergeConflictParser=f},1148:function(e,n,t){var r,i=this&&this.__createBinding||(Object.create?function(e,n,t,r){void 0===r&&(r=t);var i=Object.getOwnPropertyDescriptor(n,t);i&&!("get"in i?!n.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return n[t]}}),Object.defineProperty(e,r,i)}:function(e,n,t,r){void 0===r&&(r=t),e[r]=n[t]}),o=this&&this.__setModuleDefault||(Object.create?function(e,n){Object.defineProperty(e,"default",{enumerable:!0,value:n})}:function(e,n){e.default=n}),a=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var n=[];for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(n[n.length]=t);return n},r(e)},function(e){if(e&&e.__esModule)return e;var n={};if(null!=e)for(var t=r(e),a=0;a<t.length;a++)"default"!==t[a]&&i(n,e,t[a]);return o(n,e),n});Object.defineProperty(n,"__esModule",{value:!0});const u=a(t(1398));n.default=class{constructor(e,n){this.context=e,this.decorations={},this.decorationUsesWholeLine=!0,this.updating=new Map,this.tracker=n.createTracker("decorator")}begin(e){this.config=e,this.registerDecorationTypes(e),u.window.visibleTextEditors.forEach((e=>this.applyDecorations(e))),u.workspace.onDidOpenTextDocument((e=>{this.applyDecorationsFromEvent(e)}),null,this.context.subscriptions),u.workspace.onDidChangeTextDocument((e=>{this.applyDecorationsFromEvent(e.document)}),null,this.context.subscriptions),u.window.onDidChangeVisibleTextEditors((e=>{e.forEach((e=>this.applyDecorations(e)))}),null,this.context.subscriptions)}configurationUpdated(e){this.config=e,this.registerDecorationTypes(e),u.window.visibleTextEditors.forEach((e=>{this.removeDecorations(e),this.applyDecorations(e)}))}registerDecorationTypes(e){Object.keys(this.decorations).forEach((e=>this.decorations[e].dispose())),this.decorations={},e.enableDecorations&&e.enableEditorOverview&&((e.enableDecorations||e.enableEditorOverview)&&(this.decorations["current.content"]=u.window.createTextEditorDecorationType(this.generateBlockRenderOptions("merge.currentContentBackground","editorOverviewRuler.currentContentForeground",e)),this.decorations["incoming.content"]=u.window.createTextEditorDecorationType(this.generateBlockRenderOptions("merge.incomingContentBackground","editorOverviewRuler.incomingContentForeground",e)),this.decorations["commonAncestors.content"]=u.window.createTextEditorDecorationType(this.generateBlockRenderOptions("merge.commonContentBackground","editorOverviewRuler.commonContentForeground",e))),e.enableDecorations&&(this.decorations["current.header"]=u.window.createTextEditorDecorationType({isWholeLine:this.decorationUsesWholeLine,backgroundColor:new u.ThemeColor("merge.currentHeaderBackground"),color:new u.ThemeColor("editor.foreground"),outlineStyle:"solid",outlineWidth:"1pt",outlineColor:new u.ThemeColor("merge.border"),after:{contentText:" "+u.l10n.t("(Current Change)"),color:new u.ThemeColor("descriptionForeground")}}),this.decorations["commonAncestors.header"]=u.window.createTextEditorDecorationType({isWholeLine:this.decorationUsesWholeLine,backgroundColor:new u.ThemeColor("merge.commonHeaderBackground"),color:new u.ThemeColor("editor.foreground"),outlineStyle:"solid",outlineWidth:"1pt",outlineColor:new u.ThemeColor("merge.border")}),this.decorations.splitter=u.window.createTextEditorDecorationType({color:new u.ThemeColor("editor.foreground"),outlineStyle:"solid",outlineWidth:"1pt",outlineColor:new u.ThemeColor("merge.border"),isWholeLine:this.decorationUsesWholeLine}),this.decorations["incoming.header"]=u.window.createTextEditorDecorationType({backgroundColor:new u.ThemeColor("merge.incomingHeaderBackground"),color:new u.ThemeColor("editor.foreground"),outlineStyle:"solid",outlineWidth:"1pt",outlineColor:new u.ThemeColor("merge.border"),isWholeLine:this.decorationUsesWholeLine,after:{contentText:" "+u.l10n.t("(Incoming Change)"),color:new u.ThemeColor("descriptionForeground")}})))}dispose(){Object.keys(this.decorations).forEach((e=>{this.decorations[e].dispose()})),this.decorations={}}generateBlockRenderOptions(e,n,t){const r={};return t.enableDecorations&&(r.backgroundColor=new u.ThemeColor(e),r.isWholeLine=this.decorationUsesWholeLine),t.enableEditorOverview&&(r.overviewRulerColor=new u.ThemeColor(n),r.overviewRulerLane=u.OverviewRulerLane.Full),r}applyDecorationsFromEvent(e){for(const n of u.window.visibleTextEditors)n.document===e&&this.applyDecorations(n)}async applyDecorations(e){if(e&&e.document&&this.config&&(this.config.enableDecorations||this.config.enableEditorOverview)&&!this.updating.get(e))try{this.updating.set(e,!0);const n=await this.tracker.getConflicts(e.document);if(-1===u.window.visibleTextEditors.indexOf(e))return;if(0===n.length)return void this.removeDecorations(e);const t={},r=(e,n)=>{t[e]=t[e]||[],t[e].push(n)};n.forEach((e=>{e.current.decoratorContent.isEmpty||r("current.content",e.current.decoratorContent),e.incoming.decoratorContent.isEmpty||r("incoming.content",e.incoming.decoratorContent),e.commonAncestors.forEach((e=>{e.decoratorContent.isEmpty||r("commonAncestors.content",e.decoratorContent)})),this.config.enableDecorations&&(r("current.header",e.current.header),r("splitter",e.splitter),r("incoming.header",e.incoming.header),e.commonAncestors.forEach((e=>{r("commonAncestors.header",e.header)})))})),Object.keys(t).forEach((n=>{const r=this.decorations[n];r&&e.setDecorations(r,t[n])}))}finally{this.updating.delete(e)}}removeDecorations(e){Object.keys(this.decorations).forEach((n=>{const t=this.decorations[n];t&&e.setDecorations(t,[])}))}}},5781:function(e,n,t){var r,i=this&&this.__createBinding||(Object.create?function(e,n,t,r){void 0===r&&(r=t);var i=Object.getOwnPropertyDescriptor(n,t);i&&!("get"in i?!n.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return n[t]}}),Object.defineProperty(e,r,i)}:function(e,n,t,r){void 0===r&&(r=t),e[r]=n[t]}),o=this&&this.__setModuleDefault||(Object.create?function(e,n){Object.defineProperty(e,"default",{enumerable:!0,value:n})}:function(e,n){e.default=n}),a=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var n=[];for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(n[n.length]=t);return n},r(e)},function(e){if(e&&e.__esModule)return e;var n={};if(null!=e)for(var t=r(e),a=0;a<t.length;a++)"default"!==t[a]&&i(n,e,t[a]);return o(n,e),n}),u=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(n,"__esModule",{value:!0});const c=a(t(1398)),s=u(t(2346)),l=u(t(977)),f=u(t(7146)),d=u(t(881)),v=u(t(1148)),h=u(t(1170));n.default=class{constructor(e){this.context=e,this.services=[];const{aiKey:n}=e.extension.packageJSON;this.telemetryReporter=new h.default(n),e.subscriptions.push(this.telemetryReporter)}begin(){const e=this.createExtensionConfiguration(),n=new s.default(this.telemetryReporter);this.services.push(n,new f.default(n),new l.default(n),new d.default(this.context),new v.default(this.context,n)),this.services.forEach((n=>{n.begin&&n.begin instanceof Function&&n.begin(e)})),c.workspace.onDidChangeConfiguration((()=>{this.services.forEach((e=>{e.configurationUpdated&&e.configurationUpdated instanceof Function&&e.configurationUpdated(this.createExtensionConfiguration())}))}))}createExtensionConfiguration(){const e=c.workspace.getConfiguration("merge-conflict"),n=e.get("codeLens.enabled",!0),t=e.get("decorators.enabled",!0);return{enableCodeLens:n,enableDecorations:t,enableEditorOverview:t}}dispose(){this.services.forEach((e=>e.dispose())),this.services=[]}}},1398:e=>{e.exports=require("vscode")},5692:e=>{e.exports=require("https")},857:e=>{e.exports=require("os")}},n={};function t(r){var i=n[r];if(void 0!==i)return i.exports;var o=n[r]={exports:{}};return e[r].call(o.exports,o,o.exports,t),o.exports}t.d=(e,n)=>{for(var r in n)t.o(n,r)&&!t.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:n[r]})},t.o=(e,n)=>Object.prototype.hasOwnProperty.call(e,n),t.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r=t(7282),i=exports;for(var o in r)i[o]=r[o];r.__esModule&&Object.defineProperty(i,"__esModule",{value:!0})})();
//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/2901c5ac6db8a986a5666c3af51ff804d05af0d4/extensions/merge-conflict/dist/mergeConflictMain.js.map