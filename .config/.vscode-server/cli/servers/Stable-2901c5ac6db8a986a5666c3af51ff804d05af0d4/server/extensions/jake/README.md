# Jake - JavaScript build tool

**Notice:** This extension is bundled with Visual Studio Code. It can be disabled but not uninstalled.

## Features

This extension supports running [<PERSON>](http://jakejs.com/) tasks defined in a `Jakefile.js` file as [VS Code tasks](https://code.visualstudio.com/docs/editor/tasks). <PERSON> tasks with the name 'build', 'compile', or 'watch' are treated as build tasks.

To run Jake tasks, use the **Tasks** menu.

## Settings

- `jake.autoDetect` - Enable detecting tasks from `Jakefile.js` files, the default is `on`.
