"""
Unit tests for EmployeeJobLevel model.
Tests business logic, validations, and model functionality.
"""

import pytest
from datetime import datetime, date
from models import Employ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, User, JobLevel
from extensions import db


class TestEmployeeJobLevelModel:
    """Test suite for EmployeeJobLevel model unit tests"""

    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        """Setup test data for each test method"""
        self.app = app
        self.user = test_user
        
        # Use existing job level from database instead of creating new one
        self.test_job_level = JobLevel.query.first()
        if not self.test_job_level:
            # Only create if none exists
            self.test_job_level = JobLevel(
                name='Test Job Level EmployeeJobLevel',
                level_number=7777,
                description='Job level for EmployeeJobLevel testing'
            )
            db.session.add(self.test_job_level)
            db.session.commit()

    def test_employeejoblevel_creation_basic(self):
        """Test basic employee job level creation"""
        employee_job_level = EmployeeJobLevel(
            user_id=self.user.id,
            job_level_id=self.test_job_level.id,
            start_date=date.today(),
            current_salary=50000.0
        )
        
        db.session.add(employee_job_level)
        db.session.commit()
        
        assert employee_job_level.id is not None
        assert employee_job_level.user_id == self.user.id
        assert employee_job_level.job_level_id == self.test_job_level.id
        assert employee_job_level.start_date == date.today()
        assert employee_job_level.current_salary == 50000.0

    def test_employeejoblevel_relationships(self):
        """Test relationships with User and JobLevel models"""
        employee_job_level = EmployeeJobLevel(
            user_id=self.user.id,
            job_level_id=self.test_job_level.id,
            start_date=date.today()
        )
        
        db.session.add(employee_job_level)
        db.session.commit()
        
        # Test forward relationships (using correct relationship names)
        assert employee_job_level.employee is not None
        assert employee_job_level.employee.id == self.user.id
        assert employee_job_level.job_level is not None
        assert employee_job_level.job_level.id == self.test_job_level.id

    def test_employeejoblevel_active_assignment(self):
        """Test active job level assignment (no end_date)"""
        employee_job_level = EmployeeJobLevel(
            user_id=self.user.id,
            job_level_id=self.test_job_level.id,
            start_date=date.today(),
            is_active=True
        )
        
        db.session.add(employee_job_level)
        db.session.commit()
        
        assert employee_job_level.end_date is None
        assert employee_job_level.is_active is True

    def test_employeejoblevel_historical_assignment(self):
        """Test historical job level assignment (with end_date)"""
        employee_job_level = EmployeeJobLevel(
            user_id=self.user.id,
            job_level_id=self.test_job_level.id,
            start_date=date(2023, 1, 1),
            end_date=date(2023, 12, 31),
            is_active=False
        )
        
        db.session.add(employee_job_level)
        db.session.commit()
        
        assert employee_job_level.start_date == date(2023, 1, 1)
        assert employee_job_level.end_date == date(2023, 12, 31)
        assert employee_job_level.is_active is False

    def test_employeejoblevel_salary_tracking(self):
        """Test salary tracking functionality"""
        salaries = [40000.0, 45000.0, 50000.0, 55000.0]
        
        employee_job_levels = []
        for i, salary in enumerate(salaries):
            employee_job_level = EmployeeJobLevel(
                user_id=self.user.id,
                job_level_id=self.test_job_level.id,
                start_date=date(2023, i+1, 1),
                current_salary=salary
            )
            employee_job_levels.append(employee_job_level)
        
        db.session.add_all(employee_job_levels)
        db.session.commit()
        
        for employee_job_level, expected_salary in zip(employee_job_levels, salaries):
            assert employee_job_level.current_salary == expected_salary

    def test_employeejoblevel_notes_functionality(self):
        """Test notes field functionality"""
        notes_text = "Promoted due to excellent performance and leadership skills"
        
        employee_job_level = EmployeeJobLevel(
            user_id=self.user.id,
            job_level_id=self.test_job_level.id,
            start_date=date.today(),
            notes=notes_text
        )
        
        db.session.add(employee_job_level)
        db.session.commit()
        
        assert employee_job_level.notes == notes_text

    def test_employeejoblevel_created_by_tracking(self):
        """Test created_by field functionality"""
        # Create another user as the creator
        creator = User(username='hr_manager', email='<EMAIL>')
        db.session.add(creator)
        db.session.commit()
        
        employee_job_level = EmployeeJobLevel(
            user_id=self.user.id,
            job_level_id=self.test_job_level.id,
            start_date=date.today(),
            created_by=creator.id
        )
        
        db.session.add(employee_job_level)
        db.session.commit()
        
        assert employee_job_level.created_by == creator.id

    def test_employeejoblevel_timestamps(self):
        """Test automatic timestamp handling"""
        employee_job_level = EmployeeJobLevel(
            user_id=self.user.id,
            job_level_id=self.test_job_level.id,
            start_date=date.today()
        )
        
        db.session.add(employee_job_level)
        db.session.commit()
        
        assert employee_job_level.created_at is not None
        assert isinstance(employee_job_level.created_at, datetime)

    def test_employeejoblevel_query_current_assignments(self):
        """Test querying current job level assignments"""
        # Current assignment
        current_assignment = EmployeeJobLevel(
            user_id=self.user.id,
            job_level_id=self.test_job_level.id,
            start_date=date.today(),
            is_active=True
        )
        
        db.session.add(current_assignment)
        db.session.commit()
        
        # Query current assignments (no end_date and is_active=True)
        current_assignments = EmployeeJobLevel.query.filter_by(
            end_date=None, is_active=True
        ).all()
        
        assert len(current_assignments) >= 1
        assert current_assignment in current_assignments

    def test_employeejoblevel_query_by_user(self):
        """Test querying job level history by user"""
        employee_job_level = EmployeeJobLevel(
            user_id=self.user.id,
            job_level_id=self.test_job_level.id,
            start_date=date.today()
        )
        
        db.session.add(employee_job_level)
        db.session.commit()
        
        # Query by user
        user_job_levels = EmployeeJobLevel.query.filter_by(user_id=self.user.id).all()
        assert len(user_job_levels) >= 1
        assert employee_job_level in user_job_levels

    def test_employeejoblevel_query_by_job_level(self):
        """Test querying employees by job level"""
        employee_job_level = EmployeeJobLevel(
            user_id=self.user.id,
            job_level_id=self.test_job_level.id,
            start_date=date.today()
        )
        
        db.session.add(employee_job_level)
        db.session.commit()
        
        # Query by job level
        job_level_employees = EmployeeJobLevel.query.filter_by(
            job_level_id=self.test_job_level.id
        ).all()
        assert len(job_level_employees) >= 1
        assert employee_job_level in job_level_employees

    def test_employeejoblevel_update_operations(self):
        """Test job level assignment update operations"""
        employee_job_level = EmployeeJobLevel(
            user_id=self.user.id,
            job_level_id=self.test_job_level.id,
            start_date=date.today(),
            current_salary=45000.0,
            is_active=True
        )
        
        db.session.add(employee_job_level)
        db.session.commit()
        
        # Update assignment
        employee_job_level.current_salary = 50000.0
        employee_job_level.notes = 'Salary increase'
        
        db.session.commit()
        
        updated_assignment = EmployeeJobLevel.query.get(employee_job_level.id)
        assert updated_assignment.current_salary == 50000.0
        assert updated_assignment.notes == 'Salary increase'

    def test_employeejoblevel_end_assignment(self):
        """Test ending a job level assignment"""
        employee_job_level = EmployeeJobLevel(
            user_id=self.user.id,
            job_level_id=self.test_job_level.id,
            start_date=date(2023, 1, 1),
            is_active=True
        )
        
        db.session.add(employee_job_level)
        db.session.commit()
        
        # End the assignment
        employee_job_level.end_date = date.today()
        employee_job_level.is_active = False
        
        db.session.commit()
        
        assert employee_job_level.end_date == date.today()
        assert employee_job_level.is_active is False

    def test_employeejoblevel_deletion(self):
        """Test job level assignment deletion"""
        employee_job_level = EmployeeJobLevel(
            user_id=self.user.id,
            job_level_id=self.test_job_level.id,
            start_date=date.today()
        )
        
        db.session.add(employee_job_level)
        db.session.commit()
        assignment_id = employee_job_level.id
        
        db.session.delete(employee_job_level)
        db.session.commit()
        
        deleted_assignment = EmployeeJobLevel.query.get(assignment_id)
        assert deleted_assignment is None
