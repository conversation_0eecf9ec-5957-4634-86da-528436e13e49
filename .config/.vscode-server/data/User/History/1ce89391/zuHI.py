"""Unit tests for PollOption model."""
import pytest
from models import PollOption, Poll, User
from extensions import db

class TestPollOptionModel:
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        self.app = app
        self.user = test_user
        
        self.poll = Poll.query.first()
        if not self.poll:
            self.poll = Poll(title='Test Poll', description='Test', author_id=self.user.id)  # Campo corretto
            db.session.add(self.poll)
            db.session.commit()

    def test_polloption_creation_basic(self):
        option = PollOption(
            poll_id=self.poll.id,
            option_text='Option A'  # Campo corretto è 'option_text'
            # Rimosso order_index - non esiste nel modello
        )
        db.session.add(option)
        db.session.commit()

        assert option.id is not None
        assert option.poll_id == self.poll.id
        assert option.option_text == 'Option A'

    def test_polloption_multiple_options(self):
        options = []
        for i in range(4):
            option = PollOption(
                poll_id=self.poll.id,
                text=f'Option {chr(65+i)}',
                order_index=i+1
            )
            options.append(option)
        
        db.session.add_all(options)
        db.session.commit()
        
        for i, option in enumerate(options):
            assert option.text == f'Option {chr(65+i)}'
            assert option.order_index == i+1

    def test_polloption_deletion(self):
        option = PollOption(
            poll_id=self.poll.id,
            text='To Delete',
            order_index=1
        )
        db.session.add(option)
        db.session.commit()
        option_id = option.id
        
        db.session.delete(option)
        db.session.commit()
        
        deleted = PollOption.query.get(option_id)
        assert deleted is None
