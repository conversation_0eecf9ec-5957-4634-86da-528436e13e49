"""
Servizio per la gestione delle pre-fatture italiane.
Genera pre-fatture da timesheet entries e calcola tasse secondo normativa italiana.
"""

from datetime import date, datetime, timedelta
from decimal import Decimal
from typing import List, Dict, Optional, Tuple
from sqlalchemy import and_, or_
from extensions import db
from models import (
    PreInvoice, PreInvoiceLine, TimesheetEntry, Project, Client, 
    Contract, User, CompanyInvoicingSettings, IntegrationSettings
)


class PreInvoicingService:
    """Servizio principale per la gestione delle pre-fatture"""
    
    def __init__(self):
        self.company_settings = self._get_company_settings()
    
    def _get_company_settings(self) -> CompanyInvoicingSettings:
        """Ottiene le impostazioni aziendali per la fatturazione"""
        try:
            settings = CompanyInvoicingSettings.query.first()
        except Exception:
            # Durante i test o se le tabelle non esistono ancora, ritorna None
            settings = None

        if not settings:
            try:
                # Crea impostazioni di default se non esistono
                settings = CompanyInvoicingSettings(
                    company_name="La Mia Azienda",
                    default_vat_rate=Decimal('22.0'),
                    default_retention_rate=Decimal('20.0'),
                    default_payment_terms=30,
                    invoice_prefix="PRE",
                    current_year=datetime.now().year,
                    last_number=0
                )
                db.session.add(settings)
                db.session.commit()
            except Exception:
                # Durante i test, ritorna un oggetto mock
                settings = type('MockSettings', (), {
                    'company_name': "Test Company",
                    'default_vat_rate': Decimal('22.0'),
                    'default_retention_rate': Decimal('20.0'),
                    'default_payment_terms': 30,
                    'invoice_prefix': "TEST",
                    'current_year': datetime.now().year,
                    'last_number': 0
                })()
        return settings
    
    def get_billable_timesheet_entries(
        self, 
        client_id: int, 
        period_start: date, 
        period_end: date,
        project_ids: Optional[List[int]] = None
    ) -> List[TimesheetEntry]:
        """
        Ottiene tutte le timesheet entries fatturabili per un cliente nel periodo specificato
        """
        query = db.session.query(TimesheetEntry).join(Project).join(Contract).filter(
            and_(
                Contract.client_id == client_id,
                TimesheetEntry.date >= period_start,
                TimesheetEntry.date <= period_end,
                TimesheetEntry.billable == True,
                TimesheetEntry.billing_status == 'unbilled'
            )
        )
        
        # Filtra per progetti specifici se richiesto
        if project_ids:
            query = query.filter(TimesheetEntry.project_id.in_(project_ids))
        
        return query.all()
    
    def calculate_billing_summary(
        self, 
        client_id: int, 
        period_start: date, 
        period_end: date,
        project_ids: Optional[List[int]] = None
    ) -> Dict:
        """
        Calcola un riassunto delle ore fatturabili per periodo
        """
        entries = self.get_billable_timesheet_entries(
            client_id, period_start, period_end, project_ids
        )
        
        if not entries:
            return {
                'total_hours': Decimal('0'),
                'total_amount': Decimal('0'),
                'projects_summary': [],
                'entries_count': 0
            }
        
        # Raggruppa per progetto
        projects_summary = {}
        total_hours = Decimal('0')
        total_amount = Decimal('0')
        
        for entry in entries:
            project = entry.project
            project_id = project.id
            
            if project_id not in projects_summary:
                projects_summary[project_id] = {
                    'project': {
                        'id': project.id,
                        'name': project.name,
                        'contract': {
                            'id': project.contract.id if project.contract else None,
                            'contract_number': project.contract.contract_number if project.contract else None,
                            'hourly_rate': project.contract.hourly_rate if project.contract else None
                        }
                    },
                    'total_hours': Decimal('0'),
                    'total_amount': Decimal('0'),
                    'entries': []
                }
            
            # Calcola tariffa: usa billing_rate dell'entry o hourly_rate del contratto
            rate = Decimal(str(entry.billing_rate)) if entry.billing_rate else (
                Decimal(str(project.contract.hourly_rate)) if project.contract and project.contract.hourly_rate else Decimal('0')
            )
            
            hours = Decimal(str(entry.hours))
            amount = hours * rate
            
            projects_summary[project_id]['total_hours'] += hours
            projects_summary[project_id]['total_amount'] += amount
            projects_summary[project_id]['entries'].append({
                'id': entry.id,
                'date': entry.date,
                'hours': float(hours),
                'rate': float(rate),
                'amount': float(amount),
                'description': entry.description,
                'user': {
                    'id': entry.user.id,
                    'name': f"{entry.user.first_name} {entry.user.last_name}"
                }
            })
            
            total_hours += hours
            total_amount += amount
        
        return {
            'total_hours': float(total_hours),
            'total_amount': float(total_amount),
            'projects_summary': list(projects_summary.values()),
            'entries_count': len(entries)
        }
    
    def generate_pre_invoice(
        self, 
        client_id: int, 
        period_start: date, 
        period_end: date,
        created_by: int,
        project_ids: Optional[List[int]] = None,
        vat_rate: Optional[Decimal] = None,
        retention_rate: Optional[Decimal] = None,
        notes: Optional[str] = None
    ) -> PreInvoice:
        """
        Genera una pre-fattura da timesheet entries
        """
        # Verifica che il cliente esista
        client = Client.query.get(client_id)
        if not client:
            raise ValueError(f"Cliente con ID {client_id} non trovato")
        
        # Ottieni entries fatturabili
        timesheet_entries = self.get_billable_timesheet_entries(
            client_id, period_start, period_end, project_ids
        )
        
        if not timesheet_entries:
            raise ValueError(
                f"Nessuna timesheet entry fatturabile trovata per il cliente {client.name} "
                f"nel periodo {period_start} - {period_end}"
            )
        
        # Usa tassi di default se non specificati
        if vat_rate is None:
            vat_rate = self.company_settings.default_vat_rate
        if retention_rate is None:
            retention_rate = self.company_settings.default_retention_rate
        
        # Genera numero pre-fattura
        pre_invoice_number = self.company_settings.generate_next_number()
        
        # Crea pre-fattura
        pre_invoice = PreInvoice(
            client_id=client_id,
            pre_invoice_number=pre_invoice_number,
            billing_period_start=period_start,
            billing_period_end=period_end,
            generated_date=date.today(),
            status='draft',
            vat_rate=vat_rate,
            retention_rate=retention_rate,
            created_by=created_by,
            notes=notes or f"Pre-fattura per periodo {period_start} - {period_end}"
        )
        
        db.session.add(pre_invoice)
        db.session.flush()  # Per ottenere l'ID
        
        # Raggruppa entries per progetto e crea righe
        projects_data = self._group_entries_by_project(timesheet_entries)
        
        for project_id, project_data in projects_data.items():
            # Crea riga pre-fattura
            line = PreInvoiceLine(
                pre_invoice_id=pre_invoice.id,
                project_id=project_id,
                description=project_data['description'],
                total_hours=project_data['total_hours'],
                hourly_rate=project_data['hourly_rate'],
                total_amount=project_data['total_amount'],
                timesheet_entries_ids=[entry.id for entry in project_data['entries']]
            )
            
            db.session.add(line)
            
            # Marca entries come fatturate
            for entry in project_data['entries']:
                entry.billing_status = 'billed'
        
        # Calcola totali
        pre_invoice.calculate_totals()
        
        # Aggiorna numerazione
        db.session.add(self.company_settings)
        
        db.session.commit()
        
        return pre_invoice
    
    def _group_entries_by_project(self, entries: List[TimesheetEntry]) -> Dict:
        """
        Raggruppa timesheet entries per progetto per creare le righe fattura
        """
        projects_data = {}
        
        for entry in entries:
            project = entry.project
            project_id = project.id
            
            if project_id not in projects_data:
                # Determina la tariffa oraria per questo progetto
                default_rate = Decimal('0')
                if project.contract and project.contract.hourly_rate:
                    default_rate = Decimal(str(project.contract.hourly_rate))
                
                projects_data[project_id] = {
                    'project': project,
                    'description': f"Servizi professionali - {project.name}",
                    'total_hours': Decimal('0'),
                    'hourly_rate': default_rate,
                    'total_amount': Decimal('0'),
                    'entries': []
                }
            
            # Calcola ore e importo per questa entry
            hours = Decimal(str(entry.hours))
            rate = Decimal(str(entry.billing_rate)) if entry.billing_rate else projects_data[project_id]['hourly_rate']
            amount = hours * rate
            
            projects_data[project_id]['total_hours'] += hours
            projects_data[project_id]['total_amount'] += amount
            projects_data[project_id]['entries'].append(entry)
            
            # Aggiorna la tariffa media se necessario
            if projects_data[project_id]['total_hours'] > 0:
                projects_data[project_id]['hourly_rate'] = (
                    projects_data[project_id]['total_amount'] / projects_data[project_id]['total_hours']
                )
        
        return projects_data
    
    def get_pre_invoice_with_details(self, pre_invoice_id: int) -> Optional[Dict]:
        """
        Ottiene una pre-fattura con tutti i dettagli per la visualizzazione
        """
        pre_invoice = PreInvoice.query.get(pre_invoice_id)
        if not pre_invoice:
            return None
        
        # Prepara dati completi
        lines_data = []
        for line in pre_invoice.lines:
            lines_data.append({
                'id': line.id,
                'project': {
                    'id': line.project.id if line.project else None,
                    'name': line.project.name if line.project else 'Progetto eliminato',
                    'contract': {
                        'id': line.project.contract.id if line.project and line.project.contract else None,
                        'contract_number': line.project.contract.contract_number if line.project and line.project.contract else None
                    } if line.project else None
                },
                'description': line.description,
                'total_hours': float(line.total_hours),
                'hourly_rate': float(line.hourly_rate),
                'total_amount': float(line.total_amount),
                'timesheet_entries_count': len(line.timesheet_entries_ids) if line.timesheet_entries_ids else 0
            })
        
        return {
            'id': pre_invoice.id,
            'pre_invoice_number': pre_invoice.pre_invoice_number,
            'client': {
                'id': pre_invoice.client.id,
                'name': pre_invoice.client.name,
                'vat_number': pre_invoice.client.vat_number,
                'fiscal_code': pre_invoice.client.fiscal_code,
                'address': pre_invoice.client.address,
                'email': pre_invoice.client.email,
                'phone': pre_invoice.client.phone
            },
            'contract': {
                'id': pre_invoice.contract.id if pre_invoice.contract else None,
                'contract_number': pre_invoice.contract.contract_number if pre_invoice.contract else None
            },
            'billing_period_start': pre_invoice.billing_period_start.isoformat(),
            'billing_period_end': pre_invoice.billing_period_end.isoformat(),
            'generated_date': pre_invoice.generated_date.isoformat(),
            'status': pre_invoice.status,
            'display_status': pre_invoice.display_status,
            'subtotal': float(pre_invoice.subtotal),
            'vat_rate': float(pre_invoice.vat_rate),
            'vat_amount': float(pre_invoice.vat_amount),
            'retention_rate': float(pre_invoice.retention_rate),
            'retention_amount': float(pre_invoice.retention_amount),
            'total_amount': float(pre_invoice.total_amount),
            'external_invoice_id': pre_invoice.external_invoice_id,
            'external_status': pre_invoice.external_status,
            'external_pdf_url': pre_invoice.external_pdf_url,
            'notes': pre_invoice.notes,
            'lines': lines_data,
            'creator': {
                'id': pre_invoice.creator.id,
                'name': f"{pre_invoice.creator.first_name} {pre_invoice.creator.last_name}"
            },
            'created_at': pre_invoice.created_at.isoformat(),
            'updated_at': pre_invoice.updated_at.isoformat(),
            'can_edit': pre_invoice.can_edit,
            'can_send_external': pre_invoice.can_send_external
        }
    
    def update_pre_invoice_status(self, pre_invoice_id: int, new_status: str) -> PreInvoice:
        """
        Aggiorna lo stato di una pre-fattura e invia a FattureInCloud se necessario
        """
        valid_statuses = ['draft', 'ready', 'sent_external', 'invoiced']
        if new_status not in valid_statuses:
            raise ValueError(f"Stato non valido: {new_status}. Valori ammessi: {valid_statuses}")
        
        pre_invoice = PreInvoice.query.get(pre_invoice_id)
        if not pre_invoice:
            raise ValueError(f"Pre-fattura con ID {pre_invoice_id} non trovata")
        
        # Verifica transizioni di stato valide
        if new_status == 'ready' and pre_invoice.status != 'draft':
            raise ValueError("Solo le pre-fatture in bozza possono essere marcate come pronte")
        
        if new_status == 'sent_external' and pre_invoice.status != 'ready':
            raise ValueError("Solo le pre-fatture pronte possono essere inviate al sistema esterno")
        
        # Se stiamo inviando al sistema esterno, verifica integrazione e invia
        if new_status == 'sent_external':
            from services.fattureincloud_service import FattureInCloudService
            
            # Verifica che l'integrazione FattureInCloud sia attiva
            integration = IntegrationSettings.query.filter_by(
                provider='fattureincloud',
                is_active=True
            ).first()
            
            if not integration:
                raise ValueError("Integrazione FattureInCloud non configurata o non attiva")
            
            # Invia a FattureInCloud
            fic_service = FattureInCloudService()
            result = fic_service.send_pre_invoice(pre_invoice_id)
            
            if not result['success']:
                # Aggiorna l'errore nell'integrazione
                integration.last_error = result['error']
                integration.updated_at = datetime.utcnow()
                db.session.commit()
                
                raise ValueError(f"Errore nell'invio a FattureInCloud: {result['error']}")
            
            # Il servizio FattureInCloud ha già aggiornato lo stato e i dati esterni
            # Ricarica la pre-fattura per avere i dati aggiornati
            db.session.refresh(pre_invoice)
            return pre_invoice
        
        # Per tutti gli altri stati, aggiorna normalmente
        pre_invoice.status = new_status
        db.session.commit()
        
        return pre_invoice
    
    def delete_pre_invoice(self, pre_invoice_id: int) -> bool:
        """
        Elimina una pre-fattura e rimette le timesheet entries come non fatturate
        """
        pre_invoice = PreInvoice.query.get(pre_invoice_id)
        if not pre_invoice:
            raise ValueError(f"Pre-fattura con ID {pre_invoice_id} non trovata")
        
        # Non permettere eliminazione di pre-fatture già inviate
        if pre_invoice.status in ['sent_external', 'invoiced']:
            raise ValueError("Non è possibile eliminare pre-fatture già inviate o fatturate")
        
        # Rimetti timesheet entries come non fatturate
        for line in pre_invoice.lines:
            if line.timesheet_entries_ids:
                TimesheetEntry.query.filter(
                    TimesheetEntry.id.in_(line.timesheet_entries_ids)
                ).update({
                    'billing_status': 'unbilled'
                }, synchronize_session=False)
        
        # Elimina pre-fattura (le righe vengono eliminate automaticamente con cascade)
        db.session.delete(pre_invoice)
        db.session.commit()
        
        return True


class BillingDashboardService:
    """Servizio per le statistiche della dashboard fatturazione"""
    
    def get_billing_dashboard_stats(self) -> Dict:
        """
        Ottiene tutte le statistiche per la dashboard fatturazione
        """
        # Ore non fatturate totali
        unbilled_entries = TimesheetEntry.query.filter(
            and_(
                TimesheetEntry.billable == True,
                TimesheetEntry.billing_status == 'unbilled'
            )
        ).all()
        
        total_unbilled_hours = sum(Decimal(str(entry.hours)) for entry in unbilled_entries)
        
        # Calcola importo totale non fatturato
        total_unbilled_amount = Decimal('0')
        clients_unbilled = {}
        
        for entry in unbilled_entries:
            # Determina la tariffa
            rate = Decimal('0')
            if entry.billing_rate:
                rate = Decimal(str(entry.billing_rate))
            elif entry.project and entry.project.contract and entry.project.contract.hourly_rate:
                rate = Decimal(str(entry.project.contract.hourly_rate))
            
            amount = Decimal(str(entry.hours)) * rate
            total_unbilled_amount += amount
            
            # Raggruppa per cliente
            if entry.project and entry.project.contract:
                client_id = entry.project.contract.client_id
                client_name = entry.project.contract.client.name
                
                if client_id not in clients_unbilled:
                    clients_unbilled[client_id] = {
                        'id': client_id,
                        'name': client_name,
                        'unbilled_hours': Decimal('0'),
                        'unbilled_amount': Decimal('0')
                    }
                
                clients_unbilled[client_id]['unbilled_hours'] += Decimal(str(entry.hours))
                clients_unbilled[client_id]['unbilled_amount'] += amount
        
        # Converti in formato serializzabile e ordina per importo
        top_clients = sorted(
            [
                {
                    'id': data['id'],
                    'name': data['name'],
                    'unbilled_hours': float(data['unbilled_hours']),
                    'unbilled_amount': float(data['unbilled_amount'])
                }
                for data in clients_unbilled.values()
            ],
            key=lambda x: x['unbilled_amount'],
            reverse=True
        )[:5]  # Top 5 clienti
        
        # Pre-fatture recenti
        recent_pre_invoices = PreInvoice.query.order_by(
            PreInvoice.created_at.desc()
        ).limit(5).all()
        
        recent_pre_invoices_data = [
            {
                'id': pi.id,
                'pre_invoice_number': pi.pre_invoice_number,
                'client_name': pi.client.name,
                'total_amount': float(pi.total_amount),
                'status': pi.status,
                'display_status': pi.display_status,
                'created_at': pi.created_at.isoformat()
            }
            for pi in recent_pre_invoices
        ]
        
        return {
            'total_unbilled_amount': float(total_unbilled_amount),
            'total_unbilled_hours': float(total_unbilled_hours),
            'clients_with_unbilled_count': len(clients_unbilled),
            'top_unbilled_clients': top_clients,
            'recent_pre_invoices': recent_pre_invoices_data,
            'summary': {
                'total_clients_with_billing': len(clients_unbilled),
                'total_unbilled_entries': len(unbilled_entries),
                'avg_hourly_rate': float(total_unbilled_amount / total_unbilled_hours) if total_unbilled_hours > 0 else 0
            }
        }
    
    def get_client_billing_summary(self, client_id: int) -> Dict:
        """
        Ottiene riassunto fatturazione per un singolo cliente
        """
        client = Client.query.get(client_id)
        if not client:
            raise ValueError(f"Cliente con ID {client_id} non trovato")
        
        # Ore non fatturate per questo cliente
        unbilled_entries = TimesheetEntry.query.join(Project).join(Contract).filter(
            and_(
                Contract.client_id == client_id,
                TimesheetEntry.billable == True,
                TimesheetEntry.billing_status == 'unbilled'
            )
        ).all()
        
        # Raggruppa per progetto
        projects_billing = {}
        total_unbilled_hours = Decimal('0')
        total_unbilled_amount = Decimal('0')
        
        for entry in unbilled_entries:
            project = entry.project
            project_id = project.id
            
            if project_id not in projects_billing:
                projects_billing[project_id] = {
                    'id': project.id,
                    'name': project.name,
                    'contract_id': project.contract.id if project.contract else None,
                    'contract_number': project.contract.contract_number if project.contract else None,
                    'total_hours': Decimal('0'),
                    'billed_hours': Decimal('0'),
                    'unbilled_hours': Decimal('0'),
                    'unbilled_amount': Decimal('0')
                }
            
            # Calcola tariffa e importo
            rate = Decimal('0')
            if entry.billing_rate:
                rate = Decimal(str(entry.billing_rate))
            elif project.contract and project.contract.hourly_rate:
                rate = Decimal(str(project.contract.hourly_rate))
            
            hours = Decimal(str(entry.hours))
            amount = hours * rate
            
            projects_billing[project_id]['unbilled_hours'] += hours
            projects_billing[project_id]['unbilled_amount'] += amount
            
            total_unbilled_hours += hours
            total_unbilled_amount += amount
        
        # Ottieni ore totali per progetto (incluse quelle già fatturate)
        for project_id in projects_billing:
            total_entries = TimesheetEntry.query.filter(
                and_(
                    TimesheetEntry.project_id == project_id,
                    TimesheetEntry.billable == True
                )
            ).all()
            
            total_hours = sum(Decimal(str(entry.hours)) for entry in total_entries)
            billed_hours = sum(
                Decimal(str(entry.hours)) for entry in total_entries 
                if entry.billing_status == 'billed'
            )
            
            projects_billing[project_id]['total_hours'] = total_hours
            projects_billing[project_id]['billed_hours'] = billed_hours
        
        # Pre-fatture esistenti per questo cliente
        client_pre_invoices = PreInvoice.query.filter_by(client_id=client_id).order_by(
            PreInvoice.created_at.desc()
        ).limit(10).all()
        
        pre_invoices_data = [
            {
                'id': pi.id,
                'pre_invoice_number': pi.pre_invoice_number,
                'total_amount': float(pi.total_amount),
                'status': pi.status,
                'display_status': pi.display_status,
                'billing_period_start': pi.billing_period_start.isoformat(),
                'billing_period_end': pi.billing_period_end.isoformat(),
                'created_at': pi.created_at.isoformat()
            }
            for pi in client_pre_invoices
        ]
        
        return {
            'client': {
                'id': client.id,
                'name': client.name,
                'vat_number': client.vat_number,
                'fiscal_code': client.fiscal_code
            },
            'unbilled_summary': {
                'total_hours': float(total_unbilled_hours),
                'total_amount': float(total_unbilled_amount)
            },
            'projects': [
                {
                    'id': data['id'],
                    'name': data['name'],
                    'contract_id': data['contract_id'],
                    'contract_number': data['contract_number'],
                    'total_hours': float(data['total_hours']),
                    'billed_hours': float(data['billed_hours']),
                    'unbilled_hours': float(data['unbilled_hours']),
                    'unbilled_amount': float(data['unbilled_amount'])
                }
                for data in projects_billing.values()
            ],
            'recent_pre_invoices': pre_invoices_data
        }