"""Unit tests for Task model."""
import pytest
from datetime import date
from models import Task, Project, User
from extensions import db

class TestTaskModel:
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        self.app = app
        self.user = test_user
        
        self.project = Project.query.first()
        if not self.project:
            self.project = Project(name='Test Project', description='Test')  # Rimosso manager_id
            db.session.add(self.project)
            db.session.commit()

    def test_task_creation_basic(self):
        task = Task(
            title='Test Task',
            description='This is a test task',
            project_id=self.project.id,
            assigned_to=self.user.id
        )
        db.session.add(task)
        db.session.commit()
        
        assert task.id is not None
        assert task.title == 'Test Task'
        assert task.description == 'This is a test task'
        assert task.project_id == self.project.id
        assert task.assigned_to == self.user.id

    def test_task_status(self):
        task = Task(
            title='Status Task',
            description='Task with status',
            project_id=self.project.id,
            status='in_progress'
        )
        db.session.add(task)
        db.session.commit()
        
        assert task.status == 'in_progress'

    def test_task_deletion(self):
        task = Task(title='To Delete', description='Delete me', project_id=self.project.id)
        db.session.add(task)
        db.session.commit()
        task_id = task.id
        
        db.session.delete(task)
        db.session.commit()
        
        deleted = Task.query.get(task_id)
        assert deleted is None
