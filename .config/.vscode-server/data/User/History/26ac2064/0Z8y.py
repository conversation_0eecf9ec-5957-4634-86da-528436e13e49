"""
Unit tests for Skill model.
Tests business logic, validations, and model functionality.
"""

import pytest
from datetime import datetime
from models import Skill, User
from extensions import db


class TestSkillModel:
    """Test suite for Skill model unit tests"""

    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        """Setup test data for each test method"""
        self.app = app
        self.user = test_user

    def test_skill_creation_basic(self):
        """Test basic skill creation with required fields"""
        skill = Skill(
            name='Python',
            category='Programming Languages'
        )

        db.session.add(skill)
        db.session.commit()

        assert skill.id is not None
        assert skill.name == 'Python'
        assert skill.category == 'Programming Languages'

    def test_skill_creation_complete(self):
        """Test skill creation with all fields (based on real DB schema)"""
        skill = Skill(
            name='React.js',
            category='Frontend Frameworks',
            description='JavaScript library for building user interfaces'
        )

        db.session.add(skill)
        db.session.commit()

        assert skill.description == 'JavaScript library for building user interfaces'
        assert skill.name == 'React.js'
        assert skill.category == 'Frontend Frameworks'

    def test_skill_repr_method(self):
        """Test string representation of skill"""
        skill = Skill(name='JavaScript', category='Programming')
        
        expected_repr = '<Skill JavaScript>'
        assert repr(skill) == expected_repr

    def test_skill_name_uniqueness(self):
        """Test that skill names should be unique"""
        skill1 = Skill(name='Java', category='Programming')
        skill2 = Skill(name='Java', category='Programming')  # Duplicate name
        
        db.session.add(skill1)
        db.session.commit()
        
        # This should potentially raise an error if uniqueness is enforced
        # For now, we test that both can be created (depends on DB constraints)
        db.session.add(skill2)
        try:
            db.session.commit()
            # If no constraint, both will exist
            assert True
        except Exception:
            # If constraint exists, this is expected
            db.session.rollback()
            assert True

    def test_skill_categories(self):
        """Test different skill categories"""
        categories = [
            'Programming Languages',
            'Frameworks',
            'Databases',
            'Cloud Platforms',
            'Soft Skills',
            'Project Management',
            'Design Tools'
        ]
        
        skills = []
        for i, category in enumerate(categories):
            skill = Skill(
                name=f'Skill {i}',
                category=category
            )
            skills.append(skill)
        
        db.session.add_all(skills)
        db.session.commit()
        
        for skill, expected_category in zip(skills, categories):
            assert skill.category == expected_category

    def test_skill_categories(self):
        """Test different skill categories"""
        categories = [
            'Programming Languages',
            'Frameworks',
            'Databases',
            'Cloud Platforms',
            'Soft Skills'
        ]

        skills = []
        for i, category in enumerate(categories):
            skill = Skill(
                name=f'Skill {i}',
                category=category
            )
            skills.append(skill)

        db.session.add_all(skills)
        db.session.commit()

        for skill, expected_category in zip(skills, categories):
            assert skill.category == expected_category

    def test_skill_description_field(self):
        """Test description field for detailed information"""
        long_description = """
        Vue.js is a progressive JavaScript framework for building user interfaces.
        It is designed to be incrementally adoptable and focuses on the view layer.
        Vue.js is known for its gentle learning curve and excellent documentation.
        """

        skill = Skill(
            name='Vue.js',
            category='Frontend Frameworks',
            description=long_description.strip()
        )

        db.session.add(skill)
        db.session.commit()

        assert skill.description == long_description.strip()

    def test_skill_name_field(self):
        """Test skill name field"""
        skill = Skill(
            name='JavaScript',
            category='Programming Languages'
        )

        db.session.add(skill)
        db.session.commit()

        assert skill.name == 'JavaScript'
        assert len(skill.name) <= 64  # Based on DB schema VARCHAR(64)

    def test_skill_query_by_category(self):
        """Test querying skills by category"""
        programming_skills = [
            Skill(name='Python_Cat', category='Programming Languages'),
            Skill(name='Java_Cat', category='Programming Languages'),
            Skill(name='C++_Cat', category='Programming Languages')
        ]

        framework_skills = [
            Skill(name='Django_Cat', category='Web Frameworks'),
            Skill(name='Flask_Cat', category='Web Frameworks')
        ]

        db.session.add_all(programming_skills + framework_skills)
        db.session.commit()

        # Query programming languages
        prog_results = Skill.query.filter_by(category='Programming Languages').all()
        assert len(prog_results) >= 3  # May have other skills from previous tests

        # Query web frameworks
        framework_results = Skill.query.filter_by(category='Web Frameworks').all()
        assert len(framework_results) >= 2  # May have other skills from previous tests

    def test_skill_query_by_name(self):
        """Test querying skills by name"""
        skills = [
            Skill(name='Python', category='Programming'),
            Skill(name='Java', category='Programming'),
            Skill(name='JavaScript', category='Programming')
        ]

        db.session.add_all(skills)
        db.session.commit()

        # Query specific skill
        python_skill = Skill.query.filter_by(name='Python').first()
        assert python_skill is not None
        assert python_skill.name == 'Python'
        assert python_skill.category == 'Programming'

    def test_skill_search_functionality(self):
        """Test skill search by name pattern"""
        skills = [
            Skill(name='JavaScript', category='Programming'),
            Skill(name='Java', category='Programming'),
            Skill(name='Python', category='Programming')
        ]

        db.session.add_all(skills)
        db.session.commit()

        # Search for skills containing 'Java'
        java_skills = Skill.query.filter(Skill.name.contains('Java')).all()
        java_names = [skill.name for skill in java_skills]

        assert 'JavaScript' in java_names
        assert 'Java' in java_names
        assert 'Python' not in java_names

    def test_skill_update_operations(self):
        """Test skill update operations"""
        skill = Skill(
            name='Original Name',
            category='Original Category'
        )

        db.session.add(skill)
        db.session.commit()

        # Update skill
        skill.name = 'Updated Name'
        skill.category = 'Updated Category'
        skill.description = 'Added description'

        db.session.commit()

        # Verify updates
        updated_skill = Skill.query.get(skill.id)
        assert updated_skill.name == 'Updated Name'
        assert updated_skill.category == 'Updated Category'
        assert updated_skill.description == 'Added description'

    def test_skill_deletion(self):
        """Test skill deletion"""
        skill = Skill(
            name='To Delete',
            category='Test'
        )
        
        db.session.add(skill)
        db.session.commit()
        skill_id = skill.id
        
        # Delete skill
        db.session.delete(skill)
        db.session.commit()
        
        # Verify deletion
        deleted_skill = Skill.query.get(skill_id)
        assert deleted_skill is None

    def test_skill_optional_fields(self):
        """Test optional fields behavior"""
        # Test skill with only required fields
        skill_minimal = Skill(
            name='Minimal Skill',
            category='Test'
        )

        # Test skill with all fields
        skill_complete = Skill(
            name='Complete Skill',
            category='Test',
            description='A complete skill description'
        )

        db.session.add_all([skill_minimal, skill_complete])
        db.session.commit()

        # Check minimal skill
        assert skill_minimal.name == 'Minimal Skill'
        assert skill_minimal.category == 'Test'
        assert skill_minimal.description is None

        # Check complete skill
        assert skill_complete.name == 'Complete Skill'
        assert skill_complete.description == 'A complete skill description'
