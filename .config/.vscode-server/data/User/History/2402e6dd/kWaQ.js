import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mountComponent, mockApiResponse } from '../utils/test-helpers.js'
import TimesheetRequests from '@/views/timesheet/TimesheetRequests.vue'
import { useTimesheetStore } from '@/stores/timesheet'
import { useAuthStore } from '@/stores/auth'

// Mock stores
vi.mock('@/stores/timesheet', () => ({
  useTimesheetStore: vi.fn()
}))

vi.mock('@/stores/auth', () => ({
  useAuthStore: vi.fn()
}))

// Mock API
global.fetch = vi.fn()

describe('Timesheet Requests Components', () => {
  let mockTimesheetStore
  let mockAuthStore

  beforeEach(() => {
    vi.clearAllMocks()
    
    // Setup mock timesheet store
    mockTimesheetStore = {
      loadTimeOffRequests: vi.fn().mockResolvedValue([]),
      createTimeOffRequest: vi.fn().mockResolvedValue(true),
      deleteTimeOffRequest: vi.fn().mockResolvedValue(true),
      loadTimeOffQuotas: vi.fn().mockResolvedValue({
        vacation: { remaining: 20, total: 26 },
        leave: { used: 2, total: 10 },
        smartworking: { used: 5 }
      }),
      setError: vi.fn(),
      error: null
    }
    
    // Setup mock auth store
    mockAuthStore = {
      user: { id: 1, first_name: 'Test', last_name: 'User' },
      isAuthenticated: true,
      csrfToken: 'test-csrf-token'
    }
    
    useTimesheetStore.mockReturnValue(mockTimesheetStore)
    useAuthStore.mockReturnValue(mockAuthStore)
    
    // Reset fetch mock
    fetch.mockClear()
  })

  describe('TimesheetRequests Component', () => {
    it('should render timesheet requests page', async () => {
      const wrapper = mountComponent(TimesheetRequests)
      
      expect(wrapper.text()).toContain('Richieste Timesheet')
      expect(wrapper.find('[data-testid="vacation-button"]')).toBeTruthy()
      expect(wrapper.find('[data-testid="leave-button"]')).toBeTruthy()
      expect(wrapper.find('[data-testid="smartworking-button"]')).toBeTruthy()
    })

    it('should load requests on mount', async () => {
      const mockRequests = [
        {
          id: 1,
          type: 'vacation',  // Campo DB: 'type'
          start_date: '2025-07-01',
          end_date: '2025-07-05',
          status: 'pending',
          duration_days: 5,
          notes: 'Summer vacation',
          created_at: '2025-06-01T10:00:00Z'
        }
      ]
      
      mockTimesheetStore.loadTimeOffRequests.mockResolvedValue(mockRequests)
      
      const wrapper = mountComponent(TimesheetRequests)
      await wrapper.vm.$nextTick()
      
      expect(mockTimesheetStore.loadTimeOffRequests).toHaveBeenCalled()
      expect(mockTimesheetStore.loadTimeOffQuotas).toHaveBeenCalled()
    })

    it('should create vacation request', async () => {
      const wrapper = mountComponent(TimesheetRequests)
      
      // Click vacation button
      await wrapper.find('[data-testid="vacation-button"]').trigger('click')
      
      // Should show modal
      expect(wrapper.find('[data-testid="request-modal"]').exists()).toBe(true)
      
      // Fill form
      await wrapper.find('[data-testid="start-date"]').setValue('2025-07-01')
      await wrapper.find('[data-testid="end-date"]').setValue('2025-07-05')
      await wrapper.find('[data-testid="notes"]').setValue('Summer vacation')
      
      // Submit form
      await wrapper.find('[data-testid="submit-request"]').trigger('click')
      
      expect(mockTimesheetStore.createTimeOffRequest).toHaveBeenCalledWith({
        type: 'vacation',  // Campo DB: 'type'
        start_date: '2025-07-01',
        end_date: '2025-07-05',
        notes: 'Summer vacation'
      })
    })

    it('should handle request creation error', async () => {
      mockTimesheetStore.createTimeOffRequest.mockResolvedValue(false)
      mockTimesheetStore.error = 'Errore nella creazione della richiesta'
      
      const wrapper = mountComponent(TimesheetRequests)
      
      // Click vacation button and submit
      await wrapper.find('[data-testid="vacation-button"]').trigger('click')
      await wrapper.find('[data-testid="start-date"]').setValue('2025-07-01')
      await wrapper.find('[data-testid="end-date"]').setValue('2025-07-05')
      await wrapper.find('[data-testid="submit-request"]').trigger('click')
      
      await wrapper.vm.$nextTick()
      
      // Should show error
      expect(wrapper.text()).toContain('Errore nella creazione della richiesta')
    })

    it('should delete pending request', async () => {
      const mockRequests = [
        {
          id: 1,
          request_type: 'vacation',
          start_date: '2025-07-01',
          end_date: '2025-07-05',
          status: 'pending',
          duration_days: 5
        }
      ]
      
      mockTimesheetStore.loadTimeOffRequests.mockResolvedValue(mockRequests)
      
      const wrapper = mountComponent(TimesheetRequests)
      await wrapper.vm.$nextTick()
      
      // Mock confirm dialog
      window.confirm = vi.fn().mockReturnValue(true)
      
      // Click delete button
      await wrapper.find('[data-testid="delete-request-1"]').trigger('click')
      
      expect(mockTimesheetStore.deleteTimeOffRequest).toHaveBeenCalledWith(1)
    })
  })

  describe('Timesheet Store Integration', () => {
    it('should call correct API endpoint for creating request', async () => {
      fetch.mockResolvedValueOnce(mockApiResponse({
        success: true,
        data: { id: 1, request_type: 'vacation' }
      }))

      const store = useTimesheetStore()
      
      const result = await store.createTimeOffRequest({
        type: 'vacation',  // Campo DB: 'type'
        start_date: '2025-07-01',
        end_date: '2025-07-05',
        notes: 'Test'
      })

      expect(fetch).toHaveBeenCalledWith('/api/time-off-requests/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': 'test-csrf-token'
        },
        body: JSON.stringify({
          type: 'vacation',  // Campo DB: 'type'
          start_date: '2025-07-01',
          end_date: '2025-07-05',
          notes: 'Test'
        })
      })
      
      expect(result).toBe(true)
    })

    it('should handle API error correctly', async () => {
      fetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        json: () => Promise.resolve({
          success: false,
          message: 'Campo request_type richiesto'
        })
      })

      const store = useTimesheetStore()
      
      const result = await store.createTimeOffRequest({
        start_date: '2025-07-01',
        end_date: '2025-07-05'
        // Missing request_type
      })

      expect(result).toBe(false)
      expect(store.error).toContain('Campo request_type richiesto')
    })
  })
})
