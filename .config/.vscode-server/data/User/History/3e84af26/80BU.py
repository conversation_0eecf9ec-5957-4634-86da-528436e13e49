"""Unit tests for Poll model."""
import pytest
from datetime import datetime
from models import Poll, User
from extensions import db

class TestPollModel:
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        self.app = app
        self.user = test_user

    def test_poll_creation_basic(self):
        poll = Poll(
            title='Test Poll',
            description='This is a test poll',
            author_id=self.user.id  # Campo corretto è 'author_id'
        )
        db.session.add(poll)
        db.session.commit()

        assert poll.id is not None
        assert poll.title == 'Test Poll'
        assert poll.description == 'This is a test poll'
        assert poll.author_id == self.user.id

    def test_poll_multiple_choice(self):
        poll = Poll(
            title='Multiple Choice Poll',
            description='Choose multiple options',
            author_id=self.user.id,  # <PERSON> corretto è 'author_id'
            multiple_choice=True
        )
        db.session.add(poll)
        db.session.commit()
        
        assert poll.multiple_choice is True

    def test_poll_deletion(self):
        poll = Poll(
            title='To Delete',
            description='This will be deleted',
            created_by=self.user.id
        )
        db.session.add(poll)
        db.session.commit()
        poll_id = poll.id
        
        db.session.delete(poll)
        db.session.commit()
        
        deleted = Poll.query.get(poll_id)
        assert deleted is None
