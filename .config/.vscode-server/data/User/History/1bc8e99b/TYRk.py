"""Unit tests for Regulation model."""
import pytest
from models import Regulation, User
from extensions import db

class TestRegulationModel:
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        self.app = app
        self.user = test_user

    def test_regulation_creation_basic(self):
        regulation = Regulation(
            title='Test Regulation',
            content='This is test regulation content'
            # Rimosso category e created_by - non esistono nel modello
        )
        db.session.add(regulation)
        db.session.commit()
        
        assert regulation.id is not None
        assert regulation.title == 'Test Regulation'
        assert regulation.content == 'This is test regulation content'

    def test_regulation_deletion(self):
        regulation = Regulation(title='To Delete', content='Delete me')  # Rimosso created_by
        db.session.add(regulation)
        db.session.commit()
        reg_id = regulation.id
        
        db.session.delete(regulation)
        db.session.commit()
        
        deleted = Regulation.query.get(reg_id)
        assert deleted is None
