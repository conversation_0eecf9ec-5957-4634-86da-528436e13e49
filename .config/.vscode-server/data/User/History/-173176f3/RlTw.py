"""Unit tests for PerformanceReview model."""
import pytest
from models import PerformanceReview, User
from extensions import db

class TestPerformanceReviewModel:
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        self.app = app
        self.user = test_user

    def test_performancereview_creation_basic(self):
        from datetime import date
        review = PerformanceReview(
            employee_id=self.user.id,
            reviewer_id=self.user.id,
            review_period_start=date(2024, 1, 1),  # Campo richiesto
            review_period_end=date(2024, 3, 31),   # Campo richiesto
            review_year=2024,  # Campo richiesto
            status='draft'
        )
        db.session.add(review)
        db.session.commit()
        
        assert review.id is not None
        assert review.employee_id == self.user.id
        assert review.review_year == 2024

    def test_performancereview_deletion(self):
        review = PerformanceReview(
            employee_id=self.user.id,
            reviewer_id=self.user.id,
            review_period_start=date(2024, 1, 1),
            review_period_end=date(2024, 3, 31),
            review_year=2024
        )
        db.session.add(review)
        db.session.commit()
        review_id = review.id
        
        db.session.delete(review)
        db.session.commit()
        
        deleted = PerformanceReview.query.get(review_id)
        assert deleted is None
