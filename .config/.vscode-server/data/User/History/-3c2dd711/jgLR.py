"""
Unit tests for UserSkill model.
Tests business logic, validations, and model functionality.
"""

import pytest
from datetime import datetime, date
from models import User<PERSON>kill, User, Skill
from extensions import db


class TestUserSkillModel:
    """Test suite for UserSkill model unit tests"""

    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        """Setup test data for each test method"""
        self.app = app
        self.user = test_user

    def test_userskill_creation_basic(self):
        """Test basic user skill creation"""
        # Create test skill
        skill = Skill(
            name='Python Programming',
            category='Programming',
            description='Python development skills'
        )
        db.session.add(skill)
        db.session.commit()
        
        user_skill = UserSkill(
            user_id=self.user.id,
            skill_id=skill.id,
            proficiency_level=3,
            years_experience=2.5
        )
        
        db.session.add(user_skill)
        db.session.commit()
        
        assert user_skill.id is not None
        assert user_skill.user_id == self.user.id
        assert user_skill.skill_id == skill.id
        assert user_skill.proficiency_level == 3
        assert user_skill.years_experience == 2.5

    def test_userskill_creation_complete(self):
        """Test user skill creation with all fields"""
        skill = Skill(
            name='JavaScript Development',
            category='Programming',
            description='JavaScript skills'
        )
        db.session.add(skill)
        db.session.commit()
        
        user_skill = UserSkill(
            user_id=self.user.id,
            skill_id=skill.id,
            proficiency_level=4,
            years_experience=3.0,
            last_used=date(2024, 12, 1),
            certified=True,
            certification_date=date(2024, 6, 15),
            certification_name='JavaScript Professional Certificate'
        )
        
        db.session.add(user_skill)
        db.session.commit()
        
        assert user_skill.proficiency_level == 4
        assert user_skill.years_experience == 3.0
        assert user_skill.last_used == date(2024, 12, 1)
        assert user_skill.certified is True
        assert user_skill.certification_date == date(2024, 6, 15)
        assert user_skill.certification_name == 'JavaScript Professional Certificate'

    def test_userskill_relationships(self):
        """Test relationships with User and Skill models"""
        skill = Skill(
            name='Database Design',
            category='Database',
            description='Database design skills'
        )
        db.session.add(skill)
        db.session.commit()
        
        user_skill = UserSkill(
            user_id=self.user.id,
            skill_id=skill.id,
            proficiency_level=2
        )
        
        db.session.add(user_skill)
        db.session.commit()
        
        # Test forward relationships
        assert user_skill.user is not None
        assert user_skill.user.id == self.user.id
        assert user_skill.skill is not None
        assert user_skill.skill.id == skill.id

    def test_userskill_proficiency_levels(self):
        """Test different proficiency levels"""
        skill = Skill(
            name='Project Management',
            category='Management',
            description='Project management skills'
        )
        db.session.add(skill)
        db.session.commit()
        
        proficiency_levels = [1, 2, 3, 4, 5]  # Beginner to Expert

        # Create different skills for each proficiency level
        skills = []
        for i, level in enumerate(proficiency_levels):
            skill_unique = Skill(
                name=f'Project Management Level {level}',
                category='Management',
                description=f'Project management skills level {level}'
            )
            skills.append(skill_unique)

        db.session.add_all(skills)
        db.session.commit()

        user_skills = []
        for level, skill_unique in zip(proficiency_levels, skills):
            user_skill = UserSkill(
                user_id=self.user.id,
                skill_id=skill_unique.id,
                proficiency_level=level,
                years_experience=level * 0.5  # Different experience for each
            )
            user_skills.append(user_skill)
        
        db.session.add_all(user_skills)
        db.session.commit()
        
        for user_skill, expected_level in zip(user_skills, proficiency_levels):
            assert user_skill.proficiency_level == expected_level
            assert 1 <= user_skill.proficiency_level <= 5

    def test_userskill_years_experience(self):
        """Test years of experience field"""
        experience_values = [0.5, 1.0, 2.5, 5.0, 10.0]

        # Create different skills for each experience level
        skills = []
        for i, experience in enumerate(experience_values):
            skill_unique = Skill(
                name=f'Machine Learning Experience {i}',
                category='AI',
                description=f'ML skills {i}'
            )
            skills.append(skill_unique)

        db.session.add_all(skills)
        db.session.commit()

        user_skills = []
        for experience, skill_unique in zip(experience_values, skills):
            user_skill = UserSkill(
                user_id=self.user.id,
                skill_id=skill_unique.id,
                proficiency_level=2,
                years_experience=experience
            )
            user_skills.append(user_skill)
        
        db.session.add_all(user_skills)
        db.session.commit()
        
        for user_skill, expected_experience in zip(user_skills, experience_values):
            assert user_skill.years_experience == expected_experience
            assert user_skill.years_experience >= 0

    def test_userskill_certification_functionality(self):
        """Test certification-related fields"""
        # Create two different skills for certified and non-certified
        skill1 = Skill(
            name='AWS Cloud Certified',
            category='Cloud',
            description='AWS cloud skills certified'
        )
        skill2 = Skill(
            name='AWS Cloud Non-Certified',
            category='Cloud',
            description='AWS cloud skills non-certified'
        )
        db.session.add_all([skill1, skill2])
        db.session.commit()

        # Certified skill
        certified_skill = UserSkill(
            user_id=self.user.id,
            skill_id=skill1.id,
            proficiency_level=4,
            certified=True,
            certification_date=date(2024, 3, 15),
            certification_name='AWS Solutions Architect'
        )

        # Non-certified skill
        non_certified_skill = UserSkill(
            user_id=self.user.id,
            skill_id=skill2.id,
            proficiency_level=3,
            certified=False
        )
        
        db.session.add_all([certified_skill, non_certified_skill])
        db.session.commit()
        
        assert certified_skill.certified is True
        assert certified_skill.certification_date == date(2024, 3, 15)
        assert certified_skill.certification_name == 'AWS Solutions Architect'
        
        assert non_certified_skill.certified is False
        assert non_certified_skill.certification_date is None
        assert non_certified_skill.certification_name is None

    def test_userskill_last_used_tracking(self):
        """Test last used date tracking"""
        skill = Skill(
            name='React Development',
            category='Frontend',
            description='React skills'
        )
        db.session.add(skill)
        db.session.commit()
        
        user_skill = UserSkill(
            user_id=self.user.id,
            skill_id=skill.id,
            proficiency_level=3,
            last_used=date(2024, 11, 30)
        )
        
        db.session.add(user_skill)
        db.session.commit()
        
        assert user_skill.last_used == date(2024, 11, 30)
        
        # Update last used date
        user_skill.last_used = date(2024, 12, 15)
        db.session.commit()
        
        assert user_skill.last_used == date(2024, 12, 15)

    def test_userskill_query_by_user(self):
        """Test querying skills by user"""
        skill = Skill(
            name='Docker Containers',
            category='DevOps',
            description='Docker skills'
        )
        db.session.add(skill)
        db.session.commit()
        
        user_skill = UserSkill(
            user_id=self.user.id,
            skill_id=skill.id,
            proficiency_level=3
        )
        
        db.session.add(user_skill)
        db.session.commit()
        
        # Query skills by user
        user_skills = UserSkill.query.filter_by(user_id=self.user.id).all()
        assert len(user_skills) >= 1
        assert user_skill in user_skills

    def test_userskill_query_by_skill(self):
        """Test querying users by skill"""
        skill = Skill(
            name='Kubernetes',
            category='DevOps',
            description='Kubernetes skills'
        )
        db.session.add(skill)
        db.session.commit()
        
        user_skill = UserSkill(
            user_id=self.user.id,
            skill_id=skill.id,
            proficiency_level=2
        )
        
        db.session.add(user_skill)
        db.session.commit()
        
        # Query users by skill
        skill_users = UserSkill.query.filter_by(skill_id=skill.id).all()
        assert len(skill_users) >= 1
        assert user_skill in skill_users

    def test_userskill_query_by_proficiency(self):
        """Test querying by proficiency level"""
        skill = Skill(
            name='Data Analysis',
            category='Analytics',
            description='Data analysis skills'
        )
        db.session.add(skill)
        db.session.commit()
        
        user_skill = UserSkill(
            user_id=self.user.id,
            skill_id=skill.id,
            proficiency_level=4
        )
        
        db.session.add(user_skill)
        db.session.commit()
        
        # Query by proficiency level
        expert_skills = UserSkill.query.filter(UserSkill.proficiency_level >= 4).all()
        assert len(expert_skills) >= 1
        assert user_skill in expert_skills

    def test_userskill_update_operations(self):
        """Test skill update operations"""
        skill = Skill(
            name='Node.js Development',
            category='Backend',
            description='Node.js skills'
        )
        db.session.add(skill)
        db.session.commit()
        
        user_skill = UserSkill(
            user_id=self.user.id,
            skill_id=skill.id,
            proficiency_level=2,
            years_experience=1.0,
            certified=False
        )
        
        db.session.add(user_skill)
        db.session.commit()
        
        # Update skill
        user_skill.proficiency_level = 4
        user_skill.years_experience = 3.0
        user_skill.certified = True
        user_skill.certification_date = date(2024, 12, 1)
        user_skill.certification_name = 'Node.js Professional'
        
        db.session.commit()
        
        updated_skill = UserSkill.query.get(user_skill.id)
        assert updated_skill.proficiency_level == 4
        assert updated_skill.years_experience == 3.0
        assert updated_skill.certified is True
        assert updated_skill.certification_date == date(2024, 12, 1)

    def test_userskill_deletion(self):
        """Test skill deletion"""
        skill = Skill(
            name='GraphQL',
            category='API',
            description='GraphQL skills'
        )
        db.session.add(skill)
        db.session.commit()
        
        user_skill = UserSkill(
            user_id=self.user.id,
            skill_id=skill.id,
            proficiency_level=3
        )
        
        db.session.add(user_skill)
        db.session.commit()
        skill_id = user_skill.id
        
        db.session.delete(user_skill)
        db.session.commit()
        
        deleted_skill = UserSkill.query.get(skill_id)
        assert deleted_skill is None

    def test_userskill_certification_name_length(self):
        """Test certification name field length"""
        skill = Skill(
            name='Cybersecurity',
            category='Security',
            description='Security skills'
        )
        db.session.add(skill)
        db.session.commit()
        
        long_cert_name = 'Very Long Certification Name That Tests The Maximum Field Length Constraint'
        
        user_skill = UserSkill(
            user_id=self.user.id,
            skill_id=skill.id,
            proficiency_level=3,
            certified=True,
            certification_name=long_cert_name
        )
        
        db.session.add(user_skill)
        db.session.commit()
        
        assert user_skill.certification_name == long_cert_name
        assert len(user_skill.certification_name) <= 255  # VARCHAR(255) constraint
