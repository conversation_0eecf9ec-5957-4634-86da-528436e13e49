# Project management models
from .base import db, datetime, date

class Project(db.Model):
    __tablename__ = 'projects'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    name = db.Column(db.String(128), nullable=False)
    description = db.Column(db.Text)
    client_id = db.Column(db.In<PERSON>ger, db.ForeignKey('clients.id'))
    contract_id = db.Column(db.Integer, db.ForeignKey('contracts.id'), nullable=True)  # Nullable per progetti interni
    start_date = db.Column(db.Date)
    end_date = db.Column(db.Date)
    status = db.Column(db.String(20), default='planning')  # planning, active, completed, on-hold
    budget = db.Column(db.Float)
    expenses = db.Column(db.Float, default=0.0)
    project_type = db.Column(db.String(50), default='service')  # service, license, consulting, product, rd, internal
    is_billable = db.Column(db.<PERSON>, default=True)  # Progetto fatturabile?
    client_daily_rate = db.Column(db.Float)  # Tariffa giornaliera al cliente
    markup_percentage = db.Column(db.Float, default=0.0)  # Markup sui costi
    
    # Collegamento con sistema bandi
    funding_source = db.Column(db.String(100))  # "public_funding", "private", "internal", "mixed"
    funding_application_id = db.Column(db.Integer, db.ForeignKey('funding_applications.id'))
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    tasks = db.relationship('Task', backref='project', lazy='dynamic', cascade='all, delete-orphan')
    timesheet_entries = db.relationship('TimesheetEntry', backref='project', lazy='dynamic')
    client = db.relationship('Client', backref='projects')
    contract = db.relationship('Contract', backref='projects')
    # funding_application = db.relationship('FundingApplication', foreign_keys='FundingApplication.linked_project_id', backref='linked_projects')

    def __repr__(self):
        return f'<Project {self.name}>'

    @property
    def remaining_budget(self):
        return self.budget - self.expenses


class Task(db.Model):
    __tablename__ = 'tasks'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(128), nullable=False)
    description = db.Column(db.Text)
    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'), nullable=False)
    assignee_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    status = db.Column(db.String(20), default='todo')  # todo, in-progress, review, done
    priority = db.Column(db.String(20), default='medium')  # low, medium, high, urgent
    start_date = db.Column(db.Date)  # Data di inizio pianificata
    due_date = db.Column(db.Date)    # Data di fine pianificata
    estimated_hours = db.Column(db.Float)  # Ore stimate per completare il task
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    assignee = db.relationship('User', backref='assigned_tasks')

    def __repr__(self):
        return f'<Task {self.name}>'

    @property
    def actual_hours(self):
        """Calcola le ore effettive lavorate sul task dai timesheet"""
        return sum(entry.hours for entry in self.timesheet_entries)

    @property
    def hours_variance(self):
        """Calcola la varianza tra ore stimate e ore effettive"""
        if not self.estimated_hours or self.actual_hours == 0:
            return None
        return self.actual_hours - self.estimated_hours

    @property
    def hours_efficiency(self):
        """Calcola l'efficienza in percentuale (stimate/effettive * 100)"""
        if not self.estimated_hours or self.actual_hours == 0:
            return None
        return (self.estimated_hours / self.actual_hours) * 100

    @property
    def duration_days(self):
        """Calcola la durata pianificata in giorni"""
        if not self.start_date or not self.due_date:
            return None
        return (self.due_date - self.start_date).days + 1


class TaskDependency(db.Model):
    __tablename__ = 'task_dependencies'
    
    id = db.Column(db.Integer, primary_key=True)
    predecessor_id = db.Column(db.Integer, db.ForeignKey('tasks.id'), nullable=False)
    successor_id = db.Column(db.Integer, db.ForeignKey('tasks.id'), nullable=False)
    dependency_type = db.Column(db.String(20), default='finish_to_start')  # finish_to_start, start_to_start, etc.
    lag_days = db.Column(db.Integer, default=0)  # Giorni di ritardo/anticipo

    # Relationships
    predecessor = db.relationship('Task', foreign_keys=[predecessor_id], backref='successor_dependencies')
    successor = db.relationship('Task', foreign_keys=[successor_id], backref='predecessor_dependencies')

    def __repr__(self):
        return f'<TaskDependency {self.predecessor_id} -> {self.successor_id}>'


class ProjectResource(db.Model):
    __tablename__ = 'project_resources'

    """Allocazione risorse per progetto"""
    id = db.Column(db.Integer, primary_key=True)
    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    allocation_percentage = db.Column(db.Integer, default=100)  # Campo DB reale
    role = db.Column(db.String(50))  # Campo DB reale

    # Relationships
    project = db.relationship('Project', backref='resources')
    user = db.relationship('User', backref='project_allocations')

    def __repr__(self):
        return f'<ProjectResource {self.project_id} - {self.user_id}>'


class ProjectKPI(db.Model):
    __tablename__ = 'project_kpis'

    """KPI specifici del progetto"""
    id = db.Column(db.Integer, primary_key=True)
    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'), nullable=False)
    kpi_id = db.Column(db.Integer, nullable=False)  # Campo DB reale
    target_value = db.Column(db.Float)
    current_value = db.Column(db.Float, default=0)

    # Relationships
    project = db.relationship('Project', backref='kpis')

    def __repr__(self):
        return f'<ProjectKPI {self.name}>'

    @property
    def performance_percentage(self):
        if not self.target_value:
            return None
        return (self.current_value / self.target_value) * 100


class ProjectExpense(db.Model):
    __tablename__ = 'project_expenses'
    
    """Spese del progetto"""
    id = db.Column(db.Integer, primary_key=True)
    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'), nullable=False)
    category = db.Column(db.String(50), nullable=False)  # travel, materials, services, etc.
    description = db.Column(db.Text)
    amount = db.Column(db.Float, nullable=False)
    currency = db.Column(db.String(3), default='EUR')
    expense_date = db.Column(db.Date, nullable=False)
    receipt_path = db.Column(db.String(255))
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'))  # Chi ha effettuato la spesa
    approved_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    status = db.Column(db.String(20), default='pending')  # pending, approved, rejected
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    project = db.relationship('Project', backref='project_expenses')
    user = db.relationship('User', foreign_keys=[user_id], backref='submitted_expenses')
    approver = db.relationship('User', foreign_keys=[approved_by], backref='approved_expenses')

    def __repr__(self):
        return f'<ProjectExpense {self.project_id}: {self.amount} {self.currency}>'


class ProjectKPITemplate(db.Model):
    __tablename__ = 'project_kpi_templates'
    
    """Template per KPI di progetto"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(128), nullable=False)
    description = db.Column(db.Text)
    unit = db.Column(db.String(50))  # hours, percentage, count, EUR, etc.
    category = db.Column(db.String(50))  # budget, timeline, quality, productivity
    calculation_method = db.Column(db.String(50))  # manual, calculated, imported
    is_active = db.Column(db.Boolean, default=True)
    
    # Campi per KPI calcolati automaticamente
    calculation_formula = db.Column(db.String(255))  # Formula o riferimento al metodo di calcolo
    data_source = db.Column(db.String(100))  # timesheet, expenses, tasks, etc.
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<ProjectKPITemplate {self.name}>'

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'unit': self.unit,
            'category': self.category,
            'calculation_method': self.calculation_method,
            'is_active': self.is_active,
            'calculation_formula': self.calculation_formula,
            'data_source': self.data_source
        }


class ProjectKPITarget(db.Model):
    __tablename__ = 'project_kpi_targets'
    
    """Target KPI per progetto (istanza di un template)"""
    id = db.Column(db.Integer, primary_key=True)
    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'), nullable=False)
    kpi_template_id = db.Column(db.Integer, db.ForeignKey('project_kpi_templates.id'), nullable=False)
    
    # Valori target e attuali
    target_value = db.Column(db.Float)
    current_value = db.Column(db.Float, default=0)
    
    # Soglie per alerts
    warning_threshold = db.Column(db.Float)  # Percentuale per warning (es. 80%)
    critical_threshold = db.Column(db.Float)  # Percentuale per critical (es. 90%)
    
    # Tracking
    last_calculated_at = db.Column(db.DateTime)
    status = db.Column(db.String(20), default='on_track')  # on_track, warning, critical, completed
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    project = db.relationship('Project', backref='kpi_targets')
    template = db.relationship('ProjectKPITemplate', backref='project_instances')

    def __repr__(self):
        return f'<ProjectKPITarget Project:{self.project_id} KPI:{self.kpi_template_id}>'

    @property
    def achievement_percentage(self):
        if not self.target_value or self.target_value == 0:
            return 0
        return round((self.current_value / self.target_value) * 100, 2)

    def update_status(self):
        """Aggiorna lo stato basato sui threshold"""
        percentage = self.achievement_percentage
        
        if percentage >= 100:
            self.status = 'completed'
        elif self.critical_threshold and percentage >= self.critical_threshold:
            self.status = 'critical'
        elif self.warning_threshold and percentage >= self.warning_threshold:
            self.status = 'warning'
        else:
            self.status = 'on_track'
        
        return self.status


class ProjectFundingLink(db.Model):
    """Collegamento tra progetti e finanziamenti"""
    __tablename__ = 'project_funding_links'
    
    id = db.Column(db.Integer, primary_key=True)
    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'), nullable=False)
    funding_application_id = db.Column(db.Integer, db.ForeignKey('funding_applications.id'), nullable=False)
    allocation_percentage = db.Column(db.Float, default=100.0)  # Percentuale del progetto coperta dal finanziamento
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    project = db.relationship('Project', backref='funding_links')
    funding_application = db.relationship('FundingApplication', backref='project_links')
    
    # Unique constraint
    __table_args__ = (
        db.UniqueConstraint('project_id', 'funding_application_id', name='unique_project_funding'),
    )
    
    def __repr__(self):
        return f'<ProjectFundingLink Project:{self.project_id} Funding:{self.funding_application_id}>'