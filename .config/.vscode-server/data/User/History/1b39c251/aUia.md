# 🌐 End-to-End Testing Guide

Guida completa per il testing end-to-end con Cypress per validare workflow utente completi.

## 📋 Indice

- [Setup e Configurazione](#setup-e-configurazione)
- [Workflow Testing](#workflow-testing)
- [User Journey Testing](#user-journey-testing)
- [Cross-Browser Testing](#cross-browser-testing)
- [Data Management](#data-management)
- [Custom Commands](#custom-commands)
- [Best Practices](#best-practices)

## ⚙️ Setup e Configurazione

### Struttura Directory

```
frontend/cypress/
├── e2e/
│   ├── auth/
│   │   ├── login.cy.js              # Test autenticazione
│   │   └── user-management.cy.js    # Test gestione utenti
│   ├── projects/
│   │   ├── project-creation.cy.js   # Test creazione progetti
│   │   ├── project-workflows.cy.js  # Test workflow progetti
│   │   └── team-management.cy.js    # Test gestione team
│   ├── timesheet/
│   │   ├── timesheet-entry.cy.js    # Test inserimento ore
│   │   └── timesheet-approval.cy.js # Test approvazione
│   └── dashboard/
│       └── dashboard-overview.cy.js # Test dashboard
├── fixtures/
│   ├── projects.json               # Dati progetti di test
│   ├── users.json                 # Dati utenti di test
│   └── timesheet.json             # Dati timesheet di test
├── support/
│   ├── commands.js                # Custom commands
│   ├── e2e.js                    # Setup globale
│   └── utils.js                  # Utility functions
└── cypress.config.js             # Configurazione Cypress
```

### Configurazione Cypress

```javascript
// cypress.config.js
import { defineConfig } from 'cypress'

export default defineConfig({
  e2e: {
    baseUrl: 'http://localhost:3000',
    viewportWidth: 1280,
    viewportHeight: 720,
    video: true,
    screenshotOnRunFailure: true,

    // Test files
    specPattern: 'cypress/e2e/**/*.cy.{js,jsx,ts,tsx}',

    // Support files
    supportFile: 'cypress/support/e2e.js',

    // Timeouts
    defaultCommandTimeout: 10000,
    requestTimeout: 10000,
    responseTimeout: 10000,

    // Retry configuration
    retries: {
      runMode: 2,
      openMode: 0
    },

    // Environment variables
    env: {
      apiUrl: 'http://localhost:5000/api',
      adminUsername: 'admin',
      adminPassword: 'password'
    },

    setupNodeEvents(on, config) {
      // Task plugins
      on('task', {
        // Database seeding
        seedDatabase() {
          // Seed test data
          return null
        },

        // Clear database
        clearDatabase() {
          // Clear test data
          return null
        }
      })

      return config
    }
  }
})
```

### Setup Globale

```javascript
// cypress/support/e2e.js
import './commands'
import './utils'

// Global configuration
Cypress.on('uncaught:exception', (err, runnable) => {
  // Prevent Cypress from failing on uncaught exceptions
  if (err.message.includes('ResizeObserver loop limit exceeded')) {
    return false
  }
  return true
})

// Before each test
beforeEach(() => {
  // Clear local storage
  cy.clearLocalStorage()

  // Clear cookies
  cy.clearCookies()

  // Set viewport
  cy.viewport(1280, 720)
})

// After each test
afterEach(() => {
  // Take screenshot on failure
  if (Cypress.currentTest.state === 'failed') {
    cy.screenshot(`failed-${Cypress.currentTest.title}`)
  }
})
```

## 🔄 Workflow Testing

### Test Creazione Progetto Completo

```javascript
// cypress/e2e/projects/project-creation.cy.js
describe('Project Creation Workflow', () => {
  beforeEach(() => {
    cy.loginAsAdmin()
    cy.intercept('GET', '/api/clients*', { fixture: 'clients.json' }).as('getClients')
    cy.intercept('POST', '/api/projects', { fixture: 'project-created.json' }).as('createProject')
  })

  it('should create complete project with all details', () => {
    // 1. Navigate to project creation
    cy.visit('/app/projects')
    cy.get('[data-testid="create-project-button"]').click()
    cy.url().should('include', '/projects/new')

    // 2. Fill basic project information
    cy.get('[data-testid="project-name"]').type('E2E Complete Project')
    cy.get('[data-testid="project-description"]').type('Full end-to-end test project with comprehensive features')

    // 3. Set financial details
    cy.get('[data-testid="project-budget"]').type('75000')
    cy.get('[data-testid="project-type"]').select('service')
    cy.get('[data-testid="project-billable"]').check()
    cy.get('[data-testid="client-daily-rate"]').type('500')

    // 4. Set project timeline
    cy.get('[data-testid="project-start-date"]').type('2025-01-01')
    cy.get('[data-testid="project-end-date"]').type('2025-06-30')

    // 5. Select client
    cy.wait('@getClients')
    cy.get('[data-testid="project-client"]').select('TechCorp Solutions')

    // 6. Set project status and type
    cy.get('[data-testid="project-status"]').select('planning')
    cy.get('[data-testid="is-billable"]').check()

    // 7. Save project
    cy.get('[data-testid="save-button"]').click()
    cy.wait('@createProject')

    // 8. Verify success and navigation
    cy.get('[data-testid="success-message"]')
      .should('be.visible')
      .and('contain', 'Project created successfully')

    cy.url().should('match', /\/projects\/\d+/)

    // 9. Verify project details page
    cy.get('[data-testid="project-title"]').should('contain', 'E2E Complete Project')
    cy.get('[data-testid="project-budget"]').should('contain', '€75,000')
    cy.get('[data-testid="project-status"]').should('contain', 'Planning')
    cy.get('[data-testid="client-name"]').should('contain', 'TechCorp Solutions')
  })

  it('should handle validation errors during creation', () => {
    cy.visit('/app/projects/new')

    // Try to save without required fields
    cy.get('[data-testid="save-button"]').click()

    // Verify validation errors
    cy.get('[data-testid="name-error"]')
      .should('be.visible')
      .and('contain', 'Project name is required')

    cy.get('[data-testid="budget-error"]')
      .should('be.visible')
      .and('contain', 'Budget is required')

    // Fill invalid data
    cy.get('[data-testid="project-name"]').type('A') // Too short
    cy.get('[data-testid="project-budget"]').type('-1000') // Negative
    cy.get('[data-testid="project-start-date"]').type('2025-12-31')
    cy.get('[data-testid="project-end-date"]').type('2025-01-01') // Before start

    cy.get('[data-testid="save-button"]').click()

    // Verify specific validation errors
    cy.get('[data-testid="name-error"]').should('contain', 'at least 3 characters')
    cy.get('[data-testid="budget-error"]').should('contain', 'must be positive')
    cy.get('[data-testid="end-date-error"]').should('contain', 'must be after start date')
  })

  it('should save draft and continue later', () => {
    cy.visit('/app/projects/new')

    // Fill partial information
    cy.get('[data-testid="project-name"]').type('Draft Project')
    cy.get('[data-testid="project-description"]').type('This is a draft project')

    // Save as draft
    cy.get('[data-testid="save-draft-button"]').click()

    // Verify draft saved
    cy.get('[data-testid="draft-saved-message"]')
      .should('be.visible')
      .and('contain', 'Draft saved')

    // Navigate away and back
    cy.visit('/app/dashboard')
    cy.visit('/app/projects/new')

    // Verify draft data restored
    cy.get('[data-testid="project-name"]').should('have.value', 'Draft Project')
    cy.get('[data-testid="project-description"]').should('contain', 'This is a draft project')
  })
})
```