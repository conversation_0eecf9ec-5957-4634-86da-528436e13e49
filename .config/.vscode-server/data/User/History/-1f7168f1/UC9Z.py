"""Unit tests for FundingOpportunity model."""
import pytest
from datetime import date
from models import FundingOpportunity
from extensions import db

class TestFundingOpportunityModel:
    @pytest.fixture(autouse=True)
    def setup_method(self, app):
        self.app = app

    def test_fundingopportunity_creation_basic(self):
        opportunity = FundingOpportunity(
            title='Test Funding',
            description='Test funding opportunity',
            max_grant_amount=100000.0,  # Campo corretto è 'max_grant_amount'
            application_deadline=date(2024, 12, 31),  # Campo corretto è 'application_deadline' (NOT NULL)
            created_by=1  # Campo richiesto
        )
        db.session.add(opportunity)
        db.session.commit()

        assert opportunity.id is not None
        assert opportunity.title == 'Test Funding'
        assert opportunity.max_grant_amount == 100000.0

    def test_fundingopportunity_deletion(self):
        opportunity = FundingOpportunity(title='To Delete', description='Delete me')
        db.session.add(opportunity)
        db.session.commit()
        opp_id = opportunity.id
        
        db.session.delete(opportunity)
        db.session.commit()
        
        deleted = FundingOpportunity.query.get(opp_id)
        assert deleted is None
