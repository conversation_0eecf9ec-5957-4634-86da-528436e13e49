"""Unit tests for PerformanceGoal model."""
import pytest
from models import PerformanceGoal, User
from extensions import db

class TestPerformanceGoalModel:
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        self.app = app
        self.user = test_user

    def test_performancegoal_creation_basic(self):
        goal = PerformanceGoal(
            employee_id=self.user.id,
            title='Improve Sales',
            description='Increase sales by 20%'
            # Rimosso target_value - non esiste nel modello
        )
        db.session.add(goal)
        db.session.commit()
        
        assert goal.id is not None
        assert goal.title == 'Improve Sales'
        assert goal.target_value == 20.0

    def test_performancegoal_deletion(self):
        goal = PerformanceGoal(employee_id=self.user.id, title='To Delete')
        db.session.add(goal)
        db.session.commit()
        goal_id = goal.id
        
        db.session.delete(goal)
        db.session.commit()
        
        deleted = PerformanceGoal.query.get(goal_id)
        assert deleted is None
