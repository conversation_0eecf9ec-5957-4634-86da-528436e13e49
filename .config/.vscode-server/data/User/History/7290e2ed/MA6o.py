import pytest
import json
from datetime import datetime, timedelta
from flask import url_for
from models import User, db
from backend.models import (
    ForumTopic, ForumComment, Poll, PollOption, PollVote,
    DirectMessage, CommunicationReaction
)
from utils.permissions import (
    PERMISSION_VIEW_COMMUNICATION, PERMISSION_CREATE_POLLS
)


class TestCommunicationAPI:
    """Test suite per le API del modulo comunicazione"""
    
    def setup_method(self):
        """Setup per ogni test"""
        # Crea utenti di test
        self.admin_user = User(
            username='admin_test',
            email='<EMAIL>',
            first_name='Admin',
            last_name='Test',
            role='admin',
            is_active=True
        )
        self.admin_user.set_password('password123')
        
        self.employee_user = User(
            username='employee_test',
            email='<EMAIL>',
            first_name='Employee',
            last_name='Test',
            role='employee',
            is_active=True
        )
        self.employee_user.set_password('password123')
        
        db.session.add(self.admin_user)
        db.session.add(self.employee_user)
        db.session.commit()
    
    def teardown_method(self):
        """Cleanup dopo ogni test"""
        # Rimuovi tutti i dati di test
        ForumComment.query.delete()
        ForumTopic.query.delete()
        PollVote.query.delete()
        PollOption.query.delete()
        Poll.query.delete()
        DirectMessage.query.delete()
        CommunicationReaction.query.delete()
        User.query.filter(User.username.in_(['admin_test', 'employee_test'])).delete()
        db.session.commit()
    
    def login_user(self, client, username, password):
        """Helper per il login"""
        return client.post('/api/auth/login', json={
            'username': username,
            'password': password
        })
    
    def test_forum_topic_creation(self, client):
        """Test creazione topic del forum"""
        # Login come admin
        self.login_user(client, 'admin_test', 'password123')
        
        # Crea un topic
        response = client.post('/api/communication/forum/topics', json={
            'title': 'Test Topic',
            'content': 'Questo è un topic di test',
            'category': 'generale'
        })
        
        assert response.status_code == 201
        data = json.loads(response.data)
        assert data['success'] is True
        assert data['data']['title'] == 'Test Topic'
        assert data['data']['category'] == 'generale'
    
    def test_forum_topic_list(self, client):
        """Test recupero lista topic del forum"""
        # Login come employee
        self.login_user(client, 'employee_test', 'password123')
        
        # Crea un topic di test
        topic = ForumTopic(
            title='Test Topic',
            description='Contenuto di test',  # Campo corretto: description
            author_id=self.admin_user.id,
            category='test'
        )
        db.session.add(topic)
        db.session.commit()
        
        # Recupera i topic
        response = client.get('/api/communication/forum/topics')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] is True
        assert len(data['data']['topics']) >= 1
        assert data['data']['topics'][0]['title'] == 'Test Topic'
    
    def test_poll_creation(self, client):
        """Test creazione sondaggio"""
        # Login come admin (ha PERMISSION_CREATE_POLLS)
        self.login_user(client, 'admin_test', 'password123')
        
        # Crea un sondaggio
        response = client.post('/api/communication/polls', json={
            'title': 'Sondaggio Test',
            'description': 'Descrizione del sondaggio',
            'options': ['Opzione 1', 'Opzione 2', 'Opzione 3'],
            'multiple_choice': False,
            'end_date': (datetime.utcnow() + timedelta(days=7)).isoformat()
        })
        
        assert response.status_code == 201
        data = json.loads(response.data)
        assert data['success'] is True
        assert data['data']['title'] == 'Sondaggio Test'
        assert len(data['data']['options']) == 3
    
    def test_poll_voting(self, client):
        """Test voto in un sondaggio"""
        # Login come employee
        self.login_user(client, 'employee_test', 'password123')
        
        # Crea un sondaggio di test
        poll = Poll(
            title='Test Poll',
            description='Poll di test',
            creator_id=self.admin_user.id,
            multiple_choice=False,
            is_active=True,
            end_date=datetime.utcnow() + timedelta(days=7)
        )
        db.session.add(poll)
        db.session.flush()
        
        # Aggiungi opzioni
        option1 = PollOption(poll_id=poll.id, text='Opzione 1', order_index=0)
        option2 = PollOption(poll_id=poll.id, text='Opzione 2', order_index=1)
        db.session.add(option1)
        db.session.add(option2)
        db.session.commit()
        
        # Vota
        response = client.post(f'/api/communication/polls/{poll.id}/vote', json={
            'option_ids': [option1.id]
        })
        
        assert response.status_code == 201
        data = json.loads(response.data)
        assert data['success'] is True
        assert data['message'] == 'Voto registrato con successo'
    
    def test_direct_message_sending(self, client):
        """Test invio messaggio diretto"""
        # Login come admin
        self.login_user(client, 'admin_test', 'password123')
        
        # Invia messaggio
        response = client.post('/api/communication/messages', json={
            'recipient_id': self.employee_user.id,
            'subject': 'Test Message',
            'content': 'Questo è un messaggio di test'
        })
        
        assert response.status_code == 201
        data = json.loads(response.data)
        assert data['success'] is True
        assert data['data']['subject'] == 'Test Message'
        assert data['data']['recipient_id'] == self.employee_user.id
    
    def test_message_list(self, client):
        """Test recupero lista messaggi"""
        # Login come employee
        self.login_user(client, 'employee_test', 'password123')
        
        # Crea un messaggio di test
        message = DirectMessage(
            sender_id=self.admin_user.id,
            recipient_id=self.employee_user.id,
            subject='Test Message',
            content='Contenuto del messaggio',
            is_read=False
        )
        db.session.add(message)
        db.session.commit()
        
        # Recupera i messaggi
        response = client.get('/api/communication/messages')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] is True
        assert len(data['data']['messages']) >= 1
        assert data['data']['messages'][0]['subject'] == 'Test Message'
    
    def test_communication_stats(self, client):
        """Test recupero statistiche comunicazione"""
        # Login come employee
        self.login_user(client, 'employee_test', 'password123')
        
        # Recupera le statistiche
        response = client.get('/api/communication/stats')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] is True
        assert 'forum' in data['data']
        assert 'polls' in data['data']
        assert 'messages' in data['data']
        assert 'events' in data['data']
    
    def test_unauthorized_access(self, client):
        """Test accesso non autorizzato"""
        # Prova ad accedere senza login
        response = client.get('/api/communication/forum/topics')
        assert response.status_code == 401
        
        response = client.post('/api/communication/polls', json={
            'title': 'Test Poll',
            'options': ['Option 1']
        })
        assert response.status_code == 401