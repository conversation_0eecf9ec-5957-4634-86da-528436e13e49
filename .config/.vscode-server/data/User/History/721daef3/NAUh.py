"""
Unit tests for Department model.
Tests business logic, validations, and model functionality.
"""

import pytest
from datetime import datetime
from models import Department, User
from extensions import db


class TestDepartmentModel:
    """Test suite for Department model unit tests"""

    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        """Setup test data for each test method"""
        self.app = app
        self.user = test_user

    def test_department_creation_basic(self):
        """Test basic department creation with required fields"""
        department = Department(
            name='Engineering'
        )

        db.session.add(department)
        db.session.commit()

        assert department.id is not None
        assert department.name == 'Engineering'

    def test_department_creation_complete(self):
        """Test department creation with all fields"""
        department = Department(
            name='Human Resources',
            description='Manages employee relations and company policies',
            manager_id=self.user.id,
            budget=150000.0,
            is_active=True
        )

        db.session.add(department)
        db.session.commit()

        assert department.description == 'Manages employee relations and company policies'
        assert department.manager_id == self.user.id
        assert department.budget == 150000.0
        assert department.is_active is True
        assert department.created_at is not None

    def test_department_repr_method(self):
        """Test string representation of department"""
        department = Department(name='Marketing', code='MKT')
        
        expected_repr = '<Department Marketing (MKT)>'
        assert repr(department) == expected_repr

    def test_department_manager_relationship(self):
        """Test relationship with User model as manager"""
        department = Department(
            name='Sales',
            code='SALES',
            manager_id=self.user.id
        )
        
        db.session.add(department)
        db.session.commit()
        
        # Test forward relationship
        assert department.manager is not None
        assert department.manager.id == self.user.id
        
        # Test backward relationship
        assert department in self.user.managed_departments

    def test_department_code_uniqueness(self):
        """Test that department codes should be unique"""
        dept1 = Department(name='Department 1', code='UNIQUE')
        dept2 = Department(name='Department 2', code='UNIQUE')  # Duplicate code
        
        db.session.add(dept1)
        db.session.commit()
        
        # This should potentially raise an error if uniqueness is enforced
        db.session.add(dept2)
        try:
            db.session.commit()
            # If no constraint, both will exist
            assert True
        except Exception:
            # If constraint exists, this is expected
            db.session.rollback()
            assert True

    def test_department_budget_handling(self):
        """Test budget field functionality"""
        department = Department(
            name='Finance',
            code='FIN',
            budget=250000.50
        )
        
        db.session.add(department)
        db.session.commit()
        
        assert department.budget == 250000.50
        assert isinstance(department.budget, float)

    def test_department_active_flag(self):
        """Test is_active flag functionality"""
        active_dept = Department(
            name='Active Department',
            code='ACTIVE',
            is_active=True
        )
        
        inactive_dept = Department(
            name='Inactive Department',
            code='INACTIVE',
            is_active=False
        )
        
        db.session.add_all([active_dept, inactive_dept])
        db.session.commit()
        
        assert active_dept.is_active is True
        assert inactive_dept.is_active is False

    def test_department_description_field(self):
        """Test description field for detailed information"""
        long_description = """
        The Research and Development department is responsible for innovation,
        product development, and technological advancement within the company.
        This includes market research, prototype development, and testing.
        """
        
        department = Department(
            name='Research & Development',
            code='RND',
            description=long_description.strip()
        )
        
        db.session.add(department)
        db.session.commit()
        
        assert department.description == long_description.strip()

    def test_department_timestamps(self):
        """Test automatic timestamp handling"""
        department = Department(
            name='Timestamp Test',
            code='TIME'
        )
        
        db.session.add(department)
        db.session.commit()
        
        # Test created_at is set
        assert department.created_at is not None
        assert isinstance(department.created_at, datetime)
        
        # Test updated_at is set
        assert department.updated_at is not None
        assert isinstance(department.updated_at, datetime)
        
        # Test updated_at changes on update
        original_updated_at = department.updated_at
        department.description = 'Updated description'
        db.session.commit()
        
        assert department.updated_at > original_updated_at

    def test_department_query_by_status(self):
        """Test querying departments by active status"""
        active_depts = [
            Department(name='Active 1', code='ACT1', is_active=True),
            Department(name='Active 2', code='ACT2', is_active=True)
        ]
        
        inactive_depts = [
            Department(name='Inactive 1', code='INA1', is_active=False),
            Department(name='Inactive 2', code='INA2', is_active=False)
        ]
        
        db.session.add_all(active_depts + inactive_depts)
        db.session.commit()
        
        # Query only active departments
        active_results = Department.query.filter_by(is_active=True).all()
        active_names = [dept.name for dept in active_results]
        
        assert 'Active 1' in active_names
        assert 'Active 2' in active_names
        assert 'Inactive 1' not in active_names
        assert 'Inactive 2' not in active_names

    def test_department_query_by_manager(self):
        """Test querying departments by manager"""
        dept_with_manager = Department(
            name='Managed Department',
            code='MGMT',
            manager_id=self.user.id
        )
        
        dept_without_manager = Department(
            name='Unmanaged Department',
            code='UNMGMT'
        )
        
        db.session.add_all([dept_with_manager, dept_without_manager])
        db.session.commit()
        
        # Query departments by manager
        managed_depts = Department.query.filter_by(manager_id=self.user.id).all()
        
        assert len(managed_depts) == 1
        assert managed_depts[0].name == 'Managed Department'

    def test_department_budget_calculations(self):
        """Test budget-related calculations"""
        departments = [
            Department(name='Dept 1', code='D1', budget=100000.0),
            Department(name='Dept 2', code='D2', budget=150000.0),
            Department(name='Dept 3', code='D3', budget=200000.0)
        ]
        
        db.session.add_all(departments)
        db.session.commit()
        
        # Calculate total budget
        total_budget = sum(dept.budget for dept in departments if dept.budget)
        assert total_budget == 450000.0
        
        # Find department with highest budget
        max_budget_dept = max(departments, key=lambda d: d.budget or 0)
        assert max_budget_dept.name == 'Dept 3'

    def test_department_update_operations(self):
        """Test department update operations"""
        department = Department(
            name='Original Name',
            code='ORIG',
            budget=100000.0,
            is_active=True
        )
        
        db.session.add(department)
        db.session.commit()
        
        # Update department
        department.name = 'Updated Name'
        department.budget = 120000.0
        department.is_active = False
        department.description = 'Added description'
        
        db.session.commit()
        
        # Verify updates
        updated_dept = Department.query.get(department.id)
        assert updated_dept.name == 'Updated Name'
        assert updated_dept.budget == 120000.0
        assert updated_dept.is_active is False
        assert updated_dept.description == 'Added description'
        assert updated_dept.code == 'ORIG'  # Unchanged

    def test_department_deletion(self):
        """Test department deletion"""
        department = Department(
            name='To Delete',
            code='DELETE'
        )
        
        db.session.add(department)
        db.session.commit()
        department_id = department.id
        
        # Delete department
        db.session.delete(department)
        db.session.commit()
        
        # Verify deletion
        deleted_dept = Department.query.get(department_id)
        assert deleted_dept is None

    def test_department_search_functionality(self):
        """Test department search by name"""
        departments = [
            Department(name='Engineering Team', code='ENG'),
            Department(name='Engineering Support', code='ENGS'),
            Department(name='Marketing', code='MKT')
        ]
        
        db.session.add_all(departments)
        db.session.commit()
        
        # Search for departments containing 'Engineering'
        eng_depts = Department.query.filter(Department.name.contains('Engineering')).all()
        eng_names = [dept.name for dept in eng_depts]
        
        assert 'Engineering Team' in eng_names
        assert 'Engineering Support' in eng_names
        assert 'Marketing' not in eng_names

    def test_department_default_values(self):
        """Test default values for optional fields"""
        department = Department(
            name='Default Test',
            code='DEF'
        )
        
        db.session.add(department)
        db.session.commit()
        
        # Check default values
        assert department.is_active is True  # Assuming default is True
        assert department.budget is None     # Should be None if not set
        assert department.manager_id is None # Should be None if not set
        assert department.description is None # Should be None if not set

    def test_department_employee_count(self):
        """Test counting employees in department (if relationship exists)"""
        department = Department(
            name='Test Department',
            code='TEST'
        )
        
        db.session.add(department)
        db.session.commit()
        
        # This test assumes there's a relationship to count employees
        # The actual implementation depends on how User-Department relationship is set up
        assert hasattr(department, 'employees') or hasattr(department, 'users')

    def test_department_hierarchy(self):
        """Test department hierarchy if parent-child relationships exist"""
        parent_dept = Department(
            name='Parent Department',
            code='PARENT'
        )
        
        db.session.add(parent_dept)
        db.session.commit()
        
        # If there's a parent_id field for hierarchy
        if hasattr(Department, 'parent_id'):
            child_dept = Department(
                name='Child Department',
                code='CHILD',
                parent_id=parent_dept.id
            )
            
            db.session.add(child_dept)
            db.session.commit()
            
            assert child_dept.parent_id == parent_dept.id
