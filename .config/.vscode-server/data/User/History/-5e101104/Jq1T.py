"""Unit tests for PollVote model."""
import pytest
from models import PollVote, Poll, PollOption, User
from extensions import db

class TestPollVoteModel:
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        self.app = app
        self.user = test_user
        
        self.poll = Poll.query.first()
        if not self.poll:
            self.poll = Poll(title='Test Poll', description='Test', author_id=self.user.id)  # Campo corretto
            db.session.add(self.poll)
            db.session.commit()
            
        self.option = PollOption.query.first()
        if not self.option:
            self.option = PollOption(poll_id=self.poll.id, option_text='Option A')  # Campo corretto
            db.session.add(self.option)
            db.session.commit()

    def test_pollvote_creation_basic(self):
        vote = PollVote(
            poll_id=self.poll.id,
            option_id=self.option.id,
            user_id=self.user.id
        )
        db.session.add(vote)
        db.session.commit()
        
        assert vote.id is not None
        assert vote.poll_id == self.poll.id
        assert vote.option_id == self.option.id
        assert vote.user_id == self.user.id

    def test_pollvote_deletion(self):
        vote = PollVote(
            poll_id=self.poll.id,
            option_id=self.option.id,
            user_id=self.user.id
        )
        db.session.add(vote)
        db.session.commit()
        vote_id = vote.id
        
        db.session.delete(vote)
        db.session.commit()
        
        deleted = PollVote.query.get(vote_id)
        assert deleted is None
