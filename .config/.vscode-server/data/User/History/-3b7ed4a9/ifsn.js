import { defineStore } from 'pinia'
import { useAuthStore } from './auth'

export const useTimesheetStore = defineStore('timesheet', {
  state: () => ({
    // Dashboard stats
    stats: {
      weeklyHours: 0,
      monthlyHours: 0,
      pendingApprovals: 0,
      efficiency: 0
    },
    
    // Recent activities
    recentActivities: [],
    
    // Pending approvals (for managers)
    pendingApprovals: [],
    
    // User's timesheet status
    myStatus: {
      status: 'draft',
      totalHours: 0,
      billableHours: 0
    },
    
    // Monthly timesheet data
    currentMonth: new Date().getMonth() + 1,
    currentYear: new Date().getFullYear(),
    projectTasks: [],
    monthlyEntries: {},
    monthlyTimesheetStatus: null, // Stato del timesheet mensile: 'pending', 'confirmed', 'approved'
    
    // Available projects for timesheet
    availableProjects: [],
    
    // Loading states
    loading: {
      dashboard: false,
      monthlyData: false,
      saving: false
    },
    
    // Error states
    error: null,
    
    // Cache timestamps
    lastFetch: {
      dashboard: null,
      monthlyData: null,
      projects: null
    },

    // Additional state for time-off requests
    pendingTimeOffRequests: [],
    
    // Time-off quota information
    vacationQuota: { total: 0, used: 0, remaining: 0 },
    leaveQuota: { total: 0, used: 0 },
    smartWorkingDays: 0
  }),

  getters: {
    // Monthly calculations
    totalHours: (state) => {
      return Object.values(state.monthlyEntries).reduce((total, dayEntries) => {
        return total + Object.values(dayEntries).reduce((dayTotal, hours) => dayTotal + (hours || 0), 0)
      }, 0)
    },
    
    billableHours: (state) => {
      // Calculate based on project billable flags
      return Object.values(state.monthlyEntries).reduce((total, dayEntries) => {
        return total + Object.values(dayEntries).reduce((dayTotal, hours, index) => {
          const project = state.projectTasks[Math.floor(index / state.daysInMonth?.length || 31)]
          return dayTotal + (project?.billable ? (hours || 0) : 0)
        }, 0)
      }, 0)
    },
    
    pendingHours: (state) => {
      return state.myStatus.status === 'submitted' ? state.myStatus.totalHours : 0
    },
    
    activeProjects: (state) => {
      return state.projectTasks.length
    },
    
    daysInMonth: (state) => {
      const days = new Date(state.currentYear, state.currentMonth, 0).getDate()
      return Array.from({ length: days }, (_, i) => i + 1)
    },
    
    canApprove: () => {
      const authStore = useAuthStore()
      return authStore.user?.role === 'manager' || authStore.user?.role === 'admin'
    },
    
    // Check if data needs refresh (5 minutes cache)
    needsRefresh: (state) => {
      const fiveMinutes = 5 * 60 * 1000
      return {
        dashboard: !state.lastFetch.dashboard || Date.now() - state.lastFetch.dashboard > fiveMinutes,
        monthlyData: !state.lastFetch.monthlyData || Date.now() - state.lastFetch.monthlyData > fiveMinutes,
        projects: !state.lastFetch.projects || Date.now() - state.lastFetch.projects > fiveMinutes
      }
    }
  },

  actions: {
    // Dashboard data loading
    async loadDashboardStats() {
      if (this.loading.dashboard || !this.needsRefresh.dashboard) {
        return
      }

      this.loading.dashboard = true
      this.error = null

      try {
        const authStore = useAuthStore()
        const response = await fetch('/api/dashboard/stats', {
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': authStore.csrfToken
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result = await response.json()
        
        this.stats = {
          weeklyHours: result.data?.activities?.recent_timesheets || 0,
          monthlyHours: result.data?.activities?.recent_timesheets || 0,
          pendingApprovals: result.data?.activities?.unread_notifications || 0,
          efficiency: 85 // TODO: Calculate from actual timesheet data
        }
        
        this.lastFetch.dashboard = Date.now()
      } catch (err) {
        this.error = `Errore caricamento statistiche: ${err.message}`
        console.error('Error loading dashboard stats:', err)
      } finally {
        this.loading.dashboard = false
      }
    },

    async loadRecentActivities() {
      try {
        const authStore = useAuthStore()
        const response = await fetch('/api/timesheets/?per_page=5&page=1', {
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': authStore.csrfToken
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result = await response.json()
        const entries = result.data || []

        this.recentActivities = entries.map(entry => ({
          id: entry.id,
          description: `${entry.project_name || 'Progetto'} - ${entry.task_name || 'Task'}`,
          hours: entry.hours,
          created_at: entry.created_at,
          date: entry.date
        }))
      } catch (err) {
        console.error('Error loading recent activities:', err)
        this.recentActivities = []
      }
    },

    async loadPendingApprovals() {
      if (!this.canApprove) return

      try {
        const authStore = useAuthStore()
        const response = await fetch('/api/monthly-timesheets/?status=submitted', {
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': authStore.csrfToken
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result = await response.json()
        console.log('🔍 loadPendingApprovals API response:', result)
        console.log('🔍 Extracted data:', result.data)
        console.log('🔍 Timesheets array:', result.data?.timesheets)
        this.pendingApprovals = result.data?.timesheets || []
        console.log('🔍 Final pendingApprovals array:', this.pendingApprovals)
      } catch (err) {
        console.error('🚨 Error loading pending approvals:', err)
        console.log('🚨 Setting pendingApprovals to empty array')
        this.pendingApprovals = []
      }
    },

    async loadMyStatus() {
      if (this.canApprove) return

      try {
        const authStore = useAuthStore()
        const response = await fetch('/api/monthly-timesheets/generate', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': authStore.csrfToken
          },
          body: JSON.stringify({
            year: this.currentYear,
            month: this.currentMonth
          })
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result = await response.json()
        this.myStatus = {
          status: result.data?.status || 'draft',
          totalHours: result.data?.total_hours || 0,
          billableHours: result.data?.billable_hours || 0
        }
      } catch (err) {
        console.error('Error loading my status:', err)
      }
    },

    // Monthly timesheet data - using same API as PersonnelProfile
    async loadMonthlyData(year = this.currentYear, month = this.currentMonth, userId = null) {
      if (this.loading.monthlyData || (!this.needsRefresh.monthlyData && 
          year === this.currentYear && month === this.currentMonth)) {
        return {
          entries: this.monthlyEntries,
          projects: this.projectTasks
        }
      }

      this.loading.monthlyData = true
      this.error = null

      try {
        const authStore = useAuthStore()
        
        // Calculate start and end dates exactly like PersonnelProfile.vue
        const startDate = new Date(year, month - 1, 1).toISOString().split('T')[0]
        const endDate = new Date(year, month, 0).toISOString().split('T')[0]
        
        // Use same API call as PersonnelProfile.vue but allow filtering by different user
        const targetUserId = userId || authStore.user?.id || ''
        const response = await fetch(`/api/timesheets?start_date=${startDate}&end_date=${endDate}&user_id=${targetUserId}`, {
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': authStore.csrfToken
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result = await response.json()
        
        this.currentYear = year
        this.currentMonth = month
        
        // Check if response follows expected structure
        if (!result.success) {
          throw new Error(result.message || 'API response indicates failure')
        }
        
        // Process timesheet entries into monthly format (same logic as PersonnelProfile)
        const timesheets = result.data || []
        const monthlyEntries = {}
        const projectTasksSet = new Set()
        
        timesheets.forEach(timesheet => {
          const entryDate = timesheet.date
          const taskKey = `${timesheet.project_id}-${timesheet.task_id || 'notask'}`

          if (!monthlyEntries[entryDate]) {
            monthlyEntries[entryDate] = {}
          }

          if (!monthlyEntries[entryDate][taskKey]) {
            monthlyEntries[entryDate][taskKey] = {
              hours: 0,
              billable: timesheet.billable || false
            }
          }

          // If it's already an object, add hours; if it's a number (legacy), convert to object
          if (typeof monthlyEntries[entryDate][taskKey] === 'object') {
            monthlyEntries[entryDate][taskKey].hours += parseFloat(timesheet.hours || 0)
            // Keep billable true if any entry is billable
            monthlyEntries[entryDate][taskKey].billable = monthlyEntries[entryDate][taskKey].billable || timesheet.billable || false
          } else {
            // Convert legacy number to object
            monthlyEntries[entryDate][taskKey] = {
              hours: monthlyEntries[entryDate][taskKey] + parseFloat(timesheet.hours || 0),
              billable: timesheet.billable || false
            }
          }
          
          // Track unique project-task combinations
          projectTasksSet.add(JSON.stringify({
            id: taskKey,
            project_id: timesheet.project_id,
            task_id: timesheet.task_id || null,
            project_name: timesheet.project_name || 'Progetto Sconosciuto',
            task_name: timesheet.task_name || 'Attività Generica',
            billable: timesheet.billable || false
          }))
        })
        
        this.monthlyEntries = monthlyEntries
        this.projectTasks = Array.from(projectTasksSet).map(pt => JSON.parse(pt))
        
        // Carica lo stato del timesheet mensile
        await this.loadMonthlyTimesheetStatus(year, month)
        
        this.lastFetch.monthlyData = Date.now()
        console.log('Loaded monthly data:', {
          entries: Object.keys(this.monthlyEntries).length,
          projects: this.projectTasks.length,
          totalTimesheets: timesheets.length
        })
        
        return {
          entries: this.monthlyEntries,
          projects: this.projectTasks
        }
      } catch (err) {
        this.error = `Errore caricamento dati mensili: ${err.message}`
        console.error('Error loading monthly data:', err)
        return {
          entries: {},
          projects: []
        }
      } finally {
        this.loading.monthlyData = false
      }
    },

    // Available projects for timesheet
    async loadAvailableProjects() {
      if (this.availableProjects.length > 0 && !this.needsRefresh.projects) {
        return this.availableProjects
      }

      try {
        const authStore = useAuthStore()
        const response = await fetch('/api/projects/', {
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': authStore.csrfToken
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result = await response.json()
        
        // Handle different response structures
        let projects = []
        if (result.data && Array.isArray(result.data.projects)) {
          projects = result.data.projects
        } else if (result.data && Array.isArray(result.data.items)) {
          projects = result.data.items
        } else if (result.data && Array.isArray(result.data)) {
          projects = result.data
        } else if (Array.isArray(result)) {
          projects = result
        }

        this.availableProjects = projects.filter(p => p.status === 'active' || !p.status)
        this.lastFetch.projects = Date.now()
        console.log('Loaded available projects:', this.availableProjects.length, 'projects')
        return this.availableProjects
      } catch (err) {
        console.error('Error loading available projects:', err)
        this.availableProjects = []
        return []
      }
    },

    // Save timesheet entry using same API structure as PersonnelProfile.vue
    async saveEntry(projectTaskId, day, hours) {
      this.loading.saving = true
      
      try {
        const authStore = useAuthStore()
        
        // Parse project and task IDs from projectTaskId
        const [projectId, taskId] = projectTaskId.split('-')
        
        // Create date string
        const dateStr = `${this.currentYear}-${this.currentMonth.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`
        
        // Use same payload structure as PersonnelProfile.vue
        const payload = {
          date: dateStr,
          project_id: parseInt(projectId),
          task_id: taskId !== 'notask' ? parseInt(taskId) : null,
          hours: parseFloat(hours),
          description: `Lavoro del ${dateStr}`
        }
        
        const response = await fetch('/api/timesheets', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': authStore.csrfToken
          },
          body: JSON.stringify(payload)
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result = await response.json()
        
        // Check response success like PersonnelProfile.vue
        if (!result.success) {
          throw new Error(result.message || 'Errore durante il salvataggio')
        }

        // Update local state
        const entryKey = dateStr
        if (!this.monthlyEntries[entryKey]) {
          this.monthlyEntries[entryKey] = {}
        }
        this.monthlyEntries[entryKey][projectTaskId] = {
          hours: parseFloat(hours) || 0,
          billable: false // Default for entries saved through grid
        }
        
        console.log('Saved timesheet entry:', {
          date: dateStr,
          project: projectId,
          task: taskId,
          hours: hours,
          success: true
        })

        return true
      } catch (err) {
        this.error = `Errore salvataggio ore: ${err.message}`
        console.error('Error saving entry:', err)
        return false
      } finally {
        this.loading.saving = false
      }
    },

    // Add project to timesheet
    async addProjectToTimesheet(projectId, taskId) {
      try {
        const authStore = useAuthStore()
        const response = await fetch('/api/timesheet-projects/', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': authStore.csrfToken
          },
          body: JSON.stringify({
            project_id: projectId,
            task_id: taskId
          })
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        // Refresh monthly data to include new project
        await this.loadMonthlyData(this.currentYear, this.currentMonth)
        return true
      } catch (err) {
        this.error = `Errore aggiunta progetto: ${err.message}`
        console.error('Error adding project to timesheet:', err)
        return false
      }
    },

    // Approval actions
    async approveTimesheet(timesheetId) {
      try {
        const authStore = useAuthStore()
        const response = await fetch(`/api/monthly-timesheets/${timesheetId}/approve`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': authStore.csrfToken
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        // Refresh pending approvals
        await this.loadPendingApprovals()
        return true
      } catch (err) {
        this.error = `Errore approvazione: ${err.message}`
        console.error('Error approving timesheet:', err)
        return false
      }
    },

    // Navigation helpers
    navigateMonth(direction) {
      if (direction === 'next') {
        if (this.currentMonth === 12) {
          this.currentMonth = 1
          this.currentYear++
        } else {
          this.currentMonth++
        }
      } else {
        if (this.currentMonth === 1) {
          this.currentMonth = 12
          this.currentYear--
        } else {
          this.currentMonth--
        }
      }

      // Force refresh by invalidating cache
      this.needsRefresh.monthlyData = true

      // Load new month data
      this.loadMonthlyData(this.currentYear, this.currentMonth)
    },

    // Refresh all data
    async refreshAll() {
      // Reset cache timestamps to force refresh
      this.lastFetch = {
        dashboard: null,
        monthlyData: null,
        projects: null
      }
      
      await Promise.all([
        this.loadDashboardStats(),
        this.loadRecentActivities(),
        this.loadPendingApprovals(),
        this.loadMyStatus(),
        this.loadMonthlyData()
      ])
    },

    // Approval management
    async loadPendingTimesheets(filters = {}) {
      try {
        const authStore = useAuthStore()
        const params = new URLSearchParams({
          month: filters.month || new Date().getMonth() + 1,
          year: filters.year || new Date().getFullYear()
        })

        if (filters.status) params.append('status', filters.status)
        if (filters.user_id) params.append('user_id', filters.user_id)
        if (filters.search) params.append('search', filters.search)
        if (filters.anomalies_only) params.append('anomalies_only', 'true')

        const response = await fetch(`/api/monthly-timesheets/?${params}`, {
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': authStore.csrfToken
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result = await response.json()
        console.log('🔍 loadPendingTimesheets API response:', result)
        return result.data?.timesheets || []
      } catch (err) {
        this.error = `Errore caricamento timesheet: ${err.message}`
        console.error('Error loading pending timesheets:', err)
        return []
      }
    },

    async rejectTimesheet(timesheetId, reason) {
      try {
        const authStore = useAuthStore()
        const response = await fetch(`/api/monthly-timesheets/${timesheetId}/reject`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': authStore.csrfToken
          },
          body: JSON.stringify({ reason })
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        return true
      } catch (err) {
        this.error = `Errore rifiuto timesheet: ${err.message}`
        console.error('Error rejecting timesheet:', err)
        return false
      }
    },

    // Analytics data loading
    async loadAnalyticsData(filters = {}) {
      try {
        const authStore = useAuthStore()
        const params = new URLSearchParams()

        if (filters.start_date) params.append('start_date', filters.start_date)
        if (filters.end_date) params.append('end_date', filters.end_date)
        if (filters.department_id) params.append('department_id', filters.department_id)
        if (filters.project_id) params.append('project_id', filters.project_id)
        if (filters.analysis_type) params.append('analysis_type', filters.analysis_type)

        const response = await fetch(`/api/timesheets/analytics/?${params}`, {
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': authStore.csrfToken
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result = await response.json()
        
        if (!result.success) {
          throw new Error(result.message || 'API response indicates failure')
        }
        
        return result.data || []
      } catch (err) {
        this.error = `Errore caricamento analytics: ${err.message}`
        console.error('Error loading analytics data:', err)
        return []
      }
    },

    async loadTeamMembers() {
      try {
        const authStore = useAuthStore()
        const response = await fetch('/api/personnel/users', {
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': authStore.csrfToken
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result = await response.json()
        return result.data?.users || result.data || []
      } catch (err) {
        this.error = `Errore caricamento team: ${err.message}`
        console.error('Error loading team members:', err)
        return []
      }
    },

    async loadDepartments() {
      try {
        const authStore = useAuthStore()
        const response = await fetch('/api/personnel/departments', {
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': authStore.csrfToken
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result = await response.json()
        return result.data?.departments || result.data || []
      } catch (err) {
        console.error('Error loading departments:', err)
        return []
      }
    },

    // Bulk operations
    async bulkApproveTimesheets(timesheetIds) {
      try {
        const authStore = useAuthStore()
        const results = await Promise.allSettled(
          timesheetIds.map(id => this.approveTimesheet(id))
        )

        const successful = results.filter(r => r.status === 'fulfilled' && r.value).length
        const failed = results.length - successful

        if (failed > 0) {
          this.error = `${successful} approvati, ${failed} falliti`
        }

        return { successful, failed }
      } catch (err) {
        this.error = `Errore approvazione multipla: ${err.message}`
        return { successful: 0, failed: timesheetIds.length }
      }
    },

    async bulkRejectTimesheets(timesheetIds, reason) {
      try {
        const results = await Promise.allSettled(
          timesheetIds.map(id => this.rejectTimesheet(id, reason))
        )

        const successful = results.filter(r => r.status === 'fulfilled' && r.value).length
        const failed = results.length - successful

        if (failed > 0) {
          this.error = `${successful} rifiutati, ${failed} falliti`
        }

        return { successful, failed }
      } catch (err) {
        this.error = `Errore rifiuto multiplo: ${err.message}`
        return { successful: 0, failed: timesheetIds.length }
      }
    },

    // Export functionality
    async exportTimesheetData(filters = {}) {
      try {
        const authStore = useAuthStore()
        const params = new URLSearchParams()
        
        Object.keys(filters).forEach(key => {
          if (filters[key]) {
            params.append(key, filters[key])
          }
        })

        const response = await fetch(`/api/timesheets/export/?${params}`, {
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': authStore.csrfToken
          }
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`)
        }

        // Handle file download
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `timesheet_export_${new Date().toISOString().split('T')[0]}.xlsx`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)

        return true
      } catch (err) {
        this.error = `Errore export: ${err.message}`
        console.error('Error exporting data:', err)
        return false
      }
    },

    // Additional methods needed by TimesheetEntry
    async loadTimesheetHistory(filters = {}) {
      try {
        const authStore = useAuthStore()
        const params = new URLSearchParams()
        
        Object.keys(filters).forEach(key => {
          if (filters[key]) {
            params.append(key, filters[key])
          }
        })

        const response = await fetch(`/api/timesheets/?${params}`, {
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': authStore.csrfToken
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result = await response.json()
        return result.data || []
      } catch (err) {
        this.error = `Errore caricamento storico: ${err.message}`
        console.error('Error loading timesheet history:', err)
        return []
      }
    },


    async loadTimeOffRequests(filters = {}) {
      try {
        const authStore = useAuthStore()
        const params = new URLSearchParams()
        
        Object.keys(filters).forEach(key => {
          if (filters[key]) {
            // Map request_type to type for API consistency
            const apiKey = key === 'request_type' ? 'type' : key
            params.append(apiKey, filters[key])
          }
        })

        const response = await fetch(`/api/time-off-requests/?${params}`, {
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': authStore.csrfToken
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result = await response.json()
        // Supporta diversi formati di risposta: preferisci array di richieste
        if (Array.isArray(result.data)) {
          return result.data
        }
        if (result.data && Array.isArray(result.data.requests)) {
          return result.data.requests
        }
        // Fallback: nessun risultato valido
        return []
      } catch (err) {
        this.error = `Errore caricamento richieste: ${err.message}`
        console.error('Error loading time off requests:', err)
        return []
      }
    },

    async createTimeOffRequest(data) {
      try {
        const authStore = useAuthStore()
        const response = await fetch('/api/time-off-requests/', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': authStore.csrfToken
          },
          body: JSON.stringify(data)
        })

        if (!response.ok) {
          // Leggi il corpo della risposta per ottenere il messaggio di errore specifico
          const errorData = await response.json()
          throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`)
        }

        return true
      } catch (err) {
        this.error = `${err.message}`
        console.error('Error creating time off request:', err)
        return false
      }
    },

    async deleteTimeOffRequest(requestId) {
      try {
        const authStore = useAuthStore()
        const response = await fetch(`/api/time-off-requests/${requestId}`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': authStore.csrfToken
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        return true
      } catch (err) {
        this.error = `Errore eliminazione richiesta: ${err.message}`
        console.error('Error deleting time off request:', err)
        return false
      }
    },

    async approveTimeOffRequest(requestId) {
      try {
        const authStore = useAuthStore()
        const response = await fetch(`/api/time-off-requests/${requestId}/approve`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': authStore.csrfToken
          }
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`)
        }

        return true
      } catch (err) {
        this.error = `Errore approvazione richiesta: ${err.message}`
        console.error('Error approving time off request:', err)
        return false
      }
    },

    async rejectTimeOffRequest(requestId, reason) {
      try {
        const authStore = useAuthStore()
        const response = await fetch(`/api/time-off-requests/${requestId}/reject`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': authStore.csrfToken
          },
          body: JSON.stringify({ reason })
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`)
        }

        return true
      } catch (err) {
        this.error = `Errore rifiuto richiesta: ${err.message}`
        console.error('Error rejecting time off request:', err)
        return false
      }
    },

    // Clear error
    clearError() {
      this.error = null
    },

    // Aggiunta del metodo per caricare le quote di time-off
    async loadTimeOffQuotas() {
      try {
        const authStore = useAuthStore()
        const response = await fetch('/api/time-off-requests/quotas', {
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': authStore.csrfToken
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result = await response.json()
        const quotaData = result.data || {
          vacation: { remaining: 0, total: 0 },
          leave: { used: 0, total: 0 },
          smartworking: { used: 0 }
        }
        
        // Aggiorna le proprietà dello state
        this.vacationQuota = quotaData.vacation;
        this.leaveQuota = quotaData.leave;
        this.smartWorkingDays = quotaData.smartworking?.used || 0;
        
        return quotaData;
      } catch (err) {
        console.error('Error loading time-off quotas:', err)
        
        // Valori predefiniti in caso di errore
        const defaultData = {
          vacation: { remaining: 0, total: 0 },
          leave: { used: 0, total: 0 },
          smartworking: { used: 0 }
        };
        
        // Aggiorna comunque le proprietà con valori predefiniti
        this.vacationQuota = defaultData.vacation;
        this.leaveQuota = defaultData.leave;
        this.smartWorkingDays = 0;
        
        return defaultData;
      }
    },

    // Set monthlyTimesheetStatus
    setMonthlyTimesheetStatus(status) {
      this.monthlyTimesheetStatus = status
    },

    // Load lo stato del timesheet mensile
    async loadMonthlyTimesheetStatus(year = this.currentYear, month = this.currentMonth) {
      try {
        const authStore = useAuthStore()
        const yearMonth = `${year}-${month.toString().padStart(2, '0')}`
        
        try {
          const response = await fetch(`/api/timesheets/status?year_month=${yearMonth}&user_id=${authStore.user.id}`, {
            headers: {
              'Content-Type': 'application/json',
              'X-CSRFToken': authStore.csrfToken
            }
          })

          if (!response.ok) {
            // Se l'endpoint non esiste (404) o altri errori, usa lo stato predefinito
            this.monthlyTimesheetStatus = 'pending'
            return 'pending'
          }

          const result = await response.json()
          
          if (result.success) {
            this.monthlyTimesheetStatus = result.data?.status || 'pending'
          } else {
            this.monthlyTimesheetStatus = 'pending' // Default fallback
          }
        } catch (err) {
          // In caso di errore di rete o altro, semplicemente usa lo stato predefinito
          this.monthlyTimesheetStatus = 'pending'
        }
        
        return this.monthlyTimesheetStatus
      } catch (err) {
        // Gestione errori silenziosa in attesa dell'implementazione backend
        this.monthlyTimesheetStatus = 'pending' // Default fallback in caso di errore
        return 'pending'
      }
    },

    // Metodo per caricare le richieste di time-off in attesa
    async loadPendingTimeOffRequests() {
      try {
        // Utilizziamo il metodo esistente loadTimeOffRequests con filtro per status=pending
        const requests = await this.loadTimeOffRequests({ status: 'pending' });
        // Salviamo le richieste pendenti nello store
        this.pendingTimeOffRequests = requests;
        return requests;
      } catch (err) {
        console.error('Error loading pending time-off requests:', err);
        this.error = `Errore caricamento richieste in attesa: ${err.message}`;
        this.pendingTimeOffRequests = [];
        return [];
      }
    }
  }
})