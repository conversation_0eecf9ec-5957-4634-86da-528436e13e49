"""Unit tests for TimeOffRequest model."""
import pytest
from datetime import date
from models import TimeOffRequest, User
from extensions import db

class TestTimeOffRequestModel:
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        self.app = app
        self.user = test_user

    def test_timeoffrequest_creation_basic(self):
        request = TimeOffRequest(
            user_id=self.user.id,
            start_date=date(2024, 7, 1),
            end_date=date(2024, 7, 5),
            type='vacation',  # Campo corretto è 'type', non 'request_type'
            reason='Summer vacation'
        )
        db.session.add(request)
        db.session.commit()

        assert request.id is not None
        assert request.user_id == self.user.id
        assert request.start_date == date(2024, 7, 1)
        assert request.end_date == date(2024, 7, 5)
        assert request.type == 'vacation'

    def test_timeoffrequest_status(self):
        request = TimeOffRequest(
            user_id=self.user.id,
            start_date=date(2024, 8, 1),
            end_date=date(2024, 8, 3),
            type='sick',  # Campo corretto è 'type'
            status='pending'
        )
        db.session.add(request)
        db.session.commit()
        
        assert request.status == 'pending'

    def test_timeoffrequest_deletion(self):
        request = TimeOffRequest(
            user_id=self.user.id,
            start_date=date(2024, 9, 1),
            end_date=date(2024, 9, 2),
            type='personal'  # Campo corretto è 'type'
        )
        db.session.add(request)
        db.session.commit()
        request_id = request.id
        
        db.session.delete(request)
        db.session.commit()
        
        deleted = TimeOffRequest.query.get(request_id)
        assert deleted is None
