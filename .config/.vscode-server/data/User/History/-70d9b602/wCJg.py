"""
Unit tests for JobLevel model.
Tests business logic, validations, and model functionality.
"""

import pytest
from datetime import datetime
from models import JobLevel
from extensions import db


class TestJobLevelModel:
    """Test suite for JobLevel model unit tests"""

    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        """Setup test data for each test method"""
        self.app = app
        self.user = test_user

    def test_joblevel_creation_basic(self):
        """Test basic job level creation"""
        job_level = JobLevel(
            name='Junior Developer',
            level_number=1,
            description='Entry level developer position'
        )
        
        db.session.add(job_level)
        db.session.commit()
        
        assert job_level.id is not None
        assert job_level.name == 'Junior Developer'
        assert job_level.level_number == 1
        assert job_level.description == 'Entry level developer position'

    def test_joblevel_creation_complete(self):
        """Test job level creation with all fields"""
        job_level = JobLevel(
            name='Senior Developer',
            level_number=3,
            description='Senior level developer with leadership responsibilities',
            min_salary=60000.0,
            max_salary=90000.0,
            typical_years_experience=5,
            is_management=False,
            is_active=True
        )
        
        db.session.add(job_level)
        db.session.commit()
        
        assert job_level.name == 'Senior Developer'
        assert job_level.level_number == 3
        assert job_level.min_salary == 60000.0
        assert job_level.max_salary == 90000.0
        assert job_level.typical_years_experience == 5
        assert job_level.is_management is False
        assert job_level.is_active is True

    def test_joblevel_level_numbers(self):
        """Test different level numbers"""
        levels = [
            ('Intern', 100),
            ('Junior', 101),
            ('Mid-level', 102),
            ('Senior', 103),
            ('Lead', 104),
            ('Principal', 105)
        ]
        
        job_levels = []
        for name, level_num in levels:
            job_level = JobLevel(
                name=name,
                level_number=level_num
            )
            job_levels.append(job_level)
        
        db.session.add_all(job_levels)
        db.session.commit()
        
        for job_level, (expected_name, expected_level) in zip(job_levels, levels):
            assert job_level.name == expected_name
            assert job_level.level_number == expected_level

    def test_joblevel_salary_ranges(self):
        """Test salary range functionality"""
        salary_ranges = [
            ('Entry Level', 30000.0, 45000.0),
            ('Mid Level', 45000.0, 70000.0),
            ('Senior Level', 70000.0, 100000.0),
            ('Executive Level', 100000.0, 150000.0)
        ]
        
        job_levels = []
        for i, (name, min_sal, max_sal) in enumerate(salary_ranges):
            job_level = JobLevel(
                name=name,
                level_number=i + 1,
                min_salary=min_sal,
                max_salary=max_sal
            )
            job_levels.append(job_level)
        
        db.session.add_all(job_levels)
        db.session.commit()
        
        for job_level, (_, expected_min, expected_max) in zip(job_levels, salary_ranges):
            assert job_level.min_salary == expected_min
            assert job_level.max_salary == expected_max
            assert job_level.min_salary <= job_level.max_salary

    def test_joblevel_management_flag(self):
        """Test management flag functionality"""
        # Non-management role
        individual_contributor = JobLevel(
            name='Software Engineer',
            level_number=2,
            is_management=False
        )
        
        # Management role
        team_lead = JobLevel(
            name='Engineering Manager',
            level_number=4,
            is_management=True
        )
        
        db.session.add_all([individual_contributor, team_lead])
        db.session.commit()
        
        assert individual_contributor.is_management is False
        assert team_lead.is_management is True

    def test_joblevel_experience_requirements(self):
        """Test typical years experience field"""
        experience_levels = [
            ('Graduate', 0),
            ('Junior', 1),
            ('Mid-level', 3),
            ('Senior', 5),
            ('Staff', 8),
            ('Principal', 12)
        ]
        
        job_levels = []
        for i, (name, years) in enumerate(experience_levels):
            job_level = JobLevel(
                name=name,
                level_number=i,
                typical_years_experience=years
            )
            job_levels.append(job_level)
        
        db.session.add_all(job_levels)
        db.session.commit()
        
        for job_level, (_, expected_years) in zip(job_levels, experience_levels):
            assert job_level.typical_years_experience == expected_years
            assert job_level.typical_years_experience >= 0

    def test_joblevel_active_status(self):
        """Test active status functionality"""
        # Active job level
        active_level = JobLevel(
            name='Active Position',
            level_number=1,
            is_active=True
        )
        
        # Inactive job level
        inactive_level = JobLevel(
            name='Deprecated Position',
            level_number=2,
            is_active=False
        )
        
        db.session.add_all([active_level, inactive_level])
        db.session.commit()
        
        assert active_level.is_active is True
        assert inactive_level.is_active is False

    def test_joblevel_hierarchical_structure(self):
        """Test parent-child relationship"""
        # Parent level
        parent_level = JobLevel(
            name='Director',
            level_number=6
        )
        
        db.session.add(parent_level)
        db.session.commit()
        
        # Child level
        child_level = JobLevel(
            name='Senior Manager',
            level_number=5,
            parent_level_id=parent_level.id
        )
        
        db.session.add(child_level)
        db.session.commit()
        
        assert child_level.parent_level_id == parent_level.id

    def test_joblevel_query_by_level_number(self):
        """Test querying by level number"""
        job_level = JobLevel(
            name='Specific Level Test',
            level_number=99
        )
        
        db.session.add(job_level)
        db.session.commit()
        
        # Query by level number
        found_levels = JobLevel.query.filter_by(level_number=99).all()
        assert len(found_levels) >= 1
        assert job_level in found_levels

    def test_joblevel_query_by_management_status(self):
        """Test querying by management status"""
        manager_level = JobLevel(
            name='Manager Position',
            level_number=10,
            is_management=True
        )
        
        db.session.add(manager_level)
        db.session.commit()
        
        # Query management positions
        management_levels = JobLevel.query.filter_by(is_management=True).all()
        assert len(management_levels) >= 1
        assert manager_level in management_levels

    def test_joblevel_query_by_active_status(self):
        """Test querying by active status"""
        active_level = JobLevel(
            name='Currently Active',
            level_number=11,
            is_active=True
        )
        
        db.session.add(active_level)
        db.session.commit()
        
        # Query active levels
        active_levels = JobLevel.query.filter_by(is_active=True).all()
        assert len(active_levels) >= 1
        assert active_level in active_levels

    def test_joblevel_salary_range_queries(self):
        """Test querying by salary ranges"""
        high_salary_level = JobLevel(
            name='High Salary Position',
            level_number=12,
            min_salary=80000.0,
            max_salary=120000.0
        )
        
        db.session.add(high_salary_level)
        db.session.commit()
        
        # Query levels with high minimum salary
        high_paying_levels = JobLevel.query.filter(JobLevel.min_salary >= 75000.0).all()
        assert len(high_paying_levels) >= 1
        assert high_salary_level in high_paying_levels

    def test_joblevel_timestamps(self):
        """Test automatic timestamp handling"""
        job_level = JobLevel(
            name='Timestamp Test',
            level_number=13
        )
        
        db.session.add(job_level)
        db.session.commit()
        
        assert job_level.created_at is not None
        assert job_level.updated_at is not None
        assert isinstance(job_level.created_at, datetime)
        assert isinstance(job_level.updated_at, datetime)

    def test_joblevel_update_operations(self):
        """Test job level update operations"""
        job_level = JobLevel(
            name='Original Name',
            level_number=14,
            min_salary=40000.0,
            is_active=True
        )
        
        db.session.add(job_level)
        db.session.commit()
        
        # Update job level
        job_level.name = 'Updated Name'
        job_level.min_salary = 50000.0
        job_level.is_active = False
        
        db.session.commit()
        
        updated_level = JobLevel.query.get(job_level.id)
        assert updated_level.name == 'Updated Name'
        assert updated_level.min_salary == 50000.0
        assert updated_level.is_active is False

    def test_joblevel_deletion(self):
        """Test job level deletion"""
        job_level = JobLevel(
            name='To Be Deleted',
            level_number=15
        )
        
        db.session.add(job_level)
        db.session.commit()
        level_id = job_level.id
        
        db.session.delete(job_level)
        db.session.commit()
        
        deleted_level = JobLevel.query.get(level_id)
        assert deleted_level is None

    def test_joblevel_name_length(self):
        """Test name field length"""
        long_name = 'Very Long Job Level Name That Tests The Maximum Field Length Constraint For Job Level Names'
        
        job_level = JobLevel(
            name=long_name[:100],  # Truncate to VARCHAR(100) limit
            level_number=16
        )
        
        db.session.add(job_level)
        db.session.commit()
        
        assert len(job_level.name) <= 100
        assert job_level.name == long_name[:100]
