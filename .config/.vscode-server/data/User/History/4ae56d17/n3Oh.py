"""Unit tests for ForumTopic model."""
import pytest
from models import ForumTopic, User
from extensions import db

class TestForumTopicModel:
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        self.app = app
        self.user = test_user

    def test_forumtopic_creation_basic(self):
        topic = ForumTopic(
            title='Test Topic',
            description='This is a test forum topic',  # Campo corretto è 'description'
            author_id=self.user.id,
            category='general'
        )
        db.session.add(topic)
        db.session.commit()

        assert topic.id is not None
        assert topic.title == 'Test Topic'
        assert topic.description == 'This is a test forum topic'
        assert topic.author_id == self.user.id
        assert topic.category == 'general'

    def test_forumtopic_pinned(self):
        topic = ForumTopic(
            title='Pinned Topic',
            description='Important announcement',  # Campo corretto è 'description'
            author_id=self.user.id,
            is_pinned=True
        )
        db.session.add(topic)
        db.session.commit()
        
        assert topic.is_pinned is True

    def test_forumtopic_deletion(self):
        topic = ForumTopic(
            title='To Delete',
            description='This will be deleted',  # <PERSON> corretto è 'description'
            author_id=self.user.id
        )
        db.session.add(topic)
        db.session.commit()
        topic_id = topic.id
        
        db.session.delete(topic)
        db.session.commit()
        
        deleted = ForumTopic.query.get(topic_id)
        assert deleted is None
