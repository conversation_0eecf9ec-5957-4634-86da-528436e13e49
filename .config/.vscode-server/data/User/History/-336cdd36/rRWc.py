"""
Test per i calcoli KPI del progetto.
"""
import pytest
from datetime import datetime, date, timedelta
from decimal import Decimal

from models import Project, User, Task, TimesheetEntry, PersonnelRate, ProjectExpense, ProjectKPITemplate, ProjectKPITarget
from utils.cost_calculator import (
    calculate_project_profitability,
    calculate_project_kpis,
    get_kpi_status,
    get_user_daily_rate_for_date
)
from app import db


class TestKPICalculations:
    """Test per i calcoli KPI."""

    def test_calculate_project_profitability_basic(self, app, test_project, test_user):
        """Test calcolo profittabilità base."""
        with app.app_context():
            project = Project.query.get(test_project)
            user = test_user

            # Crea rate per l'utente
            rate = PersonnelRate(
                user_id=user.id,
                daily_rate=300.0,
                valid_from=date.today() - timedelta(days=30),
                valid_to=date.today() + timedelta(days=30)
            )
            db.session.add(rate)

            # Crea timesheet
            timesheet = TimesheetEntry(
                user_id=user.id,
                project_id=project.id,
                date=date.today(),
                hours=8.0,
                description="Test work"
            )
            db.session.add(timesheet)

            # Imposta progetto come fatturabile
            project.is_billable = True
            project.client_daily_rate = 500.0

            db.session.commit()

            # Calcola profittabilità
            result = calculate_project_profitability(project.id)

            assert result is not None
            assert 'personnel' in result
            assert 'profitability' in result
            assert 'revenue' in result

            # Verifica calcoli
            assert result['personnel']['total_cost'] == 300.0  # 1 giorno * 300€
            assert result['revenue']['potential'] == 500.0  # 1 giorno * 500€
            assert result['profitability']['gross_margin'] == 200.0  # 500 - 300
            assert result['profitability']['gross_margin_percentage'] == 40.0  # (200/500)*100

    def test_calculate_project_profitability_with_expenses(self, app, test_project, test_user):
        """Test calcolo profittabilità con spese."""
        with app.app_context():
            project = Project.query.get(test_project)
            user = test_user

            # Crea rate
            rate = PersonnelRate(
                user_id=user.id,
                daily_rate=300.0,
                valid_from=date.today() - timedelta(days=30)
            )
            db.session.add(rate)

            # Crea timesheet
            timesheet = TimesheetEntry(
                user_id=user.id,
                project_id=project.id,
                date=date.today(),
                hours=8.0
            )
            db.session.add(timesheet)

            # Crea spesa
            expense = ProjectExpense(
                project_id=project.id,
                user_id=user.id,
                category='travel',
                description='Viaggio cliente',
                amount=100.0,
                billing_type='billable',
                date=date.today(),
                status='approved'
            )
            db.session.add(expense)

            project.is_billable = True
            project.client_daily_rate = 500.0

            db.session.commit()

            result = calculate_project_profitability(project.id)

            # Verifica costi
            assert result['personnel']['total_cost'] == 300.0
            assert result['expenses']['total_amount'] == 100.0
            assert result['revenue']['potential'] == 500.0

            # Margine netto = ricavi - (personale + spese) = 500 - 400 = 100
            assert result['profitability']['net_margin'] == 100.0
            assert result['profitability']['net_margin_percentage'] == 20.0

    def test_calculate_project_kpis(self, app, test_project, test_user):
        """Test calcolo KPI completi."""
        with app.app_context():
            project = Project.query.get(test_project)
            user = test_user

            # Setup dati
            rate = PersonnelRate(
                user_id=user.id,
                daily_rate=400.0,
                valid_from=date.today() - timedelta(days=30)
            )
            db.session.add(rate)

            # Crea più timesheet per testare utilization
            for i in range(5):
                timesheet = TimesheetEntry(
                    user_id=user.id,
                    project_id=project.id,
                    date=date.today() - timedelta(days=i),
                    hours=6.0  # 6 ore su 8 = 75% utilization
                )
                db.session.add(timesheet)

            project.is_billable = True
            project.client_daily_rate = 600.0

            db.session.commit()

            kpis = calculate_project_kpis(project.id)

            assert kpis is not None
            assert 'margin_percentage' in kpis
            assert 'utilization_rate' in kpis
            assert 'cost_per_hour' in kpis
            assert 'cost_revenue_ratio' in kpis

            # Verifica utilization rate (dovrebbe essere > 0 e < 100)
            assert 0 < kpis['utilization_rate'] < 100

            # Verifica cost per hour (deve essere un valore ragionevole)
            assert kpis['cost_per_hour'] > 0
            assert kpis['cost_per_hour'] < 1000  # Soglia ragionevole

    def test_get_kpi_status_with_templates(self, app, test_project):
        """Test status KPI con template."""
        with app.app_context():
            project = Project.query.get(test_project)
            project.project_type = 'service'
            db.session.commit()

            # Crea template KPI per service
            template = ProjectKPITemplate(
                project_type='service',
                kpi_name='margin_percentage',
                target_min=15.0,
                target_max=40.0,
                warning_threshold=25.0,
                is_active=True
            )
            db.session.add(template)
            db.session.commit()

            # Test valore buono
            status = get_kpi_status(30.0, 'margin_percentage', 'service', project.id)
            assert 'status' in status
            assert 'color' in status
            assert status['status'] in ['good', 'warning', 'critical']

            # Test valore warning
            status = get_kpi_status(20.0, 'margin_percentage', 'service', project.id)
            assert 'status' in status
            assert status['status'] in ['good', 'warning', 'critical']

            # Test valore critico
            status = get_kpi_status(10.0, 'margin_percentage', 'service', project.id)
            assert 'status' in status
            assert status['status'] in ['good', 'warning', 'critical']

    def test_get_kpi_status_with_custom_targets(self, app, test_project):
        """Test status KPI con target personalizzati."""
        with app.app_context():
            project = Project.query.get(test_project)
            project.project_type = 'consulting'
            db.session.commit()

            # Crea template base
            template = ProjectKPITemplate(
                project_type='consulting',
                kpi_name='margin_percentage',
                target_min=25.0,
                target_max=60.0,
                warning_threshold=40.0,
                is_active=True
            )
            db.session.add(template)

            # Crea target personalizzato
            custom_target = ProjectKPITarget(
                project_id=project.id,
                kpi_name='margin_percentage',
                target_value=50.0,
                warning_threshold=35.0,
                created_by=1
            )
            db.session.add(custom_target)
            db.session.commit()

            # Test con target personalizzato
            status = get_kpi_status(45.0, 'margin_percentage', 'consulting', project.id)
            assert 'status' in status
            assert status['status'] in ['good', 'warning', 'critical']

            status = get_kpi_status(30.0, 'margin_percentage', 'consulting', project.id)
            assert 'status' in status
            assert status['status'] in ['good', 'warning', 'critical']

    def test_get_user_daily_rate_for_date(self, app, test_user):
        """Test recupero tariffa utente per data."""
        with app.app_context():
            user = test_user
            test_date = date.today()

            # Crea rate valido
            rate = PersonnelRate(
                user_id=user.id,
                daily_rate=350.0,
                valid_from=test_date - timedelta(days=10),
                valid_to=test_date + timedelta(days=10)
            )
            db.session.add(rate)
            db.session.commit()

            # Test recupero rate
            result = get_user_daily_rate_for_date(user.id, test_date)
            assert result == 350.0

            # Test data fuori range
            future_date = test_date + timedelta(days=20)
            result = get_user_daily_rate_for_date(user.id, future_date)
            # Potrebbe restituire l'ultimo rate valido o un default
            assert result >= 0.0  # Deve essere un valore valido

    def test_non_billable_project_kpis(self, app, test_project, test_user):
        """Test KPI per progetto non fatturabile."""
        with app.app_context():
            project = Project.query.get(test_project)
            project.is_billable = False
            user = test_user

            rate = PersonnelRate(
                user_id=user.id,
                daily_rate=300.0,
                valid_from=date.today() - timedelta(days=30)
            )
            db.session.add(rate)

            timesheet = TimesheetEntry(
                user_id=user.id,
                project_id=project.id,
                date=date.today(),
                hours=8.0
            )
            db.session.add(timesheet)
            db.session.commit()

            # Per progetti non fatturabili, revenue = 0
            result = calculate_project_profitability(project.id)
            assert result['revenue']['potential'] == 0.0
            # Il margin percentage potrebbe essere 0 o negativo per progetti non fatturabili
            assert result['profitability']['net_margin_percentage'] <= 0.0

            kpis = calculate_project_kpis(project.id)
            # Per progetti non fatturabili, cost_revenue_ratio potrebbe essere inf o None
            assert kpis['cost_revenue_ratio'] is None or kpis['cost_revenue_ratio'] == float('inf')

    def test_utilization_rate_calculation_methods(self, app, test_project, test_user):
        """Test diversi metodi di calcolo Utilization Rate."""
        with app.app_context():
            project = Project.query.get(test_project)
            user = test_user

            # Setup rate personale
            rate = PersonnelRate(
                user_id=user.id,
                daily_rate=400.0,
                valid_from=date.today() - timedelta(days=30)
            )
            db.session.add(rate)

            # Aggiungi 5 giorni di timesheet (5 giorni * 8 ore = 40 ore)
            for i in range(5):
                timesheet = TimesheetEntry(
                    user_id=user.id,
                    project_id=project.id,
                    date=date.today() - timedelta(days=i),
                    hours=8.0
                )
                db.session.add(timesheet)

            # TEST 1: Budget-based calculation
            project.budget = 10000.0  # €10,000 budget
            project.client_daily_rate = 500.0  # €500/giorno
            project.is_billable = True
            # Imposta date progetto per includere tutti i timesheet
            project.start_date = date.today() - timedelta(days=10)  # Prima dei timesheet
            project.end_date = date.today() + timedelta(days=10)    # Dopo i timesheet
            db.session.commit()

            kpis = calculate_project_kpis(project.id)

            # Budget: 10000 / 500 = 20 giorni teorici
            # Effettivi: 5 giorni
            # Utilization: 5/20 * 100 = 25%
            expected_utilization = (5 / 20) * 100
            assert abs(kpis['utilization_rate'] - expected_utilization) < 1.0

            # TEST 2: Timeline-based calculation
            project.budget = None  # Rimuovi budget per testare timeline
            project.start_date = date.today() - timedelta(days=20)
            project.end_date = date.today() + timedelta(days=10)  # 30 giorni totali
            db.session.commit()

            kpis = calculate_project_kpis(project.id)
            # Durata: 30 giorni
            # Giorni lavorativi: 30 * 5/7 ≈ 21.4 giorni
            # Team size: 1 persona
            # Giorni teorici: 1 * 21.4 * 0.8 ≈ 17.1 giorni
            # Utilization: 5/17.1 * 100 ≈ 29.2%
            working_days = 30 * 5/7
            expected_days = 1 * working_days * 0.8
            expected_utilization = (5 / expected_days) * 100
            assert abs(kpis['utilization_rate'] - expected_utilization) < 2.0

            # TEST 3: Fallback calculation
            project.start_date = None
            project.end_date = None
            db.session.commit()

            kpis = calculate_project_kpis(project.id)
            # Fallback: 1 persona * 22 giorni = 22 giorni teorici
            # Utilization: 5/22 * 100 ≈ 22.7%
            expected_utilization = (5 / 22) * 100
            assert abs(kpis['utilization_rate'] - expected_utilization) < 1.0
