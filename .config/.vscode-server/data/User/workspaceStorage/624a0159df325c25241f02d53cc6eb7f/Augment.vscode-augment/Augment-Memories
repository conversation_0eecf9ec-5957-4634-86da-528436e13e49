# Implementation Planning and Priorities
- User prefers detailed roadmaps before development, expects comprehensive production-ready solutions, and wants collaborative design with documentation in /docs directory
- User expects thorough codebase analysis including existing code (models, views, relationships) before making changes
- User expects me to proactively search the codebase for existing components rather than needing explicit guidance
- User expects thorough impact analysis and clear integration planning for new workflows before implementation begins
- User wants unused/dead files to be cleaned up rather than left in the codebase
- User prefers to develop all APIs first, document them thoroughly in Swagger, and then test them comprehensively
- User expects me to not invent information, but to work with existing facts and code
- User prefers to save testing strategy documentation in /docs/testing directory for project organization

# Database and Model Management
- User prefers database-first naming conventions - use database field names consistently throughout the codebase rather than creating aliases
- User prefers updating models to match existing database schema rather than migrating database
- User considers model-database schema mismatches to be serious bugs that require immediate attention
- Always follow database-first approach: prioritize DB schema as source of truth, verify API workflows before removing fields
- User prefers creating separate seed scripts for specific modules to avoid data conflicts and test specific functionality
- When seeding database, preserve existing users instead of clearing them completely
- User prefers not to use db_update file for verifying database schema and questions this approach
- The Flask application should be started with 'python main.py' instead of 'python app.py'

# Vue.js Development and Organization
- User prefers proper Vue.js Single File Components (.vue) with build system over inline template approach
- User prefers organizing Vue.js components within view directories rather than in separate global components directories
- User prefers isofunctional Vue.js migrations that maintain exact functional parity with legacy systems
- User strongly prefers to maintain SPA functionality with non-reloading page navigation and static sidebar
- The base.html template was removed; spa.html should be used universally
- User prefers blueprints to contain only API endpoints when using Vue.js SPA architecture
- Legacy code/files are not being used in the current Vue.js migration and should not be considered for fixes or imports

# UI/UX Design and Branding
- User prefers consolidated UI interfaces with tab-based pages, icons in tab headers, and URL hash fragments to maintain tab state
- User prefers no emojis in the UI and wants them replaced with proper icons
- User prefers consistent professional SVG icons from Heroicons library
- User prefers single-column vertical layouts for profile pages and department lists with pagination and proper grid sizing
- Future task will implement dynamic branding management using CSS variables for customization
- Dark mode toggle should be implemented in layout components and managed at component level

# Testing and Quality Assurance
- User prefers validating use cases through frontend testing in addition to backend tests
- User prioritizes system functionality over test correctness - tests should be adapted to match working system behavior
- User prefers using failing tests as guidance to understand what model fields and system components need to be fixed
- User wants comprehensive test coverage for all entities in the codebase eventually, not just priority ones
- User expects faster progress on comprehensive test coverage - wants more than 4 models tested out of 74 total entities
- User prefers testing workflow that verifies corrected models first before proceeding to test all remaining models systematically
- User prefers not to create duplicate tests for models that already have existing test coverage - focus on untested models instead
- User emphasizes that serious bugs are the purpose of testing - we must solve bugs, not skip them or focus on numerical KPIs over functionality.

# Module-Specific Requirements
- User prefers using Client model with states for lead management and Proposal model with states for opportunity/deal management in CRM
- User prefers tabular grid layouts for timesheet entry (tasks as rows, days as columns), monthly summary views for history
- User expects timesheet management views to include AI anomaly detection, bulk approval, hierarchical manager workflows, and analytics dashboards
- PersonnelProfile.vue should have a tab system (Projects, Tasks, Skills, Timesheet, CV) with profile completion progress bar
- The correct model name is TimesheetEntry, not Timesheet, in the codebase
- The correct API endpoint for departments is /api/personnel/departments not /api/departments/
- Billing rates and financial data should only be visible to admin and manager roles, not to regular employees