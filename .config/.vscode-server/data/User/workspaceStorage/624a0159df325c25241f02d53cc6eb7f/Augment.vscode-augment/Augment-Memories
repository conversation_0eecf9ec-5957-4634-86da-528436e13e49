# Implementation Planning and Priorities
- User prefers detailed roadmaps before starting development, expects comprehensive production-ready solutions, and wants collaborative design approach with documentation in /docs directory
- User expects thorough codebase analysis including existing code (models, views, relationships) before making changes
- User prefers isofunctional Vue.js migrations that maintain exact functional parity with legacy systems
- User expects me to proactively search the codebase for existing components rather than needing explicit guidance
- User expects thorough impact analysis and clear integration planning for new workflows before implementation begins
- User prefers using Client model with states for lead management and Proposal model with states for opportunity/deal management in CRM implementation
- User expects comprehensive testing with pytest for backend and frontend tests alongside new features
- User prefers validating use cases through frontend testing in addition to backend tests, indicating preference for comprehensive end-to-end testing coverage.
- User prefers comprehensive backend test coverage after making code modifications to ensure system stability.
- User prioritizes system functionality over test correctness - tests should be adapted to match working system behavior, not the other way around.
- User prefers using failing tests as guidance to understand what model fields and system components need to be fixed rather than guessing.
- User wants unused/dead files to be cleaned up rather than left in the codebase
- User prefers to develop all APIs first, document them thoroughly in Swagger, and then test them comprehensively
- User prefers creating separate seed scripts for specific modules (e.g., CRM-only seed with proposals, clients, contracts, projects) to avoid data conflicts and test specific functionality.
- User expects me to check existing models and APIs in @docs/task-3/ directory before creating new database models or API endpoints to avoid duplication.
- Always check existing API endpoints with codebase-retrieval, review database models in models.py, and examine @docs/task-3/ documentation before creating or modifying frontend components to ensure correct field names and data structures.
- User expects me to not invent or make up information, but to work with existing facts and code.
- User prefers debugging approach that uses existing working frontend implementations (like Projects.vue) as reference patterns for proper API integration, focuses on actual code changes rather than theoretical explanations, and ensures real data loading from APIs instead of hardcoded placeholders.
- User prefers updating models to match existing database schema rather than migrating database, because codebase likely uses existing database column names throughout the application.
- User prefers to save testing strategy documentation in /docs/testing directory for project documentation organization.
- User prefers database-first naming conventions - if a field has a specific name in the database, use that name consistently throughout the codebase rather than creating aliases.
- Always follow database-first approach: prioritize DB schema as source of truth, when removing fields from models verify if API workflows exist for those fields before making changes.
- User prefers to validate existing API and frontend flows before making changes, as existing implementations often contain sensible patterns worth preserving.

# UI/UX Design and Branding
- User prefers consolidated UI interfaces with tab-based pages, icons in tab headers, and URL hash fragments to maintain tab state
- User strongly prefers to maintain SPA functionality with non-reloading page navigation and static sidebar
- Future task will implement dynamic branding management using CSS variables for customization
- Dark mode toggle should be implemented in layout components and managed at component level
- User prefers no emojis in the UI and wants them replaced with proper icons
- User prefers single-column vertical layouts for profile pages and department lists with pagination and proper grid sizing
- User prefers consistent professional SVG icons from Heroicons library for timesheet navigation.

# Vue.js Development and Organization
- User prefers proper Vue.js Single File Components (.vue) with build system over inline template approach
- User prefers organizing Vue.js components within view directories rather than in separate global components directories
- The base.html template was removed; spa.html should be used universally
- User prefers blueprints to contain only API endpoints when using Vue.js SPA architecture
- Legacy code/files are not being used in the current Vue.js migration and should not be considered for fixes or imports
- PersonnelProfile.vue should have a tab system (Projects, Tasks, Skills, Timesheet, CV) with profile completion progress bar
- User prefers to remove duplicate timesheet functionality from personnel profile pages when dedicated timesheet views already exist elsewhere in the application.

# Task and Project Management
- Different project types should have different KPI expectations and calculations with configurable thresholds
- Projects should have billable/non-billable flag, with personnel costs valued using daily rates with validity periods
- For timesheet views, user prefers days as columns and hours in cells, plus an aggregated view with persons as rows
- The correct model name is TimesheetEntry, not Timesheet, in the codebase
- Timesheet system tracks hours on project activities or time-off without requiring daily containers - workers log hours directly on activities
- User prefers timesheet approval workflows in separate manager/admin views with dedicated routes, not within project components
- User prefers to display contract references in project views when contracts exist, showing project-contract relationships
- The project_funding_links table is used to connect funding applications to projects in the database schema.

# Timesheet Management
- User expects the timesheet menu system to include a view for employees to make timesheet-related requests, not just manager approvals and dashboards.
- User prefers separate Timesheet menu with bulk approval, AI anomaly detection, and hierarchical manager approval system
- User expects timesheet management views to include AI anomaly detection for unusual patterns, bulk approval functionality, hierarchical manager workflows, and comprehensive analytics dashboards with data visualization for productivity metrics and trends.
- User prefers no emojis in UI and wants 'Le Mie Ore' timesheet section to include vacation/leave requests and smart working requests alongside regular hour entries.
- User prefers tabular grid layouts for timesheet entry (tasks as rows, days as columns), monthly summary views for history instead of individual entries
- User expects all timesheet view filters to use existing APIs for data sources rather than hardcoded or mock data.

# Database and Technical Management
- The Flask application should be started with 'python main.py' instead of 'python app.py'
- User expects Swagger JSON documentation for every API endpoint and consistent API naming conventions
- User expects consistent database table naming without duplicates and prefers clean schema design
- User prefers not to use db_update file for verifying database schema and questions this approach.
- When seeding database, preserve existing users instead of clearing them completely

# HR Management
- Personnel module has 4 incomplete placeholder views (Organigramma, Competenze, Dipartimenti, Amministrazione) needing implementation
- User wants to integrate existing contractual data fields from UserProfile model into PersonnelAdmin.vue
- User prefers to keep 'Directory' as the sidebar navigation label for the personnel module instead of 'Team'
- Profile completion percentages should be rounded rather than showing decimals
- User prefers orgchart components with multiple visualization modes, comprehensive filters, and interactive controls
- The correct API endpoint for departments is /api/personnel/departments not /api/departments/.

# Security and Access Control
- Billing rates and financial data should only be visible to admin and manager roles, not to regular employees
- Managers must be able to view the billing rates of people assigned to their projects
- User prefers AI functionality using OpenAI API key stored in environment variables with longer timeouts for AI analysis operations