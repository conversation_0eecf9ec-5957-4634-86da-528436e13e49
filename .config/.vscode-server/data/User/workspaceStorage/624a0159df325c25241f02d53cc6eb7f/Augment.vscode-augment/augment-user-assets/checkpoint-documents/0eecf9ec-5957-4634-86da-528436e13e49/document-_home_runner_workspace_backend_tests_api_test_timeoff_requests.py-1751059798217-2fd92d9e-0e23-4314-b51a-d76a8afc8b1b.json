{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/api/test_timeoff_requests.py"}, "modifiedCode": "\"\"\"\nTest suite for TimeOffRequest API endpoints.\nTests CRUD operations, validation, and business logic for time-off request management.\n\"\"\"\n\nimport pytest\nfrom datetime import datetime, date, timedelta\nfrom models import TimeOffRequest, User\nfrom extensions import db\n\n\nclass TestTimeOffRequestsAPI:\n    \"\"\"Test suite for TimeOffRequest API endpoints\"\"\"\n\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        \"\"\"Setup test data for each test method\"\"\"\n        self.app = app\n        self.user = test_user\n        \n        # Create test time-off request data\n        self.timeoff_data = {\n            'user_id': self.user.id,\n            'type': 'vacation',\n            'start_date': (date.today() + timedelta(days=30)).isoformat(),\n            'end_date': (date.today() + timedelta(days=35)).isoformat(),\n            'reason': 'Annual vacation to Italy',\n            'status': 'pending'\n        }\n\n    def test_get_timeoff_requests_success(self, client):\n        \"\"\"Test successful retrieval of time-off requests list\"\"\"\n        # Create test requests\n        request1 = TimeOffRequest(\n            user_id=self.user.id,\n            type='vacation',\n            start_date=date.today() + timedelta(days=10),\n            end_date=date.today() + timedelta(days=15),\n            reason='Summer vacation',\n            status='pending'\n        )\n        request2 = TimeOffRequest(\n            user_id=self.user.id,\n            type='sick',\n            start_date=date.today() + timedelta(days=20),\n            end_date=date.today() + timedelta(days=22),\n            reason='Medical appointment',\n            status='approved'\n        )\n        db.session.add_all([request1, request2])\n        db.session.commit()\n\n        response = client.get('/api/timeoff/requests')\n        \n        assert response.status_code in [200, 401]\n        if response.status_code == 200:\n            data = response.get_json()\n            assert 'requests' in str(data).lower() or 'data' in data\n\n    def test_create_timeoff_request_success(self, client):\n        \"\"\"Test successful time-off request creation\"\"\"\n        response = client.post('/api/timeoff/requests', json=self.timeoff_data)\n        \n        assert response.status_code in [201, 200, 401, 403]\n        if response.status_code in [200, 201]:\n            # Verify request was created\n            created_request = TimeOffRequest.query.filter_by(\n                user_id=self.user.id,\n                type='vacation'\n            ).first()\n            if created_request:\n                assert created_request.reason == 'Annual vacation to Italy'\n\n    def test_create_timeoff_request_validation_error(self, client):\n        \"\"\"Test time-off request creation with missing required fields\"\"\"\n        invalid_data = {'type': 'vacation'}  # Missing required fields\n        \n        response = client.post('/api/timeoff/requests', json=invalid_data)\n        \n        assert response.status_code in [400, 422, 401, 403]\n\n    def test_update_timeoff_request_success(self, client):\n        \"\"\"Test successful time-off request update\"\"\"\n        test_request = TimeOffRequest(\n            user_id=self.user.id,\n            type='vacation',\n            start_date=date.today() + timedelta(days=10),\n            end_date=date.today() + timedelta(days=15),\n            reason='Original reason',\n            status='pending'\n        )\n        db.session.add(test_request)\n        db.session.commit()\n\n        update_data = {\n            'reason': 'Updated vacation reason',\n            'end_date': (date.today() + timedelta(days=17)).isoformat()\n        }\n        \n        response = client.put(f'/api/timeoff/requests/{test_request.id}', json=update_data)\n        \n        assert response.status_code in [200, 401, 403, 404]\n\n    def test_delete_timeoff_request_success(self, client):\n        \"\"\"Test successful time-off request deletion\"\"\"\n        test_request = TimeOffRequest(\n            user_id=self.user.id,\n            type='personal',\n            start_date=date.today() + timedelta(days=5),\n            end_date=date.today() + timedelta(days=6),\n            reason='Personal matter',\n            status='pending'\n        )\n        db.session.add(test_request)\n        db.session.commit()\n        request_id = test_request.id\n\n        response = client.delete(f'/api/timeoff/requests/{request_id}')\n        \n        assert response.status_code in [200, 401, 403, 404]\n\n    def test_timeoff_request_date_validation(self, client):\n        \"\"\"Test time-off request date validation\"\"\"\n        # Test end_date before start_date\n        invalid_data = self.timeoff_data.copy()\n        invalid_data['start_date'] = (date.today() + timedelta(days=10)).isoformat()\n        invalid_data['end_date'] = (date.today() + timedelta(days=5)).isoformat()\n        \n        response = client.post('/api/timeoff/requests', json=invalid_data)\n        assert response.status_code in [400, 422, 401, 403, 201, 200]\n        \n        # Test past dates\n        invalid_data = self.timeoff_data.copy()\n        invalid_data['start_date'] = (date.today() - timedelta(days=5)).isoformat()\n        invalid_data['end_date'] = (date.today() - timedelta(days=3)).isoformat()\n        \n        response = client.post('/api/timeoff/requests', json=invalid_data)\n        assert response.status_code in [400, 422, 401, 403, 201, 200]\n\n    def test_timeoff_request_type_validation(self, client):\n        \"\"\"Test time-off request type validation\"\"\"\n        valid_types = ['vacation', 'sick', 'personal', 'maternity', 'paternity', 'bereavement']\n        \n        for request_type in valid_types:\n            test_data = self.timeoff_data.copy()\n            test_data['type'] = request_type\n            test_data['start_date'] = (date.today() + timedelta(days=30 + len(valid_types))).isoformat()\n            test_data['end_date'] = (date.today() + timedelta(days=32 + len(valid_types))).isoformat()\n            \n            response = client.post('/api/timeoff/requests', json=test_data)\n            assert response.status_code in [201, 200, 401, 403, 400, 422]\n\n    def test_timeoff_request_status_workflow(self, client):\n        \"\"\"Test time-off request status transitions\"\"\"\n        test_request = TimeOffRequest(\n            user_id=self.user.id,\n            type='vacation',\n            start_date=date.today() + timedelta(days=20),\n            end_date=date.today() + timedelta(days=25),\n            reason='Status test',\n            status='pending'\n        )\n        db.session.add(test_request)\n        db.session.commit()\n\n        # Test status transitions\n        status_transitions = ['approved', 'rejected', 'cancelled']\n        \n        for status in status_transitions:\n            response = client.put(\n                f'/api/timeoff/requests/{test_request.id}', \n                json={'status': status}\n            )\n            assert response.status_code in [200, 401, 403, 404, 400]\n\n    def test_approve_timeoff_request(self, client):\n        \"\"\"Test time-off request approval\"\"\"\n        test_request = TimeOffRequest(\n            user_id=self.user.id,\n            type='vacation',\n            start_date=date.today() + timedelta(days=15),\n            end_date=date.today() + timedelta(days=20),\n            reason='Approval test',\n            status='pending'\n        )\n        db.session.add(test_request)\n        db.session.commit()\n\n        response = client.put(f'/api/timeoff/requests/{test_request.id}/approve')\n        assert response.status_code in [200, 401, 403, 404]\n\n    def test_reject_timeoff_request(self, client):\n        \"\"\"Test time-off request rejection\"\"\"\n        test_request = TimeOffRequest(\n            user_id=self.user.id,\n            type='personal',\n            start_date=date.today() + timedelta(days=10),\n            end_date=date.today() + timedelta(days=12),\n            reason='Rejection test',\n            status='pending'\n        )\n        db.session.add(test_request)\n        db.session.commit()\n\n        rejection_data = {'reason': 'Insufficient notice period'}\n        response = client.put(\n            f'/api/timeoff/requests/{test_request.id}/reject', \n            json=rejection_data\n        )\n        assert response.status_code in [200, 401, 403, 404]\n\n    def test_timeoff_request_search_and_filters(self, client):\n        \"\"\"Test time-off request search and filtering\"\"\"\n        # Create test requests with different types and statuses\n        request1 = TimeOffRequest(\n            user_id=self.user.id,\n            type='vacation',\n            start_date=date.today() + timedelta(days=10),\n            end_date=date.today() + timedelta(days=15),\n            reason='Vacation request',\n            status='pending'\n        )\n        request2 = TimeOffRequest(\n            user_id=self.user.id,\n            type='sick',\n            start_date=date.today() + timedelta(days=20),\n            end_date=date.today() + timedelta(days=22),\n            reason='Sick leave',\n            status='approved'\n        )\n        db.session.add_all([request1, request2])\n        db.session.commit()\n\n        # Test status filter\n        response = client.get('/api/timeoff/requests?status=pending')\n        assert response.status_code in [200, 401]\n        \n        # Test type filter\n        response = client.get('/api/timeoff/requests?type=vacation')\n        assert response.status_code in [200, 401]\n        \n        # Test user filter\n        response = client.get(f'/api/timeoff/requests?user_id={self.user.id}')\n        assert response.status_code in [200, 401]\n\n    def test_get_timeoff_request_detail(self, client):\n        \"\"\"Test single time-off request retrieval\"\"\"\n        test_request = TimeOffRequest(\n            user_id=self.user.id,\n            type='vacation',\n            start_date=date.today() + timedelta(days=30),\n            end_date=date.today() + timedelta(days=35),\n            reason='Detail test vacation',\n            status='pending'\n        )\n        db.session.add(test_request)\n        db.session.commit()\n\n        response = client.get(f'/api/timeoff/requests/{test_request.id}')\n        assert response.status_code in [200, 401, 404]\n\n    def test_timeoff_request_not_found(self, client):\n        \"\"\"Test time-off request not found scenarios\"\"\"\n        response = client.get('/api/timeoff/requests/99999')\n        assert response.status_code in [404, 401]\n        \n        response = client.put('/api/timeoff/requests/99999', json={'reason': 'Test'})\n        assert response.status_code in [404, 401]\n        \n        response = client.delete('/api/timeoff/requests/99999')\n        assert response.status_code in [404, 401]\n\n    def test_timeoff_request_overlapping_periods(self, client):\n        \"\"\"Test handling of overlapping time-off periods\"\"\"\n        # Create existing request\n        existing_request = TimeOffRequest(\n            user_id=self.user.id,\n            type='vacation',\n            start_date=date.today() + timedelta(days=10),\n            end_date=date.today() + timedelta(days=15),\n            reason='Existing vacation',\n            status='approved'\n        )\n        db.session.add(existing_request)\n        db.session.commit()\n\n        # Try to create overlapping request\n        overlapping_data = self.timeoff_data.copy()\n        overlapping_data['start_date'] = (date.today() + timedelta(days=12)).isoformat()\n        overlapping_data['end_date'] = (date.today() + timedelta(days=17)).isoformat()\n        \n        response = client.post('/api/timeoff/requests', json=overlapping_data)\n        assert response.status_code in [400, 409, 401, 403, 201, 200]\n\n    def test_timeoff_request_pagination(self, client):\n        \"\"\"Test time-off request list pagination\"\"\"\n        # Create multiple requests\n        for i in range(5):\n            request = TimeOffRequest(\n                user_id=self.user.id,\n                type='vacation',\n                start_date=date.today() + timedelta(days=10 + i * 7),\n                end_date=date.today() + timedelta(days=12 + i * 7),\n                reason=f'Vacation {i}',\n                status='pending'\n            )\n            db.session.add(request)\n        db.session.commit()\n\n        response = client.get('/api/timeoff/requests?page=1&per_page=3')\n        assert response.status_code in [200, 401]\n"}