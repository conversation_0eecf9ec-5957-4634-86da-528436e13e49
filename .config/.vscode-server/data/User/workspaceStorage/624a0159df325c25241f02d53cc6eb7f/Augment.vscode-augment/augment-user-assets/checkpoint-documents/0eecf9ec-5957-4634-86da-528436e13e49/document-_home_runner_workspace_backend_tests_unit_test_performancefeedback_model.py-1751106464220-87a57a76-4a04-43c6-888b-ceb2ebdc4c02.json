{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_performancefeedback_model.py"}, "originalCode": "\"\"\"Unit tests for PerformanceFeedback model.\"\"\"\nimport pytest\nfrom models import PerformanceFeedback, User\nfrom extensions import db\n\nclass TestPerformanceFeedbackModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n\n    def test_performancefeedback_creation_basic(self):\n        feedback = PerformanceFeedback(\n            from_user_id=self.user.id,  # Campo corretto è 'from_user_id'\n            to_user_id=self.user.id,    # Campo corretto è 'to_user_id'\n            feedback_type='peer',       # Campo richiesto\n            content='Great work!',      # <PERSON> corretto è 'content'\n            rating=5\n        )\n        db.session.add(feedback)\n        db.session.commit()\n        \n        assert feedback.id is not None\n        assert feedback.feedback_text == 'Great work!'\n        assert feedback.rating == 5\n\n    def test_performancefeedback_deletion(self):\n        feedback = PerformanceFeedback(employee_id=self.user.id, feedback_giver_id=self.user.id)\n        db.session.add(feedback)\n        db.session.commit()\n        feedback_id = feedback.id\n        \n        db.session.delete(feedback)\n        db.session.commit()\n        \n        deleted = PerformanceFeedback.query.get(feedback_id)\n        assert deleted is None\n", "modifiedCode": "\"\"\"Unit tests for PerformanceFeedback model.\"\"\"\nimport pytest\nfrom models import PerformanceFeedback, User\nfrom extensions import db\n\nclass TestPerformanceFeedbackModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n\n    def test_performancefeedback_creation_basic(self):\n        feedback = PerformanceFeedback(\n            from_user_id=self.user.id,  # Campo corretto è 'from_user_id'\n            to_user_id=self.user.id,    # Campo corretto è 'to_user_id'\n            feedback_type='peer',       # Campo richiesto\n            content='Great work!',      # <PERSON> corretto è 'content'\n            rating=5\n        )\n        db.session.add(feedback)\n        db.session.commit()\n        \n        assert feedback.id is not None\n        assert feedback.feedback_text == 'Great work!'\n        assert feedback.rating == 5\n\n    def test_performancefeedback_deletion(self):\n        feedback = PerformanceFeedback(employee_id=self.user.id, feedback_giver_id=self.user.id)\n        db.session.add(feedback)\n        db.session.commit()\n        feedback_id = feedback.id\n        \n        db.session.delete(feedback)\n        db.session.commit()\n        \n        deleted = PerformanceFeedback.query.get(feedback_id)\n        assert deleted is None\n"}