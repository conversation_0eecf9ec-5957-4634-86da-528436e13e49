{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_poll_model.py"}, "originalCode": "\"\"\"Unit tests for Poll model.\"\"\"\nimport pytest\nfrom datetime import datetime\nfrom models import Poll, User\nfrom extensions import db\n\nclass TestPollModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n\n    def test_poll_creation_basic(self):\n        poll = Poll(\n            title='Test Poll',\n            description='This is a test poll',\n            author_id=self.user.id  # Campo corretto è 'author_id'\n        )\n        db.session.add(poll)\n        db.session.commit()\n\n        assert poll.id is not None\n        assert poll.title == 'Test Poll'\n        assert poll.description == 'This is a test poll'\n        assert poll.author_id == self.user.id\n\n    def test_poll_multiple_choice(self):\n        poll = Poll(\n            title='Multiple Choice Poll',\n            description='Choose multiple options',\n            created_by=self.user.id,\n            multiple_choice=True\n        )\n        db.session.add(poll)\n        db.session.commit()\n        \n        assert poll.multiple_choice is True\n\n    def test_poll_deletion(self):\n        poll = Poll(\n            title='To Delete',\n            description='This will be deleted',\n            created_by=self.user.id\n        )\n        db.session.add(poll)\n        db.session.commit()\n        poll_id = poll.id\n        \n        db.session.delete(poll)\n        db.session.commit()\n        \n        deleted = Poll.query.get(poll_id)\n        assert deleted is None\n", "modifiedCode": "\"\"\"Unit tests for Poll model.\"\"\"\nimport pytest\nfrom datetime import datetime\nfrom models import Poll, User\nfrom extensions import db\n\nclass TestPollModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n\n    def test_poll_creation_basic(self):\n        poll = Poll(\n            title='Test Poll',\n            description='This is a test poll',\n            author_id=self.user.id  # Campo corretto è 'author_id'\n        )\n        db.session.add(poll)\n        db.session.commit()\n\n        assert poll.id is not None\n        assert poll.title == 'Test Poll'\n        assert poll.description == 'This is a test poll'\n        assert poll.author_id == self.user.id\n\n    def test_poll_multiple_choice(self):\n        poll = Poll(\n            title='Multiple Choice Poll',\n            description='Choose multiple options',\n            author_id=self.user.id,  # <PERSON> corretto è 'author_id'\n            multiple_choice=True\n        )\n        db.session.add(poll)\n        db.session.commit()\n        \n        assert poll.multiple_choice is True\n\n    def test_poll_deletion(self):\n        poll = Poll(\n            title='To Delete',\n            description='This will be deleted',\n            created_by=self.user.id\n        )\n        db.session.add(poll)\n        db.session.commit()\n        poll_id = poll.id\n        \n        db.session.delete(poll)\n        db.session.commit()\n        \n        deleted = Poll.query.get(poll_id)\n        assert deleted is None\n"}