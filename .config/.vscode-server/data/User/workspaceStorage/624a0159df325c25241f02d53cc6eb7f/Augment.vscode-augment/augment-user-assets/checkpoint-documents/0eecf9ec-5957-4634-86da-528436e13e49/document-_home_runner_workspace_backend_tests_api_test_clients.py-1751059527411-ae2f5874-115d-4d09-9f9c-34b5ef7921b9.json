{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/api/test_clients.py"}, "modifiedCode": "\"\"\"\nTest suite for Clients API endpoints.\nTests CRUD operations, validation, and business logic for client management.\n\"\"\"\n\nimport pytest\nfrom datetime import datetime\nfrom flask import url_for\nfrom models import Client, User\nfrom extensions import db\n\n\nclass TestClientsAPI:\n    \"\"\"Test suite for Clients API endpoints\"\"\"\n\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        \"\"\"Setup test data for each test method\"\"\"\n        self.app = app\n        self.user = test_user\n        \n        # Create test client data\n        self.client_data = {\n            'name': 'Test Company Ltd',\n            'description': 'A test company for API testing',\n            'industry': 'Technology',\n            'website': 'https://testcompany.com',\n            'email': '<EMAIL>',\n            'phone': '+39 ************',\n            'address': 'Via Test 123, Milano, Italy',\n            'vat_number': '*************',\n            'fiscal_code': '****************',\n            'status': 'active'\n        }\n\n    def test_get_clients_success(self, client):\n        \"\"\"Test successful retrieval of clients list\"\"\"\n        # Create test clients\n        client1 = Client(name='Client 1', status='active')\n        client2 = Client(name='Client 2', status='active')\n        db.session.add_all([client1, client2])\n        db.session.commit()\n\n        response = client.get('/api/clients')\n        \n        assert response.status_code == 200\n        data = response.get_json()\n        assert data['success'] is True\n        assert len(data['data']['clients']) >= 2\n        assert any(c['name'] == 'Client 1' for c in data['data']['clients'])\n\n    def test_get_clients_with_filters(self, client):\n        \"\"\"Test clients list with status filter\"\"\"\n        # Create clients with different statuses\n        active_client = Client(name='Active Client', status='active')\n        inactive_client = Client(name='Inactive Client', status='inactive')\n        db.session.add_all([active_client, inactive_client])\n        db.session.commit()\n\n        response = client.get('/api/clients?status=active')\n        \n        assert response.status_code == 200\n        data = response.get_json()\n        clients = data['data']['clients']\n        assert all(c['status'] == 'active' for c in clients)\n\n    def test_get_client_detail_success(self, client):\n        \"\"\"Test successful retrieval of single client\"\"\"\n        test_client = Client(**self.client_data)\n        db.session.add(test_client)\n        db.session.commit()\n\n        response = client.get(f'/api/clients/{test_client.id}')\n        \n        assert response.status_code == 200\n        data = response.get_json()\n        assert data['success'] is True\n        assert data['data']['client']['name'] == 'Test Company Ltd'\n        assert data['data']['client']['email'] == '<EMAIL>'\n\n    def test_get_client_not_found(self, client):\n        \"\"\"Test client not found error\"\"\"\n        response = client.get('/api/clients/99999')\n        \n        assert response.status_code == 404\n        data = response.get_json()\n        assert data['success'] is False\n\n    def test_create_client_success(self, client):\n        \"\"\"Test successful client creation\"\"\"\n        response = client.post('/api/clients', json=self.client_data)\n        \n        assert response.status_code in [201, 200]\n        data = response.get_json()\n        assert data['success'] is True\n        \n        # Verify client was created in database\n        created_client = Client.query.filter_by(name='Test Company Ltd').first()\n        assert created_client is not None\n        assert created_client.email == '<EMAIL>'\n\n    def test_create_client_validation_error(self, client):\n        \"\"\"Test client creation with missing required fields\"\"\"\n        invalid_data = {'description': 'Missing name field'}\n        \n        response = client.post('/api/clients', json=invalid_data)\n        \n        assert response.status_code in [400, 422]\n        data = response.get_json()\n        assert data['success'] is False\n\n    def test_create_client_duplicate_name(self, client):\n        \"\"\"Test client creation with duplicate name\"\"\"\n        # Create first client\n        existing_client = Client(name='Duplicate Name', status='active')\n        db.session.add(existing_client)\n        db.session.commit()\n\n        # Try to create client with same name\n        duplicate_data = self.client_data.copy()\n        duplicate_data['name'] = 'Duplicate Name'\n        \n        response = client.post('/api/clients', json=duplicate_data)\n        \n        assert response.status_code in [400, 409]\n        data = response.get_json()\n        assert data['success'] is False\n\n    def test_update_client_success(self, client):\n        \"\"\"Test successful client update\"\"\"\n        test_client = Client(**self.client_data)\n        db.session.add(test_client)\n        db.session.commit()\n\n        update_data = {\n            'name': 'Updated Company Name',\n            'email': '<EMAIL>',\n            'status': 'inactive'\n        }\n        \n        response = client.put(f'/api/clients/{test_client.id}', json=update_data)\n        \n        assert response.status_code == 200\n        data = response.get_json()\n        assert data['success'] is True\n        \n        # Verify update in database\n        updated_client = Client.query.get(test_client.id)\n        assert updated_client.name == 'Updated Company Name'\n        assert updated_client.email == '<EMAIL>'\n        assert updated_client.status == 'inactive'\n\n    def test_update_client_not_found(self, client):\n        \"\"\"Test update of non-existent client\"\"\"\n        update_data = {'name': 'Updated Name'}\n        \n        response = client.put('/api/clients/99999', json=update_data)\n        \n        assert response.status_code == 404\n        data = response.get_json()\n        assert data['success'] is False\n\n    def test_delete_client_success(self, client):\n        \"\"\"Test successful client deletion\"\"\"\n        test_client = Client(**self.client_data)\n        db.session.add(test_client)\n        db.session.commit()\n        client_id = test_client.id\n\n        response = client.delete(f'/api/clients/{client_id}')\n        \n        assert response.status_code == 200\n        data = response.get_json()\n        assert data['success'] is True\n        \n        # Verify deletion in database\n        deleted_client = Client.query.get(client_id)\n        assert deleted_client is None\n\n    def test_delete_client_not_found(self, client):\n        \"\"\"Test deletion of non-existent client\"\"\"\n        response = client.delete('/api/clients/99999')\n        \n        assert response.status_code == 404\n        data = response.get_json()\n        assert data['success'] is False\n\n    def test_client_search(self, client):\n        \"\"\"Test client search functionality\"\"\"\n        # Create test clients\n        client1 = Client(name='Tech Solutions Inc', industry='Technology')\n        client2 = Client(name='Marketing Agency', industry='Marketing')\n        client3 = Client(name='Tech Innovations', industry='Technology')\n        db.session.add_all([client1, client2, client3])\n        db.session.commit()\n\n        # Search by name\n        response = client.get('/api/clients?search=Tech')\n        \n        assert response.status_code == 200\n        data = response.get_json()\n        clients = data['data']['clients']\n        assert len(clients) >= 2\n        assert all('Tech' in c['name'] for c in clients)\n\n    def test_client_pagination(self, client):\n        \"\"\"Test client list pagination\"\"\"\n        # Create multiple clients\n        for i in range(15):\n            test_client = Client(name=f'Client {i}', status='active')\n            db.session.add(test_client)\n        db.session.commit()\n\n        # Test first page\n        response = client.get('/api/clients?page=1&per_page=10')\n        \n        assert response.status_code == 200\n        data = response.get_json()\n        assert len(data['data']['clients']) == 10\n        assert 'pagination' in data['data']\n        assert data['data']['pagination']['page'] == 1\n\n    def test_clients_unauthorized(self, client):\n        \"\"\"Test clients access without authentication\"\"\"\n        # This test assumes authentication is required\n        # Adjust based on your actual authentication setup\n        response = client.get('/api/clients')\n        \n        # Should either work (if no auth required) or return 401\n        assert response.status_code in [200, 401]\n\n    def test_client_response_format(self, client):\n        \"\"\"Test that client response includes all expected fields\"\"\"\n        test_client = Client(**self.client_data)\n        db.session.add(test_client)\n        db.session.commit()\n\n        response = client.get(f'/api/clients/{test_client.id}')\n        \n        assert response.status_code == 200\n        data = response.get_json()\n        client_data = data['data']['client']\n        \n        # Check required fields are present\n        required_fields = ['id', 'name', 'status', 'created_at']\n        for field in required_fields:\n            assert field in client_data\n"}