{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/db/validate_models_vs_database.py"}, "modifiedCode": "#!/usr/bin/env python3\n\"\"\"\nScript per validare la coerenza tra modelli Python e database reale.\nIdentifica discrepanze dopo il refactoring automatico.\n\"\"\"\n\nimport sys\nimport importlib\nimport sqlalchemy as sa\nfrom sqlalchemy import inspect\nfrom app import create_app\nfrom extensions import db\n\ndef get_model_tables():\n    \"\"\"Ottieni tutte le tabelle definite nei modelli Python\"\"\"\n    model_tables = {}\n    \n    # Import all models to register them with SQLAlchemy\n    try:\n        import models  # This imports all models\n        \n        # Get all tables from SQLAlchemy metadata\n        for table_name, table in db.metadata.tables.items():\n            columns = {}\n            for column in table.columns:\n                columns[column.name] = {\n                    'type': str(column.type),\n                    'nullable': column.nullable,\n                    'primary_key': column.primary_key,\n                    'foreign_key': bool(column.foreign_keys),\n                    'default': str(column.default) if column.default else None\n                }\n            model_tables[table_name] = columns\n            \n    except Exception as e:\n        print(f\"❌ Errore nell'importazione dei modelli: {e}\")\n        return {}\n    \n    return model_tables\n\ndef get_database_tables():\n    \"\"\"Ottieni tutte le tabelle presenti nel database\"\"\"\n    db_tables = {}\n    \n    try:\n        inspector = inspect(db.engine)\n        \n        for table_name in inspector.get_table_names():\n            columns = {}\n            for column in inspector.get_columns(table_name):\n                columns[column['name']] = {\n                    'type': str(column['type']),\n                    'nullable': column['nullable'],\n                    'primary_key': column.get('primary_key', False),\n                    'foreign_key': bool(column.get('foreign_keys', [])),\n                    'default': str(column['default']) if column['default'] else None\n                }\n            db_tables[table_name] = columns\n            \n    except Exception as e:\n        print(f\"❌ Errore nell'analisi del database: {e}\")\n        return {}\n    \n    return db_tables\n\ndef find_duplicate_tables(db_tables):\n    \"\"\"Trova tabelle duplicate (singolare/plurale)\"\"\"\n    duplicates = []\n    table_names = list(db_tables.keys())\n    \n    for table in table_names:\n        # Check for plural version\n        if table.endswith('s'):\n            singular = table[:-1]\n            if singular in table_names:\n                duplicates.append((singular, table))\n        else:\n            plural = table + 's'\n            if plural in table_names:\n                duplicates.append((table, plural))\n    \n    # Remove duplicates from list\n    seen = set()\n    unique_duplicates = []\n    for dup in duplicates:\n        sorted_dup = tuple(sorted(dup))\n        if sorted_dup not in seen:\n            seen.add(sorted_dup)\n            unique_duplicates.append(dup)\n    \n    return unique_duplicates\n\ndef compare_tables(model_tables, db_tables):\n    \"\"\"Confronta tabelle tra modelli e database\"\"\"\n    issues = {\n        'missing_in_db': [],\n        'missing_in_models': [],\n        'column_mismatches': [],\n        'duplicates': []\n    }\n    \n    # Find tables missing in database\n    for table_name in model_tables:\n        if table_name not in db_tables:\n            issues['missing_in_db'].append(table_name)\n    \n    # Find tables missing in models\n    for table_name in db_tables:\n        if table_name not in model_tables:\n            issues['missing_in_models'].append(table_name)\n    \n    # Find column mismatches in common tables\n    common_tables = set(model_tables.keys()) & set(db_tables.keys())\n    \n    for table_name in common_tables:\n        model_cols = model_tables[table_name]\n        db_cols = db_tables[table_name]\n        \n        table_issues = {\n            'table': table_name,\n            'missing_in_db': [],\n            'missing_in_model': [],\n            'type_mismatches': []\n        }\n        \n        # Check for missing columns\n        for col_name in model_cols:\n            if col_name not in db_cols:\n                table_issues['missing_in_db'].append(col_name)\n        \n        for col_name in db_cols:\n            if col_name not in model_cols:\n                table_issues['missing_in_model'].append(col_name)\n        \n        # Check for type mismatches in common columns\n        common_cols = set(model_cols.keys()) & set(db_cols.keys())\n        for col_name in common_cols:\n            model_type = model_cols[col_name]['type']\n            db_type = db_cols[col_name]['type']\n            \n            # Normalize types for comparison\n            if model_type != db_type:\n                table_issues['type_mismatches'].append({\n                    'column': col_name,\n                    'model_type': model_type,\n                    'db_type': db_type\n                })\n        \n        # Only add if there are issues\n        if any([table_issues['missing_in_db'], table_issues['missing_in_model'], \n                table_issues['type_mismatches']]):\n            issues['column_mismatches'].append(table_issues)\n    \n    # Find duplicate tables\n    issues['duplicates'] = find_duplicate_tables(db_tables)\n    \n    return issues\n\ndef print_validation_report(issues, model_tables, db_tables):\n    \"\"\"Stampa report di validazione\"\"\"\n    print(\"🔍 REPORT VALIDAZIONE MODELLI vs DATABASE\")\n    print(\"=\" * 60)\n    \n    print(f\"\\n📊 STATISTICHE:\")\n    print(f\"  - Tabelle nei modelli: {len(model_tables)}\")\n    print(f\"  - Tabelle nel database: {len(db_tables)}\")\n    print(f\"  - Tabelle comuni: {len(set(model_tables.keys()) & set(db_tables.keys()))}\")\n    \n    # Duplicate tables\n    if issues['duplicates']:\n        print(f\"\\n🚨 TABELLE DUPLICATE ({len(issues['duplicates'])}):\")\n        for singular, plural in issues['duplicates']:\n            print(f\"  ❌ {singular} + {plural}\")\n    \n    # Missing tables\n    if issues['missing_in_db']:\n        print(f\"\\n❌ TABELLE MANCANTI NEL DATABASE ({len(issues['missing_in_db'])}):\")\n        for table in sorted(issues['missing_in_db']):\n            print(f\"  - {table}\")\n    \n    if issues['missing_in_models']:\n        print(f\"\\n❌ TABELLE MANCANTI NEI MODELLI ({len(issues['missing_in_models'])}):\")\n        for table in sorted(issues['missing_in_models']):\n            print(f\"  - {table}\")\n    \n    # Column mismatches\n    if issues['column_mismatches']:\n        print(f\"\\n🔧 DISCREPANZE COLONNE ({len(issues['column_mismatches'])} tabelle):\")\n        for table_issue in issues['column_mismatches']:\n            table_name = table_issue['table']\n            print(f\"\\n  📋 {table_name.upper()}:\")\n            \n            if table_issue['missing_in_db']:\n                print(f\"    ❌ Colonne mancanti nel DB:\")\n                for col in table_issue['missing_in_db']:\n                    print(f\"      - {col}\")\n            \n            if table_issue['missing_in_model']:\n                print(f\"    ❌ Colonne mancanti nel modello:\")\n                for col in table_issue['missing_in_model']:\n                    print(f\"      - {col}\")\n            \n            if table_issue['type_mismatches']:\n                print(f\"    ⚠️  Tipi diversi:\")\n                for mismatch in table_issue['type_mismatches']:\n                    print(f\"      - {mismatch['column']}: {mismatch['model_type']} vs {mismatch['db_type']}\")\n    \n    # Summary\n    total_issues = (len(issues['duplicates']) + len(issues['missing_in_db']) + \n                   len(issues['missing_in_models']) + len(issues['column_mismatches']))\n    \n    print(f\"\\n📈 RIEPILOGO:\")\n    print(f\"  - Tabelle duplicate: {len(issues['duplicates'])}\")\n    print(f\"  - Tabelle mancanti nel DB: {len(issues['missing_in_db'])}\")\n    print(f\"  - Tabelle mancanti nei modelli: {len(issues['missing_in_models'])}\")\n    print(f\"  - Tabelle con discrepanze colonne: {len(issues['column_mismatches'])}\")\n    print(f\"  - TOTALE PROBLEMI: {total_issues}\")\n    \n    if total_issues == 0:\n        print(\"\\n✅ NESSUN PROBLEMA RILEVATO!\")\n    else:\n        print(f\"\\n🚨 ATTENZIONE: {total_issues} problemi da risolvere!\")\n\ndef main():\n    \"\"\"Main function\"\"\"\n    app = create_app()\n    \n    with app.app_context():\n        print(\"🔍 Analizzando modelli e database...\")\n        \n        # Get table definitions\n        model_tables = get_model_tables()\n        db_tables = get_database_tables()\n        \n        if not model_tables:\n            print(\"❌ Impossibile caricare i modelli!\")\n            return\n        \n        if not db_tables:\n            print(\"❌ Impossibile analizzare il database!\")\n            return\n        \n        # Compare and find issues\n        issues = compare_tables(model_tables, db_tables)\n        \n        # Print report\n        print_validation_report(issues, model_tables, db_tables)\n\nif __name__ == '__main__':\n    main()\n"}