{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_invoice_model.py"}, "modifiedCode": "\"\"\"Unit tests for Invoice model.\"\"\"\nimport pytest\nfrom datetime import date\nfrom models import Invoice, Client, User\nfrom extensions import db\n\nclass TestInvoiceModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n        \n        self.client = Client.query.first()\n        if not self.client:\n            self.client = Client(name='Test Client', email='<EMAIL>')\n            db.session.add(self.client)\n            db.session.commit()\n\n    def test_invoice_creation_basic(self):\n        invoice = Invoice(\n            invoice_number='INV-001',\n            client_id=self.client.id,\n            amount=5000.0,\n            issue_date=date.today(),\n            created_by=self.user.id\n        )\n        db.session.add(invoice)\n        db.session.commit()\n        \n        assert invoice.id is not None\n        assert invoice.invoice_number == 'INV-001'\n        assert invoice.amount == 5000.0\n\n    def test_invoice_deletion(self):\n        invoice = Invoice(invoice_number='INV-DEL', client_id=self.client.id, amount=100.0, created_by=self.user.id)\n        db.session.add(invoice)\n        db.session.commit()\n        invoice_id = invoice.id\n        \n        db.session.delete(invoice)\n        db.session.commit()\n        \n        deleted = Invoice.query.get(invoice_id)\n        assert deleted is None\n"}