{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_communicationreaction_model.py"}, "modifiedCode": "\"\"\"Unit tests for CommunicationReaction model.\"\"\"\nimport pytest\nfrom models import CommunicationReaction, User\nfrom extensions import db\n\nclass TestCommunicationReactionModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n\n    def test_communicationreaction_creation_basic(self):\n        reaction = CommunicationReaction(\n            user_id=self.user.id,\n            content_type='post',\n            content_id=123,\n            reaction_type='like'\n        )\n        db.session.add(reaction)\n        db.session.commit()\n        \n        assert reaction.id is not None\n        assert reaction.user_id == self.user.id\n        assert reaction.content_type == 'post'\n        assert reaction.content_id == 123\n        assert reaction.reaction_type == 'like'\n\n    def test_communicationreaction_types(self):\n        reaction_types = ['like', 'love', 'laugh', 'angry', 'sad']\n        reactions = []\n        \n        for i, reaction_type in enumerate(reaction_types):\n            reaction = CommunicationReaction(\n                user_id=self.user.id,\n                content_type='comment',\n                content_id=i+1,\n                reaction_type=reaction_type\n            )\n            reactions.append(reaction)\n        \n        db.session.add_all(reactions)\n        db.session.commit()\n        \n        for reaction, expected_type in zip(reactions, reaction_types):\n            assert reaction.reaction_type == expected_type\n\n    def test_communicationreaction_deletion(self):\n        reaction = CommunicationReaction(\n            user_id=self.user.id,\n            content_type='post',\n            content_id=1,\n            reaction_type='like'\n        )\n        db.session.add(reaction)\n        db.session.commit()\n        reaction_id = reaction.id\n        \n        db.session.delete(reaction)\n        db.session.commit()\n        \n        deleted = CommunicationReaction.query.get(reaction_id)\n        assert deleted is None\n"}