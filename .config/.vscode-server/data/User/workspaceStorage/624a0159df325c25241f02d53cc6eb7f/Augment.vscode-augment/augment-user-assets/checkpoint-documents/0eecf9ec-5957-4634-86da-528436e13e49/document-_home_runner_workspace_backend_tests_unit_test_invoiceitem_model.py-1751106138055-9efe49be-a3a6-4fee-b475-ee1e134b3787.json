{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_invoiceitem_model.py"}, "modifiedCode": "\"\"\"Unit tests for InvoiceItem model.\"\"\"\nimport pytest\nfrom models import InvoiceItem, Invoice, Client, User\nfrom extensions import db\n\nclass TestInvoiceItemModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n        \n        self.client = Client.query.first()\n        if not self.client:\n            self.client = Client(name='Test Client', email='<EMAIL>')\n            db.session.add(self.client)\n            db.session.commit()\n            \n        self.invoice = Invoice.query.first()\n        if not self.invoice:\n            self.invoice = Invoice(invoice_number='INV-001', client_id=self.client.id, amount=1000.0, created_by=self.user.id)\n            db.session.add(self.invoice)\n            db.session.commit()\n\n    def test_invoiceitem_creation_basic(self):\n        item = InvoiceItem(\n            invoice_id=self.invoice.id,\n            description='Test Service',\n            quantity=2,\n            unit_price=500.0,\n            total=1000.0\n        )\n        db.session.add(item)\n        db.session.commit()\n        \n        assert item.id is not None\n        assert item.description == 'Test Service'\n        assert item.quantity == 2\n        assert item.total == 1000.0\n\n    def test_invoiceitem_deletion(self):\n        item = InvoiceItem(invoice_id=self.invoice.id, description='To Delete', quantity=1, unit_price=100.0)\n        db.session.add(item)\n        db.session.commit()\n        item_id = item.id\n        \n        db.session.delete(item)\n        db.session.commit()\n        \n        deleted = InvoiceItem.query.get(item_id)\n        assert deleted is None\n"}