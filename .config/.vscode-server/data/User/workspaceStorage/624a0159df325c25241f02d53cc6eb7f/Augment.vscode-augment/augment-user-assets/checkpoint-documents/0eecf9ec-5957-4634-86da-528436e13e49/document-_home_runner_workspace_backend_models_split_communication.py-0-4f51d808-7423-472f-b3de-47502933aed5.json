{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/communication.py"}, "originalCode": "# Communication Models\nfrom .base import db, datetime\n\nclass ForumTopic(db.Model):\n    \"\"\"Modello per i topic del forum aziendale\"\"\"\n    __tablename__ = 'forum_topics'\n    \n    id = db.Column(db.Integer, primary_key=True)\n    title = db.Column(db.String(255), nullable=False)\n    description = db.<PERSON>umn(db.Text, nullable=False)\n    author_id = db.<PERSON>umn(db.Integer, db.ForeignKey('users.id'), nullable=False)\n    category = db.Column(db.String(100))\n    is_pinned = db.Column(db.Boolean, default=False)\n    is_locked = db.Column(db.Bo<PERSON>an, default=False)\n    view_count = db.Column(db.Integer, default=0)\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n    \n    # Relationships\n    author = db.relationship('User', backref='forum_topics')\n    comments = db.relationship('ForumComment', backref='topic', lazy='dynamic', cascade='all, delete-orphan')\n    \n    def __repr__(self):\n        return f'<ForumTopic {self.title}>'\n    \n    @property\n    def comments_count(self):\n        return self.comments.count()\n    \n    @property\n    def last_activity(self):\n        last_comment = self.comments.order_by(ForumComment.created_at.desc()).first()\n        return last_comment.created_at if last_comment else self.created_at\n    \n    def get_reactions(self):\n        \"\"\"Get all reactions for this topic\"\"\"\n        # Lazy import to avoid circular dependency\n        from models import CommunicationReaction\n        return CommunicationReaction.query.filter_by(\n            target_type='forum_topic',\n            target_id=self.id\n        ).all()\n    \n    def get_reaction_counts(self):\n        \"\"\"Get reaction counts by type\"\"\"\n        from sqlalchemy import func\n        # Lazy import to avoid circular dependency\n        from models import CommunicationReaction\n        reaction_counts = db.session.query(\n            CommunicationReaction.reaction_type,\n            func.count(CommunicationReaction.id).label('count')\n        ).filter_by(\n            target_type='forum_topic',\n            target_id=self.id\n        ).group_by(CommunicationReaction.reaction_type).all()\n        \n        return {reaction_type: count for reaction_type, count in reaction_counts}\n    \n    def get_user_reaction(self, user_id):\n        \"\"\"Get specific user's reaction for this topic\"\"\"\n        # Lazy import to avoid circular dependency\n        from models import CommunicationReaction\n        return CommunicationReaction.query.filter_by(\n            target_type='forum_topic',\n            target_id=self.id,\n            user_id=user_id\n        ).first()\n    \n    def to_dict(self, include_reactions=False, current_user_id=None):\n        data = {\n            'id': self.id,\n            'title': self.title,\n            'description': self.description,\n            'content': self.description,  # Frontend compatibility - content = description\n            'author_id': self.author_id,\n            'author': {\n                'id': self.author.id,\n                'first_name': self.author.first_name,\n                'last_name': self.author.last_name,\n                'username': self.author.username,\n                'full_name': f\"{self.author.first_name} {self.author.last_name}\".strip()\n            } if self.author else None,\n            'author_name': f\"{self.author.first_name} {self.author.last_name}\".strip() if self.author else 'Anonimo',\n            'category': self.category,\n            'is_pinned': self.is_pinned,\n            'is_locked': self.is_locked,\n            'view_count': self.view_count,\n            'comments_count': self.comments_count,\n            'last_activity': self.last_activity.isoformat() if self.last_activity else None,\n            'created_at': self.created_at.isoformat(),\n            'updated_at': self.updated_at.isoformat()\n        }\n        \n        if include_reactions:\n            # Include reaction data\n            reactions_list = [reaction.to_dict() for reaction in self.get_reactions()]\n            data['reactions'] = reactions_list\n            data['reaction_counts'] = self.get_reaction_counts()\n            \n            # Include current user's reaction if user_id provided\n            if current_user_id:\n                user_reaction = self.get_user_reaction(current_user_id)\n                data['user_reaction'] = user_reaction.to_dict() if user_reaction else None\n        \n        return data\n\n\nclass ForumComment(db.Model):\n    \"\"\"Modello per i commenti del forum\"\"\"\n    __tablename__ = 'forum_comments'\n    \n    id = db.Column(db.Integer, primary_key=True)\n    topic_id = db.Column(db.Integer, db.ForeignKey('forum_topics.id'), nullable=False)\n    author_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)\n    content = db.Column(db.Text, nullable=False)\n    parent_comment_id = db.Column(db.Integer, db.ForeignKey('forum_comments.id'))\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n    \n    # Relationships\n    author = db.relationship('User', backref='forum_comments')\n    parent_comment = db.relationship('ForumComment', remote_side=[id], backref='replies')\n    \n    def __repr__(self):\n        return f'<ForumComment {self.id} on Topic {self.topic_id}>'\n    \n    @property\n    def is_reply(self):\n        return self.parent_comment_id is not None\n    \n    def to_dict(self):\n        return {\n            'id': self.id,\n            'topic_id': self.topic_id,\n            'author_id': self.author_id,\n            'author': {\n                'id': self.author.id,\n                'first_name': self.author.first_name,\n                'last_name': self.author.last_name,\n                'username': self.author.username\n            } if self.author else None,\n            'content': self.content,\n            'parent_comment_id': self.parent_comment_id,\n            'is_reply': self.is_reply,\n            'created_at': self.created_at.isoformat(),\n            'updated_at': self.updated_at.isoformat()\n        }\n\n\nclass Poll(db.Model):\n    \"\"\"Modello per i sondaggi aziendali\"\"\"\n    __tablename__ = 'polls'\n    \n    id = db.Column(db.Integer, primary_key=True)\n    title = db.Column(db.String(255), nullable=False)\n    description = db.Column(db.Text)\n    author_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)\n    is_anonymous = db.Column(db.Boolean, default=False)\n    multiple_choice = db.Column(db.Boolean, default=False)\n    expires_at = db.Column(db.DateTime)\n    is_active = db.Column(db.Boolean, default=True)\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n    \n    # Relationships\n    author = db.relationship('User', backref='created_polls')\n    options = db.relationship('PollOption', backref='poll', lazy='dynamic', cascade='all, delete-orphan')\n    votes = db.relationship('PollVote', backref='poll', lazy='dynamic', cascade='all, delete-orphan')\n    \n    def __repr__(self):\n        return f'<Poll {self.title}>'\n    \n    @property\n    def is_expired(self):\n        if self.expires_at:\n            return datetime.utcnow() > self.expires_at\n        return False\n    \n    @property\n    def total_votes(self):\n        return self.votes.count()\n    \n    @property\n    def unique_voters(self):\n        return db.session.query(PollVote.user_id).filter_by(poll_id=self.id).distinct().count()\n    \n    def can_vote(self, user_id):\n        if not self.is_active or self.is_expired:\n            return False\n        if not self.multiple_choice:\n            return not self.votes.filter_by(user_id=user_id).first()\n        return True\n    \n    def get_results(self):\n        results = []\n        for option in self.options:\n            vote_count = option.votes.count()\n            percentage = (vote_count / self.total_votes * 100) if self.total_votes > 0 else 0\n            results.append({\n                'option_id': option.id,\n                'text': option.option_text,\n                'votes': vote_count,\n                'percentage': round(percentage, 2)\n            })\n        return results\n    \n    def to_dict(self, include_results=False):\n        data = {\n            'id': self.id,\n            'title': self.title,\n            'description': self.description,\n            'author_id': self.author_id,\n            'author': {\n                'id': self.author.id,\n                'first_name': self.author.first_name,\n                'last_name': self.author.last_name,\n                'username': self.author.username,\n                'full_name': f\"{self.author.first_name} {self.author.last_name}\".strip()\n            } if self.author else None,\n            'author_name': f\"{self.author.first_name} {self.author.last_name}\".strip() if self.author else 'Anonimo',\n            'is_anonymous': self.is_anonymous,\n            'multiple_choice': self.multiple_choice,\n            'allows_multiple_choices': self.multiple_choice,  # Frontend compatibility\n            'expires_at': self.expires_at.isoformat() if self.expires_at else None,\n            'is_active': self.is_active,\n            'is_expired': self.is_expired,\n            'total_votes': self.total_votes,\n            'unique_voters': self.unique_voters,\n            'options': [option.to_dict() for option in self.options],\n            'created_at': self.created_at.isoformat(),\n            'updated_at': self.updated_at.isoformat()\n        }\n        \n        if include_results:\n            data['results'] = self.get_results()\n        \n        return data\n\n\nclass PollOption(db.Model):\n    \"\"\"Modello per le opzioni dei sondaggi\"\"\"\n    __tablename__ = 'poll_options'\n    \n    id = db.Column(db.Integer, primary_key=True)\n    poll_id = db.Column(db.Integer, db.ForeignKey('polls.id'), nullable=False)\n    option_text = db.Column(db.String(255), nullable=False)\n    \n    # Relationships\n    votes = db.relationship('PollVote', backref='option', lazy='dynamic', cascade='all, delete-orphan')\n    \n    def __repr__(self):\n        return f'<PollOption {self.option_text}>'\n    \n    @property\n    def vote_count(self):\n        return self.votes.count()\n    \n    def to_dict(self):\n        return {\n            'id': self.id,\n            'poll_id': self.poll_id,\n            'text': self.option_text,\n            'vote_count': self.vote_count,\n            'votes': self.vote_count  # Frontend compatibility\n        }\n\n\nclass PollVote(db.Model):\n    \"\"\"Modello per i voti dei sondaggi\"\"\"\n    __tablename__ = 'poll_votes'\n    \n    id = db.Column(db.Integer, primary_key=True)\n    poll_id = db.Column(db.Integer, db.ForeignKey('polls.id'), nullable=False)\n    option_id = db.Column(db.Integer, db.ForeignKey('poll_options.id'), nullable=False)\n    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    \n    # Relationships\n    user = db.relationship('User', backref='poll_votes')\n    \n    # Constraints\n    __table_args__ = (\n        db.UniqueConstraint('poll_id', 'user_id', 'option_id', name='unique_poll_vote'),\n    )\n    \n    def __repr__(self):\n        return f'<PollVote User:{self.user_id} Poll:{self.poll_id} Option:{self.option_id}>'\n    \n    def to_dict(self):\n        return {\n            'id': self.id,\n            'poll_id': self.poll_id,\n            'option_id': self.option_id,\n            'user_id': self.user_id,\n            'user': {\n                'id': self.user.id,\n                'first_name': self.user.first_name,\n                'last_name': self.user.last_name,\n                'username': self.user.username\n            } if self.user else None,\n            'created_at': self.created_at.isoformat()\n        }\n\n\nclass DirectMessage(db.Model):\n    \"\"\"Modello per i messaggi diretti tra utenti\"\"\"\n    __tablename__ = 'direct_messages'\n    \n    id = db.Column(db.Integer, primary_key=True)\n    sender_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)\n    recipient_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)\n    message = db.Column(db.Text, nullable=False)\n    is_read = db.Column(db.Boolean, default=False)\n    read_at = db.Column(db.DateTime)\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n    \n    # Relationships\n    sender = db.relationship('User', foreign_keys=[sender_id], backref='sent_messages')\n    recipient = db.relationship('User', foreign_keys=[recipient_id], backref='received_messages')\n    \n    def __repr__(self):\n        return f'<DirectMessage from {self.sender_id} to {self.recipient_id}>'\n    \n    def mark_as_read(self):\n        if not self.is_read:\n            self.is_read = True\n            self.read_at = datetime.utcnow()\n            db.session.commit()\n    \n    def to_dict(self):\n        return {\n            'id': self.id,\n            'sender_id': self.sender_id,\n            'sender': {\n                'id': self.sender.id,\n                'first_name': self.sender.first_name,\n                'last_name': self.sender.last_name,\n                'username': self.sender.username,\n                'full_name': f\"{self.sender.first_name} {self.sender.last_name}\".strip()\n            } if self.sender else None,\n            'sender_name': f\"{self.sender.first_name} {self.sender.last_name}\".strip() if self.sender else 'Mittente sconosciuto',\n            'recipient_id': self.recipient_id,\n            'recipient': {\n                'id': self.recipient.id,\n                'first_name': self.recipient.first_name,\n                'last_name': self.recipient.last_name,\n                'username': self.recipient.username,\n                'full_name': f\"{self.recipient.first_name} {self.recipient.last_name}\".strip()\n            } if self.recipient else None,\n            'recipient_name': f\"{self.recipient.first_name} {self.recipient.last_name}\".strip() if self.recipient else 'Destinatario sconosciuto',\n            'message': self.message,\n            'body': self.message,  # Frontend compatibility\n            'content': self.message,  # Frontend compatibility\n            'subject': None,  # Campo per compatibilità frontend - da implementare se necessario\n            'is_read': self.is_read,\n            'read_at': self.read_at.isoformat() if self.read_at else None,\n            'created_at': self.created_at.isoformat() if self.created_at else None,\n            'sent_at': self.created_at.isoformat() if self.created_at else None,  # Frontend compatibility\n            'updated_at': self.updated_at.isoformat() if self.updated_at else None\n        }\n\n\nclass CommunicationReaction(db.Model):\n    \"\"\"Modello per le reazioni ai contenuti di comunicazione\"\"\"\n    __tablename__ = 'communication_reactions'\n    \n    id = db.Column(db.Integer, primary_key=True)\n    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)\n    target_type = db.Column(db.String(50), nullable=False)  # 'forum_topic', 'forum_comment', 'news', 'event'\n    target_id = db.Column(db.Integer, nullable=False)\n    reaction_type = db.Column(db.String(20), nullable=False)  # 'like', 'love', 'laugh', 'angry', 'sad'\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    \n    # Relationships\n    user = db.relationship('User', backref='communication_reactions')\n    \n    # Constraints\n    __table_args__ = (\n        db.UniqueConstraint('user_id', 'target_type', 'target_id', name='unique_user_reaction'),\n    )\n    \n    def __repr__(self):\n        return f'<CommunicationReaction {self.reaction_type} by {self.user_id} on {self.content_type}:{self.content_id}>'\n    \n    def to_dict(self):\n        return {\n            'id': self.id,\n            'user_id': self.user_id,\n            'user': {\n                'id': self.user.id,\n                'first_name': self.user.first_name,\n                'last_name': self.user.last_name,\n                'username': self.user.username,\n                'name': f\"{self.user.first_name} {self.user.last_name}\".strip()\n            } if self.user else None,\n            'target_type': self.target_type,\n            'target_id': self.target_id,\n            'reaction_type': self.reaction_type,\n            'created_at': self.created_at.isoformat()\n        }\n\n\nclass CompanyEvent(db.Model):\n    __tablename__ = 'company_events'\n    \n    id = db.Column(db.Integer, primary_key=True)\n    title = db.Column(db.String(128), nullable=False)\n    description = db.Column(db.Text)\n    project_id = db.Column(db.Integer, db.ForeignKey('project.id'))\n    start_time = db.Column(db.DateTime, nullable=False)\n    end_time = db.Column(db.DateTime, nullable=False)\n    location = db.Column(db.String(128))\n    event_type = db.Column(db.String(20))  # meeting, deadline, milestone\n    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    \n    # Nuovi campi per il sistema di comunicazione\n    is_company_wide = db.Column(db.Boolean, default=False)\n    max_participants = db.Column(db.Integer)\n    registration_required = db.Column(db.Boolean, default=False)\n    registration_deadline = db.Column(db.DateTime)\n    is_public = db.Column(db.Boolean, default=False)\n    tags = db.Column(db.Text)  # JSON string for tags\n    allow_comments = db.Column(db.Boolean, default=True)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    # Relationships\n    creator = db.relationship('User', backref='created_company_events')\n\n    def __repr__(self):\n        return f'<CompanyEvent {self.title}>'\n\n    def to_dict(self):\n        return {\n            'id': self.id,\n            'title': self.title,\n            'description': self.description,\n            'project_id': self.project_id,\n            'start_time': self.start_time.isoformat() if self.start_time else None,\n            'end_time': self.end_time.isoformat() if self.end_time else None,\n            'location': self.location,\n            'event_type': self.event_type,\n            'created_by': self.created_by,\n            'creator': {\n                'id': self.creator.id,\n                'first_name': self.creator.first_name,\n                'last_name': self.creator.last_name,\n                'username': self.creator.username\n            } if self.creator else None,\n            'is_company_wide': self.is_company_wide,\n            'max_participants': self.max_participants,\n            'registration_required': self.registration_required,\n            'registration_deadline': self.registration_deadline.isoformat() if self.registration_deadline else None,\n            'is_public': self.is_public,\n            'tags': self.tags,\n            'allow_comments': self.allow_comments,\n            'created_at': self.created_at.isoformat() if self.created_at else None,\n            'updated_at': self.updated_at.isoformat() if self.updated_at else None\n        }\n\n\nclass CompanyEventRegistration(db.Model):\n    \"\"\"Modello per le registrazioni agli eventi aziendali\"\"\"\n    __tablename__ = 'company_event_registrations'\n    \n    id = db.Column(db.Integer, primary_key=True)\n    event_id = db.Column(db.Integer, db.ForeignKey('company_events.id'), nullable=False)\n    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)\n    status = db.Column(db.String(20), default='registered')  # 'registered', 'attended', 'cancelled'\n    notes = db.Column(db.Text)\n    registered_at = db.Column(db.DateTime, default=datetime.utcnow)\n    \n    # Relationships\n    event = db.relationship('CompanyEvent', backref='registrations')\n    user = db.relationship('User', backref='event_registrations')\n    \n    # Constraints\n    __table_args__ = (\n        db.UniqueConstraint('event_id', 'user_id', name='unique_event_registration'),\n    )\n    \n    def __repr__(self):\n        return f'<CompanyEventRegistration User:{self.user_id} Event:{self.event_id}>'\n    \n    def to_dict(self):\n        return {\n            'id': self.id,\n            'event_id': self.event_id,\n            'event': {\n                'id': self.event.id,\n                'title': self.event.title,\n                'start_time': self.event.start_time.isoformat(),\n                'end_time': self.event.end_time.isoformat(),\n                'location': self.event.location\n            } if self.event else None,\n            'user_id': self.user_id,\n            'user': {\n                'id': self.user.id,\n                'first_name': self.user.first_name,\n                'last_name': self.user.last_name,\n                'username': self.user.username\n            } if self.user else None,\n            'status': self.status,\n            'notes': self.notes,\n            'registered_at': self.registered_at.isoformat() if self.registered_at else None\n        }", "modifiedCode": "# Communication Models\nfrom .base import db, datetime\n\nclass ForumTopic(db.Model):\n    \"\"\"Modello per i topic del forum aziendale\"\"\"\n    __tablename__ = 'forum_topics'\n    \n    id = db.Column(db.Integer, primary_key=True)\n    title = db.Column(db.String(255), nullable=False)\n    description = db.<PERSON>umn(db.Text, nullable=False)\n    author_id = db.<PERSON>umn(db.Integer, db.ForeignKey('users.id'), nullable=False)\n    category = db.Column(db.String(100))\n    is_pinned = db.Column(db.Boolean, default=False)\n    is_locked = db.Column(db.Bo<PERSON>an, default=False)\n    view_count = db.Column(db.Integer, default=0)\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n    \n    # Relationships\n    author = db.relationship('User', backref='forum_topics')\n    comments = db.relationship('ForumComment', backref='topic', lazy='dynamic', cascade='all, delete-orphan')\n    \n    def __repr__(self):\n        return f'<ForumTopic {self.title}>'\n    \n    @property\n    def comments_count(self):\n        return self.comments.count()\n    \n    @property\n    def last_activity(self):\n        last_comment = self.comments.order_by(ForumComment.created_at.desc()).first()\n        return last_comment.created_at if last_comment else self.created_at\n    \n    def get_reactions(self):\n        \"\"\"Get all reactions for this topic\"\"\"\n        # Lazy import to avoid circular dependency\n        from models import CommunicationReaction\n        return CommunicationReaction.query.filter_by(\n            target_type='forum_topic',\n            target_id=self.id\n        ).all()\n    \n    def get_reaction_counts(self):\n        \"\"\"Get reaction counts by type\"\"\"\n        from sqlalchemy import func\n        # Lazy import to avoid circular dependency\n        from models import CommunicationReaction\n        reaction_counts = db.session.query(\n            CommunicationReaction.reaction_type,\n            func.count(CommunicationReaction.id).label('count')\n        ).filter_by(\n            target_type='forum_topic',\n            target_id=self.id\n        ).group_by(CommunicationReaction.reaction_type).all()\n        \n        return {reaction_type: count for reaction_type, count in reaction_counts}\n    \n    def get_user_reaction(self, user_id):\n        \"\"\"Get specific user's reaction for this topic\"\"\"\n        # Lazy import to avoid circular dependency\n        from models import CommunicationReaction\n        return CommunicationReaction.query.filter_by(\n            target_type='forum_topic',\n            target_id=self.id,\n            user_id=user_id\n        ).first()\n    \n    def to_dict(self, include_reactions=False, current_user_id=None):\n        data = {\n            'id': self.id,\n            'title': self.title,\n            'description': self.description,\n            'content': self.description,  # Frontend compatibility - content = description\n            'author_id': self.author_id,\n            'author': {\n                'id': self.author.id,\n                'first_name': self.author.first_name,\n                'last_name': self.author.last_name,\n                'username': self.author.username,\n                'full_name': f\"{self.author.first_name} {self.author.last_name}\".strip()\n            } if self.author else None,\n            'author_name': f\"{self.author.first_name} {self.author.last_name}\".strip() if self.author else 'Anonimo',\n            'category': self.category,\n            'is_pinned': self.is_pinned,\n            'is_locked': self.is_locked,\n            'view_count': self.view_count,\n            'comments_count': self.comments_count,\n            'last_activity': self.last_activity.isoformat() if self.last_activity else None,\n            'created_at': self.created_at.isoformat(),\n            'updated_at': self.updated_at.isoformat()\n        }\n        \n        if include_reactions:\n            # Include reaction data\n            reactions_list = [reaction.to_dict() for reaction in self.get_reactions()]\n            data['reactions'] = reactions_list\n            data['reaction_counts'] = self.get_reaction_counts()\n            \n            # Include current user's reaction if user_id provided\n            if current_user_id:\n                user_reaction = self.get_user_reaction(current_user_id)\n                data['user_reaction'] = user_reaction.to_dict() if user_reaction else None\n        \n        return data\n\n\nclass ForumComment(db.Model):\n    \"\"\"Modello per i commenti del forum\"\"\"\n    __tablename__ = 'forum_comments'\n    \n    id = db.Column(db.Integer, primary_key=True)\n    topic_id = db.Column(db.Integer, db.ForeignKey('forum_topics.id'), nullable=False)\n    author_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)\n    content = db.Column(db.Text, nullable=False)\n    parent_comment_id = db.Column(db.Integer, db.ForeignKey('forum_comments.id'))\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n    \n    # Relationships\n    author = db.relationship('User', backref='forum_comments')\n    parent_comment = db.relationship('ForumComment', remote_side=[id], backref='replies')\n    \n    def __repr__(self):\n        return f'<ForumComment {self.id} on Topic {self.topic_id}>'\n    \n    @property\n    def is_reply(self):\n        return self.parent_comment_id is not None\n    \n    def to_dict(self):\n        return {\n            'id': self.id,\n            'topic_id': self.topic_id,\n            'author_id': self.author_id,\n            'author': {\n                'id': self.author.id,\n                'first_name': self.author.first_name,\n                'last_name': self.author.last_name,\n                'username': self.author.username\n            } if self.author else None,\n            'content': self.content,\n            'parent_comment_id': self.parent_comment_id,\n            'is_reply': self.is_reply,\n            'created_at': self.created_at.isoformat(),\n            'updated_at': self.updated_at.isoformat()\n        }\n\n\nclass Poll(db.Model):\n    \"\"\"Modello per i sondaggi aziendali\"\"\"\n    __tablename__ = 'polls'\n    \n    id = db.Column(db.Integer, primary_key=True)\n    title = db.Column(db.String(255), nullable=False)\n    description = db.Column(db.Text)\n    author_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)\n    is_anonymous = db.Column(db.Boolean, default=False)\n    multiple_choice = db.Column(db.Boolean, default=False)\n    expires_at = db.Column(db.DateTime)\n    is_active = db.Column(db.Boolean, default=True)\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n    \n    # Relationships\n    author = db.relationship('User', backref='created_polls')\n    options = db.relationship('PollOption', backref='poll', lazy='dynamic', cascade='all, delete-orphan')\n    votes = db.relationship('PollVote', backref='poll', lazy='dynamic', cascade='all, delete-orphan')\n    \n    def __repr__(self):\n        return f'<Poll {self.title}>'\n    \n    @property\n    def is_expired(self):\n        if self.expires_at:\n            return datetime.utcnow() > self.expires_at\n        return False\n    \n    @property\n    def total_votes(self):\n        return self.votes.count()\n    \n    @property\n    def unique_voters(self):\n        return db.session.query(PollVote.user_id).filter_by(poll_id=self.id).distinct().count()\n    \n    def can_vote(self, user_id):\n        if not self.is_active or self.is_expired:\n            return False\n        if not self.multiple_choice:\n            return not self.votes.filter_by(user_id=user_id).first()\n        return True\n    \n    def get_results(self):\n        results = []\n        for option in self.options:\n            vote_count = option.votes.count()\n            percentage = (vote_count / self.total_votes * 100) if self.total_votes > 0 else 0\n            results.append({\n                'option_id': option.id,\n                'text': option.option_text,\n                'votes': vote_count,\n                'percentage': round(percentage, 2)\n            })\n        return results\n    \n    def to_dict(self, include_results=False):\n        data = {\n            'id': self.id,\n            'title': self.title,\n            'description': self.description,\n            'author_id': self.author_id,\n            'author': {\n                'id': self.author.id,\n                'first_name': self.author.first_name,\n                'last_name': self.author.last_name,\n                'username': self.author.username,\n                'full_name': f\"{self.author.first_name} {self.author.last_name}\".strip()\n            } if self.author else None,\n            'author_name': f\"{self.author.first_name} {self.author.last_name}\".strip() if self.author else 'Anonimo',\n            'is_anonymous': self.is_anonymous,\n            'multiple_choice': self.multiple_choice,\n            'allows_multiple_choices': self.multiple_choice,  # Frontend compatibility\n            'expires_at': self.expires_at.isoformat() if self.expires_at else None,\n            'is_active': self.is_active,\n            'is_expired': self.is_expired,\n            'total_votes': self.total_votes,\n            'unique_voters': self.unique_voters,\n            'options': [option.to_dict() for option in self.options],\n            'created_at': self.created_at.isoformat(),\n            'updated_at': self.updated_at.isoformat()\n        }\n        \n        if include_results:\n            data['results'] = self.get_results()\n        \n        return data\n\n\nclass PollOption(db.Model):\n    \"\"\"Modello per le opzioni dei sondaggi\"\"\"\n    __tablename__ = 'poll_options'\n    \n    id = db.Column(db.Integer, primary_key=True)\n    poll_id = db.Column(db.Integer, db.ForeignKey('polls.id'), nullable=False)\n    option_text = db.Column(db.String(255), nullable=False)\n    \n    # Relationships\n    votes = db.relationship('PollVote', backref='option', lazy='dynamic', cascade='all, delete-orphan')\n    \n    def __repr__(self):\n        return f'<PollOption {self.option_text}>'\n    \n    @property\n    def vote_count(self):\n        return self.votes.count()\n    \n    def to_dict(self):\n        return {\n            'id': self.id,\n            'poll_id': self.poll_id,\n            'text': self.option_text,\n            'vote_count': self.vote_count,\n            'votes': self.vote_count  # Frontend compatibility\n        }\n\n\nclass PollVote(db.Model):\n    \"\"\"Modello per i voti dei sondaggi\"\"\"\n    __tablename__ = 'poll_votes'\n    \n    id = db.Column(db.Integer, primary_key=True)\n    poll_id = db.Column(db.Integer, db.ForeignKey('polls.id'), nullable=False)\n    option_id = db.Column(db.Integer, db.ForeignKey('poll_options.id'), nullable=False)\n    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    \n    # Relationships\n    user = db.relationship('User', backref='poll_votes')\n    \n    # Constraints\n    __table_args__ = (\n        db.UniqueConstraint('poll_id', 'user_id', 'option_id', name='unique_poll_vote'),\n    )\n    \n    def __repr__(self):\n        return f'<PollVote User:{self.user_id} Poll:{self.poll_id} Option:{self.option_id}>'\n    \n    def to_dict(self):\n        return {\n            'id': self.id,\n            'poll_id': self.poll_id,\n            'option_id': self.option_id,\n            'user_id': self.user_id,\n            'user': {\n                'id': self.user.id,\n                'first_name': self.user.first_name,\n                'last_name': self.user.last_name,\n                'username': self.user.username\n            } if self.user else None,\n            'created_at': self.created_at.isoformat()\n        }\n\n\nclass DirectMessage(db.Model):\n    \"\"\"Modello per i messaggi diretti tra utenti\"\"\"\n    __tablename__ = 'direct_messages'\n    \n    id = db.Column(db.Integer, primary_key=True)\n    sender_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)\n    recipient_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)\n    message = db.Column(db.Text, nullable=False)\n    is_read = db.Column(db.Boolean, default=False)\n    read_at = db.Column(db.DateTime)\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n    \n    # Relationships\n    sender = db.relationship('User', foreign_keys=[sender_id], backref='sent_messages')\n    recipient = db.relationship('User', foreign_keys=[recipient_id], backref='received_messages')\n    \n    def __repr__(self):\n        return f'<DirectMessage from {self.sender_id} to {self.recipient_id}>'\n    \n    def mark_as_read(self):\n        if not self.is_read:\n            self.is_read = True\n            self.read_at = datetime.utcnow()\n            db.session.commit()\n    \n    def to_dict(self):\n        return {\n            'id': self.id,\n            'sender_id': self.sender_id,\n            'sender': {\n                'id': self.sender.id,\n                'first_name': self.sender.first_name,\n                'last_name': self.sender.last_name,\n                'username': self.sender.username,\n                'full_name': f\"{self.sender.first_name} {self.sender.last_name}\".strip()\n            } if self.sender else None,\n            'sender_name': f\"{self.sender.first_name} {self.sender.last_name}\".strip() if self.sender else 'Mittente sconosciuto',\n            'recipient_id': self.recipient_id,\n            'recipient': {\n                'id': self.recipient.id,\n                'first_name': self.recipient.first_name,\n                'last_name': self.recipient.last_name,\n                'username': self.recipient.username,\n                'full_name': f\"{self.recipient.first_name} {self.recipient.last_name}\".strip()\n            } if self.recipient else None,\n            'recipient_name': f\"{self.recipient.first_name} {self.recipient.last_name}\".strip() if self.recipient else 'Destinatario sconosciuto',\n            'message': self.message,\n            'body': self.message,  # Frontend compatibility\n            'content': self.message,  # Frontend compatibility\n            'subject': None,  # Campo per compatibilità frontend - da implementare se necessario\n            'is_read': self.is_read,\n            'read_at': self.read_at.isoformat() if self.read_at else None,\n            'created_at': self.created_at.isoformat() if self.created_at else None,\n            'sent_at': self.created_at.isoformat() if self.created_at else None,  # Frontend compatibility\n            'updated_at': self.updated_at.isoformat() if self.updated_at else None\n        }\n\n\nclass CommunicationReaction(db.Model):\n    \"\"\"Modello per le reazioni ai contenuti di comunicazione\"\"\"\n    __tablename__ = 'communication_reactions'\n    \n    id = db.Column(db.Integer, primary_key=True)\n    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)\n    target_type = db.Column(db.String(50), nullable=False)  # 'forum_topic', 'forum_comment', 'news', 'event'\n    target_id = db.Column(db.Integer, nullable=False)\n    reaction_type = db.Column(db.String(20), nullable=False)  # 'like', 'love', 'laugh', 'angry', 'sad'\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    \n    # Relationships\n    user = db.relationship('User', backref='communication_reactions')\n    \n    # Constraints\n    __table_args__ = (\n        db.UniqueConstraint('user_id', 'target_type', 'target_id', name='unique_user_reaction'),\n    )\n    \n    def __repr__(self):\n        return f'<CommunicationReaction {self.reaction_type} by {self.user_id} on {self.content_type}:{self.content_id}>'\n    \n    def to_dict(self):\n        return {\n            'id': self.id,\n            'user_id': self.user_id,\n            'user': {\n                'id': self.user.id,\n                'first_name': self.user.first_name,\n                'last_name': self.user.last_name,\n                'username': self.user.username,\n                'name': f\"{self.user.first_name} {self.user.last_name}\".strip()\n            } if self.user else None,\n            'target_type': self.target_type,\n            'target_id': self.target_id,\n            'reaction_type': self.reaction_type,\n            'created_at': self.created_at.isoformat()\n        }\n\n\nclass CompanyEvent(db.Model):\n    __tablename__ = 'company_events'\n    \n    id = db.Column(db.Integer, primary_key=True)\n    title = db.Column(db.String(128), nullable=False)\n    description = db.Column(db.Text)\n    project_id = db.Column(db.Integer, db.ForeignKey('project.id'))\n    start_time = db.Column(db.DateTime, nullable=False)\n    end_time = db.Column(db.DateTime, nullable=False)\n    location = db.Column(db.String(128))\n    event_type = db.Column(db.String(20))  # meeting, deadline, milestone\n    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    \n    # Nuovi campi per il sistema di comunicazione\n    is_company_wide = db.Column(db.Boolean, default=False)\n    max_participants = db.Column(db.Integer)\n    registration_required = db.Column(db.Boolean, default=False)\n    registration_deadline = db.Column(db.DateTime)\n    is_public = db.Column(db.Boolean, default=False)\n    tags = db.Column(db.Text)  # JSON string for tags\n    allow_comments = db.Column(db.Boolean, default=True)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    # Relationships\n    creator = db.relationship('User', backref='created_company_events')\n\n    def __repr__(self):\n        return f'<CompanyEvent {self.title}>'\n\n    def to_dict(self):\n        return {\n            'id': self.id,\n            'title': self.title,\n            'description': self.description,\n            'project_id': self.project_id,\n            'start_time': self.start_time.isoformat() if self.start_time else None,\n            'end_time': self.end_time.isoformat() if self.end_time else None,\n            'location': self.location,\n            'event_type': self.event_type,\n            'created_by': self.created_by,\n            'creator': {\n                'id': self.creator.id,\n                'first_name': self.creator.first_name,\n                'last_name': self.creator.last_name,\n                'username': self.creator.username\n            } if self.creator else None,\n            'is_company_wide': self.is_company_wide,\n            'max_participants': self.max_participants,\n            'registration_required': self.registration_required,\n            'registration_deadline': self.registration_deadline.isoformat() if self.registration_deadline else None,\n            'is_public': self.is_public,\n            'tags': self.tags,\n            'allow_comments': self.allow_comments,\n            'created_at': self.created_at.isoformat() if self.created_at else None,\n            'updated_at': self.updated_at.isoformat() if self.updated_at else None\n        }\n\n\nclass CompanyEventRegistration(db.Model):\n    \"\"\"Modello per le registrazioni agli eventi aziendali\"\"\"\n    __tablename__ = 'company_event_registrations'\n    \n    id = db.Column(db.Integer, primary_key=True)\n    event_id = db.Column(db.Integer, db.ForeignKey('company_events.id'), nullable=False)\n    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)\n    status = db.Column(db.String(20), default='registered')  # 'registered', 'attended', 'cancelled'\n    notes = db.Column(db.Text)\n    registered_at = db.Column(db.DateTime, default=datetime.utcnow)\n    \n    # Relationships\n    event = db.relationship('CompanyEvent', backref='registrations')\n    user = db.relationship('User', backref='event_registrations')\n    \n    # Constraints\n    __table_args__ = (\n        db.UniqueConstraint('event_id', 'user_id', name='unique_event_registration'),\n    )\n    \n    def __repr__(self):\n        return f'<CompanyEventRegistration User:{self.user_id} Event:{self.event_id}>'\n    \n    def to_dict(self):\n        return {\n            'id': self.id,\n            'event_id': self.event_id,\n            'event': {\n                'id': self.event.id,\n                'title': self.event.title,\n                'start_time': self.event.start_time.isoformat(),\n                'end_time': self.event.end_time.isoformat(),\n                'location': self.event.location\n            } if self.event else None,\n            'user_id': self.user_id,\n            'user': {\n                'id': self.user.id,\n                'first_name': self.user.first_name,\n                'last_name': self.user.last_name,\n                'username': self.user.username\n            } if self.user else None,\n            'status': self.status,\n            'notes': self.notes,\n            'registered_at': self.registered_at.isoformat() if self.registered_at else None\n        }"}