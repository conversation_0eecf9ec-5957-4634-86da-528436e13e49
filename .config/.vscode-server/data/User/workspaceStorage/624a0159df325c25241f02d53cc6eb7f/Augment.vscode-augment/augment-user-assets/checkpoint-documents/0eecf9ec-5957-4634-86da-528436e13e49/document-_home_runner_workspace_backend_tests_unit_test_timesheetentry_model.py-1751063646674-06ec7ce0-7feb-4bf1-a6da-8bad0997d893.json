{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_timesheetentry_model.py"}, "modifiedCode": "\"\"\"\nUnit tests for TimesheetEntry model.\nTests business logic, validations, and model functionality.\n\"\"\"\n\nimport pytest\nfrom datetime import datetime, date\nfrom models import TimesheetEntry, User, Project, Task\nfrom extensions import db\n\n\nclass TestTimesheetEntryModel:\n    \"\"\"Test suite for TimesheetEntry model unit tests\"\"\"\n\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        \"\"\"Setup test data for each test method\"\"\"\n        self.app = app\n        self.user = test_user\n        \n        # Create test project\n        self.test_project = Project(\n            name='Test Project Timesheet',\n            description='Project for timesheet testing',\n            status='active'\n        )\n        db.session.add(self.test_project)\n        db.session.commit()\n\n    def test_timesheetentry_creation_basic(self):\n        \"\"\"Test basic timesheet entry creation\"\"\"\n        entry = TimesheetEntry(\n            user_id=self.user.id,\n            project_id=self.test_project.id,\n            date=date.today(),\n            hours=8.0,\n            description='Development work'\n        )\n        \n        db.session.add(entry)\n        db.session.commit()\n        \n        assert entry.id is not None\n        assert entry.user_id == self.user.id\n        assert entry.project_id == self.test_project.id\n        assert entry.hours == 8.0\n        assert entry.description == 'Development work'\n\n    def test_timesheetentry_creation_complete(self):\n        \"\"\"Test timesheet entry creation with all fields\"\"\"\n        entry = TimesheetEntry(\n            user_id=self.user.id,\n            project_id=self.test_project.id,\n            date=date.today(),\n            hours=6.5,\n            description='Frontend development',\n            status='approved',\n            billable=True,\n            billing_rate=75.0,\n            billing_status='billed'\n        )\n        \n        db.session.add(entry)\n        db.session.commit()\n        \n        assert entry.hours == 6.5\n        assert entry.status == 'approved'\n        assert entry.billable is True\n        assert entry.billing_rate == 75.0\n        assert entry.billing_status == 'billed'\n\n    def test_timesheetentry_relationships(self):\n        \"\"\"Test relationships with other models\"\"\"\n        entry = TimesheetEntry(\n            user_id=self.user.id,\n            project_id=self.test_project.id,\n            date=date.today(),\n            hours=4.0\n        )\n        \n        db.session.add(entry)\n        db.session.commit()\n        \n        # Test relationships\n        assert entry.user is not None\n        assert entry.user.id == self.user.id\n        assert entry.project is not None\n        assert entry.project.id == self.test_project.id\n\n    def test_timesheetentry_hours_validation(self):\n        \"\"\"Test hours field validation\"\"\"\n        entry = TimesheetEntry(\n            user_id=self.user.id,\n            project_id=self.test_project.id,\n            date=date.today(),\n            hours=12.5\n        )\n        \n        db.session.add(entry)\n        db.session.commit()\n        \n        assert entry.hours == 12.5\n        assert entry.hours > 0\n\n    def test_timesheetentry_billable_functionality(self):\n        \"\"\"Test billable flag and billing rate\"\"\"\n        billable_entry = TimesheetEntry(\n            user_id=self.user.id,\n            project_id=self.test_project.id,\n            date=date.today(),\n            hours=8.0,\n            billable=True,\n            billing_rate=100.0\n        )\n        \n        non_billable_entry = TimesheetEntry(\n            user_id=self.user.id,\n            project_id=self.test_project.id,\n            date=date.today(),\n            hours=2.0,\n            billable=False\n        )\n        \n        db.session.add_all([billable_entry, non_billable_entry])\n        db.session.commit()\n        \n        assert billable_entry.billable is True\n        assert billable_entry.billing_rate == 100.0\n        assert non_billable_entry.billable is False\n\n    def test_timesheetentry_status_handling(self):\n        \"\"\"Test status field functionality\"\"\"\n        statuses = ['draft', 'submitted', 'approved', 'rejected']\n        \n        entries = []\n        for i, status in enumerate(statuses):\n            entry = TimesheetEntry(\n                user_id=self.user.id,\n                project_id=self.test_project.id,\n                date=date.today(),\n                hours=1.0,\n                description=f'Entry {i}',\n                status=status\n            )\n            entries.append(entry)\n        \n        db.session.add_all(entries)\n        db.session.commit()\n        \n        for entry, expected_status in zip(entries, statuses):\n            assert entry.status == expected_status\n\n    def test_timesheetentry_billing_status(self):\n        \"\"\"Test billing status functionality\"\"\"\n        entry = TimesheetEntry(\n            user_id=self.user.id,\n            project_id=self.test_project.id,\n            date=date.today(),\n            hours=8.0,\n            billable=True,\n            billing_status='unbilled'\n        )\n        \n        db.session.add(entry)\n        db.session.commit()\n        \n        assert entry.billing_status == 'unbilled'\n        \n        # Update billing status\n        entry.billing_status = 'billed'\n        db.session.commit()\n        \n        assert entry.billing_status == 'billed'\n\n    def test_timesheetentry_date_queries(self):\n        \"\"\"Test querying entries by date\"\"\"\n        today = date.today()\n        entry = TimesheetEntry(\n            user_id=self.user.id,\n            project_id=self.test_project.id,\n            date=today,\n            hours=8.0\n        )\n        \n        db.session.add(entry)\n        db.session.commit()\n        \n        # Query by date\n        entries_today = TimesheetEntry.query.filter_by(date=today).all()\n        assert len(entries_today) >= 1\n        assert entry in entries_today\n\n    def test_timesheetentry_user_queries(self):\n        \"\"\"Test querying entries by user\"\"\"\n        entry = TimesheetEntry(\n            user_id=self.user.id,\n            project_id=self.test_project.id,\n            date=date.today(),\n            hours=6.0\n        )\n        \n        db.session.add(entry)\n        db.session.commit()\n        \n        # Query by user\n        user_entries = TimesheetEntry.query.filter_by(user_id=self.user.id).all()\n        assert len(user_entries) >= 1\n        assert entry in user_entries\n\n    def test_timesheetentry_project_queries(self):\n        \"\"\"Test querying entries by project\"\"\"\n        entry = TimesheetEntry(\n            user_id=self.user.id,\n            project_id=self.test_project.id,\n            date=date.today(),\n            hours=4.0\n        )\n        \n        db.session.add(entry)\n        db.session.commit()\n        \n        # Query by project\n        project_entries = TimesheetEntry.query.filter_by(project_id=self.test_project.id).all()\n        assert len(project_entries) >= 1\n        assert entry in project_entries\n\n    def test_timesheetentry_hours_calculation(self):\n        \"\"\"Test hours calculation for multiple entries\"\"\"\n        entries = [\n            TimesheetEntry(user_id=self.user.id, project_id=self.test_project.id, \n                          date=date.today(), hours=4.0),\n            TimesheetEntry(user_id=self.user.id, project_id=self.test_project.id, \n                          date=date.today(), hours=3.5),\n            TimesheetEntry(user_id=self.user.id, project_id=self.test_project.id, \n                          date=date.today(), hours=2.0)\n        ]\n        \n        db.session.add_all(entries)\n        db.session.commit()\n        \n        # Calculate total hours\n        total_hours = sum(entry.hours for entry in entries)\n        assert total_hours == 9.5\n\n    def test_timesheetentry_update_operations(self):\n        \"\"\"Test entry update operations\"\"\"\n        entry = TimesheetEntry(\n            user_id=self.user.id,\n            project_id=self.test_project.id,\n            date=date.today(),\n            hours=8.0,\n            description='Original description',\n            status='draft'\n        )\n        \n        db.session.add(entry)\n        db.session.commit()\n        \n        # Update entry\n        entry.hours = 7.5\n        entry.description = 'Updated description'\n        entry.status = 'submitted'\n        \n        db.session.commit()\n        \n        updated_entry = TimesheetEntry.query.get(entry.id)\n        assert updated_entry.hours == 7.5\n        assert updated_entry.description == 'Updated description'\n        assert updated_entry.status == 'submitted'\n\n    def test_timesheetentry_deletion(self):\n        \"\"\"Test entry deletion\"\"\"\n        entry = TimesheetEntry(\n            user_id=self.user.id,\n            project_id=self.test_project.id,\n            date=date.today(),\n            hours=1.0\n        )\n        \n        db.session.add(entry)\n        db.session.commit()\n        entry_id = entry.id\n        \n        db.session.delete(entry)\n        db.session.commit()\n        \n        deleted_entry = TimesheetEntry.query.get(entry_id)\n        assert deleted_entry is None\n\n    def test_timesheetentry_default_values(self):\n        \"\"\"Test default values for fields\"\"\"\n        entry = TimesheetEntry(\n            user_id=self.user.id,\n            project_id=self.test_project.id,\n            date=date.today(),\n            hours=8.0\n        )\n        \n        db.session.add(entry)\n        db.session.commit()\n        \n        assert entry.billable is False  # Default value\n        assert entry.billing_status == 'unbilled'  # Default value\n        assert entry.created_at is not None\n\n    def test_timesheetentry_timestamps(self):\n        \"\"\"Test automatic timestamp handling\"\"\"\n        entry = TimesheetEntry(\n            user_id=self.user.id,\n            project_id=self.test_project.id,\n            date=date.today(),\n            hours=8.0\n        )\n        \n        db.session.add(entry)\n        db.session.commit()\n        \n        assert entry.created_at is not None\n        assert isinstance(entry.created_at, datetime)\n"}