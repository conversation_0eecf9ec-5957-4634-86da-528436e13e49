{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/api/test_clients.py"}, "modifiedCode": "\"\"\"\nTest suite for Clients API endpoints.\nTests CRUD operations, validation, and business logic for client management.\n\"\"\"\n\nimport pytest\nfrom datetime import datetime\nfrom models import Client, User\nfrom extensions import db\n\n\nclass TestClientsAPI:\n    \"\"\"Test suite for Clients API endpoints\"\"\"\n\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        \"\"\"Setup test data for each test method\"\"\"\n        self.app = app\n        self.user = test_user\n        \n        # Create test client data\n        self.client_data = {\n            'name': 'Test Company Ltd',\n            'description': 'A test company for API testing',\n            'industry': 'Technology',\n            'website': 'https://testcompany.com',\n            'email': '<EMAIL>',\n            'phone': '+39 ************',\n            'address': 'Via Test 123, Milano, Italy',\n            'vat_number': '*************',\n            'fiscal_code': '****************',\n            'status': 'active'\n        }\n\n    def test_get_clients_requires_auth(self, client):\n        \"\"\"Test that clients API requires authentication\"\"\"\n        response = client.get('/api/clients/')\n        \n        assert response.status_code == 401\n        data = response.get_json()\n        assert data['success'] is False\n        assert 'autenticazione' in data['message'].lower() or 'auth' in data['message'].lower()\n\n    def test_create_client_requires_auth(self, client):\n        \"\"\"Test that client creation requires authentication\"\"\"\n        response = client.post('/api/clients/', json=self.client_data)\n        \n        assert response.status_code == 401\n        data = response.get_json()\n        assert data['success'] is False\n\n    def test_update_client_requires_auth(self, client):\n        \"\"\"Test that client update requires authentication\"\"\"\n        response = client.put('/api/clients/1', json={'name': 'Updated Name'})\n        \n        assert response.status_code == 401\n        data = response.get_json()\n        assert data['success'] is False\n\n    def test_delete_client_requires_auth(self, client):\n        \"\"\"Test that client deletion requires authentication\"\"\"\n        response = client.delete('/api/clients/1')\n        \n        assert response.status_code == 401\n        data = response.get_json()\n        assert data['success'] is False\n\n    def test_client_api_endpoints_exist(self, client):\n        \"\"\"Test that client API endpoints exist and return proper error codes\"\"\"\n        endpoints = [\n            ('GET', '/api/clients/'),\n            ('POST', '/api/clients/'),\n            ('GET', '/api/clients/1'),\n            ('PUT', '/api/clients/1'),\n            ('DELETE', '/api/clients/1')\n        ]\n        \n        for method, url in endpoints:\n            if method == 'GET':\n                response = client.get(url)\n            elif method == 'POST':\n                response = client.post(url, json=self.client_data)\n            elif method == 'PUT':\n                response = client.put(url, json={'name': 'Test'})\n            elif method == 'DELETE':\n                response = client.delete(url)\n            \n            # Should return 401 (auth required) not 404 (not found)\n            assert response.status_code in [401, 403], f\"{method} {url} returned {response.status_code}\"\n\n    def test_client_data_model_fields(self, client):\n        \"\"\"Test that Client model has expected fields\"\"\"\n        # Create a client to test model fields\n        test_client = Client(\n            name='Test Client',\n            description='Test description',\n            industry='Technology',\n            status='active'\n        )\n        \n        # Test that required fields exist\n        assert hasattr(test_client, 'name')\n        assert hasattr(test_client, 'description')\n        assert hasattr(test_client, 'industry')\n        assert hasattr(test_client, 'status')\n        \n        # Test field values\n        assert test_client.name == 'Test Client'\n        assert test_client.description == 'Test description'\n        assert test_client.industry == 'Technology'\n        assert test_client.status == 'active'\n\n    def test_client_model_creation(self, client):\n        \"\"\"Test Client model creation and database persistence\"\"\"\n        # Create and save client\n        test_client = Client(\n            name='Database Test Client',\n            description='Testing database operations',\n            industry='Testing',\n            status='active'\n        )\n        \n        db.session.add(test_client)\n        db.session.commit()\n        \n        # Verify client was saved\n        saved_client = Client.query.filter_by(name='Database Test Client').first()\n        assert saved_client is not None\n        assert saved_client.name == 'Database Test Client'\n        assert saved_client.industry == 'Testing'\n\n    def test_client_model_relationships(self, client):\n        \"\"\"Test Client model relationships if they exist\"\"\"\n        test_client = Client(name='Relationship Test', status='active')\n        db.session.add(test_client)\n        db.session.commit()\n        \n        # Test that client has an ID after saving\n        assert test_client.id is not None\n        assert isinstance(test_client.id, int)\n\n    def test_client_api_response_format(self, client):\n        \"\"\"Test that client API returns consistent response format\"\"\"\n        response = client.get('/api/clients/')\n        \n        assert response.status_code == 401  # Expected for unauthenticated request\n        data = response.get_json()\n        \n        # Test response structure\n        assert isinstance(data, dict)\n        assert 'success' in data\n        assert 'message' in data\n        assert data['success'] is False\n\n    def test_client_search_endpoint(self, client):\n        \"\"\"Test client search functionality\"\"\"\n        # Test search with query parameters\n        response = client.get('/api/clients/?search=test')\n        assert response.status_code == 401  # Auth required\n        \n        response = client.get('/api/clients/?status=active')\n        assert response.status_code == 401  # Auth required\n\n    def test_client_pagination_endpoint(self, client):\n        \"\"\"Test client pagination functionality\"\"\"\n        response = client.get('/api/clients/?page=1&per_page=10')\n        assert response.status_code == 401  # Auth required\n\n    def test_client_validation_fields(self, client):\n        \"\"\"Test client validation through model constraints\"\"\"\n        # Test that we can create clients with various field combinations\n        clients_data = [\n            {'name': 'Client 1', 'status': 'active'},\n            {'name': 'Client 2', 'status': 'inactive', 'industry': 'Tech'},\n            {'name': 'Client 3', 'status': 'active', 'description': 'Test desc'}\n        ]\n        \n        for client_data in clients_data:\n            test_client = Client(**client_data)\n            db.session.add(test_client)\n        \n        db.session.commit()\n        \n        # Verify all clients were created\n        created_clients = Client.query.filter(Client.name.like('Client %')).all()\n        assert len(created_clients) == 3\n"}