{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_fundingopportunity_model.py"}, "originalCode": "\"\"\"Unit tests for FundingOpportunity model.\"\"\"\nimport pytest\nfrom datetime import date\nfrom models import FundingOpportunity\nfrom extensions import db\n\nclass TestFundingOpportunityModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app):\n        self.app = app\n\n    def test_fundingopportunity_creation_basic(self):\n        opportunity = FundingOpportunity(\n            title='Test Funding',\n            description='Test funding opportunity',\n            max_grant_amount=100000.0,  # Campo corretto è 'max_grant_amount'\n            application_deadline=date(2024, 12, 31),  # Campo corretto è 'application_deadline' (NOT NULL)\n            created_by=1  # Campo richiesto\n        )\n        db.session.add(opportunity)\n        db.session.commit()\n\n        assert opportunity.id is not None\n        assert opportunity.title == 'Test Funding'\n        assert opportunity.max_grant_amount == 100000.0\n\n    def test_fundingopportunity_deletion(self):\n        opportunity = FundingOpportunity(\n            title='To Delete',\n            description='Delete me',\n            application_deadline=date(2024, 12, 31),  # Campo NOT NULL richiesto\n            created_by=1  # Campo richiesto\n        )\n        db.session.add(opportunity)\n        db.session.commit()\n        opp_id = opportunity.id\n\n        db.session.delete(opportunity)\n        db.session.commit()\n\n        deleted = FundingOpportunity.query.get(opp_id)\n        assert deleted is None\n", "modifiedCode": "\"\"\"Unit tests for FundingOpportunity model.\"\"\"\nimport pytest\nfrom datetime import date\nfrom models import FundingOpportunity\nfrom extensions import db\n\nclass TestFundingOpportunityModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app):\n        self.app = app\n\n    def test_fundingopportunity_creation_basic(self):\n        opportunity = FundingOpportunity(\n            title='Test Funding',\n            description='Test funding opportunity',\n            max_grant_amount=100000.0,  # Campo corretto è 'max_grant_amount'\n            application_deadline=date(2024, 12, 31),  # Campo corretto è 'application_deadline' (NOT NULL)\n            created_by=1  # Campo richiesto\n        )\n        db.session.add(opportunity)\n        db.session.commit()\n\n        assert opportunity.id is not None\n        assert opportunity.title == 'Test Funding'\n        assert opportunity.max_grant_amount == 100000.0\n\n    def test_fundingopportunity_deletion(self):\n        opportunity = FundingOpportunity(\n            title='To Delete',\n            description='Delete me',\n            application_deadline=date(2024, 12, 31),  # Campo NOT NULL richiesto\n            created_by=1  # Campo richiesto\n        )\n        db.session.add(opportunity)\n        db.session.commit()\n        opp_id = opportunity.id\n\n        db.session.delete(opportunity)\n        db.session.commit()\n\n        deleted = FundingOpportunity.query.get(opp_id)\n        assert deleted is None\n"}