{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/api/test_forum_topics.py"}, "modifiedCode": "\"\"\"\nTest suite for ForumTopic API endpoints.\nTests CRUD operations, validation, and business logic for forum topic management.\n\"\"\"\n\nimport pytest\nfrom datetime import datetime, date, timedelta\nfrom models import ForumTopic, User\nfrom extensions import db\n\n\nclass TestForumTopicsAPI:\n    \"\"\"Test suite for ForumTopic API endpoints\"\"\"\n\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        \"\"\"Setup test data for each test method\"\"\"\n        self.app = app\n        self.user = test_user\n        \n        # Create test forum topic data\n        self.topic_data = {\n            'title': 'Test Forum Topic',\n            'description': 'This is a test forum topic for API testing',\n            'author_id': self.user.id,\n            'category': 'general',\n            'is_pinned': False,\n            'is_locked': False\n        }\n\n    def test_get_forum_topics_success(self, client):\n        \"\"\"Test successful retrieval of forum topics list\"\"\"\n        # Create test topics\n        topic1 = ForumTopic(\n            title='Topic 1',\n            description='First test topic',\n            author_id=self.user.id,\n            category='general'\n        )\n        topic2 = ForumTopic(\n            title='Topic 2',\n            description='Second test topic',\n            author_id=self.user.id,\n            category='announcements'\n        )\n        db.session.add_all([topic1, topic2])\n        db.session.commit()\n\n        response = client.get('/api/forum/topics')\n        \n        assert response.status_code in [200, 401]\n        if response.status_code == 200:\n            data = response.get_json()\n            assert 'topics' in str(data).lower() or 'data' in data\n\n    def test_create_forum_topic_success(self, client):\n        \"\"\"Test successful forum topic creation\"\"\"\n        response = client.post('/api/forum/topics', json=self.topic_data)\n        \n        assert response.status_code in [201, 200, 401, 403]\n        if response.status_code in [200, 201]:\n            # Verify topic was created\n            created_topic = ForumTopic.query.filter_by(title='Test Forum Topic').first()\n            if created_topic:\n                assert created_topic.author_id == self.user.id\n                assert created_topic.category == 'general'\n\n    def test_create_forum_topic_validation_error(self, client):\n        \"\"\"Test forum topic creation with missing required fields\"\"\"\n        invalid_data = {'description': 'Missing title and author_id'}\n        \n        response = client.post('/api/forum/topics', json=invalid_data)\n        \n        assert response.status_code in [400, 422, 401, 403]\n\n    def test_update_forum_topic_success(self, client):\n        \"\"\"Test successful forum topic update\"\"\"\n        test_topic = ForumTopic(\n            title='Original Title',\n            description='Original description',\n            author_id=self.user.id,\n            category='general'\n        )\n        db.session.add(test_topic)\n        db.session.commit()\n\n        update_data = {\n            'title': 'Updated Forum Topic Title',\n            'description': 'Updated description',\n            'category': 'announcements'\n        }\n        \n        response = client.put(f'/api/forum/topics/{test_topic.id}', json=update_data)\n        \n        assert response.status_code in [200, 401, 403, 404]\n\n    def test_delete_forum_topic_success(self, client):\n        \"\"\"Test successful forum topic deletion\"\"\"\n        test_topic = ForumTopic(\n            title='To Delete',\n            description='This topic will be deleted',\n            author_id=self.user.id,\n            category='general'\n        )\n        db.session.add(test_topic)\n        db.session.commit()\n        topic_id = test_topic.id\n\n        response = client.delete(f'/api/forum/topics/{topic_id}')\n        \n        assert response.status_code in [200, 401, 403, 404]\n\n    def test_forum_topic_pin_unpin(self, client):\n        \"\"\"Test forum topic pin/unpin functionality\"\"\"\n        test_topic = ForumTopic(\n            title='Pin Test Topic',\n            description='Topic for testing pin functionality',\n            author_id=self.user.id,\n            category='general',\n            is_pinned=False\n        )\n        db.session.add(test_topic)\n        db.session.commit()\n\n        # Test pin\n        response = client.put(f'/api/forum/topics/{test_topic.id}/pin')\n        assert response.status_code in [200, 401, 403, 404]\n        \n        # Test unpin\n        response = client.put(f'/api/forum/topics/{test_topic.id}/unpin')\n        assert response.status_code in [200, 401, 403, 404]\n\n    def test_forum_topic_lock_unlock(self, client):\n        \"\"\"Test forum topic lock/unlock functionality\"\"\"\n        test_topic = ForumTopic(\n            title='Lock Test Topic',\n            description='Topic for testing lock functionality',\n            author_id=self.user.id,\n            category='general',\n            is_locked=False\n        )\n        db.session.add(test_topic)\n        db.session.commit()\n\n        # Test lock\n        response = client.put(f'/api/forum/topics/{test_topic.id}/lock')\n        assert response.status_code in [200, 401, 403, 404]\n        \n        # Test unlock\n        response = client.put(f'/api/forum/topics/{test_topic.id}/unlock')\n        assert response.status_code in [200, 401, 403, 404]\n\n    def test_forum_topic_search_and_filters(self, client):\n        \"\"\"Test forum topic search and filtering\"\"\"\n        # Create test topics with different categories\n        topic1 = ForumTopic(\n            title='General Discussion Topic',\n            description='A general discussion',\n            author_id=self.user.id,\n            category='general'\n        )\n        topic2 = ForumTopic(\n            title='Important Announcement',\n            description='An important announcement',\n            author_id=self.user.id,\n            category='announcements',\n            is_pinned=True\n        )\n        db.session.add_all([topic1, topic2])\n        db.session.commit()\n\n        # Test category filter\n        response = client.get('/api/forum/topics?category=general')\n        assert response.status_code in [200, 401]\n        \n        # Test search by title\n        response = client.get('/api/forum/topics?search=Discussion')\n        assert response.status_code in [200, 401]\n        \n        # Test pinned filter\n        response = client.get('/api/forum/topics?is_pinned=true')\n        assert response.status_code in [200, 401]\n\n    def test_forum_topic_categories(self, client):\n        \"\"\"Test forum topic category validation\"\"\"\n        valid_categories = ['general', 'announcements', 'help', 'feedback', 'off-topic']\n        \n        for category in valid_categories:\n            test_data = self.topic_data.copy()\n            test_data['category'] = category\n            test_data['title'] = f'Topic in {category}'\n            \n            response = client.post('/api/forum/topics', json=test_data)\n            assert response.status_code in [201, 200, 401, 403, 400, 422]\n\n    def test_forum_topic_view_count(self, client):\n        \"\"\"Test forum topic view count increment\"\"\"\n        test_topic = ForumTopic(\n            title='View Count Test',\n            description='Topic for testing view count',\n            author_id=self.user.id,\n            category='general',\n            view_count=0\n        )\n        db.session.add(test_topic)\n        db.session.commit()\n\n        # View the topic (should increment view count)\n        response = client.get(f'/api/forum/topics/{test_topic.id}')\n        assert response.status_code in [200, 401, 404]\n\n    def test_get_forum_topic_detail(self, client):\n        \"\"\"Test single forum topic retrieval\"\"\"\n        test_topic = ForumTopic(\n            title='Detail Test Topic',\n            description='Topic for testing detail view',\n            author_id=self.user.id,\n            category='general',\n            view_count=5,\n            reply_count=3\n        )\n        db.session.add(test_topic)\n        db.session.commit()\n\n        response = client.get(f'/api/forum/topics/{test_topic.id}')\n        assert response.status_code in [200, 401, 404]\n\n    def test_forum_topic_not_found(self, client):\n        \"\"\"Test forum topic not found scenarios\"\"\"\n        response = client.get('/api/forum/topics/99999')\n        assert response.status_code in [404, 401]\n        \n        response = client.put('/api/forum/topics/99999', json={'title': 'Test'})\n        assert response.status_code in [404, 401]\n        \n        response = client.delete('/api/forum/topics/99999')\n        assert response.status_code in [404, 401]\n\n    def test_forum_topic_author_permissions(self, client):\n        \"\"\"Test forum topic author permissions\"\"\"\n        # Create topic by another user (simulated)\n        other_topic = ForumTopic(\n            title='Other User Topic',\n            description='Topic by another user',\n            author_id=999,  # Different user ID\n            category='general'\n        )\n        db.session.add(other_topic)\n        db.session.commit()\n\n        # Try to update topic by different user\n        update_data = {'title': 'Unauthorized Update'}\n        response = client.put(f'/api/forum/topics/{other_topic.id}', json=update_data)\n        assert response.status_code in [403, 401, 404]\n        \n        # Try to delete topic by different user\n        response = client.delete(f'/api/forum/topics/{other_topic.id}')\n        assert response.status_code in [403, 401, 404]\n\n    def test_forum_topic_pagination(self, client):\n        \"\"\"Test forum topic list pagination\"\"\"\n        # Create multiple topics\n        for i in range(5):\n            topic = ForumTopic(\n                title=f'Topic {i}',\n                description=f'Description for topic {i}',\n                author_id=self.user.id,\n                category='general'\n            )\n            db.session.add(topic)\n        db.session.commit()\n\n        response = client.get('/api/forum/topics?page=1&per_page=3')\n        assert response.status_code in [200, 401]\n\n    def test_forum_topic_sorting(self, client):\n        \"\"\"Test forum topic sorting options\"\"\"\n        # Create topics with different timestamps\n        topic1 = ForumTopic(\n            title='Older Topic',\n            description='An older topic',\n            author_id=self.user.id,\n            category='general'\n        )\n        topic2 = ForumTopic(\n            title='Newer Topic',\n            description='A newer topic',\n            author_id=self.user.id,\n            category='general'\n        )\n        db.session.add_all([topic1, topic2])\n        db.session.commit()\n\n        # Test sorting by creation date\n        response = client.get('/api/forum/topics?sort=created_at&order=desc')\n        assert response.status_code in [200, 401]\n        \n        # Test sorting by view count\n        response = client.get('/api/forum/topics?sort=view_count&order=desc')\n        assert response.status_code in [200, 401]\n\n    def test_forum_topic_title_validation(self, client):\n        \"\"\"Test forum topic title validation\"\"\"\n        # Test empty title\n        invalid_data = self.topic_data.copy()\n        invalid_data['title'] = ''\n        \n        response = client.post('/api/forum/topics', json=invalid_data)\n        assert response.status_code in [400, 422, 401, 403]\n        \n        # Test very long title\n        invalid_data['title'] = 'x' * 300  # Assuming max length is 200\n        response = client.post('/api/forum/topics', json=invalid_data)\n        assert response.status_code in [400, 422, 401, 403, 201, 200]\n"}