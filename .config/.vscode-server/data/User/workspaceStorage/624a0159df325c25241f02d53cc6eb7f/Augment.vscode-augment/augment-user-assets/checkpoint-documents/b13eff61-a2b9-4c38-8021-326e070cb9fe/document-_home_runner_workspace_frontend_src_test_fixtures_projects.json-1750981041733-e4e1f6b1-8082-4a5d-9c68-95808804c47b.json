{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/fixtures/projects.json"}, "modifiedCode": "{\n  \"projects\": [\n    {\n      \"id\": 1,\n      \"name\": \"E-Commerce Platform\",\n      \"description\": \"Modern e-commerce platform with Vue.js frontend\",\n      \"status\": \"active\",\n      \"budget\": 75000,\n      \"expenses\": 15000,\n      \"start_date\": \"2025-01-01\",\n      \"end_date\": \"2025-08-31\",\n      \"project_type\": \"service\",\n      \"is_billable\": true,\n      \"client\": {\n        \"id\": 1,\n        \"name\": \"TechCorp Solutions\",\n        \"email\": \"<EMAIL>\",\n        \"industry\": \"Technology\"\n      },\n      \"contract\": {\n        \"id\": 1,\n        \"contract_number\": \"CNT-2025-001\",\n        \"value\": 75000\n      },\n      \"team_members\": [\n        {\n          \"id\": 1,\n          \"full_name\": \"<PERSON>\",\n          \"email\": \"<EMAIL>\",\n          \"role\": \"Project Manager\",\n          \"allocation_percentage\": 50,\n          \"hours_worked\": 120,\n          \"daily_rate\": 400\n        },\n        {\n          \"id\": 2,\n          \"full_name\": \"<PERSON>\",\n          \"email\": \"<EMAIL>\",\n          \"role\": \"Senior Developer\",\n          \"allocation_percentage\": 100,\n          \"hours_worked\": 240,\n          \"daily_rate\": 350\n        },\n        {\n          \"id\": 3,\n          \"full_name\": \"<PERSON>\",\n          \"email\": \"<EMAIL>\",\n          \"role\": \"UI/UX Designer\",\n          \"allocation_percentage\": 75,\n          \"hours_worked\": 180,\n          \"daily_rate\": 300\n        }\n      ],\n      \"tasks\": [\n        {\n          \"id\": 1,\n          \"title\": \"Setup Development Environment\",\n          \"description\": \"Configure development tools and CI/CD pipeline\",\n          \"status\": \"completed\",\n          \"priority\": \"high\",\n          \"estimated_hours\": 16,\n          \"actual_hours\": 18,\n          \"assigned_to\": 2,\n          \"due_date\": \"2025-01-15\"\n        },\n        {\n          \"id\": 2,\n          \"title\": \"Design User Interface\",\n          \"description\": \"Create wireframes and UI mockups\",\n          \"status\": \"completed\",\n          \"priority\": \"high\",\n          \"estimated_hours\": 40,\n          \"actual_hours\": 38,\n          \"assigned_to\": 3,\n          \"due_date\": \"2025-02-01\"\n        },\n        {\n          \"id\": 3,\n          \"title\": \"Implement Authentication\",\n          \"description\": \"User login, registration, and security features\",\n          \"status\": \"in_progress\",\n          \"priority\": \"high\",\n          \"estimated_hours\": 32,\n          \"actual_hours\": 20,\n          \"assigned_to\": 2,\n          \"due_date\": \"2025-02-15\"\n        },\n        {\n          \"id\": 4,\n          \"title\": \"Product Catalog\",\n          \"description\": \"Product listing, search, and filtering\",\n          \"status\": \"todo\",\n          \"priority\": \"medium\",\n          \"estimated_hours\": 48,\n          \"actual_hours\": 0,\n          \"assigned_to\": 2,\n          \"due_date\": \"2025-03-15\"\n        }\n      ],\n      \"kpis\": [\n        {\n          \"id\": 1,\n          \"name\": \"Budget Utilization\",\n          \"description\": \"Percentage of budget used\",\n          \"unit\": \"percentage\",\n          \"target_value\": 90,\n          \"current_value\": 20,\n          \"category\": \"budget\"\n        },\n        {\n          \"id\": 2,\n          \"name\": \"Task Completion Rate\",\n          \"description\": \"Percentage of tasks completed on time\",\n          \"unit\": \"percentage\",\n          \"target_value\": 95,\n          \"current_value\": 100,\n          \"category\": \"productivity\"\n        }\n      ],\n      \"expenses\": [\n        {\n          \"id\": 1,\n          \"description\": \"Development Tools License\",\n          \"amount\": 299.99,\n          \"category\": \"software\",\n          \"expense_date\": \"2025-01-05\",\n          \"status\": \"approved\",\n          \"user\": {\n            \"id\": 1,\n            \"full_name\": \"Alice Johnson\"\n          }\n        },\n        {\n          \"id\": 2,\n          \"description\": \"Cloud Hosting Setup\",\n          \"amount\": 150.00,\n          \"category\": \"infrastructure\",\n          \"expense_date\": \"2025-01-10\",\n          \"status\": \"approved\",\n          \"user\": {\n            \"id\": 2,\n            \"full_name\": \"Bob Smith\"\n          }\n        }\n      ]\n    },\n    {\n      \"id\": 2,\n      \"name\": \"Mobile App Development\",\n      \"description\": \"Cross-platform mobile application\",\n      \"status\": \"planning\",\n      \"budget\": 50000,\n      \"expenses\": 0,\n      \"start_date\": \"2025-03-01\",\n      \"end_date\": \"2025-10-31\",\n      \"project_type\": \"product\",\n      \"is_billable\": true,\n      \"client\": {\n        \"id\": 2,\n        \"name\": \"StartupXYZ\",\n        \"email\": \"<EMAIL>\",\n        \"industry\": \"Fintech\"\n      },\n      \"team_members\": [],\n      \"tasks\": [],\n      \"kpis\": [],\n      \"expenses\": []\n    },\n    {\n      \"id\": 3,\n      \"name\": \"AI Research Project\",\n      \"description\": \"Machine learning research and development\",\n      \"status\": \"active\",\n      \"budget\": 100000,\n      \"expenses\": 25000,\n      \"start_date\": \"2024-09-01\",\n      \"end_date\": \"2025-08-31\",\n      \"project_type\": \"rd\",\n      \"is_billable\": false,\n      \"funding_source\": \"public_funding\",\n      \"client\": {\n        \"id\": 3,\n        \"name\": \"Research Institute\",\n        \"email\": \"<EMAIL>\",\n        \"industry\": \"Research\"\n      },\n      \"team_members\": [\n        {\n          \"id\": 4,\n          \"full_name\": \"Dr. Emily Chen\",\n          \"email\": \"<EMAIL>\",\n          \"role\": \"Research Lead\",\n          \"allocation_percentage\": 80,\n          \"hours_worked\": 640,\n          \"daily_rate\": 500\n        },\n        {\n          \"id\": 5,\n          \"full_name\": \"David Wilson\",\n          \"email\": \"<EMAIL>\",\n          \"role\": \"Data Scientist\",\n          \"allocation_percentage\": 100,\n          \"hours_worked\": 800,\n          \"daily_rate\": 400\n        }\n      ],\n      \"tasks\": [\n        {\n          \"id\": 5,\n          \"title\": \"Literature Review\",\n          \"description\": \"Review existing research and methodologies\",\n          \"status\": \"completed\",\n          \"priority\": \"high\",\n          \"estimated_hours\": 80,\n          \"actual_hours\": 85,\n          \"assigned_to\": 4,\n          \"due_date\": \"2024-10-31\"\n        },\n        {\n          \"id\": 6,\n          \"title\": \"Data Collection\",\n          \"description\": \"Gather and prepare training datasets\",\n          \"status\": \"completed\",\n          \"priority\": \"high\",\n          \"estimated_hours\": 120,\n          \"actual_hours\": 115,\n          \"assigned_to\": 5,\n          \"due_date\": \"2024-12-15\"\n        },\n        {\n          \"id\": 7,\n          \"title\": \"Model Development\",\n          \"description\": \"Develop and train ML models\",\n          \"status\": \"in_progress\",\n          \"priority\": \"high\",\n          \"estimated_hours\": 200,\n          \"actual_hours\": 150,\n          \"assigned_to\": 5,\n          \"due_date\": \"2025-04-30\"\n        }\n      ],\n      \"kpis\": [\n        {\n          \"id\": 3,\n          \"name\": \"Research Milestones\",\n          \"description\": \"Number of research milestones achieved\",\n          \"unit\": \"count\",\n          \"target_value\": 8,\n          \"current_value\": 5,\n          \"category\": \"research\"\n        }\n      ],\n      \"expenses\": [\n        {\n          \"id\": 3,\n          \"description\": \"GPU Computing Resources\",\n          \"amount\": 5000.00,\n          \"category\": \"infrastructure\",\n          \"expense_date\": \"2024-11-01\",\n          \"status\": \"approved\",\n          \"user\": {\n            \"id\": 5,\n            \"full_name\": \"David Wilson\"\n          }\n        },\n        {\n          \"id\": 4,\n          \"description\": \"Conference Registration\",\n          \"amount\": 1200.00,\n          \"category\": \"travel\",\n          \"expense_date\": \"2025-01-15\",\n          \"status\": \"pending\",\n          \"user\": {\n            \"id\": 4,\n            \"full_name\": \"Dr. Emily Chen\"\n          }\n        }\n      ]\n    }\n  ],\n  \"pagination\": {\n    \"page\": 1,\n    \"per_page\": 10,\n    \"total\": 3,\n    \"pages\": 1\n  }\n}\n"}