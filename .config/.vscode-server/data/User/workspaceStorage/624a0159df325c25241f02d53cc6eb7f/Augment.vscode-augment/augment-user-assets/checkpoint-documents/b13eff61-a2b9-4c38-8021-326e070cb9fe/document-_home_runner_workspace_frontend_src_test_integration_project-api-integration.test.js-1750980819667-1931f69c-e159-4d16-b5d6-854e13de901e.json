{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/integration/project-api-integration.test.js"}, "modifiedCode": "import { describe, it, expect, beforeEach, vi } from 'vitest'\nimport { mount } from '@vue/test-utils'\nimport { createPinia, setActivePinia } from 'pinia'\nimport { useProjectsStore } from '@/stores/projects'\nimport Projects from '@/views/projects/Projects.vue'\nimport ProjectEdit from '@/views/projects/ProjectEdit.vue'\n\n// Mock router\nconst mockRouter = {\n  push: vi.fn(),\n  replace: vi.fn()\n}\n\nvi.mock('vue-router', () => ({\n  useRouter: () => mockRouter,\n  useRoute: () => ({ params: { id: '1' } })\n}))\n\ndescribe('Project API Integration Tests', () => {\n  let pinia\n  let projectsStore\n\n  beforeEach(() => {\n    // Setup Pinia\n    pinia = createPinia()\n    setActivePinia(pinia)\n    projectsStore = useProjectsStore()\n\n    // Mock fetch globally\n    global.fetch = vi.fn()\n    \n    // Clear all mocks\n    vi.clearAllMocks()\n  })\n\n  describe('Projects List Integration', () => {\n    it('should load projects from API and display them', async () => {\n      // Mock API response\n      const mockProjects = [\n        {\n          id: 1,\n          name: 'Project Alpha',\n          status: 'active',\n          budget: 50000,\n          client: { name: 'Client A' }\n        },\n        {\n          id: 2,\n          name: 'Project Beta',\n          status: 'planning',\n          budget: 30000,\n          client: { name: 'Client B' }\n        }\n      ]\n\n      fetch.mockResolvedValueOnce({\n        ok: true,\n        json: () => Promise.resolve({\n          success: true,\n          data: { projects: mockProjects }\n        })\n      })\n\n      // Mount component\n      const wrapper = mount(Projects, {\n        global: {\n          plugins: [pinia],\n          mocks: {\n            $router: mockRouter\n          }\n        }\n      })\n\n      // Wait for API call to complete\n      await wrapper.vm.$nextTick()\n      await new Promise(resolve => setTimeout(resolve, 0))\n\n      // Verify API was called\n      expect(fetch).toHaveBeenCalledWith('/api/projects')\n\n      // Verify store was updated\n      expect(projectsStore.projects).toEqual(mockProjects)\n      expect(projectsStore.loading).toBe(false)\n\n      // Verify UI displays projects\n      expect(wrapper.text()).toContain('Project Alpha')\n      expect(wrapper.text()).toContain('Project Beta')\n      expect(wrapper.text()).toContain('Client A')\n      expect(wrapper.text()).toContain('Client B')\n    })\n\n    it('should handle API errors gracefully', async () => {\n      // Mock API error\n      fetch.mockRejectedValueOnce(new Error('Network error'))\n\n      const wrapper = mount(Projects, {\n        global: {\n          plugins: [pinia],\n          mocks: {\n            $router: mockRouter\n          }\n        }\n      })\n\n      await wrapper.vm.$nextTick()\n      await new Promise(resolve => setTimeout(resolve, 0))\n\n      // Verify error state\n      expect(projectsStore.error).toBeTruthy()\n      expect(projectsStore.loading).toBe(false)\n      expect(wrapper.text()).toContain('Error loading projects')\n    })\n\n    it('should filter projects based on search', async () => {\n      const mockProjects = [\n        { id: 1, name: 'Alpha Project', status: 'active' },\n        { id: 2, name: 'Beta Project', status: 'planning' },\n        { id: 3, name: 'Gamma Task', status: 'active' }\n      ]\n\n      fetch.mockResolvedValueOnce({\n        ok: true,\n        json: () => Promise.resolve({\n          success: true,\n          data: { projects: mockProjects }\n        })\n      })\n\n      const wrapper = mount(Projects, {\n        global: {\n          plugins: [pinia],\n          mocks: {\n            $router: mockRouter\n          }\n        }\n      })\n\n      await wrapper.vm.$nextTick()\n\n      // Search for \"Project\"\n      const searchInput = wrapper.find('[data-testid=\"search-input\"]')\n      await searchInput.setValue('Project')\n\n      // Should show only projects with \"Project\" in name\n      expect(wrapper.text()).toContain('Alpha Project')\n      expect(wrapper.text()).toContain('Beta Project')\n      expect(wrapper.text()).not.toContain('Gamma Task')\n    })\n  })\n\n  describe('Project Creation Integration', () => {\n    it('should create project via API and redirect', async () => {\n      const newProject = {\n        name: 'New Integration Project',\n        description: 'Test project for integration',\n        budget: 25000,\n        client_id: 1\n      }\n\n      // Mock successful creation\n      fetch.mockResolvedValueOnce({\n        ok: true,\n        json: () => Promise.resolve({\n          success: true,\n          data: { ...newProject, id: 3 }\n        })\n      })\n\n      const wrapper = mount(ProjectEdit, {\n        global: {\n          plugins: [pinia],\n          mocks: {\n            $router: mockRouter\n          }\n        }\n      })\n\n      // Fill form\n      await wrapper.find('[data-testid=\"project-name\"]').setValue(newProject.name)\n      await wrapper.find('[data-testid=\"project-description\"]').setValue(newProject.description)\n      await wrapper.find('[data-testid=\"project-budget\"]').setValue(newProject.budget.toString())\n\n      // Submit form\n      await wrapper.find('[data-testid=\"save-button\"]').trigger('click')\n      await wrapper.vm.$nextTick()\n\n      // Verify API call\n      expect(fetch).toHaveBeenCalledWith('/api/projects', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(newProject)\n      })\n\n      // Verify redirect\n      expect(mockRouter.push).toHaveBeenCalledWith('/app/projects/3')\n    })\n\n    it('should handle validation errors from API', async () => {\n      // Mock validation error response\n      fetch.mockResolvedValueOnce({\n        ok: false,\n        status: 400,\n        json: () => Promise.resolve({\n          success: false,\n          errors: {\n            name: ['Project name is required'],\n            budget: ['Budget must be positive']\n          }\n        })\n      })\n\n      const wrapper = mount(ProjectEdit, {\n        global: {\n          plugins: [pinia],\n          mocks: {\n            $router: mockRouter\n          }\n        }\n      })\n\n      // Submit empty form\n      await wrapper.find('[data-testid=\"save-button\"]').trigger('click')\n      await wrapper.vm.$nextTick()\n\n      // Verify validation errors are displayed\n      expect(wrapper.text()).toContain('Project name is required')\n      expect(wrapper.text()).toContain('Budget must be positive')\n    })\n  })\n\n  describe('Real-time Data Updates', () => {\n    it('should update UI when store data changes', async () => {\n      const wrapper = mount(Projects, {\n        global: {\n          plugins: [pinia],\n          mocks: {\n            $router: mockRouter\n          }\n        }\n      })\n\n      // Initially no projects\n      expect(wrapper.text()).toContain('No projects found')\n\n      // Add project to store\n      projectsStore.projects = [\n        { id: 1, name: 'Dynamic Project', status: 'active' }\n      ]\n\n      await wrapper.vm.$nextTick()\n\n      // UI should update\n      expect(wrapper.text()).toContain('Dynamic Project')\n      expect(wrapper.text()).not.toContain('No projects found')\n    })\n\n    it('should reflect loading states', async () => {\n      const wrapper = mount(Projects, {\n        global: {\n          plugins: [pinia],\n          mocks: {\n            $router: mockRouter\n          }\n        }\n      })\n\n      // Set loading state\n      projectsStore.loading = true\n      await wrapper.vm.$nextTick()\n\n      expect(wrapper.find('[data-testid=\"loading-spinner\"]').exists()).toBe(true)\n\n      // Clear loading state\n      projectsStore.loading = false\n      await wrapper.vm.$nextTick()\n\n      expect(wrapper.find('[data-testid=\"loading-spinner\"]').exists()).toBe(false)\n    })\n  })\n\n  describe('Error Handling Integration', () => {\n    it('should display API error messages', async () => {\n      fetch.mockResolvedValueOnce({\n        ok: false,\n        status: 500,\n        json: () => Promise.resolve({\n          success: false,\n          error: 'Internal server error'\n        })\n      })\n\n      const wrapper = mount(Projects, {\n        global: {\n          plugins: [pinia],\n          mocks: {\n            $router: mockRouter\n          }\n        }\n      })\n\n      await wrapper.vm.$nextTick()\n      await new Promise(resolve => setTimeout(resolve, 0))\n\n      expect(wrapper.text()).toContain('Internal server error')\n    })\n\n    it('should retry failed requests', async () => {\n      // First call fails\n      fetch\n        .mockRejectedValueOnce(new Error('Network error'))\n        .mockResolvedValueOnce({\n          ok: true,\n          json: () => Promise.resolve({\n            success: true,\n            data: { projects: [] }\n          })\n        })\n\n      const wrapper = mount(Projects, {\n        global: {\n          plugins: [pinia],\n          mocks: {\n            $router: mockRouter\n          }\n        }\n      })\n\n      await wrapper.vm.$nextTick()\n\n      // Click retry button\n      await wrapper.find('[data-testid=\"retry-button\"]').trigger('click')\n      await wrapper.vm.$nextTick()\n\n      // Should make second API call\n      expect(fetch).toHaveBeenCalledTimes(2)\n    })\n  })\n\n  describe('Pagination Integration', () => {\n    it('should handle paginated API responses', async () => {\n      const mockResponse = {\n        success: true,\n        data: {\n          projects: [\n            { id: 1, name: 'Project 1' },\n            { id: 2, name: 'Project 2' }\n          ],\n          pagination: {\n            page: 1,\n            per_page: 10,\n            total: 25,\n            pages: 3\n          }\n        }\n      }\n\n      fetch.mockResolvedValueOnce({\n        ok: true,\n        json: () => Promise.resolve(mockResponse)\n      })\n\n      const wrapper = mount(Projects, {\n        global: {\n          plugins: [pinia],\n          mocks: {\n            $router: mockRouter\n          }\n        }\n      })\n\n      await wrapper.vm.$nextTick()\n\n      // Verify pagination info is displayed\n      expect(wrapper.text()).toContain('Page 1 of 3')\n      expect(wrapper.text()).toContain('25 total projects')\n\n      // Test page navigation\n      await wrapper.find('[data-testid=\"next-page\"]').trigger('click')\n\n      expect(fetch).toHaveBeenCalledWith('/api/projects?page=2')\n    })\n  })\n})\n"}