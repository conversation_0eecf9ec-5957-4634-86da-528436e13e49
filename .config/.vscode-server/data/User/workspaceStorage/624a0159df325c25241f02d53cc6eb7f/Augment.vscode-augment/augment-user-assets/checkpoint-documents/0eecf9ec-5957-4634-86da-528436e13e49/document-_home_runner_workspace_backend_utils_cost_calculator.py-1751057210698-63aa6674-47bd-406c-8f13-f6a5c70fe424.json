{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/utils/cost_calculator.py"}, "originalCode": "\"\"\"\nUtilità per calcolo costi e ricavi dei progetti\n\"\"\"\nfrom datetime import date, datetime\nfrom models import PersonnelRate, ProjectExpense, TimesheetEntry, Project\nfrom extensions import db\n\n\ndef get_user_daily_rate_for_date(user_id, target_date):\n    \"\"\"\n    Ottiene la tariffa giornaliera valida per un utente in una data specifica\n\n    Args:\n        user_id (int): ID dell'utente\n        target_date (date): Data per cui cercare la tariffa\n\n    Returns:\n        float: Tariffa giornaliera o None se non trovata\n    \"\"\"\n    rate = PersonnelRate.query.filter(\n        PersonnelRate.user_id == user_id,\n        PersonnelRate.valid_from <= target_date,\n        db.or_(\n            PersonnelRate.valid_to.is_(None),\n            PersonnelRate.valid_to >= target_date\n        )\n    ).order_by(PersonnelRate.valid_from.desc()).first()\n\n    return rate.daily_rate if rate else None\n\n\ndef get_user_current_daily_rate(user_id):\n    \"\"\"\n    Ottiene la tariffa giornaliera attuale per un utente\n\n    Args:\n        user_id (int): ID dell'utente\n\n    Returns:\n        float: Tariffa giornaliera attuale o None\n    \"\"\"\n    return get_user_daily_rate_for_date(user_id, date.today())\n\n\ndef calculate_personnel_cost_for_period(project_id, start_date, end_date):\n    \"\"\"\n    Calcola il costo del personale per un progetto in un periodo\n\n    Args:\n        project_id (int): ID del progetto\n        start_date (date): Data inizio\n        end_date (date): Data fine\n\n    Returns:\n        dict: {\n            'total_cost': float,\n            'total_days': int,\n            'user_costs': [{'user_id': int, 'days': int, 'cost': float}]\n        }\n    \"\"\"\n    # Ottieni tutti i timesheet del progetto nel periodo\n    timesheets = TimesheetEntry.query.filter(\n        TimesheetEntry.project_id == project_id,\n        TimesheetEntry.date >= start_date,\n        TimesheetEntry.date <= end_date\n    ).all()\n\n    # Raggruppa per utente e data\n    user_days = {}\n    for ts in timesheets:\n        if ts.user_id not in user_days:\n            user_days[ts.user_id] = set()\n        user_days[ts.user_id].add(ts.date)\n\n    # Calcola costi per utente\n    user_costs = []\n    total_cost = 0.0\n    total_days = 0\n\n    for user_id, dates in user_days.items():\n        user_total_cost = 0.0\n        days_worked = len(dates)\n\n        # Calcola costo per ogni giorno lavorato\n        for work_date in dates:\n            daily_rate = get_user_daily_rate_for_date(user_id, work_date)\n            if daily_rate:\n                user_total_cost += daily_rate\n\n        user_costs.append({\n            'user_id': user_id,\n            'days': days_worked,\n            'cost': user_total_cost\n        })\n\n        total_cost += user_total_cost\n        total_days += days_worked\n\n    return {\n        'total_cost': total_cost,\n        'total_days': total_days,\n        'user_costs': user_costs\n    }\n\n\ndef calculate_project_expenses(project_id, start_date=None, end_date=None, billing_type=None):\n    \"\"\"\n    Calcola le spese di progetto (non personale)\n\n    Args:\n        project_id (int): ID del progetto\n        start_date (date, optional): Data inizio filtro\n        end_date (date, optional): Data fine filtro\n        billing_type (str, optional): Tipo fatturazione (billable, non-billable, reimbursable)\n\n    Returns:\n        dict: {\n            'total_amount': float,\n            'by_category': dict,\n            'by_billing_type': dict\n        }\n    \"\"\"\n    query = ProjectExpense.query.filter(ProjectExpense.project_id == project_id)\n\n    if start_date:\n        query = query.filter(ProjectExpense.date >= start_date)\n    if end_date:\n        query = query.filter(ProjectExpense.date <= end_date)\n    if billing_type:\n        query = query.filter(ProjectExpense.billing_type == billing_type)\n\n    expenses = query.all()\n\n    total_amount = sum(exp.amount for exp in expenses)\n\n    # Raggruppa per categoria\n    by_category = {}\n    for exp in expenses:\n        if exp.category not in by_category:\n            by_category[exp.category] = 0.0\n        by_category[exp.category] += exp.amount\n\n    # Raggruppa per tipo fatturazione\n    by_billing_type = {}\n    for exp in expenses:\n        if exp.billing_type not in by_billing_type:\n            by_billing_type[exp.billing_type] = 0.0\n        by_billing_type[exp.billing_type] += exp.amount\n\n    return {\n        'total_amount': total_amount,\n        'by_category': by_category,\n        'by_billing_type': by_billing_type\n    }\n\n\ndef calculate_project_profitability(project_id):\n    \"\"\"\n    Calcola la redditività completa di un progetto\n\n    Args:\n        project_id (int): ID del progetto\n\n    Returns:\n        dict: Analisi completa costi/ricavi\n    \"\"\"\n    project = Project.query.get(project_id)\n    if not project:\n        return None\n\n    # Calcola costi personale\n    personnel_costs = calculate_personnel_cost_for_period(\n        project_id,\n        project.start_date or date(2020, 1, 1),\n        project.end_date or date.today()\n    )\n\n    # Calcola altre spese\n    other_expenses = calculate_project_expenses(project_id)\n\n    # Calcola ricavi potenziali\n    potential_revenue = 0.0\n    if project.is_billable and project.client_daily_rate:\n        potential_revenue = project.client_daily_rate * personnel_costs['total_days']\n\n    # Totali\n    total_costs = personnel_costs['total_cost'] + other_expenses['total_amount']\n    billable_expenses = calculate_project_expenses(project_id, billing_type='billable')['total_amount']\n\n    # Calcola margini\n    gross_margin = potential_revenue - personnel_costs['total_cost']\n    net_margin = potential_revenue - total_costs\n\n    return {\n        'project': {\n            'id': project_id,\n            'name': project.name,\n            'is_billable': project.is_billable,\n            'client_daily_rate': project.client_daily_rate,\n            'budget': project.budget\n        },\n        'personnel': personnel_costs,\n        'expenses': other_expenses,\n        'revenue': {\n            'potential': potential_revenue,\n            'billable_expenses': billable_expenses,\n            'total_billable': potential_revenue + billable_expenses\n        },\n        'profitability': {\n            'total_costs': total_costs,\n            'gross_margin': gross_margin,\n            'net_margin': net_margin,\n            'gross_margin_percentage': (gross_margin / potential_revenue * 100) if potential_revenue > 0 else 0,\n            'net_margin_percentage': (net_margin / potential_revenue * 100) if potential_revenue > 0 else 0\n        }\n    }\n\n\ndef calculate_project_kpis(project_id):\n    \"\"\"\n    Calcola tutti i KPI per un progetto\n\n    Returns:\n        dict: {\n            'margin_percentage': float,\n            'utilization_rate': float,\n            'cost_per_hour': float,\n            'cost_revenue_ratio': float\n        }\n    \"\"\"\n    profitability = calculate_project_profitability(project_id)\n    if not profitability:\n        return {}\n\n    # Recupera l'oggetto project per accedere alle date\n    project = Project.query.get(project_id)\n    if not project:\n        return {}\n\n    # Margine Netto %\n    margin_percentage = profitability['profitability']['net_margin_percentage']\n\n    # Utilization Rate % - Calcolo ibrido basato su dati disponibili\n    total_days = profitability['personnel']['total_days']\n    team_size = len(profitability['personnel']['user_costs'])\n\n    # Priorità 1: Budget-based (se disponibili budget e tariffa cliente)\n    if profitability['project']['budget'] and profitability['project']['client_daily_rate']:\n        theoretical_days = profitability['project']['budget'] / profitability['project']['client_daily_rate']\n    # Priorità 2: Timeline-based (se disponibili date progetto)\n    elif project.start_date and project.end_date:\n        project_duration_days = (project.end_date - project.start_date).days\n        working_days = project_duration_days * 5/7  # Solo giorni lavorativi (esclude weekend)\n        theoretical_days = team_size * working_days * 0.8  # 80% efficienza attesa del team\n    # Fallback: Metodo precedente migliorato\n    else:\n        theoretical_days = team_size * 22 if team_size > 0 else 1  # 22 giorni lavorativi/mese medio\n\n    utilization_rate = (total_days / theoretical_days * 100) if theoretical_days > 0 else 0\n\n    # Costo per Ora\n    total_costs = profitability['profitability']['total_costs']\n    total_hours = sum(ts.hours for ts in TimesheetEntry.query.filter_by(project_id=project_id).all())\n    cost_per_hour = (total_costs / total_hours) if total_hours > 0 else 0\n\n    # Cost/Revenue Ratio\n    total_revenue = profitability['revenue']['potential']\n    cost_revenue_ratio = (total_costs / total_revenue) if total_revenue > 0 else float('inf')\n\n    return {\n        'margin_percentage': round(margin_percentage, 1),\n        'utilization_rate': round(utilization_rate, 1),\n        'cost_per_hour': round(cost_per_hour, 2),\n        'cost_revenue_ratio': round(cost_revenue_ratio, 3)\n    }\n\n\ndef get_kpi_status(kpi_value, kpi_name, project_type, project_id=None):\n    \"\"\"\n    Determina lo status di un KPI (good, warning, critical)\n\n    Args:\n        kpi_value: Valore attuale del KPI\n        kpi_name: Nome del KPI\n        project_type: Tipologia progetto\n        project_id: ID progetto (per KPI personalizzati)\n\n    Returns:\n        dict: {'status': 'good|warning|critical', 'color': 'green|yellow|red'}\n    \"\"\"\n    from models import ProjectKPITemplate, ProjectKPITarget\n\n    # Prima cerca KPI personalizzati per il progetto\n    if project_id:\n        custom_kpi = ProjectKPITarget.query.filter_by(\n            project_id=project_id,\n            kpi_name=kpi_name\n        ).first()\n\n        if custom_kpi:\n            if kpi_value >= custom_kpi.target_value:\n                return {'status': 'good', 'color': 'green'}\n            elif kpi_value >= custom_kpi.warning_threshold:\n                return {'status': 'warning', 'color': 'yellow'}\n            else:\n                return {'status': 'critical', 'color': 'red'}\n\n    # Fallback su template default\n    template = ProjectKPITemplate.query.filter_by(\n        project_type=project_type,\n        kpi_name=kpi_name,\n        is_active=True\n    ).first()\n\n    if template:\n        if kpi_value >= template.target_min:\n            return {'status': 'good', 'color': 'green'}\n        elif kpi_value >= template.warning_threshold:\n            return {'status': 'warning', 'color': 'yellow'}\n        else:\n            return {'status': 'critical', 'color': 'red'}\n\n    # Default neutro se non ci sono template\n    return {'status': 'unknown', 'color': 'gray'}\n\n\ndef get_default_daily_rates():\n    \"\"\"\n    Restituisce tariffe giornaliere di default per ruolo\n    Usato quando non ci sono tariffe specifiche\n    \"\"\"\n    return {\n        'admin': 400.0,\n        'manager': 350.0,\n        'employee': 250.0,\n        'intern': 100.0\n    }\n\n\ndef get_default_kpi_templates():\n    \"\"\"\n    Restituisce template KPI di default per tipologie progetto\n    \"\"\"\n    return {\n        'service': {\n            'margin_percentage': {'target_min': 25, 'target_max': 40, 'warning_threshold': 15, 'unit': '%'},\n            'utilization_rate': {'target_min': 75, 'target_max': 85, 'warning_threshold': 60, 'unit': '%'},\n            'cost_per_hour': {'target_min': 30, 'target_max': 50, 'warning_threshold': 60, 'unit': '€'},\n            'cost_revenue_ratio': {'target_min': 0.6, 'target_max': 0.75, 'warning_threshold': 0.85, 'unit': 'ratio'}\n        },\n        'license': {\n            'margin_percentage': {'target_min': 70, 'target_max': 90, 'warning_threshold': 50, 'unit': '%'},\n            'utilization_rate': {'target_min': 60, 'target_max': 80, 'warning_threshold': 40, 'unit': '%'},\n            'cost_per_hour': {'target_min': 20, 'target_max': 40, 'warning_threshold': 50, 'unit': '€'},\n            'cost_revenue_ratio': {'target_min': 0.1, 'target_max': 0.3, 'warning_threshold': 0.5, 'unit': 'ratio'}\n        },\n        'consulting': {\n            'margin_percentage': {'target_min': 40, 'target_max': 60, 'warning_threshold': 25, 'unit': '%'},\n            'utilization_rate': {'target_min': 80, 'target_max': 90, 'warning_threshold': 65, 'unit': '%'},\n            'cost_per_hour': {'target_min': 40, 'target_max': 70, 'warning_threshold': 80, 'unit': '€'},\n            'cost_revenue_ratio': {'target_min': 0.4, 'target_max': 0.6, 'warning_threshold': 0.75, 'unit': 'ratio'}\n        },\n        'product': {\n            'margin_percentage': {'target_min': 30, 'target_max': 50, 'warning_threshold': 20, 'unit': '%'},\n            'utilization_rate': {'target_min': 70, 'target_max': 85, 'warning_threshold': 55, 'unit': '%'},\n            'cost_per_hour': {'target_min': 25, 'target_max': 45, 'warning_threshold': 55, 'unit': '€'},\n            'cost_revenue_ratio': {'target_min': 0.5, 'target_max': 0.7, 'warning_threshold': 0.8, 'unit': 'ratio'}\n        },\n        'rd': {\n            'margin_percentage': {'target_min': 0, 'target_max': 20, 'warning_threshold': -20, 'unit': '%'},\n            'utilization_rate': {'target_min': 60, 'target_max': 80, 'warning_threshold': 40, 'unit': '%'},\n            'cost_per_hour': {'target_min': 50, 'target_max': 80, 'warning_threshold': 100, 'unit': '€'},\n            'cost_revenue_ratio': {'target_min': 0.8, 'target_max': 1.2, 'warning_threshold': 1.5, 'unit': 'ratio'}\n        },\n        'internal': {\n            'margin_percentage': {'target_min': 0, 'target_max': 0, 'warning_threshold': -10, 'unit': '%'},\n            'utilization_rate': {'target_min': 70, 'target_max': 85, 'warning_threshold': 50, 'unit': '%'},\n            'cost_per_hour': {'target_min': 30, 'target_max': 50, 'warning_threshold': 70, 'unit': '€'},\n            'cost_revenue_ratio': {'target_min': 1.0, 'target_max': 1.0, 'warning_threshold': 1.2, 'unit': 'ratio'}\n        }\n    }\n", "modifiedCode": "\"\"\"\nUtilità per calcolo costi e ricavi dei progetti\n\"\"\"\nfrom datetime import date, datetime\nfrom models import PersonnelRate, ProjectExpense, TimesheetEntry, Project\nfrom extensions import db\n\n\ndef get_user_daily_rate_for_date(user_id, target_date):\n    \"\"\"\n    Ottiene la tariffa giornaliera valida per un utente in una data specifica\n\n    Args:\n        user_id (int): ID dell'utente\n        target_date (date): Data per cui cercare la tariffa\n\n    Returns:\n        float: Tariffa giornaliera o None se non trovata\n    \"\"\"\n    rate = PersonnelRate.query.filter(\n        PersonnelRate.user_id == user_id,\n        PersonnelRate.valid_from <= target_date,\n        db.or_(\n            PersonnelRate.valid_to.is_(None),\n            PersonnelRate.valid_to >= target_date\n        )\n    ).order_by(PersonnelRate.valid_from.desc()).first()\n\n    return rate.daily_rate if rate else None\n\n\ndef get_user_current_daily_rate(user_id):\n    \"\"\"\n    Ottiene la tariffa giornaliera attuale per un utente\n\n    Args:\n        user_id (int): ID dell'utente\n\n    Returns:\n        float: Tariffa giornaliera attuale o None\n    \"\"\"\n    return get_user_daily_rate_for_date(user_id, date.today())\n\n\ndef calculate_personnel_cost_for_period(project_id, start_date, end_date):\n    \"\"\"\n    Calcola il costo del personale per un progetto in un periodo\n\n    Args:\n        project_id (int): ID del progetto\n        start_date (date): Data inizio\n        end_date (date): Data fine\n\n    Returns:\n        dict: {\n            'total_cost': float,\n            'total_days': int,\n            'user_costs': [{'user_id': int, 'days': int, 'cost': float}]\n        }\n    \"\"\"\n    # Ottieni tutti i timesheet del progetto nel periodo\n    timesheets = TimesheetEntry.query.filter(\n        TimesheetEntry.project_id == project_id,\n        TimesheetEntry.date >= start_date,\n        TimesheetEntry.date <= end_date\n    ).all()\n\n    # Raggruppa per utente e data\n    user_days = {}\n    for ts in timesheets:\n        if ts.user_id not in user_days:\n            user_days[ts.user_id] = set()\n        user_days[ts.user_id].add(ts.date)\n\n    # Calcola costi per utente\n    user_costs = []\n    total_cost = 0.0\n    total_days = 0\n\n    for user_id, dates in user_days.items():\n        user_total_cost = 0.0\n        days_worked = len(dates)\n\n        # Calcola costo per ogni giorno lavorato\n        for work_date in dates:\n            daily_rate = get_user_daily_rate_for_date(user_id, work_date)\n            if daily_rate:\n                user_total_cost += daily_rate\n\n        user_costs.append({\n            'user_id': user_id,\n            'days': days_worked,\n            'cost': user_total_cost\n        })\n\n        total_cost += user_total_cost\n        total_days += days_worked\n\n    return {\n        'total_cost': total_cost,\n        'total_days': total_days,\n        'user_costs': user_costs\n    }\n\n\ndef calculate_project_expenses(project_id, start_date=None, end_date=None, billing_type=None):\n    \"\"\"\n    Calcola le spese di progetto (non personale)\n\n    Args:\n        project_id (int): ID del progetto\n        start_date (date, optional): Data inizio filtro\n        end_date (date, optional): Data fine filtro\n        billing_type (str, optional): Tipo fatturazione (billable, non-billable, reimbursable)\n\n    Returns:\n        dict: {\n            'total_amount': float,\n            'by_category': dict,\n            'by_billing_type': dict\n        }\n    \"\"\"\n    query = ProjectExpense.query.filter(ProjectExpense.project_id == project_id)\n\n    if start_date:\n        query = query.filter(ProjectExpense.date >= start_date)\n    if end_date:\n        query = query.filter(ProjectExpense.date <= end_date)\n    if billing_type:\n        query = query.filter(ProjectExpense.billing_type == billing_type)\n\n    expenses = query.all()\n\n    total_amount = sum(exp.amount for exp in expenses)\n\n    # Raggruppa per categoria\n    by_category = {}\n    for exp in expenses:\n        if exp.category not in by_category:\n            by_category[exp.category] = 0.0\n        by_category[exp.category] += exp.amount\n\n    # Raggruppa per tipo fatturazione\n    by_billing_type = {}\n    for exp in expenses:\n        if exp.billing_type not in by_billing_type:\n            by_billing_type[exp.billing_type] = 0.0\n        by_billing_type[exp.billing_type] += exp.amount\n\n    return {\n        'total_amount': total_amount,\n        'by_category': by_category,\n        'by_billing_type': by_billing_type\n    }\n\n\ndef calculate_project_profitability(project_id):\n    \"\"\"\n    Calcola la redditività completa di un progetto\n\n    Args:\n        project_id (int): ID del progetto\n\n    Returns:\n        dict: Analisi completa costi/ricavi\n    \"\"\"\n    project = Project.query.get(project_id)\n    if not project:\n        return None\n\n    # Calcola costi personale\n    personnel_costs = calculate_personnel_cost_for_period(\n        project_id,\n        project.start_date or date(2020, 1, 1),\n        project.end_date or date.today()\n    )\n\n    # Calcola altre spese\n    other_expenses = calculate_project_expenses(project_id)\n\n    # Calcola ricavi potenziali\n    potential_revenue = 0.0\n    if project.is_billable and project.client_daily_rate:\n        potential_revenue = project.client_daily_rate * personnel_costs['total_days']\n\n    # Totali\n    total_costs = personnel_costs['total_cost'] + other_expenses['total_amount']\n    billable_expenses = calculate_project_expenses(project_id, billing_type='billable')['total_amount']\n\n    # Calcola margini\n    gross_margin = potential_revenue - personnel_costs['total_cost']\n    net_margin = potential_revenue - total_costs\n\n    return {\n        'project': {\n            'id': project_id,\n            'name': project.name,\n            'is_billable': project.is_billable,\n            'client_daily_rate': project.client_daily_rate,\n            'budget': project.budget\n        },\n        'personnel': personnel_costs,\n        'expenses': other_expenses,\n        'revenue': {\n            'potential': potential_revenue,\n            'billable_expenses': billable_expenses,\n            'total_billable': potential_revenue + billable_expenses\n        },\n        'profitability': {\n            'total_costs': total_costs,\n            'gross_margin': gross_margin,\n            'net_margin': net_margin,\n            'gross_margin_percentage': (gross_margin / potential_revenue * 100) if potential_revenue > 0 else 0,\n            'net_margin_percentage': (net_margin / potential_revenue * 100) if potential_revenue > 0 else 0\n        }\n    }\n\n\ndef calculate_project_kpis(project_id):\n    \"\"\"\n    Calcola tutti i KPI per un progetto\n\n    Returns:\n        dict: {\n            'margin_percentage': float,\n            'utilization_rate': float,\n            'cost_per_hour': float,\n            'cost_revenue_ratio': float\n        }\n    \"\"\"\n    profitability = calculate_project_profitability(project_id)\n    if not profitability:\n        return {}\n\n    # Recupera l'oggetto project per accedere alle date\n    project = Project.query.get(project_id)\n    if not project:\n        return {}\n\n    # Margine Netto %\n    margin_percentage = profitability['profitability']['net_margin_percentage']\n\n    # Utilization Rate % - Calcolo ibrido basato su dati disponibili\n    total_days = profitability['personnel']['total_days']\n    team_size = len(profitability['personnel']['user_costs'])\n\n    # Priorità 1: Budget-based (se disponibili budget e tariffa cliente)\n    if profitability['project']['budget'] and profitability['project']['client_daily_rate']:\n        theoretical_days = profitability['project']['budget'] / profitability['project']['client_daily_rate']\n    # Priorità 2: Timeline-based (se disponibili date progetto)\n    elif project.start_date and project.end_date:\n        project_duration_days = (project.end_date - project.start_date).days\n        working_days = project_duration_days * 5/7  # Solo giorni lavorativi (esclude weekend)\n        theoretical_days = team_size * working_days * 0.8  # 80% efficienza attesa del team\n    # Fallback: Metodo precedente migliorato\n    else:\n        theoretical_days = team_size * 22 if team_size > 0 else 1  # 22 giorni lavorativi/mese medio\n\n    utilization_rate = (total_days / theoretical_days * 100) if theoretical_days > 0 else 0\n\n    # Costo per Ora\n    total_costs = profitability['profitability']['total_costs']\n    total_hours = sum(ts.hours for ts in TimesheetEntry.query.filter_by(project_id=project_id).all())\n    cost_per_hour = (total_costs / total_hours) if total_hours > 0 else 0\n\n    # Cost/Revenue Ratio\n    total_revenue = profitability['revenue']['potential']\n    cost_revenue_ratio = (total_costs / total_revenue) if total_revenue > 0 else float('inf')\n\n    return {\n        'margin_percentage': round(margin_percentage, 1),\n        'utilization_rate': round(utilization_rate, 1),\n        'cost_per_hour': round(cost_per_hour, 2),\n        'cost_revenue_ratio': round(cost_revenue_ratio, 3)\n    }\n\n\ndef get_kpi_status(kpi_value, kpi_name, project_type, project_id=None):\n    \"\"\"\n    Determina lo status di un KPI (good, warning, critical)\n\n    Args:\n        kpi_value: Valore attuale del KPI\n        kpi_name: Nome del KPI\n        project_type: Tipologia progetto\n        project_id: ID progetto (per KPI personalizzati)\n\n    Returns:\n        dict: {'status': 'good|warning|critical', 'color': 'green|yellow|red'}\n    \"\"\"\n    from models import ProjectKPITemplate, ProjectKPITarget\n\n    # Prima cerca KPI personalizzati per il progetto\n    if project_id:\n        custom_kpi = ProjectKPITarget.query.filter_by(\n            project_id=project_id,\n            kpi_name=kpi_name\n        ).first()\n\n        if custom_kpi:\n            if kpi_value >= custom_kpi.target_value:\n                return {'status': 'good', 'color': 'green'}\n            elif kpi_value >= custom_kpi.warning_threshold:\n                return {'status': 'warning', 'color': 'yellow'}\n            else:\n                return {'status': 'critical', 'color': 'red'}\n\n    # Fallback su template default\n    template = ProjectKPITemplate.query.filter_by(\n        project_type=project_type,\n        kpi_name=kpi_name,\n        is_active=True\n    ).first()\n\n    if template:\n        if kpi_value >= template.target_min:\n            return {'status': 'good', 'color': 'green'}\n        elif kpi_value >= template.warning_threshold:\n            return {'status': 'warning', 'color': 'yellow'}\n        else:\n            return {'status': 'critical', 'color': 'red'}\n\n    # Default neutro se non ci sono template\n    return {'status': 'unknown', 'color': 'gray'}\n\n\ndef get_default_daily_rates():\n    \"\"\"\n    Restituisce tariffe giornaliere di default per ruolo\n    Usato quando non ci sono tariffe specifiche\n    \"\"\"\n    return {\n        'admin': 400.0,\n        'manager': 350.0,\n        'employee': 250.0,\n        'intern': 100.0\n    }\n\n\ndef get_default_kpi_templates():\n    \"\"\"\n    Restituisce template KPI di default per tipologie progetto\n    \"\"\"\n    return {\n        'service': {\n            'margin_percentage': {'target_min': 25, 'target_max': 40, 'warning_threshold': 15, 'unit': '%'},\n            'utilization_rate': {'target_min': 75, 'target_max': 85, 'warning_threshold': 60, 'unit': '%'},\n            'cost_per_hour': {'target_min': 30, 'target_max': 50, 'warning_threshold': 60, 'unit': '€'},\n            'cost_revenue_ratio': {'target_min': 0.6, 'target_max': 0.75, 'warning_threshold': 0.85, 'unit': 'ratio'}\n        },\n        'license': {\n            'margin_percentage': {'target_min': 70, 'target_max': 90, 'warning_threshold': 50, 'unit': '%'},\n            'utilization_rate': {'target_min': 60, 'target_max': 80, 'warning_threshold': 40, 'unit': '%'},\n            'cost_per_hour': {'target_min': 20, 'target_max': 40, 'warning_threshold': 50, 'unit': '€'},\n            'cost_revenue_ratio': {'target_min': 0.1, 'target_max': 0.3, 'warning_threshold': 0.5, 'unit': 'ratio'}\n        },\n        'consulting': {\n            'margin_percentage': {'target_min': 40, 'target_max': 60, 'warning_threshold': 25, 'unit': '%'},\n            'utilization_rate': {'target_min': 80, 'target_max': 90, 'warning_threshold': 65, 'unit': '%'},\n            'cost_per_hour': {'target_min': 40, 'target_max': 70, 'warning_threshold': 80, 'unit': '€'},\n            'cost_revenue_ratio': {'target_min': 0.4, 'target_max': 0.6, 'warning_threshold': 0.75, 'unit': 'ratio'}\n        },\n        'product': {\n            'margin_percentage': {'target_min': 30, 'target_max': 50, 'warning_threshold': 20, 'unit': '%'},\n            'utilization_rate': {'target_min': 70, 'target_max': 85, 'warning_threshold': 55, 'unit': '%'},\n            'cost_per_hour': {'target_min': 25, 'target_max': 45, 'warning_threshold': 55, 'unit': '€'},\n            'cost_revenue_ratio': {'target_min': 0.5, 'target_max': 0.7, 'warning_threshold': 0.8, 'unit': 'ratio'}\n        },\n        'rd': {\n            'margin_percentage': {'target_min': 0, 'target_max': 20, 'warning_threshold': -20, 'unit': '%'},\n            'utilization_rate': {'target_min': 60, 'target_max': 80, 'warning_threshold': 40, 'unit': '%'},\n            'cost_per_hour': {'target_min': 50, 'target_max': 80, 'warning_threshold': 100, 'unit': '€'},\n            'cost_revenue_ratio': {'target_min': 0.8, 'target_max': 1.2, 'warning_threshold': 1.5, 'unit': 'ratio'}\n        },\n        'internal': {\n            'margin_percentage': {'target_min': 0, 'target_max': 0, 'warning_threshold': -10, 'unit': '%'},\n            'utilization_rate': {'target_min': 70, 'target_max': 85, 'warning_threshold': 50, 'unit': '%'},\n            'cost_per_hour': {'target_min': 30, 'target_max': 50, 'warning_threshold': 70, 'unit': '€'},\n            'cost_revenue_ratio': {'target_min': 1.0, 'target_max': 1.0, 'warning_threshold': 1.2, 'unit': 'ratio'}\n        }\n    }\n"}