{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_projectkpi_model.py"}, "modifiedCode": "\"\"\"Unit tests for ProjectKPI model.\"\"\"\nimport pytest\nfrom models import ProjectKPI, Project, KPI\nfrom extensions import db\n\nclass TestProjectKPIModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n        \n        # Create test project\n        self.project = Project.query.first()\n        if not self.project:\n            self.project = Project(name='Test Project', description='Test')\n            db.session.add(self.project)\n            db.session.commit()\n            \n        # Create test KPI\n        self.kpi = KPI.query.first()\n        if not self.kpi:\n            self.kpi = KPI(name='Test KPI', description='Test KPI')\n            db.session.add(self.kpi)\n            db.session.commit()\n\n    def test_projectkpi_creation_basic(self):\n        project_kpi = ProjectKPI(\n            project_id=self.project.id,\n            kpi_id=self.kpi.id,\n            target_value=100.0\n        )\n        db.session.add(project_kpi)\n        db.session.commit()\n        \n        assert project_kpi.id is not None\n        assert project_kpi.project_id == self.project.id\n        assert project_kpi.kpi_id == self.kpi.id\n        assert project_kpi.target_value == 100.0\n\n    def test_projectkpi_values(self):\n        project_kpi = ProjectKPI(\n            project_id=self.project.id,\n            kpi_id=self.kpi.id,\n            target_value=150.0,\n            current_value=75.0\n        )\n        db.session.add(project_kpi)\n        db.session.commit()\n        \n        assert project_kpi.target_value == 150.0\n        assert project_kpi.current_value == 75.0\n\n    def test_projectkpi_update(self):\n        project_kpi = ProjectKPI(\n            project_id=self.project.id,\n            kpi_id=self.kpi.id,\n            target_value=100.0,\n            current_value=50.0\n        )\n        db.session.add(project_kpi)\n        db.session.commit()\n        \n        project_kpi.current_value = 80.0\n        db.session.commit()\n        \n        updated = ProjectKPI.query.get(project_kpi.id)\n        assert updated.current_value == 80.0\n\n    def test_projectkpi_deletion(self):\n        project_kpi = ProjectKPI(\n            project_id=self.project.id,\n            kpi_id=self.kpi.id,\n            target_value=100.0\n        )\n        db.session.add(project_kpi)\n        db.session.commit()\n        kpi_id = project_kpi.id\n        \n        db.session.delete(project_kpi)\n        db.session.commit()\n        \n        deleted = ProjectKPI.query.get(kpi_id)\n        assert deleted is None\n"}