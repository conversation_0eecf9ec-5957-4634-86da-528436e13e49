{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}, "originalCode": "import { describe, it, expect, beforeEach, vi } from 'vitest'\nimport { mountComponent, mockApiResponse, mockApiError, mockUser } from '../utils/test-helpers.js'\nimport { useAuthStore } from '@/stores/auth'\nimport Login from '@/views/auth/Login.vue'\n\n// Mock the auth store\nvi.mock('@/stores/auth', () => ({\n  useAuthStore: vi.fn()\n}))\n\n// Mock the API\nglobal.fetch = vi.fn()\n\ndescribe('Authentication Components', () => {\n  let mockAuthStore\n\n  beforeEach(() => {\n    // Reset mocks\n    vi.clearAllMocks()\n    \n    // Setup mock auth store to match real store structure\n    mockAuthStore = {\n      login: vi.fn(),\n      logout: vi.fn(),\n      initializeAuth: vi.fn(),\n      isAuthenticated: false,\n      user: null,\n      loading: false,\n      error: null,\n      sessionChecked: false\n    }\n    \n    useAuthStore.mockReturnValue(mockAuthStore)\n    \n    // Reset fetch mock\n    fetch.mockClear()\n  })\n\n  describe('Login Component', () => {\n    it('should render login form', () => {\n      const wrapper = mountComponent(Login)\n      \n      expect(wrapper.find('[data-testid=\"username\"]').exists()).toBe(true)\n      expect(wrapper.find('[data-testid=\"password\"]').exists()).toBe(true)\n      expect(wrapper.find('[data-testid=\"login-button\"]').exists()).toBe(true)\n    })\n\n    it('should show validation errors for empty fields', async () => {\n      const wrapper = mountComponent(Login)\n\n      // Try to submit without filling fields\n      await wrapper.find('[data-testid=\"login-button\"]').trigger('click')\n\n      // HTML5 validation should prevent submission\n      // Check that form fields are required\n      expect(wrapper.find('[data-testid=\"username\"]').attributes('required')).toBeDefined()\n      expect(wrapper.find('[data-testid=\"password\"]').attributes('required')).toBeDefined()\n    })\n\n    it('should call login when form is submitted with valid data', async () => {\n      mockAuthStore.login.mockResolvedValue({ success: true })\n      \n      const wrapper = mountComponent(Login)\n      \n      // Fill form\n      await wrapper.find('[data-testid=\"username\"]').setValue('testuser')\n      await wrapper.find('[data-testid=\"password\"]').setValue('password123')\n      \n      // Submit form\n      await wrapper.find('[data-testid=\"login-button\"]').trigger('click')\n      \n      // Should call store login method\n      expect(mockAuthStore.login).toHaveBeenCalledWith({\n        username: 'testuser',\n        password: 'password123'\n      })\n    })\n\n    it('should show loading state during login', async () => {\n      mockAuthStore.loading = true\n      \n      const wrapper = mountComponent(Login)\n      \n      expect(wrapper.find('[data-testid=\"login-button\"]').attributes('disabled')).toBeDefined()\n      expect(wrapper.text()).toContain('Logging in...')\n    })\n\n    it('should show error message on login failure', async () => {\n      mockAuthStore.error = 'Invalid credentials'\n      \n      const wrapper = mountComponent(Login)\n      \n      expect(wrapper.text()).toContain('Invalid credentials')\n    })\n\n    it('should redirect to dashboard on successful login', async () => {\n      const mockRouter = { push: vi.fn() }\n      mockAuthStore.login.mockResolvedValue({ success: true })\n      \n      const wrapper = mountComponent(Login, {\n        global: {\n          mocks: {\n            $router: mockRouter\n          }\n        }\n      })\n      \n      // Fill and submit form\n      await wrapper.find('[data-testid=\"username\"]').setValue('testuser')\n      await wrapper.find('[data-testid=\"password\"]').setValue('password123')\n      await wrapper.find('[data-testid=\"login-button\"]').trigger('click')\n      \n      // Wait for async operations\n      await wrapper.vm.$nextTick()\n      \n      // Should redirect to dashboard\n      expect(mockRouter.push).toHaveBeenCalledWith('/app/dashboard')\n    })\n  })\n\n  describe('Auth Store', () => {\n    it('should handle successful login', async () => {\n      fetch.mockResolvedValueOnce(mockApiResponse({\n        success: true,\n        data: { user: mockUser, token: 'fake-token' }\n      }))\n\n      // Test actual store implementation\n      const { useAuthStore } = await import('@/stores/auth')\n      const store = useAuthStore()\n      \n      const result = await store.login({\n        username: 'testuser',\n        password: 'password123'\n      })\n\n      expect(result.success).toBe(true)\n      expect(store.isAuthenticated).toBe(true)\n      expect(store.user).toEqual(mockUser)\n    })\n\n    it('should handle login failure', async () => {\n      fetch.mockResolvedValueOnce(mockApiResponse({\n        success: false,\n        error: 'Invalid credentials'\n      }, 401))\n\n      const { useAuthStore } = await import('@/stores/auth')\n      const store = useAuthStore()\n      \n      const result = await store.login({\n        username: 'wronguser',\n        password: 'wrongpass'\n      })\n\n      expect(result.success).toBe(false)\n      expect(store.isAuthenticated).toBe(false)\n      expect(store.error).toBe('Invalid credentials')\n    })\n\n    it('should handle logout', async () => {\n      fetch.mockResolvedValueOnce(mockApiResponse({ success: true }))\n\n      const { useAuthStore } = await import('@/stores/auth')\n      const store = useAuthStore()\n      \n      // Set initial authenticated state\n      store.isAuthenticated = true\n      store.user = mockUser\n      \n      await store.logout()\n\n      expect(store.isAuthenticated).toBe(false)\n      expect(store.user).toBe(null)\n    })\n\n    it('should check session on initialization', async () => {\n      fetch.mockResolvedValueOnce(mockApiResponse({\n        success: true,\n        data: { user: mockUser }\n      }))\n\n      const { useAuthStore } = await import('@/stores/auth')\n      const store = useAuthStore()\n      \n      await store.initializeAuth()\n\n      expect(store.isAuthenticated).toBe(true)\n      expect(store.user).toEqual(mockUser)\n    })\n  })\n})\n", "modifiedCode": "import { describe, it, expect, beforeEach, vi } from 'vitest'\nimport { mountComponent, mockApiResponse, mockApiError, mockUser } from '../utils/test-helpers.js'\nimport { useAuthStore } from '@/stores/auth'\nimport Login from '@/views/auth/Login.vue'\n\n// Mock the auth store\nvi.mock('@/stores/auth', () => ({\n  useAuthStore: vi.fn()\n}))\n\n// Mock the API\nglobal.fetch = vi.fn()\n\ndescribe('Authentication Components', () => {\n  let mockAuthStore\n\n  beforeEach(() => {\n    // Reset mocks\n    vi.clearAllMocks()\n    \n    // Setup mock auth store to match real store structure\n    mockAuthStore = {\n      login: vi.fn(),\n      logout: vi.fn(),\n      initializeAuth: vi.fn(),\n      isAuthenticated: false,\n      user: null,\n      loading: false,\n      error: null,\n      sessionChecked: false\n    }\n    \n    useAuthStore.mockReturnValue(mockAuthStore)\n    \n    // Reset fetch mock\n    fetch.mockClear()\n  })\n\n  describe('Login Component', () => {\n    it('should render login form', () => {\n      const wrapper = mountComponent(Login)\n      \n      expect(wrapper.find('[data-testid=\"username\"]').exists()).toBe(true)\n      expect(wrapper.find('[data-testid=\"password\"]').exists()).toBe(true)\n      expect(wrapper.find('[data-testid=\"login-button\"]').exists()).toBe(true)\n    })\n\n    it('should show validation errors for empty fields', async () => {\n      const wrapper = mountComponent(Login)\n\n      // Try to submit without filling fields\n      await wrapper.find('[data-testid=\"login-button\"]').trigger('click')\n\n      // HTML5 validation should prevent submission\n      // Check that form fields are required\n      expect(wrapper.find('[data-testid=\"username\"]').attributes('required')).toBeDefined()\n      expect(wrapper.find('[data-testid=\"password\"]').attributes('required')).toBeDefined()\n    })\n\n    it('should call login when form is submitted with valid data', async () => {\n      mockAuthStore.login.mockResolvedValue({ success: true })\n\n      const wrapper = mountComponent(Login)\n\n      // Fill form\n      await wrapper.find('[data-testid=\"username\"]').setValue('testuser')\n      await wrapper.find('[data-testid=\"password\"]').setValue('password123')\n\n      // Submit form\n      await wrapper.find('form').trigger('submit.prevent')\n      await wrapper.vm.$nextTick()\n\n      // Should call store login method with remember field\n      expect(mockAuthStore.login).toHaveBeenCalledWith({\n        username: 'testuser',\n        password: 'password123',\n        remember: false\n      })\n    })\n\n    it('should show loading state during login', async () => {\n      mockAuthStore.loading = true\n      \n      const wrapper = mountComponent(Login)\n      \n      expect(wrapper.find('[data-testid=\"login-button\"]').attributes('disabled')).toBeDefined()\n      expect(wrapper.text()).toContain('Logging in...')\n    })\n\n    it('should show error message on login failure', async () => {\n      mockAuthStore.error = 'Invalid credentials'\n      \n      const wrapper = mountComponent(Login)\n      \n      expect(wrapper.text()).toContain('Invalid credentials')\n    })\n\n    it('should redirect to dashboard on successful login', async () => {\n      const mockRouter = { push: vi.fn() }\n      mockAuthStore.login.mockResolvedValue({ success: true })\n      \n      const wrapper = mountComponent(Login, {\n        global: {\n          mocks: {\n            $router: mockRouter\n          }\n        }\n      })\n      \n      // Fill and submit form\n      await wrapper.find('[data-testid=\"username\"]').setValue('testuser')\n      await wrapper.find('[data-testid=\"password\"]').setValue('password123')\n      await wrapper.find('[data-testid=\"login-button\"]').trigger('click')\n      \n      // Wait for async operations\n      await wrapper.vm.$nextTick()\n      \n      // Should redirect to dashboard\n      expect(mockRouter.push).toHaveBeenCalledWith('/app/dashboard')\n    })\n  })\n\n  describe('Auth Store', () => {\n    it('should handle successful login', async () => {\n      fetch.mockResolvedValueOnce(mockApiResponse({\n        success: true,\n        data: { user: mockUser, token: 'fake-token' }\n      }))\n\n      // Test actual store implementation\n      const { useAuthStore } = await import('@/stores/auth')\n      const store = useAuthStore()\n      \n      const result = await store.login({\n        username: 'testuser',\n        password: 'password123'\n      })\n\n      expect(result.success).toBe(true)\n      expect(store.isAuthenticated).toBe(true)\n      expect(store.user).toEqual(mockUser)\n    })\n\n    it('should handle login failure', async () => {\n      fetch.mockResolvedValueOnce(mockApiResponse({\n        success: false,\n        error: 'Invalid credentials'\n      }, 401))\n\n      const { useAuthStore } = await import('@/stores/auth')\n      const store = useAuthStore()\n      \n      const result = await store.login({\n        username: 'wronguser',\n        password: 'wrongpass'\n      })\n\n      expect(result.success).toBe(false)\n      expect(store.isAuthenticated).toBe(false)\n      expect(store.error).toBe('Invalid credentials')\n    })\n\n    it('should handle logout', async () => {\n      fetch.mockResolvedValueOnce(mockApiResponse({ success: true }))\n\n      const { useAuthStore } = await import('@/stores/auth')\n      const store = useAuthStore()\n      \n      // Set initial authenticated state\n      store.isAuthenticated = true\n      store.user = mockUser\n      \n      await store.logout()\n\n      expect(store.isAuthenticated).toBe(false)\n      expect(store.user).toBe(null)\n    })\n\n    it('should check session on initialization', async () => {\n      fetch.mockResolvedValueOnce(mockApiResponse({\n        success: true,\n        data: { user: mockUser }\n      }))\n\n      const { useAuthStore } = await import('@/stores/auth')\n      const store = useAuthStore()\n      \n      await store.initializeAuth()\n\n      expect(store.isAuthenticated).toBe(true)\n      expect(store.user).toEqual(mockUser)\n    })\n  })\n})\n"}