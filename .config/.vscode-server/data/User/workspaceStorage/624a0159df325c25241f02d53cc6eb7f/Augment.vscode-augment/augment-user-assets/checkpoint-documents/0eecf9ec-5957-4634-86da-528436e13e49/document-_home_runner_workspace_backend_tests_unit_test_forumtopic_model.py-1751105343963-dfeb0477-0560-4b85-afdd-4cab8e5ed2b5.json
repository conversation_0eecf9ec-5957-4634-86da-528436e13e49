{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_forumtopic_model.py"}, "originalCode": "\"\"\"Unit tests for ForumTopic model.\"\"\"\nimport pytest\nfrom models import ForumTopic, User\nfrom extensions import db\n\nclass TestForumTopicModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n\n    def test_forumtopic_creation_basic(self):\n        topic = ForumTopic(\n            title='Test Topic',\n            description='This is a test forum topic',  # Campo corretto è 'description'\n            author_id=self.user.id,\n            category='general'\n        )\n        db.session.add(topic)\n        db.session.commit()\n\n        assert topic.id is not None\n        assert topic.title == 'Test Topic'\n        assert topic.description == 'This is a test forum topic'\n        assert topic.author_id == self.user.id\n        assert topic.category == 'general'\n\n    def test_forumtopic_pinned(self):\n        topic = ForumTopic(\n            title='Pinned Topic',\n            description='Important announcement',  # Campo corretto è 'description'\n            author_id=self.user.id,\n            is_pinned=True\n        )\n        db.session.add(topic)\n        db.session.commit()\n        \n        assert topic.is_pinned is True\n\n    def test_forumtopic_deletion(self):\n        topic = ForumTopic(\n            title='To Delete',\n            content='This will be deleted',\n            author_id=self.user.id\n        )\n        db.session.add(topic)\n        db.session.commit()\n        topic_id = topic.id\n        \n        db.session.delete(topic)\n        db.session.commit()\n        \n        deleted = ForumTopic.query.get(topic_id)\n        assert deleted is None\n", "modifiedCode": "\"\"\"Unit tests for ForumTopic model.\"\"\"\nimport pytest\nfrom models import ForumTopic, User\nfrom extensions import db\n\nclass TestForumTopicModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n\n    def test_forumtopic_creation_basic(self):\n        topic = ForumTopic(\n            title='Test Topic',\n            description='This is a test forum topic',  # Campo corretto è 'description'\n            author_id=self.user.id,\n            category='general'\n        )\n        db.session.add(topic)\n        db.session.commit()\n\n        assert topic.id is not None\n        assert topic.title == 'Test Topic'\n        assert topic.description == 'This is a test forum topic'\n        assert topic.author_id == self.user.id\n        assert topic.category == 'general'\n\n    def test_forumtopic_pinned(self):\n        topic = ForumTopic(\n            title='Pinned Topic',\n            description='Important announcement',  # Campo corretto è 'description'\n            author_id=self.user.id,\n            is_pinned=True\n        )\n        db.session.add(topic)\n        db.session.commit()\n        \n        assert topic.is_pinned is True\n\n    def test_forumtopic_deletion(self):\n        topic = ForumTopic(\n            title='To Delete',\n            description='This will be deleted',  # <PERSON> corretto è 'description'\n            author_id=self.user.id\n        )\n        db.session.add(topic)\n        db.session.commit()\n        topic_id = topic.id\n        \n        db.session.delete(topic)\n        db.session.commit()\n        \n        deleted = ForumTopic.query.get(topic_id)\n        assert deleted is None\n"}