{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_skill_model.py"}, "originalCode": "\"\"\"\nUnit tests for Skill model.\nTests business logic, validations, and model functionality.\n\"\"\"\n\nimport pytest\nfrom datetime import datetime\nfrom models import Skill, User\nfrom extensions import db\n\n\nclass TestSkillModel:\n    \"\"\"Test suite for Skill model unit tests\"\"\"\n\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        \"\"\"Setup test data for each test method\"\"\"\n        self.app = app\n        self.user = test_user\n\n    def test_skill_creation_basic(self):\n        \"\"\"Test basic skill creation with required fields\"\"\"\n        skill = Skill(\n            name='Python',\n            category='Programming Languages'\n        )\n\n        db.session.add(skill)\n        db.session.commit()\n\n        assert skill.id is not None\n        assert skill.name == 'Python'\n        assert skill.category == 'Programming Languages'\n\n    def test_skill_creation_complete(self):\n        \"\"\"Test skill creation with all fields (based on real DB schema)\"\"\"\n        skill = Skill(\n            name='React.js',\n            category='Frontend Frameworks',\n            description='JavaScript library for building user interfaces'\n        )\n\n        db.session.add(skill)\n        db.session.commit()\n\n        assert skill.description == 'JavaScript library for building user interfaces'\n        assert skill.name == 'React.js'\n        assert skill.category == 'Frontend Frameworks'\n\n    def test_skill_repr_method(self):\n        \"\"\"Test string representation of skill\"\"\"\n        skill = Skill(name='JavaScript', category='Programming')\n        \n        expected_repr = '<Skill JavaScript>'\n        assert repr(skill) == expected_repr\n\n    def test_skill_name_uniqueness(self):\n        \"\"\"Test that skill names should be unique\"\"\"\n        skill1 = Skill(name='Java', category='Programming')\n        skill2 = Skill(name='Java', category='Programming')  # Duplicate name\n        \n        db.session.add(skill1)\n        db.session.commit()\n        \n        # This should potentially raise an error if uniqueness is enforced\n        # For now, we test that both can be created (depends on DB constraints)\n        db.session.add(skill2)\n        try:\n            db.session.commit()\n            # If no constraint, both will exist\n            assert True\n        except Exception:\n            # If constraint exists, this is expected\n            db.session.rollback()\n            assert True\n\n    def test_skill_categories(self):\n        \"\"\"Test different skill categories\"\"\"\n        categories = [\n            'Programming Languages',\n            'Frameworks',\n            'Databases',\n            'Cloud Platforms',\n            'Soft Skills',\n            'Project Management',\n            'Design Tools'\n        ]\n        \n        skills = []\n        for i, category in enumerate(categories):\n            skill = Skill(\n                name=f'Skill {i}',\n                category=category\n            )\n            skills.append(skill)\n        \n        db.session.add_all(skills)\n        db.session.commit()\n        \n        for skill, expected_category in zip(skills, categories):\n            assert skill.category == expected_category\n\n    def test_skill_categories(self):\n        \"\"\"Test different skill categories\"\"\"\n        categories = [\n            'Programming Languages',\n            'Frameworks',\n            'Databases',\n            'Cloud Platforms',\n            'Soft Skills'\n        ]\n\n        skills = []\n        for i, category in enumerate(categories):\n            skill = Skill(\n                name=f'Skill {i}',\n                category=category\n            )\n            skills.append(skill)\n\n        db.session.add_all(skills)\n        db.session.commit()\n\n        for skill, expected_category in zip(skills, categories):\n            assert skill.category == expected_category\n\n    def test_skill_description_field(self):\n        \"\"\"Test description field for detailed information\"\"\"\n        long_description = \"\"\"\n        Vue.js is a progressive JavaScript framework for building user interfaces.\n        It is designed to be incrementally adoptable and focuses on the view layer.\n        Vue.js is known for its gentle learning curve and excellent documentation.\n        \"\"\"\n\n        skill = Skill(\n            name='Vue.js',\n            category='Frontend Frameworks',\n            description=long_description.strip()\n        )\n\n        db.session.add(skill)\n        db.session.commit()\n\n        assert skill.description == long_description.strip()\n\n    def test_skill_name_field(self):\n        \"\"\"Test skill name field\"\"\"\n        skill = Skill(\n            name='JavaScript',\n            category='Programming Languages'\n        )\n\n        db.session.add(skill)\n        db.session.commit()\n\n        assert skill.name == 'JavaScript'\n        assert len(skill.name) <= 64  # Based on DB schema VARCHAR(64)\n\n    def test_skill_query_by_category(self):\n        \"\"\"Test querying skills by category\"\"\"\n        programming_skills = [\n            Skill(name='Python_Cat', category='Programming Languages'),\n            Skill(name='Java_Cat', category='Programming Languages'),\n            Skill(name='C++_Cat', category='Programming Languages')\n        ]\n\n        framework_skills = [\n            Skill(name='Django_Cat', category='Web Frameworks'),\n            Skill(name='Flask_Cat', category='Web Frameworks')\n        ]\n\n        db.session.add_all(programming_skills + framework_skills)\n        db.session.commit()\n\n        # Query programming languages\n        prog_results = Skill.query.filter_by(category='Programming Languages').all()\n        assert len(prog_results) >= 3  # May have other skills from previous tests\n\n        # Query web frameworks\n        framework_results = Skill.query.filter_by(category='Web Frameworks').all()\n        assert len(framework_results) >= 2  # May have other skills from previous tests\n\n    def test_skill_query_by_name(self):\n        \"\"\"Test querying skills by name\"\"\"\n        skills = [\n            Skill(name='Python_Name', category='Programming'),\n            Skill(name='Java_Name', category='Programming'),\n            Skill(name='JavaScript_Name', category='Programming')\n        ]\n\n        db.session.add_all(skills)\n        db.session.commit()\n\n        # Query specific skill\n        python_skill = Skill.query.filter_by(name='Python_Name').first()\n        assert python_skill is not None\n        assert python_skill.name == 'Python_Name'\n        assert python_skill.category == 'Programming'\n\n    def test_skill_search_functionality(self):\n        \"\"\"Test skill search by name pattern\"\"\"\n        skills = [\n            Skill(name='JavaScript', category='Programming'),\n            Skill(name='Java', category='Programming'),\n            Skill(name='Python', category='Programming')\n        ]\n\n        db.session.add_all(skills)\n        db.session.commit()\n\n        # Search for skills containing 'Java'\n        java_skills = Skill.query.filter(Skill.name.contains('Java')).all()\n        java_names = [skill.name for skill in java_skills]\n\n        assert 'JavaScript' in java_names\n        assert 'Java' in java_names\n        assert 'Python' not in java_names\n\n    def test_skill_update_operations(self):\n        \"\"\"Test skill update operations\"\"\"\n        skill = Skill(\n            name='Original Name',\n            category='Original Category'\n        )\n\n        db.session.add(skill)\n        db.session.commit()\n\n        # Update skill\n        skill.name = 'Updated Name'\n        skill.category = 'Updated Category'\n        skill.description = 'Added description'\n\n        db.session.commit()\n\n        # Verify updates\n        updated_skill = Skill.query.get(skill.id)\n        assert updated_skill.name == 'Updated Name'\n        assert updated_skill.category == 'Updated Category'\n        assert updated_skill.description == 'Added description'\n\n    def test_skill_deletion(self):\n        \"\"\"Test skill deletion\"\"\"\n        skill = Skill(\n            name='To Delete',\n            category='Test'\n        )\n        \n        db.session.add(skill)\n        db.session.commit()\n        skill_id = skill.id\n        \n        # Delete skill\n        db.session.delete(skill)\n        db.session.commit()\n        \n        # Verify deletion\n        deleted_skill = Skill.query.get(skill_id)\n        assert deleted_skill is None\n\n    def test_skill_optional_fields(self):\n        \"\"\"Test optional fields behavior\"\"\"\n        # Test skill with only required fields\n        skill_minimal = Skill(\n            name='Minimal Skill',\n            category='Test'\n        )\n\n        # Test skill with all fields\n        skill_complete = Skill(\n            name='Complete Skill',\n            category='Test',\n            description='A complete skill description'\n        )\n\n        db.session.add_all([skill_minimal, skill_complete])\n        db.session.commit()\n\n        # Check minimal skill\n        assert skill_minimal.name == 'Minimal Skill'\n        assert skill_minimal.category == 'Test'\n        assert skill_minimal.description is None\n\n        # Check complete skill\n        assert skill_complete.name == 'Complete Skill'\n        assert skill_complete.description == 'A complete skill description'\n", "modifiedCode": "\"\"\"\nUnit tests for Skill model.\nTests business logic, validations, and model functionality.\n\"\"\"\n\nimport pytest\nfrom datetime import datetime\nfrom models import Skill, User\nfrom extensions import db\n\n\nclass TestSkillModel:\n    \"\"\"Test suite for Skill model unit tests\"\"\"\n\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        \"\"\"Setup test data for each test method\"\"\"\n        self.app = app\n        self.user = test_user\n\n    def test_skill_creation_basic(self):\n        \"\"\"Test basic skill creation with required fields\"\"\"\n        skill = Skill(\n            name='Python',\n            category='Programming Languages'\n        )\n\n        db.session.add(skill)\n        db.session.commit()\n\n        assert skill.id is not None\n        assert skill.name == 'Python'\n        assert skill.category == 'Programming Languages'\n\n    def test_skill_creation_complete(self):\n        \"\"\"Test skill creation with all fields (based on real DB schema)\"\"\"\n        skill = Skill(\n            name='React.js',\n            category='Frontend Frameworks',\n            description='JavaScript library for building user interfaces'\n        )\n\n        db.session.add(skill)\n        db.session.commit()\n\n        assert skill.description == 'JavaScript library for building user interfaces'\n        assert skill.name == 'React.js'\n        assert skill.category == 'Frontend Frameworks'\n\n    def test_skill_repr_method(self):\n        \"\"\"Test string representation of skill\"\"\"\n        skill = Skill(name='JavaScript', category='Programming')\n        \n        expected_repr = '<Skill JavaScript>'\n        assert repr(skill) == expected_repr\n\n    def test_skill_name_uniqueness(self):\n        \"\"\"Test that skill names should be unique\"\"\"\n        skill1 = Skill(name='Java', category='Programming')\n        skill2 = Skill(name='Java', category='Programming')  # Duplicate name\n        \n        db.session.add(skill1)\n        db.session.commit()\n        \n        # This should potentially raise an error if uniqueness is enforced\n        # For now, we test that both can be created (depends on DB constraints)\n        db.session.add(skill2)\n        try:\n            db.session.commit()\n            # If no constraint, both will exist\n            assert True\n        except Exception:\n            # If constraint exists, this is expected\n            db.session.rollback()\n            assert True\n\n    def test_skill_categories(self):\n        \"\"\"Test different skill categories\"\"\"\n        categories = [\n            'Programming Languages',\n            'Frameworks',\n            'Databases',\n            'Cloud Platforms',\n            'Soft Skills',\n            'Project Management',\n            'Design Tools'\n        ]\n        \n        skills = []\n        for i, category in enumerate(categories):\n            skill = Skill(\n                name=f'Skill {i}',\n                category=category\n            )\n            skills.append(skill)\n        \n        db.session.add_all(skills)\n        db.session.commit()\n        \n        for skill, expected_category in zip(skills, categories):\n            assert skill.category == expected_category\n\n    def test_skill_categories(self):\n        \"\"\"Test different skill categories\"\"\"\n        categories = [\n            'Programming Languages',\n            'Frameworks',\n            'Databases',\n            'Cloud Platforms',\n            'Soft Skills'\n        ]\n\n        skills = []\n        for i, category in enumerate(categories):\n            skill = Skill(\n                name=f'Skill {i}',\n                category=category\n            )\n            skills.append(skill)\n\n        db.session.add_all(skills)\n        db.session.commit()\n\n        for skill, expected_category in zip(skills, categories):\n            assert skill.category == expected_category\n\n    def test_skill_description_field(self):\n        \"\"\"Test description field for detailed information\"\"\"\n        long_description = \"\"\"\n        Vue.js is a progressive JavaScript framework for building user interfaces.\n        It is designed to be incrementally adoptable and focuses on the view layer.\n        Vue.js is known for its gentle learning curve and excellent documentation.\n        \"\"\"\n\n        skill = Skill(\n            name='Vue.js',\n            category='Frontend Frameworks',\n            description=long_description.strip()\n        )\n\n        db.session.add(skill)\n        db.session.commit()\n\n        assert skill.description == long_description.strip()\n\n    def test_skill_name_field(self):\n        \"\"\"Test skill name field\"\"\"\n        skill = Skill(\n            name='JavaScript',\n            category='Programming Languages'\n        )\n\n        db.session.add(skill)\n        db.session.commit()\n\n        assert skill.name == 'JavaScript'\n        assert len(skill.name) <= 64  # Based on DB schema VARCHAR(64)\n\n    def test_skill_query_by_category(self):\n        \"\"\"Test querying skills by category\"\"\"\n        programming_skills = [\n            Skill(name='Python_Cat', category='Programming Languages'),\n            Skill(name='Java_Cat', category='Programming Languages'),\n            Skill(name='C++_Cat', category='Programming Languages')\n        ]\n\n        framework_skills = [\n            Skill(name='Django_Cat', category='Web Frameworks'),\n            Skill(name='Flask_Cat', category='Web Frameworks')\n        ]\n\n        db.session.add_all(programming_skills + framework_skills)\n        db.session.commit()\n\n        # Query programming languages\n        prog_results = Skill.query.filter_by(category='Programming Languages').all()\n        assert len(prog_results) >= 3  # May have other skills from previous tests\n\n        # Query web frameworks\n        framework_results = Skill.query.filter_by(category='Web Frameworks').all()\n        assert len(framework_results) >= 2  # May have other skills from previous tests\n\n    def test_skill_query_by_name(self):\n        \"\"\"Test querying skills by name\"\"\"\n        skills = [\n            Skill(name='Python_Name', category='Programming'),\n            Skill(name='Java_Name', category='Programming'),\n            Skill(name='JavaScript_Name', category='Programming')\n        ]\n\n        db.session.add_all(skills)\n        db.session.commit()\n\n        # Query specific skill\n        python_skill = Skill.query.filter_by(name='Python_Name').first()\n        assert python_skill is not None\n        assert python_skill.name == 'Python_Name'\n        assert python_skill.category == 'Programming'\n\n    def test_skill_search_functionality(self):\n        \"\"\"Test skill search by name pattern\"\"\"\n        skills = [\n            Skill(name='JavaScript', category='Programming'),\n            Skill(name='Java', category='Programming'),\n            Skill(name='Python', category='Programming')\n        ]\n\n        db.session.add_all(skills)\n        db.session.commit()\n\n        # Search for skills containing 'Java'\n        java_skills = Skill.query.filter(Skill.name.contains('Java')).all()\n        java_names = [skill.name for skill in java_skills]\n\n        assert 'JavaScript' in java_names\n        assert 'Java' in java_names\n        assert 'Python' not in java_names\n\n    def test_skill_update_operations(self):\n        \"\"\"Test skill update operations\"\"\"\n        skill = Skill(\n            name='Original Name',\n            category='Original Category'\n        )\n\n        db.session.add(skill)\n        db.session.commit()\n\n        # Update skill\n        skill.name = 'Updated Name'\n        skill.category = 'Updated Category'\n        skill.description = 'Added description'\n\n        db.session.commit()\n\n        # Verify updates\n        updated_skill = Skill.query.get(skill.id)\n        assert updated_skill.name == 'Updated Name'\n        assert updated_skill.category == 'Updated Category'\n        assert updated_skill.description == 'Added description'\n\n    def test_skill_deletion(self):\n        \"\"\"Test skill deletion\"\"\"\n        skill = Skill(\n            name='To Delete',\n            category='Test'\n        )\n        \n        db.session.add(skill)\n        db.session.commit()\n        skill_id = skill.id\n        \n        # Delete skill\n        db.session.delete(skill)\n        db.session.commit()\n        \n        # Verify deletion\n        deleted_skill = Skill.query.get(skill_id)\n        assert deleted_skill is None\n\n    def test_skill_optional_fields(self):\n        \"\"\"Test optional fields behavior\"\"\"\n        # Test skill with only required fields\n        skill_minimal = Skill(\n            name='Minimal Skill',\n            category='Test'\n        )\n\n        # Test skill with all fields\n        skill_complete = Skill(\n            name='Complete Skill',\n            category='Test',\n            description='A complete skill description'\n        )\n\n        db.session.add_all([skill_minimal, skill_complete])\n        db.session.commit()\n\n        # Check minimal skill\n        assert skill_minimal.name == 'Minimal Skill'\n        assert skill_minimal.category == 'Test'\n        assert skill_minimal.description is None\n\n        # Check complete skill\n        assert skill_complete.name == 'Complete Skill'\n        assert skill_complete.description == 'A complete skill description'\n"}