{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/hr.py"}, "originalCode": "# HR Management models\nfrom .base import db, datetime\n\nclass Skill(db.Model):\n    __tablename__ = 'skills'\n    \n    id = db.<PERSON>umn(db.Integer, primary_key=True)\n    name = db.Column(db.String(64), unique=True, nullable=False)\n    category = db.Column(db.String(64))\n    description = db.Column(db.Text)\n\n    # Relationships\n    user_skills = db.relationship('UserSkill', backref='skill', lazy='dynamic', cascade='all, delete-orphan')\n\n    def __repr__(self):\n        return f'<Skill {self.name}>'\n\n\nclass Department(db.Model):\n    \"\"\"Modello per la gestione dei dipartimenti aziendali con struttura gerarchica\"\"\"\n    __tablename__ = 'departments'\n\n    id = db.Column(db.Integer, primary_key=True)\n    name = db.Column(db.String(100), nullable=False, unique=True)\n    description = db.Column(db.Text)\n    manager_id = db.Column(db.<PERSON>, db.<PERSON>('users.id'), nullable=True)\n    parent_id = db.Column(db.Integer, db.<PERSON>Key('departments.id'), nullable=True)\n    budget = db.Column(db.Float, default=0.0)\n    is_active = db.Column(db.Boolean, default=True)\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    # Relationships\n    manager = db.relationship('User', foreign_keys=[manager_id], backref='managed_departments')\n    employees = db.relationship('User', foreign_keys='User.department_id', backref='department_obj', lazy='dynamic')\n    subdepartments = db.relationship('Department', backref=db.backref('parent', remote_side=[id]), lazy='dynamic')\n\n    def __repr__(self):\n        return f'<Department {self.name}>'\n\n    @property\n    def employee_count(self):\n        \"\"\"Conta il numero di dipendenti nel dipartimento\"\"\"\n        return self.employees.filter_by(is_active=True).count()\n\n    @property\n    def full_path(self):\n        \"\"\"Restituisce il percorso completo del dipartimento (es: IT > Development > Frontend)\"\"\"\n        if self.parent:\n            return f\"{self.parent.full_path} > {self.name}\"\n        return self.name\n\n\nclass UserProfile(db.Model):\n    \"\"\"Profilo HR esteso per gli utenti\"\"\"\n    __tablename__ = 'user_profiles'\n\n    id = db.Column(db.Integer, primary_key=True)\n    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, unique=True)\n    employee_id = db.Column(db.String(20), unique=True)\n    job_title = db.Column(db.String(100))\n    birth_date = db.Column(db.Date)\n    address = db.Column(db.Text)\n    emergency_contact_name = db.Column(db.String(100))\n    emergency_contact_phone = db.Column(db.String(20))\n    emergency_contact_relationship = db.Column(db.String(50))\n\n    # Informazioni lavorative\n    employment_type = db.Column(db.String(50), default='full_time')  # full_time, part_time, contractor, intern\n    work_location = db.Column(db.String(100))  # office, remote, hybrid\n    salary = db.Column(db.Float)  # Stipendio base\n    salary_currency = db.Column(db.String(3), default='EUR')\n\n    # Informazioni HR\n    probation_end_date = db.Column(db.Date)\n    contract_end_date = db.Column(db.Date)  # Per contratti a termine\n    notice_period_days = db.Column(db.Integer, default=30)\n\n    # Capacità lavorativa\n    weekly_hours = db.Column(db.Float, default=40.0)  # Ore settimanali standard\n    daily_hours = db.Column(db.Float, default=8.0)   # Ore giornaliere standard\n\n    # CV e documenti\n    current_cv_path = db.Column(db.String(255))  # Path del CV attuale\n    cv_last_updated = db.Column(db.DateTime)\n    cv_analysis_data = db.Column(db.Text)  # JSON con analisi AI del CV\n\n    # Metadati\n    profile_completion = db.Column(db.Float, default=0.0)  # Percentuale completamento profilo\n    notes = db.Column(db.Text)  # Note HR riservate\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    # Relationships\n    user = db.relationship('User', backref=db.backref('profile', uselist=False))\n\n    def __repr__(self):\n        return f'<UserProfile {self.user.username if self.user else self.id}>'\n\n    def calculate_completion(self):\n        \"\"\"Calcola la percentuale di completamento del profilo\"\"\"\n        # Campi UserProfile\n        profile_fields = [\n            self.employee_id, self.job_title, self.birth_date, self.address,\n            self.emergency_contact_name, self.emergency_contact_phone,\n            self.employment_type, self.work_location, self.current_cv_path\n        ]\n\n        # Campi User base (se disponibili)\n        user_fields = []\n        if self.user:\n            user_fields = [\n                self.user.first_name, self.user.last_name, self.user.phone,\n                self.user.position, self.user.bio\n            ]\n\n        # Combina tutti i campi\n        all_fields = profile_fields + user_fields\n        completed = sum(1 for field in all_fields if field)\n\n        # Calcola percentuale e arrotonda\n        if len(all_fields) > 0:\n            self.profile_completion = round((completed / len(all_fields)) * 100)\n        else:\n            self.profile_completion = 0.0\n\n        return self.profile_completion\n\n    def to_dict(self):\n        \"\"\"Converte il profilo utente in dizionario per serializzazione JSON\"\"\"\n        return {\n            'id': self.id,\n            'user_id': self.user_id,\n            'employee_id': self.employee_id,\n            'job_title': self.job_title,\n            'birth_date': self.birth_date.isoformat() if self.birth_date else None,\n            'address': self.address,\n            'emergency_contact_name': self.emergency_contact_name,\n            'emergency_contact_phone': self.emergency_contact_phone,\n            'emergency_contact_relationship': self.emergency_contact_relationship,\n            'employment_type': self.employment_type,\n            'work_location': self.work_location,\n            'salary': self.salary,\n            'salary_currency': self.salary_currency,\n            'probation_end_date': self.probation_end_date.isoformat() if self.probation_end_date else None,\n            'contract_end_date': self.contract_end_date.isoformat() if self.contract_end_date else None,\n            'notice_period_days': self.notice_period_days,\n            'weekly_hours': self.weekly_hours,\n            'daily_hours': self.daily_hours,\n            'current_cv_path': self.current_cv_path,\n            'cv_last_updated': self.cv_last_updated.isoformat() if self.cv_last_updated else None,\n            'cv_analysis_data': self.cv_analysis_data,\n            'profile_completion': self.profile_completion,\n            'notes': self.notes,\n            'created_at': self.created_at.isoformat() if self.created_at else None,\n            'updated_at': self.updated_at.isoformat() if self.updated_at else None\n        }\n\n\nclass UserSkill(db.Model):\n    __tablename__ = 'user_skills_detailed'\n    \n    id = db.Column(db.Integer, primary_key=True)\n    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)\n    skill_id = db.Column(db.Integer, db.ForeignKey('skills.id'), nullable=False)\n    proficiency_level = db.Column(db.Integer, default=1)  # 1-5 scale\n    years_experience = db.Column(db.Float, default=0.0)\n    is_certified = db.Column(db.Boolean, default=False)\n    certification_name = db.Column(db.String(100))\n    certification_date = db.Column(db.Date)\n    certification_expiry = db.Column(db.Date)\n    self_assessed = db.Column(db.Boolean, default=True)\n    manager_assessed = db.Column(db.Boolean, default=False)\n    manager_assessment_date = db.Column(db.Date)\n    notes = db.Column(db.Text)\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n    last_used = db.Column(db.Date)\n    certified = db.Column(db.Boolean, default=False)\n\n    # Relationships\n    user = db.relationship('User', backref='detailed_skills')\n    # skill relationship is already defined in Skill model via backref\n\n    # Unique constraint to prevent duplicate skills per user\n    __table_args__ = (\n        db.UniqueConstraint('user_id', 'skill_id', name='unique_user_skill'),\n    )\n\n    def __repr__(self):\n        return f'<UserSkill {self.user_id}-{self.skill_id}>'\n\n    def to_dict(self):\n        return {\n            'id': self.id,\n            'user_id': self.user_id,\n            'skill_id': self.skill_id,\n            'skill': {\n                'id': self.skill.id,\n                'name': self.skill.name,\n                'category': self.skill.category\n            } if self.skill else None,\n            'proficiency_level': self.proficiency_level,\n            'years_experience': self.years_experience,\n            'last_used': self.last_used.isoformat() if self.last_used else None,\n            'is_certified': self.is_certified,\n            'certified': self.certified,\n            'certification_date': self.certification_date.isoformat() if self.certification_date else None,\n            'certification_name': self.certification_name,\n            'certification_expiry': self.certification_expiry.isoformat() if self.certification_expiry else None,\n            'self_assessed': self.self_assessed,\n            'manager_assessed': self.manager_assessed,\n            'manager_assessment_date': self.manager_assessment_date.isoformat() if self.manager_assessment_date else None,\n            'notes': self.notes\n        }\n\n\nclass TimeOffRequest(db.Model):\n    __tablename__ = 'time_off_requests'\n    \n    id = db.Column(db.Integer, primary_key=True)\n    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)\n    type = db.Column(db.String(50), nullable=False)  # vacation, sick, personal, etc.\n    start_date = db.Column(db.Date, nullable=False)\n    end_date = db.Column(db.Date, nullable=False)\n    reason = db.Column(db.Text)\n    status = db.Column(db.String(20), default='pending')  # pending, approved, rejected, cancelled\n    approved_by = db.Column(db.Integer, db.ForeignKey('users.id'))\n    approved_at = db.Column(db.DateTime)\n    notes = db.Column(db.Text)\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    # Relationships\n    requester = db.relationship('User', foreign_keys=[user_id], backref='time_off_requests')\n    approver = db.relationship('User', foreign_keys=[approved_by], backref='approved_time_offs')\n\n    def __repr__(self):\n        return f'<TimeOffRequest {self.user_id} {self.start_date} to {self.end_date}>'\n\n\nclass JobLevel(db.Model):\n    \"\"\"Modello per la definizione dei livelli di lavoro (job levels)\"\"\"\n    __tablename__ = 'job_levels'\n    \n    id = db.Column(db.Integer, primary_key=True)\n    level_number = db.Column(db.Integer, nullable=False, unique=True)  # e.g., 1, 2, 3, 4, 5\n    name = db.Column(db.String(100), nullable=False)  # e.g., \"Junior Developer\", \"Senior Developer\"\n    description = db.Column(db.Text)\n    min_salary = db.Column(db.Float)\n    max_salary = db.Column(db.Float)\n    typical_years_experience = db.Column(db.Integer)\n    is_management = db.Column(db.Boolean, default=False)\n    is_active = db.Column(db.Boolean, default=True)  # Campo mancante aggiunto\n    parent_level_id = db.Column(db.Integer, db.ForeignKey('job_levels.id'))\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n    \n    # Self-referential relationship for hierarchy\n    parent_level = db.relationship('JobLevel', remote_side=[id], backref='sub_levels')\n    \n    def __repr__(self):\n        return f'<JobLevel {self.level_number}: {self.name}>'\n    \n    @property\n    def current_employees(self):\n        \"\"\"Get employees currently assigned to this job level\"\"\"\n        return [ejl.employee for ejl in self.employees if ejl.end_date is None and ejl.is_active]\n    \n    def to_dict(self):\n        return {\n            'id': self.id,\n            'level_number': self.level_number,\n            'name': self.name,\n            'description': self.description,\n            'min_salary': self.min_salary,\n            'max_salary': self.max_salary,\n            'typical_years_experience': self.typical_years_experience,\n            'is_management': self.is_management,\n            'parent_level_id': self.parent_level_id,\n            'parent_level': {\n                'id': self.parent_level.id,\n                'name': self.parent_level.name,\n                'level_number': self.parent_level.level_number\n            } if self.parent_level else None,\n            'created_at': self.created_at.isoformat() if self.created_at else None,\n            'updated_at': self.updated_at.isoformat() if self.updated_at else None\n        }\n\n\nclass EmployeeJobLevel(db.Model):\n    \"\"\"Storico dei livelli di lavoro per ogni dipendente\"\"\"\n    __tablename__ = 'employee_job_levels'\n    \n    id = db.Column(db.Integer, primary_key=True)\n    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)\n    job_level_id = db.Column(db.Integer, db.ForeignKey('job_levels.id'), nullable=False)\n    start_date = db.Column(db.Date, nullable=False)\n    end_date = db.Column(db.Date)  # NULL means current level\n    current_salary = db.Column(db.Float)  # Renamed from salary to match DB\n    notes = db.Column(db.Text)\n    is_active = db.Column(db.Boolean, default=True)  # Missing column added\n    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    \n    # Relationships\n    employee = db.relationship('User', foreign_keys=[user_id], backref='job_level_history')\n    job_level = db.relationship('JobLevel', backref='employees')\n    creator = db.relationship('User', foreign_keys=[created_by])\n    \n    def __repr__(self):\n        return f'<EmployeeJobLevel User:{self.user_id} Level:{self.job_level_id}>'\n    \n    @property\n    def is_current(self):\n        return self.end_date is None\n    \n    def to_dict(self):\n        return {\n            'id': self.id,\n            'user_id': self.user_id,\n            'employee': {\n                'id': self.employee.id,\n                'full_name': self.employee.full_name,\n                'username': self.employee.username\n            } if self.employee else None,\n            'job_level_id': self.job_level_id,\n            'job_level': self.job_level.to_dict() if self.job_level else None,\n            'start_date': self.start_date.isoformat() if self.start_date else None,\n            'end_date': self.end_date.isoformat() if self.end_date else None,\n            'is_current': self.is_current,\n            'current_salary': self.current_salary,\n            'notes': self.notes,\n            'created_by': self.created_by,\n            'creator': {\n                'id': self.creator.id,\n                'full_name': self.creator.full_name\n            } if self.creator else None,\n            'created_at': self.created_at.isoformat() if self.created_at else None\n        }\n\n\nclass PersonnelRate(db.Model):\n    \"\"\"Tariffe del personale per progetti e clienti\"\"\"\n    __tablename__ = 'personnel_rates'\n\n    id = db.Column(db.Integer, primary_key=True)\n    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)\n    daily_rate = db.Column(db.Float, nullable=False)  # Campo DB reale\n    valid_from = db.Column(db.Date, nullable=False)   # Campo DB reale\n    valid_to = db.Column(db.Date)                     # Campo DB reale\n    currency = db.Column(db.String(3), default='EUR')\n    notes = db.Column(db.String(255))\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n\n    # Relationships\n    user = db.relationship('User', backref='rates')\n\n    def __repr__(self):\n        return f'<PersonnelRate {self.user_id}: {self.daily_rate} {self.currency}>'", "modifiedCode": "# HR Management models\nfrom .base import db, datetime\n\nclass Skill(db.Model):\n    __tablename__ = 'skills'\n    \n    id = db.<PERSON>umn(db.Integer, primary_key=True)\n    name = db.Column(db.String(64), unique=True, nullable=False)\n    category = db.Column(db.String(64))\n    description = db.Column(db.Text)\n\n    # Relationships\n    user_skills = db.relationship('UserSkill', backref='skill', lazy='dynamic', cascade='all, delete-orphan')\n\n    def __repr__(self):\n        return f'<Skill {self.name}>'\n\n\nclass Department(db.Model):\n    \"\"\"Modello per la gestione dei dipartimenti aziendali con struttura gerarchica\"\"\"\n    __tablename__ = 'departments'\n\n    id = db.Column(db.Integer, primary_key=True)\n    name = db.Column(db.String(100), nullable=False, unique=True)\n    description = db.Column(db.Text)\n    manager_id = db.Column(db.<PERSON>, db.<PERSON>('users.id'), nullable=True)\n    parent_id = db.Column(db.Integer, db.<PERSON>Key('departments.id'), nullable=True)\n    budget = db.Column(db.Float, default=0.0)\n    is_active = db.Column(db.Boolean, default=True)\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    # Relationships\n    manager = db.relationship('User', foreign_keys=[manager_id], backref='managed_departments')\n    employees = db.relationship('User', foreign_keys='User.department_id', backref='department_obj', lazy='dynamic')\n    subdepartments = db.relationship('Department', backref=db.backref('parent', remote_side=[id]), lazy='dynamic')\n\n    def __repr__(self):\n        return f'<Department {self.name}>'\n\n    @property\n    def employee_count(self):\n        \"\"\"Conta il numero di dipendenti nel dipartimento\"\"\"\n        return self.employees.filter_by(is_active=True).count()\n\n    @property\n    def full_path(self):\n        \"\"\"Restituisce il percorso completo del dipartimento (es: IT > Development > Frontend)\"\"\"\n        if self.parent:\n            return f\"{self.parent.full_path} > {self.name}\"\n        return self.name\n\n\nclass UserProfile(db.Model):\n    \"\"\"Profilo HR esteso per gli utenti\"\"\"\n    __tablename__ = 'user_profiles'\n\n    id = db.Column(db.Integer, primary_key=True)\n    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, unique=True)\n    employee_id = db.Column(db.String(20), unique=True)\n    job_title = db.Column(db.String(100))\n    birth_date = db.Column(db.Date)\n    address = db.Column(db.Text)\n    emergency_contact_name = db.Column(db.String(100))\n    emergency_contact_phone = db.Column(db.String(20))\n    emergency_contact_relationship = db.Column(db.String(50))\n\n    # Informazioni lavorative\n    employment_type = db.Column(db.String(50), default='full_time')  # full_time, part_time, contractor, intern\n    work_location = db.Column(db.String(100))  # office, remote, hybrid\n    salary = db.Column(db.Float)  # Stipendio base\n    salary_currency = db.Column(db.String(3), default='EUR')\n\n    # Informazioni HR\n    probation_end_date = db.Column(db.Date)\n    contract_end_date = db.Column(db.Date)  # Per contratti a termine\n    notice_period_days = db.Column(db.Integer, default=30)\n\n    # Capacità lavorativa\n    weekly_hours = db.Column(db.Float, default=40.0)  # Ore settimanali standard\n    daily_hours = db.Column(db.Float, default=8.0)   # Ore giornaliere standard\n\n    # CV e documenti\n    current_cv_path = db.Column(db.String(255))  # Path del CV attuale\n    cv_last_updated = db.Column(db.DateTime)\n    cv_analysis_data = db.Column(db.Text)  # JSON con analisi AI del CV\n\n    # Metadati\n    profile_completion = db.Column(db.Float, default=0.0)  # Percentuale completamento profilo\n    notes = db.Column(db.Text)  # Note HR riservate\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    # Relationships\n    user = db.relationship('User', backref=db.backref('profile', uselist=False))\n\n    def __repr__(self):\n        return f'<UserProfile {self.user.username if self.user else self.id}>'\n\n    def calculate_completion(self):\n        \"\"\"Calcola la percentuale di completamento del profilo\"\"\"\n        # Campi UserProfile\n        profile_fields = [\n            self.employee_id, self.job_title, self.birth_date, self.address,\n            self.emergency_contact_name, self.emergency_contact_phone,\n            self.employment_type, self.work_location, self.current_cv_path\n        ]\n\n        # Campi User base (se disponibili)\n        user_fields = []\n        if self.user:\n            user_fields = [\n                self.user.first_name, self.user.last_name, self.user.phone,\n                self.user.position, self.user.bio\n            ]\n\n        # Combina tutti i campi\n        all_fields = profile_fields + user_fields\n        completed = sum(1 for field in all_fields if field)\n\n        # Calcola percentuale e arrotonda\n        if len(all_fields) > 0:\n            self.profile_completion = round((completed / len(all_fields)) * 100)\n        else:\n            self.profile_completion = 0.0\n\n        return self.profile_completion\n\n    def to_dict(self):\n        \"\"\"Converte il profilo utente in dizionario per serializzazione JSON\"\"\"\n        return {\n            'id': self.id,\n            'user_id': self.user_id,\n            'employee_id': self.employee_id,\n            'job_title': self.job_title,\n            'birth_date': self.birth_date.isoformat() if self.birth_date else None,\n            'address': self.address,\n            'emergency_contact_name': self.emergency_contact_name,\n            'emergency_contact_phone': self.emergency_contact_phone,\n            'emergency_contact_relationship': self.emergency_contact_relationship,\n            'employment_type': self.employment_type,\n            'work_location': self.work_location,\n            'salary': self.salary,\n            'salary_currency': self.salary_currency,\n            'probation_end_date': self.probation_end_date.isoformat() if self.probation_end_date else None,\n            'contract_end_date': self.contract_end_date.isoformat() if self.contract_end_date else None,\n            'notice_period_days': self.notice_period_days,\n            'weekly_hours': self.weekly_hours,\n            'daily_hours': self.daily_hours,\n            'current_cv_path': self.current_cv_path,\n            'cv_last_updated': self.cv_last_updated.isoformat() if self.cv_last_updated else None,\n            'cv_analysis_data': self.cv_analysis_data,\n            'profile_completion': self.profile_completion,\n            'notes': self.notes,\n            'created_at': self.created_at.isoformat() if self.created_at else None,\n            'updated_at': self.updated_at.isoformat() if self.updated_at else None\n        }\n\n\nclass UserSkill(db.Model):\n    __tablename__ = 'user_skills_detailed'\n    \n    id = db.Column(db.Integer, primary_key=True)\n    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)\n    skill_id = db.Column(db.Integer, db.ForeignKey('skills.id'), nullable=False)\n    proficiency_level = db.Column(db.Integer, default=1)  # 1-5 scale\n    years_experience = db.Column(db.Float, default=0.0)\n    is_certified = db.Column(db.Boolean, default=False)\n    certification_name = db.Column(db.String(100))\n    certification_date = db.Column(db.Date)\n    certification_expiry = db.Column(db.Date)\n    self_assessed = db.Column(db.Boolean, default=True)\n    manager_assessed = db.Column(db.Boolean, default=False)\n    manager_assessment_date = db.Column(db.Date)\n    notes = db.Column(db.Text)\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n    last_used = db.Column(db.Date)\n    certified = db.Column(db.Boolean, default=False)\n\n    # Relationships\n    user = db.relationship('User', backref='detailed_skills')\n    # skill relationship is already defined in Skill model via backref\n\n    # Unique constraint to prevent duplicate skills per user\n    __table_args__ = (\n        db.UniqueConstraint('user_id', 'skill_id', name='unique_user_skill'),\n    )\n\n    def __repr__(self):\n        return f'<UserSkill {self.user_id}-{self.skill_id}>'\n\n    def to_dict(self):\n        return {\n            'id': self.id,\n            'user_id': self.user_id,\n            'skill_id': self.skill_id,\n            'skill': {\n                'id': self.skill.id,\n                'name': self.skill.name,\n                'category': self.skill.category\n            } if self.skill else None,\n            'proficiency_level': self.proficiency_level,\n            'years_experience': self.years_experience,\n            'last_used': self.last_used.isoformat() if self.last_used else None,\n            'is_certified': self.is_certified,\n            'certified': self.certified,\n            'certification_date': self.certification_date.isoformat() if self.certification_date else None,\n            'certification_name': self.certification_name,\n            'certification_expiry': self.certification_expiry.isoformat() if self.certification_expiry else None,\n            'self_assessed': self.self_assessed,\n            'manager_assessed': self.manager_assessed,\n            'manager_assessment_date': self.manager_assessment_date.isoformat() if self.manager_assessment_date else None,\n            'notes': self.notes\n        }\n\n\nclass TimeOffRequest(db.Model):\n    __tablename__ = 'time_off_requests'\n    \n    id = db.Column(db.Integer, primary_key=True)\n    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)\n    type = db.Column(db.String(50), nullable=False)  # vacation, sick, personal, etc.\n    start_date = db.Column(db.Date, nullable=False)\n    end_date = db.Column(db.Date, nullable=False)\n    reason = db.Column(db.Text)\n    status = db.Column(db.String(20), default='pending')  # pending, approved, rejected, cancelled\n    approved_by = db.Column(db.Integer, db.ForeignKey('users.id'))\n    approved_at = db.Column(db.DateTime)\n    notes = db.Column(db.Text)\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    # Relationships\n    requester = db.relationship('User', foreign_keys=[user_id], backref='time_off_requests')\n    approver = db.relationship('User', foreign_keys=[approved_by], backref='approved_time_offs')\n\n    def __repr__(self):\n        return f'<TimeOffRequest {self.user_id} {self.start_date} to {self.end_date}>'\n\n\nclass JobLevel(db.Model):\n    \"\"\"Modello per la definizione dei livelli di lavoro (job levels)\"\"\"\n    __tablename__ = 'job_levels'\n    \n    id = db.Column(db.Integer, primary_key=True)\n    level_number = db.Column(db.Integer, nullable=False, unique=True)  # e.g., 1, 2, 3, 4, 5\n    name = db.Column(db.String(100), nullable=False)  # e.g., \"Junior Developer\", \"Senior Developer\"\n    description = db.Column(db.Text)\n    min_salary = db.Column(db.Float)\n    max_salary = db.Column(db.Float)\n    typical_years_experience = db.Column(db.Integer)\n    is_management = db.Column(db.Boolean, default=False)\n    is_active = db.Column(db.Boolean, default=True)  # Campo mancante aggiunto\n    parent_level_id = db.Column(db.Integer, db.ForeignKey('job_levels.id'))\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n    \n    # Self-referential relationship for hierarchy\n    parent_level = db.relationship('JobLevel', remote_side=[id], backref='sub_levels')\n    \n    def __repr__(self):\n        return f'<JobLevel {self.level_number}: {self.name}>'\n    \n    @property\n    def current_employees(self):\n        \"\"\"Get employees currently assigned to this job level\"\"\"\n        return [ejl.employee for ejl in self.employees if ejl.end_date is None and ejl.is_active]\n    \n    def to_dict(self):\n        return {\n            'id': self.id,\n            'level_number': self.level_number,\n            'name': self.name,\n            'description': self.description,\n            'min_salary': self.min_salary,\n            'max_salary': self.max_salary,\n            'typical_years_experience': self.typical_years_experience,\n            'is_management': self.is_management,\n            'parent_level_id': self.parent_level_id,\n            'parent_level': {\n                'id': self.parent_level.id,\n                'name': self.parent_level.name,\n                'level_number': self.parent_level.level_number\n            } if self.parent_level else None,\n            'created_at': self.created_at.isoformat() if self.created_at else None,\n            'updated_at': self.updated_at.isoformat() if self.updated_at else None\n        }\n\n\nclass EmployeeJobLevel(db.Model):\n    \"\"\"Storico dei livelli di lavoro per ogni dipendente\"\"\"\n    __tablename__ = 'employee_job_levels'\n    \n    id = db.Column(db.Integer, primary_key=True)\n    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)\n    job_level_id = db.Column(db.Integer, db.ForeignKey('job_levels.id'), nullable=False)\n    start_date = db.Column(db.Date, nullable=False)\n    end_date = db.Column(db.Date)  # NULL means current level\n    current_salary = db.Column(db.Float)  # Renamed from salary to match DB\n    notes = db.Column(db.Text)\n    is_active = db.Column(db.Boolean, default=True)  # Missing column added\n    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    \n    # Relationships\n    employee = db.relationship('User', foreign_keys=[user_id], backref='job_level_history')\n    job_level = db.relationship('JobLevel', backref='employees')\n    creator = db.relationship('User', foreign_keys=[created_by])\n    \n    def __repr__(self):\n        return f'<EmployeeJobLevel User:{self.user_id} Level:{self.job_level_id}>'\n    \n    @property\n    def is_current(self):\n        return self.end_date is None\n    \n    def to_dict(self):\n        return {\n            'id': self.id,\n            'user_id': self.user_id,\n            'employee': {\n                'id': self.employee.id,\n                'full_name': self.employee.full_name,\n                'username': self.employee.username\n            } if self.employee else None,\n            'job_level_id': self.job_level_id,\n            'job_level': self.job_level.to_dict() if self.job_level else None,\n            'start_date': self.start_date.isoformat() if self.start_date else None,\n            'end_date': self.end_date.isoformat() if self.end_date else None,\n            'is_current': self.is_current,\n            'current_salary': self.current_salary,\n            'notes': self.notes,\n            'created_by': self.created_by,\n            'creator': {\n                'id': self.creator.id,\n                'full_name': self.creator.full_name\n            } if self.creator else None,\n            'created_at': self.created_at.isoformat() if self.created_at else None\n        }\n\n\nclass PersonnelRate(db.Model):\n    \"\"\"Tariffe del personale per progetti e clienti\"\"\"\n    __tablename__ = 'personnel_rates'\n\n    id = db.Column(db.Integer, primary_key=True)\n    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)\n    daily_rate = db.Column(db.Float, nullable=False)  # Campo DB reale\n    valid_from = db.Column(db.Date, nullable=False)   # Campo DB reale\n    valid_to = db.Column(db.Date)                     # Campo DB reale\n    currency = db.Column(db.String(3), default='EUR')\n    notes = db.Column(db.String(255))\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n\n    # Relationships\n    user = db.relationship('User', backref='rates')\n\n    def __repr__(self):\n        return f'<PersonnelRate {self.user_id}: {self.daily_rate} {self.currency}>'"}