{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_task_dependency_model.py"}, "originalCode": "\"\"\"\nUnit tests for TaskDependency model.\nTests business logic, validations, and model functionality.\n\"\"\"\n\nimport pytest\nfrom datetime import datetime, date, timedelta\nfrom models import TaskDependency, Task, Project, User\nfrom extensions import db\n\n\nclass TestTaskDependencyModel:\n    \"\"\"Test suite for TaskDependency model unit tests\"\"\"\n\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        \"\"\"Setup test data for each test method\"\"\"\n        self.app = app\n        self.user = test_user\n        \n        # Create test project\n        self.test_project = Project(\n            name='Test Project',\n            description='Project for testing task dependencies',\n            status='active',\n            start_date=date.today(),\n            end_date=date.today() + timedelta(days=90)\n        )\n        db.session.add(self.test_project)\n        db.session.commit()\n        \n        # Create test tasks (using correct field names from DB schema)\n        self.task_a = Task(\n            name='Task A',\n            description='First task',\n            project_id=self.test_project.id,\n            assignee_id=self.user.id,\n            status='todo',\n            start_date=date.today(),\n            due_date=date.today() + timedelta(days=10)\n        )\n\n        self.task_b = Task(\n            name='Task B',\n            description='Second task',\n            project_id=self.test_project.id,\n            assignee_id=self.user.id,\n            status='todo',\n            start_date=date.today() + timedelta(days=5),\n            due_date=date.today() + timedelta(days=15)\n        )\n        \n        db.session.add_all([self.task_a, self.task_b])\n        db.session.commit()\n\n    def test_task_dependency_creation_basic(self):\n        \"\"\"Test basic task dependency creation\"\"\"\n        dependency = TaskDependency(\n            task_id=self.task_b.id,\n            depends_on_id=self.task_a.id\n        )\n        \n        db.session.add(dependency)\n        db.session.commit()\n        \n        assert dependency.id is not None\n        assert dependency.task_id == self.task_b.id\n        assert dependency.depends_on_id == self.task_a.id\n\n    def test_task_dependency_relationships(self):\n        \"\"\"Test relationships with Task model\"\"\"\n        dependency = TaskDependency(\n            task_id=self.task_b.id,\n            depends_on_id=self.task_a.id\n        )\n        \n        db.session.add(dependency)\n        db.session.commit()\n        \n        # Test forward relationships\n        assert dependency.task is not None\n        assert dependency.task.id == self.task_b.id\n        assert dependency.task.name == 'Task B'\n\n        assert dependency.depends_on is not None\n        assert dependency.depends_on.id == self.task_a.id\n        assert dependency.depends_on.name == 'Task A'\n        \n        # Test backward relationships\n        assert dependency in self.task_b.dependencies\n        assert dependency in self.task_a.dependents\n\n    def test_task_dependency_repr_method(self):\n        \"\"\"Test string representation of task dependency\"\"\"\n        dependency = TaskDependency(\n            task_id=self.task_b.id,\n            depends_on_id=self.task_a.id\n        )\n        \n        expected_repr = f'<TaskDependency {self.task_b.id} depends on {self.task_a.id}>'\n        assert repr(dependency) == expected_repr\n\n    def test_task_dependency_foreign_key_constraints(self):\n        \"\"\"Test foreign key constraints\"\"\"\n        # Test with non-existent task_id\n        with pytest.raises(Exception):  # Should raise IntegrityError\n            dependency = TaskDependency(\n                task_id=99999,  # Non-existent task\n                depends_on_id=self.task_a.id\n            )\n            db.session.add(dependency)\n            db.session.commit()\n        \n        db.session.rollback()\n        \n        # Test with non-existent depends_on_id\n        with pytest.raises(Exception):  # Should raise IntegrityError\n            dependency = TaskDependency(\n                task_id=self.task_b.id,\n                depends_on_id=99999  # Non-existent task\n            )\n            db.session.add(dependency)\n            db.session.commit()\n\n    def test_task_dependency_self_reference_prevention(self):\n        \"\"\"Test that a task cannot depend on itself\"\"\"\n        # This should be prevented by business logic or database constraints\n        dependency = TaskDependency(\n            task_id=self.task_a.id,\n            depends_on_id=self.task_a.id  # Self-reference\n        )\n        \n        db.session.add(dependency)\n        \n        # This should either raise an error or be handled by validation\n        try:\n            db.session.commit()\n            # If no constraint, we should add business logic validation\n            assert dependency.task_id != dependency.depends_on_id, \"Self-dependency should be prevented\"\n        except Exception:\n            # If constraint exists, this is expected\n            db.session.rollback()\n            assert True\n\n    def test_task_dependency_circular_detection(self):\n        \"\"\"Test detection of circular dependencies\"\"\"\n        # Create a third task\n        task_c = Task(\n            name='Task C',\n            description='Third task',\n            project_id=self.test_project.id,\n            assignee_id=self.user.id,\n            status='todo',\n            start_date=date.today() + timedelta(days=10),\n            due_date=date.today() + timedelta(days=20)\n        )\n        db.session.add(task_c)\n        db.session.commit()\n        \n        # Create dependencies: A -> B -> C\n        dep1 = TaskDependency(task_id=self.task_b.id, depends_on_id=self.task_a.id)\n        dep2 = TaskDependency(task_id=task_c.id, depends_on_id=self.task_b.id)\n        \n        db.session.add_all([dep1, dep2])\n        db.session.commit()\n        \n        # Try to create circular dependency: C -> A (would create A -> B -> C -> A)\n        circular_dep = TaskDependency(task_id=self.task_a.id, depends_on_id=task_c.id)\n        \n        db.session.add(circular_dep)\n        \n        # This should be prevented by business logic\n        try:\n            db.session.commit()\n            # If no constraint, we should add business logic validation\n            print(\"Warning: Circular dependency was allowed - should implement validation\")\n        except Exception:\n            # If constraint exists, this is expected\n            db.session.rollback()\n            assert True\n\n    def test_task_dependency_multiple_dependencies(self):\n        \"\"\"Test task with multiple dependencies\"\"\"\n        # Create additional tasks\n        task_c = Task(\n            name='Task C',\n            description='Third task',\n            project_id=self.test_project.id,\n            assignee_id=self.user.id,\n            status='todo'\n        )\n\n        task_d = Task(\n            name='Task D',\n            description='Fourth task that depends on A, B, and C',\n            project_id=self.test_project.id,\n            assignee_id=self.user.id,\n            status='todo'\n        )\n        \n        db.session.add_all([task_c, task_d])\n        db.session.commit()\n        \n        # Create multiple dependencies for task_d\n        dependencies = [\n            TaskDependency(task_id=task_d.id, depends_on_id=self.task_a.id),\n            TaskDependency(task_id=task_d.id, depends_on_id=self.task_b.id),\n            TaskDependency(task_id=task_d.id, depends_on_id=task_c.id)\n        ]\n        \n        db.session.add_all(dependencies)\n        db.session.commit()\n        \n        # Verify multiple dependencies\n        task_d_dependencies = TaskDependency.query.filter_by(task_id=task_d.id).all()\n        assert len(task_d_dependencies) == 3\n        \n        dependency_task_ids = [dep.depends_on_id for dep in task_d_dependencies]\n        assert self.task_a.id in dependency_task_ids\n        assert self.task_b.id in dependency_task_ids\n        assert task_c.id in dependency_task_ids\n\n    def test_task_dependency_query_operations(self):\n        \"\"\"Test various query operations on task dependencies\"\"\"\n        # Create several dependencies\n        task_c = Task(\n            title='Task C',\n            project_id=self.test_project.id,\n            assigned_to=self.user.id,\n            status='todo'\n        )\n        db.session.add(task_c)\n        db.session.commit()\n        \n        dependencies = [\n            TaskDependency(task_id=self.task_b.id, depends_on_id=self.task_a.id),\n            TaskDependency(task_id=task_c.id, depends_on_id=self.task_a.id),\n            TaskDependency(task_id=task_c.id, depends_on_id=self.task_b.id)\n        ]\n        \n        db.session.add_all(dependencies)\n        db.session.commit()\n        \n        # Query dependencies for a specific task\n        task_c_deps = TaskDependency.query.filter_by(task_id=task_c.id).all()\n        assert len(task_c_deps) == 2\n        \n        # Query tasks that depend on task_a\n        depends_on_a = TaskDependency.query.filter_by(depends_on_id=self.task_a.id).all()\n        assert len(depends_on_a) == 2\n\n    def test_task_dependency_deletion_cascade(self):\n        \"\"\"Test dependency deletion when tasks are deleted\"\"\"\n        dependency = TaskDependency(\n            task_id=self.task_b.id,\n            depends_on_id=self.task_a.id\n        )\n        \n        db.session.add(dependency)\n        db.session.commit()\n        dependency_id = dependency.id\n        \n        # Delete the dependent task\n        db.session.delete(self.task_b)\n        db.session.commit()\n        \n        # Verify dependency is also deleted (if cascade is set up)\n        deleted_dependency = TaskDependency.query.get(dependency_id)\n        # This depends on the cascade configuration\n        # assert deleted_dependency is None\n\n    def test_task_dependency_update_operations(self):\n        \"\"\"Test updating task dependencies\"\"\"\n        dependency = TaskDependency(\n            task_id=self.task_b.id,\n            depends_on_id=self.task_a.id\n        )\n        \n        db.session.add(dependency)\n        db.session.commit()\n        \n        # Create a third task to change dependency to\n        task_c = Task(\n            title='Task C',\n            project_id=self.test_project.id,\n            assigned_to=self.user.id,\n            status='todo'\n        )\n        db.session.add(task_c)\n        db.session.commit()\n        \n        # Update dependency\n        dependency.depends_on_id = task_c.id\n        db.session.commit()\n        \n        # Verify update\n        updated_dependency = TaskDependency.query.get(dependency.id)\n        assert updated_dependency.depends_on_id == task_c.id\n        assert updated_dependency.task_id == self.task_b.id\n\n    def test_task_dependency_unique_constraint(self):\n        \"\"\"Test that duplicate dependencies are prevented\"\"\"\n        dependency1 = TaskDependency(\n            task_id=self.task_b.id,\n            depends_on_id=self.task_a.id\n        )\n        \n        dependency2 = TaskDependency(\n            task_id=self.task_b.id,\n            depends_on_id=self.task_a.id  # Duplicate\n        )\n        \n        db.session.add(dependency1)\n        db.session.commit()\n        \n        # Try to add duplicate dependency\n        db.session.add(dependency2)\n        try:\n            db.session.commit()\n            # If no constraint, both will exist (might be allowed)\n            duplicates = TaskDependency.query.filter_by(\n                task_id=self.task_b.id,\n                depends_on_id=self.task_a.id\n            ).all()\n            # Could be 1 or 2 depending on constraints\n            assert len(duplicates) >= 1\n        except Exception:\n            # If constraint exists, this is expected\n            db.session.rollback()\n            assert True\n\n    def test_task_dependency_cross_project_prevention(self):\n        \"\"\"Test that dependencies across different projects are handled\"\"\"\n        # Create another project and task\n        other_project = Project(\n            name='Other Project',\n            description='Different project',\n            status='active',\n            start_date=date.today(),\n            end_date=date.today() + timedelta(days=60)\n        )\n        db.session.add(other_project)\n        db.session.commit()\n        \n        other_task = Task(\n            title='Other Task',\n            project_id=other_project.id,\n            assigned_to=self.user.id,\n            status='todo'\n        )\n        db.session.add(other_task)\n        db.session.commit()\n        \n        # Try to create cross-project dependency\n        cross_dependency = TaskDependency(\n            task_id=self.task_b.id,  # From test_project\n            depends_on_id=other_task.id  # From other_project\n        )\n        \n        db.session.add(cross_dependency)\n        \n        try:\n            db.session.commit()\n            # If allowed, verify it was created\n            assert cross_dependency.id is not None\n            print(\"Cross-project dependencies are allowed\")\n        except Exception:\n            # If prevented by constraints, this is expected\n            db.session.rollback()\n            print(\"Cross-project dependencies are prevented\")\n            assert True\n", "modifiedCode": "\"\"\"\nUnit tests for TaskDependency model.\nTests business logic, validations, and model functionality.\n\"\"\"\n\nimport pytest\nfrom datetime import datetime, date, timedelta\nfrom models import TaskDependency, Task, Project, User\nfrom extensions import db\n\n\nclass TestTaskDependencyModel:\n    \"\"\"Test suite for TaskDependency model unit tests\"\"\"\n\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        \"\"\"Setup test data for each test method\"\"\"\n        self.app = app\n        self.user = test_user\n        \n        # Create test project\n        self.test_project = Project(\n            name='Test Project',\n            description='Project for testing task dependencies',\n            status='active',\n            start_date=date.today(),\n            end_date=date.today() + timedelta(days=90)\n        )\n        db.session.add(self.test_project)\n        db.session.commit()\n        \n        # Create test tasks (using correct field names from DB schema)\n        self.task_a = Task(\n            name='Task A',\n            description='First task',\n            project_id=self.test_project.id,\n            assignee_id=self.user.id,\n            status='todo',\n            start_date=date.today(),\n            due_date=date.today() + timedelta(days=10)\n        )\n\n        self.task_b = Task(\n            name='Task B',\n            description='Second task',\n            project_id=self.test_project.id,\n            assignee_id=self.user.id,\n            status='todo',\n            start_date=date.today() + timedelta(days=5),\n            due_date=date.today() + timedelta(days=15)\n        )\n        \n        db.session.add_all([self.task_a, self.task_b])\n        db.session.commit()\n\n    def test_task_dependency_creation_basic(self):\n        \"\"\"Test basic task dependency creation\"\"\"\n        dependency = TaskDependency(\n            task_id=self.task_b.id,\n            depends_on_id=self.task_a.id\n        )\n        \n        db.session.add(dependency)\n        db.session.commit()\n        \n        assert dependency.id is not None\n        assert dependency.task_id == self.task_b.id\n        assert dependency.depends_on_id == self.task_a.id\n\n    def test_task_dependency_relationships(self):\n        \"\"\"Test relationships with Task model\"\"\"\n        dependency = TaskDependency(\n            task_id=self.task_b.id,\n            depends_on_id=self.task_a.id\n        )\n        \n        db.session.add(dependency)\n        db.session.commit()\n        \n        # Test forward relationships\n        assert dependency.task is not None\n        assert dependency.task.id == self.task_b.id\n        assert dependency.task.name == 'Task B'\n\n        assert dependency.depends_on is not None\n        assert dependency.depends_on.id == self.task_a.id\n        assert dependency.depends_on.name == 'Task A'\n        \n        # Test backward relationships\n        assert dependency in self.task_b.dependencies\n        assert dependency in self.task_a.dependents\n\n    def test_task_dependency_repr_method(self):\n        \"\"\"Test string representation of task dependency\"\"\"\n        dependency = TaskDependency(\n            task_id=self.task_b.id,\n            depends_on_id=self.task_a.id\n        )\n        \n        expected_repr = f'<TaskDependency {self.task_b.id} depends on {self.task_a.id}>'\n        assert repr(dependency) == expected_repr\n\n    def test_task_dependency_foreign_key_constraints(self):\n        \"\"\"Test foreign key constraints\"\"\"\n        # Test with non-existent task_id\n        with pytest.raises(Exception):  # Should raise IntegrityError\n            dependency = TaskDependency(\n                task_id=99999,  # Non-existent task\n                depends_on_id=self.task_a.id\n            )\n            db.session.add(dependency)\n            db.session.commit()\n        \n        db.session.rollback()\n        \n        # Test with non-existent depends_on_id\n        with pytest.raises(Exception):  # Should raise IntegrityError\n            dependency = TaskDependency(\n                task_id=self.task_b.id,\n                depends_on_id=99999  # Non-existent task\n            )\n            db.session.add(dependency)\n            db.session.commit()\n\n    def test_task_dependency_self_reference_prevention(self):\n        \"\"\"Test that a task cannot depend on itself\"\"\"\n        # This should be prevented by business logic or database constraints\n        dependency = TaskDependency(\n            task_id=self.task_a.id,\n            depends_on_id=self.task_a.id  # Self-reference\n        )\n        \n        db.session.add(dependency)\n        \n        # This should either raise an error or be handled by validation\n        try:\n            db.session.commit()\n            # If no constraint, we should add business logic validation\n            assert dependency.task_id != dependency.depends_on_id, \"Self-dependency should be prevented\"\n        except Exception:\n            # If constraint exists, this is expected\n            db.session.rollback()\n            assert True\n\n    def test_task_dependency_circular_detection(self):\n        \"\"\"Test detection of circular dependencies\"\"\"\n        # Create a third task\n        task_c = Task(\n            name='Task C',\n            description='Third task',\n            project_id=self.test_project.id,\n            assignee_id=self.user.id,\n            status='todo',\n            start_date=date.today() + timedelta(days=10),\n            due_date=date.today() + timedelta(days=20)\n        )\n        db.session.add(task_c)\n        db.session.commit()\n        \n        # Create dependencies: A -> B -> C\n        dep1 = TaskDependency(task_id=self.task_b.id, depends_on_id=self.task_a.id)\n        dep2 = TaskDependency(task_id=task_c.id, depends_on_id=self.task_b.id)\n        \n        db.session.add_all([dep1, dep2])\n        db.session.commit()\n        \n        # Try to create circular dependency: C -> A (would create A -> B -> C -> A)\n        circular_dep = TaskDependency(task_id=self.task_a.id, depends_on_id=task_c.id)\n        \n        db.session.add(circular_dep)\n        \n        # This should be prevented by business logic\n        try:\n            db.session.commit()\n            # If no constraint, we should add business logic validation\n            print(\"Warning: Circular dependency was allowed - should implement validation\")\n        except Exception:\n            # If constraint exists, this is expected\n            db.session.rollback()\n            assert True\n\n    def test_task_dependency_multiple_dependencies(self):\n        \"\"\"Test task with multiple dependencies\"\"\"\n        # Create additional tasks\n        task_c = Task(\n            name='Task C',\n            description='Third task',\n            project_id=self.test_project.id,\n            assignee_id=self.user.id,\n            status='todo'\n        )\n\n        task_d = Task(\n            name='Task D',\n            description='Fourth task that depends on A, B, and C',\n            project_id=self.test_project.id,\n            assignee_id=self.user.id,\n            status='todo'\n        )\n        \n        db.session.add_all([task_c, task_d])\n        db.session.commit()\n        \n        # Create multiple dependencies for task_d\n        dependencies = [\n            TaskDependency(task_id=task_d.id, depends_on_id=self.task_a.id),\n            TaskDependency(task_id=task_d.id, depends_on_id=self.task_b.id),\n            TaskDependency(task_id=task_d.id, depends_on_id=task_c.id)\n        ]\n        \n        db.session.add_all(dependencies)\n        db.session.commit()\n        \n        # Verify multiple dependencies\n        task_d_dependencies = TaskDependency.query.filter_by(task_id=task_d.id).all()\n        assert len(task_d_dependencies) == 3\n        \n        dependency_task_ids = [dep.depends_on_id for dep in task_d_dependencies]\n        assert self.task_a.id in dependency_task_ids\n        assert self.task_b.id in dependency_task_ids\n        assert task_c.id in dependency_task_ids\n\n    def test_task_dependency_query_operations(self):\n        \"\"\"Test various query operations on task dependencies\"\"\"\n        # Create several dependencies\n        task_c = Task(\n            title='Task C',\n            project_id=self.test_project.id,\n            assigned_to=self.user.id,\n            status='todo'\n        )\n        db.session.add(task_c)\n        db.session.commit()\n        \n        dependencies = [\n            TaskDependency(task_id=self.task_b.id, depends_on_id=self.task_a.id),\n            TaskDependency(task_id=task_c.id, depends_on_id=self.task_a.id),\n            TaskDependency(task_id=task_c.id, depends_on_id=self.task_b.id)\n        ]\n        \n        db.session.add_all(dependencies)\n        db.session.commit()\n        \n        # Query dependencies for a specific task\n        task_c_deps = TaskDependency.query.filter_by(task_id=task_c.id).all()\n        assert len(task_c_deps) == 2\n        \n        # Query tasks that depend on task_a\n        depends_on_a = TaskDependency.query.filter_by(depends_on_id=self.task_a.id).all()\n        assert len(depends_on_a) == 2\n\n    def test_task_dependency_deletion_cascade(self):\n        \"\"\"Test dependency deletion when tasks are deleted\"\"\"\n        dependency = TaskDependency(\n            task_id=self.task_b.id,\n            depends_on_id=self.task_a.id\n        )\n        \n        db.session.add(dependency)\n        db.session.commit()\n        dependency_id = dependency.id\n        \n        # Delete the dependent task\n        db.session.delete(self.task_b)\n        db.session.commit()\n        \n        # Verify dependency is also deleted (if cascade is set up)\n        deleted_dependency = TaskDependency.query.get(dependency_id)\n        # This depends on the cascade configuration\n        # assert deleted_dependency is None\n\n    def test_task_dependency_update_operations(self):\n        \"\"\"Test updating task dependencies\"\"\"\n        dependency = TaskDependency(\n            task_id=self.task_b.id,\n            depends_on_id=self.task_a.id\n        )\n        \n        db.session.add(dependency)\n        db.session.commit()\n        \n        # Create a third task to change dependency to\n        task_c = Task(\n            title='Task C',\n            project_id=self.test_project.id,\n            assigned_to=self.user.id,\n            status='todo'\n        )\n        db.session.add(task_c)\n        db.session.commit()\n        \n        # Update dependency\n        dependency.depends_on_id = task_c.id\n        db.session.commit()\n        \n        # Verify update\n        updated_dependency = TaskDependency.query.get(dependency.id)\n        assert updated_dependency.depends_on_id == task_c.id\n        assert updated_dependency.task_id == self.task_b.id\n\n    def test_task_dependency_unique_constraint(self):\n        \"\"\"Test that duplicate dependencies are prevented\"\"\"\n        dependency1 = TaskDependency(\n            task_id=self.task_b.id,\n            depends_on_id=self.task_a.id\n        )\n        \n        dependency2 = TaskDependency(\n            task_id=self.task_b.id,\n            depends_on_id=self.task_a.id  # Duplicate\n        )\n        \n        db.session.add(dependency1)\n        db.session.commit()\n        \n        # Try to add duplicate dependency\n        db.session.add(dependency2)\n        try:\n            db.session.commit()\n            # If no constraint, both will exist (might be allowed)\n            duplicates = TaskDependency.query.filter_by(\n                task_id=self.task_b.id,\n                depends_on_id=self.task_a.id\n            ).all()\n            # Could be 1 or 2 depending on constraints\n            assert len(duplicates) >= 1\n        except Exception:\n            # If constraint exists, this is expected\n            db.session.rollback()\n            assert True\n\n    def test_task_dependency_cross_project_prevention(self):\n        \"\"\"Test that dependencies across different projects are handled\"\"\"\n        # Create another project and task\n        other_project = Project(\n            name='Other Project',\n            description='Different project',\n            status='active',\n            start_date=date.today(),\n            end_date=date.today() + timedelta(days=60)\n        )\n        db.session.add(other_project)\n        db.session.commit()\n        \n        other_task = Task(\n            title='Other Task',\n            project_id=other_project.id,\n            assigned_to=self.user.id,\n            status='todo'\n        )\n        db.session.add(other_task)\n        db.session.commit()\n        \n        # Try to create cross-project dependency\n        cross_dependency = TaskDependency(\n            task_id=self.task_b.id,  # From test_project\n            depends_on_id=other_task.id  # From other_project\n        )\n        \n        db.session.add(cross_dependency)\n        \n        try:\n            db.session.commit()\n            # If allowed, verify it was created\n            assert cross_dependency.id is not None\n            print(\"Cross-project dependencies are allowed\")\n        except Exception:\n            # If prevented by constraints, this is expected\n            db.session.rollback()\n            print(\"Cross-project dependencies are prevented\")\n            assert True\n"}