{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/testing/e2e-testing-guide.md"}, "originalCode": "\n", "modifiedCode": "# 🌐 End-to-End Testing Guide\n\nGuida completa per il testing end-to-end con Cypress per validare workflow utente completi.\n\n## 📋 Indice\n\n- [Setup e Configurazione](#setup-e-configurazione)\n- [Workflow Testing](#workflow-testing)\n- [User Journey Testing](#user-journey-testing)\n- [Cross-Browser Testing](#cross-browser-testing)\n- [Data Management](#data-management)\n- [Custom Commands](#custom-commands)\n- [Best Practices](#best-practices)\n\n## ⚙️ Setup e Configurazione\n\n### Struttura Directory\n\n```\nfrontend/cypress/\n├── e2e/\n│   ├── auth/\n│   │   ├── login.cy.js              # Test autenticazione\n│   │   └── user-management.cy.js    # Test gestione utenti\n│   ├── projects/\n│   │   ├── project-creation.cy.js   # Test creazione progetti\n│   │   ├── project-workflows.cy.js  # Test workflow progetti\n│   │   └── team-management.cy.js    # Test gestione team\n│   ├── timesheet/\n│   │   ├── timesheet-entry.cy.js    # Test inserimento ore\n│   │   └── timesheet-approval.cy.js # Test approvazione\n│   └── dashboard/\n│       └── dashboard-overview.cy.js # Test dashboard\n├── fixtures/\n│   ├── projects.json               # Dati progetti di test\n│   ├── users.json                 # Dati utenti di test\n│   └── timesheet.json             # Dati timesheet di test\n├── support/\n│   ├── commands.js                # Custom commands\n│   ├── e2e.js                    # Setup globale\n│   └── utils.js                  # Utility functions\n└── cypress.config.js             # Configurazione Cypress\n```\n\n### Configurazione Cypress\n\n```javascript\n// cypress.config.js\nimport { defineConfig } from 'cypress'\n\nexport default defineConfig({\n  e2e: {\n    baseUrl: 'http://localhost:3000',\n    viewportWidth: 1280,\n    viewportHeight: 720,\n    video: true,\n    screenshotOnRunFailure: true,\n\n    // Test files\n    specPattern: 'cypress/e2e/**/*.cy.{js,jsx,ts,tsx}',\n\n    // Support files\n    supportFile: 'cypress/support/e2e.js',\n\n    // Timeouts\n    defaultCommandTimeout: 10000,\n    requestTimeout: 10000,\n    responseTimeout: 10000,\n\n    // Retry configuration\n    retries: {\n      runMode: 2,\n      openMode: 0\n    },\n\n    // Environment variables\n    env: {\n      apiUrl: 'http://localhost:5000/api',\n      adminUsername: 'admin',\n      adminPassword: 'password'\n    },\n\n    setupNodeEvents(on, config) {\n      // Task plugins\n      on('task', {\n        // Database seeding\n        seedDatabase() {\n          // Seed test data\n          return null\n        },\n\n        // Clear database\n        clearDatabase() {\n          // Clear test data\n          return null\n        }\n      })\n\n      return config\n    }\n  }\n})\n```\n\n### Setup Globale\n\n```javascript\n// cypress/support/e2e.js\nimport './commands'\nimport './utils'\n\n// Global configuration\nCypress.on('uncaught:exception', (err, runnable) => {\n  // Prevent Cypress from failing on uncaught exceptions\n  if (err.message.includes('ResizeObserver loop limit exceeded')) {\n    return false\n  }\n  return true\n})\n\n// Before each test\nbeforeEach(() => {\n  // Clear local storage\n  cy.clearLocalStorage()\n\n  // Clear cookies\n  cy.clearCookies()\n\n  // Set viewport\n  cy.viewport(1280, 720)\n})\n\n// After each test\nafterEach(() => {\n  // Take screenshot on failure\n  if (Cypress.currentTest.state === 'failed') {\n    cy.screenshot(`failed-${Cypress.currentTest.title}`)\n  }\n})\n```\n\n## 🔄 Workflow Testing\n\n### Test Creazione Progetto Completo\n\n```javascript\n// cypress/e2e/projects/project-creation.cy.js\ndescribe('Project Creation Workflow', () => {\n  beforeEach(() => {\n    cy.loginAsAdmin()\n    cy.intercept('GET', '/api/clients*', { fixture: 'clients.json' }).as('getClients')\n    cy.intercept('POST', '/api/projects', { fixture: 'project-created.json' }).as('createProject')\n  })\n\n  it('should create complete project with all details', () => {\n    // 1. Navigate to project creation\n    cy.visit('/app/projects')\n    cy.get('[data-testid=\"create-project-button\"]').click()\n    cy.url().should('include', '/projects/new')\n\n    // 2. Fill basic project information\n    cy.get('[data-testid=\"project-name\"]').type('E2E Complete Project')\n    cy.get('[data-testid=\"project-description\"]').type('Full end-to-end test project with comprehensive features')\n\n    // 3. Set financial details\n    cy.get('[data-testid=\"project-budget\"]').type('75000')\n    cy.get('[data-testid=\"project-type\"]').select('service')\n    cy.get('[data-testid=\"project-billable\"]').check()\n    cy.get('[data-testid=\"client-daily-rate\"]').type('500')\n\n    // 4. Set project timeline\n    cy.get('[data-testid=\"project-start-date\"]').type('2025-01-01')\n    cy.get('[data-testid=\"project-end-date\"]').type('2025-06-30')\n\n    // 5. Select client\n    cy.wait('@getClients')\n    cy.get('[data-testid=\"project-client\"]').select('TechCorp Solutions')\n\n    // 6. Set project status and type\n    cy.get('[data-testid=\"project-status\"]').select('planning')\n    cy.get('[data-testid=\"is-billable\"]').check()\n\n    // 7. Save project\n    cy.get('[data-testid=\"save-button\"]').click()\n    cy.wait('@createProject')\n\n    // 8. Verify success and navigation\n    cy.get('[data-testid=\"success-message\"]')\n      .should('be.visible')\n      .and('contain', 'Project created successfully')\n\n    cy.url().should('match', /\\/projects\\/\\d+/)\n\n    // 9. Verify project details page\n    cy.get('[data-testid=\"project-title\"]').should('contain', 'E2E Complete Project')\n    cy.get('[data-testid=\"project-budget\"]').should('contain', '€75,000')\n    cy.get('[data-testid=\"project-status\"]').should('contain', 'Planning')\n    cy.get('[data-testid=\"client-name\"]').should('contain', 'TechCorp Solutions')\n  })\n\n  it('should handle validation errors during creation', () => {\n    cy.visit('/app/projects/new')\n\n    // Try to save without required fields\n    cy.get('[data-testid=\"save-button\"]').click()\n\n    // Verify validation errors\n    cy.get('[data-testid=\"name-error\"]')\n      .should('be.visible')\n      .and('contain', 'Project name is required')\n\n    cy.get('[data-testid=\"budget-error\"]')\n      .should('be.visible')\n      .and('contain', 'Budget is required')\n\n    // Fill invalid data\n    cy.get('[data-testid=\"project-name\"]').type('A') // Too short\n    cy.get('[data-testid=\"project-budget\"]').type('-1000') // Negative\n    cy.get('[data-testid=\"project-start-date\"]').type('2025-12-31')\n    cy.get('[data-testid=\"project-end-date\"]').type('2025-01-01') // Before start\n\n    cy.get('[data-testid=\"save-button\"]').click()\n\n    // Verify specific validation errors\n    cy.get('[data-testid=\"name-error\"]').should('contain', 'at least 3 characters')\n    cy.get('[data-testid=\"budget-error\"]').should('contain', 'must be positive')\n    cy.get('[data-testid=\"end-date-error\"]').should('contain', 'must be after start date')\n  })\n\n  it('should save draft and continue later', () => {\n    cy.visit('/app/projects/new')\n\n    // Fill partial information\n    cy.get('[data-testid=\"project-name\"]').type('Draft Project')\n    cy.get('[data-testid=\"project-description\"]').type('This is a draft project')\n\n    // Save as draft\n    cy.get('[data-testid=\"save-draft-button\"]').click()\n\n    // Verify draft saved\n    cy.get('[data-testid=\"draft-saved-message\"]')\n      .should('be.visible')\n      .and('contain', 'Draft saved')\n\n    // Navigate away and back\n    cy.visit('/app/dashboard')\n    cy.visit('/app/projects/new')\n\n    // Verify draft data restored\n    cy.get('[data-testid=\"project-name\"]').should('have.value', 'Draft Project')\n    cy.get('[data-testid=\"project-description\"]').should('contain', 'This is a draft project')\n  })\n})\n```"}