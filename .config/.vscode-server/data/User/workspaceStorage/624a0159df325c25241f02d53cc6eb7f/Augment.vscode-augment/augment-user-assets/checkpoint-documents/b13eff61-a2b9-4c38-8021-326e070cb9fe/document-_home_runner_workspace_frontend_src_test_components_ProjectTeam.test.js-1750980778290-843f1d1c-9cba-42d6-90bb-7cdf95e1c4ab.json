{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/ProjectTeam.test.js"}, "modifiedCode": "import { describe, it, expect, beforeEach, vi } from 'vitest'\nimport { mount } from '@vue/test-utils'\nimport { createPinia } from 'pinia'\nimport ProjectTeam from '@/views/projects/components/ProjectTeam.vue'\nimport { useAuthStore } from '@/stores/auth'\n\n// Mock the auth store\nvi.mock('@/stores/auth', () => ({\n  useAuthStore: vi.fn()\n}))\n\ndescribe('ProjectTeam Component', () => {\n  let wrapper\n  let mockAuthStore\n  let mockProject\n\n  beforeEach(() => {\n    // Setup mock auth store\n    mockAuthStore = {\n      user: { id: 1, role: 'admin' },\n      csrfToken: 'mock-csrf-token',\n      hasPermission: vi.fn(() => true)\n    }\n    useAuthStore.mockReturnValue(mockAuthStore)\n\n    // Mock project data\n    mockProject = {\n      id: 1,\n      name: 'Test Project',\n      team_members: [\n        {\n          id: 1,\n          full_name: '<PERSON>',\n          email: '<EMAIL>',\n          role: 'Project Manager',\n          allocation_percentage: 50,\n          hours_worked: 40\n        },\n        {\n          id: 2,\n          full_name: '<PERSON>', \n          email: '<EMAIL>',\n          role: 'Developer',\n          allocation_percentage: 100,\n          hours_worked: 80\n        }\n      ]\n    }\n\n    // Mock fetch globally\n    global.fetch = vi.fn()\n  })\n\n  afterEach(() => {\n    vi.clearAllMocks()\n  })\n\n  describe('Component Rendering', () => {\n    it('should render team members correctly', () => {\n      wrapper = mount(ProjectTeam, {\n        props: { project: mockProject },\n        global: {\n          plugins: [createPinia()],\n          stubs: ['HeroIcon']\n        }\n      })\n\n      // Test that team members are displayed\n      expect(wrapper.text()).toContain('John Doe')\n      expect(wrapper.text()).toContain('Jane Smith')\n      expect(wrapper.text()).toContain('Project Manager')\n      expect(wrapper.text()).toContain('Developer')\n    })\n\n    it('should display team statistics correctly', () => {\n      wrapper = mount(ProjectTeam, {\n        props: { project: mockProject },\n        global: {\n          plugins: [createPinia()],\n          stubs: ['HeroIcon']\n        }\n      })\n\n      // Test computed statistics\n      const totalHours = wrapper.vm.totalHoursWorked\n      const averageHours = wrapper.vm.averageHoursPerMember\n      const activeMembers = wrapper.vm.activeMembersCount\n\n      expect(totalHours).toBe(120) // 40 + 80\n      expect(averageHours).toBe(60) // 120 / 2\n      expect(activeMembers).toBe(2) // Both have hours > 0\n    })\n\n    it('should show empty state when no team members', () => {\n      const emptyProject = { ...mockProject, team_members: [] }\n      \n      wrapper = mount(ProjectTeam, {\n        props: { project: emptyProject },\n        global: {\n          plugins: [createPinia()],\n          stubs: ['HeroIcon']\n        }\n      })\n\n      expect(wrapper.text()).toContain('No team members')\n      expect(wrapper.find('[data-testid=\"empty-team-state\"]').exists()).toBe(true)\n    })\n  })\n\n  describe('Props and Events', () => {\n    it('should handle loading prop correctly', () => {\n      wrapper = mount(ProjectTeam, {\n        props: { \n          project: mockProject,\n          loading: true \n        },\n        global: {\n          plugins: [createPinia()],\n          stubs: ['HeroIcon']\n        }\n      })\n\n      expect(wrapper.find('[data-testid=\"loading-spinner\"]').exists()).toBe(true)\n    })\n\n    it('should emit refresh event when team changes', async () => {\n      wrapper = mount(ProjectTeam, {\n        props: { project: mockProject },\n        global: {\n          plugins: [createPinia()],\n          stubs: ['HeroIcon']\n        }\n      })\n\n      // Mock successful API response\n      fetch.mockResolvedValueOnce({\n        ok: true,\n        json: () => Promise.resolve({ success: true })\n      })\n\n      // Trigger team member addition\n      await wrapper.vm.addMember()\n\n      // Check that refresh event was emitted\n      expect(wrapper.emitted('refresh')).toBeTruthy()\n    })\n  })\n\n  describe('User Interactions', () => {\n    beforeEach(() => {\n      wrapper = mount(ProjectTeam, {\n        props: { project: mockProject },\n        global: {\n          plugins: [createPinia()],\n          stubs: ['HeroIcon']\n        }\n      })\n    })\n\n    it('should open add member modal', async () => {\n      const addButton = wrapper.find('[data-testid=\"add-member-button\"]')\n      await addButton.trigger('click')\n\n      expect(wrapper.vm.showAddMemberModal).toBe(true)\n      expect(wrapper.find('[data-testid=\"add-member-modal\"]').exists()).toBe(true)\n    })\n\n    it('should handle form submission for adding member', async () => {\n      // Mock API response\n      fetch.mockResolvedValueOnce({\n        ok: true,\n        json: () => Promise.resolve({ success: true })\n      })\n\n      // Set form data\n      wrapper.vm.newMemberForm = {\n        user_id: '3',\n        role: 'QA Tester',\n        allocation_percentage: 75\n      }\n\n      // Submit form\n      await wrapper.vm.addMember()\n\n      // Verify API call\n      expect(fetch).toHaveBeenCalledWith(`/api/projects/${mockProject.id}/team`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'X-CSRFToken': 'mock-csrf-token'\n        },\n        body: JSON.stringify({\n          user_id: '3',\n          role: 'QA Tester',\n          allocation_percentage: 75\n        })\n      })\n    })\n\n    it('should handle API errors gracefully', async () => {\n      // Mock API error\n      fetch.mockRejectedValueOnce(new Error('Network error'))\n\n      // Spy on alert (in real app, you'd use a toast system)\n      const alertSpy = vi.spyOn(window, 'alert').mockImplementation(() => {})\n\n      await wrapper.vm.addMember()\n\n      expect(alertSpy).toHaveBeenCalledWith('Errore nell\\'aggiunta del membro')\n      alertSpy.mockRestore()\n    })\n  })\n\n  describe('Permissions and Access Control', () => {\n    it('should hide add button for unauthorized users', () => {\n      mockAuthStore.hasPermission.mockReturnValue(false)\n\n      wrapper = mount(ProjectTeam, {\n        props: { project: mockProject },\n        global: {\n          plugins: [createPinia()],\n          stubs: ['HeroIcon']\n        }\n      })\n\n      expect(wrapper.find('[data-testid=\"add-member-button\"]').exists()).toBe(false)\n    })\n\n    it('should show admin actions for admin users', () => {\n      mockAuthStore.user.role = 'admin'\n\n      wrapper = mount(ProjectTeam, {\n        props: { project: mockProject },\n        global: {\n          plugins: [createPinia()],\n          stubs: ['HeroIcon']\n        }\n      })\n\n      expect(wrapper.find('[data-testid=\"admin-actions\"]').exists()).toBe(true)\n    })\n  })\n\n  describe('Data Validation', () => {\n    beforeEach(() => {\n      wrapper = mount(ProjectTeam, {\n        props: { project: mockProject },\n        global: {\n          plugins: [createPinia()],\n          stubs: ['HeroIcon']\n        }\n      })\n    })\n\n    it('should validate form inputs', async () => {\n      // Set invalid form data\n      wrapper.vm.newMemberForm = {\n        user_id: '',\n        role: '',\n        allocation_percentage: -10\n      }\n\n      await wrapper.vm.addMember()\n\n      // Should not make API call with invalid data\n      expect(fetch).not.toHaveBeenCalled()\n    })\n\n    it('should format allocation percentage correctly', () => {\n      const member = mockProject.team_members[0]\n      const formatted = wrapper.vm.formatAllocation(member.allocation_percentage)\n      \n      expect(formatted).toBe('50%')\n    })\n  })\n})\n"}