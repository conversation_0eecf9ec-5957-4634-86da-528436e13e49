{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_userskill_model.py"}, "originalCode": "\"\"\"\nUnit tests for UserSkill model.\nTests business logic, validations, and model functionality.\n\"\"\"\n\nimport pytest\nfrom datetime import datetime, date\nfrom models import User<PERSON>kill, User, Skill\nfrom extensions import db\n\n\nclass TestUserSkillModel:\n    \"\"\"Test suite for UserSkill model unit tests\"\"\"\n\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        \"\"\"Setup test data for each test method\"\"\"\n        self.app = app\n        self.user = test_user\n\n    def test_userskill_creation_basic(self):\n        \"\"\"Test basic user skill creation\"\"\"\n        # Create test skill\n        skill = Skill(\n            name='Python Programming',\n            category='Programming',\n            description='Python development skills'\n        )\n        db.session.add(skill)\n        db.session.commit()\n        \n        user_skill = UserSkill(\n            user_id=self.user.id,\n            skill_id=skill.id,\n            proficiency_level=3,\n            years_experience=2.5\n        )\n        \n        db.session.add(user_skill)\n        db.session.commit()\n        \n        assert user_skill.id is not None\n        assert user_skill.user_id == self.user.id\n        assert user_skill.skill_id == skill.id\n        assert user_skill.proficiency_level == 3\n        assert user_skill.years_experience == 2.5\n\n    def test_userskill_creation_complete(self):\n        \"\"\"Test user skill creation with all fields\"\"\"\n        skill = Skill(\n            name='JavaScript Development',\n            category='Programming',\n            description='JavaScript skills'\n        )\n        db.session.add(skill)\n        db.session.commit()\n        \n        user_skill = UserSkill(\n            user_id=self.user.id,\n            skill_id=skill.id,\n            proficiency_level=4,\n            years_experience=3.0,\n            last_used=date(2024, 12, 1),\n            certified=True,\n            certification_date=date(2024, 6, 15),\n            certification_name='JavaScript Professional Certificate'\n        )\n        \n        db.session.add(user_skill)\n        db.session.commit()\n        \n        assert user_skill.proficiency_level == 4\n        assert user_skill.years_experience == 3.0\n        assert user_skill.last_used == date(2024, 12, 1)\n        assert user_skill.certified is True\n        assert user_skill.certification_date == date(2024, 6, 15)\n        assert user_skill.certification_name == 'JavaScript Professional Certificate'\n\n    def test_userskill_relationships(self):\n        \"\"\"Test relationships with User and Skill models\"\"\"\n        skill = Skill(\n            name='Database Design',\n            category='Database',\n            description='Database design skills'\n        )\n        db.session.add(skill)\n        db.session.commit()\n        \n        user_skill = UserSkill(\n            user_id=self.user.id,\n            skill_id=skill.id,\n            proficiency_level=2\n        )\n        \n        db.session.add(user_skill)\n        db.session.commit()\n        \n        # Test forward relationships\n        assert user_skill.user is not None\n        assert user_skill.user.id == self.user.id\n        assert user_skill.skill is not None\n        assert user_skill.skill.id == skill.id\n\n    def test_userskill_proficiency_levels(self):\n        \"\"\"Test different proficiency levels\"\"\"\n        skill = Skill(\n            name='Project Management',\n            category='Management',\n            description='Project management skills'\n        )\n        db.session.add(skill)\n        db.session.commit()\n        \n        proficiency_levels = [1, 2, 3, 4, 5]  # Beginner to Expert\n\n        # Create different skills for each proficiency level\n        skills = []\n        for i, level in enumerate(proficiency_levels):\n            skill_unique = Skill(\n                name=f'Project Management Level {level}',\n                category='Management',\n                description=f'Project management skills level {level}'\n            )\n            skills.append(skill_unique)\n\n        db.session.add_all(skills)\n        db.session.commit()\n\n        user_skills = []\n        for level, skill_unique in zip(proficiency_levels, skills):\n            user_skill = UserSkill(\n                user_id=self.user.id,\n                skill_id=skill_unique.id,\n                proficiency_level=level,\n                years_experience=level * 0.5  # Different experience for each\n            )\n            user_skills.append(user_skill)\n        \n        db.session.add_all(user_skills)\n        db.session.commit()\n        \n        for user_skill, expected_level in zip(user_skills, proficiency_levels):\n            assert user_skill.proficiency_level == expected_level\n            assert 1 <= user_skill.proficiency_level <= 5\n\n    def test_userskill_years_experience(self):\n        \"\"\"Test years of experience field\"\"\"\n        experience_values = [0.5, 1.0, 2.5, 5.0, 10.0]\n\n        # Create different skills for each experience level\n        skills = []\n        for i, experience in enumerate(experience_values):\n            skill_unique = Skill(\n                name=f'Machine Learning Experience {i}',\n                category='AI',\n                description=f'ML skills {i}'\n            )\n            skills.append(skill_unique)\n\n        db.session.add_all(skills)\n        db.session.commit()\n\n        user_skills = []\n        for experience, skill_unique in zip(experience_values, skills):\n            user_skill = UserSkill(\n                user_id=self.user.id,\n                skill_id=skill_unique.id,\n                proficiency_level=2,\n                years_experience=experience\n            )\n            user_skills.append(user_skill)\n        \n        db.session.add_all(user_skills)\n        db.session.commit()\n        \n        for user_skill, expected_experience in zip(user_skills, experience_values):\n            assert user_skill.years_experience == expected_experience\n            assert user_skill.years_experience >= 0\n\n    def test_userskill_certification_functionality(self):\n        \"\"\"Test certification-related fields\"\"\"\n        skill = Skill(\n            name='AWS Cloud',\n            category='Cloud',\n            description='AWS cloud skills'\n        )\n        db.session.add(skill)\n        db.session.commit()\n        \n        # Certified skill\n        certified_skill = UserSkill(\n            user_id=self.user.id,\n            skill_id=skill.id,\n            proficiency_level=4,\n            certified=True,\n            certification_date=date(2024, 3, 15),\n            certification_name='AWS Solutions Architect'\n        )\n        \n        # Non-certified skill\n        non_certified_skill = UserSkill(\n            user_id=self.user.id,\n            skill_id=skill.id,\n            proficiency_level=3,\n            certified=False\n        )\n        \n        db.session.add_all([certified_skill, non_certified_skill])\n        db.session.commit()\n        \n        assert certified_skill.certified is True\n        assert certified_skill.certification_date == date(2024, 3, 15)\n        assert certified_skill.certification_name == 'AWS Solutions Architect'\n        \n        assert non_certified_skill.certified is False\n        assert non_certified_skill.certification_date is None\n        assert non_certified_skill.certification_name is None\n\n    def test_userskill_last_used_tracking(self):\n        \"\"\"Test last used date tracking\"\"\"\n        skill = Skill(\n            name='React Development',\n            category='Frontend',\n            description='React skills'\n        )\n        db.session.add(skill)\n        db.session.commit()\n        \n        user_skill = UserSkill(\n            user_id=self.user.id,\n            skill_id=skill.id,\n            proficiency_level=3,\n            last_used=date(2024, 11, 30)\n        )\n        \n        db.session.add(user_skill)\n        db.session.commit()\n        \n        assert user_skill.last_used == date(2024, 11, 30)\n        \n        # Update last used date\n        user_skill.last_used = date(2024, 12, 15)\n        db.session.commit()\n        \n        assert user_skill.last_used == date(2024, 12, 15)\n\n    def test_userskill_query_by_user(self):\n        \"\"\"Test querying skills by user\"\"\"\n        skill = Skill(\n            name='Docker Containers',\n            category='DevOps',\n            description='Docker skills'\n        )\n        db.session.add(skill)\n        db.session.commit()\n        \n        user_skill = UserSkill(\n            user_id=self.user.id,\n            skill_id=skill.id,\n            proficiency_level=3\n        )\n        \n        db.session.add(user_skill)\n        db.session.commit()\n        \n        # Query skills by user\n        user_skills = UserSkill.query.filter_by(user_id=self.user.id).all()\n        assert len(user_skills) >= 1\n        assert user_skill in user_skills\n\n    def test_userskill_query_by_skill(self):\n        \"\"\"Test querying users by skill\"\"\"\n        skill = Skill(\n            name='Kubernetes',\n            category='DevOps',\n            description='Kubernetes skills'\n        )\n        db.session.add(skill)\n        db.session.commit()\n        \n        user_skill = UserSkill(\n            user_id=self.user.id,\n            skill_id=skill.id,\n            proficiency_level=2\n        )\n        \n        db.session.add(user_skill)\n        db.session.commit()\n        \n        # Query users by skill\n        skill_users = UserSkill.query.filter_by(skill_id=skill.id).all()\n        assert len(skill_users) >= 1\n        assert user_skill in skill_users\n\n    def test_userskill_query_by_proficiency(self):\n        \"\"\"Test querying by proficiency level\"\"\"\n        skill = Skill(\n            name='Data Analysis',\n            category='Analytics',\n            description='Data analysis skills'\n        )\n        db.session.add(skill)\n        db.session.commit()\n        \n        user_skill = UserSkill(\n            user_id=self.user.id,\n            skill_id=skill.id,\n            proficiency_level=4\n        )\n        \n        db.session.add(user_skill)\n        db.session.commit()\n        \n        # Query by proficiency level\n        expert_skills = UserSkill.query.filter(UserSkill.proficiency_level >= 4).all()\n        assert len(expert_skills) >= 1\n        assert user_skill in expert_skills\n\n    def test_userskill_update_operations(self):\n        \"\"\"Test skill update operations\"\"\"\n        skill = Skill(\n            name='Node.js Development',\n            category='Backend',\n            description='Node.js skills'\n        )\n        db.session.add(skill)\n        db.session.commit()\n        \n        user_skill = UserSkill(\n            user_id=self.user.id,\n            skill_id=skill.id,\n            proficiency_level=2,\n            years_experience=1.0,\n            certified=False\n        )\n        \n        db.session.add(user_skill)\n        db.session.commit()\n        \n        # Update skill\n        user_skill.proficiency_level = 4\n        user_skill.years_experience = 3.0\n        user_skill.certified = True\n        user_skill.certification_date = date(2024, 12, 1)\n        user_skill.certification_name = 'Node.js Professional'\n        \n        db.session.commit()\n        \n        updated_skill = UserSkill.query.get(user_skill.id)\n        assert updated_skill.proficiency_level == 4\n        assert updated_skill.years_experience == 3.0\n        assert updated_skill.certified is True\n        assert updated_skill.certification_date == date(2024, 12, 1)\n\n    def test_userskill_deletion(self):\n        \"\"\"Test skill deletion\"\"\"\n        skill = Skill(\n            name='GraphQL',\n            category='API',\n            description='GraphQL skills'\n        )\n        db.session.add(skill)\n        db.session.commit()\n        \n        user_skill = UserSkill(\n            user_id=self.user.id,\n            skill_id=skill.id,\n            proficiency_level=3\n        )\n        \n        db.session.add(user_skill)\n        db.session.commit()\n        skill_id = user_skill.id\n        \n        db.session.delete(user_skill)\n        db.session.commit()\n        \n        deleted_skill = UserSkill.query.get(skill_id)\n        assert deleted_skill is None\n\n    def test_userskill_certification_name_length(self):\n        \"\"\"Test certification name field length\"\"\"\n        skill = Skill(\n            name='Cybersecurity',\n            category='Security',\n            description='Security skills'\n        )\n        db.session.add(skill)\n        db.session.commit()\n        \n        long_cert_name = 'Very Long Certification Name That Tests The Maximum Field Length Constraint'\n        \n        user_skill = UserSkill(\n            user_id=self.user.id,\n            skill_id=skill.id,\n            proficiency_level=3,\n            certified=True,\n            certification_name=long_cert_name\n        )\n        \n        db.session.add(user_skill)\n        db.session.commit()\n        \n        assert user_skill.certification_name == long_cert_name\n        assert len(user_skill.certification_name) <= 255  # VARCHAR(255) constraint\n", "modifiedCode": "\"\"\"\nUnit tests for UserSkill model.\nTests business logic, validations, and model functionality.\n\"\"\"\n\nimport pytest\nfrom datetime import datetime, date\nfrom models import User<PERSON>kill, User, Skill\nfrom extensions import db\n\n\nclass TestUserSkillModel:\n    \"\"\"Test suite for UserSkill model unit tests\"\"\"\n\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        \"\"\"Setup test data for each test method\"\"\"\n        self.app = app\n        self.user = test_user\n\n    def test_userskill_creation_basic(self):\n        \"\"\"Test basic user skill creation\"\"\"\n        # Create test skill\n        skill = Skill(\n            name='Python Programming',\n            category='Programming',\n            description='Python development skills'\n        )\n        db.session.add(skill)\n        db.session.commit()\n        \n        user_skill = UserSkill(\n            user_id=self.user.id,\n            skill_id=skill.id,\n            proficiency_level=3,\n            years_experience=2.5\n        )\n        \n        db.session.add(user_skill)\n        db.session.commit()\n        \n        assert user_skill.id is not None\n        assert user_skill.user_id == self.user.id\n        assert user_skill.skill_id == skill.id\n        assert user_skill.proficiency_level == 3\n        assert user_skill.years_experience == 2.5\n\n    def test_userskill_creation_complete(self):\n        \"\"\"Test user skill creation with all fields\"\"\"\n        skill = Skill(\n            name='JavaScript Development',\n            category='Programming',\n            description='JavaScript skills'\n        )\n        db.session.add(skill)\n        db.session.commit()\n        \n        user_skill = UserSkill(\n            user_id=self.user.id,\n            skill_id=skill.id,\n            proficiency_level=4,\n            years_experience=3.0,\n            last_used=date(2024, 12, 1),\n            certified=True,\n            certification_date=date(2024, 6, 15),\n            certification_name='JavaScript Professional Certificate'\n        )\n        \n        db.session.add(user_skill)\n        db.session.commit()\n        \n        assert user_skill.proficiency_level == 4\n        assert user_skill.years_experience == 3.0\n        assert user_skill.last_used == date(2024, 12, 1)\n        assert user_skill.certified is True\n        assert user_skill.certification_date == date(2024, 6, 15)\n        assert user_skill.certification_name == 'JavaScript Professional Certificate'\n\n    def test_userskill_relationships(self):\n        \"\"\"Test relationships with User and Skill models\"\"\"\n        skill = Skill(\n            name='Database Design',\n            category='Database',\n            description='Database design skills'\n        )\n        db.session.add(skill)\n        db.session.commit()\n        \n        user_skill = UserSkill(\n            user_id=self.user.id,\n            skill_id=skill.id,\n            proficiency_level=2\n        )\n        \n        db.session.add(user_skill)\n        db.session.commit()\n        \n        # Test forward relationships\n        assert user_skill.user is not None\n        assert user_skill.user.id == self.user.id\n        assert user_skill.skill is not None\n        assert user_skill.skill.id == skill.id\n\n    def test_userskill_proficiency_levels(self):\n        \"\"\"Test different proficiency levels\"\"\"\n        skill = Skill(\n            name='Project Management',\n            category='Management',\n            description='Project management skills'\n        )\n        db.session.add(skill)\n        db.session.commit()\n        \n        proficiency_levels = [1, 2, 3, 4, 5]  # Beginner to Expert\n\n        # Create different skills for each proficiency level\n        skills = []\n        for i, level in enumerate(proficiency_levels):\n            skill_unique = Skill(\n                name=f'Project Management Level {level}',\n                category='Management',\n                description=f'Project management skills level {level}'\n            )\n            skills.append(skill_unique)\n\n        db.session.add_all(skills)\n        db.session.commit()\n\n        user_skills = []\n        for level, skill_unique in zip(proficiency_levels, skills):\n            user_skill = UserSkill(\n                user_id=self.user.id,\n                skill_id=skill_unique.id,\n                proficiency_level=level,\n                years_experience=level * 0.5  # Different experience for each\n            )\n            user_skills.append(user_skill)\n        \n        db.session.add_all(user_skills)\n        db.session.commit()\n        \n        for user_skill, expected_level in zip(user_skills, proficiency_levels):\n            assert user_skill.proficiency_level == expected_level\n            assert 1 <= user_skill.proficiency_level <= 5\n\n    def test_userskill_years_experience(self):\n        \"\"\"Test years of experience field\"\"\"\n        experience_values = [0.5, 1.0, 2.5, 5.0, 10.0]\n\n        # Create different skills for each experience level\n        skills = []\n        for i, experience in enumerate(experience_values):\n            skill_unique = Skill(\n                name=f'Machine Learning Experience {i}',\n                category='AI',\n                description=f'ML skills {i}'\n            )\n            skills.append(skill_unique)\n\n        db.session.add_all(skills)\n        db.session.commit()\n\n        user_skills = []\n        for experience, skill_unique in zip(experience_values, skills):\n            user_skill = UserSkill(\n                user_id=self.user.id,\n                skill_id=skill_unique.id,\n                proficiency_level=2,\n                years_experience=experience\n            )\n            user_skills.append(user_skill)\n        \n        db.session.add_all(user_skills)\n        db.session.commit()\n        \n        for user_skill, expected_experience in zip(user_skills, experience_values):\n            assert user_skill.years_experience == expected_experience\n            assert user_skill.years_experience >= 0\n\n    def test_userskill_certification_functionality(self):\n        \"\"\"Test certification-related fields\"\"\"\n        skill = Skill(\n            name='AWS Cloud',\n            category='Cloud',\n            description='AWS cloud skills'\n        )\n        db.session.add(skill)\n        db.session.commit()\n        \n        # Certified skill\n        certified_skill = UserSkill(\n            user_id=self.user.id,\n            skill_id=skill.id,\n            proficiency_level=4,\n            certified=True,\n            certification_date=date(2024, 3, 15),\n            certification_name='AWS Solutions Architect'\n        )\n        \n        # Non-certified skill\n        non_certified_skill = UserSkill(\n            user_id=self.user.id,\n            skill_id=skill.id,\n            proficiency_level=3,\n            certified=False\n        )\n        \n        db.session.add_all([certified_skill, non_certified_skill])\n        db.session.commit()\n        \n        assert certified_skill.certified is True\n        assert certified_skill.certification_date == date(2024, 3, 15)\n        assert certified_skill.certification_name == 'AWS Solutions Architect'\n        \n        assert non_certified_skill.certified is False\n        assert non_certified_skill.certification_date is None\n        assert non_certified_skill.certification_name is None\n\n    def test_userskill_last_used_tracking(self):\n        \"\"\"Test last used date tracking\"\"\"\n        skill = Skill(\n            name='React Development',\n            category='Frontend',\n            description='React skills'\n        )\n        db.session.add(skill)\n        db.session.commit()\n        \n        user_skill = UserSkill(\n            user_id=self.user.id,\n            skill_id=skill.id,\n            proficiency_level=3,\n            last_used=date(2024, 11, 30)\n        )\n        \n        db.session.add(user_skill)\n        db.session.commit()\n        \n        assert user_skill.last_used == date(2024, 11, 30)\n        \n        # Update last used date\n        user_skill.last_used = date(2024, 12, 15)\n        db.session.commit()\n        \n        assert user_skill.last_used == date(2024, 12, 15)\n\n    def test_userskill_query_by_user(self):\n        \"\"\"Test querying skills by user\"\"\"\n        skill = Skill(\n            name='Docker Containers',\n            category='DevOps',\n            description='Docker skills'\n        )\n        db.session.add(skill)\n        db.session.commit()\n        \n        user_skill = UserSkill(\n            user_id=self.user.id,\n            skill_id=skill.id,\n            proficiency_level=3\n        )\n        \n        db.session.add(user_skill)\n        db.session.commit()\n        \n        # Query skills by user\n        user_skills = UserSkill.query.filter_by(user_id=self.user.id).all()\n        assert len(user_skills) >= 1\n        assert user_skill in user_skills\n\n    def test_userskill_query_by_skill(self):\n        \"\"\"Test querying users by skill\"\"\"\n        skill = Skill(\n            name='Kubernetes',\n            category='DevOps',\n            description='Kubernetes skills'\n        )\n        db.session.add(skill)\n        db.session.commit()\n        \n        user_skill = UserSkill(\n            user_id=self.user.id,\n            skill_id=skill.id,\n            proficiency_level=2\n        )\n        \n        db.session.add(user_skill)\n        db.session.commit()\n        \n        # Query users by skill\n        skill_users = UserSkill.query.filter_by(skill_id=skill.id).all()\n        assert len(skill_users) >= 1\n        assert user_skill in skill_users\n\n    def test_userskill_query_by_proficiency(self):\n        \"\"\"Test querying by proficiency level\"\"\"\n        skill = Skill(\n            name='Data Analysis',\n            category='Analytics',\n            description='Data analysis skills'\n        )\n        db.session.add(skill)\n        db.session.commit()\n        \n        user_skill = UserSkill(\n            user_id=self.user.id,\n            skill_id=skill.id,\n            proficiency_level=4\n        )\n        \n        db.session.add(user_skill)\n        db.session.commit()\n        \n        # Query by proficiency level\n        expert_skills = UserSkill.query.filter(UserSkill.proficiency_level >= 4).all()\n        assert len(expert_skills) >= 1\n        assert user_skill in expert_skills\n\n    def test_userskill_update_operations(self):\n        \"\"\"Test skill update operations\"\"\"\n        skill = Skill(\n            name='Node.js Development',\n            category='Backend',\n            description='Node.js skills'\n        )\n        db.session.add(skill)\n        db.session.commit()\n        \n        user_skill = UserSkill(\n            user_id=self.user.id,\n            skill_id=skill.id,\n            proficiency_level=2,\n            years_experience=1.0,\n            certified=False\n        )\n        \n        db.session.add(user_skill)\n        db.session.commit()\n        \n        # Update skill\n        user_skill.proficiency_level = 4\n        user_skill.years_experience = 3.0\n        user_skill.certified = True\n        user_skill.certification_date = date(2024, 12, 1)\n        user_skill.certification_name = 'Node.js Professional'\n        \n        db.session.commit()\n        \n        updated_skill = UserSkill.query.get(user_skill.id)\n        assert updated_skill.proficiency_level == 4\n        assert updated_skill.years_experience == 3.0\n        assert updated_skill.certified is True\n        assert updated_skill.certification_date == date(2024, 12, 1)\n\n    def test_userskill_deletion(self):\n        \"\"\"Test skill deletion\"\"\"\n        skill = Skill(\n            name='GraphQL',\n            category='API',\n            description='GraphQL skills'\n        )\n        db.session.add(skill)\n        db.session.commit()\n        \n        user_skill = UserSkill(\n            user_id=self.user.id,\n            skill_id=skill.id,\n            proficiency_level=3\n        )\n        \n        db.session.add(user_skill)\n        db.session.commit()\n        skill_id = user_skill.id\n        \n        db.session.delete(user_skill)\n        db.session.commit()\n        \n        deleted_skill = UserSkill.query.get(skill_id)\n        assert deleted_skill is None\n\n    def test_userskill_certification_name_length(self):\n        \"\"\"Test certification name field length\"\"\"\n        skill = Skill(\n            name='Cybersecurity',\n            category='Security',\n            description='Security skills'\n        )\n        db.session.add(skill)\n        db.session.commit()\n        \n        long_cert_name = 'Very Long Certification Name That Tests The Maximum Field Length Constraint'\n        \n        user_skill = UserSkill(\n            user_id=self.user.id,\n            skill_id=skill.id,\n            proficiency_level=3,\n            certified=True,\n            certification_name=long_cert_name\n        )\n        \n        db.session.add(user_skill)\n        db.session.commit()\n        \n        assert user_skill.certification_name == long_cert_name\n        assert len(user_skill.certification_name) <= 255  # VARCHAR(255) constraint\n"}