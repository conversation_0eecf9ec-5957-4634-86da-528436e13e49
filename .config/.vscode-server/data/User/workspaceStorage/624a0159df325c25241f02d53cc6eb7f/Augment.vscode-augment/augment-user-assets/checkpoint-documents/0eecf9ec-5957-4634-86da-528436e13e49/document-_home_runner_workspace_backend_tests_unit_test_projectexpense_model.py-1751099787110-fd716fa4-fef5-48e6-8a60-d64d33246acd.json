{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_projectexpense_model.py"}, "modifiedCode": "\"\"\"Unit tests for ProjectExpense model.\"\"\"\nimport pytest\nfrom datetime import date\nfrom models import ProjectExpense, Project, User\nfrom extensions import db\n\nclass TestProjectExpenseModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n        \n        self.project = Project.query.first()\n        if not self.project:\n            self.project = Project(name='Test Project', description='Test')\n            db.session.add(self.project)\n            db.session.commit()\n\n    def test_projectexpense_creation_basic(self):\n        expense = ProjectExpense(\n            project_id=self.project.id,\n            user_id=self.user.id,\n            category='Travel',\n            description='Business trip',\n            amount=250.0,\n            date=date.today()\n        )\n        db.session.add(expense)\n        db.session.commit()\n        \n        assert expense.id is not None\n        assert expense.project_id == self.project.id\n        assert expense.user_id == self.user.id\n        assert expense.category == 'Travel'\n        assert expense.amount == 250.0\n\n    def test_projectexpense_complete(self):\n        expense = ProjectExpense(\n            project_id=self.project.id,\n            user_id=self.user.id,\n            category='Equipment',\n            description='Laptop purchase',\n            amount=1500.0,\n            billing_type='billable',\n            date=date.today(),\n            receipt_path='/receipts/laptop.pdf',\n            status='approved'\n        )\n        db.session.add(expense)\n        db.session.commit()\n        \n        assert expense.billing_type == 'billable'\n        assert expense.receipt_path == '/receipts/laptop.pdf'\n        assert expense.status == 'approved'\n\n    def test_projectexpense_categories(self):\n        categories = ['Travel', 'Equipment', 'Software', 'Training']\n        expenses = []\n        \n        for i, category in enumerate(categories):\n            expense = ProjectExpense(\n                project_id=self.project.id,\n                user_id=self.user.id,\n                category=category,\n                description=f'{category} expense',\n                amount=100.0 + i * 50,\n                date=date.today()\n            )\n            expenses.append(expense)\n        \n        db.session.add_all(expenses)\n        db.session.commit()\n        \n        for expense, expected_category in zip(expenses, categories):\n            assert expense.category == expected_category\n\n    def test_projectexpense_status_values(self):\n        statuses = ['pending', 'approved', 'rejected', 'paid']\n        expenses = []\n        \n        for i, status in enumerate(statuses):\n            expense = ProjectExpense(\n                project_id=self.project.id,\n                user_id=self.user.id,\n                category='Test',\n                description=f'Status test {i}',\n                amount=100.0,\n                date=date.today(),\n                status=status\n            )\n            expenses.append(expense)\n        \n        db.session.add_all(expenses)\n        db.session.commit()\n        \n        for expense, expected_status in zip(expenses, statuses):\n            assert expense.status == expected_status\n\n    def test_projectexpense_update(self):\n        expense = ProjectExpense(\n            project_id=self.project.id,\n            user_id=self.user.id,\n            category='Travel',\n            description='Original description',\n            amount=200.0,\n            date=date.today(),\n            status='pending'\n        )\n        db.session.add(expense)\n        db.session.commit()\n        \n        expense.amount = 300.0\n        expense.status = 'approved'\n        db.session.commit()\n        \n        updated = ProjectExpense.query.get(expense.id)\n        assert updated.amount == 300.0\n        assert updated.status == 'approved'\n\n    def test_projectexpense_deletion(self):\n        expense = ProjectExpense(\n            project_id=self.project.id,\n            user_id=self.user.id,\n            category='Test',\n            description='To be deleted',\n            amount=100.0,\n            date=date.today()\n        )\n        db.session.add(expense)\n        db.session.commit()\n        expense_id = expense.id\n        \n        db.session.delete(expense)\n        db.session.commit()\n        \n        deleted = ProjectExpense.query.get(expense_id)\n        assert deleted is None\n"}