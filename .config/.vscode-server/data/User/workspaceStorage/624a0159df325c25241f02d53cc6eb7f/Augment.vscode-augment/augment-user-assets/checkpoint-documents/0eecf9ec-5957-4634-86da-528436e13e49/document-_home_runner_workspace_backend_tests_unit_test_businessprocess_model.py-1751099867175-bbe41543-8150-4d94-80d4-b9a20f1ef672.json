{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_businessprocess_model.py"}, "modifiedCode": "\"\"\"Unit tests for BusinessProcess model.\"\"\"\nimport pytest\nfrom models import BusinessProcess, User\nfrom extensions import db\n\nclass TestBusinessProcessModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n\n    def test_businessprocess_creation_basic(self):\n        process = BusinessProcess(\n            name='Employee Onboarding',\n            description='Process for onboarding new employees'\n        )\n        db.session.add(process)\n        db.session.commit()\n        \n        assert process.id is not None\n        assert process.name == 'Employee Onboarding'\n        assert process.description == 'Process for onboarding new employees'\n\n    def test_businessprocess_creation_complete(self):\n        process = BusinessProcess(\n            name='Invoice Processing',\n            description='Complete invoice processing workflow',\n            owner_id=self.user.id,\n            status='active'\n        )\n        db.session.add(process)\n        db.session.commit()\n        \n        assert process.owner_id == self.user.id\n        assert process.status == 'active'\n\n    def test_businessprocess_status_values(self):\n        statuses = ['draft', 'active', 'inactive', 'archived']\n        processes = []\n        \n        for i, status in enumerate(statuses):\n            process = BusinessProcess(\n                name=f'Process {i}',\n                status=status\n            )\n            processes.append(process)\n        \n        db.session.add_all(processes)\n        db.session.commit()\n        \n        for process, expected_status in zip(processes, statuses):\n            assert process.status == expected_status\n\n    def test_businessprocess_update(self):\n        process = BusinessProcess(\n            name='Original Process',\n            description='Original description',\n            status='draft'\n        )\n        db.session.add(process)\n        db.session.commit()\n        \n        process.name = 'Updated Process'\n        process.status = 'active'\n        db.session.commit()\n        \n        updated = BusinessProcess.query.get(process.id)\n        assert updated.name == 'Updated Process'\n        assert updated.status == 'active'\n\n    def test_businessprocess_deletion(self):\n        process = BusinessProcess(\n            name='To Be Deleted',\n            description='This will be deleted'\n        )\n        db.session.add(process)\n        db.session.commit()\n        process_id = process.id\n        \n        db.session.delete(process)\n        db.session.commit()\n        \n        deleted = BusinessProcess.query.get(process_id)\n        assert deleted is None\n"}