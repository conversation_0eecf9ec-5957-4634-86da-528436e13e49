{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_service_model.py"}, "modifiedCode": "\"\"\"\nUnit tests for Service model.\nTests business logic, validations, and model functionality.\n\"\"\"\n\nimport pytest\nfrom datetime import datetime\nfrom models import Service\nfrom extensions import db\n\n\nclass TestServiceModel:\n    \"\"\"Test suite for Service model unit tests\"\"\"\n\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app):\n        \"\"\"Setup test data for each test method\"\"\"\n        self.app = app\n\n    def test_service_creation_basic(self):\n        \"\"\"Test basic service creation\"\"\"\n        service = Service(\n            name='Web Development',\n            description='Full-stack web development services',\n            category='Development'\n        )\n        \n        db.session.add(service)\n        db.session.commit()\n        \n        assert service.id is not None\n        assert service.name == 'Web Development'\n        assert service.description == 'Full-stack web development services'\n        assert service.category == 'Development'\n\n    def test_service_creation_complete(self):\n        \"\"\"Test service creation with all fields\"\"\"\n        service = Service(\n            name='Consulting',\n            description='Technical consulting services',\n            category='Consulting',\n            hourly_rate=150.0,\n            status='active'\n        )\n        \n        db.session.add(service)\n        db.session.commit()\n        \n        assert service.name == 'Consulting'\n        assert service.hourly_rate == 150.0\n        assert service.status == 'active'\n\n    def test_service_categories(self):\n        \"\"\"Test different service categories\"\"\"\n        categories = ['Development', 'Design', 'Consulting', 'Support', 'Training']\n        \n        services = []\n        for i, category in enumerate(categories):\n            service = Service(\n                name=f'{category} Service {i}',\n                category=category,\n                hourly_rate=100.0 + i * 25\n            )\n            services.append(service)\n        \n        db.session.add_all(services)\n        db.session.commit()\n        \n        for service, expected_category in zip(services, categories):\n            assert service.category == expected_category\n\n    def test_service_hourly_rates(self):\n        \"\"\"Test hourly rate functionality\"\"\"\n        rates = [50.0, 75.0, 100.0, 125.0, 150.0]\n        \n        services = []\n        for i, rate in enumerate(rates):\n            service = Service(\n                name=f'Service Rate {i}',\n                hourly_rate=rate,\n                category='Testing'\n            )\n            services.append(service)\n        \n        db.session.add_all(services)\n        db.session.commit()\n        \n        for service, expected_rate in zip(services, rates):\n            assert service.hourly_rate == expected_rate\n\n    def test_service_status_values(self):\n        \"\"\"Test service status field\"\"\"\n        statuses = ['active', 'inactive', 'draft', 'archived']\n        \n        services = []\n        for i, status in enumerate(statuses):\n            service = Service(\n                name=f'Service Status {i}',\n                status=status,\n                category='Testing'\n            )\n            services.append(service)\n        \n        db.session.add_all(services)\n        db.session.commit()\n        \n        for service, expected_status in zip(services, statuses):\n            assert service.status == expected_status\n\n    def test_service_timestamps(self):\n        \"\"\"Test automatic timestamp handling\"\"\"\n        service = Service(\n            name='Timestamp Test Service',\n            category='Testing'\n        )\n        \n        db.session.add(service)\n        db.session.commit()\n        \n        assert service.created_at is not None\n        assert service.updated_at is not None\n        assert isinstance(service.created_at, datetime)\n        assert isinstance(service.updated_at, datetime)\n\n    def test_service_query_by_category(self):\n        \"\"\"Test querying services by category\"\"\"\n        service = Service(\n            name='Category Test Service',\n            category='Special Category'\n        )\n        \n        db.session.add(service)\n        db.session.commit()\n        \n        category_services = Service.query.filter_by(category='Special Category').all()\n        assert len(category_services) >= 1\n        assert service in category_services\n\n    def test_service_query_by_status(self):\n        \"\"\"Test querying services by status\"\"\"\n        service = Service(\n            name='Status Test Service',\n            status='testing',\n            category='Testing'\n        )\n        \n        db.session.add(service)\n        db.session.commit()\n        \n        status_services = Service.query.filter_by(status='testing').all()\n        assert len(status_services) >= 1\n        assert service in status_services\n\n    def test_service_update_operations(self):\n        \"\"\"Test service update operations\"\"\"\n        service = Service(\n            name='Original Service',\n            category='Original',\n            hourly_rate=100.0,\n            status='draft'\n        )\n        \n        db.session.add(service)\n        db.session.commit()\n        \n        # Update service\n        service.name = 'Updated Service'\n        service.hourly_rate = 150.0\n        service.status = 'active'\n        \n        db.session.commit()\n        \n        updated_service = Service.query.get(service.id)\n        assert updated_service.name == 'Updated Service'\n        assert updated_service.hourly_rate == 150.0\n        assert updated_service.status == 'active'\n\n    def test_service_deletion(self):\n        \"\"\"Test service deletion\"\"\"\n        service = Service(\n            name='To Be Deleted',\n            category='Testing'\n        )\n        \n        db.session.add(service)\n        db.session.commit()\n        service_id = service.id\n        \n        db.session.delete(service)\n        db.session.commit()\n        \n        deleted_service = Service.query.get(service_id)\n        assert deleted_service is None\n\n    def test_service_name_length(self):\n        \"\"\"Test service name field length\"\"\"\n        long_name = 'Very Long Service Name That Tests The Maximum Field Length Constraint For Service Names'\n        \n        service = Service(\n            name=long_name[:128],  # Truncate to VARCHAR(128) limit\n            category='Testing'\n        )\n        \n        db.session.add(service)\n        db.session.commit()\n        \n        assert len(service.name) <= 128\n        assert service.name == long_name[:128]\n"}