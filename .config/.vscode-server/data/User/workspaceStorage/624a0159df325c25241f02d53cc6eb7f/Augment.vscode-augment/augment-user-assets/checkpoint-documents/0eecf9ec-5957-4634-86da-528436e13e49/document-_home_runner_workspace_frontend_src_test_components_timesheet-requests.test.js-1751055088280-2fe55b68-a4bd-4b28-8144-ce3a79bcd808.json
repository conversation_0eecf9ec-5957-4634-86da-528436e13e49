{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/timesheet-requests.test.js"}, "originalCode": "import { describe, it, expect, beforeEach, vi } from 'vitest'\nimport { mountComponent, mockApiResponse } from '../utils/test-helpers.js'\nimport TimesheetRequests from '@/views/timesheet/TimesheetRequests.vue'\nimport { useTimesheetStore } from '@/stores/timesheet'\nimport { useAuthStore } from '@/stores/auth'\n\n// Mock stores\nvi.mock('@/stores/timesheet', () => ({\n  useTimesheetStore: vi.fn()\n}))\n\nvi.mock('@/stores/auth', () => ({\n  useAuthStore: vi.fn()\n}))\n\n// Mock API\nglobal.fetch = vi.fn()\n\ndescribe('Timesheet Requests Components', () => {\n  let mockTimesheetStore\n  let mockAuthStore\n\n  beforeEach(() => {\n    vi.clearAllMocks()\n    \n    // Setup mock timesheet store\n    mockTimesheetStore = {\n      loadTimeOffRequests: vi.fn().mockResolvedValue([]),\n      createTimeOffRequest: vi.fn().mockResolvedValue(true),\n      deleteTimeOffRequest: vi.fn().mockResolvedValue(true),\n      loadTimeOffQuotas: vi.fn().mockResolvedValue({\n        vacation: { remaining: 20, total: 26 },\n        leave: { used: 2, total: 10 },\n        smartworking: { used: 5 }\n      }),\n      setError: vi.fn(),\n      error: null\n    }\n    \n    // Setup mock auth store\n    mockAuthStore = {\n      user: { id: 1, first_name: 'Test', last_name: 'User' },\n      isAuthenticated: true,\n      csrfToken: 'test-csrf-token'\n    }\n    \n    useTimesheetStore.mockReturnValue(mockTimesheetStore)\n    useAuthStore.mockReturnValue(mockAuthStore)\n    \n    // Reset fetch mock\n    fetch.mockClear()\n  })\n\n  describe('TimesheetRequests Component', () => {\n    it('should render timesheet requests page', async () => {\n      const wrapper = mountComponent(TimesheetRequests)\n      \n      expect(wrapper.text()).toContain('Richieste Timesheet')\n      expect(wrapper.find('[data-testid=\"vacation-button\"]')).toBeTruthy()\n      expect(wrapper.find('[data-testid=\"leave-button\"]')).toBeTruthy()\n      expect(wrapper.find('[data-testid=\"smartworking-button\"]')).toBeTruthy()\n    })\n\n    it('should load requests on mount', async () => {\n      const mockRequests = [\n        {\n          id: 1,\n          type: 'vacation',  // Campo DB: 'type'\n          start_date: '2025-07-01',\n          end_date: '2025-07-05',\n          status: 'pending',\n          duration_days: 5,\n          notes: 'Summer vacation',\n          created_at: '2025-06-01T10:00:00Z'\n        }\n      ]\n      \n      mockTimesheetStore.loadTimeOffRequests.mockResolvedValue(mockRequests)\n      \n      const wrapper = mountComponent(TimesheetRequests)\n      await wrapper.vm.$nextTick()\n      \n      expect(mockTimesheetStore.loadTimeOffRequests).toHaveBeenCalled()\n      expect(mockTimesheetStore.loadTimeOffQuotas).toHaveBeenCalled()\n    })\n\n    it('should create vacation request', async () => {\n      const wrapper = mountComponent(TimesheetRequests)\n      \n      // Click vacation button\n      await wrapper.find('[data-testid=\"vacation-button\"]').trigger('click')\n      \n      // Should show modal\n      expect(wrapper.find('[data-testid=\"request-modal\"]').exists()).toBe(true)\n      \n      // Fill form\n      await wrapper.find('[data-testid=\"start-date\"]').setValue('2025-07-01')\n      await wrapper.find('[data-testid=\"end-date\"]').setValue('2025-07-05')\n      await wrapper.find('[data-testid=\"notes\"]').setValue('Summer vacation')\n      \n      // Submit form\n      await wrapper.find('[data-testid=\"submit-request\"]').trigger('click')\n      \n      expect(mockTimesheetStore.createTimeOffRequest).toHaveBeenCalledWith({\n        type: 'vacation',  // Campo DB: 'type'\n        start_date: '2025-07-01',\n        end_date: '2025-07-05',\n        notes: 'Summer vacation'\n      })\n    })\n\n    it('should handle request creation error', async () => {\n      mockTimesheetStore.createTimeOffRequest.mockResolvedValue(false)\n      mockTimesheetStore.error = 'Errore nella creazione della richiesta'\n      \n      const wrapper = mountComponent(TimesheetRequests)\n      \n      // Click vacation button and submit\n      await wrapper.find('[data-testid=\"vacation-button\"]').trigger('click')\n      await wrapper.find('[data-testid=\"start-date\"]').setValue('2025-07-01')\n      await wrapper.find('[data-testid=\"end-date\"]').setValue('2025-07-05')\n      await wrapper.find('[data-testid=\"submit-request\"]').trigger('click')\n      \n      await wrapper.vm.$nextTick()\n      \n      // Should show error\n      expect(wrapper.text()).toContain('Errore nella creazione della richiesta')\n    })\n\n    it('should delete pending request', async () => {\n      const mockRequests = [\n        {\n          id: 1,\n          request_type: 'vacation',\n          start_date: '2025-07-01',\n          end_date: '2025-07-05',\n          status: 'pending',\n          duration_days: 5\n        }\n      ]\n      \n      mockTimesheetStore.loadTimeOffRequests.mockResolvedValue(mockRequests)\n      \n      const wrapper = mountComponent(TimesheetRequests)\n      await wrapper.vm.$nextTick()\n      \n      // Mock confirm dialog\n      window.confirm = vi.fn().mockReturnValue(true)\n      \n      // Click delete button\n      await wrapper.find('[data-testid=\"delete-request-1\"]').trigger('click')\n      \n      expect(mockTimesheetStore.deleteTimeOffRequest).toHaveBeenCalledWith(1)\n    })\n  })\n\n  describe('Timesheet Store Integration', () => {\n    it('should call correct API endpoint for creating request', async () => {\n      fetch.mockResolvedValueOnce(mockApiResponse({\n        success: true,\n        data: { id: 1, request_type: 'vacation' }\n      }))\n\n      const store = useTimesheetStore()\n      \n      const result = await store.createTimeOffRequest({\n        type: 'vacation',  // Campo DB: 'type'\n        start_date: '2025-07-01',\n        end_date: '2025-07-05',\n        notes: 'Test'\n      })\n\n      expect(fetch).toHaveBeenCalledWith('/api/time-off-requests/', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'X-CSRFToken': 'test-csrf-token'\n        },\n        body: JSON.stringify({\n          type: 'vacation',  // Campo DB: 'type'\n          start_date: '2025-07-01',\n          end_date: '2025-07-05',\n          notes: 'Test'\n        })\n      })\n      \n      expect(result).toBe(true)\n    })\n\n    it('should handle API error correctly', async () => {\n      fetch.mockResolvedValueOnce({\n        ok: false,\n        status: 400,\n        json: () => Promise.resolve({\n          success: false,\n          message: 'Campo request_type richiesto'\n        })\n      })\n\n      const store = useTimesheetStore()\n      \n      const result = await store.createTimeOffRequest({\n        start_date: '2025-07-01',\n        end_date: '2025-07-05'\n        // Missing request_type\n      })\n\n      expect(result).toBe(false)\n      expect(store.error).toContain('Campo request_type richiesto')\n    })\n  })\n})\n", "modifiedCode": "import { describe, it, expect, beforeEach, vi } from 'vitest'\nimport { mountComponent, mockApiResponse } from '../utils/test-helpers.js'\nimport TimesheetRequests from '@/views/timesheet/TimesheetRequests.vue'\nimport { useTimesheetStore } from '@/stores/timesheet'\nimport { useAuthStore } from '@/stores/auth'\n\n// Mock stores\nvi.mock('@/stores/timesheet', () => ({\n  useTimesheetStore: vi.fn()\n}))\n\nvi.mock('@/stores/auth', () => ({\n  useAuthStore: vi.fn()\n}))\n\n// Mock API\nglobal.fetch = vi.fn()\n\ndescribe('Timesheet Requests Components', () => {\n  let mockTimesheetStore\n  let mockAuthStore\n\n  beforeEach(() => {\n    vi.clearAllMocks()\n    \n    // Setup mock timesheet store\n    mockTimesheetStore = {\n      loadTimeOffRequests: vi.fn().mockResolvedValue([]),\n      createTimeOffRequest: vi.fn().mockResolvedValue(true),\n      deleteTimeOffRequest: vi.fn().mockResolvedValue(true),\n      loadTimeOffQuotas: vi.fn().mockResolvedValue({\n        vacation: { remaining: 20, total: 26 },\n        leave: { used: 2, total: 10 },\n        smartworking: { used: 5 }\n      }),\n      setError: vi.fn(),\n      error: null\n    }\n    \n    // Setup mock auth store\n    mockAuthStore = {\n      user: { id: 1, first_name: 'Test', last_name: 'User' },\n      isAuthenticated: true,\n      csrfToken: 'test-csrf-token'\n    }\n    \n    useTimesheetStore.mockReturnValue(mockTimesheetStore)\n    useAuthStore.mockReturnValue(mockAuthStore)\n    \n    // Reset fetch mock\n    fetch.mockClear()\n  })\n\n  describe('TimesheetRequests Component', () => {\n    it('should render timesheet requests page', async () => {\n      const wrapper = mountComponent(TimesheetRequests)\n      \n      expect(wrapper.text()).toContain('Richieste Timesheet')\n      expect(wrapper.find('[data-testid=\"vacation-button\"]')).toBeTruthy()\n      expect(wrapper.find('[data-testid=\"leave-button\"]')).toBeTruthy()\n      expect(wrapper.find('[data-testid=\"smartworking-button\"]')).toBeTruthy()\n    })\n\n    it('should load requests on mount', async () => {\n      const mockRequests = [\n        {\n          id: 1,\n          type: 'vacation',  // Campo DB: 'type'\n          start_date: '2025-07-01',\n          end_date: '2025-07-05',\n          status: 'pending',\n          duration_days: 5,\n          notes: 'Summer vacation',\n          created_at: '2025-06-01T10:00:00Z'\n        }\n      ]\n      \n      mockTimesheetStore.loadTimeOffRequests.mockResolvedValue(mockRequests)\n      \n      const wrapper = mountComponent(TimesheetRequests)\n      await wrapper.vm.$nextTick()\n      \n      expect(mockTimesheetStore.loadTimeOffRequests).toHaveBeenCalled()\n      expect(mockTimesheetStore.loadTimeOffQuotas).toHaveBeenCalled()\n    })\n\n    it('should create vacation request', async () => {\n      const wrapper = mountComponent(TimesheetRequests)\n      \n      // Click vacation button\n      await wrapper.find('[data-testid=\"vacation-button\"]').trigger('click')\n      \n      // Should show modal\n      expect(wrapper.find('[data-testid=\"request-modal\"]').exists()).toBe(true)\n      \n      // Fill form\n      await wrapper.find('[data-testid=\"start-date\"]').setValue('2025-07-01')\n      await wrapper.find('[data-testid=\"end-date\"]').setValue('2025-07-05')\n      await wrapper.find('[data-testid=\"notes\"]').setValue('Summer vacation')\n      \n      // Submit form\n      await wrapper.find('[data-testid=\"submit-request\"]').trigger('click')\n      \n      expect(mockTimesheetStore.createTimeOffRequest).toHaveBeenCalledWith({\n        type: 'vacation',  // Campo DB: 'type'\n        start_date: '2025-07-01',\n        end_date: '2025-07-05',\n        notes: 'Summer vacation'\n      })\n    })\n\n    it('should handle request creation error', async () => {\n      mockTimesheetStore.createTimeOffRequest.mockResolvedValue(false)\n      mockTimesheetStore.error = 'Errore nella creazione della richiesta'\n      \n      const wrapper = mountComponent(TimesheetRequests)\n      \n      // Click vacation button and submit\n      await wrapper.find('[data-testid=\"vacation-button\"]').trigger('click')\n      await wrapper.find('[data-testid=\"start-date\"]').setValue('2025-07-01')\n      await wrapper.find('[data-testid=\"end-date\"]').setValue('2025-07-05')\n      await wrapper.find('[data-testid=\"submit-request\"]').trigger('click')\n      \n      await wrapper.vm.$nextTick()\n      \n      // Should show error\n      expect(wrapper.text()).toContain('Errore nella creazione della richiesta')\n    })\n\n    it('should delete pending request', async () => {\n      const mockRequests = [\n        {\n          id: 1,\n          request_type: 'vacation',\n          start_date: '2025-07-01',\n          end_date: '2025-07-05',\n          status: 'pending',\n          duration_days: 5\n        }\n      ]\n      \n      mockTimesheetStore.loadTimeOffRequests.mockResolvedValue(mockRequests)\n      \n      const wrapper = mountComponent(TimesheetRequests)\n      await wrapper.vm.$nextTick()\n      \n      // Mock confirm dialog\n      window.confirm = vi.fn().mockReturnValue(true)\n      \n      // Click delete button\n      await wrapper.find('[data-testid=\"delete-request-1\"]').trigger('click')\n      \n      expect(mockTimesheetStore.deleteTimeOffRequest).toHaveBeenCalledWith(1)\n    })\n  })\n\n  describe('Timesheet Store Integration', () => {\n    it('should call correct API endpoint for creating request', async () => {\n      fetch.mockResolvedValueOnce(mockApiResponse({\n        success: true,\n        data: { id: 1, request_type: 'vacation' }\n      }))\n\n      const store = useTimesheetStore()\n      \n      const result = await store.createTimeOffRequest({\n        type: 'vacation',  // Campo DB: 'type'\n        start_date: '2025-07-01',\n        end_date: '2025-07-05',\n        notes: 'Test'\n      })\n\n      expect(fetch).toHaveBeenCalledWith('/api/time-off-requests/', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'X-CSRFToken': 'test-csrf-token'\n        },\n        body: JSON.stringify({\n          type: 'vacation',  // Campo DB: 'type'\n          start_date: '2025-07-01',\n          end_date: '2025-07-05',\n          notes: 'Test'\n        })\n      })\n      \n      expect(result).toBe(true)\n    })\n\n    it('should handle API error correctly', async () => {\n      fetch.mockResolvedValueOnce({\n        ok: false,\n        status: 400,\n        json: () => Promise.resolve({\n          success: false,\n          message: 'Campo request_type richiesto'\n        })\n      })\n\n      const store = useTimesheetStore()\n      \n      const result = await store.createTimeOffRequest({\n        start_date: '2025-07-01',\n        end_date: '2025-07-05'\n        // Missing request_type\n      })\n\n      expect(result).toBe(false)\n      expect(store.error).toContain('Campo request_type richiesto')\n    })\n  })\n})\n"}