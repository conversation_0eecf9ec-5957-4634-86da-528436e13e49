{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/testing/test-gap-analysis.md"}, "modifiedCode": "# Test Coverage Gap Analysis\n\n## 🔍 Current State (26 June 2025)\n\n### Backend Analysis\n- **Test Files**: 34 files\n- **API Endpoints**: 36 files  \n- **Models**: 37+ models across 11 modules\n- **Status**: Tests exist but have import/dependency issues\n\n### Frontend Analysis  \n- **Vue Components**: 131 files\n- **Test Files**: 4 files (basic setup only)\n- **Coverage**: ~3% (CRITICAL)\n- **Status**: Virtually no component testing\n\n## 🚨 Critical Issues Found\n\n### Backend Issues\n1. **Import Errors**: `Timesheet` → `TimesheetEntry` model name changes\n2. **Service Dependencies**: Services initializing during import before DB creation\n3. **Foreign Key Issues**: Table dependency resolution problems\n4. **Database State**: Tests failing due to missing tables\n\n### Frontend Issues  \n1. **No Component Tests**: 131 components, 0 tests\n2. **No Store Tests**: Pinia stores untested\n3. **No Integration Tests**: API-Frontend integration untested\n4. **No E2E Tests**: User journeys untested\n\n## 📊 Coverage Targets vs Reality\n\n| Module | Target | Current | Gap |\n|--------|--------|---------|-----|\n| Backend APIs | 90% | ~60% | 30% |\n| Backend Models | 85% | ~40% | 45% |\n| Frontend Components | 70% | 3% | 67% |\n| Integration Tests | 90% | 10% | 80% |\n| E2E Tests | 100% | 0% | 100% |\n\n## 🎯 Priority Action Plan\n\n### Phase 1: Backend Stabilization (2 days)\n1. **Fix Import Issues**\n   - Update all `Timesheet` references to `TimesheetEntry`\n   - Fix service initialization patterns\n   - Resolve foreign key dependencies\n\n2. **Expand API Coverage**\n   - CRM APIs (clients, proposals, contracts)\n   - Timesheet APIs (entries, approvals)\n   - Communication APIs (forum, news)\n   - Performance APIs\n   - Funding APIs\n\n### Phase 2: Frontend Foundation (3 days)\n1. **Setup Testing Infrastructure**\n   - Configure Vitest properly\n   - Create component testing utilities\n   - Setup API mocking with MSW\n   - Create test data factories\n\n2. **Core Component Tests**\n   - Authentication components\n   - Dashboard components  \n   - Navigation components\n   - Form components\n\n### Phase 3: Integration & E2E (2 days)\n1. **API-Frontend Integration**\n   - Authentication flows\n   - Data loading patterns\n   - Error handling\n\n2. **E2E User Journeys**\n   - Login/logout\n   - Project management\n   - Timesheet entry\n   - Personnel management\n\n## 🛠️ Tools & Setup Required\n\n### Backend\n- [x] pytest-cov (installed)\n- [ ] pytest-mock\n- [ ] factory-boy\n- [ ] freezegun\n\n### Frontend  \n- [ ] @vue/test-utils\n- [ ] jsdom (configured)\n- [ ] MSW (API mocking)\n- [ ] Cypress (E2E)\n\n## 📈 Success Metrics\n\n### Week 1 Goals\n- Backend: Fix all import issues, 80% API coverage\n- Frontend: 20% component coverage\n- Integration: Basic auth tests\n\n### Week 2 Goals\n- Backend: 85% line coverage\n- Frontend: 50% component coverage  \n- Integration: Core workflow tests\n\n### Week 3 Goals\n- Backend: 90% line coverage\n- Frontend: 70% component coverage\n- E2E: Critical user journeys\n- CI/CD: Automated test pipeline\n\n## 🚀 Immediate Next Steps\n\n1. **Fix Backend Issues** (Today)\n2. **Setup Frontend Testing** (Tomorrow)\n3. **Create Test Templates** (Day 3)\n4. **Implement Priority Tests** (Week 1)\n5. **Setup CI/CD Pipeline** (Week 2)\n"}