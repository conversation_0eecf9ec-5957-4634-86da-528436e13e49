{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_performancegoal_model.py"}, "originalCode": "\"\"\"Unit tests for PerformanceGoal model.\"\"\"\nimport pytest\nfrom models import PerformanceGoal, User\nfrom extensions import db\n\nclass TestPerformanceGoalModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n\n    def test_performancegoal_creation_basic(self):\n        goal = PerformanceGoal(\n            employee_id=self.user.id,\n            title='Improve Sales',\n            description='Increase sales by 20%',\n            target_value=20.0\n        )\n        db.session.add(goal)\n        db.session.commit()\n        \n        assert goal.id is not None\n        assert goal.title == 'Improve Sales'\n        assert goal.target_value == 20.0\n\n    def test_performancegoal_deletion(self):\n        goal = PerformanceGoal(employee_id=self.user.id, title='To Delete')\n        db.session.add(goal)\n        db.session.commit()\n        goal_id = goal.id\n        \n        db.session.delete(goal)\n        db.session.commit()\n        \n        deleted = PerformanceGoal.query.get(goal_id)\n        assert deleted is None\n", "modifiedCode": "\"\"\"Unit tests for PerformanceGoal model.\"\"\"\nimport pytest\nfrom models import PerformanceGoal, User\nfrom extensions import db\n\nclass TestPerformanceGoalModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n\n    def test_performancegoal_creation_basic(self):\n        goal = PerformanceGoal(\n            employee_id=self.user.id,\n            title='Improve Sales',\n            description='Increase sales by 20%'\n            # Rimosso target_value - non esiste nel modello\n        )\n        db.session.add(goal)\n        db.session.commit()\n        \n        assert goal.id is not None\n        assert goal.title == 'Improve Sales'\n        assert goal.target_value == 20.0\n\n    def test_performancegoal_deletion(self):\n        goal = PerformanceGoal(employee_id=self.user.id, title='To Delete')\n        db.session.add(goal)\n        db.session.commit()\n        goal_id = goal.id\n        \n        db.session.delete(goal)\n        db.session.commit()\n        \n        deleted = PerformanceGoal.query.get(goal_id)\n        assert deleted is None\n"}