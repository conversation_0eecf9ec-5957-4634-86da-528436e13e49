{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/api/test_polls.py"}, "modifiedCode": "\"\"\"\nTest suite for Poll API endpoints.\nTests CRUD operations, validation, and business logic for poll management.\n\"\"\"\n\nimport pytest\nfrom datetime import datetime, date, timedelta\nfrom models import Poll, User\nfrom extensions import db\n\n\nclass TestPollsAPI:\n    \"\"\"Test suite for Poll API endpoints\"\"\"\n\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        \"\"\"Setup test data for each test method\"\"\"\n        self.app = app\n        self.user = test_user\n        \n        # Create test poll data\n        self.poll_data = {\n            'title': 'Test Poll Question',\n            'description': 'This is a test poll for API testing',\n            'author_id': self.user.id,\n            'is_anonymous': False,\n            'multiple_choice': False,\n            'expires_at': (datetime.utcnow() + timedelta(days=7)).isoformat(),\n            'is_active': True\n        }\n\n    def test_get_polls_success(self, client):\n        \"\"\"Test successful retrieval of polls list\"\"\"\n        # Create test polls\n        poll1 = Poll(\n            title='Poll 1',\n            description='First test poll',\n            author_id=self.user.id,\n            is_active=True\n        )\n        poll2 = Poll(\n            title='Poll 2',\n            description='Second test poll',\n            author_id=self.user.id,\n            is_active=True\n        )\n        db.session.add_all([poll1, poll2])\n        db.session.commit()\n\n        response = client.get('/api/polls')\n        \n        assert response.status_code in [200, 401]\n        if response.status_code == 200:\n            data = response.get_json()\n            assert 'polls' in str(data).lower() or 'data' in data\n\n    def test_create_poll_success(self, client):\n        \"\"\"Test successful poll creation\"\"\"\n        response = client.post('/api/polls', json=self.poll_data)\n        \n        assert response.status_code in [201, 200, 401, 403]\n        if response.status_code in [200, 201]:\n            # Verify poll was created\n            created_poll = Poll.query.filter_by(title='Test Poll Question').first()\n            if created_poll:\n                assert created_poll.author_id == self.user.id\n                assert created_poll.is_anonymous is False\n\n    def test_create_poll_validation_error(self, client):\n        \"\"\"Test poll creation with missing required fields\"\"\"\n        invalid_data = {'description': 'Missing title and author_id'}\n        \n        response = client.post('/api/polls', json=invalid_data)\n        \n        assert response.status_code in [400, 422, 401, 403]\n\n    def test_update_poll_success(self, client):\n        \"\"\"Test successful poll update\"\"\"\n        test_poll = Poll(\n            title='Original Poll Title',\n            description='Original description',\n            author_id=self.user.id,\n            is_active=True\n        )\n        db.session.add(test_poll)\n        db.session.commit()\n\n        update_data = {\n            'title': 'Updated Poll Title',\n            'description': 'Updated description',\n            'multiple_choice': True\n        }\n        \n        response = client.put(f'/api/polls/{test_poll.id}', json=update_data)\n        \n        assert response.status_code in [200, 401, 403, 404]\n\n    def test_delete_poll_success(self, client):\n        \"\"\"Test successful poll deletion\"\"\"\n        test_poll = Poll(\n            title='Poll to Delete',\n            description='This poll will be deleted',\n            author_id=self.user.id,\n            is_active=True\n        )\n        db.session.add(test_poll)\n        db.session.commit()\n        poll_id = test_poll.id\n\n        response = client.delete(f'/api/polls/{poll_id}')\n        \n        assert response.status_code in [200, 401, 403, 404]\n\n    def test_poll_activate_deactivate(self, client):\n        \"\"\"Test poll activation/deactivation\"\"\"\n        test_poll = Poll(\n            title='Activation Test Poll',\n            description='Poll for testing activation',\n            author_id=self.user.id,\n            is_active=True\n        )\n        db.session.add(test_poll)\n        db.session.commit()\n\n        # Test deactivate\n        response = client.put(f'/api/polls/{test_poll.id}/deactivate')\n        assert response.status_code in [200, 401, 403, 404]\n        \n        # Test activate\n        response = client.put(f'/api/polls/{test_poll.id}/activate')\n        assert response.status_code in [200, 401, 403, 404]\n\n    def test_poll_expiration_validation(self, client):\n        \"\"\"Test poll expiration date validation\"\"\"\n        # Test past expiration date\n        invalid_data = self.poll_data.copy()\n        invalid_data['expires_at'] = (datetime.utcnow() - timedelta(days=1)).isoformat()\n        \n        response = client.post('/api/polls', json=invalid_data)\n        assert response.status_code in [400, 422, 401, 403, 201, 200]\n\n    def test_poll_voting_options(self, client):\n        \"\"\"Test poll with voting options\"\"\"\n        poll_with_options = self.poll_data.copy()\n        poll_with_options['options'] = [\n            {'text': 'Option 1', 'order': 1},\n            {'text': 'Option 2', 'order': 2},\n            {'text': 'Option 3', 'order': 3}\n        ]\n        \n        response = client.post('/api/polls', json=poll_with_options)\n        assert response.status_code in [201, 200, 401, 403, 400, 422]\n\n    def test_vote_on_poll(self, client):\n        \"\"\"Test voting on a poll\"\"\"\n        test_poll = Poll(\n            title='Voting Test Poll',\n            description='Poll for testing voting',\n            author_id=self.user.id,\n            is_active=True,\n            multiple_choice=False\n        )\n        db.session.add(test_poll)\n        db.session.commit()\n\n        vote_data = {\n            'option_id': 1,  # Assuming option exists\n            'user_id': self.user.id\n        }\n        \n        response = client.post(f'/api/polls/{test_poll.id}/vote', json=vote_data)\n        assert response.status_code in [200, 201, 401, 403, 404, 400]\n\n    def test_poll_results(self, client):\n        \"\"\"Test poll results retrieval\"\"\"\n        test_poll = Poll(\n            title='Results Test Poll',\n            description='Poll for testing results',\n            author_id=self.user.id,\n            is_active=True,\n            total_votes=5\n        )\n        db.session.add(test_poll)\n        db.session.commit()\n\n        response = client.get(f'/api/polls/{test_poll.id}/results')\n        assert response.status_code in [200, 401, 404]\n\n    def test_poll_search_and_filters(self, client):\n        \"\"\"Test poll search and filtering\"\"\"\n        # Create test polls with different statuses\n        poll1 = Poll(\n            title='Active Poll',\n            description='An active poll',\n            author_id=self.user.id,\n            is_active=True\n        )\n        poll2 = Poll(\n            title='Inactive Poll',\n            description='An inactive poll',\n            author_id=self.user.id,\n            is_active=False\n        )\n        poll3 = Poll(\n            title='Anonymous Poll',\n            description='An anonymous poll',\n            author_id=self.user.id,\n            is_active=True,\n            is_anonymous=True\n        )\n        db.session.add_all([poll1, poll2, poll3])\n        db.session.commit()\n\n        # Test active filter\n        response = client.get('/api/polls?is_active=true')\n        assert response.status_code in [200, 401]\n        \n        # Test search by title\n        response = client.get('/api/polls?search=Active')\n        assert response.status_code in [200, 401]\n        \n        # Test anonymous filter\n        response = client.get('/api/polls?is_anonymous=true')\n        assert response.status_code in [200, 401]\n\n    def test_poll_multiple_choice_validation(self, client):\n        \"\"\"Test poll multiple choice settings\"\"\"\n        # Test single choice poll\n        single_choice_data = self.poll_data.copy()\n        single_choice_data['multiple_choice'] = False\n        \n        response = client.post('/api/polls', json=single_choice_data)\n        assert response.status_code in [201, 200, 401, 403, 400, 422]\n        \n        # Test multiple choice poll\n        multiple_choice_data = self.poll_data.copy()\n        multiple_choice_data['multiple_choice'] = True\n        multiple_choice_data['title'] = 'Multiple Choice Poll'\n        \n        response = client.post('/api/polls', json=multiple_choice_data)\n        assert response.status_code in [201, 200, 401, 403, 400, 422]\n\n    def test_get_poll_detail(self, client):\n        \"\"\"Test single poll retrieval\"\"\"\n        test_poll = Poll(\n            title='Detail Test Poll',\n            description='Poll for testing detail view',\n            author_id=self.user.id,\n            is_active=True,\n            total_votes=10,\n            is_anonymous=False\n        )\n        db.session.add(test_poll)\n        db.session.commit()\n\n        response = client.get(f'/api/polls/{test_poll.id}')\n        assert response.status_code in [200, 401, 404]\n\n    def test_poll_not_found(self, client):\n        \"\"\"Test poll not found scenarios\"\"\"\n        response = client.get('/api/polls/99999')\n        assert response.status_code in [404, 401]\n        \n        response = client.put('/api/polls/99999', json={'title': 'Test'})\n        assert response.status_code in [404, 401]\n        \n        response = client.delete('/api/polls/99999')\n        assert response.status_code in [404, 401]\n\n    def test_poll_author_permissions(self, client):\n        \"\"\"Test poll author permissions\"\"\"\n        # Create poll by another user (simulated)\n        other_poll = Poll(\n            title='Other User Poll',\n            description='Poll by another user',\n            author_id=999,  # Different user ID\n            is_active=True\n        )\n        db.session.add(other_poll)\n        db.session.commit()\n\n        # Try to update poll by different user\n        update_data = {'title': 'Unauthorized Update'}\n        response = client.put(f'/api/polls/{other_poll.id}', json=update_data)\n        assert response.status_code in [403, 401, 404]\n        \n        # Try to delete poll by different user\n        response = client.delete(f'/api/polls/{other_poll.id}')\n        assert response.status_code in [403, 401, 404]\n\n    def test_poll_pagination(self, client):\n        \"\"\"Test poll list pagination\"\"\"\n        # Create multiple polls\n        for i in range(5):\n            poll = Poll(\n                title=f'Poll {i}',\n                description=f'Description for poll {i}',\n                author_id=self.user.id,\n                is_active=True\n            )\n            db.session.add(poll)\n        db.session.commit()\n\n        response = client.get('/api/polls?page=1&per_page=3')\n        assert response.status_code in [200, 401]\n\n    def test_poll_anonymous_settings(self, client):\n        \"\"\"Test poll anonymous voting settings\"\"\"\n        # Test anonymous poll\n        anonymous_data = self.poll_data.copy()\n        anonymous_data['is_anonymous'] = True\n        anonymous_data['title'] = 'Anonymous Poll'\n        \n        response = client.post('/api/polls', json=anonymous_data)\n        assert response.status_code in [201, 200, 401, 403, 400, 422]\n        \n        # Test non-anonymous poll\n        public_data = self.poll_data.copy()\n        public_data['is_anonymous'] = False\n        public_data['title'] = 'Public Poll'\n        \n        response = client.post('/api/polls', json=public_data)\n        assert response.status_code in [201, 200, 401, 403, 400, 422]\n\n    def test_poll_title_validation(self, client):\n        \"\"\"Test poll title validation\"\"\"\n        # Test empty title\n        invalid_data = self.poll_data.copy()\n        invalid_data['title'] = ''\n        \n        response = client.post('/api/polls', json=invalid_data)\n        assert response.status_code in [400, 422, 401, 403]\n        \n        # Test very long title\n        invalid_data['title'] = 'x' * 300  # Assuming max length is 200\n        response = client.post('/api/polls', json=invalid_data)\n        assert response.status_code in [400, 422, 401, 403, 201, 200]\n\n    def test_get_user_polls(self, client):\n        \"\"\"Test retrieval of polls created by specific user\"\"\"\n        # Create polls for the user\n        poll1 = Poll(\n            title='User Poll 1',\n            description='First poll by user',\n            author_id=self.user.id,\n            is_active=True\n        )\n        db.session.add(poll1)\n        db.session.commit()\n\n        response = client.get(f'/api/polls/user/{self.user.id}')\n        assert response.status_code in [200, 401, 404]\n"}