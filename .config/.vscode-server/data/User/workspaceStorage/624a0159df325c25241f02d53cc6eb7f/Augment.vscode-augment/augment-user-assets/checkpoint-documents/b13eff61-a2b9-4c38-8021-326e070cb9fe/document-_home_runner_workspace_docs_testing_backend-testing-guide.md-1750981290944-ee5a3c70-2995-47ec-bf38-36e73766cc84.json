{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/testing/backend-testing-guide.md"}, "modifiedCode": "# 🔧 Backend Testing Guide\n\nGuida completa per il testing del backend Flask con pytest.\n\n## 📋 Indice\n\n- [Setup e Configurazione](#setup-e-configurazione)\n- [Unit Testing](#unit-testing)\n- [Integration Testing](#integration-testing)\n- [API Testing](#api-testing)\n- [Database Testing](#database-testing)\n- [Mock e Fixtures](#mock-e-fixtures)\n- [Best Practices](#best-practices)\n\n## ⚙️ Setup e Configurazione\n\n### Struttura Directory\n\n```\nbackend/tests/\n├── conftest.py              # Configurazione pytest e fixtures globali\n├── unit/\n│   ├── test_models.py       # Test modelli database\n│   ├── test_utils.py        # Test funzioni utility\n│   └── test_business_logic.py # Test logica business\n├── integration/\n│   ├── test_project_workflows.py # Test workflow completi\n│   ├── test_user_management.py   # Test gestione utenti\n│   └── test_timesheet_flows.py   # Test flussi timesheet\n└── api/\n    ├── test_auth_endpoints.py     # Test autenticazione\n    ├── test_projects_api.py       # Test API progetti\n    ├── test_personnel_api.py      # Test API personale\n    └── test_timesheet_api.py      # Test API timesheet\n```\n\n### Configurazione pytest\n\n```python\n# conftest.py\nimport pytest\nfrom app import create_app\nfrom extensions import db\nfrom models import User, Project, Client\n\****************(scope='session')\ndef app():\n    \"\"\"Crea app Flask per testing\"\"\"\n    app = create_app(config_name='testing')\n    \n    with app.app_context():\n        db.create_all()\n        yield app\n        db.drop_all()\n\****************\ndef client(app):\n    \"\"\"Client di test per API calls\"\"\"\n    return app.test_client()\n\****************\ndef auth_headers(client):\n    \"\"\"Headers autenticazione per API protette\"\"\"\n    # Login e ottieni token\n    response = client.post('/api/auth/login', json={\n        'username': 'admin',\n        'password': 'password'\n    })\n    token = response.json['data']['token']\n    \n    return {\n        'Authorization': f'Bearer {token}',\n        'Content-Type': 'application/json'\n    }\n\****************\ndef sample_user():\n    \"\"\"Utente di esempio per test\"\"\"\n    user = User(\n        username='testuser',\n        email='<EMAIL>',\n        first_name='Test',\n        last_name='User',\n        role='employee'\n    )\n    user.set_password('password')\n    db.session.add(user)\n    db.session.commit()\n    return user\n\****************\ndef sample_project(sample_user):\n    \"\"\"Progetto di esempio per test\"\"\"\n    client = Client(\n        name='Test Client',\n        email='<EMAIL>'\n    )\n    db.session.add(client)\n    db.session.commit()\n    \n    project = Project(\n        name='Test Project',\n        description='Project for testing',\n        client_id=client.id,\n        budget=10000.0,\n        status='active'\n    )\n    db.session.add(project)\n    db.session.commit()\n    return project\n```\n\n## 🧪 Unit Testing\n\n### Test Modelli Database\n\n```python\n# tests/unit/test_models.py\nimport pytest\nfrom datetime import date, timedelta\nfrom models import Project, User, TimesheetEntry\n\nclass TestProjectModel:\n    \"\"\"Test per il modello Project\"\"\"\n    \n    def test_project_creation(self):\n        \"\"\"Test creazione progetto base\"\"\"\n        project = Project(\n            name='Test Project',\n            description='Test description',\n            budget=5000.0,\n            start_date=date.today(),\n            end_date=date.today() + timedelta(days=30)\n        )\n        \n        assert project.name == 'Test Project'\n        assert project.budget == 5000.0\n        assert project.status == 'planning'  # Default value\n        \n    def test_remaining_budget_calculation(self):\n        \"\"\"Test calcolo budget rimanente\"\"\"\n        project = Project(\n            name='Budget Test',\n            budget=10000.0,\n            expenses=2500.0\n        )\n        \n        assert project.remaining_budget == 7500.0\n        \n    def test_budget_utilization_percentage(self):\n        \"\"\"Test calcolo percentuale utilizzo budget\"\"\"\n        project = Project(\n            name='Utilization Test',\n            budget=10000.0,\n            expenses=3000.0\n        )\n        \n        assert project.budget_utilization_percentage == 30.0\n        \n    def test_project_duration_calculation(self):\n        \"\"\"Test calcolo durata progetto\"\"\"\n        start_date = date(2025, 1, 1)\n        end_date = date(2025, 3, 31)\n        \n        project = Project(\n            name='Duration Test',\n            start_date=start_date,\n            end_date=end_date\n        )\n        \n        assert project.duration_days == 89  # 31+28+31-1\n        \n    def test_project_status_transitions(self):\n        \"\"\"Test transizioni stato progetto\"\"\"\n        project = Project(name='Status Test')\n        \n        # Stato iniziale\n        assert project.status == 'planning'\n        \n        # Transizione valida\n        project.status = 'active'\n        assert project.status == 'active'\n        \n        # Test validazione stati\n        valid_statuses = ['planning', 'active', 'completed', 'on-hold']\n        for status in valid_statuses:\n            project.status = status\n            assert project.status == status\n\nclass TestUserModel:\n    \"\"\"Test per il modello User\"\"\"\n    \n    def test_user_creation(self):\n        \"\"\"Test creazione utente\"\"\"\n        user = User(\n            username='testuser',\n            email='<EMAIL>',\n            first_name='Test',\n            last_name='User'\n        )\n        \n        assert user.username == 'testuser'\n        assert user.full_name == 'Test User'\n        assert user.role == 'employee'  # Default\n        \n    def test_password_hashing(self):\n        \"\"\"Test hashing password\"\"\"\n        user = User(username='testuser')\n        user.set_password('mypassword')\n        \n        assert user.password_hash is not None\n        assert user.password_hash != 'mypassword'\n        assert user.check_password('mypassword') is True\n        assert user.check_password('wrongpassword') is False\n        \n    def test_user_permissions(self):\n        \"\"\"Test sistema permessi utente\"\"\"\n        admin = User(username='admin', role='admin')\n        manager = User(username='manager', role='manager')\n        employee = User(username='employee', role='employee')\n        \n        # Admin ha tutti i permessi\n        assert admin.has_permission('create_project') is True\n        assert admin.has_permission('delete_user') is True\n        \n        # Manager ha permessi limitati\n        assert manager.has_permission('create_project') is True\n        assert manager.has_permission('delete_user') is False\n        \n        # Employee ha permessi base\n        assert employee.has_permission('view_projects') is True\n        assert employee.has_permission('create_project') is False\n```\n\n### Test Business Logic\n\n```python\n# tests/unit/test_business_logic.py\nimport pytest\nfrom datetime import date, timedelta\nfrom services.project_service import ProjectService\nfrom services.timesheet_service import TimesheetService\n\nclass TestProjectService:\n    \"\"\"Test per la logica business dei progetti\"\"\"\n    \n    def test_calculate_project_profitability(self, sample_project):\n        \"\"\"Test calcolo profittabilità progetto\"\"\"\n        # Setup dati\n        sample_project.budget = 50000\n        sample_project.client_daily_rate = 500\n        sample_project.markup_percentage = 20\n        \n        # Aggiungi costi team\n        team_daily_cost = 300  # Costo interno giornaliero\n        project_days = 60\n        \n        profitability = ProjectService.calculate_profitability(\n            sample_project, team_daily_cost, project_days\n        )\n        \n        # Revenue: 500 * 60 = 30000\n        # Costs: 300 * 60 = 18000  \n        # Profit: 30000 - 18000 = 12000\n        # Margin: 12000 / 30000 = 40%\n        \n        assert profitability['revenue'] == 30000\n        assert profitability['costs'] == 18000\n        assert profitability['profit'] == 12000\n        assert profitability['margin_percentage'] == 40.0\n        \n    def test_project_resource_allocation(self, sample_project, sample_user):\n        \"\"\"Test allocazione risorse progetto\"\"\"\n        allocation = ProjectService.calculate_resource_allocation(\n            sample_project, sample_user, allocation_percentage=75\n        )\n        \n        assert allocation['user_id'] == sample_user.id\n        assert allocation['project_id'] == sample_project.id\n        assert allocation['allocation_percentage'] == 75\n        assert allocation['daily_capacity_hours'] == 6  # 75% of 8 hours\n\nclass TestTimesheetService:\n    \"\"\"Test per la logica business timesheet\"\"\"\n    \n    def test_calculate_weekly_hours(self, sample_user, sample_project):\n        \"\"\"Test calcolo ore settimanali\"\"\"\n        # Crea entries per una settimana\n        week_start = date(2025, 1, 6)  # Lunedì\n        \n        for i in range(5):  # Lun-Ven\n            entry = TimesheetEntry(\n                user_id=sample_user.id,\n                project_id=sample_project.id,\n                date=week_start + timedelta(days=i),\n                hours=8.0\n            )\n            db.session.add(entry)\n        db.session.commit()\n        \n        weekly_hours = TimesheetService.get_weekly_hours(\n            sample_user.id, week_start\n        )\n        \n        assert weekly_hours['total_hours'] == 40.0\n        assert weekly_hours['working_days'] == 5\n        assert weekly_hours['average_daily_hours'] == 8.0\n        \n    def test_overtime_calculation(self, sample_user):\n        \"\"\"Test calcolo straordinari\"\"\"\n        # 10 ore in un giorno (2 ore di straordinario)\n        overtime_day = date.today()\n        \n        entry = TimesheetEntry(\n            user_id=sample_user.id,\n            date=overtime_day,\n            hours=10.0\n        )\n        \n        overtime = TimesheetService.calculate_overtime(entry)\n        \n        assert overtime['regular_hours'] == 8.0\n        assert overtime['overtime_hours'] == 2.0\n        assert overtime['overtime_rate'] == 1.5  # 150%\n```\n\n## 🔗 Integration Testing\n\n### Test Workflow Completi\n\n```python\n# tests/integration/test_project_workflows.py\nimport pytest\nfrom datetime import date, timedelta\nfrom models import Project, Task, TimesheetEntry, ProjectResource\n\nclass TestProjectLifecycle:\n    \"\"\"Test del ciclo di vita completo di un progetto\"\"\"\n    \n    def test_complete_project_workflow(self, client, auth_headers, sample_user):\n        \"\"\"Test workflow completo: creazione → team → task → timesheet → completamento\"\"\"\n        \n        # 1. CREAZIONE PROGETTO\n        project_data = {\n            'name': 'Integration Test Project',\n            'description': 'Full workflow test',\n            'budget': 25000.0,\n            'start_date': '2025-01-01',\n            'end_date': '2025-06-30',\n            'client_id': 1\n        }\n        \n        response = client.post('/api/projects', \n                             json=project_data, \n                             headers=auth_headers)\n        assert response.status_code == 201\n        \n        project_id = response.json['data']['id']\n        \n        # 2. AGGIUNTA TEAM MEMBERS\n        team_member_data = {\n            'user_id': sample_user.id,\n            'role': 'Developer',\n            'allocation_percentage': 100\n        }\n        \n        response = client.post(f'/api/projects/{project_id}/team',\n                             json=team_member_data,\n                             headers=auth_headers)\n        assert response.status_code == 200\n        \n        # 3. CREAZIONE TASK\n        task_data = {\n            'title': 'Implement Feature X',\n            'description': 'Develop and test feature X',\n            'assigned_to': sample_user.id,\n            'estimated_hours': 40,\n            'priority': 'high',\n            'due_date': '2025-02-15'\n        }\n        \n        response = client.post(f'/api/projects/{project_id}/tasks',\n                             json=task_data,\n                             headers=auth_headers)\n        assert response.status_code == 201\n        \n        task_id = response.json['data']['id']\n        \n        # 4. REGISTRAZIONE ORE\n        timesheet_data = {\n            'task_id': task_id,\n            'date': '2025-01-15',\n            'hours': 8.0,\n            'description': 'Working on feature implementation'\n        }\n        \n        response = client.post('/api/timesheet/entries',\n                             json=timesheet_data,\n                             headers=auth_headers)\n        assert response.status_code == 201\n        \n        # 5. AGGIORNAMENTO STATO TASK\n        response = client.put(f'/api/projects/{project_id}/tasks/{task_id}',\n                            json={'status': 'completed'},\n                            headers=auth_headers)\n        assert response.status_code == 200\n        \n        # 6. VERIFICA STATO FINALE\n        response = client.get(f'/api/projects/{project_id}',\n                            headers=auth_headers)\n        assert response.status_code == 200\n        \n        project = response.json['data']\n        assert len(project['team_members']) == 1\n        assert len(project['tasks']) == 1\n        assert project['tasks'][0]['status'] == 'completed'\n        \n    def test_project_funding_workflow(self, client, auth_headers):\n        \"\"\"Test workflow progetto con finanziamento\"\"\"\n        \n        # 1. Crea opportunità finanziamento\n        opportunity_data = {\n            'title': 'Digital Innovation Grant',\n            'source_entity': 'EU Commission',\n            'max_grant_amount': 100000,\n            'contribution_percentage': 70,\n            'application_deadline': '2025-12-31'\n        }\n        \n        response = client.post('/api/funding/opportunities',\n                             json=opportunity_data,\n                             headers=auth_headers)\n        assert response.status_code == 201\n        \n        opportunity_id = response.json['data']['id']\n        \n        # 2. Crea application\n        application_data = {\n            'opportunity_id': opportunity_id,\n            'project_title': 'AI Platform Development',\n            'requested_amount': 70000,\n            'project_description': 'AI-powered business platform'\n        }\n        \n        response = client.post('/api/funding/applications',\n                             json=application_data,\n                             headers=auth_headers)\n        assert response.status_code == 201\n        \n        application_id = response.json['data']['id']\n        \n        # 3. Approva application\n        response = client.put(f'/api/funding/applications/{application_id}',\n                            json={\n                                'status': 'approved',\n                                'approved_amount': 70000\n                            },\n                            headers=auth_headers)\n        assert response.status_code == 200\n        \n        # 4. Crea progetto collegato\n        project_data = {\n            'name': 'AI Platform Project',\n            'budget': 100000,\n            'funding_source': 'public_funding',\n            'funding_application_id': application_id\n        }\n        \n        response = client.post('/api/projects',\n                             json=project_data,\n                             headers=auth_headers)\n        assert response.status_code == 201\n        \n        project_id = response.json['data']['id']\n        \n        # 5. Crea link finanziamento\n        link_data = {\n            'project_id': project_id,\n            'funding_application_id': application_id,\n            'allocation_percentage': 70\n        }\n        \n        response = client.post('/api/funding/project-links',\n                             json=link_data,\n                             headers=auth_headers)\n        assert response.status_code == 201\n        \n        # 6. Verifica collegamento\n        response = client.get(f'/api/projects/{project_id}',\n                            headers=auth_headers)\n        project = response.json['data']\n        \n        assert project['funding_source'] == 'public_funding'\n        assert len(project['funding_links']) == 1\n        assert project['funding_links'][0]['allocation_percentage'] == 70\n```\n\n## 🌐 API Testing\n\n### Test Endpoint REST\n\n```python\n# tests/api/test_projects_api.py\nimport pytest\nfrom models import Project\n\nclass TestProjectsAPI:\n    \"\"\"Test per gli endpoint API dei progetti\"\"\"\n    \n    def test_get_projects_list(self, client, auth_headers, sample_project):\n        \"\"\"Test GET /api/projects\"\"\"\n        response = client.get('/api/projects', headers=auth_headers)\n        \n        assert response.status_code == 200\n        assert response.json['success'] is True\n        \n        data = response.json['data']\n        assert 'projects' in data\n        assert 'pagination' in data\n        assert len(data['projects']) >= 1\n        \n    def test_get_project_detail(self, client, auth_headers, sample_project):\n        \"\"\"Test GET /api/projects/<id>\"\"\"\n        response = client.get(f'/api/projects/{sample_project.id}',\n                            headers=auth_headers)\n        \n        assert response.status_code == 200\n        \n        project = response.json['data']\n        assert project['id'] == sample_project.id\n        assert project['name'] == sample_project.name\n        assert 'team_members' in project\n        assert 'tasks' in project\n        \n    def test_create_project_validation(self, client, auth_headers):\n        \"\"\"Test validazione creazione progetto\"\"\"\n        # Test dati mancanti\n        response = client.post('/api/projects',\n                             json={},\n                             headers=auth_headers)\n        \n        assert response.status_code == 400\n        assert 'errors' in response.json\n        assert 'name' in response.json['errors']\n        \n        # Test dati invalidi\n        invalid_data = {\n            'name': '',  # Nome vuoto\n            'budget': -1000,  # Budget negativo\n            'start_date': '2025-12-31',\n            'end_date': '2025-01-01'  # End date prima di start date\n        }\n        \n        response = client.post('/api/projects',\n                             json=invalid_data,\n                             headers=auth_headers)\n        \n        assert response.status_code == 400\n        errors = response.json['errors']\n        assert 'name' in errors\n        assert 'budget' in errors\n        assert 'end_date' in errors\n        \n    def test_update_project(self, client, auth_headers, sample_project):\n        \"\"\"Test PUT /api/projects/<id>\"\"\"\n        update_data = {\n            'name': 'Updated Project Name',\n            'budget': 15000.0,\n            'status': 'active'\n        }\n        \n        response = client.put(f'/api/projects/{sample_project.id}',\n                            json=update_data,\n                            headers=auth_headers)\n        \n        assert response.status_code == 200\n        \n        updated_project = response.json['data']\n        assert updated_project['name'] == 'Updated Project Name'\n        assert updated_project['budget'] == 15000.0\n        assert updated_project['status'] == 'active'\n        \n    def test_delete_project(self, client, auth_headers, sample_project):\n        \"\"\"Test DELETE /api/projects/<id>\"\"\"\n        project_id = sample_project.id\n        \n        response = client.delete(f'/api/projects/{project_id}',\n                               headers=auth_headers)\n        \n        assert response.status_code == 200\n        \n        # Verifica che il progetto sia stato eliminato\n        response = client.get(f'/api/projects/{project_id}',\n                            headers=auth_headers)\n        assert response.status_code == 404\n        \n    def test_project_filtering(self, client, auth_headers):\n        \"\"\"Test filtri API progetti\"\"\"\n        # Test filtro per status\n        response = client.get('/api/projects?status=active',\n                            headers=auth_headers)\n        assert response.status_code == 200\n        \n        # Test filtro per client\n        response = client.get('/api/projects?client_id=1',\n                            headers=auth_headers)\n        assert response.status_code == 200\n        \n        # Test ricerca per nome\n        response = client.get('/api/projects?search=test',\n                            headers=auth_headers)\n        assert response.status_code == 200\n        \n    def test_project_pagination(self, client, auth_headers):\n        \"\"\"Test paginazione API progetti\"\"\"\n        response = client.get('/api/projects?page=1&per_page=5',\n                            headers=auth_headers)\n        \n        assert response.status_code == 200\n        \n        data = response.json['data']\n        pagination = data['pagination']\n        \n        assert pagination['page'] == 1\n        assert pagination['per_page'] == 5\n        assert 'total' in pagination\n        assert 'pages' in pagination\n```\n\n## 🗄️ Database Testing\n\n### Test Transazioni e Rollback\n\n```python\n# tests/unit/test_database.py\nimport pytest\nfrom extensions import db\nfrom models import Project, User\n\nclass TestDatabaseOperations:\n    \"\"\"Test operazioni database\"\"\"\n    \n    def test_transaction_rollback(self, app):\n        \"\"\"Test rollback transazioni in caso di errore\"\"\"\n        with app.app_context():\n            # Inizia transazione\n            project1 = Project(name='Project 1', budget=1000)\n            db.session.add(project1)\n            \n            try:\n                # Operazione che causa errore\n                project2 = Project(name=None, budget=2000)  # Nome required\n                db.session.add(project2)\n                db.session.commit()\n            except Exception:\n                db.session.rollback()\n                \n            # Verifica che nessun progetto sia stato salvato\n            assert Project.query.count() == 0\n            \n    def test_cascade_delete(self, app, sample_project, sample_user):\n        \"\"\"Test eliminazione a cascata\"\"\"\n        with app.app_context():\n            # Aggiungi task al progetto\n            task = Task(\n                project_id=sample_project.id,\n                title='Test Task',\n                assigned_to=sample_user.id\n            )\n            db.session.add(task)\n            db.session.commit()\n            \n            task_id = task.id\n            \n            # Elimina progetto\n            db.session.delete(sample_project)\n            db.session.commit()\n            \n            # Verifica che anche il task sia stato eliminato\n            assert Task.query.get(task_id) is None\n            \n    def test_unique_constraints(self, app):\n        \"\"\"Test vincoli di unicità\"\"\"\n        with app.app_context():\n            # Crea primo utente\n            user1 = User(username='testuser', email='<EMAIL>')\n            db.session.add(user1)\n            db.session.commit()\n            \n            # Tenta di creare secondo utente con stesso username\n            user2 = User(username='testuser', email='<EMAIL>')\n            db.session.add(user2)\n            \n            with pytest.raises(Exception):  # Violazione constraint\n                db.session.commit()\n```\n\n## 🎭 Mock e Fixtures\n\n### Factory Pattern per Test Data\n\n```python\n# tests/factories.py\nimport factory\nfrom datetime import date, timedelta\nfrom models import User, Project, Client, Task\n\nclass UserFactory(factory.Factory):\n    class Meta:\n        model = User\n        \n    username = factory.Sequence(lambda n: f'user{n}')\n    email = factory.LazyAttribute(lambda obj: f'{obj.username}@example.com')\n    first_name = factory.Faker('first_name')\n    last_name = factory.Faker('last_name')\n    role = 'employee'\n\nclass ClientFactory(factory.Factory):\n    class Meta:\n        model = Client\n        \n    name = factory.Faker('company')\n    email = factory.LazyAttribute(lambda obj: f'contact@{obj.name.lower().replace(\" \", \"\")}.com')\n    industry = factory.Faker('word')\n\nclass ProjectFactory(factory.Factory):\n    class Meta:\n        model = Project\n        \n    name = factory.Faker('catch_phrase')\n    description = factory.Faker('text', max_nb_chars=200)\n    budget = factory.Faker('pydecimal', left_digits=5, right_digits=2, positive=True)\n    start_date = factory.LazyFunction(lambda: date.today())\n    end_date = factory.LazyFunction(lambda: date.today() + timedelta(days=90))\n    status = 'planning'\n    client = factory.SubFactory(ClientFactory)\n\n# Uso nelle test\ndef test_with_factory_data():\n    project = ProjectFactory()\n    assert project.name is not None\n    assert project.budget > 0\n    assert project.client.name is not None\n```\n\n## 📚 Best Practices\n\n### Naming Conventions\n\n```python\n# ✅ Buono: Nomi descrittivi\ndef test_project_budget_calculation_with_expenses():\n    \"\"\"Test calcolo budget rimanente considerando le spese\"\"\"\n    pass\n\ndef test_user_authentication_with_invalid_credentials():\n    \"\"\"Test autenticazione con credenziali non valide\"\"\"\n    pass\n\n# ❌ Cattivo: Nomi generici\ndef test_project():\n    pass\n\ndef test_user_login():\n    pass\n```\n\n### Test Organization\n\n```python\n# ✅ Buono: Organizzazione per classe\nclass TestProjectModel:\n    \"\"\"Test per il modello Project\"\"\"\n    \n    def test_creation(self):\n        pass\n        \n    def test_validation(self):\n        pass\n        \n    def test_relationships(self):\n        pass\n\nclass TestProjectAPI:\n    \"\"\"Test per l'API dei progetti\"\"\"\n    \n    def test_get_list(self):\n        pass\n        \n    def test_create(self):\n        pass\n        \n    def test_update(self):\n        pass\n```\n\n### Assertion Patterns\n\n```python\n# ✅ Buono: Assertions specifiche\nassert response.status_code == 201\nassert response.json['success'] is True\nassert 'id' in response.json['data']\nassert len(projects) == 3\n\n# ❌ Cattivo: Assertions generiche\nassert response\nassert data\n```\n\n### Cleanup e Isolation\n\n```python\n# ✅ Buono: Cleanup automatico\****************(autouse=True)\ndef cleanup_database():\n    \"\"\"Pulisce database dopo ogni test\"\"\"\n    yield\n    db.session.rollback()\n    for table in reversed(db.metadata.sorted_tables):\n        db.session.execute(table.delete())\n    db.session.commit()\n```\n\n---\n\n*Guida aggiornata: 2025-01-26*\n"}