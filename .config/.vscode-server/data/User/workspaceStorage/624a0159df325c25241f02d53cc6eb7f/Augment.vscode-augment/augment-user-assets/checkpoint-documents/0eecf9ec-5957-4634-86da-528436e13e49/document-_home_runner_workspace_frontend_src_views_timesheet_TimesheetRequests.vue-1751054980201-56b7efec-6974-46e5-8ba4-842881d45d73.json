{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/timesheet/TimesheetRequests.vue"}, "originalCode": "<template>\n  <div class=\"space-y-6\">\n    <!-- <PERSON>rro<PERSON> -->\n    <div v-if=\"error\" class=\"bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800 rounded-md p-4\">\n      <div class=\"flex\">\n        <div class=\"flex-shrink-0\">\n          <HeroIcon name=\"exclamation-triangle\" size=\"md\" color=\"text-red-400\" />\n        </div>\n        <div class=\"ml-3\">\n          <p class=\"text-sm text-red-800 dark:text-red-200\">{{ error }}</p>\n        </div>\n        <div class=\"ml-auto pl-3\">\n          <div class=\"-mx-1.5 -my-1.5\">\n            <button @click=\"clearError\" class=\"inline-flex bg-red-50 dark:bg-red-900/50 rounded-md p-1.5 text-red-500 hover:bg-red-100 dark:hover:bg-red-900\">\n              <HeroIcon name=\"x-mark\" size=\"sm\" />\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Header -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n      <div class=\"flex justify-between items-center\">\n        <div>\n          <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white\">Richieste</h1>\n          <p class=\"mt-1 text-sm text-gray-600 dark:text-gray-400\">\n            Gestisci le tue richieste di ferie, permessi e smart working\n          </p>\n        </div>\n        \n        <div class=\"flex space-x-3\">\n          <button\n            @click=\"showRequestModal('vacation')\"\n            :disabled=\"loading\"\n            data-testid=\"vacation-button\"\n            class=\"bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-md text-sm font-medium\"\n          >\n            Richiedi Ferie\n          </button>\n          <button\n            @click=\"showRequestModal('leave')\"\n            :disabled=\"loading\"\n            data-testid=\"leave-button\"\n            class=\"bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white px-4 py-2 rounded-md text-sm font-medium\"\n          >\n            Richiedi Permesso\n          </button>\n          <button\n            @click=\"showRequestModal('smartworking')\"\n            :disabled=\"loading\"\n            class=\"bg-purple-600 hover:bg-purple-700 disabled:bg-purple-400 text-white px-4 py-2 rounded-md text-sm font-medium\"\n          >\n            Smart Working\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Filtri -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-4\">\n      <div class=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n            Tipo Richiesta\n          </label>\n          <select \n            v-model=\"selectedType\"\n            class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n          >\n            <option value=\"\">Tutti i tipi</option>\n            <option value=\"vacation\">Ferie</option>\n            <option value=\"leave\">Permessi</option>\n            <option value=\"smartworking\">Smart Working</option>\n          </select>\n        </div>\n        \n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n            Stato\n          </label>\n          <select \n            v-model=\"selectedStatus\"\n            class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n          >\n            <option value=\"\">Tutti gli stati</option>\n            <option value=\"pending\">In Attesa</option>\n            <option value=\"approved\">Approvato</option>\n            <option value=\"rejected\">Rifiutato</option>\n          </select>\n        </div>\n        \n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n            Da Data\n          </label>\n          <input \n            v-model=\"dateFrom\"\n            type=\"date\"\n            class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n          >\n        </div>\n        \n        <div class=\"flex items-end\">\n          <button \n            @click=\"loadRequests\"\n            class=\"w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n          >\n            Filtra\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Lista Richieste -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden\">\n      <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n        <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n          Le Mie Richieste\n        </h3>\n      </div>\n      \n      <div class=\"overflow-x-auto\">\n        <table class=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n          <thead class=\"bg-gray-50 dark:bg-gray-700\">\n            <tr>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Tipo\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Periodo\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Durata\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Motivo\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Stato\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Richiesta il\n              </th>\n              <th class=\"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Azioni\n              </th>\n            </tr>\n          </thead>\n          <tbody class=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\n            <tr v-for=\"request in requests\" :key=\"request.id\">\n              <td class=\"px-6 py-4 whitespace-nowrap\">\n                <span\n                  class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\"\n                  :class=\"getTypeClass(request.request_type)\"\n                >\n                  {{ getTypeText(request.request_type) }}\n                </span>\n              </td>\n              <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                {{ formatPeriod(request) }}\n              </td>\n              <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                {{ formatDuration(request) }}\n              </td>\n              <td class=\"px-6 py-4 text-sm text-gray-900 dark:text-white max-w-xs truncate\">\n                {{ request.notes || 'N/A' }}\n              </td>\n              <td class=\"px-6 py-4 whitespace-nowrap\">\n                <span \n                  class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\"\n                  :class=\"getStatusClass(request.status)\"\n                >\n                  {{ getStatusText(request.status) }}\n                </span>\n              </td>\n              <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                {{ formatDate(request.created_at) }}\n              </td>\n              <td class=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                <button \n                  v-if=\"request.status === 'pending'\"\n                  @click=\"editRequest(request)\"\n                  class=\"text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 mr-3\"\n                >\n                  Modifica\n                </button>\n                <button \n                  v-if=\"request.status === 'pending'\"\n                  @click=\"deleteRequest(request.id)\"\n                  class=\"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300\"\n                >\n                  Elimina\n                </button>\n                <span \n                  v-else\n                  class=\"text-gray-400 dark:text-gray-500\"\n                >\n                  {{ request.status === 'approved' ? 'Approvata' : 'Rifiutata' }}\n                </span>\n              </td>\n            </tr>\n          </tbody>\n        </table>\n        \n        <!-- Empty state -->\n        <div v-if=\"requests.length === 0\" class=\"text-center py-8\">\n          <div class=\"mx-auto h-12 w-12 text-gray-400\">\n            <HeroIcon name=\"document\" size=\"xl\" />\n          </div>\n          <h3 class=\"mt-2 text-sm font-medium text-gray-900 dark:text-white\">Nessuna richiesta</h3>\n          <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n            Non hai ancora effettuato richieste per il periodo selezionato.\n          </p>\n        </div>\n      </div>\n    </div>\n\n    <!-- Statistiche -->\n    <div class=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center\">\n              <HeroIcon name=\"calendar\" size=\"md\" color=\"text-white\" />\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Ferie Rimanenti\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ remainingVacationDays }} giorni\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center\">\n              <HeroIcon name=\"clock\" size=\"md\" color=\"text-white\" />\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Permessi Usati\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ usedLeaveDays }} / {{ totalLeaveDays }} giorni\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center\">\n              <HeroIcon name=\"user\" size=\"md\" color=\"text-white\" />\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Smart Working\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ smartWorkingDays }} giorni\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center\">\n              <HeroIcon name=\"document-text\" size=\"md\" color=\"text-white\" />\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                In Attesa\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ pendingRequests }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Modal Nuova Richiesta -->\n    <div v-if=\"showModal\" class=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\" @click=\"closeModal\">\n      <div class=\"relative top-20 mx-auto p-5 border w-[500px] shadow-lg rounded-md bg-white dark:bg-gray-800\" @click.stop>\n        <div class=\"mt-3\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n            {{ getModalTitle() }}\n          </h3>\n          \n          <form @submit.prevent=\"submitRequest\">\n            <div class=\"grid grid-cols-1 gap-4\">\n              <!-- Data Inizio -->\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Data Inizio\n                </label>\n                <input\n                  v-model=\"formData.start_date\"\n                  type=\"date\"\n                  required\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                >\n              </div>\n\n              <!-- Data Fine -->\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Data Fine\n                </label>\n                <input\n                  v-model=\"formData.end_date\"\n                  type=\"date\"\n                  required\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                >\n              </div>\n\n              <!-- Note -->\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {{ requestType === 'smartworking' ? 'Note (opzionale)' : 'Motivo' }}\n                </label>\n                <textarea\n                  v-model=\"formData.notes\"\n                  rows=\"3\"\n                  :required=\"requestType !== 'smartworking'\"\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                  :placeholder=\"requestType === 'smartworking' ? 'Note aggiuntive...' : 'Descrivi il motivo della richiesta...'\"\n                ></textarea>\n              </div>\n            </div>\n\n            <div class=\"flex justify-end space-x-3 mt-6\">\n              <button \n                type=\"button\" \n                @click=\"closeModal\"\n                class=\"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md\"\n              >\n                Annulla\n              </button>\n              <button \n                type=\"submit\"\n                :disabled=\"saving\"\n                class=\"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50\"\n              >\n                {{ saving ? 'Invio...' : 'Invia Richiesta' }}\n              </button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted, watch } from 'vue'\nimport { useTimesheetStore } from '@/stores/timesheet'\nimport { useAuthStore } from '@/stores/auth'\nimport { formatDate, getStatusClass, getStatusText } from '@/utils/timesheet'\nimport HeroIcon from '@/components/icons/HeroIcon.vue'\n\nconst timesheetStore = useTimesheetStore()\nconst authStore = useAuthStore()\n\n// Local state\nconst requests = ref([])\nconst loading = ref(false)\nconst showModal = ref(false)\nconst saving = ref(false)\nconst requestType = ref('')\nconst timeOffQuotas = ref({\n  vacation: { remaining: 0, total: 0 },\n  leave: { used: 0, total: 0 },\n  smartworking: { used: 0 }\n})\n\n// Filters\nconst selectedType = ref('')\nconst selectedStatus = ref('')\nconst dateFrom = ref('')\n\n// Form data\nconst formData = ref({\n  start_date: '',\n  end_date: '',\n  notes: ''\n})\n\n// Computed properties from store\nconst error = computed(() => timesheetStore.error)\n\nconst remainingVacationDays = computed(() => {\n  return timeOffQuotas.value.vacation?.remaining || 0\n})\n\nconst usedLeaveDays = computed(() => {\n  return timeOffQuotas.value.leave?.used || 0\n})\n\nconst totalLeaveDays = computed(() => {\n  return timeOffQuotas.value.leave?.total || 0\n})\n\nconst smartWorkingDays = computed(() => {\n  return timeOffQuotas.value.smartworking?.used || 0\n})\n\nconst pendingRequests = computed(() => {\n  if (!Array.isArray(requests.value)) return 0\n  return requests.value.filter(r => r.status === 'pending').length\n})\n\n// Methods\nconst loadQuotas = async () => {\n  try {\n    const quotas = await timesheetStore.loadTimeOffQuotas()\n    timeOffQuotas.value = quotas\n    console.log('Time-off quotas loaded:', quotas)\n  } catch (err) {\n    console.error('Failed to load time-off quotas:', err)\n    // Imposta valori predefiniti in caso di errore\n    timeOffQuotas.value = {\n      vacation: { remaining: 0, total: 0 },\n      leave: { used: 0, total: 0 },\n      smartworking: { used: 0 }\n    }\n    // Imposta un messaggio di errore che non blocchi l'esperienza utente\n    timesheetStore.setError('Impossibile caricare i dati delle quote di ferie e permessi. Verranno mostrati valori predefiniti.')\n  }\n}\n\nconst loadRequests = async () => {\n  loading.value = true\n\n  try {\n    const filters = {\n      request_type: selectedType.value,\n      status: selectedStatus.value,\n      start_date: dateFrom.value\n    }\n\n    requests.value = await timesheetStore.loadTimeOffRequests(filters)\n  } finally {\n    loading.value = false\n  }\n}\n\nconst showRequestModal = (type) => {\n  requestType.value = type\n  formData.value = {\n    start_date: '',\n    end_date: '',\n    notes: ''\n  }\n  showModal.value = true\n}\n\nconst closeModal = () => {\n  showModal.value = false\n  requestType.value = ''\n}\n\nconst getModalTitle = () => {\n  switch (requestType.value) {\n    case 'vacation':\n      return 'Richiesta Ferie'\n    case 'leave':\n      return 'Richiesta Permesso'\n    case 'smartworking':  // <-- Corretto da 'smart_working' a 'smartworking'\n      return 'Richiesta Smart Working'\n    default:\n      return 'Nuova Richiesta'\n  }\n}\n\nconst submitRequest = async () => {\n  saving.value = true\n\n  try {\n    const data = {\n      request_type: requestType.value,\n      ...formData.value\n    }\n\n    const success = await timesheetStore.createTimeOffRequest(data)\n    if (success) {\n      await loadRequests()\n      await loadQuotas()\n      closeModal()\n    } else {\n      // Chiudi il modale anche in caso di errore per mostrare l'alert all'utente\n      closeModal()\n      // Scorri la pagina verso l'alto per assicurarsi che l'utente veda l'errore\n      window.scrollTo({ top: 0, behavior: 'smooth' })\n    }\n  } finally {\n    saving.value = false\n  }\n}\n\nconst editRequest = (request) => {\n  requestType.value = request.request_type\n  formData.value = {\n    start_date: request.start_date,\n    end_date: request.end_date,\n    notes: request.notes || ''\n  }\n  showModal.value = true\n}\n\nconst deleteRequest = async (requestId) => {\n  if (!confirm('Sei sicuro di voler eliminare questa richiesta?')) return\n\n  const success = await timesheetStore.deleteTimeOffRequest(requestId)\n  if (success) {\n    await loadRequests()\n    await loadQuotas()\n  }\n}\n\nconst formatPeriod = (request) => {\n  return `${formatDate(request.start_date)} - ${formatDate(request.end_date)}`\n}\n\nconst formatDuration = (request) => {\n  return `${request.duration_days || 0} giorni`\n}\n\nconst getTypeClass = (type) => {\n  switch (type) {\n    case 'vacation':\n      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'\n    case 'leave':\n      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'\n    case 'smartworking':\n      return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'\n    default:\n      return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'\n  }\n}\n\nconst getTypeText = (type) => {\n  switch (type) {\n    case 'vacation':\n      return 'Ferie'\n    case 'leave':\n      return 'Permesso'\n    case 'smartworking':\n      return 'Smart Working'\n    default:\n      return 'Altro'\n  }\n}\n\nconst clearError = () => {\n  timesheetStore.clearError()\n}\n\n// Lifecycle\nonMounted(() => {\n  loadRequests()\n  loadQuotas()\n})\nwatch([selectedType, selectedStatus, dateFrom], () => {\n  loadRequests()\n})\n</script>\n", "modifiedCode": "<template>\n  <div class=\"space-y-6\">\n    <!-- <PERSON>rro<PERSON> -->\n    <div v-if=\"error\" class=\"bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800 rounded-md p-4\">\n      <div class=\"flex\">\n        <div class=\"flex-shrink-0\">\n          <HeroIcon name=\"exclamation-triangle\" size=\"md\" color=\"text-red-400\" />\n        </div>\n        <div class=\"ml-3\">\n          <p class=\"text-sm text-red-800 dark:text-red-200\">{{ error }}</p>\n        </div>\n        <div class=\"ml-auto pl-3\">\n          <div class=\"-mx-1.5 -my-1.5\">\n            <button @click=\"clearError\" class=\"inline-flex bg-red-50 dark:bg-red-900/50 rounded-md p-1.5 text-red-500 hover:bg-red-100 dark:hover:bg-red-900\">\n              <HeroIcon name=\"x-mark\" size=\"sm\" />\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Header -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n      <div class=\"flex justify-between items-center\">\n        <div>\n          <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white\">Richieste</h1>\n          <p class=\"mt-1 text-sm text-gray-600 dark:text-gray-400\">\n            Gestisci le tue richieste di ferie, permessi e smart working\n          </p>\n        </div>\n        \n        <div class=\"flex space-x-3\">\n          <button\n            @click=\"showRequestModal('vacation')\"\n            :disabled=\"loading\"\n            data-testid=\"vacation-button\"\n            class=\"bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-md text-sm font-medium\"\n          >\n            Richiedi Ferie\n          </button>\n          <button\n            @click=\"showRequestModal('leave')\"\n            :disabled=\"loading\"\n            data-testid=\"leave-button\"\n            class=\"bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white px-4 py-2 rounded-md text-sm font-medium\"\n          >\n            Richiedi Permesso\n          </button>\n          <button\n            @click=\"showRequestModal('smartworking')\"\n            :disabled=\"loading\"\n            class=\"bg-purple-600 hover:bg-purple-700 disabled:bg-purple-400 text-white px-4 py-2 rounded-md text-sm font-medium\"\n          >\n            Smart Working\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Filtri -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-4\">\n      <div class=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n            Tipo Richiesta\n          </label>\n          <select \n            v-model=\"selectedType\"\n            class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n          >\n            <option value=\"\">Tutti i tipi</option>\n            <option value=\"vacation\">Ferie</option>\n            <option value=\"leave\">Permessi</option>\n            <option value=\"smartworking\">Smart Working</option>\n          </select>\n        </div>\n        \n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n            Stato\n          </label>\n          <select \n            v-model=\"selectedStatus\"\n            class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n          >\n            <option value=\"\">Tutti gli stati</option>\n            <option value=\"pending\">In Attesa</option>\n            <option value=\"approved\">Approvato</option>\n            <option value=\"rejected\">Rifiutato</option>\n          </select>\n        </div>\n        \n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n            Da Data\n          </label>\n          <input \n            v-model=\"dateFrom\"\n            type=\"date\"\n            class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n          >\n        </div>\n        \n        <div class=\"flex items-end\">\n          <button \n            @click=\"loadRequests\"\n            class=\"w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n          >\n            Filtra\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Lista Richieste -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden\">\n      <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n        <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n          Le Mie Richieste\n        </h3>\n      </div>\n      \n      <div class=\"overflow-x-auto\">\n        <table class=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n          <thead class=\"bg-gray-50 dark:bg-gray-700\">\n            <tr>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Tipo\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Periodo\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Durata\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Motivo\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Stato\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Richiesta il\n              </th>\n              <th class=\"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Azioni\n              </th>\n            </tr>\n          </thead>\n          <tbody class=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\n            <tr v-for=\"request in requests\" :key=\"request.id\">\n              <td class=\"px-6 py-4 whitespace-nowrap\">\n                <span\n                  class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\"\n                  :class=\"getTypeClass(request.request_type)\"\n                >\n                  {{ getTypeText(request.request_type) }}\n                </span>\n              </td>\n              <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                {{ formatPeriod(request) }}\n              </td>\n              <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                {{ formatDuration(request) }}\n              </td>\n              <td class=\"px-6 py-4 text-sm text-gray-900 dark:text-white max-w-xs truncate\">\n                {{ request.notes || 'N/A' }}\n              </td>\n              <td class=\"px-6 py-4 whitespace-nowrap\">\n                <span \n                  class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\"\n                  :class=\"getStatusClass(request.status)\"\n                >\n                  {{ getStatusText(request.status) }}\n                </span>\n              </td>\n              <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                {{ formatDate(request.created_at) }}\n              </td>\n              <td class=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                <button \n                  v-if=\"request.status === 'pending'\"\n                  @click=\"editRequest(request)\"\n                  class=\"text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 mr-3\"\n                >\n                  Modifica\n                </button>\n                <button \n                  v-if=\"request.status === 'pending'\"\n                  @click=\"deleteRequest(request.id)\"\n                  class=\"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300\"\n                >\n                  Elimina\n                </button>\n                <span \n                  v-else\n                  class=\"text-gray-400 dark:text-gray-500\"\n                >\n                  {{ request.status === 'approved' ? 'Approvata' : 'Rifiutata' }}\n                </span>\n              </td>\n            </tr>\n          </tbody>\n        </table>\n        \n        <!-- Empty state -->\n        <div v-if=\"requests.length === 0\" class=\"text-center py-8\">\n          <div class=\"mx-auto h-12 w-12 text-gray-400\">\n            <HeroIcon name=\"document\" size=\"xl\" />\n          </div>\n          <h3 class=\"mt-2 text-sm font-medium text-gray-900 dark:text-white\">Nessuna richiesta</h3>\n          <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n            Non hai ancora effettuato richieste per il periodo selezionato.\n          </p>\n        </div>\n      </div>\n    </div>\n\n    <!-- Statistiche -->\n    <div class=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center\">\n              <HeroIcon name=\"calendar\" size=\"md\" color=\"text-white\" />\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Ferie Rimanenti\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ remainingVacationDays }} giorni\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center\">\n              <HeroIcon name=\"clock\" size=\"md\" color=\"text-white\" />\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Permessi Usati\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ usedLeaveDays }} / {{ totalLeaveDays }} giorni\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center\">\n              <HeroIcon name=\"user\" size=\"md\" color=\"text-white\" />\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Smart Working\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ smartWorkingDays }} giorni\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center\">\n              <HeroIcon name=\"document-text\" size=\"md\" color=\"text-white\" />\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                In Attesa\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ pendingRequests }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Modal Nuova Richiesta -->\n    <div v-if=\"showModal\" class=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\" @click=\"closeModal\">\n      <div class=\"relative top-20 mx-auto p-5 border w-[500px] shadow-lg rounded-md bg-white dark:bg-gray-800\" @click.stop>\n        <div class=\"mt-3\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n            {{ getModalTitle() }}\n          </h3>\n          \n          <form @submit.prevent=\"submitRequest\">\n            <div class=\"grid grid-cols-1 gap-4\">\n              <!-- Data Inizio -->\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Data Inizio\n                </label>\n                <input\n                  v-model=\"formData.start_date\"\n                  type=\"date\"\n                  required\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                >\n              </div>\n\n              <!-- Data Fine -->\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Data Fine\n                </label>\n                <input\n                  v-model=\"formData.end_date\"\n                  type=\"date\"\n                  required\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                >\n              </div>\n\n              <!-- Note -->\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {{ requestType === 'smartworking' ? 'Note (opzionale)' : 'Motivo' }}\n                </label>\n                <textarea\n                  v-model=\"formData.notes\"\n                  rows=\"3\"\n                  :required=\"requestType !== 'smartworking'\"\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                  :placeholder=\"requestType === 'smartworking' ? 'Note aggiuntive...' : 'Descrivi il motivo della richiesta...'\"\n                ></textarea>\n              </div>\n            </div>\n\n            <div class=\"flex justify-end space-x-3 mt-6\">\n              <button \n                type=\"button\" \n                @click=\"closeModal\"\n                class=\"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md\"\n              >\n                Annulla\n              </button>\n              <button \n                type=\"submit\"\n                :disabled=\"saving\"\n                class=\"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50\"\n              >\n                {{ saving ? 'Invio...' : 'Invia Richiesta' }}\n              </button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted, watch } from 'vue'\nimport { useTimesheetStore } from '@/stores/timesheet'\nimport { useAuthStore } from '@/stores/auth'\nimport { formatDate, getStatusClass, getStatusText } from '@/utils/timesheet'\nimport HeroIcon from '@/components/icons/HeroIcon.vue'\n\nconst timesheetStore = useTimesheetStore()\nconst authStore = useAuthStore()\n\n// Local state\nconst requests = ref([])\nconst loading = ref(false)\nconst showModal = ref(false)\nconst saving = ref(false)\nconst requestType = ref('')\nconst timeOffQuotas = ref({\n  vacation: { remaining: 0, total: 0 },\n  leave: { used: 0, total: 0 },\n  smartworking: { used: 0 }\n})\n\n// Filters\nconst selectedType = ref('')\nconst selectedStatus = ref('')\nconst dateFrom = ref('')\n\n// Form data\nconst formData = ref({\n  start_date: '',\n  end_date: '',\n  notes: ''\n})\n\n// Computed properties from store\nconst error = computed(() => timesheetStore.error)\n\nconst remainingVacationDays = computed(() => {\n  return timeOffQuotas.value.vacation?.remaining || 0\n})\n\nconst usedLeaveDays = computed(() => {\n  return timeOffQuotas.value.leave?.used || 0\n})\n\nconst totalLeaveDays = computed(() => {\n  return timeOffQuotas.value.leave?.total || 0\n})\n\nconst smartWorkingDays = computed(() => {\n  return timeOffQuotas.value.smartworking?.used || 0\n})\n\nconst pendingRequests = computed(() => {\n  if (!Array.isArray(requests.value)) return 0\n  return requests.value.filter(r => r.status === 'pending').length\n})\n\n// Methods\nconst loadQuotas = async () => {\n  try {\n    const quotas = await timesheetStore.loadTimeOffQuotas()\n    timeOffQuotas.value = quotas\n    console.log('Time-off quotas loaded:', quotas)\n  } catch (err) {\n    console.error('Failed to load time-off quotas:', err)\n    // Imposta valori predefiniti in caso di errore\n    timeOffQuotas.value = {\n      vacation: { remaining: 0, total: 0 },\n      leave: { used: 0, total: 0 },\n      smartworking: { used: 0 }\n    }\n    // Imposta un messaggio di errore che non blocchi l'esperienza utente\n    timesheetStore.setError('Impossibile caricare i dati delle quote di ferie e permessi. Verranno mostrati valori predefiniti.')\n  }\n}\n\nconst loadRequests = async () => {\n  loading.value = true\n\n  try {\n    const filters = {\n      request_type: selectedType.value,\n      status: selectedStatus.value,\n      start_date: dateFrom.value\n    }\n\n    requests.value = await timesheetStore.loadTimeOffRequests(filters)\n  } finally {\n    loading.value = false\n  }\n}\n\nconst showRequestModal = (type) => {\n  requestType.value = type\n  formData.value = {\n    start_date: '',\n    end_date: '',\n    notes: ''\n  }\n  showModal.value = true\n}\n\nconst closeModal = () => {\n  showModal.value = false\n  requestType.value = ''\n}\n\nconst getModalTitle = () => {\n  switch (requestType.value) {\n    case 'vacation':\n      return 'Richiesta Ferie'\n    case 'leave':\n      return 'Richiesta Permesso'\n    case 'smartworking':  // <-- Corretto da 'smart_working' a 'smartworking'\n      return 'Richiesta Smart Working'\n    default:\n      return 'Nuova Richiesta'\n  }\n}\n\nconst submitRequest = async () => {\n  saving.value = true\n\n  try {\n    const data = {\n      type: requestType.value,  // Campo DB: 'type'\n      ...formData.value\n    }\n\n    const success = await timesheetStore.createTimeOffRequest(data)\n    if (success) {\n      await loadRequests()\n      await loadQuotas()\n      closeModal()\n    } else {\n      // Chiudi il modale anche in caso di errore per mostrare l'alert all'utente\n      closeModal()\n      // Scorri la pagina verso l'alto per assicurarsi che l'utente veda l'errore\n      window.scrollTo({ top: 0, behavior: 'smooth' })\n    }\n  } finally {\n    saving.value = false\n  }\n}\n\nconst editRequest = (request) => {\n  requestType.value = request.request_type\n  formData.value = {\n    start_date: request.start_date,\n    end_date: request.end_date,\n    notes: request.notes || ''\n  }\n  showModal.value = true\n}\n\nconst deleteRequest = async (requestId) => {\n  if (!confirm('Sei sicuro di voler eliminare questa richiesta?')) return\n\n  const success = await timesheetStore.deleteTimeOffRequest(requestId)\n  if (success) {\n    await loadRequests()\n    await loadQuotas()\n  }\n}\n\nconst formatPeriod = (request) => {\n  return `${formatDate(request.start_date)} - ${formatDate(request.end_date)}`\n}\n\nconst formatDuration = (request) => {\n  return `${request.duration_days || 0} giorni`\n}\n\nconst getTypeClass = (type) => {\n  switch (type) {\n    case 'vacation':\n      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'\n    case 'leave':\n      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'\n    case 'smartworking':\n      return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'\n    default:\n      return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'\n  }\n}\n\nconst getTypeText = (type) => {\n  switch (type) {\n    case 'vacation':\n      return 'Ferie'\n    case 'leave':\n      return 'Permesso'\n    case 'smartworking':\n      return 'Smart Working'\n    default:\n      return 'Altro'\n  }\n}\n\nconst clearError = () => {\n  timesheetStore.clearError()\n}\n\n// Lifecycle\nonMounted(() => {\n  loadRequests()\n  loadQuotas()\n})\nwatch([selectedType, selectedStatus, dateFrom], () => {\n  loadRequests()\n})\n</script>\n"}