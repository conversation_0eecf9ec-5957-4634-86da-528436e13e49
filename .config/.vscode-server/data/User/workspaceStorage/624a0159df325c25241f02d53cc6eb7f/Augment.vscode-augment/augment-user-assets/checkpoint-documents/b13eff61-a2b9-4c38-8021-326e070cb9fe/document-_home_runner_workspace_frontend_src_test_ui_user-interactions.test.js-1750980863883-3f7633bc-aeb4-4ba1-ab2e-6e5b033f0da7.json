{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/ui/user-interactions.test.js"}, "modifiedCode": "import { describe, it, expect, beforeEach, vi } from 'vitest'\nimport { mount } from '@vue/test-utils'\nimport { createPinia } from 'pinia'\nimport { nextTick } from 'vue'\nimport ProjectEdit from '@/views/projects/ProjectEdit.vue'\nimport Dashboard from '@/views/Dashboard.vue'\n\n// Mock router\nconst mockRouter = {\n  push: vi.fn(),\n  replace: vi.fn(),\n  go: vi.fn()\n}\n\nvi.mock('vue-router', () => ({\n  useRouter: () => mockRouter,\n  useRoute: () => ({ params: { id: '1' } })\n}))\n\ndescribe('User Interface Interactions', () => {\n  let pinia\n\n  beforeEach(() => {\n    pinia = createPinia()\n    global.fetch = vi.fn()\n    vi.clearAllMocks()\n  })\n\n  describe('Form Interactions', () => {\n    it('should handle form input changes', async () => {\n      const wrapper = mount(ProjectEdit, {\n        global: {\n          plugins: [pinia],\n          mocks: { $router: mockRouter }\n        }\n      })\n\n      // Test text input\n      const nameInput = wrapper.find('[data-testid=\"project-name\"]')\n      await nameInput.setValue('Test Project Name')\n      \n      expect(wrapper.vm.form.name).toBe('Test Project Name')\n\n      // Test number input\n      const budgetInput = wrapper.find('[data-testid=\"project-budget\"]')\n      await budgetInput.setValue('50000')\n      \n      expect(wrapper.vm.form.budget).toBe(50000)\n\n      // Test select dropdown\n      const statusSelect = wrapper.find('[data-testid=\"project-status\"]')\n      await statusSelect.setValue('active')\n      \n      expect(wrapper.vm.form.status).toBe('active')\n\n      // Test textarea\n      const descriptionTextarea = wrapper.find('[data-testid=\"project-description\"]')\n      await descriptionTextarea.setValue('Project description text')\n      \n      expect(wrapper.vm.form.description).toBe('Project description text')\n    })\n\n    it('should validate form inputs in real-time', async () => {\n      const wrapper = mount(ProjectEdit, {\n        global: {\n          plugins: [pinia],\n          mocks: { $router: mockRouter }\n        }\n      })\n\n      // Test required field validation\n      const nameInput = wrapper.find('[data-testid=\"project-name\"]')\n      await nameInput.setValue('')\n      await nameInput.trigger('blur')\n\n      expect(wrapper.find('[data-testid=\"name-error\"]').text()).toContain('required')\n\n      // Test numeric validation\n      const budgetInput = wrapper.find('[data-testid=\"project-budget\"]')\n      await budgetInput.setValue('-1000')\n      await budgetInput.trigger('blur')\n\n      expect(wrapper.find('[data-testid=\"budget-error\"]').text()).toContain('positive')\n\n      // Test email validation\n      const emailInput = wrapper.find('[data-testid=\"contact-email\"]')\n      await emailInput.setValue('invalid-email')\n      await emailInput.trigger('blur')\n\n      expect(wrapper.find('[data-testid=\"email-error\"]').text()).toContain('valid email')\n    })\n\n    it('should handle form submission states', async () => {\n      fetch.mockResolvedValueOnce({\n        ok: true,\n        json: () => Promise.resolve({ success: true, data: { id: 1 } })\n      })\n\n      const wrapper = mount(ProjectEdit, {\n        global: {\n          plugins: [pinia],\n          mocks: { $router: mockRouter }\n        }\n      })\n\n      // Fill valid form\n      await wrapper.find('[data-testid=\"project-name\"]').setValue('Valid Project')\n      await wrapper.find('[data-testid=\"project-budget\"]').setValue('25000')\n\n      // Submit form\n      const submitButton = wrapper.find('[data-testid=\"save-button\"]')\n      await submitButton.trigger('click')\n\n      // Check loading state\n      expect(wrapper.vm.saving).toBe(true)\n      expect(submitButton.attributes('disabled')).toBeDefined()\n      expect(submitButton.text()).toContain('Saving...')\n\n      await nextTick()\n\n      // Check success state\n      expect(wrapper.vm.saving).toBe(false)\n      expect(wrapper.find('[data-testid=\"success-message\"]').exists()).toBe(true)\n    })\n  })\n\n  describe('Navigation Interactions', () => {\n    it('should handle tab navigation', async () => {\n      const wrapper = mount(Dashboard, {\n        global: {\n          plugins: [pinia],\n          mocks: { $router: mockRouter }\n        }\n      })\n\n      // Initial tab should be active\n      expect(wrapper.vm.activeTab).toBe('overview')\n      expect(wrapper.find('[data-testid=\"overview-tab\"]').classes()).toContain('active')\n\n      // Click projects tab\n      await wrapper.find('[data-testid=\"projects-tab\"]').trigger('click')\n      \n      expect(wrapper.vm.activeTab).toBe('projects')\n      expect(wrapper.find('[data-testid=\"projects-tab\"]').classes()).toContain('active')\n      expect(wrapper.find('[data-testid=\"overview-tab\"]').classes()).not.toContain('active')\n\n      // Verify URL hash update\n      expect(window.location.hash).toBe('#projects')\n    })\n\n    it('should handle breadcrumb navigation', async () => {\n      const wrapper = mount(ProjectEdit, {\n        global: {\n          plugins: [pinia],\n          mocks: { $router: mockRouter }\n        }\n      })\n\n      // Click breadcrumb links\n      await wrapper.find('[data-testid=\"breadcrumb-projects\"]').trigger('click')\n      \n      expect(mockRouter.push).toHaveBeenCalledWith('/app/projects')\n\n      await wrapper.find('[data-testid=\"breadcrumb-home\"]').trigger('click')\n      \n      expect(mockRouter.push).toHaveBeenCalledWith('/app/dashboard')\n    })\n\n    it('should handle back button functionality', async () => {\n      const wrapper = mount(ProjectEdit, {\n        global: {\n          plugins: [pinia],\n          mocks: { $router: mockRouter }\n        }\n      })\n\n      await wrapper.find('[data-testid=\"back-button\"]').trigger('click')\n      \n      expect(mockRouter.go).toHaveBeenCalledWith(-1)\n    })\n  })\n\n  describe('Modal Interactions', () => {\n    it('should open and close modals', async () => {\n      const wrapper = mount(ProjectEdit, {\n        global: {\n          plugins: [pinia],\n          mocks: { $router: mockRouter }\n        }\n      })\n\n      // Modal should be closed initially\n      expect(wrapper.find('[data-testid=\"delete-modal\"]').exists()).toBe(false)\n\n      // Open modal\n      await wrapper.find('[data-testid=\"delete-button\"]').trigger('click')\n      \n      expect(wrapper.vm.showDeleteModal).toBe(true)\n      expect(wrapper.find('[data-testid=\"delete-modal\"]').exists()).toBe(true)\n\n      // Close modal with cancel\n      await wrapper.find('[data-testid=\"cancel-delete\"]').trigger('click')\n      \n      expect(wrapper.vm.showDeleteModal).toBe(false)\n\n      // Close modal with overlay click\n      await wrapper.find('[data-testid=\"delete-button\"]').trigger('click')\n      await wrapper.find('[data-testid=\"modal-overlay\"]').trigger('click')\n      \n      expect(wrapper.vm.showDeleteModal).toBe(false)\n\n      // Close modal with escape key\n      await wrapper.find('[data-testid=\"delete-button\"]').trigger('click')\n      await wrapper.trigger('keydown', { key: 'Escape' })\n      \n      expect(wrapper.vm.showDeleteModal).toBe(false)\n    })\n\n    it('should handle modal form submissions', async () => {\n      fetch.mockResolvedValueOnce({\n        ok: true,\n        json: () => Promise.resolve({ success: true })\n      })\n\n      const wrapper = mount(ProjectEdit, {\n        global: {\n          plugins: [pinia],\n          mocks: { $router: mockRouter }\n        }\n      })\n\n      // Open delete modal\n      await wrapper.find('[data-testid=\"delete-button\"]').trigger('click')\n\n      // Confirm deletion\n      await wrapper.find('[data-testid=\"confirm-delete\"]').trigger('click')\n\n      expect(fetch).toHaveBeenCalledWith('/api/projects/1', {\n        method: 'DELETE'\n      })\n\n      expect(mockRouter.push).toHaveBeenCalledWith('/app/projects')\n    })\n  })\n\n  describe('Dynamic UI Updates', () => {\n    it('should update UI based on data changes', async () => {\n      const wrapper = mount(Dashboard, {\n        global: {\n          plugins: [pinia],\n          mocks: { $router: mockRouter }\n        }\n      })\n\n      // Initially no data\n      expect(wrapper.find('[data-testid=\"no-data\"]').exists()).toBe(true)\n\n      // Update data\n      wrapper.vm.dashboardData = {\n        totalProjects: 5,\n        activeProjects: 3,\n        completedTasks: 25\n      }\n\n      await nextTick()\n\n      // UI should update\n      expect(wrapper.find('[data-testid=\"no-data\"]').exists()).toBe(false)\n      expect(wrapper.text()).toContain('5')\n      expect(wrapper.text()).toContain('3')\n      expect(wrapper.text()).toContain('25')\n    })\n\n    it('should handle conditional rendering', async () => {\n      const wrapper = mount(ProjectEdit, {\n        global: {\n          plugins: [pinia],\n          mocks: { $router: mockRouter }\n        }\n      })\n\n      // Admin-only features should be hidden for regular users\n      wrapper.vm.user = { role: 'employee' }\n      await nextTick()\n\n      expect(wrapper.find('[data-testid=\"admin-section\"]').exists()).toBe(false)\n\n      // Admin features should show for admin users\n      wrapper.vm.user = { role: 'admin' }\n      await nextTick()\n\n      expect(wrapper.find('[data-testid=\"admin-section\"]').exists()).toBe(true)\n    })\n\n    it('should handle list updates', async () => {\n      const wrapper = mount(Dashboard, {\n        global: {\n          plugins: [pinia],\n          mocks: { $router: mockRouter }\n        }\n      })\n\n      // Add items to list\n      wrapper.vm.recentActivities = [\n        { id: 1, message: 'Project created', timestamp: '2025-01-01' },\n        { id: 2, message: 'Task completed', timestamp: '2025-01-02' }\n      ]\n\n      await nextTick()\n\n      expect(wrapper.findAll('[data-testid=\"activity-item\"]')).toHaveLength(2)\n\n      // Remove item\n      wrapper.vm.recentActivities.splice(0, 1)\n      await nextTick()\n\n      expect(wrapper.findAll('[data-testid=\"activity-item\"]')).toHaveLength(1)\n    })\n  })\n\n  describe('Accessibility Interactions', () => {\n    it('should handle keyboard navigation', async () => {\n      const wrapper = mount(ProjectEdit, {\n        global: {\n          plugins: [pinia],\n          mocks: { $router: mockRouter }\n        }\n      })\n\n      // Tab navigation\n      const firstInput = wrapper.find('[data-testid=\"project-name\"]')\n      await firstInput.trigger('keydown', { key: 'Tab' })\n\n      expect(document.activeElement).toBe(wrapper.find('[data-testid=\"project-description\"]').element)\n\n      // Enter key submission\n      await wrapper.find('[data-testid=\"save-button\"]').trigger('keydown', { key: 'Enter' })\n      \n      // Should trigger form submission\n      expect(wrapper.emitted('submit')).toBeTruthy()\n    })\n\n    it('should handle focus management', async () => {\n      const wrapper = mount(ProjectEdit, {\n        global: {\n          plugins: [pinia],\n          mocks: { $router: mockRouter }\n        }\n      })\n\n      // Focus should move to first error field on validation failure\n      await wrapper.find('[data-testid=\"save-button\"]').trigger('click')\n      \n      expect(document.activeElement).toBe(wrapper.find('[data-testid=\"project-name\"]').element)\n    })\n  })\n})\n"}