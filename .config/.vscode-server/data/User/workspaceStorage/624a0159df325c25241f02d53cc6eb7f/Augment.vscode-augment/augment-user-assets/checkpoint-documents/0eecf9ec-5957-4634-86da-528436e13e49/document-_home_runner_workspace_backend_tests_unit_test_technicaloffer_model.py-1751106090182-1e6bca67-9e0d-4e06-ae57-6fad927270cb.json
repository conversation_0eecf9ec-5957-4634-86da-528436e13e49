{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_technicaloffer_model.py"}, "modifiedCode": "\"\"\"Unit tests for TechnicalOffer model.\"\"\"\nimport pytest\nfrom models import <PERSON>Offer, User\nfrom extensions import db\n\nclass TestTechnicalOfferModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n\n    def test_technicaloffer_creation_basic(self):\n        offer = TechnicalOffer(\n            title='Test Technical Offer',\n            description='This is a test technical offer',\n            technology='Python',\n            created_by=self.user.id\n        )\n        db.session.add(offer)\n        db.session.commit()\n        \n        assert offer.id is not None\n        assert offer.title == 'Test Technical Offer'\n        assert offer.technology == 'Python'\n\n    def test_technicaloffer_deletion(self):\n        offer = TechnicalOffer(title='To Delete', description='Delete me', created_by=self.user.id)\n        db.session.add(offer)\n        db.session.commit()\n        offer_id = offer.id\n        \n        db.session.delete(offer)\n        db.session.commit()\n        \n        deleted = TechnicalOffer.query.get(offer_id)\n        assert deleted is None\n"}