{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_project_model.py"}, "originalCode": "\"\"\"Unit tests for Project model.\"\"\"\nimport pytest\nfrom datetime import date\nfrom models import Project, User\nfrom extensions import db\n\nclass TestProjectModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n\n    def test_project_creation_basic(self):\n        project = Project(\n            name='Test Project',\n            description='This is a test project'\n            # Rimosso manager_id - non esiste nel modello\n        )\n        db.session.add(project)\n        db.session.commit()\n\n        assert project.id is not None\n        assert project.name == 'Test Project'\n        assert project.description == 'This is a test project'\n\n    def test_project_dates(self):\n        project = Project(\n            name='Date Project',\n            description='Project with dates',\n            start_date=date(2024, 1, 1),\n            end_date=date(2024, 12, 31),\n            manager_id=self.user.id\n        )\n        db.session.add(project)\n        db.session.commit()\n        \n        assert project.start_date == date(2024, 1, 1)\n        assert project.end_date == date(2024, 12, 31)\n\n    def test_project_deletion(self):\n        project = Project(name='To Delete', description='Delete me', manager_id=self.user.id)\n        db.session.add(project)\n        db.session.commit()\n        project_id = project.id\n        \n        db.session.delete(project)\n        db.session.commit()\n        \n        deleted = Project.query.get(project_id)\n        assert deleted is None\n", "modifiedCode": "\"\"\"Unit tests for Project model.\"\"\"\nimport pytest\nfrom datetime import date\nfrom models import Project, User\nfrom extensions import db\n\nclass TestProjectModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n\n    def test_project_creation_basic(self):\n        project = Project(\n            name='Test Project',\n            description='This is a test project'\n            # Rimosso manager_id - non esiste nel modello\n        )\n        db.session.add(project)\n        db.session.commit()\n\n        assert project.id is not None\n        assert project.name == 'Test Project'\n        assert project.description == 'This is a test project'\n\n    def test_project_dates(self):\n        project = Project(\n            name='Date Project',\n            description='Project with dates',\n            start_date=date(2024, 1, 1),\n            end_date=date(2024, 12, 31)\n            # Rimosso manager_id - non esiste nel modello\n        )\n        db.session.add(project)\n        db.session.commit()\n        \n        assert project.start_date == date(2024, 1, 1)\n        assert project.end_date == date(2024, 12, 31)\n\n    def test_project_deletion(self):\n        project = Project(name='To Delete', description='Delete me', manager_id=self.user.id)\n        db.session.add(project)\n        db.session.commit()\n        project_id = project.id\n        \n        db.session.delete(project)\n        db.session.commit()\n        \n        deleted = Project.query.get(project_id)\n        assert deleted is None\n"}