{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/testing/frontend-testing-guide.md"}, "modifiedCode": "# 🎨 Frontend Testing Guide\n\nGuida completa per il testing del frontend Vue.js con Vitest, Vue Test Utils e Cypress.\n\n## 📋 Indice\n\n- [Setup e Configurazione](#setup-e-configurazione)\n- [Component Testing](#component-testing)\n- [Integration Testing](#integration-testing)\n- [UI Interaction Testing](#ui-interaction-testing)\n- [End-to-End Testing](#end-to-end-testing)\n- [Mock Strategies](#mock-strategies)\n- [Best Practices](#best-practices)\n\n## ⚙️ Setup e Configurazione\n\n### Struttura Directory\n\n```\nfrontend/src/test/\n├── setup.js                    # Setup globale Vitest\n├── components/\n│   ├── ProjectTeam.test.js     # Test componente team\n│   ├── Dashboard.test.js       # Test dashboard\n│   └── ProjectEdit.test.js     # Test form progetto\n├── integration/\n│   ├── project-api-integration.test.js  # Test API integration\n│   ├── store-integration.test.js        # Test store integration\n│   └── routing-integration.test.js      # Test routing\n├── ui/\n│   ├── user-interactions.test.js        # Test interazioni utente\n│   ├── form-validation.test.js          # Test validazione form\n│   └── navigation.test.js               # Test navigazione\n├── mocks/\n│   ├── api-handlers.js         # Mock API con MSW\n│   ├── server.js              # Setup MSW server\n│   └── store-mocks.js         # Mock store Pinia\n├── fixtures/\n│   ├── projects.json          # Dati progetti di test\n│   ├── users.json            # Dati utenti di test\n│   └── dashboard.json        # Dati dashboard di test\n└── utils/\n    └── test-helpers.js       # Utility per test\n```\n\n### Configurazione Vitest\n\n```javascript\n// vitest.config.js\nimport { defineConfig } from 'vitest/config'\nimport vue from '@vitejs/plugin-vue'\nimport { fileURLToPath, URL } from 'node:url'\n\nexport default defineConfig({\n  plugins: [vue()],\n  test: {\n    environment: 'jsdom',\n    globals: true,\n    setupFiles: ['./src/test/setup.js'],\n    coverage: {\n      provider: 'v8',\n      reporter: ['text', 'json', 'html'],\n      exclude: [\n        'node_modules/',\n        'src/test/',\n        'cypress/',\n        'dist/'\n      ],\n      thresholds: {\n        global: {\n          branches: 70,\n          functions: 70,\n          lines: 70,\n          statements: 70\n        }\n      }\n    }\n  },\n  resolve: {\n    alias: {\n      '@': fileURLToPath(new URL('./src', import.meta.url))\n    }\n  }\n})\n```\n\n### Setup Globale\n\n```javascript\n// src/test/setup.js\nimport { vi } from 'vitest'\nimport { config } from '@vue/test-utils'\nimport { createPinia } from 'pinia'\n\n// Mock global objects\nglobal.ResizeObserver = vi.fn(() => ({\n  observe: vi.fn(),\n  unobserve: vi.fn(),\n  disconnect: vi.fn(),\n}))\n\nglobal.fetch = vi.fn()\n\n// Configure Vue Test Utils\nconfig.global.plugins = [createPinia()]\nconfig.global.stubs = {\n  'router-link': { template: '<a><slot /></a>' },\n  'router-view': { template: '<div><slot /></div>' },\n  'HeroIcon': { template: '<svg><slot /></svg>' }\n}\n\n// Global test utilities\nglobal.testUtils = {\n  waitForUpdate: async (wrapper) => {\n    await wrapper.vm.$nextTick()\n    await new Promise(resolve => setTimeout(resolve, 0))\n  },\n  \n  fillForm: async (wrapper, formData) => {\n    for (const [field, value] of Object.entries(formData)) {\n      const input = wrapper.find(`[data-testid=\"${field}\"]`)\n      if (input.exists()) {\n        await input.setValue(value)\n      }\n    }\n  },\n  \n  mockApiResponse: (data, status = 200) => ({\n    ok: status >= 200 && status < 300,\n    status,\n    json: () => Promise.resolve(data)\n  })\n}\n```\n\n## 🧩 Component Testing\n\n### Test Componenti Base\n\n```javascript\n// tests/components/ProjectTeam.test.js\nimport { describe, it, expect, beforeEach, vi } from 'vitest'\nimport { mount } from '@vue/test-utils'\nimport { createPinia } from 'pinia'\nimport ProjectTeam from '@/views/projects/components/ProjectTeam.vue'\n\ndescribe('ProjectTeam Component', () => {\n  let wrapper\n  let mockProject\n\n  beforeEach(() => {\n    mockProject = {\n      id: 1,\n      name: 'Test Project',\n      team_members: [\n        {\n          id: 1,\n          full_name: 'John Doe',\n          role: 'Project Manager',\n          allocation_percentage: 50,\n          hours_worked: 40\n        },\n        {\n          id: 2,\n          full_name: 'Jane Smith',\n          role: 'Developer',\n          allocation_percentage: 100,\n          hours_worked: 80\n        }\n      ]\n    }\n    \n    global.fetch = vi.fn()\n  })\n\n  describe('Rendering', () => {\n    it('should render team members correctly', () => {\n      wrapper = mount(ProjectTeam, {\n        props: { project: mockProject },\n        global: { plugins: [createPinia()] }\n      })\n\n      expect(wrapper.text()).toContain('John Doe')\n      expect(wrapper.text()).toContain('Jane Smith')\n      expect(wrapper.text()).toContain('Project Manager')\n      expect(wrapper.text()).toContain('Developer')\n    })\n\n    it('should display team statistics', () => {\n      wrapper = mount(ProjectTeam, {\n        props: { project: mockProject },\n        global: { plugins: [createPinia()] }\n      })\n\n      // Total hours: 40 + 80 = 120\n      expect(wrapper.vm.totalHoursWorked).toBe(120)\n      // Average: 120 / 2 = 60\n      expect(wrapper.vm.averageHoursPerMember).toBe(60)\n      // Active members: 2\n      expect(wrapper.vm.activeMembersCount).toBe(2)\n    })\n\n    it('should show empty state when no team members', () => {\n      const emptyProject = { ...mockProject, team_members: [] }\n      \n      wrapper = mount(ProjectTeam, {\n        props: { project: emptyProject },\n        global: { plugins: [createPinia()] }\n      })\n\n      expect(wrapper.find('[data-testid=\"empty-team-state\"]').exists()).toBe(true)\n      expect(wrapper.text()).toContain('No team members')\n    })\n  })\n\n  describe('User Interactions', () => {\n    beforeEach(() => {\n      wrapper = mount(ProjectTeam, {\n        props: { project: mockProject },\n        global: { plugins: [createPinia()] }\n      })\n    })\n\n    it('should open add member modal', async () => {\n      await wrapper.find('[data-testid=\"add-member-button\"]').trigger('click')\n\n      expect(wrapper.vm.showAddMemberModal).toBe(true)\n      expect(wrapper.find('[data-testid=\"add-member-modal\"]').exists()).toBe(true)\n    })\n\n    it('should handle form submission', async () => {\n      fetch.mockResolvedValueOnce({\n        ok: true,\n        json: () => Promise.resolve({ success: true })\n      })\n\n      wrapper.vm.newMemberForm = {\n        user_id: '3',\n        role: 'QA Tester',\n        allocation_percentage: 75\n      }\n\n      await wrapper.vm.addMember()\n\n      expect(fetch).toHaveBeenCalledWith(`/api/projects/${mockProject.id}/team`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'X-CSRFToken': expect.any(String)\n        },\n        body: JSON.stringify({\n          user_id: '3',\n          role: 'QA Tester',\n          allocation_percentage: 75\n        })\n      })\n    })\n\n    it('should handle API errors', async () => {\n      fetch.mockRejectedValueOnce(new Error('Network error'))\n      const alertSpy = vi.spyOn(window, 'alert').mockImplementation(() => {})\n\n      await wrapper.vm.addMember()\n\n      expect(alertSpy).toHaveBeenCalledWith('Errore nell\\'aggiunta del membro')\n      alertSpy.mockRestore()\n    })\n  })\n\n  describe('Props and Events', () => {\n    it('should handle loading prop', () => {\n      wrapper = mount(ProjectTeam, {\n        props: { \n          project: mockProject,\n          loading: true \n        },\n        global: { plugins: [createPinia()] }\n      })\n\n      expect(wrapper.find('[data-testid=\"loading-spinner\"]').exists()).toBe(true)\n    })\n\n    it('should emit refresh event', async () => {\n      wrapper = mount(ProjectTeam, {\n        props: { project: mockProject },\n        global: { plugins: [createPinia()] }\n      })\n\n      fetch.mockResolvedValueOnce({\n        ok: true,\n        json: () => Promise.resolve({ success: true })\n      })\n\n      await wrapper.vm.addMember()\n\n      expect(wrapper.emitted('refresh')).toBeTruthy()\n    })\n  })\n})\n```\n\n### Test Form Components\n\n```javascript\n// tests/components/ProjectEdit.test.js\nimport { describe, it, expect, beforeEach, vi } from 'vitest'\nimport { mount } from '@vue/test-utils'\nimport ProjectEdit from '@/views/projects/ProjectEdit.vue'\n\ndescribe('ProjectEdit Component', () => {\n  let wrapper\n\n  beforeEach(() => {\n    wrapper = mount(ProjectEdit, {\n      global: {\n        plugins: [createPinia()],\n        mocks: {\n          $router: { push: vi.fn() },\n          $route: { params: { id: 'new' } }\n        }\n      }\n    })\n  })\n\n  describe('Form Validation', () => {\n    it('should validate required fields', async () => {\n      // Submit empty form\n      await wrapper.find('[data-testid=\"save-button\"]').trigger('click')\n\n      expect(wrapper.find('[data-testid=\"name-error\"]').text()).toContain('required')\n      expect(wrapper.find('[data-testid=\"budget-error\"]').text()).toContain('required')\n    })\n\n    it('should validate field formats', async () => {\n      await wrapper.find('[data-testid=\"project-budget\"]').setValue('-1000')\n      await wrapper.find('[data-testid=\"project-budget\"]').trigger('blur')\n\n      expect(wrapper.find('[data-testid=\"budget-error\"]').text()).toContain('positive')\n    })\n\n    it('should validate date ranges', async () => {\n      await wrapper.find('[data-testid=\"project-start-date\"]').setValue('2025-12-31')\n      await wrapper.find('[data-testid=\"project-end-date\"]').setValue('2025-01-01')\n      await wrapper.find('[data-testid=\"project-end-date\"]').trigger('blur')\n\n      expect(wrapper.find('[data-testid=\"end-date-error\"]').text()).toContain('after start date')\n    })\n  })\n\n  describe('Form Submission', () => {\n    it('should submit valid form', async () => {\n      fetch.mockResolvedValueOnce({\n        ok: true,\n        json: () => Promise.resolve({ success: true, data: { id: 1 } })\n      })\n\n      await global.testUtils.fillForm(wrapper, {\n        'project-name': 'Test Project',\n        'project-description': 'Test description',\n        'project-budget': '25000'\n      })\n\n      await wrapper.find('[data-testid=\"save-button\"]').trigger('click')\n\n      expect(fetch).toHaveBeenCalledWith('/api/projects', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          name: 'Test Project',\n          description: 'Test description',\n          budget: 25000\n        })\n      })\n    })\n\n    it('should handle submission errors', async () => {\n      fetch.mockResolvedValueOnce({\n        ok: false,\n        status: 400,\n        json: () => Promise.resolve({\n          success: false,\n          errors: { name: ['Name already exists'] }\n        })\n      })\n\n      await wrapper.find('[data-testid=\"save-button\"]').trigger('click')\n\n      expect(wrapper.find('[data-testid=\"name-error\"]').text()).toContain('already exists')\n    })\n  })\n})\n```\n\n## 🔗 Integration Testing\n\n### Test Store Integration\n\n```javascript\n// tests/integration/store-integration.test.js\nimport { describe, it, expect, beforeEach, vi } from 'vitest'\nimport { setActivePinia, createPinia } from 'pinia'\nimport { useProjectsStore } from '@/stores/projects'\n\ndescribe('Projects Store Integration', () => {\n  let projectsStore\n\n  beforeEach(() => {\n    setActivePinia(createPinia())\n    projectsStore = useProjectsStore()\n    global.fetch = vi.fn()\n  })\n\n  describe('API Integration', () => {\n    it('should fetch projects from API', async () => {\n      const mockProjects = [\n        { id: 1, name: 'Project 1' },\n        { id: 2, name: 'Project 2' }\n      ]\n\n      fetch.mockResolvedValueOnce({\n        ok: true,\n        json: () => Promise.resolve({\n          success: true,\n          data: { projects: mockProjects }\n        })\n      })\n\n      await projectsStore.fetchProjects()\n\n      expect(projectsStore.projects).toEqual(mockProjects)\n      expect(projectsStore.loading).toBe(false)\n      expect(projectsStore.error).toBeNull()\n    })\n\n    it('should handle API errors', async () => {\n      fetch.mockRejectedValueOnce(new Error('Network error'))\n\n      await projectsStore.fetchProjects()\n\n      expect(projectsStore.projects).toEqual([])\n      expect(projectsStore.loading).toBe(false)\n      expect(projectsStore.error).toBeTruthy()\n    })\n\n    it('should create project via API', async () => {\n      const newProject = { name: 'New Project', budget: 10000 }\n\n      fetch.mockResolvedValueOnce({\n        ok: true,\n        json: () => Promise.resolve({\n          success: true,\n          data: { ...newProject, id: 3 }\n        })\n      })\n\n      const result = await projectsStore.createProject(newProject)\n\n      expect(result.success).toBe(true)\n      expect(fetch).toHaveBeenCalledWith('/api/projects', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(newProject)\n      })\n    })\n  })\n\n  describe('State Management', () => {\n    it('should update loading states correctly', async () => {\n      expect(projectsStore.loading).toBe(false)\n\n      const fetchPromise = projectsStore.fetchProjects()\n      expect(projectsStore.loading).toBe(true)\n\n      await fetchPromise\n      expect(projectsStore.loading).toBe(false)\n    })\n\n    it('should filter projects correctly', () => {\n      projectsStore.projects = [\n        { id: 1, name: 'Alpha Project', status: 'active' },\n        { id: 2, name: 'Beta Project', status: 'planning' },\n        { id: 3, name: 'Gamma Task', status: 'active' }\n      ]\n\n      // Filter by status\n      const activeProjects = projectsStore.getProjectsByStatus('active')\n      expect(activeProjects).toHaveLength(2)\n\n      // Search by name\n      const searchResults = projectsStore.searchProjects('Project')\n      expect(searchResults).toHaveLength(2)\n      expect(searchResults.every(p => p.name.includes('Project'))).toBe(true)\n    })\n  })\n})\n```\n\n## 🖱️ UI Interaction Testing\n\n### Test User Interactions\n\n```javascript\n// tests/ui/user-interactions.test.js\nimport { describe, it, expect, beforeEach, vi } from 'vitest'\nimport { mount } from '@vue/test-utils'\nimport { nextTick } from 'vue'\nimport Dashboard from '@/views/Dashboard.vue'\n\ndescribe('User Interface Interactions', () => {\n  let wrapper\n\n  beforeEach(() => {\n    wrapper = mount(Dashboard, {\n      global: {\n        plugins: [createPinia()],\n        mocks: { $router: { push: vi.fn() } }\n      }\n    })\n  })\n\n  describe('Navigation', () => {\n    it('should handle tab navigation', async () => {\n      expect(wrapper.vm.activeTab).toBe('overview')\n\n      await wrapper.find('[data-testid=\"projects-tab\"]').trigger('click')\n\n      expect(wrapper.vm.activeTab).toBe('projects')\n      expect(wrapper.find('[data-testid=\"projects-tab\"]').classes()).toContain('active')\n    })\n\n    it('should update URL hash on tab change', async () => {\n      await wrapper.find('[data-testid=\"timesheet-tab\"]').trigger('click')\n\n      expect(window.location.hash).toBe('#timesheet')\n    })\n  })\n\n  describe('Modal Interactions', () => {\n    it('should open and close modals', async () => {\n      expect(wrapper.vm.showModal).toBe(false)\n\n      await wrapper.find('[data-testid=\"open-modal-button\"]').trigger('click')\n      expect(wrapper.vm.showModal).toBe(true)\n\n      await wrapper.find('[data-testid=\"close-modal-button\"]').trigger('click')\n      expect(wrapper.vm.showModal).toBe(false)\n    })\n\n    it('should close modal on escape key', async () => {\n      wrapper.vm.showModal = true\n      await nextTick()\n\n      await wrapper.trigger('keydown', { key: 'Escape' })\n      expect(wrapper.vm.showModal).toBe(false)\n    })\n  })\n\n  describe('Dynamic Updates', () => {\n    it('should update UI when data changes', async () => {\n      expect(wrapper.find('[data-testid=\"no-data\"]').exists()).toBe(true)\n\n      wrapper.vm.dashboardData = {\n        totalProjects: 5,\n        activeProjects: 3\n      }\n      await nextTick()\n\n      expect(wrapper.find('[data-testid=\"no-data\"]').exists()).toBe(false)\n      expect(wrapper.text()).toContain('5')\n      expect(wrapper.text()).toContain('3')\n    })\n  })\n})\n```\n\n## 🌐 End-to-End Testing\n\n### Test Workflow Completi\n\n```javascript\n// cypress/e2e/project-workflows.cy.js\ndescribe('Project Management Workflows', () => {\n  beforeEach(() => {\n    cy.login('admin', 'password')\n    cy.intercept('GET', '/api/projects*', { fixture: 'projects.json' }).as('getProjects')\n  })\n\n  it('should create complete project with team and tasks', () => {\n    // Navigate to projects\n    cy.visit('/app/projects')\n    cy.wait('@getProjects')\n    \n    // Create project\n    cy.get('[data-testid=\"create-project-button\"]').click()\n    cy.get('[data-testid=\"project-name\"]').type('E2E Test Project')\n    cy.get('[data-testid=\"project-budget\"]').type('50000')\n    cy.get('[data-testid=\"save-button\"]').click()\n    \n    // Verify creation\n    cy.get('[data-testid=\"success-message\"]').should('contain', 'created')\n    cy.url().should('include', '/projects/')\n    \n    // Add team member\n    cy.get('[data-testid=\"team-tab\"]').click()\n    cy.get('[data-testid=\"add-member-button\"]').click()\n    cy.get('[data-testid=\"user-select\"]').select('John Doe')\n    cy.get('[data-testid=\"role-input\"]').type('Developer')\n    cy.get('[data-testid=\"save-member-button\"]').click()\n    \n    // Verify team member\n    cy.get('[data-testid=\"team-member-list\"]').should('contain', 'John Doe')\n    \n    // Add task\n    cy.get('[data-testid=\"tasks-tab\"]').click()\n    cy.get('[data-testid=\"add-task-button\"]').click()\n    cy.get('[data-testid=\"task-title\"]').type('Setup Environment')\n    cy.get('[data-testid=\"task-assignee\"]').select('John Doe')\n    cy.get('[data-testid=\"save-task-button\"]').click()\n    \n    // Verify task\n    cy.get('[data-testid=\"task-list\"]').should('contain', 'Setup Environment')\n  })\n\n  it('should handle project status transitions', () => {\n    cy.visit('/app/projects/1')\n    \n    // Change status\n    cy.get('[data-testid=\"status-dropdown\"]').click()\n    cy.get('[data-testid=\"status-active\"]').click()\n    cy.get('[data-testid=\"confirm-status-change\"]').click()\n    \n    // Verify status change\n    cy.get('[data-testid=\"project-status\"]').should('contain', 'Active')\n    cy.get('[data-testid=\"success-message\"]').should('contain', 'updated')\n  })\n})\n\n// Custom commands\nCypress.Commands.add('login', (username, password) => {\n  cy.session([username, password], () => {\n    cy.visit('/login')\n    cy.get('[data-testid=\"username\"]').type(username)\n    cy.get('[data-testid=\"password\"]').type(password)\n    cy.get('[data-testid=\"login-button\"]').click()\n    cy.url().should('include', '/app/dashboard')\n  })\n})\n```\n\n## 🎭 Mock Strategies\n\n### MSW API Mocking\n\n```javascript\n// tests/mocks/api-handlers.js\nimport { rest } from 'msw'\n\nexport const handlers = [\n  // Projects\n  rest.get('/api/projects', (req, res, ctx) => {\n    const search = req.url.searchParams.get('search')\n    let projects = mockProjects\n    \n    if (search) {\n      projects = projects.filter(p => \n        p.name.toLowerCase().includes(search.toLowerCase())\n      )\n    }\n    \n    return res(\n      ctx.status(200),\n      ctx.json({\n        success: true,\n        data: { projects, pagination: { total: projects.length } }\n      })\n    )\n  }),\n\n  rest.post('/api/projects', (req, res, ctx) => {\n    const projectData = req.body\n    \n    if (!projectData.name) {\n      return res(\n        ctx.status(400),\n        ctx.json({\n          success: false,\n          errors: { name: ['Project name is required'] }\n        })\n      )\n    }\n    \n    const newProject = {\n      id: Date.now(),\n      ...projectData,\n      created_at: new Date().toISOString()\n    }\n    \n    return res(\n      ctx.status(201),\n      ctx.json({ success: true, data: newProject })\n    )\n  }),\n\n  // Error simulation\n  rest.get('/api/error-test', (req, res, ctx) => {\n    return res(\n      ctx.status(500),\n      ctx.json({ success: false, error: 'Internal server error' })\n    )\n  })\n]\n```\n\n### Store Mocking\n\n```javascript\n// tests/mocks/store-mocks.js\nimport { vi } from 'vitest'\n\nexport const mockProjectsStore = {\n  projects: [],\n  loading: false,\n  error: null,\n  fetchProjects: vi.fn(),\n  createProject: vi.fn(),\n  updateProject: vi.fn(),\n  deleteProject: vi.fn()\n}\n\nexport const mockAuthStore = {\n  user: { id: 1, username: 'testuser', role: 'admin' },\n  isAuthenticated: true,\n  csrfToken: 'mock-csrf-token',\n  login: vi.fn(),\n  logout: vi.fn(),\n  hasPermission: vi.fn(() => true)\n}\n```\n\n## 📚 Best Practices\n\n### Test Organization\n\n```javascript\n// ✅ Buono: Struttura chiara\ndescribe('ProjectTeam Component', () => {\n  describe('Rendering', () => {\n    it('should display team members correctly', () => {})\n    it('should show empty state when no members', () => {})\n  })\n  \n  describe('User Interactions', () => {\n    it('should open add member modal', () => {})\n    it('should handle form submission', () => {})\n  })\n  \n  describe('API Integration', () => {\n    it('should fetch team data from API', () => {})\n    it('should handle API errors', () => {})\n  })\n})\n```\n\n### Data-testid Usage\n\n```vue\n<!-- ✅ Buono: Usa data-testid per elementi testabili -->\n<template>\n  <div>\n    <button data-testid=\"add-member-button\" @click=\"openModal\">\n      Add Member\n    </button>\n    <div data-testid=\"team-member-list\">\n      <div \n        v-for=\"member in teamMembers\" \n        :key=\"member.id\"\n        data-testid=\"team-member\"\n      >\n        {{ member.full_name }}\n      </div>\n    </div>\n  </div>\n</template>\n```\n\n### Async Testing\n\n```javascript\n// ✅ Buono: Gestione corretta async/await\nit('should handle async operations', async () => {\n  fetch.mockResolvedValueOnce(mockResponse)\n  \n  await wrapper.vm.loadData()\n  await wrapper.vm.$nextTick()\n  \n  expect(wrapper.text()).toContain('Expected content')\n})\n\n// ✅ Buono: Wait for DOM updates\nit('should update UI after state change', async () => {\n  wrapper.vm.showModal = true\n  await nextTick()\n  \n  expect(wrapper.find('[data-testid=\"modal\"]').exists()).toBe(true)\n})\n```\n\n### Mock Cleanup\n\n```javascript\n// ✅ Buono: Cleanup automatico\nbeforeEach(() => {\n  vi.clearAllMocks()\n  fetch.mockClear()\n})\n\nafterEach(() => {\n  vi.restoreAllMocks()\n})\n```\n\n---\n\n*Guida aggiornata: 2025-01-26*\n"}