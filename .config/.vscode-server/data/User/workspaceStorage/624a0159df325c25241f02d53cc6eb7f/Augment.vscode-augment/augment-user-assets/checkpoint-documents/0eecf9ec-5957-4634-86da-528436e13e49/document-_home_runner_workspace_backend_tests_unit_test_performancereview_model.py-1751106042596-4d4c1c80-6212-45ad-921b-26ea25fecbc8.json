{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_performancereview_model.py"}, "modifiedCode": "\"\"\"Unit tests for PerformanceReview model.\"\"\"\nimport pytest\nfrom models import PerformanceReview, User\nfrom extensions import db\n\nclass TestPerformanceReviewModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n\n    def test_performancereview_creation_basic(self):\n        review = PerformanceReview(\n            employee_id=self.user.id,\n            reviewer_id=self.user.id,\n            period='2024-Q1',\n            status='draft'\n        )\n        db.session.add(review)\n        db.session.commit()\n        \n        assert review.id is not None\n        assert review.employee_id == self.user.id\n        assert review.period == '2024-Q1'\n\n    def test_performancereview_deletion(self):\n        review = PerformanceReview(employee_id=self.user.id, reviewer_id=self.user.id)\n        db.session.add(review)\n        db.session.commit()\n        review_id = review.id\n        \n        db.session.delete(review)\n        db.session.commit()\n        \n        deleted = PerformanceReview.query.get(review_id)\n        assert deleted is None\n"}