{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_userprofile_model.py"}, "originalCode": "\"\"\"\nUnit tests for UserProfile model.\nTests business logic, validations, and model functionality.\n\"\"\"\n\nimport pytest\nfrom datetime import datetime, date\nfrom models import UserProfile, User\nfrom extensions import db\n\n\nclass TestUserProfileModel:\n    \"\"\"Test suite for UserProfile model unit tests\"\"\"\n\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        \"\"\"Setup test data for each test method\"\"\"\n        self.app = app\n        self.user = test_user\n\n        # Create additional test users to avoid UNIQUE constraint issues\n        self.user2 = User(username='testuser2', email='<EMAIL>')\n        self.user3 = User(username='testuser3', email='<EMAIL>')\n        self.user4 = User(username='testuser4', email='<EMAIL>')\n\n        db.session.add_all([self.user2, self.user3, self.user4])\n        db.session.commit()\n\n    def test_userprofile_creation_basic(self):\n        \"\"\"Test basic user profile creation\"\"\"\n        profile = UserProfile(\n            user_id=self.user.id,\n            employee_id='EMP001',\n            job_title='Software Developer'\n        )\n        \n        db.session.add(profile)\n        db.session.commit()\n        \n        assert profile.id is not None\n        assert profile.user_id == self.user.id\n        assert profile.employee_id == 'EMP001'\n        assert profile.job_title == 'Software Developer'\n\n    def test_userprofile_creation_complete(self):\n        \"\"\"Test user profile creation with all fields\"\"\"\n        profile = UserProfile(\n            user_id=self.user.id,\n            employee_id='EMP002',\n            job_title='Senior Developer',\n            birth_date=date(1990, 5, 15),\n            address='123 Main St, City',\n            emergency_contact_name='John Doe',\n            emergency_contact_phone='+39 ************',\n            emergency_contact_relationship='Spouse',\n            employment_type='full_time',\n            work_location='Remote',\n            salary=50000.0,\n            salary_currency='EUR',\n            weekly_hours=40.0,\n            daily_hours=8.0,\n            profile_completion=75.5\n        )\n        \n        db.session.add(profile)\n        db.session.commit()\n        \n        assert profile.birth_date == date(1990, 5, 15)\n        assert profile.salary == 50000.0\n        assert profile.profile_completion == 75.5\n        assert profile.created_at is not None\n\n    def test_userprofile_user_relationship(self):\n        \"\"\"Test relationship with User model\"\"\"\n        profile = UserProfile(\n            user_id=self.user.id,\n            employee_id='EMP003'\n        )\n        \n        db.session.add(profile)\n        db.session.commit()\n        \n        assert profile.user is not None\n        assert profile.user.id == self.user.id\n\n    def test_userprofile_salary_handling(self):\n        \"\"\"Test salary field functionality\"\"\"\n        profile = UserProfile(\n            user_id=self.user.id,\n            employee_id='EMP004',\n            salary=75000.50,\n            salary_currency='USD'\n        )\n        \n        db.session.add(profile)\n        db.session.commit()\n        \n        assert profile.salary == 75000.50\n        assert profile.salary_currency == 'USD'\n\n    def test_userprofile_employment_details(self):\n        \"\"\"Test employment-related fields\"\"\"\n        profile = UserProfile(\n            user_id=self.user.id,\n            employee_id='EMP005',\n            employment_type='part_time',\n            work_location='Office',\n            weekly_hours=20.0,\n            daily_hours=4.0,\n            notice_period_days=15\n        )\n        \n        db.session.add(profile)\n        db.session.commit()\n        \n        assert profile.employment_type == 'part_time'\n        assert profile.weekly_hours == 20.0\n        assert profile.notice_period_days == 15\n\n    def test_userprofile_emergency_contact(self):\n        \"\"\"Test emergency contact fields\"\"\"\n        profile = UserProfile(\n            user_id=self.user.id,\n            employee_id='EMP006',\n            emergency_contact_name='Jane Smith',\n            emergency_contact_phone='+39 ************',\n            emergency_contact_relationship='Sister'\n        )\n        \n        db.session.add(profile)\n        db.session.commit()\n        \n        assert profile.emergency_contact_name == 'Jane Smith'\n        assert profile.emergency_contact_phone == '+39 ************'\n        assert profile.emergency_contact_relationship == 'Sister'\n\n    def test_userprofile_contract_dates(self):\n        \"\"\"Test contract-related date fields\"\"\"\n        profile = UserProfile(\n            user_id=self.user.id,\n            employee_id='EMP007',\n            probation_end_date=date(2024, 12, 31),\n            contract_end_date=date(2025, 12, 31)\n        )\n        \n        db.session.add(profile)\n        db.session.commit()\n        \n        assert profile.probation_end_date == date(2024, 12, 31)\n        assert profile.contract_end_date == date(2025, 12, 31)\n\n    def test_userprofile_cv_fields(self):\n        \"\"\"Test CV-related fields\"\"\"\n        profile = UserProfile(\n            user_id=self.user.id,\n            employee_id='EMP008',\n            current_cv_path='/uploads/cv/emp008.pdf',\n            cv_last_updated=datetime.now(),\n            cv_analysis_data='{\"skills\": [\"Python\", \"JavaScript\"]}'\n        )\n        \n        db.session.add(profile)\n        db.session.commit()\n        \n        assert profile.current_cv_path == '/uploads/cv/emp008.pdf'\n        assert profile.cv_last_updated is not None\n        assert 'Python' in profile.cv_analysis_data\n\n    def test_userprofile_profile_completion(self):\n        \"\"\"Test profile completion percentage\"\"\"\n        profile = UserProfile(\n            user_id=self.user.id,\n            employee_id='EMP009',\n            profile_completion=85.5\n        )\n        \n        db.session.add(profile)\n        db.session.commit()\n        \n        assert profile.profile_completion == 85.5\n        assert 0 <= profile.profile_completion <= 100\n\n    def test_userprofile_timestamps(self):\n        \"\"\"Test automatic timestamp handling\"\"\"\n        profile = UserProfile(\n            user_id=self.user.id,\n            employee_id='EMP010'\n        )\n        \n        db.session.add(profile)\n        db.session.commit()\n        \n        assert profile.created_at is not None\n        assert profile.updated_at is not None\n        assert isinstance(profile.created_at, datetime)\n\n    def test_userprofile_update_operations(self):\n        \"\"\"Test profile update operations\"\"\"\n        profile = UserProfile(\n            user_id=self.user.id,\n            employee_id='EMP011',\n            job_title='Junior Developer',\n            salary=30000.0\n        )\n        \n        db.session.add(profile)\n        db.session.commit()\n        \n        # Update profile\n        profile.job_title = 'Senior Developer'\n        profile.salary = 60000.0\n        profile.profile_completion = 90.0\n        \n        db.session.commit()\n        \n        updated_profile = UserProfile.query.get(profile.id)\n        assert updated_profile.job_title == 'Senior Developer'\n        assert updated_profile.salary == 60000.0\n        assert updated_profile.profile_completion == 90.0\n\n    def test_userprofile_query_by_employee_id(self):\n        \"\"\"Test querying by employee ID\"\"\"\n        profile = UserProfile(\n            user_id=self.user.id,\n            employee_id='UNIQUE_EMP_ID'\n        )\n        \n        db.session.add(profile)\n        db.session.commit()\n        \n        found_profile = UserProfile.query.filter_by(employee_id='UNIQUE_EMP_ID').first()\n        assert found_profile is not None\n        assert found_profile.id == profile.id\n\n    def test_userprofile_query_by_employment_type(self):\n        \"\"\"Test querying by employment type\"\"\"\n        profiles = [\n            UserProfile(user_id=self.user.id, employee_id='FT1', employment_type='full_time'),\n            UserProfile(user_id=self.user.id, employee_id='PT1', employment_type='part_time')\n        ]\n        \n        db.session.add_all(profiles)\n        db.session.commit()\n        \n        full_time_profiles = UserProfile.query.filter_by(employment_type='full_time').all()\n        assert len(full_time_profiles) >= 1\n\n    def test_userprofile_deletion(self):\n        \"\"\"Test profile deletion\"\"\"\n        profile = UserProfile(\n            user_id=self.user.id,\n            employee_id='TO_DELETE'\n        )\n        \n        db.session.add(profile)\n        db.session.commit()\n        profile_id = profile.id\n        \n        db.session.delete(profile)\n        db.session.commit()\n        \n        deleted_profile = UserProfile.query.get(profile_id)\n        assert deleted_profile is None\n\n    def test_userprofile_default_values(self):\n        \"\"\"Test default values for fields\"\"\"\n        profile = UserProfile(\n            user_id=self.user.id,\n            employee_id='DEFAULT_TEST'\n        )\n        \n        db.session.add(profile)\n        db.session.commit()\n        \n        assert profile.employment_type == 'full_time'\n        assert profile.salary_currency == 'EUR'\n        assert profile.notice_period_days == 30\n        assert profile.weekly_hours == 40.0\n        assert profile.daily_hours == 8.0\n        assert profile.profile_completion == 0.0\n", "modifiedCode": "\"\"\"\nUnit tests for UserProfile model.\nTests business logic, validations, and model functionality.\n\"\"\"\n\nimport pytest\nfrom datetime import datetime, date\nfrom models import UserProfile, User\nfrom extensions import db\n\n\nclass TestUserProfileModel:\n    \"\"\"Test suite for UserProfile model unit tests\"\"\"\n\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        \"\"\"Setup test data for each test method\"\"\"\n        self.app = app\n        self.user = test_user\n\n        # Create additional test users to avoid UNIQUE constraint issues\n        self.user2 = User(username='testuser2', email='<EMAIL>')\n        self.user3 = User(username='testuser3', email='<EMAIL>')\n        self.user4 = User(username='testuser4', email='<EMAIL>')\n\n        db.session.add_all([self.user2, self.user3, self.user4])\n        db.session.commit()\n\n    def test_userprofile_creation_basic(self):\n        \"\"\"Test basic user profile creation\"\"\"\n        profile = UserProfile(\n            user_id=self.user.id,\n            employee_id='EMP001',\n            job_title='Software Developer'\n        )\n        \n        db.session.add(profile)\n        db.session.commit()\n        \n        assert profile.id is not None\n        assert profile.user_id == self.user.id\n        assert profile.employee_id == 'EMP001'\n        assert profile.job_title == 'Software Developer'\n\n    def test_userprofile_creation_complete(self):\n        \"\"\"Test user profile creation with all fields\"\"\"\n        profile = UserProfile(\n            user_id=self.user2.id,\n            employee_id='EMP002',\n            job_title='Senior Developer',\n            birth_date=date(1990, 5, 15),\n            address='123 Main St, City',\n            emergency_contact_name='John Doe',\n            emergency_contact_phone='+39 ************',\n            emergency_contact_relationship='Spouse',\n            employment_type='full_time',\n            work_location='Remote',\n            salary=50000.0,\n            salary_currency='EUR',\n            weekly_hours=40.0,\n            daily_hours=8.0,\n            profile_completion=75.5\n        )\n        \n        db.session.add(profile)\n        db.session.commit()\n        \n        assert profile.birth_date == date(1990, 5, 15)\n        assert profile.salary == 50000.0\n        assert profile.profile_completion == 75.5\n        assert profile.created_at is not None\n\n    def test_userprofile_user_relationship(self):\n        \"\"\"Test relationship with User model\"\"\"\n        profile = UserProfile(\n            user_id=self.user.id,\n            employee_id='EMP003'\n        )\n        \n        db.session.add(profile)\n        db.session.commit()\n        \n        assert profile.user is not None\n        assert profile.user.id == self.user.id\n\n    def test_userprofile_salary_handling(self):\n        \"\"\"Test salary field functionality\"\"\"\n        profile = UserProfile(\n            user_id=self.user.id,\n            employee_id='EMP004',\n            salary=75000.50,\n            salary_currency='USD'\n        )\n        \n        db.session.add(profile)\n        db.session.commit()\n        \n        assert profile.salary == 75000.50\n        assert profile.salary_currency == 'USD'\n\n    def test_userprofile_employment_details(self):\n        \"\"\"Test employment-related fields\"\"\"\n        profile = UserProfile(\n            user_id=self.user.id,\n            employee_id='EMP005',\n            employment_type='part_time',\n            work_location='Office',\n            weekly_hours=20.0,\n            daily_hours=4.0,\n            notice_period_days=15\n        )\n        \n        db.session.add(profile)\n        db.session.commit()\n        \n        assert profile.employment_type == 'part_time'\n        assert profile.weekly_hours == 20.0\n        assert profile.notice_period_days == 15\n\n    def test_userprofile_emergency_contact(self):\n        \"\"\"Test emergency contact fields\"\"\"\n        profile = UserProfile(\n            user_id=self.user.id,\n            employee_id='EMP006',\n            emergency_contact_name='Jane Smith',\n            emergency_contact_phone='+39 ************',\n            emergency_contact_relationship='Sister'\n        )\n        \n        db.session.add(profile)\n        db.session.commit()\n        \n        assert profile.emergency_contact_name == 'Jane Smith'\n        assert profile.emergency_contact_phone == '+39 ************'\n        assert profile.emergency_contact_relationship == 'Sister'\n\n    def test_userprofile_contract_dates(self):\n        \"\"\"Test contract-related date fields\"\"\"\n        profile = UserProfile(\n            user_id=self.user.id,\n            employee_id='EMP007',\n            probation_end_date=date(2024, 12, 31),\n            contract_end_date=date(2025, 12, 31)\n        )\n        \n        db.session.add(profile)\n        db.session.commit()\n        \n        assert profile.probation_end_date == date(2024, 12, 31)\n        assert profile.contract_end_date == date(2025, 12, 31)\n\n    def test_userprofile_cv_fields(self):\n        \"\"\"Test CV-related fields\"\"\"\n        profile = UserProfile(\n            user_id=self.user.id,\n            employee_id='EMP008',\n            current_cv_path='/uploads/cv/emp008.pdf',\n            cv_last_updated=datetime.now(),\n            cv_analysis_data='{\"skills\": [\"Python\", \"JavaScript\"]}'\n        )\n        \n        db.session.add(profile)\n        db.session.commit()\n        \n        assert profile.current_cv_path == '/uploads/cv/emp008.pdf'\n        assert profile.cv_last_updated is not None\n        assert 'Python' in profile.cv_analysis_data\n\n    def test_userprofile_profile_completion(self):\n        \"\"\"Test profile completion percentage\"\"\"\n        profile = UserProfile(\n            user_id=self.user.id,\n            employee_id='EMP009',\n            profile_completion=85.5\n        )\n        \n        db.session.add(profile)\n        db.session.commit()\n        \n        assert profile.profile_completion == 85.5\n        assert 0 <= profile.profile_completion <= 100\n\n    def test_userprofile_timestamps(self):\n        \"\"\"Test automatic timestamp handling\"\"\"\n        profile = UserProfile(\n            user_id=self.user.id,\n            employee_id='EMP010'\n        )\n        \n        db.session.add(profile)\n        db.session.commit()\n        \n        assert profile.created_at is not None\n        assert profile.updated_at is not None\n        assert isinstance(profile.created_at, datetime)\n\n    def test_userprofile_update_operations(self):\n        \"\"\"Test profile update operations\"\"\"\n        profile = UserProfile(\n            user_id=self.user.id,\n            employee_id='EMP011',\n            job_title='Junior Developer',\n            salary=30000.0\n        )\n        \n        db.session.add(profile)\n        db.session.commit()\n        \n        # Update profile\n        profile.job_title = 'Senior Developer'\n        profile.salary = 60000.0\n        profile.profile_completion = 90.0\n        \n        db.session.commit()\n        \n        updated_profile = UserProfile.query.get(profile.id)\n        assert updated_profile.job_title == 'Senior Developer'\n        assert updated_profile.salary == 60000.0\n        assert updated_profile.profile_completion == 90.0\n\n    def test_userprofile_query_by_employee_id(self):\n        \"\"\"Test querying by employee ID\"\"\"\n        profile = UserProfile(\n            user_id=self.user.id,\n            employee_id='UNIQUE_EMP_ID'\n        )\n        \n        db.session.add(profile)\n        db.session.commit()\n        \n        found_profile = UserProfile.query.filter_by(employee_id='UNIQUE_EMP_ID').first()\n        assert found_profile is not None\n        assert found_profile.id == profile.id\n\n    def test_userprofile_query_by_employment_type(self):\n        \"\"\"Test querying by employment type\"\"\"\n        profiles = [\n            UserProfile(user_id=self.user.id, employee_id='FT1', employment_type='full_time'),\n            UserProfile(user_id=self.user.id, employee_id='PT1', employment_type='part_time')\n        ]\n        \n        db.session.add_all(profiles)\n        db.session.commit()\n        \n        full_time_profiles = UserProfile.query.filter_by(employment_type='full_time').all()\n        assert len(full_time_profiles) >= 1\n\n    def test_userprofile_deletion(self):\n        \"\"\"Test profile deletion\"\"\"\n        profile = UserProfile(\n            user_id=self.user.id,\n            employee_id='TO_DELETE'\n        )\n        \n        db.session.add(profile)\n        db.session.commit()\n        profile_id = profile.id\n        \n        db.session.delete(profile)\n        db.session.commit()\n        \n        deleted_profile = UserProfile.query.get(profile_id)\n        assert deleted_profile is None\n\n    def test_userprofile_default_values(self):\n        \"\"\"Test default values for fields\"\"\"\n        profile = UserProfile(\n            user_id=self.user.id,\n            employee_id='DEFAULT_TEST'\n        )\n        \n        db.session.add(profile)\n        db.session.commit()\n        \n        assert profile.employment_type == 'full_time'\n        assert profile.salary_currency == 'EUR'\n        assert profile.notice_period_days == 30\n        assert profile.weekly_hours == 40.0\n        assert profile.daily_hours == 8.0\n        assert profile.profile_completion == 0.0\n"}