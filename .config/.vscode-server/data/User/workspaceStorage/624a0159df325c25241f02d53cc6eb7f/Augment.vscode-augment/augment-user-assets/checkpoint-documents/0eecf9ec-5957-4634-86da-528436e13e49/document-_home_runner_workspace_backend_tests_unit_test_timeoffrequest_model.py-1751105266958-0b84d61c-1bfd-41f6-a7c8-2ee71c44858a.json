{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_timeoffrequest_model.py"}, "originalCode": "\"\"\"Unit tests for TimeOffRequest model.\"\"\"\nimport pytest\nfrom datetime import date\nfrom models import TimeOffRequest, User\nfrom extensions import db\n\nclass TestTimeOffRequestModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n\n    def test_timeoffrequest_creation_basic(self):\n        request = TimeOffRequest(\n            user_id=self.user.id,\n            start_date=date(2024, 7, 1),\n            end_date=date(2024, 7, 5),\n            type='vacation',  # Campo corretto è 'type', non 'request_type'\n            reason='Summer vacation'\n        )\n        db.session.add(request)\n        db.session.commit()\n\n        assert request.id is not None\n        assert request.user_id == self.user.id\n        assert request.start_date == date(2024, 7, 1)\n        assert request.end_date == date(2024, 7, 5)\n        assert request.type == 'vacation'\n\n    def test_timeoffrequest_status(self):\n        request = TimeOffRequest(\n            user_id=self.user.id,\n            start_date=date(2024, 8, 1),\n            end_date=date(2024, 8, 3),\n            request_type='sick',\n            status='pending'\n        )\n        db.session.add(request)\n        db.session.commit()\n        \n        assert request.status == 'pending'\n\n    def test_timeoffrequest_deletion(self):\n        request = TimeOffRequest(\n            user_id=self.user.id,\n            start_date=date(2024, 9, 1),\n            end_date=date(2024, 9, 2),\n            request_type='personal'\n        )\n        db.session.add(request)\n        db.session.commit()\n        request_id = request.id\n        \n        db.session.delete(request)\n        db.session.commit()\n        \n        deleted = TimeOffRequest.query.get(request_id)\n        assert deleted is None\n", "modifiedCode": "\"\"\"Unit tests for TimeOffRequest model.\"\"\"\nimport pytest\nfrom datetime import date\nfrom models import TimeOffRequest, User\nfrom extensions import db\n\nclass TestTimeOffRequestModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n\n    def test_timeoffrequest_creation_basic(self):\n        request = TimeOffRequest(\n            user_id=self.user.id,\n            start_date=date(2024, 7, 1),\n            end_date=date(2024, 7, 5),\n            type='vacation',  # Campo corretto è 'type', non 'request_type'\n            reason='Summer vacation'\n        )\n        db.session.add(request)\n        db.session.commit()\n\n        assert request.id is not None\n        assert request.user_id == self.user.id\n        assert request.start_date == date(2024, 7, 1)\n        assert request.end_date == date(2024, 7, 5)\n        assert request.type == 'vacation'\n\n    def test_timeoffrequest_status(self):\n        request = TimeOffRequest(\n            user_id=self.user.id,\n            start_date=date(2024, 8, 1),\n            end_date=date(2024, 8, 3),\n            request_type='sick',\n            status='pending'\n        )\n        db.session.add(request)\n        db.session.commit()\n        \n        assert request.status == 'pending'\n\n    def test_timeoffrequest_deletion(self):\n        request = TimeOffRequest(\n            user_id=self.user.id,\n            start_date=date(2024, 9, 1),\n            end_date=date(2024, 9, 2),\n            request_type='personal'\n        )\n        db.session.add(request)\n        db.session.commit()\n        request_id = request.id\n        \n        db.session.delete(request)\n        db.session.commit()\n        \n        deleted = TimeOffRequest.query.get(request_id)\n        assert deleted is None\n"}