{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_department_model.py"}, "originalCode": "\"\"\"\nUnit tests for Department model.\nTests business logic, validations, and model functionality.\n\"\"\"\n\nimport pytest\nfrom datetime import datetime\nfrom models import Department, User\nfrom extensions import db\n\n\nclass TestDepartmentModel:\n    \"\"\"Test suite for Department model unit tests\"\"\"\n\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        \"\"\"Setup test data for each test method\"\"\"\n        self.app = app\n        self.user = test_user\n\n    def test_department_creation_basic(self):\n        \"\"\"Test basic department creation with required fields\"\"\"\n        department = Department(\n            name='Engineering'\n        )\n\n        db.session.add(department)\n        db.session.commit()\n\n        assert department.id is not None\n        assert department.name == 'Engineering'\n\n    def test_department_creation_complete(self):\n        \"\"\"Test department creation with all fields\"\"\"\n        department = Department(\n            name='Human Resources',\n            code='HR',\n            description='Manages employee relations and company policies',\n            manager_id=self.user.id,\n            budget=150000.0,\n            is_active=True\n        )\n        \n        db.session.add(department)\n        db.session.commit()\n        \n        assert department.description == 'Manages employee relations and company policies'\n        assert department.manager_id == self.user.id\n        assert department.budget == 150000.0\n        assert department.is_active is True\n        assert department.created_at is not None\n\n    def test_department_repr_method(self):\n        \"\"\"Test string representation of department\"\"\"\n        department = Department(name='Marketing', code='MKT')\n        \n        expected_repr = '<Department Marketing (MKT)>'\n        assert repr(department) == expected_repr\n\n    def test_department_manager_relationship(self):\n        \"\"\"Test relationship with User model as manager\"\"\"\n        department = Department(\n            name='Sales',\n            code='SALES',\n            manager_id=self.user.id\n        )\n        \n        db.session.add(department)\n        db.session.commit()\n        \n        # Test forward relationship\n        assert department.manager is not None\n        assert department.manager.id == self.user.id\n        \n        # Test backward relationship\n        assert department in self.user.managed_departments\n\n    def test_department_code_uniqueness(self):\n        \"\"\"Test that department codes should be unique\"\"\"\n        dept1 = Department(name='Department 1', code='UNIQUE')\n        dept2 = Department(name='Department 2', code='UNIQUE')  # Duplicate code\n        \n        db.session.add(dept1)\n        db.session.commit()\n        \n        # This should potentially raise an error if uniqueness is enforced\n        db.session.add(dept2)\n        try:\n            db.session.commit()\n            # If no constraint, both will exist\n            assert True\n        except Exception:\n            # If constraint exists, this is expected\n            db.session.rollback()\n            assert True\n\n    def test_department_budget_handling(self):\n        \"\"\"Test budget field functionality\"\"\"\n        department = Department(\n            name='Finance',\n            code='FIN',\n            budget=250000.50\n        )\n        \n        db.session.add(department)\n        db.session.commit()\n        \n        assert department.budget == 250000.50\n        assert isinstance(department.budget, float)\n\n    def test_department_active_flag(self):\n        \"\"\"Test is_active flag functionality\"\"\"\n        active_dept = Department(\n            name='Active Department',\n            code='ACTIVE',\n            is_active=True\n        )\n        \n        inactive_dept = Department(\n            name='Inactive Department',\n            code='INACTIVE',\n            is_active=False\n        )\n        \n        db.session.add_all([active_dept, inactive_dept])\n        db.session.commit()\n        \n        assert active_dept.is_active is True\n        assert inactive_dept.is_active is False\n\n    def test_department_description_field(self):\n        \"\"\"Test description field for detailed information\"\"\"\n        long_description = \"\"\"\n        The Research and Development department is responsible for innovation,\n        product development, and technological advancement within the company.\n        This includes market research, prototype development, and testing.\n        \"\"\"\n        \n        department = Department(\n            name='Research & Development',\n            code='RND',\n            description=long_description.strip()\n        )\n        \n        db.session.add(department)\n        db.session.commit()\n        \n        assert department.description == long_description.strip()\n\n    def test_department_timestamps(self):\n        \"\"\"Test automatic timestamp handling\"\"\"\n        department = Department(\n            name='Timestamp Test',\n            code='TIME'\n        )\n        \n        db.session.add(department)\n        db.session.commit()\n        \n        # Test created_at is set\n        assert department.created_at is not None\n        assert isinstance(department.created_at, datetime)\n        \n        # Test updated_at is set\n        assert department.updated_at is not None\n        assert isinstance(department.updated_at, datetime)\n        \n        # Test updated_at changes on update\n        original_updated_at = department.updated_at\n        department.description = 'Updated description'\n        db.session.commit()\n        \n        assert department.updated_at > original_updated_at\n\n    def test_department_query_by_status(self):\n        \"\"\"Test querying departments by active status\"\"\"\n        active_depts = [\n            Department(name='Active 1', code='ACT1', is_active=True),\n            Department(name='Active 2', code='ACT2', is_active=True)\n        ]\n        \n        inactive_depts = [\n            Department(name='Inactive 1', code='INA1', is_active=False),\n            Department(name='Inactive 2', code='INA2', is_active=False)\n        ]\n        \n        db.session.add_all(active_depts + inactive_depts)\n        db.session.commit()\n        \n        # Query only active departments\n        active_results = Department.query.filter_by(is_active=True).all()\n        active_names = [dept.name for dept in active_results]\n        \n        assert 'Active 1' in active_names\n        assert 'Active 2' in active_names\n        assert 'Inactive 1' not in active_names\n        assert 'Inactive 2' not in active_names\n\n    def test_department_query_by_manager(self):\n        \"\"\"Test querying departments by manager\"\"\"\n        dept_with_manager = Department(\n            name='Managed Department',\n            code='MGMT',\n            manager_id=self.user.id\n        )\n        \n        dept_without_manager = Department(\n            name='Unmanaged Department',\n            code='UNMGMT'\n        )\n        \n        db.session.add_all([dept_with_manager, dept_without_manager])\n        db.session.commit()\n        \n        # Query departments by manager\n        managed_depts = Department.query.filter_by(manager_id=self.user.id).all()\n        \n        assert len(managed_depts) == 1\n        assert managed_depts[0].name == 'Managed Department'\n\n    def test_department_budget_calculations(self):\n        \"\"\"Test budget-related calculations\"\"\"\n        departments = [\n            Department(name='Dept 1', code='D1', budget=100000.0),\n            Department(name='Dept 2', code='D2', budget=150000.0),\n            Department(name='Dept 3', code='D3', budget=200000.0)\n        ]\n        \n        db.session.add_all(departments)\n        db.session.commit()\n        \n        # Calculate total budget\n        total_budget = sum(dept.budget for dept in departments if dept.budget)\n        assert total_budget == 450000.0\n        \n        # Find department with highest budget\n        max_budget_dept = max(departments, key=lambda d: d.budget or 0)\n        assert max_budget_dept.name == 'Dept 3'\n\n    def test_department_update_operations(self):\n        \"\"\"Test department update operations\"\"\"\n        department = Department(\n            name='Original Name',\n            code='ORIG',\n            budget=100000.0,\n            is_active=True\n        )\n        \n        db.session.add(department)\n        db.session.commit()\n        \n        # Update department\n        department.name = 'Updated Name'\n        department.budget = 120000.0\n        department.is_active = False\n        department.description = 'Added description'\n        \n        db.session.commit()\n        \n        # Verify updates\n        updated_dept = Department.query.get(department.id)\n        assert updated_dept.name == 'Updated Name'\n        assert updated_dept.budget == 120000.0\n        assert updated_dept.is_active is False\n        assert updated_dept.description == 'Added description'\n        assert updated_dept.code == 'ORIG'  # Unchanged\n\n    def test_department_deletion(self):\n        \"\"\"Test department deletion\"\"\"\n        department = Department(\n            name='To Delete',\n            code='DELETE'\n        )\n        \n        db.session.add(department)\n        db.session.commit()\n        department_id = department.id\n        \n        # Delete department\n        db.session.delete(department)\n        db.session.commit()\n        \n        # Verify deletion\n        deleted_dept = Department.query.get(department_id)\n        assert deleted_dept is None\n\n    def test_department_search_functionality(self):\n        \"\"\"Test department search by name\"\"\"\n        departments = [\n            Department(name='Engineering Team', code='ENG'),\n            Department(name='Engineering Support', code='ENGS'),\n            Department(name='Marketing', code='MKT')\n        ]\n        \n        db.session.add_all(departments)\n        db.session.commit()\n        \n        # Search for departments containing 'Engineering'\n        eng_depts = Department.query.filter(Department.name.contains('Engineering')).all()\n        eng_names = [dept.name for dept in eng_depts]\n        \n        assert 'Engineering Team' in eng_names\n        assert 'Engineering Support' in eng_names\n        assert 'Marketing' not in eng_names\n\n    def test_department_default_values(self):\n        \"\"\"Test default values for optional fields\"\"\"\n        department = Department(\n            name='Default Test',\n            code='DEF'\n        )\n        \n        db.session.add(department)\n        db.session.commit()\n        \n        # Check default values\n        assert department.is_active is True  # Assuming default is True\n        assert department.budget is None     # Should be None if not set\n        assert department.manager_id is None # Should be None if not set\n        assert department.description is None # Should be None if not set\n\n    def test_department_employee_count(self):\n        \"\"\"Test counting employees in department (if relationship exists)\"\"\"\n        department = Department(\n            name='Test Department',\n            code='TEST'\n        )\n        \n        db.session.add(department)\n        db.session.commit()\n        \n        # This test assumes there's a relationship to count employees\n        # The actual implementation depends on how User-Department relationship is set up\n        assert hasattr(department, 'employees') or hasattr(department, 'users')\n\n    def test_department_hierarchy(self):\n        \"\"\"Test department hierarchy if parent-child relationships exist\"\"\"\n        parent_dept = Department(\n            name='Parent Department',\n            code='PARENT'\n        )\n        \n        db.session.add(parent_dept)\n        db.session.commit()\n        \n        # If there's a parent_id field for hierarchy\n        if hasattr(Department, 'parent_id'):\n            child_dept = Department(\n                name='Child Department',\n                code='CHILD',\n                parent_id=parent_dept.id\n            )\n            \n            db.session.add(child_dept)\n            db.session.commit()\n            \n            assert child_dept.parent_id == parent_dept.id\n", "modifiedCode": "\"\"\"\nUnit tests for Department model.\nTests business logic, validations, and model functionality.\n\"\"\"\n\nimport pytest\nfrom datetime import datetime\nfrom models import Department, User\nfrom extensions import db\n\n\nclass TestDepartmentModel:\n    \"\"\"Test suite for Department model unit tests\"\"\"\n\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        \"\"\"Setup test data for each test method\"\"\"\n        self.app = app\n        self.user = test_user\n\n    def test_department_creation_basic(self):\n        \"\"\"Test basic department creation with required fields\"\"\"\n        department = Department(\n            name='Engineering'\n        )\n\n        db.session.add(department)\n        db.session.commit()\n\n        assert department.id is not None\n        assert department.name == 'Engineering'\n\n    def test_department_creation_complete(self):\n        \"\"\"Test department creation with all fields\"\"\"\n        department = Department(\n            name='Human Resources',\n            code='HR',\n            description='Manages employee relations and company policies',\n            manager_id=self.user.id,\n            budget=150000.0,\n            is_active=True\n        )\n        \n        db.session.add(department)\n        db.session.commit()\n        \n        assert department.description == 'Manages employee relations and company policies'\n        assert department.manager_id == self.user.id\n        assert department.budget == 150000.0\n        assert department.is_active is True\n        assert department.created_at is not None\n\n    def test_department_repr_method(self):\n        \"\"\"Test string representation of department\"\"\"\n        department = Department(name='Marketing', code='MKT')\n        \n        expected_repr = '<Department Marketing (MKT)>'\n        assert repr(department) == expected_repr\n\n    def test_department_manager_relationship(self):\n        \"\"\"Test relationship with User model as manager\"\"\"\n        department = Department(\n            name='Sales',\n            code='SALES',\n            manager_id=self.user.id\n        )\n        \n        db.session.add(department)\n        db.session.commit()\n        \n        # Test forward relationship\n        assert department.manager is not None\n        assert department.manager.id == self.user.id\n        \n        # Test backward relationship\n        assert department in self.user.managed_departments\n\n    def test_department_code_uniqueness(self):\n        \"\"\"Test that department codes should be unique\"\"\"\n        dept1 = Department(name='Department 1', code='UNIQUE')\n        dept2 = Department(name='Department 2', code='UNIQUE')  # Duplicate code\n        \n        db.session.add(dept1)\n        db.session.commit()\n        \n        # This should potentially raise an error if uniqueness is enforced\n        db.session.add(dept2)\n        try:\n            db.session.commit()\n            # If no constraint, both will exist\n            assert True\n        except Exception:\n            # If constraint exists, this is expected\n            db.session.rollback()\n            assert True\n\n    def test_department_budget_handling(self):\n        \"\"\"Test budget field functionality\"\"\"\n        department = Department(\n            name='Finance',\n            code='FIN',\n            budget=250000.50\n        )\n        \n        db.session.add(department)\n        db.session.commit()\n        \n        assert department.budget == 250000.50\n        assert isinstance(department.budget, float)\n\n    def test_department_active_flag(self):\n        \"\"\"Test is_active flag functionality\"\"\"\n        active_dept = Department(\n            name='Active Department',\n            code='ACTIVE',\n            is_active=True\n        )\n        \n        inactive_dept = Department(\n            name='Inactive Department',\n            code='INACTIVE',\n            is_active=False\n        )\n        \n        db.session.add_all([active_dept, inactive_dept])\n        db.session.commit()\n        \n        assert active_dept.is_active is True\n        assert inactive_dept.is_active is False\n\n    def test_department_description_field(self):\n        \"\"\"Test description field for detailed information\"\"\"\n        long_description = \"\"\"\n        The Research and Development department is responsible for innovation,\n        product development, and technological advancement within the company.\n        This includes market research, prototype development, and testing.\n        \"\"\"\n        \n        department = Department(\n            name='Research & Development',\n            code='RND',\n            description=long_description.strip()\n        )\n        \n        db.session.add(department)\n        db.session.commit()\n        \n        assert department.description == long_description.strip()\n\n    def test_department_timestamps(self):\n        \"\"\"Test automatic timestamp handling\"\"\"\n        department = Department(\n            name='Timestamp Test',\n            code='TIME'\n        )\n        \n        db.session.add(department)\n        db.session.commit()\n        \n        # Test created_at is set\n        assert department.created_at is not None\n        assert isinstance(department.created_at, datetime)\n        \n        # Test updated_at is set\n        assert department.updated_at is not None\n        assert isinstance(department.updated_at, datetime)\n        \n        # Test updated_at changes on update\n        original_updated_at = department.updated_at\n        department.description = 'Updated description'\n        db.session.commit()\n        \n        assert department.updated_at > original_updated_at\n\n    def test_department_query_by_status(self):\n        \"\"\"Test querying departments by active status\"\"\"\n        active_depts = [\n            Department(name='Active 1', code='ACT1', is_active=True),\n            Department(name='Active 2', code='ACT2', is_active=True)\n        ]\n        \n        inactive_depts = [\n            Department(name='Inactive 1', code='INA1', is_active=False),\n            Department(name='Inactive 2', code='INA2', is_active=False)\n        ]\n        \n        db.session.add_all(active_depts + inactive_depts)\n        db.session.commit()\n        \n        # Query only active departments\n        active_results = Department.query.filter_by(is_active=True).all()\n        active_names = [dept.name for dept in active_results]\n        \n        assert 'Active 1' in active_names\n        assert 'Active 2' in active_names\n        assert 'Inactive 1' not in active_names\n        assert 'Inactive 2' not in active_names\n\n    def test_department_query_by_manager(self):\n        \"\"\"Test querying departments by manager\"\"\"\n        dept_with_manager = Department(\n            name='Managed Department',\n            code='MGMT',\n            manager_id=self.user.id\n        )\n        \n        dept_without_manager = Department(\n            name='Unmanaged Department',\n            code='UNMGMT'\n        )\n        \n        db.session.add_all([dept_with_manager, dept_without_manager])\n        db.session.commit()\n        \n        # Query departments by manager\n        managed_depts = Department.query.filter_by(manager_id=self.user.id).all()\n        \n        assert len(managed_depts) == 1\n        assert managed_depts[0].name == 'Managed Department'\n\n    def test_department_budget_calculations(self):\n        \"\"\"Test budget-related calculations\"\"\"\n        departments = [\n            Department(name='Dept 1', code='D1', budget=100000.0),\n            Department(name='Dept 2', code='D2', budget=150000.0),\n            Department(name='Dept 3', code='D3', budget=200000.0)\n        ]\n        \n        db.session.add_all(departments)\n        db.session.commit()\n        \n        # Calculate total budget\n        total_budget = sum(dept.budget for dept in departments if dept.budget)\n        assert total_budget == 450000.0\n        \n        # Find department with highest budget\n        max_budget_dept = max(departments, key=lambda d: d.budget or 0)\n        assert max_budget_dept.name == 'Dept 3'\n\n    def test_department_update_operations(self):\n        \"\"\"Test department update operations\"\"\"\n        department = Department(\n            name='Original Name',\n            code='ORIG',\n            budget=100000.0,\n            is_active=True\n        )\n        \n        db.session.add(department)\n        db.session.commit()\n        \n        # Update department\n        department.name = 'Updated Name'\n        department.budget = 120000.0\n        department.is_active = False\n        department.description = 'Added description'\n        \n        db.session.commit()\n        \n        # Verify updates\n        updated_dept = Department.query.get(department.id)\n        assert updated_dept.name == 'Updated Name'\n        assert updated_dept.budget == 120000.0\n        assert updated_dept.is_active is False\n        assert updated_dept.description == 'Added description'\n        assert updated_dept.code == 'ORIG'  # Unchanged\n\n    def test_department_deletion(self):\n        \"\"\"Test department deletion\"\"\"\n        department = Department(\n            name='To Delete',\n            code='DELETE'\n        )\n        \n        db.session.add(department)\n        db.session.commit()\n        department_id = department.id\n        \n        # Delete department\n        db.session.delete(department)\n        db.session.commit()\n        \n        # Verify deletion\n        deleted_dept = Department.query.get(department_id)\n        assert deleted_dept is None\n\n    def test_department_search_functionality(self):\n        \"\"\"Test department search by name\"\"\"\n        departments = [\n            Department(name='Engineering Team', code='ENG'),\n            Department(name='Engineering Support', code='ENGS'),\n            Department(name='Marketing', code='MKT')\n        ]\n        \n        db.session.add_all(departments)\n        db.session.commit()\n        \n        # Search for departments containing 'Engineering'\n        eng_depts = Department.query.filter(Department.name.contains('Engineering')).all()\n        eng_names = [dept.name for dept in eng_depts]\n        \n        assert 'Engineering Team' in eng_names\n        assert 'Engineering Support' in eng_names\n        assert 'Marketing' not in eng_names\n\n    def test_department_default_values(self):\n        \"\"\"Test default values for optional fields\"\"\"\n        department = Department(\n            name='Default Test',\n            code='DEF'\n        )\n        \n        db.session.add(department)\n        db.session.commit()\n        \n        # Check default values\n        assert department.is_active is True  # Assuming default is True\n        assert department.budget is None     # Should be None if not set\n        assert department.manager_id is None # Should be None if not set\n        assert department.description is None # Should be None if not set\n\n    def test_department_employee_count(self):\n        \"\"\"Test counting employees in department (if relationship exists)\"\"\"\n        department = Department(\n            name='Test Department',\n            code='TEST'\n        )\n        \n        db.session.add(department)\n        db.session.commit()\n        \n        # This test assumes there's a relationship to count employees\n        # The actual implementation depends on how User-Department relationship is set up\n        assert hasattr(department, 'employees') or hasattr(department, 'users')\n\n    def test_department_hierarchy(self):\n        \"\"\"Test department hierarchy if parent-child relationships exist\"\"\"\n        parent_dept = Department(\n            name='Parent Department',\n            code='PARENT'\n        )\n        \n        db.session.add(parent_dept)\n        db.session.commit()\n        \n        # If there's a parent_id field for hierarchy\n        if hasattr(Department, 'parent_id'):\n            child_dept = Department(\n                name='Child Department',\n                code='CHILD',\n                parent_id=parent_dept.id\n            )\n            \n            db.session.add(child_dept)\n            db.session.commit()\n            \n            assert child_dept.parent_id == parent_dept.id\n"}