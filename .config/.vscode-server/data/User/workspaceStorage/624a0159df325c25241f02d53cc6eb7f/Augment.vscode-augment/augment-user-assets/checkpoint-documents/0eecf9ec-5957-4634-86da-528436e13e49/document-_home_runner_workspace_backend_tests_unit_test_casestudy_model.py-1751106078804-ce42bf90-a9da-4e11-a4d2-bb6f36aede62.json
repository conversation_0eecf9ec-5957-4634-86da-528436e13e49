{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_casestudy_model.py"}, "modifiedCode": "\"\"\"Unit tests for CaseStudy model.\"\"\"\nimport pytest\nfrom models import CaseStudy, User\nfrom extensions import db\n\nclass TestCaseStudyModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n\n    def test_casestudy_creation_basic(self):\n        case_study = CaseStudy(\n            title='Test Case Study',\n            description='This is a test case study',\n            industry='Technology',\n            created_by=self.user.id\n        )\n        db.session.add(case_study)\n        db.session.commit()\n        \n        assert case_study.id is not None\n        assert case_study.title == 'Test Case Study'\n        assert case_study.industry == 'Technology'\n\n    def test_casestudy_deletion(self):\n        case_study = CaseStudy(title='To Delete', description='Delete me', created_by=self.user.id)\n        db.session.add(case_study)\n        db.session.commit()\n        case_id = case_study.id\n        \n        db.session.delete(case_study)\n        db.session.commit()\n        \n        deleted = CaseStudy.query.get(case_id)\n        assert deleted is None\n"}