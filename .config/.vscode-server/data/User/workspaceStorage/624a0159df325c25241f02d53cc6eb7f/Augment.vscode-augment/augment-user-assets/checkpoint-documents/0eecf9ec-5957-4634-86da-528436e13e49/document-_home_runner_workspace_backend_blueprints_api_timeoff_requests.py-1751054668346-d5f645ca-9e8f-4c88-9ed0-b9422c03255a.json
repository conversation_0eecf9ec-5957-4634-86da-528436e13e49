{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}, "originalCode": "\"\"\"\nAPI Blueprint per la gestione delle richieste di ferie, permessi e smartworking.\nTask 3.1 - Timesheet Management System\n\"\"\"\n\nfrom flask import Blueprint, request, jsonify\nfrom flask_login import current_user, login_required\nfrom sqlalchemy import and_, or_, extract\nfrom datetime import datetime, date\n\nfrom models import TimeOffRequest, User\nfrom utils.api_utils import api_response, handle_api_error\nfrom utils.permissions import user_has_permission\nfrom extensions import db, csrf\n\napi_timeoff_requests = Blueprint('api_timeoff_requests', __name__)\n\n\n@api_timeoff_requests.route('/', methods=['GET'])\n@login_required\ndef get_time_off_requests():\n    \"\"\"Recupera lista richieste time-off con filtri\"\"\"\n    try:\n        # Parametri filtro\n        user_id = request.args.get('user_id', type=int)\n        request_type = request.args.get('type')  # vacation, leave, smartworking\n        status = request.args.get('status')  # pending, approved, rejected\n        start_date = request.args.get('start_date')  # YYYY-MM-DD\n        end_date = request.args.get('end_date')  # YYYY-MM-DD\n        year = request.args.get('year', type=int)\n        month = request.args.get('month', type=int)\n        \n        # Paginazione\n        page = request.args.get('page', type=int, default=1)\n        per_page = request.args.get('per_page', type=int, default=50)\n        \n        # Query base\n        query = TimeOffRequest.query\n        \n        # Controllo permessi\n        if not user_has_permission(current_user.role, 'view_all_time_off'):\n            # L'utente può vedere solo le proprie richieste\n            query = query.filter(TimeOffRequest.user_id == current_user.id)\n        \n        # Applica filtri\n        if user_id:\n            # Verifica permessi per vedere richieste di altri utenti\n            if not user_has_permission(current_user.role, 'view_all_time_off') and user_id != current_user.id:\n                return api_response(False, 'Non puoi visualizzare richieste di altri utenti', status_code=403)\n            query = query.filter(TimeOffRequest.user_id == user_id)\n            \n        if request_type:\n            query = query.filter(TimeOffRequest.type == request_type)\n            \n        if status:\n            query = query.filter(TimeOffRequest.status == status)\n            \n        if start_date:\n            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()\n            query = query.filter(TimeOffRequest.start_date >= start_date_obj)\n            \n        if end_date:\n            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()\n            query = query.filter(TimeOffRequest.end_date <= end_date_obj)\n            \n        if year:\n            query = query.filter(extract('year', TimeOffRequest.start_date) == year)\n            \n        if month:\n            query = query.filter(extract('month', TimeOffRequest.start_date) == month)\n        \n        # Ordina per data di sottomissione (più recenti prima)\n        query = query.order_by(TimeOffRequest.submission_date.desc())\n        \n        # Applica paginazione\n        paginated = query.paginate(\n            page=page, per_page=per_page, error_out=False\n        )\n        \n        # Prepara dati\n        requests_data = []\n        for req in paginated.items:\n            requests_data.append({\n                'id': req.id,\n                'user_id': req.user_id,\n                'user': {\n                    'id': req.user.id,\n                    'first_name': req.user.first_name,\n                    'last_name': req.user.last_name,\n                    'full_name': req.user.full_name\n                } if req.user else None,\n                'request_type': req.type,\n                'start_date': req.start_date.isoformat(),\n                'end_date': req.end_date.isoformat(),\n                'duration_days': req.duration_days,\n                'status': req.status,\n                'notes': req.notes,\n                'submission_date': req.submission_date.isoformat() if req.submission_date else None,\n                'approval_date': req.approval_date.isoformat() if req.approval_date else None,\n                'approved_by': req.approved_by,\n                'approver': {\n                    'id': req.approver.id,\n                    'first_name': req.approver.first_name,\n                    'last_name': req.approver.last_name,\n                    'full_name': req.approver.full_name\n                } if req.approver else None,\n                'rejection_reason': req.rejection_reason,\n                'created_at': req.created_at.isoformat(),\n                'updated_at': req.updated_at.isoformat()\n            })\n        \n        return api_response(\n            data={\n                'requests': requests_data,\n                'pagination': {\n                    'page': paginated.page,\n                    'pages': paginated.pages,\n                    'per_page': paginated.per_page,\n                    'total': paginated.total,\n                    'has_next': paginated.has_next,\n                    'has_prev': paginated.has_prev\n                }\n            },\n            message=f\"Recuperate {len(requests_data)} richieste time-off\"\n        )\n        \n    except Exception as e:\n        return handle_api_error(e)\n\n\n@api_timeoff_requests.route('/', methods=['POST'])\*************\n@login_required\ndef create_time_off_request():\n    \"\"\"Crea una nuova richiesta time-off\"\"\"\n    try:\n        data = request.get_json()\n        \n        # Validazione campi richiesti\n        required_fields = ['request_type', 'start_date', 'end_date']\n        for field in required_fields:\n            if field not in data:\n                return api_response(\n                    False,\n                    f'Campo {field} richiesto',\n                    status_code=400\n                )\n        \n        # Validazione tipo richiesta\n        valid_types = ['vacation', 'leave', 'smartworking']\n        if data['request_type'] not in valid_types:\n            return api_response(\n                False,\n                f'Tipo richiesta non valido. Valori ammessi: {\", \".join(valid_types)}',\n                status_code=400\n            )\n        \n        # Parsing date\n        try:\n            start_date = datetime.strptime(data['start_date'], '%Y-%m-%d').date()\n            end_date = datetime.strptime(data['end_date'], '%Y-%m-%d').date()\n        except ValueError:\n            return api_response(\n                False,\n                'Formato date non valido. Utilizzare YYYY-MM-DD',\n                status_code=400\n            )\n        \n        # Validazione logica date\n        if start_date > end_date:\n            return api_response(\n                False,\n                'La data di inizio non può essere successiva alla data di fine',\n                status_code=400\n            )\n        \n        if start_date < date.today():\n            return api_response(\n                False,\n                'Non è possibile creare richieste per date passate',\n                status_code=400\n            )\n        \n        # Verifica sovrapposizioni con richieste esistenti\n        overlapping = TimeOffRequest.query.filter(\n            and_(\n                TimeOffRequest.user_id == current_user.id,\n                TimeOffRequest.status.in_(['pending', 'approved']),\n                or_(\n                    and_(TimeOffRequest.start_date <= start_date, TimeOffRequest.end_date >= start_date),\n                    and_(TimeOffRequest.start_date <= end_date, TimeOffRequest.end_date >= end_date),\n                    and_(TimeOffRequest.start_date >= start_date, TimeOffRequest.end_date <= end_date)\n                )\n            )\n        ).first()\n        \n        if overlapping:\n            return api_response(\n                False,\n                f'Esiste già una richiesta {overlapping.request_type} dal {overlapping.start_date} al {overlapping.end_date}',\n                status_code=400\n            )\n        \n        # Crea nuova richiesta\n        time_off_request = TimeOffRequest(\n            user_id=current_user.id,\n            type=data['request_type'],  # Fix: campo DB è 'type', non 'request_type'\n            start_date=start_date,\n            end_date=end_date,\n            notes=data.get('notes', ''),\n            status='pending'\n        )\n        \n        db.session.add(time_off_request)\n        db.session.commit()\n        \n        return api_response(\n            data={\n                'id': time_off_request.id,\n                'request_type': time_off_request.type,  # Fix: campo DB è 'type'\n                'start_date': time_off_request.start_date.isoformat(),\n                'end_date': time_off_request.end_date.isoformat(),\n                'duration_days': time_off_request.duration_days,\n                'status': time_off_request.status\n            },\n            message='Richiesta time-off creata con successo'\n        )\n        \n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n\n@api_timeoff_requests.route('/<int:request_id>', methods=['GET'])\n@login_required\ndef get_time_off_request(request_id):\n    \"\"\"Recupera dettaglio singola richiesta time-off\"\"\"\n    try:\n        time_off_request = TimeOffRequest.query.get_or_404(request_id)\n        \n        # Controllo permessi\n        if not user_has_permission(current_user.role, 'view_all_time_off'):\n            if time_off_request.user_id != current_user.id:\n                return api_response(False, 'Non puoi visualizzare questa richiesta', status_code=403)\n        \n        return api_response(\n            data={\n                'id': time_off_request.id,\n                'user_id': time_off_request.user_id,\n                'user': {\n                    'id': time_off_request.user.id,\n                    'first_name': time_off_request.user.first_name,\n                    'last_name': time_off_request.user.last_name,\n                    'full_name': time_off_request.user.full_name\n                },\n                'request_type': time_off_request.request_type,\n                'start_date': time_off_request.start_date.isoformat(),\n                'end_date': time_off_request.end_date.isoformat(),\n                'duration_days': time_off_request.duration_days,\n                'status': time_off_request.status,\n                'notes': time_off_request.notes,\n                'submission_date': time_off_request.submission_date.isoformat() if time_off_request.submission_date else None,\n                'approval_date': time_off_request.approval_date.isoformat() if time_off_request.approval_date else None,\n                'approved_by': time_off_request.approved_by,\n                'approver': {\n                    'id': time_off_request.approver.id,\n                    'first_name': time_off_request.approver.first_name,\n                    'last_name': time_off_request.approver.last_name,\n                    'full_name': time_off_request.approver.full_name\n                } if time_off_request.approver else None,\n                'rejection_reason': time_off_request.rejection_reason,\n                'created_at': time_off_request.created_at.isoformat(),\n                'updated_at': time_off_request.updated_at.isoformat()\n            },\n            message=\"Dettaglio richiesta recuperato con successo\"\n        )\n        \n    except Exception as e:\n        return handle_api_error(e)\n\n\n@api_timeoff_requests.route('/<int:request_id>/approve', methods=['PUT'])\*************\n@login_required\ndef approve_time_off_request(request_id):\n    \"\"\"Approva una richiesta time-off\"\"\"\n    try:\n        # Controllo permessi\n        if not user_has_permission(current_user.role, 'approve_time_off'):\n            return api_response(False, 'Non hai i permessi per approvare richieste', status_code=403)\n\n        time_off_request = TimeOffRequest.query.get_or_404(request_id)\n\n        # Verifica stato\n        if time_off_request.status != 'pending':\n            return api_response(\n                False,\n                f'La richiesta è già stata {time_off_request.status}',\n                status_code=400\n            )\n\n        # Approva richiesta\n        time_off_request.status = 'approved'\n        time_off_request.approved_by = current_user.id\n        time_off_request.approval_date = datetime.utcnow()\n        time_off_request.rejection_reason = None  # Reset eventuale motivo rifiuto precedente\n\n        db.session.commit()\n\n        return api_response(\n            data={\n                'id': time_off_request.id,\n                'status': time_off_request.status,\n                'approved_by': time_off_request.approved_by,\n                'approval_date': time_off_request.approval_date.isoformat()\n            },\n            message=f'Richiesta {time_off_request.request_type} approvata con successo'\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n\n@api_timeoff_requests.route('/<int:request_id>/reject', methods=['PUT'])\*************\n@login_required\ndef reject_time_off_request(request_id):\n    \"\"\"Rifiuta una richiesta time-off\"\"\"\n    try:\n        # Controllo permessi\n        if not user_has_permission(current_user.role, 'approve_time_off'):\n            return api_response(False, 'Non hai i permessi per rifiutare richieste', status_code=403)\n\n        time_off_request = TimeOffRequest.query.get_or_404(request_id)\n\n        # Verifica stato\n        if time_off_request.status != 'pending':\n            return api_response(\n                False,\n                f'La richiesta è già stata {time_off_request.status}',\n                status_code=400\n            )\n\n        data = request.get_json() or {}\n        rejection_reason = data.get('reason', '')\n\n        if not rejection_reason:\n            return api_response(\n                False,\n                'Motivo del rifiuto richiesto',\n                status_code=400\n            )\n\n        # Rifiuta richiesta\n        time_off_request.status = 'rejected'\n        time_off_request.approved_by = current_user.id\n        time_off_request.approval_date = datetime.utcnow()\n        time_off_request.rejection_reason = rejection_reason\n\n        db.session.commit()\n\n        return api_response(\n            data={\n                'id': time_off_request.id,\n                'status': time_off_request.status,\n                'approved_by': time_off_request.approved_by,\n                'approval_date': time_off_request.approval_date.isoformat(),\n                'rejection_reason': time_off_request.rejection_reason\n            },\n            message=f'Richiesta {time_off_request.request_type} rifiutata'\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n\n@api_timeoff_requests.route('/<int:request_id>', methods=['DELETE'])\*************\n@login_required\ndef delete_time_off_request(request_id):\n    \"\"\"Elimina una richiesta time-off (solo se pending e propria)\"\"\"\n    try:\n        time_off_request = TimeOffRequest.query.get_or_404(request_id)\n\n        # Solo l'utente che ha creato la richiesta può eliminarla\n        if time_off_request.user_id != current_user.id:\n            return api_response(False, 'Puoi eliminare solo le tue richieste', status_code=403)\n\n        # Solo richieste pending possono essere eliminate\n        if time_off_request.status != 'pending':\n            return api_response(\n                False,\n                'Puoi eliminare solo richieste in attesa di approvazione',\n                status_code=400\n            )\n\n        db.session.delete(time_off_request)\n        db.session.commit()\n\n        return api_response(\n            message='Richiesta time-off eliminata con successo'\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n\n@api_timeoff_requests.route('/quotas', methods=['GET'])\n@login_required\ndef get_time_off_quotas():\n    \"\"\"Recupera le quote di time-off dell'utente corrente\"\"\"\n    try:\n        # Per ora restituiamo dati statici, in futuro da collegare al database\n        user_id = request.args.get('user_id', type=int)\n        \n        # Controllo permessi\n        if user_id and user_id != current_user.id and not user_has_permission(current_user.role, 'view_all_time_off'):\n            return api_response(False, 'Non puoi visualizzare quote di altri utenti', status_code=403)\n            \n        target_user_id = user_id or current_user.id\n        \n        # Assicuriamoci che l'utente esista\n        if not User.query.get(target_user_id):\n            return api_response(False, f'Utente con ID {target_user_id} non trovato', status_code=404)\n        \n        try:\n            # Calcolo ferie rimanenti\n            vacation_total = 26  # Giorni totali di ferie annuali\n            \n            # Calcolo ferie utilizzate\n            vacation_used = TimeOffRequest.query.filter(\n                TimeOffRequest.user_id == target_user_id,\n                TimeOffRequest.request_type == 'vacation',\n                TimeOffRequest.status == 'approved'\n            ).with_entities(\n                db.func.sum(TimeOffRequest.end_date - TimeOffRequest.start_date + 1)\n            ).scalar() or 0\n            \n            # Calcolo permessi utilizzati\n            leave_used = TimeOffRequest.query.filter(\n                TimeOffRequest.user_id == target_user_id,\n                TimeOffRequest.request_type == 'leave',\n                TimeOffRequest.status == 'approved'\n            ).with_entities(\n                db.func.sum(TimeOffRequest.end_date - TimeOffRequest.start_date + 1)\n            ).scalar() or 0\n            \n            # Calcolo giorni smart working utilizzati\n            smartworking_used = TimeOffRequest.query.filter(\n                TimeOffRequest.user_id == target_user_id,\n                TimeOffRequest.request_type == 'smartworking',\n                TimeOffRequest.status == 'approved'\n            ).with_entities(\n                db.func.sum(TimeOffRequest.end_date - TimeOffRequest.start_date + 1)\n            ).scalar() or 0\n        except Exception as query_error:\n            import traceback\n            print(f\"Errore query: {str(query_error)}\")\n            print(traceback.format_exc())\n            # In caso di errore nelle query, restituiamo valori predefiniti\n            return api_response(\n                data={\n                    'vacation': {'total': 26, 'used': 0, 'remaining': 26},\n                    'leave': {'total': 10, 'used': 0},\n                    'smartworking': {'used': 0}\n                },\n                message=\"Quote time-off predefinite (errore nel calcolo)\"\n            )\n        \n        return api_response(\n            data={\n                'vacation': {\n                    'total': vacation_total,\n                    'used': int(vacation_used),\n                    'remaining': vacation_total - int(vacation_used)\n                },\n                'leave': {\n                    'total': 10,  # Giorni totali di permessi annuali\n                    'used': int(leave_used)\n                },\n                'smartworking': {\n                    'used': int(smartworking_used)\n                }\n            },\n            message=\"Quote time-off recuperate con successo\"\n        )\n        \n    except Exception as e:\n        import traceback\n        print(f\"Errore generale in get_time_off_quotas: {str(e)}\")\n        print(traceback.format_exc())\n        # Restituisci una risposta valida anche in caso di errore\n        return api_response(\n            data={\n                'vacation': {'total': 26, 'used': 0, 'remaining': 26},\n                'leave': {'total': 10, 'used': 0},\n                'smartworking': {'used': 0}\n            },\n            message=\"Quote time-off predefinite (errore del server)\",\n            success=True  # Forza il successo per evitare errori client\n        )\n", "modifiedCode": "\"\"\"\nAPI Blueprint per la gestione delle richieste di ferie, permessi e smartworking.\nTask 3.1 - Timesheet Management System\n\"\"\"\n\nfrom flask import Blueprint, request, jsonify\nfrom flask_login import current_user, login_required\nfrom sqlalchemy import and_, or_, extract\nfrom datetime import datetime, date\n\nfrom models import TimeOffRequest, User\nfrom utils.api_utils import api_response, handle_api_error\nfrom utils.permissions import user_has_permission\nfrom extensions import db, csrf\n\napi_timeoff_requests = Blueprint('api_timeoff_requests', __name__)\n\n\n@api_timeoff_requests.route('/', methods=['GET'])\n@login_required\ndef get_time_off_requests():\n    \"\"\"Recupera lista richieste time-off con filtri\"\"\"\n    try:\n        # Parametri filtro\n        user_id = request.args.get('user_id', type=int)\n        request_type = request.args.get('type')  # vacation, leave, smartworking\n        status = request.args.get('status')  # pending, approved, rejected\n        start_date = request.args.get('start_date')  # YYYY-MM-DD\n        end_date = request.args.get('end_date')  # YYYY-MM-DD\n        year = request.args.get('year', type=int)\n        month = request.args.get('month', type=int)\n        \n        # Paginazione\n        page = request.args.get('page', type=int, default=1)\n        per_page = request.args.get('per_page', type=int, default=50)\n        \n        # Query base\n        query = TimeOffRequest.query\n        \n        # Controllo permessi\n        if not user_has_permission(current_user.role, 'view_all_time_off'):\n            # L'utente può vedere solo le proprie richieste\n            query = query.filter(TimeOffRequest.user_id == current_user.id)\n        \n        # Applica filtri\n        if user_id:\n            # Verifica permessi per vedere richieste di altri utenti\n            if not user_has_permission(current_user.role, 'view_all_time_off') and user_id != current_user.id:\n                return api_response(False, 'Non puoi visualizzare richieste di altri utenti', status_code=403)\n            query = query.filter(TimeOffRequest.user_id == user_id)\n            \n        if request_type:\n            query = query.filter(TimeOffRequest.type == request_type)\n            \n        if status:\n            query = query.filter(TimeOffRequest.status == status)\n            \n        if start_date:\n            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()\n            query = query.filter(TimeOffRequest.start_date >= start_date_obj)\n            \n        if end_date:\n            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()\n            query = query.filter(TimeOffRequest.end_date <= end_date_obj)\n            \n        if year:\n            query = query.filter(extract('year', TimeOffRequest.start_date) == year)\n            \n        if month:\n            query = query.filter(extract('month', TimeOffRequest.start_date) == month)\n        \n        # Ordina per data di sottomissione (più recenti prima)\n        query = query.order_by(TimeOffRequest.submission_date.desc())\n        \n        # Applica paginazione\n        paginated = query.paginate(\n            page=page, per_page=per_page, error_out=False\n        )\n        \n        # Prepara dati\n        requests_data = []\n        for req in paginated.items:\n            requests_data.append({\n                'id': req.id,\n                'user_id': req.user_id,\n                'user': {\n                    'id': req.user.id,\n                    'first_name': req.user.first_name,\n                    'last_name': req.user.last_name,\n                    'full_name': req.user.full_name\n                } if req.user else None,\n                'request_type': req.type,\n                'start_date': req.start_date.isoformat(),\n                'end_date': req.end_date.isoformat(),\n                'duration_days': req.duration_days,\n                'status': req.status,\n                'notes': req.notes,\n                'submission_date': req.submission_date.isoformat() if req.submission_date else None,\n                'approval_date': req.approval_date.isoformat() if req.approval_date else None,\n                'approved_by': req.approved_by,\n                'approver': {\n                    'id': req.approver.id,\n                    'first_name': req.approver.first_name,\n                    'last_name': req.approver.last_name,\n                    'full_name': req.approver.full_name\n                } if req.approver else None,\n                'rejection_reason': req.rejection_reason,\n                'created_at': req.created_at.isoformat(),\n                'updated_at': req.updated_at.isoformat()\n            })\n        \n        return api_response(\n            data={\n                'requests': requests_data,\n                'pagination': {\n                    'page': paginated.page,\n                    'pages': paginated.pages,\n                    'per_page': paginated.per_page,\n                    'total': paginated.total,\n                    'has_next': paginated.has_next,\n                    'has_prev': paginated.has_prev\n                }\n            },\n            message=f\"Recuperate {len(requests_data)} richieste time-off\"\n        )\n        \n    except Exception as e:\n        return handle_api_error(e)\n\n\n@api_timeoff_requests.route('/', methods=['POST'])\*************\n@login_required\ndef create_time_off_request():\n    \"\"\"Crea una nuova richiesta time-off\"\"\"\n    try:\n        data = request.get_json()\n        \n        # Validazione campi richiesti\n        required_fields = ['request_type', 'start_date', 'end_date']\n        for field in required_fields:\n            if field not in data:\n                return api_response(\n                    False,\n                    f'Campo {field} richiesto',\n                    status_code=400\n                )\n        \n        # Validazione tipo richiesta\n        valid_types = ['vacation', 'leave', 'smartworking']\n        if data['request_type'] not in valid_types:\n            return api_response(\n                False,\n                f'Tipo richiesta non valido. Valori ammessi: {\", \".join(valid_types)}',\n                status_code=400\n            )\n        \n        # Parsing date\n        try:\n            start_date = datetime.strptime(data['start_date'], '%Y-%m-%d').date()\n            end_date = datetime.strptime(data['end_date'], '%Y-%m-%d').date()\n        except ValueError:\n            return api_response(\n                False,\n                'Formato date non valido. Utilizzare YYYY-MM-DD',\n                status_code=400\n            )\n        \n        # Validazione logica date\n        if start_date > end_date:\n            return api_response(\n                False,\n                'La data di inizio non può essere successiva alla data di fine',\n                status_code=400\n            )\n        \n        if start_date < date.today():\n            return api_response(\n                False,\n                'Non è possibile creare richieste per date passate',\n                status_code=400\n            )\n        \n        # Verifica sovrapposizioni con richieste esistenti\n        overlapping = TimeOffRequest.query.filter(\n            and_(\n                TimeOffRequest.user_id == current_user.id,\n                TimeOffRequest.status.in_(['pending', 'approved']),\n                or_(\n                    and_(TimeOffRequest.start_date <= start_date, TimeOffRequest.end_date >= start_date),\n                    and_(TimeOffRequest.start_date <= end_date, TimeOffRequest.end_date >= end_date),\n                    and_(TimeOffRequest.start_date >= start_date, TimeOffRequest.end_date <= end_date)\n                )\n            )\n        ).first()\n        \n        if overlapping:\n            return api_response(\n                False,\n                f'Esiste già una richiesta {overlapping.type} dal {overlapping.start_date} al {overlapping.end_date}',\n                status_code=400\n            )\n        \n        # Crea nuova richiesta\n        time_off_request = TimeOffRequest(\n            user_id=current_user.id,\n            type=data['request_type'],  # Fix: campo DB è 'type', non 'request_type'\n            start_date=start_date,\n            end_date=end_date,\n            notes=data.get('notes', ''),\n            status='pending'\n        )\n        \n        db.session.add(time_off_request)\n        db.session.commit()\n        \n        return api_response(\n            data={\n                'id': time_off_request.id,\n                'request_type': time_off_request.type,  # Fix: campo DB è 'type'\n                'start_date': time_off_request.start_date.isoformat(),\n                'end_date': time_off_request.end_date.isoformat(),\n                'duration_days': time_off_request.duration_days,\n                'status': time_off_request.status\n            },\n            message='Richiesta time-off creata con successo'\n        )\n        \n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n\n@api_timeoff_requests.route('/<int:request_id>', methods=['GET'])\n@login_required\ndef get_time_off_request(request_id):\n    \"\"\"Recupera dettaglio singola richiesta time-off\"\"\"\n    try:\n        time_off_request = TimeOffRequest.query.get_or_404(request_id)\n        \n        # Controllo permessi\n        if not user_has_permission(current_user.role, 'view_all_time_off'):\n            if time_off_request.user_id != current_user.id:\n                return api_response(False, 'Non puoi visualizzare questa richiesta', status_code=403)\n        \n        return api_response(\n            data={\n                'id': time_off_request.id,\n                'user_id': time_off_request.user_id,\n                'user': {\n                    'id': time_off_request.user.id,\n                    'first_name': time_off_request.user.first_name,\n                    'last_name': time_off_request.user.last_name,\n                    'full_name': time_off_request.user.full_name\n                },\n                'request_type': time_off_request.request_type,\n                'start_date': time_off_request.start_date.isoformat(),\n                'end_date': time_off_request.end_date.isoformat(),\n                'duration_days': time_off_request.duration_days,\n                'status': time_off_request.status,\n                'notes': time_off_request.notes,\n                'submission_date': time_off_request.submission_date.isoformat() if time_off_request.submission_date else None,\n                'approval_date': time_off_request.approval_date.isoformat() if time_off_request.approval_date else None,\n                'approved_by': time_off_request.approved_by,\n                'approver': {\n                    'id': time_off_request.approver.id,\n                    'first_name': time_off_request.approver.first_name,\n                    'last_name': time_off_request.approver.last_name,\n                    'full_name': time_off_request.approver.full_name\n                } if time_off_request.approver else None,\n                'rejection_reason': time_off_request.rejection_reason,\n                'created_at': time_off_request.created_at.isoformat(),\n                'updated_at': time_off_request.updated_at.isoformat()\n            },\n            message=\"Dettaglio richiesta recuperato con successo\"\n        )\n        \n    except Exception as e:\n        return handle_api_error(e)\n\n\n@api_timeoff_requests.route('/<int:request_id>/approve', methods=['PUT'])\*************\n@login_required\ndef approve_time_off_request(request_id):\n    \"\"\"Approva una richiesta time-off\"\"\"\n    try:\n        # Controllo permessi\n        if not user_has_permission(current_user.role, 'approve_time_off'):\n            return api_response(False, 'Non hai i permessi per approvare richieste', status_code=403)\n\n        time_off_request = TimeOffRequest.query.get_or_404(request_id)\n\n        # Verifica stato\n        if time_off_request.status != 'pending':\n            return api_response(\n                False,\n                f'La richiesta è già stata {time_off_request.status}',\n                status_code=400\n            )\n\n        # Approva richiesta\n        time_off_request.status = 'approved'\n        time_off_request.approved_by = current_user.id\n        time_off_request.approval_date = datetime.utcnow()\n        time_off_request.rejection_reason = None  # Reset eventuale motivo rifiuto precedente\n\n        db.session.commit()\n\n        return api_response(\n            data={\n                'id': time_off_request.id,\n                'status': time_off_request.status,\n                'approved_by': time_off_request.approved_by,\n                'approval_date': time_off_request.approval_date.isoformat()\n            },\n            message=f'Richiesta {time_off_request.request_type} approvata con successo'\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n\n@api_timeoff_requests.route('/<int:request_id>/reject', methods=['PUT'])\*************\n@login_required\ndef reject_time_off_request(request_id):\n    \"\"\"Rifiuta una richiesta time-off\"\"\"\n    try:\n        # Controllo permessi\n        if not user_has_permission(current_user.role, 'approve_time_off'):\n            return api_response(False, 'Non hai i permessi per rifiutare richieste', status_code=403)\n\n        time_off_request = TimeOffRequest.query.get_or_404(request_id)\n\n        # Verifica stato\n        if time_off_request.status != 'pending':\n            return api_response(\n                False,\n                f'La richiesta è già stata {time_off_request.status}',\n                status_code=400\n            )\n\n        data = request.get_json() or {}\n        rejection_reason = data.get('reason', '')\n\n        if not rejection_reason:\n            return api_response(\n                False,\n                'Motivo del rifiuto richiesto',\n                status_code=400\n            )\n\n        # Rifiuta richiesta\n        time_off_request.status = 'rejected'\n        time_off_request.approved_by = current_user.id\n        time_off_request.approval_date = datetime.utcnow()\n        time_off_request.rejection_reason = rejection_reason\n\n        db.session.commit()\n\n        return api_response(\n            data={\n                'id': time_off_request.id,\n                'status': time_off_request.status,\n                'approved_by': time_off_request.approved_by,\n                'approval_date': time_off_request.approval_date.isoformat(),\n                'rejection_reason': time_off_request.rejection_reason\n            },\n            message=f'Richiesta {time_off_request.request_type} rifiutata'\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n\n@api_timeoff_requests.route('/<int:request_id>', methods=['DELETE'])\*************\n@login_required\ndef delete_time_off_request(request_id):\n    \"\"\"Elimina una richiesta time-off (solo se pending e propria)\"\"\"\n    try:\n        time_off_request = TimeOffRequest.query.get_or_404(request_id)\n\n        # Solo l'utente che ha creato la richiesta può eliminarla\n        if time_off_request.user_id != current_user.id:\n            return api_response(False, 'Puoi eliminare solo le tue richieste', status_code=403)\n\n        # Solo richieste pending possono essere eliminate\n        if time_off_request.status != 'pending':\n            return api_response(\n                False,\n                'Puoi eliminare solo richieste in attesa di approvazione',\n                status_code=400\n            )\n\n        db.session.delete(time_off_request)\n        db.session.commit()\n\n        return api_response(\n            message='Richiesta time-off eliminata con successo'\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n\n@api_timeoff_requests.route('/quotas', methods=['GET'])\n@login_required\ndef get_time_off_quotas():\n    \"\"\"Recupera le quote di time-off dell'utente corrente\"\"\"\n    try:\n        # Per ora restituiamo dati statici, in futuro da collegare al database\n        user_id = request.args.get('user_id', type=int)\n        \n        # Controllo permessi\n        if user_id and user_id != current_user.id and not user_has_permission(current_user.role, 'view_all_time_off'):\n            return api_response(False, 'Non puoi visualizzare quote di altri utenti', status_code=403)\n            \n        target_user_id = user_id or current_user.id\n        \n        # Assicuriamoci che l'utente esista\n        if not User.query.get(target_user_id):\n            return api_response(False, f'Utente con ID {target_user_id} non trovato', status_code=404)\n        \n        try:\n            # Calcolo ferie rimanenti\n            vacation_total = 26  # Giorni totali di ferie annuali\n            \n            # Calcolo ferie utilizzate\n            vacation_used = TimeOffRequest.query.filter(\n                TimeOffRequest.user_id == target_user_id,\n                TimeOffRequest.request_type == 'vacation',\n                TimeOffRequest.status == 'approved'\n            ).with_entities(\n                db.func.sum(TimeOffRequest.end_date - TimeOffRequest.start_date + 1)\n            ).scalar() or 0\n            \n            # Calcolo permessi utilizzati\n            leave_used = TimeOffRequest.query.filter(\n                TimeOffRequest.user_id == target_user_id,\n                TimeOffRequest.request_type == 'leave',\n                TimeOffRequest.status == 'approved'\n            ).with_entities(\n                db.func.sum(TimeOffRequest.end_date - TimeOffRequest.start_date + 1)\n            ).scalar() or 0\n            \n            # Calcolo giorni smart working utilizzati\n            smartworking_used = TimeOffRequest.query.filter(\n                TimeOffRequest.user_id == target_user_id,\n                TimeOffRequest.request_type == 'smartworking',\n                TimeOffRequest.status == 'approved'\n            ).with_entities(\n                db.func.sum(TimeOffRequest.end_date - TimeOffRequest.start_date + 1)\n            ).scalar() or 0\n        except Exception as query_error:\n            import traceback\n            print(f\"Errore query: {str(query_error)}\")\n            print(traceback.format_exc())\n            # In caso di errore nelle query, restituiamo valori predefiniti\n            return api_response(\n                data={\n                    'vacation': {'total': 26, 'used': 0, 'remaining': 26},\n                    'leave': {'total': 10, 'used': 0},\n                    'smartworking': {'used': 0}\n                },\n                message=\"Quote time-off predefinite (errore nel calcolo)\"\n            )\n        \n        return api_response(\n            data={\n                'vacation': {\n                    'total': vacation_total,\n                    'used': int(vacation_used),\n                    'remaining': vacation_total - int(vacation_used)\n                },\n                'leave': {\n                    'total': 10,  # Giorni totali di permessi annuali\n                    'used': int(leave_used)\n                },\n                'smartworking': {\n                    'used': int(smartworking_used)\n                }\n            },\n            message=\"Quote time-off recuperate con successo\"\n        )\n        \n    except Exception as e:\n        import traceback\n        print(f\"Errore generale in get_time_off_quotas: {str(e)}\")\n        print(traceback.format_exc())\n        # Restituisci una risposta valida anche in caso di errore\n        return api_response(\n            data={\n                'vacation': {'total': 26, 'used': 0, 'remaining': 26},\n                'leave': {'total': 10, 'used': 0},\n                'smartworking': {'used': 0}\n            },\n            message=\"Quote time-off predefinite (errore del server)\",\n            success=True  # Forza il successo per evitare errori client\n        )\n"}