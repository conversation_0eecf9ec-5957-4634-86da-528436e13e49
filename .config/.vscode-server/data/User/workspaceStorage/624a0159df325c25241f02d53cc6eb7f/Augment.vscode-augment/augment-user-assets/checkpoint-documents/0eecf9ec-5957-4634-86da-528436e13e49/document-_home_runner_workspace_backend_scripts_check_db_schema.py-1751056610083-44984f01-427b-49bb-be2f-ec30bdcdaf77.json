{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/scripts/check_db_schema.py"}, "modifiedCode": "#!/usr/bin/env python3\n\"\"\"\nScript per verificare la struttura reale del database\nMostra tutte le tabelle e le loro colonne\n\"\"\"\n\nimport sys\nimport os\nsys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))\n\nfrom app import create_app\nfrom extensions import db\nfrom sqlalchemy import inspect, text\n\ndef check_table_columns(table_name):\n    \"\"\"Verifica le colonne di una tabella specifica\"\"\"\n    app = create_app()\n    \n    with app.app_context():\n        try:\n            inspector = inspect(db.engine)\n            \n            if table_name not in inspector.get_table_names():\n                print(f\"❌ Tabella '{table_name}' NON TROVATA\")\n                return None\n                \n            columns = inspector.get_columns(table_name)\n            print(f\"\\n📋 TABELLA: {table_name}\")\n            print(\"=\" * 50)\n            \n            for col in columns:\n                nullable = \"NULL\" if col['nullable'] else \"NOT NULL\"\n                default = f\" DEFAULT: {col.get('default', 'None')}\" if col.get('default') else \"\"\n                print(f\"  {col['name']:<25} {str(col['type']):<20} {nullable}{default}\")\n                \n            return columns\n            \n        except Exception as e:\n            print(f\"❌ Errore: {e}\")\n            return None\n\ndef check_all_tables():\n    \"\"\"Verifica tutte le tabelle nel database\"\"\"\n    app = create_app()\n    \n    with app.app_context():\n        try:\n            inspector = inspect(db.engine)\n            tables = sorted(inspector.get_table_names())\n            \n            print(\"🗄️  DATABASE SCHEMA COMPLETO\")\n            print(\"=\" * 60)\n            print(f\"Totale tabelle: {len(tables)}\")\n            print()\n            \n            for table in tables:\n                check_table_columns(table)\n                print()\n                \n        except Exception as e:\n            print(f\"❌ Errore: {e}\")\n\ndef check_specific_tables():\n    \"\"\"Verifica le tabelle che abbiamo modificato nei test\"\"\"\n    tables_to_check = [\n        'personnel_rates',\n        'project_expenses', \n        'project_resources',\n        'project_kpis',\n        'task_dependencies',\n        'forum_topics',\n        'direct_messages',\n        'polls',\n        'contracts',\n        'company_events'\n    ]\n    \n    print(\"🔍 VERIFICA TABELLE MODIFICATE NEI TEST\")\n    print(\"=\" * 60)\n    \n    for table in tables_to_check:\n        check_table_columns(table)\n\ndef search_column_in_tables(column_name):\n    \"\"\"Cerca una colonna specifica in tutte le tabelle\"\"\"\n    app = create_app()\n    \n    with app.app_context():\n        try:\n            inspector = inspect(db.engine)\n            tables = inspector.get_table_names()\n            found_tables = []\n            \n            print(f\"🔍 CERCANDO COLONNA: '{column_name}'\")\n            print(\"=\" * 50)\n            \n            for table in tables:\n                columns = inspector.get_columns(table)\n                for col in columns:\n                    if column_name.lower() in col['name'].lower():\n                        found_tables.append((table, col['name'], col['type']))\n                        \n            if found_tables:\n                print(f\"✅ Trovata in {len(found_tables)} tabelle:\")\n                for table, col_name, col_type in found_tables:\n                    print(f\"  {table}.{col_name} ({col_type})\")\n            else:\n                print(f\"❌ Colonna '{column_name}' non trovata in nessuna tabella\")\n                \n        except Exception as e:\n            print(f\"❌ Errore: {e}\")\n\ndef main():\n    \"\"\"Funzione principale\"\"\"\n    if len(sys.argv) < 2:\n        print(\"Uso:\")\n        print(\"  python check_db_schema.py all                    # Tutte le tabelle\")\n        print(\"  python check_db_schema.py specific               # Tabelle modificate\")\n        print(\"  python check_db_schema.py table <nome_tabella>   # Tabella specifica\")\n        print(\"  python check_db_schema.py search <nome_colonna>  # Cerca colonna\")\n        return\n    \n    command = sys.argv[1]\n    \n    if command == \"all\":\n        check_all_tables()\n    elif command == \"specific\":\n        check_specific_tables()\n    elif command == \"table\" and len(sys.argv) > 2:\n        table_name = sys.argv[2]\n        check_table_columns(table_name)\n    elif command == \"search\" and len(sys.argv) > 2:\n        column_name = sys.argv[2]\n        search_column_in_tables(column_name)\n    else:\n        print(\"❌ Comando non riconosciuto\")\n\nif __name__ == \"__main__\":\n    main()\n"}