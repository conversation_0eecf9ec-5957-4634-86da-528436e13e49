{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/services/pre_invoicing_service.py"}, "originalCode": "\"\"\"\nServizio per la gestione delle pre-fatture italiane.\nGenera pre-fatture da timesheet entries e calcola tasse secondo normativa italiana.\n\"\"\"\n\nfrom datetime import date, datetime, timedelta\nfrom decimal import Decimal\nfrom typing import List, Dict, Optional, Tuple\nfrom sqlalchemy import and_, or_\nfrom extensions import db\nfrom models import (\n    PreInvoice, PreInvoiceLine, TimesheetEntry, Project, Client, \n    Contract, User, CompanyInvoicingSettings, IntegrationSettings\n)\n\n\nclass PreInvoicingService:\n    \"\"\"Servizio principale per la gestione delle pre-fatture\"\"\"\n    \n    def __init__(self):\n        self.company_settings = self._get_company_settings()\n    \n    def _get_company_settings(self) -> CompanyInvoicingSettings:\n        \"\"\"Ottiene le impostazioni aziendali per la fatturazione\"\"\"\n        settings = CompanyInvoicingSettings.query.first()\n        if not settings:\n            # Crea impostazioni di default se non esistono\n            settings = CompanyInvoicingSettings(\n                company_name=\"La Mia Azienda\",\n                default_vat_rate=Decimal('22.0'),\n                default_retention_rate=Decimal('20.0'),\n                default_payment_terms=30,\n                invoice_prefix=\"PRE\",\n                current_year=datetime.now().year,\n                last_number=0\n            )\n            db.session.add(settings)\n            db.session.commit()\n        return settings\n    \n    def get_billable_timesheet_entries(\n        self, \n        client_id: int, \n        period_start: date, \n        period_end: date,\n        project_ids: Optional[List[int]] = None\n    ) -> List[TimesheetEntry]:\n        \"\"\"\n        Ottiene tutte le timesheet entries fatturabili per un cliente nel periodo specificato\n        \"\"\"\n        query = db.session.query(TimesheetEntry).join(Project).join(Contract).filter(\n            and_(\n                Contract.client_id == client_id,\n                TimesheetEntry.date >= period_start,\n                TimesheetEntry.date <= period_end,\n                TimesheetEntry.billable == True,\n                TimesheetEntry.billing_status == 'unbilled'\n            )\n        )\n        \n        # Filtra per progetti specifici se richiesto\n        if project_ids:\n            query = query.filter(TimesheetEntry.project_id.in_(project_ids))\n        \n        return query.all()\n    \n    def calculate_billing_summary(\n        self, \n        client_id: int, \n        period_start: date, \n        period_end: date,\n        project_ids: Optional[List[int]] = None\n    ) -> Dict:\n        \"\"\"\n        Calcola un riassunto delle ore fatturabili per periodo\n        \"\"\"\n        entries = self.get_billable_timesheet_entries(\n            client_id, period_start, period_end, project_ids\n        )\n        \n        if not entries:\n            return {\n                'total_hours': Decimal('0'),\n                'total_amount': Decimal('0'),\n                'projects_summary': [],\n                'entries_count': 0\n            }\n        \n        # Raggruppa per progetto\n        projects_summary = {}\n        total_hours = Decimal('0')\n        total_amount = Decimal('0')\n        \n        for entry in entries:\n            project = entry.project\n            project_id = project.id\n            \n            if project_id not in projects_summary:\n                projects_summary[project_id] = {\n                    'project': {\n                        'id': project.id,\n                        'name': project.name,\n                        'contract': {\n                            'id': project.contract.id if project.contract else None,\n                            'contract_number': project.contract.contract_number if project.contract else None,\n                            'hourly_rate': project.contract.hourly_rate if project.contract else None\n                        }\n                    },\n                    'total_hours': Decimal('0'),\n                    'total_amount': Decimal('0'),\n                    'entries': []\n                }\n            \n            # Calcola tariffa: usa billing_rate dell'entry o hourly_rate del contratto\n            rate = Decimal(str(entry.billing_rate)) if entry.billing_rate else (\n                Decimal(str(project.contract.hourly_rate)) if project.contract and project.contract.hourly_rate else Decimal('0')\n            )\n            \n            hours = Decimal(str(entry.hours))\n            amount = hours * rate\n            \n            projects_summary[project_id]['total_hours'] += hours\n            projects_summary[project_id]['total_amount'] += amount\n            projects_summary[project_id]['entries'].append({\n                'id': entry.id,\n                'date': entry.date,\n                'hours': float(hours),\n                'rate': float(rate),\n                'amount': float(amount),\n                'description': entry.description,\n                'user': {\n                    'id': entry.user.id,\n                    'name': f\"{entry.user.first_name} {entry.user.last_name}\"\n                }\n            })\n            \n            total_hours += hours\n            total_amount += amount\n        \n        return {\n            'total_hours': float(total_hours),\n            'total_amount': float(total_amount),\n            'projects_summary': list(projects_summary.values()),\n            'entries_count': len(entries)\n        }\n    \n    def generate_pre_invoice(\n        self, \n        client_id: int, \n        period_start: date, \n        period_end: date,\n        created_by: int,\n        project_ids: Optional[List[int]] = None,\n        vat_rate: Optional[Decimal] = None,\n        retention_rate: Optional[Decimal] = None,\n        notes: Optional[str] = None\n    ) -> PreInvoice:\n        \"\"\"\n        Genera una pre-fattura da timesheet entries\n        \"\"\"\n        # Verifica che il cliente esista\n        client = Client.query.get(client_id)\n        if not client:\n            raise ValueError(f\"Cliente con ID {client_id} non trovato\")\n        \n        # Ottieni entries fatturabili\n        timesheet_entries = self.get_billable_timesheet_entries(\n            client_id, period_start, period_end, project_ids\n        )\n        \n        if not timesheet_entries:\n            raise ValueError(\n                f\"Nessuna timesheet entry fatturabile trovata per il cliente {client.name} \"\n                f\"nel periodo {period_start} - {period_end}\"\n            )\n        \n        # Usa tassi di default se non specificati\n        if vat_rate is None:\n            vat_rate = self.company_settings.default_vat_rate\n        if retention_rate is None:\n            retention_rate = self.company_settings.default_retention_rate\n        \n        # Genera numero pre-fattura\n        pre_invoice_number = self.company_settings.generate_next_number()\n        \n        # Crea pre-fattura\n        pre_invoice = PreInvoice(\n            client_id=client_id,\n            pre_invoice_number=pre_invoice_number,\n            billing_period_start=period_start,\n            billing_period_end=period_end,\n            generated_date=date.today(),\n            status='draft',\n            vat_rate=vat_rate,\n            retention_rate=retention_rate,\n            created_by=created_by,\n            notes=notes or f\"Pre-fattura per periodo {period_start} - {period_end}\"\n        )\n        \n        db.session.add(pre_invoice)\n        db.session.flush()  # Per ottenere l'ID\n        \n        # Raggruppa entries per progetto e crea righe\n        projects_data = self._group_entries_by_project(timesheet_entries)\n        \n        for project_id, project_data in projects_data.items():\n            # Crea riga pre-fattura\n            line = PreInvoiceLine(\n                pre_invoice_id=pre_invoice.id,\n                project_id=project_id,\n                description=project_data['description'],\n                total_hours=project_data['total_hours'],\n                hourly_rate=project_data['hourly_rate'],\n                total_amount=project_data['total_amount'],\n                timesheet_entries_ids=[entry.id for entry in project_data['entries']]\n            )\n            \n            db.session.add(line)\n            \n            # Marca entries come fatturate\n            for entry in project_data['entries']:\n                entry.billing_status = 'billed'\n        \n        # Calcola totali\n        pre_invoice.calculate_totals()\n        \n        # Aggiorna numerazione\n        db.session.add(self.company_settings)\n        \n        db.session.commit()\n        \n        return pre_invoice\n    \n    def _group_entries_by_project(self, entries: List[TimesheetEntry]) -> Dict:\n        \"\"\"\n        Raggruppa timesheet entries per progetto per creare le righe fattura\n        \"\"\"\n        projects_data = {}\n        \n        for entry in entries:\n            project = entry.project\n            project_id = project.id\n            \n            if project_id not in projects_data:\n                # Determina la tariffa oraria per questo progetto\n                default_rate = Decimal('0')\n                if project.contract and project.contract.hourly_rate:\n                    default_rate = Decimal(str(project.contract.hourly_rate))\n                \n                projects_data[project_id] = {\n                    'project': project,\n                    'description': f\"Servizi professionali - {project.name}\",\n                    'total_hours': Decimal('0'),\n                    'hourly_rate': default_rate,\n                    'total_amount': Decimal('0'),\n                    'entries': []\n                }\n            \n            # Calcola ore e importo per questa entry\n            hours = Decimal(str(entry.hours))\n            rate = Decimal(str(entry.billing_rate)) if entry.billing_rate else projects_data[project_id]['hourly_rate']\n            amount = hours * rate\n            \n            projects_data[project_id]['total_hours'] += hours\n            projects_data[project_id]['total_amount'] += amount\n            projects_data[project_id]['entries'].append(entry)\n            \n            # Aggiorna la tariffa media se necessario\n            if projects_data[project_id]['total_hours'] > 0:\n                projects_data[project_id]['hourly_rate'] = (\n                    projects_data[project_id]['total_amount'] / projects_data[project_id]['total_hours']\n                )\n        \n        return projects_data\n    \n    def get_pre_invoice_with_details(self, pre_invoice_id: int) -> Optional[Dict]:\n        \"\"\"\n        Ottiene una pre-fattura con tutti i dettagli per la visualizzazione\n        \"\"\"\n        pre_invoice = PreInvoice.query.get(pre_invoice_id)\n        if not pre_invoice:\n            return None\n        \n        # Prepara dati completi\n        lines_data = []\n        for line in pre_invoice.lines:\n            lines_data.append({\n                'id': line.id,\n                'project': {\n                    'id': line.project.id if line.project else None,\n                    'name': line.project.name if line.project else 'Progetto eliminato',\n                    'contract': {\n                        'id': line.project.contract.id if line.project and line.project.contract else None,\n                        'contract_number': line.project.contract.contract_number if line.project and line.project.contract else None\n                    } if line.project else None\n                },\n                'description': line.description,\n                'total_hours': float(line.total_hours),\n                'hourly_rate': float(line.hourly_rate),\n                'total_amount': float(line.total_amount),\n                'timesheet_entries_count': len(line.timesheet_entries_ids) if line.timesheet_entries_ids else 0\n            })\n        \n        return {\n            'id': pre_invoice.id,\n            'pre_invoice_number': pre_invoice.pre_invoice_number,\n            'client': {\n                'id': pre_invoice.client.id,\n                'name': pre_invoice.client.name,\n                'vat_number': pre_invoice.client.vat_number,\n                'fiscal_code': pre_invoice.client.fiscal_code,\n                'address': pre_invoice.client.address,\n                'email': pre_invoice.client.email,\n                'phone': pre_invoice.client.phone\n            },\n            'contract': {\n                'id': pre_invoice.contract.id if pre_invoice.contract else None,\n                'contract_number': pre_invoice.contract.contract_number if pre_invoice.contract else None\n            },\n            'billing_period_start': pre_invoice.billing_period_start.isoformat(),\n            'billing_period_end': pre_invoice.billing_period_end.isoformat(),\n            'generated_date': pre_invoice.generated_date.isoformat(),\n            'status': pre_invoice.status,\n            'display_status': pre_invoice.display_status,\n            'subtotal': float(pre_invoice.subtotal),\n            'vat_rate': float(pre_invoice.vat_rate),\n            'vat_amount': float(pre_invoice.vat_amount),\n            'retention_rate': float(pre_invoice.retention_rate),\n            'retention_amount': float(pre_invoice.retention_amount),\n            'total_amount': float(pre_invoice.total_amount),\n            'external_invoice_id': pre_invoice.external_invoice_id,\n            'external_status': pre_invoice.external_status,\n            'external_pdf_url': pre_invoice.external_pdf_url,\n            'notes': pre_invoice.notes,\n            'lines': lines_data,\n            'creator': {\n                'id': pre_invoice.creator.id,\n                'name': f\"{pre_invoice.creator.first_name} {pre_invoice.creator.last_name}\"\n            },\n            'created_at': pre_invoice.created_at.isoformat(),\n            'updated_at': pre_invoice.updated_at.isoformat(),\n            'can_edit': pre_invoice.can_edit,\n            'can_send_external': pre_invoice.can_send_external\n        }\n    \n    def update_pre_invoice_status(self, pre_invoice_id: int, new_status: str) -> PreInvoice:\n        \"\"\"\n        Aggiorna lo stato di una pre-fattura e invia a FattureInCloud se necessario\n        \"\"\"\n        valid_statuses = ['draft', 'ready', 'sent_external', 'invoiced']\n        if new_status not in valid_statuses:\n            raise ValueError(f\"Stato non valido: {new_status}. Valori ammessi: {valid_statuses}\")\n        \n        pre_invoice = PreInvoice.query.get(pre_invoice_id)\n        if not pre_invoice:\n            raise ValueError(f\"Pre-fattura con ID {pre_invoice_id} non trovata\")\n        \n        # Verifica transizioni di stato valide\n        if new_status == 'ready' and pre_invoice.status != 'draft':\n            raise ValueError(\"Solo le pre-fatture in bozza possono essere marcate come pronte\")\n        \n        if new_status == 'sent_external' and pre_invoice.status != 'ready':\n            raise ValueError(\"Solo le pre-fatture pronte possono essere inviate al sistema esterno\")\n        \n        # Se stiamo inviando al sistema esterno, verifica integrazione e invia\n        if new_status == 'sent_external':\n            from services.fattureincloud_service import FattureInCloudService\n            \n            # Verifica che l'integrazione FattureInCloud sia attiva\n            integration = IntegrationSettings.query.filter_by(\n                provider='fattureincloud',\n                is_active=True\n            ).first()\n            \n            if not integration:\n                raise ValueError(\"Integrazione FattureInCloud non configurata o non attiva\")\n            \n            # Invia a FattureInCloud\n            fic_service = FattureInCloudService()\n            result = fic_service.send_pre_invoice(pre_invoice_id)\n            \n            if not result['success']:\n                # Aggiorna l'errore nell'integrazione\n                integration.last_error = result['error']\n                integration.updated_at = datetime.utcnow()\n                db.session.commit()\n                \n                raise ValueError(f\"Errore nell'invio a FattureInCloud: {result['error']}\")\n            \n            # Il servizio FattureInCloud ha già aggiornato lo stato e i dati esterni\n            # Ricarica la pre-fattura per avere i dati aggiornati\n            db.session.refresh(pre_invoice)\n            return pre_invoice\n        \n        # Per tutti gli altri stati, aggiorna normalmente\n        pre_invoice.status = new_status\n        db.session.commit()\n        \n        return pre_invoice\n    \n    def delete_pre_invoice(self, pre_invoice_id: int) -> bool:\n        \"\"\"\n        Elimina una pre-fattura e rimette le timesheet entries come non fatturate\n        \"\"\"\n        pre_invoice = PreInvoice.query.get(pre_invoice_id)\n        if not pre_invoice:\n            raise ValueError(f\"Pre-fattura con ID {pre_invoice_id} non trovata\")\n        \n        # Non permettere eliminazione di pre-fatture già inviate\n        if pre_invoice.status in ['sent_external', 'invoiced']:\n            raise ValueError(\"Non è possibile eliminare pre-fatture già inviate o fatturate\")\n        \n        # Rimetti timesheet entries come non fatturate\n        for line in pre_invoice.lines:\n            if line.timesheet_entries_ids:\n                TimesheetEntry.query.filter(\n                    TimesheetEntry.id.in_(line.timesheet_entries_ids)\n                ).update({\n                    'billing_status': 'unbilled'\n                }, synchronize_session=False)\n        \n        # Elimina pre-fattura (le righe vengono eliminate automaticamente con cascade)\n        db.session.delete(pre_invoice)\n        db.session.commit()\n        \n        return True\n\n\nclass BillingDashboardService:\n    \"\"\"Servizio per le statistiche della dashboard fatturazione\"\"\"\n    \n    def get_billing_dashboard_stats(self) -> Dict:\n        \"\"\"\n        Ottiene tutte le statistiche per la dashboard fatturazione\n        \"\"\"\n        # Ore non fatturate totali\n        unbilled_entries = TimesheetEntry.query.filter(\n            and_(\n                TimesheetEntry.billable == True,\n                TimesheetEntry.billing_status == 'unbilled'\n            )\n        ).all()\n        \n        total_unbilled_hours = sum(Decimal(str(entry.hours)) for entry in unbilled_entries)\n        \n        # Calcola importo totale non fatturato\n        total_unbilled_amount = Decimal('0')\n        clients_unbilled = {}\n        \n        for entry in unbilled_entries:\n            # Determina la tariffa\n            rate = Decimal('0')\n            if entry.billing_rate:\n                rate = Decimal(str(entry.billing_rate))\n            elif entry.project and entry.project.contract and entry.project.contract.hourly_rate:\n                rate = Decimal(str(entry.project.contract.hourly_rate))\n            \n            amount = Decimal(str(entry.hours)) * rate\n            total_unbilled_amount += amount\n            \n            # Raggruppa per cliente\n            if entry.project and entry.project.contract:\n                client_id = entry.project.contract.client_id\n                client_name = entry.project.contract.client.name\n                \n                if client_id not in clients_unbilled:\n                    clients_unbilled[client_id] = {\n                        'id': client_id,\n                        'name': client_name,\n                        'unbilled_hours': Decimal('0'),\n                        'unbilled_amount': Decimal('0')\n                    }\n                \n                clients_unbilled[client_id]['unbilled_hours'] += Decimal(str(entry.hours))\n                clients_unbilled[client_id]['unbilled_amount'] += amount\n        \n        # Converti in formato serializzabile e ordina per importo\n        top_clients = sorted(\n            [\n                {\n                    'id': data['id'],\n                    'name': data['name'],\n                    'unbilled_hours': float(data['unbilled_hours']),\n                    'unbilled_amount': float(data['unbilled_amount'])\n                }\n                for data in clients_unbilled.values()\n            ],\n            key=lambda x: x['unbilled_amount'],\n            reverse=True\n        )[:5]  # Top 5 clienti\n        \n        # Pre-fatture recenti\n        recent_pre_invoices = PreInvoice.query.order_by(\n            PreInvoice.created_at.desc()\n        ).limit(5).all()\n        \n        recent_pre_invoices_data = [\n            {\n                'id': pi.id,\n                'pre_invoice_number': pi.pre_invoice_number,\n                'client_name': pi.client.name,\n                'total_amount': float(pi.total_amount),\n                'status': pi.status,\n                'display_status': pi.display_status,\n                'created_at': pi.created_at.isoformat()\n            }\n            for pi in recent_pre_invoices\n        ]\n        \n        return {\n            'total_unbilled_amount': float(total_unbilled_amount),\n            'total_unbilled_hours': float(total_unbilled_hours),\n            'clients_with_unbilled_count': len(clients_unbilled),\n            'top_unbilled_clients': top_clients,\n            'recent_pre_invoices': recent_pre_invoices_data,\n            'summary': {\n                'total_clients_with_billing': len(clients_unbilled),\n                'total_unbilled_entries': len(unbilled_entries),\n                'avg_hourly_rate': float(total_unbilled_amount / total_unbilled_hours) if total_unbilled_hours > 0 else 0\n            }\n        }\n    \n    def get_client_billing_summary(self, client_id: int) -> Dict:\n        \"\"\"\n        Ottiene riassunto fatturazione per un singolo cliente\n        \"\"\"\n        client = Client.query.get(client_id)\n        if not client:\n            raise ValueError(f\"Cliente con ID {client_id} non trovato\")\n        \n        # Ore non fatturate per questo cliente\n        unbilled_entries = TimesheetEntry.query.join(Project).join(Contract).filter(\n            and_(\n                Contract.client_id == client_id,\n                TimesheetEntry.billable == True,\n                TimesheetEntry.billing_status == 'unbilled'\n            )\n        ).all()\n        \n        # Raggruppa per progetto\n        projects_billing = {}\n        total_unbilled_hours = Decimal('0')\n        total_unbilled_amount = Decimal('0')\n        \n        for entry in unbilled_entries:\n            project = entry.project\n            project_id = project.id\n            \n            if project_id not in projects_billing:\n                projects_billing[project_id] = {\n                    'id': project.id,\n                    'name': project.name,\n                    'contract_id': project.contract.id if project.contract else None,\n                    'contract_number': project.contract.contract_number if project.contract else None,\n                    'total_hours': Decimal('0'),\n                    'billed_hours': Decimal('0'),\n                    'unbilled_hours': Decimal('0'),\n                    'unbilled_amount': Decimal('0')\n                }\n            \n            # Calcola tariffa e importo\n            rate = Decimal('0')\n            if entry.billing_rate:\n                rate = Decimal(str(entry.billing_rate))\n            elif project.contract and project.contract.hourly_rate:\n                rate = Decimal(str(project.contract.hourly_rate))\n            \n            hours = Decimal(str(entry.hours))\n            amount = hours * rate\n            \n            projects_billing[project_id]['unbilled_hours'] += hours\n            projects_billing[project_id]['unbilled_amount'] += amount\n            \n            total_unbilled_hours += hours\n            total_unbilled_amount += amount\n        \n        # Ottieni ore totali per progetto (incluse quelle già fatturate)\n        for project_id in projects_billing:\n            total_entries = TimesheetEntry.query.filter(\n                and_(\n                    TimesheetEntry.project_id == project_id,\n                    TimesheetEntry.billable == True\n                )\n            ).all()\n            \n            total_hours = sum(Decimal(str(entry.hours)) for entry in total_entries)\n            billed_hours = sum(\n                Decimal(str(entry.hours)) for entry in total_entries \n                if entry.billing_status == 'billed'\n            )\n            \n            projects_billing[project_id]['total_hours'] = total_hours\n            projects_billing[project_id]['billed_hours'] = billed_hours\n        \n        # Pre-fatture esistenti per questo cliente\n        client_pre_invoices = PreInvoice.query.filter_by(client_id=client_id).order_by(\n            PreInvoice.created_at.desc()\n        ).limit(10).all()\n        \n        pre_invoices_data = [\n            {\n                'id': pi.id,\n                'pre_invoice_number': pi.pre_invoice_number,\n                'total_amount': float(pi.total_amount),\n                'status': pi.status,\n                'display_status': pi.display_status,\n                'billing_period_start': pi.billing_period_start.isoformat(),\n                'billing_period_end': pi.billing_period_end.isoformat(),\n                'created_at': pi.created_at.isoformat()\n            }\n            for pi in client_pre_invoices\n        ]\n        \n        return {\n            'client': {\n                'id': client.id,\n                'name': client.name,\n                'vat_number': client.vat_number,\n                'fiscal_code': client.fiscal_code\n            },\n            'unbilled_summary': {\n                'total_hours': float(total_unbilled_hours),\n                'total_amount': float(total_unbilled_amount)\n            },\n            'projects': [\n                {\n                    'id': data['id'],\n                    'name': data['name'],\n                    'contract_id': data['contract_id'],\n                    'contract_number': data['contract_number'],\n                    'total_hours': float(data['total_hours']),\n                    'billed_hours': float(data['billed_hours']),\n                    'unbilled_hours': float(data['unbilled_hours']),\n                    'unbilled_amount': float(data['unbilled_amount'])\n                }\n                for data in projects_billing.values()\n            ],\n            'recent_pre_invoices': pre_invoices_data\n        }", "modifiedCode": "\"\"\"\nServizio per la gestione delle pre-fatture italiane.\nGenera pre-fatture da timesheet entries e calcola tasse secondo normativa italiana.\n\"\"\"\n\nfrom datetime import date, datetime, timedelta\nfrom decimal import Decimal\nfrom typing import List, Dict, Optional, Tuple\nfrom sqlalchemy import and_, or_\nfrom extensions import db\nfrom models import (\n    PreInvoice, PreInvoiceLine, TimesheetEntry, Project, Client, \n    Contract, User, CompanyInvoicingSettings, IntegrationSettings\n)\n\n\nclass PreInvoicingService:\n    \"\"\"Servizio principale per la gestione delle pre-fatture\"\"\"\n    \n    def __init__(self):\n        self.company_settings = self._get_company_settings()\n    \n    def _get_company_settings(self) -> CompanyInvoicingSettings:\n        \"\"\"Ottiene le impostazioni aziendali per la fatturazione\"\"\"\n        try:\n            settings = CompanyInvoicingSettings.query.first()\n        except Exception:\n            # Durante i test o se le tabelle non esistono ancora, ritorna None\n            settings = None\n\n        if not settings:\n            # Crea impostazioni di default se non esistono\n            settings = CompanyInvoicingSettings(\n                company_name=\"La Mia Azienda\",\n                default_vat_rate=Decimal('22.0'),\n                default_retention_rate=Decimal('20.0'),\n                default_payment_terms=30,\n                invoice_prefix=\"PRE\",\n                current_year=datetime.now().year,\n                last_number=0\n            )\n            db.session.add(settings)\n            db.session.commit()\n        return settings\n    \n    def get_billable_timesheet_entries(\n        self, \n        client_id: int, \n        period_start: date, \n        period_end: date,\n        project_ids: Optional[List[int]] = None\n    ) -> List[TimesheetEntry]:\n        \"\"\"\n        Ottiene tutte le timesheet entries fatturabili per un cliente nel periodo specificato\n        \"\"\"\n        query = db.session.query(TimesheetEntry).join(Project).join(Contract).filter(\n            and_(\n                Contract.client_id == client_id,\n                TimesheetEntry.date >= period_start,\n                TimesheetEntry.date <= period_end,\n                TimesheetEntry.billable == True,\n                TimesheetEntry.billing_status == 'unbilled'\n            )\n        )\n        \n        # Filtra per progetti specifici se richiesto\n        if project_ids:\n            query = query.filter(TimesheetEntry.project_id.in_(project_ids))\n        \n        return query.all()\n    \n    def calculate_billing_summary(\n        self, \n        client_id: int, \n        period_start: date, \n        period_end: date,\n        project_ids: Optional[List[int]] = None\n    ) -> Dict:\n        \"\"\"\n        Calcola un riassunto delle ore fatturabili per periodo\n        \"\"\"\n        entries = self.get_billable_timesheet_entries(\n            client_id, period_start, period_end, project_ids\n        )\n        \n        if not entries:\n            return {\n                'total_hours': Decimal('0'),\n                'total_amount': Decimal('0'),\n                'projects_summary': [],\n                'entries_count': 0\n            }\n        \n        # Raggruppa per progetto\n        projects_summary = {}\n        total_hours = Decimal('0')\n        total_amount = Decimal('0')\n        \n        for entry in entries:\n            project = entry.project\n            project_id = project.id\n            \n            if project_id not in projects_summary:\n                projects_summary[project_id] = {\n                    'project': {\n                        'id': project.id,\n                        'name': project.name,\n                        'contract': {\n                            'id': project.contract.id if project.contract else None,\n                            'contract_number': project.contract.contract_number if project.contract else None,\n                            'hourly_rate': project.contract.hourly_rate if project.contract else None\n                        }\n                    },\n                    'total_hours': Decimal('0'),\n                    'total_amount': Decimal('0'),\n                    'entries': []\n                }\n            \n            # Calcola tariffa: usa billing_rate dell'entry o hourly_rate del contratto\n            rate = Decimal(str(entry.billing_rate)) if entry.billing_rate else (\n                Decimal(str(project.contract.hourly_rate)) if project.contract and project.contract.hourly_rate else Decimal('0')\n            )\n            \n            hours = Decimal(str(entry.hours))\n            amount = hours * rate\n            \n            projects_summary[project_id]['total_hours'] += hours\n            projects_summary[project_id]['total_amount'] += amount\n            projects_summary[project_id]['entries'].append({\n                'id': entry.id,\n                'date': entry.date,\n                'hours': float(hours),\n                'rate': float(rate),\n                'amount': float(amount),\n                'description': entry.description,\n                'user': {\n                    'id': entry.user.id,\n                    'name': f\"{entry.user.first_name} {entry.user.last_name}\"\n                }\n            })\n            \n            total_hours += hours\n            total_amount += amount\n        \n        return {\n            'total_hours': float(total_hours),\n            'total_amount': float(total_amount),\n            'projects_summary': list(projects_summary.values()),\n            'entries_count': len(entries)\n        }\n    \n    def generate_pre_invoice(\n        self, \n        client_id: int, \n        period_start: date, \n        period_end: date,\n        created_by: int,\n        project_ids: Optional[List[int]] = None,\n        vat_rate: Optional[Decimal] = None,\n        retention_rate: Optional[Decimal] = None,\n        notes: Optional[str] = None\n    ) -> PreInvoice:\n        \"\"\"\n        Genera una pre-fattura da timesheet entries\n        \"\"\"\n        # Verifica che il cliente esista\n        client = Client.query.get(client_id)\n        if not client:\n            raise ValueError(f\"Cliente con ID {client_id} non trovato\")\n        \n        # Ottieni entries fatturabili\n        timesheet_entries = self.get_billable_timesheet_entries(\n            client_id, period_start, period_end, project_ids\n        )\n        \n        if not timesheet_entries:\n            raise ValueError(\n                f\"Nessuna timesheet entry fatturabile trovata per il cliente {client.name} \"\n                f\"nel periodo {period_start} - {period_end}\"\n            )\n        \n        # Usa tassi di default se non specificati\n        if vat_rate is None:\n            vat_rate = self.company_settings.default_vat_rate\n        if retention_rate is None:\n            retention_rate = self.company_settings.default_retention_rate\n        \n        # Genera numero pre-fattura\n        pre_invoice_number = self.company_settings.generate_next_number()\n        \n        # Crea pre-fattura\n        pre_invoice = PreInvoice(\n            client_id=client_id,\n            pre_invoice_number=pre_invoice_number,\n            billing_period_start=period_start,\n            billing_period_end=period_end,\n            generated_date=date.today(),\n            status='draft',\n            vat_rate=vat_rate,\n            retention_rate=retention_rate,\n            created_by=created_by,\n            notes=notes or f\"Pre-fattura per periodo {period_start} - {period_end}\"\n        )\n        \n        db.session.add(pre_invoice)\n        db.session.flush()  # Per ottenere l'ID\n        \n        # Raggruppa entries per progetto e crea righe\n        projects_data = self._group_entries_by_project(timesheet_entries)\n        \n        for project_id, project_data in projects_data.items():\n            # Crea riga pre-fattura\n            line = PreInvoiceLine(\n                pre_invoice_id=pre_invoice.id,\n                project_id=project_id,\n                description=project_data['description'],\n                total_hours=project_data['total_hours'],\n                hourly_rate=project_data['hourly_rate'],\n                total_amount=project_data['total_amount'],\n                timesheet_entries_ids=[entry.id for entry in project_data['entries']]\n            )\n            \n            db.session.add(line)\n            \n            # Marca entries come fatturate\n            for entry in project_data['entries']:\n                entry.billing_status = 'billed'\n        \n        # Calcola totali\n        pre_invoice.calculate_totals()\n        \n        # Aggiorna numerazione\n        db.session.add(self.company_settings)\n        \n        db.session.commit()\n        \n        return pre_invoice\n    \n    def _group_entries_by_project(self, entries: List[TimesheetEntry]) -> Dict:\n        \"\"\"\n        Raggruppa timesheet entries per progetto per creare le righe fattura\n        \"\"\"\n        projects_data = {}\n        \n        for entry in entries:\n            project = entry.project\n            project_id = project.id\n            \n            if project_id not in projects_data:\n                # Determina la tariffa oraria per questo progetto\n                default_rate = Decimal('0')\n                if project.contract and project.contract.hourly_rate:\n                    default_rate = Decimal(str(project.contract.hourly_rate))\n                \n                projects_data[project_id] = {\n                    'project': project,\n                    'description': f\"Servizi professionali - {project.name}\",\n                    'total_hours': Decimal('0'),\n                    'hourly_rate': default_rate,\n                    'total_amount': Decimal('0'),\n                    'entries': []\n                }\n            \n            # Calcola ore e importo per questa entry\n            hours = Decimal(str(entry.hours))\n            rate = Decimal(str(entry.billing_rate)) if entry.billing_rate else projects_data[project_id]['hourly_rate']\n            amount = hours * rate\n            \n            projects_data[project_id]['total_hours'] += hours\n            projects_data[project_id]['total_amount'] += amount\n            projects_data[project_id]['entries'].append(entry)\n            \n            # Aggiorna la tariffa media se necessario\n            if projects_data[project_id]['total_hours'] > 0:\n                projects_data[project_id]['hourly_rate'] = (\n                    projects_data[project_id]['total_amount'] / projects_data[project_id]['total_hours']\n                )\n        \n        return projects_data\n    \n    def get_pre_invoice_with_details(self, pre_invoice_id: int) -> Optional[Dict]:\n        \"\"\"\n        Ottiene una pre-fattura con tutti i dettagli per la visualizzazione\n        \"\"\"\n        pre_invoice = PreInvoice.query.get(pre_invoice_id)\n        if not pre_invoice:\n            return None\n        \n        # Prepara dati completi\n        lines_data = []\n        for line in pre_invoice.lines:\n            lines_data.append({\n                'id': line.id,\n                'project': {\n                    'id': line.project.id if line.project else None,\n                    'name': line.project.name if line.project else 'Progetto eliminato',\n                    'contract': {\n                        'id': line.project.contract.id if line.project and line.project.contract else None,\n                        'contract_number': line.project.contract.contract_number if line.project and line.project.contract else None\n                    } if line.project else None\n                },\n                'description': line.description,\n                'total_hours': float(line.total_hours),\n                'hourly_rate': float(line.hourly_rate),\n                'total_amount': float(line.total_amount),\n                'timesheet_entries_count': len(line.timesheet_entries_ids) if line.timesheet_entries_ids else 0\n            })\n        \n        return {\n            'id': pre_invoice.id,\n            'pre_invoice_number': pre_invoice.pre_invoice_number,\n            'client': {\n                'id': pre_invoice.client.id,\n                'name': pre_invoice.client.name,\n                'vat_number': pre_invoice.client.vat_number,\n                'fiscal_code': pre_invoice.client.fiscal_code,\n                'address': pre_invoice.client.address,\n                'email': pre_invoice.client.email,\n                'phone': pre_invoice.client.phone\n            },\n            'contract': {\n                'id': pre_invoice.contract.id if pre_invoice.contract else None,\n                'contract_number': pre_invoice.contract.contract_number if pre_invoice.contract else None\n            },\n            'billing_period_start': pre_invoice.billing_period_start.isoformat(),\n            'billing_period_end': pre_invoice.billing_period_end.isoformat(),\n            'generated_date': pre_invoice.generated_date.isoformat(),\n            'status': pre_invoice.status,\n            'display_status': pre_invoice.display_status,\n            'subtotal': float(pre_invoice.subtotal),\n            'vat_rate': float(pre_invoice.vat_rate),\n            'vat_amount': float(pre_invoice.vat_amount),\n            'retention_rate': float(pre_invoice.retention_rate),\n            'retention_amount': float(pre_invoice.retention_amount),\n            'total_amount': float(pre_invoice.total_amount),\n            'external_invoice_id': pre_invoice.external_invoice_id,\n            'external_status': pre_invoice.external_status,\n            'external_pdf_url': pre_invoice.external_pdf_url,\n            'notes': pre_invoice.notes,\n            'lines': lines_data,\n            'creator': {\n                'id': pre_invoice.creator.id,\n                'name': f\"{pre_invoice.creator.first_name} {pre_invoice.creator.last_name}\"\n            },\n            'created_at': pre_invoice.created_at.isoformat(),\n            'updated_at': pre_invoice.updated_at.isoformat(),\n            'can_edit': pre_invoice.can_edit,\n            'can_send_external': pre_invoice.can_send_external\n        }\n    \n    def update_pre_invoice_status(self, pre_invoice_id: int, new_status: str) -> PreInvoice:\n        \"\"\"\n        Aggiorna lo stato di una pre-fattura e invia a FattureInCloud se necessario\n        \"\"\"\n        valid_statuses = ['draft', 'ready', 'sent_external', 'invoiced']\n        if new_status not in valid_statuses:\n            raise ValueError(f\"Stato non valido: {new_status}. Valori ammessi: {valid_statuses}\")\n        \n        pre_invoice = PreInvoice.query.get(pre_invoice_id)\n        if not pre_invoice:\n            raise ValueError(f\"Pre-fattura con ID {pre_invoice_id} non trovata\")\n        \n        # Verifica transizioni di stato valide\n        if new_status == 'ready' and pre_invoice.status != 'draft':\n            raise ValueError(\"Solo le pre-fatture in bozza possono essere marcate come pronte\")\n        \n        if new_status == 'sent_external' and pre_invoice.status != 'ready':\n            raise ValueError(\"Solo le pre-fatture pronte possono essere inviate al sistema esterno\")\n        \n        # Se stiamo inviando al sistema esterno, verifica integrazione e invia\n        if new_status == 'sent_external':\n            from services.fattureincloud_service import FattureInCloudService\n            \n            # Verifica che l'integrazione FattureInCloud sia attiva\n            integration = IntegrationSettings.query.filter_by(\n                provider='fattureincloud',\n                is_active=True\n            ).first()\n            \n            if not integration:\n                raise ValueError(\"Integrazione FattureInCloud non configurata o non attiva\")\n            \n            # Invia a FattureInCloud\n            fic_service = FattureInCloudService()\n            result = fic_service.send_pre_invoice(pre_invoice_id)\n            \n            if not result['success']:\n                # Aggiorna l'errore nell'integrazione\n                integration.last_error = result['error']\n                integration.updated_at = datetime.utcnow()\n                db.session.commit()\n                \n                raise ValueError(f\"Errore nell'invio a FattureInCloud: {result['error']}\")\n            \n            # Il servizio FattureInCloud ha già aggiornato lo stato e i dati esterni\n            # Ricarica la pre-fattura per avere i dati aggiornati\n            db.session.refresh(pre_invoice)\n            return pre_invoice\n        \n        # Per tutti gli altri stati, aggiorna normalmente\n        pre_invoice.status = new_status\n        db.session.commit()\n        \n        return pre_invoice\n    \n    def delete_pre_invoice(self, pre_invoice_id: int) -> bool:\n        \"\"\"\n        Elimina una pre-fattura e rimette le timesheet entries come non fatturate\n        \"\"\"\n        pre_invoice = PreInvoice.query.get(pre_invoice_id)\n        if not pre_invoice:\n            raise ValueError(f\"Pre-fattura con ID {pre_invoice_id} non trovata\")\n        \n        # Non permettere eliminazione di pre-fatture già inviate\n        if pre_invoice.status in ['sent_external', 'invoiced']:\n            raise ValueError(\"Non è possibile eliminare pre-fatture già inviate o fatturate\")\n        \n        # Rimetti timesheet entries come non fatturate\n        for line in pre_invoice.lines:\n            if line.timesheet_entries_ids:\n                TimesheetEntry.query.filter(\n                    TimesheetEntry.id.in_(line.timesheet_entries_ids)\n                ).update({\n                    'billing_status': 'unbilled'\n                }, synchronize_session=False)\n        \n        # Elimina pre-fattura (le righe vengono eliminate automaticamente con cascade)\n        db.session.delete(pre_invoice)\n        db.session.commit()\n        \n        return True\n\n\nclass BillingDashboardService:\n    \"\"\"Servizio per le statistiche della dashboard fatturazione\"\"\"\n    \n    def get_billing_dashboard_stats(self) -> Dict:\n        \"\"\"\n        Ottiene tutte le statistiche per la dashboard fatturazione\n        \"\"\"\n        # Ore non fatturate totali\n        unbilled_entries = TimesheetEntry.query.filter(\n            and_(\n                TimesheetEntry.billable == True,\n                TimesheetEntry.billing_status == 'unbilled'\n            )\n        ).all()\n        \n        total_unbilled_hours = sum(Decimal(str(entry.hours)) for entry in unbilled_entries)\n        \n        # Calcola importo totale non fatturato\n        total_unbilled_amount = Decimal('0')\n        clients_unbilled = {}\n        \n        for entry in unbilled_entries:\n            # Determina la tariffa\n            rate = Decimal('0')\n            if entry.billing_rate:\n                rate = Decimal(str(entry.billing_rate))\n            elif entry.project and entry.project.contract and entry.project.contract.hourly_rate:\n                rate = Decimal(str(entry.project.contract.hourly_rate))\n            \n            amount = Decimal(str(entry.hours)) * rate\n            total_unbilled_amount += amount\n            \n            # Raggruppa per cliente\n            if entry.project and entry.project.contract:\n                client_id = entry.project.contract.client_id\n                client_name = entry.project.contract.client.name\n                \n                if client_id not in clients_unbilled:\n                    clients_unbilled[client_id] = {\n                        'id': client_id,\n                        'name': client_name,\n                        'unbilled_hours': Decimal('0'),\n                        'unbilled_amount': Decimal('0')\n                    }\n                \n                clients_unbilled[client_id]['unbilled_hours'] += Decimal(str(entry.hours))\n                clients_unbilled[client_id]['unbilled_amount'] += amount\n        \n        # Converti in formato serializzabile e ordina per importo\n        top_clients = sorted(\n            [\n                {\n                    'id': data['id'],\n                    'name': data['name'],\n                    'unbilled_hours': float(data['unbilled_hours']),\n                    'unbilled_amount': float(data['unbilled_amount'])\n                }\n                for data in clients_unbilled.values()\n            ],\n            key=lambda x: x['unbilled_amount'],\n            reverse=True\n        )[:5]  # Top 5 clienti\n        \n        # Pre-fatture recenti\n        recent_pre_invoices = PreInvoice.query.order_by(\n            PreInvoice.created_at.desc()\n        ).limit(5).all()\n        \n        recent_pre_invoices_data = [\n            {\n                'id': pi.id,\n                'pre_invoice_number': pi.pre_invoice_number,\n                'client_name': pi.client.name,\n                'total_amount': float(pi.total_amount),\n                'status': pi.status,\n                'display_status': pi.display_status,\n                'created_at': pi.created_at.isoformat()\n            }\n            for pi in recent_pre_invoices\n        ]\n        \n        return {\n            'total_unbilled_amount': float(total_unbilled_amount),\n            'total_unbilled_hours': float(total_unbilled_hours),\n            'clients_with_unbilled_count': len(clients_unbilled),\n            'top_unbilled_clients': top_clients,\n            'recent_pre_invoices': recent_pre_invoices_data,\n            'summary': {\n                'total_clients_with_billing': len(clients_unbilled),\n                'total_unbilled_entries': len(unbilled_entries),\n                'avg_hourly_rate': float(total_unbilled_amount / total_unbilled_hours) if total_unbilled_hours > 0 else 0\n            }\n        }\n    \n    def get_client_billing_summary(self, client_id: int) -> Dict:\n        \"\"\"\n        Ottiene riassunto fatturazione per un singolo cliente\n        \"\"\"\n        client = Client.query.get(client_id)\n        if not client:\n            raise ValueError(f\"Cliente con ID {client_id} non trovato\")\n        \n        # Ore non fatturate per questo cliente\n        unbilled_entries = TimesheetEntry.query.join(Project).join(Contract).filter(\n            and_(\n                Contract.client_id == client_id,\n                TimesheetEntry.billable == True,\n                TimesheetEntry.billing_status == 'unbilled'\n            )\n        ).all()\n        \n        # Raggruppa per progetto\n        projects_billing = {}\n        total_unbilled_hours = Decimal('0')\n        total_unbilled_amount = Decimal('0')\n        \n        for entry in unbilled_entries:\n            project = entry.project\n            project_id = project.id\n            \n            if project_id not in projects_billing:\n                projects_billing[project_id] = {\n                    'id': project.id,\n                    'name': project.name,\n                    'contract_id': project.contract.id if project.contract else None,\n                    'contract_number': project.contract.contract_number if project.contract else None,\n                    'total_hours': Decimal('0'),\n                    'billed_hours': Decimal('0'),\n                    'unbilled_hours': Decimal('0'),\n                    'unbilled_amount': Decimal('0')\n                }\n            \n            # Calcola tariffa e importo\n            rate = Decimal('0')\n            if entry.billing_rate:\n                rate = Decimal(str(entry.billing_rate))\n            elif project.contract and project.contract.hourly_rate:\n                rate = Decimal(str(project.contract.hourly_rate))\n            \n            hours = Decimal(str(entry.hours))\n            amount = hours * rate\n            \n            projects_billing[project_id]['unbilled_hours'] += hours\n            projects_billing[project_id]['unbilled_amount'] += amount\n            \n            total_unbilled_hours += hours\n            total_unbilled_amount += amount\n        \n        # Ottieni ore totali per progetto (incluse quelle già fatturate)\n        for project_id in projects_billing:\n            total_entries = TimesheetEntry.query.filter(\n                and_(\n                    TimesheetEntry.project_id == project_id,\n                    TimesheetEntry.billable == True\n                )\n            ).all()\n            \n            total_hours = sum(Decimal(str(entry.hours)) for entry in total_entries)\n            billed_hours = sum(\n                Decimal(str(entry.hours)) for entry in total_entries \n                if entry.billing_status == 'billed'\n            )\n            \n            projects_billing[project_id]['total_hours'] = total_hours\n            projects_billing[project_id]['billed_hours'] = billed_hours\n        \n        # Pre-fatture esistenti per questo cliente\n        client_pre_invoices = PreInvoice.query.filter_by(client_id=client_id).order_by(\n            PreInvoice.created_at.desc()\n        ).limit(10).all()\n        \n        pre_invoices_data = [\n            {\n                'id': pi.id,\n                'pre_invoice_number': pi.pre_invoice_number,\n                'total_amount': float(pi.total_amount),\n                'status': pi.status,\n                'display_status': pi.display_status,\n                'billing_period_start': pi.billing_period_start.isoformat(),\n                'billing_period_end': pi.billing_period_end.isoformat(),\n                'created_at': pi.created_at.isoformat()\n            }\n            for pi in client_pre_invoices\n        ]\n        \n        return {\n            'client': {\n                'id': client.id,\n                'name': client.name,\n                'vat_number': client.vat_number,\n                'fiscal_code': client.fiscal_code\n            },\n            'unbilled_summary': {\n                'total_hours': float(total_unbilled_hours),\n                'total_amount': float(total_unbilled_amount)\n            },\n            'projects': [\n                {\n                    'id': data['id'],\n                    'name': data['name'],\n                    'contract_id': data['contract_id'],\n                    'contract_number': data['contract_number'],\n                    'total_hours': float(data['total_hours']),\n                    'billed_hours': float(data['billed_hours']),\n                    'unbilled_hours': float(data['unbilled_hours']),\n                    'unbilled_amount': float(data['unbilled_amount'])\n                }\n                for data in projects_billing.values()\n            ],\n            'recent_pre_invoices': pre_invoices_data\n        }"}