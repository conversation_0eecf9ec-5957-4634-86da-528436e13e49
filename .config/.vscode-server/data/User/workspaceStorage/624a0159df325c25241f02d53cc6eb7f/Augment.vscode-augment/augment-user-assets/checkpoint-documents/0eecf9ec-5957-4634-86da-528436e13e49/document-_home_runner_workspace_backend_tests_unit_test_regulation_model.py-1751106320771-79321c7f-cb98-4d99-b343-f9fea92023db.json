{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_regulation_model.py"}, "originalCode": "\"\"\"Unit tests for Regulation model.\"\"\"\nimport pytest\nfrom models import Regulation, User\nfrom extensions import db\n\nclass TestRegulationModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n\n    def test_regulation_creation_basic(self):\n        regulation = Regulation(\n            title='Test Regulation',\n            content='This is test regulation content'\n            # Rimosso category e created_by - non esistono nel modello\n        )\n        db.session.add(regulation)\n        db.session.commit()\n        \n        assert regulation.id is not None\n        assert regulation.title == 'Test Regulation'\n        assert regulation.category == 'policy'\n\n    def test_regulation_deletion(self):\n        regulation = Regulation(title='To Delete', content='Delete me', created_by=self.user.id)\n        db.session.add(regulation)\n        db.session.commit()\n        reg_id = regulation.id\n        \n        db.session.delete(regulation)\n        db.session.commit()\n        \n        deleted = Regulation.query.get(reg_id)\n        assert deleted is None\n", "modifiedCode": "\"\"\"Unit tests for Regulation model.\"\"\"\nimport pytest\nfrom models import Regulation, User\nfrom extensions import db\n\nclass TestRegulationModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n\n    def test_regulation_creation_basic(self):\n        regulation = Regulation(\n            title='Test Regulation',\n            content='This is test regulation content'\n            # Rimosso category e created_by - non esistono nel modello\n        )\n        db.session.add(regulation)\n        db.session.commit()\n        \n        assert regulation.id is not None\n        assert regulation.title == 'Test Regulation'\n        assert regulation.category == 'policy'\n\n    def test_regulation_deletion(self):\n        regulation = Regulation(title='To Delete', content='Delete me', created_by=self.user.id)\n        db.session.add(regulation)\n        db.session.commit()\n        reg_id = regulation.id\n        \n        db.session.delete(regulation)\n        db.session.commit()\n        \n        deleted = Regulation.query.get(reg_id)\n        assert deleted is None\n"}