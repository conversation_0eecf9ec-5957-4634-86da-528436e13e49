{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/stores/timesheet.js"}, "originalCode": "import { defineStore } from 'pinia'\nimport { useAuthStore } from './auth'\n\nexport const useTimesheetStore = defineStore('timesheet', {\n  state: () => ({\n    // Dashboard stats\n    stats: {\n      weeklyHours: 0,\n      monthlyHours: 0,\n      pendingApprovals: 0,\n      efficiency: 0\n    },\n    \n    // Recent activities\n    recentActivities: [],\n    \n    // Pending approvals (for managers)\n    pendingApprovals: [],\n    \n    // User's timesheet status\n    myStatus: {\n      status: 'draft',\n      totalHours: 0,\n      billableHours: 0\n    },\n    \n    // Monthly timesheet data\n    currentMonth: new Date().getMonth() + 1,\n    currentYear: new Date().getFullYear(),\n    projectTasks: [],\n    monthlyEntries: {},\n    monthlyTimesheetStatus: null, // Stato del timesheet mensile: 'pending', 'confirmed', 'approved'\n    \n    // Available projects for timesheet\n    availableProjects: [],\n    \n    // Loading states\n    loading: {\n      dashboard: false,\n      monthlyData: false,\n      saving: false\n    },\n    \n    // Error states\n    error: null,\n    \n    // Cache timestamps\n    lastFetch: {\n      dashboard: null,\n      monthlyData: null,\n      projects: null\n    },\n\n    // Additional state for time-off requests\n    pendingTimeOffRequests: [],\n    \n    // Time-off quota information\n    vacationQuota: { total: 0, used: 0, remaining: 0 },\n    leaveQuota: { total: 0, used: 0 },\n    smartWorkingDays: 0\n  }),\n\n  getters: {\n    // Monthly calculations\n    totalHours: (state) => {\n      return Object.values(state.monthlyEntries).reduce((total, dayEntries) => {\n        return total + Object.values(dayEntries).reduce((dayTotal, hours) => dayTotal + (hours || 0), 0)\n      }, 0)\n    },\n    \n    billableHours: (state) => {\n      // Calculate based on project billable flags\n      return Object.values(state.monthlyEntries).reduce((total, dayEntries) => {\n        return total + Object.values(dayEntries).reduce((dayTotal, hours, index) => {\n          const project = state.projectTasks[Math.floor(index / state.daysInMonth?.length || 31)]\n          return dayTotal + (project?.billable ? (hours || 0) : 0)\n        }, 0)\n      }, 0)\n    },\n    \n    pendingHours: (state) => {\n      return state.myStatus.status === 'submitted' ? state.myStatus.totalHours : 0\n    },\n    \n    activeProjects: (state) => {\n      return state.projectTasks.length\n    },\n    \n    daysInMonth: (state) => {\n      const days = new Date(state.currentYear, state.currentMonth, 0).getDate()\n      return Array.from({ length: days }, (_, i) => i + 1)\n    },\n    \n    canApprove: () => {\n      const authStore = useAuthStore()\n      return authStore.user?.role === 'manager' || authStore.user?.role === 'admin'\n    },\n    \n    // Check if data needs refresh (5 minutes cache)\n    needsRefresh: (state) => {\n      const fiveMinutes = 5 * 60 * 1000\n      return {\n        dashboard: !state.lastFetch.dashboard || Date.now() - state.lastFetch.dashboard > fiveMinutes,\n        monthlyData: !state.lastFetch.monthlyData || Date.now() - state.lastFetch.monthlyData > fiveMinutes,\n        projects: !state.lastFetch.projects || Date.now() - state.lastFetch.projects > fiveMinutes\n      }\n    }\n  },\n\n  actions: {\n    // Dashboard data loading\n    async loadDashboardStats() {\n      if (this.loading.dashboard || !this.needsRefresh.dashboard) {\n        return\n      }\n\n      this.loading.dashboard = true\n      this.error = null\n\n      try {\n        const authStore = useAuthStore()\n        const response = await fetch('/api/dashboard/stats', {\n          headers: {\n            'Content-Type': 'application/json',\n            'X-CSRFToken': authStore.csrfToken\n          }\n        })\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n        }\n\n        const result = await response.json()\n        \n        this.stats = {\n          weeklyHours: result.data?.activities?.recent_timesheets || 0,\n          monthlyHours: result.data?.activities?.recent_timesheets || 0,\n          pendingApprovals: result.data?.activities?.unread_notifications || 0,\n          efficiency: 85 // TODO: Calculate from actual timesheet data\n        }\n        \n        this.lastFetch.dashboard = Date.now()\n      } catch (err) {\n        this.error = `Errore caricamento statistiche: ${err.message}`\n        console.error('Error loading dashboard stats:', err)\n      } finally {\n        this.loading.dashboard = false\n      }\n    },\n\n    async loadRecentActivities() {\n      try {\n        const authStore = useAuthStore()\n        const response = await fetch('/api/timesheets/?per_page=5&page=1', {\n          headers: {\n            'Content-Type': 'application/json',\n            'X-CSRFToken': authStore.csrfToken\n          }\n        })\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n        }\n\n        const result = await response.json()\n        const entries = result.data || []\n\n        this.recentActivities = entries.map(entry => ({\n          id: entry.id,\n          description: `${entry.project_name || 'Progetto'} - ${entry.task_name || 'Task'}`,\n          hours: entry.hours,\n          created_at: entry.created_at,\n          date: entry.date\n        }))\n      } catch (err) {\n        console.error('Error loading recent activities:', err)\n        this.recentActivities = []\n      }\n    },\n\n    async loadPendingApprovals() {\n      if (!this.canApprove) return\n\n      try {\n        const authStore = useAuthStore()\n        const response = await fetch('/api/monthly-timesheets/?status=submitted', {\n          headers: {\n            'Content-Type': 'application/json',\n            'X-CSRFToken': authStore.csrfToken\n          }\n        })\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n        }\n\n        const result = await response.json()\n        console.log('🔍 loadPendingApprovals API response:', result)\n        console.log('🔍 Extracted data:', result.data)\n        console.log('🔍 Timesheets array:', result.data?.timesheets)\n        this.pendingApprovals = result.data?.timesheets || []\n        console.log('🔍 Final pendingApprovals array:', this.pendingApprovals)\n      } catch (err) {\n        console.error('🚨 Error loading pending approvals:', err)\n        console.log('🚨 Setting pendingApprovals to empty array')\n        this.pendingApprovals = []\n      }\n    },\n\n    async loadMyStatus() {\n      if (this.canApprove) return\n\n      try {\n        const authStore = useAuthStore()\n        const response = await fetch('/api/monthly-timesheets/generate', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'X-CSRFToken': authStore.csrfToken\n          },\n          body: JSON.stringify({\n            year: this.currentYear,\n            month: this.currentMonth\n          })\n        })\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n        }\n\n        const result = await response.json()\n        this.myStatus = {\n          status: result.data?.status || 'draft',\n          totalHours: result.data?.total_hours || 0,\n          billableHours: result.data?.billable_hours || 0\n        }\n      } catch (err) {\n        console.error('Error loading my status:', err)\n      }\n    },\n\n    // Monthly timesheet data - using same API as PersonnelProfile\n    async loadMonthlyData(year = this.currentYear, month = this.currentMonth, userId = null) {\n      if (this.loading.monthlyData || (!this.needsRefresh.monthlyData && \n          year === this.currentYear && month === this.currentMonth)) {\n        return {\n          entries: this.monthlyEntries,\n          projects: this.projectTasks\n        }\n      }\n\n      this.loading.monthlyData = true\n      this.error = null\n\n      try {\n        const authStore = useAuthStore()\n        \n        // Calculate start and end dates exactly like PersonnelProfile.vue\n        const startDate = new Date(year, month - 1, 1).toISOString().split('T')[0]\n        const endDate = new Date(year, month, 0).toISOString().split('T')[0]\n        \n        // Use same API call as PersonnelProfile.vue but allow filtering by different user\n        const targetUserId = userId || authStore.user?.id || ''\n        const response = await fetch(`/api/timesheets?start_date=${startDate}&end_date=${endDate}&user_id=${targetUserId}`, {\n          headers: {\n            'Content-Type': 'application/json',\n            'X-CSRFToken': authStore.csrfToken\n          }\n        })\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n        }\n\n        const result = await response.json()\n        \n        this.currentYear = year\n        this.currentMonth = month\n        \n        // Check if response follows expected structure\n        if (!result.success) {\n          throw new Error(result.message || 'API response indicates failure')\n        }\n        \n        // Process timesheet entries into monthly format (same logic as PersonnelProfile)\n        const timesheets = result.data || []\n        const monthlyEntries = {}\n        const projectTasksSet = new Set()\n        \n        timesheets.forEach(timesheet => {\n          const entryDate = timesheet.date\n          const taskKey = `${timesheet.project_id}-${timesheet.task_id || 'notask'}`\n\n          if (!monthlyEntries[entryDate]) {\n            monthlyEntries[entryDate] = {}\n          }\n\n          if (!monthlyEntries[entryDate][taskKey]) {\n            monthlyEntries[entryDate][taskKey] = {\n              hours: 0,\n              billable: timesheet.billable || false\n            }\n          }\n\n          // If it's already an object, add hours; if it's a number (legacy), convert to object\n          if (typeof monthlyEntries[entryDate][taskKey] === 'object') {\n            monthlyEntries[entryDate][taskKey].hours += parseFloat(timesheet.hours || 0)\n            // Keep billable true if any entry is billable\n            monthlyEntries[entryDate][taskKey].billable = monthlyEntries[entryDate][taskKey].billable || timesheet.billable || false\n          } else {\n            // Convert legacy number to object\n            monthlyEntries[entryDate][taskKey] = {\n              hours: monthlyEntries[entryDate][taskKey] + parseFloat(timesheet.hours || 0),\n              billable: timesheet.billable || false\n            }\n          }\n          \n          // Track unique project-task combinations\n          projectTasksSet.add(JSON.stringify({\n            id: taskKey,\n            project_id: timesheet.project_id,\n            task_id: timesheet.task_id || null,\n            project_name: timesheet.project_name || 'Progetto Sconosciuto',\n            task_name: timesheet.task_name || 'Attività Generica',\n            billable: timesheet.billable || false\n          }))\n        })\n        \n        this.monthlyEntries = monthlyEntries\n        this.projectTasks = Array.from(projectTasksSet).map(pt => JSON.parse(pt))\n        \n        // Carica lo stato del timesheet mensile\n        await this.loadMonthlyTimesheetStatus(year, month)\n        \n        this.lastFetch.monthlyData = Date.now()\n        console.log('Loaded monthly data:', {\n          entries: Object.keys(this.monthlyEntries).length,\n          projects: this.projectTasks.length,\n          totalTimesheets: timesheets.length\n        })\n        \n        return {\n          entries: this.monthlyEntries,\n          projects: this.projectTasks\n        }\n      } catch (err) {\n        this.error = `Errore caricamento dati mensili: ${err.message}`\n        console.error('Error loading monthly data:', err)\n        return {\n          entries: {},\n          projects: []\n        }\n      } finally {\n        this.loading.monthlyData = false\n      }\n    },\n\n    // Available projects for timesheet\n    async loadAvailableProjects() {\n      if (this.availableProjects.length > 0 && !this.needsRefresh.projects) {\n        return this.availableProjects\n      }\n\n      try {\n        const authStore = useAuthStore()\n        const response = await fetch('/api/projects/', {\n          headers: {\n            'Content-Type': 'application/json',\n            'X-CSRFToken': authStore.csrfToken\n          }\n        })\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n        }\n\n        const result = await response.json()\n        \n        // Handle different response structures\n        let projects = []\n        if (result.data && Array.isArray(result.data.projects)) {\n          projects = result.data.projects\n        } else if (result.data && Array.isArray(result.data.items)) {\n          projects = result.data.items\n        } else if (result.data && Array.isArray(result.data)) {\n          projects = result.data\n        } else if (Array.isArray(result)) {\n          projects = result\n        }\n\n        this.availableProjects = projects.filter(p => p.status === 'active' || !p.status)\n        this.lastFetch.projects = Date.now()\n        console.log('Loaded available projects:', this.availableProjects.length, 'projects')\n        return this.availableProjects\n      } catch (err) {\n        console.error('Error loading available projects:', err)\n        this.availableProjects = []\n        return []\n      }\n    },\n\n    // Save timesheet entry using same API structure as PersonnelProfile.vue\n    async saveEntry(projectTaskId, day, hours) {\n      this.loading.saving = true\n      \n      try {\n        const authStore = useAuthStore()\n        \n        // Parse project and task IDs from projectTaskId\n        const [projectId, taskId] = projectTaskId.split('-')\n        \n        // Create date string\n        const dateStr = `${this.currentYear}-${this.currentMonth.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`\n        \n        // Use same payload structure as PersonnelProfile.vue\n        const payload = {\n          date: dateStr,\n          project_id: parseInt(projectId),\n          task_id: taskId !== 'notask' ? parseInt(taskId) : null,\n          hours: parseFloat(hours),\n          description: `Lavoro del ${dateStr}`\n        }\n        \n        const response = await fetch('/api/timesheets', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'X-CSRFToken': authStore.csrfToken\n          },\n          body: JSON.stringify(payload)\n        })\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n        }\n\n        const result = await response.json()\n        \n        // Check response success like PersonnelProfile.vue\n        if (!result.success) {\n          throw new Error(result.message || 'Errore durante il salvataggio')\n        }\n\n        // Update local state\n        const entryKey = dateStr\n        if (!this.monthlyEntries[entryKey]) {\n          this.monthlyEntries[entryKey] = {}\n        }\n        this.monthlyEntries[entryKey][projectTaskId] = {\n          hours: parseFloat(hours) || 0,\n          billable: false // Default for entries saved through grid\n        }\n        \n        console.log('Saved timesheet entry:', {\n          date: dateStr,\n          project: projectId,\n          task: taskId,\n          hours: hours,\n          success: true\n        })\n\n        return true\n      } catch (err) {\n        this.error = `Errore salvataggio ore: ${err.message}`\n        console.error('Error saving entry:', err)\n        return false\n      } finally {\n        this.loading.saving = false\n      }\n    },\n\n    // Add project to timesheet\n    async addProjectToTimesheet(projectId, taskId) {\n      try {\n        const authStore = useAuthStore()\n        const response = await fetch('/api/timesheet-projects/', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'X-CSRFToken': authStore.csrfToken\n          },\n          body: JSON.stringify({\n            project_id: projectId,\n            task_id: taskId\n          })\n        })\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n        }\n\n        // Refresh monthly data to include new project\n        await this.loadMonthlyData(this.currentYear, this.currentMonth)\n        return true\n      } catch (err) {\n        this.error = `Errore aggiunta progetto: ${err.message}`\n        console.error('Error adding project to timesheet:', err)\n        return false\n      }\n    },\n\n    // Approval actions\n    async approveTimesheet(timesheetId) {\n      try {\n        const authStore = useAuthStore()\n        const response = await fetch(`/api/monthly-timesheets/${timesheetId}/approve`, {\n          method: 'PUT',\n          headers: {\n            'Content-Type': 'application/json',\n            'X-CSRFToken': authStore.csrfToken\n          }\n        })\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n        }\n\n        // Refresh pending approvals\n        await this.loadPendingApprovals()\n        return true\n      } catch (err) {\n        this.error = `Errore approvazione: ${err.message}`\n        console.error('Error approving timesheet:', err)\n        return false\n      }\n    },\n\n    // Navigation helpers\n    navigateMonth(direction) {\n      if (direction === 'next') {\n        if (this.currentMonth === 12) {\n          this.currentMonth = 1\n          this.currentYear++\n        } else {\n          this.currentMonth++\n        }\n      } else {\n        if (this.currentMonth === 1) {\n          this.currentMonth = 12\n          this.currentYear--\n        } else {\n          this.currentMonth--\n        }\n      }\n\n      // Force refresh by invalidating cache\n      this.needsRefresh.monthlyData = true\n\n      // Load new month data\n      this.loadMonthlyData(this.currentYear, this.currentMonth)\n    },\n\n    // Refresh all data\n    async refreshAll() {\n      // Reset cache timestamps to force refresh\n      this.lastFetch = {\n        dashboard: null,\n        monthlyData: null,\n        projects: null\n      }\n      \n      await Promise.all([\n        this.loadDashboardStats(),\n        this.loadRecentActivities(),\n        this.loadPendingApprovals(),\n        this.loadMyStatus(),\n        this.loadMonthlyData()\n      ])\n    },\n\n    // Approval management\n    async loadPendingTimesheets(filters = {}) {\n      try {\n        const authStore = useAuthStore()\n        const params = new URLSearchParams({\n          month: filters.month || new Date().getMonth() + 1,\n          year: filters.year || new Date().getFullYear()\n        })\n\n        if (filters.status) params.append('status', filters.status)\n        if (filters.user_id) params.append('user_id', filters.user_id)\n        if (filters.search) params.append('search', filters.search)\n        if (filters.anomalies_only) params.append('anomalies_only', 'true')\n\n        const response = await fetch(`/api/monthly-timesheets/?${params}`, {\n          headers: {\n            'Content-Type': 'application/json',\n            'X-CSRFToken': authStore.csrfToken\n          }\n        })\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n        }\n\n        const result = await response.json()\n        console.log('🔍 loadPendingTimesheets API response:', result)\n        return result.data?.timesheets || []\n      } catch (err) {\n        this.error = `Errore caricamento timesheet: ${err.message}`\n        console.error('Error loading pending timesheets:', err)\n        return []\n      }\n    },\n\n    async rejectTimesheet(timesheetId, reason) {\n      try {\n        const authStore = useAuthStore()\n        const response = await fetch(`/api/monthly-timesheets/${timesheetId}/reject`, {\n          method: 'PUT',\n          headers: {\n            'Content-Type': 'application/json',\n            'X-CSRFToken': authStore.csrfToken\n          },\n          body: JSON.stringify({ reason })\n        })\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n        }\n\n        return true\n      } catch (err) {\n        this.error = `Errore rifiuto timesheet: ${err.message}`\n        console.error('Error rejecting timesheet:', err)\n        return false\n      }\n    },\n\n    // Analytics data loading\n    async loadAnalyticsData(filters = {}) {\n      try {\n        const authStore = useAuthStore()\n        const params = new URLSearchParams()\n\n        if (filters.start_date) params.append('start_date', filters.start_date)\n        if (filters.end_date) params.append('end_date', filters.end_date)\n        if (filters.department_id) params.append('department_id', filters.department_id)\n        if (filters.project_id) params.append('project_id', filters.project_id)\n        if (filters.analysis_type) params.append('analysis_type', filters.analysis_type)\n\n        const response = await fetch(`/api/timesheets/analytics/?${params}`, {\n          headers: {\n            'Content-Type': 'application/json',\n            'X-CSRFToken': authStore.csrfToken\n          }\n        })\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n        }\n\n        const result = await response.json()\n        \n        if (!result.success) {\n          throw new Error(result.message || 'API response indicates failure')\n        }\n        \n        return result.data || []\n      } catch (err) {\n        this.error = `Errore caricamento analytics: ${err.message}`\n        console.error('Error loading analytics data:', err)\n        return []\n      }\n    },\n\n    async loadTeamMembers() {\n      try {\n        const authStore = useAuthStore()\n        const response = await fetch('/api/personnel/users', {\n          headers: {\n            'Content-Type': 'application/json',\n            'X-CSRFToken': authStore.csrfToken\n          }\n        })\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n        }\n\n        const result = await response.json()\n        return result.data?.users || result.data || []\n      } catch (err) {\n        this.error = `Errore caricamento team: ${err.message}`\n        console.error('Error loading team members:', err)\n        return []\n      }\n    },\n\n    async loadDepartments() {\n      try {\n        const authStore = useAuthStore()\n        const response = await fetch('/api/personnel/departments', {\n          headers: {\n            'Content-Type': 'application/json',\n            'X-CSRFToken': authStore.csrfToken\n          }\n        })\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n        }\n\n        const result = await response.json()\n        return result.data?.departments || result.data || []\n      } catch (err) {\n        console.error('Error loading departments:', err)\n        return []\n      }\n    },\n\n    // Bulk operations\n    async bulkApproveTimesheets(timesheetIds) {\n      try {\n        const authStore = useAuthStore()\n        const results = await Promise.allSettled(\n          timesheetIds.map(id => this.approveTimesheet(id))\n        )\n\n        const successful = results.filter(r => r.status === 'fulfilled' && r.value).length\n        const failed = results.length - successful\n\n        if (failed > 0) {\n          this.error = `${successful} approvati, ${failed} falliti`\n        }\n\n        return { successful, failed }\n      } catch (err) {\n        this.error = `Errore approvazione multipla: ${err.message}`\n        return { successful: 0, failed: timesheetIds.length }\n      }\n    },\n\n    async bulkRejectTimesheets(timesheetIds, reason) {\n      try {\n        const results = await Promise.allSettled(\n          timesheetIds.map(id => this.rejectTimesheet(id, reason))\n        )\n\n        const successful = results.filter(r => r.status === 'fulfilled' && r.value).length\n        const failed = results.length - successful\n\n        if (failed > 0) {\n          this.error = `${successful} rifiutati, ${failed} falliti`\n        }\n\n        return { successful, failed }\n      } catch (err) {\n        this.error = `Errore rifiuto multiplo: ${err.message}`\n        return { successful: 0, failed: timesheetIds.length }\n      }\n    },\n\n    // Export functionality\n    async exportTimesheetData(filters = {}) {\n      try {\n        const authStore = useAuthStore()\n        const params = new URLSearchParams()\n        \n        Object.keys(filters).forEach(key => {\n          if (filters[key]) {\n            params.append(key, filters[key])\n          }\n        })\n\n        const response = await fetch(`/api/timesheets/export/?${params}`, {\n          headers: {\n            'Content-Type': 'application/json',\n            'X-CSRFToken': authStore.csrfToken\n          }\n        })\n\n        if (!response.ok) {\n          const errorData = await response.json()\n          throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`)\n        }\n\n        // Handle file download\n        const blob = await response.blob()\n        const url = window.URL.createObjectURL(blob)\n        const a = document.createElement('a')\n        a.href = url\n        a.download = `timesheet_export_${new Date().toISOString().split('T')[0]}.xlsx`\n        document.body.appendChild(a)\n        a.click()\n        window.URL.revokeObjectURL(url)\n        document.body.removeChild(a)\n\n        return true\n      } catch (err) {\n        this.error = `Errore export: ${err.message}`\n        console.error('Error exporting data:', err)\n        return false\n      }\n    },\n\n    // Additional methods needed by TimesheetEntry\n    async loadTimesheetHistory(filters = {}) {\n      try {\n        const authStore = useAuthStore()\n        const params = new URLSearchParams()\n        \n        Object.keys(filters).forEach(key => {\n          if (filters[key]) {\n            params.append(key, filters[key])\n          }\n        })\n\n        const response = await fetch(`/api/timesheets/?${params}`, {\n          headers: {\n            'Content-Type': 'application/json',\n            'X-CSRFToken': authStore.csrfToken\n          }\n        })\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n        }\n\n        const result = await response.json()\n        return result.data || []\n      } catch (err) {\n        this.error = `Errore caricamento storico: ${err.message}`\n        console.error('Error loading timesheet history:', err)\n        return []\n      }\n    },\n\n\n    async loadTimeOffRequests(filters = {}) {\n      try {\n        const authStore = useAuthStore()\n        const params = new URLSearchParams()\n        \n        Object.keys(filters).forEach(key => {\n          if (filters[key]) {\n            // Map request_type to type for API consistency\n            const apiKey = key === 'request_type' ? 'type' : key\n            params.append(apiKey, filters[key])\n          }\n        })\n\n        const response = await fetch(`/api/time-off-requests/?${params}`, {\n          headers: {\n            'Content-Type': 'application/json',\n            'X-CSRFToken': authStore.csrfToken\n          }\n        })\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n        }\n\n        const result = await response.json()\n        // Supporta diversi formati di risposta: preferisci array di richieste\n        if (Array.isArray(result.data)) {\n          return result.data\n        }\n        if (result.data && Array.isArray(result.data.requests)) {\n          return result.data.requests\n        }\n        // Fallback: nessun risultato valido\n        return []\n      } catch (err) {\n        this.error = `Errore caricamento richieste: ${err.message}`\n        console.error('Error loading time off requests:', err)\n        return []\n      }\n    },\n\n    async createTimeOffRequest(data) {\n      try {\n        const authStore = useAuthStore()\n        const response = await fetch('/api/time-off-requests/', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'X-CSRFToken': authStore.csrfToken\n          },\n          body: JSON.stringify(data)\n        })\n\n        if (!response.ok) {\n          // Leggi il corpo della risposta per ottenere il messaggio di errore specifico\n          const errorData = await response.json()\n          throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`)\n        }\n\n        return true\n      } catch (err) {\n        this.error = `${err.message}`\n        console.error('Error creating time off request:', err)\n        return false\n      }\n    },\n\n    async deleteTimeOffRequest(requestId) {\n      try {\n        const authStore = useAuthStore()\n        const response = await fetch(`/api/time-off-requests/${requestId}`, {\n          method: 'DELETE',\n          headers: {\n            'Content-Type': 'application/json',\n            'X-CSRFToken': authStore.csrfToken\n          }\n        })\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n        }\n\n        return true\n      } catch (err) {\n        this.error = `Errore eliminazione richiesta: ${err.message}`\n        console.error('Error deleting time off request:', err)\n        return false\n      }\n    },\n\n    async approveTimeOffRequest(requestId) {\n      try {\n        const authStore = useAuthStore()\n        const response = await fetch(`/api/time-off-requests/${requestId}/approve`, {\n          method: 'PUT',\n          headers: {\n            'Content-Type': 'application/json',\n            'X-CSRFToken': authStore.csrfToken\n          }\n        })\n\n        if (!response.ok) {\n          const errorData = await response.json()\n          throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`)\n        }\n\n        return true\n      } catch (err) {\n        this.error = `Errore approvazione richiesta: ${err.message}`\n        console.error('Error approving time off request:', err)\n        return false\n      }\n    },\n\n    async rejectTimeOffRequest(requestId, reason) {\n      try {\n        const authStore = useAuthStore()\n        const response = await fetch(`/api/time-off-requests/${requestId}/reject`, {\n          method: 'PUT',\n          headers: {\n            'Content-Type': 'application/json',\n            'X-CSRFToken': authStore.csrfToken\n          },\n          body: JSON.stringify({ reason })\n        })\n\n        if (!response.ok) {\n          const errorData = await response.json()\n          throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`)\n        }\n\n        return true\n      } catch (err) {\n        this.error = `Errore rifiuto richiesta: ${err.message}`\n        console.error('Error rejecting time off request:', err)\n        return false\n      }\n    },\n\n    // Clear error\n    clearError() {\n      this.error = null\n    },\n\n    // Aggiunta del metodo per caricare le quote di time-off\n    async loadTimeOffQuotas() {\n      try {\n        const authStore = useAuthStore()\n        const response = await fetch('/api/time-off-requests/quotas', {\n          headers: {\n            'Content-Type': 'application/json',\n            'X-CSRFToken': authStore.csrfToken\n          }\n        })\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n        }\n\n        const result = await response.json()\n        const quotaData = result.data || {\n          vacation: { remaining: 0, total: 0 },\n          leave: { used: 0, total: 0 },\n          smartworking: { used: 0 }\n        }\n        \n        // Aggiorna le proprietà dello state\n        this.vacationQuota = quotaData.vacation;\n        this.leaveQuota = quotaData.leave;\n        this.smartWorkingDays = quotaData.smartworking?.used || 0;\n        \n        return quotaData;\n      } catch (err) {\n        console.error('Error loading time-off quotas:', err)\n        \n        // Valori predefiniti in caso di errore\n        const defaultData = {\n          vacation: { remaining: 0, total: 0 },\n          leave: { used: 0, total: 0 },\n          smartworking: { used: 0 }\n        };\n        \n        // Aggiorna comunque le proprietà con valori predefiniti\n        this.vacationQuota = defaultData.vacation;\n        this.leaveQuota = defaultData.leave;\n        this.smartWorkingDays = 0;\n        \n        return defaultData;\n      }\n    },\n\n    // Set monthlyTimesheetStatus\n    setMonthlyTimesheetStatus(status) {\n      this.monthlyTimesheetStatus = status\n    },\n\n    // Load lo stato del timesheet mensile\n    async loadMonthlyTimesheetStatus(year = this.currentYear, month = this.currentMonth) {\n      try {\n        const authStore = useAuthStore()\n        const yearMonth = `${year}-${month.toString().padStart(2, '0')}`\n        \n        try {\n          const response = await fetch(`/api/timesheets/status?year_month=${yearMonth}&user_id=${authStore.user.id}`, {\n            headers: {\n              'Content-Type': 'application/json',\n              'X-CSRFToken': authStore.csrfToken\n            }\n          })\n\n          if (!response.ok) {\n            // Se l'endpoint non esiste (404) o altri errori, usa lo stato predefinito\n            this.monthlyTimesheetStatus = 'pending'\n            return 'pending'\n          }\n\n          const result = await response.json()\n          \n          if (result.success) {\n            this.monthlyTimesheetStatus = result.data?.status || 'pending'\n          } else {\n            this.monthlyTimesheetStatus = 'pending' // Default fallback\n          }\n        } catch (err) {\n          // In caso di errore di rete o altro, semplicemente usa lo stato predefinito\n          this.monthlyTimesheetStatus = 'pending'\n        }\n        \n        return this.monthlyTimesheetStatus\n      } catch (err) {\n        // Gestione errori silenziosa in attesa dell'implementazione backend\n        this.monthlyTimesheetStatus = 'pending' // Default fallback in caso di errore\n        return 'pending'\n      }\n    },\n\n    // Metodo per caricare le richieste di time-off in attesa\n    async loadPendingTimeOffRequests() {\n      try {\n        // Utilizziamo il metodo esistente loadTimeOffRequests con filtro per status=pending\n        const requests = await this.loadTimeOffRequests({ status: 'pending' });\n        // Salviamo le richieste pendenti nello store\n        this.pendingTimeOffRequests = requests;\n        return requests;\n      } catch (err) {\n        console.error('Error loading pending time-off requests:', err);\n        this.error = `Errore caricamento richieste in attesa: ${err.message}`;\n        this.pendingTimeOffRequests = [];\n        return [];\n      }\n    }\n  }\n})", "modifiedCode": "import { defineStore } from 'pinia'\nimport { useAuthStore } from './auth'\n\nexport const useTimesheetStore = defineStore('timesheet', {\n  state: () => ({\n    // Dashboard stats\n    stats: {\n      weeklyHours: 0,\n      monthlyHours: 0,\n      pendingApprovals: 0,\n      efficiency: 0\n    },\n    \n    // Recent activities\n    recentActivities: [],\n    \n    // Pending approvals (for managers)\n    pendingApprovals: [],\n    \n    // User's timesheet status\n    myStatus: {\n      status: 'draft',\n      totalHours: 0,\n      billableHours: 0\n    },\n    \n    // Monthly timesheet data\n    currentMonth: new Date().getMonth() + 1,\n    currentYear: new Date().getFullYear(),\n    projectTasks: [],\n    monthlyEntries: {},\n    monthlyTimesheetStatus: null, // Stato del timesheet mensile: 'pending', 'confirmed', 'approved'\n    \n    // Available projects for timesheet\n    availableProjects: [],\n    \n    // Loading states\n    loading: {\n      dashboard: false,\n      monthlyData: false,\n      saving: false\n    },\n    \n    // Error states\n    error: null,\n    \n    // Cache timestamps\n    lastFetch: {\n      dashboard: null,\n      monthlyData: null,\n      projects: null\n    },\n\n    // Additional state for time-off requests\n    pendingTimeOffRequests: [],\n    \n    // Time-off quota information\n    vacationQuota: { total: 0, used: 0, remaining: 0 },\n    leaveQuota: { total: 0, used: 0 },\n    smartWorkingDays: 0\n  }),\n\n  getters: {\n    // Monthly calculations\n    totalHours: (state) => {\n      return Object.values(state.monthlyEntries).reduce((total, dayEntries) => {\n        return total + Object.values(dayEntries).reduce((dayTotal, hours) => dayTotal + (hours || 0), 0)\n      }, 0)\n    },\n    \n    billableHours: (state) => {\n      // Calculate based on project billable flags\n      return Object.values(state.monthlyEntries).reduce((total, dayEntries) => {\n        return total + Object.values(dayEntries).reduce((dayTotal, hours, index) => {\n          const project = state.projectTasks[Math.floor(index / state.daysInMonth?.length || 31)]\n          return dayTotal + (project?.billable ? (hours || 0) : 0)\n        }, 0)\n      }, 0)\n    },\n    \n    pendingHours: (state) => {\n      return state.myStatus.status === 'submitted' ? state.myStatus.totalHours : 0\n    },\n    \n    activeProjects: (state) => {\n      return state.projectTasks.length\n    },\n    \n    daysInMonth: (state) => {\n      const days = new Date(state.currentYear, state.currentMonth, 0).getDate()\n      return Array.from({ length: days }, (_, i) => i + 1)\n    },\n    \n    canApprove: () => {\n      const authStore = useAuthStore()\n      return authStore.user?.role === 'manager' || authStore.user?.role === 'admin'\n    },\n    \n    // Check if data needs refresh (5 minutes cache)\n    needsRefresh: (state) => {\n      const fiveMinutes = 5 * 60 * 1000\n      return {\n        dashboard: !state.lastFetch.dashboard || Date.now() - state.lastFetch.dashboard > fiveMinutes,\n        monthlyData: !state.lastFetch.monthlyData || Date.now() - state.lastFetch.monthlyData > fiveMinutes,\n        projects: !state.lastFetch.projects || Date.now() - state.lastFetch.projects > fiveMinutes\n      }\n    }\n  },\n\n  actions: {\n    // Dashboard data loading\n    async loadDashboardStats() {\n      if (this.loading.dashboard || !this.needsRefresh.dashboard) {\n        return\n      }\n\n      this.loading.dashboard = true\n      this.error = null\n\n      try {\n        const authStore = useAuthStore()\n        const response = await fetch('/api/dashboard/stats', {\n          headers: {\n            'Content-Type': 'application/json',\n            'X-CSRFToken': authStore.csrfToken\n          }\n        })\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n        }\n\n        const result = await response.json()\n        \n        this.stats = {\n          weeklyHours: result.data?.activities?.recent_timesheets || 0,\n          monthlyHours: result.data?.activities?.recent_timesheets || 0,\n          pendingApprovals: result.data?.activities?.unread_notifications || 0,\n          efficiency: 85 // TODO: Calculate from actual timesheet data\n        }\n        \n        this.lastFetch.dashboard = Date.now()\n      } catch (err) {\n        this.error = `Errore caricamento statistiche: ${err.message}`\n        console.error('Error loading dashboard stats:', err)\n      } finally {\n        this.loading.dashboard = false\n      }\n    },\n\n    async loadRecentActivities() {\n      try {\n        const authStore = useAuthStore()\n        const response = await fetch('/api/timesheets/?per_page=5&page=1', {\n          headers: {\n            'Content-Type': 'application/json',\n            'X-CSRFToken': authStore.csrfToken\n          }\n        })\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n        }\n\n        const result = await response.json()\n        const entries = result.data || []\n\n        this.recentActivities = entries.map(entry => ({\n          id: entry.id,\n          description: `${entry.project_name || 'Progetto'} - ${entry.task_name || 'Task'}`,\n          hours: entry.hours,\n          created_at: entry.created_at,\n          date: entry.date\n        }))\n      } catch (err) {\n        console.error('Error loading recent activities:', err)\n        this.recentActivities = []\n      }\n    },\n\n    async loadPendingApprovals() {\n      if (!this.canApprove) return\n\n      try {\n        const authStore = useAuthStore()\n        const response = await fetch('/api/monthly-timesheets/?status=submitted', {\n          headers: {\n            'Content-Type': 'application/json',\n            'X-CSRFToken': authStore.csrfToken\n          }\n        })\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n        }\n\n        const result = await response.json()\n        console.log('🔍 loadPendingApprovals API response:', result)\n        console.log('🔍 Extracted data:', result.data)\n        console.log('🔍 Timesheets array:', result.data?.timesheets)\n        this.pendingApprovals = result.data?.timesheets || []\n        console.log('🔍 Final pendingApprovals array:', this.pendingApprovals)\n      } catch (err) {\n        console.error('🚨 Error loading pending approvals:', err)\n        console.log('🚨 Setting pendingApprovals to empty array')\n        this.pendingApprovals = []\n      }\n    },\n\n    async loadMyStatus() {\n      if (this.canApprove) return\n\n      try {\n        const authStore = useAuthStore()\n        const response = await fetch('/api/monthly-timesheets/generate', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'X-CSRFToken': authStore.csrfToken\n          },\n          body: JSON.stringify({\n            year: this.currentYear,\n            month: this.currentMonth\n          })\n        })\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n        }\n\n        const result = await response.json()\n        this.myStatus = {\n          status: result.data?.status || 'draft',\n          totalHours: result.data?.total_hours || 0,\n          billableHours: result.data?.billable_hours || 0\n        }\n      } catch (err) {\n        console.error('Error loading my status:', err)\n      }\n    },\n\n    // Monthly timesheet data - using same API as PersonnelProfile\n    async loadMonthlyData(year = this.currentYear, month = this.currentMonth, userId = null) {\n      if (this.loading.monthlyData || (!this.needsRefresh.monthlyData && \n          year === this.currentYear && month === this.currentMonth)) {\n        return {\n          entries: this.monthlyEntries,\n          projects: this.projectTasks\n        }\n      }\n\n      this.loading.monthlyData = true\n      this.error = null\n\n      try {\n        const authStore = useAuthStore()\n        \n        // Calculate start and end dates exactly like PersonnelProfile.vue\n        const startDate = new Date(year, month - 1, 1).toISOString().split('T')[0]\n        const endDate = new Date(year, month, 0).toISOString().split('T')[0]\n        \n        // Use same API call as PersonnelProfile.vue but allow filtering by different user\n        const targetUserId = userId || authStore.user?.id || ''\n        const response = await fetch(`/api/timesheets?start_date=${startDate}&end_date=${endDate}&user_id=${targetUserId}`, {\n          headers: {\n            'Content-Type': 'application/json',\n            'X-CSRFToken': authStore.csrfToken\n          }\n        })\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n        }\n\n        const result = await response.json()\n        \n        this.currentYear = year\n        this.currentMonth = month\n        \n        // Check if response follows expected structure\n        if (!result.success) {\n          throw new Error(result.message || 'API response indicates failure')\n        }\n        \n        // Process timesheet entries into monthly format (same logic as PersonnelProfile)\n        const timesheets = result.data || []\n        const monthlyEntries = {}\n        const projectTasksSet = new Set()\n        \n        timesheets.forEach(timesheet => {\n          const entryDate = timesheet.date\n          const taskKey = `${timesheet.project_id}-${timesheet.task_id || 'notask'}`\n\n          if (!monthlyEntries[entryDate]) {\n            monthlyEntries[entryDate] = {}\n          }\n\n          if (!monthlyEntries[entryDate][taskKey]) {\n            monthlyEntries[entryDate][taskKey] = {\n              hours: 0,\n              billable: timesheet.billable || false\n            }\n          }\n\n          // If it's already an object, add hours; if it's a number (legacy), convert to object\n          if (typeof monthlyEntries[entryDate][taskKey] === 'object') {\n            monthlyEntries[entryDate][taskKey].hours += parseFloat(timesheet.hours || 0)\n            // Keep billable true if any entry is billable\n            monthlyEntries[entryDate][taskKey].billable = monthlyEntries[entryDate][taskKey].billable || timesheet.billable || false\n          } else {\n            // Convert legacy number to object\n            monthlyEntries[entryDate][taskKey] = {\n              hours: monthlyEntries[entryDate][taskKey] + parseFloat(timesheet.hours || 0),\n              billable: timesheet.billable || false\n            }\n          }\n          \n          // Track unique project-task combinations\n          projectTasksSet.add(JSON.stringify({\n            id: taskKey,\n            project_id: timesheet.project_id,\n            task_id: timesheet.task_id || null,\n            project_name: timesheet.project_name || 'Progetto Sconosciuto',\n            task_name: timesheet.task_name || 'Attività Generica',\n            billable: timesheet.billable || false\n          }))\n        })\n        \n        this.monthlyEntries = monthlyEntries\n        this.projectTasks = Array.from(projectTasksSet).map(pt => JSON.parse(pt))\n        \n        // Carica lo stato del timesheet mensile\n        await this.loadMonthlyTimesheetStatus(year, month)\n        \n        this.lastFetch.monthlyData = Date.now()\n        console.log('Loaded monthly data:', {\n          entries: Object.keys(this.monthlyEntries).length,\n          projects: this.projectTasks.length,\n          totalTimesheets: timesheets.length\n        })\n        \n        return {\n          entries: this.monthlyEntries,\n          projects: this.projectTasks\n        }\n      } catch (err) {\n        this.error = `Errore caricamento dati mensili: ${err.message}`\n        console.error('Error loading monthly data:', err)\n        return {\n          entries: {},\n          projects: []\n        }\n      } finally {\n        this.loading.monthlyData = false\n      }\n    },\n\n    // Available projects for timesheet\n    async loadAvailableProjects() {\n      if (this.availableProjects.length > 0 && !this.needsRefresh.projects) {\n        return this.availableProjects\n      }\n\n      try {\n        const authStore = useAuthStore()\n        const response = await fetch('/api/projects/', {\n          headers: {\n            'Content-Type': 'application/json',\n            'X-CSRFToken': authStore.csrfToken\n          }\n        })\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n        }\n\n        const result = await response.json()\n        \n        // Handle different response structures\n        let projects = []\n        if (result.data && Array.isArray(result.data.projects)) {\n          projects = result.data.projects\n        } else if (result.data && Array.isArray(result.data.items)) {\n          projects = result.data.items\n        } else if (result.data && Array.isArray(result.data)) {\n          projects = result.data\n        } else if (Array.isArray(result)) {\n          projects = result\n        }\n\n        this.availableProjects = projects.filter(p => p.status === 'active' || !p.status)\n        this.lastFetch.projects = Date.now()\n        console.log('Loaded available projects:', this.availableProjects.length, 'projects')\n        return this.availableProjects\n      } catch (err) {\n        console.error('Error loading available projects:', err)\n        this.availableProjects = []\n        return []\n      }\n    },\n\n    // Save timesheet entry using same API structure as PersonnelProfile.vue\n    async saveEntry(projectTaskId, day, hours) {\n      this.loading.saving = true\n      \n      try {\n        const authStore = useAuthStore()\n        \n        // Parse project and task IDs from projectTaskId\n        const [projectId, taskId] = projectTaskId.split('-')\n        \n        // Create date string\n        const dateStr = `${this.currentYear}-${this.currentMonth.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`\n        \n        // Use same payload structure as PersonnelProfile.vue\n        const payload = {\n          date: dateStr,\n          project_id: parseInt(projectId),\n          task_id: taskId !== 'notask' ? parseInt(taskId) : null,\n          hours: parseFloat(hours),\n          description: `Lavoro del ${dateStr}`\n        }\n        \n        const response = await fetch('/api/timesheets', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'X-CSRFToken': authStore.csrfToken\n          },\n          body: JSON.stringify(payload)\n        })\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n        }\n\n        const result = await response.json()\n        \n        // Check response success like PersonnelProfile.vue\n        if (!result.success) {\n          throw new Error(result.message || 'Errore durante il salvataggio')\n        }\n\n        // Update local state\n        const entryKey = dateStr\n        if (!this.monthlyEntries[entryKey]) {\n          this.monthlyEntries[entryKey] = {}\n        }\n        this.monthlyEntries[entryKey][projectTaskId] = {\n          hours: parseFloat(hours) || 0,\n          billable: false // Default for entries saved through grid\n        }\n        \n        console.log('Saved timesheet entry:', {\n          date: dateStr,\n          project: projectId,\n          task: taskId,\n          hours: hours,\n          success: true\n        })\n\n        return true\n      } catch (err) {\n        this.error = `Errore salvataggio ore: ${err.message}`\n        console.error('Error saving entry:', err)\n        return false\n      } finally {\n        this.loading.saving = false\n      }\n    },\n\n    // Add project to timesheet\n    async addProjectToTimesheet(projectId, taskId) {\n      try {\n        const authStore = useAuthStore()\n        const response = await fetch('/api/timesheet-projects/', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'X-CSRFToken': authStore.csrfToken\n          },\n          body: JSON.stringify({\n            project_id: projectId,\n            task_id: taskId\n          })\n        })\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n        }\n\n        // Refresh monthly data to include new project\n        await this.loadMonthlyData(this.currentYear, this.currentMonth)\n        return true\n      } catch (err) {\n        this.error = `Errore aggiunta progetto: ${err.message}`\n        console.error('Error adding project to timesheet:', err)\n        return false\n      }\n    },\n\n    // Approval actions\n    async approveTimesheet(timesheetId) {\n      try {\n        const authStore = useAuthStore()\n        const response = await fetch(`/api/monthly-timesheets/${timesheetId}/approve`, {\n          method: 'PUT',\n          headers: {\n            'Content-Type': 'application/json',\n            'X-CSRFToken': authStore.csrfToken\n          }\n        })\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n        }\n\n        // Refresh pending approvals\n        await this.loadPendingApprovals()\n        return true\n      } catch (err) {\n        this.error = `Errore approvazione: ${err.message}`\n        console.error('Error approving timesheet:', err)\n        return false\n      }\n    },\n\n    // Navigation helpers\n    navigateMonth(direction) {\n      if (direction === 'next') {\n        if (this.currentMonth === 12) {\n          this.currentMonth = 1\n          this.currentYear++\n        } else {\n          this.currentMonth++\n        }\n      } else {\n        if (this.currentMonth === 1) {\n          this.currentMonth = 12\n          this.currentYear--\n        } else {\n          this.currentMonth--\n        }\n      }\n\n      // Force refresh by invalidating cache\n      this.needsRefresh.monthlyData = true\n\n      // Load new month data\n      this.loadMonthlyData(this.currentYear, this.currentMonth)\n    },\n\n    // Refresh all data\n    async refreshAll() {\n      // Reset cache timestamps to force refresh\n      this.lastFetch = {\n        dashboard: null,\n        monthlyData: null,\n        projects: null\n      }\n      \n      await Promise.all([\n        this.loadDashboardStats(),\n        this.loadRecentActivities(),\n        this.loadPendingApprovals(),\n        this.loadMyStatus(),\n        this.loadMonthlyData()\n      ])\n    },\n\n    // Approval management\n    async loadPendingTimesheets(filters = {}) {\n      try {\n        const authStore = useAuthStore()\n        const params = new URLSearchParams({\n          month: filters.month || new Date().getMonth() + 1,\n          year: filters.year || new Date().getFullYear()\n        })\n\n        if (filters.status) params.append('status', filters.status)\n        if (filters.user_id) params.append('user_id', filters.user_id)\n        if (filters.search) params.append('search', filters.search)\n        if (filters.anomalies_only) params.append('anomalies_only', 'true')\n\n        const response = await fetch(`/api/monthly-timesheets/?${params}`, {\n          headers: {\n            'Content-Type': 'application/json',\n            'X-CSRFToken': authStore.csrfToken\n          }\n        })\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n        }\n\n        const result = await response.json()\n        console.log('🔍 loadPendingTimesheets API response:', result)\n        return result.data?.timesheets || []\n      } catch (err) {\n        this.error = `Errore caricamento timesheet: ${err.message}`\n        console.error('Error loading pending timesheets:', err)\n        return []\n      }\n    },\n\n    async rejectTimesheet(timesheetId, reason) {\n      try {\n        const authStore = useAuthStore()\n        const response = await fetch(`/api/monthly-timesheets/${timesheetId}/reject`, {\n          method: 'PUT',\n          headers: {\n            'Content-Type': 'application/json',\n            'X-CSRFToken': authStore.csrfToken\n          },\n          body: JSON.stringify({ reason })\n        })\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n        }\n\n        return true\n      } catch (err) {\n        this.error = `Errore rifiuto timesheet: ${err.message}`\n        console.error('Error rejecting timesheet:', err)\n        return false\n      }\n    },\n\n    // Analytics data loading\n    async loadAnalyticsData(filters = {}) {\n      try {\n        const authStore = useAuthStore()\n        const params = new URLSearchParams()\n\n        if (filters.start_date) params.append('start_date', filters.start_date)\n        if (filters.end_date) params.append('end_date', filters.end_date)\n        if (filters.department_id) params.append('department_id', filters.department_id)\n        if (filters.project_id) params.append('project_id', filters.project_id)\n        if (filters.analysis_type) params.append('analysis_type', filters.analysis_type)\n\n        const response = await fetch(`/api/timesheets/analytics/?${params}`, {\n          headers: {\n            'Content-Type': 'application/json',\n            'X-CSRFToken': authStore.csrfToken\n          }\n        })\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n        }\n\n        const result = await response.json()\n        \n        if (!result.success) {\n          throw new Error(result.message || 'API response indicates failure')\n        }\n        \n        return result.data || []\n      } catch (err) {\n        this.error = `Errore caricamento analytics: ${err.message}`\n        console.error('Error loading analytics data:', err)\n        return []\n      }\n    },\n\n    async loadTeamMembers() {\n      try {\n        const authStore = useAuthStore()\n        const response = await fetch('/api/personnel/users', {\n          headers: {\n            'Content-Type': 'application/json',\n            'X-CSRFToken': authStore.csrfToken\n          }\n        })\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n        }\n\n        const result = await response.json()\n        return result.data?.users || result.data || []\n      } catch (err) {\n        this.error = `Errore caricamento team: ${err.message}`\n        console.error('Error loading team members:', err)\n        return []\n      }\n    },\n\n    async loadDepartments() {\n      try {\n        const authStore = useAuthStore()\n        const response = await fetch('/api/personnel/departments', {\n          headers: {\n            'Content-Type': 'application/json',\n            'X-CSRFToken': authStore.csrfToken\n          }\n        })\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n        }\n\n        const result = await response.json()\n        return result.data?.departments || result.data || []\n      } catch (err) {\n        console.error('Error loading departments:', err)\n        return []\n      }\n    },\n\n    // Bulk operations\n    async bulkApproveTimesheets(timesheetIds) {\n      try {\n        const authStore = useAuthStore()\n        const results = await Promise.allSettled(\n          timesheetIds.map(id => this.approveTimesheet(id))\n        )\n\n        const successful = results.filter(r => r.status === 'fulfilled' && r.value).length\n        const failed = results.length - successful\n\n        if (failed > 0) {\n          this.error = `${successful} approvati, ${failed} falliti`\n        }\n\n        return { successful, failed }\n      } catch (err) {\n        this.error = `Errore approvazione multipla: ${err.message}`\n        return { successful: 0, failed: timesheetIds.length }\n      }\n    },\n\n    async bulkRejectTimesheets(timesheetIds, reason) {\n      try {\n        const results = await Promise.allSettled(\n          timesheetIds.map(id => this.rejectTimesheet(id, reason))\n        )\n\n        const successful = results.filter(r => r.status === 'fulfilled' && r.value).length\n        const failed = results.length - successful\n\n        if (failed > 0) {\n          this.error = `${successful} rifiutati, ${failed} falliti`\n        }\n\n        return { successful, failed }\n      } catch (err) {\n        this.error = `Errore rifiuto multiplo: ${err.message}`\n        return { successful: 0, failed: timesheetIds.length }\n      }\n    },\n\n    // Export functionality\n    async exportTimesheetData(filters = {}) {\n      try {\n        const authStore = useAuthStore()\n        const params = new URLSearchParams()\n        \n        Object.keys(filters).forEach(key => {\n          if (filters[key]) {\n            params.append(key, filters[key])\n          }\n        })\n\n        const response = await fetch(`/api/timesheets/export/?${params}`, {\n          headers: {\n            'Content-Type': 'application/json',\n            'X-CSRFToken': authStore.csrfToken\n          }\n        })\n\n        if (!response.ok) {\n          const errorData = await response.json()\n          throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`)\n        }\n\n        // Handle file download\n        const blob = await response.blob()\n        const url = window.URL.createObjectURL(blob)\n        const a = document.createElement('a')\n        a.href = url\n        a.download = `timesheet_export_${new Date().toISOString().split('T')[0]}.xlsx`\n        document.body.appendChild(a)\n        a.click()\n        window.URL.revokeObjectURL(url)\n        document.body.removeChild(a)\n\n        return true\n      } catch (err) {\n        this.error = `Errore export: ${err.message}`\n        console.error('Error exporting data:', err)\n        return false\n      }\n    },\n\n    // Additional methods needed by TimesheetEntry\n    async loadTimesheetHistory(filters = {}) {\n      try {\n        const authStore = useAuthStore()\n        const params = new URLSearchParams()\n        \n        Object.keys(filters).forEach(key => {\n          if (filters[key]) {\n            params.append(key, filters[key])\n          }\n        })\n\n        const response = await fetch(`/api/timesheets/?${params}`, {\n          headers: {\n            'Content-Type': 'application/json',\n            'X-CSRFToken': authStore.csrfToken\n          }\n        })\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n        }\n\n        const result = await response.json()\n        return result.data || []\n      } catch (err) {\n        this.error = `Errore caricamento storico: ${err.message}`\n        console.error('Error loading timesheet history:', err)\n        return []\n      }\n    },\n\n\n    async loadTimeOffRequests(filters = {}) {\n      try {\n        const authStore = useAuthStore()\n        const params = new URLSearchParams()\n        \n        Object.keys(filters).forEach(key => {\n          if (filters[key]) {\n            // Map request_type to type for API consistency\n            const apiKey = key === 'request_type' ? 'type' : key\n            params.append(apiKey, filters[key])\n          }\n        })\n\n        const response = await fetch(`/api/time-off-requests/?${params}`, {\n          headers: {\n            'Content-Type': 'application/json',\n            'X-CSRFToken': authStore.csrfToken\n          }\n        })\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n        }\n\n        const result = await response.json()\n        // Supporta diversi formati di risposta: preferisci array di richieste\n        if (Array.isArray(result.data)) {\n          return result.data\n        }\n        if (result.data && Array.isArray(result.data.requests)) {\n          return result.data.requests\n        }\n        // Fallback: nessun risultato valido\n        return []\n      } catch (err) {\n        this.error = `Errore caricamento richieste: ${err.message}`\n        console.error('Error loading time off requests:', err)\n        return []\n      }\n    },\n\n    async createTimeOffRequest(data) {\n      try {\n        const authStore = useAuthStore()\n        const response = await fetch('/api/time-off-requests/', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'X-CSRFToken': authStore.csrfToken\n          },\n          body: JSON.stringify(data)\n        })\n\n        if (!response.ok) {\n          // Leggi il corpo della risposta per ottenere il messaggio di errore specifico\n          const errorData = await response.json()\n          throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`)\n        }\n\n        return true\n      } catch (err) {\n        this.error = `${err.message}`\n        console.error('Error creating time off request:', err)\n        return false\n      }\n    },\n\n    async deleteTimeOffRequest(requestId) {\n      try {\n        const authStore = useAuthStore()\n        const response = await fetch(`/api/time-off-requests/${requestId}`, {\n          method: 'DELETE',\n          headers: {\n            'Content-Type': 'application/json',\n            'X-CSRFToken': authStore.csrfToken\n          }\n        })\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n        }\n\n        return true\n      } catch (err) {\n        this.error = `Errore eliminazione richiesta: ${err.message}`\n        console.error('Error deleting time off request:', err)\n        return false\n      }\n    },\n\n    async approveTimeOffRequest(requestId) {\n      try {\n        const authStore = useAuthStore()\n        const response = await fetch(`/api/time-off-requests/${requestId}/approve`, {\n          method: 'PUT',\n          headers: {\n            'Content-Type': 'application/json',\n            'X-CSRFToken': authStore.csrfToken\n          }\n        })\n\n        if (!response.ok) {\n          const errorData = await response.json()\n          throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`)\n        }\n\n        return true\n      } catch (err) {\n        this.error = `Errore approvazione richiesta: ${err.message}`\n        console.error('Error approving time off request:', err)\n        return false\n      }\n    },\n\n    async rejectTimeOffRequest(requestId, reason) {\n      try {\n        const authStore = useAuthStore()\n        const response = await fetch(`/api/time-off-requests/${requestId}/reject`, {\n          method: 'PUT',\n          headers: {\n            'Content-Type': 'application/json',\n            'X-CSRFToken': authStore.csrfToken\n          },\n          body: JSON.stringify({ reason })\n        })\n\n        if (!response.ok) {\n          const errorData = await response.json()\n          throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`)\n        }\n\n        return true\n      } catch (err) {\n        this.error = `Errore rifiuto richiesta: ${err.message}`\n        console.error('Error rejecting time off request:', err)\n        return false\n      }\n    },\n\n    // Clear error\n    clearError() {\n      this.error = null\n    },\n\n    // Aggiunta del metodo per caricare le quote di time-off\n    async loadTimeOffQuotas() {\n      try {\n        const authStore = useAuthStore()\n        const response = await fetch('/api/time-off-requests/quotas', {\n          headers: {\n            'Content-Type': 'application/json',\n            'X-CSRFToken': authStore.csrfToken\n          }\n        })\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n        }\n\n        const result = await response.json()\n        const quotaData = result.data || {\n          vacation: { remaining: 0, total: 0 },\n          leave: { used: 0, total: 0 },\n          smartworking: { used: 0 }\n        }\n        \n        // Aggiorna le proprietà dello state\n        this.vacationQuota = quotaData.vacation;\n        this.leaveQuota = quotaData.leave;\n        this.smartWorkingDays = quotaData.smartworking?.used || 0;\n        \n        return quotaData;\n      } catch (err) {\n        console.error('Error loading time-off quotas:', err)\n        \n        // Valori predefiniti in caso di errore\n        const defaultData = {\n          vacation: { remaining: 0, total: 0 },\n          leave: { used: 0, total: 0 },\n          smartworking: { used: 0 }\n        };\n        \n        // Aggiorna comunque le proprietà con valori predefiniti\n        this.vacationQuota = defaultData.vacation;\n        this.leaveQuota = defaultData.leave;\n        this.smartWorkingDays = 0;\n        \n        return defaultData;\n      }\n    },\n\n    // Set monthlyTimesheetStatus\n    setMonthlyTimesheetStatus(status) {\n      this.monthlyTimesheetStatus = status\n    },\n\n    // Load lo stato del timesheet mensile\n    async loadMonthlyTimesheetStatus(year = this.currentYear, month = this.currentMonth) {\n      try {\n        const authStore = useAuthStore()\n        const yearMonth = `${year}-${month.toString().padStart(2, '0')}`\n        \n        try {\n          const response = await fetch(`/api/timesheets/status?year_month=${yearMonth}&user_id=${authStore.user.id}`, {\n            headers: {\n              'Content-Type': 'application/json',\n              'X-CSRFToken': authStore.csrfToken\n            }\n          })\n\n          if (!response.ok) {\n            // Se l'endpoint non esiste (404) o altri errori, usa lo stato predefinito\n            this.monthlyTimesheetStatus = 'pending'\n            return 'pending'\n          }\n\n          const result = await response.json()\n          \n          if (result.success) {\n            this.monthlyTimesheetStatus = result.data?.status || 'pending'\n          } else {\n            this.monthlyTimesheetStatus = 'pending' // Default fallback\n          }\n        } catch (err) {\n          // In caso di errore di rete o altro, semplicemente usa lo stato predefinito\n          this.monthlyTimesheetStatus = 'pending'\n        }\n        \n        return this.monthlyTimesheetStatus\n      } catch (err) {\n        // Gestione errori silenziosa in attesa dell'implementazione backend\n        this.monthlyTimesheetStatus = 'pending' // Default fallback in caso di errore\n        return 'pending'\n      }\n    },\n\n    // Metodo per caricare le richieste di time-off in attesa\n    async loadPendingTimeOffRequests() {\n      try {\n        // Utilizziamo il metodo esistente loadTimeOffRequests con filtro per status=pending\n        const requests = await this.loadTimeOffRequests({ status: 'pending' });\n        // Salviamo le richieste pendenti nello store\n        this.pendingTimeOffRequests = requests;\n        return requests;\n      } catch (err) {\n        console.error('Error loading pending time-off requests:', err);\n        this.error = `Errore caricamento richieste in attesa: ${err.message}`;\n        this.pendingTimeOffRequests = [];\n        return [];\n      }\n    }\n  }\n})"}