{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/mocks/api-handlers.js"}, "modifiedCode": "/**\n * Mock Service Worker (MSW) handlers for API mocking\n * Provides realistic API responses for testing\n */\n\nimport { rest } from 'msw'\n\n// Mock data\nconst mockProjects = [\n  {\n    id: 1,\n    name: 'Project Alpha',\n    description: 'First test project',\n    status: 'active',\n    budget: 50000,\n    start_date: '2025-01-01',\n    end_date: '2025-06-30',\n    client: { id: 1, name: 'Acme Corp' },\n    team_members: [\n      { id: 1, full_name: '<PERSON>', role: 'Project Manager', allocation_percentage: 50 },\n      { id: 2, full_name: '<PERSON>', role: 'Developer', allocation_percentage: 100 }\n    ],\n    tasks: [\n      { id: 1, title: 'Setup Environment', status: 'completed', estimated_hours: 8 },\n      { id: 2, title: 'Develop Features', status: 'in_progress', estimated_hours: 40 }\n    ]\n  },\n  {\n    id: 2,\n    name: 'Project Beta',\n    description: 'Second test project',\n    status: 'planning',\n    budget: 30000,\n    start_date: '2025-02-01',\n    end_date: '2025-08-31',\n    client: { id: 2, name: 'Beta Industries' },\n    team_members: [],\n    tasks: []\n  }\n]\n\nconst mockUsers = [\n  { id: 1, full_name: '<PERSON>', email: '<EMAIL>', role: 'manager' },\n  { id: 2, full_name: '<PERSON>', email: '<EMAIL>', role: 'employee' },\n  { id: 3, full_name: 'Bob Wilson', email: '<EMAIL>', role: 'employee' }\n]\n\nconst mockClients = [\n  { id: 1, name: 'Acme Corp', email: '<EMAIL>', industry: 'Technology' },\n  { id: 2, name: 'Beta Industries', email: '<EMAIL>', industry: 'Manufacturing' }\n]\n\n// API Handlers\nexport const handlers = [\n  // Authentication\n  rest.post('/api/auth/login', (req, res, ctx) => {\n    const { username, password } = req.body\n    \n    if (username === 'admin' && password === 'password') {\n      return res(\n        ctx.status(200),\n        ctx.json({\n          success: true,\n          data: {\n            user: { id: 1, username: 'admin', role: 'admin', full_name: 'Admin User' },\n            token: 'mock-jwt-token'\n          }\n        })\n      )\n    }\n    \n    return res(\n      ctx.status(401),\n      ctx.json({\n        success: false,\n        error: 'Invalid credentials'\n      })\n    )\n  }),\n\n  rest.post('/api/auth/logout', (req, res, ctx) => {\n    return res(\n      ctx.status(200),\n      ctx.json({ success: true })\n    )\n  }),\n\n  rest.get('/api/auth/me', (req, res, ctx) => {\n    const authHeader = req.headers.get('Authorization')\n    \n    if (authHeader && authHeader.includes('mock-jwt-token')) {\n      return res(\n        ctx.status(200),\n        ctx.json({\n          success: true,\n          data: { id: 1, username: 'admin', role: 'admin', full_name: 'Admin User' }\n        })\n      )\n    }\n    \n    return res(\n      ctx.status(401),\n      ctx.json({ success: false, error: 'Unauthorized' })\n    )\n  }),\n\n  // Projects\n  rest.get('/api/projects', (req, res, ctx) => {\n    const page = req.url.searchParams.get('page') || 1\n    const search = req.url.searchParams.get('search')\n    const status = req.url.searchParams.get('status')\n    \n    let filteredProjects = [...mockProjects]\n    \n    if (search) {\n      filteredProjects = filteredProjects.filter(p => \n        p.name.toLowerCase().includes(search.toLowerCase())\n      )\n    }\n    \n    if (status) {\n      filteredProjects = filteredProjects.filter(p => p.status === status)\n    }\n    \n    return res(\n      ctx.status(200),\n      ctx.json({\n        success: true,\n        data: {\n          projects: filteredProjects,\n          pagination: {\n            page: parseInt(page),\n            per_page: 10,\n            total: filteredProjects.length,\n            pages: Math.ceil(filteredProjects.length / 10)\n          }\n        }\n      })\n    )\n  }),\n\n  rest.get('/api/projects/:id', (req, res, ctx) => {\n    const { id } = req.params\n    const project = mockProjects.find(p => p.id === parseInt(id))\n    \n    if (!project) {\n      return res(\n        ctx.status(404),\n        ctx.json({ success: false, error: 'Project not found' })\n      )\n    }\n    \n    return res(\n      ctx.status(200),\n      ctx.json({ success: true, data: project })\n    )\n  }),\n\n  rest.post('/api/projects', (req, res, ctx) => {\n    const projectData = req.body\n    \n    // Validate required fields\n    if (!projectData.name || !projectData.budget) {\n      return res(\n        ctx.status(400),\n        ctx.json({\n          success: false,\n          errors: {\n            name: !projectData.name ? ['Project name is required'] : [],\n            budget: !projectData.budget ? ['Budget is required'] : []\n          }\n        })\n      )\n    }\n    \n    const newProject = {\n      id: mockProjects.length + 1,\n      ...projectData,\n      team_members: [],\n      tasks: [],\n      created_at: new Date().toISOString()\n    }\n    \n    mockProjects.push(newProject)\n    \n    return res(\n      ctx.status(201),\n      ctx.json({ success: true, data: newProject })\n    )\n  }),\n\n  rest.put('/api/projects/:id', (req, res, ctx) => {\n    const { id } = req.params\n    const updates = req.body\n    const projectIndex = mockProjects.findIndex(p => p.id === parseInt(id))\n    \n    if (projectIndex === -1) {\n      return res(\n        ctx.status(404),\n        ctx.json({ success: false, error: 'Project not found' })\n      )\n    }\n    \n    mockProjects[projectIndex] = { ...mockProjects[projectIndex], ...updates }\n    \n    return res(\n      ctx.status(200),\n      ctx.json({ success: true, data: mockProjects[projectIndex] })\n    )\n  }),\n\n  rest.delete('/api/projects/:id', (req, res, ctx) => {\n    const { id } = req.params\n    const projectIndex = mockProjects.findIndex(p => p.id === parseInt(id))\n    \n    if (projectIndex === -1) {\n      return res(\n        ctx.status(404),\n        ctx.json({ success: false, error: 'Project not found' })\n      )\n    }\n    \n    mockProjects.splice(projectIndex, 1)\n    \n    return res(\n      ctx.status(200),\n      ctx.json({ success: true })\n    )\n  }),\n\n  // Project Team Management\n  rest.post('/api/projects/:id/team', (req, res, ctx) => {\n    const { id } = req.params\n    const { user_id, role, allocation_percentage } = req.body\n    const project = mockProjects.find(p => p.id === parseInt(id))\n    \n    if (!project) {\n      return res(\n        ctx.status(404),\n        ctx.json({ success: false, error: 'Project not found' })\n      )\n    }\n    \n    const user = mockUsers.find(u => u.id === parseInt(user_id))\n    if (!user) {\n      return res(\n        ctx.status(404),\n        ctx.json({ success: false, error: 'User not found' })\n      )\n    }\n    \n    const teamMember = {\n      id: user.id,\n      full_name: user.full_name,\n      email: user.email,\n      role,\n      allocation_percentage: parseInt(allocation_percentage),\n      hours_worked: 0\n    }\n    \n    project.team_members.push(teamMember)\n    \n    return res(\n      ctx.status(200),\n      ctx.json({ success: true, data: teamMember })\n    )\n  }),\n\n  rest.delete('/api/projects/:projectId/team/:userId', (req, res, ctx) => {\n    const { projectId, userId } = req.params\n    const project = mockProjects.find(p => p.id === parseInt(projectId))\n    \n    if (!project) {\n      return res(\n        ctx.status(404),\n        ctx.json({ success: false, error: 'Project not found' })\n      )\n    }\n    \n    const memberIndex = project.team_members.findIndex(m => m.id === parseInt(userId))\n    if (memberIndex === -1) {\n      return res(\n        ctx.status(404),\n        ctx.json({ success: false, error: 'Team member not found' })\n      )\n    }\n    \n    project.team_members.splice(memberIndex, 1)\n    \n    return res(\n      ctx.status(200),\n      ctx.json({ success: true })\n    )\n  }),\n\n  // Users\n  rest.get('/api/personnel/users', (req, res, ctx) => {\n    return res(\n      ctx.status(200),\n      ctx.json({ success: true, data: mockUsers })\n    )\n  }),\n\n  // Clients\n  rest.get('/api/clients', (req, res, ctx) => {\n    return res(\n      ctx.status(200),\n      ctx.json({ success: true, data: mockClients })\n    )\n  }),\n\n  // Dashboard\n  rest.get('/api/dashboard/stats', (req, res, ctx) => {\n    return res(\n      ctx.status(200),\n      ctx.json({\n        success: true,\n        data: {\n          totalProjects: mockProjects.length,\n          activeProjects: mockProjects.filter(p => p.status === 'active').length,\n          completedTasks: 45,\n          pendingTasks: 12,\n          totalHours: 320,\n          thisWeekHours: 40\n        }\n      })\n    )\n  }),\n\n  // Error simulation\n  rest.get('/api/error-test', (req, res, ctx) => {\n    return res(\n      ctx.status(500),\n      ctx.json({ success: false, error: 'Internal server error' })\n    )\n  }),\n\n  // Network delay simulation\n  rest.get('/api/slow-endpoint', (req, res, ctx) => {\n    return res(\n      ctx.delay(2000), // 2 second delay\n      ctx.status(200),\n      ctx.json({ success: true, data: 'Slow response' })\n    )\n  })\n]\n"}