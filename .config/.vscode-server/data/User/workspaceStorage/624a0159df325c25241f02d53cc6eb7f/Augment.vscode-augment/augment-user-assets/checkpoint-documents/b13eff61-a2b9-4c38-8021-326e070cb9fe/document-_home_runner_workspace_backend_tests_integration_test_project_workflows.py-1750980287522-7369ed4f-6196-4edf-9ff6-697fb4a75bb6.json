{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/integration/test_project_workflows.py"}, "modifiedCode": "\"\"\"\nTest di integrazione per i workflow completi dei progetti.\nTesta i casi d'uso end-to-end dal backend.\n\"\"\"\nimport pytest\nfrom datetime import datetime, date, timedelta\nfrom decimal import Decimal\n\nfrom app import create_app\nfrom extensions import db\nfrom models import (\n    User, Project, Task, TimesheetEntry, Client, Contract,\n    ProjectResource, ProjectKPI, ProjectExpense, ProjectFundingLink,\n    FundingApplication, FundingOpportunity\n)\n\nclass TestProjectWorkflows:\n    \"\"\"Test dei workflow completi dei progetti\"\"\"\n\n    @pytest.fixture\n    def setup_project_data(self, app):\n        \"\"\"Setup dati per test progetti\"\"\"\n        with app.app_context():\n            # Crea utenti\n            admin = User(\n                username='admin',\n                email='<EMAIL>',\n                first_name='Admin',\n                last_name='User',\n                role='admin'\n            )\n            admin.set_password('password')\n            \n            manager = User(\n                username='manager',\n                email='<EMAIL>',\n                first_name='Project',\n                last_name='Manager',\n                role='manager'\n            )\n            manager.set_password('password')\n            \n            employee = User(\n                username='employee',\n                email='<EMAIL>',\n                first_name='Team',\n                last_name='Member',\n                role='employee'\n            )\n            employee.set_password('password')\n            \n            # Crea cliente\n            client = Client(\n                name='Test Client',\n                email='<EMAIL>',\n                industry='Technology'\n            )\n            \n            # Crea contratto\n            contract = Contract(\n                client_id=1,  # Sarà aggiornato dopo commit\n                contract_number='CNT-2025-001',\n                title='Test Contract',\n                value=50000.0,\n                start_date=date.today(),\n                end_date=date.today() + timedelta(days=365)\n            )\n            \n            db.session.add_all([admin, manager, employee, client, contract])\n            db.session.commit()\n            \n            # Aggiorna contract con client_id corretto\n            contract.client_id = client.id\n            db.session.commit()\n            \n            return {\n                'admin': admin,\n                'manager': manager,\n                'employee': employee,\n                'client': client,\n                'contract': contract\n            }\n\n    def test_complete_project_lifecycle(self, app, setup_project_data):\n        \"\"\"Test del ciclo di vita completo di un progetto\"\"\"\n        with app.app_context():\n            data = setup_project_data\n            \n            # 1. CREAZIONE PROGETTO\n            project = Project(\n                name='Test Project Lifecycle',\n                description='Progetto di test per ciclo completo',\n                client_id=data['client'].id,\n                contract_id=data['contract'].id,\n                start_date=date.today(),\n                end_date=date.today() + timedelta(days=90),\n                budget=25000.0,\n                status='planning',\n                project_type='service',\n                is_billable=True\n            )\n            db.session.add(project)\n            db.session.commit()\n            \n            # 2. AGGIUNTA TEAM MEMBERS\n            # Manager come project manager\n            project.team_members.append(data['manager'])\n            project.team_members.append(data['employee'])\n            \n            # Crea risorse progetto\n            manager_resource = ProjectResource(\n                project_id=project.id,\n                user_id=data['manager'].id,\n                role='Project Manager',\n                allocation_percentage=50.0,\n                start_date=project.start_date,\n                end_date=project.end_date,\n                daily_rate=400.0\n            )\n            \n            employee_resource = ProjectResource(\n                project_id=project.id,\n                user_id=data['employee'].id,\n                role='Developer',\n                allocation_percentage=100.0,\n                start_date=project.start_date,\n                end_date=project.end_date,\n                daily_rate=300.0\n            )\n            \n            db.session.add_all([manager_resource, employee_resource])\n            db.session.commit()\n            \n            # 3. CREAZIONE TASK\n            task1 = Task(\n                project_id=project.id,\n                title='Setup Development Environment',\n                description='Configure development tools and environment',\n                assigned_to=data['employee'].id,\n                estimated_hours=16.0,\n                status='todo',\n                priority='high',\n                due_date=date.today() + timedelta(days=7)\n            )\n            \n            task2 = Task(\n                project_id=project.id,\n                title='Implement Core Features',\n                description='Develop main application features',\n                assigned_to=data['employee'].id,\n                estimated_hours=40.0,\n                status='todo',\n                priority='medium',\n                due_date=date.today() + timedelta(days=30)\n            )\n            \n            db.session.add_all([task1, task2])\n            db.session.commit()\n            \n            # 4. AVVIO PROGETTO\n            project.status = 'active'\n            task1.status = 'in_progress'\n            db.session.commit()\n            \n            # 5. REGISTRAZIONE ORE (TIMESHEET)\n            # Settimana 1\n            for day in range(5):  # 5 giorni lavorativi\n                timesheet = TimesheetEntry(\n                    user_id=data['employee'].id,\n                    project_id=project.id,\n                    task_id=task1.id,\n                    date=date.today() + timedelta(days=day),\n                    hours=8.0,\n                    description=f'Work on task 1 - day {day+1}'\n                )\n                db.session.add(timesheet)\n            \n            db.session.commit()\n            \n            # 6. COMPLETAMENTO PRIMA TASK\n            task1.status = 'completed'\n            task1.actual_hours = 40.0  # 5 giorni * 8 ore\n            task2.status = 'in_progress'\n            db.session.commit()\n            \n            # 7. AGGIUNTA SPESE\n            expense1 = ProjectExpense(\n                project_id=project.id,\n                category='software',\n                description='Development tools license',\n                amount=299.99,\n                expense_date=date.today(),\n                user_id=data['manager'].id,\n                status='approved'\n            )\n            \n            expense2 = ProjectExpense(\n                project_id=project.id,\n                category='travel',\n                description='Client meeting travel',\n                amount=150.00,\n                expense_date=date.today() + timedelta(days=10),\n                user_id=data['manager'].id,\n                status='pending'\n            )\n            \n            db.session.add_all([expense1, expense2])\n            db.session.commit()\n            \n            # 8. AGGIORNAMENTO BUDGET SPESO\n            project.expenses = expense1.amount + expense2.amount\n            db.session.commit()\n            \n            # 9. SETUP KPI\n            kpi = ProjectKPI(\n                project_id=project.id,\n                name='Budget Utilization',\n                description='Percentage of budget used',\n                unit='percentage',\n                target_value=90.0,\n                current_value=1.8,  # (449.99 / 25000) * 100\n                category='budget'\n            )\n            db.session.add(kpi)\n            db.session.commit()\n            \n            # 10. VALIDAZIONI FINALI\n            # Verifica stato progetto\n            assert project.status == 'active'\n            assert len(project.team_members) == 2\n            assert len(project.tasks.all()) == 2\n            assert project.tasks.filter_by(status='completed').count() == 1\n            assert project.tasks.filter_by(status='in_progress').count() == 1\n            \n            # Verifica timesheet\n            total_hours = TimesheetEntry.query.filter_by(project_id=project.id).count()\n            assert total_hours == 5  # 5 giorni di lavoro\n            \n            # Verifica spese\n            total_expenses = ProjectExpense.query.filter_by(project_id=project.id).count()\n            assert total_expenses == 2\n            approved_expenses = ProjectExpense.query.filter_by(\n                project_id=project.id, \n                status='approved'\n            ).count()\n            assert approved_expenses == 1\n            \n            # Verifica KPI\n            assert project.kpis.count() == 1\n            assert project.kpis.first().current_value == 1.8\n            \n            # Verifica budget\n            assert project.remaining_budget == project.budget - project.expenses\n            \n            print(\"✅ Test ciclo di vita progetto completato con successo\")\n\n    def test_project_funding_workflow(self, app, setup_project_data):\n        \"\"\"Test workflow progetto con finanziamento\"\"\"\n        with app.app_context():\n            data = setup_project_data\n            \n            # 1. Crea opportunità di finanziamento\n            opportunity = FundingOpportunity(\n                title='Digital Innovation Grant',\n                description='Grant for digital transformation projects',\n                source_entity='EU Commission',\n                max_grant_amount=100000.0,\n                contribution_percentage=70.0,\n                application_deadline=date.today() + timedelta(days=30),\n                status='open',\n                created_by=data['admin'].id\n            )\n            db.session.add(opportunity)\n            db.session.commit()\n            \n            # 2. Crea application\n            application = FundingApplication(\n                opportunity_id=opportunity.id,\n                project_title='AI-Powered Business Platform',\n                project_description='Development of AI platform for SMEs',\n                requested_amount=70000.0,\n                status='approved',\n                created_by=data['manager'].id\n            )\n            application.approved_amount = 70000.0\n            db.session.add(application)\n            db.session.commit()\n            \n            # 3. Crea progetto finanziato\n            project = Project(\n                name='AI Business Platform',\n                description='AI-powered platform development',\n                client_id=data['client'].id,\n                start_date=date.today(),\n                end_date=date.today() + timedelta(days=365),\n                budget=100000.0,  # Budget totale\n                status='active',\n                project_type='rd',\n                is_billable=False,  # Progetto R&D finanziato\n                funding_source='public_funding',\n                funding_application_id=application.id\n            )\n            db.session.add(project)\n            db.session.commit()\n            \n            # 4. Crea link finanziamento\n            funding_link = ProjectFundingLink(\n                project_id=project.id,\n                funding_application_id=application.id,\n                allocation_percentage=70.0,  # 70% finanziato\n                notes='EU Digital Innovation Grant funding'\n            )\n            db.session.add(funding_link)\n            db.session.commit()\n            \n            # 5. Validazioni\n            assert project.funding_source == 'public_funding'\n            assert project.funding_application_id == application.id\n            assert len(project.funding_links) == 1\n            assert project.funding_links[0].allocation_percentage == 70.0\n            \n            # Calcola finanziamento\n            funded_amount = (project.budget * funding_link.allocation_percentage) / 100\n            co_financing = project.budget - funded_amount\n            \n            assert funded_amount == 70000.0\n            assert co_financing == 30000.0\n            \n            print(\"✅ Test workflow finanziamento completato con successo\")\n\n    def test_project_team_management(self, app, setup_project_data):\n        \"\"\"Test gestione team di progetto\"\"\"\n        with app.app_context():\n            data = setup_project_data\n            \n            # Crea progetto\n            project = Project(\n                name='Team Management Test',\n                description='Test per gestione team',\n                client_id=data['client'].id,\n                start_date=date.today(),\n                end_date=date.today() + timedelta(days=60),\n                budget=15000.0,\n                status='active'\n            )\n            db.session.add(project)\n            db.session.commit()\n            \n            # Aggiungi membri team\n            project.team_members.extend([data['manager'], data['employee']])\n            \n            # Crea allocazioni risorse\n            resources = [\n                ProjectResource(\n                    project_id=project.id,\n                    user_id=data['manager'].id,\n                    role='Project Manager',\n                    allocation_percentage=25.0,\n                    start_date=project.start_date,\n                    end_date=project.end_date,\n                    daily_rate=400.0\n                ),\n                ProjectResource(\n                    project_id=project.id,\n                    user_id=data['employee'].id,\n                    role='Senior Developer',\n                    allocation_percentage=75.0,\n                    start_date=project.start_date,\n                    end_date=project.end_date,\n                    daily_rate=350.0\n                )\n            ]\n            \n            db.session.add_all(resources)\n            db.session.commit()\n            \n            # Validazioni team\n            assert len(project.team_members) == 2\n            assert len(project.resources) == 2\n            \n            # Trova risorsa manager\n            manager_resource = ProjectResource.query.filter_by(\n                project_id=project.id,\n                user_id=data['manager'].id\n            ).first()\n            \n            assert manager_resource.role == 'Project Manager'\n            assert manager_resource.allocation_percentage == 25.0\n            assert manager_resource.daily_rate == 400.0\n            \n            # Calcola costo giornaliero team\n            daily_cost = sum(r.daily_rate * (r.allocation_percentage / 100) for r in project.resources)\n            expected_cost = (400.0 * 0.25) + (350.0 * 0.75)  # 100 + 262.5 = 362.5\n            \n            assert daily_cost == expected_cost\n            \n            print(\"✅ Test gestione team completato con successo\")\n"}