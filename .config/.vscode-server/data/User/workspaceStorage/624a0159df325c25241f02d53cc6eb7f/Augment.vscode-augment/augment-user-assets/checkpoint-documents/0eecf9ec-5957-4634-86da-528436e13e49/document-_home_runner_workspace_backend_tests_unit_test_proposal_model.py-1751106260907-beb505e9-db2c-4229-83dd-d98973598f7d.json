{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_proposal_model.py"}, "originalCode": "\"\"\"Unit tests for Proposal model.\"\"\"\nimport pytest\nfrom datetime import date\nfrom models import Proposal, Client, User\nfrom extensions import db\n\nclass TestProposalModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n        \n        self.client = Client.query.first()\n        if not self.client:\n            self.client = Client(name='Test Client', email='<EMAIL>')\n            db.session.add(self.client)\n            db.session.commit()\n\n    def test_proposal_creation_basic(self):\n        proposal = Proposal(\n            title='Test Proposal',\n            client_id=self.client.id,\n            created_by=self.user.id,\n            value=10000.0  # Campo corretto è 'value'\n        )\n        db.session.add(proposal)\n        db.session.commit()\n\n        assert proposal.id is not None\n        assert proposal.title == 'Test Proposal'\n        assert proposal.client_id == self.client.id\n        assert proposal.value == 10000.0\n\n    def test_proposal_with_status(self):\n        proposal = Proposal(\n            title='Status Proposal',\n            client_id=self.client.id,\n            created_by=self.user.id,\n            status='sent',\n            valid_until=date(2024, 12, 31)\n        )\n        db.session.add(proposal)\n        db.session.commit()\n        \n        assert proposal.status == 'sent'\n        assert proposal.valid_until == date(2024, 12, 31)\n\n    def test_proposal_deletion(self):\n        proposal = Proposal(title='To Delete', client_id=self.client.id, created_by=self.user.id)\n        db.session.add(proposal)\n        db.session.commit()\n        proposal_id = proposal.id\n        \n        db.session.delete(proposal)\n        db.session.commit()\n        \n        deleted = Proposal.query.get(proposal_id)\n        assert deleted is None\n", "modifiedCode": "\"\"\"Unit tests for Proposal model.\"\"\"\nimport pytest\nfrom datetime import date\nfrom models import Proposal, Client, User\nfrom extensions import db\n\nclass TestProposalModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n        \n        self.client = Client.query.first()\n        if not self.client:\n            self.client = Client(name='Test Client', email='<EMAIL>')\n            db.session.add(self.client)\n            db.session.commit()\n\n    def test_proposal_creation_basic(self):\n        proposal = Proposal(\n            title='Test Proposal',\n            client_id=self.client.id,\n            created_by=self.user.id,\n            value=10000.0  # Campo corretto è 'value'\n        )\n        db.session.add(proposal)\n        db.session.commit()\n\n        assert proposal.id is not None\n        assert proposal.title == 'Test Proposal'\n        assert proposal.client_id == self.client.id\n        assert proposal.value == 10000.0\n\n    def test_proposal_with_status(self):\n        proposal = Proposal(\n            title='Status Proposal',\n            client_id=self.client.id,\n            created_by=self.user.id,\n            status='sent',\n            expiry_date=date(2024, 12, 31)  # Campo corretto è 'expiry_date'\n        )\n        db.session.add(proposal)\n        db.session.commit()\n\n        assert proposal.status == 'sent'\n        assert proposal.expiry_date == date(2024, 12, 31)\n\n    def test_proposal_deletion(self):\n        proposal = Proposal(title='To Delete', client_id=self.client.id, created_by=self.user.id)\n        db.session.add(proposal)\n        db.session.commit()\n        proposal_id = proposal.id\n        \n        db.session.delete(proposal)\n        db.session.commit()\n        \n        deleted = Proposal.query.get(proposal_id)\n        assert deleted is None\n"}