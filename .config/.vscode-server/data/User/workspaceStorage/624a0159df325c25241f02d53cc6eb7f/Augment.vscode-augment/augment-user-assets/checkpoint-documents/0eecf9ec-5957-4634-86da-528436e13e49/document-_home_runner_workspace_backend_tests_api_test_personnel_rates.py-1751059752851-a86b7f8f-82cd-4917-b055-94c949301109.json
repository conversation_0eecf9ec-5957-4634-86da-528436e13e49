{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/api/test_personnel_rates.py"}, "modifiedCode": "\"\"\"\nTest suite for PersonnelRate API endpoints.\nTests CRUD operations, validation, and business logic for personnel rate management.\n\"\"\"\n\nimport pytest\nfrom datetime import datetime, date, timedelta\nfrom models import PersonnelRate, User\nfrom extensions import db\n\n\nclass TestPersonnelRatesAPI:\n    \"\"\"Test suite for PersonnelRate API endpoints\"\"\"\n\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        \"\"\"Setup test data for each test method\"\"\"\n        self.app = app\n        self.user = test_user\n        \n        # Create test personnel rate data\n        self.rate_data = {\n            'user_id': self.user.id,\n            'daily_rate': 350.0,\n            'valid_from': date.today().isoformat(),\n            'valid_to': (date.today() + timedelta(days=365)).isoformat(),\n            'currency': 'EUR',\n            'notes': 'Standard daily rate for senior developer'\n        }\n\n    def test_get_personnel_rates_success(self, client):\n        \"\"\"Test successful retrieval of personnel rates list\"\"\"\n        # Create test rates\n        rate1 = PersonnelRate(\n            user_id=self.user.id,\n            daily_rate=300.0,\n            valid_from=date.today() - timedelta(days=30),\n            currency='EUR'\n        )\n        rate2 = PersonnelRate(\n            user_id=self.user.id,\n            daily_rate=350.0,\n            valid_from=date.today(),\n            currency='EUR'\n        )\n        db.session.add_all([rate1, rate2])\n        db.session.commit()\n\n        response = client.get('/api/personnel/rates')\n        \n        assert response.status_code in [200, 401]\n        if response.status_code == 200:\n            data = response.get_json()\n            assert 'rates' in str(data).lower() or 'data' in data\n\n    def test_create_personnel_rate_success(self, client):\n        \"\"\"Test successful personnel rate creation\"\"\"\n        response = client.post('/api/personnel/rates', json=self.rate_data)\n        \n        assert response.status_code in [201, 200, 401, 403]\n        if response.status_code in [200, 201]:\n            # Verify rate was created\n            created_rate = PersonnelRate.query.filter_by(\n                user_id=self.user.id,\n                daily_rate=350.0\n            ).first()\n            if created_rate:\n                assert created_rate.currency == 'EUR'\n\n    def test_create_personnel_rate_validation_error(self, client):\n        \"\"\"Test personnel rate creation with missing required fields\"\"\"\n        invalid_data = {'daily_rate': 300.0}  # Missing user_id and valid_from\n        \n        response = client.post('/api/personnel/rates', json=invalid_data)\n        \n        assert response.status_code in [400, 422, 401, 403]\n\n    def test_update_personnel_rate_success(self, client):\n        \"\"\"Test successful personnel rate update\"\"\"\n        test_rate = PersonnelRate(\n            user_id=self.user.id,\n            daily_rate=300.0,\n            valid_from=date.today(),\n            currency='EUR'\n        )\n        db.session.add(test_rate)\n        db.session.commit()\n\n        update_data = {\n            'daily_rate': 400.0,\n            'notes': 'Updated rate for promotion'\n        }\n        \n        response = client.put(f'/api/personnel/rates/{test_rate.id}', json=update_data)\n        \n        assert response.status_code in [200, 401, 403, 404]\n\n    def test_delete_personnel_rate_success(self, client):\n        \"\"\"Test successful personnel rate deletion\"\"\"\n        test_rate = PersonnelRate(\n            user_id=self.user.id,\n            daily_rate=250.0,\n            valid_from=date.today(),\n            currency='EUR'\n        )\n        db.session.add(test_rate)\n        db.session.commit()\n        rate_id = test_rate.id\n\n        response = client.delete(f'/api/personnel/rates/{rate_id}')\n        \n        assert response.status_code in [200, 401, 403, 404]\n\n    def test_personnel_rate_date_validation(self, client):\n        \"\"\"Test personnel rate date validation\"\"\"\n        # Test valid_to before valid_from\n        invalid_data = self.rate_data.copy()\n        invalid_data['valid_from'] = date.today().isoformat()\n        invalid_data['valid_to'] = (date.today() - timedelta(days=1)).isoformat()\n        \n        response = client.post('/api/personnel/rates', json=invalid_data)\n        assert response.status_code in [400, 422, 401, 403, 201, 200]\n\n    def test_personnel_rate_amount_validation(self, client):\n        \"\"\"Test personnel rate amount validation\"\"\"\n        # Test negative daily rate\n        invalid_data = self.rate_data.copy()\n        invalid_data['daily_rate'] = -100.0\n        \n        response = client.post('/api/personnel/rates', json=invalid_data)\n        assert response.status_code in [400, 422, 401, 403, 201, 200]\n        \n        # Test zero daily rate\n        invalid_data['daily_rate'] = 0.0\n        response = client.post('/api/personnel/rates', json=invalid_data)\n        assert response.status_code in [400, 422, 401, 403, 201, 200]\n\n    def test_get_personnel_rates_by_user(self, client):\n        \"\"\"Test retrieval of personnel rates for specific user\"\"\"\n        # Create rates for different users\n        rate1 = PersonnelRate(\n            user_id=self.user.id,\n            daily_rate=350.0,\n            valid_from=date.today(),\n            currency='EUR'\n        )\n        db.session.add(rate1)\n        db.session.commit()\n\n        response = client.get(f'/api/personnel/users/{self.user.id}/rates')\n        assert response.status_code in [200, 401, 404]\n\n    def test_get_current_personnel_rate(self, client):\n        \"\"\"Test retrieval of current valid personnel rate\"\"\"\n        # Create historical and current rates\n        old_rate = PersonnelRate(\n            user_id=self.user.id,\n            daily_rate=300.0,\n            valid_from=date.today() - timedelta(days=365),\n            valid_to=date.today() - timedelta(days=1),\n            currency='EUR'\n        )\n        current_rate = PersonnelRate(\n            user_id=self.user.id,\n            daily_rate=350.0,\n            valid_from=date.today(),\n            currency='EUR'\n        )\n        db.session.add_all([old_rate, current_rate])\n        db.session.commit()\n\n        response = client.get(f'/api/personnel/users/{self.user.id}/rates/current')\n        assert response.status_code in [200, 401, 404]\n\n    def test_personnel_rate_search_and_filters(self, client):\n        \"\"\"Test personnel rate search and filtering\"\"\"\n        # Create test rates\n        rate1 = PersonnelRate(\n            user_id=self.user.id,\n            daily_rate=300.0,\n            valid_from=date.today() - timedelta(days=30),\n            currency='EUR'\n        )\n        rate2 = PersonnelRate(\n            user_id=self.user.id,\n            daily_rate=350.0,\n            valid_from=date.today(),\n            currency='USD'\n        )\n        db.session.add_all([rate1, rate2])\n        db.session.commit()\n\n        # Test currency filter\n        response = client.get('/api/personnel/rates?currency=EUR')\n        assert response.status_code in [200, 401]\n        \n        # Test user filter\n        response = client.get(f'/api/personnel/rates?user_id={self.user.id}')\n        assert response.status_code in [200, 401]\n        \n        # Test date range filter\n        response = client.get(f'/api/personnel/rates?valid_from={date.today().isoformat()}')\n        assert response.status_code in [200, 401]\n\n    def test_personnel_rate_currency_validation(self, client):\n        \"\"\"Test personnel rate currency validation\"\"\"\n        # Test invalid currency\n        invalid_data = self.rate_data.copy()\n        invalid_data['currency'] = 'INVALID'\n        \n        response = client.post('/api/personnel/rates', json=invalid_data)\n        assert response.status_code in [400, 422, 401, 403, 201, 200]\n\n    def test_get_personnel_rate_detail(self, client):\n        \"\"\"Test single personnel rate retrieval\"\"\"\n        test_rate = PersonnelRate(\n            user_id=self.user.id,\n            daily_rate=375.0,\n            valid_from=date.today(),\n            currency='EUR',\n            notes='Senior rate'\n        )\n        db.session.add(test_rate)\n        db.session.commit()\n\n        response = client.get(f'/api/personnel/rates/{test_rate.id}')\n        assert response.status_code in [200, 401, 404]\n\n    def test_personnel_rate_not_found(self, client):\n        \"\"\"Test personnel rate not found scenarios\"\"\"\n        response = client.get('/api/personnel/rates/99999')\n        assert response.status_code in [404, 401]\n        \n        response = client.put('/api/personnel/rates/99999', json={'daily_rate': 400.0})\n        assert response.status_code in [404, 401]\n        \n        response = client.delete('/api/personnel/rates/99999')\n        assert response.status_code in [404, 401]\n\n    def test_personnel_rate_overlapping_periods(self, client):\n        \"\"\"Test handling of overlapping rate periods\"\"\"\n        # Create existing rate\n        existing_rate = PersonnelRate(\n            user_id=self.user.id,\n            daily_rate=300.0,\n            valid_from=date.today(),\n            valid_to=date.today() + timedelta(days=30),\n            currency='EUR'\n        )\n        db.session.add(existing_rate)\n        db.session.commit()\n\n        # Try to create overlapping rate\n        overlapping_data = self.rate_data.copy()\n        overlapping_data['valid_from'] = (date.today() + timedelta(days=15)).isoformat()\n        overlapping_data['valid_to'] = (date.today() + timedelta(days=45)).isoformat()\n        \n        response = client.post('/api/personnel/rates', json=overlapping_data)\n        assert response.status_code in [400, 409, 401, 403, 201, 200]\n\n    def test_personnel_rate_pagination(self, client):\n        \"\"\"Test personnel rate list pagination\"\"\"\n        # Create multiple rates\n        for i in range(5):\n            rate = PersonnelRate(\n                user_id=self.user.id,\n                daily_rate=300.0 + i * 10,\n                valid_from=date.today() - timedelta(days=i * 30),\n                currency='EUR'\n            )\n            db.session.add(rate)\n        db.session.commit()\n\n        response = client.get('/api/personnel/rates?page=1&per_page=3')\n        assert response.status_code in [200, 401]\n"}