{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_notification_model.py"}, "modifiedCode": "\"\"\"\nUnit tests for Notification model.\nTests business logic, validations, and model functionality.\n\"\"\"\n\nimport pytest\nfrom datetime import datetime\nfrom models import Notification, User\nfrom extensions import db\n\n\nclass TestNotificationModel:\n    \"\"\"Test suite for Notification model unit tests\"\"\"\n\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        \"\"\"Setup test data for each test method\"\"\"\n        self.app = app\n        self.user = test_user\n\n    def test_notification_creation_basic(self):\n        \"\"\"Test basic notification creation\"\"\"\n        notification = Notification(\n            user_id=self.user.id,\n            title='Test Notification',\n            message='This is a test notification message'\n        )\n        \n        db.session.add(notification)\n        db.session.commit()\n        \n        assert notification.id is not None\n        assert notification.user_id == self.user.id\n        assert notification.title == 'Test Notification'\n        assert notification.message == 'This is a test notification message'\n\n    def test_notification_creation_complete(self):\n        \"\"\"Test notification creation with all fields\"\"\"\n        notification = Notification(\n            user_id=self.user.id,\n            title='Complete Notification',\n            message='Complete notification with all fields',\n            link='/dashboard',\n            is_read=False,\n            type='warning'\n        )\n        \n        db.session.add(notification)\n        db.session.commit()\n        \n        assert notification.title == 'Complete Notification'\n        assert notification.link == '/dashboard'\n        assert notification.is_read is False\n        assert notification.type == 'warning'\n\n    def test_notification_types(self):\n        \"\"\"Test different notification types\"\"\"\n        types = ['info', 'warning', 'error', 'success']\n        \n        notifications = []\n        for i, notif_type in enumerate(types):\n            notification = Notification(\n                user_id=self.user.id,\n                title=f'Notification Type {i}',\n                message=f'Message for {notif_type} notification',\n                type=notif_type\n            )\n            notifications.append(notification)\n        \n        db.session.add_all(notifications)\n        db.session.commit()\n        \n        for notification, expected_type in zip(notifications, types):\n            assert notification.type == expected_type\n\n    def test_notification_read_status(self):\n        \"\"\"Test notification read status functionality\"\"\"\n        # Unread notification\n        unread_notification = Notification(\n            user_id=self.user.id,\n            title='Unread Notification',\n            message='This notification is unread',\n            is_read=False\n        )\n        \n        # Read notification\n        read_notification = Notification(\n            user_id=self.user.id,\n            title='Read Notification',\n            message='This notification is read',\n            is_read=True\n        )\n        \n        db.session.add_all([unread_notification, read_notification])\n        db.session.commit()\n        \n        assert unread_notification.is_read is False\n        assert read_notification.is_read is True\n\n    def test_notification_links(self):\n        \"\"\"Test notification link functionality\"\"\"\n        links = ['/dashboard', '/profile', '/tasks/123', '/projects/456']\n        \n        notifications = []\n        for i, link in enumerate(links):\n            notification = Notification(\n                user_id=self.user.id,\n                title=f'Link Notification {i}',\n                message=f'Notification with link {i}',\n                link=link\n            )\n            notifications.append(notification)\n        \n        db.session.add_all(notifications)\n        db.session.commit()\n        \n        for notification, expected_link in zip(notifications, links):\n            assert notification.link == expected_link\n\n    def test_notification_timestamps(self):\n        \"\"\"Test automatic timestamp handling\"\"\"\n        notification = Notification(\n            user_id=self.user.id,\n            title='Timestamp Test',\n            message='Testing timestamp functionality'\n        )\n        \n        db.session.add(notification)\n        db.session.commit()\n        \n        assert notification.created_at is not None\n        assert isinstance(notification.created_at, datetime)\n\n    def test_notification_query_by_user(self):\n        \"\"\"Test querying notifications by user\"\"\"\n        notification = Notification(\n            user_id=self.user.id,\n            title='User Query Test',\n            message='Testing user query functionality'\n        )\n        \n        db.session.add(notification)\n        db.session.commit()\n        \n        user_notifications = Notification.query.filter_by(user_id=self.user.id).all()\n        assert len(user_notifications) >= 1\n        assert notification in user_notifications\n\n    def test_notification_query_unread(self):\n        \"\"\"Test querying unread notifications\"\"\"\n        notification = Notification(\n            user_id=self.user.id,\n            title='Unread Query Test',\n            message='Testing unread query functionality',\n            is_read=False\n        )\n        \n        db.session.add(notification)\n        db.session.commit()\n        \n        unread_notifications = Notification.query.filter_by(is_read=False).all()\n        assert len(unread_notifications) >= 1\n        assert notification in unread_notifications\n\n    def test_notification_query_by_type(self):\n        \"\"\"Test querying notifications by type\"\"\"\n        notification = Notification(\n            user_id=self.user.id,\n            title='Type Query Test',\n            message='Testing type query functionality',\n            type='urgent'\n        )\n        \n        db.session.add(notification)\n        db.session.commit()\n        \n        type_notifications = Notification.query.filter_by(type='urgent').all()\n        assert len(type_notifications) >= 1\n        assert notification in type_notifications\n\n    def test_notification_mark_as_read(self):\n        \"\"\"Test marking notification as read\"\"\"\n        notification = Notification(\n            user_id=self.user.id,\n            title='Mark Read Test',\n            message='Testing mark as read functionality',\n            is_read=False\n        )\n        \n        db.session.add(notification)\n        db.session.commit()\n        \n        # Mark as read\n        notification.is_read = True\n        db.session.commit()\n        \n        updated_notification = Notification.query.get(notification.id)\n        assert updated_notification.is_read is True\n\n    def test_notification_update_operations(self):\n        \"\"\"Test notification update operations\"\"\"\n        notification = Notification(\n            user_id=self.user.id,\n            title='Original Title',\n            message='Original message',\n            type='info',\n            is_read=False\n        )\n        \n        db.session.add(notification)\n        db.session.commit()\n        \n        # Update notification\n        notification.title = 'Updated Title'\n        notification.message = 'Updated message'\n        notification.type = 'warning'\n        notification.is_read = True\n        \n        db.session.commit()\n        \n        updated_notification = Notification.query.get(notification.id)\n        assert updated_notification.title == 'Updated Title'\n        assert updated_notification.message == 'Updated message'\n        assert updated_notification.type == 'warning'\n        assert updated_notification.is_read is True\n\n    def test_notification_deletion(self):\n        \"\"\"Test notification deletion\"\"\"\n        notification = Notification(\n            user_id=self.user.id,\n            title='To Be Deleted',\n            message='This notification will be deleted'\n        )\n        \n        db.session.add(notification)\n        db.session.commit()\n        notification_id = notification.id\n        \n        db.session.delete(notification)\n        db.session.commit()\n        \n        deleted_notification = Notification.query.get(notification_id)\n        assert deleted_notification is None\n"}