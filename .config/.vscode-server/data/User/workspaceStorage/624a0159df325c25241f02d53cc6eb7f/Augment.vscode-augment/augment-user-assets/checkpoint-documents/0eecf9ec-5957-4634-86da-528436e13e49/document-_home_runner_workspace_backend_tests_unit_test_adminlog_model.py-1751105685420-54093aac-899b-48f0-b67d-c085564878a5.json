{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_adminlog_model.py"}, "originalCode": "\"\"\"Unit tests for AdminLog model.\"\"\"\nimport pytest\nfrom models import Admin<PERSON>og, User\nfrom extensions import db\n\nclass TestAdminLogModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n\n    def test_adminlog_creation_basic(self):\n        log = AdminLog(\n            admin_id=self.user.id,  # Campo corretto è 'admin_id'\n            action='user_created'\n            # Rimossi campi che non esistono: description, ip_address\n        )\n        db.session.add(log)\n        db.session.commit()\n\n        assert log.id is not None\n        assert log.admin_id == self.user.id\n        assert log.action == 'user_created'\n\n    def test_adminlog_severity(self):\n        log = AdminLog(\n            admin_id=self.user.id,  # Campo corretto è 'admin_id'\n            action='security_breach'\n            # Rimossi campi che non esistono: description, severity\n        )\n        db.session.add(log)\n        db.session.commit()\n\n        assert log.action == 'security_breach'\n\n    def test_adminlog_deletion(self):\n        log = AdminLog(admin_id=self.user.id, action='test')  # Campo corretto è 'admin_id'\n        db.session.add(log)\n        db.session.commit()\n        log_id = log.id\n        \n        db.session.delete(log)\n        db.session.commit()\n        \n        deleted = AdminLog.query.get(log_id)\n        assert deleted is None\n", "modifiedCode": "\"\"\"Unit tests for AdminLog model.\"\"\"\nimport pytest\nfrom models import Admin<PERSON>og, User\nfrom extensions import db\n\nclass TestAdminLogModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n\n    def test_adminlog_creation_basic(self):\n        log = AdminLog(\n            admin_id=self.user.id,  # Campo corretto è 'admin_id'\n            action='user_created'\n            # Rimossi campi che non esistono: description, ip_address\n        )\n        db.session.add(log)\n        db.session.commit()\n\n        assert log.id is not None\n        assert log.admin_id == self.user.id\n        assert log.action == 'user_created'\n\n    def test_adminlog_severity(self):\n        log = AdminLog(\n            admin_id=self.user.id,  # Campo corretto è 'admin_id'\n            action='security_breach'\n            # Rimossi campi che non esistono: description, severity\n        )\n        db.session.add(log)\n        db.session.commit()\n\n        assert log.action == 'security_breach'\n\n    def test_adminlog_deletion(self):\n        log = AdminLog(admin_id=self.user.id, action='test')  # Campo corretto è 'admin_id'\n        db.session.add(log)\n        db.session.commit()\n        log_id = log.id\n        \n        db.session.delete(log)\n        db.session.commit()\n        \n        deleted = AdminLog.query.get(log_id)\n        assert deleted is None\n"}