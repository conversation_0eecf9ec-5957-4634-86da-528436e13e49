{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_product_model.py"}, "originalCode": "\"\"\"\nUnit tests for Product model.\nTests business logic, validations, and model functionality.\n\"\"\"\n\nimport pytest\nfrom datetime import datetime\nfrom models import Product\nfrom extensions import db\n\n\nclass TestProductModel:\n    \"\"\"Test suite for Product model unit tests\"\"\"\n\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        \"\"\"Setup test data for each test method\"\"\"\n        self.app = app\n        self.user = test_user\n\n    def test_product_creation_basic(self):\n        \"\"\"Test basic product creation\"\"\"\n        product = Product(\n            name='Test Product Basic',\n            description='A basic test product',\n            category='Software',\n            price=99.99,\n            status='active'\n        )\n        \n        db.session.add(product)\n        db.session.commit()\n        \n        assert product.id is not None\n        assert product.name == 'Test Product Basic'\n        assert product.description == 'A basic test product'\n        assert product.category == 'Software'\n        assert product.price == 99.99\n        assert product.status == 'active'\n\n    def test_product_creation_minimal(self):\n        \"\"\"Test product creation with minimal required fields\"\"\"\n        product = Product(\n            name='Minimal Product'\n        )\n        \n        db.session.add(product)\n        db.session.commit()\n        \n        assert product.id is not None\n        assert product.name == 'Minimal Product'\n        assert product.created_at is not None\n\n    def test_product_repr_method(self):\n        \"\"\"Test string representation of product\"\"\"\n        product = Product(name='Repr Test Product')\n        \n        expected_repr = '<Product Repr Test Product>'\n        assert repr(product) == expected_repr\n\n    def test_product_price_handling(self):\n        \"\"\"Test price field functionality\"\"\"\n        prices = [0.0, 9.99, 99.99, 999.99, 9999.99]\n        \n        products = []\n        for i, price in enumerate(prices):\n            product = Product(\n                name=f'Price Test {i}',\n                price=price\n            )\n            products.append(product)\n        \n        db.session.add_all(products)\n        db.session.commit()\n        \n        for product, expected_price in zip(products, prices):\n            assert product.price == expected_price\n            assert product.price >= 0\n\n    def test_product_categories(self):\n        \"\"\"Test different product categories\"\"\"\n        categories = [\n            'Software',\n            'Hardware',\n            'Services',\n            'Consulting',\n            'Training',\n            'Support'\n        ]\n        \n        products = []\n        for i, category in enumerate(categories):\n            product = Product(\n                name=f'Category Test {i}',\n                category=category\n            )\n            products.append(product)\n        \n        db.session.add_all(products)\n        db.session.commit()\n        \n        for product, expected_category in zip(products, categories):\n            assert product.category == expected_category\n\n    def test_product_status_types(self):\n        \"\"\"Test different product status types\"\"\"\n        statuses = ['active', 'inactive', 'discontinued', 'draft']\n        \n        products = []\n        for i, status in enumerate(statuses):\n            product = Product(\n                name=f'Status Test {i}',\n                status=status\n            )\n            products.append(product)\n        \n        db.session.add_all(products)\n        db.session.commit()\n        \n        for product, expected_status in zip(products, statuses):\n            assert product.status == expected_status\n\n    def test_product_description_field(self):\n        \"\"\"Test description field for long text\"\"\"\n        long_description = \"\"\"\n        This is a comprehensive product description that includes multiple\n        features, benefits, and technical specifications. It demonstrates\n        the ability to store detailed product information in the database.\n        The description can include formatting, special characters, and\n        extensive details about the product offering.\n        \"\"\"\n        \n        product = Product(\n            name='Description Test Product',\n            description=long_description.strip()\n        )\n        \n        db.session.add(product)\n        db.session.commit()\n        \n        assert product.description == long_description.strip()\n\n    def test_product_timestamps(self):\n        \"\"\"Test automatic timestamp handling\"\"\"\n        product = Product(\n            name='Timestamp Test Product'\n        )\n        \n        db.session.add(product)\n        db.session.commit()\n        \n        # Test created_at is set\n        assert product.created_at is not None\n        assert isinstance(product.created_at, datetime)\n        \n        # Test updated_at is set\n        assert product.updated_at is not None\n        assert isinstance(product.updated_at, datetime)\n        \n        # Test updated_at changes on update\n        original_updated_at = product.updated_at\n        product.description = 'Updated description'\n        db.session.commit()\n        \n        assert product.updated_at > original_updated_at\n\n    def test_product_query_by_category(self):\n        \"\"\"Test querying products by category\"\"\"\n        software_products = [\n            Product(name='Software 1', category='Software'),\n            Product(name='Software 2', category='Software')\n        ]\n        \n        hardware_products = [\n            Product(name='Hardware 1', category='Hardware')\n        ]\n        \n        db.session.add_all(software_products + hardware_products)\n        db.session.commit()\n        \n        # Query software products\n        software_results = Product.query.filter_by(category='Software').all()\n        software_names = [p.name for p in software_results]\n        \n        assert 'Software 1' in software_names\n        assert 'Software 2' in software_names\n        assert len([p for p in software_results if p.category == 'Software']) >= 2\n\n    def test_product_query_by_status(self):\n        \"\"\"Test querying products by status\"\"\"\n        active_products = [\n            Product(name='Active 1', status='active'),\n            Product(name='Active 2', status='active')\n        ]\n        \n        inactive_products = [\n            Product(name='Inactive 1', status='inactive')\n        ]\n        \n        db.session.add_all(active_products + inactive_products)\n        db.session.commit()\n        \n        # Query active products\n        active_results = Product.query.filter_by(status='active').all()\n        active_names = [p.name for p in active_results]\n        \n        assert 'Active 1' in active_names\n        assert 'Active 2' in active_names\n\n    def test_product_price_range_queries(self):\n        \"\"\"Test querying products by price range\"\"\"\n        products = [\n            Product(name='Cheap Product', price=10.0),\n            Product(name='Medium Product', price=50.0),\n            Product(name='Expensive Product', price=100.0)\n        ]\n        \n        db.session.add_all(products)\n        db.session.commit()\n        \n        # Query products under $60\n        affordable_products = Product.query.filter(Product.price < 60.0).all()\n        affordable_names = [p.name for p in affordable_products]\n        \n        assert 'Cheap Product' in affordable_names\n        assert 'Medium Product' in affordable_names\n        assert 'Expensive Product' not in affordable_names\n\n    def test_product_search_functionality(self):\n        \"\"\"Test product search by name\"\"\"\n        products = [\n            Product(name='Advanced Software Solution'),\n            Product(name='Basic Software Package'),\n            Product(name='Hardware Component')\n        ]\n        \n        db.session.add_all(products)\n        db.session.commit()\n        \n        # Search for products containing 'Software'\n        software_products = Product.query.filter(Product.name.contains('Software')).all()\n        software_names = [p.name for p in software_products]\n        \n        assert 'Advanced Software Solution' in software_names\n        assert 'Basic Software Package' in software_names\n        assert 'Hardware Component' not in software_names\n\n    def test_product_update_operations(self):\n        \"\"\"Test product update operations\"\"\"\n        product = Product(\n            name='Original Product Name',\n            price=50.0,\n            status='draft'\n        )\n        \n        db.session.add(product)\n        db.session.commit()\n        \n        # Update product\n        product.name = 'Updated Product Name'\n        product.price = 75.0\n        product.status = 'active'\n        product.description = 'Added description'\n        \n        db.session.commit()\n        \n        # Verify updates\n        updated_product = Product.query.get(product.id)\n        assert updated_product.name == 'Updated Product Name'\n        assert updated_product.price == 75.0\n        assert updated_product.status == 'active'\n        assert updated_product.description == 'Added description'\n\n    def test_product_deletion(self):\n        \"\"\"Test product deletion\"\"\"\n        product = Product(\n            name='Product To Delete'\n        )\n        \n        db.session.add(product)\n        db.session.commit()\n        product_id = product.id\n        \n        # Delete product\n        db.session.delete(product)\n        db.session.commit()\n        \n        # Verify deletion\n        deleted_product = Product.query.get(product_id)\n        assert deleted_product is None\n\n    def test_product_name_uniqueness(self):\n        \"\"\"Test product name handling\"\"\"\n        product1 = Product(name='Unique Product Name 1')\n        product2 = Product(name='Unique Product Name 2')\n        \n        db.session.add_all([product1, product2])\n        db.session.commit()\n        \n        assert product1.id != product2.id\n        assert product1.name != product2.name\n\n    def test_product_optional_fields(self):\n        \"\"\"Test optional fields behavior\"\"\"\n        product = Product(\n            name='Optional Fields Test'\n        )\n        \n        db.session.add(product)\n        db.session.commit()\n        \n        # Check optional fields are None when not set\n        assert product.description is None\n        assert product.category is None\n        assert product.price is None\n        assert product.status == 'active'  # Default value from model\n\n    def test_product_field_lengths(self):\n        \"\"\"Test field length constraints\"\"\"\n        # Test name field (VARCHAR 128)\n        long_name = 'A' * 128\n        product = Product(name=long_name)\n        \n        db.session.add(product)\n        db.session.commit()\n        \n        assert len(product.name) == 128\n        \n        # Test category field (VARCHAR 64)\n        product.category = 'B' * 64\n        db.session.commit()\n        \n        assert len(product.category) == 64\n", "modifiedCode": "\"\"\"\nUnit tests for Product model.\nTests business logic, validations, and model functionality.\n\"\"\"\n\nimport pytest\nfrom datetime import datetime\nfrom models import Product\nfrom extensions import db\n\n\nclass TestProductModel:\n    \"\"\"Test suite for Product model unit tests\"\"\"\n\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        \"\"\"Setup test data for each test method\"\"\"\n        self.app = app\n        self.user = test_user\n\n    def test_product_creation_basic(self):\n        \"\"\"Test basic product creation\"\"\"\n        product = Product(\n            name='Test Product Basic',\n            description='A basic test product',\n            category='Software',\n            price=99.99,\n            status='active'\n        )\n        \n        db.session.add(product)\n        db.session.commit()\n        \n        assert product.id is not None\n        assert product.name == 'Test Product Basic'\n        assert product.description == 'A basic test product'\n        assert product.category == 'Software'\n        assert product.price == 99.99\n        assert product.status == 'active'\n\n    def test_product_creation_minimal(self):\n        \"\"\"Test product creation with minimal required fields\"\"\"\n        product = Product(\n            name='Minimal Product'\n        )\n        \n        db.session.add(product)\n        db.session.commit()\n        \n        assert product.id is not None\n        assert product.name == 'Minimal Product'\n        assert product.created_at is not None\n\n    def test_product_repr_method(self):\n        \"\"\"Test string representation of product\"\"\"\n        product = Product(name='Repr Test Product')\n        \n        expected_repr = '<Product Repr Test Product>'\n        assert repr(product) == expected_repr\n\n    def test_product_price_handling(self):\n        \"\"\"Test price field functionality\"\"\"\n        prices = [0.0, 9.99, 99.99, 999.99, 9999.99]\n        \n        products = []\n        for i, price in enumerate(prices):\n            product = Product(\n                name=f'Price Test {i}',\n                price=price\n            )\n            products.append(product)\n        \n        db.session.add_all(products)\n        db.session.commit()\n        \n        for product, expected_price in zip(products, prices):\n            assert product.price == expected_price\n            assert product.price >= 0\n\n    def test_product_categories(self):\n        \"\"\"Test different product categories\"\"\"\n        categories = [\n            'Software',\n            'Hardware',\n            'Services',\n            'Consulting',\n            'Training',\n            'Support'\n        ]\n        \n        products = []\n        for i, category in enumerate(categories):\n            product = Product(\n                name=f'Category Test {i}',\n                category=category\n            )\n            products.append(product)\n        \n        db.session.add_all(products)\n        db.session.commit()\n        \n        for product, expected_category in zip(products, categories):\n            assert product.category == expected_category\n\n    def test_product_status_types(self):\n        \"\"\"Test different product status types\"\"\"\n        statuses = ['active', 'inactive', 'discontinued', 'draft']\n        \n        products = []\n        for i, status in enumerate(statuses):\n            product = Product(\n                name=f'Status Test {i}',\n                status=status\n            )\n            products.append(product)\n        \n        db.session.add_all(products)\n        db.session.commit()\n        \n        for product, expected_status in zip(products, statuses):\n            assert product.status == expected_status\n\n    def test_product_description_field(self):\n        \"\"\"Test description field for long text\"\"\"\n        long_description = \"\"\"\n        This is a comprehensive product description that includes multiple\n        features, benefits, and technical specifications. It demonstrates\n        the ability to store detailed product information in the database.\n        The description can include formatting, special characters, and\n        extensive details about the product offering.\n        \"\"\"\n        \n        product = Product(\n            name='Description Test Product',\n            description=long_description.strip()\n        )\n        \n        db.session.add(product)\n        db.session.commit()\n        \n        assert product.description == long_description.strip()\n\n    def test_product_timestamps(self):\n        \"\"\"Test automatic timestamp handling\"\"\"\n        product = Product(\n            name='Timestamp Test Product'\n        )\n        \n        db.session.add(product)\n        db.session.commit()\n        \n        # Test created_at is set\n        assert product.created_at is not None\n        assert isinstance(product.created_at, datetime)\n        \n        # Test updated_at is set\n        assert product.updated_at is not None\n        assert isinstance(product.updated_at, datetime)\n        \n        # Test updated_at changes on update\n        original_updated_at = product.updated_at\n        product.description = 'Updated description'\n        db.session.commit()\n        \n        assert product.updated_at > original_updated_at\n\n    def test_product_query_by_category(self):\n        \"\"\"Test querying products by category\"\"\"\n        software_products = [\n            Product(name='Software 1', category='Software'),\n            Product(name='Software 2', category='Software')\n        ]\n        \n        hardware_products = [\n            Product(name='Hardware 1', category='Hardware')\n        ]\n        \n        db.session.add_all(software_products + hardware_products)\n        db.session.commit()\n        \n        # Query software products\n        software_results = Product.query.filter_by(category='Software').all()\n        software_names = [p.name for p in software_results]\n        \n        assert 'Software 1' in software_names\n        assert 'Software 2' in software_names\n        assert len([p for p in software_results if p.category == 'Software']) >= 2\n\n    def test_product_query_by_status(self):\n        \"\"\"Test querying products by status\"\"\"\n        active_products = [\n            Product(name='Active 1', status='active'),\n            Product(name='Active 2', status='active')\n        ]\n        \n        inactive_products = [\n            Product(name='Inactive 1', status='inactive')\n        ]\n        \n        db.session.add_all(active_products + inactive_products)\n        db.session.commit()\n        \n        # Query active products\n        active_results = Product.query.filter_by(status='active').all()\n        active_names = [p.name for p in active_results]\n        \n        assert 'Active 1' in active_names\n        assert 'Active 2' in active_names\n\n    def test_product_price_range_queries(self):\n        \"\"\"Test querying products by price range\"\"\"\n        products = [\n            Product(name='Cheap Product', price=10.0),\n            Product(name='Medium Product', price=50.0),\n            Product(name='Expensive Product', price=100.0)\n        ]\n        \n        db.session.add_all(products)\n        db.session.commit()\n        \n        # Query products under $60\n        affordable_products = Product.query.filter(Product.price < 60.0).all()\n        affordable_names = [p.name for p in affordable_products]\n        \n        assert 'Cheap Product' in affordable_names\n        assert 'Medium Product' in affordable_names\n        assert 'Expensive Product' not in affordable_names\n\n    def test_product_search_functionality(self):\n        \"\"\"Test product search by name\"\"\"\n        products = [\n            Product(name='Advanced Software Solution'),\n            Product(name='Basic Software Package'),\n            Product(name='Hardware Component')\n        ]\n        \n        db.session.add_all(products)\n        db.session.commit()\n        \n        # Search for products containing 'Software'\n        software_products = Product.query.filter(Product.name.contains('Software')).all()\n        software_names = [p.name for p in software_products]\n        \n        assert 'Advanced Software Solution' in software_names\n        assert 'Basic Software Package' in software_names\n        assert 'Hardware Component' not in software_names\n\n    def test_product_update_operations(self):\n        \"\"\"Test product update operations\"\"\"\n        product = Product(\n            name='Original Product Name',\n            price=50.0,\n            status='draft'\n        )\n        \n        db.session.add(product)\n        db.session.commit()\n        \n        # Update product\n        product.name = 'Updated Product Name'\n        product.price = 75.0\n        product.status = 'active'\n        product.description = 'Added description'\n        \n        db.session.commit()\n        \n        # Verify updates\n        updated_product = Product.query.get(product.id)\n        assert updated_product.name == 'Updated Product Name'\n        assert updated_product.price == 75.0\n        assert updated_product.status == 'active'\n        assert updated_product.description == 'Added description'\n\n    def test_product_deletion(self):\n        \"\"\"Test product deletion\"\"\"\n        product = Product(\n            name='Product To Delete'\n        )\n        \n        db.session.add(product)\n        db.session.commit()\n        product_id = product.id\n        \n        # Delete product\n        db.session.delete(product)\n        db.session.commit()\n        \n        # Verify deletion\n        deleted_product = Product.query.get(product_id)\n        assert deleted_product is None\n\n    def test_product_name_uniqueness(self):\n        \"\"\"Test product name handling\"\"\"\n        product1 = Product(name='Unique Product Name 1')\n        product2 = Product(name='Unique Product Name 2')\n        \n        db.session.add_all([product1, product2])\n        db.session.commit()\n        \n        assert product1.id != product2.id\n        assert product1.name != product2.name\n\n    def test_product_optional_fields(self):\n        \"\"\"Test optional fields behavior\"\"\"\n        product = Product(\n            name='Optional Fields Test'\n        )\n        \n        db.session.add(product)\n        db.session.commit()\n        \n        # Check optional fields are None when not set\n        assert product.description is None\n        assert product.category is None\n        assert product.price is None\n        assert product.status == 'active'  # Default value from model\n\n    def test_product_field_lengths(self):\n        \"\"\"Test field length constraints\"\"\"\n        # Test name field (VARCHAR 128)\n        long_name = 'A' * 128\n        product = Product(name=long_name)\n        \n        db.session.add(product)\n        db.session.commit()\n        \n        assert len(product.name) == 128\n        \n        # Test category field (VARCHAR 64)\n        product.category = 'B' * 64\n        db.session.commit()\n        \n        assert len(product.category) == 64\n"}