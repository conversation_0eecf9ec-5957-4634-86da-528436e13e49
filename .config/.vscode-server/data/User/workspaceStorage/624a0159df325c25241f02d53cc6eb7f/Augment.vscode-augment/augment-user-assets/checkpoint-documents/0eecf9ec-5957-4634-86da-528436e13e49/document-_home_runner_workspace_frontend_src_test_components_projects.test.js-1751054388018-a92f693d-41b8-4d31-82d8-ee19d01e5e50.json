{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/projects.test.js"}, "originalCode": "import { describe, it, expect, beforeEach, vi } from 'vitest'\nimport { mountComponent, mockApiResponse, mockProject, mockClient, fillForm } from '../utils/test-helpers.js'\nimport Projects from '@/views/projects/Projects.vue'\nimport ProjectEdit from '@/views/projects/ProjectEdit.vue'\nimport ProjectTeam from '@/views/projects/components/ProjectTeam.vue'\nimport { useProjectsStore } from '@/stores/projects'\nimport { useAuthStore } from '@/stores/auth'\n\n// Mock stores\nvi.mock('@/stores/projects', () => ({\n  useProjectsStore: vi.fn()\n}))\n\nvi.mock('@/stores/auth', () => ({\n  useAuthStore: vi.fn()\n}))\n\n// Mock API\nglobal.fetch = vi.fn()\n\ndescribe('Projects Components', () => {\n  let mockProjectsStore\n  let mockAuthStore\n\n  beforeEach(() => {\n    vi.clearAllMocks()\n    \n    // Setup mock projects store\n    mockProjectsStore = {\n      projects: [\n        mockProject,\n        {\n          ...mockProject,\n          id: 2,\n          name: 'Another Project',\n          status: 'completed'\n        }\n      ],\n      currentProject: null,\n      loading: false,\n      error: null,\n      fetchProjects: vi.fn(),\n      createProject: vi.fn(),\n      updateProject: vi.fn(),\n      deleteProject: vi.fn(),\n      getProject: vi.fn()\n    }\n    \n    // Setup mock auth store\n    mockAuthStore = {\n      hasPermission: vi.fn(() => true),\n      user: { id: 1, role: 'admin' }\n    }\n    \n    useProjectsStore.mockReturnValue(mockProjectsStore)\n    useAuthStore.mockReturnValue(mockAuthStore)\n    \n    fetch.mockClear()\n  })\n\n  describe('Projects List Component', () => {\n    it('should render projects list', () => {\n      const wrapper = mountComponent(Projects)\n      \n      expect(wrapper.text()).toContain('Test Project')\n      expect(wrapper.text()).toContain('Another Project')\n    })\n\n    it('should show project status badges', () => {\n      const wrapper = mountComponent(Projects)\n      \n      expect(wrapper.find('[data-testid=\"status-active\"]').exists()).toBe(true)\n      expect(wrapper.find('[data-testid=\"status-completed\"]').exists()).toBe(true)\n    })\n\n    it('should filter projects by status', async () => {\n      const wrapper = mountComponent(Projects)\n      \n      // Select \"Active\" filter\n      const statusFilter = wrapper.find('[data-testid=\"status-filter\"]')\n      await statusFilter.setValue('active')\n      \n      // Should only show active projects\n      expect(wrapper.text()).toContain('Test Project')\n      expect(wrapper.text()).not.toContain('Another Project')\n    })\n\n    it('should search projects by name', async () => {\n      const wrapper = mountComponent(Projects)\n      \n      const searchInput = wrapper.find('[data-testid=\"search-input\"]')\n      await searchInput.setValue('Another')\n      \n      // Should only show matching projects\n      expect(wrapper.text()).toContain('Another Project')\n      expect(wrapper.text()).not.toContain('Test Project')\n    })\n\n    it('should show create button for authorized users', () => {\n      const wrapper = mountComponent(Projects)\n      \n      expect(wrapper.find('[data-testid=\"create-project-button\"]').exists()).toBe(true)\n    })\n\n    it('should hide create button for unauthorized users', () => {\n      mockAuthStore.hasPermission.mockReturnValue(false)\n      \n      const wrapper = mountComponent(Projects)\n      \n      expect(wrapper.find('[data-testid=\"create-project-button\"]').exists()).toBe(false)\n    })\n\n    it('should call fetchProjects on mount', () => {\n      mountComponent(Projects)\n      \n      expect(mockProjectsStore.fetchProjects).toHaveBeenCalled()\n    })\n\n    it('should show loading state', () => {\n      mockProjectsStore.loading = true\n      \n      const wrapper = mountComponent(Projects)\n      \n      expect(wrapper.find('[data-testid=\"loading-spinner\"]').exists()).toBe(true)\n    })\n\n    it('should show error state', () => {\n      mockProjectsStore.error = 'Failed to load projects'\n      \n      const wrapper = mountComponent(Projects)\n      \n      expect(wrapper.text()).toContain('Failed to load projects')\n    })\n  })\n\n  describe('Project Edit Component', () => {\n    beforeEach(() => {\n      mockProjectsStore.currentProject = mockProject\n    })\n\n    it('should render project form', () => {\n      const wrapper = mountComponent(ProjectEdit)\n      \n      expect(wrapper.find('[data-testid=\"project-name\"]').exists()).toBe(true)\n      expect(wrapper.find('[data-testid=\"project-description\"]').exists()).toBe(true)\n      expect(wrapper.find('[data-testid=\"project-budget\"]').exists()).toBe(true)\n    })\n\n    it('should populate form with existing project data', () => {\n      const wrapper = mountComponent(ProjectEdit)\n      \n      const nameInput = wrapper.find('[data-testid=\"project-name\"]')\n      expect(nameInput.element.value).toBe(mockProject.name)\n    })\n\n    it('should validate required fields', async () => {\n      const wrapper = mountComponent(ProjectEdit)\n      \n      // Clear required field\n      await wrapper.find('[data-testid=\"project-name\"]').setValue('')\n      \n      // Try to submit\n      await wrapper.find('[data-testid=\"save-button\"]').trigger('click')\n      \n      expect(wrapper.text()).toContain('Project name is required')\n    })\n\n    it('should save project on form submission', async () => {\n      mockProjectsStore.updateProject.mockResolvedValue({ success: true })\n      \n      const wrapper = mountComponent(ProjectEdit)\n      \n      // Fill form\n      await fillForm(wrapper, {\n        'project-name': 'Updated Project Name',\n        'project-description': 'Updated description',\n        'project-budget': '15000'\n      })\n      \n      // Submit form\n      await wrapper.find('[data-testid=\"save-button\"]').trigger('click')\n      \n      expect(mockProjectsStore.updateProject).toHaveBeenCalledWith(\n        mockProject.id,\n        expect.objectContaining({\n          name: 'Updated Project Name',\n          description: 'Updated description',\n          budget: 15000\n        })\n      )\n    })\n\n    it('should show success message after save', async () => {\n      mockProjectsStore.updateProject.mockResolvedValue({ success: true })\n      \n      const wrapper = mountComponent(ProjectEdit)\n      \n      await wrapper.find('[data-testid=\"save-button\"]').trigger('click')\n      \n      expect(wrapper.text()).toContain('Project saved successfully')\n    })\n\n    it('should show error message on save failure', async () => {\n      mockProjectsStore.updateProject.mockRejectedValue(new Error('Save failed'))\n      \n      const wrapper = mountComponent(ProjectEdit)\n      \n      await wrapper.find('[data-testid=\"save-button\"]').trigger('click')\n      \n      expect(wrapper.text()).toContain('Failed to save project')\n    })\n\n    it('should navigate back on cancel', async () => {\n      const mockRouter = { push: vi.fn() }\n      \n      const wrapper = mountComponent(ProjectEdit, {\n        global: {\n          mocks: {\n            $router: mockRouter\n          }\n        }\n      })\n      \n      await wrapper.find('[data-testid=\"cancel-button\"]').trigger('click')\n      \n      expect(mockRouter.push).toHaveBeenCalledWith('/app/projects')\n    })\n  })\n\n  describe('Project Team Component', () => {\n    const mockTeamProject = {\n      ...mockProject,\n      team_members: [\n        {\n          id: 1,\n          full_name: 'John Doe',\n          email: '<EMAIL>',\n          role: 'Project Manager',\n          allocation_percentage: 50,\n          hours_worked: 40\n        },\n        {\n          id: 2,\n          full_name: 'Jane Smith',\n          email: '<EMAIL>',\n          role: 'Developer',\n          allocation_percentage: 100,\n          hours_worked: 80\n        }\n      ]\n    }\n\n    it('should display team members', () => {\n      const wrapper = mountComponent(ProjectTeam, {\n        props: { project: mockTeamProject }\n      })\n\n      expect(wrapper.text()).toContain('John Doe')\n      expect(wrapper.text()).toContain('Jane Smith')\n      expect(wrapper.text()).toContain('Project Manager')\n      expect(wrapper.text()).toContain('Developer')\n    })\n\n    it('should show team statistics', () => {\n      const wrapper = mountComponent(ProjectTeam, {\n        props: { project: mockTeamProject }\n      })\n\n      // Total hours worked: 40 + 80 = 120\n      expect(wrapper.text()).toContain('120')\n      // Average hours per member: 120 / 2 = 60\n      expect(wrapper.text()).toContain('60')\n      // Active members: 2 (both have hours > 0)\n      expect(wrapper.text()).toContain('2')\n    })\n\n    it('should add team member', async () => {\n      fetch.mockResolvedValueOnce(mockApiResponse({ success: true }))\n\n      const wrapper = mountComponent(ProjectTeam, {\n        props: { project: mockTeamProject }\n      })\n\n      // Open add member modal\n      await wrapper.find('[data-testid=\"add-member-button\"]').trigger('click')\n\n      // Fill form\n      await fillForm(wrapper, {\n        'user-select': '3',\n        'role-input': 'QA Tester',\n        'allocation-input': '75'\n      })\n\n      // Submit\n      await wrapper.find('[data-testid=\"save-member-button\"]').trigger('click')\n\n      expect(fetch).toHaveBeenCalledWith(`/api/projects/${mockTeamProject.id}/team`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'X-CSRFToken': expect.any(String)\n        },\n        body: JSON.stringify({\n          user_id: '3',\n          role: 'QA Tester',\n          allocation_percentage: 75\n        })\n      })\n    })\n\n    it('should remove team member', async () => {\n      fetch.mockResolvedValueOnce(mockApiResponse({ success: true }))\n\n      const wrapper = mountComponent(ProjectTeam, {\n        props: { project: mockTeamProject }\n      })\n\n      // Click remove button for first member\n      await wrapper.find('[data-testid=\"remove-member-1\"]').trigger('click')\n\n      // Confirm removal\n      await wrapper.find('[data-testid=\"confirm-remove\"]').trigger('click')\n\n      expect(fetch).toHaveBeenCalledWith(`/api/projects/${mockTeamProject.id}/team/1`, {\n        method: 'DELETE'\n      })\n    })\n  })\n\n  describe('Project Workflow Integration', () => {\n    it('should handle complete project creation workflow', async () => {\n      // Mock successful API calls\n      fetch\n        .mockResolvedValueOnce(mockApiResponse({ // Create project\n          success: true,\n          data: { ...mockProject, id: 1 }\n        }))\n        .mockResolvedValueOnce(mockApiResponse({ // Add team member\n          success: true\n        }))\n        .mockResolvedValueOnce(mockApiResponse({ // Create task\n          success: true,\n          data: { id: 1, title: 'Setup', status: 'todo' }\n        }))\n\n      const wrapper = mountComponent(ProjectEdit)\n\n      // Fill project form\n      await fillForm(wrapper, {\n        'project-name': 'Integration Test Project',\n        'project-description': 'Full workflow test',\n        'project-budget': '25000',\n        'project-client': '1'\n      })\n\n      // Save project\n      await wrapper.find('[data-testid=\"save-button\"]').trigger('click')\n\n      // Verify project creation API call\n      expect(fetch).toHaveBeenCalledWith('/api/projects', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          name: 'Integration Test Project',\n          description: 'Full workflow test',\n          budget: 25000,\n          client_id: 1\n        })\n      })\n    })\n\n    it('should handle project status transitions', async () => {\n      const activeProject = { ...mockProject, status: 'planning' }\n\n      fetch.mockResolvedValueOnce(mockApiResponse({\n        success: true,\n        data: { ...activeProject, status: 'active' }\n      }))\n\n      const wrapper = mountComponent(ProjectEdit, {\n        props: { project: activeProject }\n      })\n\n      // Change status to active\n      await wrapper.find('[data-testid=\"status-select\"]').setValue('active')\n      await wrapper.find('[data-testid=\"save-button\"]').trigger('click')\n\n      expect(fetch).toHaveBeenCalledWith(`/api/projects/${activeProject.id}`, {\n        method: 'PUT',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          status: 'active'\n        })\n      })\n    })\n  })\n\n  describe('Projects Store', () => {\n    it('should fetch projects successfully', async () => {\n      const mockProjects = [mockProject]\n\n      fetch.mockResolvedValueOnce(mockApiResponse({\n        success: true,\n        data: mockProjects\n      }))\n\n      const { useProjectsStore } = await import('@/stores/projects')\n      const store = useProjectsStore()\n\n      await store.fetchProjects()\n\n      expect(store.projects).toEqual(mockProjects)\n      expect(store.loading).toBe(false)\n    })\n\n    it('should create project successfully', async () => {\n      const newProject = { name: 'New Project', description: 'Test' }\n\n      fetch.mockResolvedValueOnce(mockApiResponse({\n        success: true,\n        data: { ...newProject, id: 3 }\n      }))\n\n      const { useProjectsStore } = await import('@/stores/projects')\n      const store = useProjectsStore()\n\n      const result = await store.createProject(newProject)\n\n      expect(result.success).toBe(true)\n      expect(fetch).toHaveBeenCalledWith('/api/projects', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(newProject)\n      })\n    })\n\n    it('should update project successfully', async () => {\n      const updates = { name: 'Updated Name' }\n\n      fetch.mockResolvedValueOnce(mockApiResponse({\n        success: true,\n        data: { ...mockProject, ...updates }\n      }))\n\n      const { useProjectsStore } = await import('@/stores/projects')\n      const store = useProjectsStore()\n\n      const result = await store.updateProject(mockProject.id, updates)\n\n      expect(result.success).toBe(true)\n      expect(fetch).toHaveBeenCalledWith(`/api/projects/${mockProject.id}`, {\n        method: 'PUT',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(updates)\n      })\n    })\n\n    it('should delete project successfully', async () => {\n      fetch.mockResolvedValueOnce(mockApiResponse({ success: true }))\n\n      const { useProjectsStore } = await import('@/stores/projects')\n      const store = useProjectsStore()\n\n      const result = await store.deleteProject(mockProject.id)\n\n      expect(result.success).toBe(true)\n      expect(fetch).toHaveBeenCalledWith(`/api/projects/${mockProject.id}`, {\n        method: 'DELETE'\n      })\n    })\n  })\n})\n", "modifiedCode": "import { describe, it, expect, beforeEach, vi } from 'vitest'\nimport { mountComponent, mockApiResponse, mockProject, mockClient, fillForm } from '../utils/test-helpers.js'\nimport Projects from '@/views/projects/Projects.vue'\nimport ProjectEdit from '@/views/projects/ProjectEdit.vue'\nimport ProjectTeam from '@/views/projects/components/ProjectTeam.vue'\nimport { useProjectsStore } from '@/stores/projects'\nimport { useAuthStore } from '@/stores/auth'\n\n// Mock stores\nvi.mock('@/stores/projects', () => ({\n  useProjectsStore: vi.fn()\n}))\n\nvi.mock('@/stores/auth', () => ({\n  useAuthStore: vi.fn()\n}))\n\n// Mock API\nglobal.fetch = vi.fn()\n\ndescribe('Projects Components', () => {\n  let mockProjectsStore\n  let mockAuthStore\n\n  beforeEach(() => {\n    vi.clearAllMocks()\n    \n    // Setup mock projects store to match real store structure\n    mockProjectsStore = {\n      projects: [\n        mockProject,\n        {\n          ...mockProject,\n          id: 2,\n          name: 'Another Project',\n          status: 'completed'\n        }\n      ],\n      currentProject: null,\n      loading: false,\n      error: null,\n      pagination: { page: 1, per_page: 20, total: 2, total_pages: 1 },\n      filters: { search: '', status: '', client: '', type: '' },\n      filteredProjects: [mockProject],\n      fetchProjects: vi.fn().mockResolvedValue(undefined), // Returns void, populates projects\n      createProject: vi.fn().mockResolvedValue(mockProject), // Returns project\n      updateProject: vi.fn().mockResolvedValue(mockProject), // Returns project\n      deleteProject: vi.fn().mockResolvedValue(undefined), // Returns void\n      fetchProject: vi.fn().mockResolvedValue(mockProject),\n      setFilters: vi.fn(),\n      clearFilters: vi.fn()\n    }\n    \n    // Setup mock auth store\n    mockAuthStore = {\n      hasPermission: vi.fn(() => true),\n      user: { id: 1, role: 'admin' }\n    }\n    \n    useProjectsStore.mockReturnValue(mockProjectsStore)\n    useAuthStore.mockReturnValue(mockAuthStore)\n    \n    fetch.mockClear()\n  })\n\n  describe('Projects List Component', () => {\n    it('should render projects list', () => {\n      const wrapper = mountComponent(Projects)\n      \n      expect(wrapper.text()).toContain('Test Project')\n      expect(wrapper.text()).toContain('Another Project')\n    })\n\n    it('should show project status badges', () => {\n      const wrapper = mountComponent(Projects)\n      \n      expect(wrapper.find('[data-testid=\"status-active\"]').exists()).toBe(true)\n      expect(wrapper.find('[data-testid=\"status-completed\"]').exists()).toBe(true)\n    })\n\n    it('should filter projects by status', async () => {\n      const wrapper = mountComponent(Projects)\n      \n      // Select \"Active\" filter\n      const statusFilter = wrapper.find('[data-testid=\"status-filter\"]')\n      await statusFilter.setValue('active')\n      \n      // Should only show active projects\n      expect(wrapper.text()).toContain('Test Project')\n      expect(wrapper.text()).not.toContain('Another Project')\n    })\n\n    it('should search projects by name', async () => {\n      const wrapper = mountComponent(Projects)\n      \n      const searchInput = wrapper.find('[data-testid=\"search-input\"]')\n      await searchInput.setValue('Another')\n      \n      // Should only show matching projects\n      expect(wrapper.text()).toContain('Another Project')\n      expect(wrapper.text()).not.toContain('Test Project')\n    })\n\n    it('should show create button for authorized users', () => {\n      const wrapper = mountComponent(Projects)\n      \n      expect(wrapper.find('[data-testid=\"create-project-button\"]').exists()).toBe(true)\n    })\n\n    it('should hide create button for unauthorized users', () => {\n      mockAuthStore.hasPermission.mockReturnValue(false)\n      \n      const wrapper = mountComponent(Projects)\n      \n      expect(wrapper.find('[data-testid=\"create-project-button\"]').exists()).toBe(false)\n    })\n\n    it('should call fetchProjects on mount', () => {\n      mountComponent(Projects)\n      \n      expect(mockProjectsStore.fetchProjects).toHaveBeenCalled()\n    })\n\n    it('should show loading state', () => {\n      mockProjectsStore.loading = true\n      \n      const wrapper = mountComponent(Projects)\n      \n      expect(wrapper.find('[data-testid=\"loading-spinner\"]').exists()).toBe(true)\n    })\n\n    it('should show error state', () => {\n      mockProjectsStore.error = 'Failed to load projects'\n      \n      const wrapper = mountComponent(Projects)\n      \n      expect(wrapper.text()).toContain('Failed to load projects')\n    })\n  })\n\n  describe('Project Edit Component', () => {\n    beforeEach(() => {\n      mockProjectsStore.currentProject = mockProject\n    })\n\n    it('should render project form', () => {\n      const wrapper = mountComponent(ProjectEdit)\n      \n      expect(wrapper.find('[data-testid=\"project-name\"]').exists()).toBe(true)\n      expect(wrapper.find('[data-testid=\"project-description\"]').exists()).toBe(true)\n      expect(wrapper.find('[data-testid=\"project-budget\"]').exists()).toBe(true)\n    })\n\n    it('should populate form with existing project data', () => {\n      const wrapper = mountComponent(ProjectEdit)\n      \n      const nameInput = wrapper.find('[data-testid=\"project-name\"]')\n      expect(nameInput.element.value).toBe(mockProject.name)\n    })\n\n    it('should validate required fields', async () => {\n      const wrapper = mountComponent(ProjectEdit)\n      \n      // Clear required field\n      await wrapper.find('[data-testid=\"project-name\"]').setValue('')\n      \n      // Try to submit\n      await wrapper.find('[data-testid=\"save-button\"]').trigger('click')\n      \n      expect(wrapper.text()).toContain('Project name is required')\n    })\n\n    it('should save project on form submission', async () => {\n      mockProjectsStore.updateProject.mockResolvedValue({ success: true })\n      \n      const wrapper = mountComponent(ProjectEdit)\n      \n      // Fill form\n      await fillForm(wrapper, {\n        'project-name': 'Updated Project Name',\n        'project-description': 'Updated description',\n        'project-budget': '15000'\n      })\n      \n      // Submit form\n      await wrapper.find('[data-testid=\"save-button\"]').trigger('click')\n      \n      expect(mockProjectsStore.updateProject).toHaveBeenCalledWith(\n        mockProject.id,\n        expect.objectContaining({\n          name: 'Updated Project Name',\n          description: 'Updated description',\n          budget: 15000\n        })\n      )\n    })\n\n    it('should show success message after save', async () => {\n      mockProjectsStore.updateProject.mockResolvedValue({ success: true })\n      \n      const wrapper = mountComponent(ProjectEdit)\n      \n      await wrapper.find('[data-testid=\"save-button\"]').trigger('click')\n      \n      expect(wrapper.text()).toContain('Project saved successfully')\n    })\n\n    it('should show error message on save failure', async () => {\n      mockProjectsStore.updateProject.mockRejectedValue(new Error('Save failed'))\n      \n      const wrapper = mountComponent(ProjectEdit)\n      \n      await wrapper.find('[data-testid=\"save-button\"]').trigger('click')\n      \n      expect(wrapper.text()).toContain('Failed to save project')\n    })\n\n    it('should navigate back on cancel', async () => {\n      const mockRouter = { push: vi.fn() }\n      \n      const wrapper = mountComponent(ProjectEdit, {\n        global: {\n          mocks: {\n            $router: mockRouter\n          }\n        }\n      })\n      \n      await wrapper.find('[data-testid=\"cancel-button\"]').trigger('click')\n      \n      expect(mockRouter.push).toHaveBeenCalledWith('/app/projects')\n    })\n  })\n\n  describe('Project Team Component', () => {\n    const mockTeamProject = {\n      ...mockProject,\n      team_members: [\n        {\n          id: 1,\n          full_name: 'John Doe',\n          email: '<EMAIL>',\n          role: 'Project Manager',\n          allocation_percentage: 50,\n          hours_worked: 40\n        },\n        {\n          id: 2,\n          full_name: 'Jane Smith',\n          email: '<EMAIL>',\n          role: 'Developer',\n          allocation_percentage: 100,\n          hours_worked: 80\n        }\n      ]\n    }\n\n    it('should display team members', () => {\n      const wrapper = mountComponent(ProjectTeam, {\n        props: { project: mockTeamProject }\n      })\n\n      expect(wrapper.text()).toContain('John Doe')\n      expect(wrapper.text()).toContain('Jane Smith')\n      expect(wrapper.text()).toContain('Project Manager')\n      expect(wrapper.text()).toContain('Developer')\n    })\n\n    it('should show team statistics', () => {\n      const wrapper = mountComponent(ProjectTeam, {\n        props: { project: mockTeamProject }\n      })\n\n      // Total hours worked: 40 + 80 = 120\n      expect(wrapper.text()).toContain('120')\n      // Average hours per member: 120 / 2 = 60\n      expect(wrapper.text()).toContain('60')\n      // Active members: 2 (both have hours > 0)\n      expect(wrapper.text()).toContain('2')\n    })\n\n    it('should add team member', async () => {\n      fetch.mockResolvedValueOnce(mockApiResponse({ success: true }))\n\n      const wrapper = mountComponent(ProjectTeam, {\n        props: { project: mockTeamProject }\n      })\n\n      // Open add member modal\n      await wrapper.find('[data-testid=\"add-member-button\"]').trigger('click')\n\n      // Fill form\n      await fillForm(wrapper, {\n        'user-select': '3',\n        'role-input': 'QA Tester',\n        'allocation-input': '75'\n      })\n\n      // Submit\n      await wrapper.find('[data-testid=\"save-member-button\"]').trigger('click')\n\n      expect(fetch).toHaveBeenCalledWith(`/api/projects/${mockTeamProject.id}/team`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'X-CSRFToken': expect.any(String)\n        },\n        body: JSON.stringify({\n          user_id: '3',\n          role: 'QA Tester',\n          allocation_percentage: 75\n        })\n      })\n    })\n\n    it('should remove team member', async () => {\n      fetch.mockResolvedValueOnce(mockApiResponse({ success: true }))\n\n      const wrapper = mountComponent(ProjectTeam, {\n        props: { project: mockTeamProject }\n      })\n\n      // Click remove button for first member\n      await wrapper.find('[data-testid=\"remove-member-1\"]').trigger('click')\n\n      // Confirm removal\n      await wrapper.find('[data-testid=\"confirm-remove\"]').trigger('click')\n\n      expect(fetch).toHaveBeenCalledWith(`/api/projects/${mockTeamProject.id}/team/1`, {\n        method: 'DELETE'\n      })\n    })\n  })\n\n  describe('Project Workflow Integration', () => {\n    it('should handle complete project creation workflow', async () => {\n      // Mock successful API calls\n      fetch\n        .mockResolvedValueOnce(mockApiResponse({ // Create project\n          success: true,\n          data: { ...mockProject, id: 1 }\n        }))\n        .mockResolvedValueOnce(mockApiResponse({ // Add team member\n          success: true\n        }))\n        .mockResolvedValueOnce(mockApiResponse({ // Create task\n          success: true,\n          data: { id: 1, title: 'Setup', status: 'todo' }\n        }))\n\n      const wrapper = mountComponent(ProjectEdit)\n\n      // Fill project form\n      await fillForm(wrapper, {\n        'project-name': 'Integration Test Project',\n        'project-description': 'Full workflow test',\n        'project-budget': '25000',\n        'project-client': '1'\n      })\n\n      // Save project\n      await wrapper.find('[data-testid=\"save-button\"]').trigger('click')\n\n      // Verify project creation API call\n      expect(fetch).toHaveBeenCalledWith('/api/projects', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          name: 'Integration Test Project',\n          description: 'Full workflow test',\n          budget: 25000,\n          client_id: 1\n        })\n      })\n    })\n\n    it('should handle project status transitions', async () => {\n      const activeProject = { ...mockProject, status: 'planning' }\n\n      fetch.mockResolvedValueOnce(mockApiResponse({\n        success: true,\n        data: { ...activeProject, status: 'active' }\n      }))\n\n      const wrapper = mountComponent(ProjectEdit, {\n        props: { project: activeProject }\n      })\n\n      // Change status to active\n      await wrapper.find('[data-testid=\"status-select\"]').setValue('active')\n      await wrapper.find('[data-testid=\"save-button\"]').trigger('click')\n\n      expect(fetch).toHaveBeenCalledWith(`/api/projects/${activeProject.id}`, {\n        method: 'PUT',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          status: 'active'\n        })\n      })\n    })\n  })\n\n  describe('Projects Store', () => {\n    it('should fetch projects successfully', async () => {\n      const mockProjects = [mockProject]\n\n      fetch.mockResolvedValueOnce(mockApiResponse({\n        success: true,\n        data: mockProjects\n      }))\n\n      const { useProjectsStore } = await import('@/stores/projects')\n      const store = useProjectsStore()\n\n      await store.fetchProjects()\n\n      expect(store.projects).toEqual(mockProjects)\n      expect(store.loading).toBe(false)\n    })\n\n    it('should create project successfully', async () => {\n      const newProject = { name: 'New Project', description: 'Test' }\n\n      fetch.mockResolvedValueOnce(mockApiResponse({\n        success: true,\n        data: { ...newProject, id: 3 }\n      }))\n\n      const { useProjectsStore } = await import('@/stores/projects')\n      const store = useProjectsStore()\n\n      const result = await store.createProject(newProject)\n\n      expect(result.success).toBe(true)\n      expect(fetch).toHaveBeenCalledWith('/api/projects', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(newProject)\n      })\n    })\n\n    it('should update project successfully', async () => {\n      const updates = { name: 'Updated Name' }\n\n      fetch.mockResolvedValueOnce(mockApiResponse({\n        success: true,\n        data: { ...mockProject, ...updates }\n      }))\n\n      const { useProjectsStore } = await import('@/stores/projects')\n      const store = useProjectsStore()\n\n      const result = await store.updateProject(mockProject.id, updates)\n\n      expect(result.success).toBe(true)\n      expect(fetch).toHaveBeenCalledWith(`/api/projects/${mockProject.id}`, {\n        method: 'PUT',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(updates)\n      })\n    })\n\n    it('should delete project successfully', async () => {\n      fetch.mockResolvedValueOnce(mockApiResponse({ success: true }))\n\n      const { useProjectsStore } = await import('@/stores/projects')\n      const store = useProjectsStore()\n\n      const result = await store.deleteProject(mockProject.id)\n\n      expect(result.success).toBe(true)\n      expect(fetch).toHaveBeenCalledWith(`/api/projects/${mockProject.id}`, {\n        method: 'DELETE'\n      })\n    })\n  })\n})\n"}