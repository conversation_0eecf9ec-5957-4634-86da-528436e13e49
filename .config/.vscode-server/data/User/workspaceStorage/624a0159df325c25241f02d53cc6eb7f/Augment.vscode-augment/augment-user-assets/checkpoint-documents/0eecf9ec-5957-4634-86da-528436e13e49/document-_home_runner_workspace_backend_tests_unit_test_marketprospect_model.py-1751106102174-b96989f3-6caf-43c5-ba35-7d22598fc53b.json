{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_marketprospect_model.py"}, "modifiedCode": "\"\"\"Unit tests for MarketProspect model.\"\"\"\nimport pytest\nfrom models import MarketProspect, User\nfrom extensions import db\n\nclass TestMarketProspectModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n\n    def test_marketprospect_creation_basic(self):\n        prospect = MarketProspect(\n            name='Test Market Prospect',\n            industry='Technology',\n            market_size=1000000.0,\n            created_by=self.user.id\n        )\n        db.session.add(prospect)\n        db.session.commit()\n        \n        assert prospect.id is not None\n        assert prospect.name == 'Test Market Prospect'\n        assert prospect.market_size == 1000000.0\n\n    def test_marketprospect_deletion(self):\n        prospect = MarketProspect(name='To Delete', industry='Test', created_by=self.user.id)\n        db.session.add(prospect)\n        db.session.commit()\n        prospect_id = prospect.id\n        \n        db.session.delete(prospect)\n        db.session.commit()\n        \n        deleted = MarketProspect.query.get(prospect_id)\n        assert deleted is None\n"}