{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/mocks/server.js"}, "modifiedCode": "/**\n * Mock Service Worker server setup for testing\n * Intercepts API calls and provides mock responses\n */\n\nimport { setupServer } from 'msw/node'\nimport { handlers } from './api-handlers.js'\n\n// Setup MSW server with our handlers\nexport const server = setupServer(...handlers)\n\n// Start server before all tests\nbeforeAll(() => {\n  server.listen({\n    onUnhandledRequest: 'warn' // Warn about unhandled requests\n  })\n})\n\n// Reset handlers after each test\nafterEach(() => {\n  server.resetHandlers()\n})\n\n// Close server after all tests\nafterAll(() => {\n  server.close()\n})\n\n// Export utilities for test-specific mocking\nexport const mockApi = {\n  // Mock successful responses\n  mockSuccess: (endpoint, data, status = 200) => {\n    server.use(\n      rest.get(endpoint, (req, res, ctx) => {\n        return res(\n          ctx.status(status),\n          ctx.json({ success: true, data })\n        )\n      })\n    )\n  },\n\n  // Mock error responses\n  mockError: (endpoint, error, status = 500) => {\n    server.use(\n      rest.get(endpoint, (req, res, ctx) => {\n        return res(\n          ctx.status(status),\n          ctx.json({ success: false, error })\n        )\n      })\n    )\n  },\n\n  // Mock network delays\n  mockDelay: (endpoint, delay = 1000) => {\n    server.use(\n      rest.get(endpoint, (req, res, ctx) => {\n        return res(\n          ctx.delay(delay),\n          ctx.status(200),\n          ctx.json({ success: true, data: 'Delayed response' })\n        )\n      })\n    )\n  },\n\n  // Mock authentication states\n  mockAuth: {\n    authenticated: () => {\n      server.use(\n        rest.get('/api/auth/me', (req, res, ctx) => {\n          return res(\n            ctx.status(200),\n            ctx.json({\n              success: true,\n              data: { id: 1, username: 'testuser', role: 'admin' }\n            })\n          )\n        })\n      )\n    },\n\n    unauthenticated: () => {\n      server.use(\n        rest.get('/api/auth/me', (req, res, ctx) => {\n          return res(\n            ctx.status(401),\n            ctx.json({ success: false, error: 'Unauthorized' })\n          )\n        })\n      )\n    }\n  },\n\n  // Mock specific project scenarios\n  mockProjects: {\n    empty: () => {\n      server.use(\n        rest.get('/api/projects', (req, res, ctx) => {\n          return res(\n            ctx.status(200),\n            ctx.json({\n              success: true,\n              data: { projects: [], pagination: { total: 0 } }\n            })\n          )\n        })\n      )\n    },\n\n    withData: (projects) => {\n      server.use(\n        rest.get('/api/projects', (req, res, ctx) => {\n          return res(\n            ctx.status(200),\n            ctx.json({\n              success: true,\n              data: { projects, pagination: { total: projects.length } }\n            })\n          )\n        })\n      )\n    },\n\n    createError: (errors) => {\n      server.use(\n        rest.post('/api/projects', (req, res, ctx) => {\n          return res(\n            ctx.status(400),\n            ctx.json({ success: false, errors })\n          )\n        })\n      )\n    }\n  }\n}\n"}