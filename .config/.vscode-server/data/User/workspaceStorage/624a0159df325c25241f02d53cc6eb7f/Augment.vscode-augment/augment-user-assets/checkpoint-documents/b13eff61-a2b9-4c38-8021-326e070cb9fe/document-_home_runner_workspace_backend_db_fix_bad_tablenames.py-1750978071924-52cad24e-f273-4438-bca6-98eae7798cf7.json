{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/db/fix_bad_tablenames.py"}, "modifiedCode": "#!/usr/bin/env python3\n\"\"\"\nScript per correggere i nomi di tabelle sbagliati generati automaticamente.\n\"\"\"\n\nimport os\nimport re\n\ndef fix_file_tablenames(file_path, replacements):\n    \"\"\"Corregge i nomi delle tabelle in un file\"\"\"\n    \n    if not os.path.exists(file_path):\n        print(f\"❌ File {file_path} non trovato\")\n        return 0\n    \n    with open(file_path, 'r') as f:\n        content = f.read()\n    \n    original_content = content\n    changes = 0\n    \n    for bad_name, good_name in replacements.items():\n        if bad_name in content:\n            content = content.replace(f\"__tablename__ = '{bad_name}'\", f\"__tablename__ = '{good_name}'\")\n            print(f\"  ✅ {bad_name} → {good_name}\")\n            changes += 1\n    \n    if changes > 0:\n        with open(file_path, 'w') as f:\n            f.write(content)\n        print(f\"  📝 File {file_path} aggiornato con {changes} correzioni\")\n    \n    return changes\n\ndef main():\n    \"\"\"Main function\"\"\"\n    print(\"🔧 FIXING BAD TABLE NAMES\")\n    print(\"=\" * 30)\n    \n    # Definisci le correzioni necessarie\n    bad_to_good_names = {\n        # Projects.py fixes\n        'task_dependencys': 'task_dependencies',\n        'project_k_p_is': 'project_kpis', \n        'project_k_p_i_targets': 'project_kpi_targets',\n        'project_k_p_i_templates': 'project_kpi_templates',\n        \n        # Altri possibili errori\n        'k_p_is': 'kpis',\n    }\n    \n    # File da controllare\n    files_to_check = [\n        'models_split/projects.py',\n        'models_split/business.py',\n        'models_split/content.py',\n        'models_split/system.py',\n        'models_split/crm.py',\n        'models_split/performance.py'\n    ]\n    \n    total_changes = 0\n    \n    for file_path in files_to_check:\n        print(f\"\\n📝 Checking {file_path}...\")\n        changes = fix_file_tablenames(file_path, bad_to_good_names)\n        total_changes += changes\n        \n        if changes == 0:\n            print(f\"  ✅ Nessuna correzione necessaria\")\n    \n    print(f\"\\n📊 RIEPILOGO:\")\n    print(f\"  - Correzioni totali: {total_changes}\")\n    \n    if total_changes > 0:\n        print(f\"\\n✅ Correzioni completate!\")\n        print(\"\\n🔄 PROSSIMI PASSI:\")\n        print(\"  1. Riavvia l'applicazione\")\n        print(\"  2. Rimuovi le tabelle duplicate dal database\")\n        print(\"  3. Verifica che i modelli usino i nomi corretti\")\n    else:\n        print(f\"\\n✅ Tutti i nomi delle tabelle sono già corretti!\")\n\nif __name__ == '__main__':\n    main()\n"}