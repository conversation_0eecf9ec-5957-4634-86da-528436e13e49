{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/api/test_proposals.py"}, "originalCode": "\"\"\"\nTest suite for Proposals API endpoints.\nTests CRUD operations, validation, and business logic for proposal management.\n\"\"\"\n\nimport pytest\nfrom datetime import datetime, date, timedelta\nfrom models import Proposal, Client, User\nfrom extensions import db\n\n\nclass TestProposalsAPI:\n    \"\"\"Test suite for Proposals API endpoints\"\"\"\n\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        \"\"\"Setup test data for each test method\"\"\"\n        self.app = app\n        self.user = test_user\n        \n        # Create test client\n        self.test_client = Client(name='Test Client', status='active')\n        db.session.add(self.test_client)\n        db.session.commit()\n        \n        # Create test proposal data (using correct field names from model)\n        self.proposal_data = {\n            'title': 'Test Proposal',\n            'description': 'A comprehensive test proposal for API testing',\n            'client_id': self.test_client.id,\n            'value': 50000.0,  # Correct field name\n            'status': 'draft',\n            'expiry_date': (date.today() + timedelta(days=30)).isoformat(),  # Correct field name\n        }\n\n    def test_get_proposals_success(self, client):\n        \"\"\"Test successful retrieval of proposals list\"\"\"\n        # Create test proposals\n        proposal1 = Proposal(\n            title='Proposal 1', \n            client_id=self.test_client.id,\n            amount=10000.0,\n            status='draft'\n        )\n        proposal2 = Proposal(\n            title='Proposal 2', \n            client_id=self.test_client.id,\n            amount=20000.0,\n            status='sent'\n        )\n        db.session.add_all([proposal1, proposal2])\n        db.session.commit()\n\n        response = client.get('/api/proposals')\n        \n        assert response.status_code in [200, 401]\n        if response.status_code == 200:\n            data = response.get_json()\n            assert 'proposals' in str(data).lower() or 'data' in data\n\n    def test_create_proposal_success(self, client):\n        \"\"\"Test successful proposal creation\"\"\"\n        response = client.post('/api/proposals', json=self.proposal_data)\n        \n        assert response.status_code in [201, 200, 401, 403]\n        if response.status_code in [200, 201]:\n            # Verify proposal was created\n            created_proposal = Proposal.query.filter_by(title='Test Proposal').first()\n            if created_proposal:\n                assert created_proposal.amount == 50000.0\n                assert created_proposal.client_id == self.test_client.id\n\n    def test_create_proposal_validation_error(self, client):\n        \"\"\"Test proposal creation with missing required fields\"\"\"\n        invalid_data = {'description': 'Missing title and client_id'}\n        \n        response = client.post('/api/proposals', json=invalid_data)\n        \n        assert response.status_code in [400, 422, 401, 403]\n\n    def test_update_proposal_success(self, client):\n        \"\"\"Test successful proposal update\"\"\"\n        test_proposal = Proposal(\n            title='Original Title',\n            client_id=self.test_client.id,\n            amount=30000.0,\n            status='draft'\n        )\n        db.session.add(test_proposal)\n        db.session.commit()\n\n        update_data = {\n            'title': 'Updated Proposal Title',\n            'amount': 35000.0,\n            'status': 'sent'\n        }\n        \n        response = client.put(f'/api/proposals/{test_proposal.id}', json=update_data)\n        \n        assert response.status_code in [200, 401, 403, 404]\n\n    def test_delete_proposal_success(self, client):\n        \"\"\"Test successful proposal deletion\"\"\"\n        test_proposal = Proposal(\n            title='To Delete',\n            client_id=self.test_client.id,\n            amount=15000.0,\n            status='draft'\n        )\n        db.session.add(test_proposal)\n        db.session.commit()\n        proposal_id = test_proposal.id\n\n        response = client.delete(f'/api/proposals/{proposal_id}')\n        \n        assert response.status_code in [200, 401, 403, 404]\n\n    def test_proposal_status_workflow(self, client):\n        \"\"\"Test proposal status transitions\"\"\"\n        test_proposal = Proposal(\n            title='Status Test',\n            client_id=self.test_client.id,\n            amount=25000.0,\n            status='draft'\n        )\n        db.session.add(test_proposal)\n        db.session.commit()\n\n        # Test status transitions\n        status_transitions = ['sent', 'accepted', 'rejected']\n        \n        for status in status_transitions:\n            response = client.put(\n                f'/api/proposals/{test_proposal.id}', \n                json={'status': status}\n            )\n            assert response.status_code in [200, 401, 403, 404, 400]\n\n    def test_proposal_search_and_filters(self, client):\n        \"\"\"Test proposal search and filtering\"\"\"\n        # Create test proposals with different statuses\n        proposal1 = Proposal(\n            title='Draft Proposal',\n            client_id=self.test_client.id,\n            amount=10000.0,\n            status='draft'\n        )\n        proposal2 = Proposal(\n            title='Sent Proposal',\n            client_id=self.test_client.id,\n            amount=20000.0,\n            status='sent'\n        )\n        db.session.add_all([proposal1, proposal2])\n        db.session.commit()\n\n        # Test status filter\n        response = client.get('/api/proposals?status=draft')\n        assert response.status_code in [200, 401]\n        \n        # Test search by title\n        response = client.get('/api/proposals?search=Draft')\n        assert response.status_code in [200, 401]\n        \n        # Test client filter\n        response = client.get(f'/api/proposals?client_id={self.test_client.id}')\n        assert response.status_code in [200, 401]\n\n    def test_proposal_amount_validation(self, client):\n        \"\"\"Test proposal amount validation\"\"\"\n        # Test negative amount\n        invalid_data = self.proposal_data.copy()\n        invalid_data['amount'] = -1000.0\n        \n        response = client.post('/api/proposals', json=invalid_data)\n        assert response.status_code in [400, 422, 401, 403, 201, 200]\n        \n        # Test zero amount\n        invalid_data['amount'] = 0.0\n        response = client.post('/api/proposals', json=invalid_data)\n        assert response.status_code in [400, 422, 401, 403, 201, 200]\n\n    def test_proposal_date_validation(self, client):\n        \"\"\"Test proposal date validation\"\"\"\n        # Test past valid_until date\n        invalid_data = self.proposal_data.copy()\n        invalid_data['valid_until'] = (date.today() - timedelta(days=1)).isoformat()\n        \n        response = client.post('/api/proposals', json=invalid_data)\n        assert response.status_code in [400, 422, 401, 403, 201, 200]\n\n    def test_get_proposal_detail(self, client):\n        \"\"\"Test single proposal retrieval\"\"\"\n        test_proposal = Proposal(\n            title='Detail Test',\n            client_id=self.test_client.id,\n            amount=40000.0,\n            status='draft'\n        )\n        db.session.add(test_proposal)\n        db.session.commit()\n\n        response = client.get(f'/api/proposals/{test_proposal.id}')\n        assert response.status_code in [200, 401, 404]\n\n    def test_proposal_not_found(self, client):\n        \"\"\"Test proposal not found scenarios\"\"\"\n        response = client.get('/api/proposals/99999')\n        assert response.status_code in [404, 401]\n        \n        response = client.put('/api/proposals/99999', json={'title': 'Test'})\n        assert response.status_code in [404, 401]\n        \n        response = client.delete('/api/proposals/99999')\n        assert response.status_code in [404, 401]\n\n    def test_proposal_client_relationship(self, client):\n        \"\"\"Test proposal-client relationship\"\"\"\n        # Test creating proposal with non-existent client\n        invalid_data = self.proposal_data.copy()\n        invalid_data['client_id'] = 99999\n        \n        response = client.post('/api/proposals', json=invalid_data)\n        assert response.status_code in [400, 422, 404, 401, 403]\n\n    def test_proposal_pagination(self, client):\n        \"\"\"Test proposal list pagination\"\"\"\n        # Create multiple proposals\n        for i in range(5):\n            proposal = Proposal(\n                title=f'Proposal {i}',\n                client_id=self.test_client.id,\n                amount=10000.0 + i * 1000,\n                status='draft'\n            )\n            db.session.add(proposal)\n        db.session.commit()\n\n        response = client.get('/api/proposals?page=1&per_page=3')\n        assert response.status_code in [200, 401]\n\n    def test_proposal_currency_validation(self, client):\n        \"\"\"Test proposal currency validation\"\"\"\n        # Test invalid currency\n        invalid_data = self.proposal_data.copy()\n        invalid_data['currency'] = 'INVALID'\n        \n        response = client.post('/api/proposals', json=invalid_data)\n        assert response.status_code in [400, 422, 401, 403, 201, 200]  # May or may not validate\n", "modifiedCode": "\"\"\"\nTest suite for Proposals API endpoints.\nTests CRUD operations, validation, and business logic for proposal management.\n\"\"\"\n\nimport pytest\nfrom datetime import datetime, date, timedelta\nfrom models import Proposal, Client, User\nfrom extensions import db\n\n\nclass TestProposalsAPI:\n    \"\"\"Test suite for Proposals API endpoints\"\"\"\n\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        \"\"\"Setup test data for each test method\"\"\"\n        self.app = app\n        self.user = test_user\n        \n        # Create test client\n        self.test_client = Client(name='Test Client', status='active')\n        db.session.add(self.test_client)\n        db.session.commit()\n        \n        # Create test proposal data (using correct field names from model)\n        self.proposal_data = {\n            'title': 'Test Proposal',\n            'description': 'A comprehensive test proposal for API testing',\n            'client_id': self.test_client.id,\n            'value': 50000.0,  # Correct field name\n            'status': 'draft',\n            'expiry_date': (date.today() + timedelta(days=30)).isoformat(),  # Correct field name\n        }\n\n    def test_get_proposals_success(self, client):\n        \"\"\"Test successful retrieval of proposals list\"\"\"\n        # Create test proposals\n        proposal1 = Proposal(\n            title='Proposal 1', \n            client_id=self.test_client.id,\n            amount=10000.0,\n            status='draft'\n        )\n        proposal2 = Proposal(\n            title='Proposal 2', \n            client_id=self.test_client.id,\n            amount=20000.0,\n            status='sent'\n        )\n        db.session.add_all([proposal1, proposal2])\n        db.session.commit()\n\n        response = client.get('/api/proposals/')  # Add trailing slash\n        \n        assert response.status_code in [200, 401]\n        if response.status_code == 200:\n            data = response.get_json()\n            assert 'proposals' in str(data).lower() or 'data' in data\n\n    def test_create_proposal_success(self, client):\n        \"\"\"Test successful proposal creation\"\"\"\n        response = client.post('/api/proposals', json=self.proposal_data)\n        \n        assert response.status_code in [201, 200, 401, 403]\n        if response.status_code in [200, 201]:\n            # Verify proposal was created\n            created_proposal = Proposal.query.filter_by(title='Test Proposal').first()\n            if created_proposal:\n                assert created_proposal.amount == 50000.0\n                assert created_proposal.client_id == self.test_client.id\n\n    def test_create_proposal_validation_error(self, client):\n        \"\"\"Test proposal creation with missing required fields\"\"\"\n        invalid_data = {'description': 'Missing title and client_id'}\n        \n        response = client.post('/api/proposals', json=invalid_data)\n        \n        assert response.status_code in [400, 422, 401, 403]\n\n    def test_update_proposal_success(self, client):\n        \"\"\"Test successful proposal update\"\"\"\n        test_proposal = Proposal(\n            title='Original Title',\n            client_id=self.test_client.id,\n            amount=30000.0,\n            status='draft'\n        )\n        db.session.add(test_proposal)\n        db.session.commit()\n\n        update_data = {\n            'title': 'Updated Proposal Title',\n            'amount': 35000.0,\n            'status': 'sent'\n        }\n        \n        response = client.put(f'/api/proposals/{test_proposal.id}', json=update_data)\n        \n        assert response.status_code in [200, 401, 403, 404]\n\n    def test_delete_proposal_success(self, client):\n        \"\"\"Test successful proposal deletion\"\"\"\n        test_proposal = Proposal(\n            title='To Delete',\n            client_id=self.test_client.id,\n            amount=15000.0,\n            status='draft'\n        )\n        db.session.add(test_proposal)\n        db.session.commit()\n        proposal_id = test_proposal.id\n\n        response = client.delete(f'/api/proposals/{proposal_id}')\n        \n        assert response.status_code in [200, 401, 403, 404]\n\n    def test_proposal_status_workflow(self, client):\n        \"\"\"Test proposal status transitions\"\"\"\n        test_proposal = Proposal(\n            title='Status Test',\n            client_id=self.test_client.id,\n            amount=25000.0,\n            status='draft'\n        )\n        db.session.add(test_proposal)\n        db.session.commit()\n\n        # Test status transitions\n        status_transitions = ['sent', 'accepted', 'rejected']\n        \n        for status in status_transitions:\n            response = client.put(\n                f'/api/proposals/{test_proposal.id}', \n                json={'status': status}\n            )\n            assert response.status_code in [200, 401, 403, 404, 400]\n\n    def test_proposal_search_and_filters(self, client):\n        \"\"\"Test proposal search and filtering\"\"\"\n        # Create test proposals with different statuses\n        proposal1 = Proposal(\n            title='Draft Proposal',\n            client_id=self.test_client.id,\n            amount=10000.0,\n            status='draft'\n        )\n        proposal2 = Proposal(\n            title='Sent Proposal',\n            client_id=self.test_client.id,\n            amount=20000.0,\n            status='sent'\n        )\n        db.session.add_all([proposal1, proposal2])\n        db.session.commit()\n\n        # Test status filter\n        response = client.get('/api/proposals?status=draft')\n        assert response.status_code in [200, 401]\n        \n        # Test search by title\n        response = client.get('/api/proposals?search=Draft')\n        assert response.status_code in [200, 401]\n        \n        # Test client filter\n        response = client.get(f'/api/proposals?client_id={self.test_client.id}')\n        assert response.status_code in [200, 401]\n\n    def test_proposal_amount_validation(self, client):\n        \"\"\"Test proposal amount validation\"\"\"\n        # Test negative amount\n        invalid_data = self.proposal_data.copy()\n        invalid_data['amount'] = -1000.0\n        \n        response = client.post('/api/proposals', json=invalid_data)\n        assert response.status_code in [400, 422, 401, 403, 201, 200]\n        \n        # Test zero amount\n        invalid_data['amount'] = 0.0\n        response = client.post('/api/proposals', json=invalid_data)\n        assert response.status_code in [400, 422, 401, 403, 201, 200]\n\n    def test_proposal_date_validation(self, client):\n        \"\"\"Test proposal date validation\"\"\"\n        # Test past valid_until date\n        invalid_data = self.proposal_data.copy()\n        invalid_data['valid_until'] = (date.today() - timedelta(days=1)).isoformat()\n        \n        response = client.post('/api/proposals', json=invalid_data)\n        assert response.status_code in [400, 422, 401, 403, 201, 200]\n\n    def test_get_proposal_detail(self, client):\n        \"\"\"Test single proposal retrieval\"\"\"\n        test_proposal = Proposal(\n            title='Detail Test',\n            client_id=self.test_client.id,\n            amount=40000.0,\n            status='draft'\n        )\n        db.session.add(test_proposal)\n        db.session.commit()\n\n        response = client.get(f'/api/proposals/{test_proposal.id}')\n        assert response.status_code in [200, 401, 404]\n\n    def test_proposal_not_found(self, client):\n        \"\"\"Test proposal not found scenarios\"\"\"\n        response = client.get('/api/proposals/99999')\n        assert response.status_code in [404, 401]\n        \n        response = client.put('/api/proposals/99999', json={'title': 'Test'})\n        assert response.status_code in [404, 401]\n        \n        response = client.delete('/api/proposals/99999')\n        assert response.status_code in [404, 401]\n\n    def test_proposal_client_relationship(self, client):\n        \"\"\"Test proposal-client relationship\"\"\"\n        # Test creating proposal with non-existent client\n        invalid_data = self.proposal_data.copy()\n        invalid_data['client_id'] = 99999\n        \n        response = client.post('/api/proposals', json=invalid_data)\n        assert response.status_code in [400, 422, 404, 401, 403]\n\n    def test_proposal_pagination(self, client):\n        \"\"\"Test proposal list pagination\"\"\"\n        # Create multiple proposals\n        for i in range(5):\n            proposal = Proposal(\n                title=f'Proposal {i}',\n                client_id=self.test_client.id,\n                amount=10000.0 + i * 1000,\n                status='draft'\n            )\n            db.session.add(proposal)\n        db.session.commit()\n\n        response = client.get('/api/proposals?page=1&per_page=3')\n        assert response.status_code in [200, 401]\n\n    def test_proposal_currency_validation(self, client):\n        \"\"\"Test proposal currency validation\"\"\"\n        # Test invalid currency\n        invalid_data = self.proposal_data.copy()\n        invalid_data['currency'] = 'INVALID'\n        \n        response = client.post('/api/proposals', json=invalid_data)\n        assert response.status_code in [400, 422, 401, 403, 201, 200]  # May or may not validate\n"}