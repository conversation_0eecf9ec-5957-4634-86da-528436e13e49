{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/validation/data-display-validation.test.js"}, "modifiedCode": "/**\n * Tests for validating that frontend components correctly display API data\n * and handle various data states and error conditions\n */\n\nimport { describe, it, expect, beforeEach, vi } from 'vitest'\nimport { mount } from '@vue/test-utils'\nimport { createPinia, setActivePinia } from 'pinia'\nimport { nextTick } from 'vue'\nimport Projects from '@/views/projects/Projects.vue'\nimport ProjectDetail from '@/views/projects/ProjectDetail.vue'\nimport Dashboard from '@/views/Dashboard.vue'\nimport { useProjectsStore } from '@/stores/projects'\nimport { useDashboardStore } from '@/stores/dashboard'\nimport projectsFixture from '../fixtures/projects.json'\n\ndescribe('Data Display Validation', () => {\n  let pinia\n\n  beforeEach(() => {\n    pinia = createPinia()\n    setActivePinia(pinia)\n    global.fetch = vi.fn()\n    vi.clearAllMocks()\n  })\n\n  describe('API Data Rendering', () => {\n    it('should correctly display project data from API', async () => {\n      // Mock API response with fixture data\n      fetch.mockResolvedValueOnce({\n        ok: true,\n        json: () => Promise.resolve({\n          success: true,\n          data: projectsFixture\n        })\n      })\n\n      const wrapper = mount(Projects, {\n        global: { plugins: [pinia] }\n      })\n\n      // Wait for API call and component update\n      await nextTick()\n      await new Promise(resolve => setTimeout(resolve, 0))\n\n      const projectsStore = useProjectsStore()\n      \n      // Verify store received correct data\n      expect(projectsStore.projects).toHaveLength(3)\n      expect(projectsStore.projects[0].name).toBe('E-Commerce Platform')\n      expect(projectsStore.projects[0].budget).toBe(75000)\n\n      // Verify UI displays the data correctly\n      expect(wrapper.text()).toContain('E-Commerce Platform')\n      expect(wrapper.text()).toContain('€75,000')\n      expect(wrapper.text()).toContain('TechCorp Solutions')\n      expect(wrapper.text()).toContain('Active')\n\n      // Verify project status badges\n      const statusBadges = wrapper.findAll('[data-testid=\"project-status\"]')\n      expect(statusBadges[0].text()).toContain('Active')\n      expect(statusBadges[1].text()).toContain('Planning')\n\n      // Verify budget formatting\n      expect(wrapper.text()).toContain('€75,000') // First project\n      expect(wrapper.text()).toContain('€50,000') // Second project\n      expect(wrapper.text()).toContain('€100,000') // Third project\n    })\n\n    it('should display detailed project information correctly', async () => {\n      const projectData = projectsFixture.projects[0]\n      \n      fetch.mockResolvedValueOnce({\n        ok: true,\n        json: () => Promise.resolve({\n          success: true,\n          data: projectData\n        })\n      })\n\n      const wrapper = mount(ProjectDetail, {\n        global: { \n          plugins: [pinia],\n          mocks: {\n            $route: { params: { id: '1' } }\n          }\n        }\n      })\n\n      await nextTick()\n\n      // Verify project header information\n      expect(wrapper.find('[data-testid=\"project-title\"]').text()).toBe('E-Commerce Platform')\n      expect(wrapper.find('[data-testid=\"project-description\"]').text()).toContain('Modern e-commerce platform')\n      expect(wrapper.find('[data-testid=\"project-budget\"]').text()).toContain('€75,000')\n      expect(wrapper.find('[data-testid=\"project-status\"]').text()).toContain('Active')\n\n      // Verify client information\n      expect(wrapper.find('[data-testid=\"client-name\"]').text()).toBe('TechCorp Solutions')\n      expect(wrapper.find('[data-testid=\"client-industry\"]').text()).toBe('Technology')\n\n      // Verify project dates\n      expect(wrapper.find('[data-testid=\"start-date\"]').text()).toContain('2025-01-01')\n      expect(wrapper.find('[data-testid=\"end-date\"]').text()).toContain('2025-08-31')\n\n      // Verify team members display\n      const teamMembers = wrapper.findAll('[data-testid=\"team-member\"]')\n      expect(teamMembers).toHaveLength(3)\n      expect(teamMembers[0].text()).toContain('Alice Johnson')\n      expect(teamMembers[0].text()).toContain('Project Manager')\n      expect(teamMembers[0].text()).toContain('50%')\n\n      // Verify tasks display\n      const tasks = wrapper.findAll('[data-testid=\"task-item\"]')\n      expect(tasks).toHaveLength(4)\n      expect(tasks[0].text()).toContain('Setup Development Environment')\n      expect(tasks[0].text()).toContain('Completed')\n      expect(tasks[2].text()).toContain('In Progress')\n\n      // Verify KPIs display\n      const kpis = wrapper.findAll('[data-testid=\"kpi-item\"]')\n      expect(kpis).toHaveLength(2)\n      expect(kpis[0].text()).toContain('Budget Utilization')\n      expect(kpis[0].text()).toContain('20%')\n      expect(kpis[1].text()).toContain('Task Completion Rate')\n      expect(kpis[1].text()).toContain('100%')\n    })\n\n    it('should display dashboard statistics correctly', async () => {\n      const dashboardData = {\n        totalProjects: 15,\n        activeProjects: 8,\n        completedTasks: 142,\n        pendingTasks: 23,\n        totalHours: 1250,\n        thisWeekHours: 45\n      }\n\n      fetch.mockResolvedValueOnce({\n        ok: true,\n        json: () => Promise.resolve({\n          success: true,\n          data: dashboardData\n        })\n      })\n\n      const wrapper = mount(Dashboard, {\n        global: { plugins: [pinia] }\n      })\n\n      await nextTick()\n\n      const dashboardStore = useDashboardStore()\n      \n      // Verify store data\n      expect(dashboardStore.stats.totalProjects).toBe(15)\n      expect(dashboardStore.stats.activeProjects).toBe(8)\n\n      // Verify UI displays statistics\n      expect(wrapper.find('[data-testid=\"total-projects\"]').text()).toContain('15')\n      expect(wrapper.find('[data-testid=\"active-projects\"]').text()).toContain('8')\n      expect(wrapper.find('[data-testid=\"completed-tasks\"]').text()).toContain('142')\n      expect(wrapper.find('[data-testid=\"pending-tasks\"]').text()).toContain('23')\n      expect(wrapper.find('[data-testid=\"total-hours\"]').text()).toContain('1,250')\n      expect(wrapper.find('[data-testid=\"week-hours\"]').text()).toContain('45')\n    })\n  })\n\n  describe('Data Formatting and Presentation', () => {\n    it('should format currency values correctly', async () => {\n      const projectData = {\n        id: 1,\n        name: 'Test Project',\n        budget: 123456.78,\n        expenses: 12345.67\n      }\n\n      fetch.mockResolvedValueOnce({\n        ok: true,\n        json: () => Promise.resolve({\n          success: true,\n          data: { projects: [projectData] }\n        })\n      })\n\n      const wrapper = mount(Projects, {\n        global: { plugins: [pinia] }\n      })\n\n      await nextTick()\n\n      // Should format large numbers with commas and currency symbol\n      expect(wrapper.text()).toContain('€123,456.78')\n      expect(wrapper.text()).toContain('€12,345.67')\n    })\n\n    it('should format dates correctly', async () => {\n      const projectData = {\n        id: 1,\n        name: 'Test Project',\n        start_date: '2025-01-15',\n        end_date: '2025-12-31',\n        created_at: '2025-01-01T10:30:00Z'\n      }\n\n      const wrapper = mount(ProjectDetail, {\n        global: { \n          plugins: [pinia],\n          mocks: {\n            $route: { params: { id: '1' } }\n          }\n        }\n      })\n\n      // Set project data directly for this test\n      wrapper.vm.project = projectData\n      await nextTick()\n\n      // Should format dates in readable format\n      expect(wrapper.text()).toContain('Jan 15, 2025') // start_date\n      expect(wrapper.text()).toContain('Dec 31, 2025') // end_date\n      expect(wrapper.text()).toContain('Jan 1, 2025') // created_at\n    })\n\n    it('should handle percentage calculations correctly', async () => {\n      const projectData = {\n        id: 1,\n        name: 'Test Project',\n        budget: 10000,\n        expenses: 2500,\n        team_members: [\n          { allocation_percentage: 50 },\n          { allocation_percentage: 75 },\n          { allocation_percentage: 100 }\n        ],\n        kpis: [\n          { target_value: 90, current_value: 67.5 }\n        ]\n      }\n\n      const wrapper = mount(ProjectDetail, {\n        global: { \n          plugins: [pinia],\n          mocks: {\n            $route: { params: { id: '1' } }\n          }\n        }\n      })\n\n      wrapper.vm.project = projectData\n      await nextTick()\n\n      // Budget utilization: 2500/10000 = 25%\n      expect(wrapper.text()).toContain('25%')\n      \n      // Team allocation percentages\n      expect(wrapper.text()).toContain('50%')\n      expect(wrapper.text()).toContain('75%')\n      expect(wrapper.text()).toContain('100%')\n      \n      // KPI progress: 67.5/90 = 75%\n      expect(wrapper.text()).toContain('75%')\n    })\n  })\n\n  describe('Error State Handling', () => {\n    it('should display error messages from API', async () => {\n      fetch.mockResolvedValueOnce({\n        ok: false,\n        status: 500,\n        json: () => Promise.resolve({\n          success: false,\n          error: 'Internal server error occurred'\n        })\n      })\n\n      const wrapper = mount(Projects, {\n        global: { plugins: [pinia] }\n      })\n\n      await nextTick()\n\n      const projectsStore = useProjectsStore()\n      expect(projectsStore.error).toBeTruthy()\n      expect(wrapper.find('[data-testid=\"error-message\"]').text()).toContain('Internal server error occurred')\n    })\n\n    it('should display validation errors correctly', async () => {\n      fetch.mockResolvedValueOnce({\n        ok: false,\n        status: 400,\n        json: () => Promise.resolve({\n          success: false,\n          errors: {\n            name: ['Project name is required'],\n            budget: ['Budget must be a positive number'],\n            end_date: ['End date must be after start date']\n          }\n        })\n      })\n\n      const wrapper = mount(ProjectDetail, {\n        global: { \n          plugins: [pinia],\n          mocks: {\n            $route: { params: { id: 'new' } }\n          }\n        }\n      })\n\n      // Trigger form submission\n      await wrapper.find('[data-testid=\"save-button\"]').trigger('click')\n      await nextTick()\n\n      // Should display all validation errors\n      expect(wrapper.find('[data-testid=\"name-error\"]').text()).toContain('Project name is required')\n      expect(wrapper.find('[data-testid=\"budget-error\"]').text()).toContain('Budget must be a positive number')\n      expect(wrapper.find('[data-testid=\"end-date-error\"]').text()).toContain('End date must be after start date')\n    })\n\n    it('should handle network errors gracefully', async () => {\n      fetch.mockRejectedValueOnce(new Error('Network connection failed'))\n\n      const wrapper = mount(Projects, {\n        global: { plugins: [pinia] }\n      })\n\n      await nextTick()\n\n      const projectsStore = useProjectsStore()\n      expect(projectsStore.error).toBeTruthy()\n      expect(wrapper.find('[data-testid=\"error-message\"]').text()).toContain('Network connection failed')\n      expect(wrapper.find('[data-testid=\"retry-button\"]').exists()).toBe(true)\n    })\n  })\n\n  describe('Loading States', () => {\n    it('should display loading indicators during API calls', async () => {\n      // Mock a delayed response\n      fetch.mockImplementationOnce(() => \n        new Promise(resolve => \n          setTimeout(() => resolve({\n            ok: true,\n            json: () => Promise.resolve({ success: true, data: { projects: [] } })\n          }), 100)\n        )\n      )\n\n      const wrapper = mount(Projects, {\n        global: { plugins: [pinia] }\n      })\n\n      // Should show loading state immediately\n      expect(wrapper.find('[data-testid=\"loading-spinner\"]').exists()).toBe(true)\n      expect(wrapper.find('[data-testid=\"projects-list\"]').exists()).toBe(false)\n\n      // Wait for API call to complete\n      await new Promise(resolve => setTimeout(resolve, 150))\n      await nextTick()\n\n      // Should hide loading state and show content\n      expect(wrapper.find('[data-testid=\"loading-spinner\"]').exists()).toBe(false)\n      expect(wrapper.find('[data-testid=\"projects-list\"]').exists()).toBe(true)\n    })\n  })\n\n  describe('Empty States', () => {\n    it('should display empty state when no data available', async () => {\n      fetch.mockResolvedValueOnce({\n        ok: true,\n        json: () => Promise.resolve({\n          success: true,\n          data: { projects: [], pagination: { total: 0 } }\n        })\n      })\n\n      const wrapper = mount(Projects, {\n        global: { plugins: [pinia] }\n      })\n\n      await nextTick()\n\n      expect(wrapper.find('[data-testid=\"empty-state\"]').exists()).toBe(true)\n      expect(wrapper.find('[data-testid=\"empty-state\"]').text()).toContain('No projects found')\n      expect(wrapper.find('[data-testid=\"create-first-project\"]').exists()).toBe(true)\n    })\n  })\n})\n"}