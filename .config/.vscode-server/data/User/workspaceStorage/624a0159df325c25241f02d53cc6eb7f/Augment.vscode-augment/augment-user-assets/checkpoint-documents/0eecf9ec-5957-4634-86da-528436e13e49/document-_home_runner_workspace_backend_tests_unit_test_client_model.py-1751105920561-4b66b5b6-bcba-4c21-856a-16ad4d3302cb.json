{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_client_model.py"}, "modifiedCode": "\"\"\"Unit tests for Client model.\"\"\"\nimport pytest\nfrom models import Client\nfrom extensions import db\n\nclass TestClientModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app):\n        self.app = app\n\n    def test_client_creation_basic(self):\n        client = Client(\n            name='Test Company',\n            email='<EMAIL>'\n        )\n        db.session.add(client)\n        db.session.commit()\n        \n        assert client.id is not None\n        assert client.name == 'Test Company'\n        assert client.email == '<EMAIL>'\n\n    def test_client_with_details(self):\n        client = Client(\n            name='Detailed Company',\n            email='<EMAIL>',\n            phone='+1234567890',\n            address='123 Business St',\n            industry='Technology'\n        )\n        db.session.add(client)\n        db.session.commit()\n        \n        assert client.phone == '+1234567890'\n        assert client.address == '123 Business St'\n        assert client.industry == 'Technology'\n\n    def test_client_deletion(self):\n        client = Client(name='To Delete', email='<EMAIL>')\n        db.session.add(client)\n        db.session.commit()\n        client_id = client.id\n        \n        db.session.delete(client)\n        db.session.commit()\n        \n        deleted = Client.query.get(client_id)\n        assert deleted is None\n"}