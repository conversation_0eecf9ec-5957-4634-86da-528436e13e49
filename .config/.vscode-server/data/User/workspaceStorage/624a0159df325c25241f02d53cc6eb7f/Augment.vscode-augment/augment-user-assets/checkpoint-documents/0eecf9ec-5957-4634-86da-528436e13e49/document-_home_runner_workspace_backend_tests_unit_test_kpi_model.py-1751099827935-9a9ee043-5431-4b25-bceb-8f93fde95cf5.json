{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_kpi_model.py"}, "modifiedCode": "\"\"\"Unit tests for KPI model.\"\"\"\nimport pytest\nfrom models import KPI, Project, User\nfrom extensions import db\n\nclass TestKPIModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n\n    def test_kpi_creation_basic(self):\n        kpi = KPI(\n            name='Revenue Growth',\n            description='Monthly revenue growth percentage'\n        )\n        db.session.add(kpi)\n        db.session.commit()\n        \n        assert kpi.id is not None\n        assert kpi.name == 'Revenue Growth'\n        assert kpi.description == 'Monthly revenue growth percentage'\n\n    def test_kpi_creation_complete(self):\n        kpi = KPI(\n            name='Customer Satisfaction',\n            description='Customer satisfaction score',\n            category='Customer',\n            target_value=95.0,\n            current_value=87.5,\n            unit='percentage',\n            frequency='monthly',\n            progress=92.1,\n            owner_id=self.user.id\n        )\n        db.session.add(kpi)\n        db.session.commit()\n        \n        assert kpi.category == 'Customer'\n        assert kpi.target_value == 95.0\n        assert kpi.current_value == 87.5\n        assert kpi.unit == 'percentage'\n        assert kpi.frequency == 'monthly'\n        assert kpi.progress == 92.1\n        assert kpi.owner_id == self.user.id\n\n    def test_kpi_categories(self):\n        categories = ['Financial', 'Customer', 'Process', 'Learning']\n        kpis = []\n        \n        for i, category in enumerate(categories):\n            kpi = KPI(\n                name=f'{category} KPI {i}',\n                category=category,\n                target_value=100.0\n            )\n            kpis.append(kpi)\n        \n        db.session.add_all(kpis)\n        db.session.commit()\n        \n        for kpi, expected_category in zip(kpis, categories):\n            assert kpi.category == expected_category\n\n    def test_kpi_frequencies(self):\n        frequencies = ['daily', 'weekly', 'monthly', 'quarterly', 'yearly']\n        kpis = []\n        \n        for i, frequency in enumerate(frequencies):\n            kpi = KPI(\n                name=f'Frequency KPI {i}',\n                frequency=frequency,\n                target_value=100.0\n            )\n            kpis.append(kpi)\n        \n        db.session.add_all(kpis)\n        db.session.commit()\n        \n        for kpi, expected_frequency in zip(kpis, frequencies):\n            assert kpi.frequency == expected_frequency\n\n    def test_kpi_units(self):\n        units = ['percentage', 'count', 'currency', 'hours', 'days']\n        kpis = []\n        \n        for i, unit in enumerate(units):\n            kpi = KPI(\n                name=f'Unit KPI {i}',\n                unit=unit,\n                target_value=100.0\n            )\n            kpis.append(kpi)\n        \n        db.session.add_all(kpis)\n        db.session.commit()\n        \n        for kpi, expected_unit in zip(kpis, units):\n            assert kpi.unit == expected_unit\n\n    def test_kpi_progress_calculation(self):\n        kpi = KPI(\n            name='Progress Test',\n            target_value=100.0,\n            current_value=75.0,\n            progress=75.0\n        )\n        db.session.add(kpi)\n        db.session.commit()\n        \n        assert kpi.progress == 75.0\n\n    def test_kpi_update(self):\n        kpi = KPI(\n            name='Update Test',\n            target_value=100.0,\n            current_value=50.0\n        )\n        db.session.add(kpi)\n        db.session.commit()\n        \n        kpi.current_value = 80.0\n        kpi.progress = 80.0\n        db.session.commit()\n        \n        updated = KPI.query.get(kpi.id)\n        assert updated.current_value == 80.0\n        assert updated.progress == 80.0\n\n    def test_kpi_deletion(self):\n        kpi = KPI(\n            name='To Be Deleted',\n            target_value=100.0\n        )\n        db.session.add(kpi)\n        db.session.commit()\n        kpi_id = kpi.id\n        \n        db.session.delete(kpi)\n        db.session.commit()\n        \n        deleted = KPI.query.get(kpi_id)\n        assert deleted is None\n"}