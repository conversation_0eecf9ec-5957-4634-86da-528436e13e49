{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/testing/e2e-testing-guide.md"}, "originalCode": "# 🌐 End-to-End Testing Guide\n\nGuida completa per il testing end-to-end con Cypress per validare workflow utente completi.\n\n## 📋 Indice\n\n- [Setup e Configurazione](#setup-e-configurazione)\n- [Workflow Testing](#workflow-testing)\n- [User Journey Testing](#user-journey-testing)\n- [Cross-Browser Testing](#cross-browser-testing)\n- [Data Management](#data-management)\n- [Custom Commands](#custom-commands)\n- [Best Practices](#best-practices)\n\n## ⚙️ Setup e Configurazione\n\n### Struttura Directory\n\n```\nfrontend/cypress/\n├── e2e/\n│   ├── auth/\n│   │   ├── login.cy.js              # Test autenticazione\n│   │   └── user-management.cy.js    # Test gestione utenti\n│   ├── projects/\n│   │   ├── project-creation.cy.js   # Test creazione progetti\n│   │   ├── project-workflows.cy.js  # Test workflow progetti\n│   │   └── team-management.cy.js    # Test gestione team\n│   ├── timesheet/\n│   │   ├── timesheet-entry.cy.js    # Test inserimento ore\n│   │   └── timesheet-approval.cy.js # Test approvazione\n│   └── dashboard/\n│       └── dashboard-overview.cy.js # Test dashboard\n├── fixtures/\n│   ├── projects.json               # Dati progetti di test\n│   ├── users.json                 # Dati utenti di test\n│   └── timesheet.json             # Dati timesheet di test\n├── support/\n│   ├── commands.js                # Custom commands\n│   ├── e2e.js                    # Setup globale\n│   └── utils.js                  # Utility functions\n└── cypress.config.js             # Configurazione Cypress\n```\n\n### Configurazione Cypress\n\n```javascript\n// cypress.config.js\nimport { defineConfig } from 'cypress'\n\nexport default defineConfig({\n  e2e: {\n    baseUrl: 'http://localhost:3000',\n    viewportWidth: 1280,\n    viewportHeight: 720,\n    video: true,\n    screenshotOnRunFailure: true,\n\n    // Test files\n    specPattern: 'cypress/e2e/**/*.cy.{js,jsx,ts,tsx}',\n\n    // Support files\n    supportFile: 'cypress/support/e2e.js',\n\n    // Timeouts\n    defaultCommandTimeout: 10000,\n    requestTimeout: 10000,\n    responseTimeout: 10000,\n\n    // Retry configuration\n    retries: {\n      runMode: 2,\n      openMode: 0\n    },\n\n    // Environment variables\n    env: {\n      apiUrl: 'http://localhost:5000/api',\n      adminUsername: 'admin',\n      adminPassword: 'password'\n    },\n\n    setupNodeEvents(on, config) {\n      // Task plugins\n      on('task', {\n        // Database seeding\n        seedDatabase() {\n          // Seed test data\n          return null\n        },\n\n        // Clear database\n        clearDatabase() {\n          // Clear test data\n          return null\n        }\n      })\n\n      return config\n    }\n  }\n})\n```\n\n### Setup Globale\n\n```javascript\n// cypress/support/e2e.js\nimport './commands'\nimport './utils'\n\n// Global configuration\nCypress.on('uncaught:exception', (err, runnable) => {\n  // Prevent Cypress from failing on uncaught exceptions\n  if (err.message.includes('ResizeObserver loop limit exceeded')) {\n    return false\n  }\n  return true\n})\n\n// Before each test\nbeforeEach(() => {\n  // Clear local storage\n  cy.clearLocalStorage()\n\n  // Clear cookies\n  cy.clearCookies()\n\n  // Set viewport\n  cy.viewport(1280, 720)\n})\n\n// After each test\nafterEach(() => {\n  // Take screenshot on failure\n  if (Cypress.currentTest.state === 'failed') {\n    cy.screenshot(`failed-${Cypress.currentTest.title}`)\n  }\n})\n```\n\n## 🔄 Workflow Testing\n\n### Test Creazione Progetto Completo\n\n```javascript\n// cypress/e2e/projects/project-creation.cy.js\ndescribe('Project Creation Workflow', () => {\n  beforeEach(() => {\n    cy.loginAsAdmin()\n    cy.intercept('GET', '/api/clients*', { fixture: 'clients.json' }).as('getClients')\n    cy.intercept('POST', '/api/projects', { fixture: 'project-created.json' }).as('createProject')\n  })\n\n  it('should create complete project with all details', () => {\n    // 1. Navigate to project creation\n    cy.visit('/app/projects')\n    cy.get('[data-testid=\"create-project-button\"]').click()\n    cy.url().should('include', '/projects/new')\n\n    // 2. Fill basic project information\n    cy.get('[data-testid=\"project-name\"]').type('E2E Complete Project')\n    cy.get('[data-testid=\"project-description\"]').type('Full end-to-end test project with comprehensive features')\n\n    // 3. Set financial details\n    cy.get('[data-testid=\"project-budget\"]').type('75000')\n    cy.get('[data-testid=\"project-type\"]').select('service')\n    cy.get('[data-testid=\"project-billable\"]').check()\n    cy.get('[data-testid=\"client-daily-rate\"]').type('500')\n\n    // 4. Set project timeline\n    cy.get('[data-testid=\"project-start-date\"]').type('2025-01-01')\n    cy.get('[data-testid=\"project-end-date\"]').type('2025-06-30')\n\n    // 5. Select client\n    cy.wait('@getClients')\n    cy.get('[data-testid=\"project-client\"]').select('TechCorp Solutions')\n\n    // 6. Set project status and type\n    cy.get('[data-testid=\"project-status\"]').select('planning')\n    cy.get('[data-testid=\"is-billable\"]').check()\n\n    // 7. Save project\n    cy.get('[data-testid=\"save-button\"]').click()\n    cy.wait('@createProject')\n\n    // 8. Verify success and navigation\n    cy.get('[data-testid=\"success-message\"]')\n      .should('be.visible')\n      .and('contain', 'Project created successfully')\n\n    cy.url().should('match', /\\/projects\\/\\d+/)\n\n    // 9. Verify project details page\n    cy.get('[data-testid=\"project-title\"]').should('contain', 'E2E Complete Project')\n    cy.get('[data-testid=\"project-budget\"]').should('contain', '€75,000')\n    cy.get('[data-testid=\"project-status\"]').should('contain', 'Planning')\n    cy.get('[data-testid=\"client-name\"]').should('contain', 'TechCorp Solutions')\n  })\n\n  it('should handle validation errors during creation', () => {\n    cy.visit('/app/projects/new')\n\n    // Try to save without required fields\n    cy.get('[data-testid=\"save-button\"]').click()\n\n    // Verify validation errors\n    cy.get('[data-testid=\"name-error\"]')\n      .should('be.visible')\n      .and('contain', 'Project name is required')\n\n    cy.get('[data-testid=\"budget-error\"]')\n      .should('be.visible')\n      .and('contain', 'Budget is required')\n\n    // Fill invalid data\n    cy.get('[data-testid=\"project-name\"]').type('A') // Too short\n    cy.get('[data-testid=\"project-budget\"]').type('-1000') // Negative\n    cy.get('[data-testid=\"project-start-date\"]').type('2025-12-31')\n    cy.get('[data-testid=\"project-end-date\"]').type('2025-01-01') // Before start\n\n    cy.get('[data-testid=\"save-button\"]').click()\n\n    // Verify specific validation errors\n    cy.get('[data-testid=\"name-error\"]').should('contain', 'at least 3 characters')\n    cy.get('[data-testid=\"budget-error\"]').should('contain', 'must be positive')\n    cy.get('[data-testid=\"end-date-error\"]').should('contain', 'must be after start date')\n  })\n\n  it('should save draft and continue later', () => {\n    cy.visit('/app/projects/new')\n\n    // Fill partial information\n    cy.get('[data-testid=\"project-name\"]').type('Draft Project')\n    cy.get('[data-testid=\"project-description\"]').type('This is a draft project')\n\n    // Save as draft\n    cy.get('[data-testid=\"save-draft-button\"]').click()\n\n    // Verify draft saved\n    cy.get('[data-testid=\"draft-saved-message\"]')\n      .should('be.visible')\n      .and('contain', 'Draft saved')\n\n    // Navigate away and back\n    cy.visit('/app/dashboard')\n    cy.visit('/app/projects/new')\n\n    // Verify draft data restored\n    cy.get('[data-testid=\"project-name\"]').should('have.value', 'Draft Project')\n    cy.get('[data-testid=\"project-description\"]').should('contain', 'This is a draft project')\n  })\n})\n```", "modifiedCode": "# 🌐 End-to-End Testing Guide\n\nGuida completa per il testing end-to-end con Cypress per validare workflow utente completi.\n\n## 📋 Indice\n\n- [Setup e Configurazione](#setup-e-configurazione)\n- [Workflow Testing](#workflow-testing)\n- [User Journey Testing](#user-journey-testing)\n- [Cross-Browser Testing](#cross-browser-testing)\n- [Data Management](#data-management)\n- [Custom Commands](#custom-commands)\n- [Best Practices](#best-practices)\n\n## ⚙️ Setup e Configurazione\n\n### Struttura Directory\n\n```\nfrontend/cypress/\n├── e2e/\n│   ├── auth/\n│   │   ├── login.cy.js              # Test autenticazione\n│   │   └── user-management.cy.js    # Test gestione utenti\n│   ├── projects/\n│   │   ├── project-creation.cy.js   # Test creazione progetti\n│   │   ├── project-workflows.cy.js  # Test workflow progetti\n│   │   └── team-management.cy.js    # Test gestione team\n│   ├── timesheet/\n│   │   ├── timesheet-entry.cy.js    # Test inserimento ore\n│   │   └── timesheet-approval.cy.js # Test approvazione\n│   └── dashboard/\n│       └── dashboard-overview.cy.js # Test dashboard\n├── fixtures/\n│   ├── projects.json               # Dati progetti di test\n│   ├── users.json                 # Dati utenti di test\n│   └── timesheet.json             # Dati timesheet di test\n├── support/\n│   ├── commands.js                # Custom commands\n│   ├── e2e.js                    # Setup globale\n│   └── utils.js                  # Utility functions\n└── cypress.config.js             # Configurazione Cypress\n```\n\n### Configurazione Cypress\n\n```javascript\n// cypress.config.js\nimport { defineConfig } from 'cypress'\n\nexport default defineConfig({\n  e2e: {\n    baseUrl: 'http://localhost:3000',\n    viewportWidth: 1280,\n    viewportHeight: 720,\n    video: true,\n    screenshotOnRunFailure: true,\n\n    // Test files\n    specPattern: 'cypress/e2e/**/*.cy.{js,jsx,ts,tsx}',\n\n    // Support files\n    supportFile: 'cypress/support/e2e.js',\n\n    // Timeouts\n    defaultCommandTimeout: 10000,\n    requestTimeout: 10000,\n    responseTimeout: 10000,\n\n    // Retry configuration\n    retries: {\n      runMode: 2,\n      openMode: 0\n    },\n\n    // Environment variables\n    env: {\n      apiUrl: 'http://localhost:5000/api',\n      adminUsername: 'admin',\n      adminPassword: 'password'\n    },\n\n    setupNodeEvents(on, config) {\n      // Task plugins\n      on('task', {\n        // Database seeding\n        seedDatabase() {\n          // Seed test data\n          return null\n        },\n\n        // Clear database\n        clearDatabase() {\n          // Clear test data\n          return null\n        }\n      })\n\n      return config\n    }\n  }\n})\n```\n\n### Setup Globale\n\n```javascript\n// cypress/support/e2e.js\nimport './commands'\nimport './utils'\n\n// Global configuration\nCypress.on('uncaught:exception', (err, runnable) => {\n  // Prevent Cypress from failing on uncaught exceptions\n  if (err.message.includes('ResizeObserver loop limit exceeded')) {\n    return false\n  }\n  return true\n})\n\n// Before each test\nbeforeEach(() => {\n  // Clear local storage\n  cy.clearLocalStorage()\n\n  // Clear cookies\n  cy.clearCookies()\n\n  // Set viewport\n  cy.viewport(1280, 720)\n})\n\n// After each test\nafterEach(() => {\n  // Take screenshot on failure\n  if (Cypress.currentTest.state === 'failed') {\n    cy.screenshot(`failed-${Cypress.currentTest.title}`)\n  }\n})\n```\n\n## 🔄 Workflow Testing\n\n### Test Creazione Progetto Completo\n\n```javascript\n// cypress/e2e/projects/project-creation.cy.js\ndescribe('Project Creation Workflow', () => {\n  beforeEach(() => {\n    cy.loginAsAdmin()\n    cy.intercept('GET', '/api/clients*', { fixture: 'clients.json' }).as('getClients')\n    cy.intercept('POST', '/api/projects', { fixture: 'project-created.json' }).as('createProject')\n  })\n\n  it('should create complete project with all details', () => {\n    // 1. Navigate to project creation\n    cy.visit('/app/projects')\n    cy.get('[data-testid=\"create-project-button\"]').click()\n    cy.url().should('include', '/projects/new')\n\n    // 2. Fill basic project information\n    cy.get('[data-testid=\"project-name\"]').type('E2E Complete Project')\n    cy.get('[data-testid=\"project-description\"]').type('Full end-to-end test project with comprehensive features')\n\n    // 3. Set financial details\n    cy.get('[data-testid=\"project-budget\"]').type('75000')\n    cy.get('[data-testid=\"project-type\"]').select('service')\n    cy.get('[data-testid=\"project-billable\"]').check()\n    cy.get('[data-testid=\"client-daily-rate\"]').type('500')\n\n    // 4. Set project timeline\n    cy.get('[data-testid=\"project-start-date\"]').type('2025-01-01')\n    cy.get('[data-testid=\"project-end-date\"]').type('2025-06-30')\n\n    // 5. Select client\n    cy.wait('@getClients')\n    cy.get('[data-testid=\"project-client\"]').select('TechCorp Solutions')\n\n    // 6. Set project status and type\n    cy.get('[data-testid=\"project-status\"]').select('planning')\n    cy.get('[data-testid=\"is-billable\"]').check()\n\n    // 7. Save project\n    cy.get('[data-testid=\"save-button\"]').click()\n    cy.wait('@createProject')\n\n    // 8. Verify success and navigation\n    cy.get('[data-testid=\"success-message\"]')\n      .should('be.visible')\n      .and('contain', 'Project created successfully')\n\n    cy.url().should('match', /\\/projects\\/\\d+/)\n\n    // 9. Verify project details page\n    cy.get('[data-testid=\"project-title\"]').should('contain', 'E2E Complete Project')\n    cy.get('[data-testid=\"project-budget\"]').should('contain', '€75,000')\n    cy.get('[data-testid=\"project-status\"]').should('contain', 'Planning')\n    cy.get('[data-testid=\"client-name\"]').should('contain', 'TechCorp Solutions')\n  })\n\n  it('should handle validation errors during creation', () => {\n    cy.visit('/app/projects/new')\n\n    // Try to save without required fields\n    cy.get('[data-testid=\"save-button\"]').click()\n\n    // Verify validation errors\n    cy.get('[data-testid=\"name-error\"]')\n      .should('be.visible')\n      .and('contain', 'Project name is required')\n\n    cy.get('[data-testid=\"budget-error\"]')\n      .should('be.visible')\n      .and('contain', 'Budget is required')\n\n    // Fill invalid data\n    cy.get('[data-testid=\"project-name\"]').type('A') // Too short\n    cy.get('[data-testid=\"project-budget\"]').type('-1000') // Negative\n    cy.get('[data-testid=\"project-start-date\"]').type('2025-12-31')\n    cy.get('[data-testid=\"project-end-date\"]').type('2025-01-01') // Before start\n\n    cy.get('[data-testid=\"save-button\"]').click()\n\n    // Verify specific validation errors\n    cy.get('[data-testid=\"name-error\"]').should('contain', 'at least 3 characters')\n    cy.get('[data-testid=\"budget-error\"]').should('contain', 'must be positive')\n    cy.get('[data-testid=\"end-date-error\"]').should('contain', 'must be after start date')\n  })\n\n  it('should save draft and continue later', () => {\n    cy.visit('/app/projects/new')\n\n    // Fill partial information\n    cy.get('[data-testid=\"project-name\"]').type('Draft Project')\n    cy.get('[data-testid=\"project-description\"]').type('This is a draft project')\n\n    // Save as draft\n    cy.get('[data-testid=\"save-draft-button\"]').click()\n\n    // Verify draft saved\n    cy.get('[data-testid=\"draft-saved-message\"]')\n      .should('be.visible')\n      .and('contain', 'Draft saved')\n\n    // Navigate away and back\n    cy.visit('/app/dashboard')\n    cy.visit('/app/projects/new')\n\n    // Verify draft data restored\n    cy.get('[data-testid=\"project-name\"]').should('have.value', 'Draft Project')\n    cy.get('[data-testid=\"project-description\"]').should('contain', 'This is a draft project')\n  })\n})\n```\n\n### Test Gestione Team Completa\n\n```javascript\n// cypress/e2e/projects/team-management.cy.js\ndescribe('Project Team Management', () => {\n  beforeEach(() => {\n    cy.loginAsAdmin()\n    cy.intercept('GET', '/api/personnel/users*', { fixture: 'users.json' }).as('getUsers')\n    cy.intercept('POST', '/api/projects/*/team', { statusCode: 200, body: { success: true } }).as('addTeamMember')\n    cy.intercept('DELETE', '/api/projects/*/team/*', { statusCode: 200, body: { success: true } }).as('removeTeamMember')\n  })\n\n  it('should manage complete team lifecycle', () => {\n    // Navigate to existing project\n    cy.visit('/app/projects/1')\n    cy.get('[data-testid=\"team-tab\"]').click()\n\n    // Add multiple team members\n    const teamMembers = [\n      { name: 'John Doe', role: 'Project Manager', allocation: '50' },\n      { name: 'Jane Smith', role: 'Senior Developer', allocation: '100' },\n      { name: 'Bob Wilson', role: 'QA Engineer', allocation: '75' }\n    ]\n\n    teamMembers.forEach((member, index) => {\n      // Open add member modal\n      cy.get('[data-testid=\"add-member-button\"]').click()\n\n      // Fill member details\n      cy.wait('@getUsers')\n      cy.get('[data-testid=\"user-select\"]').select(member.name)\n      cy.get('[data-testid=\"role-input\"]').type(member.role)\n      cy.get('[data-testid=\"allocation-input\"]').clear().type(member.allocation)\n\n      // Save member\n      cy.get('[data-testid=\"save-member-button\"]').click()\n      cy.wait('@addTeamMember')\n\n      // Verify member added\n      cy.get('[data-testid=\"team-member-list\"]')\n        .should('contain', member.name)\n        .and('contain', member.role)\n        .and('contain', `${member.allocation}%`)\n\n      // Verify success message\n      cy.get('[data-testid=\"success-message\"]')\n        .should('be.visible')\n        .and('contain', 'Team member added successfully')\n    })\n\n    // Verify team statistics\n    cy.get('[data-testid=\"total-allocation\"]').should('contain', '225%') // 50+100+75\n    cy.get('[data-testid=\"team-size\"]').should('contain', '3 members')\n\n    // Edit team member\n    cy.get('[data-testid=\"edit-member-2\"]').click()\n    cy.get('[data-testid=\"role-input\"]').clear().type('Lead Developer')\n    cy.get('[data-testid=\"allocation-input\"]').clear().type('80')\n    cy.get('[data-testid=\"save-member-button\"]').click()\n\n    // Verify changes\n    cy.get('[data-testid=\"team-member-list\"]')\n      .should('contain', 'Lead Developer')\n      .and('contain', '80%')\n\n    // Remove team member\n    cy.get('[data-testid=\"remove-member-3\"]').click()\n    cy.get('[data-testid=\"confirm-remove\"]').click()\n    cy.wait('@removeTeamMember')\n\n    // Verify member removed\n    cy.get('[data-testid=\"team-member-list\"]').should('not.contain', 'Bob Wilson')\n    cy.get('[data-testid=\"team-size\"]').should('contain', '2 members')\n  })\n\n  it('should handle team member conflicts', () => {\n    cy.visit('/app/projects/1')\n    cy.get('[data-testid=\"team-tab\"]').click()\n\n    // Try to add user already in team\n    cy.get('[data-testid=\"add-member-button\"]').click()\n    cy.get('[data-testid=\"user-select\"]').select('John Doe') // Already in team\n    cy.get('[data-testid=\"save-member-button\"]').click()\n\n    // Verify error message\n    cy.get('[data-testid=\"error-message\"]')\n      .should('be.visible')\n      .and('contain', 'User already in team')\n\n    // Try to remove project manager\n    cy.get('[data-testid=\"remove-member-1\"]').click() // Project manager\n    cy.get('[data-testid=\"confirm-remove\"]').click()\n\n    // Verify prevention message\n    cy.get('[data-testid=\"error-message\"]')\n      .should('be.visible')\n      .and('contain', 'Cannot remove project manager')\n  })\n\n  it('should show team analytics and insights', () => {\n    cy.visit('/app/projects/1')\n    cy.get('[data-testid=\"team-tab\"]').click()\n\n    // Verify team analytics are displayed\n    cy.get('[data-testid=\"team-analytics\"]').should('be.visible')\n    cy.get('[data-testid=\"total-hours\"]').should('be.visible')\n    cy.get('[data-testid=\"average-hours\"]').should('be.visible')\n    cy.get('[data-testid=\"active-members\"]').should('be.visible')\n\n    // Verify allocation chart\n    cy.get('[data-testid=\"allocation-chart\"]').should('be.visible')\n\n    // Run AI analysis\n    cy.get('[data-testid=\"ai-analysis-button\"]').click()\n    cy.get('[data-testid=\"ai-analyzing\"]').should('be.visible')\n\n    // Wait for AI analysis to complete\n    cy.get('[data-testid=\"ai-insights\"]', { timeout: 10000 }).should('be.visible')\n    cy.get('[data-testid=\"ai-recommendations\"]').should('be.visible')\n\n    // Apply AI recommendation\n    cy.get('[data-testid=\"apply-recommendation-1\"]').click()\n    cy.get('[data-testid=\"confirm-apply\"]').click()\n\n    // Verify recommendation applied\n    cy.get('[data-testid=\"success-message\"]')\n      .should('contain', 'Recommendation applied successfully')\n  })\n})\n```\n\n## 👤 User Journey Testing\n\n### Test Workflow Utente Completo\n\n```javascript\n// cypress/e2e/user-journeys/employee-daily-workflow.cy.js\ndescribe('Employee Daily Workflow', () => {\n  beforeEach(() => {\n    cy.loginAsEmployee()\n  })\n\n  it('should complete daily employee workflow', () => {\n    // 1. Login and view dashboard\n    cy.visit('/app/dashboard')\n    cy.get('[data-testid=\"welcome-message\"]').should('contain', 'Welcome back')\n\n    // 2. Check today's tasks\n    cy.get('[data-testid=\"todays-tasks\"]').should('be.visible')\n    cy.get('[data-testid=\"task-item\"]').should('have.length.at.least', 1)\n\n    // 3. Navigate to timesheet\n    cy.get('[data-testid=\"timesheet-menu\"]').click()\n    cy.get('[data-testid=\"my-hours-submenu\"]').click()\n\n    // 4. Log hours for today\n    cy.get('[data-testid=\"add-hours-button\"]').click()\n    cy.get('[data-testid=\"project-select\"]').select('E-Commerce Platform')\n    cy.get('[data-testid=\"task-select\"]').select('Implement Authentication')\n    cy.get('[data-testid=\"hours-input\"]').type('8')\n    cy.get('[data-testid=\"description-input\"]').type('Worked on user authentication module')\n    cy.get('[data-testid=\"save-hours-button\"]').click()\n\n    // 5. Verify hours logged\n    cy.get('[data-testid=\"success-message\"]').should('contain', 'Hours logged successfully')\n    cy.get('[data-testid=\"todays-total\"]').should('contain', '8 hours')\n\n    // 6. Update task status\n    cy.visit('/app/projects/1')\n    cy.get('[data-testid=\"tasks-tab\"]').click()\n    cy.get('[data-testid=\"task-status-3\"]').select('completed')\n    cy.get('[data-testid=\"update-task-button\"]').click()\n\n    // 7. Verify task updated\n    cy.get('[data-testid=\"success-message\"]').should('contain', 'Task updated')\n    cy.get('[data-testid=\"task-3\"]').should('contain', 'Completed')\n\n    // 8. Check notifications\n    cy.get('[data-testid=\"notifications-bell\"]').click()\n    cy.get('[data-testid=\"notifications-panel\"]').should('be.visible')\n\n    // 9. Submit timesheet for approval\n    cy.visit('/app/timesheet/my-hours')\n    cy.get('[data-testid=\"submit-week-button\"]').click()\n    cy.get('[data-testid=\"confirm-submit\"]').click()\n\n    // 10. Verify submission\n    cy.get('[data-testid=\"success-message\"]')\n      .should('contain', 'Timesheet submitted for approval')\n    cy.get('[data-testid=\"week-status\"]').should('contain', 'Pending Approval')\n  })\n})\n```"}