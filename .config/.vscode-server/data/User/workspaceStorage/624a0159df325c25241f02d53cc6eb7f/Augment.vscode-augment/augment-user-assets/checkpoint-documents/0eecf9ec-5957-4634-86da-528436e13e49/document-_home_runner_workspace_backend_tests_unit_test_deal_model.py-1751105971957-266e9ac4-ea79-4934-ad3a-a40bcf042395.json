{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_deal_model.py"}, "modifiedCode": "\"\"\"Unit tests for Deal model.\"\"\"\nimport pytest\nfrom models import Deal, Client, User\nfrom extensions import db\n\nclass TestDealModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n        \n        self.client = Client.query.first()\n        if not self.client:\n            self.client = Client(name='Test Client', email='<EMAIL>')\n            db.session.add(self.client)\n            db.session.commit()\n\n    def test_deal_creation_basic(self):\n        deal = Deal(\n            name='Test Deal',\n            client_id=self.client.id,\n            value=25000.0,\n            stage='negotiation'\n        )\n        db.session.add(deal)\n        db.session.commit()\n        \n        assert deal.id is not None\n        assert deal.name == 'Test Deal'\n        assert deal.value == 25000.0\n\n    def test_deal_deletion(self):\n        deal = Deal(name='To Delete', client_id=self.client.id)\n        db.session.add(deal)\n        db.session.commit()\n        deal_id = deal.id\n        \n        db.session.delete(deal)\n        db.session.commit()\n        \n        deleted = Deal.query.get(deal_id)\n        assert deleted is None\n"}