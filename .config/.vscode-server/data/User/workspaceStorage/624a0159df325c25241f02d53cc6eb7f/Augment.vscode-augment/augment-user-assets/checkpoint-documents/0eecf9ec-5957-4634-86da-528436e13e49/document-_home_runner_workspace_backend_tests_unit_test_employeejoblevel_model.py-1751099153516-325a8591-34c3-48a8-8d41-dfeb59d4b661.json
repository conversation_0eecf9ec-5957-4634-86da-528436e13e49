{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_employeejoblevel_model.py"}, "modifiedCode": "\"\"\"\nUnit tests for EmployeeJobLevel model.\nTests business logic, validations, and model functionality.\n\"\"\"\n\nimport pytest\nfrom datetime import datetime, date\nfrom models import Employ<PERSON><PERSON><PERSON><PERSON>eve<PERSON>, User, JobLevel\nfrom extensions import db\n\n\nclass TestEmployeeJobLevelModel:\n    \"\"\"Test suite for EmployeeJobLevel model unit tests\"\"\"\n\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        \"\"\"Setup test data for each test method\"\"\"\n        self.app = app\n        self.user = test_user\n        \n        # Create test job level\n        self.test_job_level = JobLevel(\n            name='Test Job Level',\n            level_number=9999,\n            description='Job level for testing'\n        )\n        db.session.add(self.test_job_level)\n        db.session.commit()\n\n    def test_employeejoblevel_creation_basic(self):\n        \"\"\"Test basic employee job level creation\"\"\"\n        employee_job_level = EmployeeJobLevel(\n            user_id=self.user.id,\n            job_level_id=self.test_job_level.id,\n            start_date=date.today(),\n            current_salary=50000.0\n        )\n        \n        db.session.add(employee_job_level)\n        db.session.commit()\n        \n        assert employee_job_level.id is not None\n        assert employee_job_level.user_id == self.user.id\n        assert employee_job_level.job_level_id == self.test_job_level.id\n        assert employee_job_level.start_date == date.today()\n        assert employee_job_level.current_salary == 50000.0\n\n    def test_employeejoblevel_relationships(self):\n        \"\"\"Test relationships with User and JobLevel models\"\"\"\n        employee_job_level = EmployeeJobLevel(\n            user_id=self.user.id,\n            job_level_id=self.test_job_level.id,\n            start_date=date.today()\n        )\n        \n        db.session.add(employee_job_level)\n        db.session.commit()\n        \n        # Test forward relationships\n        assert employee_job_level.user is not None\n        assert employee_job_level.user.id == self.user.id\n        assert employee_job_level.job_level is not None\n        assert employee_job_level.job_level.id == self.test_job_level.id\n\n    def test_employeejoblevel_active_assignment(self):\n        \"\"\"Test active job level assignment (no end_date)\"\"\"\n        employee_job_level = EmployeeJobLevel(\n            user_id=self.user.id,\n            job_level_id=self.test_job_level.id,\n            start_date=date.today(),\n            is_active=True\n        )\n        \n        db.session.add(employee_job_level)\n        db.session.commit()\n        \n        assert employee_job_level.end_date is None\n        assert employee_job_level.is_active is True\n\n    def test_employeejoblevel_historical_assignment(self):\n        \"\"\"Test historical job level assignment (with end_date)\"\"\"\n        employee_job_level = EmployeeJobLevel(\n            user_id=self.user.id,\n            job_level_id=self.test_job_level.id,\n            start_date=date(2023, 1, 1),\n            end_date=date(2023, 12, 31),\n            is_active=False\n        )\n        \n        db.session.add(employee_job_level)\n        db.session.commit()\n        \n        assert employee_job_level.start_date == date(2023, 1, 1)\n        assert employee_job_level.end_date == date(2023, 12, 31)\n        assert employee_job_level.is_active is False\n\n    def test_employeejoblevel_salary_tracking(self):\n        \"\"\"Test salary tracking functionality\"\"\"\n        salaries = [40000.0, 45000.0, 50000.0, 55000.0]\n        \n        employee_job_levels = []\n        for i, salary in enumerate(salaries):\n            employee_job_level = EmployeeJobLevel(\n                user_id=self.user.id,\n                job_level_id=self.test_job_level.id,\n                start_date=date(2023, i+1, 1),\n                current_salary=salary\n            )\n            employee_job_levels.append(employee_job_level)\n        \n        db.session.add_all(employee_job_levels)\n        db.session.commit()\n        \n        for employee_job_level, expected_salary in zip(employee_job_levels, salaries):\n            assert employee_job_level.current_salary == expected_salary\n\n    def test_employeejoblevel_notes_functionality(self):\n        \"\"\"Test notes field functionality\"\"\"\n        notes_text = \"Promoted due to excellent performance and leadership skills\"\n        \n        employee_job_level = EmployeeJobLevel(\n            user_id=self.user.id,\n            job_level_id=self.test_job_level.id,\n            start_date=date.today(),\n            notes=notes_text\n        )\n        \n        db.session.add(employee_job_level)\n        db.session.commit()\n        \n        assert employee_job_level.notes == notes_text\n\n    def test_employeejoblevel_created_by_tracking(self):\n        \"\"\"Test created_by field functionality\"\"\"\n        # Create another user as the creator\n        creator = User(username='hr_manager', email='<EMAIL>')\n        db.session.add(creator)\n        db.session.commit()\n        \n        employee_job_level = EmployeeJobLevel(\n            user_id=self.user.id,\n            job_level_id=self.test_job_level.id,\n            start_date=date.today(),\n            created_by=creator.id\n        )\n        \n        db.session.add(employee_job_level)\n        db.session.commit()\n        \n        assert employee_job_level.created_by == creator.id\n\n    def test_employeejoblevel_timestamps(self):\n        \"\"\"Test automatic timestamp handling\"\"\"\n        employee_job_level = EmployeeJobLevel(\n            user_id=self.user.id,\n            job_level_id=self.test_job_level.id,\n            start_date=date.today()\n        )\n        \n        db.session.add(employee_job_level)\n        db.session.commit()\n        \n        assert employee_job_level.created_at is not None\n        assert isinstance(employee_job_level.created_at, datetime)\n\n    def test_employeejoblevel_query_current_assignments(self):\n        \"\"\"Test querying current job level assignments\"\"\"\n        # Current assignment\n        current_assignment = EmployeeJobLevel(\n            user_id=self.user.id,\n            job_level_id=self.test_job_level.id,\n            start_date=date.today(),\n            is_active=True\n        )\n        \n        db.session.add(current_assignment)\n        db.session.commit()\n        \n        # Query current assignments (no end_date and is_active=True)\n        current_assignments = EmployeeJobLevel.query.filter_by(\n            end_date=None, is_active=True\n        ).all()\n        \n        assert len(current_assignments) >= 1\n        assert current_assignment in current_assignments\n\n    def test_employeejoblevel_query_by_user(self):\n        \"\"\"Test querying job level history by user\"\"\"\n        employee_job_level = EmployeeJobLevel(\n            user_id=self.user.id,\n            job_level_id=self.test_job_level.id,\n            start_date=date.today()\n        )\n        \n        db.session.add(employee_job_level)\n        db.session.commit()\n        \n        # Query by user\n        user_job_levels = EmployeeJobLevel.query.filter_by(user_id=self.user.id).all()\n        assert len(user_job_levels) >= 1\n        assert employee_job_level in user_job_levels\n\n    def test_employeejoblevel_query_by_job_level(self):\n        \"\"\"Test querying employees by job level\"\"\"\n        employee_job_level = EmployeeJobLevel(\n            user_id=self.user.id,\n            job_level_id=self.test_job_level.id,\n            start_date=date.today()\n        )\n        \n        db.session.add(employee_job_level)\n        db.session.commit()\n        \n        # Query by job level\n        job_level_employees = EmployeeJobLevel.query.filter_by(\n            job_level_id=self.test_job_level.id\n        ).all()\n        assert len(job_level_employees) >= 1\n        assert employee_job_level in job_level_employees\n\n    def test_employeejoblevel_update_operations(self):\n        \"\"\"Test job level assignment update operations\"\"\"\n        employee_job_level = EmployeeJobLevel(\n            user_id=self.user.id,\n            job_level_id=self.test_job_level.id,\n            start_date=date.today(),\n            current_salary=45000.0,\n            is_active=True\n        )\n        \n        db.session.add(employee_job_level)\n        db.session.commit()\n        \n        # Update assignment\n        employee_job_level.current_salary = 50000.0\n        employee_job_level.notes = 'Salary increase'\n        \n        db.session.commit()\n        \n        updated_assignment = EmployeeJobLevel.query.get(employee_job_level.id)\n        assert updated_assignment.current_salary == 50000.0\n        assert updated_assignment.notes == 'Salary increase'\n\n    def test_employeejoblevel_end_assignment(self):\n        \"\"\"Test ending a job level assignment\"\"\"\n        employee_job_level = EmployeeJobLevel(\n            user_id=self.user.id,\n            job_level_id=self.test_job_level.id,\n            start_date=date(2023, 1, 1),\n            is_active=True\n        )\n        \n        db.session.add(employee_job_level)\n        db.session.commit()\n        \n        # End the assignment\n        employee_job_level.end_date = date.today()\n        employee_job_level.is_active = False\n        \n        db.session.commit()\n        \n        assert employee_job_level.end_date == date.today()\n        assert employee_job_level.is_active is False\n\n    def test_employeejoblevel_deletion(self):\n        \"\"\"Test job level assignment deletion\"\"\"\n        employee_job_level = EmployeeJobLevel(\n            user_id=self.user.id,\n            job_level_id=self.test_job_level.id,\n            start_date=date.today()\n        )\n        \n        db.session.add(employee_job_level)\n        db.session.commit()\n        assignment_id = employee_job_level.id\n        \n        db.session.delete(employee_job_level)\n        db.session.commit()\n        \n        deleted_assignment = EmployeeJobLevel.query.get(assignment_id)\n        assert deleted_assignment is None\n"}