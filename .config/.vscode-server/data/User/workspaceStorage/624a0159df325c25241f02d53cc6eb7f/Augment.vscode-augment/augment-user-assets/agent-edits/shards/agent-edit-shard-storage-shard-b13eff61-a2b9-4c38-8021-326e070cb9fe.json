{"id": "shard-b13eff61-a2b9-4c38-8021-326e070cb9fe", "checkpoints": {"b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/docs/testing/test-coverage-audit.md": [{"sourceToolCallRequestId": "0d4c09c7-8ae3-46f2-a690-eb2d58f5162b", "timestamp": 1750976993031, "conversationId": "b13eff61-a2b9-4c38-8021-326e070cb9fe", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/testing/test-coverage-audit.md"}}}], "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/backend/tests/unit/test_kpi_calculations.py": [{"sourceToolCallRequestId": "aab1d436-259a-4c30-b0b8-ebf5b991c00f", "timestamp": 0, "conversationId": "b13eff61-a2b9-4c38-8021-326e070cb9fe", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "c0c6b960-274c-45eb-ade1-ca64daa21428", "timestamp": 1750977038997, "conversationId": "b13eff61-a2b9-4c38-8021-326e070cb9fe", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "2858b4fd-c174-400a-be2a-ff3148442aa7", "timestamp": 1750977072008, "conversationId": "b13eff61-a2b9-4c38-8021-326e070cb9fe", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "629fb09b-6fbd-406e-9c73-adf39b95f87b", "timestamp": 1750977083494, "conversationId": "b13eff61-a2b9-4c38-8021-326e070cb9fe", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "1a7cb8e6-fe0a-4398-8975-2ad2a92a098a", "timestamp": 1750977096012, "conversationId": "b13eff61-a2b9-4c38-8021-326e070cb9fe", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "9658077b-0cf8-49cd-a329-13ccbad1baa7", "timestamp": 1750977107043, "conversationId": "b13eff61-a2b9-4c38-8021-326e070cb9fe", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "2c63e3e1-9eb2-4238-bd9a-501dec48bfe9", "timestamp": 1750977119704, "conversationId": "b13eff61-a2b9-4c38-8021-326e070cb9fe", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_kpi_calculations.py"}}}], "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/backend/tests/unit/test_task_kpi_calculations.py": [{"sourceToolCallRequestId": "a978f2d2-f368-456e-8134-131e54ea57ef", "timestamp": 0, "conversationId": "b13eff61-a2b9-4c38-8021-326e070cb9fe", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_task_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "f77466aa-9044-4ac6-b72f-08e758bdf63a", "timestamp": 1750977053023, "conversationId": "b13eff61-a2b9-4c38-8021-326e070cb9fe", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_task_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "518b42af-5715-4fc1-b212-2c2e6f43629e", "timestamp": 1750977142529, "conversationId": "b13eff61-a2b9-4c38-8021-326e070cb9fe", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_task_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "72e69561-3485-4215-a598-0fe23c2a1104", "timestamp": 1750977164253, "conversationId": "b13eff61-a2b9-4c38-8021-326e070cb9fe", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_task_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "f802e98f-4484-47ad-a851-533d54f880e9", "timestamp": 1750977186842, "conversationId": "b13eff61-a2b9-4c38-8021-326e070cb9fe", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_task_kpi_calculations.py"}}}], "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/backend/services/pre_invoicing_service.py": [{"sourceToolCallRequestId": "cdcad838-51e7-49f0-ba35-df8487137441", "timestamp": 0, "conversationId": "b13eff61-a2b9-4c38-8021-326e070cb9fe", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/services/pre_invoicing_service.py"}}}, {"sourceToolCallRequestId": "1ff89f3b-f9ef-45a7-b4ba-b0529d899e11", "timestamp": 1750977302614, "conversationId": "b13eff61-a2b9-4c38-8021-326e070cb9fe", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/services/pre_invoicing_service.py"}}}, {"sourceToolCallRequestId": "bee454d9-fde7-44d3-975a-709332961d09", "timestamp": 1750977344367, "conversationId": "b13eff61-a2b9-4c38-8021-326e070cb9fe", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/services/pre_invoicing_service.py"}}}], "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/docs/testing/test-gap-analysis.md": [{"sourceToolCallRequestId": "6afcef46-0bdb-485d-a120-a9ff90b46697", "timestamp": 1750977384479, "conversationId": "b13eff61-a2b9-4c38-8021-326e070cb9fe", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/testing/test-gap-analysis.md"}}}, {"sourceToolCallRequestId": "baa36f6c-8089-4a22-8b5f-1aff0af1ae6b", "timestamp": 1750977618108, "conversationId": "b13eff61-a2b9-4c38-8021-326e070cb9fe", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/testing/test-gap-analysis.md"}}}], "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/frontend/src/test/utils/test-helpers.js": [{"sourceToolCallRequestId": "c22c5ec4-8789-4768-a428-38c5ec3141d2", "timestamp": 1750977426442, "conversationId": "b13eff61-a2b9-4c38-8021-326e070cb9fe", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/utils/test-helpers.js"}}}], "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/frontend/src/test/components/auth.test.js": [{"sourceToolCallRequestId": "ba369577-bf56-4623-a813-ef3f0ad961dd", "timestamp": 1750977451868, "conversationId": "b13eff61-a2b9-4c38-8021-326e070cb9fe", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}], "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/frontend/src/test/components/dashboard.test.js": [{"sourceToolCallRequestId": "5769f9cb-05ac-49d8-bdb3-a44db7f3b459", "timestamp": 1750977482916, "conversationId": "b13eff61-a2b9-4c38-8021-326e070cb9fe", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/dashboard.test.js"}}}], "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/frontend/src/test/components/projects.test.js": [{"sourceToolCallRequestId": "5c3fda98-a895-44fc-bc08-4ab58e9e16c6", "timestamp": 1750977518564, "conversationId": "b13eff61-a2b9-4c38-8021-326e070cb9fe", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/projects.test.js"}}}, {"sourceToolCallRequestId": "f6cbfdac-c3bd-4432-bf0f-d75db3577df5", "timestamp": 1750980303003, "conversationId": "b13eff61-a2b9-4c38-8021-326e070cb9fe", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/projects.test.js"}}}, {"sourceToolCallRequestId": "d49443d9-162b-47ec-8006-2c775c037025", "timestamp": 1750980345233, "conversationId": "b13eff61-a2b9-4c38-8021-326e070cb9fe", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/projects.test.js"}}}], "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/backend/db/validate_models_vs_database.py": [{"sourceToolCallRequestId": "7680fffc-4db4-4087-84e0-aca8f4b9796e", "timestamp": 1750977757961, "conversationId": "b13eff61-a2b9-4c38-8021-326e070cb9fe", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/db/validate_models_vs_database.py"}}}], "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/backend/db/fix_missing_tablenames.py": [{"sourceToolCallRequestId": "7869dff0-5507-4037-b84c-fb80afe22c92", "timestamp": 1750977903384, "conversationId": "b13eff61-a2b9-4c38-8021-326e070cb9fe", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/db/fix_missing_tablenames.py"}}}], "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/backend/db/fix_bad_tablenames.py": [{"sourceToolCallRequestId": "52cad24e-f273-4438-bca6-98eae7798cf7", "timestamp": 1750978071924, "conversationId": "b13eff61-a2b9-4c38-8021-326e070cb9fe", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/db/fix_bad_tablenames.py"}}}], "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/backend/models_split/projects.py": [{"sourceToolCallRequestId": "2c278b53-4317-4ef0-8dbe-38450c3ede1d", "timestamp": 0, "conversationId": "b13eff61-a2b9-4c38-8021-326e070cb9fe", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/projects.py"}}}, {"sourceToolCallRequestId": "e8f362b9-f8bd-456b-879d-aa192eeba825", "timestamp": 1750978789760, "conversationId": "b13eff61-a2b9-4c38-8021-326e070cb9fe", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/projects.py"}}}], "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/backend/tests/integration/test_project_workflows.py": [{"sourceToolCallRequestId": "7369ed4f-6196-4edf-9ff6-697fb4a75bb6", "timestamp": 1750980287522, "conversationId": "b13eff61-a2b9-4c38-8021-326e070cb9fe", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/integration/test_project_workflows.py"}}}], "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/frontend/cypress/e2e/project-workflows.cy.js": [{"sourceToolCallRequestId": "c12466e3-3996-4b36-89b4-568eaffe9f90", "timestamp": 1750980395697, "conversationId": "b13eff61-a2b9-4c38-8021-326e070cb9fe", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/cypress/e2e/project-workflows.cy.js"}}}, {"sourceToolCallRequestId": "fae0a9a4-b339-4207-8829-19559c08c252", "timestamp": 1750980892947, "conversationId": "b13eff61-a2b9-4c38-8021-326e070cb9fe", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/cypress/e2e/project-workflows.cy.js"}}}], "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/scripts/run-all-tests.sh": [{"sourceToolCallRequestId": "743556df-b3f8-48e8-a985-a06dad9359bc", "timestamp": 1750980434531, "conversationId": "b13eff61-a2b9-4c38-8021-326e070cb9fe", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "scripts/run-all-tests.sh"}}}], "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/frontend/src/test/components/ProjectTeam.test.js": [{"sourceToolCallRequestId": "843f1d1c-9cba-42d6-90bb-7cdf95e1c4ab", "timestamp": 1750980778290, "conversationId": "b13eff61-a2b9-4c38-8021-326e070cb9fe", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/ProjectTeam.test.js"}}}], "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/frontend/src/test/integration/project-api-integration.test.js": [{"sourceToolCallRequestId": "1931f69c-e159-4d16-b5d6-854e13de901e", "timestamp": 1750980819667, "conversationId": "b13eff61-a2b9-4c38-8021-326e070cb9fe", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/integration/project-api-integration.test.js"}}}], "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/frontend/src/test/ui/user-interactions.test.js": [{"sourceToolCallRequestId": "3f7633bc-aeb4-4ba1-ab2e-6e5b033f0da7", "timestamp": 1750980863883, "conversationId": "b13eff61-a2b9-4c38-8021-326e070cb9fe", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/ui/user-interactions.test.js"}}}], "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/frontend/src/test/mocks/api-handlers.js": [{"sourceToolCallRequestId": "c9398d15-3b20-4b61-a6d4-1f08c8e2a361", "timestamp": 1750980988413, "conversationId": "b13eff61-a2b9-4c38-8021-326e070cb9fe", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/mocks/api-handlers.js"}}}], "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/frontend/src/test/mocks/server.js": [{"sourceToolCallRequestId": "269c8ef0-1594-4caa-a1eb-352deae77aef", "timestamp": 1750981007293, "conversationId": "b13eff61-a2b9-4c38-8021-326e070cb9fe", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/mocks/server.js"}}}], "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/frontend/src/test/fixtures/projects.json": [{"sourceToolCallRequestId": "e4e1f6b1-8082-4a5d-9c68-95808804c47b", "timestamp": 1750981041733, "conversationId": "b13eff61-a2b9-4c38-8021-326e070cb9fe", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/fixtures/projects.json"}}}], "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/frontend/src/test/validation/data-display-validation.test.js": [{"sourceToolCallRequestId": "ae039596-6bca-454b-9fff-53e3e563af31", "timestamp": 1750981093492, "conversationId": "b13eff61-a2b9-4c38-8021-326e070cb9fe", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/validation/data-display-validation.test.js"}}}], "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/docs/testing/README.md": [{"sourceToolCallRequestId": "dbd317ec-5dff-4f77-b3e8-dd7641ff7a6a", "timestamp": 1750981200185, "conversationId": "b13eff61-a2b9-4c38-8021-326e070cb9fe", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/testing/README.md"}}}], "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/docs/testing/backend-testing-guide.md": [{"sourceToolCallRequestId": "ee5a3c70-2995-47ec-bf38-36e73766cc84", "timestamp": 1750981290944, "conversationId": "b13eff61-a2b9-4c38-8021-326e070cb9fe", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/testing/backend-testing-guide.md"}}}], "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/docs/testing/frontend-testing-guide.md": [{"sourceToolCallRequestId": "036b3b4b-fdab-4e2e-bfbd-6e42d04a5df3", "timestamp": 1750981379303, "conversationId": "b13eff61-a2b9-4c38-8021-326e070cb9fe", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/testing/frontend-testing-guide.md"}}}], "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/docs/testing/e2e-testing-guide.md": [{"sourceToolCallRequestId": "2c41bbf7-0955-4f94-8843-a85e763d4ac5", "timestamp": 1750981477660, "conversationId": "b13eff61-a2b9-4c38-8021-326e070cb9fe", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/testing/e2e-testing-guide.md"}}}, {"sourceToolCallRequestId": "ad0d8305-87ca-4226-984c-e927f29e3f91", "timestamp": 1750981513932, "conversationId": "b13eff61-a2b9-4c38-8021-326e070cb9fe", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/testing/e2e-testing-guide.md"}}}, {"sourceToolCallRequestId": "9cfedd2e-da0b-436f-8f11-43d0747fcf85", "timestamp": 1750981554057, "conversationId": "b13eff61-a2b9-4c38-8021-326e070cb9fe", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/testing/e2e-testing-guide.md"}}}], "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/docs/testing/test-execution-guide.md": [{"sourceToolCallRequestId": "21649d2d-b7a4-4503-a148-33b32ffc2e55", "timestamp": 1750981626272, "conversationId": "b13eff61-a2b9-4c38-8021-326e070cb9fe", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/testing/test-execution-guide.md"}}}]}, "metadata": {"checkpointDocumentIds": ["b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/docs/testing/test-coverage-audit.md", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/backend/tests/unit/test_kpi_calculations.py", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/backend/tests/unit/test_task_kpi_calculations.py", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/backend/services/pre_invoicing_service.py", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/docs/testing/test-gap-analysis.md", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/frontend/src/test/utils/test-helpers.js", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/frontend/src/test/components/auth.test.js", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/frontend/src/test/components/dashboard.test.js", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/frontend/src/test/components/projects.test.js", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/backend/db/validate_models_vs_database.py", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/backend/db/fix_missing_tablenames.py", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/backend/db/fix_bad_tablenames.py", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/backend/models_split/projects.py", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/backend/tests/integration/test_project_workflows.py", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/frontend/cypress/e2e/project-workflows.cy.js", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/scripts/run-all-tests.sh", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/frontend/src/test/components/ProjectTeam.test.js", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/frontend/src/test/integration/project-api-integration.test.js", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/frontend/src/test/ui/user-interactions.test.js", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/frontend/src/test/mocks/api-handlers.js", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/frontend/src/test/mocks/server.js", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/frontend/src/test/fixtures/projects.json", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/frontend/src/test/validation/data-display-validation.test.js", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/docs/testing/README.md", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/docs/testing/backend-testing-guide.md", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/docs/testing/frontend-testing-guide.md", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/docs/testing/e2e-testing-guide.md", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/docs/testing/test-execution-guide.md"], "size": 825181, "checkpointCount": 47, "lastModified": 1750981626634}}