{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/dashboard.test.js"}, "modifiedCode": "import { describe, it, expect, beforeEach, vi } from 'vitest'\nimport { mountComponent, mockApiResponse, mockUser, mockProject } from '../utils/test-helpers.js'\nimport Dashboard from '@/views/Dashboard.vue'\nimport { useDashboardStore } from '@/stores/dashboard'\nimport { useAuthStore } from '@/stores/auth'\n\n// Mock stores\nvi.mock('@/stores/dashboard', () => ({\n  useDashboardStore: vi.fn()\n}))\n\nvi.mock('@/stores/auth', () => ({\n  useAuthStore: vi.fn()\n}))\n\n// Mock API\nglobal.fetch = vi.fn()\n\ndescribe('Dashboard Components', () => {\n  let mockDashboardStore\n  let mockAuthStore\n\n  beforeEach(() => {\n    vi.clearAllMocks()\n    \n    // Setup mock dashboard store\n    mockDashboardStore = {\n      stats: {\n        totalProjects: 10,\n        activeProjects: 7,\n        completedTasks: 45,\n        pendingTasks: 12,\n        totalHours: 320,\n        thisWeekHours: 40\n      },\n      recentActivities: [\n        {\n          id: 1,\n          type: 'project_created',\n          message: 'New project \"Test Project\" created',\n          timestamp: '2025-06-26T10:00:00Z',\n          user: '<PERSON>'\n        },\n        {\n          id: 2,\n          type: 'task_completed',\n          message: 'Task \"Setup database\" completed',\n          timestamp: '2025-06-26T09:30:00Z',\n          user: 'Jane Smith'\n        }\n      ],\n      upcomingDeadlines: [\n        {\n          id: 1,\n          title: 'Project Alpha Milestone',\n          dueDate: '2025-06-30',\n          type: 'project',\n          priority: 'high'\n        }\n      ],\n      loading: false,\n      error: null,\n      fetchDashboardData: vi.fn(),\n      refreshStats: vi.fn()\n    }\n    \n    // Setup mock auth store\n    mockAuthStore = {\n      user: mockUser,\n      isAuthenticated: true,\n      hasPermission: vi.fn(() => true)\n    }\n    \n    useDashboardStore.mockReturnValue(mockDashboardStore)\n    useAuthStore.mockReturnValue(mockAuthStore)\n    \n    fetch.mockClear()\n  })\n\n  describe('Dashboard Component', () => {\n    it('should render dashboard with stats', () => {\n      const wrapper = mountComponent(Dashboard)\n      \n      // Check if stats are displayed\n      expect(wrapper.text()).toContain('10') // totalProjects\n      expect(wrapper.text()).toContain('7')  // activeProjects\n      expect(wrapper.text()).toContain('45') // completedTasks\n      expect(wrapper.text()).toContain('320') // totalHours\n    })\n\n    it('should display welcome message with user name', () => {\n      const wrapper = mountComponent(Dashboard)\n      \n      expect(wrapper.text()).toContain(`Welcome back, ${mockUser.first_name}`)\n    })\n\n    it('should show recent activities', () => {\n      const wrapper = mountComponent(Dashboard)\n      \n      expect(wrapper.text()).toContain('New project \"Test Project\" created')\n      expect(wrapper.text()).toContain('Task \"Setup database\" completed')\n    })\n\n    it('should show upcoming deadlines', () => {\n      const wrapper = mountComponent(Dashboard)\n      \n      expect(wrapper.text()).toContain('Project Alpha Milestone')\n      expect(wrapper.text()).toContain('2025-06-30')\n    })\n\n    it('should show loading state', () => {\n      mockDashboardStore.loading = true\n      \n      const wrapper = mountComponent(Dashboard)\n      \n      expect(wrapper.find('[data-testid=\"loading-spinner\"]').exists()).toBe(true)\n    })\n\n    it('should show error state', () => {\n      mockDashboardStore.error = 'Failed to load dashboard data'\n      \n      const wrapper = mountComponent(Dashboard)\n      \n      expect(wrapper.text()).toContain('Failed to load dashboard data')\n    })\n\n    it('should call fetchDashboardData on mount', () => {\n      mountComponent(Dashboard)\n      \n      expect(mockDashboardStore.fetchDashboardData).toHaveBeenCalled()\n    })\n\n    it('should refresh data when refresh button is clicked', async () => {\n      const wrapper = mountComponent(Dashboard)\n      \n      const refreshButton = wrapper.find('[data-testid=\"refresh-button\"]')\n      if (refreshButton.exists()) {\n        await refreshButton.trigger('click')\n        expect(mockDashboardStore.refreshStats).toHaveBeenCalled()\n      }\n    })\n\n    it('should hide admin sections for non-admin users', () => {\n      mockAuthStore.hasPermission.mockReturnValue(false)\n      \n      const wrapper = mountComponent(Dashboard)\n      \n      expect(wrapper.find('[data-testid=\"admin-section\"]').exists()).toBe(false)\n    })\n\n    it('should show admin sections for admin users', () => {\n      mockAuthStore.hasPermission.mockReturnValue(true)\n      \n      const wrapper = mountComponent(Dashboard)\n      \n      expect(wrapper.find('[data-testid=\"admin-section\"]').exists()).toBe(true)\n    })\n  })\n\n  describe('Dashboard Store', () => {\n    it('should fetch dashboard data successfully', async () => {\n      const mockStats = {\n        totalProjects: 15,\n        activeProjects: 10,\n        completedTasks: 50,\n        pendingTasks: 8\n      }\n      \n      fetch.mockResolvedValueOnce(mockApiResponse({\n        success: true,\n        data: mockStats\n      }))\n\n      const { useDashboardStore } = await import('@/stores/dashboard')\n      const store = useDashboardStore()\n      \n      await store.fetchDashboardData()\n\n      expect(store.stats).toEqual(mockStats)\n      expect(store.loading).toBe(false)\n      expect(store.error).toBe(null)\n    })\n\n    it('should handle fetch error', async () => {\n      fetch.mockRejectedValueOnce(new Error('Network error'))\n\n      const { useDashboardStore } = await import('@/stores/dashboard')\n      const store = useDashboardStore()\n      \n      await store.fetchDashboardData()\n\n      expect(store.loading).toBe(false)\n      expect(store.error).toBe('Failed to load dashboard data')\n    })\n\n    it('should refresh stats', async () => {\n      fetch.mockResolvedValueOnce(mockApiResponse({\n        success: true,\n        data: { totalProjects: 20 }\n      }))\n\n      const { useDashboardStore } = await import('@/stores/dashboard')\n      const store = useDashboardStore()\n      \n      await store.refreshStats()\n\n      expect(fetch).toHaveBeenCalledWith('/api/dashboard/stats')\n    })\n  })\n\n  describe('Dashboard Widgets', () => {\n    it('should render stats cards with correct values', () => {\n      const wrapper = mountComponent(Dashboard)\n      \n      const statsCards = wrapper.findAll('[data-testid=\"stats-card\"]')\n      expect(statsCards.length).toBeGreaterThan(0)\n      \n      // Check if each stat is displayed\n      expect(wrapper.text()).toContain('Total Projects')\n      expect(wrapper.text()).toContain('Active Projects')\n      expect(wrapper.text()).toContain('Completed Tasks')\n    })\n\n    it('should format numbers correctly', () => {\n      mockDashboardStore.stats.totalHours = 1234.5\n      \n      const wrapper = mountComponent(Dashboard)\n      \n      // Should format large numbers with commas\n      expect(wrapper.text()).toContain('1,234.5')\n    })\n\n    it('should show percentage changes when available', () => {\n      mockDashboardStore.stats.projectsChange = 15.5\n      \n      const wrapper = mountComponent(Dashboard)\n      \n      expect(wrapper.text()).toContain('+15.5%')\n    })\n  })\n})\n"}