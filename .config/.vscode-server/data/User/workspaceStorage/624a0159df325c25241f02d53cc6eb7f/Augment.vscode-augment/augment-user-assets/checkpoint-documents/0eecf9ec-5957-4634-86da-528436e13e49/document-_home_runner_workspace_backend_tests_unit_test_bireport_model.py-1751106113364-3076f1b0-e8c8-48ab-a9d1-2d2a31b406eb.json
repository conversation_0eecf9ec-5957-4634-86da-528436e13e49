{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_bireport_model.py"}, "modifiedCode": "\"\"\"Unit tests for BIReport model.\"\"\"\nimport pytest\nfrom models import <PERSON><PERSON>eport, User\nfrom extensions import db\n\nclass TestBIReportModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n\n    def test_bireport_creation_basic(self):\n        report = BIReport(\n            title='Test BI Report',\n            description='This is a test BI report',\n            report_type='dashboard',\n            created_by=self.user.id\n        )\n        db.session.add(report)\n        db.session.commit()\n        \n        assert report.id is not None\n        assert report.title == 'Test BI Report'\n        assert report.report_type == 'dashboard'\n\n    def test_bireport_deletion(self):\n        report = BIReport(title='To Delete', description='Delete me', created_by=self.user.id)\n        db.session.add(report)\n        db.session.commit()\n        report_id = report.id\n        \n        db.session.delete(report)\n        db.session.commit()\n        \n        deleted = BIReport.query.get(report_id)\n        assert deleted is None\n"}