{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_contact_model.py"}, "originalCode": "\"\"\"\nUnit tests for Contact model.\nTests business logic, validations, and model functionality.\n\"\"\"\n\nimport pytest\nfrom datetime import datetime, date\nfrom models import Contact, Client, User\nfrom extensions import db\n\n\nclass TestContactModel:\n    \"\"\"Test suite for Contact model unit tests\"\"\"\n\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        \"\"\"Setup test data for each test method\"\"\"\n        self.app = app\n        self.user = test_user\n        \n        # Create test client\n        self.test_client = Client(\n            name='Test Client Company',\n            status='active',\n            industry='Technology'\n        )\n        db.session.add(self.test_client)\n        db.session.commit()\n\n    def test_contact_creation_basic(self):\n        \"\"\"Test basic contact creation with required fields\"\"\"\n        contact = Contact(\n            client_id=self.test_client.id,\n            first_name='<PERSON>',\n            last_name='<PERSON><PERSON>',\n            email='<EMAIL>'\n        )\n        \n        db.session.add(contact)\n        db.session.commit()\n        \n        assert contact.id is not None\n        assert contact.first_name == '<PERSON>'\n        assert contact.last_name == '<PERSON><PERSON>'\n        assert contact.email == '<EMAIL>'\n        assert contact.client_id == self.test_client.id\n\n    def test_contact_creation_complete(self):\n        \"\"\"Test contact creation with all fields\"\"\"\n        contact = Contact(\n            client_id=self.test_client.id,\n            first_name='<PERSON>',\n            last_name='<PERSON>',\n            position='CTO',\n            email='<EMAIL>',\n            phone='+39 ************',\n            notes='Primary technical contact for the project'\n        )\n        \n        db.session.add(contact)\n        db.session.commit()\n        \n        assert contact.position == 'CTO'\n        assert contact.phone == '+39 ************'\n        assert contact.notes == 'Primary technical contact for the project'\n        assert contact.created_at is not None\n        assert contact.updated_at is not None\n\n    def test_contact_full_name_property(self):\n        \"\"\"Test full_name property calculation\"\"\"\n        contact = Contact(\n            client_id=self.test_client.id,\n            first_name='Maria',\n            last_name='Rossi'\n        )\n        \n        assert contact.full_name == 'Maria Rossi'\n\n    def test_contact_repr_method(self):\n        \"\"\"Test string representation of contact\"\"\"\n        contact = Contact(\n            client_id=self.test_client.id,\n            first_name='Marco',\n            last_name='Bianchi'\n        )\n        \n        expected_repr = '<Contact Marco Bianchi>'\n        assert repr(contact) == expected_repr\n\n    def test_contact_client_relationship(self):\n        \"\"\"Test relationship with Client model\"\"\"\n        contact = Contact(\n            client_id=self.test_client.id,\n            first_name='Alice',\n            last_name='Johnson'\n        )\n        \n        db.session.add(contact)\n        db.session.commit()\n        \n        # Test forward relationship\n        assert contact.client is not None\n        assert contact.client.id == self.test_client.id\n        assert contact.client.name == 'Test Client Company'\n        \n        # Test backward relationship\n        assert contact in self.test_client.contacts\n\n    def test_contact_required_fields_validation(self):\n        \"\"\"Test that required fields are enforced\"\"\"\n        # Test missing client_id\n        contact = Contact(\n            first_name='Test',\n            last_name='User'\n        )\n        db.session.add(contact)\n\n        with pytest.raises(Exception):  # Should raise IntegrityError\n            db.session.commit()\n\n        # Clean up the failed transaction\n        db.session.rollback()\n\n    def test_contact_foreign_key_constraint(self):\n        \"\"\"Test foreign key constraint with non-existent client\"\"\"\n        with pytest.raises(Exception):  # Should raise IntegrityError\n            contact = Contact(\n                client_id=99999,  # Non-existent client\n                first_name='Test',\n                last_name='User'\n            )\n            db.session.add(contact)\n            db.session.commit()\n\n    def test_contact_timestamps(self):\n        \"\"\"Test automatic timestamp handling\"\"\"\n        contact = Contact(\n            client_id=self.test_client.id,\n            first_name='Time',\n            last_name='Test'\n        )\n        \n        db.session.add(contact)\n        db.session.commit()\n        \n        # Test created_at is set\n        assert contact.created_at is not None\n        assert isinstance(contact.created_at, datetime)\n        \n        # Test updated_at is set\n        assert contact.updated_at is not None\n        assert isinstance(contact.updated_at, datetime)\n        \n        # Test updated_at changes on update\n        original_updated_at = contact.updated_at\n        contact.notes = 'Updated notes'\n        db.session.commit()\n        \n        assert contact.updated_at > original_updated_at\n\n    def test_contact_email_field(self):\n        \"\"\"Test email field handling\"\"\"\n        contact = Contact(\n            client_id=self.test_client.id,\n            first_name='Email',\n            last_name='Test',\n            email='<EMAIL>'\n        )\n        \n        db.session.add(contact)\n        db.session.commit()\n        \n        assert contact.email == '<EMAIL>'\n\n    def test_contact_phone_field(self):\n        \"\"\"Test phone field handling\"\"\"\n        contact = Contact(\n            client_id=self.test_client.id,\n            first_name='Phone',\n            last_name='Test',\n            phone='+39 ************'\n        )\n        \n        db.session.add(contact)\n        db.session.commit()\n        \n        assert contact.phone == '+39 ************'\n\n    def test_contact_position_field(self):\n        \"\"\"Test position field handling\"\"\"\n        contact = Contact(\n            client_id=self.test_client.id,\n            first_name='Position',\n            last_name='Test',\n            position='Senior Developer'\n        )\n        \n        db.session.add(contact)\n        db.session.commit()\n        \n        assert contact.position == 'Senior Developer'\n\n    def test_contact_notes_field(self):\n        \"\"\"Test notes field for long text\"\"\"\n        long_notes = \"This is a very long note \" * 50  # Long text\n        \n        contact = Contact(\n            client_id=self.test_client.id,\n            first_name='Notes',\n            last_name='Test',\n            notes=long_notes\n        )\n        \n        db.session.add(contact)\n        db.session.commit()\n        \n        assert contact.notes == long_notes\n\n    def test_contact_query_by_client(self):\n        \"\"\"Test querying contacts by client\"\"\"\n        # Create multiple contacts for the same client\n        contact1 = Contact(\n            client_id=self.test_client.id,\n            first_name='Contact',\n            last_name='One'\n        )\n        contact2 = Contact(\n            client_id=self.test_client.id,\n            first_name='Contact',\n            last_name='Two'\n        )\n        \n        db.session.add_all([contact1, contact2])\n        db.session.commit()\n        \n        # Query contacts by client\n        client_contacts = Contact.query.filter_by(client_id=self.test_client.id).all()\n        \n        assert len(client_contacts) == 2\n        assert contact1 in client_contacts\n        assert contact2 in client_contacts\n\n    def test_contact_query_by_name(self):\n        \"\"\"Test querying contacts by name\"\"\"\n        contact = Contact(\n            client_id=self.test_client.id,\n            first_name='Unique',\n            last_name='Name'\n        )\n        \n        db.session.add(contact)\n        db.session.commit()\n        \n        # Query by first name\n        found_contact = Contact.query.filter_by(first_name='Unique').first()\n        assert found_contact is not None\n        assert found_contact.id == contact.id\n        \n        # Query by last name\n        found_contact = Contact.query.filter_by(last_name='Name').first()\n        assert found_contact is not None\n        assert found_contact.id == contact.id\n\n    def test_contact_update_operations(self):\n        \"\"\"Test contact update operations\"\"\"\n        contact = Contact(\n            client_id=self.test_client.id,\n            first_name='Original',\n            last_name='Name',\n            email='<EMAIL>'\n        )\n        \n        db.session.add(contact)\n        db.session.commit()\n        \n        # Update contact\n        contact.first_name = 'Updated'\n        contact.email = '<EMAIL>'\n        contact.position = 'New Position'\n        \n        db.session.commit()\n        \n        # Verify updates\n        updated_contact = Contact.query.get(contact.id)\n        assert updated_contact.first_name == 'Updated'\n        assert updated_contact.email == '<EMAIL>'\n        assert updated_contact.position == 'New Position'\n        assert updated_contact.last_name == 'Name'  # Unchanged\n\n    def test_contact_deletion(self):\n        \"\"\"Test contact deletion\"\"\"\n        contact = Contact(\n            client_id=self.test_client.id,\n            first_name='To',\n            last_name='Delete'\n        )\n        \n        db.session.add(contact)\n        db.session.commit()\n        contact_id = contact.id\n        \n        # Delete contact\n        db.session.delete(contact)\n        db.session.commit()\n        \n        # Verify deletion\n        deleted_contact = Contact.query.get(contact_id)\n        assert deleted_contact is None\n", "modifiedCode": "\"\"\"\nUnit tests for Contact model.\nTests business logic, validations, and model functionality.\n\"\"\"\n\nimport pytest\nfrom datetime import datetime, date\nfrom models import Contact, Client, User\nfrom extensions import db\n\n\nclass TestContactModel:\n    \"\"\"Test suite for Contact model unit tests\"\"\"\n\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        \"\"\"Setup test data for each test method\"\"\"\n        self.app = app\n        self.user = test_user\n        \n        # Create test client\n        self.test_client = Client(\n            name='Test Client Company',\n            status='active',\n            industry='Technology'\n        )\n        db.session.add(self.test_client)\n        db.session.commit()\n\n    def test_contact_creation_basic(self):\n        \"\"\"Test basic contact creation with required fields\"\"\"\n        contact = Contact(\n            client_id=self.test_client.id,\n            first_name='<PERSON>',\n            last_name='<PERSON><PERSON>',\n            email='<EMAIL>'\n        )\n        \n        db.session.add(contact)\n        db.session.commit()\n        \n        assert contact.id is not None\n        assert contact.first_name == '<PERSON>'\n        assert contact.last_name == '<PERSON><PERSON>'\n        assert contact.email == '<EMAIL>'\n        assert contact.client_id == self.test_client.id\n\n    def test_contact_creation_complete(self):\n        \"\"\"Test contact creation with all fields\"\"\"\n        contact = Contact(\n            client_id=self.test_client.id,\n            first_name='<PERSON>',\n            last_name='<PERSON>',\n            position='CTO',\n            email='<EMAIL>',\n            phone='+39 ************',\n            notes='Primary technical contact for the project'\n        )\n        \n        db.session.add(contact)\n        db.session.commit()\n        \n        assert contact.position == 'CTO'\n        assert contact.phone == '+39 ************'\n        assert contact.notes == 'Primary technical contact for the project'\n        assert contact.created_at is not None\n        assert contact.updated_at is not None\n\n    def test_contact_full_name_property(self):\n        \"\"\"Test full_name property calculation\"\"\"\n        contact = Contact(\n            client_id=self.test_client.id,\n            first_name='Maria',\n            last_name='Rossi'\n        )\n        \n        assert contact.full_name == 'Maria Rossi'\n\n    def test_contact_repr_method(self):\n        \"\"\"Test string representation of contact\"\"\"\n        contact = Contact(\n            client_id=self.test_client.id,\n            first_name='Marco',\n            last_name='Bianchi'\n        )\n        \n        expected_repr = '<Contact Marco Bianchi>'\n        assert repr(contact) == expected_repr\n\n    def test_contact_client_relationship(self):\n        \"\"\"Test relationship with Client model\"\"\"\n        contact = Contact(\n            client_id=self.test_client.id,\n            first_name='Alice',\n            last_name='Johnson'\n        )\n        \n        db.session.add(contact)\n        db.session.commit()\n        \n        # Test forward relationship\n        assert contact.client is not None\n        assert contact.client.id == self.test_client.id\n        assert contact.client.name == 'Test Client Company'\n        \n        # Test backward relationship\n        assert contact in self.test_client.contacts\n\n    def test_contact_required_fields_validation(self):\n        \"\"\"Test that required fields are enforced\"\"\"\n        # Test missing client_id\n        contact = Contact(\n            first_name='Test',\n            last_name='User'\n        )\n        db.session.add(contact)\n\n        with pytest.raises(Exception):  # Should raise IntegrityError\n            db.session.commit()\n\n        # Clean up the failed transaction\n        db.session.rollback()\n\n    def test_contact_foreign_key_constraint(self):\n        \"\"\"Test foreign key constraint with non-existent client\"\"\"\n        with pytest.raises(Exception):  # Should raise IntegrityError\n            contact = Contact(\n                client_id=99999,  # Non-existent client\n                first_name='Test',\n                last_name='User'\n            )\n            db.session.add(contact)\n            db.session.commit()\n\n    def test_contact_timestamps(self):\n        \"\"\"Test automatic timestamp handling\"\"\"\n        contact = Contact(\n            client_id=self.test_client.id,\n            first_name='Time',\n            last_name='Test'\n        )\n        \n        db.session.add(contact)\n        db.session.commit()\n        \n        # Test created_at is set\n        assert contact.created_at is not None\n        assert isinstance(contact.created_at, datetime)\n        \n        # Test updated_at is set\n        assert contact.updated_at is not None\n        assert isinstance(contact.updated_at, datetime)\n        \n        # Test updated_at changes on update\n        original_updated_at = contact.updated_at\n        contact.notes = 'Updated notes'\n        db.session.commit()\n        \n        assert contact.updated_at > original_updated_at\n\n    def test_contact_email_field(self):\n        \"\"\"Test email field handling\"\"\"\n        contact = Contact(\n            client_id=self.test_client.id,\n            first_name='Email',\n            last_name='Test',\n            email='<EMAIL>'\n        )\n        \n        db.session.add(contact)\n        db.session.commit()\n        \n        assert contact.email == '<EMAIL>'\n\n    def test_contact_phone_field(self):\n        \"\"\"Test phone field handling\"\"\"\n        contact = Contact(\n            client_id=self.test_client.id,\n            first_name='Phone',\n            last_name='Test',\n            phone='+39 ************'\n        )\n        \n        db.session.add(contact)\n        db.session.commit()\n        \n        assert contact.phone == '+39 ************'\n\n    def test_contact_position_field(self):\n        \"\"\"Test position field handling\"\"\"\n        contact = Contact(\n            client_id=self.test_client.id,\n            first_name='Position',\n            last_name='Test',\n            position='Senior Developer'\n        )\n        \n        db.session.add(contact)\n        db.session.commit()\n        \n        assert contact.position == 'Senior Developer'\n\n    def test_contact_notes_field(self):\n        \"\"\"Test notes field for long text\"\"\"\n        long_notes = \"This is a very long note \" * 50  # Long text\n        \n        contact = Contact(\n            client_id=self.test_client.id,\n            first_name='Notes',\n            last_name='Test',\n            notes=long_notes\n        )\n        \n        db.session.add(contact)\n        db.session.commit()\n        \n        assert contact.notes == long_notes\n\n    def test_contact_query_by_client(self):\n        \"\"\"Test querying contacts by client\"\"\"\n        # Create multiple contacts for the same client\n        contact1 = Contact(\n            client_id=self.test_client.id,\n            first_name='Contact',\n            last_name='One'\n        )\n        contact2 = Contact(\n            client_id=self.test_client.id,\n            first_name='Contact',\n            last_name='Two'\n        )\n        \n        db.session.add_all([contact1, contact2])\n        db.session.commit()\n        \n        # Query contacts by client\n        client_contacts = Contact.query.filter_by(client_id=self.test_client.id).all()\n        \n        assert len(client_contacts) == 2\n        assert contact1 in client_contacts\n        assert contact2 in client_contacts\n\n    def test_contact_query_by_name(self):\n        \"\"\"Test querying contacts by name\"\"\"\n        contact = Contact(\n            client_id=self.test_client.id,\n            first_name='Unique',\n            last_name='Name'\n        )\n        \n        db.session.add(contact)\n        db.session.commit()\n        \n        # Query by first name\n        found_contact = Contact.query.filter_by(first_name='Unique').first()\n        assert found_contact is not None\n        assert found_contact.id == contact.id\n        \n        # Query by last name\n        found_contact = Contact.query.filter_by(last_name='Name').first()\n        assert found_contact is not None\n        assert found_contact.id == contact.id\n\n    def test_contact_update_operations(self):\n        \"\"\"Test contact update operations\"\"\"\n        contact = Contact(\n            client_id=self.test_client.id,\n            first_name='Original',\n            last_name='Name',\n            email='<EMAIL>'\n        )\n        \n        db.session.add(contact)\n        db.session.commit()\n        \n        # Update contact\n        contact.first_name = 'Updated'\n        contact.email = '<EMAIL>'\n        contact.position = 'New Position'\n        \n        db.session.commit()\n        \n        # Verify updates\n        updated_contact = Contact.query.get(contact.id)\n        assert updated_contact.first_name == 'Updated'\n        assert updated_contact.email == '<EMAIL>'\n        assert updated_contact.position == 'New Position'\n        assert updated_contact.last_name == 'Name'  # Unchanged\n\n    def test_contact_deletion(self):\n        \"\"\"Test contact deletion\"\"\"\n        contact = Contact(\n            client_id=self.test_client.id,\n            first_name='To',\n            last_name='Delete'\n        )\n        \n        db.session.add(contact)\n        db.session.commit()\n        contact_id = contact.id\n        \n        # Delete contact\n        db.session.delete(contact)\n        db.session.commit()\n        \n        # Verify deletion\n        deleted_contact = Contact.query.get(contact_id)\n        assert deleted_contact is None\n"}