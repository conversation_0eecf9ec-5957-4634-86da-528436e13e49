{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_kpi_calculations.py"}, "originalCode": "\"\"\"\nTest per i calcoli KPI del progetto.\n\"\"\"\nimport pytest\nfrom datetime import datetime, date, timedelta\nfrom decimal import Decimal\n\nfrom models import Project, User, Task, TimesheetEntry, PersonnelRate, ProjectExpense, ProjectKPITemplate, ProjectKPITarget\nfrom utils.cost_calculator import (\n    calculate_project_profitability,\n    calculate_project_kpis,\n    get_kpi_status,\n    get_user_daily_rate_for_date\n)\nfrom app import db\n\n\nclass TestKPICalculations:\n    \"\"\"Test per i calcoli KPI.\"\"\"\n\n    def test_calculate_project_profitability_basic(self, app, test_project, test_user):\n        \"\"\"Test calcolo profittabilità base.\"\"\"\n        with app.app_context():\n            project = Project.query.get(test_project)\n            user = test_user\n\n            # Crea rate per l'utente\n            rate = PersonnelRate(\n                user_id=user.id,\n                hourly_rate=37.5,  # 300/8 ore = 37.5 €/ora\n                start_date=date.today() - timedelta(days=30),\n                end_date=date.today() + timedelta(days=30)\n            )\n            db.session.add(rate)\n\n            # Crea timesheet\n            timesheet = TimesheetEntry(\n                user_id=user.id,\n                project_id=project.id,\n                date=date.today(),\n                hours=8.0,\n                description=\"Test work\"\n            )\n            db.session.add(timesheet)\n\n            # Imposta progetto come fatturabile\n            project.is_billable = True\n            project.client_daily_rate = 500.0\n\n            db.session.commit()\n\n            # Calcola profittabilità\n            result = calculate_project_profitability(project.id)\n\n            assert result is not None\n            assert 'personnel' in result\n            assert 'profitability' in result\n            assert 'revenue' in result\n\n            # Verifica calcoli\n            assert result['personnel']['total_cost'] == 300.0  # 1 giorno * 300€\n            assert result['revenue']['potential'] == 500.0  # 1 giorno * 500€\n            assert result['profitability']['gross_margin'] == 200.0  # 500 - 300\n            assert result['profitability']['gross_margin_percentage'] == 40.0  # (200/500)*100\n\n    def test_calculate_project_profitability_with_expenses(self, app, test_project, test_user):\n        \"\"\"Test calcolo profittabilità con spese.\"\"\"\n        with app.app_context():\n            project = Project.query.get(test_project)\n            user = test_user\n\n            # Crea rate\n            rate = PersonnelRate(\n                user_id=user.id,\n                hourly_rate=37.5,  # 300/8 ore = 37.5 €/ora\n                start_date=date.today() - timedelta(days=30)\n            )\n            db.session.add(rate)\n\n            # Crea timesheet\n            timesheet = TimesheetEntry(\n                user_id=user.id,\n                project_id=project.id,\n                date=date.today(),\n                hours=8.0\n            )\n            db.session.add(timesheet)\n\n            # Crea spesa\n            expense = ProjectExpense(\n                project_id=project.id,\n                user_id=user.id,\n                category='travel',\n                description='Viaggio cliente',\n                amount=100.0,\n                billing_type='billable',\n                date=date.today(),\n                status='approved'\n            )\n            db.session.add(expense)\n\n            project.is_billable = True\n            project.client_daily_rate = 500.0\n\n            db.session.commit()\n\n            result = calculate_project_profitability(project.id)\n\n            # Verifica costi\n            assert result['personnel']['total_cost'] == 300.0\n            assert result['expenses']['total_amount'] == 100.0\n            assert result['revenue']['potential'] == 500.0\n\n            # Margine netto = ricavi - (personale + spese) = 500 - 400 = 100\n            assert result['profitability']['net_margin'] == 100.0\n            assert result['profitability']['net_margin_percentage'] == 20.0\n\n    def test_calculate_project_kpis(self, app, test_project, test_user):\n        \"\"\"Test calcolo KPI completi.\"\"\"\n        with app.app_context():\n            project = Project.query.get(test_project)\n            user = test_user\n\n            # Setup dati\n            rate = PersonnelRate(\n                user_id=user.id,\n                hourly_rate=50.0,  # 400/8 ore = 50 €/ora\n                start_date=date.today() - timedelta(days=30)\n            )\n            db.session.add(rate)\n\n            # Crea più timesheet per testare utilization\n            for i in range(5):\n                timesheet = TimesheetEntry(\n                    user_id=user.id,\n                    project_id=project.id,\n                    date=date.today() - timedelta(days=i),\n                    hours=6.0  # 6 ore su 8 = 75% utilization\n                )\n                db.session.add(timesheet)\n\n            project.is_billable = True\n            project.client_daily_rate = 600.0\n\n            db.session.commit()\n\n            kpis = calculate_project_kpis(project.id)\n\n            assert kpis is not None\n            assert 'margin_percentage' in kpis\n            assert 'utilization_rate' in kpis\n            assert 'cost_per_hour' in kpis\n            assert 'cost_revenue_ratio' in kpis\n\n            # Verifica utilization rate (dovrebbe essere > 0 e < 100)\n            assert 0 < kpis['utilization_rate'] < 100\n\n            # Verifica cost per hour (deve essere un valore ragionevole)\n            assert kpis['cost_per_hour'] > 0\n            assert kpis['cost_per_hour'] < 1000  # Soglia ragionevole\n\n    def test_get_kpi_status_with_templates(self, app, test_project):\n        \"\"\"Test status KPI con template.\"\"\"\n        with app.app_context():\n            project = Project.query.get(test_project)\n            project.project_type = 'service'\n            db.session.commit()\n\n            # Crea template KPI per service\n            template = ProjectKPITemplate(\n                project_type='service',\n                kpi_name='margin_percentage',\n                target_min=15.0,\n                target_max=40.0,\n                warning_threshold=25.0,\n                is_active=True\n            )\n            db.session.add(template)\n            db.session.commit()\n\n            # Test valore buono\n            status = get_kpi_status(30.0, 'margin_percentage', 'service', project.id)\n            assert 'status' in status\n            assert 'color' in status\n            assert status['status'] in ['good', 'warning', 'critical']\n\n            # Test valore warning\n            status = get_kpi_status(20.0, 'margin_percentage', 'service', project.id)\n            assert 'status' in status\n            assert status['status'] in ['good', 'warning', 'critical']\n\n            # Test valore critico\n            status = get_kpi_status(10.0, 'margin_percentage', 'service', project.id)\n            assert 'status' in status\n            assert status['status'] in ['good', 'warning', 'critical']\n\n    def test_get_kpi_status_with_custom_targets(self, app, test_project):\n        \"\"\"Test status KPI con target personalizzati.\"\"\"\n        with app.app_context():\n            project = Project.query.get(test_project)\n            project.project_type = 'consulting'\n            db.session.commit()\n\n            # Crea template base\n            template = ProjectKPITemplate(\n                project_type='consulting',\n                kpi_name='margin_percentage',\n                target_min=25.0,\n                target_max=60.0,\n                warning_threshold=40.0,\n                is_active=True\n            )\n            db.session.add(template)\n\n            # Crea target personalizzato\n            custom_target = ProjectKPITarget(\n                project_id=project.id,\n                kpi_name='margin_percentage',\n                target_value=50.0,\n                warning_threshold=35.0,\n                created_by=1\n            )\n            db.session.add(custom_target)\n            db.session.commit()\n\n            # Test con target personalizzato\n            status = get_kpi_status(45.0, 'margin_percentage', 'consulting', project.id)\n            assert 'status' in status\n            assert status['status'] in ['good', 'warning', 'critical']\n\n            status = get_kpi_status(30.0, 'margin_percentage', 'consulting', project.id)\n            assert 'status' in status\n            assert status['status'] in ['good', 'warning', 'critical']\n\n    def test_get_user_daily_rate_for_date(self, app, test_user):\n        \"\"\"Test recupero tariffa utente per data.\"\"\"\n        with app.app_context():\n            user = test_user\n            test_date = date.today()\n\n            # Crea rate valido\n            rate = PersonnelRate(\n                user_id=user.id,\n                hourly_rate=43.75,  # 350/8 ore = 43.75 €/ora\n                start_date=test_date - timedelta(days=10),\n                end_date=test_date + timedelta(days=10)\n            )\n            db.session.add(rate)\n            db.session.commit()\n\n            # Test recupero rate\n            result = get_user_daily_rate_for_date(user.id, test_date)\n            assert result == 350.0\n\n            # Test data fuori range\n            future_date = test_date + timedelta(days=20)\n            result = get_user_daily_rate_for_date(user.id, future_date)\n            # Potrebbe restituire l'ultimo rate valido o un default\n            assert result >= 0.0  # Deve essere un valore valido\n\n    def test_non_billable_project_kpis(self, app, test_project, test_user):\n        \"\"\"Test KPI per progetto non fatturabile.\"\"\"\n        with app.app_context():\n            project = Project.query.get(test_project)\n            project.is_billable = False\n            user = test_user\n\n            rate = PersonnelRate(\n                user_id=user.id,\n                hourly_rate=37.5,  # 300/8 ore = 37.5 €/ora\n                start_date=date.today() - timedelta(days=30)\n            )\n            db.session.add(rate)\n\n            timesheet = TimesheetEntry(\n                user_id=user.id,\n                project_id=project.id,\n                date=date.today(),\n                hours=8.0\n            )\n            db.session.add(timesheet)\n            db.session.commit()\n\n            # Per progetti non fatturabili, revenue = 0\n            result = calculate_project_profitability(project.id)\n            assert result['revenue']['potential'] == 0.0\n            # Il margin percentage potrebbe essere 0 o negativo per progetti non fatturabili\n            assert result['profitability']['net_margin_percentage'] <= 0.0\n\n            kpis = calculate_project_kpis(project.id)\n            # Per progetti non fatturabili, cost_revenue_ratio potrebbe essere inf o None\n            assert kpis['cost_revenue_ratio'] is None or kpis['cost_revenue_ratio'] == float('inf')\n\n    def test_utilization_rate_calculation_methods(self, app, test_project, test_user):\n        \"\"\"Test diversi metodi di calcolo Utilization Rate.\"\"\"\n        with app.app_context():\n            project = Project.query.get(test_project)\n            user = test_user\n\n            # Setup rate personale\n            rate = PersonnelRate(\n                user_id=user.id,\n                hourly_rate=50.0,  # 400/8 ore = 50 €/ora\n                start_date=date.today() - timedelta(days=30)\n            )\n            db.session.add(rate)\n\n            # Aggiungi 5 giorni di timesheet (5 giorni * 8 ore = 40 ore)\n            for i in range(5):\n                timesheet = TimesheetEntry(\n                    user_id=user.id,\n                    project_id=project.id,\n                    date=date.today() - timedelta(days=i),\n                    hours=8.0\n                )\n                db.session.add(timesheet)\n\n            # TEST 1: Budget-based calculation\n            project.budget = 10000.0  # €10,000 budget\n            project.client_daily_rate = 500.0  # €500/giorno\n            project.is_billable = True\n            # Imposta date progetto per includere tutti i timesheet\n            project.start_date = date.today() - timedelta(days=10)  # Prima dei timesheet\n            project.end_date = date.today() + timedelta(days=10)    # Dopo i timesheet\n            db.session.commit()\n\n            kpis = calculate_project_kpis(project.id)\n\n            # Budget: 10000 / 500 = 20 giorni teorici\n            # Effettivi: 5 giorni\n            # Utilization: 5/20 * 100 = 25%\n            expected_utilization = (5 / 20) * 100\n            assert abs(kpis['utilization_rate'] - expected_utilization) < 1.0\n\n            # TEST 2: Timeline-based calculation\n            project.budget = None  # Rimuovi budget per testare timeline\n            project.start_date = date.today() - timedelta(days=20)\n            project.end_date = date.today() + timedelta(days=10)  # 30 giorni totali\n            db.session.commit()\n\n            kpis = calculate_project_kpis(project.id)\n            # Durata: 30 giorni\n            # Giorni lavorativi: 30 * 5/7 ≈ 21.4 giorni\n            # Team size: 1 persona\n            # Giorni teorici: 1 * 21.4 * 0.8 ≈ 17.1 giorni\n            # Utilization: 5/17.1 * 100 ≈ 29.2%\n            working_days = 30 * 5/7\n            expected_days = 1 * working_days * 0.8\n            expected_utilization = (5 / expected_days) * 100\n            assert abs(kpis['utilization_rate'] - expected_utilization) < 2.0\n\n            # TEST 3: Fallback calculation\n            project.start_date = None\n            project.end_date = None\n            db.session.commit()\n\n            kpis = calculate_project_kpis(project.id)\n            # Fallback: 1 persona * 22 giorni = 22 giorni teorici\n            # Utilization: 5/22 * 100 ≈ 22.7%\n            expected_utilization = (5 / 22) * 100\n            assert abs(kpis['utilization_rate'] - expected_utilization) < 1.0\n", "modifiedCode": "\"\"\"\nTest per i calcoli KPI del progetto.\n\"\"\"\nimport pytest\nfrom datetime import datetime, date, timedelta\nfrom decimal import Decimal\n\nfrom models import Project, User, Task, TimesheetEntry, PersonnelRate, ProjectExpense, ProjectKPITemplate, ProjectKPITarget\nfrom utils.cost_calculator import (\n    calculate_project_profitability,\n    calculate_project_kpis,\n    get_kpi_status,\n    get_user_daily_rate_for_date\n)\nfrom app import db\n\n\nclass TestKPICalculations:\n    \"\"\"Test per i calcoli KPI.\"\"\"\n\n    def test_calculate_project_profitability_basic(self, app, test_project, test_user):\n        \"\"\"Test calcolo profittabilità base.\"\"\"\n        with app.app_context():\n            project = Project.query.get(test_project)\n            user = test_user\n\n            # Crea rate per l'utente\n            rate = PersonnelRate(\n                user_id=user.id,\n                hourly_rate=37.5,  # 300/8 ore = 37.5 €/ora\n                start_date=date.today() - timedelta(days=30),\n                end_date=date.today() + timedelta(days=30)\n            )\n            db.session.add(rate)\n\n            # Crea timesheet\n            timesheet = TimesheetEntry(\n                user_id=user.id,\n                project_id=project.id,\n                date=date.today(),\n                hours=8.0,\n                description=\"Test work\"\n            )\n            db.session.add(timesheet)\n\n            # Imposta progetto come fatturabile\n            project.is_billable = True\n            project.client_daily_rate = 500.0\n\n            db.session.commit()\n\n            # Calcola profittabilità\n            result = calculate_project_profitability(project.id)\n\n            assert result is not None\n            assert 'personnel' in result\n            assert 'profitability' in result\n            assert 'revenue' in result\n\n            # Verifica calcoli\n            assert result['personnel']['total_cost'] == 300.0  # 1 giorno * 300€\n            assert result['revenue']['potential'] == 500.0  # 1 giorno * 500€\n            assert result['profitability']['gross_margin'] == 200.0  # 500 - 300\n            assert result['profitability']['gross_margin_percentage'] == 40.0  # (200/500)*100\n\n    def test_calculate_project_profitability_with_expenses(self, app, test_project, test_user):\n        \"\"\"Test calcolo profittabilità con spese.\"\"\"\n        with app.app_context():\n            project = Project.query.get(test_project)\n            user = test_user\n\n            # Crea rate\n            rate = PersonnelRate(\n                user_id=user.id,\n                hourly_rate=37.5,  # 300/8 ore = 37.5 €/ora\n                start_date=date.today() - timedelta(days=30)\n            )\n            db.session.add(rate)\n\n            # Crea timesheet\n            timesheet = TimesheetEntry(\n                user_id=user.id,\n                project_id=project.id,\n                date=date.today(),\n                hours=8.0\n            )\n            db.session.add(timesheet)\n\n            # Crea spesa\n            expense = ProjectExpense(\n                project_id=project.id,\n                user_id=user.id,\n                category='travel',\n                description='Viaggio cliente',\n                amount=100.0,\n                billing_type='billable',\n                date=date.today(),\n                status='approved'\n            )\n            db.session.add(expense)\n\n            project.is_billable = True\n            project.client_daily_rate = 500.0\n\n            db.session.commit()\n\n            result = calculate_project_profitability(project.id)\n\n            # Verifica costi\n            assert result['personnel']['total_cost'] == 300.0\n            assert result['expenses']['total_amount'] == 100.0\n            assert result['revenue']['potential'] == 500.0\n\n            # Margine netto = ricavi - (personale + spese) = 500 - 400 = 100\n            assert result['profitability']['net_margin'] == 100.0\n            assert result['profitability']['net_margin_percentage'] == 20.0\n\n    def test_calculate_project_kpis(self, app, test_project, test_user):\n        \"\"\"Test calcolo KPI completi.\"\"\"\n        with app.app_context():\n            project = Project.query.get(test_project)\n            user = test_user\n\n            # Setup dati\n            rate = PersonnelRate(\n                user_id=user.id,\n                hourly_rate=50.0,  # 400/8 ore = 50 €/ora\n                start_date=date.today() - timedelta(days=30)\n            )\n            db.session.add(rate)\n\n            # Crea più timesheet per testare utilization\n            for i in range(5):\n                timesheet = TimesheetEntry(\n                    user_id=user.id,\n                    project_id=project.id,\n                    date=date.today() - timedelta(days=i),\n                    hours=6.0  # 6 ore su 8 = 75% utilization\n                )\n                db.session.add(timesheet)\n\n            project.is_billable = True\n            project.client_daily_rate = 600.0\n\n            db.session.commit()\n\n            kpis = calculate_project_kpis(project.id)\n\n            assert kpis is not None\n            assert 'margin_percentage' in kpis\n            assert 'utilization_rate' in kpis\n            assert 'cost_per_hour' in kpis\n            assert 'cost_revenue_ratio' in kpis\n\n            # Verifica utilization rate (dovrebbe essere > 0 e < 100)\n            assert 0 < kpis['utilization_rate'] < 100\n\n            # Verifica cost per hour (deve essere un valore ragionevole)\n            assert kpis['cost_per_hour'] > 0\n            assert kpis['cost_per_hour'] < 1000  # Soglia ragionevole\n\n    def test_get_kpi_status_with_templates(self, app, test_project):\n        \"\"\"Test status KPI con template.\"\"\"\n        with app.app_context():\n            project = Project.query.get(test_project)\n            project.project_type = 'service'\n            db.session.commit()\n\n            # Crea template KPI per service\n            template = ProjectKPITemplate(\n                project_type='service',\n                kpi_name='margin_percentage',\n                target_min=15.0,\n                target_max=40.0,\n                warning_threshold=25.0,\n                is_active=True\n            )\n            db.session.add(template)\n            db.session.commit()\n\n            # Test valore buono\n            status = get_kpi_status(30.0, 'margin_percentage', 'service', project.id)\n            assert 'status' in status\n            assert 'color' in status\n            assert status['status'] in ['good', 'warning', 'critical']\n\n            # Test valore warning\n            status = get_kpi_status(20.0, 'margin_percentage', 'service', project.id)\n            assert 'status' in status\n            assert status['status'] in ['good', 'warning', 'critical']\n\n            # Test valore critico\n            status = get_kpi_status(10.0, 'margin_percentage', 'service', project.id)\n            assert 'status' in status\n            assert status['status'] in ['good', 'warning', 'critical']\n\n    def test_get_kpi_status_with_custom_targets(self, app, test_project):\n        \"\"\"Test status KPI con target personalizzati.\"\"\"\n        with app.app_context():\n            project = Project.query.get(test_project)\n            project.project_type = 'consulting'\n            db.session.commit()\n\n            # Crea template base\n            template = ProjectKPITemplate(\n                project_type='consulting',\n                kpi_name='margin_percentage',\n                target_min=25.0,\n                target_max=60.0,\n                warning_threshold=40.0,\n                is_active=True\n            )\n            db.session.add(template)\n\n            # Crea target personalizzato\n            custom_target = ProjectKPITarget(\n                project_id=project.id,\n                kpi_name='margin_percentage',\n                target_value=50.0,\n                warning_threshold=35.0,\n                created_by=1\n            )\n            db.session.add(custom_target)\n            db.session.commit()\n\n            # Test con target personalizzato\n            status = get_kpi_status(45.0, 'margin_percentage', 'consulting', project.id)\n            assert 'status' in status\n            assert status['status'] in ['good', 'warning', 'critical']\n\n            status = get_kpi_status(30.0, 'margin_percentage', 'consulting', project.id)\n            assert 'status' in status\n            assert status['status'] in ['good', 'warning', 'critical']\n\n    def test_get_user_daily_rate_for_date(self, app, test_user):\n        \"\"\"Test recupero tariffa utente per data.\"\"\"\n        with app.app_context():\n            user = test_user\n            test_date = date.today()\n\n            # Crea rate valido\n            rate = PersonnelRate(\n                user_id=user.id,\n                hourly_rate=43.75,  # 350/8 ore = 43.75 €/ora\n                start_date=test_date - timedelta(days=10),\n                end_date=test_date + timedelta(days=10)\n            )\n            db.session.add(rate)\n            db.session.commit()\n\n            # Test recupero rate\n            result = get_user_daily_rate_for_date(user.id, test_date)\n            assert result == 350.0\n\n            # Test data fuori range\n            future_date = test_date + timedelta(days=20)\n            result = get_user_daily_rate_for_date(user.id, future_date)\n            # Potrebbe restituire l'ultimo rate valido o un default\n            assert result >= 0.0  # Deve essere un valore valido\n\n    def test_non_billable_project_kpis(self, app, test_project, test_user):\n        \"\"\"Test KPI per progetto non fatturabile.\"\"\"\n        with app.app_context():\n            project = Project.query.get(test_project)\n            project.is_billable = False\n            user = test_user\n\n            rate = PersonnelRate(\n                user_id=user.id,\n                hourly_rate=37.5,  # 300/8 ore = 37.5 €/ora\n                start_date=date.today() - timedelta(days=30)\n            )\n            db.session.add(rate)\n\n            timesheet = TimesheetEntry(\n                user_id=user.id,\n                project_id=project.id,\n                date=date.today(),\n                hours=8.0\n            )\n            db.session.add(timesheet)\n            db.session.commit()\n\n            # Per progetti non fatturabili, revenue = 0\n            result = calculate_project_profitability(project.id)\n            assert result['revenue']['potential'] == 0.0\n            # Il margin percentage potrebbe essere 0 o negativo per progetti non fatturabili\n            assert result['profitability']['net_margin_percentage'] <= 0.0\n\n            kpis = calculate_project_kpis(project.id)\n            # Per progetti non fatturabili, cost_revenue_ratio potrebbe essere inf o None\n            assert kpis['cost_revenue_ratio'] is None or kpis['cost_revenue_ratio'] == float('inf')\n\n    def test_utilization_rate_calculation_methods(self, app, test_project, test_user):\n        \"\"\"Test diversi metodi di calcolo Utilization Rate.\"\"\"\n        with app.app_context():\n            project = Project.query.get(test_project)\n            user = test_user\n\n            # Setup rate personale\n            rate = PersonnelRate(\n                user_id=user.id,\n                hourly_rate=50.0,  # 400/8 ore = 50 €/ora\n                start_date=date.today() - timedelta(days=30)\n            )\n            db.session.add(rate)\n\n            # Aggiungi 5 giorni di timesheet (5 giorni * 8 ore = 40 ore)\n            for i in range(5):\n                timesheet = TimesheetEntry(\n                    user_id=user.id,\n                    project_id=project.id,\n                    date=date.today() - timedelta(days=i),\n                    hours=8.0\n                )\n                db.session.add(timesheet)\n\n            # TEST 1: Budget-based calculation\n            project.budget = 10000.0  # €10,000 budget\n            project.client_daily_rate = 500.0  # €500/giorno\n            project.is_billable = True\n            # Imposta date progetto per includere tutti i timesheet\n            project.start_date = date.today() - timedelta(days=10)  # Prima dei timesheet\n            project.end_date = date.today() + timedelta(days=10)    # Dopo i timesheet\n            db.session.commit()\n\n            kpis = calculate_project_kpis(project.id)\n\n            # Budget: 10000 / 500 = 20 giorni teorici\n            # Effettivi: 5 giorni\n            # Utilization: 5/20 * 100 = 25%\n            expected_utilization = (5 / 20) * 100\n            assert abs(kpis['utilization_rate'] - expected_utilization) < 1.0\n\n            # TEST 2: Timeline-based calculation\n            project.budget = None  # Rimuovi budget per testare timeline\n            project.start_date = date.today() - timedelta(days=20)\n            project.end_date = date.today() + timedelta(days=10)  # 30 giorni totali\n            db.session.commit()\n\n            kpis = calculate_project_kpis(project.id)\n            # Durata: 30 giorni\n            # Giorni lavorativi: 30 * 5/7 ≈ 21.4 giorni\n            # Team size: 1 persona\n            # Giorni teorici: 1 * 21.4 * 0.8 ≈ 17.1 giorni\n            # Utilization: 5/17.1 * 100 ≈ 29.2%\n            working_days = 30 * 5/7\n            expected_days = 1 * working_days * 0.8\n            expected_utilization = (5 / expected_days) * 100\n            assert abs(kpis['utilization_rate'] - expected_utilization) < 2.0\n\n            # TEST 3: Fallback calculation\n            project.start_date = None\n            project.end_date = None\n            db.session.commit()\n\n            kpis = calculate_project_kpis(project.id)\n            # Fallback: 1 persona * 22 giorni = 22 giorni teorici\n            # Utilization: 5/22 * 100 ≈ 22.7%\n            expected_utilization = (5 / 22) * 100\n            assert abs(kpis['utilization_rate'] - expected_utilization) < 1.0\n"}