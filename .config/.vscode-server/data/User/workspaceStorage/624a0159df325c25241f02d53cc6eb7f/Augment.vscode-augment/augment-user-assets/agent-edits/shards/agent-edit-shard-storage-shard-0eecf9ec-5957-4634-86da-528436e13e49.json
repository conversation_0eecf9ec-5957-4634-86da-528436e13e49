{"id": "shard-0eecf9ec-5957-4634-86da-528436e13e49", "checkpoints": {"0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/frontend/src/views/auth/Login.vue": [{"sourceToolCallRequestId": "ea9a65ef-a15c-4aca-8d8c-29e8589f5cea", "timestamp": 0, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/auth/Login.vue"}}}, {"sourceToolCallRequestId": "6ce0ee3c-226a-4922-aef8-18226ecc54ee", "timestamp": 1751053871036, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/auth/Login.vue"}}}, {"sourceToolCallRequestId": "3767bce1-1015-434d-982d-0b3b2063134a", "timestamp": 1751053871493, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/auth/Login.vue"}}}, {"sourceToolCallRequestId": "6c7bbe45-62d8-4fb9-8bae-e6807c71611b", "timestamp": 1751053871493, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/auth/Login.vue"}}}, {"sourceToolCallRequestId": "2f7d51eb-05c0-4f44-91a1-6d7acc6c28a1", "timestamp": 1751053884296, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/auth/Login.vue"}}}, {"sourceToolCallRequestId": "70eb283f-e6f7-4351-a00e-cc0d8eaf5e93", "timestamp": 1751053884448, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/auth/Login.vue"}}}, {"sourceToolCallRequestId": "080fd654-c2c3-43cd-b263-b92da3924c6e", "timestamp": 1751053884448, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/auth/Login.vue"}}}, {"sourceToolCallRequestId": "fb2a0dc4-46f3-4c20-97d6-01d23ffafa7e", "timestamp": 1751053899012, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/auth/Login.vue"}}}, {"sourceToolCallRequestId": "2b8e6c55-14e1-471c-87c1-0089fc9b9d68", "timestamp": 1751053899152, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/auth/Login.vue"}}}, {"sourceToolCallRequestId": "7178db4b-f66c-4d01-a5a7-74db1c2c85da", "timestamp": 1751053899152, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "lastIncludedInRequestId": "6ca1a9d9-b51d-4ce5-8223-8a4130eedce4", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/auth/Login.vue"}}}], "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/frontend/src/views/projects/Projects.vue": [{"sourceToolCallRequestId": "83034c1e-e2ae-404b-851b-0fc969c6dec0", "timestamp": 0, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/Projects.vue"}}}, {"sourceToolCallRequestId": "ed30945c-4a4d-45e1-892d-496b351d19e5", "timestamp": 1751053925515, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/Projects.vue"}}}, {"sourceToolCallRequestId": "c0aaabf0-06ae-4a5b-a3bf-a613c1d40548", "timestamp": 1751053926004, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/Projects.vue"}}}, {"sourceToolCallRequestId": "33ccfb03-436f-4fd4-9001-c926a44ca14e", "timestamp": 1751053926004, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/Projects.vue"}}}, {"sourceToolCallRequestId": "48508010-c52f-439b-b827-55557e8bb8a2", "timestamp": 1751053938908, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/Projects.vue"}}}, {"sourceToolCallRequestId": "ff22477c-c556-405d-b07d-5be029f3ea0e", "timestamp": 1751053939059, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/Projects.vue"}}}, {"sourceToolCallRequestId": "b0313634-1615-4930-9e78-96e590548372", "timestamp": 1751053939059, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/Projects.vue"}}}, {"sourceToolCallRequestId": "bb289992-ba78-4aee-97b1-cc954ecc2e68", "timestamp": 1751053952501, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/Projects.vue"}}}, {"sourceToolCallRequestId": "eade0e24-0532-4bc2-af69-cfe9cc350230", "timestamp": 1751053952653, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/Projects.vue"}}}, {"sourceToolCallRequestId": "4a5aad95-55b2-4c87-bdec-1618d75001a3", "timestamp": 1751053952653, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/Projects.vue"}}}, {"sourceToolCallRequestId": "2891302e-83d6-4453-a0c5-0f81eae9887c", "timestamp": 1751053964947, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/Projects.vue"}}}, {"sourceToolCallRequestId": "43deb10e-c670-4475-8725-cfc1d5829b9b", "timestamp": 1751053965096, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/Projects.vue"}}}, {"sourceToolCallRequestId": "ffdefe84-19d6-4014-b505-c9d1cd929f6a", "timestamp": 1751053965096, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "lastIncludedInRequestId": "6ca1a9d9-b51d-4ce5-8223-8a4130eedce4", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/Projects.vue"}}}], "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/frontend/src/views/projects/ProjectEdit.vue": [{"sourceToolCallRequestId": "4c781e3d-86bd-41f9-8095-23169531383d", "timestamp": 0, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/ProjectEdit.vue"}}}, {"sourceToolCallRequestId": "df4d31c3-fda1-4196-a6d6-6e5f4fb2e04c", "timestamp": 1751053992843, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/ProjectEdit.vue"}}}, {"sourceToolCallRequestId": "7897bccb-7976-48a6-8fe8-895a6c971a4b", "timestamp": 1751053993318, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/ProjectEdit.vue"}}}, {"sourceToolCallRequestId": "9d25129a-3af4-4e5d-a948-f0c2c7ccb941", "timestamp": 1751053993318, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/ProjectEdit.vue"}}}, {"sourceToolCallRequestId": "90b3eb84-a6e0-4786-8683-80ca4af603b5", "timestamp": 1751054005414, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/ProjectEdit.vue"}}}, {"sourceToolCallRequestId": "ee96deda-f913-42a4-977e-b8a13d4e1c82", "timestamp": 1751054005559, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/ProjectEdit.vue"}}}, {"sourceToolCallRequestId": "04f18a64-bfd3-4211-8cc4-bc6aeb654f39", "timestamp": 1751054005559, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/ProjectEdit.vue"}}}, {"sourceToolCallRequestId": "3964558b-a1b4-48b8-a4e0-ae7cd2611863", "timestamp": 1751054018454, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/ProjectEdit.vue"}}}, {"sourceToolCallRequestId": "f126b5fc-839e-42a5-9ce9-6791f1e5e997", "timestamp": 1751054018594, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/ProjectEdit.vue"}}}, {"sourceToolCallRequestId": "4caf663c-0757-4e7d-ae53-546382585644", "timestamp": 1751054018594, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/ProjectEdit.vue"}}}, {"sourceToolCallRequestId": "d49a2b78-12fd-40b0-a21b-4858312bbf21", "timestamp": 1751054032023, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/ProjectEdit.vue"}}}, {"sourceToolCallRequestId": "4860a58f-767f-4aa7-a4a2-11050fc3dc21", "timestamp": 1751054032166, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/ProjectEdit.vue"}}}, {"sourceToolCallRequestId": "039579b9-518b-4dd0-927d-6dd48c19fafd", "timestamp": 1751054032166, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/ProjectEdit.vue"}}}, {"sourceToolCallRequestId": "a0dc7139-4b18-4516-93b2-464d77c4a4ac", "timestamp": 1751054045205, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/ProjectEdit.vue"}}}, {"sourceToolCallRequestId": "0cf8f51e-531d-4efd-93a9-fb4959e2be0e", "timestamp": 1751054045833, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/ProjectEdit.vue"}}}, {"sourceToolCallRequestId": "52d3a5c2-e782-4101-95c6-451bb7d68f08", "timestamp": 1751054045833, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "lastIncludedInRequestId": "6ca1a9d9-b51d-4ce5-8223-8a4130eedce4", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/ProjectEdit.vue"}}}], "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/frontend/src/views/projects/components/ProjectTeam.vue": [{"sourceToolCallRequestId": "3a14085c-debf-4c0d-984d-4067187926bf", "timestamp": 0, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/components/ProjectTeam.vue"}}}, {"sourceToolCallRequestId": "52bad249-dace-483f-b5b7-56db4fb1d409", "timestamp": 1751054071730, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/components/ProjectTeam.vue"}}}, {"sourceToolCallRequestId": "e0745973-940c-4d40-8120-7ed86a0cd59e", "timestamp": 1751054072141, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/components/ProjectTeam.vue"}}}, {"sourceToolCallRequestId": "a8787113-99e6-4af7-b20d-af680ca1102a", "timestamp": 1751054072141, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/components/ProjectTeam.vue"}}}, {"sourceToolCallRequestId": "065f8cf0-7083-4509-91fe-3747aaa2bead", "timestamp": 1751054085475, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/components/ProjectTeam.vue"}}}, {"sourceToolCallRequestId": "8937b003-1545-4237-8b05-28cfe05e1831", "timestamp": 1751054085622, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/components/ProjectTeam.vue"}}}, {"sourceToolCallRequestId": "1c0b17a2-4517-4781-8aa1-de27ce8917a0", "timestamp": 1751054085622, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "lastIncludedInRequestId": "6ca1a9d9-b51d-4ce5-8223-8a4130eedce4", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/components/ProjectTeam.vue"}}}], "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/frontend/src/test/components/auth.test.js": [{"sourceToolCallRequestId": "7997e51d-088b-4a29-84eb-06ccfeebe532", "timestamp": 0, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "d0fb8213-9dbf-4c3f-813b-65f14cfea978", "timestamp": 1751054138733, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "661562b3-27df-4c33-bf23-2945db12421b", "timestamp": 1751054139117, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "0c796400-4d7f-4de3-b37b-db0fd76d94b1", "timestamp": 1751054139117, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "43e8d461-69bf-4a35-932f-8f58ead39d60", "timestamp": 1751054152484, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "08fa6f9b-90b3-45f8-9c73-2c6d2448de67", "timestamp": 1751054152614, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "5fb8d996-e23d-49ba-bff7-496b5e507413", "timestamp": 1751054152614, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "8c642cf4-c9f4-4b21-975b-04f0fd6f9e52", "timestamp": 1751054166745, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "e41d6f45-5cf8-4e4c-97de-28d6e6497da4", "timestamp": 1751054166884, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "c7dc8134-efa3-47e4-bfa9-1d19634ecf1f", "timestamp": 1751054166884, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "623f8018-96fa-4dcb-9219-b57fb40855b0", "timestamp": 1751054179108, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "10482eab-5bf0-40aa-97cc-e699d5187788", "timestamp": 1751054179241, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "2892cf7f-c680-43d7-a82d-d4e5d05eca9d", "timestamp": 1751054179241, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "5f9c0f8b-0e91-439b-9464-cc3c9256d8df", "timestamp": 1751054193236, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "0742c620-27f2-4b46-91c1-82d8358f0192", "timestamp": 1751054193371, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "0e5d6105-0fcc-4417-8616-0bad0b9bf51c", "timestamp": 1751054193371, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "7b2c5a91-7d6f-458e-a3ba-b9ee8cc42c4b", "timestamp": 1751054209077, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "e7d8eb32-d81b-4e11-af43-b1f8cb8ea086", "timestamp": 1751054209233, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "2f443c2d-0763-4410-9dca-e12592c11eab", "timestamp": 1751054209233, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "d8be3b84-b7a1-4679-9afb-ac7e90eed6aa", "timestamp": 1751054222710, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "9b968a72-6a83-4035-89da-50a249950b2e", "timestamp": 1751054222853, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "21a30057-1865-449e-94bd-42f7da238869", "timestamp": 1751054222853, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "fcbe19ab-34ac-4c7a-bdf5-03eb3f36a570", "timestamp": 1751054235955, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "cfe9d3c3-0177-440c-a3a7-8ab9eb844c35", "timestamp": 1751054236089, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "2bcd240f-8a92-4da0-baf8-f5536011229d", "timestamp": 1751054236089, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "2a56876d-5e79-48f5-9c9d-960ad872dff1", "timestamp": 1751054249093, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "48d2c69b-9633-410d-8285-ba6b59e82b57", "timestamp": 1751054249229, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "496ea28d-a419-4028-81ba-9a102d67c38f", "timestamp": 1751054249229, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "34d2465d-0c05-4108-97c6-680131963ea9", "timestamp": 1751054279597, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "e81b9b31-3a14-4849-a6d2-a7fc405a2ce8", "timestamp": 1751054279745, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "b1e7a523-ca4f-4112-aa0b-e8b61193065b", "timestamp": 1751054279745, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "70fcf46a-4158-45d0-981a-7bc7da14b5bf", "timestamp": 1751054306831, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "3f2cbca8-633c-4b5d-89e5-fafe9c1e8bc0", "timestamp": 1751054306983, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "5be7c542-9309-4e1e-b35a-0a8a02ea5df5", "timestamp": 1751054306983, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "lastIncludedInRequestId": "6ca1a9d9-b51d-4ce5-8223-8a4130eedce4", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}], "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/frontend/src/test/components/projects.test.js": [{"sourceToolCallRequestId": "8a687cde-3713-418b-919b-ec244ad820e6", "timestamp": 0, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/projects.test.js"}}}, {"sourceToolCallRequestId": "a92f693d-41b8-4d31-82d8-ee19d01e5e50", "timestamp": 1751054388018, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/projects.test.js"}}}, {"sourceToolCallRequestId": "9e955ae7-25aa-479e-bec4-0413b082be6f", "timestamp": 1751054388418, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/projects.test.js"}}}, {"sourceToolCallRequestId": "ef14c465-3d11-4eb2-b3c4-67eb6cfcf11b", "timestamp": 1751054388418, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "lastIncludedInRequestId": "6ca1a9d9-b51d-4ce5-8223-8a4130eedce4", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/projects.test.js"}}}], "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/blueprints/api/timeoff_requests.py": [{"sourceToolCallRequestId": "61c7c27c-4d25-43f7-a4ac-c0c06b522e9a", "timestamp": 0, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "81a7f286-32b9-41e7-85b3-8b5c79ad1c74", "timestamp": 1751054609461, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "e7b3fbea-74c5-46f6-8271-e1315284c75b", "timestamp": 1751054609847, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "4622217e-1802-4884-9d4a-f2ca90f3d610", "timestamp": 1751054609847, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "b899d527-3163-4772-8324-1e9a7140ecce", "timestamp": 1751054624154, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "923cc512-4e39-4b7e-80f7-851f8d999314", "timestamp": 1751054624292, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "7f9360ef-3e8b-4eb9-b425-b5a9dacdcc27", "timestamp": 1751054624292, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "9af1c06b-b8e4-4fdc-9604-9139f8db668b", "timestamp": 1751054644413, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "5b0cfa32-8242-4e52-a1c0-2798ad95bbeb", "timestamp": 1751054644556, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "81c66914-e1d6-4900-81ea-9e11e1cb8e0f", "timestamp": 1751054644556, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "ecb3e50e-ec2e-404b-af9f-3b11a108c6df", "timestamp": 1751054656165, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "da88dae6-4000-49f1-86a0-525617ec170f", "timestamp": 1751054656303, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "6b458a47-7592-4e5d-910b-c6b737f02481", "timestamp": 1751054656303, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "d5f645ca-9e8f-4c88-9ed0-b9422c03255a", "timestamp": 1751054668346, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "7c0e8ffa-35fd-4bc1-ac66-37e7a300f122", "timestamp": 1751054668488, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "8edd1e18-dd93-4b4d-ae03-dc782badf2cc", "timestamp": 1751054668488, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "5ab6283c-d3be-4d5b-80f9-5ec567de1843", "timestamp": 1751054680074, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "dc31f215-98c7-4158-bbe1-1c81afe9f7b8", "timestamp": 1751054680237, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "9480fa58-936f-4dfb-befa-21ce47348b25", "timestamp": 1751054680237, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "0def1f37-e544-43e8-833f-014db4d4693e", "timestamp": 1751054692051, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "f109c309-4cad-4338-b6af-f6e0c9fd39c3", "timestamp": 1751054692209, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "56e29f6e-b3d9-4835-8f1e-4f64aeaea390", "timestamp": 1751054692209, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "de176f08-1dfe-4101-b936-20115a665468", "timestamp": 1751054704000, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "550be30a-89cc-480f-8a96-636320504746", "timestamp": 1751054704172, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "16f2a05d-d632-469d-baa2-d565e3caaaa8", "timestamp": 1751054704172, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "4f030dc6-339c-48c0-a8f7-62d36e5b06b0", "timestamp": 1751054717866, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "51d81f63-5d9f-4eae-8097-e158fd0b8664", "timestamp": 1751054718010, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "1fac8f04-e64a-457e-b269-b3d8877c0f8c", "timestamp": 1751054718010, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "ce3f70dc-2d37-4d5d-9308-1843e66966ec", "timestamp": 1751054730620, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "b234afa2-32bc-43d3-9b45-d260cb224a37", "timestamp": 1751054730769, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "d3c612b0-ac78-4c63-ada1-b7c30be6686e", "timestamp": 1751054730769, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "718667eb-3482-42fc-93b9-c9a2c84c2099", "timestamp": 1751054747648, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "655c5c1d-4737-4e2c-8d2f-acd7928fae1d", "timestamp": 1751054747787, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "dba6f582-8dad-40e8-97ad-6dc2ada4aa12", "timestamp": 1751054747787, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "lastIncludedInRequestId": "3c4563a7-db69-4398-9c87-7c9826d3d990", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "3c4563a7-db69-4398-9c87-7c9826d3d990", "timestamp": 1751054887425, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "a8159071-95cc-4708-9393-5c434c9af871", "timestamp": 1751054887821, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "10429965-e18e-4737-afc1-97090f20fc74", "timestamp": 1751054887821, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "0d8a3490-e9a3-432f-a3ae-e69995718d15", "timestamp": 1751054901882, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "198c78d2-789b-4140-adec-ff614ba2f614", "timestamp": 1751054902022, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "db0576f7-c1f9-4c6c-ba21-a415fc280c8d", "timestamp": 1751054902022, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "5235b056-adc1-4f3f-b116-0ce2efd33ae1", "timestamp": 1751054915352, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "215bc167-1839-41b1-a3ac-804e6e69b61a", "timestamp": 1751054915491, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "785f04a2-7010-427b-85be-7eadcfdc56fc", "timestamp": 1751054915491, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "1fda6b46-b0bd-4c40-81bf-20d39e80d84d", "timestamp": 1751054930546, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "f10684cb-0b44-4605-b96d-088971fe082c", "timestamp": 1751054930685, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "6115d272-a236-4c1b-b0c4-7be2d25014ae", "timestamp": 1751054930685, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "f69db6d8-5b41-46b5-ae3a-34c585f3a714", "timestamp": 1751054942373, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "f3c4db41-7630-4c17-98e2-38b8c192c5e8", "timestamp": 1751054942511, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "cce86121-8aa8-41e3-ba08-0f45993287e6", "timestamp": 1751054942511, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "822cbc5f-66fc-469e-9ff9-457f29978a6f", "timestamp": 1751054953687, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "f2453121-6d17-47a0-83e0-e8724ffbce1a", "timestamp": 1751054953821, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "6305c61b-ee5a-4311-a49d-9b4145b93c46", "timestamp": 1751054953821, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "lastIncludedInRequestId": "5e74a903-b19c-4b78-a68d-ddd790a1b546", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}], "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/frontend/src/test/components/timesheet-requests.test.js": [{"sourceToolCallRequestId": "fc355d74-9153-454d-8b93-36b12bd70f94", "timestamp": 1751054848092, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/timesheet-requests.test.js"}}}, {"sourceToolCallRequestId": "c48e1e7c-136e-4e5e-b919-f1a5cf5089f9", "timestamp": 1751055048377, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/timesheet-requests.test.js"}}}, {"sourceToolCallRequestId": "33edaab1-b438-46c5-a700-c75b83b93df2", "timestamp": 1751055048765, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/timesheet-requests.test.js"}}}, {"sourceToolCallRequestId": "555b7a90-4725-438e-baef-ede97eb714e2", "timestamp": 1751055048765, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/timesheet-requests.test.js"}}}, {"sourceToolCallRequestId": "10309401-3a32-4c5e-af3e-4f31b89da9e7", "timestamp": 1751055062708, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/timesheet-requests.test.js"}}}, {"sourceToolCallRequestId": "c7da532e-ca52-40e3-88ad-e836fab645f8", "timestamp": 1751055062841, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/timesheet-requests.test.js"}}}, {"sourceToolCallRequestId": "899de5c1-ebbc-4d65-b670-934163e7451f", "timestamp": 1751055062841, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/timesheet-requests.test.js"}}}, {"sourceToolCallRequestId": "892bea11-fe2c-4107-a74b-20315c2b4cb3", "timestamp": 1751055075055, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/timesheet-requests.test.js"}}}, {"sourceToolCallRequestId": "980432e0-9084-4117-8364-10309682a461", "timestamp": 1751055075197, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/timesheet-requests.test.js"}}}, {"sourceToolCallRequestId": "60f3abc4-ca7f-4e18-8739-5ea6688e0a0f", "timestamp": 1751055075197, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/timesheet-requests.test.js"}}}, {"sourceToolCallRequestId": "38f03c54-0d6a-4642-bd9a-8c9dc9ceb617", "timestamp": 1751055088143, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/timesheet-requests.test.js"}}}, {"sourceToolCallRequestId": "2bd1b268-19d3-4d89-8037-1a9afc694c25", "timestamp": 1751055088280, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/timesheet-requests.test.js"}}}, {"sourceToolCallRequestId": "2fe55b68-a4bd-4b28-8144-ce3a79bcd808", "timestamp": 1751055088280, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/timesheet-requests.test.js"}}}, {"sourceToolCallRequestId": "46ddd608-a073-4910-992e-c6714f214104", "timestamp": 1751055100085, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/timesheet-requests.test.js"}}}, {"sourceToolCallRequestId": "c6caeb82-46ec-448d-b15f-75d935cddd9d", "timestamp": 1751055100219, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/timesheet-requests.test.js"}}}, {"sourceToolCallRequestId": "a6c6e27c-8587-49c8-9898-82a61adbbb48", "timestamp": 1751055100219, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/timesheet-requests.test.js"}}}, {"sourceToolCallRequestId": "5efe751e-e6d2-4fba-8f75-2cd187f0bb9f", "timestamp": 1751055111472, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/timesheet-requests.test.js"}}}, {"sourceToolCallRequestId": "ca7eedd0-310d-4c51-b689-0376b380b695", "timestamp": 1751055111634, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/timesheet-requests.test.js"}}}, {"sourceToolCallRequestId": "a42fa5fd-db31-4d7d-9e37-6c6f6cfe0417", "timestamp": 1751055111634, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/timesheet-requests.test.js"}}}, {"sourceToolCallRequestId": "e916dcff-5b79-4218-a188-750ebfddb572", "timestamp": 1751055124554, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/timesheet-requests.test.js"}}}, {"sourceToolCallRequestId": "93506d80-3665-4d79-a906-eadb44327e7d", "timestamp": 1751055124686, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/timesheet-requests.test.js"}}}, {"sourceToolCallRequestId": "7b1a25a8-efa8-4cdf-b264-a2a509512856", "timestamp": 1751055124686, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "lastIncludedInRequestId": "5e74a903-b19c-4b78-a68d-ddd790a1b546", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/timesheet-requests.test.js"}}}], "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/frontend/src/views/timesheet/TimesheetRequests.vue": [{"sourceToolCallRequestId": "14e9bdf7-170b-4223-8bbc-b9f649e0cf51", "timestamp": 0, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/timesheet/TimesheetRequests.vue"}}}, {"sourceToolCallRequestId": "8770e6bb-80fb-4d73-bfc3-3e0b5cff0c42", "timestamp": 1751054859565, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/timesheet/TimesheetRequests.vue"}}}, {"sourceToolCallRequestId": "a51d4cac-665b-45bb-af1c-3b73fbb350a6", "timestamp": 1751054859994, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/timesheet/TimesheetRequests.vue"}}}, {"sourceToolCallRequestId": "b36f1590-d17e-428c-b019-28ecca4a5316", "timestamp": 1751054859994, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/timesheet/TimesheetRequests.vue"}}}, {"sourceToolCallRequestId": "bf702c0e-812f-4948-afce-775710d9f39d", "timestamp": 1751054872716, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/timesheet/TimesheetRequests.vue"}}}, {"sourceToolCallRequestId": "eda5b598-5ff1-4c43-8290-82c7ee362323", "timestamp": 1751054872850, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/timesheet/TimesheetRequests.vue"}}}, {"sourceToolCallRequestId": "8eba98dd-4607-4db1-aef1-b5438627b2f4", "timestamp": 1751054872850, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "lastIncludedInRequestId": "3c4563a7-db69-4398-9c87-7c9826d3d990", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/timesheet/TimesheetRequests.vue"}}}, {"sourceToolCallRequestId": "56b7efec-6974-46e5-8ba4-842881d45d73", "timestamp": 1751054980201, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/timesheet/TimesheetRequests.vue"}}}, {"sourceToolCallRequestId": "f142fc53-9034-4e66-8773-26f4fba38abd", "timestamp": 1751054980339, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/timesheet/TimesheetRequests.vue"}}}, {"sourceToolCallRequestId": "1e46362a-3e85-423c-ba91-dc66ffd03e2c", "timestamp": 1751054980339, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/timesheet/TimesheetRequests.vue"}}}, {"sourceToolCallRequestId": "fe21f232-3173-411d-9749-a641a8a32a2a", "timestamp": 1751055000203, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/timesheet/TimesheetRequests.vue"}}}, {"sourceToolCallRequestId": "af5b2769-c4ea-46a4-9419-44ec94d0a739", "timestamp": 1751055000350, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/timesheet/TimesheetRequests.vue"}}}, {"sourceToolCallRequestId": "525f6d63-5e3d-4a83-ae8e-d49ca76b1173", "timestamp": 1751055000350, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/timesheet/TimesheetRequests.vue"}}}, {"sourceToolCallRequestId": "72f6000d-6239-45b8-9e15-97f18f6e957d", "timestamp": 1751055012801, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/timesheet/TimesheetRequests.vue"}}}, {"sourceToolCallRequestId": "103e7dde-f54e-4bd8-ac03-85834362749f", "timestamp": 1751055012939, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/timesheet/TimesheetRequests.vue"}}}, {"sourceToolCallRequestId": "22b24c67-c1cf-432a-ba9d-119a19247ca0", "timestamp": 1751055012939, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "lastIncludedInRequestId": "5e74a903-b19c-4b78-a68d-ddd790a1b546", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/timesheet/TimesheetRequests.vue"}}}], "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/frontend/src/stores/timesheet.js": [{"sourceToolCallRequestId": "f1661337-5225-4641-bc16-d49af7eab72d", "timestamp": 0, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/stores/timesheet.js"}}}, {"sourceToolCallRequestId": "614b3710-c5a1-4949-856c-b4036bd7295b", "timestamp": 1751054967141, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/stores/timesheet.js"}}}, {"sourceToolCallRequestId": "093b993f-d04d-47b1-96a2-203c8e34628b", "timestamp": 1751054967552, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/stores/timesheet.js"}}}, {"sourceToolCallRequestId": "8d8c7765-b7da-44e4-83a2-4d95b2130ea7", "timestamp": 1751054967552, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "lastIncludedInRequestId": "5e74a903-b19c-4b78-a68d-ddd790a1b546", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/stores/timesheet.js"}}}], "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/models_split/communication.py": [{"sourceToolCallRequestId": "4f51d808-7423-472f-b3de-47502933aed5", "timestamp": 0, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/communication.py"}}}, {"sourceToolCallRequestId": "0b8b2bff-0309-40ec-9415-53eb527a3588", "timestamp": 1751055470092, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/communication.py"}}}, {"sourceToolCallRequestId": "1e8502e6-7779-44ee-a0b2-ea419400342d", "timestamp": 1751055470495, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/communication.py"}}}, {"sourceToolCallRequestId": "ca585c9e-195d-4896-aba6-d22d7cec3d34", "timestamp": 1751055470495, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "lastIncludedInRequestId": "57a60d3d-cb24-4b03-9d34-e5afc96b4abb", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/communication.py"}}}], "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/unit/test_kpi_calculations.py": [{"sourceToolCallRequestId": "b6b04995-4075-4362-ab65-c023c833f66c", "timestamp": 0, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "1112303c-f356-4302-bca9-e2d74a0adacd", "timestamp": 1751055658107, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "0c92a76b-1650-4d4d-9fe2-b11c4208866a", "timestamp": 1751055658601, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "9596b784-94b0-4ea2-9b20-9cd90c86bd1f", "timestamp": 1751055658601, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "6a130093-5ac1-45b4-9fe1-a0972693636f", "timestamp": 1751055671484, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "49d43f84-ce8d-4e26-8c0b-38fb9b057c75", "timestamp": 1751055671726, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "e903d147-8d7c-4742-bcd6-3002bc695ad0", "timestamp": 1751055671726, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "3f31e8c6-5a1c-4b01-8b07-a7f21dbaf1c7", "timestamp": 1751055686116, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "c9f47c9e-50d4-4a0f-8247-9219940025b7", "timestamp": 1751055686259, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "a61f6c6d-4681-437f-b8b8-874e41adc738", "timestamp": 1751055686259, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "7f087304-0c84-4325-8582-b64129ce1742", "timestamp": 1751055698820, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "560757e3-1ebd-4f04-a48e-f683ac7f914f", "timestamp": 1751055698957, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "b4d8d356-1df1-477c-ba3e-daf0fdb04cd7", "timestamp": 1751055698957, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "cf5f369d-393d-44e0-a807-29b3bf8d774b", "timestamp": 1751055710622, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "a45fee4f-2a6b-4bdb-a8f7-b0f388dc0af4", "timestamp": 1751055710753, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "28c89a04-2c96-4436-96b8-9b1b036842cb", "timestamp": 1751055710753, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "46f1a41d-684e-4a6e-b3f5-3de81ba425bc", "timestamp": 1751055722703, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "bb022f06-b3aa-486c-8d10-9a13607f66d9", "timestamp": 1751055722845, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "2f331423-02c6-42be-ad10-e5e4076b14ff", "timestamp": 1751055722845, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "lastIncludedInRequestId": "c6aab75e-4934-4bc6-b0d4-1462c7ce9420", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "5b74a3a7-4ceb-4680-91f4-67bff87577c2", "timestamp": 1751057225296, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "cdef5006-d53b-4c83-bf2c-e621a723c44e", "timestamp": 1751057225695, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "69aaa393-9c20-475b-835f-5f76c99601a3", "timestamp": 1751057225695, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "lastIncludedInRequestId": "a421f283-ecfb-468d-bdb0-83ffc37ed808", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "7d4bda71-2657-4501-b321-bdfad7fe27b6", "timestamp": 1751058171515, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "3b362e8f-bbfd-4ae3-8591-2d0039919769", "timestamp": 1751058171903, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "ba5b4b30-c371-4859-8c26-ee3e316111a5", "timestamp": 1751058171903, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "b7403178-b746-49dc-8949-df1c4af389d6", "timestamp": 1751058185258, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "f553dc99-e72f-4ad9-91c1-6237b42713f1", "timestamp": 1751058185414, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "27e23d12-c2b3-41c9-8bb6-b2219e93f5f3", "timestamp": 1751058185414, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "e84526a4-614e-42eb-899d-45d223d22c9e", "timestamp": 1751058199440, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "f38d8698-4ccf-47a4-8fe5-fc8cacfd9683", "timestamp": 1751058199582, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "1c0d791a-1ad6-4433-bd2f-74379f6614d1", "timestamp": 1751058199582, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "eb16d31a-db57-41de-878f-71a4fd4b5537", "timestamp": 1751058212022, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "1166a290-88a3-4397-a627-95f96f233fef", "timestamp": 1751058212150, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "70a66c51-c321-4f82-b77f-68139cf644e7", "timestamp": 1751058212150, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "e0461789-da1a-4318-98bc-12a24b9c7376", "timestamp": 1751058224993, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "bee93ac8-cebd-4d4b-b332-e177cd42755d", "timestamp": 1751058225126, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "fee7c877-09e1-4c31-bba2-d413a500972e", "timestamp": 1751058225126, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "lastIncludedInRequestId": "7643b68e-c919-49b7-b45d-06e224a6a3a8", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_kpi_calculations.py"}}}], "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/unit/test_task_kpi_calculations.py": [{"sourceToolCallRequestId": "4211d8ae-880b-4c52-a838-70617b3986a9", "timestamp": 0, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_task_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "73977091-f86a-4851-b434-00c3bd07280c", "timestamp": 1751055742041, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_task_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "2a1764d9-897f-465c-9939-5f4d270d6f80", "timestamp": 1751055742589, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_task_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "ccac00fe-0f6c-41c4-95ba-4429f692679f", "timestamp": 1751055742589, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_task_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "a738141d-176a-4cc5-b95d-0512f6ae32f3", "timestamp": 1751055754946, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_task_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "9404c149-4c88-44c0-a4b6-9ebd05ae07e0", "timestamp": 1751055755079, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_task_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "a9ca29f5-08f8-4543-b4fa-53695c2d8398", "timestamp": 1751055755079, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "lastIncludedInRequestId": "c6aab75e-4934-4bc6-b0d4-1462c7ce9420", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_task_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "54c443af-b0f6-46fa-be6f-cec2c74c36f8", "timestamp": 1751058239279, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_task_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "620d3504-1d2e-43fa-b39d-3b2844e7de4e", "timestamp": 1751058239702, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_task_kpi_calculations.py"}}}, {"sourceToolCallRequestId": "edc0a76f-8b71-4a4b-94b4-e8694ffdd038", "timestamp": 1751058239702, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "lastIncludedInRequestId": "7643b68e-c919-49b7-b45d-06e224a6a3a8", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_task_kpi_calculations.py"}}}], "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/utils/cost_calculator.py": [{"sourceToolCallRequestId": "abd544ca-ee19-4190-bf1a-2c9fcc0e0ed8", "timestamp": 0, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/utils/cost_calculator.py"}}}, {"sourceToolCallRequestId": "e33d407a-c96a-48ef-9f2d-d978fad193ca", "timestamp": 1751055791956, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/utils/cost_calculator.py"}}}, {"sourceToolCallRequestId": "abdcbf94-c299-463e-b5bf-662e411d9968", "timestamp": 1751055792552, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/utils/cost_calculator.py"}}}, {"sourceToolCallRequestId": "e97d9782-ccb0-4b33-80eb-b5c6d2609f83", "timestamp": 1751055792553, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/utils/cost_calculator.py"}}}, {"sourceToolCallRequestId": "d435e015-7996-4581-8670-db645135587e", "timestamp": 1751055831516, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/utils/cost_calculator.py"}}}, {"sourceToolCallRequestId": "4721a4f7-2fcf-4ec9-bcc2-7310995e6b0f", "timestamp": 1751055831655, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/utils/cost_calculator.py"}}}, {"sourceToolCallRequestId": "1707dc89-2471-4956-bd88-9808d1700634", "timestamp": 1751055831655, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "lastIncludedInRequestId": "c6aab75e-4934-4bc6-b0d4-1462c7ce9420", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/utils/cost_calculator.py"}}}, {"sourceToolCallRequestId": "526f105a-f1c0-4777-870d-27ed517aa507", "timestamp": 1751057196546, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/utils/cost_calculator.py"}}}, {"sourceToolCallRequestId": "1579cf42-db31-4e66-bae0-1d32c41f3f10", "timestamp": 1751057196959, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/utils/cost_calculator.py"}}}, {"sourceToolCallRequestId": "4bfdd77c-cd53-4405-b08d-3606061a15f9", "timestamp": 1751057196959, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/utils/cost_calculator.py"}}}, {"sourceToolCallRequestId": "4f1b8892-45c8-450a-94e5-20fdcb71fcdc", "timestamp": 1751057210566, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/utils/cost_calculator.py"}}}, {"sourceToolCallRequestId": "a1bb2a44-ebff-41e6-91e0-dc1d44b75be1", "timestamp": 1751057210698, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/utils/cost_calculator.py"}}}, {"sourceToolCallRequestId": "63aa6674-47bd-406c-8f13-f6a5c70fe424", "timestamp": 1751057210698, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "lastIncludedInRequestId": "a421f283-ecfb-468d-bdb0-83ffc37ed808", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/utils/cost_calculator.py"}}}], "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/integration/test_communication.py": [{"sourceToolCallRequestId": "7600fa5d-caec-48a1-a3a8-720de4f8e4c1", "timestamp": 0, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/integration/test_communication.py"}}}, {"sourceToolCallRequestId": "ea0627bd-9dbc-495b-b8eb-13601b949b5b", "timestamp": 1751056405186, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/integration/test_communication.py"}}}, {"sourceToolCallRequestId": "53776332-99f4-4675-ac9f-302384e8fb77", "timestamp": 1751056405573, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/integration/test_communication.py"}}}, {"sourceToolCallRequestId": "c9496dde-2033-4deb-90a0-d1a6c68f82f3", "timestamp": 1751056405573, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "lastIncludedInRequestId": "f573c245-52be-4134-af7b-74730d70934e", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/integration/test_communication.py"}}}, {"sourceToolCallRequestId": "b0bbd6f3-040f-45ab-b6b9-6d861125e81d", "timestamp": 1751056438606, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/integration/test_communication.py"}}}, {"sourceToolCallRequestId": "1eae13a2-5624-4d06-bd95-4ab8c3f237cd", "timestamp": 1751056438740, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/integration/test_communication.py"}}}, {"sourceToolCallRequestId": "0609c8f8-eb09-457c-8d0d-890cfe28c487", "timestamp": 1751056438740, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/integration/test_communication.py"}}}, {"sourceToolCallRequestId": "07d4f646-6b88-4258-8097-c91d6a52395c", "timestamp": 1751056456906, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/integration/test_communication.py"}}}, {"sourceToolCallRequestId": "2751a0d2-708c-48fd-a48f-4e593255e643", "timestamp": 1751056457039, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/integration/test_communication.py"}}}, {"sourceToolCallRequestId": "256ecec4-099a-4a98-b64d-3bd0152df5c7", "timestamp": 1751056457039, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "lastIncludedInRequestId": "6f0d8789-1cf3-43bb-a1d3-25012234c43a", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/integration/test_communication.py"}}}], "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/scripts/check_db_schema.py": [{"sourceToolCallRequestId": "44984f01-427b-49bb-be2f-ec30bdcdaf77", "timestamp": 1751056610083, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/scripts/check_db_schema.py"}}}], "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/models_split/hr.py": [{"sourceToolCallRequestId": "b40e6e2e-af64-4f2b-8d2f-4535ebd873d6", "timestamp": 0, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/hr.py"}}}, {"sourceToolCallRequestId": "424088fc-5c06-4431-81c5-f4fda885070c", "timestamp": 1751057105062, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/hr.py"}}}, {"sourceToolCallRequestId": "ca6be07f-4e8b-4afe-baea-8d73497d4f58", "timestamp": 1751057105613, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/hr.py"}}}, {"sourceToolCallRequestId": "03116808-50d2-44a3-a824-eb67c194aff6", "timestamp": 1751057105613, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "lastIncludedInRequestId": "a421f283-ecfb-468d-bdb0-83ffc37ed808", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/hr.py"}}}, {"sourceToolCallRequestId": "1e8bbcd6-f66e-4513-ace8-094b949d4619", "timestamp": 1751057792480, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/hr.py"}}}, {"sourceToolCallRequestId": "5f008a33-260b-4f70-b649-e753bc5ccf68", "timestamp": 1751057792893, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/hr.py"}}}, {"sourceToolCallRequestId": "6118224d-de6d-43c2-92f3-aabad059e3a3", "timestamp": 1751057792893, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/hr.py"}}}, {"sourceToolCallRequestId": "d3a20ee5-790b-4847-8cf6-121fb7cfa77b", "timestamp": 1751057805728, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/hr.py"}}}, {"sourceToolCallRequestId": "fc8743da-1b9a-4283-b96e-13aa992c43ca", "timestamp": 1751057805860, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/hr.py"}}}, {"sourceToolCallRequestId": "bcbc36ba-ecef-4dac-ada9-aa1dc2169b3f", "timestamp": 1751057805860, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "lastIncludedInRequestId": "a50dedbc-027a-4931-a23f-ec49ad279fef", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/hr.py"}}}], "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/models_split/projects.py": [{"sourceToolCallRequestId": "70e6334d-2967-401c-87ae-09c177af3af7", "timestamp": 0, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/projects.py"}}}, {"sourceToolCallRequestId": "bc3be49f-b96b-4a5a-ab5b-cc58cc288a32", "timestamp": 1751057120982, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/projects.py"}}}, {"sourceToolCallRequestId": "a73a3f7f-d1dd-4c55-a190-c20454fcc8c3", "timestamp": 1751057121418, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/projects.py"}}}, {"sourceToolCallRequestId": "4bb402b1-da35-40ad-910a-17753f389b69", "timestamp": 1751057121418, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/projects.py"}}}, {"sourceToolCallRequestId": "538cd275-3f43-4b0c-b1ee-9c54d60132c0", "timestamp": 1751057137643, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/projects.py"}}}, {"sourceToolCallRequestId": "db541f54-5271-4e0a-871f-a652a883b400", "timestamp": 1751057137779, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/projects.py"}}}, {"sourceToolCallRequestId": "e1b30b54-b748-49d0-b49d-67e53df8bb3e", "timestamp": 1751057137779, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/projects.py"}}}, {"sourceToolCallRequestId": "a9a00909-670b-4ea6-a692-ebf4b6cad943", "timestamp": 1751057153818, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/projects.py"}}}, {"sourceToolCallRequestId": "e7872cc2-6fde-4557-8e62-764188b499e8", "timestamp": 1751057154000, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/projects.py"}}}, {"sourceToolCallRequestId": "42a35e28-59a0-41fc-b6f1-5e2974c72c8f", "timestamp": 1751057154000, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/projects.py"}}}, {"sourceToolCallRequestId": "90e11e26-8482-4ab3-a0b9-c478f724c350", "timestamp": 1751057178559, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/projects.py"}}}, {"sourceToolCallRequestId": "2abf89e4-80fb-4a42-a791-36922f0ef4f5", "timestamp": 1751057178695, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/projects.py"}}}, {"sourceToolCallRequestId": "fd47daac-37e4-40ae-b924-10b490b52d85", "timestamp": 1751057178695, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "lastIncludedInRequestId": "a421f283-ecfb-468d-bdb0-83ffc37ed808", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/projects.py"}}}, {"sourceToolCallRequestId": "5d6cd198-8002-432a-9e31-ccd23160c125", "timestamp": 1751057266546, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/projects.py"}}}, {"sourceToolCallRequestId": "9a22ea44-cff6-4f6d-a547-f04d83eeae18", "timestamp": 1751057266683, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/projects.py"}}}, {"sourceToolCallRequestId": "642453a8-1e67-4a19-a73d-0eafa738d1c0", "timestamp": 1751057266683, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "lastIncludedInRequestId": "e2d0c1a3-4455-435b-911b-683815b1ce03", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/projects.py"}}}], "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/api/test_clients.py": [{"sourceToolCallRequestId": "ae2f5874-115d-4d09-9f9c-34b5ef7921b9", "timestamp": 1751059527411, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/api/test_clients.py"}}}, {"sourceToolCallRequestId": "6619dd68-3d3b-4a1a-ae49-4119ad253ac5", "timestamp": 1751059579018, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/api/test_clients.py"}}}, {"sourceToolCallRequestId": "7571a43a-f724-438e-a986-55e7acab3344", "timestamp": 1751059579384, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "lastIncludedInRequestId": "d3419641-c00d-45ef-ad66-9a32c3664a89", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/api/test_clients.py"}}}, {"sourceToolCallRequestId": "2181348c-131a-49cc-a692-ddedf41eda34", "timestamp": 1751060486367, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/api/test_clients.py"}}}], "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/api/test_proposals.py": [{"sourceToolCallRequestId": "44fbb197-3247-4ef2-8744-7e802e60fb1c", "timestamp": 1751059673846, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/api/test_proposals.py"}}}, {"sourceToolCallRequestId": "ad270d4d-556a-4e1d-bf22-10d6ccec7ee9", "timestamp": 1751060553891, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/api/test_proposals.py"}}}, {"sourceToolCallRequestId": "290aca03-22dd-447c-8a2e-342c21bfc80e", "timestamp": 1751060554278, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/api/test_proposals.py"}}}, {"sourceToolCallRequestId": "418a70c9-9c37-490e-bf18-673d277fc339", "timestamp": 1751060554278, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/api/test_proposals.py"}}}, {"sourceToolCallRequestId": "67af24fe-1379-4920-929d-52cd60d870c8", "timestamp": 1751060566749, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/api/test_proposals.py"}}}, {"sourceToolCallRequestId": "be104531-8011-48c7-956d-5150a764b44a", "timestamp": 1751060566879, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/api/test_proposals.py"}}}, {"sourceToolCallRequestId": "9f5aa8c7-8257-40f3-8ee8-89432c79b272", "timestamp": 1751060566880, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/api/test_proposals.py"}}}, {"sourceToolCallRequestId": "a3dc8ffc-5246-445c-b67e-4c40479b1b93", "timestamp": 1751060580493, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/api/test_proposals.py"}}}, {"sourceToolCallRequestId": "83b34078-a137-4cc2-ae80-5470bc0bf46c", "timestamp": 1751060580627, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/api/test_proposals.py"}}}, {"sourceToolCallRequestId": "8206ceb2-2d23-4ad1-b1ce-330d45193c6a", "timestamp": 1751060580627, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/api/test_proposals.py"}}}, {"sourceToolCallRequestId": "331d9700-9be7-4450-b215-2d021dffa9c2", "timestamp": 1751060592843, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/api/test_proposals.py"}}}, {"sourceToolCallRequestId": "1024372d-33ad-4238-b756-9e5381fe819e", "timestamp": 1751060592975, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/api/test_proposals.py"}}}, {"sourceToolCallRequestId": "e16c5214-148c-4eae-8c30-bb6a1e3d1a3c", "timestamp": 1751060592975, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "lastIncludedInRequestId": "ff0f60fa-e572-4790-8046-6e7d4b53a568", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/api/test_proposals.py"}}}], "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/api/test_contracts.py": [{"sourceToolCallRequestId": "458ed76c-85a8-4cb7-8db6-11aa17c06323", "timestamp": 1751059713333, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/api/test_contracts.py"}}}], "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/api/test_personnel_rates.py": [{"sourceToolCallRequestId": "a86b7f8f-82cd-4917-b055-94c949301109", "timestamp": 1751059752851, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/api/test_personnel_rates.py"}}}], "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/api/test_timeoff_requests.py": [{"sourceToolCallRequestId": "2fd92d9e-0e23-4314-b51a-d76a8afc8b1b", "timestamp": 1751059798217, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/api/test_timeoff_requests.py"}}}], "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/api/test_performance_reviews.py": [{"sourceToolCallRequestId": "9dda6fb6-0b99-4859-940e-4dd123294bb2", "timestamp": 1751059843304, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/api/test_performance_reviews.py"}}}], "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/api/test_forum_topics.py": [{"sourceToolCallRequestId": "43db311a-a931-4a06-b20e-bdc047d18377", "timestamp": 1751059886391, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/api/test_forum_topics.py"}}}], "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/api/test_direct_messages.py": [{"sourceToolCallRequestId": "0c9fc28b-39fe-4d7a-bdf8-b968529545b8", "timestamp": 1751059931027, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/api/test_direct_messages.py"}}}], "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/api/test_polls.py": [{"sourceToolCallRequestId": "87b3ea13-aae8-45c0-bb1c-5a2a29c4f1f7", "timestamp": 1751059977721, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/api/test_polls.py"}}}], "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/unit/test_contact_model.py": [{"sourceToolCallRequestId": "223a857d-95ba-4b0c-8efb-fa463c1e8a53", "timestamp": 1751061479000, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_contact_model.py"}}}, {"sourceToolCallRequestId": "1240d99e-9681-4399-8b1e-1cc5d2acf9b3", "timestamp": 1751061643267, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_contact_model.py"}}}, {"sourceToolCallRequestId": "570f6a70-f9d1-43d8-94bf-e4329d062afc", "timestamp": 1751061643416, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_contact_model.py"}}}, {"sourceToolCallRequestId": "9f15d73e-e3f7-44e2-a2db-32a373e8c876", "timestamp": 1751061643416, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_contact_model.py"}}}, {"sourceToolCallRequestId": "a4971f30-e326-4c88-83bb-f11d4b100da3", "timestamp": 1751061657773, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_contact_model.py"}}}, {"sourceToolCallRequestId": "7adb85a7-4fa8-4898-b99d-9b342e470498", "timestamp": 1751061657908, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_contact_model.py"}}}, {"sourceToolCallRequestId": "d8227552-5b1f-4b0a-90a3-6adabd500ed2", "timestamp": 1751061657908, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "lastIncludedInRequestId": "daf1ff81-2e69-4f0a-b293-c919f883a95e", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_contact_model.py"}}}], "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/unit/test_skill_model.py": [{"sourceToolCallRequestId": "88ab1dfa-604d-421a-89d9-8e3547a1992c", "timestamp": 1751061519457, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_skill_model.py"}}}, {"sourceToolCallRequestId": "fd6f1850-e85b-4bb9-84c9-d7b8d28c4ad7", "timestamp": 1751062683116, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_skill_model.py"}}}, {"sourceToolCallRequestId": "57497337-1ecc-4ab2-87f8-28260ec5d092", "timestamp": 1751062683493, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_skill_model.py"}}}, {"sourceToolCallRequestId": "ae4635cf-860a-4d57-aa99-07a2c97e928e", "timestamp": 1751062683493, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_skill_model.py"}}}, {"sourceToolCallRequestId": "6bee9374-3b16-4abe-9e6f-6b5211b699fa", "timestamp": 1751062702005, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_skill_model.py"}}}, {"sourceToolCallRequestId": "63598873-0646-405d-937f-8e3dcb8d56bb", "timestamp": 1751062702156, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_skill_model.py"}}}, {"sourceToolCallRequestId": "b131846e-964b-4661-a78c-4a86362157a8", "timestamp": 1751062702156, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_skill_model.py"}}}, {"sourceToolCallRequestId": "b68228b6-9022-434c-a0e3-eb1099f11812", "timestamp": 1751062720609, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_skill_model.py"}}}, {"sourceToolCallRequestId": "757b4c29-75bf-4acd-acb3-4a3e2fb015a7", "timestamp": 1751062720754, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_skill_model.py"}}}, {"sourceToolCallRequestId": "72ed1700-5d21-4b36-ab4c-2cb10ef8640d", "timestamp": 1751062720755, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_skill_model.py"}}}, {"sourceToolCallRequestId": "ac47ee8c-2f51-42c6-9d16-ce909a2655e1", "timestamp": 1751062743218, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_skill_model.py"}}}, {"sourceToolCallRequestId": "4af689aa-6acf-4709-a3c9-530d9da6d259", "timestamp": 1751062743361, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_skill_model.py"}}}, {"sourceToolCallRequestId": "ddcc6d73-e6a8-4138-920a-d897b191f2de", "timestamp": 1751062743361, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_skill_model.py"}}}, {"sourceToolCallRequestId": "d26bc00b-f496-4f3f-ae51-920c28577cd1", "timestamp": 1751062759501, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_skill_model.py"}}}, {"sourceToolCallRequestId": "ad7f1223-57e7-4f55-a759-00e9081b42d1", "timestamp": 1751062759651, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_skill_model.py"}}}, {"sourceToolCallRequestId": "61d5d145-e025-4c58-ac00-a91c03ba81bd", "timestamp": 1751062759651, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_skill_model.py"}}}, {"sourceToolCallRequestId": "362a5059-c945-4c16-98d9-9b5fc28a5ec5", "timestamp": 1751062777558, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_skill_model.py"}}}, {"sourceToolCallRequestId": "d493961f-51a2-49df-9422-6ef50a8b5888", "timestamp": 1751062777706, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_skill_model.py"}}}, {"sourceToolCallRequestId": "618f6eee-d415-462c-9f2e-edcc96944719", "timestamp": 1751062777706, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_skill_model.py"}}}, {"sourceToolCallRequestId": "273515bc-fdd7-48a5-9999-5179eb03410c", "timestamp": 1751062818337, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_skill_model.py"}}}, {"sourceToolCallRequestId": "1e9d567a-c2a3-4465-a81b-51f8eb515aa3", "timestamp": 1751062818466, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_skill_model.py"}}}, {"sourceToolCallRequestId": "465b1e7b-5979-44b9-bcf4-0015471208fb", "timestamp": 1751062818466, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_skill_model.py"}}}, {"sourceToolCallRequestId": "6b24cfbd-1e76-4f35-a546-2fa1fc9a1a5a", "timestamp": 1751062869988, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_skill_model.py"}}}, {"sourceToolCallRequestId": "58d78dde-bca4-48ef-a619-a8d713e583cb", "timestamp": 1751062870382, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_skill_model.py"}}}, {"sourceToolCallRequestId": "e4672acd-bbd8-4884-acd1-275c075dc916", "timestamp": 1751062870382, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_skill_model.py"}}}, {"sourceToolCallRequestId": "be47a2b6-2f9e-412c-96a6-62d601242663", "timestamp": 1751062885212, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_skill_model.py"}}}, {"sourceToolCallRequestId": "03cc10fd-b68f-4c23-9903-80dbcef593fb", "timestamp": 1751062885348, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_skill_model.py"}}}, {"sourceToolCallRequestId": "417866ca-2dc6-4b47-8e3a-396b2b11074a", "timestamp": 1751062885348, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_skill_model.py"}}}, {"sourceToolCallRequestId": "8bec2560-f134-40a3-80a7-fda7b268cbe1", "timestamp": 1751062899978, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_skill_model.py"}}}, {"sourceToolCallRequestId": "38d00f9e-dcab-467e-9da3-8898c52bfbc3", "timestamp": 1751062900112, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_skill_model.py"}}}, {"sourceToolCallRequestId": "b814d22c-0462-49ae-8984-9b7c51b51096", "timestamp": 1751062900112, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_skill_model.py"}}}, {"sourceToolCallRequestId": "9509773d-f9e2-4703-9018-c373642676e8", "timestamp": 1751062913810, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_skill_model.py"}}}, {"sourceToolCallRequestId": "9060c4cb-4e2d-47db-ad13-41384f339f78", "timestamp": 1751062913942, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_skill_model.py"}}}, {"sourceToolCallRequestId": "e4f455e4-4377-48f5-9f79-f743d1a2e865", "timestamp": 1751062913942, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_skill_model.py"}}}, {"sourceToolCallRequestId": "bd5b8b08-b9f0-44c4-b88b-a15f6d084d6e", "timestamp": 1751062929503, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_skill_model.py"}}}, {"sourceToolCallRequestId": "d05b7877-1d16-457d-b22a-caa58b482b98", "timestamp": 1751062929635, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_skill_model.py"}}}, {"sourceToolCallRequestId": "e26c6473-cec1-4a38-af90-efd2ff56c8a9", "timestamp": 1751062929635, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "lastIncludedInRequestId": "03f7b63d-f5d3-449a-a33b-d07874f67294", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_skill_model.py"}}}], "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/unit/test_department_model.py": [{"sourceToolCallRequestId": "a2f438e2-eed7-493d-9437-c25e10031862", "timestamp": 1751061564421, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_department_model.py"}}}, {"sourceToolCallRequestId": "2260989e-7f79-437e-bad9-87ca0ae06a9f", "timestamp": 1751063131212, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_department_model.py"}}}, {"sourceToolCallRequestId": "d01a7b86-6cf7-41b2-b5aa-f0ebea17fb15", "timestamp": 1751063131619, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_department_model.py"}}}, {"sourceToolCallRequestId": "a04b1e14-f993-4faa-ada8-12119a976cce", "timestamp": 1751063131619, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_department_model.py"}}}, {"sourceToolCallRequestId": "a8e88028-2d70-4ed1-917a-9410b85a15fd", "timestamp": 1751063147099, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_department_model.py"}}}, {"sourceToolCallRequestId": "df10268c-1be7-4ec9-a09d-3c09594f936d", "timestamp": 1751063147240, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_department_model.py"}}}, {"sourceToolCallRequestId": "5d796745-3a2f-4008-9532-708b41aab545", "timestamp": 1751063147240, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_department_model.py"}}}, {"sourceToolCallRequestId": "a6481cae-b5d6-4478-b946-8a9375f3c0ac", "timestamp": 1751063160714, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_department_model.py"}}}, {"sourceToolCallRequestId": "5b82b178-aef3-43bd-be29-fe2b0939161f", "timestamp": 1751063160847, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_department_model.py"}}}, {"sourceToolCallRequestId": "95f64ff6-9641-44da-959d-980377eaf745", "timestamp": 1751063160847, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "lastIncludedInRequestId": "a3101ba5-b58a-4ced-ba5d-78fdace7debe", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_department_model.py"}}}], "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/unit/test_task_dependency_model.py": [{"sourceToolCallRequestId": "19438e30-4528-4605-88b2-a963c1681087", "timestamp": 1751061613865, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_task_dependency_model.py"}}}, {"sourceToolCallRequestId": "9de969e8-f8ff-4644-9ebc-f6e4bb56c83c", "timestamp": 1751063388382, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_task_dependency_model.py"}}}, {"sourceToolCallRequestId": "097a9e05-5df1-4d2e-9c04-181b5648a02b", "timestamp": 1751063388758, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_task_dependency_model.py"}}}, {"sourceToolCallRequestId": "0f57758e-a2ce-4deb-844b-6d23a27ec0b7", "timestamp": 1751063388758, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "lastIncludedInRequestId": "768790c6-12c5-461f-8cb3-b35355876bca", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_task_dependency_model.py"}}}, {"sourceToolCallRequestId": "c2eeaab1-083d-415a-997c-d5606f3fc2b8", "timestamp": 1751063416325, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_task_dependency_model.py"}}}, {"sourceToolCallRequestId": "339c7b47-3e45-42be-8fbe-8bee4a9f900d", "timestamp": 1751063416480, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_task_dependency_model.py"}}}, {"sourceToolCallRequestId": "94c8f3d8-8b60-4665-b808-ff7bb486c7f2", "timestamp": 1751063416480, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_task_dependency_model.py"}}}, {"sourceToolCallRequestId": "ac0a12ef-85b3-4a0d-8f7b-4ade0f04b003", "timestamp": 1751063429964, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_task_dependency_model.py"}}}, {"sourceToolCallRequestId": "46d104b2-e9d4-43d4-8ea3-66891e52bbdc", "timestamp": 1751063430113, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_task_dependency_model.py"}}}, {"sourceToolCallRequestId": "218547f5-1012-44c0-90c8-3ccac3ac748b", "timestamp": 1751063430113, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_task_dependency_model.py"}}}, {"sourceToolCallRequestId": "f955a14b-55b7-4aa8-8878-fe85b8d58c66", "timestamp": 1751063444184, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_task_dependency_model.py"}}}, {"sourceToolCallRequestId": "7b6be491-e908-4c12-b675-06cb6a9bc516", "timestamp": 1751063444321, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_task_dependency_model.py"}}}, {"sourceToolCallRequestId": "5c077be2-aa71-432f-8b0f-f67e25a89912", "timestamp": 1751063444321, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_task_dependency_model.py"}}}, {"sourceToolCallRequestId": "c9930ed6-94a5-4592-acea-b169551decc8", "timestamp": 1751063458151, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_task_dependency_model.py"}}}, {"sourceToolCallRequestId": "ff0598ed-eca4-4683-b2ec-c931cd8c811b", "timestamp": 1751063458288, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_task_dependency_model.py"}}}, {"sourceToolCallRequestId": "feac7af1-67d9-4be1-8be3-86649c2bcb1a", "timestamp": 1751063458288, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_task_dependency_model.py"}}}, {"sourceToolCallRequestId": "bf77acd3-7690-43da-b679-d07c3ed817e1", "timestamp": 1751063471058, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_task_dependency_model.py"}}}, {"sourceToolCallRequestId": "ffd6c3a4-f000-4e95-8c59-0bed4e7af81b", "timestamp": 1751063471190, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_task_dependency_model.py"}}}, {"sourceToolCallRequestId": "683fdffa-9078-4e52-ae65-4cc2ee377b8e", "timestamp": 1751063471190, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_task_dependency_model.py"}}}, {"sourceToolCallRequestId": "87930852-0d2a-4926-840c-09f45a12d255", "timestamp": 1751063483285, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_task_dependency_model.py"}}}, {"sourceToolCallRequestId": "06f9d411-5223-416f-b511-c31a41aa6658", "timestamp": 1751063483418, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_task_dependency_model.py"}}}, {"sourceToolCallRequestId": "5c861a13-8c8b-4ecd-8c38-6980e1894b1b", "timestamp": 1751063483418, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "lastIncludedInRequestId": "2f02918d-7bb6-4d50-9e39-b0df1825e26e", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_task_dependency_model.py"}}}], "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/unit/test_department_model_fixed.py": [{"sourceToolCallRequestId": "28b18b43-6aef-47b5-bd2d-65180cba7811", "timestamp": 1751063203607, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_department_model_fixed.py"}}}], "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/unit/test_userprofile_model.py": [{"sourceToolCallRequestId": "ba0ecae5-a36c-431d-ba72-60324aa85c08", "timestamp": 1751063594766, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_userprofile_model.py"}}}, {"sourceToolCallRequestId": "db44f6b0-ab77-40f7-982d-9177c0357e1d", "timestamp": 1751064671162, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_userprofile_model.py"}}}, {"sourceToolCallRequestId": "dd3cd00d-4e38-4d27-a036-c362d62d7830", "timestamp": 1751064671536, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_userprofile_model.py"}}}, {"sourceToolCallRequestId": "ec4ea1dd-056b-4e8b-8aaf-34e619574bb4", "timestamp": 1751064671536, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_userprofile_model.py"}}}, {"sourceToolCallRequestId": "54f2604c-c000-4b1c-8a3f-c9e1f1d358e9", "timestamp": 1751064685604, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_userprofile_model.py"}}}, {"sourceToolCallRequestId": "3345027f-0a71-4e80-bb45-0b378e40ca6d", "timestamp": 1751064685735, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_userprofile_model.py"}}}, {"sourceToolCallRequestId": "2775a3b2-813d-4a2e-84c9-d7ce71db042f", "timestamp": 1751064685735, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_userprofile_model.py"}}}, {"sourceToolCallRequestId": "df51d73b-6bda-4f00-9618-72bf73dc182d", "timestamp": 1751064699280, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_userprofile_model.py"}}}, {"sourceToolCallRequestId": "5851ce7e-3ede-4594-a7e3-13d95054a1c0", "timestamp": 1751064699430, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_userprofile_model.py"}}}, {"sourceToolCallRequestId": "bb207109-b7b9-49b9-95d2-090289efc26d", "timestamp": 1751064699430, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_userprofile_model.py"}}}, {"sourceToolCallRequestId": "b79e3ea9-4298-4cdf-bc55-0db56a2a49d7", "timestamp": 1751064715860, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_userprofile_model.py"}}}, {"sourceToolCallRequestId": "cec891b1-0355-4969-8ff0-070991dfc5da", "timestamp": 1751064716210, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_userprofile_model.py"}}}, {"sourceToolCallRequestId": "841ec196-60b3-422e-9b50-eff592f89a5f", "timestamp": 1751064716210, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "lastIncludedInRequestId": "8b8d32e2-b64f-4c4b-9928-f0a1165f0b52", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_userprofile_model.py"}}}], "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/unit/test_timesheetentry_model.py": [{"sourceToolCallRequestId": "06ec7ce0-7feb-4bf1-a6da-8bad0997d893", "timestamp": 1751063646674, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_timesheetentry_model.py"}}}], "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/unit/test_projectresource_model.py": [{"sourceToolCallRequestId": "81a7bfe4-9fdd-457b-99b1-4034aa649ae0", "timestamp": 1751063698202, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_projectresource_model.py"}}}], "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/unit/test_product_model.py": [{"sourceToolCallRequestId": "2cfc7908-d47d-40ca-b2db-8f98d2ace605", "timestamp": 1751063752813, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_product_model.py"}}}], "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/unit/test_forumcomment_model.py": [{"sourceToolCallRequestId": "e79b6288-9aba-4672-b427-9ca5b4f96dc3", "timestamp": 1751063810019, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_forumcomment_model.py"}}}, {"sourceToolCallRequestId": "4252a714-6eaf-4c8d-912a-8c0169c705e7", "timestamp": 1751095048373, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_forumcomment_model.py"}}}, {"sourceToolCallRequestId": "1c9cca05-34d5-4225-b542-72f9f5deb720", "timestamp": 1751095048772, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_forumcomment_model.py"}}}, {"sourceToolCallRequestId": "35e45809-ffee-4751-96ee-fa86ab5b70be", "timestamp": 1751095048772, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "lastIncludedInRequestId": "8b8d32e2-b64f-4c4b-9928-f0a1165f0b52", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_forumcomment_model.py"}}}], "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/unit/test_userprofile_model_fixed.py": [{"sourceToolCallRequestId": "92a2e2c8-2cb8-4561-829b-c2268b44577c", "timestamp": 1751064755149, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_userprofile_model_fixed.py"}}}], "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/unit/test_timesheetentry_model_fixed.py": [{"sourceToolCallRequestId": "c93d9958-1842-4a37-b693-e2fa820a8d8c", "timestamp": 1751095092805, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_timesheetentry_model_fixed.py"}}}], "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/unit/test_projectresource_model_fixed.py": [{"sourceToolCallRequestId": "e7851e72-955a-4eeb-a263-5d9083358495", "timestamp": 1751095131072, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_projectresource_model_fixed.py"}}}]}, "metadata": {"checkpointDocumentIds": ["0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/frontend/src/views/auth/Login.vue", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/frontend/src/views/projects/Projects.vue", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/frontend/src/views/projects/ProjectEdit.vue", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/frontend/src/views/projects/components/ProjectTeam.vue", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/frontend/src/test/components/auth.test.js", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/frontend/src/test/components/projects.test.js", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/blueprints/api/timeoff_requests.py", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/frontend/src/test/components/timesheet-requests.test.js", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/frontend/src/views/timesheet/TimesheetRequests.vue", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/frontend/src/stores/timesheet.js", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/models_split/communication.py", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/unit/test_kpi_calculations.py", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/unit/test_task_kpi_calculations.py", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/utils/cost_calculator.py", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/integration/test_communication.py", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/scripts/check_db_schema.py", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/models_split/hr.py", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/models_split/projects.py", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/api/test_clients.py", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/api/test_proposals.py", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/api/test_contracts.py", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/api/test_personnel_rates.py", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/api/test_timeoff_requests.py", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/api/test_performance_reviews.py", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/api/test_forum_topics.py", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/api/test_direct_messages.py", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/api/test_polls.py", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/unit/test_contact_model.py", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/unit/test_skill_model.py", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/unit/test_department_model.py", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/unit/test_task_dependency_model.py", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/unit/test_department_model_fixed.py", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/unit/test_userprofile_model.py", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/unit/test_timesheetentry_model.py", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/unit/test_projectresource_model.py", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/unit/test_product_model.py", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/unit/test_forumcomment_model.py", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/unit/test_userprofile_model_fixed.py", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/unit/test_timesheetentry_model_fixed.py", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/tests/unit/test_projectresource_model_fixed.py"], "size": 9865870, "checkpointCount": 403, "lastModified": 1751095150614}}