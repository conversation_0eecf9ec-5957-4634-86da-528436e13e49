{"id": "shard-0eecf9ec-5957-4634-86da-528436e13e49", "checkpoints": {"0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/frontend/src/views/auth/Login.vue": [{"sourceToolCallRequestId": "ea9a65ef-a15c-4aca-8d8c-29e8589f5cea", "timestamp": 0, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/auth/Login.vue"}}}, {"sourceToolCallRequestId": "6ce0ee3c-226a-4922-aef8-18226ecc54ee", "timestamp": 1751053871036, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/auth/Login.vue"}}}, {"sourceToolCallRequestId": "3767bce1-1015-434d-982d-0b3b2063134a", "timestamp": 1751053871493, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/auth/Login.vue"}}}, {"sourceToolCallRequestId": "6c7bbe45-62d8-4fb9-8bae-e6807c71611b", "timestamp": 1751053871493, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/auth/Login.vue"}}}, {"sourceToolCallRequestId": "2f7d51eb-05c0-4f44-91a1-6d7acc6c28a1", "timestamp": 1751053884296, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/auth/Login.vue"}}}, {"sourceToolCallRequestId": "70eb283f-e6f7-4351-a00e-cc0d8eaf5e93", "timestamp": 1751053884448, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/auth/Login.vue"}}}, {"sourceToolCallRequestId": "080fd654-c2c3-43cd-b263-b92da3924c6e", "timestamp": 1751053884448, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/auth/Login.vue"}}}, {"sourceToolCallRequestId": "fb2a0dc4-46f3-4c20-97d6-01d23ffafa7e", "timestamp": 1751053899012, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/auth/Login.vue"}}}, {"sourceToolCallRequestId": "2b8e6c55-14e1-471c-87c1-0089fc9b9d68", "timestamp": 1751053899152, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/auth/Login.vue"}}}, {"sourceToolCallRequestId": "7178db4b-f66c-4d01-a5a7-74db1c2c85da", "timestamp": 1751053899152, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "lastIncludedInRequestId": "6ca1a9d9-b51d-4ce5-8223-8a4130eedce4", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/auth/Login.vue"}}}], "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/frontend/src/views/projects/Projects.vue": [{"sourceToolCallRequestId": "83034c1e-e2ae-404b-851b-0fc969c6dec0", "timestamp": 0, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/Projects.vue"}}}, {"sourceToolCallRequestId": "ed30945c-4a4d-45e1-892d-496b351d19e5", "timestamp": 1751053925515, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/Projects.vue"}}}, {"sourceToolCallRequestId": "c0aaabf0-06ae-4a5b-a3bf-a613c1d40548", "timestamp": 1751053926004, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/Projects.vue"}}}, {"sourceToolCallRequestId": "33ccfb03-436f-4fd4-9001-c926a44ca14e", "timestamp": 1751053926004, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/Projects.vue"}}}, {"sourceToolCallRequestId": "48508010-c52f-439b-b827-55557e8bb8a2", "timestamp": 1751053938908, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/Projects.vue"}}}, {"sourceToolCallRequestId": "ff22477c-c556-405d-b07d-5be029f3ea0e", "timestamp": 1751053939059, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/Projects.vue"}}}, {"sourceToolCallRequestId": "b0313634-1615-4930-9e78-96e590548372", "timestamp": 1751053939059, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/Projects.vue"}}}, {"sourceToolCallRequestId": "bb289992-ba78-4aee-97b1-cc954ecc2e68", "timestamp": 1751053952501, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/Projects.vue"}}}, {"sourceToolCallRequestId": "eade0e24-0532-4bc2-af69-cfe9cc350230", "timestamp": 1751053952653, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/Projects.vue"}}}, {"sourceToolCallRequestId": "4a5aad95-55b2-4c87-bdec-1618d75001a3", "timestamp": 1751053952653, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/Projects.vue"}}}, {"sourceToolCallRequestId": "2891302e-83d6-4453-a0c5-0f81eae9887c", "timestamp": 1751053964947, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/Projects.vue"}}}, {"sourceToolCallRequestId": "43deb10e-c670-4475-8725-cfc1d5829b9b", "timestamp": 1751053965096, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/Projects.vue"}}}, {"sourceToolCallRequestId": "ffdefe84-19d6-4014-b505-c9d1cd929f6a", "timestamp": 1751053965096, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "lastIncludedInRequestId": "6ca1a9d9-b51d-4ce5-8223-8a4130eedce4", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/Projects.vue"}}}], "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/frontend/src/views/projects/ProjectEdit.vue": [{"sourceToolCallRequestId": "4c781e3d-86bd-41f9-8095-23169531383d", "timestamp": 0, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/ProjectEdit.vue"}}}, {"sourceToolCallRequestId": "df4d31c3-fda1-4196-a6d6-6e5f4fb2e04c", "timestamp": 1751053992843, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/ProjectEdit.vue"}}}, {"sourceToolCallRequestId": "7897bccb-7976-48a6-8fe8-895a6c971a4b", "timestamp": 1751053993318, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/ProjectEdit.vue"}}}, {"sourceToolCallRequestId": "9d25129a-3af4-4e5d-a948-f0c2c7ccb941", "timestamp": 1751053993318, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/ProjectEdit.vue"}}}, {"sourceToolCallRequestId": "90b3eb84-a6e0-4786-8683-80ca4af603b5", "timestamp": 1751054005414, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/ProjectEdit.vue"}}}, {"sourceToolCallRequestId": "ee96deda-f913-42a4-977e-b8a13d4e1c82", "timestamp": 1751054005559, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/ProjectEdit.vue"}}}, {"sourceToolCallRequestId": "04f18a64-bfd3-4211-8cc4-bc6aeb654f39", "timestamp": 1751054005559, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/ProjectEdit.vue"}}}, {"sourceToolCallRequestId": "3964558b-a1b4-48b8-a4e0-ae7cd2611863", "timestamp": 1751054018454, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/ProjectEdit.vue"}}}, {"sourceToolCallRequestId": "f126b5fc-839e-42a5-9ce9-6791f1e5e997", "timestamp": 1751054018594, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/ProjectEdit.vue"}}}, {"sourceToolCallRequestId": "4caf663c-0757-4e7d-ae53-546382585644", "timestamp": 1751054018594, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/ProjectEdit.vue"}}}, {"sourceToolCallRequestId": "d49a2b78-12fd-40b0-a21b-4858312bbf21", "timestamp": 1751054032023, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/ProjectEdit.vue"}}}, {"sourceToolCallRequestId": "4860a58f-767f-4aa7-a4a2-11050fc3dc21", "timestamp": 1751054032166, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/ProjectEdit.vue"}}}, {"sourceToolCallRequestId": "039579b9-518b-4dd0-927d-6dd48c19fafd", "timestamp": 1751054032166, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/ProjectEdit.vue"}}}, {"sourceToolCallRequestId": "a0dc7139-4b18-4516-93b2-464d77c4a4ac", "timestamp": 1751054045205, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/ProjectEdit.vue"}}}, {"sourceToolCallRequestId": "0cf8f51e-531d-4efd-93a9-fb4959e2be0e", "timestamp": 1751054045833, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/ProjectEdit.vue"}}}, {"sourceToolCallRequestId": "52d3a5c2-e782-4101-95c6-451bb7d68f08", "timestamp": 1751054045833, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "lastIncludedInRequestId": "6ca1a9d9-b51d-4ce5-8223-8a4130eedce4", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/ProjectEdit.vue"}}}], "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/frontend/src/views/projects/components/ProjectTeam.vue": [{"sourceToolCallRequestId": "3a14085c-debf-4c0d-984d-4067187926bf", "timestamp": 0, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/components/ProjectTeam.vue"}}}, {"sourceToolCallRequestId": "52bad249-dace-483f-b5b7-56db4fb1d409", "timestamp": 1751054071730, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/components/ProjectTeam.vue"}}}, {"sourceToolCallRequestId": "e0745973-940c-4d40-8120-7ed86a0cd59e", "timestamp": 1751054072141, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/components/ProjectTeam.vue"}}}, {"sourceToolCallRequestId": "a8787113-99e6-4af7-b20d-af680ca1102a", "timestamp": 1751054072141, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/components/ProjectTeam.vue"}}}, {"sourceToolCallRequestId": "065f8cf0-7083-4509-91fe-3747aaa2bead", "timestamp": 1751054085475, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/components/ProjectTeam.vue"}}}, {"sourceToolCallRequestId": "8937b003-1545-4237-8b05-28cfe05e1831", "timestamp": 1751054085622, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/components/ProjectTeam.vue"}}}, {"sourceToolCallRequestId": "1c0b17a2-4517-4781-8aa1-de27ce8917a0", "timestamp": 1751054085622, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "lastIncludedInRequestId": "6ca1a9d9-b51d-4ce5-8223-8a4130eedce4", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/components/ProjectTeam.vue"}}}], "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/frontend/src/test/components/auth.test.js": [{"sourceToolCallRequestId": "7997e51d-088b-4a29-84eb-06ccfeebe532", "timestamp": 0, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "d0fb8213-9dbf-4c3f-813b-65f14cfea978", "timestamp": 1751054138733, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "661562b3-27df-4c33-bf23-2945db12421b", "timestamp": 1751054139117, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "0c796400-4d7f-4de3-b37b-db0fd76d94b1", "timestamp": 1751054139117, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "43e8d461-69bf-4a35-932f-8f58ead39d60", "timestamp": 1751054152484, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "08fa6f9b-90b3-45f8-9c73-2c6d2448de67", "timestamp": 1751054152614, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "5fb8d996-e23d-49ba-bff7-496b5e507413", "timestamp": 1751054152614, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "8c642cf4-c9f4-4b21-975b-04f0fd6f9e52", "timestamp": 1751054166745, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "e41d6f45-5cf8-4e4c-97de-28d6e6497da4", "timestamp": 1751054166884, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "c7dc8134-efa3-47e4-bfa9-1d19634ecf1f", "timestamp": 1751054166884, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "623f8018-96fa-4dcb-9219-b57fb40855b0", "timestamp": 1751054179108, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "10482eab-5bf0-40aa-97cc-e699d5187788", "timestamp": 1751054179241, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "2892cf7f-c680-43d7-a82d-d4e5d05eca9d", "timestamp": 1751054179241, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "5f9c0f8b-0e91-439b-9464-cc3c9256d8df", "timestamp": 1751054193236, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "0742c620-27f2-4b46-91c1-82d8358f0192", "timestamp": 1751054193371, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "0e5d6105-0fcc-4417-8616-0bad0b9bf51c", "timestamp": 1751054193371, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "7b2c5a91-7d6f-458e-a3ba-b9ee8cc42c4b", "timestamp": 1751054209077, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "e7d8eb32-d81b-4e11-af43-b1f8cb8ea086", "timestamp": 1751054209233, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "2f443c2d-0763-4410-9dca-e12592c11eab", "timestamp": 1751054209233, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "d8be3b84-b7a1-4679-9afb-ac7e90eed6aa", "timestamp": 1751054222710, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "9b968a72-6a83-4035-89da-50a249950b2e", "timestamp": 1751054222853, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "21a30057-1865-449e-94bd-42f7da238869", "timestamp": 1751054222853, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "fcbe19ab-34ac-4c7a-bdf5-03eb3f36a570", "timestamp": 1751054235955, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "cfe9d3c3-0177-440c-a3a7-8ab9eb844c35", "timestamp": 1751054236089, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "2bcd240f-8a92-4da0-baf8-f5536011229d", "timestamp": 1751054236089, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "2a56876d-5e79-48f5-9c9d-960ad872dff1", "timestamp": 1751054249093, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "48d2c69b-9633-410d-8285-ba6b59e82b57", "timestamp": 1751054249229, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "496ea28d-a419-4028-81ba-9a102d67c38f", "timestamp": 1751054249229, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "34d2465d-0c05-4108-97c6-680131963ea9", "timestamp": 1751054279597, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "e81b9b31-3a14-4849-a6d2-a7fc405a2ce8", "timestamp": 1751054279745, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "b1e7a523-ca4f-4112-aa0b-e8b61193065b", "timestamp": 1751054279745, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "70fcf46a-4158-45d0-981a-7bc7da14b5bf", "timestamp": 1751054306831, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "3f2cbca8-633c-4b5d-89e5-fafe9c1e8bc0", "timestamp": 1751054306983, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}, {"sourceToolCallRequestId": "5be7c542-9309-4e1e-b35a-0a8a02ea5df5", "timestamp": 1751054306983, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "lastIncludedInRequestId": "6ca1a9d9-b51d-4ce5-8223-8a4130eedce4", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/auth.test.js"}}}], "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/frontend/src/test/components/projects.test.js": [{"sourceToolCallRequestId": "8a687cde-3713-418b-919b-ec244ad820e6", "timestamp": 0, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/projects.test.js"}}}, {"sourceToolCallRequestId": "a92f693d-41b8-4d31-82d8-ee19d01e5e50", "timestamp": 1751054388018, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/projects.test.js"}}}, {"sourceToolCallRequestId": "9e955ae7-25aa-479e-bec4-0413b082be6f", "timestamp": 1751054388418, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/projects.test.js"}}}, {"sourceToolCallRequestId": "ef14c465-3d11-4eb2-b3c4-67eb6cfcf11b", "timestamp": 1751054388418, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "lastIncludedInRequestId": "6ca1a9d9-b51d-4ce5-8223-8a4130eedce4", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/projects.test.js"}}}], "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/blueprints/api/timeoff_requests.py": [{"sourceToolCallRequestId": "61c7c27c-4d25-43f7-a4ac-c0c06b522e9a", "timestamp": 0, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "81a7f286-32b9-41e7-85b3-8b5c79ad1c74", "timestamp": 1751054609461, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "e7b3fbea-74c5-46f6-8271-e1315284c75b", "timestamp": 1751054609847, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "4622217e-1802-4884-9d4a-f2ca90f3d610", "timestamp": 1751054609847, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "b899d527-3163-4772-8324-1e9a7140ecce", "timestamp": 1751054624154, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "923cc512-4e39-4b7e-80f7-851f8d999314", "timestamp": 1751054624292, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "7f9360ef-3e8b-4eb9-b425-b5a9dacdcc27", "timestamp": 1751054624292, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "9af1c06b-b8e4-4fdc-9604-9139f8db668b", "timestamp": 1751054644413, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "5b0cfa32-8242-4e52-a1c0-2798ad95bbeb", "timestamp": 1751054644556, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "81c66914-e1d6-4900-81ea-9e11e1cb8e0f", "timestamp": 1751054644556, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "ecb3e50e-ec2e-404b-af9f-3b11a108c6df", "timestamp": 1751054656165, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "da88dae6-4000-49f1-86a0-525617ec170f", "timestamp": 1751054656303, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "6b458a47-7592-4e5d-910b-c6b737f02481", "timestamp": 1751054656303, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "d5f645ca-9e8f-4c88-9ed0-b9422c03255a", "timestamp": 1751054668346, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "7c0e8ffa-35fd-4bc1-ac66-37e7a300f122", "timestamp": 1751054668488, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "8edd1e18-dd93-4b4d-ae03-dc782badf2cc", "timestamp": 1751054668488, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "5ab6283c-d3be-4d5b-80f9-5ec567de1843", "timestamp": 1751054680074, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "dc31f215-98c7-4158-bbe1-1c81afe9f7b8", "timestamp": 1751054680237, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "9480fa58-936f-4dfb-befa-21ce47348b25", "timestamp": 1751054680237, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "0def1f37-e544-43e8-833f-014db4d4693e", "timestamp": 1751054692051, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "f109c309-4cad-4338-b6af-f6e0c9fd39c3", "timestamp": 1751054692209, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "56e29f6e-b3d9-4835-8f1e-4f64aeaea390", "timestamp": 1751054692209, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "de176f08-1dfe-4101-b936-20115a665468", "timestamp": 1751054704000, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "550be30a-89cc-480f-8a96-636320504746", "timestamp": 1751054704172, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "16f2a05d-d632-469d-baa2-d565e3caaaa8", "timestamp": 1751054704172, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "4f030dc6-339c-48c0-a8f7-62d36e5b06b0", "timestamp": 1751054717866, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "51d81f63-5d9f-4eae-8097-e158fd0b8664", "timestamp": 1751054718010, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "1fac8f04-e64a-457e-b269-b3d8877c0f8c", "timestamp": 1751054718010, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "ce3f70dc-2d37-4d5d-9308-1843e66966ec", "timestamp": 1751054730620, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "b234afa2-32bc-43d3-9b45-d260cb224a37", "timestamp": 1751054730769, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "d3c612b0-ac78-4c63-ada1-b7c30be6686e", "timestamp": 1751054730769, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "718667eb-3482-42fc-93b9-c9a2c84c2099", "timestamp": 1751054747648, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "655c5c1d-4737-4e2c-8d2f-acd7928fae1d", "timestamp": 1751054747787, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "dba6f582-8dad-40e8-97ad-6dc2ada4aa12", "timestamp": 1751054747787, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "lastIncludedInRequestId": "3c4563a7-db69-4398-9c87-7c9826d3d990", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "3c4563a7-db69-4398-9c87-7c9826d3d990", "timestamp": 1751054887425, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "a8159071-95cc-4708-9393-5c434c9af871", "timestamp": 1751054887821, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "10429965-e18e-4737-afc1-97090f20fc74", "timestamp": 1751054887821, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "0d8a3490-e9a3-432f-a3ae-e69995718d15", "timestamp": 1751054901882, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "198c78d2-789b-4140-adec-ff614ba2f614", "timestamp": 1751054902022, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "db0576f7-c1f9-4c6c-ba21-a415fc280c8d", "timestamp": 1751054902022, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "5235b056-adc1-4f3f-b116-0ce2efd33ae1", "timestamp": 1751054915352, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "215bc167-1839-41b1-a3ac-804e6e69b61a", "timestamp": 1751054915491, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "785f04a2-7010-427b-85be-7eadcfdc56fc", "timestamp": 1751054915491, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "1fda6b46-b0bd-4c40-81bf-20d39e80d84d", "timestamp": 1751054930546, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "f10684cb-0b44-4605-b96d-088971fe082c", "timestamp": 1751054930685, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "6115d272-a236-4c1b-b0c4-7be2d25014ae", "timestamp": 1751054930685, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "f69db6d8-5b41-46b5-ae3a-34c585f3a714", "timestamp": 1751054942373, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "f3c4db41-7630-4c17-98e2-38b8c192c5e8", "timestamp": 1751054942511, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "cce86121-8aa8-41e3-ba08-0f45993287e6", "timestamp": 1751054942511, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "822cbc5f-66fc-469e-9ff9-457f29978a6f", "timestamp": 1751054953687, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "f2453121-6d17-47a0-83e0-e8724ffbce1a", "timestamp": 1751054953821, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}, {"sourceToolCallRequestId": "6305c61b-ee5a-4311-a49d-9b4145b93c46", "timestamp": 1751054953821, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timeoff_requests.py"}}}], "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/frontend/src/test/components/timesheet-requests.test.js": [{"sourceToolCallRequestId": "fc355d74-9153-454d-8b93-36b12bd70f94", "timestamp": 1751054848092, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/timesheet-requests.test.js"}}}, {"sourceToolCallRequestId": "c48e1e7c-136e-4e5e-b919-f1a5cf5089f9", "timestamp": 1751055048377, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/timesheet-requests.test.js"}}}, {"sourceToolCallRequestId": "33edaab1-b438-46c5-a700-c75b83b93df2", "timestamp": 1751055048765, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/timesheet-requests.test.js"}}}, {"sourceToolCallRequestId": "555b7a90-4725-438e-baef-ede97eb714e2", "timestamp": 1751055048765, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/timesheet-requests.test.js"}}}, {"sourceToolCallRequestId": "10309401-3a32-4c5e-af3e-4f31b89da9e7", "timestamp": 1751055062708, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/timesheet-requests.test.js"}}}, {"sourceToolCallRequestId": "c7da532e-ca52-40e3-88ad-e836fab645f8", "timestamp": 1751055062841, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/timesheet-requests.test.js"}}}, {"sourceToolCallRequestId": "899de5c1-ebbc-4d65-b670-934163e7451f", "timestamp": 1751055062841, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/timesheet-requests.test.js"}}}, {"sourceToolCallRequestId": "892bea11-fe2c-4107-a74b-20315c2b4cb3", "timestamp": 1751055075055, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/timesheet-requests.test.js"}}}, {"sourceToolCallRequestId": "980432e0-9084-4117-8364-10309682a461", "timestamp": 1751055075197, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/timesheet-requests.test.js"}}}, {"sourceToolCallRequestId": "60f3abc4-ca7f-4e18-8739-5ea6688e0a0f", "timestamp": 1751055075197, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/timesheet-requests.test.js"}}}, {"sourceToolCallRequestId": "38f03c54-0d6a-4642-bd9a-8c9dc9ceb617", "timestamp": 1751055088143, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/timesheet-requests.test.js"}}}, {"sourceToolCallRequestId": "2bd1b268-19d3-4d89-8037-1a9afc694c25", "timestamp": 1751055088280, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/timesheet-requests.test.js"}}}, {"sourceToolCallRequestId": "2fe55b68-a4bd-4b28-8144-ce3a79bcd808", "timestamp": 1751055088280, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/timesheet-requests.test.js"}}}, {"sourceToolCallRequestId": "46ddd608-a073-4910-992e-c6714f214104", "timestamp": 1751055100085, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/timesheet-requests.test.js"}}}, {"sourceToolCallRequestId": "c6caeb82-46ec-448d-b15f-75d935cddd9d", "timestamp": 1751055100219, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/timesheet-requests.test.js"}}}, {"sourceToolCallRequestId": "a6c6e27c-8587-49c8-9898-82a61adbbb48", "timestamp": 1751055100219, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/timesheet-requests.test.js"}}}, {"sourceToolCallRequestId": "5efe751e-e6d2-4fba-8f75-2cd187f0bb9f", "timestamp": 1751055111472, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/timesheet-requests.test.js"}}}, {"sourceToolCallRequestId": "ca7eedd0-310d-4c51-b689-0376b380b695", "timestamp": 1751055111634, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/timesheet-requests.test.js"}}}, {"sourceToolCallRequestId": "a42fa5fd-db31-4d7d-9e37-6c6f6cfe0417", "timestamp": 1751055111634, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/timesheet-requests.test.js"}}}, {"sourceToolCallRequestId": "e916dcff-5b79-4218-a188-750ebfddb572", "timestamp": 1751055124554, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/timesheet-requests.test.js"}}}, {"sourceToolCallRequestId": "93506d80-3665-4d79-a906-eadb44327e7d", "timestamp": 1751055124686, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/timesheet-requests.test.js"}}}, {"sourceToolCallRequestId": "7b1a25a8-efa8-4cdf-b264-a2a509512856", "timestamp": 1751055124686, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/components/timesheet-requests.test.js"}}}], "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/frontend/src/views/timesheet/TimesheetRequests.vue": [{"sourceToolCallRequestId": "14e9bdf7-170b-4223-8bbc-b9f649e0cf51", "timestamp": 0, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/timesheet/TimesheetRequests.vue"}}}, {"sourceToolCallRequestId": "8770e6bb-80fb-4d73-bfc3-3e0b5cff0c42", "timestamp": 1751054859565, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/timesheet/TimesheetRequests.vue"}}}, {"sourceToolCallRequestId": "a51d4cac-665b-45bb-af1c-3b73fbb350a6", "timestamp": 1751054859994, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/timesheet/TimesheetRequests.vue"}}}, {"sourceToolCallRequestId": "b36f1590-d17e-428c-b019-28ecca4a5316", "timestamp": 1751054859994, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/timesheet/TimesheetRequests.vue"}}}, {"sourceToolCallRequestId": "bf702c0e-812f-4948-afce-775710d9f39d", "timestamp": 1751054872716, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/timesheet/TimesheetRequests.vue"}}}, {"sourceToolCallRequestId": "eda5b598-5ff1-4c43-8290-82c7ee362323", "timestamp": 1751054872850, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/timesheet/TimesheetRequests.vue"}}}, {"sourceToolCallRequestId": "8eba98dd-4607-4db1-aef1-b5438627b2f4", "timestamp": 1751054872850, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "lastIncludedInRequestId": "3c4563a7-db69-4398-9c87-7c9826d3d990", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/timesheet/TimesheetRequests.vue"}}}, {"sourceToolCallRequestId": "56b7efec-6974-46e5-8ba4-842881d45d73", "timestamp": 1751054980201, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/timesheet/TimesheetRequests.vue"}}}, {"sourceToolCallRequestId": "f142fc53-9034-4e66-8773-26f4fba38abd", "timestamp": 1751054980339, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/timesheet/TimesheetRequests.vue"}}}, {"sourceToolCallRequestId": "1e46362a-3e85-423c-ba91-dc66ffd03e2c", "timestamp": 1751054980339, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/timesheet/TimesheetRequests.vue"}}}, {"sourceToolCallRequestId": "fe21f232-3173-411d-9749-a641a8a32a2a", "timestamp": 1751055000203, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/timesheet/TimesheetRequests.vue"}}}, {"sourceToolCallRequestId": "af5b2769-c4ea-46a4-9419-44ec94d0a739", "timestamp": 1751055000350, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/timesheet/TimesheetRequests.vue"}}}, {"sourceToolCallRequestId": "525f6d63-5e3d-4a83-ae8e-d49ca76b1173", "timestamp": 1751055000350, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/timesheet/TimesheetRequests.vue"}}}, {"sourceToolCallRequestId": "72f6000d-6239-45b8-9e15-97f18f6e957d", "timestamp": 1751055012801, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/timesheet/TimesheetRequests.vue"}}}, {"sourceToolCallRequestId": "103e7dde-f54e-4bd8-ac03-85834362749f", "timestamp": 1751055012939, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/timesheet/TimesheetRequests.vue"}}}, {"sourceToolCallRequestId": "22b24c67-c1cf-432a-ba9d-119a19247ca0", "timestamp": 1751055012939, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/timesheet/TimesheetRequests.vue"}}}], "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/frontend/src/stores/timesheet.js": [{"sourceToolCallRequestId": "f1661337-5225-4641-bc16-d49af7eab72d", "timestamp": 0, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/stores/timesheet.js"}}}, {"sourceToolCallRequestId": "614b3710-c5a1-4949-856c-b4036bd7295b", "timestamp": 1751054967141, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/stores/timesheet.js"}}}, {"sourceToolCallRequestId": "093b993f-d04d-47b1-96a2-203c8e34628b", "timestamp": 1751054967552, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/stores/timesheet.js"}}}, {"sourceToolCallRequestId": "8d8c7765-b7da-44e4-83a2-4d95b2130ea7", "timestamp": 1751054967552, "conversationId": "0eecf9ec-5957-4634-86da-528436e13e49", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/stores/timesheet.js"}}}]}, "metadata": {"checkpointDocumentIds": ["0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/frontend/src/views/auth/Login.vue", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/frontend/src/views/projects/Projects.vue", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/frontend/src/views/projects/ProjectEdit.vue", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/frontend/src/views/projects/components/ProjectTeam.vue", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/frontend/src/test/components/auth.test.js", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/frontend/src/test/components/projects.test.js", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/blueprints/api/timeoff_requests.py", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/frontend/src/test/components/timesheet-requests.test.js", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/frontend/src/views/timesheet/TimesheetRequests.vue", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/frontend/src/stores/timesheet.js"], "size": 4897323, "checkpointCount": 178, "lastModified": 1751055125493}}