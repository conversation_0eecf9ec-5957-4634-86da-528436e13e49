{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_task_kpi_calculations.py"}, "originalCode": "\"\"\"\nTest per i calcoli KPI basati sui nuovi campi dei task.\n\"\"\"\nimport pytest\nfrom datetime import datetime, date, timedelta\nfrom decimal import Decimal\n\nfrom models import Project, User, Task, TimesheetEntry, PersonnelRate\nfrom utils.cost_calculator import calculate_project_kpis\nfrom app import db\n\n\nclass TestTaskKPICalculations:\n    \"\"\"Test per i calcoli KPI basati su start_date e estimated_hours.\"\"\"\n\n    def test_task_estimation_accuracy_kpi(self, app, test_project, test_user):\n        \"\"\"Test KPI precisione stime task.\"\"\"\n        with app.app_context():\n            project = Project.query.get(test_project)\n            user = test_user\n\n            # Crea rate per l'utente\n            rate = PersonnelRate(\n                user_id=user.id,\n                hourly_rate=50.0,  # 400/8 ore = 50 €/ora\n                start_date=date.today() - timedelta(days=30)\n            )\n            db.session.add(rate)\n\n            # Crea task con stime diverse\n            task1 = Task(\n                name=\"Task Accurato\",\n                project_id=project.id,\n                assignee_id=user.id,\n                start_date=date.today() - timedelta(days=5),\n                due_date=date.today() - timedelta(days=1),\n                estimated_hours=32.0,\n                status=\"completed\"\n            )\n\n            task2 = Task(\n                name=\"Task Sottostimato\",\n                project_id=project.id,\n                assignee_id=user.id,\n                start_date=date.today() - timedelta(days=10),\n                due_date=date.today() - timedelta(days=6),\n                estimated_hours=16.0,\n                status=\"completed\"\n            )\n\n            db.session.add_all([task1, task2])\n            db.session.commit()\n\n            # Aggiungi timesheet per task1 (stima accurata: 32h stimate, 32h effettive)\n            for i in range(4):\n                timesheet = TimesheetEntry(\n                    user_id=user.id,\n                    project_id=project.id,\n                    task_id=task1.id,\n                    date=date.today() - timedelta(days=5-i),\n                    hours=8.0\n                )\n                db.session.add(timesheet)\n\n            # Aggiungi timesheet per task2 (sottostimato: 16h stimate, 24h effettive)\n            for i in range(3):\n                timesheet = TimesheetEntry(\n                    user_id=user.id,\n                    project_id=project.id,\n                    task_id=task2.id,\n                    date=date.today() - timedelta(days=10-i),\n                    hours=8.0\n                )\n                db.session.add(timesheet)\n\n            db.session.commit()\n\n            # Verifica proprietà calcolate dei task\n            assert task1.actual_hours == 32.0\n            assert task1.hours_variance == 0.0  # Perfetto\n            assert task1.hours_efficiency == 100.0\n\n            assert task2.actual_hours == 24.0\n            assert task2.hours_variance == 8.0  # 8 ore in più\n            assert abs(task2.hours_efficiency - 66.67) < 0.1  # (16/24)*100\n\n    def test_task_timeline_adherence_kpi(self, app, test_project, test_user):\n        \"\"\"Test KPI rispetto timeline task.\"\"\"\n        with app.app_context():\n            project = Project.query.get(test_project)\n            user = test_user\n\n            # Task completato in tempo\n            task_on_time = Task(\n                name=\"Task In Tempo\",\n                project_id=project.id,\n                assignee_id=user.id,\n                start_date=date.today() - timedelta(days=7),\n                due_date=date.today() - timedelta(days=1),\n                estimated_hours=40.0,\n                status=\"completed\"\n            )\n\n            # Task in ritardo\n            task_late = Task(\n                name=\"Task In Ritardo\",\n                project_id=project.id,\n                assignee_id=user.id,\n                start_date=date.today() - timedelta(days=14),\n                due_date=date.today() - timedelta(days=8),  # Scaduto 8 giorni fa\n                estimated_hours=32.0,\n                status=\"in-progress\"  # Ancora non completato\n            )\n\n            db.session.add_all([task_on_time, task_late])\n            db.session.commit()\n\n            # Verifica durata pianificata\n            assert task_on_time.duration_days == 7  # 7 giorni pianificati\n            assert task_late.duration_days == 7     # 7 giorni pianificati\n\n            # Verifica che il task in ritardo sia identificabile\n            today = date.today()\n            assert task_late.due_date < today and task_late.status != \"completed\"\n\n    def test_project_estimation_accuracy_aggregate(self, app, test_project, test_user):\n        \"\"\"Test KPI aggregato precisione stime a livello progetto.\"\"\"\n        with app.app_context():\n            project = Project.query.get(test_project)\n            user = test_user\n\n            # Setup rate\n            rate = PersonnelRate(\n                user_id=user.id,\n                daily_rate=400.0,\n                valid_from=date.today() - timedelta(days=30)\n            )\n            db.session.add(rate)\n\n            # Crea diversi task con stime diverse\n            tasks_data = [\n                {\"name\": \"Task 1\", \"estimated\": 16.0, \"actual_hours\": 16.0},  # Perfetto\n                {\"name\": \"Task 2\", \"estimated\": 24.0, \"actual_hours\": 20.0},  # Sovrastimato\n                {\"name\": \"Task 3\", \"estimated\": 32.0, \"actual_hours\": 40.0},  # Sottostimato\n                {\"name\": \"Task 4\", \"estimated\": 8.0, \"actual_hours\": 8.0},    # Perfetto\n            ]\n\n            for i, task_data in enumerate(tasks_data):\n                task = Task(\n                    name=task_data[\"name\"],\n                    project_id=project.id,\n                    assignee_id=user.id,\n                    start_date=date.today() - timedelta(days=20-i*5),\n                    due_date=date.today() - timedelta(days=15-i*5),\n                    estimated_hours=task_data[\"estimated\"],\n                    status=\"completed\"\n                )\n                db.session.add(task)\n                db.session.commit()\n\n                # Aggiungi timesheet per raggiungere le ore effettive\n                hours_per_day = 8.0\n                days_needed = int(task_data[\"actual_hours\"] / hours_per_day)\n                remaining_hours = task_data[\"actual_hours\"] % hours_per_day\n\n                for day in range(days_needed):\n                    timesheet = TimesheetEntry(\n                        user_id=user.id,\n                        project_id=project.id,\n                        task_id=task.id,\n                        date=date.today() - timedelta(days=20-i*5-day),\n                        hours=hours_per_day\n                    )\n                    db.session.add(timesheet)\n\n                if remaining_hours > 0:\n                    timesheet = TimesheetEntry(\n                        user_id=user.id,\n                        project_id=project.id,\n                        task_id=task.id,\n                        date=date.today() - timedelta(days=20-i*5-days_needed),\n                        hours=remaining_hours\n                    )\n                    db.session.add(timesheet)\n\n            db.session.commit()\n\n            # Calcola KPI del progetto\n            project.is_billable = True\n            project.client_daily_rate = 600.0\n            db.session.commit()\n\n            kpis = calculate_project_kpis(project.id)\n\n            # Verifica che i KPI includano informazioni sui task\n            assert kpis is not None\n            assert 'utilization_rate' in kpis\n            assert 'cost_per_hour' in kpis\n\n            # Il costo per ora dovrebbe riflettere l'efficienza delle stime\n            # Se le stime sono accurate, il costo per ora dovrebbe essere vicino al rate/8\n            expected_cost_per_hour = 400.0 / 8.0  # 50€/ora\n\n            # Se non ci sono timesheet, il cost_per_hour potrebbe essere 0\n            # In questo caso, verifichiamo solo che il KPI sia calcolato\n            if kpis['cost_per_hour'] > 0:\n                assert abs(kpis['cost_per_hour'] - expected_cost_per_hour) < 20.0\n            else:\n                # Se non ci sono timesheet, il costo è 0 - questo è accettabile\n                assert kpis['cost_per_hour'] == 0.0\n\n    def test_task_productivity_metrics(self, app, test_project, test_user):\n        \"\"\"Test metriche produttività basate sui task.\"\"\"\n        with app.app_context():\n            project = Project.query.get(test_project)\n            user = test_user\n\n            # Crea task con diverse efficienze\n            high_efficiency_task = Task(\n                name=\"Task Efficiente\",\n                project_id=project.id,\n                assignee_id=user.id,\n                estimated_hours=40.0,\n                status=\"completed\"\n            )\n\n            low_efficiency_task = Task(\n                name=\"Task Inefficiente\",\n                project_id=project.id,\n                assignee_id=user.id,\n                estimated_hours=20.0,\n                status=\"completed\"\n            )\n\n            db.session.add_all([high_efficiency_task, low_efficiency_task])\n            db.session.commit()\n\n            # Task efficiente: 40h stimate, 35h effettive (114% efficienza)\n            for i in range(4):\n                timesheet = TimesheetEntry(\n                    user_id=user.id,\n                    project_id=project.id,\n                    task_id=high_efficiency_task.id,\n                    date=date.today() - timedelta(days=i),\n                    hours=8.0 if i < 3 else 3.0  # 8+8+8+3 = 27h\n                )\n                db.session.add(timesheet)\n\n            # Aggiungi altre 8 ore per arrivare a 35\n            timesheet = TimesheetEntry(\n                user_id=user.id,\n                project_id=project.id,\n                task_id=high_efficiency_task.id,\n                date=date.today() - timedelta(days=4),\n                hours=8.0\n            )\n            db.session.add(timesheet)\n\n            # Task inefficiente: 20h stimate, 30h effettive (67% efficienza)\n            for i in range(4):\n                timesheet = TimesheetEntry(\n                    user_id=user.id,\n                    project_id=project.id,\n                    task_id=low_efficiency_task.id,\n                    date=date.today() - timedelta(days=i+5),\n                    hours=7.5  # 7.5*4 = 30h\n                )\n                db.session.add(timesheet)\n\n            db.session.commit()\n\n            # Verifica metriche\n            assert high_efficiency_task.actual_hours == 35.0\n            assert abs(high_efficiency_task.hours_efficiency - 114.29) < 0.1\n\n            assert low_efficiency_task.actual_hours == 30.0\n            assert abs(low_efficiency_task.hours_efficiency - 66.67) < 0.1\n", "modifiedCode": "\"\"\"\nTest per i calcoli KPI basati sui nuovi campi dei task.\n\"\"\"\nimport pytest\nfrom datetime import datetime, date, timedelta\nfrom decimal import Decimal\n\nfrom models import Project, User, Task, TimesheetEntry, PersonnelRate\nfrom utils.cost_calculator import calculate_project_kpis\nfrom app import db\n\n\nclass TestTaskKPICalculations:\n    \"\"\"Test per i calcoli KPI basati su start_date e estimated_hours.\"\"\"\n\n    def test_task_estimation_accuracy_kpi(self, app, test_project, test_user):\n        \"\"\"Test KPI precisione stime task.\"\"\"\n        with app.app_context():\n            project = Project.query.get(test_project)\n            user = test_user\n\n            # Crea rate per l'utente\n            rate = PersonnelRate(\n                user_id=user.id,\n                hourly_rate=50.0,  # 400/8 ore = 50 €/ora\n                start_date=date.today() - timedelta(days=30)\n            )\n            db.session.add(rate)\n\n            # Crea task con stime diverse\n            task1 = Task(\n                name=\"Task Accurato\",\n                project_id=project.id,\n                assignee_id=user.id,\n                start_date=date.today() - timedelta(days=5),\n                due_date=date.today() - timedelta(days=1),\n                estimated_hours=32.0,\n                status=\"completed\"\n            )\n\n            task2 = Task(\n                name=\"Task Sottostimato\",\n                project_id=project.id,\n                assignee_id=user.id,\n                start_date=date.today() - timedelta(days=10),\n                due_date=date.today() - timedelta(days=6),\n                estimated_hours=16.0,\n                status=\"completed\"\n            )\n\n            db.session.add_all([task1, task2])\n            db.session.commit()\n\n            # Aggiungi timesheet per task1 (stima accurata: 32h stimate, 32h effettive)\n            for i in range(4):\n                timesheet = TimesheetEntry(\n                    user_id=user.id,\n                    project_id=project.id,\n                    task_id=task1.id,\n                    date=date.today() - timedelta(days=5-i),\n                    hours=8.0\n                )\n                db.session.add(timesheet)\n\n            # Aggiungi timesheet per task2 (sottostimato: 16h stimate, 24h effettive)\n            for i in range(3):\n                timesheet = TimesheetEntry(\n                    user_id=user.id,\n                    project_id=project.id,\n                    task_id=task2.id,\n                    date=date.today() - timedelta(days=10-i),\n                    hours=8.0\n                )\n                db.session.add(timesheet)\n\n            db.session.commit()\n\n            # Verifica proprietà calcolate dei task\n            assert task1.actual_hours == 32.0\n            assert task1.hours_variance == 0.0  # Perfetto\n            assert task1.hours_efficiency == 100.0\n\n            assert task2.actual_hours == 24.0\n            assert task2.hours_variance == 8.0  # 8 ore in più\n            assert abs(task2.hours_efficiency - 66.67) < 0.1  # (16/24)*100\n\n    def test_task_timeline_adherence_kpi(self, app, test_project, test_user):\n        \"\"\"Test KPI rispetto timeline task.\"\"\"\n        with app.app_context():\n            project = Project.query.get(test_project)\n            user = test_user\n\n            # Task completato in tempo\n            task_on_time = Task(\n                name=\"Task In Tempo\",\n                project_id=project.id,\n                assignee_id=user.id,\n                start_date=date.today() - timedelta(days=7),\n                due_date=date.today() - timedelta(days=1),\n                estimated_hours=40.0,\n                status=\"completed\"\n            )\n\n            # Task in ritardo\n            task_late = Task(\n                name=\"Task In Ritardo\",\n                project_id=project.id,\n                assignee_id=user.id,\n                start_date=date.today() - timedelta(days=14),\n                due_date=date.today() - timedelta(days=8),  # Scaduto 8 giorni fa\n                estimated_hours=32.0,\n                status=\"in-progress\"  # Ancora non completato\n            )\n\n            db.session.add_all([task_on_time, task_late])\n            db.session.commit()\n\n            # Verifica durata pianificata\n            assert task_on_time.duration_days == 7  # 7 giorni pianificati\n            assert task_late.duration_days == 7     # 7 giorni pianificati\n\n            # Verifica che il task in ritardo sia identificabile\n            today = date.today()\n            assert task_late.due_date < today and task_late.status != \"completed\"\n\n    def test_project_estimation_accuracy_aggregate(self, app, test_project, test_user):\n        \"\"\"Test KPI aggregato precisione stime a livello progetto.\"\"\"\n        with app.app_context():\n            project = Project.query.get(test_project)\n            user = test_user\n\n            # Setup rate\n            rate = PersonnelRate(\n                user_id=user.id,\n                daily_rate=400.0,\n                valid_from=date.today() - timedelta(days=30)\n            )\n            db.session.add(rate)\n\n            # Crea diversi task con stime diverse\n            tasks_data = [\n                {\"name\": \"Task 1\", \"estimated\": 16.0, \"actual_hours\": 16.0},  # Perfetto\n                {\"name\": \"Task 2\", \"estimated\": 24.0, \"actual_hours\": 20.0},  # Sovrastimato\n                {\"name\": \"Task 3\", \"estimated\": 32.0, \"actual_hours\": 40.0},  # Sottostimato\n                {\"name\": \"Task 4\", \"estimated\": 8.0, \"actual_hours\": 8.0},    # Perfetto\n            ]\n\n            for i, task_data in enumerate(tasks_data):\n                task = Task(\n                    name=task_data[\"name\"],\n                    project_id=project.id,\n                    assignee_id=user.id,\n                    start_date=date.today() - timedelta(days=20-i*5),\n                    due_date=date.today() - timedelta(days=15-i*5),\n                    estimated_hours=task_data[\"estimated\"],\n                    status=\"completed\"\n                )\n                db.session.add(task)\n                db.session.commit()\n\n                # Aggiungi timesheet per raggiungere le ore effettive\n                hours_per_day = 8.0\n                days_needed = int(task_data[\"actual_hours\"] / hours_per_day)\n                remaining_hours = task_data[\"actual_hours\"] % hours_per_day\n\n                for day in range(days_needed):\n                    timesheet = TimesheetEntry(\n                        user_id=user.id,\n                        project_id=project.id,\n                        task_id=task.id,\n                        date=date.today() - timedelta(days=20-i*5-day),\n                        hours=hours_per_day\n                    )\n                    db.session.add(timesheet)\n\n                if remaining_hours > 0:\n                    timesheet = TimesheetEntry(\n                        user_id=user.id,\n                        project_id=project.id,\n                        task_id=task.id,\n                        date=date.today() - timedelta(days=20-i*5-days_needed),\n                        hours=remaining_hours\n                    )\n                    db.session.add(timesheet)\n\n            db.session.commit()\n\n            # Calcola KPI del progetto\n            project.is_billable = True\n            project.client_daily_rate = 600.0\n            db.session.commit()\n\n            kpis = calculate_project_kpis(project.id)\n\n            # Verifica che i KPI includano informazioni sui task\n            assert kpis is not None\n            assert 'utilization_rate' in kpis\n            assert 'cost_per_hour' in kpis\n\n            # Il costo per ora dovrebbe riflettere l'efficienza delle stime\n            # Se le stime sono accurate, il costo per ora dovrebbe essere vicino al rate/8\n            expected_cost_per_hour = 400.0 / 8.0  # 50€/ora\n\n            # Se non ci sono timesheet, il cost_per_hour potrebbe essere 0\n            # In questo caso, verifichiamo solo che il KPI sia calcolato\n            if kpis['cost_per_hour'] > 0:\n                assert abs(kpis['cost_per_hour'] - expected_cost_per_hour) < 20.0\n            else:\n                # Se non ci sono timesheet, il costo è 0 - questo è accettabile\n                assert kpis['cost_per_hour'] == 0.0\n\n    def test_task_productivity_metrics(self, app, test_project, test_user):\n        \"\"\"Test metriche produttività basate sui task.\"\"\"\n        with app.app_context():\n            project = Project.query.get(test_project)\n            user = test_user\n\n            # Crea task con diverse efficienze\n            high_efficiency_task = Task(\n                name=\"Task Efficiente\",\n                project_id=project.id,\n                assignee_id=user.id,\n                estimated_hours=40.0,\n                status=\"completed\"\n            )\n\n            low_efficiency_task = Task(\n                name=\"Task Inefficiente\",\n                project_id=project.id,\n                assignee_id=user.id,\n                estimated_hours=20.0,\n                status=\"completed\"\n            )\n\n            db.session.add_all([high_efficiency_task, low_efficiency_task])\n            db.session.commit()\n\n            # Task efficiente: 40h stimate, 35h effettive (114% efficienza)\n            for i in range(4):\n                timesheet = TimesheetEntry(\n                    user_id=user.id,\n                    project_id=project.id,\n                    task_id=high_efficiency_task.id,\n                    date=date.today() - timedelta(days=i),\n                    hours=8.0 if i < 3 else 3.0  # 8+8+8+3 = 27h\n                )\n                db.session.add(timesheet)\n\n            # Aggiungi altre 8 ore per arrivare a 35\n            timesheet = TimesheetEntry(\n                user_id=user.id,\n                project_id=project.id,\n                task_id=high_efficiency_task.id,\n                date=date.today() - timedelta(days=4),\n                hours=8.0\n            )\n            db.session.add(timesheet)\n\n            # Task inefficiente: 20h stimate, 30h effettive (67% efficienza)\n            for i in range(4):\n                timesheet = TimesheetEntry(\n                    user_id=user.id,\n                    project_id=project.id,\n                    task_id=low_efficiency_task.id,\n                    date=date.today() - timedelta(days=i+5),\n                    hours=7.5  # 7.5*4 = 30h\n                )\n                db.session.add(timesheet)\n\n            db.session.commit()\n\n            # Verifica metriche\n            assert high_efficiency_task.actual_hours == 35.0\n            assert abs(high_efficiency_task.hours_efficiency - 114.29) < 0.1\n\n            assert low_efficiency_task.actual_hours == 30.0\n            assert abs(low_efficiency_task.hours_efficiency - 66.67) < 0.1\n"}