{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_opportunity_model.py"}, "modifiedCode": "\"\"\"Unit tests for Opportunity model.\"\"\"\nimport pytest\nfrom models import Opportunity, Client, User\nfrom extensions import db\n\nclass TestOpportunityModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n        \n        self.client = Client.query.first()\n        if not self.client:\n            self.client = Client(name='Test Client', email='<EMAIL>')\n            db.session.add(self.client)\n            db.session.commit()\n\n    def test_opportunity_creation_basic(self):\n        opportunity = Opportunity(\n            name='Test Opportunity',\n            client_id=self.client.id,\n            value=50000.0,\n            stage='prospecting'\n        )\n        db.session.add(opportunity)\n        db.session.commit()\n        \n        assert opportunity.id is not None\n        assert opportunity.name == 'Test Opportunity'\n        assert opportunity.value == 50000.0\n\n    def test_opportunity_deletion(self):\n        opportunity = Opportunity(name='To Delete', client_id=self.client.id)\n        db.session.add(opportunity)\n        db.session.commit()\n        opp_id = opportunity.id\n        \n        db.session.delete(opportunity)\n        db.session.commit()\n        \n        deleted = Opportunity.query.get(opp_id)\n        assert deleted is None\n"}