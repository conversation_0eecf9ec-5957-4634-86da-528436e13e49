{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_personnelrate_model.py"}, "modifiedCode": "\"\"\"\nUnit tests for PersonnelRate model.\nTests business logic, validations, and model functionality.\n\"\"\"\n\nimport pytest\nfrom datetime import datetime, date\nfrom models import PersonnelRate, User\nfrom extensions import db\n\n\nclass TestPersonnelRateModel:\n    \"\"\"Test suite for PersonnelRate model unit tests\"\"\"\n\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        \"\"\"Setup test data for each test method\"\"\"\n        self.app = app\n        self.user = test_user\n\n    def test_personnelrate_creation_basic(self):\n        \"\"\"Test basic personnel rate creation\"\"\"\n        rate = PersonnelRate(\n            user_id=self.user.id,\n            daily_rate=500.0,\n            valid_from=date.today(),\n            currency='EUR'\n        )\n        \n        db.session.add(rate)\n        db.session.commit()\n        \n        assert rate.id is not None\n        assert rate.user_id == self.user.id\n        assert rate.daily_rate == 500.0\n        assert rate.valid_from == date.today()\n        assert rate.currency == 'EUR'\n\n    def test_personnelrate_relationships(self):\n        \"\"\"Test relationships with User model\"\"\"\n        rate = PersonnelRate(\n            user_id=self.user.id,\n            daily_rate=400.0,\n            valid_from=date.today()\n        )\n        \n        db.session.add(rate)\n        db.session.commit()\n        \n        # Test relationship exists (if defined in model)\n        assert rate.user_id == self.user.id\n\n    def test_personnelrate_date_ranges(self):\n        \"\"\"Test date range functionality\"\"\"\n        rate = PersonnelRate(\n            user_id=self.user.id,\n            daily_rate=600.0,\n            valid_from=date(2024, 1, 1),\n            valid_to=date(2024, 12, 31)\n        )\n        \n        db.session.add(rate)\n        db.session.commit()\n        \n        assert rate.valid_from == date(2024, 1, 1)\n        assert rate.valid_to == date(2024, 12, 31)\n\n    def test_personnelrate_currency_handling(self):\n        \"\"\"Test currency field\"\"\"\n        currencies = ['EUR', 'USD', 'GBP']\n        \n        rates = []\n        for i, currency in enumerate(currencies):\n            rate = PersonnelRate(\n                user_id=self.user.id,\n                daily_rate=100.0 + i * 50,\n                valid_from=date(2024, i+1, 1),\n                currency=currency\n            )\n            rates.append(rate)\n        \n        db.session.add_all(rates)\n        db.session.commit()\n        \n        for rate, expected_currency in zip(rates, currencies):\n            assert rate.currency == expected_currency\n\n    def test_personnelrate_notes_functionality(self):\n        \"\"\"Test notes field\"\"\"\n        rate = PersonnelRate(\n            user_id=self.user.id,\n            daily_rate=550.0,\n            valid_from=date.today(),\n            notes='Rate increase due to promotion'\n        )\n        \n        db.session.add(rate)\n        db.session.commit()\n        \n        assert rate.notes == 'Rate increase due to promotion'\n\n    def test_personnelrate_timestamps(self):\n        \"\"\"Test automatic timestamp handling\"\"\"\n        rate = PersonnelRate(\n            user_id=self.user.id,\n            daily_rate=450.0,\n            valid_from=date.today()\n        )\n        \n        db.session.add(rate)\n        db.session.commit()\n        \n        assert rate.created_at is not None\n        assert isinstance(rate.created_at, datetime)\n\n    def test_personnelrate_query_by_user(self):\n        \"\"\"Test querying rates by user\"\"\"\n        rate = PersonnelRate(\n            user_id=self.user.id,\n            daily_rate=350.0,\n            valid_from=date.today()\n        )\n        \n        db.session.add(rate)\n        db.session.commit()\n        \n        user_rates = PersonnelRate.query.filter_by(user_id=self.user.id).all()\n        assert len(user_rates) >= 1\n        assert rate in user_rates\n\n    def test_personnelrate_current_rate(self):\n        \"\"\"Test querying current rate (no valid_to)\"\"\"\n        current_rate = PersonnelRate(\n            user_id=self.user.id,\n            daily_rate=700.0,\n            valid_from=date.today(),\n            valid_to=None  # Current rate\n        )\n        \n        db.session.add(current_rate)\n        db.session.commit()\n        \n        assert current_rate.valid_to is None\n\n    def test_personnelrate_update_operations(self):\n        \"\"\"Test rate update operations\"\"\"\n        rate = PersonnelRate(\n            user_id=self.user.id,\n            daily_rate=300.0,\n            valid_from=date.today(),\n            currency='EUR'\n        )\n        \n        db.session.add(rate)\n        db.session.commit()\n        \n        # Update rate\n        rate.daily_rate = 400.0\n        rate.currency = 'USD'\n        rate.notes = 'Updated rate'\n        \n        db.session.commit()\n        \n        updated_rate = PersonnelRate.query.get(rate.id)\n        assert updated_rate.daily_rate == 400.0\n        assert updated_rate.currency == 'USD'\n        assert updated_rate.notes == 'Updated rate'\n\n    def test_personnelrate_deletion(self):\n        \"\"\"Test rate deletion\"\"\"\n        rate = PersonnelRate(\n            user_id=self.user.id,\n            daily_rate=250.0,\n            valid_from=date.today()\n        )\n        \n        db.session.add(rate)\n        db.session.commit()\n        rate_id = rate.id\n        \n        db.session.delete(rate)\n        db.session.commit()\n        \n        deleted_rate = PersonnelRate.query.get(rate_id)\n        assert deleted_rate is None\n"}