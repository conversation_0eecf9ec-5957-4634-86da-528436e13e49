{"version": 1, "lastUpdated": 1751055129556, "shards": {"shard-f331798c-cd73-4dc9-b150-f4cffdfb1f14": {"checkpointDocumentIds": ["f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/blueprints/api/projects.py", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/static/swagger/swagger.json", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/blueprints/api/tasks.py", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/blueprints/api/base.py", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/tests/api/test_tasks.py", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/blueprints/dashboard.py", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/blueprints/api/resources.py", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/tests/api/test_resources.py", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/utils/permissions.py", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/blueprints/api/task_dependencies.py", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/tests/api/test_task_dependencies.py", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/blueprints/api/project_kpis.py", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/tests/api/test_project_kpis.py", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/tests/api/test_projects.py", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/static/js/components.js", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/templates/components/toast.html", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/templates/components/modal.html", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/templates/components/loader.html", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/templates/base.html", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/templates/projects/index.html", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/blueprints/projects.py", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/static/js/alpine-init.js", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/static/js/utils.js", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/blueprints/api/kpis.py", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/tests/api/test_kpis.py", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/tests/conftest.py", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/models.py", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/templates/projects/create.html", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/templates/projects/view.html", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/seed_data.py", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/templates/projects/edit.html", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/utils/cost_calculator.py", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/db_update_cost_management.py", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/blueprints/admin.py", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/templates/admin/kpi_templates.html", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/app.py", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/templates/components/sidebar.html", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/templates/projects/expenses.html", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/templates/projects/kpi_config.html", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/scripts/seed_kpi_templates.py", "f331798c-cd73-4dc9-b150-f4cffdfb1f14:/home/<USER>/workspace/tests/unit/test_kpi_calculations.py"], "size": 21913987, "checkpointCount": 393, "lastModified": 1748192836659}, "shard-d1979f69-469e-4d68-9b82-aa95de57cefc": {"checkpointDocumentIds": ["d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/templates/projects/view.html", "d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/blueprints/projects.py", "d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/models.py", "d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/db_update.py", "d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/add_task_fields.sql", "d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/tests/api/test_tasks.py", "d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/tests/integration/test_project_crud.py", "d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/tests/unit/test_task_kpi_calculations.py", "d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/templates/personnel/index.html", "d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/templates/personnel/profile.html", "d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/templates/personnel/skills.html", "d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/templates/personnel/admin.html", "d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/blueprints/personnel.py", "d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/templates/personnel/edit_profile.html", "d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/templates/personnel/directory.html", "d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/templates/personnel/orgchart.html", "d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/templates/personnel/_department_node.html", "d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/templates/personnel/profile_completion.html", "d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/ai_services.py", "d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/utils/cv_parser.py", "d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/db_update_cv.py", "d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/templates/personnel/generated_cv.html", "d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/utils/filters.py", "d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/test_cv.txt", "d1979f69-469e-4d68-9b82-aa95de57cefc:/home/<USER>/workspace/templates/personnel/cv_pdf_template.html"], "size": 17271241, "checkpointCount": 137, "lastModified": 1748208267554}, "shard-b78c4e9e-00fa-421d-9c6d-2d78753d686b": {"checkpointDocumentIds": ["b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/static/css/tailwind.css", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/static/css/cv-styles.css", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/templates/projects/view.html", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/templates/personnel/generated_cv.html", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/templates/personnel/cv_pdf_template.html", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/static/css/components.css", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/templates/base.html", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/docs/css-architecture.md", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/blueprints/personnel.py", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/templates/personnel/edit_skill.html", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/templates/personnel/skills.html", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/templates/personnel/edit_profile.html", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/utils/image_utils.py", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/templates/personnel/skills_matrix.html", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/templates/personnel/profile.html", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/migrate_skills.py", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/models.py", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/create_complete_profiles.py", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/templates/personnel/index.html", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/templates/components/sidebar.html", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/templates/personnel/orgchart.html", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/templates/personnel/departments/index.html", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/templates/personnel/departments/create.html", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/templates/personnel/departments/edit.html", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/templates/personnel/departments/dashboard.html", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/templates/personnel/departments/manage_employees.html", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/templates/components/toast.html", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/templates/components/notification.html", "b78c4e9e-00fa-421d-9c6d-2d78753d686b:/home/<USER>/workspace/static/js/spa-navigation.js"], "size": 8273586, "checkpointCount": 155, "lastModified": 1748294260449}, "shard-92082ae1-d76e-4e99-a0d3-7673aa7d0d81": {"checkpointDocumentIds": ["92082ae1-d76e-4e99-a0d3-7673aa7d0d81:/home/<USER>/workspace/db_update.py", "92082ae1-d76e-4e99-a0d3-7673aa7d0d81:/home/<USER>/workspace/specs/todo_7_26_maggio.txt", "92082ae1-d76e-4e99-a0d3-7673aa7d0d81:/home/<USER>/workspace/templates/personnel/orgchart.html", "92082ae1-d76e-4e99-a0d3-7673aa7d0d81:/home/<USER>/workspace/templates/personnel/_department_chart_node.html", "92082ae1-d76e-4e99-a0d3-7673aa7d0d81:/home/<USER>/workspace/specs/task_7_status.txt", "92082ae1-d76e-4e99-a0d3-7673aa7d0d81:/home/<USER>/workspace/static/js/stagewise-config.js", "92082ae1-d76e-4e99-a0d3-7673aa7d0d81:/home/<USER>/workspace/static/js/alpine-init.js", "92082ae1-d76e-4e99-a0d3-7673aa7d0d81:/home/<USER>/workspace/templates/base.html", "92082ae1-d76e-4e99-a0d3-7673aa7d0d81:/home/<USER>/workspace/app.py"], "size": 460254, "checkpointCount": 30, "lastModified": 1748375379643}, "shard-************************************": {"checkpointDocumentIds": ["************************************:/home/<USER>/workspace/specs/task_16_vue_refactoring.txt", "************************************:/home/<USER>/workspace/blueprints/api/personnel.py", "************************************:/home/<USER>/workspace/blueprints/api/base.py", "************************************:/home/<USER>/workspace/static/swagger/swagger.json", "************************************:/home/<USER>/workspace/tests/api/test_personnel.py", "************************************:/home/<USER>/workspace/tests/conftest.py", "************************************:/home/<USER>/workspace/app.py", "************************************:/home/<USER>/workspace/blueprints/api/dashboard.py", "************************************:/home/<USER>/workspace/tests/api/test_dashboard.py", "************************************:/home/<USER>/workspace/blueprints/api/auth.py", "************************************:/home/<USER>/workspace/templates/spa.html", "************************************:/home/<USER>/workspace/tests/api/test_auth.py", "************************************:/home/<USER>/workspace/tests/api/test_kpis.py", "************************************:/home/<USER>/workspace/static/css/brand-variables.css", "************************************:/home/<USER>/workspace/tailwind.config.js", "************************************:/home/<USER>/workspace/static/js/vue/stores/brand.js", "************************************:/home/<USER>/workspace/static/js/vue/main.js", "************************************:/home/<USER>/workspace/static/js/vue/stores/auth.js", "************************************:/home/<USER>/workspace/static/js/vue/stores/app.js", "************************************:/home/<USER>/workspace/static/js/vue/router/index.js", "************************************:/home/<USER>/workspace/static/js/vue/components/App.vue", "************************************:/home/<USER>/workspace/static/js/vue/components/layout/Sidebar.vue", "************************************:/home/<USER>/workspace/static/js/vue/components/layout/SidebarItem.vue", "************************************:/home/<USER>/workspace/static/js/vue/components/layout/SidebarGroup.vue", "************************************:/home/<USER>/workspace/static/js/vue/components/layout/TopNavigation.vue", "************************************:/home/<USER>/workspace/static/js/vue/views/Dashboard.vue", "************************************:/home/<USER>/workspace/static/js/vue/components/ui/LoadingOverlay.vue", "************************************:/home/<USER>/workspace/static/js/vue/components/ui/NotificationContainer.vue", "************************************:/home/<USER>/workspace/static/js/vue/components/ui/ModalContainer.vue", "************************************:/home/<USER>/workspace/static/js/vue/components/dashboard/StatsCard.vue", "************************************:/home/<USER>/workspace/static/js/vue/components/layout/UserProfile.vue", "************************************:/home/<USER>/workspace/static/js/vue/components/ui/OfflineIndicator.vue", "************************************:/home/<USER>/workspace/static/js/vue/components/layout/MobileSidebar.vue", "************************************:/home/<USER>/workspace/static/js/vue/components/layout/MobileSidebarItem.vue", "************************************:/home/<USER>/workspace/static/js/vue/components/layout/NotificationDropdown.vue", "************************************:/home/<USER>/workspace/static/js/vue/components/layout/QuickAddDropdown.vue", "************************************:/home/<USER>/workspace/static/js/vue/components/layout/UserDropdown.vue", "************************************:/home/<USER>/workspace/static/js/vue/components/layout/Breadcrumbs.vue", "************************************:/home/<USER>/workspace/static/js/vue/components/layout/AppFooter.vue", "************************************:/home/<USER>/workspace/static/js/vue/views/public/Home.vue", "************************************:/home/<USER>/workspace/static/js/vue/components/public/PublicNavigation.vue", "************************************:/home/<USER>/workspace/static/js/vue/components/public/PublicFooter.vue", "************************************:/home/<USER>/workspace/blueprints/landing.py", "************************************:/home/<USER>/workspace/blueprints/api/public.py", "************************************:/home/<USER>/workspace/config/tenant_config.json", "************************************:/home/<USER>/workspace/static/js/vue/stores/tenant.js", "************************************:/home/<USER>/workspace/static/js/vue/views/public/Services.vue", "************************************:/home/<USER>/workspace/static/js/vue/views/public/About.vue", "************************************:/home/<USER>/workspace/static/js/vue/views/public/Contact.vue", "************************************:/home/<USER>/workspace/static/js/vue/views/public/Privacy.vue", "************************************:/home/<USER>/workspace/static/js/vue/views/public/ServiceDetail.vue", "************************************:/home/<USER>/workspace/static/js/vue/views/NotFound.vue", "************************************:/home/<USER>/workspace/templates/base.html", "************************************:/home/<USER>/workspace/templates/landing/about.html", "************************************:/home/<USER>/workspace/templates/landing/contact.html", "************************************:/home/<USER>/workspace/templates/landing/home.html", "************************************:/home/<USER>/workspace/templates/landing/privacy.html", "************************************:/home/<USER>/workspace/templates/landing/service_detail.html", "************************************:/home/<USER>/workspace/templates/landing/services.html", "************************************:/home/<USER>/workspace/static/js/vue/main-simple.js", "************************************:/home/<USER>/workspace/models.py", "************************************:/home/<USER>/workspace/blueprints/auth.py"], "size": 9074034, "checkpointCount": 331, "lastModified": 1748465886568}, "shard-************************************": {"checkpointDocumentIds": ["************************************:/home/<USER>/workspace/main.py", "************************************:/home/<USER>/workspace/static/js/vue/main.js", "************************************:/home/<USER>/workspace/specs/task_16_vue_refactoring.txt", "************************************:/home/<USER>/workspace/templates/spa.html", "************************************:/home/<USER>/workspace/app.py", "************************************:/home/<USER>/workspace/static/js/vue/views/public/Home.vue", "************************************:/home/<USER>/workspace/static/js/vue/router/index.js", "************************************:/home/<USER>/workspace/static/js/vue/components/public/PublicNavigation.js", "************************************:/home/<USER>/workspace/static/js/vue/components/public/PublicFooter.js", "************************************:/home/<USER>/workspace/static/js/vue/views/public/Home.js", "************************************:/home/<USER>/workspace/static/js/vue/views/public/About.vue", "************************************:/home/<USER>/workspace/static/js/vue/views/public/Contact.vue", "************************************:/home/<USER>/workspace/static/js/vue/views/public/Privacy.vue", "************************************:/home/<USER>/workspace/static/js/vue/views/public/Services.vue", "************************************:/home/<USER>/workspace/static/js/vue/views/auth/Login.vue", "************************************:/home/<USER>/workspace/static/js/vue/views/auth/Login.js", "************************************:/home/<USER>/workspace/specs/vue_professional_migration.md"], "size": 1467631, "checkpointCount": 79, "lastModified": 1748499174233}, "shard-4eae067e-e8d2-466b-b3f2-7e4bda9367a0": {"checkpointDocumentIds": ["4eae067e-e8d2-466b-b3f2-7e4bda9367a0:/home/<USER>/workspace/frontend/src/views/personnel/PersonnelProfile.vue", "4eae067e-e8d2-466b-b3f2-7e4bda9367a0:/home/<USER>/workspace/backend/blueprints/api/personnel.py", "4eae067e-e8d2-466b-b3f2-7e4bda9367a0:/home/<USER>/workspace/frontend/src/views/personnel/DepartmentList.vue", "4eae067e-e8d2-466b-b3f2-7e4bda9367a0:/home/<USER>/workspace/frontend/src/views/personnel/Personnel.vue", "4eae067e-e8d2-466b-b3f2-7e4bda9367a0:/home/<USER>/workspace/frontend/src/views/personnel/DepartmentView.vue", "4eae067e-e8d2-466b-b3f2-7e4bda9367a0:/home/<USER>/workspace/frontend/src/views/personnel/PersonnelDirectory.vue", "4eae067e-e8d2-466b-b3f2-7e4bda9367a0:/home/<USER>/workspace/backend/blueprints/api/timesheets.py", "4eae067e-e8d2-466b-b3f2-7e4bda9367a0:/home/<USER>/workspace/frontend/src/router/index.js", "4eae067e-e8d2-466b-b3f2-7e4bda9367a0:/home/<USER>/workspace/frontend/src/components/layout/SidebarNavigation.vue", "4eae067e-e8d2-466b-b3f2-7e4bda9367a0:/home/<USER>/workspace/frontend/src/views/personnel/DepartmentCreate.vue", "4eae067e-e8d2-466b-b3f2-7e4bda9367a0:/home/<USER>/workspace/backend/models.py", "4eae067e-e8d2-466b-b3f2-7e4bda9367a0:/home/<USER>/workspace/frontend/src/views/personnel/PersonnelOrgChart.vue", "4eae067e-e8d2-466b-b3f2-7e4bda9367a0:/home/<USER>/workspace/frontend/src/views/personnel/components/DepartmentNode.vue", "4eae067e-e8d2-466b-b3f2-7e4bda9367a0:/home/<USER>/workspace/frontend/src/views/personnel/components/DepartmentList.vue", "4eae067e-e8d2-466b-b3f2-7e4bda9367a0:/home/<USER>/workspace/frontend/src/views/personnel/SkillsMatrix.vue", "4eae067e-e8d2-466b-b3f2-7e4bda9367a0:/home/<USER>/workspace/frontend/src/views/personnel/components/SkillCell.vue", "4eae067e-e8d2-466b-b3f2-7e4bda9367a0:/home/<USER>/workspace/frontend/src/views/personnel/components/OrgChartDiagram.vue", "4eae067e-e8d2-466b-b3f2-7e4bda9367a0:/home/<USER>/workspace/frontend/src/views/personnel/PersonnelAdmin.vue", "4eae067e-e8d2-466b-b3f2-7e4bda9367a0:/home/<USER>/workspace/frontend/src/views/personnel/components/admin/UsersManagement.vue", "4eae067e-e8d2-466b-b3f2-7e4bda9367a0:/home/<USER>/workspace/frontend/src/views/personnel/components/admin/AnalyticsDashboard.vue", "4eae067e-e8d2-466b-b3f2-7e4bda9367a0:/home/<USER>/workspace/frontend/src/views/personnel/components/admin/DepartmentsManagement.vue", "4eae067e-e8d2-466b-b3f2-7e4bda9367a0:/home/<USER>/workspace/frontend/src/views/personnel/components/admin/SkillsManagement.vue", "4eae067e-e8d2-466b-b3f2-7e4bda9367a0:/home/<USER>/workspace/frontend/src/views/personnel/components/admin/BulkOperations.vue", "4eae067e-e8d2-466b-b3f2-7e4bda9367a0:/home/<USER>/workspace/frontend/src/views/personnel/components/admin/CreateUserModal.vue", "4eae067e-e8d2-466b-b3f2-7e4bda9367a0:/home/<USER>/workspace/frontend/src/views/personnel/components/admin/DepartmentModal.vue", "4eae067e-e8d2-466b-b3f2-7e4bda9367a0:/home/<USER>/workspace/frontend/src/views/personnel/components/admin/SkillModal.vue", "4eae067e-e8d2-466b-b3f2-7e4bda9367a0:/home/<USER>/workspace/frontend/vitest.config.js", "4eae067e-e8d2-466b-b3f2-7e4bda9367a0:/home/<USER>/workspace/frontend/package.json", "4eae067e-e8d2-466b-b3f2-7e4bda9367a0:/home/<USER>/workspace/frontend/src/test/setup.js", "4eae067e-e8d2-466b-b3f2-7e4bda9367a0:/home/<USER>/workspace/frontend/src/test/utils.js", "4eae067e-e8d2-466b-b3f2-7e4bda9367a0:/home/<USER>/workspace/frontend/src/views/personnel/PersonnelAdmin.test.js", "4eae067e-e8d2-466b-b3f2-7e4bda9367a0:/home/<USER>/workspace/frontend/src/utils/api.test.js", "4eae067e-e8d2-466b-b3f2-7e4bda9367a0:/home/<USER>/workspace/frontend/src/stores/auth.test.js", "4eae067e-e8d2-466b-b3f2-7e4bda9367a0:/home/<USER>/workspace/backend/tests/api/test_personnel.py", "4eae067e-e8d2-466b-b3f2-7e4bda9367a0:/home/<USER>/workspace/backend/tests/integration/test_personnel_admin_workflow.py", "4eae067e-e8d2-466b-b3f2-7e4bda9367a0:/home/<USER>/workspace/docs/testing_strategy.md", "4eae067e-e8d2-466b-b3f2-7e4bda9367a0:/home/<USER>/workspace/docs/testing_implementation_plan.md", "4eae067e-e8d2-466b-b3f2-7e4bda9367a0:/home/<USER>/workspace/frontend/src/test/simple.test.js", "4eae067e-e8d2-466b-b3f2-7e4bda9367a0:/home/<USER>/workspace/frontend/src/test/integration.test.js", "4eae067e-e8d2-466b-b3f2-7e4bda9367a0:/home/<USER>/workspace/backend/tests/conftest.py", "4eae067e-e8d2-466b-b3f2-7e4bda9367a0:/home/<USER>/workspace/backend/tests/test_fixtures.py", "4eae067e-e8d2-466b-b3f2-7e4bda9367a0:/home/<USER>/workspace/docs/testing_report.md", "4eae067e-e8d2-466b-b3f2-7e4bda9367a0:/home/<USER>/workspace/backend/tests/integration/test_auth.py", "4eae067e-e8d2-466b-b3f2-7e4bda9367a0:/home/<USER>/workspace/backend/tests/integration/test_admin.py", "4eae067e-e8d2-466b-b3f2-7e4bda9367a0:/home/<USER>/workspace/backend/tests/integration/test_rbac.py", "4eae067e-e8d2-466b-b3f2-7e4bda9367a0:/home/<USER>/workspace/backend/tests/integration/test_security.py", "4eae067e-e8d2-466b-b3f2-7e4bda9367a0:/home/<USER>/workspace/backend/tests/integration/test_session.py"], "size": 10791452, "checkpointCount": 251, "lastModified": 1748695706221}, "shard-29bfe71d-b185-4740-a88b-b50616113477": {"checkpointDocumentIds": ["29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/frontend/src/views/projects/Projects.vue", "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/backend/blueprints/api/projects.py", "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/backend/ai_services.py", "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/backend/blueprints/api/ai_resources.py", "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/backend/app.py", "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/frontend/src/views/projects/components/ProjectResourceAllocation.vue", "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/frontend/src/views/projects/ProjectView.vue", "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/tasks/tasks.json", "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/backend/tests/test_ai_resources.py", "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/docs/task_2.4_ai_resource_allocation.md", "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/backend/models.py", "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/backend/blueprints/api/base.py", "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/backend/blueprints/api/auth.py", "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/backend/blueprints/api/personnel.py", "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/backend/blueprints/api/resources.py", "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/frontend/src/components/layout/SidebarNavigation.vue", "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/frontend/src/router/index.js", "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/frontend/src/views/personnel/PersonnelAllocation.vue", "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/backend/blueprints/api/personnel_allocation.py", "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/frontend/src/components/layout/SidebarIcon.vue", "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/backend/db_update.py"], "size": 2993658, "checkpointCount": 79, "lastModified": 1748705278603}, "shard-197794ae-99fd-4db5-8b58-49673a1db767": {"checkpointDocumentIds": ["197794ae-99fd-4db5-8b58-49673a1db767:/home/<USER>/workspace/frontend/src/views/personnel/components/CVTab.vue", "197794ae-99fd-4db5-8b58-49673a1db767:/home/<USER>/workspace/backend/blueprints/api/personnel.py", "197794ae-99fd-4db5-8b58-49673a1db767:/home/<USER>/workspace/frontend/src/components/ui/Toast.vue", "197794ae-99fd-4db5-8b58-49673a1db767:/home/<USER>/workspace/frontend/src/composables/useToast.js", "197794ae-99fd-4db5-8b58-49673a1db767:/home/<USER>/workspace/frontend/src/views/personnel/PersonnelProfile.vue", "197794ae-99fd-4db5-8b58-49673a1db767:/home/<USER>/workspace/backend/blueprints/api/auth.py", "197794ae-99fd-4db5-8b58-49673a1db767:/home/<USER>/workspace/backend/app.py", "197794ae-99fd-4db5-8b58-49673a1db767:/home/<USER>/workspace/frontend/src/utils/api.js", "197794ae-99fd-4db5-8b58-49673a1db767:/home/<USER>/workspace/frontend/src/views/dashboard/Dashboard.vue", "197794ae-99fd-4db5-8b58-49673a1db767:/home/<USER>/workspace/frontend/src/views/dashboard/components/StatsCard.vue", "197794ae-99fd-4db5-8b58-49673a1db767:/home/<USER>/workspace/frontend/src/views/admin/KPITemplates.vue", "197794ae-99fd-4db5-8b58-49673a1db767:/home/<USER>/workspace/frontend/src/views/personnel/SkillsMatrix.vue", "197794ae-99fd-4db5-8b58-49673a1db767:/home/<USER>/workspace/frontend/src/stores/personnel.js", "197794ae-99fd-4db5-8b58-49673a1db767:/home/<USER>/workspace/frontend/src/views/projects/components/ProjectTasks.vue", "197794ae-99fd-4db5-8b58-49673a1db767:/home/<USER>/workspace/fix_fetch_calls.js", "197794ae-99fd-4db5-8b58-49673a1db767:/home/<USER>/workspace/frontend/src/stores/tenant.js", "197794ae-99fd-4db5-8b58-49673a1db767:/home/<USER>/workspace/backend/models.py", "197794ae-99fd-4db5-8b58-49673a1db767:/home/<USER>/workspace/backend/db_update.py", "197794ae-99fd-4db5-8b58-49673a1db767:/home/<USER>/workspace/backend/blueprints/api/dashboard.py", "197794ae-99fd-4db5-8b58-49673a1db767:/home/<USER>/workspace/backend/blueprints/api/personnel/allocation.py", "197794ae-99fd-4db5-8b58-49673a1db767:/home/<USER>/workspace/backend/blueprints/api/timesheets.py", "197794ae-99fd-4db5-8b58-49673a1db767:/home/<USER>/workspace/backend/blueprints/api/personnel_allocation.py", "197794ae-99fd-4db5-8b58-49673a1db767:/home/<USER>/workspace/backend/utils/cost_calculator.py", "197794ae-99fd-4db5-8b58-49673a1db767:/home/<USER>/workspace/backend/seed_data.py", "197794ae-99fd-4db5-8b58-49673a1db767:/home/<USER>/workspace/backend/test_tables.py", "197794ae-99fd-4db5-8b58-49673a1db767:/home/<USER>/workspace/docs/task-3/log.md", "197794ae-99fd-4db5-8b58-49673a1db767:/home/<USER>/workspace/backend/blueprints/api/timeoff_requests.py", "197794ae-99fd-4db5-8b58-49673a1db767:/home/<USER>/workspace/backend/blueprints/api/monthly_timesheets.py", "197794ae-99fd-4db5-8b58-49673a1db767:/home/<USER>/workspace/backend/static/swagger/swagger.json", "197794ae-99fd-4db5-8b58-49673a1db767:/home/<USER>/workspace/backend/blueprints/api/contracts.py", "197794ae-99fd-4db5-8b58-49673a1db767:/home/<USER>/workspace/backend/blueprints/api/invoices.py", "197794ae-99fd-4db5-8b58-49673a1db767:/home/<USER>/workspace/backend/blueprints/api/contacts.py", "197794ae-99fd-4db5-8b58-49673a1db767:/home/<USER>/workspace/backend/blueprints/api/proposals.py", "197794ae-99fd-4db5-8b58-49673a1db767:/home/<USER>/workspace/docs/task-3/frontend-implementation-plan.md", "197794ae-99fd-4db5-8b58-49673a1db767:/home/<USER>/workspace/frontend/src/views/projects/components/ProjectGantt.vue", "197794ae-99fd-4db5-8b58-49673a1db767:/home/<USER>/workspace/backend/blueprints/api/projects.py", "197794ae-99fd-4db5-8b58-49673a1db767:/home/<USER>/workspace/frontend/src/views/projects/components/ProjectOverview.vue", "197794ae-99fd-4db5-8b58-49673a1db767:/home/<USER>/workspace/backend/seed_crm_data.py", "197794ae-99fd-4db5-8b58-49673a1db767:/home/<USER>/workspace/frontend/src/views/projects/components/ProjectTimesheet.vue", "197794ae-99fd-4db5-8b58-49673a1db767:/home/<USER>/workspace/frontend/src/components/layout/SidebarNavigation.vue", "197794ae-99fd-4db5-8b58-49673a1db767:/home/<USER>/workspace/frontend/src/router/index.js", "197794ae-99fd-4db5-8b58-49673a1db767:/home/<USER>/workspace/frontend/src/views/timesheet/TimesheetEntry.vue", "197794ae-99fd-4db5-8b58-49673a1db767:/home/<USER>/workspace/frontend/src/views/timesheet/TimesheetRequests.vue", "197794ae-99fd-4db5-8b58-49673a1db767:/home/<USER>/workspace/frontend/src/views/timesheet/TimesheetDashboard.vue", "197794ae-99fd-4db5-8b58-49673a1db767:/home/<USER>/workspace/frontend/src/views/timesheet/TimesheetHistory.vue", "197794ae-99fd-4db5-8b58-49673a1db767:/home/<USER>/workspace/frontend/src/views/timesheet/TimesheetStatus.vue"], "size": 23403135, "checkpointCount": 305, "lastModified": 1748894748215}, "shard-c945c139-7f87-43ab-a9ab-bb9828968a33": {"checkpointDocumentIds": ["c945c139-7f87-43ab-a9ab-bb9828968a33:/home/<USER>/workspace/tasks/tasks.json", "c945c139-7f87-43ab-a9ab-bb9828968a33:/home/<USER>/workspace/backend/models.py", "c945c139-7f87-43ab-a9ab-bb9828968a33:/home/<USER>/workspace/backend/db_update_timesheet_migration.py", "c945c139-7f87-43ab-a9ab-bb9828968a33:/home/<USER>/workspace/backend/blueprints/api/timesheets.py", "c945c139-7f87-43ab-a9ab-bb9828968a33:/home/<USER>/workspace/backend/blueprints/api/time_off_requests.py", "c945c139-7f87-43ab-a9ab-bb9828968a33:/home/<USER>/workspace/backend/blueprints/api/base.py", "c945c139-7f87-43ab-a9ab-bb9828968a33:/home/<USER>/workspace/backend/utils/permissions.py", "c945c139-7f87-43ab-a9ab-bb9828968a33:/home/<USER>/workspace/backend/TASK_3_1_IMPLEMENTATION_SUMMARY.md", "c945c139-7f87-43ab-a9ab-bb9828968a33:/home/<USER>/workspace/docs/timesheet-management-implementation-plan.md", "c945c139-7f87-43ab-a9ab-bb9828968a33:/home/<USER>/workspace/frontend/src/views/personnel/PersonnelAdmin.vue", "c945c139-7f87-43ab-a9ab-bb9828968a33:/home/<USER>/workspace/backend/blueprints/api/projects.py", "c945c139-7f87-43ab-a9ab-bb9828968a33:/home/<USER>/workspace/restore_yesterday_evening.py", "c945c139-7f87-43ab-a9ab-bb9828968a33:/home/<USER>/workspace/backend/ai_services.py", "c945c139-7f87-43ab-a9ab-bb9828968a33:/home/<USER>/workspace/backend/blueprints/api/ai_resources.py"], "size": 2939335, "checkpointCount": 69, "lastModified": 1748858626638}, "shard-b34cd5df-ffc0-4eb2-8256-03580248a9d4": {"checkpointDocumentIds": ["b34cd5df-ffc0-4eb2-8256-03580248a9d4:/home/<USER>/workspace/frontend/src/views/timesheet/TimesheetApprovals.vue", "b34cd5df-ffc0-4eb2-8256-03580248a9d4:/home/<USER>/workspace/frontend/src/views/timesheet/TimesheetAnalytics.vue", "b34cd5df-ffc0-4eb2-8256-03580248a9d4:/home/<USER>/workspace/docs/task-3/timesheet-final-implementation.md", "b34cd5df-ffc0-4eb2-8256-03580248a9d4:/home/<USER>/workspace/backend/blueprints/api/timesheets.py", "b34cd5df-ffc0-4eb2-8256-03580248a9d4:/home/<USER>/workspace/frontend/src/views/timesheet/TimesheetEntry.vue", "b34cd5df-ffc0-4eb2-8256-03580248a9d4:/home/<USER>/workspace/frontend/src/views/timesheet/TimesheetHistory.vue", "b34cd5df-ffc0-4eb2-8256-03580248a9d4:/home/<USER>/workspace/frontend/src/components/layout/SidebarNavigation.vue", "b34cd5df-ffc0-4eb2-8256-03580248a9d4:/home/<USER>/workspace/frontend/src/components/layout/SidebarIcon.vue", "b34cd5df-ffc0-4eb2-8256-03580248a9d4:/home/<USER>/workspace/frontend/src/views/timesheet/TimesheetStatus.vue", "b34cd5df-ffc0-4eb2-8256-03580248a9d4:/home/<USER>/workspace/frontend/src/router/index.js", "b34cd5df-ffc0-4eb2-8256-03580248a9d4:/home/<USER>/workspace/frontend/src/views/timesheet/TimesheetRequests.vue", "b34cd5df-ffc0-4eb2-8256-03580248a9d4:/home/<USER>/workspace/docs/task-3/timesheet-improvements-summary.md", "b34cd5df-ffc0-4eb2-8256-03580248a9d4:/home/<USER>/workspace/frontend/src/views/timesheet/TimesheetDashboard.vue", "b34cd5df-ffc0-4eb2-8256-03580248a9d4:/home/<USER>/workspace/frontend/src/stores/projects.js"], "size": 3921781, "checkpointCount": 91, "lastModified": 1748972970332}, "shard-91bebea8-799b-4a1b-886e-36aef93ca394": {"checkpointDocumentIds": ["91bebea8-799b-4a1b-886e-36aef93ca394:/home/<USER>/workspace/frontend/src/views/personnel/PersonnelProfile.vue", "91bebea8-799b-4a1b-886e-36aef93ca394:/home/<USER>/workspace/frontend/src/views/timesheet/TimesheetEntry.vue"], "size": 2047791, "checkpointCount": 27, "lastModified": 1750975080980}, "shard-b13eff61-a2b9-4c38-8021-326e070cb9fe": {"checkpointDocumentIds": ["b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/docs/testing/test-coverage-audit.md", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/backend/tests/unit/test_kpi_calculations.py", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/backend/tests/unit/test_task_kpi_calculations.py", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/backend/services/pre_invoicing_service.py", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/docs/testing/test-gap-analysis.md", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/frontend/src/test/utils/test-helpers.js", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/frontend/src/test/components/auth.test.js", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/frontend/src/test/components/dashboard.test.js", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/frontend/src/test/components/projects.test.js", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/backend/db/validate_models_vs_database.py", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/backend/db/fix_missing_tablenames.py", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/backend/db/fix_bad_tablenames.py", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/backend/models_split/projects.py", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/backend/tests/integration/test_project_workflows.py", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/frontend/cypress/e2e/project-workflows.cy.js", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/scripts/run-all-tests.sh", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/frontend/src/test/components/ProjectTeam.test.js", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/frontend/src/test/integration/project-api-integration.test.js", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/frontend/src/test/ui/user-interactions.test.js", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/frontend/src/test/mocks/api-handlers.js", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/frontend/src/test/mocks/server.js", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/frontend/src/test/fixtures/projects.json", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/frontend/src/test/validation/data-display-validation.test.js", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/docs/testing/README.md", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/docs/testing/backend-testing-guide.md", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/docs/testing/frontend-testing-guide.md", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/docs/testing/e2e-testing-guide.md", "b13eff61-a2b9-4c38-8021-326e070cb9fe:/home/<USER>/workspace/docs/testing/test-execution-guide.md"], "size": 825181, "checkpointCount": 47, "lastModified": 1751049457232}, "shard-0eecf9ec-5957-4634-86da-528436e13e49": {"checkpointDocumentIds": ["0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/frontend/src/views/auth/Login.vue", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/frontend/src/views/projects/Projects.vue", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/frontend/src/views/projects/ProjectEdit.vue", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/frontend/src/views/projects/components/ProjectTeam.vue", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/frontend/src/test/components/auth.test.js", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/frontend/src/test/components/projects.test.js", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/backend/blueprints/api/timeoff_requests.py", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/frontend/src/test/components/timesheet-requests.test.js", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/frontend/src/views/timesheet/TimesheetRequests.vue", "0eecf9ec-5957-4634-86da-528436e13e49:/home/<USER>/workspace/frontend/src/stores/timesheet.js"], "size": 4897323, "checkpointCount": 178, "lastModified": 1751055125493}}}