{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_performancereview_model.py"}, "originalCode": "\"\"\"Unit tests for PerformanceReview model.\"\"\"\nimport pytest\nfrom models import PerformanceReview, User\nfrom extensions import db\n\nclass TestPerformanceReviewModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n\n    def test_performancereview_creation_basic(self):\n        from datetime import date\n        review = PerformanceReview(\n            employee_id=self.user.id,\n            reviewer_id=self.user.id,\n            review_period_start=date(2024, 1, 1),  # Campo richiesto\n            review_period_end=date(2024, 3, 31),   # Campo richiesto\n            review_year=2024,  # Campo richiesto\n            status='draft'\n        )\n        db.session.add(review)\n        db.session.commit()\n        \n        assert review.id is not None\n        assert review.employee_id == self.user.id\n        assert review.period == '2024-Q1'\n\n    def test_performancereview_deletion(self):\n        review = PerformanceReview(employee_id=self.user.id, reviewer_id=self.user.id)\n        db.session.add(review)\n        db.session.commit()\n        review_id = review.id\n        \n        db.session.delete(review)\n        db.session.commit()\n        \n        deleted = PerformanceReview.query.get(review_id)\n        assert deleted is None\n", "modifiedCode": "\"\"\"Unit tests for PerformanceReview model.\"\"\"\nimport pytest\nfrom models import PerformanceReview, User\nfrom extensions import db\n\nclass TestPerformanceReviewModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n\n    def test_performancereview_creation_basic(self):\n        from datetime import date\n        review = PerformanceReview(\n            employee_id=self.user.id,\n            reviewer_id=self.user.id,\n            review_period_start=date(2024, 1, 1),  # Campo richiesto\n            review_period_end=date(2024, 3, 31),   # Campo richiesto\n            review_year=2024,  # Campo richiesto\n            status='draft'\n        )\n        db.session.add(review)\n        db.session.commit()\n        \n        assert review.id is not None\n        assert review.employee_id == self.user.id\n        assert review.period == '2024-Q1'\n\n    def test_performancereview_deletion(self):\n        review = PerformanceReview(employee_id=self.user.id, reviewer_id=self.user.id)\n        db.session.add(review)\n        db.session.commit()\n        review_id = review.id\n        \n        db.session.delete(review)\n        db.session.commit()\n        \n        deleted = PerformanceReview.query.get(review_id)\n        assert deleted is None\n"}