{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_forumcomment_model.py"}, "originalCode": "\"\"\"\nUnit tests for ForumComment model.\nTests business logic, validations, and model functionality.\n\"\"\"\n\nimport pytest\nfrom datetime import datetime\nfrom models import ForumComment, ForumTopic, User\nfrom extensions import db\n\n\nclass TestForumCommentModel:\n    \"\"\"Test suite for ForumComment model unit tests\"\"\"\n\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        \"\"\"Setup test data for each test method\"\"\"\n        self.app = app\n        self.user = test_user\n        \n        # Create test forum topic (using correct field name)\n        self.test_topic = ForumTopic(\n            title='Test Forum Topic',\n            description='This is a test topic for comments',\n            author_id=self.user.id,\n            category='General'\n        )\n        db.session.add(self.test_topic)\n        db.session.commit()\n\n    def test_forumcomment_creation_basic(self):\n        \"\"\"Test basic forum comment creation\"\"\"\n        comment = ForumComment(\n            topic_id=self.test_topic.id,\n            author_id=self.user.id,\n            content='This is a test comment'\n        )\n        \n        db.session.add(comment)\n        db.session.commit()\n        \n        assert comment.id is not None\n        assert comment.topic_id == self.test_topic.id\n        assert comment.author_id == self.user.id\n        assert comment.content == 'This is a test comment'\n\n    def test_forumcomment_relationships(self):\n        \"\"\"Test relationships with ForumTopic and User models\"\"\"\n        comment = ForumComment(\n            topic_id=self.test_topic.id,\n            author_id=self.user.id,\n            content='Relationship test comment'\n        )\n        \n        db.session.add(comment)\n        db.session.commit()\n        \n        # Test forward relationships\n        assert comment.topic is not None\n        assert comment.topic.id == self.test_topic.id\n        assert comment.author is not None\n        assert comment.author.id == self.user.id\n        \n        # Test backward relationships\n        assert comment in self.test_topic.comments\n        assert comment in self.user.forum_comments\n\n    def test_forumcomment_reply_functionality(self):\n        \"\"\"Test parent-child comment relationships\"\"\"\n        # Create parent comment\n        parent_comment = ForumComment(\n            topic_id=self.test_topic.id,\n            author_id=self.user.id,\n            content='This is a parent comment'\n        )\n        \n        db.session.add(parent_comment)\n        db.session.commit()\n        \n        # Create reply comment\n        reply_comment = ForumComment(\n            topic_id=self.test_topic.id,\n            author_id=self.user.id,\n            content='This is a reply to the parent comment',\n            parent_comment_id=parent_comment.id\n        )\n        \n        db.session.add(reply_comment)\n        db.session.commit()\n        \n        # Test parent-child relationship\n        assert reply_comment.parent_comment_id == parent_comment.id\n        assert reply_comment.parent_comment is not None\n        assert reply_comment.parent_comment.id == parent_comment.id\n        assert reply_comment in parent_comment.replies\n\n    def test_forumcomment_content_handling(self):\n        \"\"\"Test content field for various text lengths\"\"\"\n        contents = [\n            'Short comment',\n            'Medium length comment with more details and information',\n            '''Very long comment with multiple paragraphs and extensive content.\n            \n            This comment spans multiple lines and includes various formatting.\n            It tests the ability to store longer text content in the database.\n            \n            The comment system should handle this type of content gracefully.'''\n        ]\n        \n        comments = []\n        for i, content in enumerate(contents):\n            comment = ForumComment(\n                topic_id=self.test_topic.id,\n                author_id=self.user.id,\n                content=content\n            )\n            comments.append(comment)\n        \n        db.session.add_all(comments)\n        db.session.commit()\n        \n        for comment, expected_content in zip(comments, contents):\n            assert comment.content == expected_content\n\n    def test_forumcomment_edit_functionality(self):\n        \"\"\"Test comment editing functionality\"\"\"\n        comment = ForumComment(\n            topic_id=self.test_topic.id,\n            author_id=self.user.id,\n            content='Original comment content'\n        )\n        \n        db.session.add(comment)\n        db.session.commit()\n        \n        # Initially not edited\n        assert comment.is_edited is False\n        assert comment.edited_at is None\n        \n        # Edit the comment\n        comment.content = 'Edited comment content'\n        comment.is_edited = True\n        comment.edited_at = datetime.now()\n        \n        db.session.commit()\n        \n        # Verify edit\n        assert comment.content == 'Edited comment content'\n        assert comment.is_edited is True\n        assert comment.edited_at is not None\n\n    def test_forumcomment_timestamps(self):\n        \"\"\"Test automatic timestamp handling\"\"\"\n        comment = ForumComment(\n            topic_id=self.test_topic.id,\n            author_id=self.user.id,\n            content='Timestamp test comment'\n        )\n        \n        db.session.add(comment)\n        db.session.commit()\n        \n        # Test created_at is set\n        assert comment.created_at is not None\n        assert isinstance(comment.created_at, datetime)\n        \n        # Test updated_at is set\n        assert comment.updated_at is not None\n        assert isinstance(comment.updated_at, datetime)\n\n    def test_forumcomment_query_by_topic(self):\n        \"\"\"Test querying comments by topic\"\"\"\n        comments = [\n            ForumComment(topic_id=self.test_topic.id, author_id=self.user.id, content='Comment 1'),\n            ForumComment(topic_id=self.test_topic.id, author_id=self.user.id, content='Comment 2'),\n            ForumComment(topic_id=self.test_topic.id, author_id=self.user.id, content='Comment 3')\n        ]\n        \n        db.session.add_all(comments)\n        db.session.commit()\n        \n        # Query comments by topic\n        topic_comments = ForumComment.query.filter_by(topic_id=self.test_topic.id).all()\n        \n        assert len(topic_comments) >= 3\n        comment_contents = [c.content for c in topic_comments]\n        assert 'Comment 1' in comment_contents\n        assert 'Comment 2' in comment_contents\n        assert 'Comment 3' in comment_contents\n\n    def test_forumcomment_query_by_author(self):\n        \"\"\"Test querying comments by author\"\"\"\n        comment = ForumComment(\n            topic_id=self.test_topic.id,\n            author_id=self.user.id,\n            content='Author query test comment'\n        )\n        \n        db.session.add(comment)\n        db.session.commit()\n        \n        # Query comments by author\n        author_comments = ForumComment.query.filter_by(author_id=self.user.id).all()\n        \n        assert len(author_comments) >= 1\n        assert comment in author_comments\n\n    def test_forumcomment_nested_replies(self):\n        \"\"\"Test multiple levels of nested replies\"\"\"\n        # Level 1: Original comment\n        level1_comment = ForumComment(\n            topic_id=self.test_topic.id,\n            author_id=self.user.id,\n            content='Level 1 comment'\n        )\n        \n        db.session.add(level1_comment)\n        db.session.commit()\n        \n        # Level 2: Reply to level 1\n        level2_comment = ForumComment(\n            topic_id=self.test_topic.id,\n            author_id=self.user.id,\n            content='Level 2 reply',\n            parent_comment_id=level1_comment.id\n        )\n        \n        db.session.add(level2_comment)\n        db.session.commit()\n        \n        # Level 3: Reply to level 2\n        level3_comment = ForumComment(\n            topic_id=self.test_topic.id,\n            author_id=self.user.id,\n            content='Level 3 reply',\n            parent_comment_id=level2_comment.id\n        )\n        \n        db.session.add(level3_comment)\n        db.session.commit()\n        \n        # Test nested structure\n        assert level2_comment.parent_comment_id == level1_comment.id\n        assert level3_comment.parent_comment_id == level2_comment.id\n        assert level1_comment.parent_comment_id is None\n\n    def test_forumcomment_update_operations(self):\n        \"\"\"Test comment update operations\"\"\"\n        comment = ForumComment(\n            topic_id=self.test_topic.id,\n            author_id=self.user.id,\n            content='Original content'\n        )\n        \n        db.session.add(comment)\n        db.session.commit()\n        \n        # Update comment\n        comment.content = 'Updated content'\n        comment.is_edited = True\n        comment.edited_at = datetime.now()\n        \n        db.session.commit()\n        \n        # Verify updates\n        updated_comment = ForumComment.query.get(comment.id)\n        assert updated_comment.content == 'Updated content'\n        assert updated_comment.is_edited is True\n        assert updated_comment.edited_at is not None\n\n    def test_forumcomment_deletion(self):\n        \"\"\"Test comment deletion\"\"\"\n        comment = ForumComment(\n            topic_id=self.test_topic.id,\n            author_id=self.user.id,\n            content='Comment to delete'\n        )\n        \n        db.session.add(comment)\n        db.session.commit()\n        comment_id = comment.id\n        \n        # Delete comment\n        db.session.delete(comment)\n        db.session.commit()\n        \n        # Verify deletion\n        deleted_comment = ForumComment.query.get(comment_id)\n        assert deleted_comment is None\n\n    def test_forumcomment_default_values(self):\n        \"\"\"Test default values for fields\"\"\"\n        comment = ForumComment(\n            topic_id=self.test_topic.id,\n            author_id=self.user.id,\n            content='Default values test'\n        )\n        \n        db.session.add(comment)\n        db.session.commit()\n        \n        # Check default values\n        assert comment.is_edited is False\n        assert comment.edited_at is None\n        assert comment.parent_comment_id is None\n        assert comment.created_at is not None\n        assert comment.updated_at is not None\n\n    def test_forumcomment_content_search(self):\n        \"\"\"Test searching comments by content\"\"\"\n        comments = [\n            ForumComment(topic_id=self.test_topic.id, author_id=self.user.id, \n                        content='Python programming discussion'),\n            ForumComment(topic_id=self.test_topic.id, author_id=self.user.id, \n                        content='JavaScript development tips'),\n            ForumComment(topic_id=self.test_topic.id, author_id=self.user.id, \n                        content='Database design patterns')\n        ]\n        \n        db.session.add_all(comments)\n        db.session.commit()\n        \n        # Search for comments containing 'programming'\n        programming_comments = ForumComment.query.filter(\n            ForumComment.content.contains('programming')\n        ).all()\n        \n        assert len(programming_comments) >= 1\n        assert any('Python programming' in c.content for c in programming_comments)\n\n    def test_forumcomment_chronological_order(self):\n        \"\"\"Test ordering comments chronologically\"\"\"\n        comments = []\n        for i in range(3):\n            comment = ForumComment(\n                topic_id=self.test_topic.id,\n                author_id=self.user.id,\n                content=f'Chronological comment {i}'\n            )\n            comments.append(comment)\n            db.session.add(comment)\n            db.session.commit()\n        \n        # Query comments ordered by creation time\n        ordered_comments = ForumComment.query.filter_by(\n            topic_id=self.test_topic.id\n        ).order_by(ForumComment.created_at).all()\n        \n        # Verify chronological order\n        for i in range(len(ordered_comments) - 1):\n            assert ordered_comments[i].created_at <= ordered_comments[i + 1].created_at\n", "modifiedCode": "\"\"\"\nUnit tests for ForumComment model.\nTests business logic, validations, and model functionality.\n\"\"\"\n\nimport pytest\nfrom datetime import datetime\nfrom models import ForumComment, ForumTopic, User\nfrom extensions import db\n\n\nclass TestForumCommentModel:\n    \"\"\"Test suite for ForumComment model unit tests\"\"\"\n\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        \"\"\"Setup test data for each test method\"\"\"\n        self.app = app\n        self.user = test_user\n        \n        # Create test forum topic (using correct field name)\n        self.test_topic = ForumTopic(\n            title='Test Forum Topic',\n            description='This is a test topic for comments',\n            author_id=self.user.id,\n            category='General'\n        )\n        db.session.add(self.test_topic)\n        db.session.commit()\n\n    def test_forumcomment_creation_basic(self):\n        \"\"\"Test basic forum comment creation\"\"\"\n        comment = ForumComment(\n            topic_id=self.test_topic.id,\n            author_id=self.user.id,\n            content='This is a test comment'\n        )\n        \n        db.session.add(comment)\n        db.session.commit()\n        \n        assert comment.id is not None\n        assert comment.topic_id == self.test_topic.id\n        assert comment.author_id == self.user.id\n        assert comment.content == 'This is a test comment'\n\n    def test_forumcomment_relationships(self):\n        \"\"\"Test relationships with ForumTopic and User models\"\"\"\n        comment = ForumComment(\n            topic_id=self.test_topic.id,\n            author_id=self.user.id,\n            content='Relationship test comment'\n        )\n        \n        db.session.add(comment)\n        db.session.commit()\n        \n        # Test forward relationships\n        assert comment.topic is not None\n        assert comment.topic.id == self.test_topic.id\n        assert comment.author is not None\n        assert comment.author.id == self.user.id\n        \n        # Test backward relationships\n        assert comment in self.test_topic.comments\n        assert comment in self.user.forum_comments\n\n    def test_forumcomment_reply_functionality(self):\n        \"\"\"Test parent-child comment relationships\"\"\"\n        # Create parent comment\n        parent_comment = ForumComment(\n            topic_id=self.test_topic.id,\n            author_id=self.user.id,\n            content='This is a parent comment'\n        )\n        \n        db.session.add(parent_comment)\n        db.session.commit()\n        \n        # Create reply comment\n        reply_comment = ForumComment(\n            topic_id=self.test_topic.id,\n            author_id=self.user.id,\n            content='This is a reply to the parent comment',\n            parent_comment_id=parent_comment.id\n        )\n        \n        db.session.add(reply_comment)\n        db.session.commit()\n        \n        # Test parent-child relationship\n        assert reply_comment.parent_comment_id == parent_comment.id\n        assert reply_comment.parent_comment is not None\n        assert reply_comment.parent_comment.id == parent_comment.id\n        assert reply_comment in parent_comment.replies\n\n    def test_forumcomment_content_handling(self):\n        \"\"\"Test content field for various text lengths\"\"\"\n        contents = [\n            'Short comment',\n            'Medium length comment with more details and information',\n            '''Very long comment with multiple paragraphs and extensive content.\n            \n            This comment spans multiple lines and includes various formatting.\n            It tests the ability to store longer text content in the database.\n            \n            The comment system should handle this type of content gracefully.'''\n        ]\n        \n        comments = []\n        for i, content in enumerate(contents):\n            comment = ForumComment(\n                topic_id=self.test_topic.id,\n                author_id=self.user.id,\n                content=content\n            )\n            comments.append(comment)\n        \n        db.session.add_all(comments)\n        db.session.commit()\n        \n        for comment, expected_content in zip(comments, contents):\n            assert comment.content == expected_content\n\n    def test_forumcomment_edit_functionality(self):\n        \"\"\"Test comment editing functionality\"\"\"\n        comment = ForumComment(\n            topic_id=self.test_topic.id,\n            author_id=self.user.id,\n            content='Original comment content'\n        )\n        \n        db.session.add(comment)\n        db.session.commit()\n        \n        # Initially not edited\n        assert comment.is_edited is False\n        assert comment.edited_at is None\n        \n        # Edit the comment\n        comment.content = 'Edited comment content'\n        comment.is_edited = True\n        comment.edited_at = datetime.now()\n        \n        db.session.commit()\n        \n        # Verify edit\n        assert comment.content == 'Edited comment content'\n        assert comment.is_edited is True\n        assert comment.edited_at is not None\n\n    def test_forumcomment_timestamps(self):\n        \"\"\"Test automatic timestamp handling\"\"\"\n        comment = ForumComment(\n            topic_id=self.test_topic.id,\n            author_id=self.user.id,\n            content='Timestamp test comment'\n        )\n        \n        db.session.add(comment)\n        db.session.commit()\n        \n        # Test created_at is set\n        assert comment.created_at is not None\n        assert isinstance(comment.created_at, datetime)\n        \n        # Test updated_at is set\n        assert comment.updated_at is not None\n        assert isinstance(comment.updated_at, datetime)\n\n    def test_forumcomment_query_by_topic(self):\n        \"\"\"Test querying comments by topic\"\"\"\n        comments = [\n            ForumComment(topic_id=self.test_topic.id, author_id=self.user.id, content='Comment 1'),\n            ForumComment(topic_id=self.test_topic.id, author_id=self.user.id, content='Comment 2'),\n            ForumComment(topic_id=self.test_topic.id, author_id=self.user.id, content='Comment 3')\n        ]\n        \n        db.session.add_all(comments)\n        db.session.commit()\n        \n        # Query comments by topic\n        topic_comments = ForumComment.query.filter_by(topic_id=self.test_topic.id).all()\n        \n        assert len(topic_comments) >= 3\n        comment_contents = [c.content for c in topic_comments]\n        assert 'Comment 1' in comment_contents\n        assert 'Comment 2' in comment_contents\n        assert 'Comment 3' in comment_contents\n\n    def test_forumcomment_query_by_author(self):\n        \"\"\"Test querying comments by author\"\"\"\n        comment = ForumComment(\n            topic_id=self.test_topic.id,\n            author_id=self.user.id,\n            content='Author query test comment'\n        )\n        \n        db.session.add(comment)\n        db.session.commit()\n        \n        # Query comments by author\n        author_comments = ForumComment.query.filter_by(author_id=self.user.id).all()\n        \n        assert len(author_comments) >= 1\n        assert comment in author_comments\n\n    def test_forumcomment_nested_replies(self):\n        \"\"\"Test multiple levels of nested replies\"\"\"\n        # Level 1: Original comment\n        level1_comment = ForumComment(\n            topic_id=self.test_topic.id,\n            author_id=self.user.id,\n            content='Level 1 comment'\n        )\n        \n        db.session.add(level1_comment)\n        db.session.commit()\n        \n        # Level 2: Reply to level 1\n        level2_comment = ForumComment(\n            topic_id=self.test_topic.id,\n            author_id=self.user.id,\n            content='Level 2 reply',\n            parent_comment_id=level1_comment.id\n        )\n        \n        db.session.add(level2_comment)\n        db.session.commit()\n        \n        # Level 3: Reply to level 2\n        level3_comment = ForumComment(\n            topic_id=self.test_topic.id,\n            author_id=self.user.id,\n            content='Level 3 reply',\n            parent_comment_id=level2_comment.id\n        )\n        \n        db.session.add(level3_comment)\n        db.session.commit()\n        \n        # Test nested structure\n        assert level2_comment.parent_comment_id == level1_comment.id\n        assert level3_comment.parent_comment_id == level2_comment.id\n        assert level1_comment.parent_comment_id is None\n\n    def test_forumcomment_update_operations(self):\n        \"\"\"Test comment update operations\"\"\"\n        comment = ForumComment(\n            topic_id=self.test_topic.id,\n            author_id=self.user.id,\n            content='Original content'\n        )\n        \n        db.session.add(comment)\n        db.session.commit()\n        \n        # Update comment\n        comment.content = 'Updated content'\n        comment.is_edited = True\n        comment.edited_at = datetime.now()\n        \n        db.session.commit()\n        \n        # Verify updates\n        updated_comment = ForumComment.query.get(comment.id)\n        assert updated_comment.content == 'Updated content'\n        assert updated_comment.is_edited is True\n        assert updated_comment.edited_at is not None\n\n    def test_forumcomment_deletion(self):\n        \"\"\"Test comment deletion\"\"\"\n        comment = ForumComment(\n            topic_id=self.test_topic.id,\n            author_id=self.user.id,\n            content='Comment to delete'\n        )\n        \n        db.session.add(comment)\n        db.session.commit()\n        comment_id = comment.id\n        \n        # Delete comment\n        db.session.delete(comment)\n        db.session.commit()\n        \n        # Verify deletion\n        deleted_comment = ForumComment.query.get(comment_id)\n        assert deleted_comment is None\n\n    def test_forumcomment_default_values(self):\n        \"\"\"Test default values for fields\"\"\"\n        comment = ForumComment(\n            topic_id=self.test_topic.id,\n            author_id=self.user.id,\n            content='Default values test'\n        )\n        \n        db.session.add(comment)\n        db.session.commit()\n        \n        # Check default values\n        assert comment.is_edited is False\n        assert comment.edited_at is None\n        assert comment.parent_comment_id is None\n        assert comment.created_at is not None\n        assert comment.updated_at is not None\n\n    def test_forumcomment_content_search(self):\n        \"\"\"Test searching comments by content\"\"\"\n        comments = [\n            ForumComment(topic_id=self.test_topic.id, author_id=self.user.id, \n                        content='Python programming discussion'),\n            ForumComment(topic_id=self.test_topic.id, author_id=self.user.id, \n                        content='JavaScript development tips'),\n            ForumComment(topic_id=self.test_topic.id, author_id=self.user.id, \n                        content='Database design patterns')\n        ]\n        \n        db.session.add_all(comments)\n        db.session.commit()\n        \n        # Search for comments containing 'programming'\n        programming_comments = ForumComment.query.filter(\n            ForumComment.content.contains('programming')\n        ).all()\n        \n        assert len(programming_comments) >= 1\n        assert any('Python programming' in c.content for c in programming_comments)\n\n    def test_forumcomment_chronological_order(self):\n        \"\"\"Test ordering comments chronologically\"\"\"\n        comments = []\n        for i in range(3):\n            comment = ForumComment(\n                topic_id=self.test_topic.id,\n                author_id=self.user.id,\n                content=f'Chronological comment {i}'\n            )\n            comments.append(comment)\n            db.session.add(comment)\n            db.session.commit()\n        \n        # Query comments ordered by creation time\n        ordered_comments = ForumComment.query.filter_by(\n            topic_id=self.test_topic.id\n        ).order_by(ForumComment.created_at).all()\n        \n        # Verify chronological order\n        for i in range(len(ordered_comments) - 1):\n            assert ordered_comments[i].created_at <= ordered_comments[i + 1].created_at\n"}