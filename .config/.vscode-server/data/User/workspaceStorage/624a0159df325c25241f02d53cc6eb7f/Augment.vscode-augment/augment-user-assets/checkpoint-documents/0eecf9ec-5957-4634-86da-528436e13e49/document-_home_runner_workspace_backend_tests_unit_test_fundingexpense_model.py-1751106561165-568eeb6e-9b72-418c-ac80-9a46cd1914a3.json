{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_fundingexpense_model.py"}, "originalCode": "\"\"\"Unit tests for FundingExpense model.\"\"\"\nimport pytest\nfrom datetime import date\nfrom models import FundingExpense, FundingApplication, FundingOpportunity, User\nfrom extensions import db\n\nclass TestFundingExpenseModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n\n    def test_fundingexpense_creation_basic(self):\n        # Creo prima una FundingApplication\n        from datetime import date\n        opportunity = FundingOpportunity(\n            title='Test Opp',\n            application_deadline=date(2024, 12, 31),\n            created_by=1\n        )\n        db.session.add(opportunity)\n        db.session.commit()\n\n        application = FundingApplication(\n            opportunity_id=opportunity.id,\n            project_title='Test Project',\n            requested_amount=50000.0,\n            created_by=1\n        )\n        db.session.add(application)\n        db.session.commit()\n\n        expense = FundingExpense(\n            application_id=application.id,  # Campo richiesto\n            description='Test Expense',\n            amount=1000.0,\n            expense_date=date.today()  # Campo richiesto\n        )\n        db.session.add(expense)\n        db.session.commit()\n        \n        assert expense.id is not None\n        assert expense.description == 'Test Expense'\n        assert expense.amount == 1000.0\n\n    def test_fundingexpense_deletion(self):\n        # Uso la stessa application del test precedente\n        application = FundingApplication.query.first()\n        expense = FundingExpense(\n            application_id=application.id,\n            description='To Delete',\n            amount=100.0,\n            expense_date=date.today()\n        )\n        db.session.add(expense)\n        db.session.commit()\n        expense_id = expense.id\n        \n        db.session.delete(expense)\n        db.session.commit()\n        \n        deleted = FundingExpense.query.get(expense_id)\n        assert deleted is None\n", "modifiedCode": "\"\"\"Unit tests for FundingExpense model.\"\"\"\nimport pytest\nfrom datetime import date\nfrom models import FundingExpense, FundingApplication, FundingOpportunity, User\nfrom extensions import db\n\nclass TestFundingExpenseModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n\n    def test_fundingexpense_creation_basic(self):\n        # Creo prima una FundingApplication\n        from datetime import date\n        opportunity = FundingOpportunity(\n            title='Test Opp',\n            application_deadline=date(2024, 12, 31),\n            created_by=1\n        )\n        db.session.add(opportunity)\n        db.session.commit()\n\n        application = FundingApplication(\n            opportunity_id=opportunity.id,\n            project_title='Test Project',\n            requested_amount=50000.0,\n            created_by=1\n        )\n        db.session.add(application)\n        db.session.commit()\n\n        expense = FundingExpense(\n            application_id=application.id,  # Campo richiesto\n            description='Test Expense',\n            amount=1000.0,\n            expense_date=date.today()  # Campo richiesto\n        )\n        db.session.add(expense)\n        db.session.commit()\n        \n        assert expense.id is not None\n        assert expense.description == 'Test Expense'\n        assert expense.amount == 1000.0\n\n    def test_fundingexpense_deletion(self):\n        # Uso la stessa application del test precedente\n        application = FundingApplication.query.first()\n        expense = FundingExpense(\n            application_id=application.id,\n            description='To Delete',\n            amount=100.0,\n            expense_date=date.today()\n        )\n        db.session.add(expense)\n        db.session.commit()\n        expense_id = expense.id\n        \n        db.session.delete(expense)\n        db.session.commit()\n        \n        deleted = FundingExpense.query.get(expense_id)\n        assert deleted is None\n"}