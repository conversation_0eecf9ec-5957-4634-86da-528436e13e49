{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/cypress/e2e/project-workflows.cy.js"}, "modifiedCode": "/**\n * Test E2E per i workflow completi dei progetti\n * Testa l'integrazione frontend-backend end-to-end\n */\n\ndescribe('Project Management Workflows', () => {\n  beforeEach(() => {\n    // Login come admin\n    cy.login('admin', 'password')\n    \n    // Intercept API calls\n    cy.intercept('GET', '/api/projects*', { fixture: 'projects.json' }).as('getProjects')\n    cy.intercept('POST', '/api/projects', { fixture: 'project-created.json' }).as('createProject')\n    cy.intercept('GET', '/api/clients*', { fixture: 'clients.json' }).as('getClients')\n    cy.intercept('GET', '/api/personnel/users*', { fixture: 'users.json' }).as('getUsers')\n  })\n\n  describe('Project Creation Workflow', () => {\n    it('should create a complete project with team and tasks', () => {\n      // 1. Navigate to projects\n      cy.visit('/app/projects')\n      cy.wait('@getProjects')\n      \n      // 2. Click create project\n      cy.get('[data-testid=\"create-project-button\"]').click()\n      \n      // 3. Fill project form\n      cy.get('[data-testid=\"project-name\"]').type('E2E Test Project')\n      cy.get('[data-testid=\"project-description\"]').type('Complete end-to-end test project')\n      cy.get('[data-testid=\"project-budget\"]').type('50000')\n      cy.get('[data-testid=\"project-client\"]').select('Test Client')\n      cy.get('[data-testid=\"project-start-date\"]').type('2025-01-01')\n      cy.get('[data-testid=\"project-end-date\"]').type('2025-06-30')\n      \n      // 4. Save project\n      cy.get('[data-testid=\"save-button\"]').click()\n      cy.wait('@createProject')\n      \n      // 5. Verify success message\n      cy.get('[data-testid=\"success-message\"]').should('contain', 'Project created successfully')\n      \n      // 6. Navigate to project details\n      cy.url().should('include', '/projects/')\n      \n      // 7. Add team members\n      cy.get('[data-testid=\"team-tab\"]').click()\n      cy.get('[data-testid=\"add-member-button\"]').click()\n      \n      cy.get('[data-testid=\"user-select\"]').select('John Doe')\n      cy.get('[data-testid=\"role-input\"]').type('Project Manager')\n      cy.get('[data-testid=\"allocation-input\"]').clear().type('50')\n      cy.get('[data-testid=\"save-member-button\"]').click()\n      \n      // 8. Verify team member added\n      cy.get('[data-testid=\"team-member-list\"]').should('contain', 'John Doe')\n      cy.get('[data-testid=\"team-member-list\"]').should('contain', 'Project Manager')\n      \n      // 9. Add tasks\n      cy.get('[data-testid=\"tasks-tab\"]').click()\n      cy.get('[data-testid=\"add-task-button\"]').click()\n      \n      cy.get('[data-testid=\"task-title\"]').type('Setup Development Environment')\n      cy.get('[data-testid=\"task-description\"]').type('Configure tools and environment')\n      cy.get('[data-testid=\"task-assignee\"]').select('John Doe')\n      cy.get('[data-testid=\"task-estimated-hours\"]').type('16')\n      cy.get('[data-testid=\"task-priority\"]').select('High')\n      cy.get('[data-testid=\"save-task-button\"]').click()\n      \n      // 10. Verify task added\n      cy.get('[data-testid=\"task-list\"]').should('contain', 'Setup Development Environment')\n      cy.get('[data-testid=\"task-list\"]').should('contain', '16 hours')\n    })\n\n    it('should handle project creation validation errors', () => {\n      cy.visit('/app/projects/new')\n      \n      // Try to save without required fields\n      cy.get('[data-testid=\"save-button\"]').click()\n      \n      // Verify validation errors\n      cy.get('[data-testid=\"name-error\"]').should('contain', 'Project name is required')\n      cy.get('[data-testid=\"budget-error\"]').should('contain', 'Budget is required')\n      \n      // Fill invalid budget\n      cy.get('[data-testid=\"project-budget\"]').type('-1000')\n      cy.get('[data-testid=\"save-button\"]').click()\n      \n      cy.get('[data-testid=\"budget-error\"]').should('contain', 'Budget must be positive')\n    })\n  })\n\n  describe('Project Team Management', () => {\n    beforeEach(() => {\n      cy.visit('/app/projects/1') // Existing project\n      cy.get('[data-testid=\"team-tab\"]').click()\n    })\n\n    it('should manage team members lifecycle', () => {\n      // Add member\n      cy.get('[data-testid=\"add-member-button\"]').click()\n      cy.get('[data-testid=\"user-select\"]').select('Jane Smith')\n      cy.get('[data-testid=\"role-input\"]').type('Senior Developer')\n      cy.get('[data-testid=\"allocation-input\"]').type('100')\n      cy.get('[data-testid=\"save-member-button\"]').click()\n      \n      // Verify member added\n      cy.get('[data-testid=\"team-member-list\"]').should('contain', 'Jane Smith')\n      \n      // Edit member\n      cy.get('[data-testid=\"edit-member-2\"]').click()\n      cy.get('[data-testid=\"role-input\"]').clear().type('Lead Developer')\n      cy.get('[data-testid=\"allocation-input\"]').clear().type('75')\n      cy.get('[data-testid=\"save-member-button\"]').click()\n      \n      // Verify changes\n      cy.get('[data-testid=\"team-member-list\"]').should('contain', 'Lead Developer')\n      cy.get('[data-testid=\"team-member-list\"]').should('contain', '75%')\n      \n      // Remove member\n      cy.get('[data-testid=\"remove-member-2\"]').click()\n      cy.get('[data-testid=\"confirm-remove\"]').click()\n      \n      // Verify member removed\n      cy.get('[data-testid=\"team-member-list\"]').should('not.contain', 'Jane Smith')\n    })\n\n    it('should show team analytics', () => {\n      // Verify team statistics are displayed\n      cy.get('[data-testid=\"total-hours\"]').should('be.visible')\n      cy.get('[data-testid=\"average-hours\"]').should('be.visible')\n      cy.get('[data-testid=\"active-members\"]').should('be.visible')\n      \n      // Verify allocation chart\n      cy.get('[data-testid=\"allocation-chart\"]').should('be.visible')\n    })\n  })\n\n  describe('Project Status Workflow', () => {\n    it('should transition project through statuses', () => {\n      cy.visit('/app/projects/1')\n      \n      // Start with planning status\n      cy.get('[data-testid=\"project-status\"]').should('contain', 'Planning')\n      \n      // Change to active\n      cy.get('[data-testid=\"status-dropdown\"]').click()\n      cy.get('[data-testid=\"status-active\"]').click()\n      cy.get('[data-testid=\"confirm-status-change\"]').click()\n      \n      // Verify status changed\n      cy.get('[data-testid=\"project-status\"]').should('contain', 'Active')\n      cy.get('[data-testid=\"success-message\"]').should('contain', 'Project status updated')\n      \n      // Change to completed\n      cy.get('[data-testid=\"status-dropdown\"]').click()\n      cy.get('[data-testid=\"status-completed\"]').click()\n      cy.get('[data-testid=\"confirm-status-change\"]').click()\n      \n      // Verify completion\n      cy.get('[data-testid=\"project-status\"]').should('contain', 'Completed')\n      cy.get('[data-testid=\"completion-date\"]').should('be.visible')\n    })\n  })\n\n  describe('Project KPI Management', () => {\n    beforeEach(() => {\n      cy.visit('/app/projects/1')\n      cy.get('[data-testid=\"kpi-tab\"]').click()\n    })\n\n    it('should manage project KPIs', () => {\n      // Add KPI\n      cy.get('[data-testid=\"add-kpi-button\"]').click()\n      cy.get('[data-testid=\"kpi-name\"]').type('Budget Utilization')\n      cy.get('[data-testid=\"kpi-target\"]').type('90')\n      cy.get('[data-testid=\"kpi-unit\"]').select('Percentage')\n      cy.get('[data-testid=\"kpi-category\"]').select('Budget')\n      cy.get('[data-testid=\"save-kpi-button\"]').click()\n      \n      // Verify KPI added\n      cy.get('[data-testid=\"kpi-list\"]').should('contain', 'Budget Utilization')\n      cy.get('[data-testid=\"kpi-list\"]').should('contain', '90%')\n      \n      // Update KPI value\n      cy.get('[data-testid=\"update-kpi-1\"]').click()\n      cy.get('[data-testid=\"current-value\"]').type('75')\n      cy.get('[data-testid=\"save-kpi-value\"]').click()\n      \n      // Verify progress bar\n      cy.get('[data-testid=\"kpi-progress\"]').should('have.attr', 'value', '83.33') // 75/90*100\n    })\n  })\n\n  describe('Project Expenses Tracking', () => {\n    beforeEach(() => {\n      cy.visit('/app/projects/1')\n      cy.get('[data-testid=\"expenses-tab\"]').click()\n    })\n\n    it('should track project expenses', () => {\n      // Add expense\n      cy.get('[data-testid=\"add-expense-button\"]').click()\n      cy.get('[data-testid=\"expense-description\"]').type('Software License')\n      cy.get('[data-testid=\"expense-amount\"]').type('299.99')\n      cy.get('[data-testid=\"expense-category\"]').select('Software')\n      cy.get('[data-testid=\"expense-date\"]').type('2025-01-15')\n      cy.get('[data-testid=\"save-expense-button\"]').click()\n      \n      // Verify expense added\n      cy.get('[data-testid=\"expense-list\"]').should('contain', 'Software License')\n      cy.get('[data-testid=\"expense-list\"]').should('contain', '€299.99')\n      \n      // Verify budget impact\n      cy.get('[data-testid=\"total-expenses\"]').should('contain', '€299.99')\n      cy.get('[data-testid=\"remaining-budget\"]').should('contain', '€49,700.01')\n      \n      // Approve expense\n      cy.get('[data-testid=\"approve-expense-1\"]').click()\n      cy.get('[data-testid=\"expense-status-1\"]').should('contain', 'Approved')\n    })\n  })\n\n  describe('Project Timesheet Integration', () => {\n    beforeEach(() => {\n      cy.visit('/app/projects/1')\n      cy.get('[data-testid=\"timesheet-tab\"]').click()\n    })\n\n    it('should display project timesheet data', () => {\n      // Verify timesheet grid\n      cy.get('[data-testid=\"timesheet-grid\"]').should('be.visible')\n      \n      // Verify team member hours\n      cy.get('[data-testid=\"member-hours\"]').should('contain', 'John Doe')\n      cy.get('[data-testid=\"total-project-hours\"]').should('be.visible')\n      \n      // Filter by date range\n      cy.get('[data-testid=\"date-from\"]').type('2025-01-01')\n      cy.get('[data-testid=\"date-to\"]').type('2025-01-31')\n      cy.get('[data-testid=\"filter-timesheet\"]').click()\n      \n      // Verify filtered results\n      cy.get('[data-testid=\"filtered-hours\"]').should('be.visible')\n    })\n  })\n})\n\n// Custom commands for E2E tests\nCypress.Commands.add('login', (username, password) => {\n  cy.session([username, password], () => {\n    cy.visit('/login')\n    cy.get('[data-testid=\"username\"]').type(username)\n    cy.get('[data-testid=\"password\"]').type(password)\n    cy.get('[data-testid=\"login-button\"]').click()\n    cy.url().should('include', '/app/dashboard')\n  })\n})\n"}