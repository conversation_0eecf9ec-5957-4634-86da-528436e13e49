{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_polloption_model.py"}, "originalCode": "\"\"\"Unit tests for PollOption model.\"\"\"\nimport pytest\nfrom models import PollOption, Poll, User\nfrom extensions import db\n\nclass TestPollOptionModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n        \n        self.poll = Poll.query.first()\n        if not self.poll:\n            self.poll = Poll(title='Test Poll', description='Test', author_id=self.user.id)  # Campo corretto\n            db.session.add(self.poll)\n            db.session.commit()\n\n    def test_polloption_creation_basic(self):\n        option = PollOption(\n            poll_id=self.poll.id,\n            option_text='Option A'  # Campo corretto è 'option_text'\n            # Rimosso order_index - non esiste nel modello\n        )\n        db.session.add(option)\n        db.session.commit()\n\n        assert option.id is not None\n        assert option.poll_id == self.poll.id\n        assert option.option_text == 'Option A'\n\n    def test_polloption_multiple_options(self):\n        options = []\n        for i in range(4):\n            option = PollOption(\n                poll_id=self.poll.id,\n                option_text=f'Option {chr(65+i)}'  # Campo corretto è 'option_text'\n                # Rimosso order_index - non esiste nel modello\n            )\n            options.append(option)\n\n        db.session.add_all(options)\n        db.session.commit()\n\n        for i, option in enumerate(options):\n            assert option.option_text == f'Option {chr(65+i)}'\n\n    def test_polloption_deletion(self):\n        option = PollOption(\n            poll_id=self.poll.id,\n            text='To Delete',\n            order_index=1\n        )\n        db.session.add(option)\n        db.session.commit()\n        option_id = option.id\n        \n        db.session.delete(option)\n        db.session.commit()\n        \n        deleted = PollOption.query.get(option_id)\n        assert deleted is None\n", "modifiedCode": "\"\"\"Unit tests for PollOption model.\"\"\"\nimport pytest\nfrom models import PollOption, Poll, User\nfrom extensions import db\n\nclass TestPollOptionModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n        \n        self.poll = Poll.query.first()\n        if not self.poll:\n            self.poll = Poll(title='Test Poll', description='Test', author_id=self.user.id)  # Campo corretto\n            db.session.add(self.poll)\n            db.session.commit()\n\n    def test_polloption_creation_basic(self):\n        option = PollOption(\n            poll_id=self.poll.id,\n            option_text='Option A'  # Campo corretto è 'option_text'\n            # Rimosso order_index - non esiste nel modello\n        )\n        db.session.add(option)\n        db.session.commit()\n\n        assert option.id is not None\n        assert option.poll_id == self.poll.id\n        assert option.option_text == 'Option A'\n\n    def test_polloption_multiple_options(self):\n        options = []\n        for i in range(4):\n            option = PollOption(\n                poll_id=self.poll.id,\n                option_text=f'Option {chr(65+i)}'  # Campo corretto è 'option_text'\n                # Rimosso order_index - non esiste nel modello\n            )\n            options.append(option)\n\n        db.session.add_all(options)\n        db.session.commit()\n\n        for i, option in enumerate(options):\n            assert option.option_text == f'Option {chr(65+i)}'\n\n    def test_polloption_deletion(self):\n        option = PollOption(\n            poll_id=self.poll.id,\n            option_text='To Delete'  # Campo corretto è 'option_text'\n            # Rimosso order_index - non esiste nel modello\n        )\n        db.session.add(option)\n        db.session.commit()\n        option_id = option.id\n        \n        db.session.delete(option)\n        db.session.commit()\n        \n        deleted = PollOption.query.get(option_id)\n        assert deleted is None\n"}