{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_fundingexpense_model.py"}, "modifiedCode": "\"\"\"Unit tests for FundingExpense model.\"\"\"\nimport pytest\nfrom models import FundingExpense, FundingApplication, User\nfrom extensions import db\n\nclass TestFundingExpenseModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n\n    def test_fundingexpense_creation_basic(self):\n        expense = FundingExpense(\n            description='Test Expense',\n            amount=1000.0,\n            category='equipment'\n        )\n        db.session.add(expense)\n        db.session.commit()\n        \n        assert expense.id is not None\n        assert expense.description == 'Test Expense'\n        assert expense.amount == 1000.0\n\n    def test_fundingexpense_deletion(self):\n        expense = FundingExpense(description='To Delete', amount=100.0)\n        db.session.add(expense)\n        db.session.commit()\n        expense_id = expense.id\n        \n        db.session.delete(expense)\n        db.session.commit()\n        \n        deleted = FundingExpense.query.get(expense_id)\n        assert deleted is None\n"}