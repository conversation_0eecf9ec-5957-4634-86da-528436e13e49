{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_skill_model.py"}, "originalCode": "\"\"\"\nUnit tests for Skill model.\nTests business logic, validations, and model functionality.\n\"\"\"\n\nimport pytest\nfrom datetime import datetime\nfrom models import Skill, User\nfrom extensions import db\n\n\nclass TestSkillModel:\n    \"\"\"Test suite for Skill model unit tests\"\"\"\n\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        \"\"\"Setup test data for each test method\"\"\"\n        self.app = app\n        self.user = test_user\n\n    def test_skill_creation_basic(self):\n        \"\"\"Test basic skill creation with required fields\"\"\"\n        skill = Skill(\n            name='Python',\n            category='Programming Languages'\n        )\n\n        db.session.add(skill)\n        db.session.commit()\n\n        assert skill.id is not None\n        assert skill.name == 'Python'\n        assert skill.category == 'Programming Languages'\n\n    def test_skill_creation_complete(self):\n        \"\"\"Test skill creation with all fields (based on real DB schema)\"\"\"\n        skill = Skill(\n            name='React.js',\n            category='Frontend Frameworks',\n            description='JavaScript library for building user interfaces'\n        )\n\n        db.session.add(skill)\n        db.session.commit()\n\n        assert skill.description == 'JavaScript library for building user interfaces'\n        assert skill.name == 'React.js'\n        assert skill.category == 'Frontend Frameworks'\n\n    def test_skill_repr_method(self):\n        \"\"\"Test string representation of skill\"\"\"\n        skill = Skill(name='JavaScript', category='Programming')\n        \n        expected_repr = '<Skill JavaScript>'\n        assert repr(skill) == expected_repr\n\n    def test_skill_name_uniqueness(self):\n        \"\"\"Test that skill names should be unique\"\"\"\n        skill1 = Skill(name='Java', category='Programming')\n        skill2 = Skill(name='Java', category='Programming')  # Duplicate name\n        \n        db.session.add(skill1)\n        db.session.commit()\n        \n        # This should potentially raise an error if uniqueness is enforced\n        # For now, we test that both can be created (depends on DB constraints)\n        db.session.add(skill2)\n        try:\n            db.session.commit()\n            # If no constraint, both will exist\n            assert True\n        except Exception:\n            # If constraint exists, this is expected\n            db.session.rollback()\n            assert True\n\n    def test_skill_categories(self):\n        \"\"\"Test different skill categories\"\"\"\n        categories = [\n            'Programming Languages',\n            'Frameworks',\n            'Databases',\n            'Cloud Platforms',\n            'Soft Skills',\n            'Project Management',\n            'Design Tools'\n        ]\n        \n        skills = []\n        for i, category in enumerate(categories):\n            skill = Skill(\n                name=f'Skill {i}',\n                category=category\n            )\n            skills.append(skill)\n        \n        db.session.add_all(skills)\n        db.session.commit()\n        \n        for skill, expected_category in zip(skills, categories):\n            assert skill.category == expected_category\n\n    def test_skill_technical_flag(self):\n        \"\"\"Test is_technical flag functionality\"\"\"\n        technical_skill = Skill(\n            name='Docker',\n            category='DevOps',\n            is_technical=True\n        )\n        \n        soft_skill = Skill(\n            name='Leadership',\n            category='Soft Skills',\n            is_technical=False\n        )\n        \n        db.session.add_all([technical_skill, soft_skill])\n        db.session.commit()\n        \n        assert technical_skill.is_technical is True\n        assert soft_skill.is_technical is False\n\n    def test_skill_active_flag(self):\n        \"\"\"Test is_active flag functionality\"\"\"\n        active_skill = Skill(\n            name='TypeScript',\n            category='Programming',\n            is_active=True\n        )\n        \n        inactive_skill = Skill(\n            name='Flash',\n            category='Legacy',\n            is_active=False\n        )\n        \n        db.session.add_all([active_skill, inactive_skill])\n        db.session.commit()\n        \n        assert active_skill.is_active is True\n        assert inactive_skill.is_active is False\n\n    def test_skill_description_field(self):\n        \"\"\"Test description field for detailed information\"\"\"\n        long_description = \"\"\"\n        Vue.js is a progressive JavaScript framework for building user interfaces.\n        It is designed to be incrementally adoptable and focuses on the view layer.\n        Vue.js is known for its gentle learning curve and excellent documentation.\n        \"\"\"\n        \n        skill = Skill(\n            name='Vue.js',\n            category='Frontend Frameworks',\n            description=long_description.strip()\n        )\n        \n        db.session.add(skill)\n        db.session.commit()\n        \n        assert skill.description == long_description.strip()\n\n    def test_skill_timestamps(self):\n        \"\"\"Test automatic timestamp handling\"\"\"\n        skill = Skill(\n            name='Timestamp Test',\n            category='Testing'\n        )\n        \n        db.session.add(skill)\n        db.session.commit()\n        \n        # Test created_at is set\n        assert skill.created_at is not None\n        assert isinstance(skill.created_at, datetime)\n        \n        # Test updated_at is set\n        assert skill.updated_at is not None\n        assert isinstance(skill.updated_at, datetime)\n        \n        # Test updated_at changes on update\n        original_updated_at = skill.updated_at\n        skill.description = 'Updated description'\n        db.session.commit()\n        \n        assert skill.updated_at > original_updated_at\n\n    def test_skill_query_by_category(self):\n        \"\"\"Test querying skills by category\"\"\"\n        programming_skills = [\n            Skill(name='Python', category='Programming Languages'),\n            Skill(name='Java', category='Programming Languages'),\n            Skill(name='C++', category='Programming Languages')\n        ]\n        \n        framework_skills = [\n            Skill(name='Django', category='Web Frameworks'),\n            Skill(name='Flask', category='Web Frameworks')\n        ]\n        \n        db.session.add_all(programming_skills + framework_skills)\n        db.session.commit()\n        \n        # Query programming languages\n        prog_results = Skill.query.filter_by(category='Programming Languages').all()\n        assert len(prog_results) == 3\n        \n        # Query web frameworks\n        framework_results = Skill.query.filter_by(category='Web Frameworks').all()\n        assert len(framework_results) == 2\n\n    def test_skill_query_active_only(self):\n        \"\"\"Test querying only active skills\"\"\"\n        active_skills = [\n            Skill(name='Active 1', category='Test', is_active=True),\n            Skill(name='Active 2', category='Test', is_active=True)\n        ]\n        \n        inactive_skills = [\n            Skill(name='Inactive 1', category='Test', is_active=False),\n            Skill(name='Inactive 2', category='Test', is_active=False)\n        ]\n        \n        db.session.add_all(active_skills + inactive_skills)\n        db.session.commit()\n        \n        # Query only active skills\n        active_results = Skill.query.filter_by(is_active=True).all()\n        active_names = [skill.name for skill in active_results]\n        \n        assert 'Active 1' in active_names\n        assert 'Active 2' in active_names\n        assert 'Inactive 1' not in active_names\n        assert 'Inactive 2' not in active_names\n\n    def test_skill_query_technical_only(self):\n        \"\"\"Test querying only technical skills\"\"\"\n        technical_skills = [\n            Skill(name='Technical 1', category='Tech', is_technical=True),\n            Skill(name='Technical 2', category='Tech', is_technical=True)\n        ]\n        \n        soft_skills = [\n            Skill(name='Communication', category='Soft', is_technical=False),\n            Skill(name='Teamwork', category='Soft', is_technical=False)\n        ]\n        \n        db.session.add_all(technical_skills + soft_skills)\n        db.session.commit()\n        \n        # Query only technical skills\n        tech_results = Skill.query.filter_by(is_technical=True).all()\n        tech_names = [skill.name for skill in tech_results]\n        \n        assert 'Technical 1' in tech_names\n        assert 'Technical 2' in tech_names\n        assert 'Communication' not in tech_names\n        assert 'Teamwork' not in tech_names\n\n    def test_skill_update_operations(self):\n        \"\"\"Test skill update operations\"\"\"\n        skill = Skill(\n            name='Original Name',\n            category='Original Category',\n            is_active=True\n        )\n        \n        db.session.add(skill)\n        db.session.commit()\n        \n        # Update skill\n        skill.name = 'Updated Name'\n        skill.category = 'Updated Category'\n        skill.is_active = False\n        skill.description = 'Added description'\n        \n        db.session.commit()\n        \n        # Verify updates\n        updated_skill = Skill.query.get(skill.id)\n        assert updated_skill.name == 'Updated Name'\n        assert updated_skill.category == 'Updated Category'\n        assert updated_skill.is_active is False\n        assert updated_skill.description == 'Added description'\n\n    def test_skill_deletion(self):\n        \"\"\"Test skill deletion\"\"\"\n        skill = Skill(\n            name='To Delete',\n            category='Test'\n        )\n        \n        db.session.add(skill)\n        db.session.commit()\n        skill_id = skill.id\n        \n        # Delete skill\n        db.session.delete(skill)\n        db.session.commit()\n        \n        # Verify deletion\n        deleted_skill = Skill.query.get(skill_id)\n        assert deleted_skill is None\n\n    def test_skill_search_functionality(self):\n        \"\"\"Test skill search by name\"\"\"\n        skills = [\n            Skill(name='JavaScript', category='Programming'),\n            Skill(name='Java', category='Programming'),\n            Skill(name='Python', category='Programming')\n        ]\n        \n        db.session.add_all(skills)\n        db.session.commit()\n        \n        # Search for skills containing 'Java'\n        java_skills = Skill.query.filter(Skill.name.contains('Java')).all()\n        java_names = [skill.name for skill in java_skills]\n        \n        assert 'JavaScript' in java_names\n        assert 'Java' in java_names\n        assert 'Python' not in java_names\n\n    def test_skill_default_values(self):\n        \"\"\"Test default values for optional fields\"\"\"\n        skill = Skill(\n            name='Default Test',\n            category='Test'\n        )\n        \n        db.session.add(skill)\n        db.session.commit()\n        \n        # Check default values\n        assert skill.is_technical is True  # Assuming default is True\n        assert skill.is_active is True     # Assuming default is True\n        assert skill.description is None   # Should be None if not set\n", "modifiedCode": "\"\"\"\nUnit tests for Skill model.\nTests business logic, validations, and model functionality.\n\"\"\"\n\nimport pytest\nfrom datetime import datetime\nfrom models import Skill, User\nfrom extensions import db\n\n\nclass TestSkillModel:\n    \"\"\"Test suite for Skill model unit tests\"\"\"\n\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        \"\"\"Setup test data for each test method\"\"\"\n        self.app = app\n        self.user = test_user\n\n    def test_skill_creation_basic(self):\n        \"\"\"Test basic skill creation with required fields\"\"\"\n        skill = Skill(\n            name='Python',\n            category='Programming Languages'\n        )\n\n        db.session.add(skill)\n        db.session.commit()\n\n        assert skill.id is not None\n        assert skill.name == 'Python'\n        assert skill.category == 'Programming Languages'\n\n    def test_skill_creation_complete(self):\n        \"\"\"Test skill creation with all fields (based on real DB schema)\"\"\"\n        skill = Skill(\n            name='React.js',\n            category='Frontend Frameworks',\n            description='JavaScript library for building user interfaces'\n        )\n\n        db.session.add(skill)\n        db.session.commit()\n\n        assert skill.description == 'JavaScript library for building user interfaces'\n        assert skill.name == 'React.js'\n        assert skill.category == 'Frontend Frameworks'\n\n    def test_skill_repr_method(self):\n        \"\"\"Test string representation of skill\"\"\"\n        skill = Skill(name='JavaScript', category='Programming')\n        \n        expected_repr = '<Skill JavaScript>'\n        assert repr(skill) == expected_repr\n\n    def test_skill_name_uniqueness(self):\n        \"\"\"Test that skill names should be unique\"\"\"\n        skill1 = Skill(name='Java', category='Programming')\n        skill2 = Skill(name='Java', category='Programming')  # Duplicate name\n        \n        db.session.add(skill1)\n        db.session.commit()\n        \n        # This should potentially raise an error if uniqueness is enforced\n        # For now, we test that both can be created (depends on DB constraints)\n        db.session.add(skill2)\n        try:\n            db.session.commit()\n            # If no constraint, both will exist\n            assert True\n        except Exception:\n            # If constraint exists, this is expected\n            db.session.rollback()\n            assert True\n\n    def test_skill_categories(self):\n        \"\"\"Test different skill categories\"\"\"\n        categories = [\n            'Programming Languages',\n            'Frameworks',\n            'Databases',\n            'Cloud Platforms',\n            'Soft Skills',\n            'Project Management',\n            'Design Tools'\n        ]\n        \n        skills = []\n        for i, category in enumerate(categories):\n            skill = Skill(\n                name=f'Skill {i}',\n                category=category\n            )\n            skills.append(skill)\n        \n        db.session.add_all(skills)\n        db.session.commit()\n        \n        for skill, expected_category in zip(skills, categories):\n            assert skill.category == expected_category\n\n    def test_skill_technical_flag(self):\n        \"\"\"Test is_technical flag functionality\"\"\"\n        technical_skill = Skill(\n            name='Docker',\n            category='DevOps',\n            is_technical=True\n        )\n        \n        soft_skill = Skill(\n            name='Leadership',\n            category='Soft Skills',\n            is_technical=False\n        )\n        \n        db.session.add_all([technical_skill, soft_skill])\n        db.session.commit()\n        \n        assert technical_skill.is_technical is True\n        assert soft_skill.is_technical is False\n\n    def test_skill_active_flag(self):\n        \"\"\"Test is_active flag functionality\"\"\"\n        active_skill = Skill(\n            name='TypeScript',\n            category='Programming',\n            is_active=True\n        )\n        \n        inactive_skill = Skill(\n            name='Flash',\n            category='Legacy',\n            is_active=False\n        )\n        \n        db.session.add_all([active_skill, inactive_skill])\n        db.session.commit()\n        \n        assert active_skill.is_active is True\n        assert inactive_skill.is_active is False\n\n    def test_skill_description_field(self):\n        \"\"\"Test description field for detailed information\"\"\"\n        long_description = \"\"\"\n        Vue.js is a progressive JavaScript framework for building user interfaces.\n        It is designed to be incrementally adoptable and focuses on the view layer.\n        Vue.js is known for its gentle learning curve and excellent documentation.\n        \"\"\"\n        \n        skill = Skill(\n            name='Vue.js',\n            category='Frontend Frameworks',\n            description=long_description.strip()\n        )\n        \n        db.session.add(skill)\n        db.session.commit()\n        \n        assert skill.description == long_description.strip()\n\n    def test_skill_timestamps(self):\n        \"\"\"Test automatic timestamp handling\"\"\"\n        skill = Skill(\n            name='Timestamp Test',\n            category='Testing'\n        )\n        \n        db.session.add(skill)\n        db.session.commit()\n        \n        # Test created_at is set\n        assert skill.created_at is not None\n        assert isinstance(skill.created_at, datetime)\n        \n        # Test updated_at is set\n        assert skill.updated_at is not None\n        assert isinstance(skill.updated_at, datetime)\n        \n        # Test updated_at changes on update\n        original_updated_at = skill.updated_at\n        skill.description = 'Updated description'\n        db.session.commit()\n        \n        assert skill.updated_at > original_updated_at\n\n    def test_skill_query_by_category(self):\n        \"\"\"Test querying skills by category\"\"\"\n        programming_skills = [\n            Skill(name='Python', category='Programming Languages'),\n            Skill(name='Java', category='Programming Languages'),\n            Skill(name='C++', category='Programming Languages')\n        ]\n        \n        framework_skills = [\n            Skill(name='Django', category='Web Frameworks'),\n            Skill(name='Flask', category='Web Frameworks')\n        ]\n        \n        db.session.add_all(programming_skills + framework_skills)\n        db.session.commit()\n        \n        # Query programming languages\n        prog_results = Skill.query.filter_by(category='Programming Languages').all()\n        assert len(prog_results) == 3\n        \n        # Query web frameworks\n        framework_results = Skill.query.filter_by(category='Web Frameworks').all()\n        assert len(framework_results) == 2\n\n    def test_skill_query_active_only(self):\n        \"\"\"Test querying only active skills\"\"\"\n        active_skills = [\n            Skill(name='Active 1', category='Test', is_active=True),\n            Skill(name='Active 2', category='Test', is_active=True)\n        ]\n        \n        inactive_skills = [\n            Skill(name='Inactive 1', category='Test', is_active=False),\n            Skill(name='Inactive 2', category='Test', is_active=False)\n        ]\n        \n        db.session.add_all(active_skills + inactive_skills)\n        db.session.commit()\n        \n        # Query only active skills\n        active_results = Skill.query.filter_by(is_active=True).all()\n        active_names = [skill.name for skill in active_results]\n        \n        assert 'Active 1' in active_names\n        assert 'Active 2' in active_names\n        assert 'Inactive 1' not in active_names\n        assert 'Inactive 2' not in active_names\n\n    def test_skill_query_technical_only(self):\n        \"\"\"Test querying only technical skills\"\"\"\n        technical_skills = [\n            Skill(name='Technical 1', category='Tech', is_technical=True),\n            Skill(name='Technical 2', category='Tech', is_technical=True)\n        ]\n        \n        soft_skills = [\n            Skill(name='Communication', category='Soft', is_technical=False),\n            Skill(name='Teamwork', category='Soft', is_technical=False)\n        ]\n        \n        db.session.add_all(technical_skills + soft_skills)\n        db.session.commit()\n        \n        # Query only technical skills\n        tech_results = Skill.query.filter_by(is_technical=True).all()\n        tech_names = [skill.name for skill in tech_results]\n        \n        assert 'Technical 1' in tech_names\n        assert 'Technical 2' in tech_names\n        assert 'Communication' not in tech_names\n        assert 'Teamwork' not in tech_names\n\n    def test_skill_update_operations(self):\n        \"\"\"Test skill update operations\"\"\"\n        skill = Skill(\n            name='Original Name',\n            category='Original Category',\n            is_active=True\n        )\n        \n        db.session.add(skill)\n        db.session.commit()\n        \n        # Update skill\n        skill.name = 'Updated Name'\n        skill.category = 'Updated Category'\n        skill.is_active = False\n        skill.description = 'Added description'\n        \n        db.session.commit()\n        \n        # Verify updates\n        updated_skill = Skill.query.get(skill.id)\n        assert updated_skill.name == 'Updated Name'\n        assert updated_skill.category == 'Updated Category'\n        assert updated_skill.is_active is False\n        assert updated_skill.description == 'Added description'\n\n    def test_skill_deletion(self):\n        \"\"\"Test skill deletion\"\"\"\n        skill = Skill(\n            name='To Delete',\n            category='Test'\n        )\n        \n        db.session.add(skill)\n        db.session.commit()\n        skill_id = skill.id\n        \n        # Delete skill\n        db.session.delete(skill)\n        db.session.commit()\n        \n        # Verify deletion\n        deleted_skill = Skill.query.get(skill_id)\n        assert deleted_skill is None\n\n    def test_skill_search_functionality(self):\n        \"\"\"Test skill search by name\"\"\"\n        skills = [\n            Skill(name='JavaScript', category='Programming'),\n            Skill(name='Java', category='Programming'),\n            Skill(name='Python', category='Programming')\n        ]\n        \n        db.session.add_all(skills)\n        db.session.commit()\n        \n        # Search for skills containing 'Java'\n        java_skills = Skill.query.filter(Skill.name.contains('Java')).all()\n        java_names = [skill.name for skill in java_skills]\n        \n        assert 'JavaScript' in java_names\n        assert 'Java' in java_names\n        assert 'Python' not in java_names\n\n    def test_skill_default_values(self):\n        \"\"\"Test default values for optional fields\"\"\"\n        skill = Skill(\n            name='Default Test',\n            category='Test'\n        )\n        \n        db.session.add(skill)\n        db.session.commit()\n        \n        # Check default values\n        assert skill.is_technical is True  # Assuming default is True\n        assert skill.is_active is True     # Assuming default is True\n        assert skill.description is None   # Should be None if not set\n"}