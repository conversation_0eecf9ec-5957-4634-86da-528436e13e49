{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/testing/test-execution-guide.md"}, "modifiedCode": "# 🚀 Test Execution Guide\n\nGuida completa per l'esecuzione dei test e l'interpretazione dei risultati.\n\n## 📋 Indice\n\n- [Comandi di Esecuzione](#comandi-di-esecuzione)\n- [Script Automatizzati](#script-automatizzati)\n- [Coverage Reports](#coverage-reports)\n- [CI/CD Integration](#cicd-integration)\n- [Debugging Tests](#debugging-tests)\n- [Performance Monitoring](#performance-monitoring)\n- [Troubleshooting](#troubleshooting)\n\n## ⚡ Comandi di Esecuzione\n\n### Backend Tests\n\n```bash\n# Tutti i test backend\ncd backend && python -m pytest tests/ -v\n\n# Solo unit tests\npython -m pytest tests/unit/ -v\n\n# Solo integration tests\npython -m pytest tests/integration/ -v\n\n# Solo API tests\npython -m pytest tests/api/ -v\n\n# Con coverage\npython -m pytest tests/ --cov=. --cov-report=html --cov-report=term\n\n# Test specifico\npython -m pytest tests/unit/test_models.py::TestProjectModel::test_budget_calculation -v\n\n# Test con markers\npython -m pytest -m \"slow\" -v  # Solo test lenti\npython -m pytest -m \"not slow\" -v  # Escludi test lenti\n\n# Parallel execution\npython -m pytest tests/ -n auto  # Usa tutti i core disponibili\npython -m pytest tests/ -n 4     # Usa 4 processi\n```\n\n### Frontend Tests\n\n```bash\n# Tutti i test frontend\ncd frontend && npm run test\n\n# Test specifici\nnpm run test:unit          # Solo component tests\nnpm run test:integration   # Solo integration tests\nnpm run test:e2e          # Solo E2E tests\n\n# Watch mode per sviluppo\nnpm run test:watch\n\n# Coverage\nnpm run test:coverage\n\n# Test specifico file\nnpm run test ProjectTeam.test.js\n\n# Test con pattern\nnpm run test -- --grep \"should handle form submission\"\n\n# Debug mode\nnpm run test:debug\n```\n\n### E2E Tests\n\n```bash\n# Cypress headless\ncd frontend && npx cypress run\n\n# Cypress interactive\nnpx cypress open\n\n# Specific spec\nnpx cypress run --spec \"cypress/e2e/projects/project-creation.cy.js\"\n\n# Specific browser\nnpx cypress run --browser chrome\nnpx cypress run --browser firefox\n\n# With video recording\nnpx cypress run --record --key <record-key>\n\n# Parallel execution\nnpx cypress run --parallel --record --key <record-key>\n```\n\n## 🤖 Script Automatizzati\n\n### Script Esecuzione Completa\n\n```bash\n#!/bin/bash\n# scripts/run-all-tests.sh\n\nset -e\n\necho \"🚀 AVVIO TEST SUITE COMPLETA\"\necho \"==================================\"\n\n# Colors\nRED='\\033[0;31m'\nGREEN='\\033[0;32m'\nYELLOW='\\033[1;33m'\nBLUE='\\033[0;34m'\nNC='\\033[0m'\n\nprint_status() {\n    echo -e \"${BLUE}[INFO]${NC} $1\"\n}\n\nprint_success() {\n    echo -e \"${GREEN}[SUCCESS]${NC} $1\"\n}\n\nprint_error() {\n    echo -e \"${RED}[ERROR]${NC} $1\"\n}\n\n# Variables\nBACKEND_DIR=\"backend\"\nFRONTEND_DIR=\"frontend\"\nTEST_RESULTS_DIR=\"test-results\"\nTIMESTAMP=$(date +\"%Y%m%d_%H%M%S\")\n\nmkdir -p $TEST_RESULTS_DIR\n\n# 1. BACKEND TESTS\nprint_status \"🔧 ESECUZIONE BACKEND TESTS\"\ncd $BACKEND_DIR\n\n# Setup test database\nprint_status \"Setup database di test...\"\npython -c \"\nfrom app import create_app\nfrom extensions import db\napp = create_app()\nwith app.app_context():\n    db.create_all()\n    print('✅ Database di test creato')\n\"\n\n# Run tests with coverage\nprint_status \"Esecuzione backend tests...\"\npython -m pytest tests/ -v \\\n    --cov=. \\\n    --cov-report=html:../$TEST_RESULTS_DIR/backend-coverage-$TIMESTAMP \\\n    --cov-report=term-missing \\\n    --junitxml=../$TEST_RESULTS_DIR/backend-results-$TIMESTAMP.xml\n\nprint_success \"Backend tests completati\"\ncd ..\n\n# 2. FRONTEND TESTS\nprint_status \"🎨 ESECUZIONE FRONTEND TESTS\"\ncd $FRONTEND_DIR\n\n# Install dependencies if needed\nif [ ! -d \"node_modules\" ]; then\n    print_status \"Installazione dipendenze...\"\n    npm install\nfi\n\n# Run unit and integration tests\nprint_status \"Esecuzione frontend tests...\"\nnpm run test:coverage -- \\\n    --reporter=junit \\\n    --outputFile=../$TEST_RESULTS_DIR/frontend-results-$TIMESTAMP.xml\n\nprint_success \"Frontend tests completati\"\n\n# 3. E2E TESTS\nprint_status \"🌐 ESECUZIONE E2E TESTS\"\n\n# Start backend server\nprint_status \"Avvio backend server...\"\ncd ../$BACKEND_DIR\npython main.py &\nBACKEND_PID=$!\nsleep 5\n\ncd ../$FRONTEND_DIR\n\n# Start frontend dev server\nprint_status \"Avvio frontend dev server...\"\nnpm run dev &\nFRONTEND_PID=$!\nsleep 10\n\n# Run E2E tests\nprint_status \"Esecuzione E2E tests...\"\nnpx cypress run \\\n    --reporter junit \\\n    --reporter-options \"mochaFile=../$TEST_RESULTS_DIR/e2e-results-$TIMESTAMP.xml\"\n\n# Cleanup\nprint_status \"Cleanup servers...\"\nkill $BACKEND_PID 2>/dev/null || true\nkill $FRONTEND_PID 2>/dev/null || true\n\ncd ..\n\n# 4. GENERATE REPORT\nprint_status \"📊 GENERAZIONE REPORT FINALE\"\n./scripts/generate-test-report.sh $TIMESTAMP\n\nprint_success \"🎉 TEST SUITE COMPLETA ESEGUITA!\"\nprint_status \"📊 Risultati in: $TEST_RESULTS_DIR/\"\n```\n\n### Script Generazione Report\n\n```bash\n#!/bin/bash\n# scripts/generate-test-report.sh\n\nTIMESTAMP=$1\nTEST_RESULTS_DIR=\"test-results\"\n\n# Extract test counts from XML reports\nBACKEND_TESTS=$(grep -o 'tests=\"[0-9]*\"' $TEST_RESULTS_DIR/backend-results-$TIMESTAMP.xml | grep -o '[0-9]*' || echo \"0\")\nFRONTEND_TESTS=$(grep -o 'tests=\"[0-9]*\"' $TEST_RESULTS_DIR/frontend-results-$TIMESTAMP.xml | grep -o '[0-9]*' || echo \"0\")\nE2E_TESTS=$(grep -o 'tests=\"[0-9]*\"' $TEST_RESULTS_DIR/e2e-results-$TIMESTAMP.xml | grep -o '[0-9]*' || echo \"0\")\n\nTOTAL_TESTS=$((BACKEND_TESTS + FRONTEND_TESTS + E2E_TESTS))\n\n# Generate HTML report\ncat > $TEST_RESULTS_DIR/test-report-$TIMESTAMP.html << EOF\n<!DOCTYPE html>\n<html>\n<head>\n    <title>Test Execution Report - $TIMESTAMP</title>\n    <style>\n        body { font-family: Arial, sans-serif; margin: 40px; }\n        .header { background: #f8f9fa; padding: 20px; border-radius: 8px; }\n        .stats { display: flex; gap: 20px; margin: 20px 0; }\n        .stat-card { background: #e3f2fd; padding: 15px; border-radius: 8px; flex: 1; }\n        .success { background: #e8f5e8; }\n        .warning { background: #fff3cd; }\n        .error { background: #f8d7da; }\n        .coverage { margin: 20px 0; }\n        .coverage-bar { background: #ddd; height: 20px; border-radius: 10px; overflow: hidden; }\n        .coverage-fill { background: #4caf50; height: 100%; transition: width 0.3s; }\n    </style>\n</head>\n<body>\n    <div class=\"header\">\n        <h1>🧪 Test Execution Report</h1>\n        <p><strong>Timestamp:</strong> $TIMESTAMP</p>\n        <p><strong>Total Tests:</strong> $TOTAL_TESTS</p>\n    </div>\n    \n    <div class=\"stats\">\n        <div class=\"stat-card success\">\n            <h3>Backend Tests</h3>\n            <p><strong>$BACKEND_TESTS</strong> tests executed</p>\n            <p>✅ Unit, Integration, API</p>\n        </div>\n        <div class=\"stat-card success\">\n            <h3>Frontend Tests</h3>\n            <p><strong>$FRONTEND_TESTS</strong> tests executed</p>\n            <p>✅ Component, Integration, UI</p>\n        </div>\n        <div class=\"stat-card success\">\n            <h3>E2E Tests</h3>\n            <p><strong>$E2E_TESTS</strong> tests executed</p>\n            <p>✅ User Workflows</p>\n        </div>\n    </div>\n    \n    <div class=\"coverage\">\n        <h3>📊 Coverage Reports</h3>\n        <p><a href=\"backend-coverage-$TIMESTAMP/index.html\">Backend Coverage Report</a></p>\n        <p><a href=\"frontend-coverage-$TIMESTAMP/index.html\">Frontend Coverage Report</a></p>\n    </div>\n    \n    <div>\n        <h3>📁 Test Artifacts</h3>\n        <ul>\n            <li><a href=\"backend-results-$TIMESTAMP.xml\">Backend JUnit Results</a></li>\n            <li><a href=\"frontend-results-$TIMESTAMP.xml\">Frontend JUnit Results</a></li>\n            <li><a href=\"e2e-results-$TIMESTAMP.xml\">E2E JUnit Results</a></li>\n        </ul>\n    </div>\n    \n    <div>\n        <h3>🎯 Validated Use Cases</h3>\n        <ul>\n            <li>✅ Project Management (Creation, Editing, Team, Tasks)</li>\n            <li>✅ User Authentication & Authorization</li>\n            <li>✅ Timesheet Management & Approval</li>\n            <li>✅ Financial Tracking & KPIs</li>\n            <li>✅ Team Collaboration & Communication</li>\n            <li>✅ Dashboard & Analytics</li>\n        </ul>\n    </div>\n</body>\n</html>\nEOF\n\necho \"📊 Report HTML generato: $TEST_RESULTS_DIR/test-report-$TIMESTAMP.html\"\n```\n\n## 📊 Coverage Reports\n\n### Interpretazione Coverage Backend\n\n```bash\n# Genera coverage dettagliato\npython -m pytest --cov=. --cov-report=html --cov-report=term-missing\n\n# Coverage per modulo\npython -m pytest --cov=models --cov-report=term\npython -m pytest --cov=services --cov-report=term\npython -m pytest --cov=blueprints --cov-report=term\n\n# Coverage con soglie\npython -m pytest --cov=. --cov-fail-under=80\n\n# Coverage escludendo file\npython -m pytest --cov=. --cov-omit=\"*/tests/*,*/migrations/*\"\n```\n\n### Interpretazione Coverage Frontend\n\n```bash\n# Coverage dettagliato\nnpm run test:coverage\n\n# Coverage per directory\nnpm run test:coverage -- --include=\"src/components/**\"\nnpm run test:coverage -- --include=\"src/stores/**\"\n\n# Coverage con soglie\nnpm run test:coverage -- --coverage.thresholds.global.lines=70\n\n# Report in formati diversi\nnpm run test:coverage -- --coverage.reporter=html\nnpm run test:coverage -- --coverage.reporter=lcov\nnpm run test:coverage -- --coverage.reporter=json\n```\n\n## 🔧 CI/CD Integration\n\n### GitHub Actions Workflow\n\n```yaml\n# .github/workflows/tests.yml\nname: Test Suite\non: \n  push:\n    branches: [ main, develop ]\n  pull_request:\n    branches: [ main ]\n\njobs:\n  backend-tests:\n    runs-on: ubuntu-latest\n    services:\n      postgres:\n        image: postgres:13\n        env:\n          POSTGRES_PASSWORD: postgres\n          POSTGRES_DB: test_db\n        options: >-\n          --health-cmd pg_isready\n          --health-interval 10s\n          --health-timeout 5s\n          --health-retries 5\n    \n    steps:\n    - uses: actions/checkout@v2\n    \n    - name: Set up Python\n      uses: actions/setup-python@v2\n      with:\n        python-version: 3.9\n    \n    - name: Install dependencies\n      run: |\n        cd backend\n        pip install -r requirements.txt\n        pip install pytest pytest-cov\n    \n    - name: Run backend tests\n      run: |\n        cd backend\n        python -m pytest tests/ --cov=. --cov-report=xml\n    \n    - name: Upload coverage to Codecov\n      uses: codecov/codecov-action@v1\n      with:\n        file: backend/coverage.xml\n        flags: backend\n\n  frontend-tests:\n    runs-on: ubuntu-latest\n    \n    steps:\n    - uses: actions/checkout@v2\n    \n    - name: Set up Node.js\n      uses: actions/setup-node@v2\n      with:\n        node-version: '18'\n        cache: 'npm'\n        cache-dependency-path: frontend/package-lock.json\n    \n    - name: Install dependencies\n      run: |\n        cd frontend\n        npm ci\n    \n    - name: Run frontend tests\n      run: |\n        cd frontend\n        npm run test:coverage\n    \n    - name: Upload coverage\n      uses: codecov/codecov-action@v1\n      with:\n        file: frontend/coverage/lcov.info\n        flags: frontend\n\n  e2e-tests:\n    runs-on: ubuntu-latest\n    needs: [backend-tests, frontend-tests]\n    \n    steps:\n    - uses: actions/checkout@v2\n    \n    - name: Set up environment\n      run: |\n        # Setup backend and frontend\n        cd backend && pip install -r requirements.txt &\n        cd frontend && npm ci &\n        wait\n    \n    - name: Start services\n      run: |\n        cd backend && python main.py &\n        cd frontend && npm run dev &\n        sleep 30\n    \n    - name: Run E2E tests\n      run: |\n        cd frontend\n        npx cypress run --record --key ${{ secrets.CYPRESS_RECORD_KEY }}\n    \n    - name: Upload test artifacts\n      uses: actions/upload-artifact@v2\n      if: failure()\n      with:\n        name: cypress-screenshots\n        path: frontend/cypress/screenshots\n```\n\n## 🐛 Debugging Tests\n\n### Debug Backend Tests\n\n```bash\n# Debug con pdb\npython -m pytest tests/unit/test_models.py::test_function -s --pdb\n\n# Debug con logging\npython -m pytest tests/ -s --log-cli-level=DEBUG\n\n# Debug test specifico\npython -m pytest tests/unit/test_models.py::TestProjectModel::test_budget -vvv -s\n\n# Debug con breakpoint\n# Nel codice: import pdb; pdb.set_trace()\npython -m pytest tests/ -s\n```\n\n### Debug Frontend Tests\n\n```bash\n# Debug mode Vitest\nnpm run test:debug\n\n# Debug specifico test\nnpm run test -- --reporter=verbose ProjectTeam.test.js\n\n# Debug con browser\nnpm run test -- --ui\n\n# Debug con console.log\n# Nel test: console.log(wrapper.html())\nnpm run test -- --reporter=verbose\n```\n\n### Debug E2E Tests\n\n```bash\n# Debug mode Cypress\nnpx cypress open\n\n# Debug con video\nnpx cypress run --record\n\n# Debug con screenshots\nnpx cypress run --screenshot\n\n# Debug specifico test\nnpx cypress run --spec \"cypress/e2e/projects/project-creation.cy.js\" --headed\n\n# Debug con browser tools\n# Nel test: cy.debug() o cy.pause()\n```\n\n## 📈 Performance Monitoring\n\n### Metriche Test Performance\n\n```bash\n# Tempo esecuzione backend\npython -m pytest tests/ --durations=10\n\n# Tempo esecuzione frontend\nnpm run test -- --reporter=verbose --run\n\n# Tempo esecuzione E2E\nnpx cypress run --reporter json > cypress-results.json\n\n# Analisi performance\n./scripts/analyze-test-performance.sh\n```\n\n### Ottimizzazione Test\n\n```bash\n# Parallel execution backend\npython -m pytest tests/ -n auto\n\n# Parallel execution frontend\nnpm run test -- --threads\n\n# Parallel execution E2E\nnpx cypress run --parallel --record\n\n# Cache dependencies\nnpm ci --cache .npm\npip install --cache-dir .pip-cache\n```\n\n## 🔧 Troubleshooting\n\n### Problemi Comuni Backend\n\n```bash\n# Database connection issues\nexport DATABASE_URL=\"postgresql://user:pass@localhost/test_db\"\n\n# Import path issues\nexport PYTHONPATH=\"${PYTHONPATH}:$(pwd)/backend\"\n\n# Permission issues\nchmod +x scripts/*.sh\n\n# Clean cache\nfind . -name \"*.pyc\" -delete\nfind . -name \"__pycache__\" -delete\n```\n\n### Problemi Comuni Frontend\n\n```bash\n# Node modules issues\nrm -rf node_modules package-lock.json\nnpm install\n\n# Cache issues\nnpm run test -- --clearCache\n\n# Memory issues\nnpm run test -- --maxWorkers=2\n\n# Port conflicts\nlsof -ti:3000 | xargs kill -9\n```\n\n### Problemi Comuni E2E\n\n```bash\n# Browser issues\nnpx cypress verify\nnpx cypress info\n\n# Video/screenshot issues\nrm -rf cypress/videos cypress/screenshots\n\n# Network issues\nnpx cypress run --config defaultCommandTimeout=10000\n\n# Headless issues\nnpx cypress run --headed --no-exit\n```\n\n---\n\n*Guida aggiornata: 2025-01-26*\n"}