{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/api/test_direct_messages.py"}, "modifiedCode": "\"\"\"\nTest suite for DirectMessage API endpoints.\nTests CRUD operations, validation, and business logic for direct message management.\n\"\"\"\n\nimport pytest\nfrom datetime import datetime, date, timedelta\nfrom models import DirectMessage, User\nfrom extensions import db\n\n\nclass TestDirectMessagesAPI:\n    \"\"\"Test suite for DirectMessage API endpoints\"\"\"\n\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        \"\"\"Setup test data for each test method\"\"\"\n        self.app = app\n        self.user = test_user\n        \n        # Create a second user for messaging\n        self.recipient_user = User(\n            username='recipient_user',\n            email='<EMAIL>',\n            first_name='Recipient',\n            last_name='User'\n        )\n        self.recipient_user.set_password('password123')\n        db.session.add(self.recipient_user)\n        db.session.commit()\n        \n        # Create test direct message data\n        self.message_data = {\n            'sender_id': self.user.id,\n            'recipient_id': self.recipient_user.id,\n            'message': 'This is a test direct message for API testing',\n            'is_read': False\n        }\n\n    def test_get_direct_messages_success(self, client):\n        \"\"\"Test successful retrieval of direct messages list\"\"\"\n        # Create test messages\n        message1 = DirectMessage(\n            sender_id=self.user.id,\n            recipient_id=self.recipient_user.id,\n            message='First test message',\n            is_read=False\n        )\n        message2 = DirectMessage(\n            sender_id=self.recipient_user.id,\n            recipient_id=self.user.id,\n            message='Reply message',\n            is_read=True\n        )\n        db.session.add_all([message1, message2])\n        db.session.commit()\n\n        response = client.get('/api/messages')\n        \n        assert response.status_code in [200, 401]\n        if response.status_code == 200:\n            data = response.get_json()\n            assert 'messages' in str(data).lower() or 'data' in data\n\n    def test_send_direct_message_success(self, client):\n        \"\"\"Test successful direct message sending\"\"\"\n        response = client.post('/api/messages', json=self.message_data)\n        \n        assert response.status_code in [201, 200, 401, 403]\n        if response.status_code in [200, 201]:\n            # Verify message was created\n            created_message = DirectMessage.query.filter_by(\n                sender_id=self.user.id,\n                recipient_id=self.recipient_user.id\n            ).first()\n            if created_message:\n                assert created_message.message == 'This is a test direct message for API testing'\n                assert created_message.is_read is False\n\n    def test_send_direct_message_validation_error(self, client):\n        \"\"\"Test direct message sending with missing required fields\"\"\"\n        invalid_data = {'message': 'Missing sender and recipient'}\n        \n        response = client.post('/api/messages', json=invalid_data)\n        \n        assert response.status_code in [400, 422, 401, 403]\n\n    def test_send_message_to_self(self, client):\n        \"\"\"Test sending message to self (should be prevented)\"\"\"\n        invalid_data = self.message_data.copy()\n        invalid_data['recipient_id'] = self.user.id  # Same as sender\n        \n        response = client.post('/api/messages', json=invalid_data)\n        \n        assert response.status_code in [400, 422, 401, 403, 201, 200]\n\n    def test_mark_message_as_read(self, client):\n        \"\"\"Test marking a direct message as read\"\"\"\n        test_message = DirectMessage(\n            sender_id=self.recipient_user.id,\n            recipient_id=self.user.id,\n            message='Unread message',\n            is_read=False\n        )\n        db.session.add(test_message)\n        db.session.commit()\n\n        response = client.put(f'/api/messages/{test_message.id}/read')\n        \n        assert response.status_code in [200, 401, 403, 404]\n\n    def test_mark_message_as_unread(self, client):\n        \"\"\"Test marking a direct message as unread\"\"\"\n        test_message = DirectMessage(\n            sender_id=self.recipient_user.id,\n            recipient_id=self.user.id,\n            message='Read message',\n            is_read=True,\n            read_at=datetime.utcnow()\n        )\n        db.session.add(test_message)\n        db.session.commit()\n\n        response = client.put(f'/api/messages/{test_message.id}/unread')\n        \n        assert response.status_code in [200, 401, 403, 404]\n\n    def test_delete_direct_message_success(self, client):\n        \"\"\"Test successful direct message deletion\"\"\"\n        test_message = DirectMessage(\n            sender_id=self.user.id,\n            recipient_id=self.recipient_user.id,\n            message='Message to delete',\n            is_read=False\n        )\n        db.session.add(test_message)\n        db.session.commit()\n        message_id = test_message.id\n\n        response = client.delete(f'/api/messages/{message_id}')\n        \n        assert response.status_code in [200, 401, 403, 404]\n\n    def test_get_conversation_messages(self, client):\n        \"\"\"Test retrieval of conversation between two users\"\"\"\n        # Create conversation messages\n        message1 = DirectMessage(\n            sender_id=self.user.id,\n            recipient_id=self.recipient_user.id,\n            message='Hello there!',\n            is_read=True\n        )\n        message2 = DirectMessage(\n            sender_id=self.recipient_user.id,\n            recipient_id=self.user.id,\n            message='Hi! How are you?',\n            is_read=False\n        )\n        message3 = DirectMessage(\n            sender_id=self.user.id,\n            recipient_id=self.recipient_user.id,\n            message='I am doing well, thanks!',\n            is_read=False\n        )\n        db.session.add_all([message1, message2, message3])\n        db.session.commit()\n\n        response = client.get(f'/api/messages/conversation/{self.recipient_user.id}')\n        \n        assert response.status_code in [200, 401, 404]\n\n    def test_get_unread_messages_count(self, client):\n        \"\"\"Test retrieval of unread messages count\"\"\"\n        # Create unread messages\n        message1 = DirectMessage(\n            sender_id=self.recipient_user.id,\n            recipient_id=self.user.id,\n            message='Unread message 1',\n            is_read=False\n        )\n        message2 = DirectMessage(\n            sender_id=self.recipient_user.id,\n            recipient_id=self.user.id,\n            message='Unread message 2',\n            is_read=False\n        )\n        db.session.add_all([message1, message2])\n        db.session.commit()\n\n        response = client.get('/api/messages/unread/count')\n        \n        assert response.status_code in [200, 401]\n\n    def test_get_message_detail(self, client):\n        \"\"\"Test single direct message retrieval\"\"\"\n        test_message = DirectMessage(\n            sender_id=self.user.id,\n            recipient_id=self.recipient_user.id,\n            message='Detailed message content',\n            is_read=False\n        )\n        db.session.add(test_message)\n        db.session.commit()\n\n        response = client.get(f'/api/messages/{test_message.id}')\n        \n        assert response.status_code in [200, 401, 404]\n\n    def test_direct_message_not_found(self, client):\n        \"\"\"Test direct message not found scenarios\"\"\"\n        response = client.get('/api/messages/99999')\n        assert response.status_code in [404, 401]\n        \n        response = client.put('/api/messages/99999/read')\n        assert response.status_code in [404, 401]\n        \n        response = client.delete('/api/messages/99999')\n        assert response.status_code in [404, 401]\n\n    def test_message_search_and_filters(self, client):\n        \"\"\"Test direct message search and filtering\"\"\"\n        # Create test messages\n        message1 = DirectMessage(\n            sender_id=self.user.id,\n            recipient_id=self.recipient_user.id,\n            message='Important project update',\n            is_read=True\n        )\n        message2 = DirectMessage(\n            sender_id=self.recipient_user.id,\n            recipient_id=self.user.id,\n            message='Meeting reminder for tomorrow',\n            is_read=False\n        )\n        db.session.add_all([message1, message2])\n        db.session.commit()\n\n        # Test search by content\n        response = client.get('/api/messages?search=project')\n        assert response.status_code in [200, 401]\n        \n        # Test filter by read status\n        response = client.get('/api/messages?is_read=false')\n        assert response.status_code in [200, 401]\n        \n        # Test filter by sender\n        response = client.get(f'/api/messages?sender_id={self.user.id}')\n        assert response.status_code in [200, 401]\n\n    def test_message_permissions(self, client):\n        \"\"\"Test message access permissions\"\"\"\n        # Create message between other users (simulated)\n        other_message = DirectMessage(\n            sender_id=999,  # Different user\n            recipient_id=998,  # Different user\n            message='Private message between others',\n            is_read=False\n        )\n        db.session.add(other_message)\n        db.session.commit()\n\n        # Try to access message not involving current user\n        response = client.get(f'/api/messages/{other_message.id}')\n        assert response.status_code in [403, 401, 404]\n        \n        # Try to mark as read\n        response = client.put(f'/api/messages/{other_message.id}/read')\n        assert response.status_code in [403, 401, 404]\n\n    def test_message_pagination(self, client):\n        \"\"\"Test direct message list pagination\"\"\"\n        # Create multiple messages\n        for i in range(5):\n            message = DirectMessage(\n                sender_id=self.user.id if i % 2 == 0 else self.recipient_user.id,\n                recipient_id=self.recipient_user.id if i % 2 == 0 else self.user.id,\n                message=f'Message {i}',\n                is_read=i % 3 == 0\n            )\n            db.session.add(message)\n        db.session.commit()\n\n        response = client.get('/api/messages?page=1&per_page=3')\n        assert response.status_code in [200, 401]\n\n    def test_message_content_validation(self, client):\n        \"\"\"Test direct message content validation\"\"\"\n        # Test empty message\n        invalid_data = self.message_data.copy()\n        invalid_data['message'] = ''\n        \n        response = client.post('/api/messages', json=invalid_data)\n        assert response.status_code in [400, 422, 401, 403]\n        \n        # Test very long message\n        invalid_data['message'] = 'x' * 5000  # Assuming max length limit\n        response = client.post('/api/messages', json=invalid_data)\n        assert response.status_code in [400, 422, 401, 403, 201, 200]\n\n    def test_bulk_mark_as_read(self, client):\n        \"\"\"Test bulk marking messages as read\"\"\"\n        # Create multiple unread messages\n        message_ids = []\n        for i in range(3):\n            message = DirectMessage(\n                sender_id=self.recipient_user.id,\n                recipient_id=self.user.id,\n                message=f'Bulk read test {i}',\n                is_read=False\n            )\n            db.session.add(message)\n            db.session.flush()\n            message_ids.append(message.id)\n        db.session.commit()\n\n        bulk_data = {'message_ids': message_ids}\n        response = client.put('/api/messages/bulk/read', json=bulk_data)\n        \n        assert response.status_code in [200, 401, 403, 400]\n\n    def test_get_recent_conversations(self, client):\n        \"\"\"Test retrieval of recent conversations\"\"\"\n        # Create messages with different users to form conversations\n        message1 = DirectMessage(\n            sender_id=self.user.id,\n            recipient_id=self.recipient_user.id,\n            message='Recent conversation',\n            is_read=True\n        )\n        db.session.add(message1)\n        db.session.commit()\n\n        response = client.get('/api/messages/conversations/recent')\n        assert response.status_code in [200, 401]\n"}