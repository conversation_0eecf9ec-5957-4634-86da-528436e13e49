{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/timesheet/TimesheetEntry.vue"}, "originalCode": "<template>\n  <div class=\"space-y-6\">\n    <!-- <PERSON>rro<PERSON> -->\n    <div v-if=\"error\" class=\"bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800 rounded-md p-4\">\n      <div class=\"flex\">\n        <div class=\"flex-shrink-0\">\n          <svg class=\"h-5 w-5 text-red-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z\"></path>\n          </svg>\n        </div>\n        <div class=\"ml-3\">\n          <p class=\"text-sm text-red-800 dark:text-red-200\">{{ error }}</p>\n        </div>\n        <div class=\"ml-auto pl-3\">\n          <div class=\"-mx-1.5 -my-1.5\">\n            <button @click=\"clearError\" class=\"inline-flex bg-red-50 dark:bg-red-900/50 rounded-md p-1.5 text-red-500 hover:bg-red-100 dark:hover:bg-red-900\">\n              <svg class=\"h-4 w-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>\n              </svg>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Header -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n      <div class=\"flex justify-between items-center\">\n        <div>\n          <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white\">Le Mie Ore</h1>\n          <p class=\"mt-1 text-sm text-gray-600 dark:text-gray-400\">\n            Registra le tue ore di lavoro con la griglia mensile\n          </p>\n        </div>\n\n        <!-- Controlli periodo -->\n        <div class=\"flex items-center space-x-4\">\n          <div class=\"flex items-center space-x-2\">\n            <button\n              @click=\"previousMonth\"\n              class=\"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n            >\n              <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 19l-7-7 7-7\"></path>\n              </svg>\n            </button>\n\n            <div class=\"text-center\">\n              <h2 class=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                {{ monthNames[currentMonth - 1] }} {{ currentYear }}\n              </h2>\n            </div>\n\n            <button\n              @click=\"nextMonth\"\n              class=\"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n            >\n              <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\"></path>\n              </svg>\n            </button>\n          </div>\n\n          <button\n            @click=\"showAddHoursModal = true\"\n            class=\"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center\"\n          >\n            <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\"></path>\n            </svg>\n            Aggiungi Ore\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Griglia Timesheet Mensile -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden\">\n      <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n        <div class=\"flex items-center justify-between\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n            Griglia Ore - {{ monthNames[currentMonth - 1] }} {{ currentYear }}\n          </h3>\n          <div class=\"text-sm text-gray-500 dark:text-gray-400\">\n            Totale: {{ formatHours(totalHours) }} | Fatturabili: {{ formatHours(billableHours) }}\n          </div>\n        </div>\n      </div>\n\n      <div class=\"overflow-x-auto\">\n        <table class=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n          <!-- Header con giorni del mese -->\n          <thead class=\"bg-gray-50 dark:bg-gray-700\">\n            <tr>\n              <th class=\"sticky left-0 z-10 bg-gray-50 dark:bg-gray-700 px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-r border-gray-200 dark:border-gray-600\">\n                Progetto/Task\n              </th>\n              <th\n                v-for=\"day in daysInMonth\"\n                :key=\"day\"\n                class=\"px-2 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider min-w-[50px]\"\n                :class=\"{\n                  'bg-blue-50 dark:bg-blue-900/20': isToday(day, currentMonth, currentYear),\n                  'bg-red-50 dark:bg-red-900/20': isWeekend(day, currentMonth, currentYear)\n                }\"\n              >\n                <div>{{ day }}</div>\n                <div class=\"text-xs text-gray-400\">{{ getDayName(day, currentMonth, currentYear) }}</div>\n              </th>\n              <th class=\"px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-l border-gray-200 dark:border-gray-600\">\n                Totale\n              </th>\n            </tr>\n          </thead>\n\n          <!-- Righe progetti/task -->\n          <tbody class=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\n            <tr v-if=\"timesheetGridData.length === 0\">\n              <td :colspan=\"daysInMonth.length + 2\" class=\"px-6 py-8 text-center text-gray-500 dark:text-gray-400\">\n                <div class=\"space-y-2\">\n                  <p>Nessun timesheet registrato per {{ getMonthName(currentMonth) }} {{ currentYear }}</p>\n                  <button\n                    @click=\"showAddHoursModal = true\"\n                    class=\"text-primary-600 hover:text-primary-700 dark:text-primary-400\"\n                  >\n                    Registra le tue prime ore\n                  </button>\n                </div>\n              </td>\n            </tr>\n            \n            <tr v-for=\"taskRow in timesheetGridData\" :key=\"taskRow.taskId\">\n              <!-- Colonna progetto/task -->\n              <td class=\"sticky left-0 z-10 bg-white dark:bg-gray-800 px-4 py-3 border-r border-gray-200 dark:border-gray-600\">\n                <div class=\"flex items-center justify-between\">\n                  <div class=\"min-w-0 flex-1\">\n                    <p class=\"text-sm font-medium text-gray-900 dark:text-white truncate\">\n                      {{ taskRow.projectName }}\n                    </p>\n                    <p v-if=\"taskRow.taskName\" class=\"text-xs text-gray-500 dark:text-gray-400 truncate\">\n                      {{ taskRow.taskName }}\n                    </p>\n                    <p class=\"text-xs text-gray-500 dark:text-gray-400\">\n                      {{ taskRow.assignees }}\n                    </p>\n                  </div>\n                </div>\n              </td>\n              \n              <!-- Colonne giorni -->\n              <td\n                v-for=\"day in daysInMonth\"\n                :key=\"day\"\n                class=\"px-1 py-3 text-center\"\n                :class=\"{\n                  'bg-blue-50 dark:bg-blue-900/20': isToday(day, currentMonth, currentYear),\n                  'bg-red-50 dark:bg-red-900/20': isWeekend(day, currentMonth, currentYear)\n                }\"\n              >\n                <div v-if=\"taskRow.hours[getDateString(day)]\" class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  {{ taskRow.hours[getDateString(day)] }}\n                </div>\n                <div v-else class=\"text-gray-300 dark:text-gray-600\">-</div>\n              </td>\n              \n              <!-- Colonna totale riga -->\n              <td class=\"px-4 py-3 text-center text-sm font-medium text-gray-900 dark:text-white bg-gray-50 dark:bg-gray-700 border-l border-gray-200 dark:border-gray-600\">\n                {{ taskRow.total }}\n              </td>\n            </tr>\n          </tbody>\n        </table>\n      </div>\n    </div>\n\n    <!-- Quick Stats -->\n    <div class=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Ore Totali\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ formatHours(totalHours) }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Ore Fatturabili\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ formatHours(billableHours) }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                In Attesa\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ formatHours(pendingHours) }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Progetti Attivi\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ activeProjects }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Modal Aggiungi Ore -->\n    <div v-if=\"showAddHoursModal\" class=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\" @click=\"closeHoursModal\">\n      <div class=\"relative top-20 mx-auto p-5 border w-[400px] shadow-lg rounded-md bg-white dark:bg-gray-800\" @click.stop>\n        <div class=\"mt-3\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n            Aggiungi Ore\n          </h3>\n\n          <form @submit.prevent=\"saveHours\">\n            <div class=\"grid grid-cols-1 gap-4\">\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Progetto</label>\n                <select\n                  v-model=\"hoursForm.project_id\"\n                  @change=\"onProjectChange\"\n                  required\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                >\n                  <option value=\"\">Seleziona progetto</option>\n                  <option\n                    v-for=\"project in userProjects\"\n                    :key=\"project.id\"\n                    :value=\"project.id\"\n                  >\n                    {{ project.name }}\n                  </option>\n                </select>\n              </div>\n\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Task</label>\n                <select\n                  v-model=\"hoursForm.task_id\"\n                  required\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                >\n                  <option value=\"\">Seleziona task</option>\n                  <option\n                    v-for=\"task in availableTasks\"\n                    :key=\"task.id\"\n                    :value=\"task.id\"\n                  >\n                    {{ task.name }}\n                  </option>\n                </select>\n              </div>\n\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Data</label>\n                <input\n                  v-model=\"hoursForm.date\"\n                  type=\"date\"\n                  required\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                >\n              </div>\n\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Ore</label>\n                <input\n                  v-model=\"hoursForm.hours\"\n                  type=\"number\"\n                  step=\"0.25\"\n                  min=\"0\"\n                  max=\"24\"\n                  required\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                >\n              </div>\n\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Descrizione</label>\n                <textarea\n                  v-model=\"hoursForm.description\"\n                  rows=\"3\"\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                ></textarea>\n              </div>\n\n              <!-- Sezione Billing (solo per manager/admin) -->\n              <div v-if=\"canViewBilling\" class=\"border-t border-gray-200 dark:border-gray-600 pt-4\">\n                <h4 class=\"text-sm font-medium text-gray-900 dark:text-white mb-3\">Informazioni Fatturazione</h4>\n\n                <div class=\"space-y-3\">\n                  <div class=\"flex items-center justify-between\">\n                    <label class=\"flex items-center\">\n                      <input\n                        type=\"radio\"\n                        v-model=\"hoursForm.billable\"\n                        :value=\"true\"\n                        class=\"text-primary-600 focus:ring-primary-500\"\n                      />\n                      <span class=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">Sì - Fatturabile</span>\n                    </label>\n                    <label class=\"flex items-center\">\n                      <input\n                        type=\"radio\"\n                        v-model=\"hoursForm.billable\"\n                        :value=\"false\"\n                        class=\"text-primary-600 focus:ring-primary-500\"\n                      />\n                      <span class=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">No - Non fatturabile</span>\n                    </label>\n                  </div>\n\n                  <div v-if=\"hoursForm.billable\">\n                    <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                      Tariffa (€/h)\n                    </label>\n                    <input\n                      v-model=\"hoursForm.billing_rate\"\n                      type=\"number\"\n                      step=\"0.01\"\n                      min=\"0\"\n                      class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                    >\n                    <p class=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n                      Tariffa contrattuale: {{ formatCurrency(selectedProject?.contract?.hourly_rate || 0) }}\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div class=\"flex justify-end space-x-3 mt-6\">\n              <button\n                type=\"button\"\n                @click=\"closeHoursModal\"\n                class=\"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md\"\n              >\n                Annulla\n              </button>\n              <button\n                type=\"submit\"\n                :disabled=\"saving\"\n                class=\"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50\"\n              >\n                {{ saving ? 'Salvataggio...' : 'Aggiungi' }}\n              </button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue'\nimport { useTimesheetStore } from '@/stores/timesheet'\nimport { usePermissions } from '@/composables/usePermissions'\nimport { useAuthStore } from '@/stores/auth'\nimport {\n  monthNames,\n  formatHours,\n  isToday,\n  isWeekend,\n  getDayName\n} from '@/utils/timesheet'\nimport api from '@/utils/api'\n\nconst timesheetStore = useTimesheetStore()\nconst { hasPermission, isManager, isAdmin } = usePermissions()\nconst authStore = useAuthStore()\n\n// Local state\nconst showAddHoursModal = ref(false)\nconst availableTasks = ref([])\nconst userProjects = ref([])\nconst saving = ref(false)\n\n// Form data\nconst hoursForm = ref({\n  project_id: '',\n  task_id: '',\n  date: new Date().toISOString().split('T')[0],\n  hours: 0,\n  description: '',\n  billable: true,\n  billing_rate: null\n})\n\n// Computed properties from store\nconst currentYear = computed(() => timesheetStore.currentYear)\nconst currentMonth = computed(() => timesheetStore.currentMonth)\nconst projectTasks = computed(() => timesheetStore.projectTasks)\nconst monthlyEntries = computed(() => timesheetStore.monthlyEntries)\nconst availableProjects = computed(() => timesheetStore.availableProjects)\nconst loading = computed(() => timesheetStore.loading.monthlyData || timesheetStore.loading.saving)\nconst error = computed(() => timesheetStore.error)\n\n// New computed properties for hours modal\nconst canViewBilling = computed(() => isManager.value || isAdmin.value)\n\nconst selectedProject = computed(() => {\n  return userProjects.value.find(p => p.id === parseInt(hoursForm.value.project_id))\n})\n\n// Computed calculations\nconst daysInMonth = computed(() => timesheetStore.daysInMonth)\nconst totalHours = computed(() => timesheetStore.totalHours)\nconst billableHours = computed(() => timesheetStore.billableHours)\nconst pendingHours = computed(() => timesheetStore.pendingHours)\nconst activeProjects = computed(() => timesheetStore.activeProjects)\n\n// Build timesheet grid from existing entries like PersonnelProfile.vue\nconst timesheetGridData = computed(() => {\n  const taskGroups = {}\n  \n  // Process timesheet entries from the store\n  Object.entries(monthlyEntries.value).forEach(([date, dayEntries]) => {\n    Object.entries(dayEntries).forEach(([taskKey, hours]) => {\n      if (!taskGroups[taskKey]) {\n        // Find project task info\n        const projectTask = projectTasks.value.find(pt => pt.id === taskKey)\n        taskGroups[taskKey] = {\n          taskId: taskKey,\n          taskName: projectTask?.task_name || 'Attività Generica',\n          projectName: projectTask?.project_name || 'Progetto Sconosciuto', \n          assignees: 'Tu',\n          hours: {},\n          total: 0\n        }\n      }\n      \n      taskGroups[taskKey].hours[date] = parseFloat(hours || 0).toFixed(1)\n      taskGroups[taskKey].total += parseFloat(hours || 0)\n    })\n  })\n  \n  // Format totals\n  return Object.values(taskGroups).map(task => ({\n    ...task,\n    total: task.total.toFixed(1)\n  }))\n})\n\n// Methods\nconst previousMonth = () => {\n  timesheetStore.navigateMonth('previous')\n}\n\nconst nextMonth = () => {\n  timesheetStore.navigateMonth('next')\n}\n\nconst clearError = () => {\n  timesheetStore.clearError()\n}\n\n\n\n// Hours modal functions\nconst closeHoursModal = () => {\n  showAddHoursModal.value = false\n  resetHoursForm()\n  availableTasks.value = []\n}\n\nconst resetHoursForm = () => {\n  hoursForm.value = {\n    project_id: '',\n    task_id: '',\n    date: new Date().toISOString().split('T')[0],\n    hours: 0,\n    description: '',\n    billable: true,\n    billing_rate: null\n  }\n}\n\nconst onProjectChange = async () => {\n  hoursForm.value.task_id = ''\n  availableTasks.value = []\n\n  if (hoursForm.value.project_id) {\n    await loadTasksForProject(hoursForm.value.project_id)\n\n    // Set default billing rate from project contract\n    const project = selectedProject.value\n    if (project?.contract?.hourly_rate && canViewBilling.value) {\n      hoursForm.value.billing_rate = project.contract.hourly_rate\n    }\n  }\n}\n\nconst loadTasksForProject = async (projectId) => {\n  try {\n    const response = await api.get(`/api/tasks?project_id=${projectId}&status=open`)\n    if (response.data.success) {\n      availableTasks.value = response.data.data.tasks || []\n    }\n  } catch (error) {\n    console.error('Error loading tasks:', error)\n    availableTasks.value = []\n  }\n}\n\nconst saveHours = async () => {\n  saving.value = true\n\n  try {\n    const payload = {\n      user_id: authStore.user.id,\n      project_id: parseInt(hoursForm.value.project_id),\n      task_id: hoursForm.value.task_id ? parseInt(hoursForm.value.task_id) : null,\n      date: hoursForm.value.date,\n      hours: parseFloat(hoursForm.value.hours),\n      description: hoursForm.value.description,\n      billable: hoursForm.value.billable,\n      billing_rate: hoursForm.value.billable && hoursForm.value.billing_rate ?\n                   parseFloat(hoursForm.value.billing_rate) : null\n    }\n\n    const response = await api.post('/api/timesheets', payload)\n\n    if (response.data.success) {\n      closeHoursModal()\n      // Refresh timesheet data\n      await timesheetStore.loadMonthlyData()\n    } else {\n      throw new Error(response.data.message || 'Errore durante il salvataggio')\n    }\n  } catch (error) {\n    console.error('Error saving hours:', error)\n    // You might want to show an error message to the user\n  } finally {\n    saving.value = false\n  }\n}\n\nconst getEntryValue = (projectTaskId, day) => {\n  const entryKey = `${currentYear.value}-${currentMonth.value}-${day}`\n  return monthlyEntries.value[entryKey]?.[projectTaskId] || 0\n}\n\nconst getDateString = (day) => {\n  return `${currentYear.value}-${currentMonth.value.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`\n}\n\nconst getMonthName = (month) => {\n  const names = [\n    'Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno',\n    'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'\n  ]\n  return names[month - 1]\n}\n\n// Utility functions\nconst formatCurrency = (amount) => {\n  return new Intl.NumberFormat('it-IT', {\n    style: 'currency',\n    currency: 'EUR'\n  }).format(amount || 0)\n}\n\nconst loadUserProjects = async () => {\n  try {\n    const response = await api.get('/api/projects')\n    if (response.data.success) {\n      // Handle different response structures like in the projects store\n      if (response.data.data.items) {\n        userProjects.value = response.data.data.items\n      } else if (response.data.data.projects) {\n        userProjects.value = response.data.data.projects\n      } else if (Array.isArray(response.data.data)) {\n        userProjects.value = response.data.data\n      } else {\n        userProjects.value = []\n      }\n    }\n  } catch (error) {\n    console.error('Error loading user projects:', error)\n    userProjects.value = []\n  }\n}\n\n// Lifecycle\nonMounted(async () => {\n  // Load data from store with caching\n  await Promise.all([\n    timesheetStore.loadAvailableProjects(),\n    timesheetStore.loadMonthlyData(),\n    loadUserProjects()\n  ])\n})\n</script>", "modifiedCode": "<template>\n  <div class=\"space-y-6\">\n    <!-- Erro<PERSON> -->\n    <AlertsSection v-if=\"error\" :alerts=\"errorAlerts\" />\n\n    <!-- Header -->\n    <PageHeader\n      title=\"Le Mie Ore\"\n      subtitle=\"Registra le tue ore di lavoro con la griglia mensile\"\n      icon=\"clock\"\n      icon-color=\"text-blue-600\"\n    >\n      <template #actions>\n        <!-- Month Navigation -->\n        <div class=\"flex items-center space-x-4\">\n          <div class=\"flex items-center space-x-2\">\n            <button\n              @click=\"previousMonth\"\n              class=\"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-md transition-colors\"\n            >\n              <HeroIcon name=\"chevron-left\" size=\"md\" />\n            </button>\n\n            <div class=\"text-center min-w-[150px]\">\n              <h2 class=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                {{ monthNames[currentMonth - 1] }} {{ currentYear }}\n              </h2>\n            </div>\n\n            <button\n              @click=\"nextMonth\"\n              class=\"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-md transition-colors\"\n            >\n              <HeroIcon name=\"chevron-right\" size=\"md\" />\n            </button>\n          </div>\n\n          <div class=\"flex items-center space-x-3\">\n            <button\n              @click=\"showAddHoursModal = true\"\n              :disabled=\"isTimesheetConfirmed\"\n              class=\"inline-flex items-center px-4 py-2 bg-brand-primary-600 hover:bg-brand-primary-700 text-white rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n              :title=\"isTimesheetConfirmed ? 'Timesheet non modificabile (confermato o approvato)' : 'Aggiungi ore'\"\n            >\n              <HeroIcon name=\"plus\" size=\"sm\" class=\"mr-2\" />\n              Aggiungi Ore\n            </button>\n            \n            <button\n              v-if=\"!isTimesheetConfirmed\"\n              @click=\"confirmTimesheet\"\n              :disabled=\"confirmingTimesheet\"\n              class=\"inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium disabled:opacity-50 transition-colors\"\n            >\n              <HeroIcon v-if=\"confirmingTimesheet\" name=\"arrow-path\" size=\"sm\" className=\"animate-spin mr-2\" />\n              <HeroIcon v-else name=\"check\" size=\"sm\" class=\"mr-2\" />\n              {{ confirmingTimesheet ? 'Confermando...' : 'Conferma' }}\n            </button>\n          </div>\n        </div>\n      </template>\n    </PageHeader>\n\n    <!-- Timesheet Grid -->\n    <TimesheetGrid\n      :title=\"`Le Mie Ore - ${monthNames[currentMonth - 1]} ${currentYear}`\"\n      :tasks=\"timesheetGridData\"\n      :days=\"monthDays\"\n      :daily-totals=\"dailyTotals\"\n      :grand-total=\"grandTotal\"\n      :loading=\"loading\"\n      :error=\"error\"\n      :editable=\"!isTimesheetConfirmed\"\n      :show-stats=\"true\"\n      :show-day-totals=\"true\"\n      :show-indicators=\"true\"\n      :show-legend=\"true\"\n      :status=\"timesheetStatus\"\n      row-header-label=\"Progetto/Task\"\n      empty-message=\"Nessun timesheet registrato per questo mese\"\n      @cell-click=\"handleCellClick\"\n      @cell-update=\"handleCellUpdate\"\n      @bulk-save=\"handleBulkSave\"\n      @hours-changed=\"handleHoursChanged\"\n    >\n      <template #empty-state>\n        <HeroIcon name=\"clock\" size=\"lg\" class=\"mx-auto text-gray-400 mb-4\" />\n        <p class=\"text-gray-500 dark:text-gray-400 mb-2\">Nessun timesheet registrato per questo mese</p>\n        <button\n          @click=\"showAddHoursModal = true\"\n          :disabled=\"isTimesheetConfirmed\"\n          class=\"text-brand-primary-600 hover:text-brand-primary-700 dark:text-brand-primary-400 disabled:opacity-50 disabled:cursor-not-allowed text-sm font-medium\"\n          :title=\"isTimesheetConfirmed ? 'Timesheet non modificabile' : 'Registra le tue prime ore'\"\n        >\n          Registra le tue prime ore\n        </button>\n      </template>\n    </TimesheetGrid>\n\n    <!-- Add Hours Modal -->\n    <div v-if=\"showAddHoursModal\" class=\"fixed inset-0 z-50 overflow-y-auto\" aria-labelledby=\"modal-title\" role=\"dialog\" aria-modal=\"true\">\n      <div class=\"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\">\n        <div class=\"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\" @click=\"closeHoursModal\"></div>\n        \n        <span class=\"hidden sm:inline-block sm:align-middle sm:h-screen\">&#8203;</span>\n        \n        <div class=\"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full\">\n          <div class=\"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4\">\n            <div class=\"sm:flex sm:items-start\">\n              <div class=\"mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 dark:bg-blue-900 sm:mx-0 sm:h-10 sm:w-10\">\n                <HeroIcon name=\"clock\" size=\"md\" color=\"text-blue-600 dark:text-blue-400\" />\n              </div>\n              <div class=\"mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full\">\n                <h3 class=\"text-lg leading-6 font-medium text-gray-900 dark:text-white\" id=\"modal-title\">\n                  Registra Ore\n                </h3>\n                <div class=\"mt-4 space-y-4\">\n                  <!-- Project Selection -->\n                  <div>\n                    <label for=\"project\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                      Progetto <span class=\"text-red-500\">*</span>\n                    </label>\n                    <select\n                      id=\"project\"\n                      v-model=\"hoursForm.project_id\"\n                      @change=\"onProjectChange\"\n                      class=\"mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-brand-primary-500 focus:ring-brand-primary-500\"\n                      required\n                    >\n                      <option value=\"\">Seleziona progetto</option>\n                      <option v-for=\"project in userProjects\" :key=\"project.id\" :value=\"project.id\">\n                        {{ project.name }}\n                      </option>\n                    </select>\n                  </div>\n\n                  <!-- Task Selection -->\n                  <div v-if=\"availableTasks.length > 0\">\n                    <label for=\"task\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                      Task (Opzionale)\n                    </label>\n                    <select\n                      id=\"task\"\n                      v-model=\"hoursForm.task_id\"\n                      class=\"mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-brand-primary-500 focus:ring-brand-primary-500\"\n                    >\n                      <option value=\"\">Nessun task specifico</option>\n                                                                    <option v-for=\"task in availableTasks\" :key=\"task.id\" :value=\"task.id\">\n                        {{ task.name }}\n                      </option>\n                    </select>\n                  </div>\n\n                  <div class=\"grid grid-cols-2 gap-4\">\n                    <!-- Date -->\n                    <div>\n                      <label for=\"date\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                        Data <span class=\"text-red-500\">*</span>\n                      </label>\n                      <input\n                        type=\"date\"\n                        id=\"date\"\n                        v-model=\"hoursForm.date\"\n                        class=\"mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-brand-primary-500 focus:ring-brand-primary-500\"\n                        required\n                      />\n                    </div>\n\n                    <!-- Hours -->\n                    <div>\n                      <label for=\"hours\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                        Ore <span class=\"text-red-500\">*</span>\n                      </label>\n                      <input\n                        type=\"number\"\n                        id=\"hours\"\n                        v-model=\"hoursForm.hours\"\n                        step=\"0.5\"\n                        min=\"0\"\n                        max=\"24\"\n                        class=\"mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-brand-primary-500 focus:ring-brand-primary-500\"\n                        required\n                      />\n                    </div>\n                  </div>\n\n                  <!-- Description -->\n                  <div>\n                    <label for=\"description\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                      Descrizione\n                    </label>\n                    <textarea\n                      id=\"description\"\n                      v-model=\"hoursForm.description\"\n                      rows=\"3\"\n                      class=\"mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-brand-primary-500 focus:ring-brand-primary-500\"\n                      placeholder=\"Descrivi l'attività svolta...\"\n                    ></textarea>\n                  </div>\n\n                  <!-- Billing Options -->\n                  <div class=\"space-y-3\">\n                    <div class=\"flex items-center\">\n                      <input\n                        id=\"billable\"\n                        v-model=\"hoursForm.billable\"\n                        type=\"checkbox\"\n                        class=\"h-4 w-4 text-brand-primary-600 focus:ring-brand-primary-500 border-gray-300 dark:border-gray-600 rounded\"\n                      />\n                      <label for=\"billable\" class=\"ml-2 block text-sm text-gray-700 dark:text-gray-300\">\n                        Ore fatturabili\n                      </label>\n                    </div>\n\n                    <div v-if=\"hoursForm.billable && canViewBilling\" class=\"ml-6\">\n                      <label for=\"billing_rate\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                        Tariffa Oraria (€)\n                      </label>\n                      <input\n                        type=\"number\"\n                        id=\"billing_rate\"\n                        v-model=\"hoursForm.billing_rate\"\n                        step=\"0.01\"\n                        min=\"0\"\n                        class=\"mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-brand-primary-500 focus:ring-brand-primary-500\"\n                        placeholder=\"Es. 45.00\"\n                      />\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n          <div class=\"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse\">\n            <button\n              type=\"button\"\n              @click=\"saveHours\"\n              :disabled=\"!isFormValid || saving\"\n              class=\"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-brand-primary-600 text-base font-medium text-white hover:bg-brand-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-primary-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {{ saving ? 'Salvataggio...' : 'Salva Ore' }}\n            </button>\n            <button\n              type=\"button\"\n              @click=\"closeHoursModal\"\n              class=\"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm\"\n            >\n              Annulla\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue'\nimport { useRouter } from 'vue-router'\nimport PageHeader from '@/components/design-system/PageHeader.vue'\nimport AlertsSection from '@/components/design-system/AlertsSection.vue'\nimport TimesheetGrid from '@/components/design-system/grids/TimesheetGrid.vue'\nimport HeroIcon from '@/components/icons/HeroIcon.vue'\nimport { useTimesheetStore } from '@/stores/timesheet'\nimport { useAuthStore } from '@/stores/auth'\nimport api from '@/utils/api'\n\nconst router = useRouter()\nconst timesheetStore = useTimesheetStore()\nconst authStore = useAuthStore()\n\n// Reactive state\nconst showAddHoursModal = ref(false)\nconst saving = ref(false)\nconst confirmingTimesheet = ref(false)\nconst userProjects = ref([])\nconst availableTasks = ref([])\nconst hoursForm = ref({\n  project_id: '',\n  task_id: '',\n  date: new Date().toISOString().split('T')[0],\n  hours: 0,\n  description: '',\n  billable: true,\n  billing_rate: null\n})\nconst loading = ref(false)\nconst pendingChanges = ref(new Map()) // Per tracciare le modifiche in attesa di salvataggio\nconst bulkSaving = ref(false)\n\n// Constants\nconst monthNames = [\n  'Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno',\n  'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'\n]\n\n// Store computed properties\nconst currentMonth = computed(() => timesheetStore.currentMonth)\nconst currentYear = computed(() => timesheetStore.currentYear)\nconst monthlyTimesheetStatus = computed(() => timesheetStore.monthlyTimesheetStatus)\nconst error = computed(() => timesheetStore.error)\nconst canViewBilling = computed(() => authStore.user?.permissions?.canViewBilling || false)\n\n// Computed properties\nconst isTimesheetConfirmed = computed(() => \n  monthlyTimesheetStatus.value === 'confirmed' || monthlyTimesheetStatus.value === 'approved'\n)\n\nconst selectedProject = computed(() => \n  userProjects.value.find(p => p.id == hoursForm.value.project_id)\n)\n\nconst isFormValid = computed(() => \n  hoursForm.value.project_id && hoursForm.value.date && hoursForm.value.hours > 0\n)\n\nconst errorAlerts = computed(() => {\n  if (!error.value) return []\n  \n  return [\n    {\n      id: 'error',\n      type: 'error',\n      title: 'Errore',\n      message: error.value,\n      dismissible: true\n    }\n  ]\n})\n\n// Generate month days for TimesheetGrid\nconst monthDays = computed(() => {\n  const year = currentYear.value\n  const month = currentMonth.value\n  const daysInMonth = new Date(year, month, 0).getDate()\n  const days = []\n  \n  for (let day = 1; day <= daysInMonth; day++) {\n    const date = new Date(year, month - 1, day)\n    days.push({\n      key: `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`,\n      label: day.toString(),\n      sublabel: date.toLocaleDateString('it-IT', { weekday: 'short' }),\n      isWeekend: date.getDay() === 0 || date.getDay() === 6,\n      isToday: date.toDateString() === new Date().toDateString()\n    })\n  }\n  \n  return days\n})\n\n// Transform timesheet data for TimesheetGrid\nconst timesheetGridData = computed(() => {\n  // Uso i dati corretti dallo store: monthlyEntries e projectTasks\n  const entries = timesheetStore.monthlyEntries\n  const projects = timesheetStore.projectTasks\n  \n  console.log('🔍 TimesheetEntry - monthlyEntries:', entries)\n  console.log('🔍 TimesheetEntry - projectTasks:', projects)\n  \n  if (!entries || !projects || projects.length === 0) {\n    console.log('❌ Nessun dato timesheet disponibile')\n    return []\n  }\n\n  // Trasformo i dati nel formato che si aspetta TimesheetGrid\n  const transformedTasks = projects.map(project => {\n    const taskData = {\n      id: project.id,\n      projectName: project.project_name,\n      taskName: project.task_name,\n      name: `${project.project_name}${project.task_name ? ` - ${project.task_name}` : ''}`,\n      projectId: project.project_id,\n      taskId: project.task_id,\n      assignees: project.assignees || '',\n      hours: {},\n      billing: {},\n      total: 0\n    }\n    \n    // Aggiungo le ore per ogni giorno dal monthlyEntries\n    Object.entries(entries).forEach(([date, dayEntries]) => {\n      if (dayEntries[project.id]) {\n        const entry = dayEntries[project.id]\n        const hours = typeof entry === 'object' ? entry.hours : entry\n        const billable = typeof entry === 'object' ? entry.billable : false\n        \n        if (hours > 0) {\n          taskData.hours[date] = hours.toFixed(1)\n          taskData.billing[date] = billable\n          taskData.total += hours\n        }\n      }\n    })\n    \n    return {\n      ...taskData,\n      total: taskData.total.toFixed(1)\n    }\n  })\n  \n  console.log('✅ TimesheetEntry - dati trasformati:', transformedTasks)\n  return transformedTasks\n})\n\nconst totalHours = computed(() => {\n  return timesheetGridData.value.reduce((total, row) => total + parseFloat(row.total), 0)\n})\n\nconst billableHours = computed(() => {\n  return timesheetGridData.value.reduce((total, row) => {\n    const rowBillableHours = Object.entries(row.billing || {})\n      .filter(([date, billable]) => billable && row.hours[date])\n      .reduce((sum, [date]) => sum + parseFloat(row.hours[date] || 0), 0)\n    return total + rowBillableHours\n  }, 0)\n})\n\n// Computed properties per TimesheetGrid\nconst dailyTotals = computed(() => {\n  const totals = {}\n  monthDays.value.forEach(day => {\n    totals[day.key] = timesheetGridData.value.reduce((sum, row) => {\n      return sum + parseFloat(row.hours[day.key] || 0)\n    }, 0)\n  })\n  return totals\n})\n\nconst grandTotal = computed(() => {\n  return Object.values(dailyTotals.value).reduce((sum, daily) => sum + daily, 0)\n})\n\nconst timesheetStatus = computed(() => {\n  return {\n    status: monthlyTimesheetStatus.value || 'pending',\n    totalHours: totalHours.value,\n    billableHours: billableHours.value,\n    pendingChanges: pendingChanges.value.size\n  }\n})\n\n// Status methods\nconst getStatusClasses = (status) => {\n  const classes = {\n    pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-300',\n    confirmed: 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300',\n    approved: 'bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-300'\n  }\n  return classes[status] || classes.pending\n}\n\nconst getStatusIcon = (status) => {\n  const icons = {\n    pending: 'exclamation-circle',\n    confirmed: 'check',\n    approved: 'check-circle'\n  }\n  return icons[status] || icons.pending\n}\n\nconst getStatusLabel = (status) => {\n  const labels = {\n    pending: 'In attesa',\n    confirmed: 'Confermato',\n    approved: 'Approvato'\n  }\n  return labels[status] || labels.pending\n}\n\n// Navigation methods\nconst previousMonth = async () => {\n  timesheetStore.navigateMonth('previous')\n  await timesheetStore.loadMonthlyData(timesheetStore.currentYear, timesheetStore.currentMonth)\n}\n\nconst nextMonth = async () => {\n  timesheetStore.navigateMonth('next')\n  await timesheetStore.loadMonthlyData(timesheetStore.currentYear, timesheetStore.currentMonth)\n}\n\n// Cell click handler\nconst handleCellClick = (row, day) => {\n  // Pre-fill modal with clicked cell data\n  hoursForm.value = {\n    project_id: row.projectId,\n    task_id: row.taskId || '',\n    date: day.key,\n    hours: parseFloat(row.hours[day.key] || 0),\n    description: '',\n    billable: row.billing ? row.billing[day.key] || true : true,\n    billing_rate: null\n  }\n  \n  // Load tasks for the project\n  if (row.projectId) {\n    loadTasksForProject(row.projectId)\n  }\n  \n  showAddHoursModal.value = true\n}\n\n// Nuovi handler per TimesheetGrid ottimizzato\nconst handleCellUpdate = async (taskId, date, hours) => {\n  try {\n    // Trova il task corrispondente\n    const task = timesheetGridData.value.find(t => t.id === taskId)\n    if (!task) return\n\n    // Prepara il payload per l'aggiornamento\n    const payload = {\n      user_id: authStore.user.id,\n      project_id: task.projectId,\n      task_id: task.taskId || null,\n      date: date,\n      hours: parseFloat(hours) || 0,\n      billable: task.billing[date] || true\n    }\n\n    // Salva direttamente se le ore sono > 0, altrimenti elimina l'entry\n    if (payload.hours > 0) {\n      const response = await api.post('/api/timesheets', payload)\n      if (!response.data.success) {\n        throw new Error(response.data.message || 'Errore durante il salvataggio')\n      }\n    } else {\n      // Elimina l'entry esistente se ore = 0\n      const response = await api.delete(`/api/timesheets/entry`, {\n        data: {\n          user_id: payload.user_id,\n          project_id: payload.project_id,\n          task_id: payload.task_id,\n          date: payload.date\n        }\n      })\n      if (!response.data.success) {\n        throw new Error(response.data.message || 'Errore durante l\\'eliminazione')\n      }\n    }\n\n    // Aggiorna i dati locali\n    await timesheetStore.loadMonthlyData()\n    \n  } catch (error) {\n    console.error('Error updating cell:', error)\n    timesheetStore.error = error.message || 'Impossibile aggiornare le ore'\n  }\n}\n\nconst handleHoursChanged = (taskId, date, hours) => {\n  // Traccia le modifiche in attesa di salvataggio batch\n  const key = `${taskId}-${date}`\n  if (hours && parseFloat(hours) > 0) {\n    pendingChanges.value.set(key, {\n      taskId,\n      date,\n      hours: parseFloat(hours)\n    })\n  } else {\n    pendingChanges.value.delete(key)\n  }\n}\n\nconst handleBulkSave = async (changes) => {\n  if (bulkSaving.value) return\n  \n  bulkSaving.value = true\n  try {\n    const savePromises = []\n    \n    // Processa tutti i cambiamenti\n    for (const [key, change] of changes.entries()) {\n      const task = timesheetGridData.value.find(t => t.id === change.taskId)\n      if (!task) continue\n\n      const payload = {\n        user_id: authStore.user.id,\n        project_id: task.projectId,\n        task_id: task.taskId || null,\n        date: change.date,\n        hours: change.hours,\n        billable: task.billing[change.date] || true\n      }\n\n      if (payload.hours > 0) {\n        savePromises.push(api.post('/api/timesheets', payload))\n      } else {\n        // Elimina l'entry se ore = 0\n        savePromises.push(api.delete(`/api/timesheets/entry`, {\n          data: {\n            user_id: payload.user_id,\n            project_id: payload.project_id,\n            task_id: payload.task_id,\n            date: payload.date\n          }\n        }))\n      }\n    }\n\n    // Esegui tutti i salvataggi in parallelo\n    const results = await Promise.allSettled(savePromises)\n    \n    // Controlla se ci sono stati errori\n    const errors = results.filter(result => result.status === 'rejected')\n    if (errors.length > 0) {\n      console.error('Alcuni salvataggi sono falliti:', errors)\n      timesheetStore.error = `${errors.length} modifiche non sono state salvate`\n    }\n\n    // Pulisci le modifiche in attesa e ricarica i dati\n    pendingChanges.value.clear()\n    await timesheetStore.loadMonthlyData()\n    \n  } catch (error) {\n    console.error('Error in bulk save:', error)\n    timesheetStore.error = error.message || 'Errore durante il salvataggio di massa'\n  } finally {\n    bulkSaving.value = false\n  }\n}\n\n// Modal methods\nconst closeHoursModal = () => {\n  showAddHoursModal.value = false\n  resetHoursForm()\n  availableTasks.value = []\n}\n\nconst resetHoursForm = () => {\n  hoursForm.value = {\n    project_id: '',\n    task_id: '',\n    date: new Date().toISOString().split('T')[0],\n    hours: 0,\n    description: '',\n    billable: true,\n    billing_rate: null\n  }\n}\n\nconst onProjectChange = async () => {\n  hoursForm.value.task_id = ''\n  availableTasks.value = []\n\n  if (hoursForm.value.project_id) {\n    await loadTasksForProject(hoursForm.value.project_id)\n\n    // Set default billing rate from project contract\n    const project = selectedProject.value\n    if (project?.contract?.hourly_rate && canViewBilling.value) {\n      hoursForm.value.billing_rate = project.contract.hourly_rate\n    }\n  }\n}\n\nconst loadTasksForProject = async (projectId) => {\n  try {\n    const response = await api.get(`/api/tasks?project_id=${projectId}`)\n    \n    if (response.data.success) {\n      // L'API restituisce sempre data.tasks\n      const allTasks = response.data.data.tasks || []\n      // Filter out completed tasks\n      const activeTasks = allTasks.filter(task => task.status !== 'done')\n      availableTasks.value = activeTasks\n    } else {\n      availableTasks.value = []\n    }\n  } catch (error) {\n    console.error('Error loading tasks:', error)\n    availableTasks.value = []\n  }\n}\n\nconst saveHours = async () => {\n  if (!isFormValid.value) return\n  \n  saving.value = true\n\n  try {\n    const payload = {\n      user_id: authStore.user.id,\n      project_id: parseInt(hoursForm.value.project_id),\n      task_id: hoursForm.value.task_id ? parseInt(hoursForm.value.task_id) : null,\n      date: hoursForm.value.date,\n      hours: parseFloat(hoursForm.value.hours),\n      description: hoursForm.value.description,\n      billable: hoursForm.value.billable,\n      billing_rate: hoursForm.value.billable && hoursForm.value.billing_rate ?\n                   parseFloat(hoursForm.value.billing_rate) : null\n    }\n\n    const response = await api.post('/api/timesheets', payload)\n\n    if (response.data.success) {\n      closeHoursModal()\n      // Refresh timesheet data\n      await timesheetStore.loadMonthlyData()\n    } else {\n      throw new Error(response.data.message || 'Errore durante il salvataggio')\n    }\n      } catch (error) {\n      console.error('Error saving hours:', error)\n      timesheetStore.error = error.message || 'Impossibile salvare le ore'\n    } finally {\n    saving.value = false\n  }\n}\n\n// Confirm timesheet\nconst confirmTimesheet = async () => {\n  if (confirmingTimesheet.value) return\n  \n  confirmingTimesheet.value = true\n  try {\n    const yearMonth = `${currentYear.value}-${currentMonth.value.toString().padStart(2, '0')}`\n    \n    const response = await api.post('/api/timesheets/confirm', {\n      user_id: authStore.user.id,\n      year_month: yearMonth\n    })\n    \n    if (response.data.success) {\n      timesheetStore.setMonthlyTimesheetStatus('confirmed')\n    } else {\n      throw new Error(response.data.message || 'Errore durante la conferma del timesheet')\n    }\n  } catch (error) {\n    console.error('Error confirming timesheet:', error)\n    timesheetStore.error = error.message || 'Impossibile confermare il timesheet'\n  } finally {\n    confirmingTimesheet.value = false\n  }\n}\n\n// Load user projects\nconst loadUserProjects = async () => {\n  try {\n    const response = await api.get('/api/projects')\n    if (response.data.success) {\n      // L'API projects restituisce data.projects oppure data.items\n      const projectsData = response.data.data\n      if (projectsData.projects) {\n        userProjects.value = projectsData.projects\n      } else if (projectsData.items) {\n        userProjects.value = projectsData.items\n      } else if (Array.isArray(projectsData)) {\n        userProjects.value = projectsData\n      } else {\n        userProjects.value = []\n      }\n    } else {\n      userProjects.value = []\n    }\n  } catch (error) {\n    console.error('Error loading user projects:', error)\n    userProjects.value = []\n  }\n}\n\n// Lifecycle\nonMounted(async () => {\n  await Promise.all([\n    timesheetStore.loadAvailableProjects(),\n    timesheetStore.loadMonthlyData(),\n    loadUserProjects()\n  ])\n})\n</script>"}