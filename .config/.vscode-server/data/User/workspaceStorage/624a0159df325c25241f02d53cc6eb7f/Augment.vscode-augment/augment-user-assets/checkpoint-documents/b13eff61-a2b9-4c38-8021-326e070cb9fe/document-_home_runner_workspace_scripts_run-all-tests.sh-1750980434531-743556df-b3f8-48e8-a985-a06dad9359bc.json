{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "scripts/run-all-tests.sh"}, "modifiedCode": "#!/bin/bash\n\n# Script per eseguire tutti i test (backend, frontend, e2e)\n# Valida il funzionamento completo dei casi d'uso\n\nset -e  # Exit on any error\n\necho \"🚀 AVVIO TEST SUITE COMPLETA\"\necho \"==================================\"\n\n# Colors for output\nRED='\\033[0;31m'\nGREEN='\\033[0;32m'\nYELLOW='\\033[1;33m'\nBLUE='\\033[0;34m'\nNC='\\033[0m' # No Color\n\n# Function to print colored output\nprint_status() {\n    echo -e \"${BLUE}[INFO]${NC} $1\"\n}\n\nprint_success() {\n    echo -e \"${GREEN}[SUCCESS]${NC} $1\"\n}\n\nprint_warning() {\n    echo -e \"${YELLOW}[WARNING]${NC} $1\"\n}\n\nprint_error() {\n    echo -e \"${RED}[ERROR]${NC} $1\"\n}\n\n# Check if we're in the right directory\nif [ ! -f \"backend/app.py\" ] || [ ! -f \"frontend/package.json\" ]; then\n    print_error \"Script deve essere eseguito dalla root del progetto\"\n    exit 1\nfi\n\n# Variables\nBACKEND_DIR=\"backend\"\nFRONTEND_DIR=\"frontend\"\nTEST_RESULTS_DIR=\"test-results\"\nTIMESTAMP=$(date +\"%Y%m%d_%H%M%S\")\n\n# Create test results directory\nmkdir -p $TEST_RESULTS_DIR\n\nprint_status \"Creazione ambiente di test...\"\n\n# 1. BACKEND TESTS\nprint_status \"🔧 ESECUZIONE BACKEND TESTS\"\necho \"================================\"\n\ncd $BACKEND_DIR\n\n# Setup test database\nprint_status \"Setup database di test...\"\npython -c \"\nfrom app import create_app\nfrom extensions import db\napp = create_app()\nwith app.app_context():\n    db.create_all()\n    print('✅ Database di test creato')\n\"\n\n# Run unit tests\nprint_status \"Esecuzione unit tests...\"\npython -m pytest tests/unit/ -v --tb=short --junitxml=../$TEST_RESULTS_DIR/backend-unit-$TIMESTAMP.xml\n\n# Run integration tests\nprint_status \"Esecuzione integration tests...\"\npython -m pytest tests/integration/ -v --tb=short --junitxml=../$TEST_RESULTS_DIR/backend-integration-$TIMESTAMP.xml\n\n# Run API tests\nprint_status \"Esecuzione API tests...\"\npython -m pytest tests/api/ -v --tb=short --junitxml=../$TEST_RESULTS_DIR/backend-api-$TIMESTAMP.xml\n\n# Generate coverage report\nprint_status \"Generazione coverage report...\"\npython -m pytest --cov=. --cov-report=html:../$TEST_RESULTS_DIR/backend-coverage-$TIMESTAMP --cov-report=term-missing tests/ > ../$TEST_RESULTS_DIR/backend-coverage-$TIMESTAMP.txt\n\nprint_success \"Backend tests completati\"\n\ncd ..\n\n# 2. FRONTEND TESTS\nprint_status \"🎨 ESECUZIONE FRONTEND TESTS\"\necho \"=================================\"\n\ncd $FRONTEND_DIR\n\n# Install dependencies if needed\nif [ ! -d \"node_modules\" ]; then\n    print_status \"Installazione dipendenze frontend...\"\n    npm install\nfi\n\n# Run unit tests\nprint_status \"Esecuzione component tests...\"\nnpm run test:unit -- --reporter=junit --outputFile=../$TEST_RESULTS_DIR/frontend-unit-$TIMESTAMP.xml\n\n# Run coverage\nprint_status \"Generazione coverage frontend...\"\nnpm run test:coverage -- --reporter=html --outputDir=../$TEST_RESULTS_DIR/frontend-coverage-$TIMESTAMP\n\nprint_success \"Frontend tests completati\"\n\ncd ..\n\n# 3. E2E TESTS\nprint_status \"🌐 ESECUZIONE E2E TESTS\"\necho \"==========================\"\n\n# Start backend server\nprint_status \"Avvio backend server per E2E...\"\ncd $BACKEND_DIR\npython main.py &\nBACKEND_PID=$!\nsleep 5  # Wait for server to start\n\ncd ../$FRONTEND_DIR\n\n# Start frontend dev server\nprint_status \"Avvio frontend dev server per E2E...\"\nnpm run dev &\nFRONTEND_PID=$!\nsleep 10  # Wait for frontend to start\n\n# Run E2E tests\nprint_status \"Esecuzione E2E tests...\"\nnpx cypress run --reporter junit --reporter-options \"mochaFile=../$TEST_RESULTS_DIR/e2e-$TIMESTAMP.xml\"\n\n# Cleanup: Kill servers\nprint_status \"Cleanup servers...\"\nkill $BACKEND_PID 2>/dev/null || true\nkill $FRONTEND_PID 2>/dev/null || true\n\ncd ..\n\n# 4. GENERATE FINAL REPORT\nprint_status \"📊 GENERAZIONE REPORT FINALE\"\necho \"=============================\"\n\ncat > $TEST_RESULTS_DIR/test-summary-$TIMESTAMP.md << EOF\n# Test Execution Summary - $TIMESTAMP\n\n## 🎯 Test Results Overview\n\n### Backend Tests\n- **Unit Tests**: $(grep -c \"passed\" $TEST_RESULTS_DIR/backend-unit-$TIMESTAMP.xml 2>/dev/null || echo \"N/A\") passed\n- **Integration Tests**: $(grep -c \"passed\" $TEST_RESULTS_DIR/backend-integration-$TIMESTAMP.xml 2>/dev/null || echo \"N/A\") passed  \n- **API Tests**: $(grep -c \"passed\" $TEST_RESULTS_DIR/backend-api-$TIMESTAMP.xml 2>/dev/null || echo \"N/A\") passed\n- **Coverage**: Available in backend-coverage-$TIMESTAMP/\n\n### Frontend Tests\n- **Component Tests**: $(grep -c \"passed\" $TEST_RESULTS_DIR/frontend-unit-$TIMESTAMP.xml 2>/dev/null || echo \"N/A\") passed\n- **Coverage**: Available in frontend-coverage-$TIMESTAMP/\n\n### E2E Tests\n- **User Workflows**: $(grep -c \"passed\" $TEST_RESULTS_DIR/e2e-$TIMESTAMP.xml 2>/dev/null || echo \"N/A\") passed\n\n## 📁 Test Artifacts\n\n- Backend Coverage: \\`$TEST_RESULTS_DIR/backend-coverage-$TIMESTAMP/index.html\\`\n- Frontend Coverage: \\`$TEST_RESULTS_DIR/frontend-coverage-$TIMESTAMP/index.html\\`\n- JUnit Reports: \\`$TEST_RESULTS_DIR/*-$TIMESTAMP.xml\\`\n\n## 🚀 Validated Use Cases\n\n### ✅ Project Management\n- Project creation and editing\n- Team member management\n- Task assignment and tracking\n- Status transitions\n- Budget and expense tracking\n\n### ✅ Team Collaboration  \n- User authentication and authorization\n- Role-based access control\n- Team member allocation\n- Resource management\n\n### ✅ Financial Tracking\n- Project budgets and expenses\n- KPI monitoring and reporting\n- Funding application integration\n- Cost calculations\n\n### ✅ Timesheet Integration\n- Hour logging and approval\n- Project time tracking\n- Team productivity metrics\n- Reporting and analytics\n\n## 📈 Quality Metrics\n\n- **Backend API Coverage**: Comprehensive\n- **Frontend Component Coverage**: Expanding\n- **E2E User Journey Coverage**: Core workflows\n- **Integration Test Coverage**: Critical paths\n\n## 🎯 Next Steps\n\n1. **Expand Frontend Coverage**: Add more component tests\n2. **Performance Testing**: Add load and stress tests  \n3. **Security Testing**: Add authentication and authorization tests\n4. **Mobile Testing**: Add responsive design tests\n5. **Accessibility Testing**: Add a11y compliance tests\n\n---\n*Generated on $(date)*\nEOF\n\nprint_success \"Report finale generato: $TEST_RESULTS_DIR/test-summary-$TIMESTAMP.md\"\n\n# 5. OPEN RESULTS\nif command -v xdg-open > /dev/null; then\n    print_status \"Apertura report di coverage...\"\n    xdg-open $TEST_RESULTS_DIR/backend-coverage-$TIMESTAMP/index.html 2>/dev/null &\n    xdg-open $TEST_RESULTS_DIR/frontend-coverage-$TIMESTAMP/index.html 2>/dev/null &\nfi\n\necho \"\"\nprint_success \"🎉 TEST SUITE COMPLETA ESEGUITA CON SUCCESSO!\"\necho \"\"\nprint_status \"📊 Risultati disponibili in: $TEST_RESULTS_DIR/\"\nprint_status \"📋 Summary report: $TEST_RESULTS_DIR/test-summary-$TIMESTAMP.md\"\necho \"\"\nprint_status \"🔍 Per vedere i dettagli:\"\necho \"   - Backend coverage: open $TEST_RESULTS_DIR/backend-coverage-$TIMESTAMP/index.html\"\necho \"   - Frontend coverage: open $TEST_RESULTS_DIR/frontend-coverage-$TIMESTAMP/index.html\"\necho \"   - Test summary: cat $TEST_RESULTS_DIR/test-summary-$TIMESTAMP.md\"\n"}