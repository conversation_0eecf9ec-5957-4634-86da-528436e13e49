{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_joblevel_model.py"}, "originalCode": "\"\"\"\nUnit tests for JobLevel model.\nTests business logic, validations, and model functionality.\n\"\"\"\n\nimport pytest\nfrom datetime import datetime\nfrom models import JobLevel\nfrom extensions import db\n\n\nclass TestJobLevelModel:\n    \"\"\"Test suite for JobLevel model unit tests\"\"\"\n\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        \"\"\"Setup test data for each test method\"\"\"\n        self.app = app\n        self.user = test_user\n\n    def test_joblevel_creation_basic(self):\n        \"\"\"Test basic job level creation\"\"\"\n        job_level = JobLevel(\n            name='Junior Developer',\n            level_number=1,\n            description='Entry level developer position'\n        )\n        \n        db.session.add(job_level)\n        db.session.commit()\n        \n        assert job_level.id is not None\n        assert job_level.name == 'Junior Developer'\n        assert job_level.level_number == 1\n        assert job_level.description == 'Entry level developer position'\n\n    def test_joblevel_creation_complete(self):\n        \"\"\"Test job level creation with all fields\"\"\"\n        job_level = JobLevel(\n            name='Senior Developer',\n            level_number=3,\n            description='Senior level developer with leadership responsibilities',\n            min_salary=60000.0,\n            max_salary=90000.0,\n            typical_years_experience=5,\n            is_management=False,\n            is_active=True\n        )\n        \n        db.session.add(job_level)\n        db.session.commit()\n        \n        assert job_level.name == 'Senior Developer'\n        assert job_level.level_number == 3\n        assert job_level.min_salary == 60000.0\n        assert job_level.max_salary == 90000.0\n        assert job_level.typical_years_experience == 5\n        assert job_level.is_management is False\n        assert job_level.is_active is True\n\n    def test_joblevel_level_numbers(self):\n        \"\"\"Test different level numbers\"\"\"\n        levels = [\n            ('Intern', 100),\n            ('Junior', 101),\n            ('Mid-level', 102),\n            ('Senior', 103),\n            ('Lead', 104),\n            ('Principal', 105)\n        ]\n        \n        job_levels = []\n        for name, level_num in levels:\n            job_level = JobLevel(\n                name=name,\n                level_number=level_num\n            )\n            job_levels.append(job_level)\n        \n        db.session.add_all(job_levels)\n        db.session.commit()\n        \n        for job_level, (expected_name, expected_level) in zip(job_levels, levels):\n            assert job_level.name == expected_name\n            assert job_level.level_number == expected_level\n\n    def test_joblevel_salary_ranges(self):\n        \"\"\"Test salary range functionality\"\"\"\n        salary_ranges = [\n            ('Entry Level', 30000.0, 45000.0),\n            ('Mid Level', 45000.0, 70000.0),\n            ('Senior Level', 70000.0, 100000.0),\n            ('Executive Level', 100000.0, 150000.0)\n        ]\n        \n        job_levels = []\n        for i, (name, min_sal, max_sal) in enumerate(salary_ranges):\n            job_level = JobLevel(\n                name=name,\n                level_number=200 + i,  # Numeri unici\n                min_salary=min_sal,\n                max_salary=max_sal\n            )\n            job_levels.append(job_level)\n        \n        db.session.add_all(job_levels)\n        db.session.commit()\n        \n        for job_level, (_, expected_min, expected_max) in zip(job_levels, salary_ranges):\n            assert job_level.min_salary == expected_min\n            assert job_level.max_salary == expected_max\n            assert job_level.min_salary <= job_level.max_salary\n\n    def test_joblevel_management_flag(self):\n        \"\"\"Test management flag functionality\"\"\"\n        # Non-management role\n        individual_contributor = JobLevel(\n            name='Software Engineer',\n            level_number=300,\n            is_management=False\n        )\n\n        # Management role\n        team_lead = JobLevel(\n            name='Engineering Manager',\n            level_number=301,\n            is_management=True\n        )\n        \n        db.session.add_all([individual_contributor, team_lead])\n        db.session.commit()\n        \n        assert individual_contributor.is_management is False\n        assert team_lead.is_management is True\n\n    def test_joblevel_experience_requirements(self):\n        \"\"\"Test typical years experience field\"\"\"\n        experience_levels = [\n            ('Graduate', 0),\n            ('Junior', 1),\n            ('Mid-level', 3),\n            ('Senior', 5),\n            ('Staff', 8),\n            ('Principal', 12)\n        ]\n        \n        job_levels = []\n        for i, (name, years) in enumerate(experience_levels):\n            job_level = JobLevel(\n                name=name,\n                level_number=i,\n                typical_years_experience=years\n            )\n            job_levels.append(job_level)\n        \n        db.session.add_all(job_levels)\n        db.session.commit()\n        \n        for job_level, (_, expected_years) in zip(job_levels, experience_levels):\n            assert job_level.typical_years_experience == expected_years\n            assert job_level.typical_years_experience >= 0\n\n    def test_joblevel_active_status(self):\n        \"\"\"Test active status functionality\"\"\"\n        # Active job level\n        active_level = JobLevel(\n            name='Active Position',\n            level_number=1,\n            is_active=True\n        )\n        \n        # Inactive job level\n        inactive_level = JobLevel(\n            name='Deprecated Position',\n            level_number=2,\n            is_active=False\n        )\n        \n        db.session.add_all([active_level, inactive_level])\n        db.session.commit()\n        \n        assert active_level.is_active is True\n        assert inactive_level.is_active is False\n\n    def test_joblevel_hierarchical_structure(self):\n        \"\"\"Test parent-child relationship\"\"\"\n        # Parent level\n        parent_level = JobLevel(\n            name='Director',\n            level_number=6\n        )\n        \n        db.session.add(parent_level)\n        db.session.commit()\n        \n        # Child level\n        child_level = JobLevel(\n            name='Senior Manager',\n            level_number=5,\n            parent_level_id=parent_level.id\n        )\n        \n        db.session.add(child_level)\n        db.session.commit()\n        \n        assert child_level.parent_level_id == parent_level.id\n\n    def test_joblevel_query_by_level_number(self):\n        \"\"\"Test querying by level number\"\"\"\n        job_level = JobLevel(\n            name='Specific Level Test',\n            level_number=99\n        )\n        \n        db.session.add(job_level)\n        db.session.commit()\n        \n        # Query by level number\n        found_levels = JobLevel.query.filter_by(level_number=99).all()\n        assert len(found_levels) >= 1\n        assert job_level in found_levels\n\n    def test_joblevel_query_by_management_status(self):\n        \"\"\"Test querying by management status\"\"\"\n        manager_level = JobLevel(\n            name='Manager Position',\n            level_number=10,\n            is_management=True\n        )\n        \n        db.session.add(manager_level)\n        db.session.commit()\n        \n        # Query management positions\n        management_levels = JobLevel.query.filter_by(is_management=True).all()\n        assert len(management_levels) >= 1\n        assert manager_level in management_levels\n\n    def test_joblevel_query_by_active_status(self):\n        \"\"\"Test querying by active status\"\"\"\n        active_level = JobLevel(\n            name='Currently Active',\n            level_number=11,\n            is_active=True\n        )\n        \n        db.session.add(active_level)\n        db.session.commit()\n        \n        # Query active levels\n        active_levels = JobLevel.query.filter_by(is_active=True).all()\n        assert len(active_levels) >= 1\n        assert active_level in active_levels\n\n    def test_joblevel_salary_range_queries(self):\n        \"\"\"Test querying by salary ranges\"\"\"\n        high_salary_level = JobLevel(\n            name='High Salary Position',\n            level_number=12,\n            min_salary=80000.0,\n            max_salary=120000.0\n        )\n        \n        db.session.add(high_salary_level)\n        db.session.commit()\n        \n        # Query levels with high minimum salary\n        high_paying_levels = JobLevel.query.filter(JobLevel.min_salary >= 75000.0).all()\n        assert len(high_paying_levels) >= 1\n        assert high_salary_level in high_paying_levels\n\n    def test_joblevel_timestamps(self):\n        \"\"\"Test automatic timestamp handling\"\"\"\n        job_level = JobLevel(\n            name='Timestamp Test',\n            level_number=13\n        )\n        \n        db.session.add(job_level)\n        db.session.commit()\n        \n        assert job_level.created_at is not None\n        assert job_level.updated_at is not None\n        assert isinstance(job_level.created_at, datetime)\n        assert isinstance(job_level.updated_at, datetime)\n\n    def test_joblevel_update_operations(self):\n        \"\"\"Test job level update operations\"\"\"\n        job_level = JobLevel(\n            name='Original Name',\n            level_number=14,\n            min_salary=40000.0,\n            is_active=True\n        )\n        \n        db.session.add(job_level)\n        db.session.commit()\n        \n        # Update job level\n        job_level.name = 'Updated Name'\n        job_level.min_salary = 50000.0\n        job_level.is_active = False\n        \n        db.session.commit()\n        \n        updated_level = JobLevel.query.get(job_level.id)\n        assert updated_level.name == 'Updated Name'\n        assert updated_level.min_salary == 50000.0\n        assert updated_level.is_active is False\n\n    def test_joblevel_deletion(self):\n        \"\"\"Test job level deletion\"\"\"\n        job_level = JobLevel(\n            name='To Be Deleted',\n            level_number=15\n        )\n        \n        db.session.add(job_level)\n        db.session.commit()\n        level_id = job_level.id\n        \n        db.session.delete(job_level)\n        db.session.commit()\n        \n        deleted_level = JobLevel.query.get(level_id)\n        assert deleted_level is None\n\n    def test_joblevel_name_length(self):\n        \"\"\"Test name field length\"\"\"\n        long_name = 'Very Long Job Level Name That Tests The Maximum Field Length Constraint For Job Level Names'\n        \n        job_level = JobLevel(\n            name=long_name[:100],  # Truncate to VARCHAR(100) limit\n            level_number=16\n        )\n        \n        db.session.add(job_level)\n        db.session.commit()\n        \n        assert len(job_level.name) <= 100\n        assert job_level.name == long_name[:100]\n", "modifiedCode": "\"\"\"\nUnit tests for JobLevel model.\nTests business logic, validations, and model functionality.\n\"\"\"\n\nimport pytest\nfrom datetime import datetime\nfrom models import JobLevel\nfrom extensions import db\n\n\nclass TestJobLevelModel:\n    \"\"\"Test suite for JobLevel model unit tests\"\"\"\n\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        \"\"\"Setup test data for each test method\"\"\"\n        self.app = app\n        self.user = test_user\n\n    def test_joblevel_creation_basic(self):\n        \"\"\"Test basic job level creation\"\"\"\n        job_level = JobLevel(\n            name='Junior Developer',\n            level_number=1,\n            description='Entry level developer position'\n        )\n        \n        db.session.add(job_level)\n        db.session.commit()\n        \n        assert job_level.id is not None\n        assert job_level.name == 'Junior Developer'\n        assert job_level.level_number == 1\n        assert job_level.description == 'Entry level developer position'\n\n    def test_joblevel_creation_complete(self):\n        \"\"\"Test job level creation with all fields\"\"\"\n        job_level = JobLevel(\n            name='Senior Developer',\n            level_number=3,\n            description='Senior level developer with leadership responsibilities',\n            min_salary=60000.0,\n            max_salary=90000.0,\n            typical_years_experience=5,\n            is_management=False,\n            is_active=True\n        )\n        \n        db.session.add(job_level)\n        db.session.commit()\n        \n        assert job_level.name == 'Senior Developer'\n        assert job_level.level_number == 3\n        assert job_level.min_salary == 60000.0\n        assert job_level.max_salary == 90000.0\n        assert job_level.typical_years_experience == 5\n        assert job_level.is_management is False\n        assert job_level.is_active is True\n\n    def test_joblevel_level_numbers(self):\n        \"\"\"Test different level numbers\"\"\"\n        levels = [\n            ('Intern', 100),\n            ('Junior', 101),\n            ('Mid-level', 102),\n            ('Senior', 103),\n            ('Lead', 104),\n            ('Principal', 105)\n        ]\n        \n        job_levels = []\n        for name, level_num in levels:\n            job_level = JobLevel(\n                name=name,\n                level_number=level_num\n            )\n            job_levels.append(job_level)\n        \n        db.session.add_all(job_levels)\n        db.session.commit()\n        \n        for job_level, (expected_name, expected_level) in zip(job_levels, levels):\n            assert job_level.name == expected_name\n            assert job_level.level_number == expected_level\n\n    def test_joblevel_salary_ranges(self):\n        \"\"\"Test salary range functionality\"\"\"\n        salary_ranges = [\n            ('Entry Level', 30000.0, 45000.0),\n            ('Mid Level', 45000.0, 70000.0),\n            ('Senior Level', 70000.0, 100000.0),\n            ('Executive Level', 100000.0, 150000.0)\n        ]\n        \n        job_levels = []\n        for i, (name, min_sal, max_sal) in enumerate(salary_ranges):\n            job_level = JobLevel(\n                name=name,\n                level_number=200 + i,  # Numeri unici\n                min_salary=min_sal,\n                max_salary=max_sal\n            )\n            job_levels.append(job_level)\n        \n        db.session.add_all(job_levels)\n        db.session.commit()\n        \n        for job_level, (_, expected_min, expected_max) in zip(job_levels, salary_ranges):\n            assert job_level.min_salary == expected_min\n            assert job_level.max_salary == expected_max\n            assert job_level.min_salary <= job_level.max_salary\n\n    def test_joblevel_management_flag(self):\n        \"\"\"Test management flag functionality\"\"\"\n        # Non-management role\n        individual_contributor = JobLevel(\n            name='Software Engineer',\n            level_number=300,\n            is_management=False\n        )\n\n        # Management role\n        team_lead = JobLevel(\n            name='Engineering Manager',\n            level_number=301,\n            is_management=True\n        )\n        \n        db.session.add_all([individual_contributor, team_lead])\n        db.session.commit()\n        \n        assert individual_contributor.is_management is False\n        assert team_lead.is_management is True\n\n    def test_joblevel_experience_requirements(self):\n        \"\"\"Test typical years experience field\"\"\"\n        experience_levels = [\n            ('Graduate', 0),\n            ('Junior', 1),\n            ('Mid-level', 3),\n            ('Senior', 5),\n            ('Staff', 8),\n            ('Principal', 12)\n        ]\n        \n        job_levels = []\n        for i, (name, years) in enumerate(experience_levels):\n            job_level = JobLevel(\n                name=name,\n                level_number=i,\n                typical_years_experience=years\n            )\n            job_levels.append(job_level)\n        \n        db.session.add_all(job_levels)\n        db.session.commit()\n        \n        for job_level, (_, expected_years) in zip(job_levels, experience_levels):\n            assert job_level.typical_years_experience == expected_years\n            assert job_level.typical_years_experience >= 0\n\n    def test_joblevel_active_status(self):\n        \"\"\"Test active status functionality\"\"\"\n        # Active job level\n        active_level = JobLevel(\n            name='Active Position',\n            level_number=1,\n            is_active=True\n        )\n        \n        # Inactive job level\n        inactive_level = JobLevel(\n            name='Deprecated Position',\n            level_number=2,\n            is_active=False\n        )\n        \n        db.session.add_all([active_level, inactive_level])\n        db.session.commit()\n        \n        assert active_level.is_active is True\n        assert inactive_level.is_active is False\n\n    def test_joblevel_hierarchical_structure(self):\n        \"\"\"Test parent-child relationship\"\"\"\n        # Parent level\n        parent_level = JobLevel(\n            name='Director',\n            level_number=6\n        )\n        \n        db.session.add(parent_level)\n        db.session.commit()\n        \n        # Child level\n        child_level = JobLevel(\n            name='Senior Manager',\n            level_number=5,\n            parent_level_id=parent_level.id\n        )\n        \n        db.session.add(child_level)\n        db.session.commit()\n        \n        assert child_level.parent_level_id == parent_level.id\n\n    def test_joblevel_query_by_level_number(self):\n        \"\"\"Test querying by level number\"\"\"\n        job_level = JobLevel(\n            name='Specific Level Test',\n            level_number=99\n        )\n        \n        db.session.add(job_level)\n        db.session.commit()\n        \n        # Query by level number\n        found_levels = JobLevel.query.filter_by(level_number=99).all()\n        assert len(found_levels) >= 1\n        assert job_level in found_levels\n\n    def test_joblevel_query_by_management_status(self):\n        \"\"\"Test querying by management status\"\"\"\n        manager_level = JobLevel(\n            name='Manager Position',\n            level_number=10,\n            is_management=True\n        )\n        \n        db.session.add(manager_level)\n        db.session.commit()\n        \n        # Query management positions\n        management_levels = JobLevel.query.filter_by(is_management=True).all()\n        assert len(management_levels) >= 1\n        assert manager_level in management_levels\n\n    def test_joblevel_query_by_active_status(self):\n        \"\"\"Test querying by active status\"\"\"\n        active_level = JobLevel(\n            name='Currently Active',\n            level_number=11,\n            is_active=True\n        )\n        \n        db.session.add(active_level)\n        db.session.commit()\n        \n        # Query active levels\n        active_levels = JobLevel.query.filter_by(is_active=True).all()\n        assert len(active_levels) >= 1\n        assert active_level in active_levels\n\n    def test_joblevel_salary_range_queries(self):\n        \"\"\"Test querying by salary ranges\"\"\"\n        high_salary_level = JobLevel(\n            name='High Salary Position',\n            level_number=12,\n            min_salary=80000.0,\n            max_salary=120000.0\n        )\n        \n        db.session.add(high_salary_level)\n        db.session.commit()\n        \n        # Query levels with high minimum salary\n        high_paying_levels = JobLevel.query.filter(JobLevel.min_salary >= 75000.0).all()\n        assert len(high_paying_levels) >= 1\n        assert high_salary_level in high_paying_levels\n\n    def test_joblevel_timestamps(self):\n        \"\"\"Test automatic timestamp handling\"\"\"\n        job_level = JobLevel(\n            name='Timestamp Test',\n            level_number=13\n        )\n        \n        db.session.add(job_level)\n        db.session.commit()\n        \n        assert job_level.created_at is not None\n        assert job_level.updated_at is not None\n        assert isinstance(job_level.created_at, datetime)\n        assert isinstance(job_level.updated_at, datetime)\n\n    def test_joblevel_update_operations(self):\n        \"\"\"Test job level update operations\"\"\"\n        job_level = JobLevel(\n            name='Original Name',\n            level_number=14,\n            min_salary=40000.0,\n            is_active=True\n        )\n        \n        db.session.add(job_level)\n        db.session.commit()\n        \n        # Update job level\n        job_level.name = 'Updated Name'\n        job_level.min_salary = 50000.0\n        job_level.is_active = False\n        \n        db.session.commit()\n        \n        updated_level = JobLevel.query.get(job_level.id)\n        assert updated_level.name == 'Updated Name'\n        assert updated_level.min_salary == 50000.0\n        assert updated_level.is_active is False\n\n    def test_joblevel_deletion(self):\n        \"\"\"Test job level deletion\"\"\"\n        job_level = JobLevel(\n            name='To Be Deleted',\n            level_number=15\n        )\n        \n        db.session.add(job_level)\n        db.session.commit()\n        level_id = job_level.id\n        \n        db.session.delete(job_level)\n        db.session.commit()\n        \n        deleted_level = JobLevel.query.get(level_id)\n        assert deleted_level is None\n\n    def test_joblevel_name_length(self):\n        \"\"\"Test name field length\"\"\"\n        long_name = 'Very Long Job Level Name That Tests The Maximum Field Length Constraint For Job Level Names'\n        \n        job_level = JobLevel(\n            name=long_name[:100],  # Truncate to VARCHAR(100) limit\n            level_number=16\n        )\n        \n        db.session.add(job_level)\n        db.session.commit()\n        \n        assert len(job_level.name) <= 100\n        assert job_level.name == long_name[:100]\n"}