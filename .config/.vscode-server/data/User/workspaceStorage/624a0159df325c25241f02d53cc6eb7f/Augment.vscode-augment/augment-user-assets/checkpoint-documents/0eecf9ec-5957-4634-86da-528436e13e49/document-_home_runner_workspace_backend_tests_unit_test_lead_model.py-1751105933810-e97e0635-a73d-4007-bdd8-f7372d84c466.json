{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_lead_model.py"}, "modifiedCode": "\"\"\"Unit tests for Lead model.\"\"\"\nimport pytest\nfrom models import Lead, User\nfrom extensions import db\n\nclass TestLeadModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n\n    def test_lead_creation_basic(self):\n        lead = Lead(\n            name='<PERSON>',\n            email='<EMAIL>',\n            source='website'\n        )\n        db.session.add(lead)\n        db.session.commit()\n        \n        assert lead.id is not None\n        assert lead.name == '<PERSON>'\n        assert lead.email == '<EMAIL>'\n        assert lead.source == 'website'\n\n    def test_lead_with_status(self):\n        lead = Lead(\n            name='<PERSON>',\n            email='<EMAIL>',\n            status='qualified',\n            assigned_to=self.user.id\n        )\n        db.session.add(lead)\n        db.session.commit()\n        \n        assert lead.status == 'qualified'\n        assert lead.assigned_to == self.user.id\n\n    def test_lead_deletion(self):\n        lead = Lead(name='To Delete', email='<EMAIL>')\n        db.session.add(lead)\n        db.session.commit()\n        lead_id = lead.id\n        \n        db.session.delete(lead)\n        db.session.commit()\n        \n        deleted = Lead.query.get(lead_id)\n        assert deleted is None\n"}