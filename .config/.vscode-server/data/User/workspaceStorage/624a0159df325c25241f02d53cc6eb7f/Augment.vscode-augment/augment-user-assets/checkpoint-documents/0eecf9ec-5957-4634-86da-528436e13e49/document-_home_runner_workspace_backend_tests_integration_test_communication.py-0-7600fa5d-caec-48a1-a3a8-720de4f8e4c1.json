{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/integration/test_communication.py"}, "originalCode": "import pytest\nimport json\nfrom datetime import datetime, timedelta\nfrom flask import url_for\nfrom models import User, db\nfrom backend.models import (\n    ForumTopic, ForumComment, Poll, PollOption, PollVote,\n    DirectMessage, CommunicationReaction\n)\nfrom utils.permissions import (\n    PERMISSION_VIEW_COMMUNICATION, PERMISSION_CREATE_POLLS\n)\n\n\nclass TestCommunicationAPI:\n    \"\"\"Test suite per le API del modulo comunicazione\"\"\"\n    \n    def setup_method(self):\n        \"\"\"Setup per ogni test\"\"\"\n        # Crea utenti di test\n        self.admin_user = User(\n            username='admin_test',\n            email='<EMAIL>',\n            first_name='Admin',\n            last_name='Test',\n            role='admin',\n            is_active=True\n        )\n        self.admin_user.set_password('password123')\n        \n        self.employee_user = User(\n            username='employee_test',\n            email='<EMAIL>',\n            first_name='Employee',\n            last_name='Test',\n            role='employee',\n            is_active=True\n        )\n        self.employee_user.set_password('password123')\n        \n        db.session.add(self.admin_user)\n        db.session.add(self.employee_user)\n        db.session.commit()\n    \n    def teardown_method(self):\n        \"\"\"Cleanup dopo ogni test\"\"\"\n        # Rimuovi tutti i dati di test\n        ForumComment.query.delete()\n        ForumTopic.query.delete()\n        PollVote.query.delete()\n        PollOption.query.delete()\n        Poll.query.delete()\n        DirectMessage.query.delete()\n        CommunicationReaction.query.delete()\n        User.query.filter(User.username.in_(['admin_test', 'employee_test'])).delete()\n        db.session.commit()\n    \n    def login_user(self, client, username, password):\n        \"\"\"Helper per il login\"\"\"\n        return client.post('/api/auth/login', json={\n            'username': username,\n            'password': password\n        })\n    \n    def test_forum_topic_creation(self, client):\n        \"\"\"Test creazione topic del forum\"\"\"\n        # Login come admin\n        self.login_user(client, 'admin_test', 'password123')\n        \n        # Crea un topic\n        response = client.post('/api/communication/forum/topics', json={\n            'title': 'Test Topic',\n            'content': 'Questo è un topic di test',\n            'category': 'generale'\n        })\n        \n        assert response.status_code == 201\n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert data['data']['title'] == 'Test Topic'\n        assert data['data']['category'] == 'generale'\n    \n    def test_forum_topic_list(self, client):\n        \"\"\"Test recupero lista topic del forum\"\"\"\n        # Login come employee\n        self.login_user(client, 'employee_test', 'password123')\n        \n        # Crea un topic di test\n        topic = ForumTopic(\n            title='Test Topic',\n            content='Contenuto di test',\n            author_id=self.admin_user.id,\n            category='test'\n        )\n        db.session.add(topic)\n        db.session.commit()\n        \n        # Recupera i topic\n        response = client.get('/api/communication/forum/topics')\n        \n        assert response.status_code == 200\n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert len(data['data']['topics']) >= 1\n        assert data['data']['topics'][0]['title'] == 'Test Topic'\n    \n    def test_poll_creation(self, client):\n        \"\"\"Test creazione sondaggio\"\"\"\n        # Login come admin (ha PERMISSION_CREATE_POLLS)\n        self.login_user(client, 'admin_test', 'password123')\n        \n        # Crea un sondaggio\n        response = client.post('/api/communication/polls', json={\n            'title': 'Sondaggio Test',\n            'description': 'Descrizione del sondaggio',\n            'options': ['Opzione 1', 'Opzione 2', 'Opzione 3'],\n            'multiple_choice': False,\n            'end_date': (datetime.utcnow() + timedelta(days=7)).isoformat()\n        })\n        \n        assert response.status_code == 201\n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert data['data']['title'] == 'Sondaggio Test'\n        assert len(data['data']['options']) == 3\n    \n    def test_poll_voting(self, client):\n        \"\"\"Test voto in un sondaggio\"\"\"\n        # Login come employee\n        self.login_user(client, 'employee_test', 'password123')\n        \n        # Crea un sondaggio di test\n        poll = Poll(\n            title='Test Poll',\n            description='Poll di test',\n            creator_id=self.admin_user.id,\n            multiple_choice=False,\n            is_active=True,\n            end_date=datetime.utcnow() + timedelta(days=7)\n        )\n        db.session.add(poll)\n        db.session.flush()\n        \n        # Aggiungi opzioni\n        option1 = PollOption(poll_id=poll.id, text='Opzione 1', order_index=0)\n        option2 = PollOption(poll_id=poll.id, text='Opzione 2', order_index=1)\n        db.session.add(option1)\n        db.session.add(option2)\n        db.session.commit()\n        \n        # Vota\n        response = client.post(f'/api/communication/polls/{poll.id}/vote', json={\n            'option_ids': [option1.id]\n        })\n        \n        assert response.status_code == 201\n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert data['message'] == 'Voto registrato con successo'\n    \n    def test_direct_message_sending(self, client):\n        \"\"\"Test invio messaggio diretto\"\"\"\n        # Login come admin\n        self.login_user(client, 'admin_test', 'password123')\n        \n        # Invia messaggio\n        response = client.post('/api/communication/messages', json={\n            'recipient_id': self.employee_user.id,\n            'subject': 'Test Message',\n            'content': 'Questo è un messaggio di test'\n        })\n        \n        assert response.status_code == 201\n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert data['data']['subject'] == 'Test Message'\n        assert data['data']['recipient_id'] == self.employee_user.id\n    \n    def test_message_list(self, client):\n        \"\"\"Test recupero lista messaggi\"\"\"\n        # Login come employee\n        self.login_user(client, 'employee_test', 'password123')\n        \n        # Crea un messaggio di test\n        message = DirectMessage(\n            sender_id=self.admin_user.id,\n            recipient_id=self.employee_user.id,\n            subject='Test Message',\n            content='Contenuto del messaggio',\n            is_read=False\n        )\n        db.session.add(message)\n        db.session.commit()\n        \n        # Recupera i messaggi\n        response = client.get('/api/communication/messages')\n        \n        assert response.status_code == 200\n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert len(data['data']['messages']) >= 1\n        assert data['data']['messages'][0]['subject'] == 'Test Message'\n    \n    def test_communication_stats(self, client):\n        \"\"\"Test recupero statistiche comunicazione\"\"\"\n        # Login come employee\n        self.login_user(client, 'employee_test', 'password123')\n        \n        # Recupera le statistiche\n        response = client.get('/api/communication/stats')\n        \n        assert response.status_code == 200\n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert 'forum' in data['data']\n        assert 'polls' in data['data']\n        assert 'messages' in data['data']\n        assert 'events' in data['data']\n    \n    def test_unauthorized_access(self, client):\n        \"\"\"Test accesso non autorizzato\"\"\"\n        # Prova ad accedere senza login\n        response = client.get('/api/communication/forum/topics')\n        assert response.status_code == 401\n        \n        response = client.post('/api/communication/polls', json={\n            'title': 'Test Poll',\n            'options': ['Option 1']\n        })\n        assert response.status_code == 401", "modifiedCode": "import pytest\nimport json\nfrom datetime import datetime, timedelta\nfrom flask import url_for\nfrom models import User, db\nfrom backend.models import (\n    ForumTopic, ForumComment, Poll, PollOption, PollVote,\n    DirectMessage, CommunicationReaction\n)\nfrom utils.permissions import (\n    PERMISSION_VIEW_COMMUNICATION, PERMISSION_CREATE_POLLS\n)\n\n\nclass TestCommunicationAPI:\n    \"\"\"Test suite per le API del modulo comunicazione\"\"\"\n    \n    def setup_method(self):\n        \"\"\"Setup per ogni test\"\"\"\n        # Crea utenti di test\n        self.admin_user = User(\n            username='admin_test',\n            email='<EMAIL>',\n            first_name='Admin',\n            last_name='Test',\n            role='admin',\n            is_active=True\n        )\n        self.admin_user.set_password('password123')\n        \n        self.employee_user = User(\n            username='employee_test',\n            email='<EMAIL>',\n            first_name='Employee',\n            last_name='Test',\n            role='employee',\n            is_active=True\n        )\n        self.employee_user.set_password('password123')\n        \n        db.session.add(self.admin_user)\n        db.session.add(self.employee_user)\n        db.session.commit()\n    \n    def teardown_method(self):\n        \"\"\"Cleanup dopo ogni test\"\"\"\n        # Rimuovi tutti i dati di test\n        ForumComment.query.delete()\n        ForumTopic.query.delete()\n        PollVote.query.delete()\n        PollOption.query.delete()\n        Poll.query.delete()\n        DirectMessage.query.delete()\n        CommunicationReaction.query.delete()\n        User.query.filter(User.username.in_(['admin_test', 'employee_test'])).delete()\n        db.session.commit()\n    \n    def login_user(self, client, username, password):\n        \"\"\"Helper per il login\"\"\"\n        return client.post('/api/auth/login', json={\n            'username': username,\n            'password': password\n        })\n    \n    def test_forum_topic_creation(self, client):\n        \"\"\"Test creazione topic del forum\"\"\"\n        # Login come admin\n        self.login_user(client, 'admin_test', 'password123')\n        \n        # Crea un topic\n        response = client.post('/api/communication/forum/topics', json={\n            'title': 'Test Topic',\n            'content': 'Questo è un topic di test',\n            'category': 'generale'\n        })\n        \n        assert response.status_code == 201\n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert data['data']['title'] == 'Test Topic'\n        assert data['data']['category'] == 'generale'\n    \n    def test_forum_topic_list(self, client):\n        \"\"\"Test recupero lista topic del forum\"\"\"\n        # Login come employee\n        self.login_user(client, 'employee_test', 'password123')\n        \n        # Crea un topic di test\n        topic = ForumTopic(\n            title='Test Topic',\n            content='Contenuto di test',\n            author_id=self.admin_user.id,\n            category='test'\n        )\n        db.session.add(topic)\n        db.session.commit()\n        \n        # Recupera i topic\n        response = client.get('/api/communication/forum/topics')\n        \n        assert response.status_code == 200\n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert len(data['data']['topics']) >= 1\n        assert data['data']['topics'][0]['title'] == 'Test Topic'\n    \n    def test_poll_creation(self, client):\n        \"\"\"Test creazione sondaggio\"\"\"\n        # Login come admin (ha PERMISSION_CREATE_POLLS)\n        self.login_user(client, 'admin_test', 'password123')\n        \n        # Crea un sondaggio\n        response = client.post('/api/communication/polls', json={\n            'title': 'Sondaggio Test',\n            'description': 'Descrizione del sondaggio',\n            'options': ['Opzione 1', 'Opzione 2', 'Opzione 3'],\n            'multiple_choice': False,\n            'end_date': (datetime.utcnow() + timedelta(days=7)).isoformat()\n        })\n        \n        assert response.status_code == 201\n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert data['data']['title'] == 'Sondaggio Test'\n        assert len(data['data']['options']) == 3\n    \n    def test_poll_voting(self, client):\n        \"\"\"Test voto in un sondaggio\"\"\"\n        # Login come employee\n        self.login_user(client, 'employee_test', 'password123')\n        \n        # Crea un sondaggio di test\n        poll = Poll(\n            title='Test Poll',\n            description='Poll di test',\n            creator_id=self.admin_user.id,\n            multiple_choice=False,\n            is_active=True,\n            end_date=datetime.utcnow() + timedelta(days=7)\n        )\n        db.session.add(poll)\n        db.session.flush()\n        \n        # Aggiungi opzioni\n        option1 = PollOption(poll_id=poll.id, text='Opzione 1', order_index=0)\n        option2 = PollOption(poll_id=poll.id, text='Opzione 2', order_index=1)\n        db.session.add(option1)\n        db.session.add(option2)\n        db.session.commit()\n        \n        # Vota\n        response = client.post(f'/api/communication/polls/{poll.id}/vote', json={\n            'option_ids': [option1.id]\n        })\n        \n        assert response.status_code == 201\n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert data['message'] == 'Voto registrato con successo'\n    \n    def test_direct_message_sending(self, client):\n        \"\"\"Test invio messaggio diretto\"\"\"\n        # Login come admin\n        self.login_user(client, 'admin_test', 'password123')\n        \n        # Invia messaggio\n        response = client.post('/api/communication/messages', json={\n            'recipient_id': self.employee_user.id,\n            'subject': 'Test Message',\n            'content': 'Questo è un messaggio di test'\n        })\n        \n        assert response.status_code == 201\n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert data['data']['subject'] == 'Test Message'\n        assert data['data']['recipient_id'] == self.employee_user.id\n    \n    def test_message_list(self, client):\n        \"\"\"Test recupero lista messaggi\"\"\"\n        # Login come employee\n        self.login_user(client, 'employee_test', 'password123')\n        \n        # Crea un messaggio di test\n        message = DirectMessage(\n            sender_id=self.admin_user.id,\n            recipient_id=self.employee_user.id,\n            subject='Test Message',\n            content='Contenuto del messaggio',\n            is_read=False\n        )\n        db.session.add(message)\n        db.session.commit()\n        \n        # Recupera i messaggi\n        response = client.get('/api/communication/messages')\n        \n        assert response.status_code == 200\n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert len(data['data']['messages']) >= 1\n        assert data['data']['messages'][0]['subject'] == 'Test Message'\n    \n    def test_communication_stats(self, client):\n        \"\"\"Test recupero statistiche comunicazione\"\"\"\n        # Login come employee\n        self.login_user(client, 'employee_test', 'password123')\n        \n        # Recupera le statistiche\n        response = client.get('/api/communication/stats')\n        \n        assert response.status_code == 200\n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert 'forum' in data['data']\n        assert 'polls' in data['data']\n        assert 'messages' in data['data']\n        assert 'events' in data['data']\n    \n    def test_unauthorized_access(self, client):\n        \"\"\"Test accesso non autorizzato\"\"\"\n        # Prova ad accedere senza login\n        response = client.get('/api/communication/forum/topics')\n        assert response.status_code == 401\n        \n        response = client.post('/api/communication/polls', json={\n            'title': 'Test Poll',\n            'options': ['Option 1']\n        })\n        assert response.status_code == 401"}