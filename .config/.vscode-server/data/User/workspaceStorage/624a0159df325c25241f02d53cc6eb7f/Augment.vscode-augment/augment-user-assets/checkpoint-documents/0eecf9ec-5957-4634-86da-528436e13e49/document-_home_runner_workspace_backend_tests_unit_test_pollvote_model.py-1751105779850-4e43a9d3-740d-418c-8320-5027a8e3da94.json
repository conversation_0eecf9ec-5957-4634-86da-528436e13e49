{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_pollvote_model.py"}, "originalCode": "\"\"\"Unit tests for PollVote model.\"\"\"\nimport pytest\nfrom models import PollVote, Poll, PollOption, User\nfrom extensions import db\n\nclass TestPollVoteModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n        \n        self.poll = Poll.query.first()\n        if not self.poll:\n            self.poll = Poll(title='Test Poll', description='Test', author_id=self.user.id)  # Campo corretto\n            db.session.add(self.poll)\n            db.session.commit()\n            \n        self.option = PollOption.query.first()\n        if not self.option:\n            self.option = PollOption(poll_id=self.poll.id, option_text='Option A')  # Campo corretto\n            db.session.add(self.option)\n            db.session.commit()\n\n    def test_pollvote_creation_basic(self):\n        vote = PollVote(\n            poll_id=self.poll.id,\n            option_id=self.option.id,\n            user_id=self.user.id\n        )\n        db.session.add(vote)\n        db.session.commit()\n        \n        assert vote.id is not None\n        assert vote.poll_id == self.poll.id\n        assert vote.option_id == self.option.id\n        assert vote.user_id == self.user.id\n\n    def test_pollvote_deletion(self):\n        vote = PollVote(\n            poll_id=self.poll.id,\n            option_id=self.option.id,\n            user_id=self.user.id\n        )\n        db.session.add(vote)\n        db.session.commit()\n        vote_id = vote.id\n        \n        db.session.delete(vote)\n        db.session.commit()\n        \n        deleted = PollVote.query.get(vote_id)\n        assert deleted is None\n", "modifiedCode": "\"\"\"Unit tests for PollVote model.\"\"\"\nimport pytest\nfrom models import PollVote, Poll, PollOption, User\nfrom extensions import db\n\nclass TestPollVoteModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n        \n        self.poll = Poll.query.first()\n        if not self.poll:\n            self.poll = Poll(title='Test Poll', description='Test', author_id=self.user.id)  # Campo corretto\n            db.session.add(self.poll)\n            db.session.commit()\n            \n        self.option = PollOption.query.first()\n        if not self.option:\n            self.option = PollOption(poll_id=self.poll.id, option_text='Option A')  # Campo corretto\n            db.session.add(self.option)\n            db.session.commit()\n\n    def test_pollvote_creation_basic(self):\n        vote = PollVote(\n            poll_id=self.poll.id,\n            option_id=self.option.id,\n            user_id=self.user.id\n        )\n        db.session.add(vote)\n        db.session.commit()\n        \n        assert vote.id is not None\n        assert vote.poll_id == self.poll.id\n        assert vote.option_id == self.option.id\n        assert vote.user_id == self.user.id\n\n    def test_pollvote_deletion(self):\n        vote = PollVote(\n            poll_id=self.poll.id,\n            option_id=self.option.id,\n            user_id=self.user.id\n        )\n        db.session.add(vote)\n        db.session.commit()\n        vote_id = vote.id\n        \n        db.session.delete(vote)\n        db.session.commit()\n        \n        deleted = PollVote.query.get(vote_id)\n        assert deleted is None\n"}