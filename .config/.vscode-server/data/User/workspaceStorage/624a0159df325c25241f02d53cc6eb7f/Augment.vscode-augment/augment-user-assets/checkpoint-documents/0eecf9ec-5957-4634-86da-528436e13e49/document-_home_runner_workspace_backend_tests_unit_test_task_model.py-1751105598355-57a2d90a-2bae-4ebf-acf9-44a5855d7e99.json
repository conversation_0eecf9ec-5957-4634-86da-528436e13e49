{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_task_model.py"}, "originalCode": "\"\"\"Unit tests for Task model.\"\"\"\nimport pytest\nfrom datetime import date\nfrom models import Task, Project, User\nfrom extensions import db\n\nclass TestTaskModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n        \n        self.project = Project.query.first()\n        if not self.project:\n            self.project = Project(name='Test Project', description='Test')  # Rimosso manager_id\n            db.session.add(self.project)\n            db.session.commit()\n\n    def test_task_creation_basic(self):\n        task = Task(\n            name='Test Task',  # Campo corretto è 'name'\n            description='This is a test task',\n            project_id=self.project.id,\n            assignee_id=self.user.id  # Campo corretto è 'assignee_id'\n        )\n        db.session.add(task)\n        db.session.commit()\n\n        assert task.id is not None\n        assert task.name == 'Test Task'\n        assert task.description == 'This is a test task'\n        assert task.project_id == self.project.id\n        assert task.assignee_id == self.user.id\n\n    def test_task_status(self):\n        task = Task(\n            title='Status Task',\n            description='Task with status',\n            project_id=self.project.id,\n            status='in_progress'\n        )\n        db.session.add(task)\n        db.session.commit()\n        \n        assert task.status == 'in_progress'\n\n    def test_task_deletion(self):\n        task = Task(title='To Delete', description='Delete me', project_id=self.project.id)\n        db.session.add(task)\n        db.session.commit()\n        task_id = task.id\n        \n        db.session.delete(task)\n        db.session.commit()\n        \n        deleted = Task.query.get(task_id)\n        assert deleted is None\n", "modifiedCode": "\"\"\"Unit tests for Task model.\"\"\"\nimport pytest\nfrom datetime import date\nfrom models import Task, Project, User\nfrom extensions import db\n\nclass TestTaskModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n        \n        self.project = Project.query.first()\n        if not self.project:\n            self.project = Project(name='Test Project', description='Test')  # Rimosso manager_id\n            db.session.add(self.project)\n            db.session.commit()\n\n    def test_task_creation_basic(self):\n        task = Task(\n            name='Test Task',  # Campo corretto è 'name'\n            description='This is a test task',\n            project_id=self.project.id,\n            assignee_id=self.user.id  # Campo corretto è 'assignee_id'\n        )\n        db.session.add(task)\n        db.session.commit()\n\n        assert task.id is not None\n        assert task.name == 'Test Task'\n        assert task.description == 'This is a test task'\n        assert task.project_id == self.project.id\n        assert task.assignee_id == self.user.id\n\n    def test_task_status(self):\n        task = Task(\n            name='Status Task',  # <PERSON> corretto è 'name'\n            description='Task with status',\n            project_id=self.project.id,\n            status='in_progress'\n        )\n        db.session.add(task)\n        db.session.commit()\n        \n        assert task.status == 'in_progress'\n\n    def test_task_deletion(self):\n        task = Task(title='To Delete', description='Delete me', project_id=self.project.id)\n        db.session.add(task)\n        db.session.commit()\n        task_id = task.id\n        \n        db.session.delete(task)\n        db.session.commit()\n        \n        deleted = Task.query.get(task_id)\n        assert deleted is None\n"}