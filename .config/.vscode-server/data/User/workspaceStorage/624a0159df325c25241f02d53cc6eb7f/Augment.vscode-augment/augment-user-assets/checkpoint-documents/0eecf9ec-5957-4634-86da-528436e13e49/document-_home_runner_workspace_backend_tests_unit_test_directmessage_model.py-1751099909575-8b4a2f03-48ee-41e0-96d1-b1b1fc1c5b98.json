{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_directmessage_model.py"}, "modifiedCode": "\"\"\"Unit tests for DirectMessage model.\"\"\"\nimport pytest\nfrom models import DirectMessage, User\nfrom extensions import db\n\nclass TestDirectMessageModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n        \n        self.recipient = User(username='recipient', email='<EMAIL>')\n        db.session.add(self.recipient)\n        db.session.commit()\n\n    def test_directmessage_creation_basic(self):\n        message = DirectMessage(\n            sender_id=self.user.id,\n            recipient_id=self.recipient.id,\n            content='Hello, this is a test message'\n        )\n        db.session.add(message)\n        db.session.commit()\n        \n        assert message.id is not None\n        assert message.sender_id == self.user.id\n        assert message.recipient_id == self.recipient.id\n        assert message.content == 'Hello, this is a test message'\n\n    def test_directmessage_read_status(self):\n        message = DirectMessage(\n            sender_id=self.user.id,\n            recipient_id=self.recipient.id,\n            content='Test message',\n            is_read=False\n        )\n        db.session.add(message)\n        db.session.commit()\n        \n        assert message.is_read is False\n        \n        message.is_read = True\n        db.session.commit()\n        \n        updated = DirectMessage.query.get(message.id)\n        assert updated.is_read is True\n\n    def test_directmessage_deletion(self):\n        message = DirectMessage(\n            sender_id=self.user.id,\n            recipient_id=self.recipient.id,\n            content='To be deleted'\n        )\n        db.session.add(message)\n        db.session.commit()\n        message_id = message.id\n        \n        db.session.delete(message)\n        db.session.commit()\n        \n        deleted = DirectMessage.query.get(message_id)\n        assert deleted is None\n"}