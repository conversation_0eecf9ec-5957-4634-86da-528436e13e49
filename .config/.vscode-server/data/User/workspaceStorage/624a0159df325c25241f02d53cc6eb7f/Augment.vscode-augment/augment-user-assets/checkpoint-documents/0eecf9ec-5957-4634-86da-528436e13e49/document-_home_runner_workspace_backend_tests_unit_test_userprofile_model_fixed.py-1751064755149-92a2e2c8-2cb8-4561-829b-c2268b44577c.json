{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_userprofile_model_fixed.py"}, "modifiedCode": "\"\"\"\nUnit tests for UserProfile model.\nTests business logic, validations, and model functionality.\nFixed version with unique user_id handling.\n\"\"\"\n\nimport pytest\nfrom datetime import datetime, date\nfrom models import UserProfile, User\nfrom extensions import db\n\n\nclass TestUserProfileModel:\n    \"\"\"Test suite for UserProfile model unit tests\"\"\"\n\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        \"\"\"Setup test data for each test method\"\"\"\n        self.app = app\n        self.user = test_user\n\n    def test_userprofile_creation_basic(self):\n        \"\"\"Test basic user profile creation\"\"\"\n        profile = UserProfile(\n            user_id=self.user.id,\n            employee_id='EMP001_Basic',\n            job_title='Software Developer'\n        )\n        \n        db.session.add(profile)\n        db.session.commit()\n        \n        assert profile.id is not None\n        assert profile.user_id == self.user.id\n        assert profile.employee_id == 'EMP001_Basic'\n        assert profile.job_title == 'Software Developer'\n\n    def test_userprofile_creation_complete(self):\n        \"\"\"Test user profile creation with all fields\"\"\"\n        # Create unique user for this test\n        user2 = User(username='testuser_complete', email='<EMAIL>')\n        db.session.add(user2)\n        db.session.commit()\n        \n        profile = UserProfile(\n            user_id=user2.id,\n            employee_id='EMP002_Complete',\n            job_title='Senior Developer',\n            birth_date=date(1990, 5, 15),\n            address='123 Main St, City',\n            emergency_contact_name='John Doe',\n            emergency_contact_phone='+39 ************',\n            emergency_contact_relationship='Spouse',\n            employment_type='full_time',\n            work_location='Remote',\n            salary=50000.0,\n            salary_currency='EUR',\n            weekly_hours=40.0,\n            daily_hours=8.0,\n            profile_completion=75.5\n        )\n        \n        db.session.add(profile)\n        db.session.commit()\n        \n        assert profile.birth_date == date(1990, 5, 15)\n        assert profile.salary == 50000.0\n        assert profile.profile_completion == 75.5\n        assert profile.created_at is not None\n\n    def test_userprofile_user_relationship(self):\n        \"\"\"Test relationship with User model\"\"\"\n        user3 = User(username='testuser_relationship', email='<EMAIL>')\n        db.session.add(user3)\n        db.session.commit()\n        \n        profile = UserProfile(\n            user_id=user3.id,\n            employee_id='EMP003_Relationship'\n        )\n        \n        db.session.add(profile)\n        db.session.commit()\n        \n        assert profile.user is not None\n        assert profile.user.id == user3.id\n\n    def test_userprofile_salary_handling(self):\n        \"\"\"Test salary field functionality\"\"\"\n        user4 = User(username='testuser_salary', email='<EMAIL>')\n        db.session.add(user4)\n        db.session.commit()\n        \n        profile = UserProfile(\n            user_id=user4.id,\n            employee_id='EMP004_Salary',\n            salary=75000.50,\n            salary_currency='USD'\n        )\n        \n        db.session.add(profile)\n        db.session.commit()\n        \n        assert profile.salary == 75000.50\n        assert profile.salary_currency == 'USD'\n\n    def test_userprofile_employment_details(self):\n        \"\"\"Test employment-related fields\"\"\"\n        user5 = User(username='testuser_employment', email='<EMAIL>')\n        db.session.add(user5)\n        db.session.commit()\n        \n        profile = UserProfile(\n            user_id=user5.id,\n            employee_id='EMP005_Employment',\n            employment_type='part_time',\n            work_location='Office',\n            weekly_hours=20.0,\n            daily_hours=4.0,\n            notice_period_days=15\n        )\n        \n        db.session.add(profile)\n        db.session.commit()\n        \n        assert profile.employment_type == 'part_time'\n        assert profile.weekly_hours == 20.0\n        assert profile.notice_period_days == 15\n\n    def test_userprofile_emergency_contact(self):\n        \"\"\"Test emergency contact fields\"\"\"\n        user6 = User(username='testuser_emergency', email='<EMAIL>')\n        db.session.add(user6)\n        db.session.commit()\n        \n        profile = UserProfile(\n            user_id=user6.id,\n            employee_id='EMP006_Emergency',\n            emergency_contact_name='Jane Smith',\n            emergency_contact_phone='+39 ************',\n            emergency_contact_relationship='Sister'\n        )\n        \n        db.session.add(profile)\n        db.session.commit()\n        \n        assert profile.emergency_contact_name == 'Jane Smith'\n        assert profile.emergency_contact_phone == '+39 ************'\n        assert profile.emergency_contact_relationship == 'Sister'\n\n    def test_userprofile_timestamps(self):\n        \"\"\"Test automatic timestamp handling\"\"\"\n        user7 = User(username='testuser_timestamps', email='<EMAIL>')\n        db.session.add(user7)\n        db.session.commit()\n        \n        profile = UserProfile(\n            user_id=user7.id,\n            employee_id='EMP007_Timestamps'\n        )\n        \n        db.session.add(profile)\n        db.session.commit()\n        \n        assert profile.created_at is not None\n        assert profile.updated_at is not None\n        assert isinstance(profile.created_at, datetime)\n\n    def test_userprofile_update_operations(self):\n        \"\"\"Test profile update operations\"\"\"\n        user8 = User(username='testuser_update', email='<EMAIL>')\n        db.session.add(user8)\n        db.session.commit()\n        \n        profile = UserProfile(\n            user_id=user8.id,\n            employee_id='EMP008_Update',\n            job_title='Junior Developer',\n            salary=30000.0\n        )\n        \n        db.session.add(profile)\n        db.session.commit()\n        \n        # Update profile\n        profile.job_title = 'Senior Developer'\n        profile.salary = 60000.0\n        profile.profile_completion = 90.0\n        \n        db.session.commit()\n        \n        updated_profile = UserProfile.query.get(profile.id)\n        assert updated_profile.job_title == 'Senior Developer'\n        assert updated_profile.salary == 60000.0\n        assert updated_profile.profile_completion == 90.0\n\n    def test_userprofile_query_by_employee_id(self):\n        \"\"\"Test querying by employee ID\"\"\"\n        user9 = User(username='testuser_query', email='<EMAIL>')\n        db.session.add(user9)\n        db.session.commit()\n        \n        profile = UserProfile(\n            user_id=user9.id,\n            employee_id='UNIQUE_EMP_ID_QUERY'\n        )\n        \n        db.session.add(profile)\n        db.session.commit()\n        \n        found_profile = UserProfile.query.filter_by(employee_id='UNIQUE_EMP_ID_QUERY').first()\n        assert found_profile is not None\n        assert found_profile.id == profile.id\n\n    def test_userprofile_deletion(self):\n        \"\"\"Test profile deletion\"\"\"\n        user10 = User(username='testuser_delete', email='<EMAIL>')\n        db.session.add(user10)\n        db.session.commit()\n        \n        profile = UserProfile(\n            user_id=user10.id,\n            employee_id='EMP_TO_DELETE'\n        )\n        \n        db.session.add(profile)\n        db.session.commit()\n        profile_id = profile.id\n        \n        db.session.delete(profile)\n        db.session.commit()\n        \n        deleted_profile = UserProfile.query.get(profile_id)\n        assert deleted_profile is None\n\n    def test_userprofile_default_values(self):\n        \"\"\"Test default values for fields\"\"\"\n        user11 = User(username='testuser_defaults', email='<EMAIL>')\n        db.session.add(user11)\n        db.session.commit()\n        \n        profile = UserProfile(\n            user_id=user11.id,\n            employee_id='EMP_DEFAULT_TEST'\n        )\n        \n        db.session.add(profile)\n        db.session.commit()\n        \n        assert profile.employment_type == 'full_time'\n        assert profile.salary_currency == 'EUR'\n        assert profile.notice_period_days == 30\n        assert profile.weekly_hours == 40.0\n        assert profile.daily_hours == 8.0\n        assert profile.profile_completion == 0.0\n"}