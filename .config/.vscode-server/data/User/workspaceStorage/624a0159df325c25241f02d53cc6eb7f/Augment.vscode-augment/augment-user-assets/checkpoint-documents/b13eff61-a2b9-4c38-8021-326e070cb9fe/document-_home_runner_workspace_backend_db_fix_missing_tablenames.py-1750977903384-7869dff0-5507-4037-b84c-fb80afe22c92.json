{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/db/fix_missing_tablenames.py"}, "modifiedCode": "#!/usr/bin/env python3\n\"\"\"\nScript per aggiungere __tablename__ mancanti nei modelli.\nQuesto risolve il problema delle tabelle duplicate singolare/plurale.\n\"\"\"\n\nimport os\nimport re\n\ndef add_tablename_to_model(file_path, class_name, table_name):\n    \"\"\"Aggiunge __tablename__ a un modello specifico\"\"\"\n    \n    with open(file_path, 'r') as f:\n        content = f.read()\n    \n    # Pattern per trovare la definizione della classe\n    class_pattern = rf'class {class_name}\\(db\\.Model\\):\\s*\\n'\n    \n    # Cerca la classe\n    match = re.search(class_pattern, content)\n    if not match:\n        print(f\"  ❌ Classe {class_name} non trovata in {file_path}\")\n        return False\n    \n    # Verifica se __tablename__ esiste già\n    if f\"__tablename__ = '{table_name}'\" in content:\n        print(f\"  ✅ {class_name} ha già __tablename__ = '{table_name}'\")\n        return False\n    \n    # Trova la posizione dopo la definizione della classe\n    class_end = match.end()\n    \n    # Inserisci __tablename__ dopo la definizione della classe\n    tablename_line = f\"    __tablename__ = '{table_name}'\\n    \\n\"\n    \n    new_content = content[:class_end] + tablename_line + content[class_end:]\n    \n    # Scrivi il file aggiornato\n    with open(file_path, 'w') as f:\n        f.write(new_content)\n    \n    print(f\"  ✅ Aggiunto __tablename__ = '{table_name}' a {class_name}\")\n    return True\n\ndef fix_content_models():\n    \"\"\"Fix modelli in content.py\"\"\"\n    file_path = 'models_split/content.py'\n    print(f\"\\n📝 Fixing {file_path}...\")\n    \n    models_to_fix = [\n        ('News', 'news'),\n        ('Document', 'documents'),\n        ('Regulation', 'regulations'),\n        ('StartupResource', 'startup_resources')\n    ]\n    \n    changes = 0\n    for class_name, table_name in models_to_fix:\n        if add_tablename_to_model(file_path, class_name, table_name):\n            changes += 1\n    \n    return changes\n\ndef fix_business_models():\n    \"\"\"Fix modelli in business.py\"\"\"\n    file_path = 'models_split/business.py'\n    print(f\"\\n📝 Fixing {file_path}...\")\n    \n    models_to_fix = [\n        ('Product', 'products'),\n        ('Service', 'services'),\n        ('KPI', 'kpis')\n    ]\n    \n    changes = 0\n    for class_name, table_name in models_to_fix:\n        if add_tablename_to_model(file_path, class_name, table_name):\n            changes += 1\n    \n    return changes\n\ndef fix_system_models():\n    \"\"\"Fix modelli in system.py\"\"\"\n    file_path = 'models_split/system.py'\n    print(f\"\\n📝 Fixing {file_path}...\")\n    \n    models_to_fix = [\n        ('Notification', 'notifications')\n    ]\n    \n    changes = 0\n    for class_name, table_name in models_to_fix:\n        if add_tablename_to_model(file_path, class_name, table_name):\n            changes += 1\n    \n    return changes\n\ndef fix_crm_models():\n    \"\"\"Fix modelli in crm.py se necessario\"\"\"\n    file_path = 'models_split/crm.py'\n    print(f\"\\n📝 Checking {file_path}...\")\n    \n    # Prima verifichiamo se ci sono modelli senza __tablename__\n    with open(file_path, 'r') as f:\n        content = f.read()\n    \n    models_to_check = ['Contact', 'Proposal']\n    models_to_fix = []\n    \n    for model in models_to_check:\n        class_pattern = rf'class {model}\\(db\\.Model\\):'\n        if re.search(class_pattern, content):\n            # Verifica se ha __tablename__\n            if f'__tablename__' not in content[content.find(f'class {model}'):content.find(f'class {model}') + 500]:\n                table_name = model.lower() + 's'\n                models_to_fix.append((model, table_name))\n    \n    changes = 0\n    for class_name, table_name in models_to_fix:\n        if add_tablename_to_model(file_path, class_name, table_name):\n            changes += 1\n    \n    return changes\n\ndef check_projects_models():\n    \"\"\"Verifica se ci sono modelli senza __tablename__ in projects.py\"\"\"\n    file_path = 'models_split/projects.py'\n    print(f\"\\n📝 Checking {file_path}...\")\n    \n    with open(file_path, 'r') as f:\n        content = f.read()\n    \n    # Cerca modelli che potrebbero non avere __tablename__\n    models_to_check = ['TaskDependency', 'ProjectResource', 'ProjectKPI', 'ProjectExpense', 'ProjectKPITemplate', 'ProjectKPITarget']\n    models_to_fix = []\n    \n    for model in models_to_check:\n        class_pattern = rf'class {model}\\(db\\.Model\\):'\n        match = re.search(class_pattern, content)\n        if match:\n            # Estrai il contenuto della classe (primi 500 caratteri)\n            class_start = match.start()\n            class_content = content[class_start:class_start + 500]\n            \n            if '__tablename__' not in class_content:\n                # Converti CamelCase a snake_case plurale\n                table_name = re.sub(r'(?<!^)(?=[A-Z])', '_', model).lower()\n                if not table_name.endswith('s'):\n                    table_name += 's'\n                models_to_fix.append((model, table_name))\n    \n    changes = 0\n    for class_name, table_name in models_to_fix:\n        if add_tablename_to_model(file_path, class_name, table_name):\n            changes += 1\n    \n    return changes\n\ndef main():\n    \"\"\"Main function\"\"\"\n    print(\"🔧 FIXING MISSING __tablename__ DEFINITIONS\")\n    print(\"=\" * 50)\n    \n    total_changes = 0\n    \n    # Fix tutti i modelli problematici\n    total_changes += fix_content_models()\n    total_changes += fix_business_models() \n    total_changes += fix_system_models()\n    total_changes += fix_crm_models()\n    total_changes += check_projects_models()\n    \n    print(f\"\\n📊 RIEPILOGO:\")\n    print(f\"  - Modelli modificati: {total_changes}\")\n    \n    if total_changes > 0:\n        print(f\"\\n✅ Fix completato! {total_changes} modelli aggiornati.\")\n        print(\"\\n🚨 IMPORTANTE:\")\n        print(\"  1. Riavvia l'applicazione per ricaricare i modelli\")\n        print(\"  2. Le tabelle duplicate nel database devono essere rimosse manualmente\")\n        print(\"  3. Esegui una migrazione per sincronizzare il database\")\n    else:\n        print(\"\\n✅ Tutti i modelli hanno già __tablename__ definito!\")\n\nif __name__ == '__main__':\n    main()\n"}