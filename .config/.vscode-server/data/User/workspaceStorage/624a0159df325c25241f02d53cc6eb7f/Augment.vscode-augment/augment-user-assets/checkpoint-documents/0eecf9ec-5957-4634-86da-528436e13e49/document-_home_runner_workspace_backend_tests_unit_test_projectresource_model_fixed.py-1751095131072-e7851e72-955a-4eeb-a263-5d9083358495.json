{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_projectresource_model_fixed.py"}, "modifiedCode": "\"\"\"\nUnit tests for ProjectResource model.\nTests business logic, validations, and model functionality.\nFixed version with proper session handling.\n\"\"\"\n\nimport pytest\nfrom datetime import datetime, date\nfrom models import ProjectResource, User, Project\nfrom extensions import db\n\n\nclass TestProjectResourceModel:\n    \"\"\"Test suite for ProjectResource model unit tests\"\"\"\n\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        \"\"\"Setup test data for each test method\"\"\"\n        self.app = app\n        self.user = test_user\n\n    def test_projectresource_creation_basic(self):\n        \"\"\"Test basic project resource creation\"\"\"\n        # Create test project for this test\n        project = Project(\n            name='Test Project Resource Basic',\n            description='Project for resource testing',\n            status='active'\n        )\n        db.session.add(project)\n        db.session.commit()\n        \n        resource = ProjectResource(\n            project_id=project.id,\n            user_id=self.user.id,\n            allocation_percentage=100,\n            role='Developer'\n        )\n        \n        db.session.add(resource)\n        db.session.commit()\n        \n        assert resource.id is not None\n        assert resource.project_id == project.id\n        assert resource.user_id == self.user.id\n        assert resource.allocation_percentage == 100\n        assert resource.role == 'Developer'\n\n    def test_projectresource_relationships(self):\n        \"\"\"Test relationships with Project and User models\"\"\"\n        project = Project(\n            name='Test Project Relationships',\n            description='Relationships test',\n            status='active'\n        )\n        db.session.add(project)\n        db.session.commit()\n        \n        resource = ProjectResource(\n            project_id=project.id,\n            user_id=self.user.id,\n            role='Team Lead'\n        )\n        \n        db.session.add(resource)\n        db.session.commit()\n        \n        # Test forward relationships\n        assert resource.project is not None\n        assert resource.project.id == project.id\n        assert resource.user is not None\n        assert resource.user.id == self.user.id\n\n    def test_projectresource_allocation_percentage(self):\n        \"\"\"Test allocation percentage functionality\"\"\"\n        project = Project(\n            name='Test Project Allocation',\n            description='Allocation test',\n            status='active'\n        )\n        db.session.add(project)\n        db.session.commit()\n        \n        allocations = [25, 50, 75, 100]\n        \n        resources = []\n        for i, allocation in enumerate(allocations):\n            resource = ProjectResource(\n                project_id=project.id,\n                user_id=self.user.id,\n                allocation_percentage=allocation,\n                role=f'Role_{i}'\n            )\n            resources.append(resource)\n        \n        db.session.add_all(resources)\n        db.session.commit()\n        \n        for resource, expected_allocation in zip(resources, allocations):\n            assert resource.allocation_percentage == expected_allocation\n            assert 0 <= resource.allocation_percentage <= 100\n\n    def test_projectresource_role_types(self):\n        \"\"\"Test different role types\"\"\"\n        project = Project(\n            name='Test Project Roles',\n            description='Roles test',\n            status='active'\n        )\n        db.session.add(project)\n        db.session.commit()\n        \n        roles = [\n            'Project Manager',\n            'Senior Developer',\n            'Junior Developer',\n            'Designer',\n            'QA Engineer'\n        ]\n        \n        resources = []\n        for i, role in enumerate(roles):\n            resource = ProjectResource(\n                project_id=project.id,\n                user_id=self.user.id,\n                role=role,\n                allocation_percentage=50\n            )\n            resources.append(resource)\n        \n        db.session.add_all(resources)\n        db.session.commit()\n        \n        for resource, expected_role in zip(resources, roles):\n            assert resource.role == expected_role\n\n    def test_projectresource_default_allocation(self):\n        \"\"\"Test default allocation percentage\"\"\"\n        project = Project(\n            name='Test Project Default',\n            description='Default test',\n            status='active'\n        )\n        db.session.add(project)\n        db.session.commit()\n        \n        resource = ProjectResource(\n            project_id=project.id,\n            user_id=self.user.id,\n            role='Developer'\n        )\n        \n        db.session.add(resource)\n        db.session.commit()\n        \n        assert resource.allocation_percentage == 100  # Default value\n\n    def test_projectresource_query_by_project(self):\n        \"\"\"Test querying resources by project\"\"\"\n        project = Project(\n            name='Test Project Query',\n            description='Query test',\n            status='active'\n        )\n        db.session.add(project)\n        db.session.commit()\n        \n        resource = ProjectResource(\n            project_id=project.id,\n            user_id=self.user.id,\n            role='Tester'\n        )\n        \n        db.session.add(resource)\n        db.session.commit()\n        \n        # Query by project\n        project_resources = ProjectResource.query.filter_by(project_id=project.id).all()\n        assert len(project_resources) >= 1\n        assert resource in project_resources\n\n    def test_projectresource_query_by_user(self):\n        \"\"\"Test querying resources by user\"\"\"\n        project = Project(\n            name='Test Project User Query',\n            description='User query test',\n            status='active'\n        )\n        db.session.add(project)\n        db.session.commit()\n        \n        resource = ProjectResource(\n            project_id=project.id,\n            user_id=self.user.id,\n            role='Analyst'\n        )\n        \n        db.session.add(resource)\n        db.session.commit()\n        \n        # Query by user\n        user_resources = ProjectResource.query.filter_by(user_id=self.user.id).all()\n        assert len(user_resources) >= 1\n        assert resource in user_resources\n\n    def test_projectresource_update_operations(self):\n        \"\"\"Test resource update operations\"\"\"\n        project = Project(\n            name='Test Project Update',\n            description='Update test',\n            status='active'\n        )\n        db.session.add(project)\n        db.session.commit()\n        \n        resource = ProjectResource(\n            project_id=project.id,\n            user_id=self.user.id,\n            allocation_percentage=50,\n            role='Junior Developer'\n        )\n        \n        db.session.add(resource)\n        db.session.commit()\n        \n        # Update resource\n        resource.allocation_percentage = 75\n        resource.role = 'Senior Developer'\n        \n        db.session.commit()\n        \n        updated_resource = ProjectResource.query.get(resource.id)\n        assert updated_resource.allocation_percentage == 75\n        assert updated_resource.role == 'Senior Developer'\n\n    def test_projectresource_deletion(self):\n        \"\"\"Test resource deletion\"\"\"\n        project = Project(\n            name='Test Project Delete',\n            description='Delete test',\n            status='active'\n        )\n        db.session.add(project)\n        db.session.commit()\n        \n        resource = ProjectResource(\n            project_id=project.id,\n            user_id=self.user.id,\n            role='To Delete'\n        )\n        \n        db.session.add(resource)\n        db.session.commit()\n        resource_id = resource.id\n        \n        db.session.delete(resource)\n        db.session.commit()\n        \n        deleted_resource = ProjectResource.query.get(resource_id)\n        assert deleted_resource is None\n\n    def test_projectresource_multiple_projects(self):\n        \"\"\"Test user assigned to multiple projects\"\"\"\n        # Create two projects\n        project1 = Project(\n            name='First Project',\n            description='First project',\n            status='active'\n        )\n        project2 = Project(\n            name='Second Project',\n            description='Second project',\n            status='active'\n        )\n        db.session.add_all([project1, project2])\n        db.session.commit()\n        \n        # Assign user to both projects\n        resource1 = ProjectResource(\n            project_id=project1.id,\n            user_id=self.user.id,\n            allocation_percentage=60,\n            role='Developer'\n        )\n        \n        resource2 = ProjectResource(\n            project_id=project2.id,\n            user_id=self.user.id,\n            allocation_percentage=40,\n            role='Consultant'\n        )\n        \n        db.session.add_all([resource1, resource2])\n        db.session.commit()\n        \n        # Verify user is assigned to both projects\n        user_projects = ProjectResource.query.filter_by(user_id=self.user.id).all()\n        assert len(user_projects) >= 2\n        \n        project_ids = [r.project_id for r in user_projects]\n        assert project1.id in project_ids\n        assert project2.id in project_ids\n\n    def test_projectresource_allocation_validation(self):\n        \"\"\"Test allocation percentage validation\"\"\"\n        project = Project(\n            name='Test Project Validation',\n            description='Validation test',\n            status='active'\n        )\n        db.session.add(project)\n        db.session.commit()\n        \n        # Test valid allocations\n        valid_allocations = [0, 25, 50, 75, 100]\n        \n        for allocation in valid_allocations:\n            resource = ProjectResource(\n                project_id=project.id,\n                user_id=self.user.id,\n                allocation_percentage=allocation,\n                role=f'Valid_{allocation}'\n            )\n            \n            db.session.add(resource)\n            db.session.commit()\n            \n            assert resource.allocation_percentage == allocation\n\n    def test_projectresource_role_length(self):\n        \"\"\"Test role field length\"\"\"\n        project = Project(\n            name='Test Project Role Length',\n            description='Role length test',\n            status='active'\n        )\n        db.session.add(project)\n        db.session.commit()\n        \n        long_role = 'Very Long Role Name That Tests Field Length'\n        \n        resource = ProjectResource(\n            project_id=project.id,\n            user_id=self.user.id,\n            role=long_role\n        )\n        \n        db.session.add(resource)\n        db.session.commit()\n        \n        assert resource.role == long_role\n        assert len(resource.role) <= 50  # Based on VARCHAR(50) constraint\n"}