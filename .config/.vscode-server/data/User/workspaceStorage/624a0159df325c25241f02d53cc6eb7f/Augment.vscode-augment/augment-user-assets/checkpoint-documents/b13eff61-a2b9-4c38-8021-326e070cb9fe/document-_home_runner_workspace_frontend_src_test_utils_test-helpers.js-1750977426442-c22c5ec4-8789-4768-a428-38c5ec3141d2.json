{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/utils/test-helpers.js"}, "modifiedCode": "/**\n * Test utilities and helpers for Vue component testing\n */\nimport { mount, shallowMount } from '@vue/test-utils'\nimport { createPinia } from 'pinia'\nimport { createRouter, createWebHistory } from 'vue-router'\n\n// Mock router for testing\nexport const createMockRouter = (routes = []) => {\n  return createRouter({\n    history: createWebHistory(),\n    routes: [\n      { path: '/', component: { template: '<div>Home</div>' } },\n      { path: '/login', component: { template: '<div>Login</div>' } },\n      { path: '/dashboard', component: { template: '<div>Dashboard</div>' } },\n      ...routes\n    ]\n  })\n}\n\n// Mock store factory\nexport const createMockStore = () => {\n  return createPinia()\n}\n\n// Default mount options\nexport const defaultMountOptions = {\n  global: {\n    plugins: [createMockStore(), createMockRouter()],\n    stubs: {\n      'router-link': true,\n      'router-view': true,\n      'HeroIcon': true,\n      'transition': false,\n      'teleport': true\n    },\n    mocks: {\n      $t: (key) => key, // Mock i18n\n      $route: {\n        path: '/',\n        params: {},\n        query: {},\n        meta: {}\n      },\n      $router: {\n        push: vi.fn(),\n        replace: vi.fn(),\n        go: vi.fn(),\n        back: vi.fn()\n      }\n    }\n  }\n}\n\n// Enhanced mount helper\nexport const mountComponent = (component, options = {}) => {\n  const mergedOptions = {\n    ...defaultMountOptions,\n    ...options,\n    global: {\n      ...defaultMountOptions.global,\n      ...options.global\n    }\n  }\n  \n  return mount(component, mergedOptions)\n}\n\n// Shallow mount helper\nexport const shallowMountComponent = (component, options = {}) => {\n  const mergedOptions = {\n    ...defaultMountOptions,\n    ...options,\n    global: {\n      ...defaultMountOptions.global,\n      ...options.global\n    }\n  }\n  \n  return shallowMount(component, mergedOptions)\n}\n\n// Mock API responses\nexport const mockApiResponse = (data, status = 200) => {\n  return Promise.resolve({\n    ok: status >= 200 && status < 300,\n    status,\n    json: () => Promise.resolve(data),\n    text: () => Promise.resolve(JSON.stringify(data))\n  })\n}\n\n// Mock API error\nexport const mockApiError = (message = 'API Error', status = 500) => {\n  return Promise.reject({\n    ok: false,\n    status,\n    message,\n    json: () => Promise.resolve({ error: message })\n  })\n}\n\n// Wait for next tick and DOM updates\nexport const waitForUpdate = async (wrapper) => {\n  await wrapper.vm.$nextTick()\n  await new Promise(resolve => setTimeout(resolve, 0))\n}\n\n// Mock user data\nexport const mockUser = {\n  id: 1,\n  username: 'testuser',\n  email: '<EMAIL>',\n  first_name: 'Test',\n  last_name: 'User',\n  role: 'employee',\n  department_id: 1,\n  is_active: true\n}\n\n// Mock admin user\nexport const mockAdminUser = {\n  ...mockUser,\n  id: 2,\n  username: 'admin',\n  email: '<EMAIL>',\n  role: 'admin'\n}\n\n// Mock project data\nexport const mockProject = {\n  id: 1,\n  name: 'Test Project',\n  description: 'A test project',\n  status: 'active',\n  start_date: '2025-01-01',\n  end_date: '2025-12-31',\n  budget: 10000,\n  client_id: 1,\n  manager_id: 1\n}\n\n// Mock client data\nexport const mockClient = {\n  id: 1,\n  name: 'Test Client',\n  email: '<EMAIL>',\n  phone: '+1234567890',\n  address: '123 Test St',\n  status: 'active'\n}\n\n// Form validation helpers\nexport const fillForm = async (wrapper, formData) => {\n  for (const [field, value] of Object.entries(formData)) {\n    const input = wrapper.find(`[data-testid=\"${field}\"]`)\n    if (input.exists()) {\n      await input.setValue(value)\n    }\n  }\n  await waitForUpdate(wrapper)\n}\n\n// Event helpers\nexport const triggerEvent = async (wrapper, selector, event, payload = {}) => {\n  const element = wrapper.find(selector)\n  if (element.exists()) {\n    await element.trigger(event, payload)\n    await waitForUpdate(wrapper)\n  }\n}\n\n// Local storage mock helpers\nexport const mockLocalStorage = () => {\n  const store = {}\n  return {\n    getItem: vi.fn((key) => store[key] || null),\n    setItem: vi.fn((key, value) => { store[key] = value }),\n    removeItem: vi.fn((key) => { delete store[key] }),\n    clear: vi.fn(() => { Object.keys(store).forEach(key => delete store[key]) })\n  }\n}\n\n// Console helpers for testing\nexport const suppressConsoleErrors = () => {\n  const originalError = console.error\n  console.error = vi.fn()\n  return () => { console.error = originalError }\n}\n\nexport const suppressConsoleWarnings = () => {\n  const originalWarn = console.warn\n  console.warn = vi.fn()\n  return () => { console.warn = originalWarn }\n}\n"}