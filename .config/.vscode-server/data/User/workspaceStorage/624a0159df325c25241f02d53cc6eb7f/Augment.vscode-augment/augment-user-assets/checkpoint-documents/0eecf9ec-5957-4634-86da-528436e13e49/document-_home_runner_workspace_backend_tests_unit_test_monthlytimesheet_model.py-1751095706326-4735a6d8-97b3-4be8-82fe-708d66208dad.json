{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_monthlytimesheet_model.py"}, "modifiedCode": "\"\"\"\nUnit tests for MonthlyTimesheet model.\nTests business logic, validations, and model functionality.\n\"\"\"\n\nimport pytest\nfrom datetime import datetime, date\nfrom models import MonthlyTimesheet, User\nfrom extensions import db\n\n\nclass TestMonthlyTimesheetModel:\n    \"\"\"Test suite for MonthlyTimesheet model unit tests\"\"\"\n\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        \"\"\"Setup test data for each test method\"\"\"\n        self.app = app\n        self.user = test_user\n\n    def test_monthlytimesheet_creation_basic(self):\n        \"\"\"Test basic monthly timesheet creation\"\"\"\n        timesheet = MonthlyTimesheet(\n            user_id=self.user.id,\n            year=2024,\n            month=12,\n            status='draft'\n        )\n        \n        db.session.add(timesheet)\n        db.session.commit()\n        \n        assert timesheet.id is not None\n        assert timesheet.user_id == self.user.id\n        assert timesheet.year == 2024\n        assert timesheet.month == 12\n        assert timesheet.status == 'draft'\n\n    def test_monthlytimesheet_creation_complete(self):\n        \"\"\"Test monthly timesheet creation with all fields\"\"\"\n        # Create approver user\n        approver = User(username='approver', email='<EMAIL>')\n        db.session.add(approver)\n        db.session.commit()\n        \n        timesheet = MonthlyTimesheet(\n            user_id=self.user.id,\n            year=2024,\n            month=11,\n            status='approved',\n            submission_date=datetime(2024, 12, 1, 10, 0, 0),\n            approval_date=datetime(2024, 12, 2, 14, 30, 0),\n            approved_by=approver.id,\n            rejection_reason=None\n        )\n        \n        db.session.add(timesheet)\n        db.session.commit()\n        \n        assert timesheet.year == 2024\n        assert timesheet.month == 11\n        assert timesheet.status == 'approved'\n        assert timesheet.submission_date == datetime(2024, 12, 1, 10, 0, 0)\n        assert timesheet.approval_date == datetime(2024, 12, 2, 14, 30, 0)\n        assert timesheet.approved_by == approver.id\n\n    def test_monthlytimesheet_relationships(self):\n        \"\"\"Test relationships with User model\"\"\"\n        timesheet = MonthlyTimesheet(\n            user_id=self.user.id,\n            year=2024,\n            month=10,\n            status='submitted'\n        )\n        \n        db.session.add(timesheet)\n        db.session.commit()\n        \n        # Test forward relationship\n        assert timesheet.user is not None\n        assert timesheet.user.id == self.user.id\n\n    def test_monthlytimesheet_status_types(self):\n        \"\"\"Test different status types\"\"\"\n        statuses = ['draft', 'submitted', 'approved', 'rejected']\n        \n        timesheets = []\n        for i, status in enumerate(statuses):\n            timesheet = MonthlyTimesheet(\n                user_id=self.user.id,\n                year=2024,\n                month=i + 1,  # Different months to avoid conflicts\n                status=status\n            )\n            timesheets.append(timesheet)\n        \n        db.session.add_all(timesheets)\n        db.session.commit()\n        \n        for timesheet, expected_status in zip(timesheets, statuses):\n            assert timesheet.status == expected_status\n\n    def test_monthlytimesheet_year_month_validation(self):\n        \"\"\"Test year and month field validation\"\"\"\n        # Test valid year/month combinations\n        valid_combinations = [\n            (2024, 1), (2024, 6), (2024, 12),\n            (2023, 1), (2025, 12)\n        ]\n        \n        timesheets = []\n        for year, month in valid_combinations:\n            timesheet = MonthlyTimesheet(\n                user_id=self.user.id,\n                year=year,\n                month=month,\n                status='draft'\n            )\n            timesheets.append(timesheet)\n        \n        db.session.add_all(timesheets)\n        db.session.commit()\n        \n        for timesheet, (expected_year, expected_month) in zip(timesheets, valid_combinations):\n            assert timesheet.year == expected_year\n            assert timesheet.month == expected_month\n            assert 1 <= timesheet.month <= 12\n            assert timesheet.year >= 2020  # Reasonable year range\n\n    def test_monthlytimesheet_approval_workflow(self):\n        \"\"\"Test approval workflow functionality\"\"\"\n        approver = User(username='manager', email='<EMAIL>')\n        db.session.add(approver)\n        db.session.commit()\n        \n        # Draft timesheet\n        timesheet = MonthlyTimesheet(\n            user_id=self.user.id,\n            year=2024,\n            month=9,\n            status='draft'\n        )\n        \n        db.session.add(timesheet)\n        db.session.commit()\n        \n        # Submit timesheet\n        timesheet.status = 'submitted'\n        timesheet.submission_date = datetime.now()\n        db.session.commit()\n        \n        assert timesheet.status == 'submitted'\n        assert timesheet.submission_date is not None\n        \n        # Approve timesheet\n        timesheet.status = 'approved'\n        timesheet.approval_date = datetime.now()\n        timesheet.approved_by = approver.id\n        db.session.commit()\n        \n        assert timesheet.status == 'approved'\n        assert timesheet.approval_date is not None\n        assert timesheet.approved_by == approver.id\n\n    def test_monthlytimesheet_rejection_workflow(self):\n        \"\"\"Test rejection workflow functionality\"\"\"\n        timesheet = MonthlyTimesheet(\n            user_id=self.user.id,\n            year=2024,\n            month=8,\n            status='submitted',\n            submission_date=datetime.now()\n        )\n        \n        db.session.add(timesheet)\n        db.session.commit()\n        \n        # Reject timesheet\n        rejection_reason = 'Missing project details for several entries'\n        timesheet.status = 'rejected'\n        timesheet.rejection_reason = rejection_reason\n        \n        db.session.commit()\n        \n        assert timesheet.status == 'rejected'\n        assert timesheet.rejection_reason == rejection_reason\n\n    def test_monthlytimesheet_query_by_user(self):\n        \"\"\"Test querying timesheets by user\"\"\"\n        timesheet = MonthlyTimesheet(\n            user_id=self.user.id,\n            year=2024,\n            month=7,\n            status='draft'\n        )\n        \n        db.session.add(timesheet)\n        db.session.commit()\n        \n        # Query by user\n        user_timesheets = MonthlyTimesheet.query.filter_by(user_id=self.user.id).all()\n        assert len(user_timesheets) >= 1\n        assert timesheet in user_timesheets\n\n    def test_monthlytimesheet_query_by_period(self):\n        \"\"\"Test querying timesheets by year/month\"\"\"\n        timesheet = MonthlyTimesheet(\n            user_id=self.user.id,\n            year=2024,\n            month=6,\n            status='approved'\n        )\n        \n        db.session.add(timesheet)\n        db.session.commit()\n        \n        # Query by year and month\n        period_timesheets = MonthlyTimesheet.query.filter_by(\n            year=2024, month=6\n        ).all()\n        assert len(period_timesheets) >= 1\n        assert timesheet in period_timesheets\n\n    def test_monthlytimesheet_query_by_status(self):\n        \"\"\"Test querying timesheets by status\"\"\"\n        timesheet = MonthlyTimesheet(\n            user_id=self.user.id,\n            year=2024,\n            month=5,\n            status='submitted'\n        )\n        \n        db.session.add(timesheet)\n        db.session.commit()\n        \n        # Query by status\n        submitted_timesheets = MonthlyTimesheet.query.filter_by(status='submitted').all()\n        assert len(submitted_timesheets) >= 1\n        assert timesheet in submitted_timesheets\n\n    def test_monthlytimesheet_timestamps(self):\n        \"\"\"Test automatic timestamp handling\"\"\"\n        timesheet = MonthlyTimesheet(\n            user_id=self.user.id,\n            year=2024,\n            month=4,\n            status='draft'\n        )\n        \n        db.session.add(timesheet)\n        db.session.commit()\n        \n        assert timesheet.created_at is not None\n        assert timesheet.updated_at is not None\n        assert isinstance(timesheet.created_at, datetime)\n        assert isinstance(timesheet.updated_at, datetime)\n\n    def test_monthlytimesheet_update_operations(self):\n        \"\"\"Test timesheet update operations\"\"\"\n        timesheet = MonthlyTimesheet(\n            user_id=self.user.id,\n            year=2024,\n            month=3,\n            status='draft'\n        )\n        \n        db.session.add(timesheet)\n        db.session.commit()\n        \n        # Update timesheet\n        timesheet.status = 'submitted'\n        timesheet.submission_date = datetime.now()\n        \n        db.session.commit()\n        \n        updated_timesheet = MonthlyTimesheet.query.get(timesheet.id)\n        assert updated_timesheet.status == 'submitted'\n        assert updated_timesheet.submission_date is not None\n\n    def test_monthlytimesheet_deletion(self):\n        \"\"\"Test timesheet deletion\"\"\"\n        timesheet = MonthlyTimesheet(\n            user_id=self.user.id,\n            year=2024,\n            month=2,\n            status='draft'\n        )\n        \n        db.session.add(timesheet)\n        db.session.commit()\n        timesheet_id = timesheet.id\n        \n        db.session.delete(timesheet)\n        db.session.commit()\n        \n        deleted_timesheet = MonthlyTimesheet.query.get(timesheet_id)\n        assert deleted_timesheet is None\n\n    def test_monthlytimesheet_unique_constraints(self):\n        \"\"\"Test unique constraints if any\"\"\"\n        # Test that multiple timesheets for same user/period might be allowed\n        # or test unique constraint if it exists\n        timesheet1 = MonthlyTimesheet(\n            user_id=self.user.id,\n            year=2024,\n            month=1,\n            status='draft'\n        )\n        \n        timesheet2 = MonthlyTimesheet(\n            user_id=self.user.id,\n            year=2023,\n            month=1,  # Different year, same month\n            status='approved'\n        )\n        \n        db.session.add_all([timesheet1, timesheet2])\n        db.session.commit()\n        \n        assert timesheet1.id != timesheet2.id\n        assert timesheet1.year != timesheet2.year\n\n    def test_monthlytimesheet_rejection_reason_length(self):\n        \"\"\"Test rejection reason field for long text\"\"\"\n        long_reason = \"\"\"\n        This timesheet is rejected due to multiple issues:\n        1. Missing project codes for several entries\n        2. Overtime hours not properly justified\n        3. Some entries lack detailed descriptions\n        4. Time allocation doesn't match project requirements\n        Please review and resubmit with corrections.\n        \"\"\"\n        \n        timesheet = MonthlyTimesheet(\n            user_id=self.user.id,\n            year=2023,\n            month=12,\n            status='rejected',\n            rejection_reason=long_reason.strip()\n        )\n        \n        db.session.add(timesheet)\n        db.session.commit()\n        \n        assert timesheet.rejection_reason == long_reason.strip()\n        assert len(timesheet.rejection_reason) > 100  # Verify it can handle long text\n"}