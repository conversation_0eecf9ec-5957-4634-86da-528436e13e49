{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_fundingapplication_model.py"}, "modifiedCode": "\"\"\"Unit tests for FundingApplication model.\"\"\"\nimport pytest\nfrom models import FundingApplication, FundingOpportunity, User\nfrom extensions import db\n\nclass TestFundingApplicationModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n        \n        self.opportunity = FundingOpportunity.query.first()\n        if not self.opportunity:\n            self.opportunity = FundingOpportunity(title='Test Funding', description='Test')\n            db.session.add(self.opportunity)\n            db.session.commit()\n\n    def test_fundingapplication_creation_basic(self):\n        application = FundingApplication(\n            opportunity_id=self.opportunity.id,\n            applicant_id=self.user.id,\n            status='submitted',\n            requested_amount=50000.0\n        )\n        db.session.add(application)\n        db.session.commit()\n        \n        assert application.id is not None\n        assert application.opportunity_id == self.opportunity.id\n        assert application.status == 'submitted'\n\n    def test_fundingapplication_deletion(self):\n        application = FundingApplication(opportunity_id=self.opportunity.id, applicant_id=self.user.id)\n        db.session.add(application)\n        db.session.commit()\n        app_id = application.id\n        \n        db.session.delete(application)\n        db.session.commit()\n        \n        deleted = FundingApplication.query.get(app_id)\n        assert deleted is None\n"}