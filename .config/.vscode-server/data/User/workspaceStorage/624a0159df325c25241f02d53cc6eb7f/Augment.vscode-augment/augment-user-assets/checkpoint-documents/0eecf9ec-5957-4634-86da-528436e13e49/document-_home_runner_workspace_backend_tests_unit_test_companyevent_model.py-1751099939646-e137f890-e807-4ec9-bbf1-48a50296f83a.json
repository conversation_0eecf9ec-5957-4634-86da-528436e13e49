{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_companyevent_model.py"}, "modifiedCode": "\"\"\"Unit tests for CompanyEvent model.\"\"\"\nimport pytest\nfrom datetime import datetime, date\nfrom models import CompanyEvent, User\nfrom extensions import db\n\nclass TestCompanyEventModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n\n    def test_companyevent_creation_basic(self):\n        event = CompanyEvent(\n            title='Team Building',\n            description='Annual team building event',\n            start_date=datetime(2024, 6, 15, 10, 0),\n            end_date=datetime(2024, 6, 15, 18, 0),\n            created_by=self.user.id\n        )\n        db.session.add(event)\n        db.session.commit()\n        \n        assert event.id is not None\n        assert event.title == 'Team Building'\n        assert event.description == 'Annual team building event'\n        assert event.created_by == self.user.id\n\n    def test_companyevent_location(self):\n        event = CompanyEvent(\n            title='Conference',\n            description='Tech conference',\n            start_date=datetime(2024, 7, 1, 9, 0),\n            end_date=datetime(2024, 7, 1, 17, 0),\n            location='Convention Center',\n            created_by=self.user.id\n        )\n        db.session.add(event)\n        db.session.commit()\n        \n        assert event.location == 'Convention Center'\n\n    def test_companyevent_capacity(self):\n        event = CompanyEvent(\n            title='Workshop',\n            description='Technical workshop',\n            start_date=datetime(2024, 8, 1, 14, 0),\n            end_date=datetime(2024, 8, 1, 16, 0),\n            max_participants=20,\n            created_by=self.user.id\n        )\n        db.session.add(event)\n        db.session.commit()\n        \n        assert event.max_participants == 20\n\n    def test_companyevent_deletion(self):\n        event = CompanyEvent(\n            title='To Delete',\n            description='This will be deleted',\n            start_date=datetime(2024, 9, 1, 10, 0),\n            end_date=datetime(2024, 9, 1, 12, 0),\n            created_by=self.user.id\n        )\n        db.session.add(event)\n        db.session.commit()\n        event_id = event.id\n        \n        db.session.delete(event)\n        db.session.commit()\n        \n        deleted = CompanyEvent.query.get(event_id)\n        assert deleted is None\n"}