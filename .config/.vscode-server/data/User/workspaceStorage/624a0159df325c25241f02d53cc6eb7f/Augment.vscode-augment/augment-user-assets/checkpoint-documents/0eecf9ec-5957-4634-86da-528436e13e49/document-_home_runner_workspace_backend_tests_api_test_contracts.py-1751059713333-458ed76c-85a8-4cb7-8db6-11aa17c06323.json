{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/api/test_contracts.py"}, "modifiedCode": "\"\"\"\nTest suite for Contracts API endpoints.\nTests CRUD operations, validation, and business logic for contract management.\n\"\"\"\n\nimport pytest\nfrom datetime import datetime, date, timedelta\nfrom models import Contract, Client, User\nfrom extensions import db\n\n\nclass TestContractsAPI:\n    \"\"\"Test suite for Contracts API endpoints\"\"\"\n\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        \"\"\"Setup test data for each test method\"\"\"\n        self.app = app\n        self.user = test_user\n        \n        # Create test client\n        self.test_client = Client(name='Test Client', status='active')\n        db.session.add(self.test_client)\n        db.session.commit()\n        \n        # Create test contract data\n        self.contract_data = {\n            'client_id': self.test_client.id,\n            'contract_number': 'CTR-2025-001',\n            'title': 'Test Contract',\n            'description': 'A comprehensive test contract for API testing',\n            'contract_type': 'hourly',\n            'hourly_rate': 80.0,\n            'budget_hours': 100.0,\n            'budget_amount': 8000.0,\n            'start_date': date.today().isoformat(),\n            'end_date': (date.today() + timedelta(days=90)).isoformat(),\n            'status': 'draft'\n        }\n\n    def test_get_contracts_success(self, client):\n        \"\"\"Test successful retrieval of contracts list\"\"\"\n        # Create test contracts\n        contract1 = Contract(\n            client_id=self.test_client.id,\n            contract_number='CTR-001',\n            title='Contract 1',\n            contract_type='hourly',\n            start_date=date.today(),\n            status='active'\n        )\n        contract2 = Contract(\n            client_id=self.test_client.id,\n            contract_number='CTR-002',\n            title='Contract 2',\n            contract_type='fixed',\n            start_date=date.today(),\n            status='draft'\n        )\n        db.session.add_all([contract1, contract2])\n        db.session.commit()\n\n        response = client.get('/api/contracts')\n        \n        assert response.status_code in [200, 401]\n        if response.status_code == 200:\n            data = response.get_json()\n            assert 'contracts' in str(data).lower() or 'data' in data\n\n    def test_create_contract_success(self, client):\n        \"\"\"Test successful contract creation\"\"\"\n        response = client.post('/api/contracts', json=self.contract_data)\n        \n        assert response.status_code in [201, 200, 401, 403]\n        if response.status_code in [200, 201]:\n            # Verify contract was created\n            created_contract = Contract.query.filter_by(contract_number='CTR-2025-001').first()\n            if created_contract:\n                assert created_contract.title == 'Test Contract'\n                assert created_contract.client_id == self.test_client.id\n\n    def test_create_contract_validation_error(self, client):\n        \"\"\"Test contract creation with missing required fields\"\"\"\n        invalid_data = {'description': 'Missing required fields'}\n        \n        response = client.post('/api/contracts', json=invalid_data)\n        \n        assert response.status_code in [400, 422, 401, 403]\n\n    def test_update_contract_success(self, client):\n        \"\"\"Test successful contract update\"\"\"\n        test_contract = Contract(\n            client_id=self.test_client.id,\n            contract_number='CTR-UPDATE',\n            title='Original Title',\n            contract_type='hourly',\n            start_date=date.today(),\n            status='draft'\n        )\n        db.session.add(test_contract)\n        db.session.commit()\n\n        update_data = {\n            'title': 'Updated Contract Title',\n            'hourly_rate': 90.0,\n            'status': 'active'\n        }\n        \n        response = client.put(f'/api/contracts/{test_contract.id}', json=update_data)\n        \n        assert response.status_code in [200, 401, 403, 404]\n\n    def test_delete_contract_success(self, client):\n        \"\"\"Test successful contract deletion\"\"\"\n        test_contract = Contract(\n            client_id=self.test_client.id,\n            contract_number='CTR-DELETE',\n            title='To Delete',\n            contract_type='fixed',\n            start_date=date.today(),\n            status='draft'\n        )\n        db.session.add(test_contract)\n        db.session.commit()\n        contract_id = test_contract.id\n\n        response = client.delete(f'/api/contracts/{contract_id}')\n        \n        assert response.status_code in [200, 401, 403, 404]\n\n    def test_contract_types_validation(self, client):\n        \"\"\"Test different contract types\"\"\"\n        contract_types = ['hourly', 'fixed', 'milestone', 'subscription', 'retainer']\n        \n        for contract_type in contract_types:\n            test_data = self.contract_data.copy()\n            test_data['contract_type'] = contract_type\n            test_data['contract_number'] = f'CTR-{contract_type.upper()}'\n            \n            response = client.post('/api/contracts', json=test_data)\n            assert response.status_code in [201, 200, 401, 403, 400, 422]\n\n    def test_contract_status_workflow(self, client):\n        \"\"\"Test contract status transitions\"\"\"\n        test_contract = Contract(\n            client_id=self.test_client.id,\n            contract_number='CTR-STATUS',\n            title='Status Test',\n            contract_type='hourly',\n            start_date=date.today(),\n            status='draft'\n        )\n        db.session.add(test_contract)\n        db.session.commit()\n\n        # Test status transitions\n        status_transitions = ['active', 'completed', 'cancelled']\n        \n        for status in status_transitions:\n            response = client.put(\n                f'/api/contracts/{test_contract.id}', \n                json={'status': status}\n            )\n            assert response.status_code in [200, 401, 403, 404, 400]\n\n    def test_contract_search_and_filters(self, client):\n        \"\"\"Test contract search and filtering\"\"\"\n        # Create test contracts with different types and statuses\n        contract1 = Contract(\n            client_id=self.test_client.id,\n            contract_number='CTR-HOURLY',\n            title='Hourly Contract',\n            contract_type='hourly',\n            start_date=date.today(),\n            status='active'\n        )\n        contract2 = Contract(\n            client_id=self.test_client.id,\n            contract_number='CTR-FIXED',\n            title='Fixed Contract',\n            contract_type='fixed',\n            start_date=date.today(),\n            status='draft'\n        )\n        db.session.add_all([contract1, contract2])\n        db.session.commit()\n\n        # Test status filter\n        response = client.get('/api/contracts?status=active')\n        assert response.status_code in [200, 401]\n        \n        # Test contract type filter\n        response = client.get('/api/contracts?contract_type=hourly')\n        assert response.status_code in [200, 401]\n        \n        # Test client filter\n        response = client.get(f'/api/contracts?client_id={self.test_client.id}')\n        assert response.status_code in [200, 401]\n\n    def test_contract_date_validation(self, client):\n        \"\"\"Test contract date validation\"\"\"\n        # Test end_date before start_date\n        invalid_data = self.contract_data.copy()\n        invalid_data['start_date'] = date.today().isoformat()\n        invalid_data['end_date'] = (date.today() - timedelta(days=1)).isoformat()\n        \n        response = client.post('/api/contracts', json=invalid_data)\n        assert response.status_code in [400, 422, 401, 403, 201, 200]\n\n    def test_contract_rate_validation(self, client):\n        \"\"\"Test contract rate and budget validation\"\"\"\n        # Test negative hourly rate\n        invalid_data = self.contract_data.copy()\n        invalid_data['hourly_rate'] = -50.0\n        \n        response = client.post('/api/contracts', json=invalid_data)\n        assert response.status_code in [400, 422, 401, 403, 201, 200]\n        \n        # Test negative budget\n        invalid_data = self.contract_data.copy()\n        invalid_data['budget_amount'] = -1000.0\n        \n        response = client.post('/api/contracts', json=invalid_data)\n        assert response.status_code in [400, 422, 401, 403, 201, 200]\n\n    def test_get_contract_detail(self, client):\n        \"\"\"Test single contract retrieval\"\"\"\n        test_contract = Contract(\n            client_id=self.test_client.id,\n            contract_number='CTR-DETAIL',\n            title='Detail Test',\n            contract_type='hourly',\n            start_date=date.today(),\n            status='active'\n        )\n        db.session.add(test_contract)\n        db.session.commit()\n\n        response = client.get(f'/api/contracts/{test_contract.id}')\n        assert response.status_code in [200, 401, 404]\n\n    def test_contract_not_found(self, client):\n        \"\"\"Test contract not found scenarios\"\"\"\n        response = client.get('/api/contracts/99999')\n        assert response.status_code in [404, 401]\n        \n        response = client.put('/api/contracts/99999', json={'title': 'Test'})\n        assert response.status_code in [404, 401]\n        \n        response = client.delete('/api/contracts/99999')\n        assert response.status_code in [404, 401]\n\n    def test_contract_number_uniqueness(self, client):\n        \"\"\"Test contract number uniqueness\"\"\"\n        # Create first contract\n        first_contract = Contract(\n            client_id=self.test_client.id,\n            contract_number='CTR-UNIQUE',\n            title='First Contract',\n            contract_type='hourly',\n            start_date=date.today(),\n            status='draft'\n        )\n        db.session.add(first_contract)\n        db.session.commit()\n\n        # Try to create contract with same number\n        duplicate_data = self.contract_data.copy()\n        duplicate_data['contract_number'] = 'CTR-UNIQUE'\n        \n        response = client.post('/api/contracts', json=duplicate_data)\n        assert response.status_code in [400, 409, 401, 403, 201, 200]\n\n    def test_contract_pagination(self, client):\n        \"\"\"Test contract list pagination\"\"\"\n        # Create multiple contracts\n        for i in range(5):\n            contract = Contract(\n                client_id=self.test_client.id,\n                contract_number=f'CTR-PAGE-{i}',\n                title=f'Contract {i}',\n                contract_type='hourly',\n                start_date=date.today(),\n                status='draft'\n            )\n            db.session.add(contract)\n        db.session.commit()\n\n        response = client.get('/api/contracts?page=1&per_page=3')\n        assert response.status_code in [200, 401]\n"}