{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models_split/projects.py"}, "originalCode": "# Project management models\nfrom .base import db, datetime, date\n\nclass Project(db.Model):\n    __tablename__ = 'projects'\n    \n    id = db.<PERSON>umn(db.Integer, primary_key=True)\n    name = db.Column(db.String(128), nullable=False)\n    description = db.Column(db.Text)\n    client_id = db.Column(db.In<PERSON>ger, db.ForeignKey('clients.id'))\n    contract_id = db.Column(db.Integer, db.ForeignKey('contracts.id'), nullable=True)  # Nullable per progetti interni\n    start_date = db.Column(db.Date)\n    end_date = db.Column(db.Date)\n    status = db.Column(db.String(20), default='planning')  # planning, active, completed, on-hold\n    budget = db.Column(db.Float)\n    expenses = db.Column(db.Float, default=0.0)\n    project_type = db.Column(db.String(50), default='service')  # service, license, consulting, product, rd, internal\n    is_billable = db.Column(db.<PERSON>, default=True)  # Progetto fatturabile?\n    client_daily_rate = db.Column(db.Float)  # Tariffa giornaliera al cliente\n    markup_percentage = db.Column(db.Float, default=0.0)  # Markup sui costi\n    \n    # Collegamento con sistema bandi\n    funding_source = db.Column(db.String(100))  # \"public_funding\", \"private\", \"internal\", \"mixed\"\n    funding_application_id = db.Column(db.Integer, db.ForeignKey('funding_applications.id'))\n    \n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    # Relationships\n    tasks = db.relationship('Task', backref='project', lazy='dynamic', cascade='all, delete-orphan')\n    timesheet_entries = db.relationship('TimesheetEntry', backref='project', lazy='dynamic')\n    client = db.relationship('Client', backref='projects')\n    contract = db.relationship('Contract', backref='projects')\n    # funding_application = db.relationship('FundingApplication', foreign_keys='FundingApplication.linked_project_id', backref='linked_projects')\n\n    def __repr__(self):\n        return f'<Project {self.name}>'\n\n    @property\n    def remaining_budget(self):\n        return self.budget - self.expenses\n\n\nclass Task(db.Model):\n    __tablename__ = 'tasks'\n    \n    id = db.Column(db.Integer, primary_key=True)\n    name = db.Column(db.String(128), nullable=False)\n    description = db.Column(db.Text)\n    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'), nullable=False)\n    assignee_id = db.Column(db.Integer, db.ForeignKey('users.id'))\n    status = db.Column(db.String(20), default='todo')  # todo, in-progress, review, done\n    priority = db.Column(db.String(20), default='medium')  # low, medium, high, urgent\n    start_date = db.Column(db.Date)  # Data di inizio pianificata\n    due_date = db.Column(db.Date)    # Data di fine pianificata\n    estimated_hours = db.Column(db.Float)  # Ore stimate per completare il task\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    # Relationships\n    assignee = db.relationship('User', backref='assigned_tasks')\n\n    def __repr__(self):\n        return f'<Task {self.name}>'\n\n    @property\n    def actual_hours(self):\n        \"\"\"Calcola le ore effettive lavorate sul task dai timesheet\"\"\"\n        return sum(entry.hours for entry in self.timesheet_entries)\n\n    @property\n    def hours_variance(self):\n        \"\"\"Calcola la varianza tra ore stimate e ore effettive\"\"\"\n        if not self.estimated_hours or self.actual_hours == 0:\n            return None\n        return self.actual_hours - self.estimated_hours\n\n    @property\n    def hours_efficiency(self):\n        \"\"\"Calcola l'efficienza in percentuale (stimate/effettive * 100)\"\"\"\n        if not self.estimated_hours or self.actual_hours == 0:\n            return None\n        return (self.estimated_hours / self.actual_hours) * 100\n\n    @property\n    def duration_days(self):\n        \"\"\"Calcola la durata pianificata in giorni\"\"\"\n        if not self.start_date or not self.due_date:\n            return None\n        return (self.due_date - self.start_date).days + 1\n\n\nclass TaskDependency(db.Model):\n    __tablename__ = 'task_dependencies'\n\n    id = db.Column(db.Integer, primary_key=True)\n    task_id = db.Column(db.Integer, db.ForeignKey('tasks.id'), nullable=False)  # Campo DB reale\n    depends_on_id = db.Column(db.Integer, db.ForeignKey('tasks.id'), nullable=False)  # Campo DB reale\n\n    # Relationships\n    task = db.relationship('Task', foreign_keys=[task_id], backref='dependencies')\n    depends_on = db.relationship('Task', foreign_keys=[depends_on_id], backref='dependents')\n\n    def __repr__(self):\n        return f'<TaskDependency {self.predecessor_id} -> {self.successor_id}>'\n\n\nclass ProjectResource(db.Model):\n    __tablename__ = 'project_resources'\n\n    \"\"\"Allocazione risorse per progetto\"\"\"\n    id = db.Column(db.Integer, primary_key=True)\n    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'), nullable=False)\n    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)\n    allocation_percentage = db.Column(db.Integer, default=100)  # Campo DB reale\n    role = db.Column(db.String(50))  # Campo DB reale\n\n    # Relationships\n    project = db.relationship('Project', backref='resources')\n    user = db.relationship('User', backref='project_allocations')\n\n    def __repr__(self):\n        return f'<ProjectResource {self.project_id} - {self.user_id}>'\n\n\nclass ProjectKPI(db.Model):\n    __tablename__ = 'project_kpis'\n\n    \"\"\"KPI specifici del progetto\"\"\"\n    id = db.Column(db.Integer, primary_key=True)\n    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'), nullable=False)\n    kpi_id = db.Column(db.Integer, nullable=False)  # Campo DB reale\n    target_value = db.Column(db.Float)\n    current_value = db.Column(db.Float, default=0)\n\n    # Relationships\n    project = db.relationship('Project', backref='kpis')\n\n    def __repr__(self):\n        return f'<ProjectKPI {self.name}>'\n\n    @property\n    def performance_percentage(self):\n        if not self.target_value:\n            return None\n        return (self.current_value / self.target_value) * 100\n\n\nclass ProjectExpense(db.Model):\n    __tablename__ = 'project_expenses'\n\n    \"\"\"Spese del progetto\"\"\"\n    id = db.Column(db.Integer, primary_key=True)\n    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'), nullable=False)\n    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)\n    category = db.Column(db.String(50), nullable=False)\n    description = db.Column(db.String(255), nullable=False)\n    amount = db.Column(db.Float, nullable=False)\n    billing_type = db.Column(db.String(20))  # Campo DB reale\n    date = db.Column(db.Date, nullable=False)  # Campo DB reale\n    receipt_path = db.Column(db.String(255))\n    status = db.Column(db.String(20))\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n\n    # Relationships\n    project = db.relationship('Project', backref='project_expenses')\n    user = db.relationship('User', foreign_keys=[user_id], backref='submitted_expenses')\n    approver = db.relationship('User', foreign_keys=[approved_by], backref='approved_expenses')\n\n    def __repr__(self):\n        return f'<ProjectExpense {self.project_id}: {self.amount} {self.currency}>'\n\n\nclass ProjectKPITemplate(db.Model):\n    __tablename__ = 'project_kpi_templates'\n    \n    \"\"\"Template per KPI di progetto\"\"\"\n    id = db.Column(db.Integer, primary_key=True)\n    name = db.Column(db.String(128), nullable=False)\n    description = db.Column(db.Text)\n    unit = db.Column(db.String(50))  # hours, percentage, count, EUR, etc.\n    category = db.Column(db.String(50))  # budget, timeline, quality, productivity\n    calculation_method = db.Column(db.String(50))  # manual, calculated, imported\n    is_active = db.Column(db.Boolean, default=True)\n    \n    # Campi per KPI calcolati automaticamente\n    calculation_formula = db.Column(db.String(255))  # Formula o riferimento al metodo di calcolo\n    data_source = db.Column(db.String(100))  # timesheet, expenses, tasks, etc.\n    \n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    def __repr__(self):\n        return f'<ProjectKPITemplate {self.name}>'\n\n    def to_dict(self):\n        return {\n            'id': self.id,\n            'name': self.name,\n            'description': self.description,\n            'unit': self.unit,\n            'category': self.category,\n            'calculation_method': self.calculation_method,\n            'is_active': self.is_active,\n            'calculation_formula': self.calculation_formula,\n            'data_source': self.data_source\n        }\n\n\nclass ProjectKPITarget(db.Model):\n    __tablename__ = 'project_kpi_targets'\n    \n    \"\"\"Target KPI per progetto (istanza di un template)\"\"\"\n    id = db.Column(db.Integer, primary_key=True)\n    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'), nullable=False)\n    kpi_template_id = db.Column(db.Integer, db.ForeignKey('project_kpi_templates.id'), nullable=False)\n    \n    # Valori target e attuali\n    target_value = db.Column(db.Float)\n    current_value = db.Column(db.Float, default=0)\n    \n    # Soglie per alerts\n    warning_threshold = db.Column(db.Float)  # Percentuale per warning (es. 80%)\n    critical_threshold = db.Column(db.Float)  # Percentuale per critical (es. 90%)\n    \n    # Tracking\n    last_calculated_at = db.Column(db.DateTime)\n    status = db.Column(db.String(20), default='on_track')  # on_track, warning, critical, completed\n    \n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    # Relationships\n    project = db.relationship('Project', backref='kpi_targets')\n    template = db.relationship('ProjectKPITemplate', backref='project_instances')\n\n    def __repr__(self):\n        return f'<ProjectKPITarget Project:{self.project_id} KPI:{self.kpi_template_id}>'\n\n    @property\n    def achievement_percentage(self):\n        if not self.target_value or self.target_value == 0:\n            return 0\n        return round((self.current_value / self.target_value) * 100, 2)\n\n    def update_status(self):\n        \"\"\"Aggiorna lo stato basato sui threshold\"\"\"\n        percentage = self.achievement_percentage\n        \n        if percentage >= 100:\n            self.status = 'completed'\n        elif self.critical_threshold and percentage >= self.critical_threshold:\n            self.status = 'critical'\n        elif self.warning_threshold and percentage >= self.warning_threshold:\n            self.status = 'warning'\n        else:\n            self.status = 'on_track'\n        \n        return self.status\n\n\nclass ProjectFundingLink(db.Model):\n    \"\"\"Collegamento tra progetti e finanziamenti\"\"\"\n    __tablename__ = 'project_funding_links'\n    \n    id = db.Column(db.Integer, primary_key=True)\n    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'), nullable=False)\n    funding_application_id = db.Column(db.Integer, db.ForeignKey('funding_applications.id'), nullable=False)\n    allocation_percentage = db.Column(db.Float, default=100.0)  # Percentuale del progetto coperta dal finanziamento\n    notes = db.Column(db.Text)\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    \n    # Relationships\n    project = db.relationship('Project', backref='funding_links')\n    funding_application = db.relationship('FundingApplication', backref='project_links')\n    \n    # Unique constraint\n    __table_args__ = (\n        db.UniqueConstraint('project_id', 'funding_application_id', name='unique_project_funding'),\n    )\n    \n    def __repr__(self):\n        return f'<ProjectFundingLink Project:{self.project_id} Funding:{self.funding_application_id}>'", "modifiedCode": "# Project management models\nfrom .base import db, datetime, date\n\nclass Project(db.Model):\n    __tablename__ = 'projects'\n    \n    id = db.<PERSON>umn(db.Integer, primary_key=True)\n    name = db.Column(db.String(128), nullable=False)\n    description = db.Column(db.Text)\n    client_id = db.Column(db.In<PERSON>ger, db.ForeignKey('clients.id'))\n    contract_id = db.Column(db.Integer, db.ForeignKey('contracts.id'), nullable=True)  # Nullable per progetti interni\n    start_date = db.Column(db.Date)\n    end_date = db.Column(db.Date)\n    status = db.Column(db.String(20), default='planning')  # planning, active, completed, on-hold\n    budget = db.Column(db.Float)\n    expenses = db.Column(db.Float, default=0.0)\n    project_type = db.Column(db.String(50), default='service')  # service, license, consulting, product, rd, internal\n    is_billable = db.Column(db.<PERSON>, default=True)  # Progetto fatturabile?\n    client_daily_rate = db.Column(db.Float)  # Tariffa giornaliera al cliente\n    markup_percentage = db.Column(db.Float, default=0.0)  # Markup sui costi\n    \n    # Collegamento con sistema bandi\n    funding_source = db.Column(db.String(100))  # \"public_funding\", \"private\", \"internal\", \"mixed\"\n    funding_application_id = db.Column(db.Integer, db.ForeignKey('funding_applications.id'))\n    \n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    # Relationships\n    tasks = db.relationship('Task', backref='project', lazy='dynamic', cascade='all, delete-orphan')\n    timesheet_entries = db.relationship('TimesheetEntry', backref='project', lazy='dynamic')\n    client = db.relationship('Client', backref='projects')\n    contract = db.relationship('Contract', backref='projects')\n    # funding_application = db.relationship('FundingApplication', foreign_keys='FundingApplication.linked_project_id', backref='linked_projects')\n\n    def __repr__(self):\n        return f'<Project {self.name}>'\n\n    @property\n    def remaining_budget(self):\n        return self.budget - self.expenses\n\n\nclass Task(db.Model):\n    __tablename__ = 'tasks'\n    \n    id = db.Column(db.Integer, primary_key=True)\n    name = db.Column(db.String(128), nullable=False)\n    description = db.Column(db.Text)\n    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'), nullable=False)\n    assignee_id = db.Column(db.Integer, db.ForeignKey('users.id'))\n    status = db.Column(db.String(20), default='todo')  # todo, in-progress, review, done\n    priority = db.Column(db.String(20), default='medium')  # low, medium, high, urgent\n    start_date = db.Column(db.Date)  # Data di inizio pianificata\n    due_date = db.Column(db.Date)    # Data di fine pianificata\n    estimated_hours = db.Column(db.Float)  # Ore stimate per completare il task\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    # Relationships\n    assignee = db.relationship('User', backref='assigned_tasks')\n\n    def __repr__(self):\n        return f'<Task {self.name}>'\n\n    @property\n    def actual_hours(self):\n        \"\"\"Calcola le ore effettive lavorate sul task dai timesheet\"\"\"\n        return sum(entry.hours for entry in self.timesheet_entries)\n\n    @property\n    def hours_variance(self):\n        \"\"\"Calcola la varianza tra ore stimate e ore effettive\"\"\"\n        if not self.estimated_hours or self.actual_hours == 0:\n            return None\n        return self.actual_hours - self.estimated_hours\n\n    @property\n    def hours_efficiency(self):\n        \"\"\"Calcola l'efficienza in percentuale (stimate/effettive * 100)\"\"\"\n        if not self.estimated_hours or self.actual_hours == 0:\n            return None\n        return (self.estimated_hours / self.actual_hours) * 100\n\n    @property\n    def duration_days(self):\n        \"\"\"Calcola la durata pianificata in giorni\"\"\"\n        if not self.start_date or not self.due_date:\n            return None\n        return (self.due_date - self.start_date).days + 1\n\n\nclass TaskDependency(db.Model):\n    __tablename__ = 'task_dependencies'\n\n    id = db.Column(db.Integer, primary_key=True)\n    task_id = db.Column(db.Integer, db.ForeignKey('tasks.id'), nullable=False)  # Campo DB reale\n    depends_on_id = db.Column(db.Integer, db.ForeignKey('tasks.id'), nullable=False)  # Campo DB reale\n\n    # Relationships\n    task = db.relationship('Task', foreign_keys=[task_id], backref='dependencies')\n    depends_on = db.relationship('Task', foreign_keys=[depends_on_id], backref='dependents')\n\n    def __repr__(self):\n        return f'<TaskDependency {self.predecessor_id} -> {self.successor_id}>'\n\n\nclass ProjectResource(db.Model):\n    __tablename__ = 'project_resources'\n\n    \"\"\"Allocazione risorse per progetto\"\"\"\n    id = db.Column(db.Integer, primary_key=True)\n    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'), nullable=False)\n    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)\n    allocation_percentage = db.Column(db.Integer, default=100)  # Campo DB reale\n    role = db.Column(db.String(50))  # Campo DB reale\n\n    # Relationships\n    project = db.relationship('Project', backref='resources')\n    user = db.relationship('User', backref='project_allocations')\n\n    def __repr__(self):\n        return f'<ProjectResource {self.project_id} - {self.user_id}>'\n\n\nclass ProjectKPI(db.Model):\n    __tablename__ = 'project_kpis'\n\n    \"\"\"KPI specifici del progetto\"\"\"\n    id = db.Column(db.Integer, primary_key=True)\n    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'), nullable=False)\n    kpi_id = db.Column(db.Integer, nullable=False)  # Campo DB reale\n    target_value = db.Column(db.Float)\n    current_value = db.Column(db.Float, default=0)\n\n    # Relationships\n    project = db.relationship('Project', backref='kpis')\n\n    def __repr__(self):\n        return f'<ProjectKPI {self.name}>'\n\n    @property\n    def performance_percentage(self):\n        if not self.target_value:\n            return None\n        return (self.current_value / self.target_value) * 100\n\n\nclass ProjectExpense(db.Model):\n    __tablename__ = 'project_expenses'\n\n    \"\"\"Spese del progetto\"\"\"\n    id = db.Column(db.Integer, primary_key=True)\n    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'), nullable=False)\n    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)\n    category = db.Column(db.String(50), nullable=False)\n    description = db.Column(db.String(255), nullable=False)\n    amount = db.Column(db.Float, nullable=False)\n    billing_type = db.Column(db.String(20))  # Campo DB reale\n    date = db.Column(db.Date, nullable=False)  # Campo DB reale\n    receipt_path = db.Column(db.String(255))\n    status = db.Column(db.String(20))\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n\n    # Relationships\n    project = db.relationship('Project', backref='project_expenses')\n    user = db.relationship('User', foreign_keys=[user_id], backref='submitted_expenses')\n\n    def __repr__(self):\n        return f'<ProjectExpense {self.project_id}: {self.amount} {self.currency}>'\n\n\nclass ProjectKPITemplate(db.Model):\n    __tablename__ = 'project_kpi_templates'\n    \n    \"\"\"Template per KPI di progetto\"\"\"\n    id = db.Column(db.Integer, primary_key=True)\n    name = db.Column(db.String(128), nullable=False)\n    description = db.Column(db.Text)\n    unit = db.Column(db.String(50))  # hours, percentage, count, EUR, etc.\n    category = db.Column(db.String(50))  # budget, timeline, quality, productivity\n    calculation_method = db.Column(db.String(50))  # manual, calculated, imported\n    is_active = db.Column(db.Boolean, default=True)\n    \n    # Campi per KPI calcolati automaticamente\n    calculation_formula = db.Column(db.String(255))  # Formula o riferimento al metodo di calcolo\n    data_source = db.Column(db.String(100))  # timesheet, expenses, tasks, etc.\n    \n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    def __repr__(self):\n        return f'<ProjectKPITemplate {self.name}>'\n\n    def to_dict(self):\n        return {\n            'id': self.id,\n            'name': self.name,\n            'description': self.description,\n            'unit': self.unit,\n            'category': self.category,\n            'calculation_method': self.calculation_method,\n            'is_active': self.is_active,\n            'calculation_formula': self.calculation_formula,\n            'data_source': self.data_source\n        }\n\n\nclass ProjectKPITarget(db.Model):\n    __tablename__ = 'project_kpi_targets'\n    \n    \"\"\"Target KPI per progetto (istanza di un template)\"\"\"\n    id = db.Column(db.Integer, primary_key=True)\n    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'), nullable=False)\n    kpi_template_id = db.Column(db.Integer, db.ForeignKey('project_kpi_templates.id'), nullable=False)\n    \n    # Valori target e attuali\n    target_value = db.Column(db.Float)\n    current_value = db.Column(db.Float, default=0)\n    \n    # Soglie per alerts\n    warning_threshold = db.Column(db.Float)  # Percentuale per warning (es. 80%)\n    critical_threshold = db.Column(db.Float)  # Percentuale per critical (es. 90%)\n    \n    # Tracking\n    last_calculated_at = db.Column(db.DateTime)\n    status = db.Column(db.String(20), default='on_track')  # on_track, warning, critical, completed\n    \n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    # Relationships\n    project = db.relationship('Project', backref='kpi_targets')\n    template = db.relationship('ProjectKPITemplate', backref='project_instances')\n\n    def __repr__(self):\n        return f'<ProjectKPITarget Project:{self.project_id} KPI:{self.kpi_template_id}>'\n\n    @property\n    def achievement_percentage(self):\n        if not self.target_value or self.target_value == 0:\n            return 0\n        return round((self.current_value / self.target_value) * 100, 2)\n\n    def update_status(self):\n        \"\"\"Aggiorna lo stato basato sui threshold\"\"\"\n        percentage = self.achievement_percentage\n        \n        if percentage >= 100:\n            self.status = 'completed'\n        elif self.critical_threshold and percentage >= self.critical_threshold:\n            self.status = 'critical'\n        elif self.warning_threshold and percentage >= self.warning_threshold:\n            self.status = 'warning'\n        else:\n            self.status = 'on_track'\n        \n        return self.status\n\n\nclass ProjectFundingLink(db.Model):\n    \"\"\"Collegamento tra progetti e finanziamenti\"\"\"\n    __tablename__ = 'project_funding_links'\n    \n    id = db.Column(db.Integer, primary_key=True)\n    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'), nullable=False)\n    funding_application_id = db.Column(db.Integer, db.ForeignKey('funding_applications.id'), nullable=False)\n    allocation_percentage = db.Column(db.Float, default=100.0)  # Percentuale del progetto coperta dal finanziamento\n    notes = db.Column(db.Text)\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    \n    # Relationships\n    project = db.relationship('Project', backref='funding_links')\n    funding_application = db.relationship('FundingApplication', backref='project_links')\n    \n    # Unique constraint\n    __table_args__ = (\n        db.UniqueConstraint('project_id', 'funding_application_id', name='unique_project_funding'),\n    )\n    \n    def __repr__(self):\n        return f'<ProjectFundingLink Project:{self.project_id} Funding:{self.funding_application_id}>'"}