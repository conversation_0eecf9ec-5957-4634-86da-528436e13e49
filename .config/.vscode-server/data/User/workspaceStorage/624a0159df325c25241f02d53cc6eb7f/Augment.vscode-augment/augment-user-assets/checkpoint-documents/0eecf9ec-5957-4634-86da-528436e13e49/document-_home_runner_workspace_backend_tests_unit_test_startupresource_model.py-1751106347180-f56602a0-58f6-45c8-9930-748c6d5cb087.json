{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/unit/test_startupresource_model.py"}, "originalCode": "\"\"\"Unit tests for StartupResource model.\"\"\"\nimport pytest\nfrom models import StartupResource, User\nfrom extensions import db\n\nclass TestStartupResourceModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n\n    def test_startupresource_creation_basic(self):\n        resource = StartupResource(\n            title='Test Resource',\n            description='This is a test startup resource',\n            # Rimosso category - non esiste nel modello\n            url='https://example.com'\n        )\n        db.session.add(resource)\n        db.session.commit()\n        \n        assert resource.id is not None\n        assert resource.title == 'Test Resource'\n        assert resource.category == 'funding'\n        assert resource.url == 'https://example.com'\n\n    def test_startupresource_deletion(self):\n        resource = StartupResource(title='To Delete', description='Delete me')\n        db.session.add(resource)\n        db.session.commit()\n        resource_id = resource.id\n        \n        db.session.delete(resource)\n        db.session.commit()\n        \n        deleted = StartupResource.query.get(resource_id)\n        assert deleted is None\n", "modifiedCode": "\"\"\"Unit tests for StartupResource model.\"\"\"\nimport pytest\nfrom models import StartupResource, User\nfrom extensions import db\n\nclass TestStartupResourceModel:\n    @pytest.fixture(autouse=True)\n    def setup_method(self, app, test_user):\n        self.app = app\n        self.user = test_user\n\n    def test_startupresource_creation_basic(self):\n        resource = StartupResource(\n            title='Test Resource',\n            description='This is a test startup resource',\n            # Rimosso category - non esiste nel modello\n            url='https://example.com'\n        )\n        db.session.add(resource)\n        db.session.commit()\n        \n        assert resource.id is not None\n        assert resource.title == 'Test Resource'\n        assert resource.category == 'funding'\n        assert resource.url == 'https://example.com'\n\n    def test_startupresource_deletion(self):\n        resource = StartupResource(title='To Delete', description='Delete me')\n        db.session.add(resource)\n        db.session.commit()\n        resource_id = resource.id\n        \n        db.session.delete(resource)\n        db.session.commit()\n        \n        deleted = StartupResource.query.get(resource_id)\n        assert deleted is None\n"}