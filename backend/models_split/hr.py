# HR Management models
from .base import db, datetime

class Skill(db.Model):
    __tablename__ = 'skills'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    name = db.Column(db.String(64), unique=True, nullable=False)
    category = db.Column(db.String(64))
    description = db.Column(db.Text)

    # Relationships
    user_skills = db.relationship('UserSkill', backref='skill', lazy='dynamic', cascade='all, delete-orphan')

    def __repr__(self):
        return f'<Skill {self.name}>'


class Department(db.Model):
    """Modello per la gestione dei dipartimenti aziendali con struttura gerarchica"""
    __tablename__ = 'departments'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    description = db.Column(db.Text)
    manager_id = db.Column(db.<PERSON>, db.<PERSON>('users.id'), nullable=True)
    parent_id = db.Column(db.Integer, db.<PERSON>Key('departments.id'), nullable=True)
    budget = db.Column(db.Float, default=0.0)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    manager = db.relationship('User', foreign_keys=[manager_id], backref='managed_departments')
    employees = db.relationship('User', foreign_keys='User.department_id', backref='department_obj', lazy='dynamic')
    subdepartments = db.relationship('Department', backref=db.backref('parent', remote_side=[id]), lazy='dynamic')

    def __repr__(self):
        return f'<Department {self.name}>'

    @property
    def employee_count(self):
        """Conta il numero di dipendenti nel dipartimento"""
        return self.employees.filter_by(is_active=True).count()

    @property
    def full_path(self):
        """Restituisce il percorso completo del dipartimento (es: IT > Development > Frontend)"""
        if self.parent:
            return f"{self.parent.full_path} > {self.name}"
        return self.name


class UserProfile(db.Model):
    """Profilo HR esteso per gli utenti"""
    __tablename__ = 'user_profiles'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, unique=True)
    employee_id = db.Column(db.String(20), unique=True)
    job_title = db.Column(db.String(100))
    birth_date = db.Column(db.Date)
    address = db.Column(db.Text)
    emergency_contact_name = db.Column(db.String(100))
    emergency_contact_phone = db.Column(db.String(20))
    emergency_contact_relationship = db.Column(db.String(50))

    # Informazioni lavorative
    employment_type = db.Column(db.String(50), default='full_time')  # full_time, part_time, contractor, intern
    work_location = db.Column(db.String(100))  # office, remote, hybrid
    salary = db.Column(db.Float)  # Stipendio base
    salary_currency = db.Column(db.String(3), default='EUR')

    # Informazioni HR
    probation_end_date = db.Column(db.Date)
    contract_end_date = db.Column(db.Date)  # Per contratti a termine
    notice_period_days = db.Column(db.Integer, default=30)

    # Capacità lavorativa
    weekly_hours = db.Column(db.Float, default=40.0)  # Ore settimanali standard
    daily_hours = db.Column(db.Float, default=8.0)   # Ore giornaliere standard

    # CV e documenti
    current_cv_path = db.Column(db.String(255))  # Path del CV attuale
    cv_last_updated = db.Column(db.DateTime)
    cv_analysis_data = db.Column(db.Text)  # JSON con analisi AI del CV

    # Metadati
    profile_completion = db.Column(db.Float, default=0.0)  # Percentuale completamento profilo
    notes = db.Column(db.Text)  # Note HR riservate
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = db.relationship('User', backref=db.backref('profile', uselist=False))

    def __repr__(self):
        return f'<UserProfile {self.user.username if self.user else self.id}>'

    def calculate_completion(self):
        """Calcola la percentuale di completamento del profilo"""
        # Campi UserProfile
        profile_fields = [
            self.employee_id, self.job_title, self.birth_date, self.address,
            self.emergency_contact_name, self.emergency_contact_phone,
            self.employment_type, self.work_location, self.current_cv_path
        ]

        # Campi User base (se disponibili)
        user_fields = []
        if self.user:
            user_fields = [
                self.user.first_name, self.user.last_name, self.user.phone,
                self.user.position, self.user.bio
            ]

        # Combina tutti i campi
        all_fields = profile_fields + user_fields
        completed = sum(1 for field in all_fields if field)

        # Calcola percentuale e arrotonda
        if len(all_fields) > 0:
            self.profile_completion = round((completed / len(all_fields)) * 100)
        else:
            self.profile_completion = 0.0

        return self.profile_completion

    def to_dict(self):
        """Converte il profilo utente in dizionario per serializzazione JSON"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'employee_id': self.employee_id,
            'job_title': self.job_title,
            'birth_date': self.birth_date.isoformat() if self.birth_date else None,
            'address': self.address,
            'emergency_contact_name': self.emergency_contact_name,
            'emergency_contact_phone': self.emergency_contact_phone,
            'emergency_contact_relationship': self.emergency_contact_relationship,
            'employment_type': self.employment_type,
            'work_location': self.work_location,
            'salary': self.salary,
            'salary_currency': self.salary_currency,
            'probation_end_date': self.probation_end_date.isoformat() if self.probation_end_date else None,
            'contract_end_date': self.contract_end_date.isoformat() if self.contract_end_date else None,
            'notice_period_days': self.notice_period_days,
            'weekly_hours': self.weekly_hours,
            'daily_hours': self.daily_hours,
            'current_cv_path': self.current_cv_path,
            'cv_last_updated': self.cv_last_updated.isoformat() if self.cv_last_updated else None,
            'cv_analysis_data': self.cv_analysis_data,
            'profile_completion': self.profile_completion,
            'notes': self.notes,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class UserSkill(db.Model):
    __tablename__ = 'user_skills_detailed'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    skill_id = db.Column(db.Integer, db.ForeignKey('skills.id'), nullable=False)
    proficiency_level = db.Column(db.Integer, default=1)  # 1-5 scale
    years_experience = db.Column(db.Float, default=0.0)
    is_certified = db.Column(db.Boolean, default=False)
    certification_name = db.Column(db.String(100))
    certification_date = db.Column(db.Date)
    certification_expiry = db.Column(db.Date)
    self_assessed = db.Column(db.Boolean, default=True)
    manager_assessed = db.Column(db.Boolean, default=False)
    manager_assessment_date = db.Column(db.Date)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_used = db.Column(db.Date)
    certified = db.Column(db.Boolean, default=False)

    # Relationships
    user = db.relationship('User', backref='detailed_skills')
    # skill relationship is already defined in Skill model via backref

    # Unique constraint to prevent duplicate skills per user
    __table_args__ = (
        db.UniqueConstraint('user_id', 'skill_id', name='unique_user_skill'),
    )

    def __repr__(self):
        return f'<UserSkill {self.user_id}-{self.skill_id}>'

    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'skill_id': self.skill_id,
            'skill': {
                'id': self.skill.id,
                'name': self.skill.name,
                'category': self.skill.category
            } if self.skill else None,
            'proficiency_level': self.proficiency_level,
            'years_experience': self.years_experience,
            'last_used': self.last_used.isoformat() if self.last_used else None,
            'is_certified': self.is_certified,
            'certified': self.certified,
            'certification_date': self.certification_date.isoformat() if self.certification_date else None,
            'certification_name': self.certification_name,
            'certification_expiry': self.certification_expiry.isoformat() if self.certification_expiry else None,
            'self_assessed': self.self_assessed,
            'manager_assessed': self.manager_assessed,
            'manager_assessment_date': self.manager_assessment_date.isoformat() if self.manager_assessment_date else None,
            'notes': self.notes
        }


class TimeOffRequest(db.Model):
    __tablename__ = 'time_off_requests'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    type = db.Column(db.String(50), nullable=False)  # vacation, sick, personal, etc.
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    reason = db.Column(db.Text)
    status = db.Column(db.String(20), default='pending')  # pending, approved, rejected, cancelled
    approved_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    approved_at = db.Column(db.DateTime)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    requester = db.relationship('User', foreign_keys=[user_id], backref='time_off_requests')
    approver = db.relationship('User', foreign_keys=[approved_by], backref='approved_time_offs')

    def __repr__(self):
        return f'<TimeOffRequest {self.user_id} {self.start_date} to {self.end_date}>'


class JobLevel(db.Model):
    """Modello per la definizione dei livelli di lavoro (job levels)"""
    __tablename__ = 'job_levels'
    
    id = db.Column(db.Integer, primary_key=True)
    level_number = db.Column(db.Integer, nullable=False, unique=True)  # e.g., 1, 2, 3, 4, 5
    name = db.Column(db.String(100), nullable=False)  # e.g., "Junior Developer", "Senior Developer"
    description = db.Column(db.Text)
    min_salary = db.Column(db.Float)
    max_salary = db.Column(db.Float)
    typical_years_experience = db.Column(db.Integer)
    is_management = db.Column(db.Boolean, default=False)
    is_active = db.Column(db.Boolean, default=True)  # Campo mancante aggiunto
    parent_level_id = db.Column(db.Integer, db.ForeignKey('job_levels.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Self-referential relationship for hierarchy
    parent_level = db.relationship('JobLevel', remote_side=[id], backref='sub_levels')
    
    def __repr__(self):
        return f'<JobLevel {self.level_number}: {self.name}>'
    
    @property
    def current_employees(self):
        """Get employees currently assigned to this job level"""
        return [ejl.employee for ejl in self.employees if ejl.end_date is None and ejl.is_active]
    
    def to_dict(self):
        return {
            'id': self.id,
            'level_number': self.level_number,
            'name': self.name,
            'description': self.description,
            'min_salary': self.min_salary,
            'max_salary': self.max_salary,
            'typical_years_experience': self.typical_years_experience,
            'is_management': self.is_management,
            'parent_level_id': self.parent_level_id,
            'parent_level': {
                'id': self.parent_level.id,
                'name': self.parent_level.name,
                'level_number': self.parent_level.level_number
            } if self.parent_level else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class EmployeeJobLevel(db.Model):
    """Storico dei livelli di lavoro per ogni dipendente"""
    __tablename__ = 'employee_job_levels'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    job_level_id = db.Column(db.Integer, db.ForeignKey('job_levels.id'), nullable=False)
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date)  # NULL means current level
    current_salary = db.Column(db.Float)  # Renamed from salary to match DB
    notes = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)  # Missing column added
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    employee = db.relationship('User', foreign_keys=[user_id], backref='job_level_history')
    job_level = db.relationship('JobLevel', backref='employees')
    creator = db.relationship('User', foreign_keys=[created_by])
    
    def __repr__(self):
        return f'<EmployeeJobLevel User:{self.user_id} Level:{self.job_level_id}>'
    
    @property
    def is_current(self):
        return self.end_date is None
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'employee': {
                'id': self.employee.id,
                'full_name': self.employee.full_name,
                'username': self.employee.username
            } if self.employee else None,
            'job_level_id': self.job_level_id,
            'job_level': self.job_level.to_dict() if self.job_level else None,
            'start_date': self.start_date.isoformat() if self.start_date else None,
            'end_date': self.end_date.isoformat() if self.end_date else None,
            'is_current': self.is_current,
            'current_salary': self.current_salary,
            'notes': self.notes,
            'created_by': self.created_by,
            'creator': {
                'id': self.creator.id,
                'full_name': self.creator.full_name
            } if self.creator else None,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }


class PersonnelRate(db.Model):
    """Tariffe del personale per progetti e clienti"""
    __tablename__ = 'personnel_rates'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    daily_rate = db.Column(db.Float, nullable=False)  # Campo DB reale
    valid_from = db.Column(db.Date, nullable=False)   # Campo DB reale
    valid_to = db.Column(db.Date)                     # Campo DB reale
    currency = db.Column(db.String(3), default='EUR')
    notes = db.Column(db.String(255))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    user = db.relationship('User', backref='rates')

    def __repr__(self):
        return f'<PersonnelRate {self.user_id}: {self.daily_rate} {self.currency}>'