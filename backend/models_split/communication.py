# Communication Models
from .base import db, datetime

class ForumTopic(db.Model):
    """Modello per i topic del forum aziendale"""
    __tablename__ = 'forum_topics'
    
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(255), nullable=False)
    description = db.<PERSON>umn(db.Text, nullable=False)
    author_id = db.<PERSON>umn(db.Integer, db.ForeignKey('users.id'), nullable=False)
    category = db.Column(db.String(100))
    is_pinned = db.Column(db.Boolean, default=False)
    is_locked = db.Column(db.Bo<PERSON>an, default=False)
    view_count = db.Column(db.Integer, default=0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    author = db.relationship('User', backref='forum_topics')
    comments = db.relationship('ForumComment', backref='topic', lazy='dynamic', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<ForumTopic {self.title}>'
    
    @property
    def comments_count(self):
        return self.comments.count()
    
    @property
    def last_activity(self):
        last_comment = self.comments.order_by(ForumComment.created_at.desc()).first()
        return last_comment.created_at if last_comment else self.created_at
    
    def get_reactions(self):
        """Get all reactions for this topic"""
        # Lazy import to avoid circular dependency
        from models import CommunicationReaction
        return CommunicationReaction.query.filter_by(
            target_type='forum_topic',
            target_id=self.id
        ).all()
    
    def get_reaction_counts(self):
        """Get reaction counts by type"""
        from sqlalchemy import func
        # Lazy import to avoid circular dependency
        from models import CommunicationReaction
        reaction_counts = db.session.query(
            CommunicationReaction.reaction_type,
            func.count(CommunicationReaction.id).label('count')
        ).filter_by(
            target_type='forum_topic',
            target_id=self.id
        ).group_by(CommunicationReaction.reaction_type).all()
        
        return {reaction_type: count for reaction_type, count in reaction_counts}
    
    def get_user_reaction(self, user_id):
        """Get specific user's reaction for this topic"""
        # Lazy import to avoid circular dependency
        from models import CommunicationReaction
        return CommunicationReaction.query.filter_by(
            target_type='forum_topic',
            target_id=self.id,
            user_id=user_id
        ).first()
    
    def to_dict(self, include_reactions=False, current_user_id=None):
        data = {
            'id': self.id,
            'title': self.title,
            'description': self.description,
            'content': self.description,  # Frontend compatibility - content = description
            'author_id': self.author_id,
            'author': {
                'id': self.author.id,
                'first_name': self.author.first_name,
                'last_name': self.author.last_name,
                'username': self.author.username,
                'full_name': f"{self.author.first_name} {self.author.last_name}".strip()
            } if self.author else None,
            'author_name': f"{self.author.first_name} {self.author.last_name}".strip() if self.author else 'Anonimo',
            'category': self.category,
            'is_pinned': self.is_pinned,
            'is_locked': self.is_locked,
            'view_count': self.view_count,
            'comments_count': self.comments_count,
            'last_activity': self.last_activity.isoformat() if self.last_activity else None,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
        
        if include_reactions:
            # Include reaction data
            reactions_list = [reaction.to_dict() for reaction in self.get_reactions()]
            data['reactions'] = reactions_list
            data['reaction_counts'] = self.get_reaction_counts()
            
            # Include current user's reaction if user_id provided
            if current_user_id:
                user_reaction = self.get_user_reaction(current_user_id)
                data['user_reaction'] = user_reaction.to_dict() if user_reaction else None
        
        return data


class ForumComment(db.Model):
    """Modello per i commenti del forum"""
    __tablename__ = 'forum_comments'
    
    id = db.Column(db.Integer, primary_key=True)
    topic_id = db.Column(db.Integer, db.ForeignKey('forum_topics.id'), nullable=False)
    author_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    content = db.Column(db.Text, nullable=False)
    parent_comment_id = db.Column(db.Integer, db.ForeignKey('forum_comments.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    author = db.relationship('User', backref='forum_comments')
    parent_comment = db.relationship('ForumComment', remote_side=[id], backref='replies')
    
    def __repr__(self):
        return f'<ForumComment {self.id} on Topic {self.topic_id}>'
    
    @property
    def is_reply(self):
        return self.parent_comment_id is not None
    
    def to_dict(self):
        return {
            'id': self.id,
            'topic_id': self.topic_id,
            'author_id': self.author_id,
            'author': {
                'id': self.author.id,
                'first_name': self.author.first_name,
                'last_name': self.author.last_name,
                'username': self.author.username
            } if self.author else None,
            'content': self.content,
            'parent_comment_id': self.parent_comment_id,
            'is_reply': self.is_reply,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }


class Poll(db.Model):
    """Modello per i sondaggi aziendali"""
    __tablename__ = 'polls'
    
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text)
    author_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    is_anonymous = db.Column(db.Boolean, default=False)
    multiple_choice = db.Column(db.Boolean, default=False)
    expires_at = db.Column(db.DateTime)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    author = db.relationship('User', backref='created_polls')
    options = db.relationship('PollOption', backref='poll', lazy='dynamic', cascade='all, delete-orphan')
    votes = db.relationship('PollVote', backref='poll', lazy='dynamic', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Poll {self.title}>'
    
    @property
    def is_expired(self):
        if self.expires_at:
            return datetime.utcnow() > self.expires_at
        return False
    
    @property
    def total_votes(self):
        return self.votes.count()
    
    @property
    def unique_voters(self):
        return db.session.query(PollVote.user_id).filter_by(poll_id=self.id).distinct().count()
    
    def can_vote(self, user_id):
        if not self.is_active or self.is_expired:
            return False
        if not self.multiple_choice:
            return not self.votes.filter_by(user_id=user_id).first()
        return True
    
    def get_results(self):
        results = []
        for option in self.options:
            vote_count = option.votes.count()
            percentage = (vote_count / self.total_votes * 100) if self.total_votes > 0 else 0
            results.append({
                'option_id': option.id,
                'text': option.option_text,
                'votes': vote_count,
                'percentage': round(percentage, 2)
            })
        return results
    
    def to_dict(self, include_results=False):
        data = {
            'id': self.id,
            'title': self.title,
            'description': self.description,
            'author_id': self.author_id,
            'author': {
                'id': self.author.id,
                'first_name': self.author.first_name,
                'last_name': self.author.last_name,
                'username': self.author.username,
                'full_name': f"{self.author.first_name} {self.author.last_name}".strip()
            } if self.author else None,
            'author_name': f"{self.author.first_name} {self.author.last_name}".strip() if self.author else 'Anonimo',
            'is_anonymous': self.is_anonymous,
            'multiple_choice': self.multiple_choice,
            'allows_multiple_choices': self.multiple_choice,  # Frontend compatibility
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'is_active': self.is_active,
            'is_expired': self.is_expired,
            'total_votes': self.total_votes,
            'unique_voters': self.unique_voters,
            'options': [option.to_dict() for option in self.options],
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
        
        if include_results:
            data['results'] = self.get_results()
        
        return data


class PollOption(db.Model):
    """Modello per le opzioni dei sondaggi"""
    __tablename__ = 'poll_options'
    
    id = db.Column(db.Integer, primary_key=True)
    poll_id = db.Column(db.Integer, db.ForeignKey('polls.id'), nullable=False)
    option_text = db.Column(db.String(255), nullable=False)
    
    # Relationships
    votes = db.relationship('PollVote', backref='option', lazy='dynamic', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<PollOption {self.option_text}>'
    
    @property
    def vote_count(self):
        return self.votes.count()
    
    def to_dict(self):
        return {
            'id': self.id,
            'poll_id': self.poll_id,
            'text': self.option_text,
            'vote_count': self.vote_count,
            'votes': self.vote_count  # Frontend compatibility
        }


class PollVote(db.Model):
    """Modello per i voti dei sondaggi"""
    __tablename__ = 'poll_votes'
    
    id = db.Column(db.Integer, primary_key=True)
    poll_id = db.Column(db.Integer, db.ForeignKey('polls.id'), nullable=False)
    option_id = db.Column(db.Integer, db.ForeignKey('poll_options.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    user = db.relationship('User', backref='poll_votes')
    
    # Constraints
    __table_args__ = (
        db.UniqueConstraint('poll_id', 'user_id', 'option_id', name='unique_poll_vote'),
    )
    
    def __repr__(self):
        return f'<PollVote User:{self.user_id} Poll:{self.poll_id} Option:{self.option_id}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'poll_id': self.poll_id,
            'option_id': self.option_id,
            'user_id': self.user_id,
            'user': {
                'id': self.user.id,
                'first_name': self.user.first_name,
                'last_name': self.user.last_name,
                'username': self.user.username
            } if self.user else None,
            'created_at': self.created_at.isoformat()
        }


class DirectMessage(db.Model):
    """Modello per i messaggi diretti tra utenti"""
    __tablename__ = 'direct_messages'
    
    id = db.Column(db.Integer, primary_key=True)
    sender_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    recipient_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    message = db.Column(db.Text, nullable=False)
    is_read = db.Column(db.Boolean, default=False)
    read_at = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    sender = db.relationship('User', foreign_keys=[sender_id], backref='sent_messages')
    recipient = db.relationship('User', foreign_keys=[recipient_id], backref='received_messages')
    
    def __repr__(self):
        return f'<DirectMessage from {self.sender_id} to {self.recipient_id}>'
    
    def mark_as_read(self):
        if not self.is_read:
            self.is_read = True
            self.read_at = datetime.utcnow()
            db.session.commit()
    
    def to_dict(self):
        return {
            'id': self.id,
            'sender_id': self.sender_id,
            'sender': {
                'id': self.sender.id,
                'first_name': self.sender.first_name,
                'last_name': self.sender.last_name,
                'username': self.sender.username,
                'full_name': f"{self.sender.first_name} {self.sender.last_name}".strip()
            } if self.sender else None,
            'sender_name': f"{self.sender.first_name} {self.sender.last_name}".strip() if self.sender else 'Mittente sconosciuto',
            'recipient_id': self.recipient_id,
            'recipient': {
                'id': self.recipient.id,
                'first_name': self.recipient.first_name,
                'last_name': self.recipient.last_name,
                'username': self.recipient.username,
                'full_name': f"{self.recipient.first_name} {self.recipient.last_name}".strip()
            } if self.recipient else None,
            'recipient_name': f"{self.recipient.first_name} {self.recipient.last_name}".strip() if self.recipient else 'Destinatario sconosciuto',
            'message': self.message,
            'body': self.message,  # Frontend compatibility
            'content': self.message,  # Frontend compatibility
            'subject': None,  # Campo per compatibilità frontend - da implementare se necessario
            'is_read': self.is_read,
            'read_at': self.read_at.isoformat() if self.read_at else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'sent_at': self.created_at.isoformat() if self.created_at else None,  # Frontend compatibility
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class CommunicationReaction(db.Model):
    """Modello per le reazioni ai contenuti di comunicazione"""
    __tablename__ = 'communication_reactions'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    target_type = db.Column(db.String(50), nullable=False)  # 'forum_topic', 'forum_comment', 'news', 'event'
    target_id = db.Column(db.Integer, nullable=False)
    reaction_type = db.Column(db.String(20), nullable=False)  # 'like', 'love', 'laugh', 'angry', 'sad'
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    user = db.relationship('User', backref='communication_reactions')
    
    # Constraints
    __table_args__ = (
        db.UniqueConstraint('user_id', 'target_type', 'target_id', name='unique_user_reaction'),
    )
    
    def __repr__(self):
        return f'<CommunicationReaction {self.reaction_type} by {self.user_id} on {self.content_type}:{self.content_id}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'user': {
                'id': self.user.id,
                'first_name': self.user.first_name,
                'last_name': self.user.last_name,
                'username': self.user.username,
                'name': f"{self.user.first_name} {self.user.last_name}".strip()
            } if self.user else None,
            'target_type': self.target_type,
            'target_id': self.target_id,
            'reaction_type': self.reaction_type,
            'created_at': self.created_at.isoformat()
        }


class CompanyEvent(db.Model):
    __tablename__ = 'company_events'
    
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(128), nullable=False)
    description = db.Column(db.Text)
    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'))
    start_time = db.Column(db.DateTime, nullable=False)
    end_time = db.Column(db.DateTime, nullable=False)
    location = db.Column(db.String(128))
    event_type = db.Column(db.String(20))  # meeting, deadline, milestone
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Nuovi campi per il sistema di comunicazione
    is_company_wide = db.Column(db.Boolean, default=False)
    max_participants = db.Column(db.Integer)
    registration_required = db.Column(db.Boolean, default=False)
    registration_deadline = db.Column(db.DateTime)
    is_public = db.Column(db.Boolean, default=False)
    tags = db.Column(db.Text)  # JSON string for tags
    allow_comments = db.Column(db.Boolean, default=True)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    creator = db.relationship('User', backref='created_company_events')

    def __repr__(self):
        return f'<CompanyEvent {self.title}>'

    def to_dict(self):
        return {
            'id': self.id,
            'title': self.title,
            'description': self.description,
            'project_id': self.project_id,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'location': self.location,
            'event_type': self.event_type,
            'created_by': self.created_by,
            'creator': {
                'id': self.creator.id,
                'first_name': self.creator.first_name,
                'last_name': self.creator.last_name,
                'username': self.creator.username
            } if self.creator else None,
            'is_company_wide': self.is_company_wide,
            'max_participants': self.max_participants,
            'registration_required': self.registration_required,
            'registration_deadline': self.registration_deadline.isoformat() if self.registration_deadline else None,
            'is_public': self.is_public,
            'tags': self.tags,
            'allow_comments': self.allow_comments,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class CompanyEventRegistration(db.Model):
    """Modello per le registrazioni agli eventi aziendali"""
    __tablename__ = 'company_event_registrations'
    
    id = db.Column(db.Integer, primary_key=True)
    event_id = db.Column(db.Integer, db.ForeignKey('company_events.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    status = db.Column(db.String(20), default='registered')  # 'registered', 'attended', 'cancelled'
    notes = db.Column(db.Text)
    registered_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    event = db.relationship('CompanyEvent', backref='registrations')
    user = db.relationship('User', backref='event_registrations')
    
    # Constraints
    __table_args__ = (
        db.UniqueConstraint('event_id', 'user_id', name='unique_event_registration'),
    )
    
    def __repr__(self):
        return f'<CompanyEventRegistration User:{self.user_id} Event:{self.event_id}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'event_id': self.event_id,
            'event': {
                'id': self.event.id,
                'title': self.event.title,
                'start_time': self.event.start_time.isoformat(),
                'end_time': self.event.end_time.isoformat(),
                'location': self.event.location
            } if self.event else None,
            'user_id': self.user_id,
            'user': {
                'id': self.user.id,
                'first_name': self.user.first_name,
                'last_name': self.user.last_name,
                'username': self.user.username
            } if self.user else None,
            'status': self.status,
            'notes': self.notes,
            'registered_at': self.registered_at.isoformat() if self.registered_at else None
        }