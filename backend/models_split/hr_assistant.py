# HR Assistant Models
from .base import db, datetime, json
from .user import User

class HRKnowledgeBase(db.Model):
    """Knowledge base HR per chatbot assistant"""
    __tablename__ = 'hr_knowledge_base'
    
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(255), nullable=False)
    content = db.Column(db.Text, nullable=False)
    category = db.Column(db.String(100), nullable=False)  # contracts, onboarding, offboarding, leave, permits, travel, benefits, tools, purchases, training
    tags = db.Column(db.Text)  # JSON array of tags
    is_active = db.Column(db.<PERSON>an, default=True)
    
    # AI-assisted content creation
    created_with_ai = db.Column(db.Boolean, default=False)
    ai_sources = db.Column(db.Text)  # JSON array of web sources used
    ai_confidence = db.Column(db.String(20))  # high, medium, low
    
    # Standard fields
    created_by = db.<PERSON>umn(db.<PERSON>, db.<PERSON>ey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    creator = db.relationship('User', backref='hr_knowledge_entries')
    
    def to_dict(self):
        return {
            'id': self.id,
            'title': self.title,
            'content': self.content,
            'category': self.category,
            'tags': json.loads(self.tags) if self.tags else [],
            'is_active': self.is_active,
            'created_with_ai': self.created_with_ai,
            'ai_confidence': self.ai_confidence,
            'created_by': self.created_by,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'creator': {
                'id': self.creator.id,
                'full_name': self.creator.full_name
            } if self.creator else None
        }

class HRChatConversation(db.Model):
    """Conversazioni chatbot HR per tracking e analytics"""
    __tablename__ = 'hr_chat_conversations'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    session_id = db.Column(db.String(100), nullable=False)  # Frontend session tracking
    
    # Conversation data
    user_message = db.Column(db.Text, nullable=False)
    bot_response = db.Column(db.Text, nullable=False)
    category_detected = db.Column(db.String(100))  # Auto-detected category
    confidence_score = db.Column(db.String(20))  # AI confidence in response
    
    # Knowledge base references used
    kb_entries_used = db.Column(db.Text)  # JSON array of KB entry IDs
    
    # Analytics fields
    response_time_ms = db.Column(db.Integer)  # Response time in milliseconds
    user_feedback = db.Column(db.String(20))  # helpful, not_helpful, neutral
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    user = db.relationship('User', backref='hr_chat_history')
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'session_id': self.session_id,
            'user_message': self.user_message,
            'bot_response': self.bot_response,
            'category_detected': self.category_detected,
            'confidence_score': self.confidence_score,
            'kb_entries_used': json.loads(self.kb_entries_used) if self.kb_entries_used else [],
            'response_time_ms': self.response_time_ms,
            'user_feedback': self.user_feedback,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'user': {
                'id': self.user.id,
                'full_name': self.user.full_name
            } if self.user else None
        }

class HRContentTemplate(db.Model):
    """Template pre-configurati per creazione contenuti HR"""
    __tablename__ = 'hr_content_templates'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    category = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    
    # Template structure
    prompt_template = db.Column(db.Text, nullable=False)  # Template for AI content generation
    required_fields = db.Column(db.Text)  # JSON array of required input fields
    output_format = db.Column(db.Text)  # Expected output structure
    
    # Usage tracking
    usage_count = db.Column(db.Integer, default=0)
    last_used = db.Column(db.DateTime)
    
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'category': self.category,
            'description': self.description,
            'prompt_template': self.prompt_template,
            'required_fields': json.loads(self.required_fields) if self.required_fields else [],
            'output_format': self.output_format,
            'usage_count': self.usage_count,
            'last_used': self.last_used.isoformat() if self.last_used else None,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class HRAnalytics(db.Model):
    """Analytics aggregati per utilizzo HR Assistant"""
    __tablename__ = 'hr_analytics'
    
    id = db.Column(db.Integer, primary_key=True)
    date = db.Column(db.Date, nullable=False)
    
    # Metriche giornaliere
    total_conversations = db.Column(db.Integer, default=0)
    unique_users = db.Column(db.Integer, default=0)
    avg_response_time_ms = db.Column(db.Float, default=0)
    
    # Distribuzione categorie (JSON)
    category_distribution = db.Column(db.Text)  # JSON: {"onboarding": 5, "leave": 3, ...}
    
    # Feedback distribution (JSON)  
    feedback_distribution = db.Column(db.Text)  # JSON: {"helpful": 8, "not_helpful": 1, ...}
    
    # Knowledge base stats
    total_kb_entries = db.Column(db.Integer, default=0)
    ai_generated_entries = db.Column(db.Integer, default=0)
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'date': self.date.isoformat() if self.date else None,
            'total_conversations': self.total_conversations,
            'unique_users': self.unique_users,
            'avg_response_time_ms': self.avg_response_time_ms,
            'category_distribution': json.loads(self.category_distribution) if self.category_distribution else {},
            'feedback_distribution': json.loads(self.feedback_distribution) if self.feedback_distribution else {},
            'total_kb_entries': self.total_kb_entries,
            'ai_generated_entries': self.ai_generated_entries,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }