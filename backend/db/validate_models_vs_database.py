#!/usr/bin/env python3
"""
Script per validare la coerenza tra modelli Python e database reale.
Identifica discrepanze dopo il refactoring automatico.
"""

import sys
import importlib
import sqlalchemy as sa
from sqlalchemy import inspect
from app import create_app
from extensions import db

def get_model_tables():
    """Ottieni tutte le tabelle definite nei modelli Python"""
    model_tables = {}
    
    # Import all models to register them with SQLAlchemy
    try:
        import models  # This imports all models
        
        # Get all tables from SQLAlchemy metadata
        for table_name, table in db.metadata.tables.items():
            columns = {}
            for column in table.columns:
                columns[column.name] = {
                    'type': str(column.type),
                    'nullable': column.nullable,
                    'primary_key': column.primary_key,
                    'foreign_key': bool(column.foreign_keys),
                    'default': str(column.default) if column.default else None
                }
            model_tables[table_name] = columns
            
    except Exception as e:
        print(f"❌ Errore nell'importazione dei modelli: {e}")
        return {}
    
    return model_tables

def get_database_tables():
    """Ottieni tutte le tabelle presenti nel database"""
    db_tables = {}
    
    try:
        inspector = inspect(db.engine)
        
        for table_name in inspector.get_table_names():
            columns = {}
            for column in inspector.get_columns(table_name):
                columns[column['name']] = {
                    'type': str(column['type']),
                    'nullable': column['nullable'],
                    'primary_key': column.get('primary_key', False),
                    'foreign_key': bool(column.get('foreign_keys', [])),
                    'default': str(column['default']) if column['default'] else None
                }
            db_tables[table_name] = columns
            
    except Exception as e:
        print(f"❌ Errore nell'analisi del database: {e}")
        return {}
    
    return db_tables

def find_duplicate_tables(db_tables):
    """Trova tabelle duplicate (singolare/plurale)"""
    duplicates = []
    table_names = list(db_tables.keys())
    
    for table in table_names:
        # Check for plural version
        if table.endswith('s'):
            singular = table[:-1]
            if singular in table_names:
                duplicates.append((singular, table))
        else:
            plural = table + 's'
            if plural in table_names:
                duplicates.append((table, plural))
    
    # Remove duplicates from list
    seen = set()
    unique_duplicates = []
    for dup in duplicates:
        sorted_dup = tuple(sorted(dup))
        if sorted_dup not in seen:
            seen.add(sorted_dup)
            unique_duplicates.append(dup)
    
    return unique_duplicates

def compare_tables(model_tables, db_tables):
    """Confronta tabelle tra modelli e database"""
    issues = {
        'missing_in_db': [],
        'missing_in_models': [],
        'column_mismatches': [],
        'duplicates': []
    }
    
    # Find tables missing in database
    for table_name in model_tables:
        if table_name not in db_tables:
            issues['missing_in_db'].append(table_name)
    
    # Find tables missing in models
    for table_name in db_tables:
        if table_name not in model_tables:
            issues['missing_in_models'].append(table_name)
    
    # Find column mismatches in common tables
    common_tables = set(model_tables.keys()) & set(db_tables.keys())
    
    for table_name in common_tables:
        model_cols = model_tables[table_name]
        db_cols = db_tables[table_name]
        
        table_issues = {
            'table': table_name,
            'missing_in_db': [],
            'missing_in_model': [],
            'type_mismatches': []
        }
        
        # Check for missing columns
        for col_name in model_cols:
            if col_name not in db_cols:
                table_issues['missing_in_db'].append(col_name)
        
        for col_name in db_cols:
            if col_name not in model_cols:
                table_issues['missing_in_model'].append(col_name)
        
        # Check for type mismatches in common columns
        common_cols = set(model_cols.keys()) & set(db_cols.keys())
        for col_name in common_cols:
            model_type = model_cols[col_name]['type']
            db_type = db_cols[col_name]['type']
            
            # Normalize types for comparison
            if model_type != db_type:
                table_issues['type_mismatches'].append({
                    'column': col_name,
                    'model_type': model_type,
                    'db_type': db_type
                })
        
        # Only add if there are issues
        if any([table_issues['missing_in_db'], table_issues['missing_in_model'], 
                table_issues['type_mismatches']]):
            issues['column_mismatches'].append(table_issues)
    
    # Find duplicate tables
    issues['duplicates'] = find_duplicate_tables(db_tables)
    
    return issues

def print_validation_report(issues, model_tables, db_tables):
    """Stampa report di validazione"""
    print("🔍 REPORT VALIDAZIONE MODELLI vs DATABASE")
    print("=" * 60)
    
    print(f"\n📊 STATISTICHE:")
    print(f"  - Tabelle nei modelli: {len(model_tables)}")
    print(f"  - Tabelle nel database: {len(db_tables)}")
    print(f"  - Tabelle comuni: {len(set(model_tables.keys()) & set(db_tables.keys()))}")
    
    # Duplicate tables
    if issues['duplicates']:
        print(f"\n🚨 TABELLE DUPLICATE ({len(issues['duplicates'])}):")
        for singular, plural in issues['duplicates']:
            print(f"  ❌ {singular} + {plural}")
    
    # Missing tables
    if issues['missing_in_db']:
        print(f"\n❌ TABELLE MANCANTI NEL DATABASE ({len(issues['missing_in_db'])}):")
        for table in sorted(issues['missing_in_db']):
            print(f"  - {table}")
    
    if issues['missing_in_models']:
        print(f"\n❌ TABELLE MANCANTI NEI MODELLI ({len(issues['missing_in_models'])}):")
        for table in sorted(issues['missing_in_models']):
            print(f"  - {table}")
    
    # Column mismatches
    if issues['column_mismatches']:
        print(f"\n🔧 DISCREPANZE COLONNE ({len(issues['column_mismatches'])} tabelle):")
        for table_issue in issues['column_mismatches']:
            table_name = table_issue['table']
            print(f"\n  📋 {table_name.upper()}:")
            
            if table_issue['missing_in_db']:
                print(f"    ❌ Colonne mancanti nel DB:")
                for col in table_issue['missing_in_db']:
                    print(f"      - {col}")
            
            if table_issue['missing_in_model']:
                print(f"    ❌ Colonne mancanti nel modello:")
                for col in table_issue['missing_in_model']:
                    print(f"      - {col}")
            
            if table_issue['type_mismatches']:
                print(f"    ⚠️  Tipi diversi:")
                for mismatch in table_issue['type_mismatches']:
                    print(f"      - {mismatch['column']}: {mismatch['model_type']} vs {mismatch['db_type']}")
    
    # Summary
    total_issues = (len(issues['duplicates']) + len(issues['missing_in_db']) + 
                   len(issues['missing_in_models']) + len(issues['column_mismatches']))
    
    print(f"\n📈 RIEPILOGO:")
    print(f"  - Tabelle duplicate: {len(issues['duplicates'])}")
    print(f"  - Tabelle mancanti nel DB: {len(issues['missing_in_db'])}")
    print(f"  - Tabelle mancanti nei modelli: {len(issues['missing_in_models'])}")
    print(f"  - Tabelle con discrepanze colonne: {len(issues['column_mismatches'])}")
    print(f"  - TOTALE PROBLEMI: {total_issues}")
    
    if total_issues == 0:
        print("\n✅ NESSUN PROBLEMA RILEVATO!")
    else:
        print(f"\n🚨 ATTENZIONE: {total_issues} problemi da risolvere!")

def main():
    """Main function"""
    app = create_app()
    
    with app.app_context():
        print("🔍 Analizzando modelli e database...")
        
        # Get table definitions
        model_tables = get_model_tables()
        db_tables = get_database_tables()
        
        if not model_tables:
            print("❌ Impossibile caricare i modelli!")
            return
        
        if not db_tables:
            print("❌ Impossibile analizzare il database!")
            return
        
        # Compare and find issues
        issues = compare_tables(model_tables, db_tables)
        
        # Print report
        print_validation_report(issues, model_tables, db_tables)

if __name__ == '__main__':
    main()
