#!/usr/bin/env python3
"""
Script per correggere i nomi di tabelle sbagliati generati automaticamente.
"""

import os
import re

def fix_file_tablenames(file_path, replacements):
    """Corregge i nomi delle tabelle in un file"""
    
    if not os.path.exists(file_path):
        print(f"❌ File {file_path} non trovato")
        return 0
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    original_content = content
    changes = 0
    
    for bad_name, good_name in replacements.items():
        if bad_name in content:
            content = content.replace(f"__tablename__ = '{bad_name}'", f"__tablename__ = '{good_name}'")
            print(f"  ✅ {bad_name} → {good_name}")
            changes += 1
    
    if changes > 0:
        with open(file_path, 'w') as f:
            f.write(content)
        print(f"  📝 File {file_path} aggiornato con {changes} correzioni")
    
    return changes

def main():
    """Main function"""
    print("🔧 FIXING BAD TABLE NAMES")
    print("=" * 30)
    
    # Definisci le correzioni necessarie
    bad_to_good_names = {
        # Projects.py fixes
        'task_dependencys': 'task_dependencies',
        'project_k_p_is': 'project_kpis', 
        'project_k_p_i_targets': 'project_kpi_targets',
        'project_k_p_i_templates': 'project_kpi_templates',
        
        # Altri possibili errori
        'k_p_is': 'kpis',
    }
    
    # File da controllare
    files_to_check = [
        'models_split/projects.py',
        'models_split/business.py',
        'models_split/content.py',
        'models_split/system.py',
        'models_split/crm.py',
        'models_split/performance.py'
    ]
    
    total_changes = 0
    
    for file_path in files_to_check:
        print(f"\n📝 Checking {file_path}...")
        changes = fix_file_tablenames(file_path, bad_to_good_names)
        total_changes += changes
        
        if changes == 0:
            print(f"  ✅ Nessuna correzione necessaria")
    
    print(f"\n📊 RIEPILOGO:")
    print(f"  - Correzioni totali: {total_changes}")
    
    if total_changes > 0:
        print(f"\n✅ Correzioni completate!")
        print("\n🔄 PROSSIMI PASSI:")
        print("  1. Riavvia l'applicazione")
        print("  2. Rimuovi le tabelle duplicate dal database")
        print("  3. Verifica che i modelli usino i nomi corretti")
    else:
        print(f"\n✅ Tutti i nomi delle tabelle sono già corretti!")

if __name__ == '__main__':
    main()
