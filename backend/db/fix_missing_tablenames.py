#!/usr/bin/env python3
"""
Script per aggiungere __tablename__ mancanti nei modelli.
Questo risolve il problema delle tabelle duplicate singolare/plurale.
"""

import os
import re

def add_tablename_to_model(file_path, class_name, table_name):
    """Aggiunge __tablename__ a un modello specifico"""
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Pattern per trovare la definizione della classe
    class_pattern = rf'class {class_name}\(db\.Model\):\s*\n'
    
    # Cerca la classe
    match = re.search(class_pattern, content)
    if not match:
        print(f"  ❌ Classe {class_name} non trovata in {file_path}")
        return False
    
    # Verifica se __tablename__ esiste già
    if f"__tablename__ = '{table_name}'" in content:
        print(f"  ✅ {class_name} ha già __tablename__ = '{table_name}'")
        return False
    
    # Trova la posizione dopo la definizione della classe
    class_end = match.end()
    
    # Inserisci __tablename__ dopo la definizione della classe
    tablename_line = f"    __tablename__ = '{table_name}'\n    \n"
    
    new_content = content[:class_end] + tablename_line + content[class_end:]
    
    # Scrivi il file aggiornato
    with open(file_path, 'w') as f:
        f.write(new_content)
    
    print(f"  ✅ Aggiunto __tablename__ = '{table_name}' a {class_name}")
    return True

def fix_content_models():
    """Fix modelli in content.py"""
    file_path = 'models_split/content.py'
    print(f"\n📝 Fixing {file_path}...")
    
    models_to_fix = [
        ('News', 'news'),
        ('Document', 'documents'),
        ('Regulation', 'regulations'),
        ('StartupResource', 'startup_resources')
    ]
    
    changes = 0
    for class_name, table_name in models_to_fix:
        if add_tablename_to_model(file_path, class_name, table_name):
            changes += 1
    
    return changes

def fix_business_models():
    """Fix modelli in business.py"""
    file_path = 'models_split/business.py'
    print(f"\n📝 Fixing {file_path}...")
    
    models_to_fix = [
        ('Product', 'products'),
        ('Service', 'services'),
        ('KPI', 'kpis')
    ]
    
    changes = 0
    for class_name, table_name in models_to_fix:
        if add_tablename_to_model(file_path, class_name, table_name):
            changes += 1
    
    return changes

def fix_system_models():
    """Fix modelli in system.py"""
    file_path = 'models_split/system.py'
    print(f"\n📝 Fixing {file_path}...")
    
    models_to_fix = [
        ('Notification', 'notifications')
    ]
    
    changes = 0
    for class_name, table_name in models_to_fix:
        if add_tablename_to_model(file_path, class_name, table_name):
            changes += 1
    
    return changes

def fix_crm_models():
    """Fix modelli in crm.py se necessario"""
    file_path = 'models_split/crm.py'
    print(f"\n📝 Checking {file_path}...")
    
    # Prima verifichiamo se ci sono modelli senza __tablename__
    with open(file_path, 'r') as f:
        content = f.read()
    
    models_to_check = ['Contact', 'Proposal']
    models_to_fix = []
    
    for model in models_to_check:
        class_pattern = rf'class {model}\(db\.Model\):'
        if re.search(class_pattern, content):
            # Verifica se ha __tablename__
            if f'__tablename__' not in content[content.find(f'class {model}'):content.find(f'class {model}') + 500]:
                table_name = model.lower() + 's'
                models_to_fix.append((model, table_name))
    
    changes = 0
    for class_name, table_name in models_to_fix:
        if add_tablename_to_model(file_path, class_name, table_name):
            changes += 1
    
    return changes

def check_projects_models():
    """Verifica se ci sono modelli senza __tablename__ in projects.py"""
    file_path = 'models_split/projects.py'
    print(f"\n📝 Checking {file_path}...")
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Cerca modelli che potrebbero non avere __tablename__
    models_to_check = ['TaskDependency', 'ProjectResource', 'ProjectKPI', 'ProjectExpense', 'ProjectKPITemplate', 'ProjectKPITarget']
    models_to_fix = []
    
    for model in models_to_check:
        class_pattern = rf'class {model}\(db\.Model\):'
        match = re.search(class_pattern, content)
        if match:
            # Estrai il contenuto della classe (primi 500 caratteri)
            class_start = match.start()
            class_content = content[class_start:class_start + 500]
            
            if '__tablename__' not in class_content:
                # Converti CamelCase a snake_case plurale
                table_name = re.sub(r'(?<!^)(?=[A-Z])', '_', model).lower()
                if not table_name.endswith('s'):
                    table_name += 's'
                models_to_fix.append((model, table_name))
    
    changes = 0
    for class_name, table_name in models_to_fix:
        if add_tablename_to_model(file_path, class_name, table_name):
            changes += 1
    
    return changes

def main():
    """Main function"""
    print("🔧 FIXING MISSING __tablename__ DEFINITIONS")
    print("=" * 50)
    
    total_changes = 0
    
    # Fix tutti i modelli problematici
    total_changes += fix_content_models()
    total_changes += fix_business_models() 
    total_changes += fix_system_models()
    total_changes += fix_crm_models()
    total_changes += check_projects_models()
    
    print(f"\n📊 RIEPILOGO:")
    print(f"  - Modelli modificati: {total_changes}")
    
    if total_changes > 0:
        print(f"\n✅ Fix completato! {total_changes} modelli aggiornati.")
        print("\n🚨 IMPORTANTE:")
        print("  1. Riavvia l'applicazione per ricaricare i modelli")
        print("  2. Le tabelle duplicate nel database devono essere rimosse manualmente")
        print("  3. Esegui una migrazione per sincronizzare il database")
    else:
        print("\n✅ Tutti i modelli hanno già __tablename__ definito!")

if __name__ == '__main__':
    main()
