"""
Test suite for TimeOffRequest API endpoints.
Tests CRUD operations, validation, and business logic for time-off request management.
"""

import pytest
from datetime import datetime, date, timedelta
from models import TimeOffRequest, User
from extensions import db


class TestTimeOffRequestsAPI:
    """Test suite for TimeOffRequest API endpoints"""

    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        """Setup test data for each test method"""
        self.app = app
        self.user = test_user
        
        # Create test time-off request data
        self.timeoff_data = {
            'user_id': self.user.id,
            'type': 'vacation',
            'start_date': (date.today() + timedelta(days=30)).isoformat(),
            'end_date': (date.today() + timedelta(days=35)).isoformat(),
            'reason': 'Annual vacation to Italy',
            'status': 'pending'
        }

    def test_get_timeoff_requests_success(self, client):
        """Test successful retrieval of time-off requests list"""
        # Create test requests
        request1 = TimeOffRequest(
            user_id=self.user.id,
            type='vacation',
            start_date=date.today() + timedelta(days=10),
            end_date=date.today() + timedelta(days=15),
            reason='Summer vacation',
            status='pending'
        )
        request2 = TimeOffRequest(
            user_id=self.user.id,
            type='sick',
            start_date=date.today() + timedelta(days=20),
            end_date=date.today() + timedelta(days=22),
            reason='Medical appointment',
            status='approved'
        )
        db.session.add_all([request1, request2])
        db.session.commit()

        response = client.get('/api/timeoff/requests')
        
        assert response.status_code in [200, 401]
        if response.status_code == 200:
            data = response.get_json()
            assert 'requests' in str(data).lower() or 'data' in data

    def test_create_timeoff_request_success(self, client):
        """Test successful time-off request creation"""
        response = client.post('/api/timeoff/requests', json=self.timeoff_data)
        
        assert response.status_code in [201, 200, 401, 403]
        if response.status_code in [200, 201]:
            # Verify request was created
            created_request = TimeOffRequest.query.filter_by(
                user_id=self.user.id,
                type='vacation'
            ).first()
            if created_request:
                assert created_request.reason == 'Annual vacation to Italy'

    def test_create_timeoff_request_validation_error(self, client):
        """Test time-off request creation with missing required fields"""
        invalid_data = {'type': 'vacation'}  # Missing required fields
        
        response = client.post('/api/timeoff/requests', json=invalid_data)
        
        assert response.status_code in [400, 422, 401, 403]

    def test_update_timeoff_request_success(self, client):
        """Test successful time-off request update"""
        test_request = TimeOffRequest(
            user_id=self.user.id,
            type='vacation',
            start_date=date.today() + timedelta(days=10),
            end_date=date.today() + timedelta(days=15),
            reason='Original reason',
            status='pending'
        )
        db.session.add(test_request)
        db.session.commit()

        update_data = {
            'reason': 'Updated vacation reason',
            'end_date': (date.today() + timedelta(days=17)).isoformat()
        }
        
        response = client.put(f'/api/timeoff/requests/{test_request.id}', json=update_data)
        
        assert response.status_code in [200, 401, 403, 404]

    def test_delete_timeoff_request_success(self, client):
        """Test successful time-off request deletion"""
        test_request = TimeOffRequest(
            user_id=self.user.id,
            type='personal',
            start_date=date.today() + timedelta(days=5),
            end_date=date.today() + timedelta(days=6),
            reason='Personal matter',
            status='pending'
        )
        db.session.add(test_request)
        db.session.commit()
        request_id = test_request.id

        response = client.delete(f'/api/timeoff/requests/{request_id}')
        
        assert response.status_code in [200, 401, 403, 404]

    def test_timeoff_request_date_validation(self, client):
        """Test time-off request date validation"""
        # Test end_date before start_date
        invalid_data = self.timeoff_data.copy()
        invalid_data['start_date'] = (date.today() + timedelta(days=10)).isoformat()
        invalid_data['end_date'] = (date.today() + timedelta(days=5)).isoformat()
        
        response = client.post('/api/timeoff/requests', json=invalid_data)
        assert response.status_code in [400, 422, 401, 403, 201, 200]
        
        # Test past dates
        invalid_data = self.timeoff_data.copy()
        invalid_data['start_date'] = (date.today() - timedelta(days=5)).isoformat()
        invalid_data['end_date'] = (date.today() - timedelta(days=3)).isoformat()
        
        response = client.post('/api/timeoff/requests', json=invalid_data)
        assert response.status_code in [400, 422, 401, 403, 201, 200]

    def test_timeoff_request_type_validation(self, client):
        """Test time-off request type validation"""
        valid_types = ['vacation', 'sick', 'personal', 'maternity', 'paternity', 'bereavement']
        
        for request_type in valid_types:
            test_data = self.timeoff_data.copy()
            test_data['type'] = request_type
            test_data['start_date'] = (date.today() + timedelta(days=30 + len(valid_types))).isoformat()
            test_data['end_date'] = (date.today() + timedelta(days=32 + len(valid_types))).isoformat()
            
            response = client.post('/api/timeoff/requests', json=test_data)
            assert response.status_code in [201, 200, 401, 403, 400, 422]

    def test_timeoff_request_status_workflow(self, client):
        """Test time-off request status transitions"""
        test_request = TimeOffRequest(
            user_id=self.user.id,
            type='vacation',
            start_date=date.today() + timedelta(days=20),
            end_date=date.today() + timedelta(days=25),
            reason='Status test',
            status='pending'
        )
        db.session.add(test_request)
        db.session.commit()

        # Test status transitions
        status_transitions = ['approved', 'rejected', 'cancelled']
        
        for status in status_transitions:
            response = client.put(
                f'/api/timeoff/requests/{test_request.id}', 
                json={'status': status}
            )
            assert response.status_code in [200, 401, 403, 404, 400]

    def test_approve_timeoff_request(self, client):
        """Test time-off request approval"""
        test_request = TimeOffRequest(
            user_id=self.user.id,
            type='vacation',
            start_date=date.today() + timedelta(days=15),
            end_date=date.today() + timedelta(days=20),
            reason='Approval test',
            status='pending'
        )
        db.session.add(test_request)
        db.session.commit()

        response = client.put(f'/api/timeoff/requests/{test_request.id}/approve')
        assert response.status_code in [200, 401, 403, 404]

    def test_reject_timeoff_request(self, client):
        """Test time-off request rejection"""
        test_request = TimeOffRequest(
            user_id=self.user.id,
            type='personal',
            start_date=date.today() + timedelta(days=10),
            end_date=date.today() + timedelta(days=12),
            reason='Rejection test',
            status='pending'
        )
        db.session.add(test_request)
        db.session.commit()

        rejection_data = {'reason': 'Insufficient notice period'}
        response = client.put(
            f'/api/timeoff/requests/{test_request.id}/reject', 
            json=rejection_data
        )
        assert response.status_code in [200, 401, 403, 404]

    def test_timeoff_request_search_and_filters(self, client):
        """Test time-off request search and filtering"""
        # Create test requests with different types and statuses
        request1 = TimeOffRequest(
            user_id=self.user.id,
            type='vacation',
            start_date=date.today() + timedelta(days=10),
            end_date=date.today() + timedelta(days=15),
            reason='Vacation request',
            status='pending'
        )
        request2 = TimeOffRequest(
            user_id=self.user.id,
            type='sick',
            start_date=date.today() + timedelta(days=20),
            end_date=date.today() + timedelta(days=22),
            reason='Sick leave',
            status='approved'
        )
        db.session.add_all([request1, request2])
        db.session.commit()

        # Test status filter
        response = client.get('/api/timeoff/requests?status=pending')
        assert response.status_code in [200, 401]
        
        # Test type filter
        response = client.get('/api/timeoff/requests?type=vacation')
        assert response.status_code in [200, 401]
        
        # Test user filter
        response = client.get(f'/api/timeoff/requests?user_id={self.user.id}')
        assert response.status_code in [200, 401]

    def test_get_timeoff_request_detail(self, client):
        """Test single time-off request retrieval"""
        test_request = TimeOffRequest(
            user_id=self.user.id,
            type='vacation',
            start_date=date.today() + timedelta(days=30),
            end_date=date.today() + timedelta(days=35),
            reason='Detail test vacation',
            status='pending'
        )
        db.session.add(test_request)
        db.session.commit()

        response = client.get(f'/api/timeoff/requests/{test_request.id}')
        assert response.status_code in [200, 401, 404]

    def test_timeoff_request_not_found(self, client):
        """Test time-off request not found scenarios"""
        response = client.get('/api/timeoff/requests/99999')
        assert response.status_code in [404, 401]
        
        response = client.put('/api/timeoff/requests/99999', json={'reason': 'Test'})
        assert response.status_code in [404, 401]
        
        response = client.delete('/api/timeoff/requests/99999')
        assert response.status_code in [404, 401]

    def test_timeoff_request_overlapping_periods(self, client):
        """Test handling of overlapping time-off periods"""
        # Create existing request
        existing_request = TimeOffRequest(
            user_id=self.user.id,
            type='vacation',
            start_date=date.today() + timedelta(days=10),
            end_date=date.today() + timedelta(days=15),
            reason='Existing vacation',
            status='approved'
        )
        db.session.add(existing_request)
        db.session.commit()

        # Try to create overlapping request
        overlapping_data = self.timeoff_data.copy()
        overlapping_data['start_date'] = (date.today() + timedelta(days=12)).isoformat()
        overlapping_data['end_date'] = (date.today() + timedelta(days=17)).isoformat()
        
        response = client.post('/api/timeoff/requests', json=overlapping_data)
        assert response.status_code in [400, 409, 401, 403, 201, 200]

    def test_timeoff_request_pagination(self, client):
        """Test time-off request list pagination"""
        # Create multiple requests
        for i in range(5):
            request = TimeOffRequest(
                user_id=self.user.id,
                type='vacation',
                start_date=date.today() + timedelta(days=10 + i * 7),
                end_date=date.today() + timedelta(days=12 + i * 7),
                reason=f'Vacation {i}',
                status='pending'
            )
            db.session.add(request)
        db.session.commit()

        response = client.get('/api/timeoff/requests?page=1&per_page=3')
        assert response.status_code in [200, 401]
