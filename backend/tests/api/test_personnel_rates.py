"""
Test suite for PersonnelRate API endpoints.
Tests CRUD operations, validation, and business logic for personnel rate management.
"""

import pytest
from datetime import datetime, date, timedelta
from models import PersonnelRate, User
from extensions import db


class TestPersonnelRatesAPI:
    """Test suite for PersonnelRate API endpoints"""

    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        """Setup test data for each test method"""
        self.app = app
        self.user = test_user
        
        # Create test personnel rate data
        self.rate_data = {
            'user_id': self.user.id,
            'daily_rate': 350.0,
            'valid_from': date.today().isoformat(),
            'valid_to': (date.today() + timedelta(days=365)).isoformat(),
            'currency': 'EUR',
            'notes': 'Standard daily rate for senior developer'
        }

    def test_get_personnel_rates_success(self, client):
        """Test successful retrieval of personnel rates list"""
        # Create test rates
        rate1 = PersonnelRate(
            user_id=self.user.id,
            daily_rate=300.0,
            valid_from=date.today() - timedelta(days=30),
            currency='EUR'
        )
        rate2 = PersonnelRate(
            user_id=self.user.id,
            daily_rate=350.0,
            valid_from=date.today(),
            currency='EUR'
        )
        db.session.add_all([rate1, rate2])
        db.session.commit()

        response = client.get('/api/personnel/rates')
        
        assert response.status_code in [200, 401]
        if response.status_code == 200:
            data = response.get_json()
            assert 'rates' in str(data).lower() or 'data' in data

    def test_create_personnel_rate_success(self, client):
        """Test successful personnel rate creation"""
        response = client.post('/api/personnel/rates', json=self.rate_data)
        
        assert response.status_code in [201, 200, 401, 403]
        if response.status_code in [200, 201]:
            # Verify rate was created
            created_rate = PersonnelRate.query.filter_by(
                user_id=self.user.id,
                daily_rate=350.0
            ).first()
            if created_rate:
                assert created_rate.currency == 'EUR'

    def test_create_personnel_rate_validation_error(self, client):
        """Test personnel rate creation with missing required fields"""
        invalid_data = {'daily_rate': 300.0}  # Missing user_id and valid_from
        
        response = client.post('/api/personnel/rates', json=invalid_data)
        
        assert response.status_code in [400, 422, 401, 403]

    def test_update_personnel_rate_success(self, client):
        """Test successful personnel rate update"""
        test_rate = PersonnelRate(
            user_id=self.user.id,
            daily_rate=300.0,
            valid_from=date.today(),
            currency='EUR'
        )
        db.session.add(test_rate)
        db.session.commit()

        update_data = {
            'daily_rate': 400.0,
            'notes': 'Updated rate for promotion'
        }
        
        response = client.put(f'/api/personnel/rates/{test_rate.id}', json=update_data)
        
        assert response.status_code in [200, 401, 403, 404]

    def test_delete_personnel_rate_success(self, client):
        """Test successful personnel rate deletion"""
        test_rate = PersonnelRate(
            user_id=self.user.id,
            daily_rate=250.0,
            valid_from=date.today(),
            currency='EUR'
        )
        db.session.add(test_rate)
        db.session.commit()
        rate_id = test_rate.id

        response = client.delete(f'/api/personnel/rates/{rate_id}')
        
        assert response.status_code in [200, 401, 403, 404]

    def test_personnel_rate_date_validation(self, client):
        """Test personnel rate date validation"""
        # Test valid_to before valid_from
        invalid_data = self.rate_data.copy()
        invalid_data['valid_from'] = date.today().isoformat()
        invalid_data['valid_to'] = (date.today() - timedelta(days=1)).isoformat()
        
        response = client.post('/api/personnel/rates', json=invalid_data)
        assert response.status_code in [400, 422, 401, 403, 201, 200]

    def test_personnel_rate_amount_validation(self, client):
        """Test personnel rate amount validation"""
        # Test negative daily rate
        invalid_data = self.rate_data.copy()
        invalid_data['daily_rate'] = -100.0
        
        response = client.post('/api/personnel/rates', json=invalid_data)
        assert response.status_code in [400, 422, 401, 403, 201, 200]
        
        # Test zero daily rate
        invalid_data['daily_rate'] = 0.0
        response = client.post('/api/personnel/rates', json=invalid_data)
        assert response.status_code in [400, 422, 401, 403, 201, 200]

    def test_get_personnel_rates_by_user(self, client):
        """Test retrieval of personnel rates for specific user"""
        # Create rates for different users
        rate1 = PersonnelRate(
            user_id=self.user.id,
            daily_rate=350.0,
            valid_from=date.today(),
            currency='EUR'
        )
        db.session.add(rate1)
        db.session.commit()

        response = client.get(f'/api/personnel/users/{self.user.id}/rates')
        assert response.status_code in [200, 401, 404]

    def test_get_current_personnel_rate(self, client):
        """Test retrieval of current valid personnel rate"""
        # Create historical and current rates
        old_rate = PersonnelRate(
            user_id=self.user.id,
            daily_rate=300.0,
            valid_from=date.today() - timedelta(days=365),
            valid_to=date.today() - timedelta(days=1),
            currency='EUR'
        )
        current_rate = PersonnelRate(
            user_id=self.user.id,
            daily_rate=350.0,
            valid_from=date.today(),
            currency='EUR'
        )
        db.session.add_all([old_rate, current_rate])
        db.session.commit()

        response = client.get(f'/api/personnel/users/{self.user.id}/rates/current')
        assert response.status_code in [200, 401, 404]

    def test_personnel_rate_search_and_filters(self, client):
        """Test personnel rate search and filtering"""
        # Create test rates
        rate1 = PersonnelRate(
            user_id=self.user.id,
            daily_rate=300.0,
            valid_from=date.today() - timedelta(days=30),
            currency='EUR'
        )
        rate2 = PersonnelRate(
            user_id=self.user.id,
            daily_rate=350.0,
            valid_from=date.today(),
            currency='USD'
        )
        db.session.add_all([rate1, rate2])
        db.session.commit()

        # Test currency filter
        response = client.get('/api/personnel/rates?currency=EUR')
        assert response.status_code in [200, 401]
        
        # Test user filter
        response = client.get(f'/api/personnel/rates?user_id={self.user.id}')
        assert response.status_code in [200, 401]
        
        # Test date range filter
        response = client.get(f'/api/personnel/rates?valid_from={date.today().isoformat()}')
        assert response.status_code in [200, 401]

    def test_personnel_rate_currency_validation(self, client):
        """Test personnel rate currency validation"""
        # Test invalid currency
        invalid_data = self.rate_data.copy()
        invalid_data['currency'] = 'INVALID'
        
        response = client.post('/api/personnel/rates', json=invalid_data)
        assert response.status_code in [400, 422, 401, 403, 201, 200]

    def test_get_personnel_rate_detail(self, client):
        """Test single personnel rate retrieval"""
        test_rate = PersonnelRate(
            user_id=self.user.id,
            daily_rate=375.0,
            valid_from=date.today(),
            currency='EUR',
            notes='Senior rate'
        )
        db.session.add(test_rate)
        db.session.commit()

        response = client.get(f'/api/personnel/rates/{test_rate.id}')
        assert response.status_code in [200, 401, 404]

    def test_personnel_rate_not_found(self, client):
        """Test personnel rate not found scenarios"""
        response = client.get('/api/personnel/rates/99999')
        assert response.status_code in [404, 401]
        
        response = client.put('/api/personnel/rates/99999', json={'daily_rate': 400.0})
        assert response.status_code in [404, 401]
        
        response = client.delete('/api/personnel/rates/99999')
        assert response.status_code in [404, 401]

    def test_personnel_rate_overlapping_periods(self, client):
        """Test handling of overlapping rate periods"""
        # Create existing rate
        existing_rate = PersonnelRate(
            user_id=self.user.id,
            daily_rate=300.0,
            valid_from=date.today(),
            valid_to=date.today() + timedelta(days=30),
            currency='EUR'
        )
        db.session.add(existing_rate)
        db.session.commit()

        # Try to create overlapping rate
        overlapping_data = self.rate_data.copy()
        overlapping_data['valid_from'] = (date.today() + timedelta(days=15)).isoformat()
        overlapping_data['valid_to'] = (date.today() + timedelta(days=45)).isoformat()
        
        response = client.post('/api/personnel/rates', json=overlapping_data)
        assert response.status_code in [400, 409, 401, 403, 201, 200]

    def test_personnel_rate_pagination(self, client):
        """Test personnel rate list pagination"""
        # Create multiple rates
        for i in range(5):
            rate = PersonnelRate(
                user_id=self.user.id,
                daily_rate=300.0 + i * 10,
                valid_from=date.today() - timedelta(days=i * 30),
                currency='EUR'
            )
            db.session.add(rate)
        db.session.commit()

        response = client.get('/api/personnel/rates?page=1&per_page=3')
        assert response.status_code in [200, 401]
