"""
Test suite for Contracts API endpoints.
Tests CRUD operations, validation, and business logic for contract management.
"""

import pytest
from datetime import datetime, date, timedelta
from models import Contract, Client, User
from extensions import db


class TestContractsAPI:
    """Test suite for Contracts API endpoints"""

    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        """Setup test data for each test method"""
        self.app = app
        self.user = test_user
        
        # Create test client
        self.test_client = Client(name='Test Client', status='active')
        db.session.add(self.test_client)
        db.session.commit()
        
        # Create test contract data
        self.contract_data = {
            'client_id': self.test_client.id,
            'contract_number': 'CTR-2025-001',
            'title': 'Test Contract',
            'description': 'A comprehensive test contract for API testing',
            'contract_type': 'hourly',
            'hourly_rate': 80.0,
            'budget_hours': 100.0,
            'budget_amount': 8000.0,
            'start_date': date.today().isoformat(),
            'end_date': (date.today() + timedelta(days=90)).isoformat(),
            'status': 'draft'
        }

    def test_get_contracts_success(self, client):
        """Test successful retrieval of contracts list"""
        # Create test contracts
        contract1 = Contract(
            client_id=self.test_client.id,
            contract_number='CTR-001',
            title='Contract 1',
            contract_type='hourly',
            start_date=date.today(),
            status='active'
        )
        contract2 = Contract(
            client_id=self.test_client.id,
            contract_number='CTR-002',
            title='Contract 2',
            contract_type='fixed',
            start_date=date.today(),
            status='draft'
        )
        db.session.add_all([contract1, contract2])
        db.session.commit()

        response = client.get('/api/contracts')
        
        assert response.status_code in [200, 401]
        if response.status_code == 200:
            data = response.get_json()
            assert 'contracts' in str(data).lower() or 'data' in data

    def test_create_contract_success(self, client):
        """Test successful contract creation"""
        response = client.post('/api/contracts', json=self.contract_data)
        
        assert response.status_code in [201, 200, 401, 403]
        if response.status_code in [200, 201]:
            # Verify contract was created
            created_contract = Contract.query.filter_by(contract_number='CTR-2025-001').first()
            if created_contract:
                assert created_contract.title == 'Test Contract'
                assert created_contract.client_id == self.test_client.id

    def test_create_contract_validation_error(self, client):
        """Test contract creation with missing required fields"""
        invalid_data = {'description': 'Missing required fields'}
        
        response = client.post('/api/contracts', json=invalid_data)
        
        assert response.status_code in [400, 422, 401, 403]

    def test_update_contract_success(self, client):
        """Test successful contract update"""
        test_contract = Contract(
            client_id=self.test_client.id,
            contract_number='CTR-UPDATE',
            title='Original Title',
            contract_type='hourly',
            start_date=date.today(),
            status='draft'
        )
        db.session.add(test_contract)
        db.session.commit()

        update_data = {
            'title': 'Updated Contract Title',
            'hourly_rate': 90.0,
            'status': 'active'
        }
        
        response = client.put(f'/api/contracts/{test_contract.id}', json=update_data)
        
        assert response.status_code in [200, 401, 403, 404]

    def test_delete_contract_success(self, client):
        """Test successful contract deletion"""
        test_contract = Contract(
            client_id=self.test_client.id,
            contract_number='CTR-DELETE',
            title='To Delete',
            contract_type='fixed',
            start_date=date.today(),
            status='draft'
        )
        db.session.add(test_contract)
        db.session.commit()
        contract_id = test_contract.id

        response = client.delete(f'/api/contracts/{contract_id}')
        
        assert response.status_code in [200, 401, 403, 404]

    def test_contract_types_validation(self, client):
        """Test different contract types"""
        contract_types = ['hourly', 'fixed', 'milestone', 'subscription', 'retainer']
        
        for contract_type in contract_types:
            test_data = self.contract_data.copy()
            test_data['contract_type'] = contract_type
            test_data['contract_number'] = f'CTR-{contract_type.upper()}'
            
            response = client.post('/api/contracts', json=test_data)
            assert response.status_code in [201, 200, 401, 403, 400, 422]

    def test_contract_status_workflow(self, client):
        """Test contract status transitions"""
        test_contract = Contract(
            client_id=self.test_client.id,
            contract_number='CTR-STATUS',
            title='Status Test',
            contract_type='hourly',
            start_date=date.today(),
            status='draft'
        )
        db.session.add(test_contract)
        db.session.commit()

        # Test status transitions
        status_transitions = ['active', 'completed', 'cancelled']
        
        for status in status_transitions:
            response = client.put(
                f'/api/contracts/{test_contract.id}', 
                json={'status': status}
            )
            assert response.status_code in [200, 401, 403, 404, 400]

    def test_contract_search_and_filters(self, client):
        """Test contract search and filtering"""
        # Create test contracts with different types and statuses
        contract1 = Contract(
            client_id=self.test_client.id,
            contract_number='CTR-HOURLY',
            title='Hourly Contract',
            contract_type='hourly',
            start_date=date.today(),
            status='active'
        )
        contract2 = Contract(
            client_id=self.test_client.id,
            contract_number='CTR-FIXED',
            title='Fixed Contract',
            contract_type='fixed',
            start_date=date.today(),
            status='draft'
        )
        db.session.add_all([contract1, contract2])
        db.session.commit()

        # Test status filter
        response = client.get('/api/contracts?status=active')
        assert response.status_code in [200, 401]
        
        # Test contract type filter
        response = client.get('/api/contracts?contract_type=hourly')
        assert response.status_code in [200, 401]
        
        # Test client filter
        response = client.get(f'/api/contracts?client_id={self.test_client.id}')
        assert response.status_code in [200, 401]

    def test_contract_date_validation(self, client):
        """Test contract date validation"""
        # Test end_date before start_date
        invalid_data = self.contract_data.copy()
        invalid_data['start_date'] = date.today().isoformat()
        invalid_data['end_date'] = (date.today() - timedelta(days=1)).isoformat()
        
        response = client.post('/api/contracts', json=invalid_data)
        assert response.status_code in [400, 422, 401, 403, 201, 200]

    def test_contract_rate_validation(self, client):
        """Test contract rate and budget validation"""
        # Test negative hourly rate
        invalid_data = self.contract_data.copy()
        invalid_data['hourly_rate'] = -50.0
        
        response = client.post('/api/contracts', json=invalid_data)
        assert response.status_code in [400, 422, 401, 403, 201, 200]
        
        # Test negative budget
        invalid_data = self.contract_data.copy()
        invalid_data['budget_amount'] = -1000.0
        
        response = client.post('/api/contracts', json=invalid_data)
        assert response.status_code in [400, 422, 401, 403, 201, 200]

    def test_get_contract_detail(self, client):
        """Test single contract retrieval"""
        test_contract = Contract(
            client_id=self.test_client.id,
            contract_number='CTR-DETAIL',
            title='Detail Test',
            contract_type='hourly',
            start_date=date.today(),
            status='active'
        )
        db.session.add(test_contract)
        db.session.commit()

        response = client.get(f'/api/contracts/{test_contract.id}')
        assert response.status_code in [200, 401, 404]

    def test_contract_not_found(self, client):
        """Test contract not found scenarios"""
        response = client.get('/api/contracts/99999')
        assert response.status_code in [404, 401]
        
        response = client.put('/api/contracts/99999', json={'title': 'Test'})
        assert response.status_code in [404, 401]
        
        response = client.delete('/api/contracts/99999')
        assert response.status_code in [404, 401]

    def test_contract_number_uniqueness(self, client):
        """Test contract number uniqueness"""
        # Create first contract
        first_contract = Contract(
            client_id=self.test_client.id,
            contract_number='CTR-UNIQUE',
            title='First Contract',
            contract_type='hourly',
            start_date=date.today(),
            status='draft'
        )
        db.session.add(first_contract)
        db.session.commit()

        # Try to create contract with same number
        duplicate_data = self.contract_data.copy()
        duplicate_data['contract_number'] = 'CTR-UNIQUE'
        
        response = client.post('/api/contracts', json=duplicate_data)
        assert response.status_code in [400, 409, 401, 403, 201, 200]

    def test_contract_pagination(self, client):
        """Test contract list pagination"""
        # Create multiple contracts
        for i in range(5):
            contract = Contract(
                client_id=self.test_client.id,
                contract_number=f'CTR-PAGE-{i}',
                title=f'Contract {i}',
                contract_type='hourly',
                start_date=date.today(),
                status='draft'
            )
            db.session.add(contract)
        db.session.commit()

        response = client.get('/api/contracts?page=1&per_page=3')
        assert response.status_code in [200, 401]
