"""
Test suite for Clients API endpoints.
Tests CRUD operations, validation, and business logic for client management.
"""

import pytest
from datetime import datetime
from models import Client, User
from extensions import db


class TestClientsAPI:
    """Test suite for Clients API endpoints"""

    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        """Setup test data for each test method"""
        self.app = app
        self.user = test_user
        
        # Create test client data
        self.client_data = {
            'name': 'Test Company Ltd',
            'description': 'A test company for API testing',
            'industry': 'Technology',
            'website': 'https://testcompany.com',
            'email': '<EMAIL>',
            'phone': '+39 ************',
            'address': 'Via Test 123, Milano, Italy',
            'vat_number': '*************',
            'fiscal_code': '****************',
            'status': 'active'
        }

    def test_get_clients_requires_auth(self, client):
        """Test that clients API requires authentication"""
        response = client.get('/api/clients/')
        
        assert response.status_code == 401
        data = response.get_json()
        assert data['success'] is False
        assert 'autenticazione' in data['message'].lower() or 'auth' in data['message'].lower()

    def test_create_client_requires_auth(self, client):
        """Test that client creation requires authentication"""
        response = client.post('/api/clients/', json=self.client_data)
        
        assert response.status_code == 401
        data = response.get_json()
        assert data['success'] is False

    def test_update_client_requires_auth(self, client):
        """Test that client update requires authentication"""
        response = client.put('/api/clients/1', json={'name': 'Updated Name'})
        
        assert response.status_code == 401
        data = response.get_json()
        assert data['success'] is False

    def test_delete_client_requires_auth(self, client):
        """Test that client deletion requires authentication"""
        response = client.delete('/api/clients/1')
        
        assert response.status_code == 401
        data = response.get_json()
        assert data['success'] is False

    def test_client_api_endpoints_exist(self, client):
        """Test that client API endpoints exist and return proper error codes"""
        endpoints = [
            ('GET', '/api/clients/'),
            ('POST', '/api/clients/'),
            ('GET', '/api/clients/1'),
            ('PUT', '/api/clients/1'),
            ('DELETE', '/api/clients/1')
        ]
        
        for method, url in endpoints:
            if method == 'GET':
                response = client.get(url)
            elif method == 'POST':
                response = client.post(url, json=self.client_data)
            elif method == 'PUT':
                response = client.put(url, json={'name': 'Test'})
            elif method == 'DELETE':
                response = client.delete(url)
            
            # Should return 401 (auth required) not 404 (not found)
            assert response.status_code in [401, 403], f"{method} {url} returned {response.status_code}"

    def test_client_data_model_fields(self, client):
        """Test that Client model has expected fields"""
        # Create a client to test model fields
        test_client = Client(
            name='Test Client',
            description='Test description',
            industry='Technology',
            status='active'
        )
        
        # Test that required fields exist
        assert hasattr(test_client, 'name')
        assert hasattr(test_client, 'description')
        assert hasattr(test_client, 'industry')
        assert hasattr(test_client, 'status')
        
        # Test field values
        assert test_client.name == 'Test Client'
        assert test_client.description == 'Test description'
        assert test_client.industry == 'Technology'
        assert test_client.status == 'active'

    def test_client_model_creation(self, client):
        """Test Client model creation and database persistence"""
        # Create and save client
        test_client = Client(
            name='Database Test Client',
            description='Testing database operations',
            industry='Testing',
            status='active'
        )
        
        db.session.add(test_client)
        db.session.commit()
        
        # Verify client was saved
        saved_client = Client.query.filter_by(name='Database Test Client').first()
        assert saved_client is not None
        assert saved_client.name == 'Database Test Client'
        assert saved_client.industry == 'Testing'

    def test_client_model_relationships(self, client):
        """Test Client model relationships if they exist"""
        test_client = Client(name='Relationship Test', status='active')
        db.session.add(test_client)
        db.session.commit()
        
        # Test that client has an ID after saving
        assert test_client.id is not None
        assert isinstance(test_client.id, int)

    def test_client_api_response_format(self, client):
        """Test that client API returns consistent response format"""
        response = client.get('/api/clients/')
        
        assert response.status_code == 401  # Expected for unauthenticated request
        data = response.get_json()
        
        # Test response structure
        assert isinstance(data, dict)
        assert 'success' in data
        assert 'message' in data
        assert data['success'] is False

    def test_client_search_endpoint(self, client):
        """Test client search functionality"""
        # Test search with query parameters
        response = client.get('/api/clients/?search=test')
        assert response.status_code == 401  # Auth required
        
        response = client.get('/api/clients/?status=active')
        assert response.status_code == 401  # Auth required

    def test_client_pagination_endpoint(self, client):
        """Test client pagination functionality"""
        response = client.get('/api/clients/?page=1&per_page=10')
        assert response.status_code == 401  # Auth required

    def test_client_validation_fields(self, client):
        """Test client validation through model constraints"""
        # Test that we can create clients with various field combinations
        clients_data = [
            {'name': 'Client 1', 'status': 'active'},
            {'name': 'Client 2', 'status': 'inactive', 'industry': 'Tech'},
            {'name': 'Client 3', 'status': 'active', 'description': 'Test desc'}
        ]
        
        for client_data in clients_data:
            test_client = Client(**client_data)
            db.session.add(test_client)
        
        db.session.commit()
        
        # Verify all clients were created
        created_clients = Client.query.filter(Client.name.like('Client %')).all()
        assert len(created_clients) == 3
