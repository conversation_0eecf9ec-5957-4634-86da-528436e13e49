"""
Test suite for Clients API endpoints.
Tests CRUD operations, validation, and business logic for client management.
"""

import pytest
from datetime import datetime
from flask import url_for
from models import Client, User
from extensions import db


class TestClientsAPI:
    """Test suite for Clients API endpoints"""

    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        """Setup test data for each test method"""
        self.app = app
        self.user = test_user
        
        # Create test client data
        self.client_data = {
            'name': 'Test Company Ltd',
            'description': 'A test company for API testing',
            'industry': 'Technology',
            'website': 'https://testcompany.com',
            'email': '<EMAIL>',
            'phone': '+39 ************',
            'address': 'Via Test 123, Milano, Italy',
            'vat_number': '*************',
            'fiscal_code': '****************',
            'status': 'active'
        }

    def test_get_clients_success(self, client):
        """Test successful retrieval of clients list"""
        # Create test clients
        client1 = Client(name='Client 1', status='active')
        client2 = Client(name='Client 2', status='active')
        db.session.add_all([client1, client2])
        db.session.commit()

        response = client.get('/api/clients')
        
        assert response.status_code == 200
        data = response.get_json()
        assert data['success'] is True
        assert len(data['data']['clients']) >= 2
        assert any(c['name'] == 'Client 1' for c in data['data']['clients'])

    def test_get_clients_with_filters(self, client):
        """Test clients list with status filter"""
        # Create clients with different statuses
        active_client = Client(name='Active Client', status='active')
        inactive_client = Client(name='Inactive Client', status='inactive')
        db.session.add_all([active_client, inactive_client])
        db.session.commit()

        response = client.get('/api/clients?status=active')
        
        assert response.status_code == 200
        data = response.get_json()
        clients = data['data']['clients']
        assert all(c['status'] == 'active' for c in clients)

    def test_get_client_detail_success(self, client):
        """Test successful retrieval of single client"""
        test_client = Client(**self.client_data)
        db.session.add(test_client)
        db.session.commit()

        response = client.get(f'/api/clients/{test_client.id}')
        
        assert response.status_code == 200
        data = response.get_json()
        assert data['success'] is True
        assert data['data']['client']['name'] == 'Test Company Ltd'
        assert data['data']['client']['email'] == '<EMAIL>'

    def test_get_client_not_found(self, client):
        """Test client not found error"""
        response = client.get('/api/clients/99999')
        
        assert response.status_code == 404
        data = response.get_json()
        assert data['success'] is False

    def test_create_client_success(self, client):
        """Test successful client creation"""
        response = client.post('/api/clients', json=self.client_data)
        
        assert response.status_code in [201, 200]
        data = response.get_json()
        assert data['success'] is True
        
        # Verify client was created in database
        created_client = Client.query.filter_by(name='Test Company Ltd').first()
        assert created_client is not None
        assert created_client.email == '<EMAIL>'

    def test_create_client_validation_error(self, client):
        """Test client creation with missing required fields"""
        invalid_data = {'description': 'Missing name field'}
        
        response = client.post('/api/clients', json=invalid_data)
        
        assert response.status_code in [400, 422]
        data = response.get_json()
        assert data['success'] is False

    def test_create_client_duplicate_name(self, client):
        """Test client creation with duplicate name"""
        # Create first client
        existing_client = Client(name='Duplicate Name', status='active')
        db.session.add(existing_client)
        db.session.commit()

        # Try to create client with same name
        duplicate_data = self.client_data.copy()
        duplicate_data['name'] = 'Duplicate Name'
        
        response = client.post('/api/clients', json=duplicate_data)
        
        assert response.status_code in [400, 409]
        data = response.get_json()
        assert data['success'] is False

    def test_update_client_success(self, client):
        """Test successful client update"""
        test_client = Client(**self.client_data)
        db.session.add(test_client)
        db.session.commit()

        update_data = {
            'name': 'Updated Company Name',
            'email': '<EMAIL>',
            'status': 'inactive'
        }
        
        response = client.put(f'/api/clients/{test_client.id}', json=update_data)
        
        assert response.status_code == 200
        data = response.get_json()
        assert data['success'] is True
        
        # Verify update in database
        updated_client = Client.query.get(test_client.id)
        assert updated_client.name == 'Updated Company Name'
        assert updated_client.email == '<EMAIL>'
        assert updated_client.status == 'inactive'

    def test_update_client_not_found(self, client):
        """Test update of non-existent client"""
        update_data = {'name': 'Updated Name'}
        
        response = client.put('/api/clients/99999', json=update_data)
        
        assert response.status_code == 404
        data = response.get_json()
        assert data['success'] is False

    def test_delete_client_success(self, client):
        """Test successful client deletion"""
        test_client = Client(**self.client_data)
        db.session.add(test_client)
        db.session.commit()
        client_id = test_client.id

        response = client.delete(f'/api/clients/{client_id}')
        
        assert response.status_code == 200
        data = response.get_json()
        assert data['success'] is True
        
        # Verify deletion in database
        deleted_client = Client.query.get(client_id)
        assert deleted_client is None

    def test_delete_client_not_found(self, client):
        """Test deletion of non-existent client"""
        response = client.delete('/api/clients/99999')
        
        assert response.status_code == 404
        data = response.get_json()
        assert data['success'] is False

    def test_client_search(self, client):
        """Test client search functionality"""
        # Create test clients
        client1 = Client(name='Tech Solutions Inc', industry='Technology')
        client2 = Client(name='Marketing Agency', industry='Marketing')
        client3 = Client(name='Tech Innovations', industry='Technology')
        db.session.add_all([client1, client2, client3])
        db.session.commit()

        # Search by name
        response = client.get('/api/clients?search=Tech')
        
        assert response.status_code == 200
        data = response.get_json()
        clients = data['data']['clients']
        assert len(clients) >= 2
        assert all('Tech' in c['name'] for c in clients)

    def test_client_pagination(self, client):
        """Test client list pagination"""
        # Create multiple clients
        for i in range(15):
            test_client = Client(name=f'Client {i}', status='active')
            db.session.add(test_client)
        db.session.commit()

        # Test first page
        response = client.get('/api/clients?page=1&per_page=10')
        
        assert response.status_code == 200
        data = response.get_json()
        assert len(data['data']['clients']) == 10
        assert 'pagination' in data['data']
        assert data['data']['pagination']['page'] == 1

    def test_clients_unauthorized(self, client):
        """Test clients access without authentication"""
        # This test assumes authentication is required
        # Adjust based on your actual authentication setup
        response = client.get('/api/clients')
        
        # Should either work (if no auth required) or return 401
        assert response.status_code in [200, 401]

    def test_client_response_format(self, client):
        """Test that client response includes all expected fields"""
        test_client = Client(**self.client_data)
        db.session.add(test_client)
        db.session.commit()

        response = client.get(f'/api/clients/{test_client.id}')
        
        assert response.status_code == 200
        data = response.get_json()
        client_data = data['data']['client']
        
        # Check required fields are present
        required_fields = ['id', 'name', 'status', 'created_at']
        for field in required_fields:
            assert field in client_data
