"""
Test suite for ForumTopic API endpoints.
Tests CRUD operations, validation, and business logic for forum topic management.
"""

import pytest
from datetime import datetime, date, timedelta
from models import ForumTopic, User
from extensions import db


class TestForumTopicsAPI:
    """Test suite for ForumTopic API endpoints"""

    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        """Setup test data for each test method"""
        self.app = app
        self.user = test_user
        
        # Create test forum topic data
        self.topic_data = {
            'title': 'Test Forum Topic',
            'description': 'This is a test forum topic for API testing',
            'author_id': self.user.id,
            'category': 'general',
            'is_pinned': False,
            'is_locked': False
        }

    def test_get_forum_topics_success(self, client):
        """Test successful retrieval of forum topics list"""
        # Create test topics
        topic1 = ForumTopic(
            title='Topic 1',
            description='First test topic',
            author_id=self.user.id,
            category='general'
        )
        topic2 = ForumTopic(
            title='Topic 2',
            description='Second test topic',
            author_id=self.user.id,
            category='announcements'
        )
        db.session.add_all([topic1, topic2])
        db.session.commit()

        response = client.get('/api/forum/topics')
        
        assert response.status_code in [200, 401]
        if response.status_code == 200:
            data = response.get_json()
            assert 'topics' in str(data).lower() or 'data' in data

    def test_create_forum_topic_success(self, client):
        """Test successful forum topic creation"""
        response = client.post('/api/forum/topics', json=self.topic_data)
        
        assert response.status_code in [201, 200, 401, 403]
        if response.status_code in [200, 201]:
            # Verify topic was created
            created_topic = ForumTopic.query.filter_by(title='Test Forum Topic').first()
            if created_topic:
                assert created_topic.author_id == self.user.id
                assert created_topic.category == 'general'

    def test_create_forum_topic_validation_error(self, client):
        """Test forum topic creation with missing required fields"""
        invalid_data = {'description': 'Missing title and author_id'}
        
        response = client.post('/api/forum/topics', json=invalid_data)
        
        assert response.status_code in [400, 422, 401, 403]

    def test_update_forum_topic_success(self, client):
        """Test successful forum topic update"""
        test_topic = ForumTopic(
            title='Original Title',
            description='Original description',
            author_id=self.user.id,
            category='general'
        )
        db.session.add(test_topic)
        db.session.commit()

        update_data = {
            'title': 'Updated Forum Topic Title',
            'description': 'Updated description',
            'category': 'announcements'
        }
        
        response = client.put(f'/api/forum/topics/{test_topic.id}', json=update_data)
        
        assert response.status_code in [200, 401, 403, 404]

    def test_delete_forum_topic_success(self, client):
        """Test successful forum topic deletion"""
        test_topic = ForumTopic(
            title='To Delete',
            description='This topic will be deleted',
            author_id=self.user.id,
            category='general'
        )
        db.session.add(test_topic)
        db.session.commit()
        topic_id = test_topic.id

        response = client.delete(f'/api/forum/topics/{topic_id}')
        
        assert response.status_code in [200, 401, 403, 404]

    def test_forum_topic_pin_unpin(self, client):
        """Test forum topic pin/unpin functionality"""
        test_topic = ForumTopic(
            title='Pin Test Topic',
            description='Topic for testing pin functionality',
            author_id=self.user.id,
            category='general',
            is_pinned=False
        )
        db.session.add(test_topic)
        db.session.commit()

        # Test pin
        response = client.put(f'/api/forum/topics/{test_topic.id}/pin')
        assert response.status_code in [200, 401, 403, 404]
        
        # Test unpin
        response = client.put(f'/api/forum/topics/{test_topic.id}/unpin')
        assert response.status_code in [200, 401, 403, 404]

    def test_forum_topic_lock_unlock(self, client):
        """Test forum topic lock/unlock functionality"""
        test_topic = ForumTopic(
            title='Lock Test Topic',
            description='Topic for testing lock functionality',
            author_id=self.user.id,
            category='general',
            is_locked=False
        )
        db.session.add(test_topic)
        db.session.commit()

        # Test lock
        response = client.put(f'/api/forum/topics/{test_topic.id}/lock')
        assert response.status_code in [200, 401, 403, 404]
        
        # Test unlock
        response = client.put(f'/api/forum/topics/{test_topic.id}/unlock')
        assert response.status_code in [200, 401, 403, 404]

    def test_forum_topic_search_and_filters(self, client):
        """Test forum topic search and filtering"""
        # Create test topics with different categories
        topic1 = ForumTopic(
            title='General Discussion Topic',
            description='A general discussion',
            author_id=self.user.id,
            category='general'
        )
        topic2 = ForumTopic(
            title='Important Announcement',
            description='An important announcement',
            author_id=self.user.id,
            category='announcements',
            is_pinned=True
        )
        db.session.add_all([topic1, topic2])
        db.session.commit()

        # Test category filter
        response = client.get('/api/forum/topics?category=general')
        assert response.status_code in [200, 401]
        
        # Test search by title
        response = client.get('/api/forum/topics?search=Discussion')
        assert response.status_code in [200, 401]
        
        # Test pinned filter
        response = client.get('/api/forum/topics?is_pinned=true')
        assert response.status_code in [200, 401]

    def test_forum_topic_categories(self, client):
        """Test forum topic category validation"""
        valid_categories = ['general', 'announcements', 'help', 'feedback', 'off-topic']
        
        for category in valid_categories:
            test_data = self.topic_data.copy()
            test_data['category'] = category
            test_data['title'] = f'Topic in {category}'
            
            response = client.post('/api/forum/topics', json=test_data)
            assert response.status_code in [201, 200, 401, 403, 400, 422]

    def test_forum_topic_view_count(self, client):
        """Test forum topic view count increment"""
        test_topic = ForumTopic(
            title='View Count Test',
            description='Topic for testing view count',
            author_id=self.user.id,
            category='general',
            view_count=0
        )
        db.session.add(test_topic)
        db.session.commit()

        # View the topic (should increment view count)
        response = client.get(f'/api/forum/topics/{test_topic.id}')
        assert response.status_code in [200, 401, 404]

    def test_get_forum_topic_detail(self, client):
        """Test single forum topic retrieval"""
        test_topic = ForumTopic(
            title='Detail Test Topic',
            description='Topic for testing detail view',
            author_id=self.user.id,
            category='general',
            view_count=5,
            reply_count=3
        )
        db.session.add(test_topic)
        db.session.commit()

        response = client.get(f'/api/forum/topics/{test_topic.id}')
        assert response.status_code in [200, 401, 404]

    def test_forum_topic_not_found(self, client):
        """Test forum topic not found scenarios"""
        response = client.get('/api/forum/topics/99999')
        assert response.status_code in [404, 401]
        
        response = client.put('/api/forum/topics/99999', json={'title': 'Test'})
        assert response.status_code in [404, 401]
        
        response = client.delete('/api/forum/topics/99999')
        assert response.status_code in [404, 401]

    def test_forum_topic_author_permissions(self, client):
        """Test forum topic author permissions"""
        # Create topic by another user (simulated)
        other_topic = ForumTopic(
            title='Other User Topic',
            description='Topic by another user',
            author_id=999,  # Different user ID
            category='general'
        )
        db.session.add(other_topic)
        db.session.commit()

        # Try to update topic by different user
        update_data = {'title': 'Unauthorized Update'}
        response = client.put(f'/api/forum/topics/{other_topic.id}', json=update_data)
        assert response.status_code in [403, 401, 404]
        
        # Try to delete topic by different user
        response = client.delete(f'/api/forum/topics/{other_topic.id}')
        assert response.status_code in [403, 401, 404]

    def test_forum_topic_pagination(self, client):
        """Test forum topic list pagination"""
        # Create multiple topics
        for i in range(5):
            topic = ForumTopic(
                title=f'Topic {i}',
                description=f'Description for topic {i}',
                author_id=self.user.id,
                category='general'
            )
            db.session.add(topic)
        db.session.commit()

        response = client.get('/api/forum/topics?page=1&per_page=3')
        assert response.status_code in [200, 401]

    def test_forum_topic_sorting(self, client):
        """Test forum topic sorting options"""
        # Create topics with different timestamps
        topic1 = ForumTopic(
            title='Older Topic',
            description='An older topic',
            author_id=self.user.id,
            category='general'
        )
        topic2 = ForumTopic(
            title='Newer Topic',
            description='A newer topic',
            author_id=self.user.id,
            category='general'
        )
        db.session.add_all([topic1, topic2])
        db.session.commit()

        # Test sorting by creation date
        response = client.get('/api/forum/topics?sort=created_at&order=desc')
        assert response.status_code in [200, 401]
        
        # Test sorting by view count
        response = client.get('/api/forum/topics?sort=view_count&order=desc')
        assert response.status_code in [200, 401]

    def test_forum_topic_title_validation(self, client):
        """Test forum topic title validation"""
        # Test empty title
        invalid_data = self.topic_data.copy()
        invalid_data['title'] = ''
        
        response = client.post('/api/forum/topics', json=invalid_data)
        assert response.status_code in [400, 422, 401, 403]
        
        # Test very long title
        invalid_data['title'] = 'x' * 300  # Assuming max length is 200
        response = client.post('/api/forum/topics', json=invalid_data)
        assert response.status_code in [400, 422, 401, 403, 201, 200]
