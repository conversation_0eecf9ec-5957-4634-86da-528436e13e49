"""
Test suite for PerformanceReview API endpoints.
Tests CRUD operations, validation, and business logic for performance review management.
"""

import pytest
from datetime import datetime, date, timedelta
from models import PerformanceReview, User
from extensions import db


class TestPerformanceReviewsAPI:
    """Test suite for PerformanceReview API endpoints"""

    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        """Setup test data for each test method"""
        self.app = app
        self.user = test_user
        
        # Create test performance review data
        self.review_data = {
            'user_id': self.user.id,
            'reviewer_id': self.user.id,  # Self-review for testing
            'review_period_start': (date.today() - timedelta(days=365)).isoformat(),
            'review_period_end': date.today().isoformat(),
            'status': 'draft',
            'due_date': (date.today() + timedelta(days=30)).isoformat(),
            'goals': 'Improve technical skills and team collaboration',
            'achievements': 'Successfully completed 3 major projects',
            'areas_for_improvement': 'Time management and communication',
            'overall_rating': 4.0,
            'notes': 'Strong performer with potential for growth'
        }

    def test_get_performance_reviews_success(self, client):
        """Test successful retrieval of performance reviews list"""
        # Create test reviews
        review1 = PerformanceReview(
            user_id=self.user.id,
            reviewer_id=self.user.id,
            review_period_start=date.today() - timedelta(days=365),
            review_period_end=date.today() - timedelta(days=1),
            status='completed',
            overall_rating=4.5
        )
        review2 = PerformanceReview(
            user_id=self.user.id,
            reviewer_id=self.user.id,
            review_period_start=date.today() - timedelta(days=180),
            review_period_end=date.today(),
            status='draft',
            overall_rating=4.0
        )
        db.session.add_all([review1, review2])
        db.session.commit()

        response = client.get('/api/performance/reviews')
        
        assert response.status_code in [200, 401]
        if response.status_code == 200:
            data = response.get_json()
            assert 'reviews' in str(data).lower() or 'data' in data

    def test_create_performance_review_success(self, client):
        """Test successful performance review creation"""
        response = client.post('/api/performance/reviews', json=self.review_data)
        
        assert response.status_code in [201, 200, 401, 403]
        if response.status_code in [200, 201]:
            # Verify review was created
            created_review = PerformanceReview.query.filter_by(
                user_id=self.user.id,
                status='draft'
            ).first()
            if created_review:
                assert created_review.overall_rating == 4.0

    def test_create_performance_review_validation_error(self, client):
        """Test performance review creation with missing required fields"""
        invalid_data = {'status': 'draft'}  # Missing required fields
        
        response = client.post('/api/performance/reviews', json=invalid_data)
        
        assert response.status_code in [400, 422, 401, 403]

    def test_update_performance_review_success(self, client):
        """Test successful performance review update"""
        test_review = PerformanceReview(
            user_id=self.user.id,
            reviewer_id=self.user.id,
            review_period_start=date.today() - timedelta(days=365),
            review_period_end=date.today(),
            status='draft',
            overall_rating=3.5
        )
        db.session.add(test_review)
        db.session.commit()

        update_data = {
            'overall_rating': 4.5,
            'achievements': 'Updated achievements list',
            'status': 'in_progress'
        }
        
        response = client.put(f'/api/performance/reviews/{test_review.id}', json=update_data)
        
        assert response.status_code in [200, 401, 403, 404]

    def test_delete_performance_review_success(self, client):
        """Test successful performance review deletion"""
        test_review = PerformanceReview(
            user_id=self.user.id,
            reviewer_id=self.user.id,
            review_period_start=date.today() - timedelta(days=180),
            review_period_end=date.today(),
            status='draft',
            overall_rating=3.0
        )
        db.session.add(test_review)
        db.session.commit()
        review_id = test_review.id

        response = client.delete(f'/api/performance/reviews/{review_id}')
        
        assert response.status_code in [200, 401, 403, 404]

    def test_performance_review_date_validation(self, client):
        """Test performance review date validation"""
        # Test review_period_end before review_period_start
        invalid_data = self.review_data.copy()
        invalid_data['review_period_start'] = date.today().isoformat()
        invalid_data['review_period_end'] = (date.today() - timedelta(days=1)).isoformat()
        
        response = client.post('/api/performance/reviews', json=invalid_data)
        assert response.status_code in [400, 422, 401, 403, 201, 200]
        
        # Test due_date in the past
        invalid_data = self.review_data.copy()
        invalid_data['due_date'] = (date.today() - timedelta(days=1)).isoformat()
        
        response = client.post('/api/performance/reviews', json=invalid_data)
        assert response.status_code in [400, 422, 401, 403, 201, 200]

    def test_performance_review_rating_validation(self, client):
        """Test performance review rating validation"""
        # Test rating below minimum
        invalid_data = self.review_data.copy()
        invalid_data['overall_rating'] = 0.5
        
        response = client.post('/api/performance/reviews', json=invalid_data)
        assert response.status_code in [400, 422, 401, 403, 201, 200]
        
        # Test rating above maximum
        invalid_data['overall_rating'] = 5.5
        response = client.post('/api/performance/reviews', json=invalid_data)
        assert response.status_code in [400, 422, 401, 403, 201, 200]

    def test_performance_review_status_workflow(self, client):
        """Test performance review status transitions"""
        test_review = PerformanceReview(
            user_id=self.user.id,
            reviewer_id=self.user.id,
            review_period_start=date.today() - timedelta(days=365),
            review_period_end=date.today(),
            status='draft',
            overall_rating=4.0
        )
        db.session.add(test_review)
        db.session.commit()

        # Test status transitions
        status_transitions = ['in_progress', 'completed', 'approved']
        
        for status in status_transitions:
            response = client.put(
                f'/api/performance/reviews/{test_review.id}', 
                json={'status': status}
            )
            assert response.status_code in [200, 401, 403, 404, 400]

    def test_submit_performance_review(self, client):
        """Test performance review submission"""
        test_review = PerformanceReview(
            user_id=self.user.id,
            reviewer_id=self.user.id,
            review_period_start=date.today() - timedelta(days=365),
            review_period_end=date.today(),
            status='draft',
            overall_rating=4.0
        )
        db.session.add(test_review)
        db.session.commit()

        response = client.put(f'/api/performance/reviews/{test_review.id}/submit')
        assert response.status_code in [200, 401, 403, 404]

    def test_approve_performance_review(self, client):
        """Test performance review approval"""
        test_review = PerformanceReview(
            user_id=self.user.id,
            reviewer_id=self.user.id,
            review_period_start=date.today() - timedelta(days=365),
            review_period_end=date.today(),
            status='completed',
            overall_rating=4.0
        )
        db.session.add(test_review)
        db.session.commit()

        response = client.put(f'/api/performance/reviews/{test_review.id}/approve')
        assert response.status_code in [200, 401, 403, 404]

    def test_performance_review_search_and_filters(self, client):
        """Test performance review search and filtering"""
        # Create test reviews with different statuses and ratings
        review1 = PerformanceReview(
            user_id=self.user.id,
            reviewer_id=self.user.id,
            review_period_start=date.today() - timedelta(days=365),
            review_period_end=date.today() - timedelta(days=1),
            status='completed',
            overall_rating=4.5
        )
        review2 = PerformanceReview(
            user_id=self.user.id,
            reviewer_id=self.user.id,
            review_period_start=date.today() - timedelta(days=180),
            review_period_end=date.today(),
            status='draft',
            overall_rating=3.5
        )
        db.session.add_all([review1, review2])
        db.session.commit()

        # Test status filter
        response = client.get('/api/performance/reviews?status=completed')
        assert response.status_code in [200, 401]
        
        # Test user filter
        response = client.get(f'/api/performance/reviews?user_id={self.user.id}')
        assert response.status_code in [200, 401]
        
        # Test rating filter
        response = client.get('/api/performance/reviews?min_rating=4.0')
        assert response.status_code in [200, 401]

    def test_get_performance_review_detail(self, client):
        """Test single performance review retrieval"""
        test_review = PerformanceReview(
            user_id=self.user.id,
            reviewer_id=self.user.id,
            review_period_start=date.today() - timedelta(days=365),
            review_period_end=date.today(),
            status='completed',
            overall_rating=4.2,
            goals='Test goals',
            achievements='Test achievements'
        )
        db.session.add(test_review)
        db.session.commit()

        response = client.get(f'/api/performance/reviews/{test_review.id}')
        assert response.status_code in [200, 401, 404]

    def test_performance_review_not_found(self, client):
        """Test performance review not found scenarios"""
        response = client.get('/api/performance/reviews/99999')
        assert response.status_code in [404, 401]
        
        response = client.put('/api/performance/reviews/99999', json={'overall_rating': 4.0})
        assert response.status_code in [404, 401]
        
        response = client.delete('/api/performance/reviews/99999')
        assert response.status_code in [404, 401]

    def test_get_user_performance_reviews(self, client):
        """Test retrieval of performance reviews for specific user"""
        # Create reviews for the user
        review1 = PerformanceReview(
            user_id=self.user.id,
            reviewer_id=self.user.id,
            review_period_start=date.today() - timedelta(days=365),
            review_period_end=date.today() - timedelta(days=1),
            status='completed',
            overall_rating=4.0
        )
        db.session.add(review1)
        db.session.commit()

        response = client.get(f'/api/performance/users/{self.user.id}/reviews')
        assert response.status_code in [200, 401, 404]

    def test_performance_review_pagination(self, client):
        """Test performance review list pagination"""
        # Create multiple reviews
        for i in range(5):
            review = PerformanceReview(
                user_id=self.user.id,
                reviewer_id=self.user.id,
                review_period_start=date.today() - timedelta(days=365 + i * 30),
                review_period_end=date.today() - timedelta(days=i * 30),
                status='completed',
                overall_rating=3.0 + i * 0.2
            )
            db.session.add(review)
        db.session.commit()

        response = client.get('/api/performance/reviews?page=1&per_page=3')
        assert response.status_code in [200, 401]
