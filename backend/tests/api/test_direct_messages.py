"""
Test suite for DirectMessage API endpoints.
Tests CRUD operations, validation, and business logic for direct message management.
"""

import pytest
from datetime import datetime, date, timedelta
from models import DirectMessage, User
from extensions import db


class TestDirectMessagesAPI:
    """Test suite for DirectMessage API endpoints"""

    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        """Setup test data for each test method"""
        self.app = app
        self.user = test_user
        
        # Create a second user for messaging
        self.recipient_user = User(
            username='recipient_user',
            email='<EMAIL>',
            first_name='Recipient',
            last_name='User'
        )
        self.recipient_user.set_password('password123')
        db.session.add(self.recipient_user)
        db.session.commit()
        
        # Create test direct message data
        self.message_data = {
            'sender_id': self.user.id,
            'recipient_id': self.recipient_user.id,
            'message': 'This is a test direct message for API testing',
            'is_read': False
        }

    def test_get_direct_messages_success(self, client):
        """Test successful retrieval of direct messages list"""
        # Create test messages
        message1 = DirectMessage(
            sender_id=self.user.id,
            recipient_id=self.recipient_user.id,
            message='First test message',
            is_read=False
        )
        message2 = DirectMessage(
            sender_id=self.recipient_user.id,
            recipient_id=self.user.id,
            message='Reply message',
            is_read=True
        )
        db.session.add_all([message1, message2])
        db.session.commit()

        response = client.get('/api/messages')
        
        assert response.status_code in [200, 401]
        if response.status_code == 200:
            data = response.get_json()
            assert 'messages' in str(data).lower() or 'data' in data

    def test_send_direct_message_success(self, client):
        """Test successful direct message sending"""
        response = client.post('/api/messages', json=self.message_data)
        
        assert response.status_code in [201, 200, 401, 403]
        if response.status_code in [200, 201]:
            # Verify message was created
            created_message = DirectMessage.query.filter_by(
                sender_id=self.user.id,
                recipient_id=self.recipient_user.id
            ).first()
            if created_message:
                assert created_message.message == 'This is a test direct message for API testing'
                assert created_message.is_read is False

    def test_send_direct_message_validation_error(self, client):
        """Test direct message sending with missing required fields"""
        invalid_data = {'message': 'Missing sender and recipient'}
        
        response = client.post('/api/messages', json=invalid_data)
        
        assert response.status_code in [400, 422, 401, 403]

    def test_send_message_to_self(self, client):
        """Test sending message to self (should be prevented)"""
        invalid_data = self.message_data.copy()
        invalid_data['recipient_id'] = self.user.id  # Same as sender
        
        response = client.post('/api/messages', json=invalid_data)
        
        assert response.status_code in [400, 422, 401, 403, 201, 200]

    def test_mark_message_as_read(self, client):
        """Test marking a direct message as read"""
        test_message = DirectMessage(
            sender_id=self.recipient_user.id,
            recipient_id=self.user.id,
            message='Unread message',
            is_read=False
        )
        db.session.add(test_message)
        db.session.commit()

        response = client.put(f'/api/messages/{test_message.id}/read')
        
        assert response.status_code in [200, 401, 403, 404]

    def test_mark_message_as_unread(self, client):
        """Test marking a direct message as unread"""
        test_message = DirectMessage(
            sender_id=self.recipient_user.id,
            recipient_id=self.user.id,
            message='Read message',
            is_read=True,
            read_at=datetime.utcnow()
        )
        db.session.add(test_message)
        db.session.commit()

        response = client.put(f'/api/messages/{test_message.id}/unread')
        
        assert response.status_code in [200, 401, 403, 404]

    def test_delete_direct_message_success(self, client):
        """Test successful direct message deletion"""
        test_message = DirectMessage(
            sender_id=self.user.id,
            recipient_id=self.recipient_user.id,
            message='Message to delete',
            is_read=False
        )
        db.session.add(test_message)
        db.session.commit()
        message_id = test_message.id

        response = client.delete(f'/api/messages/{message_id}')
        
        assert response.status_code in [200, 401, 403, 404]

    def test_get_conversation_messages(self, client):
        """Test retrieval of conversation between two users"""
        # Create conversation messages
        message1 = DirectMessage(
            sender_id=self.user.id,
            recipient_id=self.recipient_user.id,
            message='Hello there!',
            is_read=True
        )
        message2 = DirectMessage(
            sender_id=self.recipient_user.id,
            recipient_id=self.user.id,
            message='Hi! How are you?',
            is_read=False
        )
        message3 = DirectMessage(
            sender_id=self.user.id,
            recipient_id=self.recipient_user.id,
            message='I am doing well, thanks!',
            is_read=False
        )
        db.session.add_all([message1, message2, message3])
        db.session.commit()

        response = client.get(f'/api/messages/conversation/{self.recipient_user.id}')
        
        assert response.status_code in [200, 401, 404]

    def test_get_unread_messages_count(self, client):
        """Test retrieval of unread messages count"""
        # Create unread messages
        message1 = DirectMessage(
            sender_id=self.recipient_user.id,
            recipient_id=self.user.id,
            message='Unread message 1',
            is_read=False
        )
        message2 = DirectMessage(
            sender_id=self.recipient_user.id,
            recipient_id=self.user.id,
            message='Unread message 2',
            is_read=False
        )
        db.session.add_all([message1, message2])
        db.session.commit()

        response = client.get('/api/messages/unread/count')
        
        assert response.status_code in [200, 401]

    def test_get_message_detail(self, client):
        """Test single direct message retrieval"""
        test_message = DirectMessage(
            sender_id=self.user.id,
            recipient_id=self.recipient_user.id,
            message='Detailed message content',
            is_read=False
        )
        db.session.add(test_message)
        db.session.commit()

        response = client.get(f'/api/messages/{test_message.id}')
        
        assert response.status_code in [200, 401, 404]

    def test_direct_message_not_found(self, client):
        """Test direct message not found scenarios"""
        response = client.get('/api/messages/99999')
        assert response.status_code in [404, 401]
        
        response = client.put('/api/messages/99999/read')
        assert response.status_code in [404, 401]
        
        response = client.delete('/api/messages/99999')
        assert response.status_code in [404, 401]

    def test_message_search_and_filters(self, client):
        """Test direct message search and filtering"""
        # Create test messages
        message1 = DirectMessage(
            sender_id=self.user.id,
            recipient_id=self.recipient_user.id,
            message='Important project update',
            is_read=True
        )
        message2 = DirectMessage(
            sender_id=self.recipient_user.id,
            recipient_id=self.user.id,
            message='Meeting reminder for tomorrow',
            is_read=False
        )
        db.session.add_all([message1, message2])
        db.session.commit()

        # Test search by content
        response = client.get('/api/messages?search=project')
        assert response.status_code in [200, 401]
        
        # Test filter by read status
        response = client.get('/api/messages?is_read=false')
        assert response.status_code in [200, 401]
        
        # Test filter by sender
        response = client.get(f'/api/messages?sender_id={self.user.id}')
        assert response.status_code in [200, 401]

    def test_message_permissions(self, client):
        """Test message access permissions"""
        # Create message between other users (simulated)
        other_message = DirectMessage(
            sender_id=999,  # Different user
            recipient_id=998,  # Different user
            message='Private message between others',
            is_read=False
        )
        db.session.add(other_message)
        db.session.commit()

        # Try to access message not involving current user
        response = client.get(f'/api/messages/{other_message.id}')
        assert response.status_code in [403, 401, 404]
        
        # Try to mark as read
        response = client.put(f'/api/messages/{other_message.id}/read')
        assert response.status_code in [403, 401, 404]

    def test_message_pagination(self, client):
        """Test direct message list pagination"""
        # Create multiple messages
        for i in range(5):
            message = DirectMessage(
                sender_id=self.user.id if i % 2 == 0 else self.recipient_user.id,
                recipient_id=self.recipient_user.id if i % 2 == 0 else self.user.id,
                message=f'Message {i}',
                is_read=i % 3 == 0
            )
            db.session.add(message)
        db.session.commit()

        response = client.get('/api/messages?page=1&per_page=3')
        assert response.status_code in [200, 401]

    def test_message_content_validation(self, client):
        """Test direct message content validation"""
        # Test empty message
        invalid_data = self.message_data.copy()
        invalid_data['message'] = ''
        
        response = client.post('/api/messages', json=invalid_data)
        assert response.status_code in [400, 422, 401, 403]
        
        # Test very long message
        invalid_data['message'] = 'x' * 5000  # Assuming max length limit
        response = client.post('/api/messages', json=invalid_data)
        assert response.status_code in [400, 422, 401, 403, 201, 200]

    def test_bulk_mark_as_read(self, client):
        """Test bulk marking messages as read"""
        # Create multiple unread messages
        message_ids = []
        for i in range(3):
            message = DirectMessage(
                sender_id=self.recipient_user.id,
                recipient_id=self.user.id,
                message=f'Bulk read test {i}',
                is_read=False
            )
            db.session.add(message)
            db.session.flush()
            message_ids.append(message.id)
        db.session.commit()

        bulk_data = {'message_ids': message_ids}
        response = client.put('/api/messages/bulk/read', json=bulk_data)
        
        assert response.status_code in [200, 401, 403, 400]

    def test_get_recent_conversations(self, client):
        """Test retrieval of recent conversations"""
        # Create messages with different users to form conversations
        message1 = DirectMessage(
            sender_id=self.user.id,
            recipient_id=self.recipient_user.id,
            message='Recent conversation',
            is_read=True
        )
        db.session.add(message1)
        db.session.commit()

        response = client.get('/api/messages/conversations/recent')
        assert response.status_code in [200, 401]
