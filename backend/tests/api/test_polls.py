"""
Test suite for Poll API endpoints.
Tests CRUD operations, validation, and business logic for poll management.
"""

import pytest
from datetime import datetime, date, timedelta
from models import Poll, User
from extensions import db


class TestPollsAPI:
    """Test suite for Poll API endpoints"""

    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        """Setup test data for each test method"""
        self.app = app
        self.user = test_user
        
        # Create test poll data
        self.poll_data = {
            'title': 'Test Poll Question',
            'description': 'This is a test poll for API testing',
            'author_id': self.user.id,
            'is_anonymous': False,
            'multiple_choice': False,
            'expires_at': (datetime.utcnow() + timedelta(days=7)).isoformat(),
            'is_active': True
        }

    def test_get_polls_success(self, client):
        """Test successful retrieval of polls list"""
        # Create test polls
        poll1 = Poll(
            title='Poll 1',
            description='First test poll',
            author_id=self.user.id,
            is_active=True
        )
        poll2 = Poll(
            title='Poll 2',
            description='Second test poll',
            author_id=self.user.id,
            is_active=True
        )
        db.session.add_all([poll1, poll2])
        db.session.commit()

        response = client.get('/api/polls')
        
        assert response.status_code in [200, 401]
        if response.status_code == 200:
            data = response.get_json()
            assert 'polls' in str(data).lower() or 'data' in data

    def test_create_poll_success(self, client):
        """Test successful poll creation"""
        response = client.post('/api/polls', json=self.poll_data)
        
        assert response.status_code in [201, 200, 401, 403]
        if response.status_code in [200, 201]:
            # Verify poll was created
            created_poll = Poll.query.filter_by(title='Test Poll Question').first()
            if created_poll:
                assert created_poll.author_id == self.user.id
                assert created_poll.is_anonymous is False

    def test_create_poll_validation_error(self, client):
        """Test poll creation with missing required fields"""
        invalid_data = {'description': 'Missing title and author_id'}
        
        response = client.post('/api/polls', json=invalid_data)
        
        assert response.status_code in [400, 422, 401, 403]

    def test_update_poll_success(self, client):
        """Test successful poll update"""
        test_poll = Poll(
            title='Original Poll Title',
            description='Original description',
            author_id=self.user.id,
            is_active=True
        )
        db.session.add(test_poll)
        db.session.commit()

        update_data = {
            'title': 'Updated Poll Title',
            'description': 'Updated description',
            'multiple_choice': True
        }
        
        response = client.put(f'/api/polls/{test_poll.id}', json=update_data)
        
        assert response.status_code in [200, 401, 403, 404]

    def test_delete_poll_success(self, client):
        """Test successful poll deletion"""
        test_poll = Poll(
            title='Poll to Delete',
            description='This poll will be deleted',
            author_id=self.user.id,
            is_active=True
        )
        db.session.add(test_poll)
        db.session.commit()
        poll_id = test_poll.id

        response = client.delete(f'/api/polls/{poll_id}')
        
        assert response.status_code in [200, 401, 403, 404]

    def test_poll_activate_deactivate(self, client):
        """Test poll activation/deactivation"""
        test_poll = Poll(
            title='Activation Test Poll',
            description='Poll for testing activation',
            author_id=self.user.id,
            is_active=True
        )
        db.session.add(test_poll)
        db.session.commit()

        # Test deactivate
        response = client.put(f'/api/polls/{test_poll.id}/deactivate')
        assert response.status_code in [200, 401, 403, 404]
        
        # Test activate
        response = client.put(f'/api/polls/{test_poll.id}/activate')
        assert response.status_code in [200, 401, 403, 404]

    def test_poll_expiration_validation(self, client):
        """Test poll expiration date validation"""
        # Test past expiration date
        invalid_data = self.poll_data.copy()
        invalid_data['expires_at'] = (datetime.utcnow() - timedelta(days=1)).isoformat()
        
        response = client.post('/api/polls', json=invalid_data)
        assert response.status_code in [400, 422, 401, 403, 201, 200]

    def test_poll_voting_options(self, client):
        """Test poll with voting options"""
        poll_with_options = self.poll_data.copy()
        poll_with_options['options'] = [
            {'text': 'Option 1', 'order': 1},
            {'text': 'Option 2', 'order': 2},
            {'text': 'Option 3', 'order': 3}
        ]
        
        response = client.post('/api/polls', json=poll_with_options)
        assert response.status_code in [201, 200, 401, 403, 400, 422]

    def test_vote_on_poll(self, client):
        """Test voting on a poll"""
        test_poll = Poll(
            title='Voting Test Poll',
            description='Poll for testing voting',
            author_id=self.user.id,
            is_active=True,
            multiple_choice=False
        )
        db.session.add(test_poll)
        db.session.commit()

        vote_data = {
            'option_id': 1,  # Assuming option exists
            'user_id': self.user.id
        }
        
        response = client.post(f'/api/polls/{test_poll.id}/vote', json=vote_data)
        assert response.status_code in [200, 201, 401, 403, 404, 400]

    def test_poll_results(self, client):
        """Test poll results retrieval"""
        test_poll = Poll(
            title='Results Test Poll',
            description='Poll for testing results',
            author_id=self.user.id,
            is_active=True,
            total_votes=5
        )
        db.session.add(test_poll)
        db.session.commit()

        response = client.get(f'/api/polls/{test_poll.id}/results')
        assert response.status_code in [200, 401, 404]

    def test_poll_search_and_filters(self, client):
        """Test poll search and filtering"""
        # Create test polls with different statuses
        poll1 = Poll(
            title='Active Poll',
            description='An active poll',
            author_id=self.user.id,
            is_active=True
        )
        poll2 = Poll(
            title='Inactive Poll',
            description='An inactive poll',
            author_id=self.user.id,
            is_active=False
        )
        poll3 = Poll(
            title='Anonymous Poll',
            description='An anonymous poll',
            author_id=self.user.id,
            is_active=True,
            is_anonymous=True
        )
        db.session.add_all([poll1, poll2, poll3])
        db.session.commit()

        # Test active filter
        response = client.get('/api/polls?is_active=true')
        assert response.status_code in [200, 401]
        
        # Test search by title
        response = client.get('/api/polls?search=Active')
        assert response.status_code in [200, 401]
        
        # Test anonymous filter
        response = client.get('/api/polls?is_anonymous=true')
        assert response.status_code in [200, 401]

    def test_poll_multiple_choice_validation(self, client):
        """Test poll multiple choice settings"""
        # Test single choice poll
        single_choice_data = self.poll_data.copy()
        single_choice_data['multiple_choice'] = False
        
        response = client.post('/api/polls', json=single_choice_data)
        assert response.status_code in [201, 200, 401, 403, 400, 422]
        
        # Test multiple choice poll
        multiple_choice_data = self.poll_data.copy()
        multiple_choice_data['multiple_choice'] = True
        multiple_choice_data['title'] = 'Multiple Choice Poll'
        
        response = client.post('/api/polls', json=multiple_choice_data)
        assert response.status_code in [201, 200, 401, 403, 400, 422]

    def test_get_poll_detail(self, client):
        """Test single poll retrieval"""
        test_poll = Poll(
            title='Detail Test Poll',
            description='Poll for testing detail view',
            author_id=self.user.id,
            is_active=True,
            total_votes=10,
            is_anonymous=False
        )
        db.session.add(test_poll)
        db.session.commit()

        response = client.get(f'/api/polls/{test_poll.id}')
        assert response.status_code in [200, 401, 404]

    def test_poll_not_found(self, client):
        """Test poll not found scenarios"""
        response = client.get('/api/polls/99999')
        assert response.status_code in [404, 401]
        
        response = client.put('/api/polls/99999', json={'title': 'Test'})
        assert response.status_code in [404, 401]
        
        response = client.delete('/api/polls/99999')
        assert response.status_code in [404, 401]

    def test_poll_author_permissions(self, client):
        """Test poll author permissions"""
        # Create poll by another user (simulated)
        other_poll = Poll(
            title='Other User Poll',
            description='Poll by another user',
            author_id=999,  # Different user ID
            is_active=True
        )
        db.session.add(other_poll)
        db.session.commit()

        # Try to update poll by different user
        update_data = {'title': 'Unauthorized Update'}
        response = client.put(f'/api/polls/{other_poll.id}', json=update_data)
        assert response.status_code in [403, 401, 404]
        
        # Try to delete poll by different user
        response = client.delete(f'/api/polls/{other_poll.id}')
        assert response.status_code in [403, 401, 404]

    def test_poll_pagination(self, client):
        """Test poll list pagination"""
        # Create multiple polls
        for i in range(5):
            poll = Poll(
                title=f'Poll {i}',
                description=f'Description for poll {i}',
                author_id=self.user.id,
                is_active=True
            )
            db.session.add(poll)
        db.session.commit()

        response = client.get('/api/polls?page=1&per_page=3')
        assert response.status_code in [200, 401]

    def test_poll_anonymous_settings(self, client):
        """Test poll anonymous voting settings"""
        # Test anonymous poll
        anonymous_data = self.poll_data.copy()
        anonymous_data['is_anonymous'] = True
        anonymous_data['title'] = 'Anonymous Poll'
        
        response = client.post('/api/polls', json=anonymous_data)
        assert response.status_code in [201, 200, 401, 403, 400, 422]
        
        # Test non-anonymous poll
        public_data = self.poll_data.copy()
        public_data['is_anonymous'] = False
        public_data['title'] = 'Public Poll'
        
        response = client.post('/api/polls', json=public_data)
        assert response.status_code in [201, 200, 401, 403, 400, 422]

    def test_poll_title_validation(self, client):
        """Test poll title validation"""
        # Test empty title
        invalid_data = self.poll_data.copy()
        invalid_data['title'] = ''
        
        response = client.post('/api/polls', json=invalid_data)
        assert response.status_code in [400, 422, 401, 403]
        
        # Test very long title
        invalid_data['title'] = 'x' * 300  # Assuming max length is 200
        response = client.post('/api/polls', json=invalid_data)
        assert response.status_code in [400, 422, 401, 403, 201, 200]

    def test_get_user_polls(self, client):
        """Test retrieval of polls created by specific user"""
        # Create polls for the user
        poll1 = Poll(
            title='User Poll 1',
            description='First poll by user',
            author_id=self.user.id,
            is_active=True
        )
        db.session.add(poll1)
        db.session.commit()

        response = client.get(f'/api/polls/user/{self.user.id}')
        assert response.status_code in [200, 401, 404]
