"""
Test di integrazione per i workflow completi dei progetti.
Testa i casi d'uso end-to-end dal backend.
"""
import pytest
from datetime import datetime, date, timedelta
from decimal import Decimal

from app import create_app
from extensions import db
from models import (
    User, Project, Task, TimesheetEntry, Client, Contract,
    ProjectResource, ProjectKPI, ProjectExpense, ProjectFundingLink,
    FundingApplication, FundingOpportunity
)

class TestProjectWorkflows:
    """Test dei workflow completi dei progetti"""

    @pytest.fixture
    def setup_project_data(self, app):
        """Setup dati per test progetti"""
        with app.app_context():
            # Crea utenti
            admin = User(
                username='admin',
                email='<EMAIL>',
                first_name='Admin',
                last_name='User',
                role='admin'
            )
            admin.set_password('password')
            
            manager = User(
                username='manager',
                email='<EMAIL>',
                first_name='Project',
                last_name='Manager',
                role='manager'
            )
            manager.set_password('password')
            
            employee = User(
                username='employee',
                email='<EMAIL>',
                first_name='Team',
                last_name='Member',
                role='employee'
            )
            employee.set_password('password')
            
            # Crea cliente
            client = Client(
                name='Test Client',
                email='<EMAIL>',
                industry='Technology'
            )
            
            # Crea contratto
            contract = Contract(
                client_id=1,  # Sarà aggiornato dopo commit
                contract_number='CNT-2025-001',
                title='Test Contract',
                value=50000.0,
                start_date=date.today(),
                end_date=date.today() + timedelta(days=365)
            )
            
            db.session.add_all([admin, manager, employee, client, contract])
            db.session.commit()
            
            # Aggiorna contract con client_id corretto
            contract.client_id = client.id
            db.session.commit()
            
            return {
                'admin': admin,
                'manager': manager,
                'employee': employee,
                'client': client,
                'contract': contract
            }

    def test_complete_project_lifecycle(self, app, setup_project_data):
        """Test del ciclo di vita completo di un progetto"""
        with app.app_context():
            data = setup_project_data
            
            # 1. CREAZIONE PROGETTO
            project = Project(
                name='Test Project Lifecycle',
                description='Progetto di test per ciclo completo',
                client_id=data['client'].id,
                contract_id=data['contract'].id,
                start_date=date.today(),
                end_date=date.today() + timedelta(days=90),
                budget=25000.0,
                status='planning',
                project_type='service',
                is_billable=True
            )
            db.session.add(project)
            db.session.commit()
            
            # 2. AGGIUNTA TEAM MEMBERS
            # Manager come project manager
            project.team_members.append(data['manager'])
            project.team_members.append(data['employee'])
            
            # Crea risorse progetto
            manager_resource = ProjectResource(
                project_id=project.id,
                user_id=data['manager'].id,
                role='Project Manager',
                allocation_percentage=50.0,
                start_date=project.start_date,
                end_date=project.end_date,
                daily_rate=400.0
            )
            
            employee_resource = ProjectResource(
                project_id=project.id,
                user_id=data['employee'].id,
                role='Developer',
                allocation_percentage=100.0,
                start_date=project.start_date,
                end_date=project.end_date,
                daily_rate=300.0
            )
            
            db.session.add_all([manager_resource, employee_resource])
            db.session.commit()
            
            # 3. CREAZIONE TASK
            task1 = Task(
                project_id=project.id,
                title='Setup Development Environment',
                description='Configure development tools and environment',
                assigned_to=data['employee'].id,
                estimated_hours=16.0,
                status='todo',
                priority='high',
                due_date=date.today() + timedelta(days=7)
            )
            
            task2 = Task(
                project_id=project.id,
                title='Implement Core Features',
                description='Develop main application features',
                assigned_to=data['employee'].id,
                estimated_hours=40.0,
                status='todo',
                priority='medium',
                due_date=date.today() + timedelta(days=30)
            )
            
            db.session.add_all([task1, task2])
            db.session.commit()
            
            # 4. AVVIO PROGETTO
            project.status = 'active'
            task1.status = 'in_progress'
            db.session.commit()
            
            # 5. REGISTRAZIONE ORE (TIMESHEET)
            # Settimana 1
            for day in range(5):  # 5 giorni lavorativi
                timesheet = TimesheetEntry(
                    user_id=data['employee'].id,
                    project_id=project.id,
                    task_id=task1.id,
                    date=date.today() + timedelta(days=day),
                    hours=8.0,
                    description=f'Work on task 1 - day {day+1}'
                )
                db.session.add(timesheet)
            
            db.session.commit()
            
            # 6. COMPLETAMENTO PRIMA TASK
            task1.status = 'completed'
            task1.actual_hours = 40.0  # 5 giorni * 8 ore
            task2.status = 'in_progress'
            db.session.commit()
            
            # 7. AGGIUNTA SPESE
            expense1 = ProjectExpense(
                project_id=project.id,
                category='software',
                description='Development tools license',
                amount=299.99,
                expense_date=date.today(),
                user_id=data['manager'].id,
                status='approved'
            )
            
            expense2 = ProjectExpense(
                project_id=project.id,
                category='travel',
                description='Client meeting travel',
                amount=150.00,
                expense_date=date.today() + timedelta(days=10),
                user_id=data['manager'].id,
                status='pending'
            )
            
            db.session.add_all([expense1, expense2])
            db.session.commit()
            
            # 8. AGGIORNAMENTO BUDGET SPESO
            project.expenses = expense1.amount + expense2.amount
            db.session.commit()
            
            # 9. SETUP KPI
            kpi = ProjectKPI(
                project_id=project.id,
                name='Budget Utilization',
                description='Percentage of budget used',
                unit='percentage',
                target_value=90.0,
                current_value=1.8,  # (449.99 / 25000) * 100
                category='budget'
            )
            db.session.add(kpi)
            db.session.commit()
            
            # 10. VALIDAZIONI FINALI
            # Verifica stato progetto
            assert project.status == 'active'
            assert len(project.team_members) == 2
            assert len(project.tasks.all()) == 2
            assert project.tasks.filter_by(status='completed').count() == 1
            assert project.tasks.filter_by(status='in_progress').count() == 1
            
            # Verifica timesheet
            total_hours = TimesheetEntry.query.filter_by(project_id=project.id).count()
            assert total_hours == 5  # 5 giorni di lavoro
            
            # Verifica spese
            total_expenses = ProjectExpense.query.filter_by(project_id=project.id).count()
            assert total_expenses == 2
            approved_expenses = ProjectExpense.query.filter_by(
                project_id=project.id, 
                status='approved'
            ).count()
            assert approved_expenses == 1
            
            # Verifica KPI
            assert project.kpis.count() == 1
            assert project.kpis.first().current_value == 1.8
            
            # Verifica budget
            assert project.remaining_budget == project.budget - project.expenses
            
            print("✅ Test ciclo di vita progetto completato con successo")

    def test_project_funding_workflow(self, app, setup_project_data):
        """Test workflow progetto con finanziamento"""
        with app.app_context():
            data = setup_project_data
            
            # 1. Crea opportunità di finanziamento
            opportunity = FundingOpportunity(
                title='Digital Innovation Grant',
                description='Grant for digital transformation projects',
                source_entity='EU Commission',
                max_grant_amount=100000.0,
                contribution_percentage=70.0,
                application_deadline=date.today() + timedelta(days=30),
                status='open',
                created_by=data['admin'].id
            )
            db.session.add(opportunity)
            db.session.commit()
            
            # 2. Crea application
            application = FundingApplication(
                opportunity_id=opportunity.id,
                project_title='AI-Powered Business Platform',
                project_description='Development of AI platform for SMEs',
                requested_amount=70000.0,
                status='approved',
                created_by=data['manager'].id
            )
            application.approved_amount = 70000.0
            db.session.add(application)
            db.session.commit()
            
            # 3. Crea progetto finanziato
            project = Project(
                name='AI Business Platform',
                description='AI-powered platform development',
                client_id=data['client'].id,
                start_date=date.today(),
                end_date=date.today() + timedelta(days=365),
                budget=100000.0,  # Budget totale
                status='active',
                project_type='rd',
                is_billable=False,  # Progetto R&D finanziato
                funding_source='public_funding',
                funding_application_id=application.id
            )
            db.session.add(project)
            db.session.commit()
            
            # 4. Crea link finanziamento
            funding_link = ProjectFundingLink(
                project_id=project.id,
                funding_application_id=application.id,
                allocation_percentage=70.0,  # 70% finanziato
                notes='EU Digital Innovation Grant funding'
            )
            db.session.add(funding_link)
            db.session.commit()
            
            # 5. Validazioni
            assert project.funding_source == 'public_funding'
            assert project.funding_application_id == application.id
            assert len(project.funding_links) == 1
            assert project.funding_links[0].allocation_percentage == 70.0
            
            # Calcola finanziamento
            funded_amount = (project.budget * funding_link.allocation_percentage) / 100
            co_financing = project.budget - funded_amount
            
            assert funded_amount == 70000.0
            assert co_financing == 30000.0
            
            print("✅ Test workflow finanziamento completato con successo")

    def test_project_team_management(self, app, setup_project_data):
        """Test gestione team di progetto"""
        with app.app_context():
            data = setup_project_data
            
            # Crea progetto
            project = Project(
                name='Team Management Test',
                description='Test per gestione team',
                client_id=data['client'].id,
                start_date=date.today(),
                end_date=date.today() + timedelta(days=60),
                budget=15000.0,
                status='active'
            )
            db.session.add(project)
            db.session.commit()
            
            # Aggiungi membri team
            project.team_members.extend([data['manager'], data['employee']])
            
            # Crea allocazioni risorse
            resources = [
                ProjectResource(
                    project_id=project.id,
                    user_id=data['manager'].id,
                    role='Project Manager',
                    allocation_percentage=25.0,
                    start_date=project.start_date,
                    end_date=project.end_date,
                    daily_rate=400.0
                ),
                ProjectResource(
                    project_id=project.id,
                    user_id=data['employee'].id,
                    role='Senior Developer',
                    allocation_percentage=75.0,
                    start_date=project.start_date,
                    end_date=project.end_date,
                    daily_rate=350.0
                )
            ]
            
            db.session.add_all(resources)
            db.session.commit()
            
            # Validazioni team
            assert len(project.team_members) == 2
            assert len(project.resources) == 2
            
            # Trova risorsa manager
            manager_resource = ProjectResource.query.filter_by(
                project_id=project.id,
                user_id=data['manager'].id
            ).first()
            
            assert manager_resource.role == 'Project Manager'
            assert manager_resource.allocation_percentage == 25.0
            assert manager_resource.daily_rate == 400.0
            
            # Calcola costo giornaliero team
            daily_cost = sum(r.daily_rate * (r.allocation_percentage / 100) for r in project.resources)
            expected_cost = (400.0 * 0.25) + (350.0 * 0.75)  # 100 + 262.5 = 362.5
            
            assert daily_cost == expected_cost
            
            print("✅ Test gestione team completato con successo")
