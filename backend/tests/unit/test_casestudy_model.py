"""Unit tests for CaseStudy model."""
import pytest
from models import CaseStudy, User
from extensions import db

class TestCaseStudyModel:
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        self.app = app
        self.user = test_user

    def test_casestudy_creation_basic(self):
        case_study = CaseStudy(
            title='Test Case Study',
            description='This is a test case study',
            industry='Technology',
            created_by=self.user.id
        )
        db.session.add(case_study)
        db.session.commit()
        
        assert case_study.id is not None
        assert case_study.title == 'Test Case Study'
        assert case_study.industry == 'Technology'

    def test_casestudy_deletion(self):
        case_study = CaseStudy(title='To Delete', description='Delete me', created_by=self.user.id)
        db.session.add(case_study)
        db.session.commit()
        case_id = case_study.id
        
        db.session.delete(case_study)
        db.session.commit()
        
        deleted = CaseStudy.query.get(case_id)
        assert deleted is None
