"""Unit tests for DirectMessage model."""
import pytest
from models import DirectMessage, User
from extensions import db

class TestDirectMessageModel:
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        self.app = app
        self.user = test_user
        
        self.recipient = User(username='recipient', email='<EMAIL>')
        db.session.add(self.recipient)
        db.session.commit()

    def test_directmessage_creation_basic(self):
        message = DirectMessage(
            sender_id=self.user.id,
            recipient_id=self.recipient.id,
            content='Hello, this is a test message'
        )
        db.session.add(message)
        db.session.commit()
        
        assert message.id is not None
        assert message.sender_id == self.user.id
        assert message.recipient_id == self.recipient.id
        assert message.content == 'Hello, this is a test message'

    def test_directmessage_read_status(self):
        message = DirectMessage(
            sender_id=self.user.id,
            recipient_id=self.recipient.id,
            content='Test message',
            is_read=False
        )
        db.session.add(message)
        db.session.commit()
        
        assert message.is_read is False
        
        message.is_read = True
        db.session.commit()
        
        updated = DirectMessage.query.get(message.id)
        assert updated.is_read is True

    def test_directmessage_deletion(self):
        message = DirectMessage(
            sender_id=self.user.id,
            recipient_id=self.recipient.id,
            content='To be deleted'
        )
        db.session.add(message)
        db.session.commit()
        message_id = message.id
        
        db.session.delete(message)
        db.session.commit()
        
        deleted = DirectMessage.query.get(message_id)
        assert deleted is None
