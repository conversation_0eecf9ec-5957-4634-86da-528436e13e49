"""Unit tests for Proposal model."""
import pytest
from datetime import date
from models import Proposal, Client, User
from extensions import db

class TestProposalModel:
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        self.app = app
        self.user = test_user
        
        self.client = Client.query.first()
        if not self.client:
            self.client = Client(name='Test Client', email='<EMAIL>')
            db.session.add(self.client)
            db.session.commit()

    def test_proposal_creation_basic(self):
        proposal = Proposal(
            title='Test Proposal',
            client_id=self.client.id,
            created_by=self.user.id,
            amount=10000.0
        )
        db.session.add(proposal)
        db.session.commit()
        
        assert proposal.id is not None
        assert proposal.title == 'Test Proposal'
        assert proposal.client_id == self.client.id
        assert proposal.amount == 10000.0

    def test_proposal_with_status(self):
        proposal = Proposal(
            title='Status Proposal',
            client_id=self.client.id,
            created_by=self.user.id,
            status='sent',
            valid_until=date(2024, 12, 31)
        )
        db.session.add(proposal)
        db.session.commit()
        
        assert proposal.status == 'sent'
        assert proposal.valid_until == date(2024, 12, 31)

    def test_proposal_deletion(self):
        proposal = Proposal(title='To Delete', client_id=self.client.id, created_by=self.user.id)
        db.session.add(proposal)
        db.session.commit()
        proposal_id = proposal.id
        
        db.session.delete(proposal)
        db.session.commit()
        
        deleted = Proposal.query.get(proposal_id)
        assert deleted is None
