"""Unit tests for AdminLog model."""
import pytest
from models import Admin<PERSON>og, User
from extensions import db

class TestAdminLogModel:
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        self.app = app
        self.user = test_user

    def test_adminlog_creation_basic(self):
        log = AdminLog(
            admin_id=self.user.id,  # Campo corretto è 'admin_id'
            action='user_created'
            # Rimossi campi che non esistono: description, ip_address
        )
        db.session.add(log)
        db.session.commit()

        assert log.id is not None
        assert log.admin_id == self.user.id
        assert log.action == 'user_created'

    def test_adminlog_severity(self):
        log = AdminLog(
            admin_id=self.user.id,  # Campo corretto è 'admin_id'
            action='security_breach'
            # Rimossi campi che non esistono: description, severity
        )
        db.session.add(log)
        db.session.commit()

        assert log.action == 'security_breach'

    def test_adminlog_deletion(self):
        log = AdminLog(admin_id=self.user.id, action='test')  # Campo corretto è 'admin_id'
        db.session.add(log)
        db.session.commit()
        log_id = log.id
        
        db.session.delete(log)
        db.session.commit()
        
        deleted = AdminLog.query.get(log_id)
        assert deleted is None
