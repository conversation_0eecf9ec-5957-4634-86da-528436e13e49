"""Unit tests for StartupResource model."""
import pytest
from models import StartupResource, User
from extensions import db

class TestStartupResourceModel:
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        self.app = app
        self.user = test_user

    def test_startupresource_creation_basic(self):
        resource = StartupResource(
            title='Test Resource',
            description='This is a test startup resource',
            category='funding',
            url='https://example.com'
        )
        db.session.add(resource)
        db.session.commit()
        
        assert resource.id is not None
        assert resource.title == 'Test Resource'
        assert resource.category == 'funding'
        assert resource.url == 'https://example.com'

    def test_startupresource_deletion(self):
        resource = StartupResource(title='To Delete', description='Delete me')
        db.session.add(resource)
        db.session.commit()
        resource_id = resource.id
        
        db.session.delete(resource)
        db.session.commit()
        
        deleted = StartupResource.query.get(resource_id)
        assert deleted is None
