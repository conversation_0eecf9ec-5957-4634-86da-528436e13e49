"""Unit tests for ProjectKPI model."""
import pytest
from models import ProjectKPI, Project, KPI
from extensions import db

class TestProjectKPIModel:
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        self.app = app
        self.user = test_user
        
        # Create test project
        self.project = Project.query.first()
        if not self.project:
            self.project = Project(name='Test Project', description='Test')
            db.session.add(self.project)
            db.session.commit()
            
        # Create test KPI
        self.kpi = KPI.query.first()
        if not self.kpi:
            self.kpi = KPI(name='Test KPI', description='Test KPI')
            db.session.add(self.kpi)
            db.session.commit()

    def test_projectkpi_creation_basic(self):
        project_kpi = ProjectKPI(
            project_id=self.project.id,
            kpi_id=self.kpi.id,
            target_value=100.0
        )
        db.session.add(project_kpi)
        db.session.commit()
        
        assert project_kpi.id is not None
        assert project_kpi.project_id == self.project.id
        assert project_kpi.kpi_id == self.kpi.id
        assert project_kpi.target_value == 100.0

    def test_projectkpi_values(self):
        project_kpi = ProjectKPI(
            project_id=self.project.id,
            kpi_id=self.kpi.id,
            target_value=150.0,
            current_value=75.0
        )
        db.session.add(project_kpi)
        db.session.commit()
        
        assert project_kpi.target_value == 150.0
        assert project_kpi.current_value == 75.0

    def test_projectkpi_update(self):
        project_kpi = ProjectKPI(
            project_id=self.project.id,
            kpi_id=self.kpi.id,
            target_value=100.0,
            current_value=50.0
        )
        db.session.add(project_kpi)
        db.session.commit()
        
        project_kpi.current_value = 80.0
        db.session.commit()
        
        updated = ProjectKPI.query.get(project_kpi.id)
        assert updated.current_value == 80.0

    def test_projectkpi_deletion(self):
        project_kpi = ProjectKPI(
            project_id=self.project.id,
            kpi_id=self.kpi.id,
            target_value=100.0
        )
        db.session.add(project_kpi)
        db.session.commit()
        kpi_id = project_kpi.id
        
        db.session.delete(project_kpi)
        db.session.commit()
        
        deleted = ProjectKPI.query.get(kpi_id)
        assert deleted is None
