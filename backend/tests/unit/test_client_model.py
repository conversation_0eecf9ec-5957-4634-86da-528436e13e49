"""Unit tests for Client model."""
import pytest
from models import Client
from extensions import db

class TestClientModel:
    @pytest.fixture(autouse=True)
    def setup_method(self, app):
        self.app = app

    def test_client_creation_basic(self):
        client = Client(
            name='Test Company',
            email='<EMAIL>'
        )
        db.session.add(client)
        db.session.commit()
        
        assert client.id is not None
        assert client.name == 'Test Company'
        assert client.email == '<EMAIL>'

    def test_client_with_details(self):
        client = Client(
            name='Detailed Company',
            email='<EMAIL>',
            phone='+1234567890',
            address='123 Business St',
            industry='Technology'
        )
        db.session.add(client)
        db.session.commit()
        
        assert client.phone == '+1234567890'
        assert client.address == '123 Business St'
        assert client.industry == 'Technology'

    def test_client_deletion(self):
        client = Client(name='To Delete', email='<EMAIL>')
        db.session.add(client)
        db.session.commit()
        client_id = client.id
        
        db.session.delete(client)
        db.session.commit()
        
        deleted = Client.query.get(client_id)
        assert deleted is None
