"""Unit tests for InvoiceItem model."""
import pytest
from models import InvoiceItem, Invoice, Client, User
from extensions import db

class TestInvoiceItemModel:
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        self.app = app
        self.user = test_user
        
        self.client = Client.query.first()
        if not self.client:
            self.client = Client(name='Test Client', email='<EMAIL>')
            db.session.add(self.client)
            db.session.commit()
            
        self.invoice = Invoice.query.first()
        if not self.invoice:
            self.invoice = Invoice(invoice_number='INV-001', client_id=self.client.id, amount=1000.0, created_by=self.user.id)
            db.session.add(self.invoice)
            db.session.commit()

    def test_invoiceitem_creation_basic(self):
        item = InvoiceItem(
            invoice_id=self.invoice.id,
            description='Test Service',
            quantity=2,
            unit_price=500.0,
            total=1000.0
        )
        db.session.add(item)
        db.session.commit()
        
        assert item.id is not None
        assert item.description == 'Test Service'
        assert item.quantity == 2
        assert item.total == 1000.0

    def test_invoiceitem_deletion(self):
        item = InvoiceItem(invoice_id=self.invoice.id, description='To Delete', quantity=1, unit_price=100.0)
        db.session.add(item)
        db.session.commit()
        item_id = item.id
        
        db.session.delete(item)
        db.session.commit()
        
        deleted = InvoiceItem.query.get(item_id)
        assert deleted is None
