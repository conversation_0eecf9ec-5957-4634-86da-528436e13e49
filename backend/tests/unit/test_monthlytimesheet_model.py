"""
Unit tests for MonthlyTimesheet model.
Tests business logic, validations, and model functionality.
"""

import pytest
from datetime import datetime, date
from models import MonthlyTimesheet, User
from extensions import db


class TestMonthlyTimesheetModel:
    """Test suite for MonthlyTimesheet model unit tests"""

    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        """Setup test data for each test method"""
        self.app = app
        self.user = test_user

    def test_monthlytimesheet_creation_basic(self):
        """Test basic monthly timesheet creation"""
        timesheet = MonthlyTimesheet(
            user_id=self.user.id,
            year=2024,
            month=12,
            status='draft'
        )
        
        db.session.add(timesheet)
        db.session.commit()
        
        assert timesheet.id is not None
        assert timesheet.user_id == self.user.id
        assert timesheet.year == 2024
        assert timesheet.month == 12
        assert timesheet.status == 'draft'

    def test_monthlytimesheet_creation_complete(self):
        """Test monthly timesheet creation with all fields"""
        # Create approver user
        approver = User(username='approver', email='<EMAIL>')
        db.session.add(approver)
        db.session.commit()
        
        timesheet = MonthlyTimesheet(
            user_id=self.user.id,
            year=2024,
            month=11,
            status='approved',
            submission_date=datetime(2024, 12, 1, 10, 0, 0),
            approval_date=datetime(2024, 12, 2, 14, 30, 0),
            approved_by=approver.id,
            rejection_reason=None
        )
        
        db.session.add(timesheet)
        db.session.commit()
        
        assert timesheet.year == 2024
        assert timesheet.month == 11
        assert timesheet.status == 'approved'
        assert timesheet.submission_date == datetime(2024, 12, 1, 10, 0, 0)
        assert timesheet.approval_date == datetime(2024, 12, 2, 14, 30, 0)
        assert timesheet.approved_by == approver.id

    def test_monthlytimesheet_relationships(self):
        """Test relationships with User model"""
        timesheet = MonthlyTimesheet(
            user_id=self.user.id,
            year=2024,
            month=10,
            status='submitted'
        )
        
        db.session.add(timesheet)
        db.session.commit()
        
        # Test forward relationship
        assert timesheet.user is not None
        assert timesheet.user.id == self.user.id

    def test_monthlytimesheet_status_types(self):
        """Test different status types"""
        statuses = ['draft', 'submitted', 'approved', 'rejected']
        
        timesheets = []
        for i, status in enumerate(statuses):
            timesheet = MonthlyTimesheet(
                user_id=self.user.id,
                year=2024,
                month=i + 1,  # Different months to avoid conflicts
                status=status
            )
            timesheets.append(timesheet)
        
        db.session.add_all(timesheets)
        db.session.commit()
        
        for timesheet, expected_status in zip(timesheets, statuses):
            assert timesheet.status == expected_status

    def test_monthlytimesheet_year_month_validation(self):
        """Test year and month field validation"""
        # Test valid year/month combinations
        valid_combinations = [
            (2024, 1), (2024, 6), (2024, 12),
            (2023, 1), (2025, 12)
        ]
        
        timesheets = []
        for year, month in valid_combinations:
            timesheet = MonthlyTimesheet(
                user_id=self.user.id,
                year=year,
                month=month,
                status='draft'
            )
            timesheets.append(timesheet)
        
        db.session.add_all(timesheets)
        db.session.commit()
        
        for timesheet, (expected_year, expected_month) in zip(timesheets, valid_combinations):
            assert timesheet.year == expected_year
            assert timesheet.month == expected_month
            assert 1 <= timesheet.month <= 12
            assert timesheet.year >= 2020  # Reasonable year range

    def test_monthlytimesheet_approval_workflow(self):
        """Test approval workflow functionality"""
        approver = User(username='manager', email='<EMAIL>')
        db.session.add(approver)
        db.session.commit()
        
        # Draft timesheet
        timesheet = MonthlyTimesheet(
            user_id=self.user.id,
            year=2024,
            month=9,
            status='draft'
        )
        
        db.session.add(timesheet)
        db.session.commit()
        
        # Submit timesheet
        timesheet.status = 'submitted'
        timesheet.submission_date = datetime.now()
        db.session.commit()
        
        assert timesheet.status == 'submitted'
        assert timesheet.submission_date is not None
        
        # Approve timesheet
        timesheet.status = 'approved'
        timesheet.approval_date = datetime.now()
        timesheet.approved_by = approver.id
        db.session.commit()
        
        assert timesheet.status == 'approved'
        assert timesheet.approval_date is not None
        assert timesheet.approved_by == approver.id

    def test_monthlytimesheet_rejection_workflow(self):
        """Test rejection workflow functionality"""
        timesheet = MonthlyTimesheet(
            user_id=self.user.id,
            year=2024,
            month=8,
            status='submitted',
            submission_date=datetime.now()
        )
        
        db.session.add(timesheet)
        db.session.commit()
        
        # Reject timesheet
        rejection_reason = 'Missing project details for several entries'
        timesheet.status = 'rejected'
        timesheet.rejection_reason = rejection_reason
        
        db.session.commit()
        
        assert timesheet.status == 'rejected'
        assert timesheet.rejection_reason == rejection_reason

    def test_monthlytimesheet_query_by_user(self):
        """Test querying timesheets by user"""
        timesheet = MonthlyTimesheet(
            user_id=self.user.id,
            year=2024,
            month=7,
            status='draft'
        )
        
        db.session.add(timesheet)
        db.session.commit()
        
        # Query by user
        user_timesheets = MonthlyTimesheet.query.filter_by(user_id=self.user.id).all()
        assert len(user_timesheets) >= 1
        assert timesheet in user_timesheets

    def test_monthlytimesheet_query_by_period(self):
        """Test querying timesheets by year/month"""
        timesheet = MonthlyTimesheet(
            user_id=self.user.id,
            year=2024,
            month=6,
            status='approved'
        )
        
        db.session.add(timesheet)
        db.session.commit()
        
        # Query by year and month
        period_timesheets = MonthlyTimesheet.query.filter_by(
            year=2024, month=6
        ).all()
        assert len(period_timesheets) >= 1
        assert timesheet in period_timesheets

    def test_monthlytimesheet_query_by_status(self):
        """Test querying timesheets by status"""
        timesheet = MonthlyTimesheet(
            user_id=self.user.id,
            year=2024,
            month=5,
            status='submitted'
        )
        
        db.session.add(timesheet)
        db.session.commit()
        
        # Query by status
        submitted_timesheets = MonthlyTimesheet.query.filter_by(status='submitted').all()
        assert len(submitted_timesheets) >= 1
        assert timesheet in submitted_timesheets

    def test_monthlytimesheet_timestamps(self):
        """Test automatic timestamp handling"""
        timesheet = MonthlyTimesheet(
            user_id=self.user.id,
            year=2024,
            month=4,
            status='draft'
        )
        
        db.session.add(timesheet)
        db.session.commit()
        
        assert timesheet.created_at is not None
        assert timesheet.updated_at is not None
        assert isinstance(timesheet.created_at, datetime)
        assert isinstance(timesheet.updated_at, datetime)

    def test_monthlytimesheet_update_operations(self):
        """Test timesheet update operations"""
        timesheet = MonthlyTimesheet(
            user_id=self.user.id,
            year=2024,
            month=3,
            status='draft'
        )
        
        db.session.add(timesheet)
        db.session.commit()
        
        # Update timesheet
        timesheet.status = 'submitted'
        timesheet.submission_date = datetime.now()
        
        db.session.commit()
        
        updated_timesheet = MonthlyTimesheet.query.get(timesheet.id)
        assert updated_timesheet.status == 'submitted'
        assert updated_timesheet.submission_date is not None

    def test_monthlytimesheet_deletion(self):
        """Test timesheet deletion"""
        timesheet = MonthlyTimesheet(
            user_id=self.user.id,
            year=2024,
            month=2,
            status='draft'
        )
        
        db.session.add(timesheet)
        db.session.commit()
        timesheet_id = timesheet.id
        
        db.session.delete(timesheet)
        db.session.commit()
        
        deleted_timesheet = MonthlyTimesheet.query.get(timesheet_id)
        assert deleted_timesheet is None

    def test_monthlytimesheet_unique_constraints(self):
        """Test unique constraints if any"""
        # Test that multiple timesheets for same user/period might be allowed
        # or test unique constraint if it exists
        timesheet1 = MonthlyTimesheet(
            user_id=self.user.id,
            year=2024,
            month=1,
            status='draft'
        )
        
        timesheet2 = MonthlyTimesheet(
            user_id=self.user.id,
            year=2023,
            month=1,  # Different year, same month
            status='approved'
        )
        
        db.session.add_all([timesheet1, timesheet2])
        db.session.commit()
        
        assert timesheet1.id != timesheet2.id
        assert timesheet1.year != timesheet2.year

    def test_monthlytimesheet_rejection_reason_length(self):
        """Test rejection reason field for long text"""
        long_reason = """
        This timesheet is rejected due to multiple issues:
        1. Missing project codes for several entries
        2. Overtime hours not properly justified
        3. Some entries lack detailed descriptions
        4. Time allocation doesn't match project requirements
        Please review and resubmit with corrections.
        """
        
        timesheet = MonthlyTimesheet(
            user_id=self.user.id,
            year=2023,
            month=12,
            status='rejected',
            rejection_reason=long_reason.strip()
        )
        
        db.session.add(timesheet)
        db.session.commit()
        
        assert timesheet.rejection_reason == long_reason.strip()
        assert len(timesheet.rejection_reason) > 100  # Verify it can handle long text
