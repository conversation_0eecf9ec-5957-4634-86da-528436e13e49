"""
Unit tests for Service model.
Tests business logic, validations, and model functionality.
"""

import pytest
from datetime import datetime
from models import Service
from extensions import db


class TestServiceModel:
    """Test suite for Service model unit tests"""

    @pytest.fixture(autouse=True)
    def setup_method(self, app):
        """Setup test data for each test method"""
        self.app = app

    def test_service_creation_basic(self):
        """Test basic service creation"""
        service = Service(
            name='Web Development',
            description='Full-stack web development services',
            category='Development'
        )
        
        db.session.add(service)
        db.session.commit()
        
        assert service.id is not None
        assert service.name == 'Web Development'
        assert service.description == 'Full-stack web development services'
        assert service.category == 'Development'

    def test_service_creation_complete(self):
        """Test service creation with all fields"""
        service = Service(
            name='Consulting',
            description='Technical consulting services',
            category='Consulting',
            hourly_rate=150.0,
            status='active'
        )
        
        db.session.add(service)
        db.session.commit()
        
        assert service.name == 'Consulting'
        assert service.hourly_rate == 150.0
        assert service.status == 'active'

    def test_service_categories(self):
        """Test different service categories"""
        categories = ['Development', 'Design', 'Consulting', 'Support', 'Training']
        
        services = []
        for i, category in enumerate(categories):
            service = Service(
                name=f'{category} Service {i}',
                category=category,
                hourly_rate=100.0 + i * 25
            )
            services.append(service)
        
        db.session.add_all(services)
        db.session.commit()
        
        for service, expected_category in zip(services, categories):
            assert service.category == expected_category

    def test_service_hourly_rates(self):
        """Test hourly rate functionality"""
        rates = [50.0, 75.0, 100.0, 125.0, 150.0]
        
        services = []
        for i, rate in enumerate(rates):
            service = Service(
                name=f'Service Rate {i}',
                hourly_rate=rate,
                category='Testing'
            )
            services.append(service)
        
        db.session.add_all(services)
        db.session.commit()
        
        for service, expected_rate in zip(services, rates):
            assert service.hourly_rate == expected_rate

    def test_service_status_values(self):
        """Test service status field"""
        statuses = ['active', 'inactive', 'draft', 'archived']
        
        services = []
        for i, status in enumerate(statuses):
            service = Service(
                name=f'Service Status {i}',
                status=status,
                category='Testing'
            )
            services.append(service)
        
        db.session.add_all(services)
        db.session.commit()
        
        for service, expected_status in zip(services, statuses):
            assert service.status == expected_status

    def test_service_timestamps(self):
        """Test automatic timestamp handling"""
        service = Service(
            name='Timestamp Test Service',
            category='Testing'
        )
        
        db.session.add(service)
        db.session.commit()
        
        assert service.created_at is not None
        assert service.updated_at is not None
        assert isinstance(service.created_at, datetime)
        assert isinstance(service.updated_at, datetime)

    def test_service_query_by_category(self):
        """Test querying services by category"""
        service = Service(
            name='Category Test Service',
            category='Special Category'
        )
        
        db.session.add(service)
        db.session.commit()
        
        category_services = Service.query.filter_by(category='Special Category').all()
        assert len(category_services) >= 1
        assert service in category_services

    def test_service_query_by_status(self):
        """Test querying services by status"""
        service = Service(
            name='Status Test Service',
            status='testing',
            category='Testing'
        )
        
        db.session.add(service)
        db.session.commit()
        
        status_services = Service.query.filter_by(status='testing').all()
        assert len(status_services) >= 1
        assert service in status_services

    def test_service_update_operations(self):
        """Test service update operations"""
        service = Service(
            name='Original Service',
            category='Original',
            hourly_rate=100.0,
            status='draft'
        )
        
        db.session.add(service)
        db.session.commit()
        
        # Update service
        service.name = 'Updated Service'
        service.hourly_rate = 150.0
        service.status = 'active'
        
        db.session.commit()
        
        updated_service = Service.query.get(service.id)
        assert updated_service.name == 'Updated Service'
        assert updated_service.hourly_rate == 150.0
        assert updated_service.status == 'active'

    def test_service_deletion(self):
        """Test service deletion"""
        service = Service(
            name='To Be Deleted',
            category='Testing'
        )
        
        db.session.add(service)
        db.session.commit()
        service_id = service.id
        
        db.session.delete(service)
        db.session.commit()
        
        deleted_service = Service.query.get(service_id)
        assert deleted_service is None

    def test_service_name_length(self):
        """Test service name field length"""
        long_name = 'Very Long Service Name That Tests The Maximum Field Length Constraint For Service Names'
        
        service = Service(
            name=long_name[:128],  # Truncate to VARCHAR(128) limit
            category='Testing'
        )
        
        db.session.add(service)
        db.session.commit()
        
        assert len(service.name) <= 128
        assert service.name == long_name[:128]
