"""
Unit tests for Notification model.
Tests business logic, validations, and model functionality.
"""

import pytest
from datetime import datetime
from models import Notification, User
from extensions import db


class TestNotificationModel:
    """Test suite for Notification model unit tests"""

    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        """Setup test data for each test method"""
        self.app = app
        self.user = test_user

    def test_notification_creation_basic(self):
        """Test basic notification creation"""
        notification = Notification(
            user_id=self.user.id,
            title='Test Notification',
            message='This is a test notification message'
        )
        
        db.session.add(notification)
        db.session.commit()
        
        assert notification.id is not None
        assert notification.user_id == self.user.id
        assert notification.title == 'Test Notification'
        assert notification.message == 'This is a test notification message'

    def test_notification_creation_complete(self):
        """Test notification creation with all fields"""
        notification = Notification(
            user_id=self.user.id,
            title='Complete Notification',
            message='Complete notification with all fields',
            link='/dashboard',
            is_read=False,
            type='warning'
        )
        
        db.session.add(notification)
        db.session.commit()
        
        assert notification.title == 'Complete Notification'
        assert notification.link == '/dashboard'
        assert notification.is_read is False
        assert notification.type == 'warning'

    def test_notification_types(self):
        """Test different notification types"""
        types = ['info', 'warning', 'error', 'success']
        
        notifications = []
        for i, notif_type in enumerate(types):
            notification = Notification(
                user_id=self.user.id,
                title=f'Notification Type {i}',
                message=f'Message for {notif_type} notification',
                type=notif_type
            )
            notifications.append(notification)
        
        db.session.add_all(notifications)
        db.session.commit()
        
        for notification, expected_type in zip(notifications, types):
            assert notification.type == expected_type

    def test_notification_read_status(self):
        """Test notification read status functionality"""
        # Unread notification
        unread_notification = Notification(
            user_id=self.user.id,
            title='Unread Notification',
            message='This notification is unread',
            is_read=False
        )
        
        # Read notification
        read_notification = Notification(
            user_id=self.user.id,
            title='Read Notification',
            message='This notification is read',
            is_read=True
        )
        
        db.session.add_all([unread_notification, read_notification])
        db.session.commit()
        
        assert unread_notification.is_read is False
        assert read_notification.is_read is True

    def test_notification_links(self):
        """Test notification link functionality"""
        links = ['/dashboard', '/profile', '/tasks/123', '/projects/456']
        
        notifications = []
        for i, link in enumerate(links):
            notification = Notification(
                user_id=self.user.id,
                title=f'Link Notification {i}',
                message=f'Notification with link {i}',
                link=link
            )
            notifications.append(notification)
        
        db.session.add_all(notifications)
        db.session.commit()
        
        for notification, expected_link in zip(notifications, links):
            assert notification.link == expected_link

    def test_notification_timestamps(self):
        """Test automatic timestamp handling"""
        notification = Notification(
            user_id=self.user.id,
            title='Timestamp Test',
            message='Testing timestamp functionality'
        )
        
        db.session.add(notification)
        db.session.commit()
        
        assert notification.created_at is not None
        assert isinstance(notification.created_at, datetime)

    def test_notification_query_by_user(self):
        """Test querying notifications by user"""
        notification = Notification(
            user_id=self.user.id,
            title='User Query Test',
            message='Testing user query functionality'
        )
        
        db.session.add(notification)
        db.session.commit()
        
        user_notifications = Notification.query.filter_by(user_id=self.user.id).all()
        assert len(user_notifications) >= 1
        assert notification in user_notifications

    def test_notification_query_unread(self):
        """Test querying unread notifications"""
        notification = Notification(
            user_id=self.user.id,
            title='Unread Query Test',
            message='Testing unread query functionality',
            is_read=False
        )
        
        db.session.add(notification)
        db.session.commit()
        
        unread_notifications = Notification.query.filter_by(is_read=False).all()
        assert len(unread_notifications) >= 1
        assert notification in unread_notifications

    def test_notification_query_by_type(self):
        """Test querying notifications by type"""
        notification = Notification(
            user_id=self.user.id,
            title='Type Query Test',
            message='Testing type query functionality',
            type='urgent'
        )
        
        db.session.add(notification)
        db.session.commit()
        
        type_notifications = Notification.query.filter_by(type='urgent').all()
        assert len(type_notifications) >= 1
        assert notification in type_notifications

    def test_notification_mark_as_read(self):
        """Test marking notification as read"""
        notification = Notification(
            user_id=self.user.id,
            title='Mark Read Test',
            message='Testing mark as read functionality',
            is_read=False
        )
        
        db.session.add(notification)
        db.session.commit()
        
        # Mark as read
        notification.is_read = True
        db.session.commit()
        
        updated_notification = Notification.query.get(notification.id)
        assert updated_notification.is_read is True

    def test_notification_update_operations(self):
        """Test notification update operations"""
        notification = Notification(
            user_id=self.user.id,
            title='Original Title',
            message='Original message',
            type='info',
            is_read=False
        )
        
        db.session.add(notification)
        db.session.commit()
        
        # Update notification
        notification.title = 'Updated Title'
        notification.message = 'Updated message'
        notification.type = 'warning'
        notification.is_read = True
        
        db.session.commit()
        
        updated_notification = Notification.query.get(notification.id)
        assert updated_notification.title == 'Updated Title'
        assert updated_notification.message == 'Updated message'
        assert updated_notification.type == 'warning'
        assert updated_notification.is_read is True

    def test_notification_deletion(self):
        """Test notification deletion"""
        notification = Notification(
            user_id=self.user.id,
            title='To Be Deleted',
            message='This notification will be deleted'
        )
        
        db.session.add(notification)
        db.session.commit()
        notification_id = notification.id
        
        db.session.delete(notification)
        db.session.commit()
        
        deleted_notification = Notification.query.get(notification_id)
        assert deleted_notification is None
