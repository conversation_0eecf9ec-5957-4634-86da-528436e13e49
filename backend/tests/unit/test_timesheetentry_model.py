"""
Unit tests for TimesheetEntry model.
Tests business logic, validations, and model functionality.
"""

import pytest
from datetime import datetime, date
from models import TimesheetEntry, User, Project, Task
from extensions import db


class TestTimesheetEntryModel:
    """Test suite for TimesheetEntry model unit tests"""

    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        """Setup test data for each test method"""
        self.app = app
        self.user = test_user
        
        # Create test project
        self.test_project = Project(
            name='Test Project Timesheet',
            description='Project for timesheet testing',
            status='active'
        )
        db.session.add(self.test_project)
        db.session.commit()

    def test_timesheetentry_creation_basic(self):
        """Test basic timesheet entry creation"""
        entry = TimesheetEntry(
            user_id=self.user.id,
            project_id=self.test_project.id,
            date=date.today(),
            hours=8.0,
            description='Development work'
        )
        
        db.session.add(entry)
        db.session.commit()
        
        assert entry.id is not None
        assert entry.user_id == self.user.id
        assert entry.project_id == self.test_project.id
        assert entry.hours == 8.0
        assert entry.description == 'Development work'

    def test_timesheetentry_creation_complete(self):
        """Test timesheet entry creation with all fields"""
        entry = TimesheetEntry(
            user_id=self.user.id,
            project_id=self.test_project.id,
            date=date.today(),
            hours=6.5,
            description='Frontend development',
            status='approved',
            billable=True,
            billing_rate=75.0,
            billing_status='billed'
        )
        
        db.session.add(entry)
        db.session.commit()
        
        assert entry.hours == 6.5
        assert entry.status == 'approved'
        assert entry.billable is True
        assert entry.billing_rate == 75.0
        assert entry.billing_status == 'billed'

    def test_timesheetentry_relationships(self):
        """Test relationships with other models"""
        entry = TimesheetEntry(
            user_id=self.user.id,
            project_id=self.test_project.id,
            date=date.today(),
            hours=4.0
        )
        
        db.session.add(entry)
        db.session.commit()
        
        # Test relationships
        assert entry.user is not None
        assert entry.user.id == self.user.id
        assert entry.project is not None
        assert entry.project.id == self.test_project.id

    def test_timesheetentry_hours_validation(self):
        """Test hours field validation"""
        entry = TimesheetEntry(
            user_id=self.user.id,
            project_id=self.test_project.id,
            date=date.today(),
            hours=12.5
        )
        
        db.session.add(entry)
        db.session.commit()
        
        assert entry.hours == 12.5
        assert entry.hours > 0

    def test_timesheetentry_billable_functionality(self):
        """Test billable flag and billing rate"""
        billable_entry = TimesheetEntry(
            user_id=self.user.id,
            project_id=self.test_project.id,
            date=date.today(),
            hours=8.0,
            billable=True,
            billing_rate=100.0
        )
        
        non_billable_entry = TimesheetEntry(
            user_id=self.user.id,
            project_id=self.test_project.id,
            date=date.today(),
            hours=2.0,
            billable=False
        )
        
        db.session.add_all([billable_entry, non_billable_entry])
        db.session.commit()
        
        assert billable_entry.billable is True
        assert billable_entry.billing_rate == 100.0
        assert non_billable_entry.billable is False

    def test_timesheetentry_status_handling(self):
        """Test status field functionality"""
        statuses = ['draft', 'submitted', 'approved', 'rejected']
        
        entries = []
        for i, status in enumerate(statuses):
            entry = TimesheetEntry(
                user_id=self.user.id,
                project_id=self.test_project.id,
                date=date.today(),
                hours=1.0,
                description=f'Entry {i}',
                status=status
            )
            entries.append(entry)
        
        db.session.add_all(entries)
        db.session.commit()
        
        for entry, expected_status in zip(entries, statuses):
            assert entry.status == expected_status

    def test_timesheetentry_billing_status(self):
        """Test billing status functionality"""
        entry = TimesheetEntry(
            user_id=self.user.id,
            project_id=self.test_project.id,
            date=date.today(),
            hours=8.0,
            billable=True,
            billing_status='unbilled'
        )
        
        db.session.add(entry)
        db.session.commit()
        
        assert entry.billing_status == 'unbilled'
        
        # Update billing status
        entry.billing_status = 'billed'
        db.session.commit()
        
        assert entry.billing_status == 'billed'

    def test_timesheetentry_date_queries(self):
        """Test querying entries by date"""
        today = date.today()
        entry = TimesheetEntry(
            user_id=self.user.id,
            project_id=self.test_project.id,
            date=today,
            hours=8.0
        )
        
        db.session.add(entry)
        db.session.commit()
        
        # Query by date
        entries_today = TimesheetEntry.query.filter_by(date=today).all()
        assert len(entries_today) >= 1
        assert entry in entries_today

    def test_timesheetentry_user_queries(self):
        """Test querying entries by user"""
        entry = TimesheetEntry(
            user_id=self.user.id,
            project_id=self.test_project.id,
            date=date.today(),
            hours=6.0
        )
        
        db.session.add(entry)
        db.session.commit()
        
        # Query by user
        user_entries = TimesheetEntry.query.filter_by(user_id=self.user.id).all()
        assert len(user_entries) >= 1
        assert entry in user_entries

    def test_timesheetentry_project_queries(self):
        """Test querying entries by project"""
        entry = TimesheetEntry(
            user_id=self.user.id,
            project_id=self.test_project.id,
            date=date.today(),
            hours=4.0
        )
        
        db.session.add(entry)
        db.session.commit()
        
        # Query by project
        project_entries = TimesheetEntry.query.filter_by(project_id=self.test_project.id).all()
        assert len(project_entries) >= 1
        assert entry in project_entries

    def test_timesheetentry_hours_calculation(self):
        """Test hours calculation for multiple entries"""
        entries = [
            TimesheetEntry(user_id=self.user.id, project_id=self.test_project.id, 
                          date=date.today(), hours=4.0),
            TimesheetEntry(user_id=self.user.id, project_id=self.test_project.id, 
                          date=date.today(), hours=3.5),
            TimesheetEntry(user_id=self.user.id, project_id=self.test_project.id, 
                          date=date.today(), hours=2.0)
        ]
        
        db.session.add_all(entries)
        db.session.commit()
        
        # Calculate total hours
        total_hours = sum(entry.hours for entry in entries)
        assert total_hours == 9.5

    def test_timesheetentry_update_operations(self):
        """Test entry update operations"""
        entry = TimesheetEntry(
            user_id=self.user.id,
            project_id=self.test_project.id,
            date=date.today(),
            hours=8.0,
            description='Original description',
            status='draft'
        )
        
        db.session.add(entry)
        db.session.commit()
        
        # Update entry
        entry.hours = 7.5
        entry.description = 'Updated description'
        entry.status = 'submitted'
        
        db.session.commit()
        
        updated_entry = TimesheetEntry.query.get(entry.id)
        assert updated_entry.hours == 7.5
        assert updated_entry.description == 'Updated description'
        assert updated_entry.status == 'submitted'

    def test_timesheetentry_deletion(self):
        """Test entry deletion"""
        entry = TimesheetEntry(
            user_id=self.user.id,
            project_id=self.test_project.id,
            date=date.today(),
            hours=1.0
        )
        
        db.session.add(entry)
        db.session.commit()
        entry_id = entry.id
        
        db.session.delete(entry)
        db.session.commit()
        
        deleted_entry = TimesheetEntry.query.get(entry_id)
        assert deleted_entry is None

    def test_timesheetentry_default_values(self):
        """Test default values for fields"""
        entry = TimesheetEntry(
            user_id=self.user.id,
            project_id=self.test_project.id,
            date=date.today(),
            hours=8.0
        )
        
        db.session.add(entry)
        db.session.commit()
        
        assert entry.billable is False  # Default value
        assert entry.billing_status == 'unbilled'  # Default value
        assert entry.created_at is not None

    def test_timesheetentry_timestamps(self):
        """Test automatic timestamp handling"""
        entry = TimesheetEntry(
            user_id=self.user.id,
            project_id=self.test_project.id,
            date=date.today(),
            hours=8.0
        )
        
        db.session.add(entry)
        db.session.commit()
        
        assert entry.created_at is not None
        assert isinstance(entry.created_at, datetime)
