"""Unit tests for Project model."""
import pytest
from datetime import date
from models import Project, User
from extensions import db

class TestProjectModel:
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        self.app = app
        self.user = test_user

    def test_project_creation_basic(self):
        project = Project(
            name='Test Project',
            description='This is a test project'
            # Rimosso manager_id - non esiste nel modello
        )
        db.session.add(project)
        db.session.commit()

        assert project.id is not None
        assert project.name == 'Test Project'
        assert project.description == 'This is a test project'

    def test_project_dates(self):
        project = Project(
            name='Date Project',
            description='Project with dates',
            start_date=date(2024, 1, 1),
            end_date=date(2024, 12, 31)
            # Rimosso manager_id - non esiste nel modello
        )
        db.session.add(project)
        db.session.commit()
        
        assert project.start_date == date(2024, 1, 1)
        assert project.end_date == date(2024, 12, 31)

    def test_project_deletion(self):
        project = Project(name='To Delete', description='Delete me')  # Rimosso manager_id
        db.session.add(project)
        db.session.commit()
        project_id = project.id
        
        db.session.delete(project)
        db.session.commit()
        
        deleted = Project.query.get(project_id)
        assert deleted is None
