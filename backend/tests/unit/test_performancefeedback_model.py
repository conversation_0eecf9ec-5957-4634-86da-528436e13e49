"""Unit tests for PerformanceFeedback model."""
import pytest
from models import PerformanceFeedback, User
from extensions import db

class TestPerformanceFeedbackModel:
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        self.app = app
        self.user = test_user

    def test_performancefeedback_creation_basic(self):
        feedback = PerformanceFeedback(
            from_user_id=self.user.id,  # Campo corretto è 'from_user_id'
            to_user_id=self.user.id,    # Campo corretto è 'to_user_id'
            feedback_type='peer',       # Campo richiesto
            content='Great work!',      # <PERSON> corretto è 'content'
            rating=5
        )
        db.session.add(feedback)
        db.session.commit()
        
        assert feedback.id is not None
        assert feedback.content == 'Great work!'
        assert feedback.rating == 5

    def test_performancefeedback_deletion(self):
        feedback = PerformanceFeedback(
            from_user_id=self.user.id,
            to_user_id=self.user.id,
            feedback_type='peer',
            content='Delete me'
        )
        db.session.add(feedback)
        db.session.commit()
        feedback_id = feedback.id
        
        db.session.delete(feedback)
        db.session.commit()
        
        deleted = PerformanceFeedback.query.get(feedback_id)
        assert deleted is None
