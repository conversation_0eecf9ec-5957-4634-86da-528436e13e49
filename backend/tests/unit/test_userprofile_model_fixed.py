"""
Unit tests for UserProfile model.
Tests business logic, validations, and model functionality.
Fixed version with unique user_id handling.
"""

import pytest
from datetime import datetime, date
from models import UserProfile, User
from extensions import db


class TestUserProfileModel:
    """Test suite for UserProfile model unit tests"""

    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        """Setup test data for each test method"""
        self.app = app
        self.user = test_user

    def test_userprofile_creation_basic(self):
        """Test basic user profile creation"""
        profile = UserProfile(
            user_id=self.user.id,
            employee_id='EMP001_Basic',
            job_title='Software Developer'
        )
        
        db.session.add(profile)
        db.session.commit()
        
        assert profile.id is not None
        assert profile.user_id == self.user.id
        assert profile.employee_id == 'EMP001_Basic'
        assert profile.job_title == 'Software Developer'

    def test_userprofile_creation_complete(self):
        """Test user profile creation with all fields"""
        # Create unique user for this test
        user2 = User(username='testuser_complete', email='<EMAIL>')
        db.session.add(user2)
        db.session.commit()
        
        profile = UserProfile(
            user_id=user2.id,
            employee_id='EMP002_Complete',
            job_title='Senior Developer',
            birth_date=date(1990, 5, 15),
            address='123 Main St, City',
            emergency_contact_name='John Doe',
            emergency_contact_phone='+39 ************',
            emergency_contact_relationship='Spouse',
            employment_type='full_time',
            work_location='Remote',
            salary=50000.0,
            salary_currency='EUR',
            weekly_hours=40.0,
            daily_hours=8.0,
            profile_completion=75.5
        )
        
        db.session.add(profile)
        db.session.commit()
        
        assert profile.birth_date == date(1990, 5, 15)
        assert profile.salary == 50000.0
        assert profile.profile_completion == 75.5
        assert profile.created_at is not None

    def test_userprofile_user_relationship(self):
        """Test relationship with User model"""
        user3 = User(username='testuser_relationship', email='<EMAIL>')
        db.session.add(user3)
        db.session.commit()
        
        profile = UserProfile(
            user_id=user3.id,
            employee_id='EMP003_Relationship'
        )
        
        db.session.add(profile)
        db.session.commit()
        
        assert profile.user is not None
        assert profile.user.id == user3.id

    def test_userprofile_salary_handling(self):
        """Test salary field functionality"""
        user4 = User(username='testuser_salary', email='<EMAIL>')
        db.session.add(user4)
        db.session.commit()
        
        profile = UserProfile(
            user_id=user4.id,
            employee_id='EMP004_Salary',
            salary=75000.50,
            salary_currency='USD'
        )
        
        db.session.add(profile)
        db.session.commit()
        
        assert profile.salary == 75000.50
        assert profile.salary_currency == 'USD'

    def test_userprofile_employment_details(self):
        """Test employment-related fields"""
        user5 = User(username='testuser_employment', email='<EMAIL>')
        db.session.add(user5)
        db.session.commit()
        
        profile = UserProfile(
            user_id=user5.id,
            employee_id='EMP005_Employment',
            employment_type='part_time',
            work_location='Office',
            weekly_hours=20.0,
            daily_hours=4.0,
            notice_period_days=15
        )
        
        db.session.add(profile)
        db.session.commit()
        
        assert profile.employment_type == 'part_time'
        assert profile.weekly_hours == 20.0
        assert profile.notice_period_days == 15

    def test_userprofile_emergency_contact(self):
        """Test emergency contact fields"""
        user6 = User(username='testuser_emergency', email='<EMAIL>')
        db.session.add(user6)
        db.session.commit()
        
        profile = UserProfile(
            user_id=user6.id,
            employee_id='EMP006_Emergency',
            emergency_contact_name='Jane Smith',
            emergency_contact_phone='+39 ************',
            emergency_contact_relationship='Sister'
        )
        
        db.session.add(profile)
        db.session.commit()
        
        assert profile.emergency_contact_name == 'Jane Smith'
        assert profile.emergency_contact_phone == '+39 ************'
        assert profile.emergency_contact_relationship == 'Sister'

    def test_userprofile_timestamps(self):
        """Test automatic timestamp handling"""
        user7 = User(username='testuser_timestamps', email='<EMAIL>')
        db.session.add(user7)
        db.session.commit()
        
        profile = UserProfile(
            user_id=user7.id,
            employee_id='EMP007_Timestamps'
        )
        
        db.session.add(profile)
        db.session.commit()
        
        assert profile.created_at is not None
        assert profile.updated_at is not None
        assert isinstance(profile.created_at, datetime)

    def test_userprofile_update_operations(self):
        """Test profile update operations"""
        user8 = User(username='testuser_update', email='<EMAIL>')
        db.session.add(user8)
        db.session.commit()
        
        profile = UserProfile(
            user_id=user8.id,
            employee_id='EMP008_Update',
            job_title='Junior Developer',
            salary=30000.0
        )
        
        db.session.add(profile)
        db.session.commit()
        
        # Update profile
        profile.job_title = 'Senior Developer'
        profile.salary = 60000.0
        profile.profile_completion = 90.0
        
        db.session.commit()
        
        updated_profile = UserProfile.query.get(profile.id)
        assert updated_profile.job_title == 'Senior Developer'
        assert updated_profile.salary == 60000.0
        assert updated_profile.profile_completion == 90.0

    def test_userprofile_query_by_employee_id(self):
        """Test querying by employee ID"""
        user9 = User(username='testuser_query', email='<EMAIL>')
        db.session.add(user9)
        db.session.commit()
        
        profile = UserProfile(
            user_id=user9.id,
            employee_id='UNIQUE_EMP_ID_QUERY'
        )
        
        db.session.add(profile)
        db.session.commit()
        
        found_profile = UserProfile.query.filter_by(employee_id='UNIQUE_EMP_ID_QUERY').first()
        assert found_profile is not None
        assert found_profile.id == profile.id

    def test_userprofile_deletion(self):
        """Test profile deletion"""
        user10 = User(username='testuser_delete', email='<EMAIL>')
        db.session.add(user10)
        db.session.commit()
        
        profile = UserProfile(
            user_id=user10.id,
            employee_id='EMP_TO_DELETE'
        )
        
        db.session.add(profile)
        db.session.commit()
        profile_id = profile.id
        
        db.session.delete(profile)
        db.session.commit()
        
        deleted_profile = UserProfile.query.get(profile_id)
        assert deleted_profile is None

    def test_userprofile_default_values(self):
        """Test default values for fields"""
        user11 = User(username='testuser_defaults', email='<EMAIL>')
        db.session.add(user11)
        db.session.commit()
        
        profile = UserProfile(
            user_id=user11.id,
            employee_id='EMP_DEFAULT_TEST'
        )
        
        db.session.add(profile)
        db.session.commit()
        
        assert profile.employment_type == 'full_time'
        assert profile.salary_currency == 'EUR'
        assert profile.notice_period_days == 30
        assert profile.weekly_hours == 40.0
        assert profile.daily_hours == 8.0
        assert profile.profile_completion == 0.0
