"""
Unit tests for ProjectResource model.
Tests business logic, validations, and model functionality.
"""

import pytest
from datetime import datetime, date
from models import ProjectResource, User, Project
from extensions import db


class TestProjectResourceModel:
    """Test suite for ProjectResource model unit tests"""

    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        """Setup test data for each test method"""
        self.app = app
        self.user = test_user
        
        # Create test project
        self.test_project = Project(
            name='Test Project Resource',
            description='Project for resource testing',
            status='active'
        )
        db.session.add(self.test_project)
        db.session.commit()

    def test_projectresource_creation_basic(self):
        """Test basic project resource creation"""
        resource = ProjectResource(
            project_id=self.test_project.id,
            user_id=self.user.id,
            allocation_percentage=100,
            role='Developer'
        )
        
        db.session.add(resource)
        db.session.commit()
        
        assert resource.id is not None
        assert resource.project_id == self.test_project.id
        assert resource.user_id == self.user.id
        assert resource.allocation_percentage == 100
        assert resource.role == 'Developer'

    def test_projectresource_relationships(self):
        """Test relationships with Project and User models"""
        resource = ProjectResource(
            project_id=self.test_project.id,
            user_id=self.user.id,
            role='Team Lead'
        )
        
        db.session.add(resource)
        db.session.commit()
        
        # Test forward relationships
        assert resource.project is not None
        assert resource.project.id == self.test_project.id
        assert resource.user is not None
        assert resource.user.id == self.user.id

    def test_projectresource_allocation_percentage(self):
        """Test allocation percentage functionality"""
        allocations = [25, 50, 75, 100]
        
        resources = []
        for i, allocation in enumerate(allocations):
            resource = ProjectResource(
                project_id=self.test_project.id,
                user_id=self.user.id,
                allocation_percentage=allocation,
                role=f'Role_{i}'
            )
            resources.append(resource)
        
        db.session.add_all(resources)
        db.session.commit()
        
        for resource, expected_allocation in zip(resources, allocations):
            assert resource.allocation_percentage == expected_allocation
            assert 0 <= resource.allocation_percentage <= 100

    def test_projectresource_role_types(self):
        """Test different role types"""
        roles = [
            'Project Manager',
            'Senior Developer',
            'Junior Developer',
            'Designer',
            'QA Engineer',
            'DevOps Engineer'
        ]
        
        resources = []
        for i, role in enumerate(roles):
            resource = ProjectResource(
                project_id=self.test_project.id,
                user_id=self.user.id,
                role=role,
                allocation_percentage=50
            )
            resources.append(resource)
        
        db.session.add_all(resources)
        db.session.commit()
        
        for resource, expected_role in zip(resources, roles):
            assert resource.role == expected_role

    def test_projectresource_default_allocation(self):
        """Test default allocation percentage"""
        resource = ProjectResource(
            project_id=self.test_project.id,
            user_id=self.user.id,
            role='Developer'
        )
        
        db.session.add(resource)
        db.session.commit()
        
        assert resource.allocation_percentage == 100  # Default value

    def test_projectresource_query_by_project(self):
        """Test querying resources by project"""
        resource = ProjectResource(
            project_id=self.test_project.id,
            user_id=self.user.id,
            role='Tester'
        )
        
        db.session.add(resource)
        db.session.commit()
        
        # Query by project
        project_resources = ProjectResource.query.filter_by(project_id=self.test_project.id).all()
        assert len(project_resources) >= 1
        assert resource in project_resources

    def test_projectresource_query_by_user(self):
        """Test querying resources by user"""
        resource = ProjectResource(
            project_id=self.test_project.id,
            user_id=self.user.id,
            role='Analyst'
        )
        
        db.session.add(resource)
        db.session.commit()
        
        # Query by user
        user_resources = ProjectResource.query.filter_by(user_id=self.user.id).all()
        assert len(user_resources) >= 1
        assert resource in user_resources

    def test_projectresource_query_by_role(self):
        """Test querying resources by role"""
        role_name = 'Unique_Role_Test'
        resource = ProjectResource(
            project_id=self.test_project.id,
            user_id=self.user.id,
            role=role_name
        )
        
        db.session.add(resource)
        db.session.commit()
        
        # Query by role
        role_resources = ProjectResource.query.filter_by(role=role_name).all()
        assert len(role_resources) == 1
        assert role_resources[0].id == resource.id

    def test_projectresource_allocation_calculations(self):
        """Test allocation percentage calculations"""
        resources = [
            ProjectResource(project_id=self.test_project.id, user_id=self.user.id, 
                          allocation_percentage=50, role='Dev1'),
            ProjectResource(project_id=self.test_project.id, user_id=self.user.id, 
                          allocation_percentage=30, role='Dev2'),
            ProjectResource(project_id=self.test_project.id, user_id=self.user.id, 
                          allocation_percentage=20, role='Dev3')
        ]
        
        db.session.add_all(resources)
        db.session.commit()
        
        # Calculate total allocation for user
        total_allocation = sum(r.allocation_percentage for r in resources)
        assert total_allocation == 100

    def test_projectresource_update_operations(self):
        """Test resource update operations"""
        resource = ProjectResource(
            project_id=self.test_project.id,
            user_id=self.user.id,
            allocation_percentage=50,
            role='Junior Developer'
        )
        
        db.session.add(resource)
        db.session.commit()
        
        # Update resource
        resource.allocation_percentage = 75
        resource.role = 'Senior Developer'
        
        db.session.commit()
        
        updated_resource = ProjectResource.query.get(resource.id)
        assert updated_resource.allocation_percentage == 75
        assert updated_resource.role == 'Senior Developer'

    def test_projectresource_deletion(self):
        """Test resource deletion"""
        resource = ProjectResource(
            project_id=self.test_project.id,
            user_id=self.user.id,
            role='To Delete'
        )
        
        db.session.add(resource)
        db.session.commit()
        resource_id = resource.id
        
        db.session.delete(resource)
        db.session.commit()
        
        deleted_resource = ProjectResource.query.get(resource_id)
        assert deleted_resource is None

    def test_projectresource_unique_constraints(self):
        """Test unique constraints if any"""
        resource1 = ProjectResource(
            project_id=self.test_project.id,
            user_id=self.user.id,
            role='Developer_Unique_1'
        )
        
        resource2 = ProjectResource(
            project_id=self.test_project.id,
            user_id=self.user.id,
            role='Developer_Unique_2'
        )
        
        db.session.add_all([resource1, resource2])
        db.session.commit()
        
        # Both should be created successfully
        assert resource1.id is not None
        assert resource2.id is not None

    def test_projectresource_allocation_validation(self):
        """Test allocation percentage validation"""
        # Test valid allocations
        valid_allocations = [0, 25, 50, 75, 100]
        
        for allocation in valid_allocations:
            resource = ProjectResource(
                project_id=self.test_project.id,
                user_id=self.user.id,
                allocation_percentage=allocation,
                role=f'Valid_{allocation}'
            )
            
            db.session.add(resource)
            db.session.commit()
            
            assert resource.allocation_percentage == allocation

    def test_projectresource_role_length(self):
        """Test role field length"""
        long_role = 'Very Long Role Name That Tests Field Length'
        
        resource = ProjectResource(
            project_id=self.test_project.id,
            user_id=self.user.id,
            role=long_role
        )
        
        db.session.add(resource)
        db.session.commit()
        
        assert resource.role == long_role
        assert len(resource.role) <= 50  # Based on VARCHAR(50) constraint

    def test_projectresource_multiple_projects(self):
        """Test user assigned to multiple projects"""
        # Create another project
        project2 = Project(
            name='Second Project',
            description='Another project',
            status='active'
        )
        db.session.add(project2)
        db.session.commit()
        
        # Assign user to both projects
        resource1 = ProjectResource(
            project_id=self.test_project.id,
            user_id=self.user.id,
            allocation_percentage=60,
            role='Developer'
        )
        
        resource2 = ProjectResource(
            project_id=project2.id,
            user_id=self.user.id,
            allocation_percentage=40,
            role='Consultant'
        )
        
        db.session.add_all([resource1, resource2])
        db.session.commit()
        
        # Verify user is assigned to both projects
        user_projects = ProjectResource.query.filter_by(user_id=self.user.id).all()
        assert len(user_projects) >= 2
        
        project_ids = [r.project_id for r in user_projects]
        assert self.test_project.id in project_ids
        assert project2.id in project_ids
