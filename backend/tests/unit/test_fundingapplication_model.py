"""Unit tests for FundingApplication model."""
import pytest
from models import FundingApplication, FundingOpportunity, User
from extensions import db

class TestFundingApplicationModel:
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        self.app = app
        self.user = test_user
        
        self.opportunity = FundingOpportunity.query.first()
        if not self.opportunity:
            self.opportunity = FundingOpportunity(title='Test Funding', description='Test')
            db.session.add(self.opportunity)
            db.session.commit()

    def test_fundingapplication_creation_basic(self):
        application = FundingApplication(
            opportunity_id=self.opportunity.id,
            applicant_id=self.user.id,
            status='submitted',
            requested_amount=50000.0
        )
        db.session.add(application)
        db.session.commit()
        
        assert application.id is not None
        assert application.opportunity_id == self.opportunity.id
        assert application.status == 'submitted'

    def test_fundingapplication_deletion(self):
        application = FundingApplication(opportunity_id=self.opportunity.id, applicant_id=self.user.id)
        db.session.add(application)
        db.session.commit()
        app_id = application.id
        
        db.session.delete(application)
        db.session.commit()
        
        deleted = FundingApplication.query.get(app_id)
        assert deleted is None
