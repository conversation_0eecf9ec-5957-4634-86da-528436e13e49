"""Unit tests for TechnicalOffer model."""
import pytest
from models import <PERSON>Offer, User
from extensions import db

class TestTechnicalOfferModel:
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        self.app = app
        self.user = test_user

    def test_technicaloffer_creation_basic(self):
        offer = TechnicalOffer(
            title='Test Technical Offer',
            description='This is a test technical offer',
            technology='Python',
            created_by=self.user.id
        )
        db.session.add(offer)
        db.session.commit()
        
        assert offer.id is not None
        assert offer.title == 'Test Technical Offer'
        assert offer.technology == 'Python'

    def test_technicaloffer_deletion(self):
        offer = TechnicalOffer(title='To Delete', description='Delete me', created_by=self.user.id)
        db.session.add(offer)
        db.session.commit()
        offer_id = offer.id
        
        db.session.delete(offer)
        db.session.commit()
        
        deleted = TechnicalOffer.query.get(offer_id)
        assert deleted is None
