"""
Unit tests for TaskDependency model.
Tests business logic, validations, and model functionality.
"""

import pytest
from datetime import datetime, date, timedelta
from models import TaskDependency, Task, Project, User
from extensions import db


class TestTaskDependencyModel:
    """Test suite for TaskDependency model unit tests"""

    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        """Setup test data for each test method"""
        self.app = app
        self.user = test_user
        
        # Create test project
        self.test_project = Project(
            name='Test Project',
            description='Project for testing task dependencies',
            status='active',
            start_date=date.today(),
            end_date=date.today() + timedelta(days=90)
        )
        db.session.add(self.test_project)
        db.session.commit()
        
        # Create test tasks (using correct field names from DB schema)
        self.task_a = Task(
            name='Task A',
            description='First task',
            project_id=self.test_project.id,
            assignee_id=self.user.id,
            status='todo',
            start_date=date.today(),
            due_date=date.today() + timedelta(days=10)
        )

        self.task_b = Task(
            name='Task B',
            description='Second task',
            project_id=self.test_project.id,
            assignee_id=self.user.id,
            status='todo',
            start_date=date.today() + timedelta(days=5),
            due_date=date.today() + timedelta(days=15)
        )
        
        db.session.add_all([self.task_a, self.task_b])
        db.session.commit()

    def test_task_dependency_creation_basic(self):
        """Test basic task dependency creation"""
        dependency = TaskDependency(
            task_id=self.task_b.id,
            depends_on_id=self.task_a.id
        )
        
        db.session.add(dependency)
        db.session.commit()
        
        assert dependency.id is not None
        assert dependency.task_id == self.task_b.id
        assert dependency.depends_on_id == self.task_a.id

    def test_task_dependency_relationships(self):
        """Test relationships with Task model"""
        dependency = TaskDependency(
            task_id=self.task_b.id,
            depends_on_id=self.task_a.id
        )
        
        db.session.add(dependency)
        db.session.commit()
        
        # Test forward relationships
        assert dependency.task is not None
        assert dependency.task.id == self.task_b.id
        assert dependency.task.title == 'Task B'
        
        assert dependency.depends_on is not None
        assert dependency.depends_on.id == self.task_a.id
        assert dependency.depends_on.title == 'Task A'
        
        # Test backward relationships
        assert dependency in self.task_b.dependencies
        assert dependency in self.task_a.dependents

    def test_task_dependency_repr_method(self):
        """Test string representation of task dependency"""
        dependency = TaskDependency(
            task_id=self.task_b.id,
            depends_on_id=self.task_a.id
        )
        
        expected_repr = f'<TaskDependency {self.task_b.id} depends on {self.task_a.id}>'
        assert repr(dependency) == expected_repr

    def test_task_dependency_foreign_key_constraints(self):
        """Test foreign key constraints"""
        # Test with non-existent task_id
        with pytest.raises(Exception):  # Should raise IntegrityError
            dependency = TaskDependency(
                task_id=99999,  # Non-existent task
                depends_on_id=self.task_a.id
            )
            db.session.add(dependency)
            db.session.commit()
        
        db.session.rollback()
        
        # Test with non-existent depends_on_id
        with pytest.raises(Exception):  # Should raise IntegrityError
            dependency = TaskDependency(
                task_id=self.task_b.id,
                depends_on_id=99999  # Non-existent task
            )
            db.session.add(dependency)
            db.session.commit()

    def test_task_dependency_self_reference_prevention(self):
        """Test that a task cannot depend on itself"""
        # This should be prevented by business logic or database constraints
        dependency = TaskDependency(
            task_id=self.task_a.id,
            depends_on_id=self.task_a.id  # Self-reference
        )
        
        db.session.add(dependency)
        
        # This should either raise an error or be handled by validation
        try:
            db.session.commit()
            # If no constraint, we should add business logic validation
            assert dependency.task_id != dependency.depends_on_id, "Self-dependency should be prevented"
        except Exception:
            # If constraint exists, this is expected
            db.session.rollback()
            assert True

    def test_task_dependency_circular_detection(self):
        """Test detection of circular dependencies"""
        # Create a third task
        task_c = Task(
            title='Task C',
            description='Third task',
            project_id=self.test_project.id,
            assigned_to=self.user.id,
            status='todo',
            start_date=date.today() + timedelta(days=10),
            due_date=date.today() + timedelta(days=20)
        )
        db.session.add(task_c)
        db.session.commit()
        
        # Create dependencies: A -> B -> C
        dep1 = TaskDependency(task_id=self.task_b.id, depends_on_id=self.task_a.id)
        dep2 = TaskDependency(task_id=task_c.id, depends_on_id=self.task_b.id)
        
        db.session.add_all([dep1, dep2])
        db.session.commit()
        
        # Try to create circular dependency: C -> A (would create A -> B -> C -> A)
        circular_dep = TaskDependency(task_id=self.task_a.id, depends_on_id=task_c.id)
        
        db.session.add(circular_dep)
        
        # This should be prevented by business logic
        try:
            db.session.commit()
            # If no constraint, we should add business logic validation
            print("Warning: Circular dependency was allowed - should implement validation")
        except Exception:
            # If constraint exists, this is expected
            db.session.rollback()
            assert True

    def test_task_dependency_multiple_dependencies(self):
        """Test task with multiple dependencies"""
        # Create additional tasks
        task_c = Task(
            title='Task C',
            description='Third task',
            project_id=self.test_project.id,
            assigned_to=self.user.id,
            status='todo'
        )
        
        task_d = Task(
            title='Task D',
            description='Fourth task that depends on A, B, and C',
            project_id=self.test_project.id,
            assigned_to=self.user.id,
            status='todo'
        )
        
        db.session.add_all([task_c, task_d])
        db.session.commit()
        
        # Create multiple dependencies for task_d
        dependencies = [
            TaskDependency(task_id=task_d.id, depends_on_id=self.task_a.id),
            TaskDependency(task_id=task_d.id, depends_on_id=self.task_b.id),
            TaskDependency(task_id=task_d.id, depends_on_id=task_c.id)
        ]
        
        db.session.add_all(dependencies)
        db.session.commit()
        
        # Verify multiple dependencies
        task_d_dependencies = TaskDependency.query.filter_by(task_id=task_d.id).all()
        assert len(task_d_dependencies) == 3
        
        dependency_task_ids = [dep.depends_on_id for dep in task_d_dependencies]
        assert self.task_a.id in dependency_task_ids
        assert self.task_b.id in dependency_task_ids
        assert task_c.id in dependency_task_ids

    def test_task_dependency_query_operations(self):
        """Test various query operations on task dependencies"""
        # Create several dependencies
        task_c = Task(
            title='Task C',
            project_id=self.test_project.id,
            assigned_to=self.user.id,
            status='todo'
        )
        db.session.add(task_c)
        db.session.commit()
        
        dependencies = [
            TaskDependency(task_id=self.task_b.id, depends_on_id=self.task_a.id),
            TaskDependency(task_id=task_c.id, depends_on_id=self.task_a.id),
            TaskDependency(task_id=task_c.id, depends_on_id=self.task_b.id)
        ]
        
        db.session.add_all(dependencies)
        db.session.commit()
        
        # Query dependencies for a specific task
        task_c_deps = TaskDependency.query.filter_by(task_id=task_c.id).all()
        assert len(task_c_deps) == 2
        
        # Query tasks that depend on task_a
        depends_on_a = TaskDependency.query.filter_by(depends_on_id=self.task_a.id).all()
        assert len(depends_on_a) == 2

    def test_task_dependency_deletion_cascade(self):
        """Test dependency deletion when tasks are deleted"""
        dependency = TaskDependency(
            task_id=self.task_b.id,
            depends_on_id=self.task_a.id
        )
        
        db.session.add(dependency)
        db.session.commit()
        dependency_id = dependency.id
        
        # Delete the dependent task
        db.session.delete(self.task_b)
        db.session.commit()
        
        # Verify dependency is also deleted (if cascade is set up)
        deleted_dependency = TaskDependency.query.get(dependency_id)
        # This depends on the cascade configuration
        # assert deleted_dependency is None

    def test_task_dependency_update_operations(self):
        """Test updating task dependencies"""
        dependency = TaskDependency(
            task_id=self.task_b.id,
            depends_on_id=self.task_a.id
        )
        
        db.session.add(dependency)
        db.session.commit()
        
        # Create a third task to change dependency to
        task_c = Task(
            title='Task C',
            project_id=self.test_project.id,
            assigned_to=self.user.id,
            status='todo'
        )
        db.session.add(task_c)
        db.session.commit()
        
        # Update dependency
        dependency.depends_on_id = task_c.id
        db.session.commit()
        
        # Verify update
        updated_dependency = TaskDependency.query.get(dependency.id)
        assert updated_dependency.depends_on_id == task_c.id
        assert updated_dependency.task_id == self.task_b.id

    def test_task_dependency_unique_constraint(self):
        """Test that duplicate dependencies are prevented"""
        dependency1 = TaskDependency(
            task_id=self.task_b.id,
            depends_on_id=self.task_a.id
        )
        
        dependency2 = TaskDependency(
            task_id=self.task_b.id,
            depends_on_id=self.task_a.id  # Duplicate
        )
        
        db.session.add(dependency1)
        db.session.commit()
        
        # Try to add duplicate dependency
        db.session.add(dependency2)
        try:
            db.session.commit()
            # If no constraint, both will exist (might be allowed)
            duplicates = TaskDependency.query.filter_by(
                task_id=self.task_b.id,
                depends_on_id=self.task_a.id
            ).all()
            # Could be 1 or 2 depending on constraints
            assert len(duplicates) >= 1
        except Exception:
            # If constraint exists, this is expected
            db.session.rollback()
            assert True

    def test_task_dependency_cross_project_prevention(self):
        """Test that dependencies across different projects are handled"""
        # Create another project and task
        other_project = Project(
            name='Other Project',
            description='Different project',
            status='active',
            start_date=date.today(),
            end_date=date.today() + timedelta(days=60)
        )
        db.session.add(other_project)
        db.session.commit()
        
        other_task = Task(
            title='Other Task',
            project_id=other_project.id,
            assigned_to=self.user.id,
            status='todo'
        )
        db.session.add(other_task)
        db.session.commit()
        
        # Try to create cross-project dependency
        cross_dependency = TaskDependency(
            task_id=self.task_b.id,  # From test_project
            depends_on_id=other_task.id  # From other_project
        )
        
        db.session.add(cross_dependency)
        
        try:
            db.session.commit()
            # If allowed, verify it was created
            assert cross_dependency.id is not None
            print("Cross-project dependencies are allowed")
        except Exception:
            # If prevented by constraints, this is expected
            db.session.rollback()
            print("Cross-project dependencies are prevented")
            assert True
