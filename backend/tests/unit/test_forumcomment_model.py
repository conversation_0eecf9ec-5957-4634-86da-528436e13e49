"""
Unit tests for ForumComment model.
Tests business logic, validations, and model functionality.
"""

import pytest
from datetime import datetime
from models import ForumComment, ForumTopic, User
from extensions import db


class TestForumCommentModel:
    """Test suite for ForumComment model unit tests"""

    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        """Setup test data for each test method"""
        self.app = app
        self.user = test_user
        
        # Create test forum topic
        self.test_topic = ForumTopic(
            title='Test Forum Topic',
            content='This is a test topic for comments',
            author_id=self.user.id,
            category='General'
        )
        db.session.add(self.test_topic)
        db.session.commit()

    def test_forumcomment_creation_basic(self):
        """Test basic forum comment creation"""
        comment = ForumComment(
            topic_id=self.test_topic.id,
            author_id=self.user.id,
            content='This is a test comment'
        )
        
        db.session.add(comment)
        db.session.commit()
        
        assert comment.id is not None
        assert comment.topic_id == self.test_topic.id
        assert comment.author_id == self.user.id
        assert comment.content == 'This is a test comment'

    def test_forumcomment_relationships(self):
        """Test relationships with ForumTopic and User models"""
        comment = ForumComment(
            topic_id=self.test_topic.id,
            author_id=self.user.id,
            content='Relationship test comment'
        )
        
        db.session.add(comment)
        db.session.commit()
        
        # Test forward relationships
        assert comment.topic is not None
        assert comment.topic.id == self.test_topic.id
        assert comment.author is not None
        assert comment.author.id == self.user.id
        
        # Test backward relationships
        assert comment in self.test_topic.comments
        assert comment in self.user.forum_comments

    def test_forumcomment_reply_functionality(self):
        """Test parent-child comment relationships"""
        # Create parent comment
        parent_comment = ForumComment(
            topic_id=self.test_topic.id,
            author_id=self.user.id,
            content='This is a parent comment'
        )
        
        db.session.add(parent_comment)
        db.session.commit()
        
        # Create reply comment
        reply_comment = ForumComment(
            topic_id=self.test_topic.id,
            author_id=self.user.id,
            content='This is a reply to the parent comment',
            parent_comment_id=parent_comment.id
        )
        
        db.session.add(reply_comment)
        db.session.commit()
        
        # Test parent-child relationship
        assert reply_comment.parent_comment_id == parent_comment.id
        assert reply_comment.parent_comment is not None
        assert reply_comment.parent_comment.id == parent_comment.id
        assert reply_comment in parent_comment.replies

    def test_forumcomment_content_handling(self):
        """Test content field for various text lengths"""
        contents = [
            'Short comment',
            'Medium length comment with more details and information',
            '''Very long comment with multiple paragraphs and extensive content.
            
            This comment spans multiple lines and includes various formatting.
            It tests the ability to store longer text content in the database.
            
            The comment system should handle this type of content gracefully.'''
        ]
        
        comments = []
        for i, content in enumerate(contents):
            comment = ForumComment(
                topic_id=self.test_topic.id,
                author_id=self.user.id,
                content=content
            )
            comments.append(comment)
        
        db.session.add_all(comments)
        db.session.commit()
        
        for comment, expected_content in zip(comments, contents):
            assert comment.content == expected_content

    def test_forumcomment_edit_functionality(self):
        """Test comment editing functionality"""
        comment = ForumComment(
            topic_id=self.test_topic.id,
            author_id=self.user.id,
            content='Original comment content'
        )
        
        db.session.add(comment)
        db.session.commit()
        
        # Initially not edited
        assert comment.is_edited is False
        assert comment.edited_at is None
        
        # Edit the comment
        comment.content = 'Edited comment content'
        comment.is_edited = True
        comment.edited_at = datetime.now()
        
        db.session.commit()
        
        # Verify edit
        assert comment.content == 'Edited comment content'
        assert comment.is_edited is True
        assert comment.edited_at is not None

    def test_forumcomment_timestamps(self):
        """Test automatic timestamp handling"""
        comment = ForumComment(
            topic_id=self.test_topic.id,
            author_id=self.user.id,
            content='Timestamp test comment'
        )
        
        db.session.add(comment)
        db.session.commit()
        
        # Test created_at is set
        assert comment.created_at is not None
        assert isinstance(comment.created_at, datetime)
        
        # Test updated_at is set
        assert comment.updated_at is not None
        assert isinstance(comment.updated_at, datetime)

    def test_forumcomment_query_by_topic(self):
        """Test querying comments by topic"""
        comments = [
            ForumComment(topic_id=self.test_topic.id, author_id=self.user.id, content='Comment 1'),
            ForumComment(topic_id=self.test_topic.id, author_id=self.user.id, content='Comment 2'),
            ForumComment(topic_id=self.test_topic.id, author_id=self.user.id, content='Comment 3')
        ]
        
        db.session.add_all(comments)
        db.session.commit()
        
        # Query comments by topic
        topic_comments = ForumComment.query.filter_by(topic_id=self.test_topic.id).all()
        
        assert len(topic_comments) >= 3
        comment_contents = [c.content for c in topic_comments]
        assert 'Comment 1' in comment_contents
        assert 'Comment 2' in comment_contents
        assert 'Comment 3' in comment_contents

    def test_forumcomment_query_by_author(self):
        """Test querying comments by author"""
        comment = ForumComment(
            topic_id=self.test_topic.id,
            author_id=self.user.id,
            content='Author query test comment'
        )
        
        db.session.add(comment)
        db.session.commit()
        
        # Query comments by author
        author_comments = ForumComment.query.filter_by(author_id=self.user.id).all()
        
        assert len(author_comments) >= 1
        assert comment in author_comments

    def test_forumcomment_nested_replies(self):
        """Test multiple levels of nested replies"""
        # Level 1: Original comment
        level1_comment = ForumComment(
            topic_id=self.test_topic.id,
            author_id=self.user.id,
            content='Level 1 comment'
        )
        
        db.session.add(level1_comment)
        db.session.commit()
        
        # Level 2: Reply to level 1
        level2_comment = ForumComment(
            topic_id=self.test_topic.id,
            author_id=self.user.id,
            content='Level 2 reply',
            parent_comment_id=level1_comment.id
        )
        
        db.session.add(level2_comment)
        db.session.commit()
        
        # Level 3: Reply to level 2
        level3_comment = ForumComment(
            topic_id=self.test_topic.id,
            author_id=self.user.id,
            content='Level 3 reply',
            parent_comment_id=level2_comment.id
        )
        
        db.session.add(level3_comment)
        db.session.commit()
        
        # Test nested structure
        assert level2_comment.parent_comment_id == level1_comment.id
        assert level3_comment.parent_comment_id == level2_comment.id
        assert level1_comment.parent_comment_id is None

    def test_forumcomment_update_operations(self):
        """Test comment update operations"""
        comment = ForumComment(
            topic_id=self.test_topic.id,
            author_id=self.user.id,
            content='Original content'
        )
        
        db.session.add(comment)
        db.session.commit()
        
        # Update comment
        comment.content = 'Updated content'
        comment.is_edited = True
        comment.edited_at = datetime.now()
        
        db.session.commit()
        
        # Verify updates
        updated_comment = ForumComment.query.get(comment.id)
        assert updated_comment.content == 'Updated content'
        assert updated_comment.is_edited is True
        assert updated_comment.edited_at is not None

    def test_forumcomment_deletion(self):
        """Test comment deletion"""
        comment = ForumComment(
            topic_id=self.test_topic.id,
            author_id=self.user.id,
            content='Comment to delete'
        )
        
        db.session.add(comment)
        db.session.commit()
        comment_id = comment.id
        
        # Delete comment
        db.session.delete(comment)
        db.session.commit()
        
        # Verify deletion
        deleted_comment = ForumComment.query.get(comment_id)
        assert deleted_comment is None

    def test_forumcomment_default_values(self):
        """Test default values for fields"""
        comment = ForumComment(
            topic_id=self.test_topic.id,
            author_id=self.user.id,
            content='Default values test'
        )
        
        db.session.add(comment)
        db.session.commit()
        
        # Check default values
        assert comment.is_edited is False
        assert comment.edited_at is None
        assert comment.parent_comment_id is None
        assert comment.created_at is not None
        assert comment.updated_at is not None

    def test_forumcomment_content_search(self):
        """Test searching comments by content"""
        comments = [
            ForumComment(topic_id=self.test_topic.id, author_id=self.user.id, 
                        content='Python programming discussion'),
            ForumComment(topic_id=self.test_topic.id, author_id=self.user.id, 
                        content='JavaScript development tips'),
            ForumComment(topic_id=self.test_topic.id, author_id=self.user.id, 
                        content='Database design patterns')
        ]
        
        db.session.add_all(comments)
        db.session.commit()
        
        # Search for comments containing 'programming'
        programming_comments = ForumComment.query.filter(
            ForumComment.content.contains('programming')
        ).all()
        
        assert len(programming_comments) >= 1
        assert any('Python programming' in c.content for c in programming_comments)

    def test_forumcomment_chronological_order(self):
        """Test ordering comments chronologically"""
        comments = []
        for i in range(3):
            comment = ForumComment(
                topic_id=self.test_topic.id,
                author_id=self.user.id,
                content=f'Chronological comment {i}'
            )
            comments.append(comment)
            db.session.add(comment)
            db.session.commit()
        
        # Query comments ordered by creation time
        ordered_comments = ForumComment.query.filter_by(
            topic_id=self.test_topic.id
        ).order_by(ForumComment.created_at).all()
        
        # Verify chronological order
        for i in range(len(ordered_comments) - 1):
            assert ordered_comments[i].created_at <= ordered_comments[i + 1].created_at
