"""
Unit tests for Contact model.
Tests business logic, validations, and model functionality.
"""

import pytest
from datetime import datetime, date
from models import Contact, Client, User
from extensions import db


class TestContactModel:
    """Test suite for Contact model unit tests"""

    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        """Setup test data for each test method"""
        self.app = app
        self.user = test_user
        
        # Create test client
        self.test_client = Client(
            name='Test Client Company',
            status='active',
            industry='Technology'
        )
        db.session.add(self.test_client)
        db.session.commit()

    def test_contact_creation_basic(self):
        """Test basic contact creation with required fields"""
        contact = Contact(
            client_id=self.test_client.id,
            first_name='<PERSON>',
            last_name='<PERSON><PERSON>',
            email='<EMAIL>'
        )
        
        db.session.add(contact)
        db.session.commit()
        
        assert contact.id is not None
        assert contact.first_name == '<PERSON>'
        assert contact.last_name == '<PERSON><PERSON>'
        assert contact.email == '<EMAIL>'
        assert contact.client_id == self.test_client.id

    def test_contact_creation_complete(self):
        """Test contact creation with all fields"""
        contact = Contact(
            client_id=self.test_client.id,
            first_name='<PERSON>',
            last_name='<PERSON>',
            position='CTO',
            email='<EMAIL>',
            phone='+39 ************',
            notes='Primary technical contact for the project'
        )
        
        db.session.add(contact)
        db.session.commit()
        
        assert contact.position == 'CTO'
        assert contact.phone == '+39 ************'
        assert contact.notes == 'Primary technical contact for the project'
        assert contact.created_at is not None
        assert contact.updated_at is not None

    def test_contact_full_name_property(self):
        """Test full_name property calculation"""
        contact = Contact(
            client_id=self.test_client.id,
            first_name='Maria',
            last_name='Rossi'
        )
        
        assert contact.full_name == 'Maria Rossi'

    def test_contact_repr_method(self):
        """Test string representation of contact"""
        contact = Contact(
            client_id=self.test_client.id,
            first_name='Marco',
            last_name='Bianchi'
        )
        
        expected_repr = '<Contact Marco Bianchi>'
        assert repr(contact) == expected_repr

    def test_contact_client_relationship(self):
        """Test relationship with Client model"""
        contact = Contact(
            client_id=self.test_client.id,
            first_name='Alice',
            last_name='Johnson'
        )
        
        db.session.add(contact)
        db.session.commit()
        
        # Test forward relationship
        assert contact.client is not None
        assert contact.client.id == self.test_client.id
        assert contact.client.name == 'Test Client Company'
        
        # Test backward relationship
        assert contact in self.test_client.contacts

    def test_contact_required_fields_validation(self):
        """Test that required fields are enforced"""
        # Test missing client_id
        contact = Contact(
            first_name='Test',
            last_name='User'
        )
        db.session.add(contact)

        with pytest.raises(Exception):  # Should raise IntegrityError
            db.session.commit()

        # Clean up the failed transaction
        db.session.rollback()

    def test_contact_foreign_key_constraint(self):
        """Test foreign key constraint with non-existent client"""
        contact = Contact(
            client_id=99999,  # Non-existent client
            first_name='Test',
            last_name='User'
        )
        db.session.add(contact)

        with pytest.raises(Exception):  # Should raise IntegrityError
            db.session.commit()

        # Clean up the failed transaction
        db.session.rollback()

    def test_contact_timestamps(self):
        """Test automatic timestamp handling"""
        contact = Contact(
            client_id=self.test_client.id,
            first_name='Time',
            last_name='Test'
        )
        
        db.session.add(contact)
        db.session.commit()
        
        # Test created_at is set
        assert contact.created_at is not None
        assert isinstance(contact.created_at, datetime)
        
        # Test updated_at is set
        assert contact.updated_at is not None
        assert isinstance(contact.updated_at, datetime)
        
        # Test updated_at changes on update
        original_updated_at = contact.updated_at
        contact.notes = 'Updated notes'
        db.session.commit()
        
        assert contact.updated_at > original_updated_at

    def test_contact_email_field(self):
        """Test email field handling"""
        contact = Contact(
            client_id=self.test_client.id,
            first_name='Email',
            last_name='Test',
            email='<EMAIL>'
        )
        
        db.session.add(contact)
        db.session.commit()
        
        assert contact.email == '<EMAIL>'

    def test_contact_phone_field(self):
        """Test phone field handling"""
        contact = Contact(
            client_id=self.test_client.id,
            first_name='Phone',
            last_name='Test',
            phone='+39 ************'
        )
        
        db.session.add(contact)
        db.session.commit()
        
        assert contact.phone == '+39 ************'

    def test_contact_position_field(self):
        """Test position field handling"""
        contact = Contact(
            client_id=self.test_client.id,
            first_name='Position',
            last_name='Test',
            position='Senior Developer'
        )
        
        db.session.add(contact)
        db.session.commit()
        
        assert contact.position == 'Senior Developer'

    def test_contact_notes_field(self):
        """Test notes field for long text"""
        long_notes = "This is a very long note " * 50  # Long text
        
        contact = Contact(
            client_id=self.test_client.id,
            first_name='Notes',
            last_name='Test',
            notes=long_notes
        )
        
        db.session.add(contact)
        db.session.commit()
        
        assert contact.notes == long_notes

    def test_contact_query_by_client(self):
        """Test querying contacts by client"""
        # Create multiple contacts for the same client
        contact1 = Contact(
            client_id=self.test_client.id,
            first_name='Contact',
            last_name='One'
        )
        contact2 = Contact(
            client_id=self.test_client.id,
            first_name='Contact',
            last_name='Two'
        )
        
        db.session.add_all([contact1, contact2])
        db.session.commit()
        
        # Query contacts by client
        client_contacts = Contact.query.filter_by(client_id=self.test_client.id).all()
        
        assert len(client_contacts) == 2
        assert contact1 in client_contacts
        assert contact2 in client_contacts

    def test_contact_query_by_name(self):
        """Test querying contacts by name"""
        contact = Contact(
            client_id=self.test_client.id,
            first_name='Unique',
            last_name='Name'
        )
        
        db.session.add(contact)
        db.session.commit()
        
        # Query by first name
        found_contact = Contact.query.filter_by(first_name='Unique').first()
        assert found_contact is not None
        assert found_contact.id == contact.id
        
        # Query by last name
        found_contact = Contact.query.filter_by(last_name='Name').first()
        assert found_contact is not None
        assert found_contact.id == contact.id

    def test_contact_update_operations(self):
        """Test contact update operations"""
        contact = Contact(
            client_id=self.test_client.id,
            first_name='Original',
            last_name='Name',
            email='<EMAIL>'
        )
        
        db.session.add(contact)
        db.session.commit()
        
        # Update contact
        contact.first_name = 'Updated'
        contact.email = '<EMAIL>'
        contact.position = 'New Position'
        
        db.session.commit()
        
        # Verify updates
        updated_contact = Contact.query.get(contact.id)
        assert updated_contact.first_name == 'Updated'
        assert updated_contact.email == '<EMAIL>'
        assert updated_contact.position == 'New Position'
        assert updated_contact.last_name == 'Name'  # Unchanged

    def test_contact_deletion(self):
        """Test contact deletion"""
        contact = Contact(
            client_id=self.test_client.id,
            first_name='To',
            last_name='Delete'
        )
        
        db.session.add(contact)
        db.session.commit()
        contact_id = contact.id
        
        # Delete contact
        db.session.delete(contact)
        db.session.commit()
        
        # Verify deletion
        deleted_contact = Contact.query.get(contact_id)
        assert deleted_contact is None
