"""Unit tests for CommunicationReaction model."""
import pytest
from models import CommunicationReaction, User
from extensions import db

class TestCommunicationReactionModel:
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        self.app = app
        self.user = test_user

    def test_communicationreaction_creation_basic(self):
        reaction = CommunicationReaction(
            user_id=self.user.id,
            content_type='post',
            content_id=123,
            reaction_type='like'
        )
        db.session.add(reaction)
        db.session.commit()
        
        assert reaction.id is not None
        assert reaction.user_id == self.user.id
        assert reaction.content_type == 'post'
        assert reaction.content_id == 123
        assert reaction.reaction_type == 'like'

    def test_communicationreaction_types(self):
        reaction_types = ['like', 'love', 'laugh', 'angry', 'sad']
        reactions = []
        
        for i, reaction_type in enumerate(reaction_types):
            reaction = CommunicationReaction(
                user_id=self.user.id,
                content_type='comment',
                content_id=i+1,
                reaction_type=reaction_type
            )
            reactions.append(reaction)
        
        db.session.add_all(reactions)
        db.session.commit()
        
        for reaction, expected_type in zip(reactions, reaction_types):
            assert reaction.reaction_type == expected_type

    def test_communicationreaction_deletion(self):
        reaction = CommunicationReaction(
            user_id=self.user.id,
            content_type='post',
            content_id=1,
            reaction_type='like'
        )
        db.session.add(reaction)
        db.session.commit()
        reaction_id = reaction.id
        
        db.session.delete(reaction)
        db.session.commit()
        
        deleted = CommunicationReaction.query.get(reaction_id)
        assert deleted is None
