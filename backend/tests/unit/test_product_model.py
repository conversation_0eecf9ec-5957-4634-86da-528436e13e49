"""
Unit tests for Product model.
Tests business logic, validations, and model functionality.
"""

import pytest
from datetime import datetime
from models import Product
from extensions import db


class TestProductModel:
    """Test suite for Product model unit tests"""

    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        """Setup test data for each test method"""
        self.app = app
        self.user = test_user

    def test_product_creation_basic(self):
        """Test basic product creation"""
        product = Product(
            name='Test Product Basic',
            description='A basic test product',
            category='Software',
            price=99.99,
            status='active'
        )
        
        db.session.add(product)
        db.session.commit()
        
        assert product.id is not None
        assert product.name == 'Test Product Basic'
        assert product.description == 'A basic test product'
        assert product.category == 'Software'
        assert product.price == 99.99
        assert product.status == 'active'

    def test_product_creation_minimal(self):
        """Test product creation with minimal required fields"""
        product = Product(
            name='Minimal Product'
        )
        
        db.session.add(product)
        db.session.commit()
        
        assert product.id is not None
        assert product.name == 'Minimal Product'
        assert product.created_at is not None

    def test_product_repr_method(self):
        """Test string representation of product"""
        product = Product(name='Repr Test Product')
        
        expected_repr = '<Product Repr Test Product>'
        assert repr(product) == expected_repr

    def test_product_price_handling(self):
        """Test price field functionality"""
        prices = [0.0, 9.99, 99.99, 999.99, 9999.99]
        
        products = []
        for i, price in enumerate(prices):
            product = Product(
                name=f'Price Test {i}',
                price=price
            )
            products.append(product)
        
        db.session.add_all(products)
        db.session.commit()
        
        for product, expected_price in zip(products, prices):
            assert product.price == expected_price
            assert product.price >= 0

    def test_product_categories(self):
        """Test different product categories"""
        categories = [
            'Software',
            'Hardware',
            'Services',
            'Consulting',
            'Training',
            'Support'
        ]
        
        products = []
        for i, category in enumerate(categories):
            product = Product(
                name=f'Category Test {i}',
                category=category
            )
            products.append(product)
        
        db.session.add_all(products)
        db.session.commit()
        
        for product, expected_category in zip(products, categories):
            assert product.category == expected_category

    def test_product_status_types(self):
        """Test different product status types"""
        statuses = ['active', 'inactive', 'discontinued', 'draft']
        
        products = []
        for i, status in enumerate(statuses):
            product = Product(
                name=f'Status Test {i}',
                status=status
            )
            products.append(product)
        
        db.session.add_all(products)
        db.session.commit()
        
        for product, expected_status in zip(products, statuses):
            assert product.status == expected_status

    def test_product_description_field(self):
        """Test description field for long text"""
        long_description = """
        This is a comprehensive product description that includes multiple
        features, benefits, and technical specifications. It demonstrates
        the ability to store detailed product information in the database.
        The description can include formatting, special characters, and
        extensive details about the product offering.
        """
        
        product = Product(
            name='Description Test Product',
            description=long_description.strip()
        )
        
        db.session.add(product)
        db.session.commit()
        
        assert product.description == long_description.strip()

    def test_product_timestamps(self):
        """Test automatic timestamp handling"""
        product = Product(
            name='Timestamp Test Product'
        )
        
        db.session.add(product)
        db.session.commit()
        
        # Test created_at is set
        assert product.created_at is not None
        assert isinstance(product.created_at, datetime)
        
        # Test updated_at is set
        assert product.updated_at is not None
        assert isinstance(product.updated_at, datetime)
        
        # Test updated_at changes on update
        original_updated_at = product.updated_at
        product.description = 'Updated description'
        db.session.commit()
        
        assert product.updated_at > original_updated_at

    def test_product_query_by_category(self):
        """Test querying products by category"""
        software_products = [
            Product(name='Software 1', category='Software'),
            Product(name='Software 2', category='Software')
        ]
        
        hardware_products = [
            Product(name='Hardware 1', category='Hardware')
        ]
        
        db.session.add_all(software_products + hardware_products)
        db.session.commit()
        
        # Query software products
        software_results = Product.query.filter_by(category='Software').all()
        software_names = [p.name for p in software_results]
        
        assert 'Software 1' in software_names
        assert 'Software 2' in software_names
        assert len([p for p in software_results if p.category == 'Software']) >= 2

    def test_product_query_by_status(self):
        """Test querying products by status"""
        active_products = [
            Product(name='Active 1', status='active'),
            Product(name='Active 2', status='active')
        ]
        
        inactive_products = [
            Product(name='Inactive 1', status='inactive')
        ]
        
        db.session.add_all(active_products + inactive_products)
        db.session.commit()
        
        # Query active products
        active_results = Product.query.filter_by(status='active').all()
        active_names = [p.name for p in active_results]
        
        assert 'Active 1' in active_names
        assert 'Active 2' in active_names

    def test_product_price_range_queries(self):
        """Test querying products by price range"""
        products = [
            Product(name='Cheap Product', price=10.0),
            Product(name='Medium Product', price=50.0),
            Product(name='Expensive Product', price=100.0)
        ]
        
        db.session.add_all(products)
        db.session.commit()
        
        # Query products under $60
        affordable_products = Product.query.filter(Product.price < 60.0).all()
        affordable_names = [p.name for p in affordable_products]
        
        assert 'Cheap Product' in affordable_names
        assert 'Medium Product' in affordable_names
        assert 'Expensive Product' not in affordable_names

    def test_product_search_functionality(self):
        """Test product search by name"""
        products = [
            Product(name='Advanced Software Solution'),
            Product(name='Basic Software Package'),
            Product(name='Hardware Component')
        ]
        
        db.session.add_all(products)
        db.session.commit()
        
        # Search for products containing 'Software'
        software_products = Product.query.filter(Product.name.contains('Software')).all()
        software_names = [p.name for p in software_products]
        
        assert 'Advanced Software Solution' in software_names
        assert 'Basic Software Package' in software_names
        assert 'Hardware Component' not in software_names

    def test_product_update_operations(self):
        """Test product update operations"""
        product = Product(
            name='Original Product Name',
            price=50.0,
            status='draft'
        )
        
        db.session.add(product)
        db.session.commit()
        
        # Update product
        product.name = 'Updated Product Name'
        product.price = 75.0
        product.status = 'active'
        product.description = 'Added description'
        
        db.session.commit()
        
        # Verify updates
        updated_product = Product.query.get(product.id)
        assert updated_product.name == 'Updated Product Name'
        assert updated_product.price == 75.0
        assert updated_product.status == 'active'
        assert updated_product.description == 'Added description'

    def test_product_deletion(self):
        """Test product deletion"""
        product = Product(
            name='Product To Delete'
        )
        
        db.session.add(product)
        db.session.commit()
        product_id = product.id
        
        # Delete product
        db.session.delete(product)
        db.session.commit()
        
        # Verify deletion
        deleted_product = Product.query.get(product_id)
        assert deleted_product is None

    def test_product_name_uniqueness(self):
        """Test product name handling"""
        product1 = Product(name='Unique Product Name 1')
        product2 = Product(name='Unique Product Name 2')
        
        db.session.add_all([product1, product2])
        db.session.commit()
        
        assert product1.id != product2.id
        assert product1.name != product2.name

    def test_product_optional_fields(self):
        """Test optional fields behavior"""
        product = Product(
            name='Optional Fields Test'
        )
        
        db.session.add(product)
        db.session.commit()
        
        # Check optional fields are None when not set
        assert product.description is None
        assert product.category is None
        assert product.price is None
        assert product.status == 'active'  # Default value from model

    def test_product_field_lengths(self):
        """Test field length constraints"""
        # Test name field (VARCHAR 128)
        long_name = 'A' * 128
        product = Product(name=long_name)
        
        db.session.add(product)
        db.session.commit()
        
        assert len(product.name) == 128
        
        # Test category field (VARCHAR 64)
        product.category = 'B' * 64
        db.session.commit()
        
        assert len(product.category) == 64
