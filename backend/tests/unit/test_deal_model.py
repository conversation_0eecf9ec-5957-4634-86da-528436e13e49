"""Unit tests for Deal model."""
import pytest
from models import Deal, Client, User
from extensions import db

class TestDealModel:
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        self.app = app
        self.user = test_user
        
        self.client = Client.query.first()
        if not self.client:
            self.client = Client(name='Test Client', email='<EMAIL>')
            db.session.add(self.client)
            db.session.commit()

    def test_deal_creation_basic(self):
        deal = Deal(
            name='Test Deal',
            client_id=self.client.id,
            value=25000.0,
            stage='negotiation'
        )
        db.session.add(deal)
        db.session.commit()
        
        assert deal.id is not None
        assert deal.name == 'Test Deal'
        assert deal.value == 25000.0

    def test_deal_deletion(self):
        deal = Deal(name='To Delete', client_id=self.client.id)
        db.session.add(deal)
        db.session.commit()
        deal_id = deal.id
        
        db.session.delete(deal)
        db.session.commit()
        
        deleted = Deal.query.get(deal_id)
        assert deleted is None
