"""Unit tests for FundingExpense model."""
import pytest
from models import FundingExpense, FundingApplication, User
from extensions import db

class TestFundingExpenseModel:
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        self.app = app
        self.user = test_user

    def test_fundingexpense_creation_basic(self):
        expense = FundingExpense(
            description='Test Expense',
            amount=1000.0,
            category='equipment'
        )
        db.session.add(expense)
        db.session.commit()
        
        assert expense.id is not None
        assert expense.description == 'Test Expense'
        assert expense.amount == 1000.0

    def test_fundingexpense_deletion(self):
        expense = FundingExpense(description='To Delete', amount=100.0)
        db.session.add(expense)
        db.session.commit()
        expense_id = expense.id
        
        db.session.delete(expense)
        db.session.commit()
        
        deleted = FundingExpense.query.get(expense_id)
        assert deleted is None
