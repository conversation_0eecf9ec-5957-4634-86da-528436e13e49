"""Unit tests for FundingExpense model."""
import pytest
from datetime import date
from models import FundingExpense, FundingApplication, FundingOpportunity, User
from extensions import db

class TestFundingExpenseModel:
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        self.app = app
        self.user = test_user

    def test_fundingexpense_creation_basic(self):
        # Creo prima una FundingApplication
        opportunity = FundingOpportunity(
            title='Test Opp',
            application_deadline=date(2024, 12, 31),
            created_by=1
        )
        db.session.add(opportunity)
        db.session.commit()

        application = FundingApplication(
            opportunity_id=opportunity.id,
            project_title='Test Project',
            requested_amount=50000.0,
            created_by=1
        )
        db.session.add(application)
        db.session.commit()

        expense = FundingExpense(
            application_id=application.id,  # Campo richiesto
            description='Test Expense',
            amount=1000.0,
            expense_date=date.today()  # Campo richiesto
        )
        db.session.add(expense)
        db.session.commit()
        
        assert expense.id is not None
        assert expense.description == 'Test Expense'
        assert expense.amount == 1000.0

    def test_fundingexpense_deletion(self):
        # Uso la stessa application del test precedente
        application = FundingApplication.query.first()
        expense = FundingExpense(
            application_id=application.id,
            description='To Delete',
            amount=100.0,
            expense_date=date.today()
        )
        db.session.add(expense)
        db.session.commit()
        expense_id = expense.id
        
        db.session.delete(expense)
        db.session.commit()
        
        deleted = FundingExpense.query.get(expense_id)
        assert deleted is None
