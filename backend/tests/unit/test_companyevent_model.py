"""Unit tests for CompanyEvent model."""
import pytest
from datetime import datetime, date
from models import CompanyEvent, User
from extensions import db

class TestCompanyEventModel:
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        self.app = app
        self.user = test_user

    def test_companyevent_creation_basic(self):
        event = CompanyEvent(
            title='Team Building',
            description='Annual team building event',
            start_date=datetime(2024, 6, 15, 10, 0),
            end_date=datetime(2024, 6, 15, 18, 0),
            created_by=self.user.id
        )
        db.session.add(event)
        db.session.commit()
        
        assert event.id is not None
        assert event.title == 'Team Building'
        assert event.description == 'Annual team building event'
        assert event.created_by == self.user.id

    def test_companyevent_location(self):
        event = CompanyEvent(
            title='Conference',
            description='Tech conference',
            start_date=datetime(2024, 7, 1, 9, 0),
            end_date=datetime(2024, 7, 1, 17, 0),
            location='Convention Center',
            created_by=self.user.id
        )
        db.session.add(event)
        db.session.commit()
        
        assert event.location == 'Convention Center'

    def test_companyevent_capacity(self):
        event = CompanyEvent(
            title='Workshop',
            description='Technical workshop',
            start_date=datetime(2024, 8, 1, 14, 0),
            end_date=datetime(2024, 8, 1, 16, 0),
            max_participants=20,
            created_by=self.user.id
        )
        db.session.add(event)
        db.session.commit()
        
        assert event.max_participants == 20

    def test_companyevent_deletion(self):
        event = CompanyEvent(
            title='To Delete',
            description='This will be deleted',
            start_date=datetime(2024, 9, 1, 10, 0),
            end_date=datetime(2024, 9, 1, 12, 0),
            created_by=self.user.id
        )
        db.session.add(event)
        db.session.commit()
        event_id = event.id
        
        db.session.delete(event)
        db.session.commit()
        
        deleted = CompanyEvent.query.get(event_id)
        assert deleted is None
