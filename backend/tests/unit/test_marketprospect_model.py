"""Unit tests for MarketProspect model."""
import pytest
from models import MarketProspect, User
from extensions import db

class TestMarketProspectModel:
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        self.app = app
        self.user = test_user

    def test_marketprospect_creation_basic(self):
        prospect = MarketProspect(
            name='Test Market Prospect',
            industry='Technology',
            market_size=1000000.0,
            created_by=self.user.id
        )
        db.session.add(prospect)
        db.session.commit()
        
        assert prospect.id is not None
        assert prospect.name == 'Test Market Prospect'
        assert prospect.market_size == 1000000.0

    def test_marketprospect_deletion(self):
        prospect = MarketProspect(name='To Delete', industry='Test', created_by=self.user.id)
        db.session.add(prospect)
        db.session.commit()
        prospect_id = prospect.id
        
        db.session.delete(prospect)
        db.session.commit()
        
        deleted = MarketProspect.query.get(prospect_id)
        assert deleted is None
