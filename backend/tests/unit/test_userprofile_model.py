"""
Unit tests for UserProfile model.
Tests business logic, validations, and model functionality.
"""

import pytest
from datetime import datetime, date
from models import UserProfile, User
from extensions import db


class TestUserProfileModel:
    """Test suite for UserProfile model unit tests"""

    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        """Setup test data for each test method"""
        self.app = app
        self.user = test_user

    def test_userprofile_creation_basic(self):
        """Test basic user profile creation"""
        profile = UserProfile(
            user_id=self.user.id,
            employee_id='EMP001',
            job_title='Software Developer'
        )
        
        db.session.add(profile)
        db.session.commit()
        
        assert profile.id is not None
        assert profile.user_id == self.user.id
        assert profile.employee_id == 'EMP001'
        assert profile.job_title == 'Software Developer'

    def test_userprofile_creation_complete(self):
        """Test user profile creation with all fields"""
        profile = UserProfile(
            user_id=self.user.id,
            employee_id='EMP002',
            job_title='Senior Developer',
            birth_date=date(1990, 5, 15),
            address='123 Main St, City',
            emergency_contact_name='<PERSON>',
            emergency_contact_phone='+39 ************',
            emergency_contact_relationship='Spouse',
            employment_type='full_time',
            work_location='Remote',
            salary=50000.0,
            salary_currency='EUR',
            weekly_hours=40.0,
            daily_hours=8.0,
            profile_completion=75.5
        )
        
        db.session.add(profile)
        db.session.commit()
        
        assert profile.birth_date == date(1990, 5, 15)
        assert profile.salary == 50000.0
        assert profile.profile_completion == 75.5
        assert profile.created_at is not None

    def test_userprofile_user_relationship(self):
        """Test relationship with User model"""
        profile = UserProfile(
            user_id=self.user.id,
            employee_id='EMP003'
        )
        
        db.session.add(profile)
        db.session.commit()
        
        assert profile.user is not None
        assert profile.user.id == self.user.id

    def test_userprofile_salary_handling(self):
        """Test salary field functionality"""
        profile = UserProfile(
            user_id=self.user.id,
            employee_id='EMP004',
            salary=75000.50,
            salary_currency='USD'
        )
        
        db.session.add(profile)
        db.session.commit()
        
        assert profile.salary == 75000.50
        assert profile.salary_currency == 'USD'

    def test_userprofile_employment_details(self):
        """Test employment-related fields"""
        profile = UserProfile(
            user_id=self.user.id,
            employee_id='EMP005',
            employment_type='part_time',
            work_location='Office',
            weekly_hours=20.0,
            daily_hours=4.0,
            notice_period_days=15
        )
        
        db.session.add(profile)
        db.session.commit()
        
        assert profile.employment_type == 'part_time'
        assert profile.weekly_hours == 20.0
        assert profile.notice_period_days == 15

    def test_userprofile_emergency_contact(self):
        """Test emergency contact fields"""
        profile = UserProfile(
            user_id=self.user.id,
            employee_id='EMP006',
            emergency_contact_name='Jane Smith',
            emergency_contact_phone='+39 ************',
            emergency_contact_relationship='Sister'
        )
        
        db.session.add(profile)
        db.session.commit()
        
        assert profile.emergency_contact_name == 'Jane Smith'
        assert profile.emergency_contact_phone == '+39 ************'
        assert profile.emergency_contact_relationship == 'Sister'

    def test_userprofile_contract_dates(self):
        """Test contract-related date fields"""
        profile = UserProfile(
            user_id=self.user.id,
            employee_id='EMP007',
            probation_end_date=date(2024, 12, 31),
            contract_end_date=date(2025, 12, 31)
        )
        
        db.session.add(profile)
        db.session.commit()
        
        assert profile.probation_end_date == date(2024, 12, 31)
        assert profile.contract_end_date == date(2025, 12, 31)

    def test_userprofile_cv_fields(self):
        """Test CV-related fields"""
        profile = UserProfile(
            user_id=self.user.id,
            employee_id='EMP008',
            current_cv_path='/uploads/cv/emp008.pdf',
            cv_last_updated=datetime.now(),
            cv_analysis_data='{"skills": ["Python", "JavaScript"]}'
        )
        
        db.session.add(profile)
        db.session.commit()
        
        assert profile.current_cv_path == '/uploads/cv/emp008.pdf'
        assert profile.cv_last_updated is not None
        assert 'Python' in profile.cv_analysis_data

    def test_userprofile_profile_completion(self):
        """Test profile completion percentage"""
        profile = UserProfile(
            user_id=self.user.id,
            employee_id='EMP009',
            profile_completion=85.5
        )
        
        db.session.add(profile)
        db.session.commit()
        
        assert profile.profile_completion == 85.5
        assert 0 <= profile.profile_completion <= 100

    def test_userprofile_timestamps(self):
        """Test automatic timestamp handling"""
        profile = UserProfile(
            user_id=self.user.id,
            employee_id='EMP010'
        )
        
        db.session.add(profile)
        db.session.commit()
        
        assert profile.created_at is not None
        assert profile.updated_at is not None
        assert isinstance(profile.created_at, datetime)

    def test_userprofile_update_operations(self):
        """Test profile update operations"""
        profile = UserProfile(
            user_id=self.user.id,
            employee_id='EMP011',
            job_title='Junior Developer',
            salary=30000.0
        )
        
        db.session.add(profile)
        db.session.commit()
        
        # Update profile
        profile.job_title = 'Senior Developer'
        profile.salary = 60000.0
        profile.profile_completion = 90.0
        
        db.session.commit()
        
        updated_profile = UserProfile.query.get(profile.id)
        assert updated_profile.job_title == 'Senior Developer'
        assert updated_profile.salary == 60000.0
        assert updated_profile.profile_completion == 90.0

    def test_userprofile_query_by_employee_id(self):
        """Test querying by employee ID"""
        profile = UserProfile(
            user_id=self.user.id,
            employee_id='UNIQUE_EMP_ID'
        )
        
        db.session.add(profile)
        db.session.commit()
        
        found_profile = UserProfile.query.filter_by(employee_id='UNIQUE_EMP_ID').first()
        assert found_profile is not None
        assert found_profile.id == profile.id

    def test_userprofile_query_by_employment_type(self):
        """Test querying by employment type"""
        profiles = [
            UserProfile(user_id=self.user.id, employee_id='FT1', employment_type='full_time'),
            UserProfile(user_id=self.user.id, employee_id='PT1', employment_type='part_time')
        ]
        
        db.session.add_all(profiles)
        db.session.commit()
        
        full_time_profiles = UserProfile.query.filter_by(employment_type='full_time').all()
        assert len(full_time_profiles) >= 1

    def test_userprofile_deletion(self):
        """Test profile deletion"""
        profile = UserProfile(
            user_id=self.user.id,
            employee_id='TO_DELETE'
        )
        
        db.session.add(profile)
        db.session.commit()
        profile_id = profile.id
        
        db.session.delete(profile)
        db.session.commit()
        
        deleted_profile = UserProfile.query.get(profile_id)
        assert deleted_profile is None

    def test_userprofile_default_values(self):
        """Test default values for fields"""
        profile = UserProfile(
            user_id=self.user.id,
            employee_id='DEFAULT_TEST'
        )
        
        db.session.add(profile)
        db.session.commit()
        
        assert profile.employment_type == 'full_time'
        assert profile.salary_currency == 'EUR'
        assert profile.notice_period_days == 30
        assert profile.weekly_hours == 40.0
        assert profile.daily_hours == 8.0
        assert profile.profile_completion == 0.0
