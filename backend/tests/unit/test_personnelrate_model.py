"""
Unit tests for PersonnelRate model.
Tests business logic, validations, and model functionality.
"""

import pytest
from datetime import datetime, date
from models import PersonnelRate, User
from extensions import db


class TestPersonnelRateModel:
    """Test suite for PersonnelRate model unit tests"""

    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        """Setup test data for each test method"""
        self.app = app
        self.user = test_user

    def test_personnelrate_creation_basic(self):
        """Test basic personnel rate creation"""
        rate = PersonnelRate(
            user_id=self.user.id,
            daily_rate=500.0,
            valid_from=date.today(),
            currency='EUR'
        )
        
        db.session.add(rate)
        db.session.commit()
        
        assert rate.id is not None
        assert rate.user_id == self.user.id
        assert rate.daily_rate == 500.0
        assert rate.valid_from == date.today()
        assert rate.currency == 'EUR'

    def test_personnelrate_relationships(self):
        """Test relationships with User model"""
        rate = PersonnelRate(
            user_id=self.user.id,
            daily_rate=400.0,
            valid_from=date.today()
        )
        
        db.session.add(rate)
        db.session.commit()
        
        # Test relationship exists (if defined in model)
        assert rate.user_id == self.user.id

    def test_personnelrate_date_ranges(self):
        """Test date range functionality"""
        rate = PersonnelRate(
            user_id=self.user.id,
            daily_rate=600.0,
            valid_from=date(2024, 1, 1),
            valid_to=date(2024, 12, 31)
        )
        
        db.session.add(rate)
        db.session.commit()
        
        assert rate.valid_from == date(2024, 1, 1)
        assert rate.valid_to == date(2024, 12, 31)

    def test_personnelrate_currency_handling(self):
        """Test currency field"""
        currencies = ['EUR', 'USD', 'GBP']
        
        rates = []
        for i, currency in enumerate(currencies):
            rate = PersonnelRate(
                user_id=self.user.id,
                daily_rate=100.0 + i * 50,
                valid_from=date(2024, i+1, 1),
                currency=currency
            )
            rates.append(rate)
        
        db.session.add_all(rates)
        db.session.commit()
        
        for rate, expected_currency in zip(rates, currencies):
            assert rate.currency == expected_currency

    def test_personnelrate_notes_functionality(self):
        """Test notes field"""
        rate = PersonnelRate(
            user_id=self.user.id,
            daily_rate=550.0,
            valid_from=date.today(),
            notes='Rate increase due to promotion'
        )
        
        db.session.add(rate)
        db.session.commit()
        
        assert rate.notes == 'Rate increase due to promotion'

    def test_personnelrate_timestamps(self):
        """Test automatic timestamp handling"""
        rate = PersonnelRate(
            user_id=self.user.id,
            daily_rate=450.0,
            valid_from=date.today()
        )
        
        db.session.add(rate)
        db.session.commit()
        
        assert rate.created_at is not None
        assert isinstance(rate.created_at, datetime)

    def test_personnelrate_query_by_user(self):
        """Test querying rates by user"""
        rate = PersonnelRate(
            user_id=self.user.id,
            daily_rate=350.0,
            valid_from=date.today()
        )
        
        db.session.add(rate)
        db.session.commit()
        
        user_rates = PersonnelRate.query.filter_by(user_id=self.user.id).all()
        assert len(user_rates) >= 1
        assert rate in user_rates

    def test_personnelrate_current_rate(self):
        """Test querying current rate (no valid_to)"""
        current_rate = PersonnelRate(
            user_id=self.user.id,
            daily_rate=700.0,
            valid_from=date.today(),
            valid_to=None  # Current rate
        )
        
        db.session.add(current_rate)
        db.session.commit()
        
        assert current_rate.valid_to is None

    def test_personnelrate_update_operations(self):
        """Test rate update operations"""
        rate = PersonnelRate(
            user_id=self.user.id,
            daily_rate=300.0,
            valid_from=date.today(),
            currency='EUR'
        )
        
        db.session.add(rate)
        db.session.commit()
        
        # Update rate
        rate.daily_rate = 400.0
        rate.currency = 'USD'
        rate.notes = 'Updated rate'
        
        db.session.commit()
        
        updated_rate = PersonnelRate.query.get(rate.id)
        assert updated_rate.daily_rate == 400.0
        assert updated_rate.currency == 'USD'
        assert updated_rate.notes == 'Updated rate'

    def test_personnelrate_deletion(self):
        """Test rate deletion"""
        rate = PersonnelRate(
            user_id=self.user.id,
            daily_rate=250.0,
            valid_from=date.today()
        )
        
        db.session.add(rate)
        db.session.commit()
        rate_id = rate.id
        
        db.session.delete(rate)
        db.session.commit()
        
        deleted_rate = PersonnelRate.query.get(rate_id)
        assert deleted_rate is None
