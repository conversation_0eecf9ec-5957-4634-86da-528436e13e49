"""
Test per i calcoli KPI basati sui nuovi campi dei task.
"""
import pytest
from datetime import datetime, date, timedelta
from decimal import Decimal

from models import Project, User, Task, TimesheetEntry, PersonnelRate
from utils.cost_calculator import calculate_project_kpis
from app import db


class TestTaskKPICalculations:
    """Test per i calcoli KPI basati su start_date e estimated_hours."""

    def test_task_estimation_accuracy_kpi(self, app, test_project, test_user):
        """Test KPI precisione stime task."""
        with app.app_context():
            project = Project.query.get(test_project)
            user = test_user

            # Crea rate per l'utente
            rate = PersonnelRate(
                user_id=user.id,
                hourly_rate=50.0,  # 400/8 ore = 50 €/ora
                start_date=date.today() - timedelta(days=30)
            )
            db.session.add(rate)

            # Crea task con stime diverse
            task1 = Task(
                name="Task Accurato",
                project_id=project.id,
                assignee_id=user.id,
                start_date=date.today() - timedelta(days=5),
                due_date=date.today() - timedelta(days=1),
                estimated_hours=32.0,
                status="completed"
            )

            task2 = Task(
                name="Task Sottostimato",
                project_id=project.id,
                assignee_id=user.id,
                start_date=date.today() - timedelta(days=10),
                due_date=date.today() - timedelta(days=6),
                estimated_hours=16.0,
                status="completed"
            )

            db.session.add_all([task1, task2])
            db.session.commit()

            # Aggiungi timesheet per task1 (stima accurata: 32h stimate, 32h effettive)
            for i in range(4):
                timesheet = TimesheetEntry(
                    user_id=user.id,
                    project_id=project.id,
                    task_id=task1.id,
                    date=date.today() - timedelta(days=5-i),
                    hours=8.0
                )
                db.session.add(timesheet)

            # Aggiungi timesheet per task2 (sottostimato: 16h stimate, 24h effettive)
            for i in range(3):
                timesheet = TimesheetEntry(
                    user_id=user.id,
                    project_id=project.id,
                    task_id=task2.id,
                    date=date.today() - timedelta(days=10-i),
                    hours=8.0
                )
                db.session.add(timesheet)

            db.session.commit()

            # Verifica proprietà calcolate dei task
            assert task1.actual_hours == 32.0
            assert task1.hours_variance == 0.0  # Perfetto
            assert task1.hours_efficiency == 100.0

            assert task2.actual_hours == 24.0
            assert task2.hours_variance == 8.0  # 8 ore in più
            assert abs(task2.hours_efficiency - 66.67) < 0.1  # (16/24)*100

    def test_task_timeline_adherence_kpi(self, app, test_project, test_user):
        """Test KPI rispetto timeline task."""
        with app.app_context():
            project = Project.query.get(test_project)
            user = test_user

            # Task completato in tempo
            task_on_time = Task(
                name="Task In Tempo",
                project_id=project.id,
                assignee_id=user.id,
                start_date=date.today() - timedelta(days=7),
                due_date=date.today() - timedelta(days=1),
                estimated_hours=40.0,
                status="completed"
            )

            # Task in ritardo
            task_late = Task(
                name="Task In Ritardo",
                project_id=project.id,
                assignee_id=user.id,
                start_date=date.today() - timedelta(days=14),
                due_date=date.today() - timedelta(days=8),  # Scaduto 8 giorni fa
                estimated_hours=32.0,
                status="in-progress"  # Ancora non completato
            )

            db.session.add_all([task_on_time, task_late])
            db.session.commit()

            # Verifica durata pianificata
            assert task_on_time.duration_days == 7  # 7 giorni pianificati
            assert task_late.duration_days == 7     # 7 giorni pianificati

            # Verifica che il task in ritardo sia identificabile
            today = date.today()
            assert task_late.due_date < today and task_late.status != "completed"

    def test_project_estimation_accuracy_aggregate(self, app, test_project, test_user):
        """Test KPI aggregato precisione stime a livello progetto."""
        with app.app_context():
            project = Project.query.get(test_project)
            user = test_user

            # Setup rate
            rate = PersonnelRate(
                user_id=user.id,
                hourly_rate=50.0,  # 400/8 ore = 50 €/ora
                start_date=date.today() - timedelta(days=30)
            )
            db.session.add(rate)

            # Crea diversi task con stime diverse
            tasks_data = [
                {"name": "Task 1", "estimated": 16.0, "actual_hours": 16.0},  # Perfetto
                {"name": "Task 2", "estimated": 24.0, "actual_hours": 20.0},  # Sovrastimato
                {"name": "Task 3", "estimated": 32.0, "actual_hours": 40.0},  # Sottostimato
                {"name": "Task 4", "estimated": 8.0, "actual_hours": 8.0},    # Perfetto
            ]

            for i, task_data in enumerate(tasks_data):
                task = Task(
                    name=task_data["name"],
                    project_id=project.id,
                    assignee_id=user.id,
                    start_date=date.today() - timedelta(days=20-i*5),
                    due_date=date.today() - timedelta(days=15-i*5),
                    estimated_hours=task_data["estimated"],
                    status="completed"
                )
                db.session.add(task)
                db.session.commit()

                # Aggiungi timesheet per raggiungere le ore effettive
                hours_per_day = 8.0
                days_needed = int(task_data["actual_hours"] / hours_per_day)
                remaining_hours = task_data["actual_hours"] % hours_per_day

                for day in range(days_needed):
                    timesheet = TimesheetEntry(
                        user_id=user.id,
                        project_id=project.id,
                        task_id=task.id,
                        date=date.today() - timedelta(days=20-i*5-day),
                        hours=hours_per_day
                    )
                    db.session.add(timesheet)

                if remaining_hours > 0:
                    timesheet = TimesheetEntry(
                        user_id=user.id,
                        project_id=project.id,
                        task_id=task.id,
                        date=date.today() - timedelta(days=20-i*5-days_needed),
                        hours=remaining_hours
                    )
                    db.session.add(timesheet)

            db.session.commit()

            # Calcola KPI del progetto
            project.is_billable = True
            project.client_daily_rate = 600.0
            db.session.commit()

            kpis = calculate_project_kpis(project.id)

            # Verifica che i KPI includano informazioni sui task
            assert kpis is not None
            assert 'utilization_rate' in kpis
            assert 'cost_per_hour' in kpis

            # Il costo per ora dovrebbe riflettere l'efficienza delle stime
            # Se le stime sono accurate, il costo per ora dovrebbe essere vicino al rate/8
            expected_cost_per_hour = 400.0 / 8.0  # 50€/ora

            # Se non ci sono timesheet, il cost_per_hour potrebbe essere 0
            # In questo caso, verifichiamo solo che il KPI sia calcolato
            if kpis['cost_per_hour'] > 0:
                assert abs(kpis['cost_per_hour'] - expected_cost_per_hour) < 20.0
            else:
                # Se non ci sono timesheet, il costo è 0 - questo è accettabile
                assert kpis['cost_per_hour'] == 0.0

    def test_task_productivity_metrics(self, app, test_project, test_user):
        """Test metriche produttività basate sui task."""
        with app.app_context():
            project = Project.query.get(test_project)
            user = test_user

            # Crea task con diverse efficienze
            high_efficiency_task = Task(
                name="Task Efficiente",
                project_id=project.id,
                assignee_id=user.id,
                estimated_hours=40.0,
                status="completed"
            )

            low_efficiency_task = Task(
                name="Task Inefficiente",
                project_id=project.id,
                assignee_id=user.id,
                estimated_hours=20.0,
                status="completed"
            )

            db.session.add_all([high_efficiency_task, low_efficiency_task])
            db.session.commit()

            # Task efficiente: 40h stimate, 35h effettive (114% efficienza)
            for i in range(4):
                timesheet = TimesheetEntry(
                    user_id=user.id,
                    project_id=project.id,
                    task_id=high_efficiency_task.id,
                    date=date.today() - timedelta(days=i),
                    hours=8.0 if i < 3 else 3.0  # 8+8+8+3 = 27h
                )
                db.session.add(timesheet)

            # Aggiungi altre 8 ore per arrivare a 35
            timesheet = TimesheetEntry(
                user_id=user.id,
                project_id=project.id,
                task_id=high_efficiency_task.id,
                date=date.today() - timedelta(days=4),
                hours=8.0
            )
            db.session.add(timesheet)

            # Task inefficiente: 20h stimate, 30h effettive (67% efficienza)
            for i in range(4):
                timesheet = TimesheetEntry(
                    user_id=user.id,
                    project_id=project.id,
                    task_id=low_efficiency_task.id,
                    date=date.today() - timedelta(days=i+5),
                    hours=7.5  # 7.5*4 = 30h
                )
                db.session.add(timesheet)

            db.session.commit()

            # Verifica metriche
            assert high_efficiency_task.actual_hours == 35.0
            assert abs(high_efficiency_task.hours_efficiency - 114.29) < 0.1

            assert low_efficiency_task.actual_hours == 30.0
            assert abs(low_efficiency_task.hours_efficiency - 66.67) < 0.1
