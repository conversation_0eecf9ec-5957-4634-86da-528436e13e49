"""Unit tests for BusinessProcess model."""
import pytest
from models import BusinessProcess, User
from extensions import db

class TestBusinessProcessModel:
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        self.app = app
        self.user = test_user

    def test_businessprocess_creation_basic(self):
        process = BusinessProcess(
            name='Employee Onboarding',
            description='Process for onboarding new employees'
        )
        db.session.add(process)
        db.session.commit()
        
        assert process.id is not None
        assert process.name == 'Employee Onboarding'
        assert process.description == 'Process for onboarding new employees'

    def test_businessprocess_creation_complete(self):
        process = BusinessProcess(
            name='Invoice Processing',
            description='Complete invoice processing workflow',
            owner_id=self.user.id,
            status='active'
        )
        db.session.add(process)
        db.session.commit()
        
        assert process.owner_id == self.user.id
        assert process.status == 'active'

    def test_businessprocess_status_values(self):
        statuses = ['draft', 'active', 'inactive', 'archived']
        processes = []
        
        for i, status in enumerate(statuses):
            process = BusinessProcess(
                name=f'Process {i}',
                status=status
            )
            processes.append(process)
        
        db.session.add_all(processes)
        db.session.commit()
        
        for process, expected_status in zip(processes, statuses):
            assert process.status == expected_status

    def test_businessprocess_update(self):
        process = BusinessProcess(
            name='Original Process',
            description='Original description',
            status='draft'
        )
        db.session.add(process)
        db.session.commit()
        
        process.name = 'Updated Process'
        process.status = 'active'
        db.session.commit()
        
        updated = BusinessProcess.query.get(process.id)
        assert updated.name == 'Updated Process'
        assert updated.status == 'active'

    def test_businessprocess_deletion(self):
        process = BusinessProcess(
            name='To Be Deleted',
            description='This will be deleted'
        )
        db.session.add(process)
        db.session.commit()
        process_id = process.id
        
        db.session.delete(process)
        db.session.commit()
        
        deleted = BusinessProcess.query.get(process_id)
        assert deleted is None
