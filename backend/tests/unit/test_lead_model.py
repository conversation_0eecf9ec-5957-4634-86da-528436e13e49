"""Unit tests for Lead model."""
import pytest
from models import Lead, User
from extensions import db

class TestLeadModel:
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        self.app = app
        self.user = test_user

    def test_lead_creation_basic(self):
        lead = Lead(
            name='<PERSON>',
            email='<EMAIL>',
            source='website'
        )
        db.session.add(lead)
        db.session.commit()
        
        assert lead.id is not None
        assert lead.name == '<PERSON>'
        assert lead.email == '<EMAIL>'
        assert lead.source == 'website'

    def test_lead_with_status(self):
        lead = Lead(
            name='<PERSON>',
            email='<EMAIL>',
            status='qualified',
            assigned_to=self.user.id
        )
        db.session.add(lead)
        db.session.commit()
        
        assert lead.status == 'qualified'
        assert lead.assigned_to == self.user.id

    def test_lead_deletion(self):
        lead = Lead(name='To Delete', email='<EMAIL>')
        db.session.add(lead)
        db.session.commit()
        lead_id = lead.id
        
        db.session.delete(lead)
        db.session.commit()
        
        deleted = Lead.query.get(lead_id)
        assert deleted is None
