"""Unit tests for ProjectExpense model."""
import pytest
from datetime import date
from models import ProjectExpense, Project, User
from extensions import db

class TestProjectExpenseModel:
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        self.app = app
        self.user = test_user
        
        self.project = Project.query.first()
        if not self.project:
            self.project = Project(name='Test Project', description='Test')
            db.session.add(self.project)
            db.session.commit()

    def test_projectexpense_creation_basic(self):
        expense = ProjectExpense(
            project_id=self.project.id,
            user_id=self.user.id,
            category='Travel',
            description='Business trip',
            amount=250.0,
            date=date.today()
        )
        db.session.add(expense)
        db.session.commit()
        
        assert expense.id is not None
        assert expense.project_id == self.project.id
        assert expense.user_id == self.user.id
        assert expense.category == 'Travel'
        assert expense.amount == 250.0

    def test_projectexpense_complete(self):
        expense = ProjectExpense(
            project_id=self.project.id,
            user_id=self.user.id,
            category='Equipment',
            description='Laptop purchase',
            amount=1500.0,
            billing_type='billable',
            date=date.today(),
            receipt_path='/receipts/laptop.pdf',
            status='approved'
        )
        db.session.add(expense)
        db.session.commit()
        
        assert expense.billing_type == 'billable'
        assert expense.receipt_path == '/receipts/laptop.pdf'
        assert expense.status == 'approved'

    def test_projectexpense_categories(self):
        categories = ['Travel', 'Equipment', 'Software', 'Training']
        expenses = []
        
        for i, category in enumerate(categories):
            expense = ProjectExpense(
                project_id=self.project.id,
                user_id=self.user.id,
                category=category,
                description=f'{category} expense',
                amount=100.0 + i * 50,
                date=date.today()
            )
            expenses.append(expense)
        
        db.session.add_all(expenses)
        db.session.commit()
        
        for expense, expected_category in zip(expenses, categories):
            assert expense.category == expected_category

    def test_projectexpense_status_values(self):
        statuses = ['pending', 'approved', 'rejected', 'paid']
        expenses = []
        
        for i, status in enumerate(statuses):
            expense = ProjectExpense(
                project_id=self.project.id,
                user_id=self.user.id,
                category='Test',
                description=f'Status test {i}',
                amount=100.0,
                date=date.today(),
                status=status
            )
            expenses.append(expense)
        
        db.session.add_all(expenses)
        db.session.commit()
        
        for expense, expected_status in zip(expenses, statuses):
            assert expense.status == expected_status

    def test_projectexpense_update(self):
        expense = ProjectExpense(
            project_id=self.project.id,
            user_id=self.user.id,
            category='Travel',
            description='Original description',
            amount=200.0,
            date=date.today(),
            status='pending'
        )
        db.session.add(expense)
        db.session.commit()
        
        expense.amount = 300.0
        expense.status = 'approved'
        db.session.commit()
        
        updated = ProjectExpense.query.get(expense.id)
        assert updated.amount == 300.0
        assert updated.status == 'approved'

    def test_projectexpense_deletion(self):
        expense = ProjectExpense(
            project_id=self.project.id,
            user_id=self.user.id,
            category='Test',
            description='To be deleted',
            amount=100.0,
            date=date.today()
        )
        db.session.add(expense)
        db.session.commit()
        expense_id = expense.id
        
        db.session.delete(expense)
        db.session.commit()
        
        deleted = ProjectExpense.query.get(expense_id)
        assert deleted is None
