"""Unit tests for Invoice model."""
import pytest
from datetime import date
from models import Invoice, Client, User
from extensions import db

class TestInvoiceModel:
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        self.app = app
        self.user = test_user
        
        self.client = Client.query.first()
        if not self.client:
            self.client = Client(name='Test Client', email='<EMAIL>')
            db.session.add(self.client)
            db.session.commit()

    def test_invoice_creation_basic(self):
        invoice = Invoice(
            invoice_number='INV-001',
            client_id=self.client.id,
            amount=5000.0,
            issue_date=date.today(),
            created_by=self.user.id
        )
        db.session.add(invoice)
        db.session.commit()
        
        assert invoice.id is not None
        assert invoice.invoice_number == 'INV-001'
        assert invoice.amount == 5000.0

    def test_invoice_deletion(self):
        invoice = Invoice(invoice_number='INV-DEL', client_id=self.client.id, amount=100.0, created_by=self.user.id)
        db.session.add(invoice)
        db.session.commit()
        invoice_id = invoice.id
        
        db.session.delete(invoice)
        db.session.commit()
        
        deleted = Invoice.query.get(invoice_id)
        assert deleted is None
