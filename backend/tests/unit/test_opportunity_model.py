"""Unit tests for Opportunity model."""
import pytest
from models import Opportunity, Client, User
from extensions import db

class TestOpportunityModel:
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        self.app = app
        self.user = test_user
        
        self.client = Client.query.first()
        if not self.client:
            self.client = Client(name='Test Client', email='<EMAIL>')
            db.session.add(self.client)
            db.session.commit()

    def test_opportunity_creation_basic(self):
        opportunity = Opportunity(
            name='Test Opportunity',
            client_id=self.client.id,
            value=50000.0,
            stage='prospecting'
        )
        db.session.add(opportunity)
        db.session.commit()
        
        assert opportunity.id is not None
        assert opportunity.name == 'Test Opportunity'
        assert opportunity.value == 50000.0

    def test_opportunity_deletion(self):
        opportunity = Opportunity(name='To Delete', client_id=self.client.id)
        db.session.add(opportunity)
        db.session.commit()
        opp_id = opportunity.id
        
        db.session.delete(opportunity)
        db.session.commit()
        
        deleted = Opportunity.query.get(opp_id)
        assert deleted is None
