"""Unit tests for KPI model."""
import pytest
from models import KPI, Project, User
from extensions import db

class TestKPIModel:
    @pytest.fixture(autouse=True)
    def setup_method(self, app, test_user):
        self.app = app
        self.user = test_user

    def test_kpi_creation_basic(self):
        kpi = KPI(
            name='Revenue Growth',
            description='Monthly revenue growth percentage'
        )
        db.session.add(kpi)
        db.session.commit()
        
        assert kpi.id is not None
        assert kpi.name == 'Revenue Growth'
        assert kpi.description == 'Monthly revenue growth percentage'

    def test_kpi_creation_complete(self):
        kpi = KPI(
            name='Customer Satisfaction',
            description='Customer satisfaction score',
            category='Customer',
            target_value=95.0,
            current_value=87.5,
            unit='percentage',
            frequency='monthly',
            progress=92.1,
            owner_id=self.user.id
        )
        db.session.add(kpi)
        db.session.commit()
        
        assert kpi.category == 'Customer'
        assert kpi.target_value == 95.0
        assert kpi.current_value == 87.5
        assert kpi.unit == 'percentage'
        assert kpi.frequency == 'monthly'
        assert kpi.progress == 92.1
        assert kpi.owner_id == self.user.id

    def test_kpi_categories(self):
        categories = ['Financial', 'Customer', 'Process', 'Learning']
        kpis = []
        
        for i, category in enumerate(categories):
            kpi = KPI(
                name=f'{category} KPI {i}',
                category=category,
                target_value=100.0
            )
            kpis.append(kpi)
        
        db.session.add_all(kpis)
        db.session.commit()
        
        for kpi, expected_category in zip(kpis, categories):
            assert kpi.category == expected_category

    def test_kpi_frequencies(self):
        frequencies = ['daily', 'weekly', 'monthly', 'quarterly', 'yearly']
        kpis = []
        
        for i, frequency in enumerate(frequencies):
            kpi = KPI(
                name=f'Frequency KPI {i}',
                frequency=frequency,
                target_value=100.0
            )
            kpis.append(kpi)
        
        db.session.add_all(kpis)
        db.session.commit()
        
        for kpi, expected_frequency in zip(kpis, frequencies):
            assert kpi.frequency == expected_frequency

    def test_kpi_units(self):
        units = ['percentage', 'count', 'currency', 'hours', 'days']
        kpis = []
        
        for i, unit in enumerate(units):
            kpi = KPI(
                name=f'Unit KPI {i}',
                unit=unit,
                target_value=100.0
            )
            kpis.append(kpi)
        
        db.session.add_all(kpis)
        db.session.commit()
        
        for kpi, expected_unit in zip(kpis, units):
            assert kpi.unit == expected_unit

    def test_kpi_progress_calculation(self):
        kpi = KPI(
            name='Progress Test',
            target_value=100.0,
            current_value=75.0,
            progress=75.0
        )
        db.session.add(kpi)
        db.session.commit()
        
        assert kpi.progress == 75.0

    def test_kpi_update(self):
        kpi = KPI(
            name='Update Test',
            target_value=100.0,
            current_value=50.0
        )
        db.session.add(kpi)
        db.session.commit()
        
        kpi.current_value = 80.0
        kpi.progress = 80.0
        db.session.commit()
        
        updated = KPI.query.get(kpi.id)
        assert updated.current_value == 80.0
        assert updated.progress == 80.0

    def test_kpi_deletion(self):
        kpi = KPI(
            name='To Be Deleted',
            target_value=100.0
        )
        db.session.add(kpi)
        db.session.commit()
        kpi_id = kpi.id
        
        db.session.delete(kpi)
        db.session.commit()
        
        deleted = KPI.query.get(kpi_id)
        assert deleted is None
