import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from extensions import db
import sqlalchemy as sa
from sqlalchemy import inspect, text

# Crea l'applicazione
app = create_app()

def show_tables():
    """Mostra tutte le tabelle nel database"""
    with app.app_context():
        inspector = sa.inspect(db.engine)
        tables = inspector.get_table_names()
        print("\nTabelle nel database:")
        for table in sorted(tables):
            print(f"- {table}")
        print("")

def execute_sql(sql_statement):
    """Esegue uno statement SQL"""
    with app.app_context():
        try:
            db.session.execute(sa.text(sql_statement))
            db.session.commit()
            print(f"SQL eseguito con successo: {sql_statement[:50]}...")
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Errore durante l'esecuzione SQL: {e}")
            return False

def add_client_contact_fields():
    """Aggiunge campi email e phone a Client"""
    with app.app_context():
        print("Aggiungendo campi contact a Client...")
        
        # Lista dei campi da aggiungere a Client
        client_fields = [
            "ALTER TABLE client ADD COLUMN IF NOT EXISTS email VARCHAR(120);",
            "ALTER TABLE client ADD COLUMN IF NOT EXISTS phone VARCHAR(20);"
        ]
        
        success_count = 0
        for sql in client_fields:
            if execute_sql(sql):
                success_count += 1
        
        print(f"\n✅ Aggiunti {success_count}/{len(client_fields)} campi a Client")
        return success_count == len(client_fields)

def add_contract_fields():
    """Aggiunge campi per nuovi tipi di contratto"""
    with app.app_context():
        print("Aggiungendo campi contratto...")
        
        # Lista dei campi da aggiungere
        fields_to_add = [
            "ALTER TABLE contracts ADD COLUMN IF NOT EXISTS milestone_amount FLOAT;",
            "ALTER TABLE contracts ADD COLUMN IF NOT EXISTS milestone_count INTEGER;",
            "ALTER TABLE contracts ADD COLUMN IF NOT EXISTS subscription_frequency VARCHAR(20);",
            "ALTER TABLE contracts ADD COLUMN IF NOT EXISTS subscription_amount FLOAT;",
            "ALTER TABLE contracts ADD COLUMN IF NOT EXISTS retainer_amount FLOAT;",
            "ALTER TABLE contracts ADD COLUMN IF NOT EXISTS retainer_frequency VARCHAR(20);"
        ]
        
        success_count = 0
        for sql in fields_to_add:
            if execute_sql(sql):
                success_count += 1
        
        print(f"\n✅ Aggiunti {success_count}/{len(fields_to_add)} campi contratto")
        return success_count == len(fields_to_add)

def run_sql_file(filename):
    """Esegue tutti gli statement SQL da un file"""
    try:
        with open(filename, 'r') as file:
            sql_script = file.read()
            statements = sql_script.split(';')
            success_count = 0

            for statement in statements:
                if statement.strip():
                    if execute_sql(statement):
                        success_count += 1

            print(f"\nEseguiti {success_count} statements SQL da {filename}")
            return True
    except Exception as e:
        print(f"Errore durante la lettura/esecuzione del file SQL: {e}")
        return False

def add_start_date_to_task():
    """Aggiunge il campo start_date alla tabella task"""
    with app.app_context():
        try:
            # Verifica se la colonna esiste già
            inspector = sa.inspect(db.engine)
            columns = inspector.get_columns('task')
            column_names = [col['name'] for col in columns]

            if 'start_date' in column_names:
                print("Campo start_date già presente nella tabella task")
                return True

            # Aggiungi la colonna start_date
            sql = "ALTER TABLE task ADD COLUMN start_date DATE"
            db.session.execute(sa.text(sql))
            db.session.commit()
            print("Campo start_date aggiunto con successo alla tabella task")
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Errore durante l'aggiunta del campo start_date: {e}")
            return False

def create_hr_tables():
    """Crea le nuove tabelle HR: departments, user_profiles, user_skills_detailed"""
    with app.app_context():
        try:
            inspector = sa.inspect(db.engine)
            existing_tables = inspector.get_table_names()

            print("🏗️  Creazione tabelle HR...")

            # 1. Tabella departments
            if 'departments' not in existing_tables:
                sql_departments = """
                CREATE TABLE departments (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(100) NOT NULL UNIQUE,
                    description TEXT,
                    manager_id INTEGER,
                    parent_id INTEGER,
                    budget FLOAT DEFAULT 0.0,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (manager_id) REFERENCES "user" (id),
                    FOREIGN KEY (parent_id) REFERENCES departments (id)
                )
                """
                db.session.execute(sa.text(sql_departments))
                print("  ✅ Tabella 'departments' creata")
            else:
                print("  ⚠️  Tabella 'departments' già esistente")

            # 2. Tabella user_profiles
            if 'user_profiles' not in existing_tables:
                sql_user_profiles = """
                CREATE TABLE user_profiles (
                    id SERIAL PRIMARY KEY,
                    user_id INTEGER NOT NULL UNIQUE,
                    employee_id VARCHAR(20) UNIQUE,
                    job_title VARCHAR(100),
                    birth_date DATE,
                    address TEXT,
                    emergency_contact_name VARCHAR(100),
                    emergency_contact_phone VARCHAR(20),
                    emergency_contact_relationship VARCHAR(50),
                    employment_type VARCHAR(50) DEFAULT 'full_time',
                    work_location VARCHAR(100),
                    salary FLOAT,
                    salary_currency VARCHAR(3) DEFAULT 'EUR',
                    probation_end_date DATE,
                    contract_end_date DATE,
                    notice_period_days INTEGER DEFAULT 30,
                    weekly_hours FLOAT DEFAULT 40.0,
                    daily_hours FLOAT DEFAULT 8.0,
                    profile_completion FLOAT DEFAULT 0.0,
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES "user" (id)
                )
                """
                db.session.execute(sa.text(sql_user_profiles))
                print("  ✅ Tabella 'user_profiles' creata")
            else:
                print("  ⚠️  Tabella 'user_profiles' già esistente")

            # 3. Tabella user_skills_detailed
            if 'user_skills_detailed' not in existing_tables:
                sql_user_skills_detailed = """
                CREATE TABLE user_skills_detailed (
                    id SERIAL PRIMARY KEY,
                    user_id INTEGER NOT NULL,
                    skill_id INTEGER NOT NULL,
                    proficiency_level INTEGER DEFAULT 1,
                    years_experience FLOAT DEFAULT 0.0,
                    is_certified BOOLEAN DEFAULT FALSE,
                    certification_name VARCHAR(100),
                    certification_date DATE,
                    certification_expiry DATE,
                    self_assessed BOOLEAN DEFAULT TRUE,
                    manager_assessed BOOLEAN DEFAULT FALSE,
                    manager_assessment_date DATE,
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES "user" (id),
                    FOREIGN KEY (skill_id) REFERENCES skill (id),
                    UNIQUE (user_id, skill_id)
                )
                """
                db.session.execute(sa.text(sql_user_skills_detailed))
                print("  ✅ Tabella 'user_skills_detailed' creata")
            else:
                print("  ⚠️  Tabella 'user_skills_detailed' già esistente")

            # 4. Aggiungi campo department_id alla tabella user se non esiste
            user_columns = inspector.get_columns('user')
            user_column_names = [col['name'] for col in user_columns]

            if 'department_id' not in user_column_names:
                sql_add_department_id = 'ALTER TABLE "user" ADD COLUMN department_id INTEGER REFERENCES departments(id)'
                db.session.execute(sa.text(sql_add_department_id))
                print("  ✅ Campo 'department_id' aggiunto alla tabella 'user'")
            else:
                print("  ⚠️  Campo 'department_id' già presente nella tabella 'user'")

            db.session.commit()
            print("🎉 Tabelle HR create con successo!")
            return True

        except Exception as e:
            db.session.rollback()
            print(f"❌ Errore durante la creazione delle tabelle HR: {e}")
            return False

def migrate_user_skills():
    """Migra i dati dalla tabella user_skills alla nuova user_skills_detailed"""
    with app.app_context():
        try:
            print("🔄 Migrazione competenze utenti...")

            # Verifica se ci sono dati da migrare
            result = db.session.execute(sa.text("SELECT COUNT(*) FROM user_skills")).fetchone()
            old_skills_count = result[0] if result else 0

            if old_skills_count == 0:
                print("  ⚠️  Nessuna competenza da migrare dalla tabella user_skills")
                return True

            # Migra i dati
            migrate_sql = """
            INSERT INTO user_skills_detailed (user_id, skill_id, proficiency_level, self_assessed, created_at)
            SELECT user_id, skill_id, 3, 1, CURRENT_TIMESTAMP
            FROM user_skills
            WHERE NOT EXISTS (
                SELECT 1 FROM user_skills_detailed
                WHERE user_skills_detailed.user_id = user_skills.user_id
                AND user_skills_detailed.skill_id = user_skills.skill_id
            )
            """

            result = db.session.execute(sa.text(migrate_sql))
            migrated_count = result.rowcount

            db.session.commit()
            print(f"  ✅ Migrate {migrated_count} competenze dalla tabella user_skills")
            print("  ℹ️  Le competenze sono state impostate con livello 3 (Intermedio) di default")
            return True

        except Exception as e:
            db.session.rollback()
            print(f"❌ Errore durante la migrazione delle competenze: {e}")
            return False

def populate_orgchart_data():
    """Popola il database con dati di esempio per l'organigramma"""
    with app.app_context():
        try:
            print("🏢 Popolamento dati organigramma...")

            # Verifica se ci sono già dipartimenti
            result = db.session.execute(sa.text("SELECT COUNT(*) FROM departments")).fetchone()
            dept_count = result[0] if result else 0

            if dept_count > 0:
                print(f"  ⚠️  Trovati {dept_count} dipartimenti esistenti. Aggiorno solo se necessario...")

            # 1. Crea dipartimenti principali
            departments_data = [
                # Dipartimenti di primo livello
                ("Direzione Generale", "Direzione e coordinamento strategico aziendale", None, None, 500000.0),
                ("IT & Technology", "Sviluppo software e infrastruttura tecnologica", None, None, 200000.0),
                ("Risorse Umane", "Gestione del personale e sviluppo organizzativo", None, None, 80000.0),
                ("Amministrazione", "Gestione amministrativa e finanziaria", None, None, 120000.0),
                ("Commerciale", "Vendite e sviluppo business", None, None, 150000.0),
                ("Marketing", "Marketing e comunicazione", None, None, 100000.0),

                # Sottidipartimenti IT
                ("Development", "Team di sviluppo software", None, 2, 120000.0),
                ("Infrastructure", "Gestione infrastruttura e DevOps", None, 2, 80000.0),
                ("QA & Testing", "Quality Assurance e testing", None, 2, 60000.0),

                # Sottosottidipartimenti Development
                ("Frontend Team", "Sviluppo interfacce utente", None, 7, 50000.0),
                ("Backend Team", "Sviluppo servizi e API", None, 7, 70000.0),
                ("Mobile Team", "Sviluppo applicazioni mobile", None, 7, 40000.0),

                # Sottidipartimenti HR
                ("Recruiting", "Selezione e acquisizione talenti", None, 3, 30000.0),
                ("Training & Development", "Formazione e sviluppo competenze", None, 3, 25000.0),

                # Sottidipartimenti Commerciale
                ("Sales", "Vendite dirette", None, 5, 80000.0),
                ("Business Development", "Sviluppo nuovi mercati", None, 5, 70000.0),
                ("Customer Success", "Gestione clienti esistenti", None, 5, 50000.0),
            ]

            # Inserisci dipartimenti
            for name, description, manager_id, parent_id, budget in departments_data:
                # Verifica se il dipartimento esiste già
                existing = db.session.execute(
                    sa.text("SELECT id FROM departments WHERE name = :name"),
                    {"name": name}
                ).fetchone()

                if not existing:
                    insert_sql = """
                    INSERT INTO departments (name, description, manager_id, parent_id, budget, is_active, created_at, updated_at)
                    VALUES (:name, :description, :manager_id, :parent_id, :budget, TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                    """
                    db.session.execute(sa.text(insert_sql), {
                        "name": name,
                        "description": description,
                        "manager_id": manager_id,
                        "parent_id": parent_id,
                        "budget": budget
                    })
                    print(f"  ✅ Dipartimento '{name}' creato")
                else:
                    print(f"  ⚠️  Dipartimento '{name}' già esistente")

            # 2. Crea utenti di esempio se non esistono
            users_data = [
                ("mario.rossi", "<EMAIL>", "Mario", "Rossi", "admin", "CEO", 1),
                ("anna.verdi", "<EMAIL>", "Anna", "Verdi", "manager", "CTO", 2),
                ("luca.bianchi", "<EMAIL>", "Luca", "Bianchi", "manager", "HR Director", 3),
                ("sara.neri", "<EMAIL>", "Sara", "Neri", "manager", "CFO", 4),
                ("paolo.gialli", "<EMAIL>", "Paolo", "Gialli", "manager", "Sales Director", 5),
                ("elena.blu", "<EMAIL>", "Elena", "Blu", "manager", "Marketing Director", 6),
                ("marco.viola", "<EMAIL>", "Marco", "Viola", "manager", "Dev Manager", 7),
                ("giulia.rosa", "<EMAIL>", "Giulia", "Rosa", "manager", "Infrastructure Manager", 8),
                ("andrea.oro", "<EMAIL>", "Andrea", "Oro", "manager", "QA Manager", 9),
                ("francesca.argento", "<EMAIL>", "Francesca", "Argento", "employee", "Frontend Lead", 10),
                ("roberto.bronzo", "<EMAIL>", "Roberto", "Bronzo", "employee", "Backend Lead", 11),
                ("chiara.rame", "<EMAIL>", "Chiara", "Rame", "employee", "Mobile Lead", 12),
                ("davide.ferro", "<EMAIL>", "Davide", "Ferro", "employee", "Recruiter", 13),
                ("laura.acciaio", "<EMAIL>", "Laura", "Acciaio", "employee", "Training Manager", 14),
                ("simone.titanio", "<EMAIL>", "Simone", "Titanio", "employee", "Sales Manager", 15),
                ("valentina.platino", "<EMAIL>", "Valentina", "Platino", "employee", "Business Dev", 16),
                ("alessandro.zinco", "<EMAIL>", "Alessandro", "Zinco", "employee", "Customer Success", 17),
            ]

            for username, email, first_name, last_name, role, position, dept_id in users_data:
                # Verifica se l'utente esiste già
                existing = db.session.execute(
                    sa.text("SELECT id FROM \"user\" WHERE username = :username"),
                    {"username": username}
                ).fetchone()

                if not existing:
                    insert_sql = """
                    INSERT INTO "user" (username, email, first_name, last_name, role, position, department_id, is_active, created_at)
                    VALUES (:username, :email, :first_name, :last_name, :role, :position, :dept_id, TRUE, CURRENT_TIMESTAMP)
                    """
                    db.session.execute(sa.text(insert_sql), {
                        "username": username,
                        "email": email,
                        "first_name": first_name,
                        "last_name": last_name,
                        "role": role,
                        "position": position,
                        "dept_id": dept_id
                    })
                    print(f"  ✅ Utente '{username}' creato")
                else:
                    print(f"  ⚠️  Utente '{username}' già esistente")

            # 3. Aggiorna i manager dei dipartimenti
            manager_assignments = [
                (1, 1),  # Direzione Generale -> Mario Rossi
                (2, 2),  # IT & Technology -> Anna Verdi
                (3, 3),  # Risorse Umane -> Luca Bianchi
                (4, 4),  # Amministrazione -> Sara Neri
                (5, 5),  # Commerciale -> Paolo Gialli
                (6, 6),  # Marketing -> Elena Blu
                (7, 7),  # Development -> Marco Viola
                (8, 8),  # Infrastructure -> Giulia Rosa
                (9, 9),  # QA & Testing -> Andrea Oro
                (10, 10), # Frontend Team -> Francesca Argento
                (11, 11), # Backend Team -> Roberto Bronzo
                (12, 12), # Mobile Team -> Chiara Rame
                (13, 13), # Recruiting -> Davide Ferro
                (14, 14), # Training & Development -> Laura Acciaio
                (15, 15), # Sales -> Simone Titanio
                (16, 16), # Business Development -> Valentina Platino
                (17, 17), # Customer Success -> Alessandro Zinco
            ]

            for dept_id, manager_id in manager_assignments:
                # Verifica se l'utente esiste
                user_exists = db.session.execute(
                    sa.text("SELECT id FROM \"user\" WHERE id = :user_id"),
                    {"user_id": manager_id}
                ).fetchone()

                if user_exists:
                    update_sql = "UPDATE departments SET manager_id = :manager_id WHERE id = :dept_id"
                    db.session.execute(sa.text(update_sql), {
                        "manager_id": manager_id,
                        "dept_id": dept_id
                    })
                    print(f"  ✅ Manager assegnato al dipartimento {dept_id}")

            db.session.commit()
            print("🎉 Dati organigramma popolati con successo!")

            # Mostra statistiche
            dept_count = db.session.execute(sa.text("SELECT COUNT(*) FROM departments")).fetchone()[0]
            user_count = db.session.execute(sa.text("SELECT COUNT(*) FROM \"user\"")).fetchone()[0]
            print(f"📊 Statistiche: {dept_count} dipartimenti, {user_count} utenti")

            return True

        except Exception as e:
            db.session.rollback()
            print(f"❌ Errore durante il popolamento dati organigramma: {e}")
            return False

def extend_client_table():
    """Estende la tabella client con campo status per lead management"""
    with app.app_context():
        try:
            print("🔧 Estensione tabella client per lead management...")

            inspector = sa.inspect(db.engine)
            columns = inspector.get_columns('client')
            column_names = [col['name'] for col in columns]

            # Aggiungi campo status se non esiste
            if 'status' not in column_names:
                sql = "ALTER TABLE client ADD COLUMN status VARCHAR(20) DEFAULT 'client'"
                db.session.execute(sa.text(sql))
                print("  ✅ Campo 'status' aggiunto alla tabella 'client'")

                # Aggiorna tutti i client esistenti con status 'client'
                update_sql = "UPDATE client SET status = 'client' WHERE status IS NULL"
                db.session.execute(sa.text(update_sql))
                print("  ✅ Status 'client' assegnato a tutti i clienti esistenti")
            else:
                print("  ⚠️  Campo 'status' già presente nella tabella 'client'")

            db.session.commit()
            return True

        except Exception as e:
            db.session.rollback()
            print(f"❌ Errore durante l'estensione tabella client: {e}")
            return False

def setup_timesheet_module():
    """Setup completo del modulo Timesheet - Task 3.1 + 4"""
    print("🚀 Setup modulo Timesheet & CRM - Task 3.1 + 4")
    print("=" * 50)

    success = True

    # 1. Crea le nuove tabelle
    if not create_timesheet_tables():
        success = False

    # 2. Estendi tabella timesheet esistente
    if not extend_timesheet_table():
        success = False

    # 3. Estendi tabella project
    if not extend_project_table():
        success = False

    # 4. Estendi tabella client per lead management
    if not extend_client_table():
        success = False

    # 5. Popola dati di esempio
    if not populate_timesheet_sample_data():
        success = False

    if success:
        print("\n🎉 Setup modulo Timesheet & CRM completato con successo!")
        print("📋 Funzionalità disponibili:")
        print("   - MonthlyTimesheet: Approvazione mensile")
        print("   - TimeOffRequest: Ferie/permessi/smartworking")
        print("   - Contract: Contratti clienti")
        print("   - Invoice: Fatturazione per periodo")
        print("   - TimesheetEntry: Ore con billing info")
        print("   - Client: Lead management con stati")
        print("   - Contact: Gestione contatti clienti")
        print("   - Proposal: Opportunità/deal con stati")
    else:
        print("\n❌ Setup modulo Timesheet & CRM fallito. Controlla gli errori sopra.")

    return success

def create_timesheet_tables():
    """Crea le nuove tabelle per Task 3.1 + 4"""
    with app.app_context():
        try:
            print("🏗️  Creazione tabelle Timesheet & CRM...")

            # Usa SQLAlchemy per creare tutte le tabelle
            from models import (
                MonthlyTimesheet, TimeOffRequest, Contract,
                Invoice, InvoiceLine
            )

            db.create_all()

            # Verifica che le tabelle siano state create
            inspector = sa.inspect(db.engine)
            existing_tables = inspector.get_table_names()

            required_tables = [
                'monthly_timesheets',
                'time_off_requests',
                'contracts',
                'invoices',
                'invoice_lines'
            ]

            for table in required_tables:
                if table in existing_tables:
                    print(f"  ✅ Tabella '{table}' creata/verificata")
                else:
                    print(f"  ❌ Tabella '{table}' NON trovata")
                    return False

            db.session.commit()
            return True

        except Exception as e:
            db.session.rollback()
            print(f"❌ Errore durante la creazione tabelle Timesheet: {e}")
            return False

def extend_timesheet_table():
    """Estende la tabella timesheet_entries con nuovi campi"""
    with app.app_context():
        try:
            print("🔧 Estensione tabella timesheet_entries...")

            inspector = sa.inspect(db.engine)

            # Determina il nome della tabella (timesheet o timesheet_entries)
            existing_tables = inspector.get_table_names()
            table_name = 'timesheet_entries' if 'timesheet_entries' in existing_tables else 'timesheet'

            columns = inspector.get_columns(table_name)
            column_names = [col['name'] for col in columns]

            # Campi da aggiungere a timesheet_entries
            new_columns = [
                ("monthly_timesheet_id", "INTEGER"),
                ("billable", "BOOLEAN DEFAULT FALSE"),
                ("billing_rate", "FLOAT"),
                ("contract_id", "INTEGER"),
                ("invoice_line_id", "INTEGER"),
                ("billing_status", "VARCHAR(20) DEFAULT 'unbilled'")
            ]

            for column_name, column_type in new_columns:
                if column_name not in column_names:
                    sql = f"ALTER TABLE {table_name} ADD COLUMN {column_name} {column_type}"
                    db.session.execute(sa.text(sql))
                    print(f"  ✅ Campo '{column_name}' aggiunto a {table_name}")
                else:
                    print(f"  ⚠️  Campo '{column_name}' già esistente in {table_name}")

            db.session.commit()
            return True

        except Exception as e:
            db.session.rollback()
            print(f"❌ Errore durante l'estensione tabella timesheet: {e}")
            return False

def extend_project_table():
    """Aggiunge contract_id alla tabella project"""
    with app.app_context():
        try:
            print("🔧 Estensione tabella project...")

            inspector = sa.inspect(db.engine)
            columns = inspector.get_columns('project')
            column_names = [col['name'] for col in columns]

            # Campo da aggiungere
            if 'contract_id' not in column_names:
                sql = "ALTER TABLE project ADD COLUMN contract_id INTEGER"
                db.session.execute(sa.text(sql))
                print("  ✅ Campo 'contract_id' aggiunto a project")
            else:
                print("  ⚠️  Campo 'contract_id' già esistente in project")

            db.session.commit()
            return True

        except Exception as e:
            db.session.rollback()
            print(f"❌ Errore durante l'estensione tabella project: {e}")
            return False

def populate_timesheet_sample_data():
    """Popola dati di esempio per testing"""
    with app.app_context():
        try:
            print("🌱 Popolamento dati esempio Timesheet...")

            # Verifica se ci sono già dati
            contract_count = db.session.execute(sa.text("SELECT COUNT(*) FROM contracts")).fetchone()[0]
            if contract_count > 0:
                print("  ⚠️  Dati esempio già presenti")
                return True

            # Trova primo cliente
            client_result = db.session.execute(sa.text("SELECT id FROM client LIMIT 1")).fetchone()
            if not client_result:
                print("  ⚠️  Nessun cliente trovato, salto dati esempio")
                return True

            client_id = client_result[0]

            # Crea contratto esempio
            contract_sql = """
            INSERT INTO contracts (client_id, contract_number, title, description, contract_type, hourly_rate, budget_hours, start_date, status, created_at, updated_at)
            VALUES (:client_id, 'CTR-2024-001', 'Contratto Sviluppo Software', 'Contratto per sviluppo applicazione web', 'hourly', 80.0, 100.0, CURRENT_DATE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            """

            db.session.execute(sa.text(contract_sql), {"client_id": client_id})
            print("  ✅ Contratto esempio creato")

            db.session.commit()
            return True

        except Exception as e:
            db.session.rollback()
            print(f"❌ Errore durante popolamento dati esempio: {e}")
            return False

def cleanup_duplicate_tables():
    """Rimuove tabelle duplicate con CASCADE e rinomina timesheet in timesheet_entries"""
    with app.app_context():
        try:
            print("🧹 Pulizia drastica tabelle duplicate...")

            inspector = sa.inspect(db.engine)
            existing_tables = inspector.get_table_names()

            # Step 1: Elimina TUTTE le tabelle duplicate con CASCADE
            tables_to_drop_cascade = [
                'timesheets',          # Duplicato con vincoli
                'timesheet_entries',   # Duplicato vuoto
                'timesheet_entry',     # Duplicato
                'time_off_request'     # Duplicato (mantieni time_off_requests)
            ]

            for table in tables_to_drop_cascade:
                if table in existing_tables:
                    try:
                        db.session.execute(sa.text(f"DROP TABLE {table} CASCADE"))
                        print(f"  ✅ Tabella '{table}' eliminata con CASCADE")
                    except Exception as e:
                        print(f"  ⚠️  Errore eliminazione '{table}': {e}")
                else:
                    print(f"  ⏭️  Tabella '{table}' non trovata")

            # Commit eliminazioni
            db.session.commit()

            # Step 2: Rinomina timesheet → timesheet_entries (ora che non ci sono conflitti)
            inspector = sa.inspect(db.engine)
            existing_tables = inspector.get_table_names()

            if 'timesheet' in existing_tables and 'timesheet_entries' not in existing_tables:
                db.session.execute(sa.text("ALTER TABLE timesheet RENAME TO timesheet_entries"))
                print("  ✅ Tabella 'timesheet' rinominata in 'timesheet_entries'")
            elif 'timesheet' in existing_tables and 'timesheet_entries' in existing_tables:
                print("  ⚠️  Conflitto: entrambe 'timesheet' e 'timesheet_entries' esistono")
            else:
                print("  ⏭️  Rinominazione non necessaria")

            db.session.commit()
            print("✅ Pulizia drastica completata")
            return True

        except Exception as e:
            db.session.rollback()
            print(f"❌ Errore durante pulizia drastica: {e}")
            return False

def setup_hr_module():
    """Setup completo del modulo HR"""
    print("🚀 Setup modulo HR - Task 7.1")
    print("=" * 50)

    success = True

    # 1. Crea le tabelle HR
    if not create_hr_tables():
        success = False

    # 2. Migra le competenze esistenti
    if not migrate_user_skills():
        success = False

    if success:
        print("\n🎉 Setup modulo HR completato con successo!")
        print("📋 Prossimi passi:")
        print("   - Subtask 7.2: Employee Profile Management")
        print("   - Subtask 7.3: Skills Management System")
        print("   - Subtask 7.4: Department Management Module")
    else:
        print("\n❌ Setup modulo HR fallito. Controlla gli errori sopra.")

    return success

def migrate_job_levels_tables():
    """Create job_levels and employee_job_levels tables"""
    try:
        print("Creating job_levels and employee_job_levels tables...")
        
        # Create tables
        db.create_all()
        
        # Add some sample job levels
        from models import JobLevel
        
        sample_job_levels = [
            {
                'name': 'Junior Developer',
                'description': 'Entry-level developer position',
                'category': 'impiegato',
                'level': 'Junior',
                'salary_min': 25000,
                'salary_max': 35000,
                'benefits': 'Buoni pasto, assicurazione sanitaria',
                'requirements': 'Laurea in informatica o equivalente, conoscenza base di programmazione'
            },
            {
                'name': 'Senior Developer',
                'description': 'Experienced developer position',
                'category': 'impiegato',
                'level': 'Senior',
                'salary_min': 40000,
                'salary_max': 55000,
                'benefits': 'Buoni pasto, assicurazione sanitaria, auto aziendale',
                'requirements': 'Laurea in informatica, almeno 3 anni di esperienza'
            },
            {
                'name': 'Tech Lead',
                'description': 'Technical leadership position',
                'category': 'quadro',
                'level': 'Lead',
                'salary_min': 50000,
                'salary_max': 70000,
                'benefits': 'Buoni pasto, assicurazione sanitaria, auto aziendale, stock options',
                'requirements': 'Laurea in informatica, almeno 5 anni di esperienza, competenze di leadership'
            },
            {
                'name': 'Engineering Manager',
                'description': 'Engineering team management position',
                'category': 'dirigente',
                'level': 'Manager',
                'salary_min': 60000,
                'salary_max': 90000,
                'benefits': 'Buoni pasto, assicurazione sanitaria, auto aziendale, stock options, bonus annuale',
                'requirements': 'Laurea in informatica o management, almeno 7 anni di esperienza, comprovata esperienza di gestione team'
            }
        ]
        
        for jl_data in sample_job_levels:
            existing = JobLevel.query.filter_by(name=jl_data['name']).first()
            if not existing:
                job_level = JobLevel(**jl_data)
                db.session.add(job_level)
        
        db.session.commit()
        print("✅ Job levels tables created successfully with sample data")
        
    except Exception as e:
        print(f"❌ Error creating job levels tables: {str(e)}")
        db.session.rollback()
        raise

def create_comprehensive_performance_data():
    """Crea dati di test completi e realistici per il sistema performance"""
    with app.app_context():
        try:
            print("🎯 Creazione dati performance completi...")

            # Verifica che esistano utenti
            users = db.session.execute(sa.text("SELECT id, first_name, last_name, role FROM \"user\" WHERE is_active = TRUE ORDER BY id")).fetchall()
            if not users:
                print("  ⚠️  Nessun utente trovato, creo utenti di esempio...")
                create_sample_users()
                users = db.session.execute(sa.text("SELECT id, first_name, last_name, role FROM \"user\" WHERE is_active = TRUE ORDER BY id")).fetchall()

            from datetime import date, datetime
            current_year = date.today().year
            
            # 1. Crea template di valutazione avanzati
            templates_data = [
                {
                    "name": "Valutazione Annuale Developer",
                    "description": "Template completo per valutazione annuale degli sviluppatori",
                    "template_type": "annual_review",
                    "target_role": "developer",
                    "fields_config": {
                        "technical_skills": {
                            "weight": 40,
                            "subcriteria": ["Coding Quality", "Architecture Design", "Problem Solving", "Tech Stack Knowledge"]
                        },
                        "soft_skills": {
                            "weight": 30,
                            "subcriteria": ["Communication", "Teamwork", "Learning Agility", "Time Management"]
                        },
                        "achievements": {
                            "weight": 20,
                            "subcriteria": ["Project Deliveries", "Innovation", "Process Improvement"]
                        },
                        "leadership": {
                            "weight": 10,
                            "subcriteria": ["Mentoring", "Initiative", "Decision Making"]
                        }
                    },
                    "rating_scale": {
                        "scale": "1-5",
                        "labels": {
                            "1": "Needs Improvement",
                            "2": "Below Expectations", 
                            "3": "Meets Expectations",
                            "4": "Exceeds Expectations",
                            "5": "Outstanding"
                        }
                    },
                    "is_active": True,
                    "is_default": True
                },
                {
                    "name": "Valutazione Manager",
                    "description": "Template per valutazione manager e team lead",
                    "template_type": "annual_review",
                    "target_role": "manager",
                    "fields_config": {
                        "leadership": {
                            "weight": 35,
                            "subcriteria": ["Team Management", "Strategic Thinking", "Decision Making", "Delegation"]
                        },
                        "technical_skills": {
                            "weight": 25,
                            "subcriteria": ["Technical Knowledge", "Architecture", "Code Review"]
                        },
                        "business_impact": {
                            "weight": 25,
                            "subcriteria": ["Project Success", "Client Satisfaction", "Revenue Impact"]
                        },
                        "people_development": {
                            "weight": 15,
                            "subcriteria": ["Team Growth", "Mentoring", "Performance Management"]
                        }
                    },
                    "rating_scale": {
                        "scale": "1-5",
                        "labels": {
                            "1": "Needs Improvement",
                            "2": "Below Expectations",
                            "3": "Meets Expectations", 
                            "4": "Exceeds Expectations",
                            "5": "Outstanding"
                        }
                    },
                    "is_active": True,
                    "is_default": False
                },
                {
                    "name": "Valutazione Periodo di Prova",
                    "description": "Template per valutazione al termine del periodo di prova",
                    "template_type": "probation_review",
                    "target_role": "all",
                    "fields_config": {
                        "adaptation": {
                            "weight": 35,
                            "subcriteria": ["Learning Speed", "Integration", "Company Culture Fit"]
                        },
                        "performance": {
                            "weight": 35,
                            "subcriteria": ["Task Completion", "Quality", "Productivity"]
                        },
                        "teamwork": {
                            "weight": 20,
                            "subcriteria": ["Collaboration", "Communication", "Reliability"]
                        },
                        "potential": {
                            "weight": 10,
                            "subcriteria": ["Growth Mindset", "Initiative", "Feedback Reception"]
                        }
                    },
                    "rating_scale": {
                        "scale": "1-5",
                        "pass_threshold": 3.0
                    },
                    "is_active": True,
                    "is_default": False
                }
            ]

            # Inserisci template
            template_ids = {}
            for template_data in templates_data:
                existing_template = db.session.execute(
                    sa.text("SELECT id FROM performance_templates WHERE name = :name"),
                    {"name": template_data["name"]}
                ).fetchone()

                if not existing_template:
                    template_sql = """
                    INSERT INTO performance_templates (
                        name, description, template_type, target_role, fields_config,
                        rating_scale, is_active, is_default, created_by, created_at, updated_at
                    ) VALUES (
                        :name, :description, :template_type, :target_role, :fields_config,
                        :rating_scale, :is_active, :is_default, :created_by, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
                    ) RETURNING id
                    """
                    import json
                    template_data['fields_config'] = json.dumps(template_data['fields_config'])
                    template_data['rating_scale'] = json.dumps(template_data['rating_scale'])
                    template_data['created_by'] = users[0][0]  # Primo utente

                    result = db.session.execute(sa.text(template_sql), template_data)
                    template_id = result.fetchone()[0]
                    template_ids[template_data["name"]] = template_id
                    print(f"  ✅ Template '{template_data['name']}' creato (ID: {template_id})")
                else:
                    template_ids[template_data["name"]] = existing_template[0]
                    print(f"  ⚠️  Template '{template_data['name']}' già esistente")

            # 2. Crea valutazioni per diversi stati e anni
            performance_reviews_data = []
            
            # Seleziona diversi utenti per diverse situazioni
            for i, user in enumerate(users[:6]):  # Primi 6 utenti
                user_id, first_name, last_name, role = user
                
                # Manager o admin come reviewer
                reviewer = next((u for u in users if u[3] in ['admin', 'manager']), users[0])
                reviewer_id = reviewer[0]

                # Anno precedente - valutazione completata
                if i < 3:  # Primi 3 utenti hanno valutazione 2023 completata
                    review_2023 = {
                        "employee_id": user_id,
                        "reviewer_id": reviewer_id,
                        "template_id": template_ids.get("Valutazione Annuale Developer"),
                        "review_period_start": date(2023, 1, 1),
                        "review_period_end": date(2023, 12, 31),
                        "review_year": 2023,
                        "status": "completed",
                        "due_date": date(2024, 3, 31),
                        "submitted_date": datetime(2024, 2, 15, 10, 0),
                        "completed_date": datetime(2024, 2, 20, 14, 30),
                        "approved_date": datetime(2024, 2, 25, 16, 45),
                        "approved_by": reviewer_id,
                        "overall_rating": 4.2 if i == 0 else (3.8 if i == 1 else 4.5),
                        "technical_skills_rating": 4.5 if i == 0 else (4.0 if i == 1 else 4.8),
                        "soft_skills_rating": 3.9 if i == 0 else (3.6 if i == 1 else 4.2),
                        "goals_achievement_rating": 4.0 if i == 0 else (3.8 if i == 1 else 4.6),
                        "communication_rating": 4.1 if i == 0 else (3.7 if i == 1 else 4.3),
                        "teamwork_rating": 4.3 if i == 0 else (4.1 if i == 1 else 4.7),
                        "leadership_rating": 3.5 if i == 0 else (3.2 if i == 1 else 4.0),
                        "initiative_rating": 4.0 if i == 0 else (3.9 if i == 1 else 4.4),
                        "achievements": f"Ha completato con successo {3+i} progetti principali, implementato nuove features chiave, migliorato performance del 15%",
                        "areas_improvement": "Comunicazione con stakeholder non tecnici" if i == 0 else ("Gestione del tempo su task complessi" if i == 1 else "Leadership skills per progetti cross-team"),
                        "strengths": f"Eccellenti competenze tecniche, problem solving, collaborazione" + (", innovazione" if i == 2 else ""),
                        "development_goals": f"Corso di leadership, certificazione AWS, mentoring junior developer",
                        "reviewer_comments": f"Dipendente di grande valore, raccomando {'promozione e ' if i == 2 else ''}aumento salariale",
                        "employee_comments": "Soddisfatto dell'anno, obiettivi raggiunti. Interessato a maggiori responsabilità" + (" di leadership" if i == 2 else ""),
                        "hr_comments": "Performance eccellente, da considerare per promozione" if i == 2 else "Buona performance, continua crescita",
                        "created_by": reviewer_id
                    }
                    performance_reviews_data.append(review_2023)

                # Anno corrente - diversi stati
                review_2024_status = ["draft", "in_progress", "completed", "completed"][i % 4]
                
                review_2024 = {
                    "employee_id": user_id,
                    "reviewer_id": reviewer_id,
                    "template_id": template_ids.get("Valutazione Manager" if role in ['admin', 'manager'] else "Valutazione Annuale Developer"),
                    "review_period_start": date(2024, 1, 1),
                    "review_period_end": date(2024, 12, 31),
                    "review_year": 2024,
                    "status": review_2024_status,
                    "due_date": date(2025, 3, 31),
                    "created_by": reviewer_id
                }

                # Aggiungi dati basati sullo status
                if review_2024_status == "completed":
                    review_2024.update({
                        "submitted_date": datetime(2024, 12, 15, 9, 30),
                        "completed_date": datetime(2024, 12, 20, 11, 15),
                        "overall_rating": 4.1 + (i * 0.2),
                        "technical_skills_rating": 4.0 + (i * 0.15),
                        "soft_skills_rating": 3.8 + (i * 0.25),
                        "goals_achievement_rating": 3.9 + (i * 0.2),
                        "achievements": f"Obiettivi 2024 raggiunti al {85 + (i * 5)}%, leadership progetti critici",
                        "reviewer_comments": f"Performance solida, crescita costante"
                    })

                performance_reviews_data.append(review_2024)

            # Inserisci performance reviews
            review_ids = {}
            for review_data in performance_reviews_data:
                # Verifica se esiste già
                existing_review = db.session.execute(
                    sa.text("SELECT id FROM performance_reviews WHERE employee_id = :employee_id AND review_year = :review_year"),
                    {"employee_id": review_data["employee_id"], "review_year": review_data["review_year"]}
                ).fetchone()

                if not existing_review:
                    # Costruisci query dinamicamente basata sui campi disponibili
                    fields = list(review_data.keys())
                    placeholders = ", ".join([f":{field}" for field in fields])
                    field_names = ", ".join(fields)
                    
                    review_sql = f"""
                    INSERT INTO performance_reviews ({field_names}, created_at, updated_at)
                    VALUES ({placeholders}, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                    RETURNING id
                    """
                    
                    result = db.session.execute(sa.text(review_sql), review_data)
                    review_id = result.fetchone()[0]
                    review_ids[f"{review_data['employee_id']}_{review_data['review_year']}"] = review_id
                    
                    user_name = f"{users[review_data['employee_id']-1][1]} {users[review_data['employee_id']-1][2]}"
                    print(f"  ✅ Performance review {review_data['review_year']} per {user_name} ({review_data['status']})")
                else:
                    review_ids[f"{review_data['employee_id']}_{review_data['review_year']}"] = existing_review[0]

            # 3. Crea obiettivi dettagliati per il 2025
            performance_goals_data = []
            goal_categories = ["technical", "leadership", "business", "personal_development", "innovation"]
            
            for user_id, first_name, last_name, role in users[:4]:  # Primi 4 utenti
                # Ogni utente ha 3-5 obiettivi diversificati
                user_goals = [
                    {
                        "employee_id": user_id,
                        "review_id": review_ids.get(f"{user_id}_2024"),
                        "title": f"Certificazione AWS Solutions Architect",
                        "description": "Ottenere certificazione AWS per migliorare competenze cloud e supportare migrazione infrastruttura",
                        "category": "technical",
                        "priority": "high",
                        "target_date": date(2025, 6, 30),
                        "quarter": "Q2",
                        "year": 2025,
                        "target_year": 2025,
                        "status": "active",
                        "progress_percentage": 15,
                        "set_by_id": users[0][0]
                    },
                    {
                        "employee_id": user_id,
                        "review_id": review_ids.get(f"{user_id}_2024"),
                        "title": "Mentoring Team Junior",
                        "description": "Guidare e formare 2 sviluppatori junior, supportare crescita professionale team",
                        "category": "leadership",
                        "priority": "medium",
                        "target_date": date(2025, 12, 31),
                        "quarter": "Q4",
                        "year": 2025,
                        "target_year": 2025,
                        "status": "active",
                        "progress_percentage": 8,
                        "set_by_id": users[0][0]
                    },
                    {
                        "employee_id": user_id,
                        "review_id": review_ids.get(f"{user_id}_2024"),
                        "title": "Ottimizzazione Performance Sistema",
                        "description": "Ridurre tempi di risposta API del 30% e migliorare user experience",
                        "category": "technical",
                        "priority": "high",
                        "target_date": date(2025, 9, 30),
                        "quarter": "Q3", 
                        "year": 2025,
                        "target_year": 2025,
                        "status": "active",
                        "progress_percentage": 25,
                        "set_by_id": users[0][0]
                    }
                ]
                
                if role in ['admin', 'manager']:
                    user_goals.append({
                        "employee_id": user_id,
                        "review_id": review_ids.get(f"{user_id}_2024"),
                        "title": "Aumentare Soddisfazione Team",
                        "description": "Raggiungere NPS team >8/10 tramite miglioramento processi e comunicazione",
                        "category": "leadership",
                        "priority": "high",
                        "target_date": date(2025, 12, 31),
                        "quarter": "Q4",
                        "year": 2025,
                        "target_year": 2025,
                        "status": "active",
                        "progress_percentage": 12,
                        "set_by_id": users[0][0]
                    })
                
                performance_goals_data.extend(user_goals)

            # Inserisci obiettivi
            goal_ids = {}
            for goal_data in performance_goals_data:
                goal_sql = """
                INSERT INTO performance_goals (
                    employee_id, review_id, title, description, category, priority,
                    target_date, quarter, year, target_year, status, progress_percentage, set_by_id,
                    created_at, updated_at
                ) VALUES (
                    :employee_id, :review_id, :title, :description, :category, :priority,
                    :target_date, :quarter, :year, :target_year, :status, :progress_percentage, :set_by_id,
                    CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
                ) RETURNING id
                """
                
                result = db.session.execute(sa.text(goal_sql), goal_data)
                goal_id = result.fetchone()[0]
                goal_ids[goal_data["title"]] = goal_id
                
                user_name = f"{users[goal_data['employee_id']-1][1]} {users[goal_data['employee_id']-1][2]}"
                print(f"  ✅ Obiettivo '{goal_data['title']}' per {user_name}")

            # 4. Crea KPI misurabili per gli obiettivi
            performance_kpis_data = []
            
            for goal_title, goal_id in goal_ids.items():
                if "AWS" in goal_title:
                    kpis = [
                        {
                            "goal_id": goal_id,
                            "name": "Moduli Studio Completati",
                            "description": "Numero moduli corso AWS completati",
                            "unit_of_measure": "moduli",
                            "target_value": 12.0,
                            "current_value": 2.0,
                            "baseline_value": 0.0,
                            "measurement_frequency": "weekly"
                        },
                        {
                            "goal_id": goal_id,
                            "name": "Practice Tests Superati",
                            "description": "Score medio practice tests (target >80%)",
                            "unit_of_measure": "%",
                            "target_value": 80.0,
                            "current_value": 65.0,
                            "baseline_value": 0.0,
                            "measurement_frequency": "weekly"
                        }
                    ]
                elif "Mentoring" in goal_title:
                    kpis = [
                        {
                            "goal_id": goal_id,
                            "name": "Sessioni Mentoring",
                            "description": "Sessioni mentoring one-on-one settimanali",
                            "unit_of_measure": "sessioni",
                            "target_value": 50.0,
                            "current_value": 4.0,
                            "baseline_value": 0.0,
                            "measurement_frequency": "weekly"
                        },
                        {
                            "goal_id": goal_id,
                            "name": "Feedback Score Junior",
                            "description": "Score feedback ricevuto da junior (target >4/5)",
                            "unit_of_measure": "score",
                            "target_value": 4.0,
                            "current_value": 0.0,
                            "baseline_value": 0.0,
                            "measurement_frequency": "monthly"
                        }
                    ]
                elif "Performance" in goal_title:
                    kpis = [
                        {
                            "goal_id": goal_id,
                            "name": "Response Time API",
                            "description": "Tempo medio risposta API (target <200ms)",
                            "unit_of_measure": "ms",
                            "target_value": 200.0,
                            "current_value": 285.0,
                            "baseline_value": 300.0,
                            "measurement_frequency": "daily"
                        },
                        {
                            "goal_id": goal_id,
                            "name": "Error Rate",
                            "description": "Percentuale errori API (target <1%)",
                            "unit_of_measure": "%",
                            "target_value": 1.0,
                            "current_value": 2.3,
                            "baseline_value": 3.1,
                            "measurement_frequency": "daily"
                        }
                    ]
                elif "Soddisfazione" in goal_title:
                    kpis = [
                        {
                            "goal_id": goal_id,
                            "name": "NPS Team Score",
                            "description": "Net Promoter Score del team (target >8/10)",
                            "unit_of_measure": "score",
                            "target_value": 8.0,
                            "current_value": 7.2,
                            "baseline_value": 6.8,
                            "measurement_frequency": "monthly"
                        }
                    ]
                
                performance_kpis_data.extend(kpis)

            # Inserisci KPI
            for kpi_data in performance_kpis_data:
                kpi_sql = """
                INSERT INTO performance_kpis (
                    goal_id, name, description, unit_of_measure, target_value,
                    current_value, baseline_value, measurement_frequency, is_active,
                    created_at, updated_at
                ) VALUES (
                    :goal_id, :name, :description, :unit_of_measure, :target_value,
                    :current_value, :baseline_value, :measurement_frequency, :is_active,
                    CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
                )
                """
                
                kpi_data["is_active"] = True
                db.session.execute(sa.text(kpi_sql), kpi_data)
                print(f"  ✅ KPI '{kpi_data['name']}' creato")

            # 5. Crea feedback 360° di esempio
            feedback_data = []
            for review_key, review_id in review_ids.items():
                if "2024" in review_key:  # Solo per review 2024
                    employee_id = int(review_key.split("_")[0])
                    
                    # Self assessment
                    feedback_data.append({
                        "review_id": review_id,
                        "feedback_giver_id": employee_id,
                        "feedback_type": "self_assessment",
                        "title": "Auto-valutazione 2024",
                        "content": "Quest'anno ho consolidato le mie competenze tecniche e iniziato a prendere maggiori responsabilità. Sono soddisfatto dei progetti completati.",
                        "technical_feedback": "Problem solving, attenzione ai dettagli, capacità di apprendimento",
                        "behavioral_feedback": "Gestione del tempo, public speaking, leadership skills",
                        "rating": 4.0
                    })
                    
                    # Manager feedback
                    manager_id = users[0][0]  # Primo utente come manager
                    if employee_id != manager_id:
                        feedback_data.append({
                            "review_id": review_id,
                            "feedback_giver_id": manager_id,
                            "feedback_type": "manager_feedback",
                            "title": "Valutazione Manager",
                            "content": "Dipendente affidabile con forte crescita tecnica. Ha gestito progetti complessi con successo.",
                            "technical_feedback": "Competenze tecniche solide, proattività, collaborazione",
                            "behavioral_feedback": "Comunicazione con stakeholder, pianificazione a lungo termine",
                            "rating": 4.2
                        })

            # Inserisci feedback
            for feedback in feedback_data:
                feedback_sql = """
                INSERT INTO performance_feedback (
                    review_id, feedback_giver_id, feedback_type, title, content,
                    technical_feedback, behavioral_feedback, rating,
                    created_at, updated_at
                ) VALUES (
                    :review_id, :feedback_giver_id, :feedback_type, :title, :content,
                    :technical_feedback, :behavioral_feedback, :rating,
                    CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
                )
                """
                db.session.execute(sa.text(feedback_sql), feedback)
                print(f"  ✅ Feedback {feedback['feedback_type']} creato")

            # 6. Crea alcuni reward di esempio
            rewards_data = [
                {
                    "employee_id": users[0][0],
                    "review_id": list(review_ids.values())[0],
                    "created_by": users[1][0] if len(users) > 1 else users[0][0],
                    "reward_type": "bonus",
                    "title": "Bonus Performance 2024",
                    "description": "Riconoscimento per eccellente performance e raggiungimento obiettivi",
                    "monetary_value": 2500.0,
                    "awarded_date": date(2024, 12, 31),
                    "effective_date": date(2025, 1, 31),
                    "status": "approved"
                },
                {
                    "employee_id": users[2][0] if len(users) > 2 else users[0][0],
                    "created_by": users[0][0],
                    "reward_type": "recognition",
                    "title": "Employee of the Quarter Q4 2024",
                    "description": "Riconoscimento per leadership eccezionale e mentoring del team",
                    "awarded_date": date(2024, 12, 15),
                    "status": "pending"
                }
            ]

            for reward_data in rewards_data:
                reward_sql = """
                INSERT INTO performance_rewards (
                    employee_id, review_id, created_by, reward_type, title,
                    description, monetary_value, awarded_date, effective_date,
                    status, created_at, updated_at
                ) VALUES (
                    :employee_id, :review_id, :created_by, :reward_type, :title,
                    :description, :monetary_value, :awarded_date, :effective_date,
                    :status, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
                )
                """
                db.session.execute(sa.text(reward_sql), reward_data)
                print(f"  ✅ Reward '{reward_data['title']}' creato")

            db.session.commit()
            
            # Statistiche finali
            stats = db.session.execute(sa.text("""
                SELECT 
                    (SELECT COUNT(*) FROM performance_templates) as templates,
                    (SELECT COUNT(*) FROM performance_reviews) as reviews,
                    (SELECT COUNT(*) FROM performance_goals) as goals,
                    (SELECT COUNT(*) FROM performance_kpis) as kpis,
                    (SELECT COUNT(*) FROM performance_feedback) as feedback,
                    (SELECT COUNT(*) FROM performance_rewards) as rewards
            """)).fetchone()

            print(f"\n📊 Statistiche sistema performance:")
            print(f"     - {stats[0]} template di valutazione")
            print(f"     - {stats[1]} performance review")
            print(f"     - {stats[2]} obiettivi attivi")
            print(f"     - {stats[3]} KPI misurabili")
            print(f"     - {stats[4]} feedback 360°")
            print(f"     - {stats[5]} riconoscimenti")

            return True

        except Exception as e:
            db.session.rollback()
            print(f"❌ Errore durante la creazione dati performance: {e}")
            return False

def create_sample_users():
    """Crea utenti di esempio se non esistono"""
    try:
        sample_users = [
            ("mario.rossi", "<EMAIL>", "Mario", "Rossi", "admin"),
            ("anna.verdi", "<EMAIL>", "Anna", "Verdi", "manager"),
            ("luca.bianchi", "<EMAIL>", "Luca", "Bianchi", "user"),
            ("sara.neri", "<EMAIL>", "Sara", "Neri", "user"),
            ("paolo.gialli", "<EMAIL>", "Paolo", "Gialli", "user"),
            ("elena.blu", "<EMAIL>", "Elena", "Blu", "user")
        ]
        
        for username, email, first_name, last_name, role in sample_users:
            existing = db.session.execute(
                sa.text("SELECT id FROM \"user\" WHERE username = :username"),
                {"username": username}
            ).fetchone()
            
            if not existing:
                user_sql = """
                INSERT INTO "user" (username, email, first_name, last_name, role, is_active, created_at)
                VALUES (:username, :email, :first_name, :last_name, :role, TRUE, CURRENT_TIMESTAMP)
                """
                db.session.execute(sa.text(user_sql), {
                    "username": username,
                    "email": email,
                    "first_name": first_name,
                    "last_name": last_name,
                    "role": role
                })
                print(f"  ✅ Utente '{username}' creato")
        
        db.session.commit()
        return True
        
    except Exception as e:
        db.session.rollback()
        print(f"❌ Errore creazione utenti: {e}")
        return False

def create_bi_tables():
    """Crea le tabelle per le nuove funzionalità Business Intelligence"""
    with app.app_context():
        try:
            print("🏗️  Creazione tabelle Business Intelligence...")

            inspector = sa.inspect(db.engine)
            existing_tables = inspector.get_table_names()

            # 1. Tabella core_competencies per raggruppare competenze aziendali
            if 'core_competencies' not in existing_tables:
                sql_core_competencies = """
                CREATE TABLE core_competencies (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(100) NOT NULL UNIQUE,
                    description TEXT,
                    category VARCHAR(50) DEFAULT 'technical',
                    market_positioning TEXT,
                    skill_ids JSON,
                    min_team_size INTEGER DEFAULT 3,
                    avg_proficiency_required FLOAT DEFAULT 3.5,
                    business_value TEXT,
                    target_markets JSON,
                    competitive_advantage TEXT,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """
                db.session.execute(sa.text(sql_core_competencies))
                print("  ✅ Tabella 'core_competencies' creata")
            else:
                print("  ⚠️  Tabella 'core_competencies' già esistente")

            # 2. Tabella technical_offers per offerte tecniche generate
            if 'technical_offers' not in existing_tables:
                sql_technical_offers = """
                CREATE TABLE technical_offers (
                    id SERIAL PRIMARY KEY,
                    title VARCHAR(200) NOT NULL,
                    description TEXT,
                    core_competency_id INTEGER REFERENCES core_competencies(id),
                    target_sector VARCHAR(100),
                    technology_stack JSON,
                    team_composition JSON,
                    estimated_duration_days INTEGER,
                    estimated_cost_min FLOAT,
                    estimated_cost_max FLOAT,
                    deliverables JSON,
                    success_metrics JSON,
                    risk_factors JSON,
                    generated_by_ai BOOLEAN DEFAULT FALSE,
                    ai_prompt_used TEXT,
                    status VARCHAR(20) DEFAULT 'draft',
                    created_by INTEGER REFERENCES "user"(id),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """
                db.session.execute(sa.text(sql_technical_offers))
                print("  ✅ Tabella 'technical_offers' creata")
            else:
                print("  ⚠️  Tabella 'technical_offers' già esistente")

            # 3. Tabella market_prospects per prospect da Market Intelligence
            if 'market_prospects' not in existing_tables:
                sql_market_prospects = """
                CREATE TABLE market_prospects (
                    id SERIAL PRIMARY KEY,
                    company_name VARCHAR(200) NOT NULL,
                    sector VARCHAR(100),
                    size_category VARCHAR(50),
                    location VARCHAR(100),
                    website VARCHAR(200),
                    contact_info JSON,
                    technology_needs JSON,
                    matching_competencies JSON,
                    fit_score FLOAT DEFAULT 0.0,
                    estimated_budget_min FLOAT,
                    estimated_budget_max FLOAT,
                    lead_status VARCHAR(20) DEFAULT 'prospect',
                    source VARCHAR(50) DEFAULT 'market_intelligence',
                    source_data JSON,
                    notes TEXT,
                    assigned_to INTEGER REFERENCES "user"(id),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """
                db.session.execute(sa.text(sql_market_prospects))
                print("  ✅ Tabella 'market_prospects' creata")
            else:
                print("  ⚠️  Tabella 'market_prospects' già esistente")

            # 4. Tabella bi_reports per reportistica avanzata
            if 'bi_reports' not in existing_tables:
                sql_bi_reports = """
                CREATE TABLE bi_reports (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(200) NOT NULL,
                    description TEXT,
                    report_type VARCHAR(50) NOT NULL,
                    data_sources JSON,
                    filters JSON,
                    chart_config JSON,
                    schedule_config JSON,
                    export_formats JSON DEFAULT '["pdf", "excel", "csv"]',
                    is_scheduled BOOLEAN DEFAULT FALSE,
                    last_generated TIMESTAMP,
                    next_generation TIMESTAMP,
                    status VARCHAR(20) DEFAULT 'active',
                    created_by INTEGER REFERENCES "user"(id),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """
                db.session.execute(sa.text(sql_bi_reports))
                print("  ✅ Tabella 'bi_reports' creata")
            else:
                print("  ⚠️  Tabella 'bi_reports' già esistente")

            db.session.commit()
            print("🎉 Tabelle Business Intelligence create con successo!")
            return True

        except Exception as e:
            db.session.rollback()
            print(f"❌ Errore durante la creazione delle tabelle BI: {e}")
            return False

def populate_bi_sample_data():
    """Popola dati di esempio per le nuove funzionalità BI"""
    with app.app_context():
        try:
            print("🌱 Popolamento dati esempio Business Intelligence...")

            # 1. Core Competencies di esempio
            core_competencies_data = [
                {
                    'name': 'Sviluppo Web Full-Stack',
                    'description': 'Competenza completa nello sviluppo di applicazioni web moderne',
                    'category': 'technical',
                    'market_positioning': 'Soluzioni web scalabili e performanti per PMI e Enterprise',
                    'skill_ids': [1, 2, 3, 4, 5],  # Python, JavaScript, React, Vue.js, Node.js
                    'min_team_size': 3,
                    'avg_proficiency_required': 4.0,
                    'business_value': 'Digitalizzazione processi aziendali, automazione workflow, miglioramento UX',
                    'target_markets': ['E-commerce', 'FinTech', 'Healthcare', 'Education'],
                    'competitive_advantage': 'Stack tecnologico moderno, metodologie agili, focus su performance'
                },
                {
                    'name': 'Business Intelligence & Analytics',
                    'description': 'Analisi dati avanzata e dashboard per decision making',
                    'category': 'analytical',
                    'market_positioning': 'Trasformazione data-driven per aziende in crescita',
                    'skill_ids': [1, 6, 8],  # Python, PostgreSQL, Project Management
                    'min_team_size': 2,
                    'avg_proficiency_required': 3.5,
                    'business_value': 'Insights strategici, ottimizzazione processi, ROI misurabili',
                    'target_markets': ['Manufacturing', 'Retail', 'Consulting'],
                    'competitive_advantage': 'Soluzioni personalizzate, integrazione AI, real-time analytics'
                },
                {
                    'name': 'Automazione & DevOps',
                    'description': 'Infrastruttura cloud e automazione deployment',
                    'category': 'infrastructure',
                    'market_positioning': 'Modernizzazione infrastruttura IT per scalabilità',
                    'skill_ids': [7],  # Docker
                    'min_team_size': 2,
                    'avg_proficiency_required': 4.0,
                    'business_value': 'Riduzione time-to-market, affidabilità sistemi, contenimento costi',
                    'target_markets': ['Technology', 'Startups', 'SaaS'],
                    'competitive_advantage': 'Esperienza multi-cloud, security-first approach'
                }
            ]

            for comp_data in core_competencies_data:
                existing = db.session.execute(
                    sa.text("SELECT id FROM core_competencies WHERE name = :name"),
                    {"name": comp_data["name"]}
                ).fetchone()

                if not existing:
                    import json
                    comp_data['skill_ids'] = json.dumps(comp_data['skill_ids'])
                    comp_data['target_markets'] = json.dumps(comp_data['target_markets'])
                    
                    insert_sql = """
                    INSERT INTO core_competencies (
                        name, description, category, market_positioning, skill_ids,
                        min_team_size, avg_proficiency_required, business_value,
                        target_markets, competitive_advantage, is_active, created_at, updated_at
                    ) VALUES (
                        :name, :description, :category, :market_positioning, :skill_ids,
                        :min_team_size, :avg_proficiency_required, :business_value,
                        :target_markets, :competitive_advantage, TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
                    )
                    """
                    db.session.execute(sa.text(insert_sql), comp_data)
                    print(f"  ✅ Core Competency '{comp_data['name']}' creata")

            # 2. Market Prospects di esempio
            prospects_data = [
                {
                    'company_name': 'TechStartup Milano',
                    'sector': 'Technology',
                    'size_category': 'small',
                    'location': 'Milano, IT',
                    'website': 'www.techstartup.it',
                    'contact_info': {'email': '<EMAIL>', 'phone': '+39 02 1234567'},
                    'technology_needs': ['Web Development', 'Cloud Migration', 'DevOps'],
                    'matching_competencies': [1, 3],
                    'fit_score': 8.5,
                    'estimated_budget_min': 15000,
                    'estimated_budget_max': 35000,
                    'lead_status': 'prospect',
                    'source': 'market_intelligence'
                },
                {
                    'company_name': 'Retail Solutions SRL',
                    'sector': 'Retail',
                    'size_category': 'medium',
                    'location': 'Roma, IT',
                    'website': 'www.retailsolutions.it',
                    'contact_info': {'email': '<EMAIL>'},
                    'technology_needs': ['E-commerce Platform', 'Analytics Dashboard', 'CRM Integration'],
                    'matching_competencies': [1, 2],
                    'fit_score': 9.2,
                    'estimated_budget_min': 25000,
                    'estimated_budget_max': 60000,
                    'lead_status': 'qualified',
                    'source': 'market_intelligence'
                }
            ]

            for prospect_data in prospects_data:
                existing = db.session.execute(
                    sa.text("SELECT id FROM market_prospects WHERE company_name = :name"),
                    {"name": prospect_data["company_name"]}
                ).fetchone()

                if not existing:
                    import json
                    prospect_data['contact_info'] = json.dumps(prospect_data['contact_info'])
                    prospect_data['technology_needs'] = json.dumps(prospect_data['technology_needs'])
                    prospect_data['matching_competencies'] = json.dumps(prospect_data['matching_competencies'])
                    
                    insert_sql = """
                    INSERT INTO market_prospects (
                        company_name, sector, size_category, location, website,
                        contact_info, technology_needs, matching_competencies, fit_score,
                        estimated_budget_min, estimated_budget_max, lead_status, source,
                        created_at, updated_at
                    ) VALUES (
                        :company_name, :sector, :size_category, :location, :website,
                        :contact_info, :technology_needs, :matching_competencies, :fit_score,
                        :estimated_budget_min, :estimated_budget_max, :lead_status, :source,
                        CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
                    )
                    """
                    db.session.execute(sa.text(insert_sql), prospect_data)
                    print(f"  ✅ Market Prospect '{prospect_data['company_name']}' creato")

            # 3. BI Reports template di esempio
            reports_data = [
                {
                    'name': 'Report Competenze Aziendali',
                    'description': 'Analisi delle competenze del team e gap analysis',
                    'report_type': 'skills_analysis',
                    'data_sources': ['user_skills_detailed', 'core_competencies'],
                    'filters': {'department': 'all', 'min_proficiency': 3},
                    'chart_config': {'type': 'radar', 'groupBy': 'category'},
                    'export_formats': ['pdf', 'excel']
                },
                {
                    'name': 'Market Intelligence Report',
                    'description': 'Analisi prospect e opportunità di mercato',
                    'report_type': 'market_analysis',
                    'data_sources': ['market_prospects', 'core_competencies'],
                    'filters': {'fit_score_min': 7.0, 'lead_status': 'prospect'},
                    'chart_config': {'type': 'scatter', 'x': 'fit_score', 'y': 'estimated_budget_max'},
                    'export_formats': ['pdf', 'csv']
                }
            ]

            for report_data in reports_data:
                existing = db.session.execute(
                    sa.text("SELECT id FROM bi_reports WHERE name = :name"),
                    {"name": report_data["name"]}
                ).fetchone()

                if not existing:
                    import json
                    report_data['data_sources'] = json.dumps(report_data['data_sources'])
                    report_data['filters'] = json.dumps(report_data['filters'])
                    report_data['chart_config'] = json.dumps(report_data['chart_config'])
                    report_data['export_formats'] = json.dumps(report_data['export_formats'])
                    
                    # Trova primo utente admin
                    admin_user = db.session.execute(
                        sa.text("SELECT id FROM \"user\" WHERE role = 'admin' LIMIT 1")
                    ).fetchone()
                    report_data['created_by'] = admin_user[0] if admin_user else 1
                    
                    insert_sql = """
                    INSERT INTO bi_reports (
                        name, description, report_type, data_sources, filters,
                        chart_config, export_formats, status, created_by,
                        created_at, updated_at
                    ) VALUES (
                        :name, :description, :report_type, :data_sources, :filters,
                        :chart_config, :export_formats, 'active', :created_by,
                        CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
                    )
                    """
                    db.session.execute(sa.text(insert_sql), report_data)
                    print(f"  ✅ BI Report '{report_data['name']}' creato")

            db.session.commit()
            print("🎉 Dati esempio Business Intelligence popolati con successo!")
            return True

        except Exception as e:
            db.session.rollback()
            print(f"❌ Errore durante il popolamento dati BI: {e}")
            return False

def setup_business_intelligence_module():
    """Setup completo del modulo Business Intelligence"""
    print("🚀 Setup modulo Business Intelligence - Nuove Funzionalità")
    print("=" * 60)

    success = True

    # 1. Crea le nuove tabelle
    if not create_bi_tables():
        success = False

    # 2. Popola dati di esempio
    if not populate_bi_sample_data():
        success = False

    if success:
        print("\n🎉 Setup modulo Business Intelligence completato con successo!")
        print("📋 Funzionalità disponibili:")
        print("   - Core Competencies: Analisi competenze aziendali")
        print("   - Technical Offers: Generazione offerte AI")
        print("   - Market Intelligence: Prospect analysis")
        print("   - Advanced Reports: Reportistica personalizzata")
        print("   - Case Studies: Generazione AI già esistente")
        print("   - Dashboard BI: Metriche real-time già esistenti")
    else:
        print("\n❌ Setup modulo Business Intelligence fallito. Controlla gli errori sopra.")

    return success

# ============================================================================
# AREA BANDI E FINANZIAMENTI - FUNDING MODULE
# ============================================================================

def create_funding_tables():
    """Crea le tabelle per l'area bandi e finanziamenti"""
    with app.app_context():
        try:
            print("🏗️  Creazione tabelle Area Bandi...")
            
            # Usa SQLAlchemy per creare tutte le tabelle
            # from models import FundingOpportunity, FundingApplication, FundingExpense
            
            db.create_all()
            
            # Verifica che le tabelle siano state create
            inspector = sa.inspect(db.engine)
            existing_tables = inspector.get_table_names()
            
            required_tables = [
                'funding_opportunities',
                'funding_applications', 
                'funding_expenses',
                'project_funding_links'
            ]
            
            for table in required_tables:
                if table in existing_tables:
                    print(f"  ✅ Tabella '{table}' creata/verificata")
                else:
                    print(f"  ❌ Tabella '{table}' NON trovata")
                    return False
            
            # Aggiorna anche la tabella project con i nuovi campi per bandi
            update_project_for_funding()
            
            # Crea la tabella di collegamento project-funding
            create_project_funding_links_table()
            
            db.session.commit()
            return True
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Errore durante la creazione tabelle Bandi: {e}")
            return False

def update_project_for_funding():
    """Aggiunge campi funding alla tabella project"""
    print("  📝 Aggiornamento tabella Project per supporto bandi...")
    
    # Campi da aggiungere alla tabella project
    project_funding_fields = [
        "ALTER TABLE project ADD COLUMN IF NOT EXISTS funding_source VARCHAR(100);",
        "ALTER TABLE project ADD COLUMN IF NOT EXISTS funding_application_id INTEGER REFERENCES funding_applications(id);"
    ]
    
    for sql in project_funding_fields:
        try:
            db.session.execute(sa.text(sql))
            print(f"    ✅ Campo aggiunto: {sql.split('ADD COLUMN IF NOT EXISTS')[1].split()[0]}")
        except Exception as e:
            print(f"    ⚠️  Campo già esistente o errore: {e}")

def create_project_funding_links_table():
    """Crea la tabella project_funding_links per collegamenti many-to-many"""
    print("  📝 Creazione tabella project_funding_links...")
    
    try:
        # Verifica se la tabella esiste già
        inspector = sa.inspect(db.engine)
        existing_tables = inspector.get_table_names()
        
        if 'project_funding_links' not in existing_tables:
            create_table_sql = """
            CREATE TABLE project_funding_links (
                id SERIAL PRIMARY KEY,
                project_id INTEGER NOT NULL,
                funding_application_id INTEGER NOT NULL,
                percentage_allocation FLOAT DEFAULT 100.0,
                linked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                notes TEXT,
                created_by INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (project_id) REFERENCES project (id) ON DELETE CASCADE,
                FOREIGN KEY (funding_application_id) REFERENCES funding_applications (id) ON DELETE CASCADE,
                FOREIGN KEY (created_by) REFERENCES "user" (id),
                UNIQUE (project_id, funding_application_id)
            )
            """
            db.session.execute(sa.text(create_table_sql))
            print(f"    ✅ Tabella 'project_funding_links' creata")
        else:
            print(f"    ⚠️  Tabella 'project_funding_links' già esistente")
            
    except Exception as e:
        print(f"    ❌ Errore creazione tabella project_funding_links: {e}")

def populate_funding_sample_data():
    """Popola dati di esempio per testing bandi"""
    with app.app_context():
        try:
            print("🌱 Popolamento dati esempio Bandi...")
            
            # Trova primo utente admin
            admin_user = db.session.execute(
                sa.text("SELECT id FROM \"user\" WHERE role = 'admin' LIMIT 1")
            ).fetchone()
            
            if not admin_user:
                print("  ⚠️  Nessun admin trovato per creare dati esempio")
                return False
            
            admin_id = admin_user[0]
            
            # Dati esempio bandi
            sample_opportunities = [
                {
                    'title': 'Digitalizzazione PMI 2024',
                    'description': 'Bando per la digitalizzazione dei processi aziendali delle PMI lombarde',
                    'source_entity': 'Regione Lombardia', 
                    'program_name': 'Digital Innovation Hub',
                    'call_identifier': 'DIG-PMI-2024-001',
                    'total_budget': 5000000.0,
                    'max_grant_amount': 50000.0,
                    'min_grant_amount': 10000.0,
                    'contribution_percentage': 70.0,
                    'application_deadline': '2024-12-31',
                    'geographic_scope': 'regionale',
                    'status': 'open',
                    'eligibility_criteria': '["PMI con sede in Lombardia", "Fatturato < 50M€", "Progetto digitale innovativo"]',
                    'target_sectors': '["Manifatturiero", "Servizi", "ICT"]',
                    'official_url': 'https://www.regione.lombardia.it/bandi/digitale',
                    'created_by': admin_id
                },
                {
                    'title': 'Horizon Europe Innovation Fund',
                    'description': 'Finanziamento europeo per progetti di ricerca e innovazione',
                    'source_entity': 'European Commission',
                    'program_name': 'Horizon Europe',
                    'call_identifier': 'HORIZON-EIC-2024-PATHFINDER',
                    'total_budget': 300000000.0,
                    'max_grant_amount': 4000000.0,
                    'min_grant_amount': 1000000.0,
                    'contribution_percentage': 100.0,
                    'application_deadline': '2024-09-17',
                    'geographic_scope': 'europeo',
                    'status': 'open',
                    'eligibility_criteria': '["Aziende UE", "Progetti ad alto rischio/alto impatto", "TRL 1-4"]',
                    'target_sectors': '["AI", "Quantum Technologies", "Biotecnologie", "Energia"]',
                    'official_url': 'https://eic.ec.europa.eu/eic-funding-opportunities/calls-proposals',
                    'created_by': admin_id
                },
                {
                    'title': 'PNRR Transizione 4.0',
                    'description': 'Credito d\'imposta per investimenti in beni strumentali e digitali',
                    'source_entity': 'MISE',
                    'program_name': 'Piano Nazionale di Ripresa e Resilienza',
                    'call_identifier': 'PNRR-T40-2024',
                    'total_budget': 13000000000.0,
                    'max_grant_amount': 2000000.0,
                    'min_grant_amount': 20000.0,
                    'contribution_percentage': 50.0,
                    'application_deadline': '2024-11-30',
                    'geographic_scope': 'nazionale',
                    'status': 'open',
                    'eligibility_criteria': '["Imprese residenti in Italia", "Investimenti in beni strumentali nuovi"]',
                    'target_sectors': '["Manifatturiero", "Servizi", "Commercio"]',
                    'official_url': 'https://www.mise.gov.it/transizione40',
                    'created_by': admin_id
                }
            ]
            
            for opportunity_data in sample_opportunities:
                # Verifica se già esiste
                existing = db.session.execute(
                    sa.text("SELECT id FROM funding_opportunities WHERE call_identifier = :call_id"),
                    {"call_id": opportunity_data["call_identifier"]}
                ).fetchone()
                
                if not existing:
                    insert_sql = """
                    INSERT INTO funding_opportunities (
                        title, description, source_entity, program_name, call_identifier,
                        total_budget, max_grant_amount, min_grant_amount, contribution_percentage,
                        application_deadline, geographic_scope, status, eligibility_criteria,
                        target_sectors, official_url, created_by, created_at, updated_at
                    ) VALUES (
                        :title, :description, :source_entity, :program_name, :call_identifier,
                        :total_budget, :max_grant_amount, :min_grant_amount, :contribution_percentage,
                        :application_deadline, :geographic_scope, :status, :eligibility_criteria,
                        :target_sectors, :official_url, :created_by,
                        CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
                    )
                    """
                    db.session.execute(sa.text(insert_sql), opportunity_data)
                    print(f"  ✅ Opportunità '{opportunity_data['title']}' creata")
                else:
                    print(f"  ⚠️  Opportunità '{opportunity_data['title']}' già esistente")
            
            # Crea una candidatura di esempio se ci sono progetti
            project = db.session.execute(
                sa.text("SELECT id, name FROM project LIMIT 1")
            ).fetchone()
            
            if project:
                opportunity = db.session.execute(
                    sa.text("SELECT id FROM funding_opportunities LIMIT 1")
                ).fetchone()
                
                if opportunity:
                    # Verifica se la candidatura esiste già
                    existing_app = db.session.execute(
                        sa.text("SELECT id FROM funding_applications WHERE opportunity_id = :opp_id"),
                        {"opp_id": opportunity[0]}
                    ).fetchone()
                    
                    if not existing_app:
                        application_data = {
                            'opportunity_id': opportunity[0],
                            'project_title': f'Digitalizzazione processo {project[1]}',
                            'project_description': 'Progetto di digitalizzazione per migliorare l\'efficienza operativa',
                            'project_duration_months': 12,
                            'requested_amount': 35000.0,
                            'co_financing_amount': 15000.0,
                            'project_manager_id': admin_id,
                            'status': 'draft',
                            'created_by': admin_id,
                            'linked_project_id': project[0]
                        }
                        
                        insert_app_sql = """
                        INSERT INTO funding_applications (
                            opportunity_id, project_title, project_description, project_duration_months,
                            requested_amount, co_financing_amount, project_manager_id, status,
                            created_by, linked_project_id, created_at, updated_at
                        ) VALUES (
                            :opportunity_id, :project_title, :project_description, :project_duration_months,
                            :requested_amount, :co_financing_amount, :project_manager_id, :status,
                            :created_by, :linked_project_id, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
                        )
                        """
                        db.session.execute(sa.text(insert_app_sql), application_data)
                        print(f"  ✅ Candidatura esempio creata per progetto '{project[1]}'")
            
            db.session.commit()
            return True
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Errore durante popolamento dati bandi: {e}")
            return False

def setup_funding_module():
    """Setup completo del modulo Bandi e Finanziamenti"""
    print("🚀 Setup modulo Bandi e Finanziamenti")
    print("=" * 60)
    
    success = True
    
    # 1. Crea le tabelle
    print("\n📋 Fase 1: Creazione tabelle database")
    if not create_funding_tables():
        success = False
    
    # 2. Popola dati di esempio
    print("\n📋 Fase 2: Popolamento dati di esempio")
    if not populate_funding_sample_data():
        success = False
    
    if success:
        print("\n🎉 Setup modulo Bandi completato con successo!")
        print("📋 Funzionalità disponibili:")
        print("   - FundingOpportunity: Gestione opportunità bandi")
        print("   - FundingApplication: Candidature e progetti")
        print("   - FundingExpense: Rendicontazione spese")
        print("   - Dashboard con scadenze e stati")
        print("   - Workflow approval completo")
        print("   - Collegamento progetti ↔ bandi")
        print("\n📊 Database pronto per:")
        print("   - API backend (/api/funding/*)")
        print("   - Frontend Vue.js area bandi")
        print("   - Sistema di rendicontazione")
    else:
        print("\n❌ Setup modulo Bandi fallito. Controlla gli errori sopra.")
    
    return success

def add_ai_fields_to_funding_opportunities():
    """Aggiunge campi AI al modello FundingOpportunity per la ricerca con Perplexity"""
    try:
        # Verifica se i campi esistono già
        inspector = inspect(db.engine)
        columns = [col['name'] for col in inspector.get_columns('funding_opportunities')]
        
        if 'ai_generated' not in columns:
            print("🤖 Adding AI fields to funding_opportunities table...")
            
            # Aggiungi i nuovi campi
            with db.engine.connect() as conn:
                conn.execute(text("""
                    ALTER TABLE funding_opportunities 
                    ADD COLUMN ai_generated BOOLEAN DEFAULT FALSE
                """))
                
                conn.execute(text("""
                    ALTER TABLE funding_opportunities 
                    ADD COLUMN ai_search_query TEXT
                """))
                
                conn.execute(text("""
                    ALTER TABLE funding_opportunities 
                    ADD COLUMN ai_match_score FLOAT DEFAULT 0.0
                """))
                
                conn.execute(text("""
                    ALTER TABLE funding_opportunities 
                    ADD COLUMN ai_content TEXT
                """))
                
                conn.commit()
            
            print("✅ AI fields added successfully to funding_opportunities table")
        else:
            print("ℹ️ AI fields already exist in funding_opportunities table")
            
    except Exception as e:
        print(f"❌ Error adding AI fields to funding_opportunities: {e}")
        db.session.rollback()


def migrate_communication_system():
    """Migra il sistema di comunicazione secondo il piano"""
    with app.app_context():
        print("🔄 Starting communication system migration...")
        
        try:
            # FASE 1A: Rinomina tabella event in company_events
            print("📝 Step 1: Renaming 'event' table to 'company_events'...")
            rename_event_table()
            
            # FASE 1B: Estendi tabella news -> company_communications
            print("📝 Step 2: Extending 'news' table to 'company_communications'...")
            extend_news_table()
            
            # FASE 1C: Estendi tabella company_events
            print("📝 Step 3: Extending 'company_events' table...")
            extend_company_events_table()
            
            # FASE 1D: Crea nuove tabelle per il sistema di comunicazione
            print("📝 Step 4: Creating new communication tables...")
            create_communication_tables()
            
            # FASE 1E: Rimuovi tabella document
            print("📝 Step 5: Removing 'document' table...")
            remove_document_table()
            
            print("✅ Communication system migration completed successfully!")
            
        except Exception as e:
            print(f"❌ Communication system migration failed: {e}")
            db.session.rollback()
            raise


def rename_event_table():
    """Rinomina la tabella event in company_events per evitare conflitti"""
    try:
        # Controlla se la tabella event esiste
        inspector = sa.inspect(db.engine)
        tables = inspector.get_table_names()
        
        if 'event' in tables:
            if 'company_events' not in tables:
                db.session.execute(sa.text("ALTER TABLE event RENAME TO company_events;"))
                print("✅ Table 'event' renamed to 'company_events'")
            else:
                print("⚠️ Table 'company_events' already exists, skipping rename")
        else:
            print("⚠️ Table 'event' not found, skipping rename")
            
        db.session.commit()
        
    except Exception as e:
        print(f"❌ Error renaming event table: {e}")
        db.session.rollback()
        raise


def extend_news_table():
    """Estende la tabella news e la rinomina in company_communications"""
    try:
        inspector = sa.inspect(db.engine)
        tables = inspector.get_table_names()
        
        if 'news' in tables:
            # Rinomina news in company_communications
            if 'company_communications' not in tables:
                db.session.execute(sa.text("ALTER TABLE news RENAME TO company_communications;"))
                print("✅ Table 'news' renamed to 'company_communications'")
            
            # Aggiungi nuove colonne
            news_extensions = [
                "ALTER TABLE company_communications ADD COLUMN IF NOT EXISTS communication_type VARCHAR(50) DEFAULT 'news';",
                "ALTER TABLE company_communications ADD COLUMN IF NOT EXISTS target_audience VARCHAR(100);",
                "ALTER TABLE company_communications ADD COLUMN IF NOT EXISTS priority_level VARCHAR(20) DEFAULT 'normal';",
                "ALTER TABLE company_communications ADD COLUMN IF NOT EXISTS is_pinned BOOLEAN DEFAULT FALSE;",
                "ALTER TABLE company_communications ADD COLUMN IF NOT EXISTS scheduled_at TIMESTAMP;",
                "ALTER TABLE company_communications ADD COLUMN IF NOT EXISTS expires_at TIMESTAMP;",
                "ALTER TABLE company_communications ADD COLUMN IF NOT EXISTS tags TEXT[];",
                "ALTER TABLE company_communications ADD COLUMN IF NOT EXISTS allow_comments BOOLEAN DEFAULT TRUE;",
                "ALTER TABLE company_communications ADD COLUMN IF NOT EXISTS view_count INTEGER DEFAULT 0;",
                "ALTER TABLE company_communications ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;",
                "ALTER TABLE company_communications ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;"
            ]
            
            for sql in news_extensions:
                db.session.execute(sa.text(sql))
            
            print("✅ Company communications table extended successfully")
        else:
            print("⚠️ Table 'news' not found, skipping extension")
            
        db.session.commit()
        
    except Exception as e:
        print(f"❌ Error extending news table: {e}")
        db.session.rollback()
        raise


def extend_company_events_table():
    """Estende la tabella company_events con nuovi campi"""
    try:
        inspector = sa.inspect(db.engine)
        tables = inspector.get_table_names()
        
        if 'company_events' in tables:
            # Aggiungi nuove colonne
            events_extensions = [
                "ALTER TABLE company_events ADD COLUMN IF NOT EXISTS is_company_wide BOOLEAN DEFAULT FALSE;",
                "ALTER TABLE company_events ADD COLUMN IF NOT EXISTS max_participants INTEGER;",
                "ALTER TABLE company_events ADD COLUMN IF NOT EXISTS registration_required BOOLEAN DEFAULT FALSE;",
                "ALTER TABLE company_events ADD COLUMN IF NOT EXISTS registration_deadline TIMESTAMP;",
                "ALTER TABLE company_events ADD COLUMN IF NOT EXISTS is_public BOOLEAN DEFAULT FALSE;",
                "ALTER TABLE company_events ADD COLUMN IF NOT EXISTS tags TEXT[];",
                "ALTER TABLE company_events ADD COLUMN IF NOT EXISTS allow_comments BOOLEAN DEFAULT TRUE;",
                "ALTER TABLE company_events ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;"
            ]
            
            for sql in events_extensions:
                db.session.execute(sa.text(sql))
            
            print("✅ Company events table extended successfully")
        else:
            print("⚠️ Table 'company_events' not found, skipping extension")
            
        db.session.commit()
        
    except Exception as e:
        print(f"❌ Error extending company_events table: {e}")
        db.session.rollback()
        raise


def create_communication_tables():
    """Crea le nuove tabelle per il sistema di comunicazione"""
    try:
        # Direct Messages
        direct_messages_sql = """
        CREATE TABLE IF NOT EXISTS direct_messages (
            id SERIAL PRIMARY KEY,
            sender_id INTEGER NOT NULL,
            recipient_id INTEGER NOT NULL,
            message TEXT NOT NULL,
            is_read BOOLEAN DEFAULT FALSE,
            read_at TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (sender_id) REFERENCES "user" (id),
            FOREIGN KEY (recipient_id) REFERENCES "user" (id)
        );
        """
        
        # Forum Topics
        forum_topics_sql = """
        CREATE TABLE IF NOT EXISTS forum_topics (
            id SERIAL PRIMARY KEY,
            title VARCHAR(200) NOT NULL,
            description TEXT,
            author_id INTEGER NOT NULL,
            category VARCHAR(100),
            is_pinned BOOLEAN DEFAULT FALSE,
            is_locked BOOLEAN DEFAULT FALSE,
            view_count INTEGER DEFAULT 0,
            reply_count INTEGER DEFAULT 0,
            last_activity_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (author_id) REFERENCES "user" (id)
        );
        """
        
        # Forum Comments
        forum_comments_sql = """
        CREATE TABLE IF NOT EXISTS forum_comments (
            id SERIAL PRIMARY KEY,
            topic_id INTEGER NOT NULL,
            author_id INTEGER NOT NULL,
            content TEXT NOT NULL,
            parent_comment_id INTEGER,
            is_edited BOOLEAN DEFAULT FALSE,
            edited_at TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (topic_id) REFERENCES forum_topics (id) ON DELETE CASCADE,
            FOREIGN KEY (author_id) REFERENCES "user" (id),
            FOREIGN KEY (parent_comment_id) REFERENCES forum_comments (id)
        );
        """
        
        # Polls
        polls_sql = """
        CREATE TABLE IF NOT EXISTS polls (
            id SERIAL PRIMARY KEY,
            title VARCHAR(200) NOT NULL,
            description TEXT,
            author_id INTEGER NOT NULL,
            is_anonymous BOOLEAN DEFAULT FALSE,
            multiple_choice BOOLEAN DEFAULT FALSE,
            expires_at TIMESTAMP,
            is_active BOOLEAN DEFAULT TRUE,
            total_votes INTEGER DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (author_id) REFERENCES "user" (id)
        );
        """
        
        # Poll Options
        poll_options_sql = """
        CREATE TABLE IF NOT EXISTS poll_options (
            id SERIAL PRIMARY KEY,
            poll_id INTEGER NOT NULL,
            option_text VARCHAR(200) NOT NULL,
            vote_count INTEGER DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (poll_id) REFERENCES polls (id) ON DELETE CASCADE
        );
        """
        
        # Poll Votes
        poll_votes_sql = """
        CREATE TABLE IF NOT EXISTS poll_votes (
            id SERIAL PRIMARY KEY,
            poll_id INTEGER NOT NULL,
            option_id INTEGER NOT NULL,
            user_id INTEGER NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (poll_id) REFERENCES polls (id) ON DELETE CASCADE,
            FOREIGN KEY (option_id) REFERENCES poll_options (id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES "user" (id),
            UNIQUE(poll_id, user_id, option_id)
        );
        """
        
        # Communication Reactions
        reactions_sql = """
        CREATE TABLE IF NOT EXISTS communication_reactions (
            id SERIAL PRIMARY KEY,
            user_id INTEGER NOT NULL,
            target_type VARCHAR(50) NOT NULL,
            target_id INTEGER NOT NULL,
            reaction_type VARCHAR(20) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES "user" (id),
            UNIQUE(user_id, target_type, target_id, reaction_type)
        );
        """
        
        # Company Event Registrations
        company_event_registrations_sql = """
        CREATE TABLE IF NOT EXISTS company_event_registrations (
            id SERIAL PRIMARY KEY,
            event_id INTEGER NOT NULL,
            user_id INTEGER NOT NULL,
            status VARCHAR(20) DEFAULT 'registered',
            registered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            notes TEXT,
            FOREIGN KEY (event_id) REFERENCES company_events (id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES "user" (id),
            UNIQUE(event_id, user_id)
        );
        """
        
        # Esegui tutte le query
        tables_to_create = [
            ("direct_messages", direct_messages_sql),
            ("forum_topics", forum_topics_sql),
            ("forum_comments", forum_comments_sql),
            ("polls", polls_sql),
            ("poll_options", poll_options_sql),
            ("poll_votes", poll_votes_sql),
            ("communication_reactions", reactions_sql),
            ("company_event_registrations", company_event_registrations_sql)
        ]
        
        for table_name, sql in tables_to_create:
            db.session.execute(sa.text(sql))
            print(f"✅ Table '{table_name}' created successfully")
        
        db.session.commit()
        
    except Exception as e:
        print(f"❌ Error creating communication tables: {e}")
        db.session.rollback()
        raise


def remove_document_table():
    """Rimuove la tabella document se esiste"""
    try:
        inspector = sa.inspect(db.engine)
        tables = inspector.get_table_names()
        
        if 'document' in tables:
            db.session.execute(sa.text("DROP TABLE IF EXISTS document CASCADE;"))
            print("✅ Table 'document' removed successfully")
        else:
            print("⚠️ Table 'document' not found, skipping removal")
            
        db.session.commit()
        
    except Exception as e:
        print(f"❌ Error removing document table: {e}")
        db.session.rollback()
        raise


def update_kpi_table():
    """Aggiorna la tabella KPI con i campi mancanti per il dashboard"""
    try:
        print("🔄 Updating KPI table with missing fields...")
        
        # Lista dei campi da aggiungere alla tabella KPI
        kpi_fields = [
            "ALTER TABLE kpi ADD COLUMN IF NOT EXISTS category VARCHAR(64);",
            "ALTER TABLE kpi ADD COLUMN IF NOT EXISTS frequency VARCHAR(20);", 
            "ALTER TABLE kpi ADD COLUMN IF NOT EXISTS progress FLOAT DEFAULT 0.0;"
        ]
        
        success_count = 0
        for sql in kpi_fields:
            try:
                db.session.execute(sa.text(sql))
                success_count += 1
                print(f"✅ Added field: {sql.split('ADD COLUMN IF NOT EXISTS')[1].split()[0]}")
            except Exception as e:
                print(f"⚠️ Field might already exist or error: {e}")
        
        # Se measurement_frequency esiste, copialo in frequency
        try:
            db.session.execute(sa.text("""
                UPDATE kpi 
                SET frequency = measurement_frequency 
                WHERE frequency IS NULL AND measurement_frequency IS NOT NULL
            """))
            print("✅ Migrated measurement_frequency to frequency field")
        except Exception as e:
            print(f"⚠️ Error migrating frequency field: {e}")
        
        db.session.commit()
        print(f"✅ KPI table updated successfully! ({success_count} fields processed)")
        
    except Exception as e:
        print(f"❌ Error updating KPI table: {e}")
        db.session.rollback()
        raise

def create_hr_assistant_tables():
    """Crea le tabelle per il modulo HR Assistant"""
    with app.app_context():
        print("🤖 Creazione tabelle HR Assistant...")
        
        try:
            # Tabella hr_knowledge_base
            hr_kb_sql = """
            CREATE TABLE IF NOT EXISTS hr_knowledge_base (
                id SERIAL PRIMARY KEY,
                title VARCHAR(255) NOT NULL,
                content TEXT NOT NULL,
                category VARCHAR(100) NOT NULL,
                tags TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_with_ai BOOLEAN DEFAULT FALSE,
                ai_sources TEXT,
                ai_confidence VARCHAR(20),
                created_by INTEGER NOT NULL REFERENCES users(id),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            """
            
            # Tabella hr_chat_conversations
            hr_chat_sql = """
            CREATE TABLE IF NOT EXISTS hr_chat_conversations (
                id SERIAL PRIMARY KEY,
                user_id INTEGER NOT NULL REFERENCES users(id),
                session_id VARCHAR(100) NOT NULL,
                user_message TEXT NOT NULL,
                bot_response TEXT NOT NULL,
                category_detected VARCHAR(100),
                confidence_score VARCHAR(20),
                kb_entries_used TEXT,
                response_time_ms INTEGER,
                user_feedback VARCHAR(20),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            """
            
            # Tabella hr_content_templates
            hr_templates_sql = """
            CREATE TABLE IF NOT EXISTS hr_content_templates (
                id SERIAL PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                category VARCHAR(100) NOT NULL,
                description TEXT,
                prompt_template TEXT NOT NULL,
                required_fields TEXT,
                output_format TEXT,
                usage_count INTEGER DEFAULT 0,
                last_used TIMESTAMP,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            """
            
            # Tabella hr_analytics
            hr_analytics_sql = """
            CREATE TABLE IF NOT EXISTS hr_analytics (
                id SERIAL PRIMARY KEY,
                date DATE NOT NULL,
                total_conversations INTEGER DEFAULT 0,
                unique_users INTEGER DEFAULT 0,
                avg_response_time_ms FLOAT DEFAULT 0,
                category_distribution TEXT,
                feedback_distribution TEXT,
                total_kb_entries INTEGER DEFAULT 0,
                ai_generated_entries INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            """
            
            # Esegui le query
            tables = [
                ("hr_knowledge_base", hr_kb_sql),
                ("hr_chat_conversations", hr_chat_sql), 
                ("hr_content_templates", hr_templates_sql),
                ("hr_analytics", hr_analytics_sql)
            ]
            
            for table_name, sql in tables:
                db.session.execute(sa.text(sql))
                print(f"  ✅ Tabella '{table_name}' creata/verificata")
            
            # Crea indici per performance
            indices_sql = [
                "CREATE INDEX IF NOT EXISTS idx_hr_kb_category ON hr_knowledge_base(category);",
                "CREATE INDEX IF NOT EXISTS idx_hr_kb_active ON hr_knowledge_base(is_active);", 
                "CREATE INDEX IF NOT EXISTS idx_hr_kb_created_at ON hr_knowledge_base(created_at);",
                "CREATE INDEX IF NOT EXISTS idx_hr_chat_user ON hr_chat_conversations(user_id);",
                "CREATE INDEX IF NOT EXISTS idx_hr_chat_session ON hr_chat_conversations(session_id);",
                "CREATE INDEX IF NOT EXISTS idx_hr_chat_category ON hr_chat_conversations(category_detected);",
                "CREATE INDEX IF NOT EXISTS idx_hr_chat_created ON hr_chat_conversations(created_at);",
                "CREATE INDEX IF NOT EXISTS idx_hr_template_category ON hr_content_templates(category);",
                "CREATE INDEX IF NOT EXISTS idx_hr_template_active ON hr_content_templates(is_active);",
                "CREATE UNIQUE INDEX IF NOT EXISTS idx_hr_analytics_date ON hr_analytics(date);"
            ]
            
            for index_sql in indices_sql:
                db.session.execute(sa.text(index_sql))
            
            print("  ✅ Indici di performance creati")
            
            # Inserisci template predefiniti
            default_templates = [
                {
                    'name': 'Onboarding - Primo Giorno',
                    'category': 'onboarding',
                    'description': 'Template per guide primo giorno lavorativo',
                    'prompt_template': 'Crea una guida completa per il primo giorno di lavoro di un nuovo dipendente in una PMI italiana. Include: {requirements}',
                    'required_fields': '["ruolo", "dipartimento", "responsabile"]',
                    'output_format': 'Markdown con checklist e sezioni strutturate'
                },
                {
                    'name': 'Ferie - Procedure',
                    'category': 'leave',
                    'description': 'Template per procedure richiesta ferie',
                    'prompt_template': 'Spiega le procedure per richiedere ferie in una PMI italiana, includendo normative e best practices. Considera: {requirements}',
                    'required_fields': '["ccnl", "policy_aziendale"]',
                    'output_format': 'Guida step-by-step con esempi pratici'
                },
                {
                    'name': 'Rimborsi - Spese',
                    'category': 'travel',
                    'description': 'Template per procedure rimborso spese',
                    'prompt_template': 'Crea una guida per la richiesta rimborsi spese aziendali secondo normative italiane. Include: {requirements}',
                    'required_fields': '["tipologie_spese", "limiti_budget"]',
                    'output_format': 'Procedura con moduli e documenti necessari'
                }
            ]
            
            for template in default_templates:
                insert_template_sql = """
                INSERT INTO hr_content_templates (name, category, description, prompt_template, required_fields, output_format)
                VALUES (:name, :category, :description, :prompt_template, :required_fields, :output_format)
                ON CONFLICT DO NOTHING;
                """
                db.session.execute(sa.text(insert_template_sql), template)
            
            print("  ✅ Template predefiniti inseriti")
            
            db.session.commit()
            print("🎉 Tabelle HR Assistant create con successo!")
            return True
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Errore creazione tabelle HR Assistant: {e}")
            return False


if __name__ == "__main__":
    print("🔄 Starting database update...")
    
    try:
        with app.app_context():
            # Migrazione per campi AI
            add_ai_fields_to_funding_opportunities()
            
            # Migrazione del sistema di comunicazione
            migrate_communication_system()
            
            # Aggiornamento tabella KPI per il dashboard
            update_kpi_table()
            
            # Creazione tabelle HR Assistant
            create_hr_assistant_tables()
            
            print("✅ Database update completed successfully!")
            
    except Exception as e:
        print(f"❌ Database update failed: {e}")
        import traceback
        traceback.print_exc()