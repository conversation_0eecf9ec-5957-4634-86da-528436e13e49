# Import all models from modular structure
# This file maintains backward compatibility while using the new modular structure

from models_split.base import db, datetime, date, json
from models_split.user import User
from models_split.communication import (
    ForumTopic, ForumComment, Poll, PollOption, PollVote,
    DirectMessage, CommunicationReaction, CompanyEvent, CompanyEventRegistration
)
from models_split.hr import (
    Skill, Department, UserProfile, UserSkill, TimeOffRequest,
    JobLevel, EmployeeJobLevel, PersonnelRate
)
from models_split.projects import (
    Project, Task, TaskDependency, ProjectResource, ProjectKPI,
    ProjectExpense, ProjectKPITemplate, ProjectKPITarget, ProjectFundingLink
)
from models_split.timesheets import TimesheetEntry, MonthlyTimesheet
from models_split.crm import Client, Contact, Proposal, Contract
from models_split.associations import project_team
from models_split.invoicing import (
    PreInvoice, PreInvoiceLine, CompanyInvoicingSettings, 
    Invoice, InvoiceLine, IntegrationSettings
)
from models_split.business import Product, Service, KPI, BusinessProcess, ProcessStep
from models_split.content import News, Document, Regulation, StartupResource
from models_split.system import Notification, AdminLog
from models_split.funding import FundingOpportunity, FundingApplication, FundingExpense
from models_split.performance import (
    PerformanceReview, PerformanceFeedback, PerformanceGoal, PerformanceTemplate,
    PerformanceKPI, PerformanceReward, PerformanceReviewParticipant, CoreCompetency
)
from models_split.portfolio import CaseStudy, TechnicalOffer, MarketProspect, BIReport
from models_split.certifications import (
    CertificationStandard, CompanyCertification, CertificationAudit,
    ReadinessTask, CertificationDocument
)
from models_split.ceo import (
    ResearchSession, ResearchQuery, StrategicInsight, 
    AIInteraction, ScheduledTask, CompanyProfile
)
from models_split.hr_assistant import (
    HRKnowledgeBase, HRChatConversation, HRContentTemplate, HRAnalytics
)

# All models have been split into modular files