import{_ as ne}from"./PageHeader.js";import{_ as de}from"./StatsGrid.js";import{_ as ce}from"./FilterBar.js";import{_ as S}from"./DataTable.js";import{_ as ue}from"./AlertsSection.js";import{c as w,H as j}from"./app.js";import{u as ge}from"./timesheet.js";import"./auto.js";import{r as o,c as v,w as be,o as pe,y as me,b as ye,f as T,g as he,e as b,l as e,p as n,n as x,t as s,z as ve,Q as f,j as C,v as H,E as U}from"./vendor.js";import"./formatters.js";const xe={class:"space-y-6"},fe={class:"flex items-center space-x-3"},ke=["disabled"],_e=["disabled"],we={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},Ce={class:"bg-white dark:bg-gray-800 shadow-sm rounded-lg border border-gray-200 dark:border-gray-700 p-6"},ze={class:"flex items-center justify-between mb-4"},je={class:"flex items-center space-x-2"},Te={class:"relative h-64 w-full"},Pe={class:"bg-white dark:bg-gray-800 shadow-sm rounded-lg border border-gray-200 dark:border-gray-700 p-6"},Re={class:"flex items-center justify-between mb-4"},Ae={class:"flex items-center space-x-2"},De={class:"text-sm text-gray-500 dark:text-gray-400"},Fe={class:"relative h-64 w-full"},Ne={class:"bg-white dark:bg-gray-800 shadow-sm rounded-lg border border-gray-200 dark:border-gray-700"},Be={class:"flex items-center"},Ee={class:"w-8 h-8 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center mr-3"},Me={class:"text-xs font-medium text-gray-700 dark:text-gray-300"},Oe={class:"text-sm font-medium text-gray-900 dark:text-white"},Se={class:"text-xs text-gray-500 dark:text-gray-400"},He={class:"text-right"},Ue={class:"text-sm font-medium text-gray-900 dark:text-white"},$e={class:"text-xs text-gray-500 dark:text-gray-400"},Ie={class:"flex items-center"},Le={class:"flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-2"},Ge={class:"text-sm font-medium text-gray-900 dark:text-white"},Ve={class:"text-right"},qe={class:"text-sm font-medium text-gray-900 dark:text-white"},Ze={class:"text-xs text-gray-500 dark:text-gray-400"},Qe={class:"bg-white dark:bg-gray-800 shadow-sm rounded-lg border border-gray-200 dark:border-gray-700"},We={class:"text-sm font-medium text-gray-900 dark:text-white"},Je={class:"text-xs text-gray-500 dark:text-gray-400"},Ke={class:"text-right"},Xe={class:"text-sm font-medium text-gray-900 dark:text-white"},Ye={class:"text-xs text-gray-500 dark:text-gray-400"},et={class:"flex items-center"},tt={class:"flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-2"},at={class:"text-sm font-medium text-gray-900 dark:text-white"},rt={class:"text-right"},st={class:"text-sm font-medium text-gray-900 dark:text-white"},lt={class:"text-xs text-gray-500 dark:text-gray-400"},ot={class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},it={class:"bg-white dark:bg-gray-800 shadow-sm rounded-lg border border-gray-200 dark:border-gray-700 p-6"},nt={class:"relative h-48 w-full"},dt={class:"bg-white dark:bg-gray-800 shadow-sm rounded-lg border border-gray-200 dark:border-gray-700 p-6"},ct={class:"relative h-48 w-full"},ut={class:"bg-white dark:bg-gray-800 shadow-sm rounded-lg border border-gray-200 dark:border-gray-700 p-6"},gt={class:"relative h-48 w-full"},Ct={__name:"TimesheetAnalytics",setup(bt){ge();const i=o(!1),u=o(null),p=o("month"),m=o("current_month"),y=o(""),h=o(""),g=o("productivity"),P=o([]),R=o([]),d=o({totalHours:0,totalHoursChange:0,avgProductivity:0,productivityChange:0,revenue:0,revenueChange:0,utilization:0,utilizationChange:0}),A=o([]),D=o([]),F=o(null),N=o(null),B=o(null),E=o(null),M=o(null);let l={};const $=v(()=>u.value?[{id:"error",type:"error",title:"Errore",message:u.value,dismissible:!0}]:[]),I=v(()=>[{id:"period",label:"Periodo",type:"select",value:m.value,options:[{value:"current_month",label:"Mese Corrente"},{value:"last_month",label:"Mese Scorso"},{value:"current_quarter",label:"Trimestre Corrente"},{value:"last_quarter",label:"Trimestre Scorso"},{value:"current_year",label:"Anno Corrente"}]},{id:"department",label:"Dipartimento",type:"select",value:y.value,options:[{value:"",label:"Tutti"},...P.value.map(t=>({value:t.id,label:t.name}))]},{id:"project",label:"Progetto",type:"select",value:h.value,options:[{value:"",label:"Tutti"},...R.value.map(t=>({value:t.id,label:t.name}))]},{id:"analysisType",label:"Tipo Analisi",type:"select",value:g.value,options:[{value:"productivity",label:"Produttività"},{value:"utilization",label:"Utilizzo"},{value:"billing",label:"Fatturazione"},{value:"trends",label:"Trend"}]}]),L=v(()=>[{id:"total-hours",label:"Ore Totali",value:d.value.totalHours,format:"hours",change:d.value.totalHoursChange,icon:"clock",iconBgColor:"bg-blue-100 dark:bg-blue-900",iconColor:"text-blue-600 dark:text-blue-400"},{id:"productivity",label:"Produttività Media",value:d.value.avgProductivity,format:"percentage",change:d.value.productivityChange,icon:"chart-line",iconBgColor:"bg-green-100 dark:bg-green-900",iconColor:"text-green-600 dark:text-green-400"},{id:"revenue",label:"Revenue Generato",value:d.value.revenue,format:"currency",change:d.value.revenueChange,icon:"currency-dollar",iconBgColor:"bg-yellow-100 dark:bg-yellow-900",iconColor:"text-yellow-600 dark:text-yellow-400"},{id:"utilization",label:"Utilizzo Risorse",value:d.value.utilization,format:"percentage",change:d.value.utilizationChange,icon:"chart-line",iconBgColor:"bg-purple-100 dark:bg-purple-900",iconColor:"text-purple-600 dark:text-purple-400"}]),G=v(()=>[{key:"employee",label:"Dipendente",sortable:!0,width:"25%"},{key:"hours",label:"Ore",sortable:!0,align:"right",width:"20%"},{key:"productivity",label:"Produttività",sortable:!0,width:"25%"},{key:"revenue",label:"Revenue",sortable:!0,align:"right",width:"15%"},{key:"utilization",label:"Utilizzo",sortable:!0,align:"center",width:"15%"}]),V=v(()=>[{key:"project",label:"Progetto",sortable:!0,width:"30%"},{key:"hours",label:"Ore",sortable:!0,align:"right",width:"15%"},{key:"progress",label:"Progresso",sortable:!0,width:"20%"},{key:"budget",label:"Budget",sortable:!0,align:"right",width:"20%"},{key:"status",label:"Stato",sortable:!0,align:"center",width:"15%"}]),q=(t,a)=>{switch(t){case"period":m.value=a;break;case"department":y.value=a;break;case"project":h.value=a;break;case"analysisType":g.value=a;break}k()},Z=()=>{m.value="current_month",y.value="",h.value="",g.value="productivity",k()},k=async()=>{i.value=!0,u.value=null;try{const t={period:m.value,department:y.value,project:h.value,analysisType:g.value},r=(await w.get("/api/timesheets/analytics/",{params:t})).data.data||{},c={totalHours:0,totalHoursChange:0,avgProductivity:0,productivityChange:0,revenue:0,revenueChange:0,utilization:0,utilizationChange:0,...r.metrics};d.value=Object.fromEntries(Object.entries(c).map(([ie,z])=>[ie,typeof z=="number"&&!isNaN(z)?z:0])),A.value=r.team||[],D.value=r.projects||[],await ve(),ee()}catch(t){console.error("Error loading analytics:",t),u.value="Errore nel caricamento dei dati analytics"}finally{i.value=!1}},Q=async()=>{i.value=!0;try{const t={period:m.value,department:y.value,project:h.value,analysisType:g.value},a=await w.get("/api/timesheets/analytics/",{params:{...t,export:"xlsx"},responseType:"blob"}),r=window.URL.createObjectURL(new Blob([a.data])),c=document.createElement("a");c.href=r,c.setAttribute("download",`timesheet-analytics-${new Date().toISOString().split("T")[0]}.xlsx`),document.body.appendChild(c),c.click(),c.remove(),window.URL.revokeObjectURL(r)}catch(t){console.error("Error exporting data:",t),u.value="Errore nell'esportazione dei dati"}finally{i.value=!1}},_=t=>{const a=typeof t=="number"&&!isNaN(t)?t:0;return new Intl.NumberFormat("it-IT",{minimumFractionDigits:0,maximumFractionDigits:0}).format(a)},W=t=>t!=null&&t.name?t.name.split(" ").map(a=>a.charAt(0)).join("").toUpperCase().slice(0,2):"??",J=t=>t>=90?"bg-green-500":t>=75?"bg-yellow-500":"bg-red-500",K=t=>t>=85?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300":t>=70?"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300":"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",X=t=>({active:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",on_hold:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",completed:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",cancelled:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"})[t]||"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300",Y=t=>({active:"Attivo",on_hold:"In Pausa",completed:"Completato",cancelled:"Cancellato"})[t]||t,ee=()=>{O(),te(),ae(),re(),se()},O=()=>{var a;const t=(a=F.value)==null?void 0:a.getContext("2d");t&&(l.productivity&&l.productivity.destroy(),l.productivity=new f(t,{type:"line",data:{labels:["Gen","Feb","Mar","Apr","Mag","Giu"],datasets:[{label:"Produttività %",data:[75,80,85,82,88,90],borderColor:"rgb(59, 130, 246)",backgroundColor:"rgba(59, 130, 246, 0.1)",tension:.4}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1}},scales:{y:{beginAtZero:!0,max:100}}}}))},te=()=>{var a;const t=(a=N.value)==null?void 0:a.getContext("2d");t&&(l.distribution&&l.distribution.destroy(),l.distribution=new f(t,{type:"doughnut",data:{labels:["Fatturabili","Non Fatturabili","Formazione","Riunioni"],datasets:[{data:[65,20,10,5],backgroundColor:["rgb(34, 197, 94)","rgb(239, 68, 68)","rgb(59, 130, 246)","rgb(168, 85, 247)"]}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom"}}}}))},ae=()=>{var a;const t=(a=B.value)==null?void 0:a.getContext("2d");t&&(l.department&&l.department.destroy(),l.department=new f(t,{type:"bar",data:{labels:["IT","Marketing","Sales","HR"],datasets:[{label:"Ore",data:[120,85,95,45],backgroundColor:"rgba(59, 130, 246, 0.8)"}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1}}}}))},re=()=>{var a;const t=(a=E.value)==null?void 0:a.getContext("2d");t&&(l.billing&&l.billing.destroy(),l.billing=new f(t,{type:"pie",data:{labels:["Fatturabili","Non Fatturabili"],datasets:[{data:[75,25],backgroundColor:["rgb(34, 197, 94)","rgb(239, 68, 68)"]}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom"}}}}))},se=()=>{var a;const t=(a=M.value)==null?void 0:a.getContext("2d");t&&(l.weekly&&l.weekly.destroy(),l.weekly=new f(t,{type:"line",data:{labels:["Lun","Mar","Mer","Gio","Ven"],datasets:[{label:"Ore Giornaliere",data:[8,7.5,8.2,7.8,8.5],borderColor:"rgb(168, 85, 247)",backgroundColor:"rgba(168, 85, 247, 0.1)",tension:.4}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1}},scales:{y:{beginAtZero:!0,max:10}}}}))},le=async()=>{try{const[t,a]=await Promise.all([w.get("/api/departments"),w.get("/api/projects")]);P.value=t.data.data||[],R.value=a.data.data||[]}catch(t){console.error("Error loading initial data:",t)}},oe=()=>{Object.values(l).forEach(t=>{t&&t.destroy()}),l={}};return be(p,()=>{O()}),pe(async()=>{await le(),await k()}),me(()=>{oe()}),(t,a)=>(C(),ye("div",xe,[u.value?(C(),T(ue,{key:0,alerts:$.value},null,8,["alerts"])):he("",!0),b(ne,{title:"Analytics Timesheet",subtitle:"Report avanzati e analisi delle performance del team",icon:"chart-bar","icon-color":"text-purple-600"},{actions:n(()=>[e("div",fe,[e("button",{onClick:Q,disabled:i.value,class:"inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium disabled:opacity-50 transition-colors"},[b(j,{name:"arrow-down-tray",size:"sm",class:"mr-2"}),H(" "+s(i.value?"Esportando...":"Esporta Report"),1)],8,ke),e("button",{onClick:k,disabled:i.value,class:"inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium disabled:opacity-50 transition-colors"},[i.value?(C(),T(j,{key:0,name:"arrow-path",size:"sm",className:"animate-spin mr-2"})):(C(),T(j,{key:1,name:"arrow-path",size:"sm",class:"mr-2"})),H(" "+s(i.value?"Caricando...":"Aggiorna"),1)],8,_e)])]),_:1}),b(ce,{filters:I.value,onFilterChange:q,onResetFilters:Z},null,8,["filters"]),b(de,{stats:L.value},null,8,["stats"]),e("div",we,[e("div",Ce,[e("div",ze,[a[2]||(a[2]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Trend Produttività",-1)),e("div",je,[e("button",{onClick:a[0]||(a[0]=r=>p.value="week"),class:x([p.value==="week"?"bg-brand-primary-100 dark:bg-brand-primary-900 text-brand-primary-700 dark:text-brand-primary-300":"text-gray-500 hover:text-gray-700 dark:hover:text-gray-300","px-3 py-1 rounded text-sm transition-colors"])}," Settimana ",2),e("button",{onClick:a[1]||(a[1]=r=>p.value="month"),class:x([p.value==="month"?"bg-brand-primary-100 dark:bg-brand-primary-900 text-brand-primary-700 dark:text-brand-primary-300":"text-gray-500 hover:text-gray-700 dark:hover:text-gray-300","px-3 py-1 rounded text-sm transition-colors"])}," Mese ",2)])]),e("div",Te,[e("canvas",{ref_key:"productivityChart",ref:F,class:"w-full h-full"},null,512)])]),e("div",Pe,[e("div",Re,[a[3]||(a[3]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Distribuzione Ore",-1)),e("div",Ae,[e("span",De,"Tipo: "+s(g.value),1)])]),e("div",Fe,[e("canvas",{ref_key:"distributionChart",ref:N,class:"w-full h-full"},null,512)])])]),e("div",Ne,[a[4]||(a[4]=e("div",{class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},[e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Performance del Team")],-1)),b(S,{columns:G.value,data:A.value,loading:i.value,searchable:!0,sortable:!0,"empty-message":"Nessun dato disponibile per il periodo selezionato"},{"cell-employee":n(({row:r})=>[e("div",Be,[e("div",Ee,[e("span",Me,s(W(r.employee)),1)]),e("div",null,[e("p",Oe,s(r.employee.name),1),e("p",Se,s(r.employee.department),1)])])]),"cell-hours":n(({row:r})=>[e("div",He,[e("p",Ue,s(r.hours||0)+"h",1),e("p",$e,s(r.billableHours||0)+"h fatturabili",1)])]),"cell-productivity":n(({row:r})=>[e("div",Ie,[e("div",Le,[e("div",{class:x(["h-2 rounded-full transition-all duration-300",J(r.productivity||0)]),style:U({width:`${Math.min(r.productivity||0,100)}%`})},null,6)]),e("span",Ge,s(r.productivity||0)+"%",1)])]),"cell-revenue":n(({row:r})=>[e("div",Ve,[e("p",qe,"€"+s(_(r.revenue)),1),e("p",Ze,"€"+s(_(r.avgHourlyRate))+"/h",1)])]),"cell-utilization":n(({row:r})=>[e("span",{class:x(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",K(r.utilization||0)])},s(r.utilization||0)+"% ",3)]),_:1},8,["columns","data","loading"])]),e("div",Qe,[a[5]||(a[5]=e("div",{class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},[e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Breakdown per Progetto")],-1)),b(S,{columns:V.value,data:D.value,loading:i.value,searchable:!0,sortable:!0,"empty-message":"Nessun progetto nel periodo selezionato"},{"cell-project":n(({row:r})=>[e("div",null,[e("p",We,s(r.project.name),1),e("p",Je,s(r.project.client),1)])]),"cell-hours":n(({row:r})=>[e("div",Ke,[e("p",Xe,s(r.totalHours)+"h",1),e("p",Ye,s(r.teamMembers)+" persone",1)])]),"cell-progress":n(({row:r})=>[e("div",et,[e("div",tt,[e("div",{class:"bg-brand-primary-600 h-2 rounded-full transition-all duration-300",style:U({width:`${Math.min(r.progress,100)}%`})},null,4)]),e("span",at,s(r.progress)+"%",1)])]),"cell-budget":n(({row:r})=>[e("div",rt,[e("p",st,"€"+s(_(r.budgetUsed)),1),e("p",lt,"di €"+s(_(r.totalBudget)),1)])]),"cell-status":n(({row:r})=>[e("span",{class:x(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",X(r.status)])},s(Y(r.status)),3)]),_:1},8,["columns","data","loading"])]),e("div",ot,[e("div",it,[a[6]||(a[6]=e("h4",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Confronto Dipartimenti",-1)),e("div",nt,[e("canvas",{ref_key:"departmentChart",ref:B,class:"w-full h-full"},null,512)])]),e("div",dt,[a[7]||(a[7]=e("h4",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Ore Fatturabili vs Non",-1)),e("div",ct,[e("canvas",{ref_key:"billingChart",ref:E,class:"w-full h-full"},null,512)])]),e("div",ut,[a[8]||(a[8]=e("h4",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Trend Settimanale",-1)),e("div",gt,[e("canvas",{ref_key:"weeklyChart",ref:M,class:"w-full h-full"},null,512)])])])]))}};export{Ct as default};
