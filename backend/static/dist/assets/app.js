const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index.js","assets/jsx-runtime-BDYu3_Il.js","assets/vendor.js","assets/index.es.js","assets/index2.js","assets/index3.js","assets/index4.js","assets/PlusIcon.js","assets/Home.js","assets/About.js","assets/Contact.js","assets/Services.js","assets/Privacy.js","assets/CookiePolicy.js","assets/Login.js","assets/Register.js","assets/Dashboard.js","assets/DashboardTemplate.js","assets/DashboardTemplate.css","assets/useToast.js","assets/Dashboard.css","assets/Projects.js","assets/projects2.js","assets/PageHeader.js","assets/FilterBar.js","assets/ProjectCreate.js","assets/ProjectView.js","assets/ProjectView.css","assets/ProjectEdit.js","assets/TimesheetEntry.js","assets/AlertsSection.js","assets/TimesheetGrid.js","assets/TimesheetGrid.css","assets/timesheet.js","assets/TimesheetRequests.js","assets/TimesheetDashboard.js","assets/StatsGrid.js","assets/TabContainer.js","assets/TabContainer.css","assets/personnel.js","assets/TabNavigation.css","assets/TimesheetAnalytics.js","assets/DataTable.js","assets/formatters.js","assets/auto.js","assets/CommunicationDashboard.js","assets/useFormatters.js","assets/StatusBadge.js","assets/StatusBadge.css","assets/CommunicationDashboard.css","assets/ForumIndex.js","assets/ListPageTemplate.js","assets/Pagination.js","assets/Pagination.css","assets/EditTopicModal.js","assets/ConfirmationModal.js","assets/ForumIndex.css","assets/TopicView.js","assets/TopicView.css","assets/PollsIndex.js","assets/EditPollModal.js","assets/EditPollModal.css","assets/PollsIndex.css","assets/PollView.js","assets/PollView.css","assets/MessagesIndex.js","assets/MessagesIndex.css","assets/EventsIndex.js","assets/EventEditModal.js","assets/EventEditModal.css","assets/EventsIndex.css","assets/EventView.js","assets/NewsIndex.js","assets/FormBuilder.js","assets/FormBuilder.css","assets/NewsIndex.css","assets/NewsView.js","assets/NewsView.css","assets/HRAssistantChat.js","assets/ConfidenceBadge.js","assets/HRKnowledgeBase.js","assets/DashboardExample.js","assets/DashboardExample.css","assets/TimesheetGridExample.js","assets/ComponentsExample.js","assets/TabNavigation.js","assets/ViewModeToggle.js","assets/ViewModeToggle.css","assets/KanbanView.js","assets/ProposalCard.js","assets/ProposalCard.css","assets/FormBuilderExample.js","assets/FormBuilderExample.css","assets/ViewModeToggleExample.js","assets/ViewModeToggleExample.css","assets/KanbanExample.js","assets/ProposalCardExample.js","assets/IconSystemExample.js","assets/WizardContainerExample.js","assets/WizardContainer.js","assets/WizardContainer.css","assets/WizardContainerExample.css","assets/PersonnelOrgChart.js","assets/PersonnelOrgChart.css","assets/SkillsMatrix.js","assets/SkillsMatrix.css","assets/JobLevels.js","assets/JobLevels.css","assets/DepartmentCreate.js","assets/DepartmentView.js","assets/DepartmentEdit.js","assets/PersonnelAllocation.js","assets/PersonnelAdmin.js","assets/PersonnelAdmin.css","assets/PersonnelPerformance.js","assets/PersonnelPerformance.css","assets/PersonnelProfile.js","assets/PersonnelProfile.css","assets/Admin.js","assets/AdminSettings.js","assets/KPITemplates.js","assets/Profile.js","assets/Settings.js","assets/CRMDashboard.js","assets/crm.js","assets/CRMDashboard.css","assets/ClientsList.js","assets/industries.js","assets/ClientForm.js","assets/ClientView.js","assets/ContactsList.js","assets/ProposalsPipeline.js","assets/ProposalsPipeline.css","assets/ProposalForm.js","assets/ProposalView.js","assets/ContractsList.js","assets/contractTypes.js","assets/ContractForm.js","assets/ContractView.js","assets/PreInvoicesList.js","assets/PreInvoiceForm.js","assets/PreInvoiceView.js","assets/BIDashboard.js","assets/CaseStudies.js","assets/CoreSkills.js","assets/TechnicalOffer.js","assets/MarketIntelligence.js","assets/AdvancedReports.js","assets/CertificationsDashboard.js","assets/certifications.js","assets/CertificationsList.js","assets/CertificationsCatalog.js","assets/Breadcrumb.js","assets/CertificationCreate.js","assets/CertificationView.js","assets/CertificationEdit.js","assets/CertificationReadiness.js","assets/CertificationReadiness.css","assets/CEODashboard.js","assets/marked.esm.js","assets/CEODashboard.css","assets/AIAssistant.js","assets/AIAssistant.css","assets/InsightsReports.js","assets/InsightsReports.css","assets/ResearchConfig.js","assets/ResearchConfig.css","assets/FundingDashboard.js","assets/funding.js","assets/FundingDashboard.css","assets/FundingSearch.js","assets/FundingSearch.css","assets/FundingOpportunityView.js","assets/FundingOpportunityView.css","assets/FundingApplicationView.js","assets/FundingApplicationView.css","assets/FundingReporting.js","assets/FundingReporting.css","assets/FundingApplicationForm.js","assets/FundingApplicationForm.css","assets/FundingExpenseForm.js","assets/FundingExpenseView.js","assets/DesignSystemTest.js"])))=>i.map(i=>d[i]);
import{r as I,w as Z,a as Re,d as J,c as x,u as Y,o as F,b as l,e as w,f as D,g as C,h as O,i as ie,j as i,k as Le,m as Se,l as a,n as V,p as E,t as P,F as B,q as U,s as G,v as M,x as T,y as Oe,z as ze,A as Ne,B as Be,C as He,D as W,E as Ze,T as Ue,G as Fe,H as Ge,I as We,J as Ke,K as Qe,L as Je,M as Ye}from"./vendor.js";(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))s(n);new MutationObserver(n=>{for(const o of n)if(o.type==="childList")for(const m of o.addedNodes)m.tagName==="LINK"&&m.rel==="modulepreload"&&s(m)}).observe(document,{childList:!0,subtree:!0});function r(n){const o={};return n.integrity&&(o.integrity=n.integrity),n.referrerPolicy&&(o.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?o.credentials="include":n.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function s(n){if(n.ep)return;n.ep=!0;const o=r(n);fetch(n.href,o)}})();const Xe="modulepreload",et=function(t){return"/"+t},ue={},d=function(e,r,s){let n=Promise.resolve();if(r&&r.length>0){document.getElementsByTagName("link");const m=document.querySelector("meta[property=csp-nonce]"),f=(m==null?void 0:m.nonce)||(m==null?void 0:m.getAttribute("nonce"));n=Promise.allSettled(r.map(v=>{if(v=et(v),v in ue)return;ue[v]=!0;const _=v.endsWith(".css"),p=_?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${v}"]${p}`))return;const c=document.createElement("link");if(c.rel=_?"stylesheet":Xe,_||(c.as="script"),c.crossOrigin="",c.href=v,f&&c.setAttribute("nonce",f),document.head.appendChild(c),_)return new Promise((u,h)=>{c.addEventListener("load",u),c.addEventListener("error",()=>h(new Error(`Unable to preload CSS for ${v}`)))})}))}function o(m){const f=new Event("vite:preloadError",{cancelable:!0});if(f.payload=m,window.dispatchEvent(f),!f.defaultPrevented)throw m}return n.then(m=>{for(const f of m||[])f.status==="rejected"&&o(f.reason);return e().catch(o)})},N=I(!1);let de=!1;const Me=t=>{t?(document.documentElement.classList.add("dark"),localStorage.setItem("darkMode","true")):(document.documentElement.classList.remove("dark"),localStorage.setItem("darkMode","false"))},tt=()=>{de||(Z(N,t=>{Me(t)}),de=!0)};function ce(){return tt(),{isDarkMode:N,toggleDarkMode:()=>{N.value=!N.value},setDarkMode:s=>{N.value=s},initializeDarkMode:()=>{const s=localStorage.getItem("darkMode"),n=document.documentElement.classList.contains("dark");if(s==="true")N.value=!0;else if(s==="false")N.value=!1;else{const f=window.matchMedia("(prefers-color-scheme: dark)").matches;N.value=n||f}Me(N.value);const o=window.matchMedia("(prefers-color-scheme: dark)"),m=f=>{const v=localStorage.getItem("darkMode");(!v||v==="null")&&(N.value=f.matches)};o.addEventListener("change",m)}}}const y=Re.create({baseURL:"",timeout:6e4,withCredentials:!0,headers:{"Content-Type":"application/json"}});y.interceptors.request.use(t=>{var r,s;const e=(r=document.querySelector('meta[name="csrf-token"]'))==null?void 0:r.getAttribute("content");return e&&["post","put","patch","delete"].includes((s=t.method)==null?void 0:s.toLowerCase())&&(t.headers["X-CSRFToken"]=e),t},t=>Promise.reject(t));y.interceptors.response.use(t=>t,t=>{var e;return((e=t.response)==null?void 0:e.status)===401&&(localStorage.removeItem("user"),console.warn("Sessione scaduta, autenticazione richiesta")),Promise.reject(t)});const X=J("tenant",()=>{const t=I(null),e=I(!1),r=I(null),s=x(()=>{var p;return((p=t.value)==null?void 0:p.company)||{}}),n=x(()=>{var p;return((p=t.value)==null?void 0:p.contact)||{}}),o=x(()=>{var p;return((p=t.value)==null?void 0:p.pages)||{}}),m=x(()=>{var p;return((p=t.value)==null?void 0:p.navigation)||{}}),f=x(()=>{var p;return((p=t.value)==null?void 0:p.footer)||{}});async function v(){try{if(e.value=!0,window.TENANT_CONFIG){t.value=window.TENANT_CONFIG;return}const p=await fetch("/api/config/tenant");t.value=await p.json()}catch(p){r.value="Errore nel caricamento della configurazione",console.error("Errore caricamento tenant config:",p)}finally{e.value=!1}}function _(p,c={}){if(!p||typeof p!="string")return p;let u=p;const h={"company.name":s.value.name||"DatVinci","company.tagline":s.value.tagline||"","company.description":s.value.description||"","company.mission":s.value.mission||"","company.vision":s.value.vision||"","company.founded":s.value.founded||"","company.team_size":s.value.team_size||"","contact.email":n.value.email||"","contact.phone":n.value.phone||"","contact.address":n.value.address||"",current_year:new Date().getFullYear().toString(),...c};for(const[k,g]of Object.entries(h)){const A=new RegExp(`\\{${k}\\}`,"g");u=u.replace(A,g||"")}return u}return{config:t,loading:e,error:r,company:s,contact:n,pages:o,navigation:m,footer:f,loadConfig:v,interpolateText:_}}),me=I("");function rt(){const t=Y(),e=X(),r={home:"home",about:"about",contact:"contact",services:"services",privacy:"privacy",login:"Accesso - {company.name}",register:"Registrazione - {company.name}",dashboard:"Dashboard - {company.name}",projects:"Progetti - {company.name}","project-view":"Progetto - {company.name}","project-create":"Nuovo Progetto - {company.name}","project-edit":"Modifica Progetto - {company.name}",tasks:"Task - {company.name}","task-view":"Task - {company.name}","task-create":"Nuovo Task - {company.name}","task-edit":"Modifica Task - {company.name}",personnel:"Personale - {company.name}","personnel-profile":"Profilo Personale - {company.name}",timesheet:"Timesheet - {company.name}","timesheet-entry":"Inserimento Timesheet - {company.name}","timesheet-analytics":"Analytics Timesheet - {company.name}","timesheet-requests":"Richieste Timesheet - {company.name}","timesheet-dashboard":"Dashboard Timesheet - {company.name}",sales:"Vendite - {company.name}","sales-dashboard":"Dashboard Vendite - {company.name}","sales-opportunities":"Opportunità - {company.name}","sales-clients":"Clienti - {company.name}","sales-reports":"Report Vendite - {company.name}",invoicing:"Fatturazione - {company.name}","invoicing-invoices":"Fatture - {company.name}","invoicing-pre-invoices":"Pre-fatture - {company.name}","invoicing-invoice-create":"Nuova Fattura - {company.name}","invoicing-pre-invoice-create":"Nuova Pre-fattura - {company.name}","business-intelligence":"Business Intelligence - {company.name}","business-intelligence-dashboard":"BI Dashboard - {company.name}","business-intelligence-case-studies":"Case Studies - {company.name}","business-intelligence-core-skills":"Competenze Core - {company.name}","business-intelligence-technical-offer":"Offerta Tecnica - {company.name}","business-intelligence-market-intel":"Market Intelligence - {company.name}","business-intelligence-advanced-reports":"Reportistica Avanzata - {company.name}",admin:"Amministrazione - {company.name}","admin-users":"Gestione Utenti - {company.name}","admin-kpi-templates":"Template KPI - {company.name}",profile:"Il tuo Profilo - {company.name}",settings:"Impostazioni - {company.name}"};function s(v,_){var p,c;return!v||!_?"Portale":v.replace(/{company\.name}/g,((p=_.company)==null?void 0:p.name)||"Portale").replace(/{company\.tagline}/g,((c=_.company)==null?void 0:c.tagline)||"").replace(/{current_year}/g,new Date().getFullYear())}function n(v,_){var c,u;let p=r[v];return p||["home","about","contact","services","privacy"].includes(v)&&(p=(u=(c=_==null?void 0:_.routes)==null?void 0:c[v])==null?void 0:u.title),p||(p="{company.name}"),p.includes("{")?s(p,_):p}function o(v){v&&(me.value=v,document.title=v)}function m(){const v=e.config;if(!v)return;const _=t.name||t.path.split("/").pop()||"home",p=n(_,v);o(p)}function f(v){const _=e.config,p=s(v,_);o(p)}return Z(()=>t.name,()=>m(),{immediate:!0}),Z(()=>e.config,()=>m(),{immediate:!0}),{currentTitle:me,updateTitle:o,setTitleFromRoute:m,setCustomTitle:f,getTitleForRoute:n}}const at={id:"app"},st={__name:"App",setup(t){const{initializeDarkMode:e}=ce();e(),rt();const r=I(null),s=I(null),n=x(()=>!1),o=x(()=>({plugins:s.value?[s.value]:[]}));return F(async()=>{if(n.value)try{const[m,f]=await Promise.all([d(()=>import("./index.js"),__vite__mapDeps([0,1,2])),d(()=>import("./index.es.js"),__vite__mapDeps([3,1]))]);r.value=m.StagewiseToolbar,s.value=f.VuePlugin}catch(m){console.warn("Failed to load Stagewise components:",m)}}),(m,f)=>{const v=O("router-view");return i(),l("div",at,[w(v),n.value&&r.value?(i(),D(ie(r.value),{key:0,config:o.value},null,8,["config"])):C("",!0)])}}},H=J("auth",()=>{var A;const t=localStorage.getItem("user"),e=I(t?JSON.parse(t):null),r=I(!1),s=I(null),n=I(!1),o=I(((A=document.querySelector('meta[name="csrf-token"]'))==null?void 0:A.getAttribute("content"))||""),m=async()=>{var b,q,L;try{const R=await y.get("/api/auth/csrf-token");(b=R.data)!=null&&b.success&&((L=(q=R.data)==null?void 0:q.data)!=null&&L.csrf_token)&&(o.value=R.data.data.csrf_token,console.log("CSRF token refreshed successfully"))}catch(R){console.error("Error refreshing CSRF token:",R)}return o.value},f=x(()=>!!e.value&&n.value),v={admin:["admin","manage_users","assign_roles","view_all_projects","create_project","edit_project","delete_project","assign_to_project","manage_project_tasks","manage_project_resources","approve_timesheets","view_personnel_data","edit_personnel_data","view_contracts","manage_contracts","view_crm","manage_clients","manage_proposals","view_reports","view_dashboard","submit_timesheet","view_own_timesheets","view_funding","manage_funding","view_products","manage_products","view_performance","manage_performance","view_communications","manage_communications","view_startup","manage_startup","view_ceo","view_compliance","view_agents"],manager:["view_dashboard","view_all_projects","edit_project","assign_to_project","manage_project_tasks","manage_project_resources","approve_timesheets","view_personnel_data","view_crm","view_reports","submit_timesheet","view_own_timesheets","manage_clients","manage_proposals","view_funding","manage_funding","view_products","manage_products","view_performance","manage_performance","view_communications","manage_communications","view_startup","manage_startup","view_ceo","view_compliance","view_agents"],employee:["view_dashboard","view_own_timesheets","submit_timesheet"],sales:["view_dashboard","view_crm","manage_clients","manage_proposals","submit_timesheet","view_own_timesheets","view_reports","view_funding","view_products","manage_products"],human_resources:["view_dashboard","manage_users","view_personnel_data","edit_personnel_data","view_contracts","manage_contracts","submit_timesheet","view_own_timesheets","view_reports","view_funding","manage_funding","view_performance","manage_performance","view_communications","manage_communications","view_startup","manage_startup"]},_=b=>!e.value||!e.value.role?!1:e.value.role==="admin"?!0:(v[e.value.role]||[]).includes(b),p=()=>{var b,q;console.log("Current user:",e.value),console.log("User role:",(b=e.value)==null?void 0:b.role),console.log("Has admin permission:",_("admin")),console.log("Available permissions for role:",v[(q=e.value)==null?void 0:q.role])};async function c(b){var q,L;r.value=!0,s.value=null;try{await m();const R=await y.post("/api/auth/login",b);return R.data.success?(e.value=R.data.data.user,localStorage.setItem("user",JSON.stringify(e.value)),n.value=!0,{success:!0}):(s.value=R.data.message||"Errore durante il login",{success:!1,error:s.value})}catch(R){return s.value=((L=(q=R.response)==null?void 0:q.data)==null?void 0:L.message)||"Errore di connessione",{success:!1,error:s.value}}finally{r.value=!1}}async function u(b){var q,L;r.value=!0,s.value=null;try{await m();const R=await y.post("/api/auth/register",b);return R.data.success?{success:!0,message:R.data.message}:(s.value=R.data.message||"Errore durante la registrazione",{success:!1,error:s.value})}catch(R){return s.value=((L=(q=R.response)==null?void 0:q.data)==null?void 0:L.message)||"Errore di connessione",{success:!1,error:s.value}}finally{r.value=!1}}async function h(){try{await y.post("/api/auth/logout")}catch(b){console.warn("Errore durante il logout:",b)}finally{e.value=null,n.value=!1,localStorage.removeItem("user")}}async function k(){if(n.value)return f.value;try{const b=await y.get("/api/auth/me");return b.data.success?(e.value=b.data.data.user,localStorage.setItem("user",JSON.stringify(e.value)),n.value=!0,!0):(await h(),!1)}catch{return await h(),!1}}async function g(){return e.value?(await m(),await k()):(n.value=!0,!1)}return{user:e,loading:r,error:s,sessionChecked:n,isAuthenticated:f,hasPermission:_,debugPermissions:p,login:c,register:u,logout:h,checkAuth:k,initializeAuth:g,csrfToken:o,refreshCsrfToken:m}}),nt={add:"plus",create:"plus",new:"plus",edit:"pencil",modify:"pencil",delete:"trash",remove:"trash",save:"check",cancel:"x-mark",close:"x-mark",search:"magnifying-glass",filter:"funnel",sort:"bars-3",refresh:"arrow-path",reload:"arrow-path",download:"arrow-down-tray",upload:"arrow-up-tray",export:"arrow-up-tray",import:"arrow-down-tray",menu:"bars-3",hamburger:"bars-3",back:"arrow-left",forward:"arrow-right",next:"chevron-right",previous:"chevron-left",prev:"chevron-left",up:"chevron-up",down:"chevron-down",expand:"chevron-down",collapse:"chevron-up",external:"arrow-top-right-on-square","external-link":"arrow-top-right-on-square",success:"check-circle",error:"x-circle",warning:"exclamation-triangle",info:"information-circle",loading:"arrow-path",spinner:"arrow-path",pending:"clock",approved:"check-circle",rejected:"x-circle",draft:"document",user:"user",users:"users","user-circle":"user-circle",team:"user-group",profile:"user-circle",account:"user-circle",ban:"no-symbol",block:"no-symbol",disable:"no-symbol",client:"home-modern",company:"home-modern",project:"folder",projects:"folder",task:"clipboard",tasks:"clipboard","clipboard-list":"clipboard",document:"document",file:"document",folder:"folder",calendar:"calendar",date:"calendar",time:"clock",timer:"clock",timesheet:"clipboard",report:"presentation-chart-bar",analytics:"chart-bar",dashboard:"squares-plus",settings:"wrench-screwdriver",config:"wrench-screwdriver","cog-6-tooth":"cog-6-tooth",admin:"shield-check","shield-check":"shield-check","book-open":"book-open","clipboard-document-check":"clipboard-document-check","calendar-days":"calendar-days",email:"envelope",mail:"envelope",message:"chat-bubble-left",chat:"chat-bubble-left",notification:"bell",alert:"bell",phone:"phone",call:"phone",money:"banknotes",payment:"credit-card",invoice:"document-text",bill:"document-text",expense:"credit-card",budget:"calculator",price:"currency-euro",cost:"currency-euro",revenue:"arrow-trending-up",profit:"arrow-trending-up","trending-up":"arrow-trending-up",funding:"banknotes",grants:"gift",opportunity:"light-bulb",application:"document-plus","lightning-bolt":"bolt","light-bulb":"light-bulb",reporting:"presentation-chart-bar",database:"circle-stack",server:"server",cloud:"cloud",api:"code-bracket",code:"code-bracket",bug:"bug-ant",security:"shield-check",lock:"lock-closed",unlock:"lock-open",key:"key",ai:"cpu-chip",agents:"cpu-chip","artificial-intelligence":"cpu-chip",automation:"cpu-chip",bot:"cpu-chip","machine-learning":"cpu-chip",brain:"cpu-chip",education:"academic-cap",certification:"academic-cap",certifications:"academic-cap",compliance:"academic-cap",training:"academic-cap",learning:"academic-cap",course:"academic-cap",degree:"academic-cap",qualification:"academic-cap",readiness:"clipboard-document-check",audit:"calendar-days",audits:"calendar-days",catalog:"book-open",standards:"book-open",renewal:"arrow-path",health:"heart",score:"chart-bar",organization:"building-office-2",office:"building-office-2",building:"building-office-2",department:"building-office-2",departments:"building-office-2",ceo:"building-office-2",executive:"building-office-2",management:"building-office-2",corporate:"building-office-2",headquarters:"building-office-2",design:"sparkles",creative:"sparkles",magic:"sparkles",inspiration:"sparkles",innovation:"sparkles",style:"sparkles",theme:"sparkles",image:"photo",photo:"photo",video:"video-camera",camera:"camera",microphone:"microphone",speaker:"speaker-wave",volume:"speaker-wave",play:"play",pause:"pause",stop:"stop",home:"home",star:"star",heart:"heart",bookmark:"bookmark",tag:"tag",label:"tag",link:"link",share:"share",copy:"document-duplicate",paste:"clipboard",cut:"scissors",print:"printer",view:"eye",hide:"eye-slash",visible:"eye",invisible:"eye-slash",grid:"squares-plus",list:"list-bullet",table:"table-cells",card:"rectangle-stack",sidebar:"bars-3",fullscreen:"rectangle-group",minimize:"minus",kanban:"view-columns",columns:"view-columns",board:"view-columns",proposal:"identification",badge:"identification","id-card":"identification",move:"hand-raised",drag:"hand-raised",resize:"hand-raised",rotate:"arrow-path",flip:"arrow-path",chart:"chart-bar","chart-pie":"chart-pie","pie-chart":"chart-pie","analytics-pie":"chart-pie","squares-2x2":"squares-2x2","grid-2x2":"squares-2x2",components:"squares-2x2","components-base":"squares-2x2",briefcase:"briefcase",location:"map-pin",address:"map-pin",map:"map",pin:"map-pin",marker:"map-pin"};function ot(t){const e=t.toLowerCase().replace(/_/g,"-");return nt[e]||e}function nn(t){const e={"Sviluppo Software":"code-bracket","Intelligenza Artificiale":"cpu-chip","Consulenza IT":"computer-desktop","Gestione Progetti Innovativi":"briefcase","Gestione Progetti":"briefcase","Supporto su Bandi e Finanziamenti":"banknotes",Finanziamenti:"banknotes",default:"wrench-screwdriver"};return e[t]||e.default}const ee=(t,e)=>{const r=t.__vccOpts||t;for(const[s,n]of e)r[s]=n;return r},it={__name:"HeroIcon",props:{name:{type:String,required:!0},variant:{type:String,default:"outline",validator:t=>["outline","solid","mini"].includes(t)},size:{type:[String,Number],default:"md",validator:t=>typeof t=="number"?!0:["xs","sm","md","lg","xl","2xl"].includes(t)},color:{type:String,default:"currentColor"},ariaLabel:{type:String,default:null},className:{type:String,default:""}},setup(t){const e=t,r={xs:"w-3 h-3",sm:"w-4 h-4",md:"w-5 h-5",lg:"w-6 h-6",xl:"w-8 h-8","2xl":"w-10 h-10"},s=_=>_.split("-").map(p=>p.charAt(0).toUpperCase()+p.slice(1)).join("")+"Icon",n=new Map,o=I(null),m=async(_,p)=>{const c=`${_}-${p}`;if(n.has(c))return n.get(c);try{let u;switch(p){case"solid":u=await d(()=>import("./index2.js"),__vite__mapDeps([4,2]));break;case"mini":u=await d(()=>import("./index3.js"),__vite__mapDeps([5,2]));break;case"outline":default:u=await d(()=>import("./index4.js"),__vite__mapDeps([6,2,7]));break}const h=u[_];if(!h){console.warn(`HeroIcon: Icon "${_}" not found in ${p} variant`);const g=(await d(()=>import("./index4.js"),__vite__mapDeps([6,2,7]))).QuestionMarkCircleIcon;return n.set(c,g),g}return n.set(c,h),h}catch(u){return console.error(`Failed to load icon ${_}:`,u),(await d(()=>import("./index4.js"),__vite__mapDeps([6,2,7]))).QuestionMarkCircleIcon}};Le(async()=>{const _=ot(e.name),p=s(_),c=await m(p,e.variant);o.value=c});const f=x(()=>{const _=[];return typeof e.size=="string"&&r[e.size]?_.push(r[e.size]):e.size,e.color&&e.color!=="currentColor"&&_.push(e.color),e.className&&_.push(e.className),_.join(" ")}),v=x(()=>typeof e.size=="number"?{width:`${e.size}px`,height:`${e.size}px`}:{});return(_,p)=>(i(),D(ie(o.value),Se({class:f.value,style:v.value,"aria-hidden":!t.ariaLabel,"aria-label":t.ariaLabel},_.$attrs),null,16,["class","style","aria-hidden","aria-label"]))}},j=ee(it,[["__scopeId","data-v-47f32c2c"]]);function ct(){const t=H(),e=x(()=>h=>t.hasPermission(h)),r=x(()=>{var h;return((h=t.user)==null?void 0:h.role)||null}),s=x(()=>r.value==="admin"),n=x(()=>r.value==="manager"),o=x(()=>r.value==="employee"),m=x(()=>r.value==="sales"),f=x(()=>r.value==="human_resources"),v=x(()=>e.value("create_project")||e.value("edit_project")||e.value("delete_project")),_=x(()=>e.value("manage_users")||e.value("assign_roles")),p=x(()=>e.value("view_all_projects")),c=x(()=>e.value("view_personnel_data")||e.value("edit_personnel_data")),u=x(()=>e.value("approve_timesheets"));return{hasPermission:e,userRole:r,isAdmin:s,isManager:n,isEmployee:o,isSales:m,isHR:f,canManageProjects:v,canManageUsers:_,canViewAllProjects:p,canManagePersonnel:c,canApproveTimesheets:u}}function re(t,e){return i(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M4.26 10.147a60.438 60.438 0 0 0-.491 6.347A48.62 48.62 0 0 1 12 20.904a48.62 48.62 0 0 1 8.232-4.41 60.46 60.46 0 0 0-.491-6.347m-15.482 0a50.636 50.636 0 0 0-2.658-.813A59.906 59.906 0 0 1 12 3.493a59.903 59.903 0 0 1 10.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.717 50.717 0 0 1 12 13.489a50.702 50.702 0 0 1 7.74-3.342M6.75 15a.75.75 0 1 0 0-1.5.75.75 0 0 0 0 1.5Zm0 0v-3.675A55.378 55.378 0 0 1 12 8.443m-7.007 11.55A5.981 5.981 0 0 0 6.75 15.75v-1.5"})])}function lt(t,e){return i(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5M10 11.25h4M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z"})])}function ut(t,e){return i(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.25 18 9 11.25l4.306 4.306a11.95 11.95 0 0 1 5.814-5.518l2.74-1.22m0 0-5.94-2.281m5.94 2.28-2.28 5.941"})])}function dt(t,e){return i(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M7.5 21 3 16.5m0 0L7.5 12M3 16.5h13.5m0-13.5L21 7.5m0 0L16.5 12M21 7.5H7.5"})])}function mt(t,e){return i(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.25 18.75a60.07 60.07 0 0 1 15.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 0 1 3 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 0 0-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 0 1-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 0 0 3 15h-.75M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm3 0h.008v.008H18V10.5Zm-12 0h.008v.008H6V10.5Z"})])}function pt(t,e){return i(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9.75 3.104v5.714a2.25 2.25 0 0 1-.659 1.591L5 14.5M9.75 3.104c-.251.023-.501.05-.75.082m.75-.082a24.301 24.301 0 0 1 4.5 0m0 0v5.714c0 .597.237 1.17.659 1.591L19.8 15.3M14.25 3.104c.251.023.501.05.75.082M19.8 15.3l-1.57.393A9.065 9.065 0 0 1 12 15a9.065 9.065 0 0 0-6.23-.693L5 14.5m14.8.8 1.402 1.402c1.232 1.232.65 3.318-1.067 3.611A48.309 48.309 0 0 1 12 21c-2.773 0-5.491-.235-8.135-.687-1.718-.293-2.3-2.379-1.067-3.61L5 14.5"})])}function pe(t,e){return i(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"})])}function ht(t,e){return i(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M17.593 3.322c1.1.128 1.907 1.077 1.907 2.185V21L12 17.25 4.5 21V5.507c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0 1 11.186 0Z"})])}function he(t,e){return i(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.25 21h19.5m-18-18v18m10.5-18v18m6-13.5V21M6.75 6.75h.75m-.75 3h.75m-.75 3h.75m3-6h.75m-.75 3h.75m-.75 3h.75M6.75 21v-3.375c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21M3 3h12m-.75 4.5H21m-3.75 3.75h.008v.008h-.008v-.008Zm0 3h.008v.008h-.008v-.008Zm0 3h.008v.008h-.008v-.008Z"})])}function gt(t,e){return i(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3.75 21h16.5M4.5 3h15M5.25 3v18m13.5-18v18M9 6.75h1.5m-1.5 3h1.5m-1.5 3h1.5m3-6H15m-1.5 3H15m-1.5 3H15M9 21v-3.375c0-.621.504-1.125 1.125-1.125h3.75c.621 0 1.125.504 1.125 1.125V21"})])}function ge(t,e){return i(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z"})])}function ve(t,e){return i(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"})])}function fe(t,e){return i(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M10.5 6a7.5 7.5 0 1 0 7.5 7.5h-7.5V6Z"}),a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M13.5 10.5H21A7.5 7.5 0 0 0 13.5 3v7.5Z"})])}function vt(t,e){return i(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 0 1-.825-.242m9.345-8.334a2.126 2.126 0 0 0-.476-.095 48.64 48.64 0 0 0-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0 0 11.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155"})])}function ft(t,e){return i(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.25 12.76c0 1.6 1.123 2.994 2.707 3.227 1.087.16 2.185.283 3.293.369V21l4.076-4.076a1.526 1.526 0 0 1 1.037-.443 48.282 48.282 0 0 0 5.68-.494c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z"})])}function _t(t,e){return i(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"})])}function _e(t,e){return i(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M11.35 3.836c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m8.9-4.414c.376.023.75.05 1.124.08 1.131.094 1.976 1.057 1.976 2.192V16.5A2.25 2.25 0 0 1 18 18.75h-2.25m-7.5-10.5H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V18.75m-7.5-10.5h6.375c.621 0 1.125.504 1.125 1.125v9.375m-8.25-3 1.5 1.5 3-3.75"})])}function wt(t,e){return i(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z"})])}function yt(t,e){return i(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15.666 3.888A2.25 2.25 0 0 0 13.5 2.25h-3c-1.03 0-1.9.693-2.166 1.638m7.332 0c.055.194.084.4.084.612v0a.75.75 0 0 1-.75.75H9a.75.75 0 0 1-.75-.75v0c0-.212.03-.418.084-.612m7.332 0c.646.049 1.288.11 1.927.184 1.1.128 1.907 1.077 1.907 2.185V19.5a2.25 2.25 0 0 1-2.25 2.25H6.75A2.25 2.25 0 0 1 4.5 19.5V6.257c0-1.108.806-2.057 1.907-2.185a48.208 48.208 0 0 1 1.927-.184"})])}function ae(t,e){return i(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"})])}function xt(t,e){return i(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z"}),a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"})])}function kt(t,e){return i(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M4.5 12a7.5 7.5 0 0 0 15 0m-15 0a7.5 7.5 0 1 1 15 0m-15 0H3m16.5 0H21m-1.5 0H12m-8.457 3.077 1.41-.513m14.095-5.13 1.41-.513M5.106 17.785l1.15-.964m11.49-9.642 1.149-.964M7.501 19.795l.75-1.3m7.5-12.99.75-1.3m-6.063 16.658.26-1.477m2.605-14.772.26-1.477m0 17.726-.26-1.477M10.698 4.614l-.26-1.477M16.5 19.794l-.75-1.299M7.5 4.205 12 12m6.894 5.785-1.149-.964M6.256 7.178l-1.15-.964m15.352 8.864-1.41-.513M4.954 9.435l-1.41-.514M12.002 12l-3.75 6.495"})])}function we(t,e){return i(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M8.25 3v1.5M4.5 8.25H3m18 0h-1.5M4.5 12H3m18 0h-1.5m-15 3.75H3m18 0h-1.5M8.25 19.5V21M12 3v1.5m0 15V21m3.75-18v1.5m0 15V21m-9-1.5h10.5a2.25 2.25 0 0 0 2.25-2.25V6.75a2.25 2.25 0 0 0-2.25-2.25H6.75A2.25 2.25 0 0 0 4.5 6.75v10.5a2.25 2.25 0 0 0 2.25 2.25Zm.75-12h9v9h-9v-9Z"})])}function bt(t,e){return i(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.25 7.756a4.5 4.5 0 1 0 0 8.488M7.5 10.5h5.25m-5.25 3h5.25M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"})])}function At(t,e){return i(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25M9 16.5v.75m3-3v3M15 12v5.25m-4.5-15H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"})])}function ye(t,e){return i(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"})])}function Ct(t,e){return i(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"})])}function xe(t,e){return i(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.25 12.75V12A2.25 2.25 0 0 1 4.5 9.75h15A2.25 2.25 0 0 1 21.75 12v.75m-8.69-6.44-2.12-2.12a1.5 1.5 0 0 0-1.061-.44H4.5A2.25 2.25 0 0 0 2.25 6v12a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18V9a2.25 2.25 0 0 0-2.25-2.25h-5.379a1.5 1.5 0 0 1-1.06-.44Z"})])}function Pt(t,e){return i(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 21a9.004 9.004 0 0 0 8.716-6.747M12 21a9.004 9.004 0 0 1-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 0 1 7.843 4.582M12 3a8.997 8.997 0 0 0-7.843 4.582m15.686 0A11.953 11.953 0 0 1 12 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0 1 21 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0 1 12 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 0 1 3 12c0-1.605.42-3.113 1.157-4.418"})])}function $t(t,e){return i(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15 9h3.75M15 12h3.75M15 15h3.75M4.5 19.5h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Zm6-10.125a1.875 1.875 0 1 1-3.75 0 1.875 1.875 0 0 1 3.75 0Zm1.294 6.336a6.721 6.721 0 0 1-3.17.789 6.721 6.721 0 0 1-3.168-.789 3.376 3.376 0 0 1 6.338 0Z"})])}function Et(t,e){return i(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"})])}function Mt(t,e){return i(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 7.5h1.5m-1.5 3h1.5m-7.5 3h7.5m-7.5 3h7.5m3-9h3.375c.621 0 1.125.504 1.125 1.125V18a2.25 2.25 0 0 1-2.25 2.25M16.5 7.5V18a2.25 2.25 0 0 0 2.25 2.25M16.5 7.5V4.875c0-.621-.504-1.125-1.125-1.125H4.125C3.504 3.75 3 4.254 3 4.875V18a2.25 2.25 0 0 0 2.25 2.25h13.5M6 7.5h3v3H6v-3Z"})])}function qt(t,e){return i(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3.75 3v11.25A2.25 2.25 0 0 0 6 16.5h2.25M3.75 3h-1.5m1.5 0h16.5m0 0h1.5m-1.5 0v11.25A2.25 2.25 0 0 1 18 16.5h-2.25m-7.5 0h7.5m-7.5 0-1 3m8.5-3 1 3m0 0 .5 1.5m-.5-1.5h-9.5m0 0-.5 1.5m.75-9 3-3 2.148 2.148A12.061 12.061 0 0 1 16.5 7.605"})])}function It(t,e){return i(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9.879 7.519c1.171-1.025 3.071-1.025 4.242 0 1.172 1.025 1.172 2.687 0 3.712-.203.179-.43.326-.67.442-.745.361-1.45.999-1.45 1.827v.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 5.25h.008v.008H12v-.008Z"})])}function jt(t,e){return i(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.25 7.125C2.25 6.504 2.754 6 3.375 6h6c.621 0 1.125.504 1.125 1.125v3.75c0 .621-.504 1.125-1.125 1.125h-6a1.125 1.125 0 0 1-1.125-1.125v-3.75ZM14.25 8.625c0-.621.504-1.125 1.125-1.125h5.25c.621 0 1.125.504 1.125 1.125v8.25c0 .621-.504 1.125-1.125 1.125h-5.25a1.125 1.125 0 0 1-1.125-1.125v-8.25ZM3.75 16.125c0-.621.504-1.125 1.125-1.125h5.25c.621 0 1.125.504 1.125 1.125v2.25c0 .621-.504 1.125-1.125 1.125h-5.25a1.125 1.125 0 0 1-1.125-1.125v-2.25Z"})])}function ke(t,e){return i(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"})])}function Tt(t,e){return i(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09ZM18.259 8.715 18 9.75l-.259-1.035a3.375 3.375 0 0 0-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 0 0 2.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 0 0 2.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 0 0-2.456 2.456ZM16.894 20.567 16.5 21.75l-.394-1.183a2.25 2.25 0 0 0-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 0 0 1.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 0 0 1.423 1.423l1.183.394-1.183.394a2.25 2.25 0 0 0-1.423 1.423Z"})])}function Vt(t,e){return i(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3.75 6A2.25 2.25 0 0 1 6 3.75h2.25A2.25 2.25 0 0 1 10.5 6v2.25a2.25 2.25 0 0 1-2.25 2.25H6a2.25 2.25 0 0 1-2.25-2.25V6ZM3.75 15.75A2.25 2.25 0 0 1 6 13.5h2.25a2.25 2.25 0 0 1 2.25 2.25V18a2.25 2.25 0 0 1-2.25 2.25H6A2.25 2.25 0 0 1 3.75 18v-2.25ZM13.5 6a2.25 2.25 0 0 1 2.25-2.25H18A2.25 2.25 0 0 1 20.25 6v2.25A2.25 2.25 0 0 1 18 10.5h-2.25a2.25 2.25 0 0 1-2.25-2.25V6ZM13.5 15.75a2.25 2.25 0 0 1 2.25-2.25H18a2.25 2.25 0 0 1 2.25 2.25V18A2.25 2.25 0 0 1 18 20.25h-2.25A2.25 2.25 0 0 1 13.5 18v-2.25Z"})])}function be(t,e){return i(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M13.5 16.875h3.375m0 0h3.375m-3.375 0V13.5m0 3.375v3.375M6 10.5h2.25a2.25 2.25 0 0 0 2.25-2.25V6a2.25 2.25 0 0 0-2.25-2.25H6A2.25 2.25 0 0 0 3.75 6v2.25A2.25 2.25 0 0 0 6 10.5Zm0 9.75h2.25A2.25 2.25 0 0 0 10.5 18v-2.25a2.25 2.25 0 0 0-2.25-2.25H6a2.25 2.25 0 0 0-2.25 2.25V18A2.25 2.25 0 0 0 6 20.25Zm9.75-9.75H18a2.25 2.25 0 0 0 2.25-2.25V6A2.25 2.25 0 0 0 18 3.75h-2.25A2.25 2.25 0 0 0 13.5 6v2.25a2.25 2.25 0 0 0 2.25 2.25Z"})])}function Dt(t,e){return i(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3.375 19.5h17.25m-17.25 0a1.125 1.125 0 0 1-1.125-1.125M3.375 19.5h7.5c.621 0 1.125-.504 1.125-1.125m-9.75 0V5.625m0 12.75v-1.5c0-.621.504-1.125 1.125-1.125m18.375 2.625V5.625m0 12.75c0 .621-.504 1.125-1.125 1.125m1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125m0 3.75h-7.5A1.125 1.125 0 0 1 12 18.375m9.75-12.75c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125m19.5 0v1.5c0 .621-.504 1.125-1.125 1.125M2.25 5.625v1.5c0 .621.504 1.125 1.125 1.125m0 0h17.25m-17.25 0h7.5c.621 0 1.125.504 1.125 1.125M3.375 8.25c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125m17.25-3.75h-7.5c-.621 0-1.125.504-1.125 1.125m8.625-1.125c.621 0 1.125.504 1.125 1.125v1.5c0 .621-.504 1.125-1.125 1.125m-17.25 0h7.5m-7.5 0c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125M12 10.875v-1.5m0 1.5c0 .621-.504 1.125-1.125 1.125M12 10.875c0 .621.504 1.125 1.125 1.125m-2.25 0c.621 0 1.125.504 1.125 1.125M13.125 12h7.5m-7.5 0c-.621 0-1.125.504-1.125 1.125M20.625 12c.621 0 1.125.504 1.125 1.125v1.5c0 .621-.504 1.125-1.125 1.125m-17.25 0h7.5M12 14.625v-1.5m0 1.5c0 .621-.504 1.125-1.125 1.125M12 14.625c0 .621.504 1.125 1.125 1.125m-2.25 0c.621 0 1.125.504 1.125 1.125m0 1.5v-1.5m0 0c0-.621.504-1.125 1.125-1.125m0 0h7.5"})])}function Ae(t,e){return i(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M17.982 18.725A7.488 7.488 0 0 0 12 15.75a7.488 7.488 0 0 0-5.982 2.975m11.963 0a9 9 0 1 0-11.963 0m11.963 0A8.966 8.966 0 0 1 12 21a8.966 8.966 0 0 1-5.982-2.275M15 9.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"})])}function se(t,e){return i(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Z"})])}function Rt(t,e){return i(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M18 7.5v3m0 0v3m0-3h3m-3 0h-3m-2.25-4.125a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0ZM3 19.235v-.11a6.375 6.375 0 0 1 12.75 0v.109A12.318 12.318 0 0 1 9.374 21c-2.331 0-4.512-.645-6.374-1.766Z"})])}function Lt(t,e){return i(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"})])}function St(t,e){return i(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"})])}function Ot(t,e){return i(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 4.5v15m6-15v15m-10.875 0h15.75c.621 0 1.125-.504 1.125-1.125V5.625c0-.621-.504-1.125-1.125-1.125H4.125C3.504 4.5 3 5.004 3 5.625v12.75c0 .621.504 1.125 1.125 1.125Z"})])}function zt(t,e){return i(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M11.42 15.17 17.25 21A2.652 2.652 0 0 0 21 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 1 1-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 0 0 4.486-6.336l-3.276 3.277a3.004 3.004 0 0 1-2.25-2.25l3.276-3.276a4.5 4.5 0 0 0-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437 1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008Z"})])}const K={__name:"SidebarIcon",props:{icon:{type:String,required:!0},className:{type:String,default:"w-6 h-6"}},setup(t){const e=t,r={dashboard:be,projects:xe,users:St,timesheet:yt,sales:ut,"business-intelligence":we,funding:bt,admin:ke,settings:kt,communications:vt,certifications:re,"chat-bubble-left":ft,envelope:Ct,"calendar-days":ge,newspaper:Mt,clients:gt,contact:Ae,proposal:ye,contract:_e,invoice:mt,reports:At,analytics:ve,clock:ae,"calendar-plus":ge,"chart-bar":ve,archive:lt,"user-group":se,"chart-line":qt,directory:pe,orgchart:fe,skills:re,allocation:jt,"user-profile":Lt,team:se,departments:he,"case-study":ht,technical:zt,market:Pt,robot:pt,search:Et,reporting:wt,history:ae,"check-circle":_t,"users-check":se,"clock-play":ae,"user-management":Rt,products:xe,"academic-cap":re,"building-office-2":he,"cpu-chip":we,sparkles:Tt,"squares-plus":be,"table-cells":Dt,"document-text":ye,"view-columns":Ot,identification:$t,"chart-pie":fe,"squares-2x2":Vt,"arrows-right-left":dt,"shield-check":ke,"book-open":pe,"clipboard-document-check":_e,"user-circle":Ae,"cog-6-tooth":xt},s=x(()=>r[e.icon]||It);return(n,o)=>(i(),D(ie(s.value),{class:V(t.className)},null,8,["class"]))}},Nt={key:0,class:"truncate"},Bt={key:0,class:"truncate"},Ce={__name:"SidebarNavItem",props:{item:{type:Object,required:!0},isCollapsed:{type:Boolean,default:!1}},emits:["click"],setup(t){const e=x(()=>["text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-primary-600 dark:hover:text-primary-400"]);return(r,s)=>{const n=O("router-link");return i(),l("div",null,[t.item.path!=="#"?(i(),D(n,{key:0,to:t.item.path,class:V(["group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150",[e.value,{"justify-center":t.isCollapsed}]]),"active-class":"text-primary-600 bg-primary-50 border-r-2 border-primary-600",onClick:s[0]||(s[0]=o=>r.$emit("click"))},{default:E(()=>[w(K,{icon:t.item.icon,class:V(["flex-shrink-0 h-6 w-6",{"mr-0":t.isCollapsed,"mr-3":!t.isCollapsed}])},null,8,["icon","class"]),t.isCollapsed?C("",!0):(i(),l("span",Nt,P(t.item.name),1))]),_:1},8,["to","class"])):(i(),l("div",{key:1,class:V(["group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150 cursor-not-allowed opacity-75",["text-gray-400 hover:text-gray-500",{"justify-center":t.isCollapsed}]])},[w(K,{icon:t.item.icon,class:V(["flex-shrink-0 h-6 w-6",{"mr-0":t.isCollapsed,"mr-3":!t.isCollapsed}])},null,8,["icon","class"]),t.isCollapsed?C("",!0):(i(),l("span",Bt,P(t.item.name),1))],2))])}}},Ht={key:0,class:"flex-1 text-left truncate"},Zt={key:0,class:"ml-6 space-y-1 mt-1"},Ut={class:"truncate"},z={__name:"SidebarNavItemCollapsible",props:{item:{type:Object,required:!0},isCollapsed:{type:Boolean,default:!1}},emits:["click"],setup(t){const e=t,r=Y(),s=H(),n=I(!1),o=x(()=>["text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-primary-600 dark:hover:text-primary-400",{"text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900":m.value}]),m=x(()=>e.item.children?e.item.children.some(p=>p.path!=="#"&&r.path.startsWith(p.path)):!1),f=x(()=>e.item.children?e.item.children.filter(p=>{var c;return p.admin?((c=s.user)==null?void 0:c.role)==="admin":!0}):[]);m.value&&(n.value=!0);function v(){e.isCollapsed||(n.value=!n.value)}function _(p){if(p.path==="#")return!1}return(p,c)=>{const u=O("router-link");return i(),l("div",null,[a("button",{onClick:v,class:V(["group w-full flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150",[o.value,{"justify-center":t.isCollapsed}]])},[w(K,{icon:t.item.icon,class:V(["flex-shrink-0 h-6 w-6",{"mr-0":t.isCollapsed,"mr-3":!t.isCollapsed}])},null,8,["icon","class"]),t.isCollapsed?C("",!0):(i(),l("span",Ht,P(t.item.name),1)),t.isCollapsed?C("",!0):(i(),D(j,{key:1,name:"chevron-right",size:"sm",className:{"rotate-90":n.value,"ml-2 transition-transform duration-150":!0}},null,8,["className"]))],2),n.value&&!t.isCollapsed?(i(),l("div",Zt,[(i(!0),l(B,null,U(f.value,h=>(i(),D(u,{key:h.name,to:h.path,class:V(["group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150",h.path==="#"?"text-gray-400 dark:text-gray-500 hover:text-gray-500 dark:hover:text-gray-400 cursor-not-allowed opacity-75":"text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-primary-600 dark:hover:text-primary-400"]),"active-class":"text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900",onClick:k=>_(h)},{default:E(()=>[h.icon?(i(),D(K,{key:0,icon:h.icon,class:"flex-shrink-0 h-4 w-4 mr-2"},null,8,["icon"])):C("",!0),a("span",Ut,P(h.name),1)]),_:2},1032,["to","class","onClick"]))),128))])):C("",!0)])}}},Ft={class:"mt-5 flex-grow flex flex-col overflow-hidden"},Gt={class:"flex-1 px-2 pb-4 space-y-1 overflow-y-auto sidebar-scroll"},Wt={__name:"SidebarNavigation",props:{isCollapsed:{type:Boolean,default:!1}},emits:["item-click"],setup(t){const{hasPermission:e}=ct(),r=x(()=>e.value("view_dashboard")),s=x(()=>e.value("view_personnel"));x(()=>e.value("view_all_projects"));const n=x(()=>e.value("manage_timesheets")||e.value("view_all_projects")),o=x(()=>e.value("view_all_projects")),m=x(()=>e.value("view_products")||e.value("view_all_projects")),f=x(()=>e.value("view_communications")),v=x(()=>e.value("view_compliance")),_=x(()=>e.value("view_ceo")),p=x(()=>e.value("view_agents")),c=x(()=>e.value("view_funding")||e.value("view_reports")),u=x(()=>e.value("admin_access")),h=x(()=>e.value("view_dashboard"));return(k,g)=>(i(),l("div",Ft,[a("nav",Gt,[r.value?(i(),D(Ce,{key:0,item:{name:"Dashboard",path:"/app/dashboard",icon:"dashboard"},"is-collapsed":t.isCollapsed,onClick:g[0]||(g[0]=A=>k.$emit("item-click"))},null,8,["is-collapsed"])):C("",!0),s.value?(i(),D(z,{key:1,item:{name:"Personale",icon:"users",children:[{name:"Gestione",path:"/app/personnel/admin",icon:"admin",admin:!0},{name:"Inquadramenti",path:"/app/personnel/job-levels",icon:"user-group"},{name:"Performance",path:"/app/personnel/performance",icon:"chart-line"}]},"is-collapsed":t.isCollapsed,onClick:g[1]||(g[1]=A=>k.$emit("item-click"))},null,8,["is-collapsed"])):C("",!0),n.value?(i(),D(z,{key:2,item:{name:"Attività",icon:"timesheet",children:[{name:"Dashboard",path:"/app/timesheet/dashboard",icon:"dashboard"},{name:"Le Mie Ore",path:"/app/timesheet/entry",icon:"clock"},{name:"Progetti",path:"/app/projects",icon:"projects"},{name:"Reportistica",path:"/app/timesheet/analytics",icon:"chart-line",admin:!0},{name:"Richieste",path:"/app/timesheet/requests",icon:"calendar-plus"}]},"is-collapsed":t.isCollapsed,onClick:g[2]||(g[2]=A=>k.$emit("item-click"))},null,8,["is-collapsed"])):C("",!0),o.value?(i(),D(z,{key:3,item:{name:"Sales",icon:"sales",children:[{name:"Dashboard",path:"/app/crm/dashboard",icon:"dashboard"},{name:"Clienti",path:"/app/crm/clients",icon:"clients"},{name:"Contatti",path:"/app/crm/contacts",icon:"contact"},{name:"Contratti",path:"/app/crm/contracts",icon:"contract"},{name:"Pre-Fatture",path:"/app/invoicing/pre-invoices",icon:"invoice"},{name:"Proposte",path:"/app/crm/proposals",icon:"proposal"}]},"is-collapsed":t.isCollapsed,onClick:g[3]||(g[3]=A=>k.$emit("item-click"))},null,8,["is-collapsed"])):C("",!0),m.value?(i(),D(z,{key:4,item:{name:"Business Intelligence",icon:"business-intelligence",children:[{name:"Dashboard",path:"/app/business-intelligence/dashboard",icon:"dashboard"},{name:"Case Studies",path:"/app/business-intelligence/case-studies",icon:"case-study"},{name:"Competenze Core",path:"/app/business-intelligence/core-skills",icon:"skills"},{name:"Market Intelligence",path:"/app/business-intelligence/market-intel",icon:"market"},{name:"Offerta Tecnica",path:"/app/business-intelligence/technical-offer",icon:"technical"},{name:"Reportistica",path:"/app/business-intelligence/advanced-reports",icon:"chart-bar"}]},"is-collapsed":t.isCollapsed,onClick:g[4]||(g[4]=A=>k.$emit("item-click"))},null,8,["is-collapsed"])):C("",!0),c.value?(i(),D(z,{key:5,item:{name:"Bandi",icon:"funding",children:[{name:"Dashboard",path:"/app/funding/dashboard",icon:"dashboard"},{name:"Ricerca",path:"/app/funding/search",icon:"search"},{name:"Rendicontazione",path:"/app/funding/reporting",icon:"reporting"}]},"is-collapsed":t.isCollapsed,onClick:g[5]||(g[5]=A=>k.$emit("item-click"))},null,8,["is-collapsed"])):C("",!0),f.value?(i(),D(z,{key:6,item:{name:"Comunicazioni",icon:"communications",children:[{name:"Dashboard",path:"/app/communications/dashboard",icon:"dashboard"},{name:"Forum",path:"/app/communications/forum",icon:"chat-bubble-left"},{name:"Sondaggi",path:"/app/communications/polls",icon:"chart-bar"},{name:"Messaggi",path:"/app/communications/messages",icon:"envelope"},{name:"Eventi",path:"/app/communications/events",icon:"calendar-days"},{name:"News",path:"/app/communications/news",icon:"newspaper"},{name:"Assistente HR",path:"/app/communications/hr-assistant",icon:"chat-bubble-left-right"},{name:"Knowledge Base HR",path:"/app/communications/hr-knowledge-base",icon:"academic-cap",admin:!0}]},"is-collapsed":t.isCollapsed,onClick:g[6]||(g[6]=A=>k.$emit("item-click"))},null,8,["is-collapsed"])):C("",!0),v.value?(i(),D(z,{key:7,item:{name:"Certificazioni",icon:"certifications",children:[{name:"Dashboard",path:"/app/certifications/dashboard",icon:"dashboard"},{name:"Gestione",path:"/app/certifications/list",icon:"shield-check"},{name:"Catalogo Standard",path:"/app/certifications/catalog",icon:"book-open"},{name:"Valuta Readiness",path:"/app/certifications/readiness",icon:"clipboard-document-check"}]},"is-collapsed":t.isCollapsed,onClick:g[7]||(g[7]=A=>k.$emit("item-click"))},null,8,["is-collapsed"])):C("",!0),_.value?(i(),D(z,{key:8,item:{name:"Human CEO",icon:"user-circle",children:[{name:"Strategic Dashboard",path:"/app/ceo/dashboard",icon:"chart-bar"},{name:"AI Assistant",path:"/app/ceo/assistant",icon:"cpu-chip"},{name:"Insights & Reports",path:"/app/ceo/insights",icon:"document-text"},{name:"Research Config",path:"/app/ceo/config",icon:"cog-6-tooth"}]},"is-collapsed":t.isCollapsed,onClick:g[8]||(g[8]=A=>k.$emit("item-click"))},null,8,["is-collapsed"])):C("",!0),p.value?(i(),D(Ce,{key:9,item:{name:"Agenti",path:"#",icon:"cpu-chip"},"is-collapsed":t.isCollapsed,onClick:g[9]||(g[9]=A=>k.$emit("item-click"))},null,8,["is-collapsed"])):C("",!0),u.value?(i(),D(z,{key:10,item:{name:"Amministrazione",icon:"settings",children:[{name:"Configurazioni",path:"/app/admin/settings",icon:"settings"},{name:"Gestione Utenti",path:"/app/admin/users",icon:"user-management"},{name:"Template KPI",path:"/app/admin/kpi-templates",icon:"reports"}]},"is-collapsed":t.isCollapsed,onClick:g[10]||(g[10]=A=>k.$emit("item-click"))},null,8,["is-collapsed"])):C("",!0),h.value?(i(),D(z,{key:11,item:{name:"Design System",icon:"sparkles",children:[{name:"Dashboard Template",path:"/app/examples/dashboard",icon:"chart-pie"},{name:"Timesheet Grid",path:"/app/examples/timesheet-grid",icon:"table-cells"},{name:"Componenti Base",path:"/app/examples/components",icon:"squares-2x2"},{name:"FormBuilder",path:"/app/examples/form-builder",icon:"document-text"},{name:"ViewModeToggle",path:"/app/examples/view-mode-toggle",icon:"squares-plus"},{name:"KanbanView",path:"/app/examples/kanban",icon:"view-columns"},{name:"ProposalCard",path:"/app/examples/proposal-card",icon:"identification"},{name:"WizardContainer",path:"/app/examples/wizard-container",icon:"arrows-right-left"}]},"is-collapsed":t.isCollapsed,onClick:g[11]||(g[11]=A=>k.$emit("item-click"))},null,8,["is-collapsed"])):C("",!0)])]))}},Pe=ee(Wt,[["__scopeId","data-v-dcf11a14"]]),Kt={class:"flex-shrink-0 border-t border-gray-200 dark:border-gray-700 p-4"},Qt={class:"flex-shrink-0"},Jt={class:"h-9 w-9 rounded-full bg-gray-200 dark:bg-gray-600 flex items-center justify-center overflow-hidden"},Yt=["src"],Xt={key:0,class:"ml-3 flex-1 min-w-0"},er={class:"text-sm font-medium text-gray-900 dark:text-white truncate"},tr={class:"text-xs text-gray-500 dark:text-gray-400 truncate"},rr={class:"py-1"},ar={key:0,class:"mt-3 text-xs text-gray-400 text-center"},$e={__name:"SidebarFooter",props:{isCollapsed:{type:Boolean,default:!1}},setup(t){const e=G(),r=H(),s=I(!1),n=I(!1),o=x(()=>{const c=r.user;return c?c.first_name&&c.last_name?`${c.first_name} ${c.last_name}`:c.username||c.email||"Utente":"Utente"}),m=x(()=>{const c=r.user;return n.value?null:c!=null&&c.avatar_url&&c.avatar_url!=="/img/default-avatar.png"?c.avatar_url:null}),f=x(()=>r.user?{admin:"Amministratore",manager:"Manager",employee:"Dipendente",client:"Cliente"}[r.user.role]||r.user.role:""),v=x(()=>"1.0.0");function _(){n.value=!0}async function p(){s.value=!1,await r.logout(),e.push("/auth/login")}return(c,u)=>{const h=O("router-link");return i(),l("div",Kt,[a("div",{class:V(["flex items-center",{"justify-center":t.isCollapsed}])},[a("div",Qt,[a("div",Jt,[m.value&&m.value!=="/img/default-avatar.png"?(i(),l("img",{key:0,src:m.value,class:"h-full w-full rounded-full object-cover",alt:"User avatar",onError:_},null,40,Yt)):(i(),D(j,{key:1,name:"user-circle",size:"lg",class:"text-gray-400 dark:text-gray-500"}))])]),t.isCollapsed?C("",!0):(i(),l("div",Xt,[a("p",er,P(o.value),1),a("p",tr,P(f.value),1)])),a("div",{class:V(["relative",{"ml-3":!t.isCollapsed}])},[a("button",{onClick:u[0]||(u[0]=k=>s.value=!s.value),class:"p-1 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:text-gray-500 dark:hover:text-gray-300 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-primary-500"},[w(j,{name:"ellipsis-vertical",size:"sm"})]),s.value?(i(),l("div",{key:0,onClick:u[3]||(u[3]=k=>s.value=!1),class:"origin-bottom-left fixed bottom-16 left-4 w-48 rounded-md shadow-xl bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 border border-gray-200 dark:border-gray-600",style:{"z-index":"99999"}},[a("div",rr,[w(h,{to:"/app/profile",class:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:u[1]||(u[1]=k=>s.value=!1)},{default:E(()=>u[4]||(u[4]=[M(" Il tuo profilo ")])),_:1,__:[4]}),w(h,{to:"/app/settings",class:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:u[2]||(u[2]=k=>s.value=!1)},{default:E(()=>u[5]||(u[5]=[M(" Impostazioni ")])),_:1,__:[5]}),u[6]||(u[6]=a("hr",{class:"my-1 border-gray-200 dark:border-gray-600"},null,-1)),a("button",{onClick:p,class:"block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"}," Esci ")])])):C("",!0)],2)],2),v.value&&!t.isCollapsed?(i(),l("div",ar," v"+P(v.value),1)):C("",!0)])}}},sr={class:"flex"},nr={class:"hidden lg:flex lg:flex-shrink-0 lg:fixed lg:inset-y-0 z-10"},or={class:"flex flex-col h-full bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 shadow-sm"},ir={class:"flex items-center flex-shrink-0 px-4 pt-5"},cr={class:"w-10 h-10 bg-primary-600 rounded flex items-center justify-center mr-3"},lr={class:"text-white font-bold text-lg"},ur={class:"text-xl font-semibold text-gray-900 dark:text-white"},dr={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center"},mr={class:"text-white font-bold text-sm"},pr={class:"flex flex-col h-full bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 shadow-sm"},hr={class:"flex items-center justify-between px-4 pt-5 mb-4 flex-shrink-0"},gr={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center mr-3"},vr={class:"text-white font-bold text-sm"},fr={class:"text-xl font-semibold text-gray-900 dark:text-white"},_r={__name:"AppSidebar",props:{isMobileOpen:{type:Boolean,default:!1}},emits:["close","toggle-collapsed"],setup(t,{emit:e}){const r=e,s=X(),n=I(!1),o=x(()=>s.config||{}),m=x(()=>{var p;return((p=o.value.company)==null?void 0:p.name)||"DatPortal"}),f=x(()=>m.value.split(" ").map(c=>c[0]).join("").toUpperCase().slice(0,2));function v(){n.value=!n.value,r("toggle-collapsed",n.value)}function _(){n.value&&(n.value=!1)}return(p,c)=>{const u=O("router-link");return i(),l("div",sr,[a("div",nr,[a("div",{class:V(["flex flex-col transition-all duration-300",[n.value?"w-20":"w-64"]])},[a("div",or,[a("div",ir,[a("div",{class:V(["flex items-center",{"justify-center":n.value}])},[w(u,{to:"/app/dashboard",class:V(["flex items-center",{hidden:n.value}])},{default:E(()=>[a("div",cr,[a("span",lr,P(f.value),1)]),a("h3",ur,P(m.value),1)]),_:1},8,["class"]),w(u,{to:"/app/dashboard",class:V(["flex items-center justify-center",{hidden:!n.value}])},{default:E(()=>[a("div",dr,[a("span",mr,P(f.value),1)])]),_:1},8,["class"])],2),a("button",{onClick:v,class:"ml-auto text-gray-600 dark:text-gray-400 focus:outline-none hover:bg-gray-100 dark:hover:bg-gray-700 p-1 rounded"},[n.value?(i(),D(j,{key:1,name:"chevron-double-right",size:"sm"})):(i(),D(j,{key:0,name:"chevron-double-left",size:"sm"}))])]),w(Pe,{"is-collapsed":n.value,onItemClick:_},null,8,["is-collapsed"]),w($e,{"is-collapsed":n.value},null,8,["is-collapsed"])])],2)]),a("div",{class:V(["fixed inset-y-0 left-0 z-30 w-64 bg-primary-700 transform transition-transform duration-300 ease-in-out lg:hidden",t.isMobileOpen?"translate-x-0":"-translate-x-full"])},[a("div",pr,[a("div",hr,[w(u,{to:"/app/dashboard",class:"flex items-center"},{default:E(()=>[a("div",gr,[a("span",vr,P(f.value),1)]),a("h3",fr,P(m.value),1)]),_:1}),a("button",{onClick:c[0]||(c[0]=h=>p.$emit("close")),class:"p-2 rounded-md text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700"},[w(j,{name:"x-mark",size:"md"})])]),w(Pe,{"is-collapsed":!1,onItemClick:c[1]||(c[1]=h=>p.$emit("close"))}),w($e,{"is-collapsed":!1})])],2)])}}},wr={class:"flex","aria-label":"Breadcrumb"},yr={class:"flex items-center space-x-2 text-sm text-gray-500"},xr={key:0,class:"mr-2"},kr={class:"flex items-center"},br={key:2,class:"font-medium text-gray-900"},Ar={__name:"HeaderBreadcrumbs",props:{breadcrumbs:{type:Array,required:!0}},setup(t){return(e,r)=>{const s=O("router-link");return i(),l("nav",wr,[a("ol",yr,[(i(!0),l(B,null,U(t.breadcrumbs,(n,o)=>(i(),l("li",{key:o,class:"flex items-center"},[o>0?(i(),l("div",xr,[w(j,{name:"chevron-right",size:"xs",color:"text-gray-400"})])):C("",!0),n.to&&o<t.breadcrumbs.length-1?(i(),D(s,{key:1,to:n.to,class:"hover:text-gray-700 transition-colors duration-150"},{default:E(()=>[a("span",kr,[n.icon?(i(),D(j,{key:0,name:n.icon,size:"xs",className:"mr-1"},null,8,["name"])):C("",!0),M(" "+P(n.label),1)])]),_:2},1032,["to"])):(i(),l("span",br,P(n.label),1))]))),128))])])}}},Cr={class:"flex items-center space-x-2"},Pr=["title"],$r={__name:"HeaderQuickActions",emits:["quick-create-project"],setup(t){const e=Y(),{isDarkMode:r,toggleDarkMode:s}=ce(),n=x(()=>{var o;return((o=e.name)==null?void 0:o.includes("projects"))||e.path.includes("/projects")});return(o,m)=>(i(),l("div",Cr,[n.value?(i(),l("button",{key:0,onClick:m[0]||(m[0]=f=>o.$emit("quick-create-project")),class:"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[w(j,{name:"add",size:"xs",className:"mr-1"}),m[2]||(m[2]=M(" Nuovo Progetto "))])):C("",!0),a("button",{onClick:m[1]||(m[1]=(...f)=>T(s)&&T(s)(...f)),class:"inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700",title:T(r)?"Modalità chiara":"Modalità scura"},[w(j,{name:T(r)?"sun":"moon",size:"xs"},null,8,["name"])],8,Pr)]))}},Er=J("notifications",{state:()=>({notifications:[],unreadCount:0,loading:!1,error:null}),getters:{unreadNotifications:t=>t.notifications.filter(e=>!e.is_read),hasUnreadNotifications:t=>t.unreadCount>0},actions:{async fetchNotifications(t=!1){this.loading=!0,this.error=null;try{const e=new URLSearchParams;t&&e.append("unread_only","true"),e.append("limit","50");const r=await y.get(`/notifications?${e.toString()}`);r.data&&(this.notifications=r.data.notifications||[],this.unreadCount=r.data.unread_count||0)}catch(e){this.error=e.message||"Errore nel caricamento delle notifiche",console.error("Error fetching notifications:",e)}finally{this.loading=!1}},async fetchUnreadCount(){try{const t=await y.get("/notifications/unread-count");t.data&&(this.unreadCount=t.data.unread_count||0)}catch(t){console.error("Error fetching unread count:",t)}},async markAsRead(t){try{await y.put(`/notifications/${t}/read`);const e=this.notifications.find(r=>r.id===t);e&&!e.is_read&&(e.is_read=!0,this.unreadCount=Math.max(0,this.unreadCount-1))}catch(e){throw console.error("Error marking notification as read:",e),e}},async markAllAsRead(){try{await y.put("/notifications/mark-all-read"),this.notifications.forEach(t=>{t.is_read=!0}),this.unreadCount=0}catch(t){throw console.error("Error marking all notifications as read:",t),t}},async createNotification(t,e,r,s="info",n=null){try{const o={user_id:t,title:e,message:r,type:s,link:n};return(await y.post("/notifications",o)).data}catch(o){throw console.error("Error creating notification:",o),o}},async pollNotifications(){this.loading||await this.fetchUnreadCount()},reset(){this.notifications=[],this.unreadCount=0,this.loading=!1,this.error=null},addNotification(t){this.notifications.unshift(t),t.is_read||this.unreadCount++}}}),Mr=J("communication",{state:()=>({topics:[],currentTopic:null,categories:[],polls:[],currentPoll:null,userVotes:{},messages:[],conversations:[],events:[],userRegistrations:{},news:[],reactions:{},comments:[],searchResults:[],loading:{topics:!1,polls:!1,messages:!1,conversations:!1,events:!1,news:!1,reactions:!1,comments:!1,search:!1},unreadCounts:{messages:0,news:0},errors:{topics:null,polls:null,messages:null,conversations:null,events:null,news:null,reactions:null,comments:null,search:null},pagination:{topics:{page:1,totalPages:1,totalItems:0,hasMore:!1},polls:{page:1,totalPages:1,totalItems:0,hasMore:!1},messages:{page:1,totalPages:1,totalItems:0,hasMore:!1},conversations:{page:1,totalPages:1,totalItems:0,hasMore:!1},events:{page:1,totalPages:1,totalItems:0,hasMore:!1},news:{page:1,totalPages:1,totalItems:0,hasMore:!1},comments:{page:1,totalPages:1,totalItems:0,hasMore:!1},search:{page:1,totalPages:1,totalItems:0,hasMore:!1}},stats:{}}),getters:{getTopicsByCategory:t=>e=>(t.topics||[]).filter(r=>r.category===e),getPinnedTopics:t=>(t.topics||[]).filter(e=>e.is_pinned),getActivePolls:t=>Array.isArray(t.polls)?t.polls.filter(e=>e.is_expired!==void 0?e.is_active&&!e.is_expired:e.is_active?e.expires_at?new Date(e.expires_at)>new Date:!0:!1):[],getUserVoteForPoll:t=>e=>t.userVotes&&t.userVotes[e]||null,getUnreadMessages:t=>(t.messages||[]).filter(e=>!e.is_read),getUpcomingEvents:t=>{const e=new Date;return(t.events||[]).filter(r=>new Date(r.start_time||r.event_time)>e).sort((r,s)=>new Date(r.start_time||r.event_time)-new Date(s.start_time||s.event_time))},getUserEventRegistration:t=>e=>t.userRegistrations[e]||null,getConversationMessages:t=>e=>(t.messages||[]).filter(r=>r.sender_id===e||r.recipient_id===e).sort((r,s)=>new Date(r.sent_at)-new Date(s.sent_at)),getUnreadMessagesCount:t=>e=>(t.messages||[]).filter(r=>!r.is_read&&r.sender_id!==e).length,getUnreadNewsCount:t=>(t.news||[]).filter(e=>!e.is_read).length},actions:{async fetchForumTopics(t={}){var e,r,s,n;this.loading.topics=!0,this.errors.topics=null;try{const o=await y.get("/api/communication/forum/topics",{params:t});this.topics=((e=o.data.data)==null?void 0:e.topics)||[],this.pagination.topics=((r=o.data.data)==null?void 0:r.pagination)||{page:1,totalPages:1,totalItems:0,hasMore:!1}}catch(o){throw console.error("Failed to fetch forum topics:",o),this.errors.topics=((n=(s=o.response)==null?void 0:s.data)==null?void 0:n.message)||"Errore nel caricamento dei topic",this.topics=[],o}finally{this.loading.topics=!1}},async createForumTopic(t){try{const e=await y.post("/api/communication/forum/topics",t),r=e.data.data||e.data;return this.topics.unshift(r),r}catch(e){throw e}},async createTopic(t){return this.createForumTopic(t)},async updateTopic(t){try{const e=await y.put(`/api/communication/forum/topics/${t.id}`,t),r=e.data.data||e.data,s=this.topics.findIndex(n=>n.id===t.id);return s!==-1&&(this.topics[s]=r),r}catch(e){throw e}},async deleteTopic(t){try{await y.delete(`/api/communication/forum/topics/${t}`),this.topics=this.topics.filter(e=>e.id!==t)}catch(e){throw e}},async fetchTopic(t){try{const e=await y.get(`/api/communication/forum/topics/${t}`),r=e.data.data||e.data;return this.currentTopic=r,r}catch(e){throw e}},async incrementTopicViews(t){try{await y.post(`/api/communication/forum/topics/${t}/views`)}catch(e){console.warn("Errore nell'incremento delle visualizzazioni:",e)}},async toggleTopicPin(t){try{const e=await y.post(`/api/communication/forum/topics/${t}/pin`),r=e.data.data||e.data,s=this.topics.findIndex(n=>n.id===t);return s!==-1&&(this.topics[s].is_pinned=r.is_pinned),r}catch(e){throw e}},async toggleTopicLock(t){try{const e=await y.post(`/api/communication/forum/topics/${t}/lock`),r=e.data.data||e.data,s=this.topics.findIndex(n=>n.id===t);return s!==-1&&(this.topics[s].is_locked=r.is_locked),r}catch(e){throw e}},async fetchTopicComments(t){var e;try{const r=await y.get(`/api/communication/forum/topics/${t}/comments`);this.comments=((e=r.data.data)==null?void 0:e.comments)||[]}catch(r){throw r}},async createTopicComment(t,e){try{const r=await y.post(`/api/communication/forum/topics/${t}/comments`,e),s=r.data.data||r.data;return this.comments.push(s),s}catch(r){throw r}},async fetchPolls(t={}){var e,r,s,n;this.loading.polls=!0,this.errors.polls=null;try{const o=await y.get("/api/communication/polls",{params:t});this.polls=((e=o.data.data)==null?void 0:e.polls)||[],this.pagination.polls=((r=o.data.data)==null?void 0:r.pagination)||{page:1,totalPages:1,totalItems:0,hasMore:!1}}catch(o){throw console.error("Failed to fetch polls:",o),this.errors.polls=((n=(s=o.response)==null?void 0:s.data)==null?void 0:n.message)||"Errore nel caricamento dei sondaggi",this.polls=[],o}finally{this.loading.polls=!1}},async createPoll(t){try{const e=await y.post("/api/communication/polls",t),r=e.data.data||e.data;return this.polls.unshift(r),r}catch(e){throw e}},async updatePoll(t){try{const e=await y.put(`/api/communication/polls/${t.id}`,t),r=e.data.data||e.data,s=this.polls.findIndex(n=>n.id===t.id);return s!==-1&&(this.polls[s]=r),r}catch(e){throw e}},async deletePoll(t){try{await y.delete(`/api/communication/polls/${t}`),this.polls=this.polls.filter(e=>e.id!==t)}catch(e){throw e}},async fetchPoll(t){try{const e=await y.get(`/api/communication/polls/${t}`);return this.currentPoll=e.data.data||e.data,this.currentPoll}catch(e){throw e}},async fetchUserVoteForPoll(t){try{const e=await y.get(`/api/communication/polls/${t}/vote`),r=e.data.data||e.data;return!r||typeof r=="object"&&Object.keys(r).length===0?(this.userVotes[t]=null,null):(this.userVotes[t]=r,r)}catch{return this.userVotes[t]=null,null}},async votePoll(t,e){try{const r=await y.post(`/api/communication/polls/${t}/vote`,{option_id:e});this.userVotes[t]=e;const s=this.polls.findIndex(n=>n.id===t);return s!==-1&&(this.polls[s]=r.data.data||r.data),r.data.data||r.data}catch(r){throw r}},async submitPollVote(t){try{const{poll_id:e,option_id:r,option_ids:s}=t,n=s||(r?[r]:[]);if(!n.length)throw new Error("Nessuna opzione selezionata");const o=await y.post(`/api/communication/polls/${e}/vote`,{option_ids:n});return this.userVotes[e]={option_ids:n},await this.fetchPoll(e),o.data.data||o.data}catch(e){throw e}},async fetchMessages(t={}){var e,r,s,n;this.loading.messages=!0,this.errors.messages=null;try{const o=await y.get("/api/communication/messages",{params:t});this.messages=((e=o.data.data)==null?void 0:e.messages)||[],this.pagination.messages=((r=o.data.data)==null?void 0:r.pagination)||{page:1,totalPages:1,totalItems:0,hasMore:!1},this.unreadCounts.messages=(this.messages||[]).filter(m=>!m.is_read&&m.recipient_id&&m.recipient_id!==m.sender_id).length}catch(o){throw console.error("Failed to fetch messages:",o),this.errors.messages=((n=(s=o.response)==null?void 0:s.data)==null?void 0:n.message)||"Errore nel caricamento dei messaggi",this.messages=[],o}finally{this.loading.messages=!1}},async sendMessage(t){try{console.log("📤 Sending message:",t);const e=await y.post("/api/communication/messages",t);console.log("📤 Send response:",e),console.log("📤 Send response data:",e.data);const r=e.data.data||e.data;return console.log("📤 New message to store:",r),this.messages.unshift(r),console.log("📤 Messages after adding new:",this.messages.length),r}catch(e){throw console.error("❌ Error sending message:",e),e}},async markMessageAsRead(t){try{await y.put(`/api/communication/messages/${t}/read`);const e=this.messages.findIndex(r=>r.id===t);e!==-1&&(this.messages[e].is_read=!0,this.unreadCounts.messages=Math.max(0,this.unreadCounts.messages-1))}catch(e){throw e}},async deleteMessage(t){try{await y.delete(`/api/communication/messages/${t}`),this.messages=this.messages.filter(e=>e.id!==t)}catch(e){throw e}},async fetchConversations(t={}){var e,r,s,n;this.loading.conversations=!0,this.errors.conversations=null;try{const o=await y.get("/api/communication/messages",{params:t});this.conversations=((e=o.data.data)==null?void 0:e.messages)||[],this.pagination.conversations=((r=o.data.data)==null?void 0:r.pagination)||{page:1,totalPages:1,totalItems:0}}catch(o){throw this.errors.conversations=((n=(s=o.response)==null?void 0:s.data)==null?void 0:n.message)||"Errore nel caricamento delle conversazioni",this.conversations=[],o}finally{this.loading.conversations=!1}},async markConversationAsRead(t){try{await y.put(`/api/communication/messages/${t}/read`);const e=this.conversations.findIndex(r=>r.id===t);e!==-1&&(this.conversations[e].unread_count=0)}catch(e){throw e}},async fetchEvents(t={}){var e,r,s,n;this.loading.events=!0,this.errors.events=null;try{const o=await y.get("/api/communication/events",{params:t});this.events=((e=o.data.data)==null?void 0:e.events)||[],this.pagination.events=((r=o.data.data)==null?void 0:r.pagination)||{page:1,totalPages:1,totalItems:0,hasMore:!1}}catch(o){throw console.error("Failed to fetch events:",o),this.errors.events=((n=(s=o.response)==null?void 0:s.data)==null?void 0:n.message)||"Errore nel caricamento degli eventi",this.events=[],o}finally{this.loading.events=!1}},async fetchEvent(t){try{const e=await y.get(`/api/communication/events/${t}`),r=e.data.data||e.data,s=this.events.findIndex(n=>n.id==t);return s!==-1?this.events[s]=r:this.events.push(r),r}catch(e){throw console.error("Failed to fetch event:",e),e}},async registerForEvent(t){try{const e=await y.post(`/api/communication/events/${t}/register`);if(e.data.success){this.userRegistrations[t]=e.data.data;const r=this.events.findIndex(s=>s.id===t);return r!==-1&&(this.events[r].user_registered=!0,e.status===201&&this.events[r].participants_count!==void 0&&(this.events[r].participants_count+=1)),e.data.data}else throw new Error(e.data.message||"Errore nella registrazione")}catch(e){throw console.error("Error in registerForEvent store:",e),e}},async unregisterFromEvent(t){try{await y.delete(`/api/communication/events/${t}/register`),delete this.userRegistrations[t];const e=this.events.findIndex(r=>r.id===t);e!==-1&&(this.events[e].user_registered=!1)}catch(e){throw e}},async createEvent(t){try{const e=await y.post("/api/communication/events",t);return this.events.unshift(e.data),e.data}catch(e){throw e}},async updateEvent(t){try{const e=await y.put(`/api/communication/events/${t.id}`,t),r=this.events.findIndex(s=>s.id===t.id);return r!==-1&&(this.events[r]=e.data),e.data}catch(e){throw e}},async deleteEvent(t){try{await y.delete(`/api/communication/events/${t}`),this.events=this.events.filter(e=>e.id!==t)}catch(e){throw e}},async duplicateEvent(t){try{const e=await y.post(`/api/communication/events/${t}/duplicate`);return this.events.unshift(e.data.data||e.data),e.data.data||e.data}catch(e){throw e}},async fetchEventParticipants(t){try{const e=await y.get(`/api/communication/events/${t}/participants`);return e.data.data||e.data||[]}catch(e){throw console.error("Failed to fetch event participants:",e),e}},async fetchNews(t={}){var e,r,s,n;this.loading.news=!0,this.errors.news=null;try{const o=await y.get("/api/communication/company",{params:t});this.news=((e=o.data.data)==null?void 0:e.communications)||[],this.pagination.news=((r=o.data.data)==null?void 0:r.pagination)||{page:1,totalPages:1,totalItems:0,hasMore:!1},this.unreadCounts.news=(this.news||[]).filter(m=>!m.is_read).length}catch(o){throw this.errors.news=((n=(s=o.response)==null?void 0:s.data)==null?void 0:n.message)||"Errore nel caricamento delle news",this.news=[],o}finally{this.loading.news=!1}},async createNews(t){try{const e=await y.post("/api/communication/company",t),r=e.data.data||e.data;return this.news.unshift(r),r}catch(e){throw e}},async updateNews(t){try{const e=await y.put(`/api/communication/company/${t.id}`,t),r=e.data.data||e.data,s=this.news.findIndex(n=>n.id===t.id);return s!==-1&&(this.news[s]=r),r}catch(e){throw e}},async deleteNews(t){try{await y.delete(`/api/communication/company/${t}`),this.news=this.news.filter(e=>e.id!==t)}catch(e){throw e}},async markNewsAsRead(t){try{await y.post(`/api/communication/company/${t}/read`);const e=this.news.findIndex(r=>r.id===t);e!==-1&&!this.news[e].is_read&&(this.news[e].is_read=!0,this.unreadCounts.news=Math.max(0,this.unreadCounts.news-1))}catch(e){throw e}},async pinNews(t){try{const e=await y.post(`/api/communication/company/${t}/pin`),r=this.news.findIndex(s=>s.id===t);return r!==-1&&(this.news[r].is_pinned=!0),e.data}catch(e){throw e}},async unpinNews(t){try{const e=await y.delete(`/api/communication/company/${t}/pin`),r=this.news.findIndex(s=>s.id===t);return r!==-1&&(this.news[r].is_pinned=!1),e.data}catch(e){throw e}},async duplicateNews(t){try{const e=await y.post(`/api/communication/company/${t}/duplicate`);return this.news.unshift(e.data),e.data}catch(e){throw e}},async fetchComments(t,e,r={}){var s,n,o,m;this.loading.comments=!0,this.errors.comments=null;try{const f=await y.get(`/api/communication/${t}/${e}/comments`,{params:r});this.comments=((s=f.data.data)==null?void 0:s.comments)||f.data.comments||[],this.pagination.comments=((n=f.data.data)==null?void 0:n.pagination)||f.data.pagination||{page:1,totalPages:1,totalItems:0,hasMore:!1}}catch(f){throw this.errors.comments=((m=(o=f.response)==null?void 0:o.data)==null?void 0:m.message)||"Errore nel caricamento dei commenti",this.comments=[],f}finally{this.loading.comments=!1}},async createComment(t,e,r){try{const s=await y.post(`/api/communication/${t}/${e}/comments`,r);return this.comments.push(s.data.data||s.data),s.data.data||s.data}catch(s){throw s}},async updateComment(t,e){try{const r=await y.put(`/api/communication/comments/${t}`,e),s=this.comments.findIndex(n=>n.id===t);return s!==-1&&(this.comments[s]=r.data),r.data}catch(r){throw r}},async deleteComment(t){try{await y.delete(`/api/communication/comments/${t}`),this.comments=this.comments.filter(e=>e.id!==t)}catch(e){throw e}},async addReaction(t,e,r){try{const s={topic:"forum_topic",comment:"forum_comment",news:"news",event:"company_event"},n=await y.post("/api/communication/reactions",{content_type:s[t]||t,content_id:e,reaction_type:r});return this.reactions[t]||(this.reactions[t]={}),this.reactions[t][e]||(this.reactions[t][e]=[]),this.reactions[t][e].push(n.data.data||n.data),n.data.data||n.data}catch(s){throw s}},async removeReaction(t,e,r){try{await y.delete(`/api/communication/reactions/${r}`),this.reactions[t]&&this.reactions[t][e]&&(this.reactions[t][e]=this.reactions[t][e].filter(s=>s.id!==r))}catch(s){throw s}},async fetchCategories(){var t;try{const e=await y.get("/api/communication/categories");return this.categories=((t=e.data.data)==null?void 0:t.categories)||e.data||[],this.categories}catch(e){throw this.categories=[],e}},async fetchForumCategories(){try{const t=[...new Set((this.topics||[]).map(e=>e.category).filter(Boolean))];return this.categories=t,t}catch(t){throw this.categories=[],t}},async createCategory(t){try{const e=await y.post("/api/communication/categories",t);return this.categories.push(e.data),e.data}catch(e){throw e}},async updateCategory(t){try{const e=await y.put(`/api/communication/categories/${t.id}`,t),r=this.categories.findIndex(s=>s.id===t.id);return r!==-1&&(this.categories[r]=e.data),e.data}catch(e){throw e}},async deleteCategory(t){try{await y.delete(`/api/communication/categories/${t}`),this.categories=this.categories.filter(e=>e.id!==t)}catch(e){throw e}},async fetchUsers(){var t;try{const e=await y.get("/api/personnel/users");return(((t=e.data.data)==null?void 0:t.users)||e.data||[]).map(s=>({id:s.id,name:s.full_name||s.name,role:s.position||s.role,department:s.department_name||s.department,email:s.email}))}catch(e){throw console.error("Failed to fetch users for messaging:",e),e}},async searchUsers(t={}){var e;try{const r=await y.get("/api/personnel/users",{params:{search:t.query,per_page:50,...t}});return(((e=r.data.data)==null?void 0:e.users)||r.data||[]).filter(n=>t.exclude_self?n.id!==t.current_user_id:!0).map(n=>({id:n.id,name:n.full_name||n.name,role:n.position||n.role,department:n.department_name||n.department,email:n.email}))}catch(r){throw console.error("Failed to search users:",r),r}},async fetchCommunicationStats(){try{const t=await y.get("/api/communication/stats");return this.stats=t.data.data||t.data,this.stats}catch(t){throw console.error("Failed to fetch stats:",t),this.stats={},t}},async searchContent(t,e={}){var r,s;this.loading.search=!0,this.errors.search=null;try{const n=await y.get("/api/communication/search",{params:{query:t,...e}});return this.searchResults=n.data.results||[],this.pagination.search=n.data.pagination||{page:1,totalPages:1,totalItems:0,hasMore:!1},n.data}catch(n){throw this.errors.search=((s=(r=n.response)==null?void 0:r.data)==null?void 0:s.message)||"Errore nella ricerca",this.searchResults=[],n}finally{this.loading.search=!1}},clearSearchResults(){this.searchResults=[],this.pagination.search={page:1,totalPages:1,totalItems:0,hasMore:!1}},async markAllAsRead(){try{await y.post("/api/communication/notifications/mark-all-read")}catch(t){throw t}},async getNotificationSettings(){try{return(await y.get("/api/communication/notification-settings")).data}catch(t){throw t}},async updateNotificationSettings(t){try{return(await y.put("/api/communication/notification-settings",t)).data}catch(e){throw e}},clearErrors(){this.errors={topics:null,polls:null,messages:null,conversations:null,events:null,news:null,reactions:null,comments:null,search:null}},resetPagination(){this.pagination={topics:{page:1,totalPages:1,totalItems:0,hasMore:!1},polls:{page:1,totalPages:1,totalItems:0,hasMore:!1},messages:{page:1,totalPages:1,totalItems:0,hasMore:!1},conversations:{page:1,totalPages:1,totalItems:0,hasMore:!1},events:{page:1,totalPages:1,totalItems:0,hasMore:!1},news:{page:1,totalPages:1,totalItems:0,hasMore:!1},comments:{page:1,totalPages:1,totalItems:0,hasMore:!1},search:{page:1,totalPages:1,totalItems:0,hasMore:!1}}}}}),qr={class:"relative"},Ir={class:"relative"},jr={key:0,class:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center"},Tr={class:"py-1"},Vr={key:0,class:"px-4 py-8 text-center text-gray-500 text-sm"},Dr={key:1,class:"px-4 py-8 text-center text-gray-500 text-sm"},Rr={key:2,class:"max-h-64 overflow-y-auto"},Lr=["onClick"],Sr={class:"flex items-start"},Or={class:"flex-shrink-0"},zr={class:"ml-3 flex-1"},Nr={class:"text-sm font-medium text-gray-900"},Br={class:"text-xs text-gray-500 mt-1"},Hr={class:"text-xs text-gray-400 mt-1"},Zr={key:0,class:"flex-shrink-0"},Ur={key:3,class:"px-4 py-2 border-t border-gray-100"},Fr={__name:"HeaderNotifications",setup(t){const e=I(!1),r=G(),s=Er(),n=Mr();H();const o=x(()=>s.notifications),m=x(()=>{const k=s.unreadCount,g=n.unreadCounts.messages,A=n.unreadCounts.news;return k+g+A}),f=x(()=>s.loading);let v=null;F(async()=>{await s.fetchNotifications();try{await Promise.all([n.fetchMessages({per_page:1}).catch(k=>console.warn("Messages failed:",k)),n.fetchNews({per_page:1}).catch(k=>console.warn("News failed:",k))])}catch(k){console.warn("Errore nel caricamento dati comunicazione:",k)}v=setInterval(async()=>{await s.pollNotifications();try{await Promise.all([n.fetchMessages({per_page:1}).catch(k=>console.warn("Messages poll failed:",k)),n.fetchNews({per_page:1}).catch(k=>console.warn("News poll failed:",k))])}catch(k){console.warn("Errore nel polling comunicazioni:",k)}},3e5)}),Oe(()=>{v&&clearInterval(v)});function _(k){const g={task:"h-6 w-6 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center",project:"h-6 w-6 rounded-full bg-green-100 text-green-600 flex items-center justify-center",user:"h-6 w-6 rounded-full bg-purple-100 text-purple-600 flex items-center justify-center",message:"h-6 w-6 rounded-full bg-indigo-100 text-indigo-600 flex items-center justify-center",news:"h-6 w-6 rounded-full bg-orange-100 text-orange-600 flex items-center justify-center",communication:"h-6 w-6 rounded-full bg-pink-100 text-pink-600 flex items-center justify-center",system:"h-6 w-6 rounded-full bg-gray-100 text-gray-600 flex items-center justify-center"};return g[k]||g.system}function p(k){return{task:"clipboard-document-list",project:"folder",user:"user",message:"envelope",news:"newspaper",communication:"chat-bubble-left",system:"information-circle"}[k]||"information-circle"}function c(k){const g=new Date(k),b=new Date-g;return b<6e4?"Adesso":b<36e5?`${Math.floor(b/6e4)}m fa`:b<864e5?`${Math.floor(b/36e5)}h fa`:g.toLocaleDateString("it-IT")}async function u(k){try{k.is_read||await s.markAsRead(k.id),e.value=!1,k.link&&r.push(k.link)}catch(g){console.error("Errore gestione click notifica:",g)}}async function h(){try{await s.markAllAsRead()}catch(k){console.error("Errore segnando tutte come lette:",k)}}return(k,g)=>(i(),l("div",qr,[a("button",{onClick:g[0]||(g[0]=A=>e.value=!e.value),class:"p-2 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[g[2]||(g[2]=a("span",{class:"sr-only"},"Visualizza notifiche",-1)),a("div",Ir,[w(j,{name:"bell",size:"md"}),m.value>0?(i(),l("span",jr,P(m.value>9?"9+":m.value),1)):C("",!0)])]),e.value?(i(),l("div",{key:0,onClick:g[1]||(g[1]=A=>e.value=!1),class:"origin-top-right absolute right-0 mt-2 w-80 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50"},[a("div",Tr,[g[5]||(g[5]=a("div",{class:"px-4 py-2 border-b border-gray-100"},[a("h3",{class:"text-sm font-medium text-gray-900"},"Notifiche")],-1)),f.value?(i(),l("div",Vr,g[3]||(g[3]=[a("div",{class:"animate-spin rounded-full h-6 w-6 border-b-2 border-primary-500 mx-auto"},null,-1),a("div",{class:"mt-2"},"Caricamento...",-1)]))):o.value.length===0?(i(),l("div",Dr," Nessuna notifica ")):(i(),l("div",Rr,[(i(!0),l(B,null,U(o.value,A=>(i(),l("div",{key:A.id,class:"px-4 py-3 hover:bg-gray-50 cursor-pointer border-b border-gray-50 last:border-b-0",onClick:b=>u(A)},[a("div",Sr,[a("div",Or,[a("div",{class:V(_(A.type))},[w(j,{name:p(A.type),size:"sm"},null,8,["name"])],2)]),a("div",zr,[a("p",Nr,P(A.title),1),a("p",Br,P(A.message),1),a("p",Hr,P(c(A.created_at)),1)]),A.is_read?C("",!0):(i(),l("div",Zr,g[4]||(g[4]=[a("div",{class:"h-2 w-2 bg-primary-500 rounded-full"},null,-1)])))])],8,Lr))),128))])),o.value.length>0?(i(),l("div",Ur,[a("button",{onClick:h,class:"text-xs text-primary-600 hover:text-primary-800"}," Segna tutte come lette ")])):C("",!0)])])):C("",!0)]))}},Gr={class:"relative"},Wr={class:"flex items-start justify-center min-h-screen pt-16 px-4 pb-20 text-center sm:block sm:p-0"},Kr={class:"inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6"},Qr={class:"flex items-center"},Jr={class:"flex-1"},Yr={key:0,class:"mt-4 max-h-64 overflow-y-auto"},Xr={class:"space-y-1"},ea=["onClick"],ta={class:"flex-shrink-0"},ra={class:"ml-3 flex-1 min-w-0"},aa={class:"text-sm font-medium text-gray-900 truncate"},sa={class:"text-xs text-gray-500 truncate"},na={class:"ml-2 text-xs text-gray-400"},oa={key:1,class:"mt-4 text-center py-4"},ia={key:2,class:"mt-4 text-center py-4"},ca={__name:"HeaderSearch",setup(t){const e=G(),r=I(!1),s=I(""),n=I([]),o=I(-1),m=I(!1),f=I(null),v=[{id:1,type:"project",title:"Website Redesign",description:"Progetto di redesign del sito web aziendale",path:"/app/projects/1"},{id:2,type:"person",title:"Mario Rossi",description:"Senior Developer",path:"/app/personnel/directory/1"},{id:3,type:"document",title:"Specifiche Tecniche",description:"Documento delle specifiche del progetto mobile",path:"/app/documents/1"},{id:4,type:"task",title:"Implementazione API",description:"Task per l'implementazione delle API REST",path:"/app/projects/1/tasks/5"}];Z(r,async A=>{var b;A?(await ze(),(b=f.value)==null||b.focus()):(s.value="",n.value=[],o.value=-1)});function _(){if(!s.value.trim()){n.value=[];return}m.value=!0,setTimeout(()=>{n.value=v.filter(A=>A.title.toLowerCase().includes(s.value.toLowerCase())||A.description.toLowerCase().includes(s.value.toLowerCase())),o.value=-1,m.value=!1},200)}function p(A){if(n.value.length===0)return;const b=o.value+A;b>=0&&b<n.value.length&&(o.value=b)}function c(){o.value>=0&&n.value[o.value]&&u(n.value[o.value])}function u(A){r.value=!1,e.push(A.path)}function h(A){const b={project:"h-6 w-6 rounded bg-blue-100 text-blue-600 flex items-center justify-center",person:"h-6 w-6 rounded bg-green-100 text-green-600 flex items-center justify-center",document:"h-6 w-6 rounded bg-yellow-100 text-yellow-600 flex items-center justify-center",task:"h-6 w-6 rounded bg-purple-100 text-purple-600 flex items-center justify-center"};return b[A]||b.document}function k(A){return{project:"folder",person:"user",document:"document",task:"clipboard-document-list"}[A]||"document"}function g(A){return{project:"Progetto",person:"Persona",document:"Documento",task:"Task"}[A]||"Elemento"}return(A,b)=>(i(),l("div",Gr,[a("button",{onClick:b[0]||(b[0]=q=>r.value=!r.value),class:"p-2 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[b[7]||(b[7]=a("span",{class:"sr-only"},"Cerca",-1)),w(j,{name:"search",size:"md"})]),r.value?(i(),l("div",{key:0,class:"fixed inset-0 z-50 overflow-y-auto",onClick:b[6]||(b[6]=Ne(q=>r.value=!1,["self"]))},[a("div",Wr,[b[10]||(b[10]=a("div",{class:"fixed inset-0 bg-black bg-opacity-25 transition-opacity"},null,-1)),a("div",Kr,[a("div",null,[a("div",Qr,[a("div",Jr,[Be(a("input",{ref_key:"searchInput",ref:f,"onUpdate:modelValue":b[1]||(b[1]=q=>s.value=q),onInput:_,onKeydown:[b[2]||(b[2]=W(q=>r.value=!1,["escape"])),W(c,["enter"]),b[3]||(b[3]=W(q=>p(-1),["up"])),b[4]||(b[4]=W(q=>p(1),["down"]))],type:"text",placeholder:"Cerca progetti, persone, documenti...",class:"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"},null,544),[[He,s.value]])]),a("button",{onClick:b[5]||(b[5]=q=>r.value=!1),class:"ml-3 p-2 text-gray-400 hover:text-gray-600"},[w(j,{name:"close",size:"md"})])]),n.value.length>0?(i(),l("div",Yr,[a("div",Xr,[(i(!0),l(B,null,U(n.value,(q,L)=>(i(),l("div",{key:q.id,onClick:R=>u(q),class:V(["flex items-center px-3 py-2 rounded-md cursor-pointer",L===o.value?"bg-primary-50":"hover:bg-gray-50"])},[a("div",ta,[a("div",{class:V(h(q.type))},[w(j,{name:k(q.type),size:"sm"},null,8,["name"])],2)]),a("div",ra,[a("p",aa,P(q.title),1),a("p",sa,P(q.description),1)]),a("div",na,P(g(q.type)),1)],10,ea))),128))])])):s.value&&!m.value?(i(),l("div",oa,b[8]||(b[8]=[a("p",{class:"text-sm text-gray-500"},"Nessun risultato trovato",-1)]))):s.value?C("",!0):(i(),l("div",ia,b[9]||(b[9]=[a("p",{class:"text-xs text-gray-400"},"Inizia a digitare per cercare...",-1)])))])])])])):C("",!0)]))}},la={class:"relative"},ua={class:"h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center"},da={class:"text-sm font-medium text-primary-700"},ma={class:"py-1"},pa={class:"px-4 py-2 border-b border-gray-100 dark:border-gray-700"},ha={class:"text-sm font-medium text-gray-900 dark:text-white"},ga={class:"text-xs text-gray-500 dark:text-gray-400"},va={class:"flex items-center"},fa={__name:"HeaderUserMenu",setup(t){const e=G(),r=H(),s=I(!1),{isDarkMode:n,toggleDarkMode:o}=ce(),m=x(()=>r.user&&(r.user.name||r.user.username)||"Utente"),f=x(()=>{var p;return((p=r.user)==null?void 0:p.email)||""}),v=x(()=>r.user?m.value.charAt(0).toUpperCase():"U");async function _(){s.value=!1,await r.logout(),e.push("/auth/login")}return(p,c)=>{const u=O("router-link");return i(),l("div",la,[a("button",{onClick:c[0]||(c[0]=h=>s.value=!s.value),class:"flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[c[5]||(c[5]=a("span",{class:"sr-only"},"Apri menu utente",-1)),a("div",ua,[a("span",da,P(v.value),1)])]),s.value?(i(),l("div",{key:0,onClick:c[4]||(c[4]=h=>s.value=!1),class:"origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 z-50"},[a("div",ma,[a("div",pa,[a("p",ha,P(m.value),1),a("p",ga,P(f.value),1)]),w(u,{to:"/app/profile",class:"flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:c[1]||(c[1]=h=>s.value=!1)},{default:E(()=>[w(j,{name:"user",size:"sm",className:"mr-2"}),c[6]||(c[6]=M(" Il tuo profilo "))]),_:1,__:[6]}),w(u,{to:"/app/settings",class:"flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:c[2]||(c[2]=h=>s.value=!1)},{default:E(()=>[w(j,{name:"settings",size:"sm",className:"mr-2"}),c[7]||(c[7]=M(" Impostazioni "))]),_:1,__:[7]}),c[9]||(c[9]=a("div",{class:"border-t border-gray-100 dark:border-gray-700 my-1"},null,-1)),a("button",{onClick:c[3]||(c[3]=(...h)=>T(o)&&T(o)(...h)),class:"flex items-center justify-between w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"},[a("span",va,[w(j,{name:T(n)?"sun":"moon",size:"sm",className:"mr-2"},null,8,["name"]),M(" "+P(T(n)?"Modalità chiara":"Modalità scura"),1)])]),a("button",{onClick:_,class:"flex items-center w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"},[w(j,{name:"arrow-right-on-rectangle",size:"sm",className:"mr-2"}),c[8]||(c[8]=M(" Esci "))])])])):C("",!0)])}}},_a={class:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700"},wa={class:"flex justify-between items-center px-4 py-4 sm:px-6 lg:px-8"},ya={class:"flex items-center space-x-4"},xa={class:"flex flex-col"},ka={class:"text-lg font-semibold text-gray-900 dark:text-white"},ba={class:"flex items-center space-x-4"},Aa={class:"hidden md:flex items-center space-x-2"},Ca={__name:"AppHeader",props:{pageTitle:{type:String,required:!0},breadcrumbs:{type:Array,default:()=>[]}},emits:["toggle-mobile-sidebar","quick-create-project","quick-add-task"],setup(t){return(e,r)=>(i(),l("header",_a,[a("div",wa,[a("div",ya,[a("button",{onClick:r[0]||(r[0]=s=>e.$emit("toggle-mobile-sidebar")),class:"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:text-gray-500 dark:hover:text-gray-300 dark:hover:bg-gray-700"},[w(j,{name:"bars-3",size:"md"})]),a("div",xa,[a("h2",ka,P(t.pageTitle),1),t.breadcrumbs.length>0?(i(),D(Ar,{key:0,breadcrumbs:t.breadcrumbs},null,8,["breadcrumbs"])):C("",!0)])]),a("div",ba,[a("div",Aa,[w($r,{onQuickCreateProject:r[1]||(r[1]=s=>e.$emit("quick-create-project")),onQuickAddTask:r[2]||(r[2]=s=>e.$emit("quick-add-task"))})]),w(Fr),w(ca),w(fa)])])]))}},Pa={__name:"LoadingSpinner",props:{size:{type:String,default:"md",validator:t=>["sm","md","lg","xl"].includes(t)},message:{type:String,default:""},centered:{type:Boolean,default:!0}},setup(t){const e=t,r=x(()=>{const m={sm:"20px",md:"32px",lg:"48px",xl:"64px"};return`width: ${m[e.size]}; height: ${m[e.size]};`}),s=x(()=>["flex",e.centered?"items-center justify-center":"","space-y-2"]),n=x(()=>["flex items-center justify-center"]),o=x(()=>["text-sm text-gray-600 text-center"]);return(m,f)=>(i(),l("div",{class:V(s.value)},[a("div",{class:V(n.value)},[a("div",{class:"animate-spin rounded-full border-2 border-gray-300 border-t-primary-600",style:Ze(r.value)},null,4)],2),t.message?(i(),l("p",{key:0,class:V(o.value)},P(t.message),3)):C("",!0)],2))}},$a={class:"fixed bottom-0 right-0 z-[9999] p-6 space-y-4 pointer-events-none"},Ea={class:"p-4"},Ma={class:"flex items-start"},qa={class:"flex-shrink-0"},Ia={class:"ml-3 w-0 flex-1 pt-0.5"},ja={class:"text-sm font-semibold text-gray-900 dark:text-white"},Ta={class:"mt-1 text-sm text-gray-700 dark:text-gray-300"},Va={class:"ml-4 flex-shrink-0 flex"},Da=["onClick"],Ra={__name:"NotificationManager",setup(t){const e=I([]);function r(f){const v=Date.now(),_={id:v,type:f.type||"info",title:f.title,message:f.message,duration:f.duration||5e3};e.value.push(_),_.duration>0&&setTimeout(()=>{s(v)},_.duration)}function s(f){const v=e.value.findIndex(_=>_.id===f);v>-1&&e.value.splice(v,1)}function n(f){const v={success:"border-l-4 border-green-500 dark:border-green-400",error:"border-l-4 border-red-500 dark:border-red-400",warning:"border-l-4 border-yellow-500 dark:border-yellow-400",info:"border-l-4 border-blue-500 dark:border-blue-400"};return v[f]||v.info}function o(f){const v={success:"h-8 w-8 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 flex items-center justify-center",error:"h-8 w-8 rounded-full bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 flex items-center justify-center",warning:"h-8 w-8 rounded-full bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400 flex items-center justify-center",info:"h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 flex items-center justify-center"};return v[f]||v.info}function m(f){const v={success:"check-circle",error:"x-circle",warning:"exclamation-circle",info:"information-circle"};return v[f]||v.info}return window.showNotification=r,F(()=>{}),(f,v)=>(i(),l("div",$a,[w(Ue,{name:"notification",tag:"div",class:"space-y-4"},{default:E(()=>[(i(!0),l(B,null,U(e.value,_=>(i(),l("div",{key:_.id,class:V([n(_.type),"max-w-sm w-full bg-white dark:bg-gray-800 shadow-xl rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 dark:ring-white dark:ring-opacity-10 overflow-hidden border border-gray-200 dark:border-gray-700"])},[a("div",Ea,[a("div",Ma,[a("div",qa,[a("div",{class:V(o(_.type))},[w(j,{name:m(_.type),size:"sm"},null,8,["name"])],2)]),a("div",Ia,[a("p",ja,P(_.title),1),a("p",Ta,P(_.message),1)]),a("div",Va,[a("button",{onClick:p=>s(_.id),class:"bg-white dark:bg-gray-800 rounded-md inline-flex text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:focus:ring-offset-gray-800"},[v[0]||(v[0]=a("span",{class:"sr-only"},"Chiudi",-1)),w(j,{name:"close",size:"sm"})],8,Da)])])])],2))),128))]),_:1})]))}},La=ee(Ra,[["__scopeId","data-v-539205d1"]]),Sa={class:"h-screen flex bg-gray-50 dark:bg-gray-900"},Oa={class:"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900"},za={class:"py-6"},Na={class:"px-4 sm:px-6 lg:px-8"},Ba={key:0,class:"mb-6"},Ha={key:1,class:"flex items-center justify-center h-64"},Za={__name:"AppLayout",setup(t){const e=Y(),r=G(),s=X(),n=I(!1),o=I(!1),m=I(!1);x(()=>s.config||{});const f=x(()=>s.config!==null),v=x(()=>{var A;if((A=e.meta)!=null&&A.title)return e.meta.title;const g=e.path;return g.startsWith("/app/dashboard")?"Dashboard":g.startsWith("/app/personnel")?"Personale":g.startsWith("/app/timesheet")||g.startsWith("/app/projects")?"Attività":g.startsWith("/app/crm")||g.startsWith("/app/invoicing")?"Sales":g.startsWith("/app/business-intelligence")?"Business Intelligence":g.startsWith("/app/funding")?"Bandi":g.startsWith("/app/communications")?"Comunicazioni":g.startsWith("/app/compliance")?"Certificazioni":g.startsWith("/app/ceo")?"CEO":g.startsWith("/app/agents")?"Agenti":g.startsWith("/app/admin")?"Amministrazione":g.startsWith("/app/examples")?"Design System":g.startsWith("/app/profile")?"Profilo":"Portale"}),_=x(()=>{var g;return(g=e.meta)!=null&&g.breadcrumbs?e.meta.breadcrumbs.map(A=>({label:A.label,to:A.to,icon:A.icon})):[]}),p=x(()=>{var g;return((g=e.meta)==null?void 0:g.hasActions)||!1});function c(){n.value=!n.value}function u(){n.value=!1}function h(g){o.value=g}function k(){r.push("/app/projects/create")}return Z(e,()=>{m.value=!0,setTimeout(()=>{m.value=!1},300)}),Z(e,()=>{u()}),F(()=>{f.value||s.loadConfig()}),(g,A)=>{const b=O("router-view");return i(),l("div",Sa,[n.value?(i(),l("div",{key:0,onClick:u,class:"fixed inset-0 z-20 bg-black bg-opacity-50 lg:hidden"})):C("",!0),w(_r,{"is-mobile-open":n.value,onClose:u,onToggleCollapsed:h},null,8,["is-mobile-open"]),a("div",{class:V(["flex flex-col flex-1 overflow-hidden transition-all duration-300",[o.value?"lg:ml-20":"lg:ml-64"]])},[w(Ca,{"page-title":v.value,breadcrumbs:_.value,onToggleMobileSidebar:c,onQuickCreateProject:k},null,8,["page-title","breadcrumbs"]),a("main",Oa,[a("div",za,[a("div",Na,[p.value?(i(),l("div",Ba,[Fe(g.$slots,"page-actions")])):C("",!0),m.value?(i(),l("div",Ha,[w(Pa)])):(i(),D(b,{key:2}))])])])],2),w(La)])}}},$=Ge({necessary:!0,preferences:!1,analytics:!1,marketing:!1,consentGiven:!1,consentDate:null,showBanner:!1,showSettings:!1}),ne="cookie-consent",oe="cookie-consent-date",Ua={necessary:{name:"Necessari",description:"Cookie tecnici essenziali per il funzionamento del sito",required:!0,color:"green"},preferences:{name:"Preferenze",description:"Cookie per ricordare le tue preferenze e personalizzare l'esperienza",required:!1,color:"blue"},analytics:{name:"Analitici",description:"Cookie per analizzare l'utilizzo del sito e migliorare le performance",required:!1,color:"yellow"},marketing:{name:"Marketing",description:"Cookie per pubblicità personalizzata e remarketing",required:!1,color:"purple"}};function qe(){const t=()=>{const S={necessary:$.necessary,preferences:$.preferences,analytics:$.analytics,marketing:$.marketing,consentGiven:!0,consentDate:new Date().toISOString()};localStorage.setItem(ne,JSON.stringify(S)),localStorage.setItem(oe,S.consentDate),Object.assign($,S)},e=()=>{try{const S=localStorage.getItem(ne),te=localStorage.getItem(oe);if(S&&te){const Te=JSON.parse(S),Ve=new Date(te),le=new Date,De=new Date(le.setMonth(le.getMonth()-12));if(Ve>De)return Object.assign($,{...Te,consentDate:te,showBanner:!1}),r(),!0}}catch(S){console.error("Errore nel caricamento del consenso cookie:",S)}return $.showBanner=!0,!1},r=()=>{$.analytics?s():n(),$.marketing?o():m(),$.preferences?f():v()},s=()=>{typeof gtag<"u"&&gtag("consent","update",{analytics_storage:"granted"})},n=()=>{typeof gtag<"u"&&gtag("consent","update",{analytics_storage:"denied"})},o=()=>{typeof gtag<"u"&&gtag("consent","update",{ad_storage:"granted"})},m=()=>{typeof gtag<"u"&&gtag("consent","update",{ad_storage:"denied"})},f=()=>{console.log("Cookie di preferenze abilitati")},v=()=>{console.log("Cookie di preferenze disabilitati"),document.cookie.split(";").forEach(function(S){document.cookie=S.replace(/^ +/,"").replace(/=.*/,"=;expires="+new Date().toUTCString()+";path=/")})},_=()=>{$.preferences=!0,$.analytics=!0,$.marketing=!0,t(),r(),h()},p=()=>{$.preferences=!1,$.analytics=!1,$.marketing=!1,t(),r(),h()},c=()=>{t(),r(),g(),h()},u=()=>{$.showBanner=!0},h=()=>{$.showBanner=!1},k=()=>{$.showSettings=!0},g=()=>{$.showSettings=!1},A=()=>{$.showSettings=!$.showSettings},b=S=>$[S]===!0,q=()=>!$.consentGiven||$.showBanner,L=()=>{localStorage.removeItem(ne),localStorage.removeItem(oe),Object.assign($,{necessary:!0,preferences:!1,analytics:!1,marketing:!1,consentGiven:!1,consentDate:null,showBanner:!0,showSettings:!1})},R=x(()=>({necessary:$.necessary,preferences:$.preferences,analytics:$.analytics,marketing:$.marketing,consentGiven:$.consentGiven,consentDate:$.consentDate})),je=x(()=>({showBanner:$.showBanner,showSettings:$.showSettings}));return{cookieConsent:We($),consentStatus:R,uiState:je,COOKIE_CATEGORIES:Ua,loadConsent:e,saveConsent:t,applyCookieSettings:r,acceptAll:_,acceptNecessaryOnly:p,saveCustomSettings:c,resetConsent:L,showBanner:u,hideBanner:h,showSettings:k,hideSettings:g,toggleSettings:A,hasConsent:b,isConsentRequired:q}}const Fa={key:0,class:"fixed bottom-0 left-0 right-0 z-50 bg-white border-t-2 border-gray-200 shadow-lg",role:"banner","aria-label":"Cookie consent banner"},Ga={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4"},Wa={class:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4"},Ka={class:"flex-1"},Qa={class:"flex items-start"},Ja={class:"flex flex-wrap gap-4 mt-3 text-sm"},Ya={class:"flex flex-col sm:flex-row gap-3 lg:flex-shrink-0"},Xa={key:1,class:"fixed inset-0 z-50 overflow-y-auto","aria-labelledby":"cookie-settings-title",role:"dialog","aria-modal":"true"},es={class:"flex min-h-full items-center justify-center p-4"},ts={class:"relative bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden"},rs={class:"px-6 py-4 border-b border-gray-200"},as={class:"flex items-center justify-between"},ss={class:"px-6 py-4 max-h-96 overflow-y-auto"},ns={class:"space-y-6"},os={class:"flex items-start justify-between"},is={class:"flex-1"},cs={class:"flex items-center mb-2"},ls={class:"text-lg font-medium text-gray-900"},us={key:0,class:"ml-2 bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full"},ds={class:"text-sm text-gray-600"},ms={class:"ml-4"},ps={class:"relative inline-flex items-center cursor-pointer"},hs=["checked","disabled","onChange"],gs={class:"px-6 py-4 border-t border-gray-200 bg-gray-50"},vs={class:"flex flex-col sm:flex-row gap-3 justify-end"},fs={__name:"CookieBanner",setup(t){const{cookieConsent:e,uiState:r,COOKIE_CATEGORIES:s,acceptAll:n,acceptNecessaryOnly:o,saveCustomSettings:m,showSettings:f,hideSettings:v}=qe(),_=(p,c)=>{s[p].required||(e[p]=c)};return(p,c)=>{const u=O("router-link");return i(),l(B,null,[T(r).showBanner?(i(),l("div",Fa,[a("div",Ga,[a("div",Wa,[a("div",Ka,[a("div",Qa,[w(j,{name:"information-circle",class:"w-6 h-6 text-primary-600 mt-1 mr-3 flex-shrink-0"}),a("div",null,[c[9]||(c[9]=a("h3",{class:"text-lg font-semibold text-gray-900 mb-2"}," Utilizziamo i Cookie ",-1)),c[10]||(c[10]=a("p",{class:"text-sm text-gray-700 leading-relaxed"}," Utilizziamo cookie tecnici per garantire il funzionamento del sito e cookie opzionali per migliorare la tua esperienza. Puoi accettare tutti i cookie, solo quelli necessari, o personalizzare le tue preferenze. ",-1)),a("div",Ja,[w(u,{to:"/privacy",class:"text-primary-600 hover:text-primary-700 underline",target:"_blank"},{default:E(()=>c[7]||(c[7]=[M(" Privacy Policy ")])),_:1,__:[7]}),w(u,{to:"/cookie-policy",class:"text-primary-600 hover:text-primary-700 underline",target:"_blank"},{default:E(()=>c[8]||(c[8]=[M(" Cookie Policy ")])),_:1,__:[8]})])])])]),a("div",Ya,[a("button",{onClick:c[0]||(c[0]=(...h)=>T(f)&&T(f)(...h)),class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors"},[w(j,{name:"cog",class:"w-4 h-4 inline mr-2"}),c[11]||(c[11]=M(" Personalizza "))]),a("button",{onClick:c[1]||(c[1]=(...h)=>T(o)&&T(o)(...h)),class:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors"}," Solo Necessari "),a("button",{onClick:c[2]||(c[2]=(...h)=>T(n)&&T(n)(...h)),class:"px-6 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors"},[w(j,{name:"check",class:"w-4 h-4 inline mr-2"}),c[12]||(c[12]=M(" Accetta Tutti "))])])])])])):C("",!0),T(r).showSettings?(i(),l("div",Xa,[a("div",{class:"fixed inset-0 bg-black bg-opacity-50 transition-opacity",onClick:c[3]||(c[3]=(...h)=>T(v)&&T(v)(...h))}),a("div",es,[a("div",ts,[a("div",rs,[a("div",as,[c[13]||(c[13]=a("h2",{id:"cookie-settings-title",class:"text-xl font-semibold text-gray-900"}," Impostazioni Cookie ",-1)),a("button",{onClick:c[4]||(c[4]=(...h)=>T(v)&&T(v)(...h)),class:"text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-primary-500 rounded-md p-1"},[w(j,{name:"x-mark",class:"w-6 h-6"})])])]),a("div",ss,[c[14]||(c[14]=a("p",{class:"text-sm text-gray-600 mb-6"}," Gestisci le tue preferenze sui cookie. I cookie necessari sono sempre attivi per garantire il funzionamento del sito. ",-1)),a("div",ns,[(i(!0),l(B,null,U(T(s),(h,k)=>(i(),l("div",{key:k,class:"border border-gray-200 rounded-lg p-4"},[a("div",os,[a("div",is,[a("div",cs,[a("div",{class:V(`w-3 h-3 rounded-full mr-3 bg-${h.color}-500`)},null,2),a("h3",ls,P(h.name),1),h.required?(i(),l("span",us," Necessari ")):C("",!0)]),a("p",ds,P(h.description),1)]),a("div",ms,[a("label",ps,[a("input",{type:"checkbox",checked:T(e)[k],disabled:h.required,onChange:g=>_(k,g.target.checked),class:"sr-only"},null,40,hs),a("div",{class:V(["w-11 h-6 rounded-full transition-colors duration-200 ease-in-out",T(e)[k]?"bg-primary-600":"bg-gray-200",h.required?"opacity-50 cursor-not-allowed":"cursor-pointer"])},[a("div",{class:V(["w-5 h-5 bg-white rounded-full shadow-md transform transition-transform duration-200 ease-in-out",T(e)[k]?"translate-x-5":"translate-x-0"])},null,2)],2)])])])]))),128))])]),a("div",gs,[a("div",vs,[a("button",{onClick:c[5]||(c[5]=(...h)=>T(v)&&T(v)(...h)),class:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors"}," Annulla "),a("button",{onClick:c[6]||(c[6]=(...h)=>T(m)&&T(m)(...h)),class:"px-6 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors"},[w(j,{name:"check",class:"w-4 h-4 inline mr-2"}),c[15]||(c[15]=M(" Salva Preferenze "))])])])])])])):C("",!0)],64)}}},_s=ee(fs,[["__scopeId","data-v-516fe8dc"]]),ws={class:"min-h-screen bg-gray-50"},ys={class:"bg-white shadow-sm border-b"},xs={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},ks={class:"flex justify-between h-16"},bs={class:"flex items-center"},As={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center"},Cs={class:"text-white font-bold text-sm"},Ps={class:"text-xl font-semibold text-gray-900"},$s={class:"hidden md:flex items-center space-x-8"},Es={class:"flex items-center space-x-4 ml-8 pl-8 border-l border-gray-200"},Ms={class:"md:hidden flex items-center"},qs={key:0,class:"md:hidden"},Is={class:"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t"},js={class:"flex-1"},Ts={class:"bg-gray-900 text-white"},Vs={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12"},Ds={class:"grid grid-cols-1 md:grid-cols-4 gap-8"},Rs={class:"md:col-span-2"},Ls={class:"text-lg font-semibold mb-4"},Ss={class:"text-gray-300 mb-4"},Os={class:"flex space-x-4"},zs=["href"],Ns=["href"],Bs={class:"space-y-2"},Hs={class:"space-y-2"},Zs={class:"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400"},Ee={__name:"PublicLayout",setup(t){const e=X(),{loadConsent:r,showSettings:s}=qe(),n=I(!1),o=x(()=>e.config||{}),m=x(()=>o.value.company||{}),f=x(()=>o.value.contact||{}),v=x(()=>{var c;return((c=o.value.company)==null?void 0:c.name)||"DatVinci"}),_=x(()=>v.value.split(" ").map(u=>u[0]).join("").toUpperCase().slice(0,2)),p=()=>{s()};return F(()=>{e.loadConfig(),r()}),(c,u)=>{const h=O("router-link"),k=O("router-view");return i(),l("div",ws,[a("nav",ys,[a("div",xs,[a("div",ks,[a("div",bs,[w(h,{to:"/",class:"flex items-center space-x-3"},{default:E(()=>[a("div",As,[a("span",Cs,P(_.value),1)]),a("span",Ps,P(v.value),1)]),_:1})]),a("div",$s,[w(h,{to:"/",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:E(()=>u[1]||(u[1]=[M(" Home ")])),_:1,__:[1]}),w(h,{to:"/about",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:E(()=>u[2]||(u[2]=[M(" Chi Siamo ")])),_:1,__:[2]}),w(h,{to:"/services",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:E(()=>u[3]||(u[3]=[M(" Servizi ")])),_:1,__:[3]}),w(h,{to:"/contact",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:E(()=>u[4]||(u[4]=[M(" Contatti ")])),_:1,__:[4]}),a("div",Es,[w(h,{to:"/auth/login",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:E(()=>u[5]||(u[5]=[M(" Accedi ")])),_:1,__:[5]}),w(h,{to:"/auth/register",class:"bg-primary-600 text-white hover:bg-primary-700 px-4 py-2 rounded-md text-sm font-medium"},{default:E(()=>u[6]||(u[6]=[M(" Registrati ")])),_:1,__:[6]})])]),a("div",Ms,[a("button",{onClick:u[0]||(u[0]=g=>n.value=!n.value),class:"text-gray-400 hover:text-gray-500"},[w(j,{name:"bars-3",size:"md"})])])])]),n.value?(i(),l("div",qs,[a("div",Is,[w(h,{to:"/",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:E(()=>u[7]||(u[7]=[M(" Home ")])),_:1,__:[7]}),w(h,{to:"/about",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:E(()=>u[8]||(u[8]=[M(" Chi Siamo ")])),_:1,__:[8]}),w(h,{to:"/services",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:E(()=>u[9]||(u[9]=[M(" Servizi ")])),_:1,__:[9]}),w(h,{to:"/contact",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:E(()=>u[10]||(u[10]=[M(" Contatti ")])),_:1,__:[10]}),u[13]||(u[13]=a("hr",{class:"my-2"},null,-1)),w(h,{to:"/auth/login",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:E(()=>u[11]||(u[11]=[M(" Accedi ")])),_:1,__:[11]}),w(h,{to:"/auth/register",class:"block px-3 py-2 text-base font-medium bg-primary-600 text-white rounded-md"},{default:E(()=>u[12]||(u[12]=[M(" Registrati ")])),_:1,__:[12]})])])):C("",!0)]),a("main",js,[w(k)]),a("footer",Ts,[a("div",Vs,[a("div",Ds,[a("div",Rs,[a("h3",Ls,P(m.value.name),1),a("p",Ss,P(m.value.description),1),a("div",Os,[f.value.email?(i(),l("a",{key:0,href:`mailto:${f.value.email}`,class:"text-gray-300 hover:text-white"},[w(j,{name:"envelope",class:"w-5 h-5"})],8,zs)):C("",!0),f.value.phone?(i(),l("a",{key:1,href:`tel:${f.value.phone}`,class:"text-gray-300 hover:text-white"},[w(j,{name:"phone",class:"w-5 h-5"})],8,Ns)):C("",!0)])]),a("div",null,[u[18]||(u[18]=a("h3",{class:"text-lg font-semibold mb-4"},"Link Utili",-1)),a("ul",Bs,[a("li",null,[w(h,{to:"/",class:"text-gray-300 hover:text-white"},{default:E(()=>u[14]||(u[14]=[M("Home")])),_:1,__:[14]})]),a("li",null,[w(h,{to:"/about",class:"text-gray-300 hover:text-white"},{default:E(()=>u[15]||(u[15]=[M("Chi Siamo")])),_:1,__:[15]})]),a("li",null,[w(h,{to:"/services",class:"text-gray-300 hover:text-white"},{default:E(()=>u[16]||(u[16]=[M("Servizi")])),_:1,__:[16]})]),a("li",null,[w(h,{to:"/contact",class:"text-gray-300 hover:text-white"},{default:E(()=>u[17]||(u[17]=[M("Contatti")])),_:1,__:[17]})])])]),a("div",null,[u[21]||(u[21]=a("h3",{class:"text-lg font-semibold mb-4"},"Informazioni Legali",-1)),a("ul",Hs,[a("li",null,[w(h,{to:"/privacy",class:"text-gray-300 hover:text-white"},{default:E(()=>u[19]||(u[19]=[M("Privacy Policy")])),_:1,__:[19]})]),a("li",null,[w(h,{to:"/cookie-policy",class:"text-gray-300 hover:text-white"},{default:E(()=>u[20]||(u[20]=[M("Cookie Policy")])),_:1,__:[20]})]),a("li",null,[a("button",{onClick:p,class:"text-gray-300 hover:text-white text-left"}," Gestisci Cookie ")])])])]),a("div",Zs,[a("p",null,"© "+P(new Date().getFullYear())+" "+P(m.value.name)+". Tutti i diritti riservati.",1)])])]),w(_s)])}}},Us=()=>d(()=>import("./Home.js"),__vite__mapDeps([8,2])),Fs=()=>d(()=>import("./About.js"),__vite__mapDeps([9,2])),Gs=()=>d(()=>import("./Contact.js"),__vite__mapDeps([10,2])),Ws=()=>d(()=>import("./Services.js"),__vite__mapDeps([11,2])),Ks=()=>d(()=>import("./Privacy.js"),__vite__mapDeps([12,2])),Qs=()=>d(()=>import("./CookiePolicy.js"),__vite__mapDeps([13,2])),Js=()=>d(()=>import("./Login.js"),__vite__mapDeps([14,2])),Ys=()=>d(()=>import("./Register.js"),__vite__mapDeps([15,2])),Xs=()=>d(()=>import("./Dashboard.js"),__vite__mapDeps([16,2,17,18,19,20])),en=()=>d(()=>import("./Projects.js"),__vite__mapDeps([21,2,22,23,24])),tn=[{path:"/",component:Ee,children:[{path:"",name:"home",component:Us},{path:"about",name:"about",component:Fs},{path:"contact",name:"contact",component:Gs},{path:"services",name:"services",component:Ws},{path:"privacy",name:"privacy",component:Ks},{path:"cookie-policy",name:"cookie-policy",component:Qs}]},{path:"/auth",component:Ee,children:[{path:"login",name:"login",component:Js},{path:"register",name:"register",component:Ys}]},{path:"/app",component:Za,meta:{requiresAuth:!0},children:[{path:"",redirect:"/app/dashboard"},{path:"dashboard",name:"dashboard",component:Xs,meta:{requiresAuth:!0,requiredPermission:"view_dashboard"}},{path:"projects",name:"projects",component:en,meta:{requiresAuth:!0,requiredPermission:"view_all_projects"}},{path:"projects/create",name:"projects-create",component:()=>d(()=>import("./ProjectCreate.js"),__vite__mapDeps([25,2])),meta:{requiresAuth:!0,requiredPermission:"edit_project"}},{path:"projects/:id",name:"project-view",component:()=>d(()=>import("./ProjectView.js"),__vite__mapDeps([26,2,22,19,7,27])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects"}},{path:"projects/:id/edit",name:"project-edit",component:()=>d(()=>import("./ProjectEdit.js"),__vite__mapDeps([28,2])),meta:{requiresAuth:!0,requiredPermission:"edit_project"}},{path:"timesheet",redirect:"/app/timesheet/entry"},{path:"timesheet/entry",name:"timesheet-entry",component:()=>d(()=>import("./TimesheetEntry.js"),__vite__mapDeps([29,2,23,30,31,32,33])),meta:{requiresAuth:!0,requiredPermission:"manage_timesheets"}},{path:"timesheet/requests",name:"timesheet-requests",component:()=>d(()=>import("./TimesheetRequests.js"),__vite__mapDeps([34,2,33])),meta:{requiresAuth:!0,requiredPermission:"manage_timesheets"}},{path:"timesheet/dashboard",name:"timesheet-dashboard",component:()=>d(()=>import("./TimesheetDashboard.js"),__vite__mapDeps([35,2,23,36,37,38,30,31,32,33,39,40])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects"}},{path:"timesheet/analytics",name:"timesheet-analytics",component:()=>d(()=>import("./TimesheetAnalytics.js"),__vite__mapDeps([41,23,2,36,24,42,43,30,33,44])),meta:{requiresAuth:!0,requiredPermission:"admin_access"}},{path:"communications",redirect:"/app/communications/dashboard"},{path:"communications/dashboard",name:"communications-dashboard",component:()=>d(()=>import("./CommunicationDashboard.js"),__vite__mapDeps([45,2,46,43,17,18,47,48,49])),meta:{requiresAuth:!0,requiredPermission:"view_communications"}},{path:"communications/forum",name:"communications-forum",component:()=>d(()=>import("./ForumIndex.js"),__vite__mapDeps([50,2,46,43,51,52,53,54,55,56])),meta:{requiresAuth:!0,requiredPermission:"view_communications"}},{path:"communications/forum/topics/:id",name:"communications-topic",component:()=>d(()=>import("./TopicView.js"),__vite__mapDeps([57,2,46,43,54,55,58])),meta:{requiresAuth:!0,requiredPermission:"view_communications"}},{path:"communications/polls",name:"communications-polls",component:()=>d(()=>import("./PollsIndex.js"),__vite__mapDeps([59,2,46,43,51,52,53,47,48,60,61,55,62])),meta:{requiresAuth:!0,requiredPermission:"view_communications"}},{path:"communications/polls/:id",name:"communications-poll",component:()=>d(()=>import("./PollView.js"),__vite__mapDeps([63,2,46,43,47,48,60,61,55,64])),meta:{requiresAuth:!0,requiredPermission:"view_communications"}},{path:"communications/messages",name:"communications-messages",component:()=>d(()=>import("./MessagesIndex.js"),__vite__mapDeps([65,2,46,43,51,52,53,55,66])),meta:{requiresAuth:!0,requiredPermission:"view_communications"}},{path:"communications/events",name:"communications-events",component:()=>d(()=>import("./EventsIndex.js"),__vite__mapDeps([67,2,46,43,51,52,53,47,48,68,69,55,70])),meta:{requiresAuth:!0,requiredPermission:"view_communications"}},{path:"communications/events/:id",name:"communications-event-view",component:()=>d(()=>import("./EventView.js"),__vite__mapDeps([71,2,46,43,68,69,55])),meta:{requiresAuth:!0,requiredPermission:"view_communications"}},{path:"communications/news",name:"communications-news",component:()=>d(()=>import("./NewsIndex.js"),__vite__mapDeps([72,2,46,43,51,52,53,47,48,19,73,30,74,55,75])),meta:{requiresAuth:!0,requiredPermission:"view_communications"}},{path:"communications/news/:id",name:"communications-news-view",component:()=>d(()=>import("./NewsView.js"),__vite__mapDeps([76,2,46,43,19,47,48,77])),meta:{requiresAuth:!0,requiredPermission:"view_communications"}},{path:"communications/hr-assistant",name:"hr-assistant-chat",component:()=>d(()=>import("./HRAssistantChat.js"),__vite__mapDeps([78,2,79])),meta:{requiresAuth:!0,requiredPermission:"view_communications"}},{path:"communications/hr-knowledge-base",name:"hr-knowledge-base",component:()=>d(()=>import("./HRKnowledgeBase.js"),__vite__mapDeps([80,2,79,52,53])),meta:{requiresAuth:!0,requiredPermission:"view_communications"}},{path:"examples/dashboard",name:"dashboard-example",component:()=>d(()=>import("./DashboardExample.js"),__vite__mapDeps([81,17,2,18,82])),meta:{requiresAuth:!0,requiredPermission:"view_dashboard"}},{path:"examples/timesheet-grid",name:"timesheet-grid-example",component:()=>d(()=>import("./TimesheetGridExample.js"),__vite__mapDeps([83,31,2,32])),meta:{requiresAuth:!0,requiredPermission:"view_dashboard"}},{path:"examples/components",name:"components-example",component:()=>d(()=>import("./ComponentsExample.js"),__vite__mapDeps([84,23,2,36,30,85,40,73,74,86,87,88,89,90])),meta:{requiresAuth:!0,requiredPermission:"view_dashboard"}},{path:"examples/form-builder",name:"form-builder-example",component:()=>d(()=>import("./FormBuilderExample.js"),__vite__mapDeps([91,73,2,30,74,92])),meta:{requiresAuth:!0,requiredPermission:"view_dashboard"}},{path:"examples/view-mode-toggle",name:"view-mode-toggle-example",component:()=>d(()=>import("./ViewModeToggleExample.js"),__vite__mapDeps([93,86,2,87,94])),meta:{requiresAuth:!0,requiredPermission:"view_dashboard"}},{path:"examples/kanban",name:"kanban-example",component:()=>d(()=>import("./KanbanExample.js"),__vite__mapDeps([95,2,23,88])),meta:{requiresAuth:!0,requiredPermission:"view_dashboard"}},{path:"examples/proposal-card",name:"proposal-card-example",component:()=>d(()=>import("./ProposalCardExample.js"),__vite__mapDeps([96,2,23,89,90])),meta:{requiresAuth:!0,requiredPermission:"view_dashboard"}},{path:"examples/icon-system",name:"icon-system-example",component:()=>d(()=>import("./IconSystemExample.js"),__vite__mapDeps([97,2])),meta:{requiresAuth:!0,requiredPermission:"view_dashboard"}},{path:"examples/wizard-container",name:"wizard-container-example",component:()=>d(()=>import("./WizardContainerExample.js"),__vite__mapDeps([98,2,99,100,101])),meta:{requiresAuth:!0,requiredPermission:"view_dashboard"}},{path:"personnel",redirect:"/app/personnel/admin"},{path:"personnel/orgchart",name:"personnel-orgchart",component:()=>d(()=>import("./PersonnelOrgChart.js"),__vite__mapDeps([102,2,23,86,87,36,103])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data"}},{path:"personnel/skills",name:"personnel-skills",component:()=>d(()=>import("./SkillsMatrix.js"),__vite__mapDeps([104,2,23,24,36,30,105])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data"}},{path:"personnel/job-levels",name:"personnel-job-levels",component:()=>d(()=>import("./JobLevels.js"),__vite__mapDeps([106,2,51,52,53,107])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data"}},{path:"personnel/inquadramenti",redirect:"/app/personnel/job-levels"},{path:"personnel/departments",redirect:"/app/personnel/admin"},{path:"personnel/departments/create",name:"department-create",component:()=>d(()=>import("./DepartmentCreate.js"),__vite__mapDeps([108,2,23,73,30,74])),meta:{requiresAuth:!0,requiredPermission:"manage_users"}},{path:"personnel/departments/:id",name:"department-view",component:()=>d(()=>import("./DepartmentView.js"),__vite__mapDeps([109,2,39,23,37,38,48])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data"}},{path:"personnel/departments/:id/edit",name:"department-edit",component:()=>d(()=>import("./DepartmentEdit.js"),__vite__mapDeps([110,2,23,73,30,74,48])),meta:{requiresAuth:!0,requiredPermission:"manage_users"}},{path:"personnel/allocation",name:"personnel-allocation",component:()=>d(()=>import("./PersonnelAllocation.js"),__vite__mapDeps([111,23,2,36,24,42,43,46])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data"}},{path:"personnel/admin",name:"personnel-admin",component:()=>d(()=>import("./PersonnelAdmin.js"),__vite__mapDeps([112,23,2,30,85,40,52,53,113])),meta:{requiresAuth:!0,requiredPermission:"admin_access"}},{path:"personnel/performance",name:"personnel-performance",component:()=>d(()=>import("./PersonnelPerformance.js"),__vite__mapDeps([114,2,39,17,18,85,40,42,43,115])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data"}},{path:"personnel/:id",name:"personnel-profile",component:()=>d(()=>import("./PersonnelProfile.js"),__vite__mapDeps([116,2,39,19,37,38,52,53,117])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data"}},{path:"admin",redirect:"/app/admin/users"},{path:"admin/users",name:"admin-users",component:()=>d(()=>import("./Admin.js"),__vite__mapDeps([118,2])),meta:{requiresAuth:!0,requiredPermission:"admin_access"}},{path:"admin/settings",name:"admin-settings",component:()=>d(()=>import("./AdminSettings.js"),__vite__mapDeps([119,2])),meta:{requiresAuth:!0,requiredPermission:"admin_access"}},{path:"admin/kpi-templates",name:"admin-kpi-templates",component:()=>d(()=>import("./KPITemplates.js"),__vite__mapDeps([120,2])),meta:{requiresAuth:!0,requiredPermission:"admin_access"}},{path:"profile",name:"profile",component:()=>d(()=>import("./Profile.js"),__vite__mapDeps([121,2])),meta:{requiresAuth:!0}},{path:"settings",name:"settings",component:()=>d(()=>import("./Settings.js"),__vite__mapDeps([122,2])),meta:{requiresAuth:!0}},{path:"crm",redirect:"/app/crm/dashboard"},{path:"crm/dashboard",name:"crm-dashboard",component:()=>d(()=>import("./CRMDashboard.js"),__vite__mapDeps([123,124,2,19,17,18,125])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects"}},{path:"crm/clients",name:"crm-clients",component:()=>d(()=>import("./ClientsList.js"),__vite__mapDeps([126,2,124,19,127,51,52,53])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects"}},{path:"crm/clients/create",name:"crm-clients-create",component:()=>d(()=>import("./ClientForm.js"),__vite__mapDeps([128,2,124,19,127,23,73,30,74])),meta:{requiresAuth:!0,requiredPermission:"edit_project"}},{path:"crm/clients/:id",name:"crm-client-view",component:()=>d(()=>import("./ClientView.js"),__vite__mapDeps([129,2,124,19,127,23,36,37,38,47,48,74])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects"}},{path:"crm/clients/:id/edit",name:"crm-client-edit",component:()=>d(()=>import("./ClientForm.js"),__vite__mapDeps([128,2,124,19,127,23,73,30,74])),meta:{requiresAuth:!0,requiredPermission:"edit_project"}},{path:"crm/contacts",name:"crm-contacts",component:()=>d(()=>import("./ContactsList.js"),__vite__mapDeps([130,2,124,19,51,52,53])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects"}},{path:"crm/proposals",name:"crm-proposals",component:()=>d(()=>import("./ProposalsPipeline.js"),__vite__mapDeps([131,2,124,19,46,43,23,24,36,88,89,90,132])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects"}},{path:"crm/proposals/new",name:"crm-proposals-new",component:()=>d(()=>import("./ProposalForm.js"),__vite__mapDeps([133,2,124,19,23])),meta:{requiresAuth:!0,requiredPermission:"edit_project"}},{path:"crm/proposals/create",name:"crm-proposals-create",component:()=>d(()=>import("./ProposalForm.js"),__vite__mapDeps([133,2,124,19,23])),meta:{requiresAuth:!0,requiredPermission:"edit_project"}},{path:"crm/proposals/:id",name:"crm-proposal-view",component:()=>d(()=>import("./ProposalView.js"),__vite__mapDeps([134,2,124,19,47,48,23,36])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects"}},{path:"crm/proposals/:id/edit",name:"crm-proposal-edit",component:()=>d(()=>import("./ProposalForm.js"),__vite__mapDeps([133,2,124,19,23])),meta:{requiresAuth:!0,requiredPermission:"edit_project"}},{path:"crm/contracts",name:"crm-contracts",component:()=>d(()=>import("./ContractsList.js"),__vite__mapDeps([135,2,124,19,47,48,136,51,52,53])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects"}},{path:"crm/contracts/new",name:"crm-contract-create",component:()=>d(()=>import("./ContractForm.js"),__vite__mapDeps([137,2,124,19,136,23])),meta:{requiresAuth:!0,requiredPermission:"manage_contracts"}},{path:"crm/contracts/:id/edit",name:"crm-contract-edit",component:()=>d(()=>import("./ContractForm.js"),__vite__mapDeps([137,2,124,19,136,23])),meta:{requiresAuth:!0,requiredPermission:"manage_contracts"}},{path:"crm/contracts/:id",name:"crm-contract-view",component:()=>d(()=>import("./ContractView.js"),__vite__mapDeps([138,2,124,19,47,48,23,36,136])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects"}},{path:"invoicing",redirect:"/app/invoicing/pre-invoices"},{path:"invoicing/pre-invoices",name:"invoicing-pre-invoices",component:()=>d(()=>import("./PreInvoicesList.js"),__vite__mapDeps([139,2,19,23,24,36,42,43,52,53])),meta:{requiresAuth:!0,requiredPermission:"view_all_invoices"}},{path:"invoicing/pre-invoices/new",name:"invoicing-pre-invoice-create",component:()=>d(()=>import("./PreInvoiceForm.js"),__vite__mapDeps([140,2,19,23])),meta:{requiresAuth:!0,requiredPermission:"manage_invoices"}},{path:"invoicing/pre-invoices/:id",name:"invoicing-pre-invoice-view",component:()=>d(()=>import("./PreInvoiceView.js"),__vite__mapDeps([141,2,19,23])),meta:{requiresAuth:!0,requiredPermission:"view_all_invoices"}},{path:"business-intelligence",redirect:"/app/business-intelligence/dashboard"},{path:"business-intelligence/dashboard",name:"business-intelligence-dashboard",component:()=>d(()=>import("./BIDashboard.js"),__vite__mapDeps([142,17,2,18,44])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects"}},{path:"business-intelligence/case-studies",name:"business-intelligence-case-studies",component:()=>d(()=>import("./CaseStudies.js"),__vite__mapDeps([143,2,19])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects"}},{path:"business-intelligence/core-skills",name:"business-intelligence-core-skills",component:()=>d(()=>import("./CoreSkills.js"),__vite__mapDeps([144,2])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data"}},{path:"business-intelligence/technical-offer",name:"business-intelligence-technical-offer",component:()=>d(()=>import("./TechnicalOffer.js"),__vite__mapDeps([145,2])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects"}},{path:"business-intelligence/market-intel",name:"business-intelligence-market-intel",component:()=>d(()=>import("./MarketIntelligence.js"),__vite__mapDeps([146,2])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects"}},{path:"business-intelligence/advanced-reports",name:"business-intelligence-advanced-reports",component:()=>d(()=>import("./AdvancedReports.js"),__vite__mapDeps([147,2])),meta:{requiresAuth:!0,requiredPermission:"view_reports"}},{path:"certifications",redirect:"/app/certifications/dashboard"},{path:"certifications/dashboard",name:"certifications-dashboard",component:()=>d(()=>import("./CertificationsDashboard.js"),__vite__mapDeps([148,149,2,17,18])),meta:{requiresAuth:!0,requiredPermission:"view_compliance"}},{path:"certifications/list",name:"certifications-list",component:()=>d(()=>import("./CertificationsList.js"),__vite__mapDeps([150,2,149])),meta:{requiresAuth:!0,requiredPermission:"view_compliance"}},{path:"certifications/catalog",name:"certifications-catalog",component:()=>d(()=>import("./CertificationsCatalog.js"),__vite__mapDeps([151,2,149,23,152])),meta:{requiresAuth:!0,requiredPermission:"view_compliance"}},{path:"certifications/create",name:"certifications-create",component:()=>d(()=>import("./CertificationCreate.js"),__vite__mapDeps([153,2,149,19,23])),meta:{requiresAuth:!0,requiredPermission:"view_compliance"}},{path:"certifications/:id",name:"certification-view",component:()=>d(()=>import("./CertificationView.js"),__vite__mapDeps([154,2,149,19,23,152])),meta:{requiresAuth:!0,requiredPermission:"view_compliance"}},{path:"certifications/:id/edit",name:"certification-edit",component:()=>d(()=>import("./CertificationEdit.js"),__vite__mapDeps([155,2,149,19,23,152])),meta:{requiresAuth:!0,requiredPermission:"view_compliance"}},{path:"certifications/readiness",name:"certifications-readiness",component:()=>d(()=>import("./CertificationReadiness.js"),__vite__mapDeps([156,2,149,23,152,157])),meta:{requiresAuth:!0,requiredPermission:"view_compliance"}},{path:"ceo",redirect:"/app/ceo/dashboard"},{path:"ceo/dashboard",name:"ceo-dashboard",component:()=>d(()=>import("./CEODashboard.js"),__vite__mapDeps([158,2,17,18,19,159,160])),meta:{requiresAuth:!0,requiredPermission:"view_ceo"}},{path:"ceo/assistant",name:"ceo-assistant",component:()=>d(()=>import("./AIAssistant.js"),__vite__mapDeps([161,2,23,19,162])),meta:{requiresAuth:!0,requiredPermission:"view_ceo"}},{path:"ceo/insights",name:"ceo-insights",component:()=>d(()=>import("./InsightsReports.js").then(t=>t.I),__vite__mapDeps([163,2,23,19,159,164])),meta:{requiresAuth:!0,requiredPermission:"view_ceo"}},{path:"ceo/config",name:"ceo-config",component:()=>d(()=>import("./ResearchConfig.js"),__vite__mapDeps([165,2,23,19,166])),meta:{requiresAuth:!0,requiredPermission:"view_ceo"}},{path:"funding",redirect:"/app/funding/dashboard"},{path:"funding/dashboard",name:"funding-dashboard",component:()=>d(()=>import("./FundingDashboard.js"),__vite__mapDeps([167,2,168,17,18,19,169])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects"}},{path:"funding/search",name:"funding-search",component:()=>d(()=>import("./FundingSearch.js"),__vite__mapDeps([170,2,168,19,24,23,171])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects"}},{path:"funding/opportunities/:id",name:"funding-opportunity-view",component:()=>d(()=>import("./FundingOpportunityView.js"),__vite__mapDeps([172,2,168,19,23,47,48,173])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects"}},{path:"funding/applications/:id",name:"funding-application-view",component:()=>d(()=>import("./FundingApplicationView.js"),__vite__mapDeps([174,2,168,46,43,23,47,48,175])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects"}},{path:"funding/reporting",name:"funding-reporting",component:()=>d(()=>import("./FundingReporting.js"),__vite__mapDeps([176,2,168,46,43,23,47,48,177])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects"}},{path:"funding/applications/new",name:"funding-application-form",component:()=>d(()=>import("./FundingApplicationForm.js"),__vite__mapDeps([178,2,168,46,43,19,99,100,179])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects"}},{path:"funding/applications/new/:opportunityId",name:"funding-application-form-with-opportunity",component:()=>d(()=>import("./FundingApplicationForm.js"),__vite__mapDeps([178,2,168,46,43,19,99,100,179])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects"}},{path:"funding/expenses/new",name:"funding-expense-form",component:()=>d(()=>import("./FundingExpenseForm.js"),__vite__mapDeps([180,2,168,19,23])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects"}},{path:"funding/expenses/:id",name:"funding-expense-view",component:()=>d(()=>import("./FundingExpenseView.js"),__vite__mapDeps([181,2,168,19,43,23,47,48])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects"}},{path:"funding/expenses/:id/edit",name:"funding-expense-edit",component:()=>d(()=>import("./FundingExpenseForm.js"),__vite__mapDeps([180,2,168,19,23])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects"}},{path:"test/design-system",name:"test-design-system",component:()=>d(()=>import("./DesignSystemTest.js"),__vite__mapDeps([182,2,51,52,53])),meta:{requiresAuth:!0}}]}],Ie=Ke({history:Qe(),routes:tn});Ie.beforeEach(async(t,e,r)=>{const s=H();if(t.meta.requiresAuth){if(s.sessionChecked||await s.initializeAuth(),!s.isAuthenticated){r("/auth/login");return}if(t.meta.requiredPermission&&!s.hasPermission(t.meta.requiredPermission)){console.warn(`Accesso negato a ${t.path}: permesso '${t.meta.requiredPermission}' richiesto`),r("/app/dashboard");return}}r()});const Q=Je(st),rn=Ye();Q.use(rn);Q.use(Ie);const an=H();an.initializeAuth().then(()=>{console.log("Auth initialized successfully"),Q.mount("#app")}).catch(t=>{console.error("Auth initialization failed:",t),Q.mount("#app")});export{se as $,_e as A,wt as B,yt as C,ae as D,xt as E,kt as F,we as G,j as H,bt as I,At as J,ye as K,Ct as L,xe as M,Pt as N,$t as O,Et as P,Mt as Q,qt as R,It as S,jt as T,ke as U,Tt as V,Vt as W,be as X,Dt as Y,Ae as Z,ee as _,qe as a,Rt as a0,Lt as a1,St as a2,Ot as a3,zt as a4,H as b,y as c,Mr as d,ct as e,Pa as f,nn as g,ce as h,d as i,lt as j,ut as k,dt as l,mt as m,pt as n,pe as o,ht as p,he as q,re as r,gt as s,ge as t,X as u,ve as v,fe as w,vt as x,ft as y,_t as z};
