import{c as k,b as l,j as d,l as e,t as i,n as y,F as m,q as h,g,G as p}from"./vendor.js";const K={class:"bg-white rounded-lg shadow overflow-hidden"},_={class:"p-4 border-b border-gray-200"},w={class:"text-lg font-semibold text-gray-900"},S={class:"p-6"},C={class:"flex items-center justify-between mb-4"},B={class:"flex items-center space-x-2"},I={class:"font-medium text-gray-900"},M={class:"bg-gray-200 text-gray-600 text-xs px-2 py-1 rounded-full"},N={class:"bg-white rounded-lg border border-gray-200 p-4 cursor-pointer hover:shadow-md transition-shadow"},V={class:"font-medium text-gray-900 text-sm mb-2"},D={key:0,class:"text-sm text-gray-600 mb-2"},j={class:"text-xs text-gray-500"},q={key:0,class:"text-center py-8"},A={class:"text-sm text-gray-500"},F={__name:"KanbanView",props:{title:{type:String,default:"Kanban Board"},stages:{type:Array,required:!0,validator:t=>t.every(c=>c.name&&(c.key||c.status))},items:{type:Array,required:!0},stageKey:{type:String,default:"status"},itemKey:{type:String,default:"id"},titleKey:{type:String,default:"title"},descriptionKey:{type:String,default:"description"},dateKey:{type:String,default:"created_at"},columns:{type:Number,default:null},columnClass:{type:String,default:""},emptyMessage:{type:String,default:"Nessun elemento"}},emits:["item-click","stage-change","item-drop"],setup(t,{emit:c}){const n=t,x=k(()=>{const s=n.columns||Math.min(n.stages.length,5);return{1:"grid-cols-1",2:"grid-cols-1 md:grid-cols-2",3:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3",4:"grid-cols-1 md:grid-cols-2 lg:grid-cols-4",5:"grid-cols-1 md:grid-cols-3 lg:grid-cols-5"}[s]||"grid-cols-1 md:grid-cols-3 lg:grid-cols-5"}),u=s=>n.items.filter(a=>a[n.stageKey]===s),f=s=>u(s).length,b=s=>n.items.findIndex(a=>a[n.itemKey]===s[n.itemKey]),v=s=>s?new Date(s).toLocaleDateString("it-IT"):"N/A";return(s,a)=>(d(),l("div",K,[e("div",_,[e("h2",w,i(t.title),1)]),e("div",S,[e("div",{class:y(["grid gap-6",x.value])},[(d(!0),l(m,null,h(t.stages,r=>(d(),l("div",{key:r.key||r.status,class:"bg-gray-50 rounded-lg p-4"},[e("div",C,[e("div",B,[r.color?(d(),l("div",{key:0,class:y(["w-3 h-3 rounded-full",r.color])},null,2)):g("",!0),e("h3",I,i(r.name),1),e("span",M,i(f(r.key||r.status)),1)])]),e("div",{class:y(["space-y-3",t.columnClass])},[(d(!0),l(m,null,h(u(r.key||r.status),o=>p(s.$slots,"item",{key:o[t.itemKey],item:o,stage:r,index:b(o)},()=>[e("div",N,[e("h4",V,i(o[t.titleKey]||o.title||o.name),1),o[t.descriptionKey]?(d(),l("p",D,i(o[t.descriptionKey]),1)):g("",!0),e("div",j,i(o[t.dateKey]?v(o[t.dateKey]):""),1)])])),128)),u(r.key||r.status).length===0?(d(),l("div",q,[p(s.$slots,"empty-state",{stage:r},()=>[a[0]||(a[0]=e("svg",{class:"w-8 h-8 mx-auto mb-2 text-gray-300",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1)),e("p",A,i(t.emptyMessage||"Nessun elemento"),1)])])):g("",!0)],2)]))),128))],2)])]))}};export{F as _};
