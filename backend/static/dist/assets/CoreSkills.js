import{r as u,c as z,o as X,b as i,l as e,g as y,e as b,f as q,v as E,t as l,B as n,S,C as m,F as A,q as T,A as I,j as s,n as U,E as Y}from"./vendor.js";import{H as g,c as B}from"./app.js";const Z={class:"min-h-screen bg-gray-50 dark:bg-gray-900"},ee={class:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700"},te={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},ae={class:"flex justify-between h-16"},re={class:"flex items-center space-x-4"},se=["disabled"],le={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6"},oe={class:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8"},ie={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},de={class:"flex items-center"},ne={class:"flex-shrink-0"},ge={class:"ml-4"},ue={class:"text-2xl font-bold text-gray-900 dark:text-white"},ce={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},xe={class:"flex items-center"},me={class:"flex-shrink-0"},be={class:"ml-4"},ye={class:"text-2xl font-bold text-gray-900 dark:text-white"},ve={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},pe={class:"flex items-center"},ke={class:"flex-shrink-0"},fe={class:"ml-4"},he={class:"text-2xl font-bold text-gray-900 dark:text-white"},_e={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},we={class:"flex items-center"},Ce={class:"flex-shrink-0"},ze={class:"ml-4"},qe={class:"text-2xl font-bold text-gray-900 dark:text-white"},Ve={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6"},Me={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},Se={key:0,class:"flex justify-center py-12"},Ae={key:1,class:"text-center py-12"},Te={key:2,class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},Ue={class:"flex items-start justify-between mb-4"},Be={class:"flex-1"},je={class:"text-lg font-semibold text-gray-900 dark:text-white mb-2"},De={class:"text-sm text-gray-600 dark:text-gray-400 mb-3"},Ge={class:"flex flex-col items-end space-y-2"},Ne={class:"text-right"},Ee={class:"text-2xl font-bold text-gray-900 dark:text-white"},Ie={class:"mb-4"},Le={class:"flex justify-between items-center mb-2"},Fe={class:"text-sm text-gray-500 dark:text-gray-400"},Pe={class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"},$e={key:0,class:"mb-4"},He={class:"flex flex-wrap gap-2"},Re={key:0,class:"inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400"},Oe={key:1,class:"mb-4"},We={class:"text-sm text-gray-600 dark:text-gray-400"},Je={key:2,class:"mb-4"},Ke={class:"flex flex-wrap gap-2"},Qe={class:"flex justify-between items-center pt-4 border-t border-gray-200 dark:border-gray-700"},Xe={class:"flex items-center space-x-2"},Ye={class:"flex space-x-2"},Ze=["onClick"],et=["onClick"],tt={key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},at={class:"relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white dark:bg-gray-800"},rt={class:"mt-3"},st={class:"flex items-center justify-between mb-4"},lt={class:"flex justify-end space-x-3 pt-4"},ot=["disabled"],it={key:1,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},dt={class:"relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white dark:bg-gray-800"},nt={class:"mt-3"},gt={class:"flex items-center justify-between mb-4"},ut={class:"text-lg font-medium text-gray-900 dark:text-white"},ct={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},xt={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},mt={class:"flex justify-end space-x-3 pt-4"},bt=["disabled"],kt={__name:"CoreSkills",setup(yt){const p=u(!1),_=u(!1),k=u(!1),c=u([]),f=u(!1),w=u(!1),C=u(null),L=u(null),d=u({category:"",coverage:"",search:""}),o=u({name:"",description:"",category:"technical",min_team_size:3,avg_proficiency_required:3.5,business_value:""}),x=u({competency_area:"",business_context:"",target_market:"",specific_requirements:""}),j=z(()=>{let r=c.value;if(d.value.category&&(r=r.filter(t=>t.category===d.value.category)),d.value.coverage&&(r=r.filter(t=>{const a=t.team_coverage;return d.value.coverage==="high"?a>=5:d.value.coverage==="medium"?a>=3&&a<5:d.value.coverage==="low"?a<3:!0})),d.value.search){const t=d.value.search.toLowerCase();r=r.filter(a=>{var h;return a.name.toLowerCase().includes(t)||((h=a.description)==null?void 0:h.toLowerCase().includes(t))})}return r}),F=z(()=>{if(c.value.length===0)return 0;const r=c.value.reduce((t,a)=>t+a.team_coverage,0);return Math.round(r/c.value.length)}),P=z(()=>c.value.length===0?0:c.value.reduce((t,a)=>t+a.avg_proficiency_required,0)/c.value.length),$=z(()=>c.value.filter(r=>r.team_coverage<r.min_team_size).length),V=async()=>{p.value=!0;try{const r=await B.get("/api/business-intelligence/core-competencies");r.data.success&&(c.value=r.data.data.competencies)}catch(r){console.error("Error fetching competencies:",r)}finally{p.value=!1}},H=async()=>{_.value=!0;try{(await B.post("/api/business-intelligence/core-competencies",o.value)).data.success&&(M(),await V())}catch(r){console.error("Error saving competency:",r)}finally{_.value=!1}},R=r=>{C.value=r,o.value={...r},f.value=!0},O=r=>{console.log("View details for:",r)},M=()=>{f.value=!1,C.value=null,o.value={name:"",description:"",category:"technical",min_team_size:3,avg_proficiency_required:3.5,business_value:""}},W=async()=>{k.value=!0;try{const r=await B.post("/api/business-intelligence/core-competencies/generate-ai",x.value);r.data.success&&(L.value=r.data.data,o.value={name:r.data.data.name,description:r.data.data.description,category:r.data.data.category,min_team_size:r.data.data.min_team_size,avg_proficiency_required:r.data.data.avg_proficiency_required,business_value:r.data.data.business_value},w.value=!1,f.value=!0)}catch(r){console.error("Error generating competency with AI:",r)}finally{k.value=!1}},D=()=>{w.value=!1,x.value={competency_area:"",business_context:"",target_market:"",specific_requirements:""}},J=r=>({technical:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",business:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",soft_skills:"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300"})[r]||"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300",K=r=>{const t=r.team_coverage/r.min_team_size;return t>=1?"check-circle":t>=.7?"exclamation-triangle":"x-circle"},G=r=>{const t=r.team_coverage/r.min_team_size;return t>=1?"text-green-600 dark:text-green-400":t>=.7?"text-yellow-600 dark:text-yellow-400":"text-red-600 dark:text-red-400"},Q=r=>{const t=r.team_coverage/r.min_team_size;return t>=1?"Copertura Completa":t>=.7?"Copertura Parziale":"Gap Critico"};return X(()=>{V()}),(r,t)=>(s(),i("div",Z,[e("div",ee,[e("div",te,[e("div",ae,[t[17]||(t[17]=e("div",{class:"flex items-center"},[e("h1",{class:"text-2xl font-semibold text-gray-900 dark:text-white"},"Competenze Core")],-1)),e("div",re,[e("button",{onClick:t[0]||(t[0]=a=>w.value=!0),class:"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center space-x-2"},[b(g,{name:"sparkles",class:"h-4 w-4"}),t[15]||(t[15]=e("span",null,"Genera con AI",-1))]),e("button",{onClick:t[1]||(t[1]=a=>f.value=!0),class:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"}," + Nuova Competenza "),e("button",{onClick:V,disabled:p.value,class:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 disabled:opacity-50 transition-colors"},[p.value?(s(),q(g,{key:0,name:"arrow-path",class:"animate-spin h-4 w-4 mr-2 inline"})):y("",!0),t[16]||(t[16]=E(" Aggiorna "))],8,se)])])])]),e("div",le,[e("div",oe,[e("div",ie,[e("div",de,[e("div",ne,[b(g,{name:"academic-cap",class:"h-8 w-8 text-blue-600"})]),e("div",ge,[e("div",ue,l(c.value.length),1),t[18]||(t[18]=e("div",{class:"text-sm text-gray-600 dark:text-gray-400"},"Competenze Core",-1))])])]),e("div",ce,[e("div",xe,[e("div",me,[b(g,{name:"users",class:"h-8 w-8 text-green-600"})]),e("div",be,[e("div",ye,l(F.value),1),t[19]||(t[19]=e("div",{class:"text-sm text-gray-600 dark:text-gray-400"},"Copertura Media Team",-1))])])]),e("div",ve,[e("div",pe,[e("div",ke,[b(g,{name:"chart-bar",class:"h-8 w-8 text-purple-600"})]),e("div",fe,[e("div",he,l(P.value.toFixed(1)),1),t[20]||(t[20]=e("div",{class:"text-sm text-gray-600 dark:text-gray-400"},"Proficiency Media",-1))])])]),e("div",_e,[e("div",we,[e("div",Ce,[b(g,{name:"exclamation-triangle",class:"h-8 w-8 text-yellow-600"})]),e("div",ze,[e("div",qe,l($.value),1),t[21]||(t[21]=e("div",{class:"text-sm text-gray-600 dark:text-gray-400"},"Gap Identificati",-1))])])])]),e("div",Ve,[e("div",Me,[e("div",null,[t[23]||(t[23]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Categoria",-1)),n(e("select",{"onUpdate:modelValue":t[2]||(t[2]=a=>d.value.category=a),class:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},t[22]||(t[22]=[e("option",{value:""},"Tutte le categorie",-1),e("option",{value:"technical"},"Tecniche",-1),e("option",{value:"business"},"Business",-1),e("option",{value:"soft_skills"},"Soft Skills",-1)]),512),[[S,d.value.category]])]),e("div",null,[t[25]||(t[25]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Copertura Team",-1)),n(e("select",{"onUpdate:modelValue":t[3]||(t[3]=a=>d.value.coverage=a),class:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},t[24]||(t[24]=[e("option",{value:""},"Tutte",-1),e("option",{value:"high"},"Alta (≥5 persone)",-1),e("option",{value:"medium"},"Media (3-4 persone)",-1),e("option",{value:"low"},"Bassa (<3 persone)",-1)]),512),[[S,d.value.coverage]])]),e("div",null,[t[26]||(t[26]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Ricerca",-1)),n(e("input",{"onUpdate:modelValue":t[4]||(t[4]=a=>d.value.search=a),type:"text",placeholder:"Cerca competenze...",class:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},null,512),[[m,d.value.search]])])])]),p.value?(s(),i("div",Se,t[27]||(t[27]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1)]))):j.value.length===0?(s(),i("div",Ae,[b(g,{name:"academic-cap",class:"h-12 w-12 text-gray-400 mx-auto mb-4"}),t[28]||(t[28]=e("p",{class:"text-gray-500 dark:text-gray-400"},"Nessuna competenza trovata con i filtri selezionati.",-1))])):(s(),i("div",Te,[(s(!0),i(A,null,T(j.value,a=>{var h,N;return s(),i("div",{key:a.id,class:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6"},[e("div",Ue,[e("div",Be,[e("h3",je,l(a.name),1),e("p",De,l(a.description),1),e("span",{class:U([J(a.category),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},l(a.category),3)]),e("div",Ge,[e("div",Ne,[e("div",Ee,l(a.team_coverage),1),t[29]||(t[29]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"Copertura Team",-1))])])]),e("div",Ie,[e("div",Le,[t[30]||(t[30]=e("span",{class:"text-sm font-medium text-gray-700 dark:text-gray-300"},"Copertura vs Target",-1)),e("span",Fe,l(a.team_coverage)+"/"+l(a.min_team_size),1)]),e("div",Pe,[e("div",{class:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:Y({width:Math.min(a.team_coverage/a.min_team_size*100,100)+"%"})},null,4)])]),(h=a.skills)!=null&&h.length?(s(),i("div",$e,[t[31]||(t[31]=e("h4",{class:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Skills Correlate",-1)),e("div",He,[(s(!0),i(A,null,T(a.skills.slice(0,5),v=>(s(),i("span",{key:v.id,class:"inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300"},l(v.name),1))),128)),a.skills.length>5?(s(),i("span",Re," +"+l(a.skills.length-5)+" altre ",1)):y("",!0)])])):y("",!0),a.business_value?(s(),i("div",Oe,[t[32]||(t[32]=e("h4",{class:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Valore Business",-1)),e("p",We,l(a.business_value),1)])):y("",!0),(N=a.target_markets)!=null&&N.length?(s(),i("div",Je,[t[33]||(t[33]=e("h4",{class:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Mercati Target",-1)),e("div",Ke,[(s(!0),i(A,null,T(a.target_markets,v=>(s(),i("span",{key:v,class:"inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-300"},l(v),1))),128))])])):y("",!0),e("div",Qe,[e("div",Xe,[b(g,{name:K(a),class:U([G(a),"h-4 w-4"])},null,8,["name","class"]),e("span",{class:U(["text-sm",G(a)])},l(Q(a)),3)]),e("div",Ye,[e("button",{onClick:v=>R(a),class:"text-blue-600 hover:text-blue-800 text-sm font-medium"}," Modifica ",8,Ze),e("button",{onClick:v=>O(a),class:"text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200 text-sm font-medium"}," Dettagli ",8,et)])])])}),128))]))]),w.value?(s(),i("div",tt,[e("div",at,[e("div",rt,[e("div",st,[t[34]||(t[34]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Genera Competenza Core con AI ",-1)),e("button",{onClick:D,class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},[b(g,{name:"x-mark",class:"h-6 w-6"})])]),e("form",{onSubmit:I(W,["prevent"]),class:"space-y-6"},[e("div",null,[t[35]||(t[35]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Area di Competenza",-1)),n(e("input",{"onUpdate:modelValue":t[5]||(t[5]=a=>x.value.competency_area=a),type:"text",required:"",placeholder:"es. Machine Learning, DevOps, Data Analytics...",class:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},null,512),[[m,x.value.competency_area]])]),e("div",null,[t[36]||(t[36]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Contesto Business",-1)),n(e("textarea",{"onUpdate:modelValue":t[6]||(t[6]=a=>x.value.business_context=a),rows:"3",required:"",placeholder:"Descrivi il contesto aziendale e perché questa competenza è importante...",class:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},null,512),[[m,x.value.business_context]])]),e("div",null,[t[37]||(t[37]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Mercato Target",-1)),n(e("input",{"onUpdate:modelValue":t[7]||(t[7]=a=>x.value.target_market=a),type:"text",placeholder:"es. Fintech, E-commerce, Healthcare...",class:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},null,512),[[m,x.value.target_market]])]),e("div",null,[t[38]||(t[38]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Requisiti Specifici",-1)),n(e("textarea",{"onUpdate:modelValue":t[8]||(t[8]=a=>x.value.specific_requirements=a),rows:"3",placeholder:"Eventuali requisiti specifici, certificazioni, tecnologie particolari...",class:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},null,512),[[m,x.value.specific_requirements]])]),e("div",lt,[e("button",{type:"button",onClick:D,class:"px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-200 dark:bg-gray-600 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-500"}," Annulla "),e("button",{type:"submit",disabled:k.value,class:"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 flex items-center space-x-2"},[k.value?(s(),q(g,{key:0,name:"arrow-path",class:"animate-spin h-4 w-4"})):(s(),q(g,{key:1,name:"sparkles",class:"h-4 w-4"})),e("span",null,l(k.value?"Generando...":"Genera con AI"),1)],8,ot)])],32)])])])):y("",!0),f.value?(s(),i("div",it,[e("div",dt,[e("div",nt,[e("div",gt,[e("h3",ut,l(C.value?"Modifica Competenza Core":"Nuova Competenza Core"),1),e("button",{onClick:M,class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},[b(g,{name:"x-mark",class:"h-6 w-6"})])]),e("form",{onSubmit:I(H,["prevent"]),class:"space-y-4"},[e("div",ct,[e("div",null,[t[39]||(t[39]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Nome",-1)),n(e("input",{"onUpdate:modelValue":t[9]||(t[9]=a=>o.value.name=a),type:"text",required:"",class:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},null,512),[[m,o.value.name]])]),e("div",null,[t[41]||(t[41]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Categoria",-1)),n(e("select",{"onUpdate:modelValue":t[10]||(t[10]=a=>o.value.category=a),required:"",class:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},t[40]||(t[40]=[e("option",{value:"technical"},"Tecniche",-1),e("option",{value:"business"},"Business",-1),e("option",{value:"soft_skills"},"Soft Skills",-1)]),512),[[S,o.value.category]])])]),e("div",null,[t[42]||(t[42]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Descrizione",-1)),n(e("textarea",{"onUpdate:modelValue":t[11]||(t[11]=a=>o.value.description=a),rows:"3",class:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},null,512),[[m,o.value.description]])]),e("div",xt,[e("div",null,[t[43]||(t[43]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Team Size Minimo",-1)),n(e("input",{"onUpdate:modelValue":t[12]||(t[12]=a=>o.value.min_team_size=a),type:"number",min:"1",required:"",class:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},null,512),[[m,o.value.min_team_size,void 0,{number:!0}]])]),e("div",null,[t[44]||(t[44]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Proficiency Richiesta",-1)),n(e("input",{"onUpdate:modelValue":t[13]||(t[13]=a=>o.value.avg_proficiency_required=a),type:"number",min:"1",max:"5",step:"0.1",required:"",class:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},null,512),[[m,o.value.avg_proficiency_required,void 0,{number:!0}]])])]),e("div",null,[t[45]||(t[45]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Valore Business",-1)),n(e("textarea",{"onUpdate:modelValue":t[14]||(t[14]=a=>o.value.business_value=a),rows:"2",class:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},null,512),[[m,o.value.business_value]])]),e("div",mt,[e("button",{type:"button",onClick:M,class:"px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-200 dark:bg-gray-600 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-500"}," Annulla "),e("button",{type:"submit",disabled:_.value,class:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"},[_.value?(s(),q(g,{key:0,name:"arrow-path",class:"animate-spin h-4 w-4 mr-2 inline"})):y("",!0),E(" "+l(C.value?"Aggiorna":"Crea"),1)],8,bt)])],32)])])])):y("",!0)]))}};export{kt as default};
