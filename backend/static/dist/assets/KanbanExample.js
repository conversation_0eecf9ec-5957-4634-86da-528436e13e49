import{r as l,b as d,e as c,l as t,B as u,S as C,P as h,F as f,q as w,j as g,t as b}from"./vendor.js";import{_ as I}from"./PageHeader.js";import{_ as D}from"./KanbanView.js";import"./app.js";const _={class:"p-6"},S={class:"space-y-8"},M={class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"},z={class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"},V={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},B={class:"space-y-4"},F={class:"flex items-center"},K={class:"space-y-3"},R={class:"grid grid-cols-2 gap-3"},T={class:"text-sm font-medium text-gray-900 dark:text-white"},U={class:"text-lg font-bold text-primary-600"},N={__name:"KanbanExample",setup(E){const i=l("default"),m=l(!0),s=l([{key:"todo",name:"Da Fare",color:"bg-gray-100"},{key:"progress",name:"In Corso",color:"bg-blue-100"},{key:"review",name:"In Revisione",color:"bg-yellow-100"},{key:"done",name:"Completato",color:"bg-green-100"}]),n=l([{id:1,title:"Implementare Login",description:"Sistema di autenticazione utenti con JWT",column:"todo",priority:"high",assignee:"Mario Rossi",dueDate:"2024-02-15",tags:["Frontend","Security"]},{id:2,title:"Database Schema",description:"Definire schema database principale",column:"todo",priority:"medium",assignee:"Giulia Bianchi",dueDate:"2024-02-20",tags:["Backend","Database"]},{id:3,title:"API Endpoints",description:"Creare endpoint REST per CRUD operazioni",column:"progress",priority:"high",assignee:"Luca Verdi",dueDate:"2024-02-18",tags:["Backend","API"]},{id:4,title:"UI Components",description:"Sviluppare componenti riusabili",column:"progress",priority:"medium",assignee:"Anna Neri",dueDate:"2024-02-25",tags:["Frontend","UI/UX"]},{id:5,title:"Testing Setup",description:"Configurare ambiente di testing",column:"review",priority:"low",assignee:"Marco Blu",dueDate:"2024-02-22",tags:["Testing","DevOps"]},{id:6,title:"Documentation",description:"Documentazione API e componenti",column:"done",priority:"low",assignee:"Sara Rosa",dueDate:"2024-02-10",tags:["Documentation"]}]),p=()=>{const e={default:[{color:"bg-gray-100 dark:bg-gray-700",textColor:"text-gray-800 dark:text-gray-200"},{color:"bg-blue-100 dark:bg-blue-900",textColor:"text-blue-800 dark:text-blue-200"},{color:"bg-yellow-100 dark:bg-yellow-900",textColor:"text-yellow-800 dark:text-yellow-200"},{color:"bg-green-100 dark:bg-green-900",textColor:"text-green-800 dark:text-green-200"}],colorful:[{color:"bg-red-100 dark:bg-red-900",textColor:"text-red-800 dark:text-red-200"},{color:"bg-purple-100 dark:bg-purple-900",textColor:"text-purple-800 dark:text-purple-200"},{color:"bg-orange-100 dark:bg-orange-900",textColor:"text-orange-800 dark:text-orange-200"},{color:"bg-emerald-100 dark:bg-emerald-900",textColor:"text-emerald-800 dark:text-emerald-200"}],minimal:[{color:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600",textColor:"text-gray-900 dark:text-gray-100"},{color:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600",textColor:"text-gray-900 dark:text-gray-100"},{color:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600",textColor:"text-gray-900 dark:text-gray-100"},{color:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600",textColor:"text-gray-900 dark:text-gray-100"}]}[i.value];s.value.forEach((o,a)=>{e[a]&&(o.color=e[a].color,o.textColor=e[a].textColor)})},y=r=>n.value.filter(e=>e.column===r).length,k=(r,e,o)=>{console.log("Item spostato:",r.title,"da",e,"a",o);const a=n.value.findIndex(v=>v.id===r.id);a!==-1&&(n.value[a].column=o)},x=r=>{console.log("Item cliccato:",r.title),alert(`Dettagli: ${r.title}
${r.description}`)};return(r,e)=>(g(),d("div",_,[c(I,{title:"KanbanView - Design System",subtitle:"Componente per visualizzazione Kanban con drag & drop",icon:"view-columns","icon-color":"text-blue-600"}),t("div",S,[t("div",M,[e[2]||(e[2]=t("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white mb-4"}," Esempio Kanban - Gestione Progetti ",-1)),c(D,{stages:s.value,items:n.value,"stage-key":"column",onItemMoved:k,onItemClicked:x},null,8,["stages","items"])]),t("div",z,[e[7]||(e[7]=t("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white mb-4"}," Configurazione ",-1)),t("div",V,[t("div",B,[t("div",null,[e[4]||(e[4]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Tema Colonne ",-1)),u(t("select",{"onUpdate:modelValue":e[0]||(e[0]=o=>i.value=o),onChange:p,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"},e[3]||(e[3]=[t("option",{value:"default"},"Default",-1),t("option",{value:"colorful"},"Colorato",-1),t("option",{value:"minimal"},"Minimale",-1)]),544),[[C,i.value]])]),t("div",null,[t("label",F,[u(t("input",{"onUpdate:modelValue":e[1]||(e[1]=o=>m.value=o),type:"checkbox",class:"rounded border-gray-300 text-primary-600 focus:ring-primary-500"},null,512),[[h,m.value]]),e[5]||(e[5]=t("span",{class:"ml-2 text-sm text-gray-700 dark:text-gray-300"}," Mostra statistiche colonne ",-1))])])]),t("div",K,[e[6]||(e[6]=t("h4",{class:"font-medium text-gray-900 dark:text-white"},"Statistiche Correnti",-1)),t("div",R,[(g(!0),d(f,null,w(s.value,o=>(g(),d("div",{key:o.key,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-3"},[t("div",T,b(o.name),1),t("div",U,b(y(o.key)),1)]))),128))])])])]),e[8]||(e[8]=t("div",{class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"},[t("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white mb-4"}," Utilizzo del Componente "),t("pre",{class:"bg-gray-50 dark:bg-gray-900 rounded-lg p-4 overflow-x-auto text-sm"},[t("code",null,`<template>
  <KanbanView
    :stages="kanbanColumns"
    :items="kanbanItems"
    stage-key="column"
    @item-moved="handleItemMoved"
    @item-clicked="handleItemClicked"
  />
</template>

<script setup>
import KanbanView from '@/components/design-system/KanbanView.vue'

const kanbanColumns = [
  { key: 'todo', name: 'Da Fare', color: 'bg-gray-100' },
  { key: 'progress', name: 'In Corso', color: 'bg-blue-100' },
  { key: 'review', name: 'In Revisione', color: 'bg-yellow-100' },
  { key: 'done', name: 'Completato', color: 'bg-green-100' }
]

const kanbanItems = [
  {
    id: 1,
    title: 'Implementare Login',
    description: 'Sistema di autenticazione utenti',
    column: 'todo',
    priority: 'high',
    assignee: 'Mario Rossi'
  }
]

const handleItemMoved = (item, fromColumn, toColumn) => {
  console.log('Item moved:', item, fromColumn, toColumn)
}
<\/script>`)])],-1))])]))}};export{N as default};
