import{b as d,j as c,l as e,g as u,t as a,A as i,e as p,v}from"./vendor.js";import{_ as b,H as x}from"./app.js";const w={class:"flex justify-between items-start mb-2"},h={class:"font-medium text-gray-900 text-sm line-clamp-2"},k={class:"relative"},$={key:0,class:"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200"},C={class:"py-1"},D={class:"flex items-center space-x-2 mb-2"},z={class:"text-sm text-gray-600"},N={key:0,class:"flex items-center space-x-2 mb-2"},I={class:"text-sm font-medium text-gray-900"},M={class:"flex justify-between items-center text-xs text-gray-500"},V={key:1,class:"mt-2"},j={__name:"ProposalCard",props:{proposal:{type:Object,required:!0},showMenu:{type:Boolean,default:!1}},emits:["click","toggle-menu","view","edit","duplicate","delete"],setup(o,{emit:B}){const f=s=>new Intl.NumberFormat("it-IT").format(s||0),y=s=>s?new Date(s).toLocaleDateString("it-IT"):"N/A",g=s=>{if(!s.expiry_date||s.status==="accepted"||s.status==="rejected")return!1;const n=new Date(s.expiry_date)-new Date,r=Math.ceil(n/(1e3*60*60*24));return r<=7&&r>=0};return(s,t)=>{var m,n,r;return c(),d("div",{class:"bg-white rounded-lg border border-gray-200 p-4 cursor-pointer hover:shadow-md transition-shadow",onClick:t[5]||(t[5]=l=>s.$emit("click",o.proposal))},[e("div",w,[e("h4",h,a(o.proposal.title),1),e("div",k,[e("button",{onClick:t[0]||(t[0]=i(l=>s.$emit("toggle-menu",o.proposal.id),["stop"])),class:"text-gray-400 hover:text-gray-600"},[p(x,{name:"ellipsis-vertical",class:"w-5 h-5"})]),o.showMenu?(c(),d("div",$,[e("div",C,[e("button",{onClick:t[1]||(t[1]=i(l=>s.$emit("view",o.proposal.id),["stop"])),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"}," Visualizza "),e("button",{onClick:t[2]||(t[2]=i(l=>s.$emit("edit",o.proposal.id),["stop"])),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"}," Modifica "),e("button",{onClick:t[3]||(t[3]=i(l=>s.$emit("duplicate",o.proposal.id),["stop"])),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"}," Duplica "),t[6]||(t[6]=e("div",{class:"border-t border-gray-100"},null,-1)),e("button",{onClick:t[4]||(t[4]=i(l=>s.$emit("delete",o.proposal.id),["stop"])),class:"block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50"}," Elimina ")])])):u("",!0)])]),e("div",D,[p(x,{name:"building-office-2",class:"w-4 h-4 text-gray-400"}),e("span",z,a(((m=o.proposal.client)==null?void 0:m.name)||"N/A"),1)]),o.proposal.value?(c(),d("div",N,[p(x,{name:"currency-euro",class:"w-4 h-4 text-gray-400"}),e("span",I," €"+a(f(o.proposal.value)),1)])):u("",!0),e("div",M,[e("span",null,a((n=o.proposal.creator)==null?void 0:n.first_name)+" "+a((r=o.proposal.creator)==null?void 0:r.last_name),1),e("span",null,a(y(o.proposal.created_at)),1)]),g(o.proposal)?(c(),d("div",V,t[7]||(t[7]=[e("span",{class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800"},[e("svg",{class:"w-3 h-3 mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z","clip-rule":"evenodd"})]),v(" In scadenza ")],-1)]))):u("",!0)])}}},E=b(j,[["__scopeId","data-v-9b267b84"]]);export{E as P};
