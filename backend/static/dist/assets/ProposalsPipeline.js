import{r as a,c as i,w as L,o as q,b,e as c,f as A,g as H,p as g,l as k,s as R,h as G,j as f,v as O}from"./vendor.js";import{u as Q}from"./crm.js";import{u as U}from"./useToast.js";import{u as J}from"./useFormatters.js";import{_ as K,H as W}from"./app.js";import{_ as X}from"./PageHeader.js";import{_ as Y}from"./FilterBar.js";import{_ as Z}from"./StatsGrid.js";import{_ as ee}from"./KanbanView.js";import{P as te}from"./ProposalCard.js";import"./formatters.js";const oe={class:"proposals-pipeline"},ae={key:0,class:"bg-white rounded-lg shadow p-8"},se={__name:"ProposalsPipeline",setup(re){const h=R(),s=Q(),{showToast:u}=U(),{formatCurrency:x}=J(),v=a(!1),r=a(""),l=a(""),n=a(""),d=a({}),T=a([{type:"search",key:"search",label:"Cerca",placeholder:"Titolo proposta...",value:r},{type:"select",key:"client",label:"Cliente",value:l,options:i(()=>[{value:"",label:"Tutti i clienti"},...$.value.map(e=>({value:e.id,label:e.name}))])},{type:"select",key:"creator",label:"Creatore",value:n,options:i(()=>[{value:"",label:"Tutti"},...y.value.map(e=>({value:e.id,label:`${e.first_name} ${e.last_name}`}))])}]),y=a([]),w=[{status:"draft",name:"Bozza",color:"bg-gray-400"},{status:"sent",name:"Inviata",color:"bg-blue-400"},{status:"negotiating",name:"Negoziazione",color:"bg-yellow-400"},{status:"accepted",name:"Accettata",color:"bg-green-400"},{status:"rejected",name:"Rifiutata",color:"bg-red-400"}],C=i(()=>s.proposals),$=i(()=>s.clients),E=i(()=>w.map(e=>({label:e.name,value:z(e.status),subValue:`€${x(N(e.status))}`,color:e.color,icon:e.status==="accepted"?"check-circle":e.status==="rejected"?"x-circle":e.status==="negotiating"?"chat-bubble-left-right":"document-text"}))),_=i(()=>{let e=C.value;if(r.value){const t=r.value.toLowerCase();e=e.filter(o=>o.title.toLowerCase().includes(t)||o.description&&o.description.toLowerCase().includes(t))}return l.value&&(e=e.filter(t=>t.client_id===parseInt(l.value))),n.value&&(e=e.filter(t=>t.created_by===parseInt(n.value))),e}),S=async()=>{try{v.value=!0,await Promise.all([s.fetchProposals(),s.fetchClients(),B()])}catch(e){console.error("Error loading data:",e),u("Errore nel caricamento dei dati","error")}finally{v.value=!1}},B=async()=>{var e;try{const t=await fetch("/api/personnel");if(t.ok){const o=await t.json();y.value=((e=o.data)==null?void 0:e.personnel)||[]}}catch(t){console.error("Error loading users:",t)}},z=e=>_.value.filter(t=>t.status===e).length,N=e=>_.value.filter(t=>t.status===e).reduce((t,o)=>t+(o.value||0),0),V=(e,t)=>{switch(e){case"search":r.value=t;break;case"client":l.value=t;break;case"creator":n.value=t;break}},j=()=>{r.value="",l.value="",n.value=""},P=e=>{h.push(`/app/crm/proposals/${e}`)},F=e=>{h.push(`/app/crm/proposals/${e}/edit`)},I=async e=>{try{const t=C.value.find(p=>p.id===e);if(!t)return;const o={client_id:t.client_id,title:`${t.title} (Copia)`,description:t.description,value:t.value,status:"draft"};await s.createProposal(o),u("Proposta duplicata con successo","success"),m()}catch(t){console.error("Error duplicating proposal:",t),u("Errore nella duplicazione della proposta","error")}},M=async e=>{if(confirm("Sei sicuro di voler eliminare questa proposta?"))try{await s.deleteProposal(e),u("Proposta eliminata con successo","success"),m()}catch(t){console.error("Error deleting proposal:",t),u("Errore nell'eliminazione della proposta","error")}},D=e=>{m(),d.value[e]=!0},m=()=>{d.value={}};return L([r,l,n],()=>{clearTimeout(window.proposalSearchTimeout),window.proposalSearchTimeout=setTimeout(()=>{},300)}),q(()=>{S()}),(e,t)=>{const o=G("router-link");return f(),b("div",oe,[c(X,{title:"Pipeline Proposte",description:"Gestione proposte commerciali con vista kanban"},{actions:g(()=>[c(o,{to:"/app/crm/proposals/new",class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"},{default:g(()=>[c(W,{name:"plus",class:"w-4 h-4 mr-2"}),t[0]||(t[0]=O(" Nuova Proposta "))]),_:1,__:[0]})]),_:1}),c(Y,{filters:T.value,onFilterChange:V,onClearFilters:j},null,8,["filters"]),c(Z,{stats:E.value,class:"mb-6 grid-cols-2 sm:grid-cols-3 lg:grid-cols-5"},null,8,["stats"]),v.value?(f(),b("div",ae,t[1]||(t[1]=[k("div",{class:"flex justify-center"},[k("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})],-1)]))):(f(),A(ee,{key:1,title:"Pipeline Board",stages:w,items:_.value,"stage-key":"status","empty-message":"Nessuna proposta"},{item:g(({item:p})=>[c(te,{proposal:p,"show-menu":d.value[p.id],onClick:le=>P(p.id),onToggleMenu:D,onView:P,onEdit:F,onDuplicate:I,onDelete:M},null,8,["proposal","show-menu","onClick"])]),_:1},8,["items"])),Object.keys(d.value).length>0?(f(),b("div",{key:2,onClick:m,class:"fixed inset-0 z-5"})):H("",!0)])}}},ge=K(se,[["__scopeId","data-v-e794470f"]]);export{ge as default};
