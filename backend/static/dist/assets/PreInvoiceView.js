import{r as p,o as P,b as i,g as n,l as t,e as l,p as x,t as s,h as $,F as E,q as V,v as u,f as F,B as N,S as B,u as R,s as A,j as d,n as L}from"./vendor.js";import{H as m,c as S}from"./app.js";import{u as M}from"./useToast.js";import{_ as q}from"./PageHeader.js";const H={class:"py-6"},U={key:0,class:"flex items-center justify-center py-12"},G={key:1},O={class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},J={class:"lg:col-span-2 space-y-6"},K={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Q={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},W={class:"mt-1 text-sm text-gray-900 dark:text-white"},X={class:"mt-1 text-sm text-gray-900 dark:text-white"},Y={class:"mt-1 text-sm text-gray-900 dark:text-white"},Z={class:"mt-1 text-sm text-gray-900 dark:text-white"},tt={key:0},et={class:"mt-1 text-sm text-gray-900 dark:text-white"},at={class:"mt-1 text-sm text-gray-900 dark:text-white"},st={key:0,class:"mt-4"},rt={class:"mt-1 text-sm text-gray-900 dark:text-white"},ot={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},dt={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},it={class:"text-lg font-medium text-gray-900 dark:text-white"},nt={class:"overflow-x-auto"},lt={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},ut={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},gt={class:"px-6 py-4 whitespace-nowrap"},mt={class:"text-sm font-medium text-gray-900 dark:text-white"},xt={key:0,class:"text-xs text-gray-500 dark:text-gray-400"},ct={class:"px-6 py-4"},yt={class:"text-sm text-gray-900 dark:text-white"},pt={class:"px-6 py-4 whitespace-nowrap"},ft={class:"text-sm text-gray-900 dark:text-white"},vt={class:"text-xs text-gray-500 dark:text-gray-400"},_t={class:"px-6 py-4 whitespace-nowrap"},bt={class:"text-sm text-gray-900 dark:text-white"},kt={class:"px-6 py-4 whitespace-nowrap"},ht={class:"text-sm font-medium text-gray-900 dark:text-white"},wt={key:0,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Ct={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},It={key:0},Ft={class:"mt-1 text-sm text-gray-900 dark:text-white"},St={key:1},Tt={class:"mt-1 text-sm text-gray-900 dark:text-white"},Dt={key:2,class:"md:col-span-2"},zt={class:"mt-1"},jt=["href"],Pt={class:"lg:col-span-1 space-y-6"},$t={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Et={class:"space-y-3"},Vt={class:"flex justify-between"},Nt={class:"text-sm font-medium text-gray-900 dark:text-white"},Bt={class:"flex justify-between"},Rt={class:"text-sm text-gray-600 dark:text-gray-400"},At={class:"text-sm font-medium text-gray-900 dark:text-white"},Lt={class:"flex justify-between"},Mt={class:"text-sm text-gray-600 dark:text-gray-400"},qt={class:"text-sm font-medium text-red-600 dark:text-red-400"},Ht={class:"flex justify-between border-t border-gray-200 dark:border-gray-700 pt-3"},Ut={class:"text-base font-bold text-gray-900 dark:text-white"},Gt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Ot={class:"space-y-3"},Jt=["disabled"],Kt=["disabled"],Qt={key:2,class:"text-sm text-yellow-600 dark:text-yellow-400 bg-yellow-50 dark:bg-yellow-900/20 p-3 rounded-md"},Wt={class:"flex"},Xt={class:"mt-1"},Yt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Zt={class:"space-y-3 text-sm"},te={class:"text-gray-900 dark:text-white"},ee={class:"text-gray-900 dark:text-white"},ae={class:"text-gray-900 dark:text-white"},se={key:2,class:"text-center py-12"},re={class:"mt-6"},oe={key:3,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},de={class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800"},ie={class:"mt-3 text-center"},ne={class:"mt-4"},le={class:"flex justify-center space-x-3 mt-6"},ue=["disabled"],pe={__name:"PreInvoiceView",setup(ge){const T=R();A();const{showToast:_}=M(),b=p(!0),a=p(null),f=p(!1),v=p(""),y=p(!1),c=o=>new Intl.NumberFormat("it-IT",{minimumFractionDigits:0,maximumFractionDigits:0}).format(o||0),k=o=>new Date(o).toLocaleDateString("it-IT"),h=o=>new Date(o).toLocaleDateString("it-IT",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),D=o=>{const e={draft:"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300",ready:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",sent_external:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",invoiced:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"};return e[o]||e.draft},z=async()=>{var o;b.value=!0;try{const e=await S.get(`/api/pre-invoices/${T.params.id}`);e.data.success&&(a.value=e.data.data,v.value=a.value.status)}catch(e){console.error("Errore nel caricamento pre-fattura:",e),((o=e.response)==null?void 0:o.status)===404?a.value=null:_({type:"error",title:"Errore",message:"Impossibile caricare la pre-fattura",duration:4e3})}finally{b.value=!1}},w=async o=>{var e,g;y.value=!0;try{const r=await S.put(`/api/pre-invoices/${a.value.id}/status`,{status:o});r.data.success&&(a.value.status=r.data.data.status,a.value.display_status=r.data.data.display_status,a.value.updated_at=r.data.data.updated_at,_({type:"success",title:"Successo",message:r.data.message,duration:3e3}))}catch(r){console.error("Errore nell'aggiornamento stato:",r),_({type:"error",title:"Errore",message:((g=(e=r.response)==null?void 0:e.data)==null?void 0:g.message)||"Impossibile aggiornare lo stato",duration:4e3})}finally{y.value=!1}},j=async()=>{await w(v.value),f.value=!1};return P(()=>{z()}),(o,e)=>{const g=$("router-link");return d(),i("div",H,[b.value?(d(),i("div",U,e[5]||(e[5]=[t("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1),t("span",{class:"ml-2 text-gray-600 dark:text-gray-400"},"Caricamento pre-fattura...",-1)]))):a.value?(d(),i("div",G,[l(q,{title:a.value.pre_invoice_number,subtitle:`Pre-fattura per ${a.value.client.name}`,icon:"document-text","icon-color":"text-purple-600"},{"title-suffix":x(()=>[t("span",{class:L(["ml-3 inline-flex px-3 py-1 text-sm font-semibold rounded-full",D(a.value.status)])},s(a.value.display_status),3)]),actions:x(()=>[a.value.can_edit?(d(),i("button",{key:0,onClick:e[0]||(e[0]=r=>f.value=!0),class:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[l(m,{name:"pencil-square",class:"w-4 h-4 mr-2"}),e[6]||(e[6]=u(" Cambia Stato "))])):n("",!0),l(g,{to:"/app/invoicing/pre-invoices",class:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},{default:x(()=>[l(m,{name:"arrow-left",class:"w-4 h-4 mr-2"}),e[7]||(e[7]=u(" Torna alla lista "))]),_:1,__:[7]})]),_:1},8,["title","subtitle"]),t("div",O,[t("div",J,[t("div",K,[e[15]||(e[15]=t("h2",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Informazioni Generali ",-1)),t("dl",Q,[t("div",null,[e[8]||(e[8]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Numero",-1)),t("dd",W,s(a.value.pre_invoice_number),1)]),t("div",null,[e[9]||(e[9]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Cliente",-1)),t("dd",X,s(a.value.client.name),1)]),t("div",null,[e[10]||(e[10]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Periodo fatturazione",-1)),t("dd",Y,s(k(a.value.billing_period_start))+" - "+s(k(a.value.billing_period_end)),1)]),t("div",null,[e[11]||(e[11]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Data generazione",-1)),t("dd",Z,s(k(a.value.generated_date)),1)]),a.value.contract?(d(),i("div",tt,[e[12]||(e[12]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Contratto",-1)),t("dd",et,[l(g,{to:`/app/crm/contracts/${a.value.contract.id}`,class:"text-primary-600 hover:text-primary-800 dark:text-primary-400"},{default:x(()=>[u(s(a.value.contract.contract_number),1)]),_:1},8,["to"])])])):n("",!0),t("div",null,[e[13]||(e[13]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Creato da",-1)),t("dd",at,s(a.value.creator.name),1)])]),a.value.notes?(d(),i("div",st,[e[14]||(e[14]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Note",-1)),t("dd",rt,s(a.value.notes),1)])):n("",!0)]),t("div",ot,[t("div",dt,[t("h2",it," Righe Pre-fattura ("+s(a.value.lines.length)+") ",1)]),t("div",nt,[t("table",lt,[e[16]||(e[16]=t("thead",{class:"bg-gray-50 dark:bg-gray-700"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Progetto "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Descrizione "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Ore "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Tariffa "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Importo ")])],-1)),t("tbody",ut,[(d(!0),i(E,null,V(a.value.lines,r=>{var C,I;return d(),i("tr",{key:r.id},[t("td",gt,[t("div",mt,s(((C=r.project)==null?void 0:C.name)||"Progetto eliminato"),1),(I=r.project)!=null&&I.contract?(d(),i("div",xt,s(r.project.contract.contract_number),1)):n("",!0)]),t("td",ct,[t("div",yt,s(r.description),1)]),t("td",pt,[t("div",ft,s(r.total_hours)+"h",1),t("div",vt,s(r.timesheet_entries_count)+" entries ",1)]),t("td",_t,[t("div",bt," €"+s(c(r.hourly_rate))+"/h ",1)]),t("td",kt,[t("div",ht," €"+s(c(r.total_amount)),1)])])}),128))])])])]),a.value.external_invoice_id||a.value.external_status?(d(),i("div",wt,[e[20]||(e[20]=t("h2",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Integrazione Esterna ",-1)),t("dl",Ct,[a.value.external_invoice_id?(d(),i("div",It,[e[17]||(e[17]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"ID Fattura Esterna",-1)),t("dd",Ft,s(a.value.external_invoice_id),1)])):n("",!0),a.value.external_status?(d(),i("div",St,[e[18]||(e[18]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Stato Esterno",-1)),t("dd",Tt,s(a.value.external_status),1)])):n("",!0),a.value.external_pdf_url?(d(),i("div",Dt,[e[19]||(e[19]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"PDF Fattura",-1)),t("dd",zt,[t("a",{href:a.value.external_pdf_url,target:"_blank",class:"text-primary-600 hover:text-primary-800 dark:text-primary-400 text-sm"}," Scarica PDF fattura elettronica ",8,jt)])])):n("",!0)])])):n("",!0)]),t("div",Pt,[t("div",$t,[e[23]||(e[23]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Totali",-1)),t("dl",Et,[t("div",Vt,[e[21]||(e[21]=t("dt",{class:"text-sm text-gray-600 dark:text-gray-400"},"Subtotale",-1)),t("dd",Nt," €"+s(c(a.value.subtotal)),1)]),t("div",Bt,[t("dt",Rt," IVA ("+s(a.value.vat_rate)+"%) ",1),t("dd",At," €"+s(c(a.value.vat_amount)),1)]),t("div",Lt,[t("dt",Mt," Ritenuta ("+s(a.value.retention_rate)+"%) ",1),t("dd",qt," -€"+s(c(a.value.retention_amount)),1)]),t("div",Ht,[e[22]||(e[22]=t("dt",{class:"text-base font-medium text-gray-900 dark:text-white"},"Totale",-1)),t("dd",Ut," €"+s(c(a.value.total_amount)),1)])])]),t("div",Gt,[e[29]||(e[29]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Azioni",-1)),t("div",Ot,[a.value.status==="draft"?(d(),i("button",{key:0,onClick:e[1]||(e[1]=r=>w("ready")),disabled:y.value,class:"w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"},[l(m,{name:"check-circle",class:"w-4 h-4 mr-2"}),e[24]||(e[24]=u(" Marca come Pronta "))],8,Jt)):n("",!0),a.value.status==="ready"?(d(),i("button",{key:1,onClick:e[2]||(e[2]=(...r)=>o.sendToFattureInCloud&&o.sendToFattureInCloud(...r)),disabled:o.sendingToFIC||!o.isFattureInCloudConfigured,class:"w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"},[o.sendingToFIC?(d(),F(m,{key:0,name:"arrow-path",class:"animate-spin -ml-1 mr-2 h-4 w-4 text-white"})):(d(),F(m,{key:1,name:"paper-airplane",class:"w-4 h-4 mr-2"})),u(" "+s(o.sendingToFIC?"Invio in corso...":"Invia a FattureInCloud"),1)],8,Kt)):n("",!0),!o.isFattureInCloudConfigured&&a.value.status==="ready"?(d(),i("div",Qt,[t("div",Wt,[l(m,{name:"exclamation-circle",class:"w-5 h-5 mr-2 flex-shrink-0"}),t("div",null,[e[27]||(e[27]=t("p",{class:"font-medium"},"FattureInCloud non configurato",-1)),t("p",Xt,[l(g,{to:"/app/admin/settings",class:"underline"},{default:x(()=>e[25]||(e[25]=[u(" Configura l'integrazione ")])),_:1,__:[25]}),e[26]||(e[26]=u(" per abilitare l'invio automatico delle fatture. "))])])])])):n("",!0),l(g,{to:`/app/crm/clients/${a.value.client.id}`,class:"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},{default:x(()=>[l(m,{name:"user",class:"w-4 h-4 mr-2"}),e[28]||(e[28]=u(" Vedi Cliente "))]),_:1,__:[28]},8,["to"])])]),t("div",Yt,[e[33]||(e[33]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Informazioni",-1)),t("dl",Zt,[t("div",null,[e[30]||(e[30]=t("dt",{class:"text-gray-600 dark:text-gray-400"},"Creato",-1)),t("dd",te,s(h(a.value.created_at)),1)]),t("div",null,[e[31]||(e[31]=t("dt",{class:"text-gray-600 dark:text-gray-400"},"Ultima modifica",-1)),t("dd",ee,s(h(a.value.updated_at)),1)]),t("div",null,[e[32]||(e[32]=t("dt",{class:"text-gray-600 dark:text-gray-400"},"Righe",-1)),t("dd",ae,s(a.value.lines.length),1)])])])])])])):(d(),i("div",se,[l(m,{name:"exclamation-triangle",class:"mx-auto h-12 w-12 text-gray-400"}),e[35]||(e[35]=t("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Pre-fattura non trovata",-1)),e[36]||(e[36]=t("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," La pre-fattura richiesta non esiste o non hai i permessi per visualizzarla. ",-1)),t("div",re,[l(g,{to:"/app/invoicing/pre-invoices",class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},{default:x(()=>e[34]||(e[34]=[u(" Torna alla lista ")])),_:1,__:[34]})])])),f.value?(d(),i("div",oe,[t("div",de,[t("div",ie,[e[38]||(e[38]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Cambia Stato",-1)),t("div",ne,[N(t("select",{"onUpdate:modelValue":e[3]||(e[3]=r=>v.value=r),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},e[37]||(e[37]=[t("option",{value:"draft"},"Bozza",-1),t("option",{value:"ready"},"Pronta",-1),t("option",{value:"sent_external"},"Inviata esternamente",-1),t("option",{value:"invoiced"},"Fatturata",-1)]),512),[[B,v.value]])]),t("div",le,[t("button",{onClick:e[4]||(e[4]=r=>f.value=!1),class:"px-4 py-2 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-400 dark:hover:bg-gray-500"}," Annulla "),t("button",{onClick:j,disabled:y.value,class:"px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700 disabled:opacity-50"},s(y.value?"Aggiornamento...":"Conferma"),9,ue)])])])])):n("",!0)])}}};export{pe as default};
