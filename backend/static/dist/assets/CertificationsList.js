import{b as r,l as e,g as h,e as n,p as f,h as S,B as p,S as b,O as z,t as a,F as T,q as j,c as w,r as v,o as A,j as d,v as k,n as x,E as L}from"./vendor.js";import{u as M}from"./certifications.js";import{_ as R,H as U}from"./app.js";const q={name:"CertificationsList",components:{HeroIcon:U},setup(){const u=M(),t=w(()=>u.certifications),C=w(()=>u.loading),i=w(()=>u.error),y=v({status:"",category:"",expiring_soon:"",responsible_person_id:""}),m=v(!1),s=v(null),c=v(!1),g=async()=>{try{await u.fetchCertifications(y.value)}catch(l){console.error("Errore nel caricamento certificazioni:",l)}},o=()=>{g()},_=l=>l?new Date(l).toLocaleDateString("it-IT"):"N/A",D=l=>({active:"bg-green-100 text-green-800",expired:"bg-red-100 text-red-800",in_renewal:"bg-yellow-100 text-yellow-800",suspended:"bg-gray-100 text-gray-800"})[l]||"bg-gray-100 text-gray-800",F=l=>({active:"Attiva",expired:"Scaduta",in_renewal:"In Rinnovo",suspended:"Sospesa"})[l]||l,H=l=>l>=80?"text-green-600":l>=60?"text-yellow-600":"text-red-600",N=l=>l>=80?"bg-green-500":l>=60?"bg-yellow-500":"bg-red-500",V=l=>{s.value=l,m.value=!0},B=()=>{m.value=!1,s.value=null},E=async()=>{if(s.value)try{c.value=!0,await u.deleteCertification(s.value.id),m.value=!1,s.value=null}catch(l){console.error("Errore nell'eliminazione:",l)}finally{c.value=!1}};return A(()=>{g()}),{certifications:t,loading:C,error:i,filters:y,showDeleteModal:m,certificationToDelete:s,deleting:c,loadCertifications:g,applyFilters:o,formatDate:_,getStatusBadgeClass:D,getStatusLabel:F,getHealthScoreColor:H,getHealthScoreBarColor:N,confirmDelete:V,cancelDelete:B,deleteCertification:E}}},Q={class:"certifications-list"},G={class:"bg-white rounded-lg shadow-sm p-6 mb-6"},O={class:"flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6"},P={class:"mt-4 lg:mt-0"},J={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},K={class:"bg-white rounded-lg shadow-sm"},W={key:0,class:"flex justify-center py-12"},X={key:1,class:"p-6 text-center text-red-600"},Y={key:2,class:"p-12 text-center text-gray-500"},Z={key:3},$={class:"divide-y divide-gray-200"},ee={class:"hidden md:grid md:grid-cols-6 gap-4 items-center"},te={class:"font-semibold text-gray-900"},oe={class:"text-sm text-gray-600"},ie={class:"text-gray-900"},se={key:0,class:"flex items-center mt-1 text-orange-600"},le={class:"text-gray-900"},ne={key:0,class:"text-sm text-gray-600"},ae={class:"text-center"},re={class:"w-full bg-gray-200 rounded-full h-2 mt-1"},de={class:"flex items-center space-x-2"},ce=["onClick"],ue={class:"md:hidden"},ge={class:"flex justify-between items-start mb-3"},fe={class:"flex-1"},me={class:"font-semibold text-gray-900"},xe={class:"text-sm text-gray-600"},pe={class:"grid grid-cols-2 gap-4 text-sm"},be={class:"block font-medium"},ve={class:"flex justify-end space-x-2 mt-4"},ye=["onClick"],_e={key:0,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},he={class:"bg-white rounded-lg max-w-md w-full mx-4 p-6"},we={class:"flex items-center mb-4"},Ce={class:"text-gray-600 mb-6"},Se={class:"flex justify-end space-x-3"},ze=["disabled"],ke={key:0},De={key:1};function Fe(u,t,C,i,y,m){var g;const s=S("HeroIcon"),c=S("router-link");return d(),r("div",Q,[e("div",G,[e("div",O,[t[12]||(t[12]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900"},"Certificazioni"),e("p",{class:"text-gray-600 mt-1"},"Gestisci le certificazioni aziendali")],-1)),e("div",P,[n(c,{to:"/app/certifications/create",class:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center"},{default:f(()=>[n(s,{name:"plus",class:"h-5 w-5 mr-2"}),t[11]||(t[11]=k(" Nuova Certificazione "))]),_:1,__:[11]})])]),e("div",J,[e("div",null,[t[14]||(t[14]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Stato",-1)),p(e("select",{"onUpdate:modelValue":t[0]||(t[0]=o=>i.filters.status=o),onChange:t[1]||(t[1]=(...o)=>i.applyFilters&&i.applyFilters(...o)),class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},t[13]||(t[13]=[z('<option value="">Tutti</option><option value="active">Attive</option><option value="expired">Scadute</option><option value="in_renewal">In Rinnovo</option><option value="suspended">Sospese</option>',5)]),544),[[b,i.filters.status]])]),e("div",null,[t[16]||(t[16]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Categoria",-1)),p(e("select",{"onUpdate:modelValue":t[2]||(t[2]=o=>i.filters.category=o),onChange:t[3]||(t[3]=(...o)=>i.applyFilters&&i.applyFilters(...o)),class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},t[15]||(t[15]=[z('<option value="">Tutte</option><option value="quality">Qualità</option><option value="security">Sicurezza</option><option value="environmental">Ambientale</option><option value="privacy">Privacy</option>',5)]),544),[[b,i.filters.category]])]),e("div",null,[t[18]||(t[18]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"In scadenza",-1)),p(e("select",{"onUpdate:modelValue":t[4]||(t[4]=o=>i.filters.expiring_soon=o),onChange:t[5]||(t[5]=(...o)=>i.applyFilters&&i.applyFilters(...o)),class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},t[17]||(t[17]=[e("option",{value:""},"Tutte",-1),e("option",{value:"true"},"Solo in scadenza",-1),e("option",{value:"false"},"Non in scadenza",-1)]),544),[[b,i.filters.expiring_soon]])]),e("div",null,[t[20]||(t[20]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Responsabile",-1)),p(e("select",{"onUpdate:modelValue":t[6]||(t[6]=o=>i.filters.responsible_person_id=o),onChange:t[7]||(t[7]=(...o)=>i.applyFilters&&i.applyFilters(...o)),class:"w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},t[19]||(t[19]=[e("option",{value:""},"Tutti",-1)]),544),[[b,i.filters.responsible_person_id]])])])]),e("div",K,[i.loading?(d(),r("div",W,t[21]||(t[21]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1)]))):i.error?(d(),r("div",X,[n(s,{name:"exclamation-triangle",class:"h-8 w-8 mx-auto mb-2"}),e("p",null,a(i.error),1),e("button",{onClick:t[8]||(t[8]=(...o)=>i.loadCertifications&&i.loadCertifications(...o)),class:"mt-3 text-blue-600 hover:text-blue-800 font-medium"}," Riprova ")])):i.certifications.length===0?(d(),r("div",Y,[n(s,{name:"security",class:"h-12 w-12 mx-auto mb-4 text-gray-300"}),t[23]||(t[23]=e("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"Nessuna certificazione trovata",-1)),t[24]||(t[24]=e("p",{class:"text-gray-600 mb-6"},"Inizia aggiungendo la prima certificazione aziendale",-1)),n(c,{to:"/app/certifications/create",class:"bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors inline-flex items-center"},{default:f(()=>[n(s,{name:"plus",class:"h-5 w-5 mr-2"}),t[22]||(t[22]=k(" Nuova Certificazione "))]),_:1,__:[22]})])):(d(),r("div",Z,[t[28]||(t[28]=e("div",{class:"hidden md:grid md:grid-cols-6 gap-4 px-6 py-4 bg-gray-50 border-b border-gray-200 text-sm font-medium text-gray-700"},[e("div",null,"Certificazione"),e("div",null,"Ente Certificatore"),e("div",null,"Stato"),e("div",null,"Scadenza"),e("div",null,"Health Score"),e("div",null,"Azioni")],-1)),e("div",$,[(d(!0),r(T,null,j(i.certifications,o=>(d(),r("div",{key:o.id,class:"p-6 hover:bg-gray-50 transition-colors"},[e("div",ee,[e("div",null,[e("div",te,a(o.standard_name),1),e("div",oe,a(o.certificate_number||"N/A"),1)]),e("div",ie,a(o.certifying_body),1),e("div",null,[e("span",{class:x([i.getStatusBadgeClass(o.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},a(i.getStatusLabel(o.status)),3),o.is_expiring_soon?(d(),r("div",se,[n(s,{name:"exclamation-triangle",class:"h-4 w-4 mr-1"}),t[25]||(t[25]=e("span",{class:"text-xs"},"In scadenza",-1))])):h("",!0)]),e("div",null,[e("div",le,a(i.formatDate(o.expiry_date)),1),o.days_to_expiry!==null?(d(),r("div",ne,a(o.days_to_expiry)+" giorni ",1)):h("",!0)]),e("div",ae,[e("div",{class:x(["text-lg font-semibold",i.getHealthScoreColor(o.health_score)])},a(o.health_score)+"% ",3),e("div",re,[e("div",{class:x(["h-2 rounded-full transition-all duration-300",i.getHealthScoreBarColor(o.health_score)]),style:L({width:o.health_score+"%"})},null,6)])]),e("div",de,[n(c,{to:`/app/certifications/${o.id}`,class:"text-blue-600 hover:text-blue-800 p-2 rounded-lg hover:bg-blue-50",title:"Visualizza dettagli"},{default:f(()=>[n(s,{name:"eye",class:"h-4 w-4"})]),_:2},1032,["to"]),n(c,{to:`/app/certifications/${o.id}/edit`,class:"text-green-600 hover:text-green-800 p-2 rounded-lg hover:bg-green-50",title:"Modifica"},{default:f(()=>[n(s,{name:"pencil",class:"h-4 w-4"})]),_:2},1032,["to"]),e("button",{onClick:_=>i.confirmDelete(o),class:"text-red-600 hover:text-red-800 p-2 rounded-lg hover:bg-red-50",title:"Elimina"},[n(s,{name:"trash",class:"h-4 w-4"})],8,ce)])]),e("div",ue,[e("div",ge,[e("div",fe,[e("h3",me,a(o.standard_name),1),e("p",xe,a(o.certifying_body),1)]),e("span",{class:x([i.getStatusBadgeClass(o.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},a(i.getStatusLabel(o.status)),3)]),e("div",pe,[e("div",null,[t[26]||(t[26]=e("span",{class:"text-gray-500"},"Scadenza:",-1)),e("span",be,a(i.formatDate(o.expiry_date)),1)]),e("div",null,[t[27]||(t[27]=e("span",{class:"text-gray-500"},"Health Score:",-1)),e("span",{class:x(["block font-medium",i.getHealthScoreColor(o.health_score)])},a(o.health_score)+"% ",3)])]),e("div",ve,[n(c,{to:`/app/certifications/${o.id}`,class:"text-blue-600 hover:text-blue-800 p-2"},{default:f(()=>[n(s,{name:"eye",class:"h-5 w-5"})]),_:2},1032,["to"]),n(c,{to:`/app/certifications/${o.id}/edit`,class:"text-green-600 hover:text-green-800 p-2"},{default:f(()=>[n(s,{name:"pencil",class:"h-5 w-5"})]),_:2},1032,["to"]),e("button",{onClick:_=>i.confirmDelete(o),class:"text-red-600 hover:text-red-800 p-2"},[n(s,{name:"trash",class:"h-5 w-5"})],8,ye)])])]))),128))])]))]),i.showDeleteModal?(d(),r("div",_e,[e("div",he,[e("div",we,[n(s,{name:"exclamation-triangle",class:"h-8 w-8 text-red-500 mr-3"}),t[29]||(t[29]=e("h3",{class:"text-lg font-semibold text-gray-900"},"Conferma Eliminazione",-1))]),e("p",Ce,' Sei sicuro di voler eliminare la certificazione "'+a((g=i.certificationToDelete)==null?void 0:g.standard_name)+'"? Questa azione non può essere annullata. ',1),e("div",Se,[e("button",{onClick:t[9]||(t[9]=(...o)=>i.cancelDelete&&i.cancelDelete(...o)),class:"px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"}," Annulla "),e("button",{onClick:t[10]||(t[10]=(...o)=>i.deleteCertification&&i.deleteCertification(...o)),class:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700",disabled:i.deleting},[i.deleting?(d(),r("span",ke,"Eliminazione...")):(d(),r("span",De,"Elimina"))],8,ze)])])])):h("",!0)])}const Be=R(q,[["render",Fe]]);export{Be as default};
