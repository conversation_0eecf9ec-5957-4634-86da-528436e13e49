import{r as t,c as b,o as v,b as h,l as _,e as d,s as y,j as w}from"./vendor.js";import{_ as x}from"./PageHeader.js";import{F as T}from"./FormBuilder.js";import"./app.js";import"./AlertsSection.js";/* empty css                                                           */const E={class:"min-h-screen bg-gray-50 dark:bg-gray-900 py-6"},$={class:"max-w-3xl mx-auto px-4 sm:px-6 lg:px-8"},B={__name:"DepartmentCreate",setup(D){const p=y(),r=t({name:"",description:"",parent_id:"",manager_id:"",budget:null}),l=t([]),i=t([]),s=t(!1),o=t(null),c=t({}),u=b(()=>[{id:"name",type:"text",label:"Nome Dipartimento",placeholder:"Es. Sviluppo Software",required:!0},{id:"description",type:"textarea",label:"Descrizione",placeholder:"Descrizione del dipartimento e delle sue responsabilità",rows:3,required:!1},{id:"parent_id",type:"select",label:"Dipartimento Padre",placeholder:"Nessun dipartimento padre (livello radice)",required:!1,options:l.value.map(e=>({value:e.id,label:e.name}))},{id:"manager_id",type:"select",label:"Manager",placeholder:"Nessun manager assegnato",required:!1,options:i.value.map(e=>({value:e.id,label:`${e.first_name} ${e.last_name} (${e.email})`}))},{id:"budget",type:"number",label:"Budget Annuale (€)",placeholder:"0",min:0,step:1e3,required:!1}]),m=async()=>{try{const e=await fetch("/api/personnel/departments",{credentials:"include"});if(!e.ok)throw new Error(`HTTP ${e.status}: ${e.statusText}`);const a=await e.json();a.success&&(l.value=a.data.departments||[])}catch(e){console.error("Error fetching departments:",e)}},f=async()=>{try{const e=await fetch("/api/personnel/users",{credentials:"include"});if(!e.ok)throw new Error(`HTTP ${e.status}: ${e.statusText}`);const a=await e.json();a.success&&(i.value=a.data.users||[])}catch(e){console.error("Error fetching users:",e)}},g=async()=>{s.value=!0,o.value=null,c.value={};try{const e={name:r.value.name,description:r.value.description,parent_id:r.value.parent_id||null,manager_id:r.value.manager_id||null,budget:r.value.budget||null},a=await fetch("/api/personnel/departments",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(e)});if(!a.ok)throw new Error(`HTTP ${a.status}: ${a.statusText}`);const n=await a.json();if(n.success)p.push("/app/personnel/departments");else throw new Error(n.message||"Errore nella creazione del dipartimento")}catch(e){console.error("Error creating department:",e),o.value=e.message}finally{s.value=!1}};return v(async()=>{await Promise.all([m(),f()])}),(e,a)=>(w(),h("div",E,[_("div",$,[d(x,{title:"Crea Nuovo Dipartimento",subtitle:"Aggiungi un nuovo dipartimento all'organizzazione",icon:"building-office","icon-color":"text-brand-primary-600","show-back":!0,"back-route":"/app/personnel/departments"}),d(T,{modelValue:r.value,"onUpdate:modelValue":a[0]||(a[0]=n=>r.value=n),fields:u.value,errors:c.value,"global-error":o.value,loading:s.value,"submit-label":"Crea Dipartimento","loading-label":"Creazione...","cancel-label":"Annulla","cancel-route":"/app/personnel/departments",onSubmit:g},null,8,["modelValue","fields","errors","global-error","loading"])])]))}};export{B as default};
