import{r as j,H as Y,b as a,g as F,j as l,l as e,e as u,A as R,B as g,C as f,S as L,F as v,q as z,P as Z,t as p,v as y,c as $,o as ee,p as P,s as te,n as x,x as T}from"./vendor.js";import{u as J}from"./funding.js";import{H as c,_ as re,b as oe}from"./app.js";import{D as ne}from"./DashboardTemplate.js";import{u as se}from"./useToast.js";const ie={class:"flex items-center justify-between p-6 border-b"},ae={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},le={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},de={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},ue={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},ce={class:"grid grid-cols-2 md:grid-cols-3 gap-3"},me=["value"],pe={class:"ml-2 text-sm text-gray-700"},ge={class:"space-y-2"},be=["onUpdate:modelValue"],fe=["onClick"],ye={class:"space-y-2"},xe=["onUpdate:modelValue"],ve=["onClick"],_e={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},he={class:"flex items-center justify-end gap-3 pt-6 border-t"},we=["disabled"],ke={key:0,class:"flex items-center gap-2"},Ce={key:1},ze={__name:"CreateOpportunityModal",props:{show:{type:Boolean,default:!1}},emits:["close","created"],setup(B,{emit:_}){const m=_,O=J(),{showToast:h}=se(),w=j(!1),S=j([]),D=["Tecnologia","Manifatturiero","Servizi digitali","Energia rinnovabile","Biotecnologie","Automotive","Aerospazio","Agroalimentare","Turismo","Sanità","Edilizia","Logistica"],r=Y({title:"",description:"",source_entity:"",min_grant_amount:null,max_grant_amount:null,funding_percentage:80,opening_date:"",application_deadline:"",status:"open",funding_type:"grant",eligibility_criteria:[""],required_documents:[""],contact_email:"",contact_phone:"",website:""});function q(){r.eligibility_criteria.push("")}function E(d){r.eligibility_criteria.length>1&&r.eligibility_criteria.splice(d,1)}function V(){r.required_documents.push("")}function I(d){r.required_documents.length>1&&r.required_documents.splice(d,1)}async function U(){try{w.value=!0;const d={...r,target_sectors:JSON.stringify(S.value),eligibility_criteria:JSON.stringify(r.eligibility_criteria.filter(t=>t.trim())),required_documents:JSON.stringify(r.required_documents.filter(t=>t.trim())),contact_info:JSON.stringify({email:r.contact_email,phone:r.contact_phone,website:r.website})};delete d.contact_email,delete d.contact_phone,delete d.website,console.log("Creating opportunity:",d),await O.createOpportunity(d),h("Opportunità di finanziamento creata con successo!","success"),m("created"),m("close"),N()}catch(d){console.error("Error creating opportunity:",d),h("Errore nella creazione dell'opportunità","error")}finally{w.value=!1}}function N(){Object.keys(r).forEach(d=>{Array.isArray(r[d])?r[d]=[""]:typeof r[d]=="number"?r[d]=d==="funding_percentage"?80:null:r[d]=d==="status"?"open":d==="funding_type"?"grant":""}),S.value=[]}return(d,t)=>B.show?(l(),a("div",{key:0,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",onClick:t[17]||(t[17]=i=>d.$emit("close"))},[e("div",{class:"bg-white rounded-lg max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto",onClick:t[16]||(t[16]=R(()=>{},["stop"]))},[e("div",ie,[t[18]||(t[18]=e("h3",{class:"text-xl font-semibold text-gray-900"},"Nuova Opportunità di Finanziamento",-1)),e("button",{onClick:t[0]||(t[0]=i=>d.$emit("close")),class:"text-gray-400 hover:text-gray-600"},[u(c,{name:"x-mark",size:"lg"})])]),e("form",{onSubmit:R(U,["prevent"]),class:"p-6 space-y-6"},[e("div",ae,[e("div",null,[t[19]||(t[19]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Titolo * ",-1)),g(e("input",{"onUpdate:modelValue":t[1]||(t[1]=i=>r.title=i),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary-500 focus:border-brand-primary-500",placeholder:"Es. Bando Innovazione Digitale 2024"},null,512),[[f,r.title]])]),e("div",null,[t[20]||(t[20]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Ente Erogatore * ",-1)),g(e("input",{"onUpdate:modelValue":t[2]||(t[2]=i=>r.source_entity=i),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary-500 focus:border-brand-primary-500",placeholder:"Es. Regione Lombardia"},null,512),[[f,r.source_entity]])])]),e("div",null,[t[21]||(t[21]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Descrizione * ",-1)),g(e("textarea",{"onUpdate:modelValue":t[3]||(t[3]=i=>r.description=i),required:"",rows:"4",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary-500 focus:border-brand-primary-500",placeholder:"Descrivi l'opportunità di finanziamento..."},null,512),[[f,r.description]])]),e("div",le,[e("div",null,[t[22]||(t[22]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Importo Minimo (€) ",-1)),g(e("input",{"onUpdate:modelValue":t[4]||(t[4]=i=>r.min_grant_amount=i),type:"number",min:"0",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary-500 focus:border-brand-primary-500",placeholder:"10000"},null,512),[[f,r.min_grant_amount,void 0,{number:!0}]])]),e("div",null,[t[23]||(t[23]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Importo Massimo (€) * ",-1)),g(e("input",{"onUpdate:modelValue":t[5]||(t[5]=i=>r.max_grant_amount=i),type:"number",min:"0",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary-500 focus:border-brand-primary-500",placeholder:"500000"},null,512),[[f,r.max_grant_amount,void 0,{number:!0}]])]),e("div",null,[t[24]||(t[24]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," % Finanziamento ",-1)),g(e("input",{"onUpdate:modelValue":t[6]||(t[6]=i=>r.funding_percentage=i),type:"number",min:"0",max:"100",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary-500 focus:border-brand-primary-500",placeholder:"80"},null,512),[[f,r.funding_percentage,void 0,{number:!0}]])])]),e("div",de,[e("div",null,[t[25]||(t[25]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Data Apertura * ",-1)),g(e("input",{"onUpdate:modelValue":t[7]||(t[7]=i=>r.opening_date=i),type:"date",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary-500 focus:border-brand-primary-500"},null,512),[[f,r.opening_date]])]),e("div",null,[t[26]||(t[26]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Scadenza Candidature * ",-1)),g(e("input",{"onUpdate:modelValue":t[8]||(t[8]=i=>r.application_deadline=i),type:"date",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary-500 focus:border-brand-primary-500"},null,512),[[f,r.application_deadline]])])]),e("div",ue,[e("div",null,[t[28]||(t[28]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Status * ",-1)),g(e("select",{"onUpdate:modelValue":t[9]||(t[9]=i=>r.status=i),required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary-500 focus:border-brand-primary-500"},t[27]||(t[27]=[e("option",{value:"open"},"Aperto",-1),e("option",{value:"closed"},"Chiuso",-1),e("option",{value:"upcoming"},"In Arrivo",-1)]),512),[[L,r.status]])]),e("div",null,[t[30]||(t[30]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Tipo Finanziamento ",-1)),g(e("select",{"onUpdate:modelValue":t[10]||(t[10]=i=>r.funding_type=i),class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary-500 focus:border-brand-primary-500"},t[29]||(t[29]=[e("option",{value:"grant"},"Contributo a Fondo Perduto",-1),e("option",{value:"loan"},"Prestito Agevolato",-1),e("option",{value:"mixed"},"Misto",-1)]),512),[[L,r.funding_type]])])]),e("div",null,[t[31]||(t[31]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Settori Target ",-1)),e("div",ce,[(l(),a(v,null,z(D,i=>e("label",{key:i,class:"flex items-center"},[g(e("input",{type:"checkbox",value:i,"onUpdate:modelValue":t[11]||(t[11]=b=>S.value=b),class:"rounded border-gray-300 text-brand-primary-600 focus:ring-brand-primary-500"},null,8,me),[[Z,S.value]]),e("span",pe,p(i),1)])),64))])]),e("div",null,[t[33]||(t[33]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Criteri di Eligibilità ",-1)),e("div",ge,[(l(!0),a(v,null,z(r.eligibility_criteria,(i,b)=>(l(),a("div",{key:b,class:"flex gap-2"},[g(e("input",{"onUpdate:modelValue":k=>r.eligibility_criteria[b]=k,type:"text",class:"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary-500 focus:border-brand-primary-500",placeholder:"Es. Essere una PMI italiana"},null,8,be),[[f,r.eligibility_criteria[b]]]),e("button",{type:"button",onClick:k=>E(b),class:"px-3 py-2 text-red-600 hover:text-red-800"},[u(c,{name:"trash",size:"sm"})],8,fe)]))),128)),e("button",{type:"button",onClick:q,class:"text-sm text-brand-primary-600 hover:text-brand-primary-700 flex items-center gap-1"},[u(c,{name:"plus",size:"sm"}),t[32]||(t[32]=y(" Aggiungi criterio "))])])]),e("div",null,[t[35]||(t[35]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Documenti Richiesti ",-1)),e("div",ye,[(l(!0),a(v,null,z(r.required_documents,(i,b)=>(l(),a("div",{key:b,class:"flex gap-2"},[g(e("input",{"onUpdate:modelValue":k=>r.required_documents[b]=k,type:"text",class:"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary-500 focus:border-brand-primary-500",placeholder:"Es. Business plan"},null,8,xe),[[f,r.required_documents[b]]]),e("button",{type:"button",onClick:k=>I(b),class:"px-3 py-2 text-red-600 hover:text-red-800"},[u(c,{name:"trash",size:"sm"})],8,ve)]))),128)),e("button",{type:"button",onClick:V,class:"text-sm text-brand-primary-600 hover:text-brand-primary-700 flex items-center gap-1"},[u(c,{name:"plus",size:"sm"}),t[34]||(t[34]=y(" Aggiungi documento "))])])]),e("div",_e,[e("div",null,[t[36]||(t[36]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Email Contatto ",-1)),g(e("input",{"onUpdate:modelValue":t[12]||(t[12]=i=>r.contact_email=i),type:"email",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary-500 focus:border-brand-primary-500",placeholder:"<EMAIL>"},null,512),[[f,r.contact_email]])]),e("div",null,[t[37]||(t[37]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Telefono Contatto ",-1)),g(e("input",{"onUpdate:modelValue":t[13]||(t[13]=i=>r.contact_phone=i),type:"tel",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary-500 focus:border-brand-primary-500",placeholder:"+39 02 12345678"},null,512),[[f,r.contact_phone]])])]),e("div",null,[t[38]||(t[38]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Sito Web / Link Bando ",-1)),g(e("input",{"onUpdate:modelValue":t[14]||(t[14]=i=>r.website=i),type:"url",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary-500 focus:border-brand-primary-500",placeholder:"https://www.ente.gov.it/bando"},null,512),[[f,r.website]])]),e("div",he,[e("button",{type:"button",onClick:t[15]||(t[15]=i=>d.$emit("close")),class:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-primary-500"}," Annulla "),e("button",{type:"submit",disabled:w.value,class:"px-4 py-2 text-sm font-medium text-white bg-brand-primary-600 border border-transparent rounded-md hover:bg-brand-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"},[w.value?(l(),a("span",ke,t[39]||(t[39]=[e("div",{class:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"},null,-1),y(" Creazione... ")]))):(l(),a("span",Ce,"Crea Opportunità"))],8,we)])],32)])])):F("",!0)}},Se={class:"space-y-6"},Ae={class:"bg-white rounded-lg shadow-sm border p-6"},$e={class:"flex items-center justify-between mb-4"},De={class:"text-lg font-semibold text-gray-900 flex items-center gap-2"},Ve={class:"text-sm text-gray-500"},Ie={key:0,class:"text-center py-8 text-gray-500"},Te={key:1,class:"space-y-3"},Oe={class:"flex-1"},qe={class:"font-medium text-gray-900"},Ee={class:"text-sm text-gray-600"},Fe={class:"text-right"},Ue={class:"text-sm font-medium text-amber-700"},Ne={class:"text-xs text-gray-500"},Me={class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},je={class:"bg-white rounded-lg shadow-sm border p-6"},Be={class:"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2"},Re={key:0,class:"text-center py-8 text-gray-500"},Le={key:1,class:"space-y-3"},Pe=["onClick"],Je={class:"font-medium text-gray-900 text-sm"},He={class:"text-xs text-gray-600 mt-1"},Ge={class:"flex items-center justify-between mt-2"},We={class:"text-xs text-gray-500"},Ke={class:"bg-white rounded-lg shadow-sm border p-6"},Qe={class:"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2"},Xe={key:0,class:"text-center py-8 text-gray-500"},Ye={key:1,class:"space-y-3"},Ze=["onClick"],et={class:"font-medium text-gray-900 text-sm"},tt={class:"text-xs text-gray-600 mt-1"},rt={class:"flex items-center justify-between mt-2"},ot={class:"text-xs text-gray-500"},nt={class:"bg-white rounded-lg shadow-sm border p-6"},st={class:"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2"},it={class:"space-y-3"},at={class:"flex items-center gap-3"},lt={class:"flex items-center gap-3"},dt={class:"flex items-center gap-3"},ut={class:"flex items-center gap-3"},ct={class:"space-y-2"},mt={class:"flex items-center justify-between"},pt=["disabled"],gt={key:0,class:"p-3 bg-gray-50 rounded-lg border animate-pulse"},bt={key:1,class:"space-y-2"},ft={class:"flex items-start gap-3"},yt={class:"flex-1 min-w-0"},xt={key:2,class:"p-3 bg-gray-50 rounded-lg border border-gray-200"},vt={class:"flex items-center gap-3"},_t={__name:"FundingDashboard",setup(B){const _=te(),m=J(),O=oe(),h=j(!1),w=$(()=>{var s,o;return((s=O.user)==null?void 0:s.role)==="admin"||((o=O.user)==null?void 0:o.role)==="manager"}),S=$(()=>{const s=m.dashboardStats;return[{title:"Opportunità Aperte",value:s.open_opportunities||0,icon:"folder-open",color:"emerald"},{title:"Candidature Attive",value:s.total_applications||0,icon:"document-text",color:"blue"},{title:"Scadenze Imminenti",value:s.approaching_deadline||0,icon:"clock",color:"orange"},{title:"Finanziamenti Totali",value:M(s.total_approved_funding||0),icon:"currency-euro",color:"indigo"}]}),D=$(()=>m.recentActivity.upcoming_deadlines||[]),r=$(()=>D.value.length),q=$(()=>m.recentActivity.recent_opportunities||[]),E=$(()=>m.recentActivity.recent_applications||[]);async function V(){try{console.log("🔄 Refreshing funding dashboard..."),await Promise.all([m.fetchDashboardStats(),m.fetchRecentActivity(10),m.fetchAIDashboardSuggestions()]),console.log("✅ Dashboard data loaded:",{stats:m.dashboardStats,recentActivity:m.recentActivity,aiSuggestions:m.aiSuggestions})}catch(s){console.error("❌ Error refreshing dashboard:",s)}}function I(){_.push("/app/funding/search")}function U(){_.push("/app/funding/applications/new")}function N(){_.push("/app/funding/reporting")}function d(){V()}function t(s){_.push(`/app/funding/opportunities/${s}`)}function i(s){_.push(`/app/funding/applications/${s}`)}function b(s){return s?new Date(s).toLocaleDateString("it-IT",{day:"numeric",month:"short",year:"numeric"}):""}function k(s){if(!s)return 0;const o=new Date(s),A=new Date,n=o.getTime()-A.getTime(),C=Math.ceil(n/(1e3*60*60*24));return Math.max(0,C)}function M(s){return s?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR",minimumFractionDigits:0,maximumFractionDigits:0}).format(s):"€0"}function H(s){return{draft:"bg-gray-100 text-gray-800",submitted:"bg-blue-100 text-blue-800",under_evaluation:"bg-yellow-100 text-yellow-800",approved:"bg-green-100 text-green-800",rejected:"bg-red-100 text-red-800",funded:"bg-purple-100 text-purple-800"}[s]||"bg-gray-100 text-gray-800"}function G(s){return{draft:"Bozza",submitted:"Inviata",under_evaluation:"In Valutazione",approved:"Approvata",rejected:"Rifiutata",funded:"Finanziata"}[s]||s}async function W(){try{await m.refreshAISuggestions()}catch(s){console.error("❌ Error refreshing AI suggestions:",s)}}function K(s){const o={ricerca:"magnifying-glass",candidatura:"document-plus",strategia:"chart-bar",networking:"user-group",default:"light-bulb"};return o[s]||o.default}function Q(s){return{open:"bg-green-100 text-green-800",closed:"bg-gray-100 text-gray-800",evaluating:"bg-blue-100 text-blue-800",completed:"bg-purple-100 text-purple-800",suspended:"bg-orange-100 text-orange-800"}[s]||"bg-gray-100 text-gray-800"}function X(s){return{open:"Aperto",closed:"Chiuso",evaluating:"In Valutazione",completed:"Completato",suspended:"Sospeso"}[s]||s}return ee(()=>{V()}),(s,o)=>(l(),a(v,null,[u(ne,{title:"Dashboard Bandi",subtitle:"Gestisci opportunità di finanziamento e candidature",stats:S.value,onRefresh:V},{"header-actions":P(()=>[w.value?(l(),a("button",{key:0,onClick:o[0]||(o[0]=A=>h.value=!0),class:"btn-primary flex items-center gap-2"},[u(c,{name:"plus",size:"sm"}),o[3]||(o[3]=y(" Nuova Opportunità "))])):F("",!0),e("button",{onClick:I,class:"btn-secondary flex items-center gap-2"},[u(c,{name:"magnifying-glass",size:"sm"}),o[4]||(o[4]=y(" Cerca Bandi "))])]),widget:P(()=>{var A;return[e("div",Se,[e("div",Ae,[e("div",$e,[e("h3",De,[u(c,{name:"clock",class:"text-amber-500"}),o[5]||(o[5]=y(" Scadenze Imminenti "))]),e("span",Ve,p(r.value)+" opportunità",1)]),D.value.length===0?(l(),a("div",Ie,[u(c,{name:"check-circle",size:"lg",class:"text-green-500 mx-auto mb-2"}),o[6]||(o[6]=e("p",null,"Nessuna scadenza imminente",-1))])):(l(),a("div",Te,[(l(!0),a(v,null,z(D.value.slice(0,5),n=>(l(),a("div",{key:n.id,class:"flex items-center justify-between p-3 bg-amber-50 rounded-lg border border-amber-200"},[e("div",Oe,[e("h4",qe,p(n.title),1),e("p",Ee,p(n.source_entity),1)]),e("div",Fe,[e("p",Ue,p(b(n.application_deadline)),1),e("p",Ne,p(k(n.application_deadline))+" giorni rimasti ",1)])]))),128))]))]),e("div",Me,[e("div",je,[e("h3",Be,[u(c,{name:"newspaper",class:"text-blue-500"}),o[7]||(o[7]=y(" Nuove Opportunità "))]),q.value.length===0?(l(),a("div",Re,[u(c,{name:"inbox",size:"lg",class:"mx-auto mb-2"}),o[8]||(o[8]=e("p",null,"Nessuna nuova opportunità",-1))])):(l(),a("div",Le,[(l(!0),a(v,null,z(q.value.slice(0,4),n=>(l(),a("div",{key:n.id,class:"p-3 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors",onClick:C=>t(n.id)},[e("h4",Je,p(n.title),1),e("p",He,p(n.source_entity),1),e("div",Ge,[e("span",{class:x(["inline-flex items-center px-2 py-1 rounded-full text-xs font-medium",Q(n.status)])},p(X(n.status)),3),e("span",We,p(M(n.max_grant_amount)),1)])],8,Pe))),128))]))]),e("div",Ke,[e("h3",Qe,[u(c,{name:"document-text",class:"text-purple-500"}),o[9]||(o[9]=y(" Le Mie Candidature "))]),E.value.length===0?(l(),a("div",Xe,[u(c,{name:"document-plus",size:"lg",class:"mx-auto mb-2"}),o[10]||(o[10]=e("p",null,"Nessuna candidatura ancora",-1)),e("button",{onClick:I,class:"mt-2 text-sm text-brand-primary-600 hover:text-brand-primary-700"}," Cerca opportunità disponibili ")])):(l(),a("div",Ye,[(l(!0),a(v,null,z(E.value.slice(0,4),n=>{var C;return l(),a("div",{key:n.id,class:"p-3 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors",onClick:ht=>i(n.id)},[e("h4",et,p(n.project_title),1),e("p",tt,p((C=n.opportunity)==null?void 0:C.title),1),e("div",rt,[e("span",{class:x(["inline-flex items-center px-2 py-1 rounded-full text-xs font-medium",H(n.status)])},p(G(n.status)),3),e("span",ot,p(M(n.requested_amount)),1)])],8,Ze)}),128))]))]),e("div",nt,[e("h3",st,[u(c,{name:"lightning-bolt",class:"text-yellow-500"}),o[11]||(o[11]=y(" Azioni Rapide "))]),e("div",it,[e("button",{onClick:U,class:"w-full p-3 text-left border rounded-lg hover:bg-blue-50 border-blue-200 transition-colors"},[e("div",at,[u(c,{name:"document-plus",class:"text-blue-500"}),o[12]||(o[12]=e("div",null,[e("p",{class:"font-medium text-gray-900"},"Nuova Candidatura"),e("p",{class:"text-sm text-gray-600"},"Candidati a un bando")],-1))])]),e("button",{onClick:I,class:"w-full p-3 text-left border rounded-lg hover:bg-gray-50 transition-colors"},[e("div",lt,[u(c,{name:"magnifying-glass",class:"text-blue-500"}),o[13]||(o[13]=e("div",null,[e("p",{class:"font-medium text-gray-900"},"Cerca Bandi"),e("p",{class:"text-sm text-gray-600"},"Trova opportunità adatte")],-1))])]),e("button",{onClick:N,class:"w-full p-3 text-left border rounded-lg hover:bg-gray-50 transition-colors"},[e("div",dt,[u(c,{name:"chart-bar",class:"text-green-500"}),o[14]||(o[14]=e("div",null,[e("p",{class:"font-medium text-gray-900"},"Rendicontazione Finanziaria"),e("p",{class:"text-sm text-gray-600"},"Gestisci spese progetto")],-1))])]),w.value?(l(),a("button",{key:0,onClick:o[1]||(o[1]=n=>h.value=!0),class:"w-full p-3 text-left border rounded-lg hover:bg-gray-50 transition-colors"},[e("div",ut,[u(c,{name:"plus-circle",class:"text-purple-500"}),o[15]||(o[15]=e("div",null,[e("p",{class:"font-medium text-gray-900"},"Nuova Opportunità"),e("p",{class:"text-sm text-gray-600"},"Aggiungi opportunità di finanziamento")],-1))])])):F("",!0),e("div",ct,[e("div",mt,[o[16]||(o[16]=e("h4",{class:"font-medium text-gray-700 text-sm"},"💡 Suggerimenti AI",-1)),e("button",{onClick:W,disabled:T(m).aiSuggestionsLoading,class:"text-xs p-1 hover:bg-gray-100 rounded transition-colors disabled:opacity-50",title:"Aggiorna suggerimenti"},[u(c,{name:"arrow-path",size:"xs",class:x({"animate-spin":T(m).aiSuggestionsLoading})},null,8,["class"])],8,pt)]),T(m).aiSuggestionsLoading?(l(),a("div",gt,o[17]||(o[17]=[e("div",{class:"flex items-center gap-2"},[e("div",{class:"w-4 h-4 bg-gray-300 rounded"}),e("div",{class:"h-3 bg-gray-300 rounded flex-1"})],-1),e("div",{class:"h-2 bg-gray-300 rounded mt-2"},null,-1)]))):((A=T(m).aiSuggestions)==null?void 0:A.length)>0?(l(),a("div",bt,[(l(!0),a(v,null,z(T(m).aiSuggestions.slice(0,2),(n,C)=>(l(),a("div",{key:C,class:x(["p-3 rounded-lg border",n.priority==="high"?"bg-red-50 border-red-200":n.priority==="medium"?"bg-yellow-50 border-yellow-200":"bg-blue-50 border-blue-200"])},[e("div",ft,[u(c,{name:K(n.category),class:x(["mt-0.5",n.priority==="high"?"text-red-500":n.priority==="medium"?"text-yellow-500":"text-blue-500"]),size:"sm"},null,8,["name","class"]),e("div",yt,[e("p",{class:x(["font-medium text-sm",n.priority==="high"?"text-red-900":n.priority==="medium"?"text-yellow-900":"text-blue-900"])},p(n.title),3),e("p",{class:x(["text-xs mt-1",n.priority==="high"?"text-red-700":n.priority==="medium"?"text-yellow-700":"text-blue-700"])},p(n.description),3),n.action?(l(),a("p",{key:0,class:x(["text-xs mt-1 font-medium",n.priority==="high"?"text-red-600":n.priority==="medium"?"text-yellow-600":"text-blue-600"])}," ▶ "+p(n.action),3)):F("",!0)])])],2))),128))])):(l(),a("div",xt,[e("div",vt,[u(c,{name:"light-bulb",class:"text-gray-400 mt-0.5",size:"sm"}),o[18]||(o[18]=e("div",null,[e("p",{class:"font-medium text-gray-600 text-sm"},"Suggerimenti AI"),e("p",{class:"text-xs text-gray-500 mt-1"}," Carica i suggerimenti personalizzati per ottimizzare la tua strategia funding. ")],-1))])]))])])])])])]}),_:1},8,["stats"]),u(ze,{show:h.value,onClose:o[2]||(o[2]=A=>h.value=!1),onCreated:d},null,8,["show"])],64))}},At=re(_t,[["__scopeId","data-v-a359c5eb"]]);export{At as default};
