import{_ as u}from"./app.js";import{c as t,b as o,j as g,g as i,v as p,n as b,t as y}from"./vendor.js";const x={__name:"StatusBadge",props:{status:{type:String,required:!0},type:{type:String,default:"project",validator:e=>["project","task","user","client","proposal","funding_opportunity","funding_application","generic"].includes(e)},showDot:{type:Boolean,default:!1}},setup(e){const l=e,s={project:{planning:{label:"Pianificazione",classes:"bg-yellow-100 text-yellow-800",dot:"bg-yellow-400"},active:{label:"Attivo",classes:"bg-green-100 text-green-800",dot:"bg-green-400"},on_hold:{label:"In Pausa",classes:"bg-orange-100 text-orange-800",dot:"bg-orange-400"},completed:{label:"Completato",classes:"bg-blue-100 text-blue-800",dot:"bg-blue-400"},cancelled:{label:"Ann<PERSON>ato",classes:"bg-red-100 text-red-800",dot:"bg-red-400"}},task:{todo:{label:"Da Fare",classes:"bg-gray-100 text-gray-800",dot:"bg-gray-400"},in_progress:{label:"In Corso",classes:"bg-blue-100 text-blue-800",dot:"bg-blue-400"},review:{label:"In Revisione",classes:"bg-purple-100 text-purple-800",dot:"bg-purple-400"},done:{label:"Completato",classes:"bg-green-100 text-green-800",dot:"bg-green-400"},blocked:{label:"Bloccato",classes:"bg-red-100 text-red-800",dot:"bg-red-400"}},user:{active:{label:"Attivo",classes:"bg-green-100 text-green-800",dot:"bg-green-400"},inactive:{label:"Inattivo",classes:"bg-gray-100 text-gray-800",dot:"bg-gray-400"},pending:{label:"In Attesa",classes:"bg-yellow-100 text-yellow-800",dot:"bg-yellow-400"}},client:{lead:{label:"Lead",classes:"bg-yellow-100 text-yellow-800",dot:"bg-yellow-400"},prospect:{label:"Prospect",classes:"bg-blue-100 text-blue-800",dot:"bg-blue-400"},client:{label:"Cliente",classes:"bg-green-100 text-green-800",dot:"bg-green-400"},inactive:{label:"Inattivo",classes:"bg-gray-100 text-gray-800",dot:"bg-gray-400"}},proposal:{draft:{label:"Bozza",classes:"bg-gray-100 text-gray-800",dot:"bg-gray-400"},sent:{label:"Inviata",classes:"bg-blue-100 text-blue-800",dot:"bg-blue-400"},negotiating:{label:"Negoziazione",classes:"bg-yellow-100 text-yellow-800",dot:"bg-yellow-400"},accepted:{label:"Accettata",classes:"bg-green-100 text-green-800",dot:"bg-green-400"},rejected:{label:"Rifiutata",classes:"bg-red-100 text-red-800",dot:"bg-red-400"}},funding_opportunity:{open:{label:"Aperto",classes:"bg-green-100 text-green-800",dot:"bg-green-400"},closed:{label:"Chiuso",classes:"bg-gray-100 text-gray-800",dot:"bg-gray-400"},evaluating:{label:"In Valutazione",classes:"bg-blue-100 text-blue-800",dot:"bg-blue-400"},completed:{label:"Completato",classes:"bg-purple-100 text-purple-800",dot:"bg-purple-400"},suspended:{label:"Sospeso",classes:"bg-orange-100 text-orange-800",dot:"bg-orange-400"}},funding_application:{draft:{label:"Bozza",classes:"bg-gray-100 text-gray-800",dot:"bg-gray-400"},submitted:{label:"Inviata",classes:"bg-blue-100 text-blue-800",dot:"bg-blue-400"},under_evaluation:{label:"In Valutazione",classes:"bg-yellow-100 text-yellow-800",dot:"bg-yellow-400"},approved:{label:"Approvata",classes:"bg-green-100 text-green-800",dot:"bg-green-400"},rejected:{label:"Rifiutata",classes:"bg-red-100 text-red-800",dot:"bg-red-400"},funded:{label:"Finanziata",classes:"bg-emerald-100 text-emerald-800",dot:"bg-emerald-400"}},generic:{success:{label:"Successo",classes:"bg-green-100 text-green-800",dot:"bg-green-400"},warning:{label:"Attenzione",classes:"bg-yellow-100 text-yellow-800",dot:"bg-yellow-400"},error:{label:"Errore",classes:"bg-red-100 text-red-800",dot:"bg-red-400"},info:{label:"Info",classes:"bg-blue-100 text-blue-800",dot:"bg-blue-400"}}},a=t(()=>(s[l.type]||s.generic)[l.status]||{label:l.status,classes:"bg-gray-100 text-gray-800",dot:"bg-gray-400"}),r=t(()=>a.value.label),n=t(()=>a.value.classes),c=t(()=>a.value.dot);return(d,f)=>(g(),o("span",{class:b(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",n.value])},[e.showDot?(g(),o("span",{key:0,class:b(["w-1.5 h-1.5 rounded-full mr-1.5",c.value])},null,2)):i("",!0),p(" "+y(r.value),1)],2))}},w=u(x,[["__scopeId","data-v-2e56fc56"]]);export{w as S};
