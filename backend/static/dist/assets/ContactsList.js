import{r as x,c as h,o as W,b as n,e as c,g as D,p as z,l as e,t as i,A as G,B as m,C as p,S as T,F as N,q as S,v as w,h as J,j as d}from"./vendor.js";import{u as K}from"./crm.js";import{u as O}from"./useToast.js";import{_ as Q}from"./ListPageTemplate.js";import{H as y}from"./app.js";import"./Pagination.js";const X={class:"flex space-x-4"},Y=["value"],Z={class:"overflow-x-auto"},ee={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},te={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},ae={class:"px-6 py-4 whitespace-nowrap"},oe={class:"flex items-center"},re={class:"flex-shrink-0 h-10 w-10"},se={class:"h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900/20 flex items-center justify-center"},le={class:"text-sm font-medium text-blue-600 dark:text-blue-400"},ie={class:"ml-4"},ne={class:"text-sm font-medium text-gray-900 dark:text-white"},de={class:"text-sm text-gray-500 dark:text-gray-400"},ue={class:"px-6 py-4 whitespace-nowrap"},ce={class:"text-sm text-gray-900 dark:text-white"},me={class:"px-6 py-4 whitespace-nowrap"},ge={class:"text-sm text-gray-900 dark:text-white"},xe={class:"px-6 py-4 whitespace-nowrap"},pe={class:"text-sm text-gray-900 dark:text-white"},ye={key:0,class:"flex items-center mb-1"},be={class:"text-sm"},fe={class:"flex items-center"},ve=["href"],ke={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"},he={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},we={class:"flex justify-end space-x-2"},Ce=["onClick"],_e=["onClick"],ze={class:"text-center py-12"},Ne={key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},Ve={class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800"},Se={class:"mt-3"},Ae={class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},De={class:"grid grid-cols-2 gap-4"},Te=["value"],Ue={class:"flex justify-end space-x-3 pt-4"},Me=["disabled"],Fe={key:0,class:"flex items-center"},$e={key:1},Re={__name:"ContactsList",setup(qe){const u=K(),{showToast:b}=O(),f=x(""),v=x(""),C=x(!1),g=x(null),_=x(!1),r=x({first_name:"",last_name:"",email:"",phone:"",position:"",client_id:""}),U=[{key:"full_name",label:"Contatto"},{key:"client",label:"Cliente"},{key:"position",label:"Ruolo"},{key:"contact_info",label:"Contatti"},{key:"created_at",label:"Data Creazione"},{key:"actions",label:"Azioni"}],M=h(()=>{const a=[...new Set(k.value.map(l=>l.client_id))].length,t=k.value.filter(l=>l.phone).length;return[{label:"Totale Contatti",value:k.value.length,icon:"user",iconClass:"text-blue-500"},{label:"Clienti Coinvolti",value:a,icon:"building-office",iconClass:"text-green-500"},{label:"Con Telefono",value:t,icon:"phone",iconClass:"text-purple-500"},{label:"Con Ruolo",value:k.value.filter(l=>l.position).length,icon:"identification",iconClass:"text-orange-500"}]}),F=h(()=>u.loading),k=h(()=>u.contacts),V=h(()=>u.clients),$=h(()=>{let a=k.value;if(f.value&&(a=a.filter(t=>t.client_id==f.value)),v.value){const t=v.value.toLowerCase();a=a.filter(l=>l.position&&l.position.toLowerCase().includes(t))}return a}),q=async()=>{await Promise.all([u.fetchContacts(),u.fetchClients()])},E=a=>a?a.split(" ").map(t=>t.charAt(0)).join("").toUpperCase().slice(0,2):"N/A",L=a=>{const t=V.value.find(l=>l.id==a);return t?t.name:"Cliente sconosciuto"},P=a=>{g.value=a,r.value={first_name:a.first_name||"",last_name:a.last_name||"",email:a.email||"",phone:a.phone||"",position:a.position||"",client_id:a.client_id||""}},A=()=>{C.value=!1,g.value=null,r.value={first_name:"",last_name:"",email:"",phone:"",position:"",client_id:""}},j=async()=>{_.value=!0;try{g.value?(await u.updateContact(g.value.id,r.value),b("Contatto aggiornato con successo","success")):(await u.createContact(r.value),b("Contatto creato con successo","success")),A()}catch{b("Errore nel salvataggio del contatto","error")}finally{_.value=!1}},B=async a=>{if(confirm("Sei sicuro di voler eliminare questo contatto?"))try{await u.deleteContact(a),b("Contatto eliminato con successo","success")}catch{b("Errore nell'eliminazione del contatto","error")}},I=()=>{f.value="",v.value=""},R=a=>a?new Date(a).toLocaleDateString("it-IT"):"N/A";return W(()=>{q()}),(a,t)=>{const l=J("router-link");return d(),n(N,null,[c(Q,{title:"Contatti",subtitle:"Gestione contatti aziendali e persone di riferimento",data:$.value,columns:U,stats:M.value,loading:F.value,"can-create":!0,"create-label":"Nuovo Contatto","search-placeholder":"Nome, email o posizione...","empty-message":"Inizia creando il tuo primo contatto","results-label":"contatti",onCreate:t[3]||(t[3]=o=>C.value=!0)},{filters:z(()=>[e("div",X,[e("div",null,[t[11]||(t[11]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Cliente",-1)),m(e("select",{"onUpdate:modelValue":t[0]||(t[0]=o=>f.value=o),class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},[t[10]||(t[10]=e("option",{value:""},"Tutti i clienti",-1)),(d(!0),n(N,null,S(V.value,o=>(d(),n("option",{key:o.id,value:o.id},i(o.name),9,Y))),128))],512),[[T,f.value]])]),e("div",null,[t[12]||(t[12]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ruolo",-1)),m(e("input",{"onUpdate:modelValue":t[1]||(t[1]=o=>v.value=o),type:"text",placeholder:"Filtra per ruolo...",class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"},null,512),[[p,v.value]])]),e("div",{class:"flex items-end"},[e("button",{onClick:I,class:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-md"}," Pulisci Filtri ")])])]),content:z(({data:o})=>[e("div",Z,[e("table",ee,[t[15]||(t[15]=e("thead",{class:"bg-gray-50 dark:bg-gray-800"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"}," Contatto "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"}," Cliente "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"}," Ruolo "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"}," Contatti "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"}," Data Creazione "),e("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"}," Azioni ")])],-1)),e("tbody",te,[(d(!0),n(N,null,S(o,s=>(d(),n("tr",{key:s.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[e("td",ae,[e("div",oe,[e("div",re,[e("div",se,[e("span",le,i(E(s.full_name)),1)])]),e("div",ie,[e("div",ne,i(s.full_name),1),e("div",de,i(s.email),1)])])]),e("td",ue,[e("div",ce,[c(l,{to:`/app/crm/clients/${s.client_id}`,class:"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium"},{default:z(()=>[w(i(L(s.client_id)),1)]),_:2},1032,["to"])])]),e("td",me,[e("span",ge,i(s.position||"N/A"),1)]),e("td",xe,[e("div",pe,[s.phone?(d(),n("div",ye,[c(y,{name:"phone",class:"w-4 h-4 mr-1 text-gray-400 dark:text-gray-500"}),e("span",be,i(s.phone),1)])):D("",!0),e("div",fe,[c(y,{name:"envelope",class:"w-4 h-4 mr-1 text-gray-400 dark:text-gray-500"}),e("a",{href:`mailto:${s.email}`,class:"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm"},i(s.email),9,ve)])])]),e("td",ke,i(R(s.created_at)),1),e("td",he,[e("div",we,[e("button",{onClick:H=>P(s),class:"inline-flex items-center px-3 py-1 text-xs font-medium rounded-md text-gray-700 bg-gray-100 hover:bg-gray-200 dark:text-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 transition-colors"},[c(y,{name:"pencil",class:"w-3 h-3 mr-1"}),t[13]||(t[13]=w(" Modifica "))],8,Ce),e("button",{onClick:H=>B(s.id),class:"inline-flex items-center px-3 py-1 text-xs font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 dark:text-red-400 dark:bg-red-900/20 dark:hover:bg-red-900/30 transition-colors"},[c(y,{name:"trash",class:"w-3 h-3 mr-1"}),t[14]||(t[14]=w(" Elimina "))],8,_e)])])]))),128))])])])]),"empty-state":z(()=>[e("div",ze,[c(y,{name:"user",class:"w-16 h-16 mx-auto mb-4 text-gray-300 dark:text-gray-600"}),t[17]||(t[17]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},"Nessun contatto trovato",-1)),t[18]||(t[18]=e("p",{class:"text-gray-500 dark:text-gray-400 mb-4"},"Inizia creando il tuo primo contatto",-1)),e("button",{onClick:t[2]||(t[2]=o=>C.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},[c(y,{name:"plus",class:"w-4 h-4 mr-2"}),t[16]||(t[16]=w(" Crea Primo Contatto "))])])]),_:1},8,["data","stats","loading"]),C.value||g.value?(d(),n("div",Ne,[e("div",Ve,[e("div",Se,[e("h3",Ae,i(g.value?"Modifica Contatto":"Nuovo Contatto"),1),e("form",{onSubmit:G(j,["prevent"]),class:"space-y-4"},[e("div",De,[e("div",null,[t[19]||(t[19]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Nome *",-1)),m(e("input",{"onUpdate:modelValue":t[4]||(t[4]=o=>r.value.first_name=o),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Nome"},null,512),[[p,r.value.first_name]])]),e("div",null,[t[20]||(t[20]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Cognome *",-1)),m(e("input",{"onUpdate:modelValue":t[5]||(t[5]=o=>r.value.last_name=o),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Cognome"},null,512),[[p,r.value.last_name]])])]),e("div",null,[t[21]||(t[21]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Email *",-1)),m(e("input",{"onUpdate:modelValue":t[6]||(t[6]=o=>r.value.email=o),type:"email",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"<EMAIL>"},null,512),[[p,r.value.email]])]),e("div",null,[t[22]||(t[22]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Telefono",-1)),m(e("input",{"onUpdate:modelValue":t[7]||(t[7]=o=>r.value.phone=o),type:"tel",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"+39 ************"},null,512),[[p,r.value.phone]])]),e("div",null,[t[23]||(t[23]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Posizione/Ruolo",-1)),m(e("input",{"onUpdate:modelValue":t[8]||(t[8]=o=>r.value.position=o),type:"text",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"es. Direttore Commerciale"},null,512),[[p,r.value.position]])]),e("div",null,[t[25]||(t[25]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Cliente *",-1)),m(e("select",{"onUpdate:modelValue":t[9]||(t[9]=o=>r.value.client_id=o),required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},[t[24]||(t[24]=e("option",{value:""},"Seleziona cliente",-1)),(d(!0),n(N,null,S(V.value,o=>(d(),n("option",{key:o.id,value:o.id},i(o.name),9,Te))),128))],512),[[T,r.value.client_id]])]),e("div",Ue,[e("button",{type:"button",onClick:A,class:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-md"}," Annulla "),e("button",{type:"submit",disabled:_.value,class:"px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 rounded-md"},[_.value?(d(),n("span",Fe,t[26]||(t[26]=[e("svg",{class:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",fill:"none",viewBox:"0 0 24 24"},[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),w(" Salvataggio... ")]))):(d(),n("span",$e,i(g.value?"Aggiorna Contatto":"Crea Contatto"),1))],8,Me)])],32)])])])):D("",!0)],64)}}};export{Re as default};
