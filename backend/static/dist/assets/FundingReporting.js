import{_ as nt,H as _,f as lt,i as rt}from"./app.js";import{r as R,c as D,w as dt,o as ut,b as i,e as x,g as h,l as t,p as ct,B as N,S as V,F as j,q as E,O as U,t as a,x as m,v as I,u as pt,s as mt,j as n,n as xt}from"./vendor.js";import{u as gt}from"./funding.js";import{u as vt}from"./useFormatters.js";import{_ as _t}from"./PageHeader.js";import{S as ft}from"./StatusBadge.js";import"./formatters.js";const ht=["disabled"],yt={key:0,class:"bg-white rounded-lg shadow-sm border p-4 mb-6"},bt={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},wt=["value"],kt={key:1,class:"bg-white rounded-lg shadow-sm border p-6 mb-6"},Dt={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"},St={class:"bg-blue-50 rounded-lg p-4"},zt={class:"flex items-center"},Rt={class:"ml-3"},Ct={class:"text-2xl font-semibold text-blue-900"},jt={class:"bg-green-50 rounded-lg p-4"},It={class:"flex items-center"},Et={class:"ml-3"},At={class:"text-2xl font-semibold text-green-900"},Ft={class:"text-xs text-green-700"},Tt={class:"bg-orange-50 rounded-lg p-4"},Pt={class:"flex items-center"},Mt={class:"ml-3"},$t={class:"text-2xl font-semibold text-orange-900"},Nt={class:"bg-purple-50 rounded-lg p-4"},Vt={class:"flex items-center"},Lt={class:"ml-3"},Bt={class:"text-2xl font-semibold text-purple-900"},Ot={class:"text-xs text-purple-700"},Qt={key:2,class:"bg-white rounded-lg shadow-sm border p-6 mb-6"},Ut={key:0,class:"mb-6"},Yt={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"},qt={class:"flex items-center justify-between"},Gt={class:"font-medium text-gray-900"},Ht={class:"text-sm text-gray-600"},Xt={class:"text-right"},Wt={class:"text-lg font-semibold text-gray-900"},Jt={class:"text-sm text-gray-600"},Kt={class:"overflow-x-auto"},Zt={class:"min-w-full divide-y divide-gray-200"},te={class:"bg-white divide-y divide-gray-200"},ee={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},se={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},ae={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},oe={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},ie={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"},ne={class:"px-6 py-4 text-sm text-gray-900"},le={class:"font-medium"},re={class:"text-gray-600 text-xs"},de={key:0,class:"mt-4 text-center"},ue={class:"text-sm text-gray-600"},ce={class:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6"},pe={class:"bg-white rounded-lg shadow-sm border p-6"},me={class:"flex items-center"},xe={class:"flex-shrink-0"},ge={class:"w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center"},ve={class:"ml-4"},_e={class:"text-sm font-medium text-gray-600"},fe={class:"text-2xl font-semibold text-gray-900"},he={key:0,class:"text-xs text-gray-500"},ye={class:"bg-white rounded-lg shadow-sm border p-6"},be={class:"flex items-center"},we={class:"flex-shrink-0"},ke={class:"w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center"},De={class:"ml-4"},Se={class:"text-2xl font-semibold text-gray-900"},ze={class:"bg-white rounded-lg shadow-sm border p-6"},Re={class:"flex items-center"},Ce={class:"flex-shrink-0"},je={class:"w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center"},Ie={class:"ml-4"},Ee={class:"text-2xl font-semibold text-gray-900"},Ae={class:"bg-white rounded-lg shadow-sm border p-6"},Fe={class:"flex items-center"},Te={class:"flex-shrink-0"},Pe={class:"w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center"},Me={class:"ml-4"},$e={class:"text-2xl font-semibold text-gray-900"},Ne={class:"bg-white rounded-lg shadow-sm border"},Ve={key:0,class:"flex justify-center items-center py-12"},Le={key:1,class:"text-center py-12"},Be={class:"text-gray-600 mb-4"},Oe={key:2,class:"overflow-x-auto"},Qe={class:"min-w-full divide-y divide-gray-200"},Ue={class:"bg-white divide-y divide-gray-200"},Ye={class:"px-6 py-4 whitespace-nowrap"},qe={class:"text-sm font-medium text-gray-900"},Ge={key:0,class:"text-sm text-gray-500"},He={class:"px-6 py-4 whitespace-nowrap"},Xe={class:"text-sm text-gray-900"},We={class:"text-sm text-gray-500"},Je={class:"px-6 py-4 whitespace-nowrap"},Ke={class:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 capitalize"},Ze={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},ts={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},es={class:"px-6 py-4 whitespace-nowrap"},ss={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},as=["onClick"],os=["onClick"],is={key:3,class:"bg-white px-4 py-3 border-t border-gray-200 sm:px-6"},ns={class:"flex items-center justify-between"},ls={class:"flex-1 flex justify-between sm:hidden"},rs=["disabled"],ds=["disabled"],us={class:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"},cs={class:"text-sm text-gray-700"},ps={class:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px"},ms=["disabled"],xs=["onClick"],gs=["disabled"],vs={key:4,class:"bg-white rounded-lg shadow-sm border"},_s={class:"px-6 py-4 border-b border-gray-200"},fs={class:"text-sm text-gray-600 mt-1"},hs={class:"p-6"},ys={class:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6"},bs={class:"bg-blue-50 rounded-lg p-4 text-center"},ws={class:"text-2xl font-bold text-blue-900"},ks={class:"bg-green-50 rounded-lg p-4 text-center"},Ds={class:"text-2xl font-bold text-green-900"},Ss={class:"bg-purple-50 rounded-lg p-4 text-center"},zs={class:"text-2xl font-bold text-purple-900"},Rs={key:0},Cs={class:"overflow-x-auto"},js={class:"min-w-full divide-y divide-gray-200"},Is={class:"bg-white divide-y divide-gray-200"},Es={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"},As={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-600"},Fs={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},Ts={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"},Ps={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-600"},Ms={class:"mt-4 p-3 bg-amber-50 border border-amber-200 rounded-lg"},$s={class:"flex"},F=10,Ns={__name:"FundingReporting",setup(Vs){const L=mt(),Y=pt(),k=gt(),{formatDate:O,formatCurrency:g}=vt(),z=R(!1),B=R(!1),v=R(1),r=R(null),f=R(null),u=R({applicationId:"",status:"",category:"",dateRange:""}),T=R([]),A=R([]),q=D(()=>f.value?`Rendicontazione: ${f.value.project_title}`:"Rendicontazione Finanziaria"),G=D(()=>f.value?"Dati completi di bando, progetto e consuntivazione":"Gestisci le spese dei progetti e i report di finanziamento"),y=D(()=>{let o=[...T.value];if(u.value.applicationId&&(o=o.filter(e=>e.application_id===parseInt(u.value.applicationId))),u.value.status&&(o=o.filter(e=>e.approval_status===u.value.status)),u.value.category&&(o=o.filter(e=>e.category===u.value.category)),u.value.dateRange){const e=new Date,c=Z(u.value.dateRange,e);o=o.filter(b=>{const l=new Date(b.expense_date);return l>=c&&l<=e})}return o.sort((e,c)=>new Date(c.expense_date)-new Date(e.expense_date))}),H=D(()=>u.value.applicationId||u.value.status||u.value.category||u.value.dateRange),P=D(()=>Math.ceil(y.value.length/F)),X=D(()=>(v.value-1)*F+1),W=D(()=>Math.min(v.value*F,y.value.length)),J=D(()=>{const o=(v.value-1)*F,e=o+F;return y.value.slice(o,e)}),K=D(()=>{const o=P.value,e=v.value,c=2,b=[],l=[];for(let p=Math.max(2,e-c);p<=Math.min(o-1,e+c);p++)b.push(p);return e-c>2?l.push(1,"..."):l.push(1),l.push(...b),e+c<o-1?l.push("...",o):o>1&&l.push(o),l.filter(p=>p!=="..."||l.length>3)}),S=D(()=>{const o=y.value.reduce((l,p)=>l+(p.amount||0),0),e=y.value.filter(l=>l.approval_status==="approved").reduce((l,p)=>l+(p.amount||0),0),c=y.value.filter(l=>l.approval_status==="pending").reduce((l,p)=>l+(p.amount||0),0),b=y.value.filter(l=>l.approval_status==="rejected").reduce((l,p)=>l+(p.amount||0),0);return{totalExpenses:o,approvedExpenses:e,pendingExpenses:c,rejectedExpenses:b}});function Z(o,e){switch(o){case"thisMonth":return new Date(e.getFullYear(),e.getMonth(),1);case"lastMonth":return new Date(e.getFullYear(),e.getMonth()-1,1);case"thisQuarter":const c=Math.floor(e.getMonth()/3);return new Date(e.getFullYear(),c*3,1);case"thisYear":return new Date(e.getFullYear(),0,1);default:return new Date(0)}}async function tt(){z.value=!0;try{const o=Y.query.applicationId;o?await et(parseInt(o)):(await Promise.all([k.fetchExpenses(),k.fetchApplications()]),T.value=k.expenses||[],A.value=k.applications||[])}catch(o){console.error("Failed to load funding data:",o)}finally{z.value=!1}}async function et(o){try{r.value=await k.fetchReportingData(o),f.value=r.value.application,T.value=r.value.expenses||[]}catch(e){console.error("Failed to load reporting data:",e),await Promise.all([k.fetchExpenses(),k.fetchApplications()]),T.value=k.expenses||[],A.value=k.applications||[]}}function Q(){L.push("/app/funding/expenses/new")}function st(o){L.push(`/app/funding/expenses/${o.id}`)}function at(o){L.push(`/app/funding/expenses/${o.id}/edit`)}async function ot(){if(f.value)try{z.value=!0,await k.exportReport(f.value.id)}catch(o){console.error("Failed to export application report:",o)}finally{z.value=!1}}async function it(){try{z.value=!0;const o={expenses:y.value,applications:A.value,summary:S.value,filters:u.value,generated_at:new Date().toISOString()},e=await rt(()=>import("https://unpkg.com/xlsx/xlsx.mjs"),[]),c=e.utils.book_new(),b=[["Riepilogo Spese di Finanziamento",""],["Data Generazione",new Date().toLocaleDateString("it-IT")],["",""],["Spese Totali",`€ ${S.value.totalExpenses.toLocaleString("it-IT",{minimumFractionDigits:2})}`],["Spese Approvate",`€ ${S.value.approvedExpenses.toLocaleString("it-IT",{minimumFractionDigits:2})}`],["Spese in Attesa",`€ ${S.value.pendingExpenses.toLocaleString("it-IT",{minimumFractionDigits:2})}`],["Spese Rifiutate",`€ ${S.value.rejectedExpenses.toLocaleString("it-IT",{minimumFractionDigits:2})}`]],l=e.utils.aoa_to_sheet(b);e.utils.book_append_sheet(c,l,"Riepilogo");const p=[["Data","Candidatura","Descrizione","Categoria","Importo (€)","Stato Approvazione","Note"]];y.value.forEach(d=>{var C;p.push([new Date(d.expense_date).toLocaleDateString("it-IT"),((C=d.application)==null?void 0:C.project_title)||"N/A",d.description,d.category||"Altro",d.amount,d.approval_status==="approved"?"Approvato":d.approval_status==="pending"?"In Attesa":"Rifiutato",d.notes||""])});const M=e.utils.aoa_to_sheet(p);e.utils.book_append_sheet(c,M,"Dettaglio Spese");const s=[["ID","Titolo Progetto","Bando","Importo Richiesto (€)","Stato","Data Candidatura"]];A.value.forEach(d=>{var C;s.push([d.id,d.project_title,((C=d.opportunity)==null?void 0:C.title)||"N/A",d.requested_amount||0,d.status==="draft"?"Bozza":d.status==="submitted"?"Inviata":d.status==="approved"?"Approvata":d.status==="rejected"?"Rifiutata":d.status,d.submission_date?new Date(d.submission_date).toLocaleDateString("it-IT"):"Non inviata"])});const w=e.utils.aoa_to_sheet(s);e.utils.book_append_sheet(c,w,"Candidature");const $=`Spese_Funding_${new Date().toISOString().split("T")[0]}.xlsx`;e.writeFile(c,$)}catch(o){console.error("Failed to export global report:",o)}finally{z.value=!1}}return dt(u,()=>{v.value=1},{deep:!0}),ut(()=>{tt()}),(o,e)=>{var c,b,l,p,M;return n(),i(j,null,[x(_t,{title:q.value,subtitle:G.value},{actions:ct(()=>[f.value?h("",!0):(n(),i("button",{key:0,onClick:e[0]||(e[0]=s=>B.value=!B.value),class:"btn-secondary"},[x(_,{name:"adjustments-horizontal",size:"sm"}),e[9]||(e[9]=I(" Filtri "))])),f.value?(n(),i("button",{key:1,onClick:ot,class:"btn-secondary",disabled:z.value},[x(_,{name:"document-arrow-down",size:"sm"}),e[10]||(e[10]=I(" Esporta Report "))],8,ht)):h("",!0),f.value?h("",!0):(n(),i("button",{key:2,onClick:it,class:"btn-secondary"},[x(_,{name:"document-text",size:"sm"}),e[11]||(e[11]=I(" Esporta Spese "))])),t("button",{onClick:Q,class:"btn-primary"},[x(_,{name:"plus",size:"sm"}),e[12]||(e[12]=I(" Nuova Spesa "))])]),_:1},8,["title","subtitle"]),B.value?(n(),i("div",yt,[t("div",bt,[t("div",null,[e[14]||(e[14]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Candidatura",-1)),N(t("select",{"onUpdate:modelValue":e[1]||(e[1]=s=>u.value.applicationId=s),class:"form-select"},[e[13]||(e[13]=t("option",{value:""},"Tutte le Candidature",-1)),(n(!0),i(j,null,E(A.value,s=>(n(),i("option",{key:s.id,value:s.id},a(s.project_title),9,wt))),128))],512),[[V,u.value.applicationId]])]),t("div",null,[e[16]||(e[16]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Stato",-1)),N(t("select",{"onUpdate:modelValue":e[2]||(e[2]=s=>u.value.status=s),class:"form-select"},e[15]||(e[15]=[t("option",{value:""},"Tutti gli Stati",-1),t("option",{value:"pending"},"In Attesa",-1),t("option",{value:"approved"},"Approvata",-1),t("option",{value:"rejected"},"Rifiutata",-1)]),512),[[V,u.value.status]])]),t("div",null,[e[18]||(e[18]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Categoria",-1)),N(t("select",{"onUpdate:modelValue":e[3]||(e[3]=s=>u.value.category=s),class:"form-select"},e[17]||(e[17]=[U('<option value="" data-v-bd0ef76a>Tutte le Categorie</option><option value="personnel" data-v-bd0ef76a>Personale</option><option value="equipment" data-v-bd0ef76a>Attrezzature</option><option value="travel" data-v-bd0ef76a>Viaggi</option><option value="external" data-v-bd0ef76a>Servizi Esterni</option><option value="other" data-v-bd0ef76a>Altro</option>',6)]),512),[[V,u.value.category]])]),t("div",null,[e[20]||(e[20]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Periodo",-1)),N(t("select",{"onUpdate:modelValue":e[4]||(e[4]=s=>u.value.dateRange=s),class:"form-select"},e[19]||(e[19]=[U('<option value="" data-v-bd0ef76a>Tutto il Periodo</option><option value="thisMonth" data-v-bd0ef76a>Questo Mese</option><option value="lastMonth" data-v-bd0ef76a>Mese Scorso</option><option value="thisQuarter" data-v-bd0ef76a>Questo Trimestre</option><option value="thisYear" data-v-bd0ef76a>Quest&#39;Anno</option>',5)]),512),[[V,u.value.dateRange]])])])])):h("",!0),f.value&&r.value?(n(),i("div",kt,[e[25]||(e[25]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"}," 🎯 Riepilogo Finanziario Progetto ",-1)),t("div",Dt,[t("div",St,[t("div",zt,[x(_,{name:"currency-euro",class:"text-blue-600",size:"sm"}),t("div",Rt,[e[21]||(e[21]=t("p",{class:"text-sm font-medium text-blue-600"},"Budget Totale",-1)),t("p",Ct,a(m(g)(r.value.financial_summary.total_project_budget)),1)])])]),t("div",jt,[t("div",It,[x(_,{name:"users",class:"text-green-600",size:"sm"}),t("div",Et,[e[22]||(e[22]=t("p",{class:"text-sm font-medium text-green-600"},"Costi Personale",-1)),t("p",At,a(m(g)(r.value.financial_summary.personnel_costs)),1),t("p",Ft,a(r.value.project_costs.total_hours)+"h totali ",1)])])]),t("div",Tt,[t("div",Pt,[x(_,{name:"receipt-percent",class:"text-orange-600",size:"sm"}),t("div",Mt,[e[23]||(e[23]=t("p",{class:"text-sm font-medium text-orange-600"},"Spese Dirette",-1)),t("p",$t,a(m(g)(r.value.financial_summary.direct_expenses)),1)])])]),t("div",Nt,[t("div",Vt,[x(_,{name:"chart-pie",class:"text-purple-600",size:"sm"}),t("div",Lt,[e[24]||(e[24]=t("p",{class:"text-sm font-medium text-purple-600"},"Utilizzo Budget",-1)),t("p",Bt,a(r.value.financial_summary.funding_utilization_percentage.toFixed(1))+"% ",1),t("p",Ot,a(m(g)(r.value.financial_summary.remaining_budget))+" residuo ",1)])])])])])):h("",!0),f.value&&((b=(c=r.value)==null?void 0:c.timesheet_entries)==null?void 0:b.length)>0?(n(),i("div",Qt,[e[30]||(e[30]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"}," ⏱️ Consuntivazione Ore del Progetto ",-1)),r.value.project_costs.personnel_breakdown?(n(),i("div",Ut,[e[26]||(e[26]=t("h4",{class:"text-md font-medium text-gray-700 mb-3"},"Riepilogo per Risorsa",-1)),t("div",Yt,[(n(!0),i(j,null,E(r.value.project_costs.personnel_breakdown,(s,w)=>(n(),i("div",{key:w,class:"bg-gray-50 rounded-lg p-4"},[t("div",qt,[t("div",null,[t("p",Gt,a(w),1),t("p",Ht,a(s.role),1)]),t("div",Xt,[t("p",Wt,a(s.hours)+"h",1),t("p",Jt,a(m(g)(s.cost)),1)])])]))),128))])])):h("",!0),t("div",Kt,[t("table",Zt,[e[27]||(e[27]=t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Data"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Risorsa"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Ore"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Tariffa"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Costo"),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Attività")])],-1)),t("tbody",te,[(n(!0),i(j,null,E(r.value.timesheet_entries.slice(0,20),s=>(n(),i("tr",{key:s.id},[t("td",ee,a(m(O)(s.date)),1),t("td",se,a(s.user_name),1),t("td",ae,a(s.hours)+"h ",1),t("td",oe,a(m(g)(s.hourly_rate))+"/h ",1),t("td",ie,a(m(g)(s.cost)),1),t("td",ne,[t("div",null,[t("p",le,a(s.task_name||"Attività generica"),1),t("p",re,a(s.description),1)])])]))),128))])]),r.value.timesheet_entries.length>20?(n(),i("div",de,[t("p",ue,[I(" Mostrando prime 20 righe di "+a(r.value.timesheet_entries.length)+" totali. ",1),e[28]||(e[28]=t("br",null,null,-1)),e[29]||(e[29]=t("span",{class:"font-medium"},'Usa "Esporta Report" per il dettaglio completo.',-1))])])):h("",!0)])])):h("",!0),t("div",ce,[t("div",pe,[t("div",me,[t("div",xe,[t("div",ge,[x(_,{name:"currency-euro",size:"sm",class:"text-blue-600"})])]),t("div",ve,[t("p",_e,a(f.value?"Spese Dirette":"Spese Totali"),1),t("p",fe,a(m(g)(S.value.totalExpenses)),1),f.value&&r.value?(n(),i("p",he," + "+a(m(g)(r.value.financial_summary.personnel_costs))+" costi personale ",1)):h("",!0)])])]),t("div",ye,[t("div",be,[t("div",we,[t("div",ke,[x(_,{name:"check-circle",size:"sm",class:"text-green-600"})])]),t("div",De,[e[31]||(e[31]=t("p",{class:"text-sm font-medium text-gray-600"},"Approvate",-1)),t("p",Se,a(m(g)(S.value.approvedExpenses)),1)])])]),t("div",ze,[t("div",Re,[t("div",Ce,[t("div",je,[x(_,{name:"clock",size:"sm",class:"text-yellow-600"})])]),t("div",Ie,[e[32]||(e[32]=t("p",{class:"text-sm font-medium text-gray-600"},"In Attesa",-1)),t("p",Ee,a(m(g)(S.value.pendingExpenses)),1)])])]),t("div",Ae,[t("div",Fe,[t("div",Te,[t("div",Pe,[x(_,{name:"exclamation-triangle",size:"sm",class:"text-red-600"})])]),t("div",Me,[e[33]||(e[33]=t("p",{class:"text-sm font-medium text-gray-600"},"Rifiutate",-1)),t("p",$e,a(m(g)(S.value.rejectedExpenses)),1)])])])]),t("div",Ne,[e[44]||(e[44]=t("div",{class:"px-6 py-4 border-b border-gray-200"},[t("h3",{class:"text-lg font-medium text-gray-900"},"Spese di Finanziamento")],-1)),z.value?(n(),i("div",Ve,[x(lt)])):y.value.length===0?(n(),i("div",Le,[x(_,{name:"presentation-chart-bar",size:"xl",class:"text-gray-400 mx-auto mb-4"}),e[35]||(e[35]=t("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"Nessuna Spesa Trovata",-1)),t("p",Be,a(H.value?"Nessuna spesa corrisponde ai filtri attuali.":"Inizia aggiungendo la tua prima spesa di finanziamento."),1),t("button",{onClick:Q,class:"btn-primary"},[x(_,{name:"plus",size:"sm"}),e[34]||(e[34]=I(" Aggiungi Prima Spesa "))])])):(n(),i("div",Oe,[t("table",Qe,[e[36]||(e[36]=t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Descrizione "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Candidatura "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Categoria "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Importo "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Data "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Stato "),t("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"}," Azioni ")])],-1)),t("tbody",Ue,[(n(!0),i(j,null,E(J.value,s=>{var w,$,d;return n(),i("tr",{key:s.id,class:"hover:bg-gray-50"},[t("td",Ye,[t("div",null,[t("div",qe,a(s.description),1),s.notes?(n(),i("div",Ge,a(s.notes),1)):h("",!0)])]),t("td",He,[t("div",Xe,a(((w=s.application)==null?void 0:w.project_title)||"N/A"),1),t("div",We,a(((d=($=s.application)==null?void 0:$.opportunity)==null?void 0:d.source_entity)||""),1)]),t("td",Je,[t("span",Ke,a(s.category||"other"),1)]),t("td",Ze,a(m(g)(s.amount)),1),t("td",ts,a(m(O)(s.expense_date)),1),t("td",es,[x(ft,{status:s.approval_status},null,8,["status"])]),t("td",ss,[t("button",{onClick:C=>st(s),class:"text-blue-600 hover:text-blue-900 mr-3"}," View ",8,as),s.approval_status==="pending"?(n(),i("button",{key:0,onClick:C=>at(s),class:"text-indigo-600 hover:text-indigo-900"}," Edit ",8,os)):h("",!0)])])}),128))])])])),P.value>1?(n(),i("div",is,[t("div",ns,[t("div",ls,[t("button",{onClick:e[5]||(e[5]=s=>v.value--),disabled:v.value===1,class:"btn-pagination"}," Previous ",8,rs),t("button",{onClick:e[6]||(e[6]=s=>v.value++),disabled:v.value===P.value,class:"btn-pagination"}," Next ",8,ds)]),t("div",us,[t("div",null,[t("p",cs," Showing "+a(X.value)+" to "+a(W.value)+" of "+a(y.value.length)+" results ",1)]),t("div",null,[t("nav",ps,[t("button",{onClick:e[7]||(e[7]=s=>v.value--),disabled:v.value===1,class:"btn-pagination-nav"}," Previous ",8,ms),(n(!0),i(j,null,E(K.value,s=>(n(),i("button",{key:s,onClick:w=>v.value=s,class:xt(s===v.value?"btn-pagination-active":"btn-pagination-nav")},a(s),11,xs))),128)),t("button",{onClick:e[8]||(e[8]=s=>v.value++),disabled:v.value===P.value,class:"btn-pagination-nav"}," Next ",8,gs)])])])])])):h("",!0),f.value&&((p=(l=r.value)==null?void 0:l.project_costs)!=null&&p.total_hours)?(n(),i("div",vs,[t("div",_s,[e[37]||(e[37]=t("h3",{class:"text-lg font-medium text-gray-900"}," 📊 Costi Aggregati Progetto Collegato ",-1)),t("p",fs,' Riepilogo costi del personale e risorse dal progetto "'+a(((M=r.value.project)==null?void 0:M.name)||"Collegato")+'" ',1)]),t("div",hs,[t("div",ys,[t("div",bs,[t("div",ws,a(r.value.project_costs.total_hours)+"h ",1),e[38]||(e[38]=t("div",{class:"text-sm text-blue-700"},"Ore Totali",-1))]),t("div",ks,[t("div",Ds,a(m(g)(r.value.project_costs.total_personnel_cost)),1),e[39]||(e[39]=t("div",{class:"text-sm text-green-700"},"Costo Personale",-1))]),t("div",Ss,[t("div",zs,a(m(g)(r.value.project_costs.average_hourly_rate))+"/h ",1),e[40]||(e[40]=t("div",{class:"text-sm text-purple-700"},"Tariffa Media",-1))])]),Object.keys(r.value.project_costs.personnel_breakdown).length>0?(n(),i("div",Rs,[e[42]||(e[42]=t("h4",{class:"text-md font-medium text-gray-700 mb-3"},"Dettaglio per Risorsa",-1)),t("div",Cs,[t("table",js,[e[41]||(e[41]=t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Risorsa "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Ruolo "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Ore "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Costo "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Tariffa Media ")])],-1)),t("tbody",Is,[(n(!0),i(j,null,E(r.value.project_costs.personnel_breakdown,(s,w)=>(n(),i("tr",{key:w},[t("td",Es,a(w),1),t("td",As,a(s.role),1),t("td",Fs,a(s.hours)+"h ",1),t("td",Ts,a(m(g)(s.cost)),1),t("td",Ps,a(m(g)(s.hours>0?s.cost/s.hours:0))+"/h ",1)]))),128))])])])])):h("",!0),t("div",Ms,[t("div",$s,[x(_,{name:"information-circle",size:"sm",class:"text-amber-400 mr-2 mt-0.5"}),e[43]||(e[43]=t("div",{class:"text-sm text-amber-700"},[t("p",null,[t("strong",null,"Nota:"),I(" Questi costi sono calcolati automaticamente dal timesheet del progetto collegato.")]),t("p",{class:"mt-1"},'Per il report completo utilizzare il pulsante "Esporta Report" che include anche:'),t("ul",{class:"list-disc list-inside mt-1 space-y-1"},[t("li",null,"Dettaglio ore per data e attività"),t("li",null,"Spese dirette della candidatura"),t("li",null,"Riepilogo finanziario completo")])],-1))])])])])):h("",!0)])],64)}}},Gs=nt(Ns,[["__scopeId","data-v-bd0ef76a"]]);export{Gs as default};
