import{d as K,r as p,c as v,w as j,o as L,b as w,l as e,g as I,f as G,v as q,t as b,e as E,F as O,q as $,B as g,Y as B,A as H,C as m,n as M,P as Y,j as x}from"./vendor.js";import{c as k,H as z}from"./app.js";const J=K("adminSettings",()=>{const f=p(null),s=p({}),y=p([]),n=p(!1),i=p(null),S=v(()=>f.value&&f.value.company_name&&f.value.vat_number),o=v(()=>{const a=s.value.fattureincloud;return a&&a.has_api_key&&a.company_id}),u=v(()=>{const a=s.value.fattureincloud;return a&&a.last_error}),d=async()=>{try{n.value=!0,i.value=null;const a=await k.get("/api/admin/settings/invoicing");return f.value=a.data.data,a}catch(a){throw i.value=a.message||"Errore nel caricamento delle impostazioni di fatturazione",a}finally{n.value=!1}},_=async a=>{try{n.value=!0,i.value=null;const c=await k.put("/api/admin/settings/invoicing",a);return await d(),c}catch(c){throw i.value=c.message||"Errore nell'aggiornamento delle impostazioni di fatturazione",c}finally{n.value=!1}},h=async()=>{try{n.value=!0,i.value=null;const a=await k.get("/api/admin/settings/integration/fattureincloud");return s.value.fattureincloud=a.data.data,a}catch(a){throw i.value=a.message||"Errore nel caricamento delle impostazioni FattureInCloud",a}finally{n.value=!1}},C=async a=>{try{n.value=!0,i.value=null;const c=await k.put("/api/admin/settings/integration/fattureincloud",a);return await h(),c}catch(c){throw i.value=c.message||"Errore nell'aggiornamento delle impostazioni FattureInCloud",c}finally{n.value=!1}},V=async(a=null)=>{try{n.value=!0,i.value=null;const c=a||{},N=await k.post("/api/admin/settings/integration/fattureincloud/test",c);return s.value.fattureincloud&&(s.value.fattureincloud.last_error=null,s.value.fattureincloud.last_sync_date=new Date().toISOString()),N}catch(c){throw i.value=c.message||"Errore nel test della connessione FattureInCloud",s.value.fattureincloud&&(s.value.fattureincloud.last_error=c.message),c}finally{n.value=!1}},F=async()=>{try{const a=await k.get("/api/admin/settings/integration/providers");return y.value=a.data.data||a.data,a}catch(a){throw i.value=a.message||"Errore nel caricamento dei provider disponibili",a}};return{invoicingSettings:f,integrationSettings:s,availableProviders:y,loading:n,error:i,isInvoicingConfigured:S,isFattureInCloudConfigured:o,hasIntegrationErrors:u,fetchInvoicingSettings:d,updateInvoicingSettings:_,fetchFattureInCloudSettings:h,updateFattureInCloudSettings:C,testFattureInCloudConnection:V,fetchAvailableProviders:F,fetchHealthCheck:async()=>{try{return(await k.get("/api/admin/settings/health")).data}catch(a){throw i.value=a.message||"Errore nel controllo dello stato delle configurazioni",a}},initializeSettings:async()=>{try{n.value=!0,i.value=null,await Promise.all([d(),h(),F()])}catch(a){throw i.value=a.message||"Errore nell'inizializzazione delle impostazioni",a}finally{n.value=!1}},clearError:()=>{i.value=null},resetSettings:()=>{f.value=null,s.value={},y.value=[],i.value=null}}}),Q={class:"flex flex-col md:flex-row md:items-center md:justify-between mb-8"},W={class:"mt-4 md:mt-0"},X=["disabled"],Z={key:0,class:"mb-6 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md p-4"},ee={class:"flex"},te={class:"flex-shrink-0"},ae={class:"ml-3"},re={class:"mt-2 text-sm text-red-700 dark:text-red-300"},oe={class:"ml-auto pl-3"},ne={class:"border-b border-gray-200 dark:border-gray-700 mb-8"},le={class:"-mb-px flex space-x-8"},ie=["onClick"],se={class:"space-y-8"},de={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},ue={class:"px-6 py-4"},ce={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},ge={class:"flex justify-end"},me=["disabled"],pe={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},ye={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},ve={class:"flex items-center justify-between"},fe={class:"flex items-center space-x-2"},be=["disabled"],xe={class:"px-6 py-4"},ke={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},_e={class:"flex items-center"},he={key:0,class:"bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md p-4"},we={class:"flex"},Ce={class:"flex-shrink-0"},Ie={class:"ml-3"},ze={class:"mt-2 text-sm text-red-700 dark:text-red-300"},Se={class:"flex justify-end space-x-3"},Fe=["disabled"],Ee=["disabled"],Ue={__name:"AdminSettings",setup(f){const s=J(),y=p("company"),n=p(!0),i=p(!1),S=p(!1),o=p({company_name:"",vat_number:"",fiscal_code:"",address:"",phone:"",email:"",pec:"",default_vat_rate:22,default_retention_rate:20,default_payment_terms:30,invoice_prefix:"PRE",tax_regime:"ordinario"}),u=p({api_key:"",company_id:"",is_active:!1}),d=v(()=>s.invoicingSettings),_=v(()=>s.integrationSettings),h=v(()=>s.error),C=v(()=>u.value.api_key&&u.value.company_id),V=v(()=>s.hasIntegrationErrors),F=[{id:"company",name:"Azienda",icon:"building-office"},{id:"integrations",name:"Integrazioni",icon:"cog-6-tooth"}],P=()=>{console.log("🔄 Populating invoicing form with:",d.value),d.value?(o.value={company_name:d.value.company_name||"",vat_number:d.value.vat_number||"",fiscal_code:d.value.fiscal_code||"",address:d.value.address||"",phone:d.value.phone||"",email:d.value.email||"",pec:d.value.pec||"",default_vat_rate:d.value.default_vat_rate||22,default_retention_rate:d.value.default_retention_rate||20,default_payment_terms:d.value.default_payment_terms||30,invoice_prefix:d.value.invoice_prefix||"PRE",tax_regime:d.value.tax_regime||"ordinario"},console.log("✅ Invoicing form populated:",o.value)):console.log("❌ No invoicing settings available")},A=()=>{var t;const l=(t=_.value)==null?void 0:t.fattureincloud;console.log("🔄 Populating FIC form with:",l),l?(u.value={api_key:l.api_key||"",company_id:l.company_id||"",is_active:l.is_active||!1},console.log("✅ FIC form populated:",u.value)):console.log("❌ No FIC settings available")},T=async()=>{try{console.log("🔄 Loading settings..."),await s.initializeSettings(),console.log("✅ Settings loaded from store"),console.log("📊 Invoicing settings:",d.value),console.log("🔌 Integration settings:",_.value),P(),A(),console.log("📝 Forms populated"),console.log("📝 Invoicing form:",o.value),console.log("📝 FIC form:",u.value)}catch(l){console.error("❌ Error loading settings:",l)}finally{n.value=!1}},U=async()=>{i.value=!0;try{await s.updateInvoicingSettings(o.value),alert("Impostazioni fatturazione salvate con successo!")}catch(l){console.error("Error saving invoicing settings:",l),alert("Errore nel salvataggio delle impostazioni di fatturazione")}finally{i.value=!1}},a=async()=>{i.value=!0;try{await s.updateFattureInCloudSettings(u.value),alert("Impostazioni FattureInCloud salvate con successo!")}catch(l){console.error("Error saving FattureInCloud settings:",l),alert("Errore nel salvataggio delle impostazioni FattureInCloud")}finally{i.value=!1}},c=async()=>{S.value=!0;try{const l=await s.testFattureInCloudConnection({api_key:u.value.api_key,company_id:u.value.company_id});l.success?alert("Connessione a FattureInCloud riuscita!"):alert(`Errore nella connessione: ${l.message}`)}catch(l){console.error("Error testing connection:",l),alert("Errore nel test della connessione")}finally{S.value=!1}},N=async()=>{try{await Promise.all([U(),a()]),alert("Tutte le configurazioni sono state salvate!")}catch{alert("Errore nel salvataggio di alcune configurazioni")}},R=()=>{s.clearError()};return j(d,l=>{l&&P()},{immediate:!0}),j(_,l=>{l&&l.fattureincloud&&A()},{immediate:!0,deep:!0}),L(()=>{T()}),(l,t)=>{var D;return x(),w("div",null,[e("div",Q,[t[14]||(t[14]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Configurazioni"),e("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Gestisci le impostazioni aziendali e le integrazioni esterne. ")],-1)),e("div",W,[e("button",{onClick:N,disabled:n.value,class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"},[n.value?(x(),G(z,{key:0,name:"arrow-path",size:"sm",className:"animate-spin -ml-1 mr-3"})):I("",!0),q(" "+b(n.value?"Salvataggio...":"Salva Tutto"),1)],8,X)])]),h.value?(x(),w("div",Z,[e("div",ee,[e("div",te,[E(z,{name:"exclamation-circle",size:"md",color:"text-red-400"})]),e("div",ae,[t[15]||(t[15]=e("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"},"Errore",-1)),e("div",re,b(h.value),1)]),e("div",oe,[e("button",{onClick:R,class:"text-red-400 hover:text-red-600"},[E(z,{name:"x-mark",size:"md"})])])])])):I("",!0),e("div",ne,[e("nav",le,[(x(),w(O,null,$(F,r=>e("button",{key:r.id,onClick:Ve=>y.value=r.id,class:M([y.value===r.id?"border-primary-500 text-primary-600 dark:text-primary-400":"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300","whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center"])},[E(z,{name:r.icon,size:"md",className:"mr-2"},null,8,["name"]),q(" "+b(r.name),1)],10,ie)),64))])]),e("div",se,[g(e("div",de,[t[27]||(t[27]=e("div",{class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},[e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Impostazioni Aziendali"),e("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Configura i dati della tua azienda per la fatturazione. ")],-1)),e("div",ue,[e("form",{onSubmit:H(U,["prevent"]),class:"space-y-6"},[e("div",ce,[e("div",null,[t[16]||(t[16]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Ragione Sociale * ",-1)),g(e("input",{"onUpdate:modelValue":t[0]||(t[0]=r=>o.value.company_name=r),type:"text",required:"",class:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:"Nome della tua azienda"},null,512),[[m,o.value.company_name]])]),e("div",null,[t[17]||(t[17]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Partita IVA * ",-1)),g(e("input",{"onUpdate:modelValue":t[1]||(t[1]=r=>o.value.vat_number=r),type:"text",required:"",class:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:"*************"},null,512),[[m,o.value.vat_number]])]),e("div",null,[t[18]||(t[18]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Codice Fiscale ",-1)),g(e("input",{"onUpdate:modelValue":t[2]||(t[2]=r=>o.value.fiscal_code=r),type:"text",class:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:"Codice fiscale azienda"},null,512),[[m,o.value.fiscal_code]])]),e("div",null,[t[19]||(t[19]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Indirizzo ",-1)),g(e("input",{"onUpdate:modelValue":t[3]||(t[3]=r=>o.value.address=r),type:"text",class:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:"Via, numero civico"},null,512),[[m,o.value.address]])]),e("div",null,[t[20]||(t[20]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Telefono ",-1)),g(e("input",{"onUpdate:modelValue":t[4]||(t[4]=r=>o.value.phone=r),type:"tel",class:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:"+39 ************"},null,512),[[m,o.value.phone]])]),e("div",null,[t[21]||(t[21]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Email ",-1)),g(e("input",{"onUpdate:modelValue":t[5]||(t[5]=r=>o.value.email=r),type:"email",class:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:"<EMAIL>"},null,512),[[m,o.value.email]])]),e("div",null,[t[22]||(t[22]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," PEC ",-1)),g(e("input",{"onUpdate:modelValue":t[6]||(t[6]=r=>o.value.pec=r),type:"email",class:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:"<EMAIL>"},null,512),[[m,o.value.pec]])]),e("div",null,[t[23]||(t[23]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," IVA Default (%) ",-1)),g(e("input",{"onUpdate:modelValue":t[7]||(t[7]=r=>o.value.default_vat_rate=r),type:"number",min:"0",max:"100",step:"0.1",class:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:"22.0"},null,512),[[m,o.value.default_vat_rate,void 0,{number:!0}]])]),e("div",null,[t[24]||(t[24]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Ritenuta Default (%) ",-1)),g(e("input",{"onUpdate:modelValue":t[8]||(t[8]=r=>o.value.default_retention_rate=r),type:"number",min:"0",max:"100",step:"0.1",class:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:"20.0"},null,512),[[m,o.value.default_retention_rate,void 0,{number:!0}]])]),e("div",null,[t[25]||(t[25]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Prefisso Fatture ",-1)),g(e("input",{"onUpdate:modelValue":t[9]||(t[9]=r=>o.value.invoice_prefix=r),type:"text",class:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:"FAT"},null,512),[[m,o.value.invoice_prefix]])]),e("div",null,[t[26]||(t[26]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Giorni Pagamento ",-1)),g(e("input",{"onUpdate:modelValue":t[10]||(t[10]=r=>o.value.default_payment_terms=r),type:"number",min:"1",max:"365",class:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:"30"},null,512),[[m,o.value.default_payment_terms,void 0,{number:!0}]])])]),e("div",ge,[e("button",{type:"submit",disabled:n.value,class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"},b(n.value?"Salvataggio...":"Salva Impostazioni"),9,me)])],32)])],512),[[B,y.value==="company"]]),g(e("div",pe,[e("div",ye,[e("div",ve,[t[28]||(t[28]=e("div",null,[e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"FattureInCloud"),e("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Integrazione per l'invio automatico delle fatture. ")],-1)),e("div",fe,[e("span",{class:M([C.value?"bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200":"bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200","px-2 py-1 text-xs font-medium rounded-full"])},b(C.value?"Configurato":"Non configurato"),3),C.value?(x(),w("button",{key:0,onClick:c,disabled:n.value,class:"inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-primary-700 dark:text-primary-300 bg-primary-100 dark:bg-primary-900 hover:bg-primary-200 dark:hover:bg-primary-800 disabled:opacity-50"}," Test Connessione ",8,be)):I("",!0)])])]),e("div",xe,[e("form",{onSubmit:H(a,["prevent"]),class:"space-y-6"},[e("div",ke,[e("div",null,[t[29]||(t[29]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," API Key * ",-1)),g(e("input",{"onUpdate:modelValue":t[11]||(t[11]=r=>u.value.api_key=r),type:"password",required:"",class:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:"La tua API Key di FattureInCloud"},null,512),[[m,u.value.api_key]]),t[30]||(t[30]=e("p",{class:"mt-1 text-xs text-gray-500 dark:text-gray-400"}," Ottieni la tua API Key dal pannello FattureInCloud ",-1))]),e("div",null,[t[31]||(t[31]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Company ID * ",-1)),g(e("input",{"onUpdate:modelValue":t[12]||(t[12]=r=>u.value.company_id=r),type:"text",required:"",class:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500",placeholder:"ID della tua azienda"},null,512),[[m,u.value.company_id]])])]),e("div",_e,[g(e("input",{"onUpdate:modelValue":t[13]||(t[13]=r=>u.value.is_active=r),type:"checkbox",class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 rounded"},null,512),[[Y,u.value.is_active]]),t[32]||(t[32]=e("label",{class:"ml-2 block text-sm text-gray-900 dark:text-white"}," Abilita integrazione FattureInCloud ",-1))]),V.value?(x(),w("div",he,[e("div",we,[e("div",Ce,[E(z,{name:"exclamation-circle",size:"md",color:"text-red-400"})]),e("div",Ie,[t[33]||(t[33]=e("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"},"Errore di connessione",-1)),e("div",ze,b((D=_.value.fattureincloud)==null?void 0:D.last_error),1)])])])):I("",!0),e("div",Se,[u.value.api_key&&u.value.company_id?(x(),w("button",{key:0,type:"button",onClick:c,disabled:n.value,class:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50"}," Test Connessione ",8,Fe)):I("",!0),e("button",{type:"submit",disabled:n.value,class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"},b(n.value?"Salvataggio...":"Salva Configurazione"),9,Ee)])],32)])],512),[[B,y.value==="integrations"]])])])}}};export{Ue as default};
