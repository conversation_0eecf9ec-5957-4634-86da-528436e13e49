import{r as f,c as I,o as Ce,b as o,l as e,e as d,p as V,h as ze,F as k,q as C,g as p,v as u,B as b,V as M,S as T,C as A,n as z,t as n,E as pe,u as je,s as Se,j as l}from"./vendor.js";import{u as Ae}from"./certifications.js";import{u as Ie}from"./useToast.js";import{H as m}from"./app.js";import{_ as Ve}from"./PageHeader.js";const Pe={class:"certification-create"},De={class:"flex mb-4","aria-label":"Breadcrumb"},Ne={class:"inline-flex items-center space-x-1 md:space-x-3"},Be={class:"inline-flex items-center"},Ue={class:"flex items-center"},qe={class:"mb-6"},Me={class:"max-w-4xl mx-auto"},Te={class:"mb-8"},Ee={class:"flex items-center justify-center space-x-4"},Le={class:"bg-white rounded-lg shadow-sm p-6"},Fe={key:0},Oe={class:"text-lg font-semibold text-gray-900 mb-4 flex items-center"},Re={key:0,class:"text-center py-12"},$e={key:1,class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Ge=["onClick"],He={class:"flex items-start justify-between"},Qe={class:"flex-1"},We={class:"font-semibold text-gray-900 text-sm"},Je={class:"text-xs text-gray-600 mt-1"},Ke={key:0,class:"text-xs text-gray-500 mt-2 line-clamp-3"},Xe={class:"mt-2 flex items-center space-x-4 text-xs text-gray-500"},Ye={key:0},Ze={key:0,class:"ml-2"},et={class:"mt-6 flex justify-end"},tt=["disabled"],st={key:1},at={class:"text-lg font-semibold text-gray-900 mb-4 flex items-center"},ot={class:"space-y-6"},lt={class:"space-y-3"},it={class:"flex items-center"},nt={class:"flex items-center"},rt={class:"flex items-center"},dt={key:0,class:"bg-blue-50 border border-blue-200 rounded-lg p-4"},ut={key:0,class:"text-center py-4"},ct=["value"],mt={key:0},gt={key:1,class:"bg-green-50 border border-green-200 rounded-lg p-4"},pt={key:2,class:"bg-gray-50 border border-gray-200 rounded-lg p-4"},vt={class:"flex items-center"},bt={class:"flex justify-between mt-8"},_t=["disabled"],ft={key:2},xt={class:"text-lg font-semibold text-gray-900 mb-4 flex items-center"},yt={key:0,class:"text-center py-12"},ht={key:1,class:"space-y-6"},wt={class:"bg-blue-50 rounded-lg p-4"},kt={class:"font-semibold text-blue-900 mb-3 flex items-center"},Ct={class:"flex items-center justify-between mb-2"},zt={class:"text-lg font-bold text-blue-900"},jt={class:"w-full bg-blue-200 rounded-full h-2"},St={class:"text-xs text-blue-600 mt-2"},At={class:"bg-green-50 rounded-lg p-4"},It={class:"font-semibold text-green-900 mb-3 flex items-center"},Vt={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Pt={class:"flex items-center justify-between mb-2"},Dt={class:"text-lg font-bold text-green-900"},Nt={class:"w-full bg-green-200 rounded-full h-2"},Bt={class:"text-xs text-green-600 mt-2"},Ut={class:"text-xs text-green-600 mt-1 space-y-1"},qt={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Mt={class:"bg-orange-50 rounded-lg p-4"},Tt={class:"font-semibold text-orange-900 mb-3 flex items-center"},Et={class:"text-2xl font-bold text-orange-900 mb-2"},Lt={class:"space-y-2"},Ft={class:"flex justify-between text-orange-700"},Ot={class:"bg-purple-50 rounded-lg p-4"},Rt={class:"font-semibold text-purple-900 mb-3 flex items-center"},$t={class:"text-lg font-bold text-purple-900 mb-2"},Gt={class:"space-y-1"},Ht={class:"flex justify-between text-purple-700"},Qt={class:"mt-6 flex justify-between"},Wt=["disabled"],Jt={key:3},Kt={class:"text-lg font-semibold text-gray-900 mb-4 flex items-center"},Xt={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Yt={class:"space-y-4"},Zt=["placeholder"],es={class:"space-y-4"},ts={key:0,class:"bg-blue-50 rounded-lg p-4"},ss={class:"text-sm text-blue-700 space-y-1"},as={class:"mt-6 flex justify-between"},os=["disabled"],cs={__name:"CertificationCreate",setup(ls){const ve=Se(),be=je(),j=Ae(),{addToast:S}=Ie(),g=f(1),P=f(!1),D=f(!1),y=f(!1),r=f(null),_e=f(null),a=f({standard_code:"",project_action:"create",project_name:"",existing_project_id:null,description:"",target_date:"",team_lead_id:"",estimated_budget:null,priority:"medium",initial_score:0}),E=f([]),N=f(!1),L=[{id:1,title:"Standard"},{id:2,title:"Progetto"},{id:3,title:"Analisi AI"},{id:4,title:"Dettagli"}],F=I(()=>{const i=j.standardsByCategory,t=[];return Object.values(i).forEach(c=>{Array.isArray(c)?t.push(...c):Object.values(c).forEach(_=>{t.push(_)})}),t}),O=I(()=>a.value.standard_code&&B.value&&(a.value.project_action==="create"?a.value.project_name&&a.value.project_name.trim().length>3:!0)),B=I(()=>a.value.project_action==="none"?!0:a.value.project_action==="existing"?a.value.existing_project_id!==null&&a.value.existing_project_id!=="":a.value.project_action==="create"?a.value.project_name&&a.value.project_name.trim().length>3:!1),fe=I(()=>{var i,t,c;if((c=(t=(i=r.value)==null?void 0:i.ai_insights)==null?void 0:t.cost_estimation)!=null&&c.total_range){const _=(r.value.ai_insights.cost_estimation.total_range.min+r.value.ai_insights.cost_estimation.total_range.max)/2;return`Suggerito: €${h(Math.round(_))}`}return"Budget stimato"}),xe=async()=>{P.value=!0;try{await j.fetchStandardsCatalog();const i=be.query.standard_code;if(i){const t=F.value.find(c=>c.code===i);t&&R(t)}}catch{S("Errore nel caricamento del catalogo","error")}finally{P.value=!1}},ye=async()=>{N.value=!0;try{const i=await j.fetchAvailableProjects();i.success&&(E.value=i.data)}catch{S("Errore nel caricamento progetti","error")}finally{N.value=!1}},R=i=>{a.value.standard_code=i.code,_e.value=i,a.value.project_name||(a.value.project_name=`Certificazione ${i.name}`)},U=async()=>{g.value===1&&a.value.standard_code?(g.value=2,a.value.project_action==="existing"&&await ye()):g.value===2?(g.value=3,await he()):g.value===3&&(g.value=4)},q=()=>{g.value>1&&g.value--},he=async()=>{var i,t,c,_;D.value=!0;try{const v=await j.fetchAIInsights(a.value.standard_code);if(v.success){if(r.value=v.data,(t=(i=v.data.ai_insights)==null?void 0:i.cost_estimation)!=null&&t.total_range&&!a.value.estimated_budget){const x=(v.data.ai_insights.cost_estimation.total_range.min+v.data.ai_insights.cost_estimation.total_range.max)/2;a.value.estimated_budget=Math.round(x)}if((_=(c=v.data.ai_insights)==null?void 0:c.timeline_prediction)!=null&&_.estimated_months&&!a.value.target_date){const x=new Date;x.setMonth(x.getMonth()+v.data.ai_insights.timeline_prediction.estimated_months),a.value.target_date=x.toISOString().split("T")[0]}a.value.initial_score=v.data.combined_score||0}}catch{S("Errore nel caricamento dell'analisi AI","error")}finally{D.value=!1}},we=async()=>{var i;if(O.value){y.value=!0;try{const t={...a.value,ai_insights:(i=r.value)==null?void 0:i.ai_insights},c=await j.createCertificationWithAI(t);c.success&&(S("Certificazione creata con successo!","success"),ve.push(`/app/certifications/${c.data.id}`))}catch{S("Errore nella creazione della certificazione","error")}finally{y.value=!1}}},h=i=>i?new Intl.NumberFormat("it-IT").format(i):"0",ke=i=>({quality:"Qualità",security:"Sicurezza",environmental:"Ambientale",privacy:"Privacy",regulatory:"Normativo"})[i]||i;return Ce(()=>{xe()}),(i,t)=>{var _,v,x,$,G,H,Q,W,J,K,X,Y,Z,ee,te,se,ae,oe,le,ie,ne,re,de,ue,ce,me,ge;const c=ze("router-link");return l(),o("div",Pe,[e("nav",De,[e("ol",Ne,[e("li",Be,[d(c,{to:"/app/certifications/dashboard",class:"inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600"},{default:V(()=>[d(m,{name:"home",class:"h-4 w-4 mr-2"}),t[11]||(t[11]=u(" Certificazioni "))]),_:1,__:[11]})]),e("li",null,[e("div",Ue,[d(m,{name:"chevron-right",class:"h-4 w-4 text-gray-400"}),t[12]||(t[12]=e("span",{class:"ml-1 text-sm font-medium text-gray-500 md:ml-2"},"Nuova Certificazione",-1))])])])]),e("div",qe,[d(Ve,{title:"Nuova Certificazione",subtitle:"Crea una nuova certificazione aziendale con supporto AI",icon:"plus-circle","icon-color":"text-purple-600"},{actions:V(()=>[d(c,{to:"/app/certifications/catalog",class:"btn-secondary flex items-center gap-2"},{default:V(()=>[d(m,{name:"book-open",size:"sm"}),t[13]||(t[13]=u(" Sfoglia Catalogo "))]),_:1,__:[13]}),d(c,{to:"/app/certifications/dashboard",class:"btn-secondary flex items-center gap-2"},{default:V(()=>[d(m,{name:"arrow-left",size:"sm"}),t[14]||(t[14]=u(" Dashboard "))]),_:1,__:[14]})]),_:1})]),e("div",Me,[e("div",Te,[e("div",Ee,[(l(),o(k,null,C(L,(s,w)=>e("div",{key:s.id,class:"flex items-center"},[e("div",{class:z(["flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium",g.value>=w+1?"bg-brand-primary-600 text-white":"bg-gray-200 text-gray-600"])},n(w+1),3),e("span",{class:z(["ml-2 text-sm font-medium",g.value>=w+1?"text-blue-600":"text-gray-500"])},n(s.title),3),w<L.length-1?(l(),o("div",{key:0,class:z(["ml-4 w-16 h-0.5",g.value>w+1?"bg-brand-primary-600":"bg-gray-200"])},null,2)):p("",!0)])),64))])]),e("div",Le,[g.value===1?(l(),o("div",Fe,[e("h2",Oe,[d(m,{name:"shield-check",class:"h-6 w-6 mr-2 text-blue-600"}),t[15]||(t[15]=u(" Seleziona Standard di Certificazione "))]),P.value?(l(),o("div",Re,t[16]||(t[16]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"},null,-1),e("p",{class:"text-gray-500"},"Caricamento standard...",-1)]))):(l(),o("div",$e,[(l(!0),o(k,null,C(F.value,s=>(l(),o("div",{key:s.code,class:z(["border border-gray-200 rounded-lg p-4 hover:border-blue-300 cursor-pointer transition-colors",{"border-blue-500 bg-blue-50":a.value.standard_code===s.code}]),onClick:w=>R(s)},[e("div",He,[e("div",Qe,[e("h3",We,n(s.name),1),e("p",Je,n(ke(s.category)),1),s.description?(l(),o("p",Ke,n(s.description),1)):p("",!0),e("div",Xe,[e("span",null,"Validità: "+n(s.typical_validity_years)+" anni",1),s.estimated_cost_min?(l(),o("span",Ye,"€"+n(h(s.estimated_cost_min))+" - €"+n(h(s.estimated_cost_max)),1)):p("",!0)])]),a.value.standard_code===s.code?(l(),o("div",Ze,[d(m,{name:"check-circle",class:"h-5 w-5 text-blue-500"})])):p("",!0)])],10,Ge))),128))])),e("div",et,[e("button",{onClick:U,disabled:!a.value.standard_code,class:"btn-primary"}," Continua ",8,tt)])])):p("",!0),g.value===2?(l(),o("div",st,[e("h2",at,[d(m,{name:"folder-open",class:"h-6 w-6 mr-2 text-blue-600"}),t[17]||(t[17]=u(" Collegamento Progetto "))]),e("div",ot,[e("div",null,[t[21]||(t[21]=e("label",{class:"block text-sm font-medium text-gray-700 mb-3"}," Come vuoi gestire il progetto per questa certificazione? ",-1)),e("div",lt,[e("label",it,[b(e("input",{"onUpdate:modelValue":t[0]||(t[0]=s=>a.value.project_action=s),type:"radio",value:"none",class:"form-radio text-brand-primary-600"},null,512),[[M,a.value.project_action]]),t[18]||(t[18]=e("span",{class:"ml-3 text-sm text-gray-700"},[e("strong",null,"Nessun progetto"),u(" - Gestisci la certificazione in modo autonomo ")],-1))]),e("label",nt,[b(e("input",{"onUpdate:modelValue":t[1]||(t[1]=s=>a.value.project_action=s),type:"radio",value:"existing",class:"form-radio text-brand-primary-600"},null,512),[[M,a.value.project_action]]),t[19]||(t[19]=e("span",{class:"ml-3 text-sm text-gray-700"},[e("strong",null,"Collega a progetto esistente"),u(" - Associa a un progetto già presente ")],-1))]),e("label",rt,[b(e("input",{"onUpdate:modelValue":t[2]||(t[2]=s=>a.value.project_action=s),type:"radio",value:"create",class:"form-radio text-brand-primary-600"},null,512),[[M,a.value.project_action]]),t[20]||(t[20]=e("span",{class:"ml-3 text-sm text-gray-700"},[e("strong",null,"Crea nuovo progetto"),u(" - Crea automaticamente un progetto dedicato ")],-1))])])]),a.value.project_action==="existing"?(l(),o("div",dt,[t[24]||(t[24]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Seleziona Progetto Esistente * ",-1)),N.value?(l(),o("div",ut,t[22]||(t[22]=[e("div",{class:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mx-auto mb-2"},null,-1),e("p",{class:"text-sm text-gray-500"},"Caricamento progetti...",-1)]))):b((l(),o("select",{key:1,"onUpdate:modelValue":t[3]||(t[3]=s=>a.value.existing_project_id=s),class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500",required:""},[t[23]||(t[23]=e("option",{value:""},"Seleziona un progetto...",-1)),(l(!0),o(k,null,C(E.value,s=>(l(),o("option",{key:s.id,value:s.id},[u(n(s.name)+" ("+n(s.status)+") ",1),s.certifications_count>0?(l(),o("span",mt," - "+n(s.certifications_count)+" certificazioni ",1)):p("",!0)],8,ct))),128))],512)),[[T,a.value.existing_project_id]]),t[25]||(t[25]=e("p",{class:"text-xs text-gray-500 mt-2"}," La certificazione sarà collegata al progetto selezionato e visibile nelle sue attività ",-1))])):p("",!0),a.value.project_action==="create"?(l(),o("div",gt,[t[26]||(t[26]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Nome del Nuovo Progetto * ",-1)),b(e("input",{"onUpdate:modelValue":t[4]||(t[4]=s=>a.value.project_name=s),type:"text",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500",placeholder:"es. Certificazione ISO 9001:2015",required:""},null,512),[[A,a.value.project_name]]),t[27]||(t[27]=e("p",{class:"text-xs text-gray-500 mt-2"}," Verrà creato automaticamente un nuovo progetto dedicato a questa certificazione ",-1))])):p("",!0),a.value.project_action==="none"?(l(),o("div",pt,[e("div",vt,[d(m,{name:"information-circle",class:"h-5 w-5 text-gray-400 mr-2"}),t[28]||(t[28]=e("p",{class:"text-sm text-gray-600"}," La certificazione sarà gestita in modo autonomo, senza collegamento a progetti. Potrai sempre collegare un progetto in seguito. ",-1))])])):p("",!0)]),e("div",bt,[e("button",{onClick:q,class:"btn-secondary flex items-center"},[d(m,{name:"arrow-left",size:"sm",class:"mr-2"}),t[29]||(t[29]=u(" Indietro "))]),e("button",{onClick:U,disabled:!B.value,class:z(["btn-primary flex items-center",{"opacity-50 cursor-not-allowed":!B.value}])},[t[30]||(t[30]=u(" Continua ")),d(m,{name:"arrow-right",size:"sm",class:"ml-2"})],10,_t)])])):p("",!0),g.value===3?(l(),o("div",ft,[e("h2",xt,[d(m,{name:"cpu-chip",class:"h-6 w-6 mr-2 text-blue-600"}),t[31]||(t[31]=u(" Analisi AI della Preparazione "))]),D.value?(l(),o("div",yt,t[32]||(t[32]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"},null,-1),e("p",{class:"text-gray-500"},"Analizzando la preparazione con AI...",-1),e("p",{class:"text-xs text-gray-400 mt-2"},"Questo può richiedere alcuni secondi",-1)]))):r.value?(l(),o("div",ht,[e("div",wt,[e("h3",kt,[d(m,{name:"chart-bar",class:"h-5 w-5 mr-2"}),t[33]||(t[33]=u(" Livello di Preparazione DatPortal "))]),e("div",Ct,[t[34]||(t[34]=e("span",{class:"text-sm text-blue-700"},"Score Compliance Piattaforma",-1)),e("span",zt,n(((_=r.value.platform_analysis)==null?void 0:_.platform_compliance_score)||0)+"% ",1)]),e("div",jt,[e("div",{class:"h-2 rounded-full bg-blue-600 transition-all duration-300",style:pe({width:(((v=r.value.platform_analysis)==null?void 0:v.platform_compliance_score)||0)+"%"})},null,4)]),e("p",St,n(((x=r.value.platform_analysis)==null?void 0:x.total_features_analyzed)||0)+" funzionalità DatPortal analizzate ",1)]),e("div",At,[e("h3",It,[d(m,{name:"light-bulb",class:"h-5 w-5 mr-2"}),t[35]||(t[35]=u(" Valutazione AI Readiness "))]),e("div",Vt,[e("div",null,[e("div",Pt,[t[36]||(t[36]=e("span",{class:"text-sm text-green-700"},"Score Generale",-1)),e("span",Dt,n(((G=($=r.value.ai_insights)==null?void 0:$.readiness_assessment)==null?void 0:G.overall_score)||0)+"% ",1)]),e("div",Nt,[e("div",{class:"h-2 rounded-full bg-green-600 transition-all duration-300",style:pe({width:(((Q=(H=r.value.ai_insights)==null?void 0:H.readiness_assessment)==null?void 0:Q.overall_score)||0)+"%"})},null,4)]),e("p",Bt,n(((J=(W=r.value.ai_insights)==null?void 0:W.readiness_assessment)==null?void 0:J.readiness_level)||"Da valutare"),1)]),e("div",null,[t[37]||(t[37]=e("span",{class:"text-sm font-medium text-green-700"},"Punti di Forza:",-1)),e("ul",Ut,[(l(!0),o(k,null,C(((X=(K=r.value.ai_insights)==null?void 0:K.readiness_assessment)==null?void 0:X.strengths)||[],s=>(l(),o("li",{key:s,class:"flex items-center"},[d(m,{name:"check",class:"h-3 w-3 mr-1 flex-shrink-0"}),u(" "+n(s),1)]))),128))])])])]),e("div",qt,[e("div",Mt,[e("h3",Tt,[d(m,{name:"clock",class:"h-5 w-5 mr-2"}),t[38]||(t[38]=u(" Timeline Stimata "))]),e("div",Et,n(((Z=(Y=r.value.ai_insights)==null?void 0:Y.timeline_prediction)==null?void 0:Z.estimated_months)||6)+" mesi ",1),e("div",Lt,[(l(!0),o(k,null,C(((te=(ee=r.value.ai_insights)==null?void 0:ee.timeline_prediction)==null?void 0:te.phases)||[],s=>(l(),o("div",{key:s.name,class:"text-xs"},[e("div",Ft,[e("span",null,n(s.name),1),e("span",null,n(s.duration_weeks)+" sett.",1)])]))),128))])]),e("div",Ot,[e("h3",Rt,[d(m,{name:"currency-euro",class:"h-5 w-5 mr-2"}),t[39]||(t[39]=u(" Stima Costi "))]),e("div",$t," €"+n(h(((oe=(ae=(se=r.value.ai_insights)==null?void 0:se.cost_estimation)==null?void 0:ae.total_range)==null?void 0:oe.min)||0))+" - €"+n(h(((ne=(ie=(le=r.value.ai_insights)==null?void 0:le.cost_estimation)==null?void 0:ie.total_range)==null?void 0:ne.max)||0)),1),e("div",Gt,[(l(!0),o(k,null,C(((de=(re=r.value.ai_insights)==null?void 0:re.cost_estimation)==null?void 0:de.breakdown)||[],s=>(l(),o("div",{key:s.category,class:"text-xs"},[e("div",Ht,[e("span",null,n(s.category),1),e("span",null,"€"+n(h(s.amount)),1)])]))),128))])])])])):p("",!0),e("div",Qt,[e("button",{onClick:q,class:"btn-secondary"}," Indietro "),e("button",{onClick:U,disabled:!r.value,class:"btn-primary"}," Continua ",8,Wt)])])):p("",!0),g.value===4?(l(),o("div",Jt,[e("h2",Kt,[d(m,{name:"clipboard-document-check",class:"h-6 w-6 mr-2 text-blue-600"}),t[40]||(t[40]=u(" Dettagli Progetto Certificazione "))]),e("div",Xt,[e("div",Yt,[e("div",null,[t[41]||(t[41]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Nome Progetto * ",-1)),b(e("input",{"onUpdate:modelValue":t[5]||(t[5]=s=>a.value.project_name=s),type:"text",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500",placeholder:"es. Certificazione ISO 9001:2015",required:""},null,512),[[A,a.value.project_name]])]),e("div",null,[t[42]||(t[42]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Descrizione ",-1)),b(e("textarea",{"onUpdate:modelValue":t[6]||(t[6]=s=>a.value.description=s),rows:"3",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500",placeholder:"Descrizione dettagliata del progetto di certificazione..."},null,512),[[A,a.value.description]])]),e("div",null,[t[43]||(t[43]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Data Target Completamento ",-1)),b(e("input",{"onUpdate:modelValue":t[7]||(t[7]=s=>a.value.target_date=s),type:"date",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"},null,512),[[A,a.value.target_date]])]),e("div",null,[t[44]||(t[44]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Budget Stimato (€) ",-1)),b(e("input",{"onUpdate:modelValue":t[8]||(t[8]=s=>a.value.estimated_budget=s),type:"number",min:"0",step:"100",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500",placeholder:fe.value},null,8,Zt),[[A,a.value.estimated_budget,void 0,{number:!0}]])])]),e("div",es,[e("div",null,[t[46]||(t[46]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Team Lead ",-1)),b(e("select",{"onUpdate:modelValue":t[9]||(t[9]=s=>a.value.team_lead_id=s),class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"},t[45]||(t[45]=[e("option",{value:""},"Seleziona Team Lead",-1),e("option",{value:"1"},"Mario Rossi",-1),e("option",{value:"2"},"Laura Bianchi",-1),e("option",{value:"3"},"Giuseppe Verdi",-1)]),512),[[T,a.value.team_lead_id]])]),e("div",null,[t[48]||(t[48]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Priorità ",-1)),b(e("select",{"onUpdate:modelValue":t[10]||(t[10]=s=>a.value.priority=s),class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"},t[47]||(t[47]=[e("option",{value:"low"},"Bassa",-1),e("option",{value:"medium"},"Media",-1),e("option",{value:"high"},"Alta",-1),e("option",{value:"critical"},"Critica",-1)]),512),[[T,a.value.priority]])]),(ce=(ue=r.value)==null?void 0:ue.ai_insights)!=null&&ce.team_recommendations?(l(),o("div",ts,[t[52]||(t[52]=e("h4",{class:"font-medium text-blue-900 mb-2"},"💡 Suggerimenti AI",-1)),e("div",ss,[e("div",null,[t[49]||(t[49]=e("strong",null,"Ruoli richiesti:",-1)),u(" "+n((me=r.value.ai_insights.team_recommendations.required_roles)==null?void 0:me.join(", ")),1)]),e("div",null,[t[50]||(t[50]=e("strong",null,"Ore stimate:",-1)),u(" "+n(r.value.ai_insights.team_recommendations.estimated_effort_hours),1)]),e("div",null,[t[51]||(t[51]=e("strong",null,"Competenze chiave:",-1)),u(" "+n((ge=r.value.ai_insights.team_recommendations.key_competencies)==null?void 0:ge.join(", ")),1)])])])):p("",!0)])]),e("div",as,[e("button",{onClick:q,class:"btn-secondary"}," Indietro "),e("button",{onClick:we,disabled:!O.value||y.value,class:"btn-primary flex items-center"},[d(m,{name:y.value?"arrow-path":"check",size:"sm",class:z([{"animate-spin":y.value},"mr-2"])},null,8,["name","class"]),u(" "+n(y.value?"Creazione...":"Crea Certificazione"),1)],8,os)])])):p("",!0)])])])}}};export{cs as default};
