import{r as y,c as k,b as p,j as l,l as e,e as u,A as V,B as h,g as b,C as O,n as B,t as c,S as A,O as te,P,f as w,v as D,o as se,p as I,x as S,F as N,s as oe,q as R}from"./vendor.js";import{_ as q,d as L,H as r,f as ie,b as le}from"./app.js";import{u as ae}from"./useFormatters.js";import{_ as ne}from"./ListPageTemplate.js";import{E as re}from"./EditTopicModal.js";import{C as de}from"./ConfirmationModal.js";import"./formatters.js";import"./Pagination.js";const ce={class:"fixed inset-0 z-50 overflow-y-auto","aria-labelledby":"modal-title",role:"dialog","aria-modal":"true"},ue={class:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},me={class:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full"},pe={class:"bg-white dark:bg-gray-800 px-6 pt-6 pb-4"},ve={class:"flex items-center justify-between"},xe={class:"bg-white dark:bg-gray-800 px-6 pb-6 space-y-6"},ge={key:0,class:"mt-1 text-sm text-red-600 dark:text-red-400"},fe={key:0,class:"mt-1 text-sm text-red-600 dark:text-red-400"},be={class:"space-y-3"},ye={class:"flex items-center"},_e={class:"flex items-center"},ke={class:"bg-gray-50 dark:bg-gray-700 px-6 py-3 flex justify-end space-x-3"},he=["disabled"],we=["disabled"],Ce={__name:"CreateTopicModal",emits:["close","created"],setup(G,{emit:M}){const m=M,T=L(),i=y({title:"",description:"",category:"",is_pinned:!1,is_locked:!1}),g=y(!1),a=y({}),C=k(()=>i.value.title.trim()&&i.value.description.trim()),f=()=>(a.value={},i.value.title.trim()?i.value.title.length>255&&(a.value.title="Il titolo non può superare i 255 caratteri"):a.value.title="Il titolo è obbligatorio",i.value.description.trim()?i.value.description.length>5e3&&(a.value.description="La descrizione non può superare i 5000 caratteri"):a.value.description="La descrizione è obbligatoria",Object.keys(a.value).length===0),z=async()=>{var v,s,n;if(f()){g.value=!0;try{const _={title:i.value.title.trim(),content:i.value.description.trim(),category:i.value.category||null,is_pinned:i.value.is_pinned,is_locked:i.value.is_locked},E=await T.createTopic(_);m("created",E),m("close")}catch(_){console.error("Errore nella creazione del topic:",_),((v=_.response)==null?void 0:v.status)===400&&((n=(s=_.response)==null?void 0:s.data)!=null&&n.errors)?a.value=_.response.data.errors:a.value.general="Errore nella creazione del topic. Riprova più tardi."}finally{g.value=!1}}};return(v,s)=>(l(),p("div",ce,[e("div",ue,[e("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity","aria-hidden":"true",onClick:s[0]||(s[0]=n=>v.$emit("close"))}),e("div",me,[e("div",pe,[e("div",ve,[s[8]||(s[8]=e("h3",{class:"text-lg leading-6 font-medium text-gray-900 dark:text-white",id:"modal-title"}," Nuovo Topic ",-1)),e("button",{onClick:s[1]||(s[1]=n=>v.$emit("close")),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},[u(r,{name:"x-mark",size:"md"})])])]),e("form",{onSubmit:V(z,["prevent"])},[e("div",xe,[e("div",null,[s[9]||(s[9]=e("label",{for:"title",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Titolo * ",-1)),h(e("input",{id:"title","onUpdate:modelValue":s[2]||(s[2]=n=>i.value.title=n),type:"text",required:"",placeholder:"Inserisci il titolo del topic...",class:B(["input w-full",{"border-red-300":a.value.title}])},null,2),[[O,i.value.title]]),a.value.title?(l(),p("p",ge,c(a.value.title),1)):b("",!0)]),e("div",null,[s[11]||(s[11]=e("label",{for:"category",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Categoria ",-1)),h(e("select",{id:"category","onUpdate:modelValue":s[3]||(s[3]=n=>i.value.category=n),class:"select w-full"},s[10]||(s[10]=[te('<option value="" data-v-4e231601>Seleziona una categoria</option><option value="general" data-v-4e231601>Generale</option><option value="announcements" data-v-4e231601>Annunci</option><option value="discussions" data-v-4e231601>Discussioni</option><option value="help" data-v-4e231601>Aiuto</option><option value="suggestions" data-v-4e231601>Suggerimenti</option><option value="technical" data-v-4e231601>Tecnico</option><option value="other" data-v-4e231601>Altro</option>',8)]),512),[[A,i.value.category]])]),e("div",null,[s[12]||(s[12]=e("label",{for:"description",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Descrizione * ",-1)),h(e("textarea",{id:"description","onUpdate:modelValue":s[4]||(s[4]=n=>i.value.description=n),required:"",rows:"8",placeholder:"Descrivi il topic in dettaglio...",class:B(["input w-full resize-none",{"border-red-300":a.value.description}])},null,2),[[O,i.value.description]]),a.value.description?(l(),p("p",fe,c(a.value.description),1)):b("",!0)]),e("div",be,[e("div",ye,[h(e("input",{id:"is_pinned","onUpdate:modelValue":s[5]||(s[5]=n=>i.value.is_pinned=n),type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded dark:border-gray-600 dark:bg-gray-700"},null,512),[[P,i.value.is_pinned]]),s[13]||(s[13]=e("label",{for:"is_pinned",class:"ml-2 block text-sm text-gray-700 dark:text-gray-300"}," Fissa in evidenza ",-1))]),e("div",_e,[h(e("input",{id:"is_locked","onUpdate:modelValue":s[6]||(s[6]=n=>i.value.is_locked=n),type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded dark:border-gray-600 dark:bg-gray-700"},null,512),[[P,i.value.is_locked]]),s[14]||(s[14]=e("label",{for:"is_locked",class:"ml-2 block text-sm text-gray-700 dark:text-gray-300"}," Blocca le risposte ",-1))])])]),e("div",ke,[e("button",{type:"button",onClick:s[7]||(s[7]=n=>v.$emit("close")),class:"btn btn-secondary",disabled:g.value}," Annulla ",8,he),e("button",{type:"submit",class:"btn btn-primary",disabled:g.value||!C.value},[g.value?(l(),w(ie,{key:0,size:"sm",class:"mr-2"})):(l(),w(r,{key:1,name:"plus",size:"sm",class:"mr-2"})),D(" "+c(g.value?"Creazione...":"Crea Topic"),1)],8,we)])],32)])])]))}},ze=q(Ce,[["__scopeId","data-v-4e231601"]]),Te={class:"grid grid-cols-1 gap-5 sm:grid-cols-4"},$e={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},Se={class:"p-5"},Me={class:"flex items-center"},Ee={class:"flex-shrink-0"},Ie={class:"ml-5 w-0 flex-1"},Ne={class:"text-lg font-medium text-gray-900 dark:text-white"},Ve={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},Ae={class:"p-5"},De={class:"flex items-center"},Ue={class:"flex-shrink-0"},je={class:"ml-5 w-0 flex-1"},Fe={class:"text-lg font-medium text-gray-900 dark:text-white"},Oe={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},Be={class:"p-5"},Pe={class:"flex items-center"},Re={class:"flex-shrink-0"},qe={class:"ml-5 w-0 flex-1"},Le={class:"text-lg font-medium text-gray-900 dark:text-white"},Ge={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},He={class:"p-5"},We={class:"flex items-center"},Je={class:"flex-shrink-0"},Ke={class:"ml-5 w-0 flex-1"},Qe={class:"text-lg font-medium text-gray-900 dark:text-white"},Xe={class:"flex flex-col sm:flex-row gap-3 sm:gap-4"},Ye={class:"flex-1 min-w-0"},Ze=["value"],et={class:"flex-1 min-w-0 sm:max-w-xs"},tt={key:0,class:"flex justify-center items-center h-64"},st={key:1,class:"text-center py-12"},ot={key:2,class:"p-6"},it={class:"space-y-4"},lt=["onClick"],at={class:"flex items-start justify-between"},nt={class:"flex-1 min-w-0"},rt={class:"flex items-center space-x-2 mb-2"},dt={class:"text-lg font-semibold text-gray-900 dark:text-white truncate"},ct={class:"text-gray-600 dark:text-gray-400 text-sm line-clamp-2 mb-3"},ut={class:"flex items-center justify-between text-sm text-gray-500 dark:text-gray-400"},mt={class:"flex items-center space-x-4"},pt={class:"flex items-center space-x-1"},vt={class:"flex items-center space-x-1"},xt={class:"flex items-center space-x-1"},gt={class:"flex items-center space-x-4"},ft={class:"flex items-center space-x-1"},bt={class:"flex items-center space-x-1"},yt={key:0,class:"flex items-center space-x-1"},_t={class:"ml-4 flex space-x-2"},kt=["onClick"],ht=["onClick"],wt={__name:"ForumIndex",setup(G){const M=oe(),m=L(),T=le(),{formatDate:i}=ae(),g=y(!1),a=y(!1),C=y(!1),f=y(null),z=y(""),v=y(""),s=k(()=>T.hasPermission("PERMISSION_VIEW_COMMUNICATION")),n=k(()=>T.hasPermission("PERMISSION_MODERATE_FORUM")),_=k(()=>[...new Set(m.topics.map(o=>o.category).filter(Boolean))]),E=k(()=>m.topics.filter(o=>!o.is_locked).length),H=k(()=>m.topics.filter(o=>o.is_pinned).length),W=k(()=>m.topics.reduce((o,t)=>o+(t.comments_count||0),0)),U=k(()=>{let o=m.topics;return z.value&&(o=o.filter(t=>t.category===z.value)),v.value==="pinned"?o=o.filter(t=>t.is_pinned):v.value==="locked"?o=o.filter(t=>t.is_locked):v.value==="active"&&(o=o.filter(t=>!t.is_locked)),o}),j=o=>!o||typeof o!="object"?0:Object.values(o).reduce((t,$)=>t+$,0),J=o=>{M.push(`/app/communications/forum/topics/${o.id}`)},K=o=>{f.value=o,a.value=!0},Q=o=>{f.value=o,C.value=!0},X=o=>{g.value=!1},Y=o=>{a.value=!1,f.value=null},Z=async()=>{try{await m.deleteTopic(f.value.id),C.value=!1,f.value=null}catch(o){console.error("Errore nell'eliminazione del topic:",o)}};return se(async()=>{try{await m.fetchForumTopics()}catch(o){console.error("Errore nel caricamento dei topic:",o)}}),(o,t)=>{var $;return l(),p(N,null,[u(ne,{title:"Forum Aziendale",subtitle:"Discussioni e argomenti della community aziendale",data:S(m).topics,loading:S(m).loading.topics,"can-create":s.value,"create-label":"Nuovo Topic","search-placeholder":"Cerca topic...","empty-message":"Nessun topic nel forum","results-label":"topic",onCreate:t[2]||(t[2]=x=>g.value=!0)},{stats:I(()=>[e("div",Te,[e("div",$e,[e("div",Se,[e("div",Me,[e("div",Ee,[u(r,{name:"chat-bubble-left",size:"lg",class:"text-blue-500"})]),e("div",Ie,[e("dl",null,[t[6]||(t[6]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"},"Totali",-1)),e("dd",Ne,c(S(m).topics.length),1)])])])])]),e("div",Ve,[e("div",Ae,[e("div",De,[e("div",Ue,[u(r,{name:"fire",size:"lg",class:"text-red-500"})]),e("div",je,[e("dl",null,[t[7]||(t[7]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"},"Attivi",-1)),e("dd",Fe,c(E.value),1)])])])])]),e("div",Oe,[e("div",Be,[e("div",Pe,[e("div",Re,[u(r,{name:"bookmark",size:"lg",class:"text-yellow-500"})]),e("div",qe,[e("dl",null,[t[8]||(t[8]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"},"Pinned",-1)),e("dd",Le,c(H.value),1)])])])])]),e("div",Ge,[e("div",He,[e("div",We,[e("div",Je,[u(r,{name:"chat-bubble-oval-left",size:"lg",class:"text-green-500"})]),e("div",Ke,[e("dl",null,[t[9]||(t[9]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"},"Commenti",-1)),e("dd",Qe,c(W.value),1)])])])])])])]),filters:I(()=>[e("div",Xe,[e("div",Ye,[t[11]||(t[11]=e("label",{for:"category-filter",class:"block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1"}," Categoria ",-1)),h(e("select",{id:"category-filter","onUpdate:modelValue":t[0]||(t[0]=x=>z.value=x),class:"block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},[t[10]||(t[10]=e("option",{value:""},"Tutte le categorie",-1)),(l(!0),p(N,null,R(_.value,x=>(l(),p("option",{key:x,value:x},c(x),9,Ze))),128))],512),[[A,z.value]])]),e("div",et,[t[13]||(t[13]=e("label",{for:"status-filter",class:"block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1"}," Stato ",-1)),h(e("select",{id:"status-filter","onUpdate:modelValue":t[1]||(t[1]=x=>v.value=x),class:"block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},t[12]||(t[12]=[e("option",{value:""},"Tutti",-1),e("option",{value:"pinned"},"Pinned",-1),e("option",{value:"locked"},"Chiusi",-1),e("option",{value:"active"},"Attivi",-1)]),512),[[A,v.value]])])])]),content:I(({data:x,loading:ee})=>[ee?(l(),p("div",tt,t[14]||(t[14]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):U.value.length===0?(l(),p("div",st,[u(r,{name:"chat-bubble-left",size:"2xl",class:"mx-auto text-gray-400 mb-4"}),t[15]||(t[15]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Nessun topic trovato",-1)),t[16]||(t[16]=e("p",{class:"text-gray-500 dark:text-gray-400"},"Non ci sono topic da visualizzare con i filtri selezionati.",-1))])):(l(),p("div",ot,[e("div",it,[(l(!0),p(N,null,R(U.value,d=>(l(),p("div",{key:d.id,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6 hover:shadow-lg transition-shadow cursor-pointer",onClick:F=>J(d)},[e("div",at,[e("div",nt,[e("div",rt,[d.is_pinned?(l(),w(r,{key:0,name:"bookmark",size:"sm",class:"text-yellow-500"})):b("",!0),d.is_locked?(l(),w(r,{key:1,name:"lock-closed",size:"sm",class:"text-red-500"})):b("",!0),e("h3",dt,c(d.title),1)]),e("p",ct,c(d.content),1),e("div",ut,[e("div",mt,[e("div",pt,[u(r,{name:"user",size:"xs"}),e("span",null,c(d.author_name||"Anonimo"),1)]),e("div",vt,[u(r,{name:"tag",size:"xs"}),e("span",null,c(d.category||"Generale"),1)]),e("div",xt,[u(r,{name:"calendar",size:"xs"}),e("span",null,c(S(i)(d.created_at)),1)])]),e("div",gt,[e("div",ft,[u(r,{name:"eye",size:"xs"}),e("span",null,c(d.view_count||0),1)]),e("div",bt,[u(r,{name:"chat-bubble-oval-left",size:"xs"}),e("span",null,c(d.comments_count||0),1)]),d.reaction_counts&&j(d.reaction_counts)>0?(l(),p("div",yt,[u(r,{name:"heart",size:"xs"}),e("span",null,c(j(d.reaction_counts)),1)])):b("",!0)])])]),e("div",_t,[e("button",{onClick:V(F=>K(d),["stop"]),class:"inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},[u(r,{name:"pencil",size:"xs",class:"mr-1"}),t[17]||(t[17]=D(" Modifica "))],8,kt),n.value?(l(),p("button",{key:0,onClick:V(F=>Q(d),["stop"]),class:"inline-flex items-center px-3 py-1 border border-red-300 shadow-sm text-xs font-medium rounded text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"},[u(r,{name:"trash",size:"xs",class:"mr-1"}),t[18]||(t[18]=D(" Elimina "))],8,ht)):b("",!0)])])],8,lt))),128))])]))]),_:1},8,["data","loading","can-create"]),g.value?(l(),w(ze,{key:0,onClose:t[3]||(t[3]=x=>g.value=!1),onCreated:X})):b("",!0),a.value?(l(),w(re,{key:1,topic:f.value,onClose:t[4]||(t[4]=x=>a.value=!1),onUpdated:Y},null,8,["topic"])):b("",!0),C.value?(l(),w(de,{key:2,title:"Elimina Topic",message:`Sei sicuro di voler eliminare il topic '${($=f.value)==null?void 0:$.title}'?`,"confirm-text":"Elimina","confirm-class":"bg-red-600 hover:bg-red-700",onConfirm:Z,onCancel:t[5]||(t[5]=x=>C.value=!1)},null,8,["message"])):b("",!0)],64)}}},Nt=q(wt,[["__scopeId","data-v-8df83000"]]);export{Nt as default};
