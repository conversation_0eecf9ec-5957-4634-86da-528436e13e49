import{r as b,c as z,u as A,w as E,o as P,b as g,e as l,g as C,p as v,l as e,t as c,A as q,v as d,B as r,C as p,S as w,O as R,E as H,n as V,h as F,s as G,j as f,f as O}from"./vendor.js";import{u as Q}from"./certifications.js";import{u as J}from"./useToast.js";import{H as n}from"./app.js";import{_ as K}from"./PageHeader.js";import{_ as W}from"./Breadcrumb.js";const X={class:"certification-edit"},Y={key:0,class:"flex justify-center items-center py-12"},Z={key:1,class:"bg-red-50 border border-red-200 rounded-lg p-4 mb-6"},ee={class:"flex items-center"},te={class:"text-red-800"},ae={key:2,class:"max-w-4xl mx-auto"},se={class:"bg-white rounded-lg shadow-sm p-6"},oe={class:"mb-8"},ie={class:"text-lg font-semibold text-gray-900 mb-4 flex items-center"},le={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},re=["value"],ne={class:"md:col-span-2"},de={class:"mb-8"},ue={class:"text-lg font-semibold text-gray-900 mb-4 flex items-center"},ce={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},me={class:"mb-8"},pe={class:"text-lg font-semibold text-gray-900 mb-4 flex items-center"},fe={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},be={class:"flex items-center space-x-3"},ge={class:"text-sm font-medium text-gray-700 w-12"},ve={class:"mt-2 w-full bg-gray-200 rounded-full h-2"},xe={class:"mb-8"},ye={class:"flex items-center"},_e={class:"font-semibold"},ze={class:"text-sm opacity-90"},we={class:"flex justify-between items-center pt-6 border-t border-gray-200"},Se={class:"flex items-center space-x-3"},ke={class:"text-sm text-gray-500"},he={class:"flex space-x-3"},Ce=["disabled"],Ne={__name:"CertificationEdit",setup(Ve){const B=G(),S=A(),x=Q(),{addToast:k}=J(),y=b(!1),m=b(null),u=b(!1),s=b({project_name:"",description:"",target_date:"",team_lead_id:"",estimated_budget:null,priority:"medium",status:"draft",current_score:0}),o=z(()=>x.currentCertification),D=z(()=>{var a;return[{name:"Certificazioni",href:"/app/certifications/dashboard"},{name:"Lista",href:"/app/certifications/list"},{name:((a=o.value)==null?void 0:a.project_name)||"Modifica",href:`/app/certifications/${S.params.id}`},{name:"Modifica",href:null}]}),h=z(()=>s.value.project_name&&s.value.project_name.trim().length>3),j=async()=>{const a=S.params.id;if(!a){m.value="ID certificazione non valido";return}y.value=!0,m.value=null;try{await x.fetchCertification(parseInt(a))}catch(t){m.value=t.message||"Errore nel caricamento della certificazione"}finally{y.value=!1}},M=async()=>{if(!(!h.value||!o.value)){u.value=!0;try{await x.updateCertification(o.value.id,s.value),k("Certificazione aggiornata con successo!","success"),B.push(`/app/certifications/${o.value.id}`)}catch{k("Errore nell'aggiornamento della certificazione","error")}finally{u.value=!1}}},I=a=>a?new Date(a).toLocaleDateString("it-IT"):null,L=a=>({quality:"Qualità",security:"Sicurezza",environmental:"Ambientale",privacy:"Privacy",regulatory:"Normativo"})[a]||a||"Non specificata",N=a=>({draft:"Bozza",active:"Attiva",in_progress:"In Corso",completed:"Completata",expired:"Scaduta",suspended:"Sospesa",deleted:"Eliminata"})[a]||a||"Sconosciuto",T=a=>({draft:"La certificazione è in fase di preparazione",active:"Certificazione attiva e valida",in_progress:"Processo di certificazione in corso",completed:"Certificazione ottenuta con successo",expired:"Certificazione scaduta, necessario rinnovo",suspended:"Certificazione temporaneamente sospesa",deleted:"Certificazione eliminata dal sistema"})[a]||"Stato non definito",U=a=>({draft:"document-text",active:"check-circle",in_progress:"arrow-path",completed:"trophy",expired:"exclamation-triangle",suspended:"pause-circle",deleted:"x-circle"})[a]||"question-mark-circle",$=a=>({draft:"bg-gray-100 text-gray-800",active:"bg-green-100 text-green-800",in_progress:"bg-blue-100 text-blue-800",completed:"bg-purple-100 text-purple-800",expired:"bg-red-100 text-red-800",suspended:"bg-yellow-100 text-yellow-800",deleted:"bg-gray-100 text-gray-800"})[a]||"bg-gray-100 text-gray-800";return E(o,a=>{a&&(s.value={project_name:a.project_name||"",description:a.description||"",target_date:a.target_date?a.target_date.split("T")[0]:"",team_lead_id:a.team_lead_id||"",estimated_budget:a.estimated_budget||null,priority:a.priority||"medium",status:a.status||"draft",current_score:a.current_score||0})},{immediate:!0}),P(()=>{j()}),(a,t)=>{const _=F("router-link");return f(),g("div",X,[l(W,{items:D.value},null,8,["items"]),l(K,{title:o.value?`Modifica ${o.value.project_name}`:"Modifica Certificazione",subtitle:o.value?`${L(o.value.category)} • ${o.value.status}`:"Modifica certificazione aziendale",icon:"pencil","icon-color":"text-blue-600"},{actions:v(()=>[o.value?(f(),O(_,{key:0,to:`/app/certifications/${o.value.id}`,class:"btn-secondary flex items-center gap-2"},{default:v(()=>[l(n,{name:"eye",size:"sm"}),t[8]||(t[8]=d(" Visualizza "))]),_:1,__:[8]},8,["to"])):C("",!0),l(_,{to:"/app/certifications/dashboard",class:"btn-secondary flex items-center gap-2"},{default:v(()=>[l(n,{name:"arrow-left",size:"sm"}),t[9]||(t[9]=d(" Dashboard "))]),_:1,__:[9]})]),_:1},8,["title","subtitle"]),y.value?(f(),g("div",Y,t[10]||(t[10]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"},null,-1),e("span",{class:"ml-3 text-gray-600"},"Caricamento certificazione...",-1)]))):m.value?(f(),g("div",Z,[e("div",ee,[l(n,{name:"exclamation-triangle",class:"h-5 w-5 text-red-400 mr-2"}),e("span",te,c(m.value),1)])])):o.value?(f(),g("div",ae,[e("div",se,[e("form",{onSubmit:q(M,["prevent"])},[e("div",oe,[e("h2",ie,[l(n,{name:"information-circle",class:"h-6 w-6 mr-2 text-blue-600"}),t[11]||(t[11]=d(" Informazioni Base "))]),e("div",le,[e("div",null,[t[12]||(t[12]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Nome Progetto * ",-1)),r(e("input",{"onUpdate:modelValue":t[0]||(t[0]=i=>s.value.project_name=i),type:"text",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500",placeholder:"Nome del progetto di certificazione",required:""},null,512),[[p,s.value.project_name]])]),e("div",null,[t[13]||(t[13]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Standard di Certificazione ",-1)),e("input",{value:o.value.standard_code,type:"text",class:"w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-500",disabled:""},null,8,re),t[14]||(t[14]=e("p",{class:"text-xs text-gray-500 mt-1"},"Lo standard non può essere modificato dopo la creazione",-1))]),e("div",ne,[t[15]||(t[15]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Descrizione ",-1)),r(e("textarea",{"onUpdate:modelValue":t[1]||(t[1]=i=>s.value.description=i),rows:"3",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500",placeholder:"Descrizione dettagliata del progetto di certificazione..."},null,512),[[p,s.value.description]])])])]),e("div",de,[e("h2",ue,[l(n,{name:"clipboard-document-check",class:"h-6 w-6 mr-2 text-blue-600"}),t[16]||(t[16]=d(" Dettagli Progetto "))]),e("div",ce,[e("div",null,[t[17]||(t[17]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Data Target Completamento ",-1)),r(e("input",{"onUpdate:modelValue":t[2]||(t[2]=i=>s.value.target_date=i),type:"date",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"},null,512),[[p,s.value.target_date]])]),e("div",null,[t[18]||(t[18]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Budget Stimato (€) ",-1)),r(e("input",{"onUpdate:modelValue":t[3]||(t[3]=i=>s.value.estimated_budget=i),type:"number",min:"0",step:"100",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500",placeholder:"Budget in euro"},null,512),[[p,s.value.estimated_budget,void 0,{number:!0}]])]),e("div",null,[t[20]||(t[20]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Priorità ",-1)),r(e("select",{"onUpdate:modelValue":t[4]||(t[4]=i=>s.value.priority=i),class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"},t[19]||(t[19]=[e("option",{value:"low"},"Bassa",-1),e("option",{value:"medium"},"Media",-1),e("option",{value:"high"},"Alta",-1),e("option",{value:"critical"},"Critica",-1)]),512),[[w,s.value.priority]])]),e("div",null,[t[22]||(t[22]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Stato ",-1)),r(e("select",{"onUpdate:modelValue":t[5]||(t[5]=i=>s.value.status=i),class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"},t[21]||(t[21]=[R('<option value="draft">Bozza</option><option value="in_progress">In Corso</option><option value="active">Attiva</option><option value="completed">Completata</option><option value="suspended">Sospesa</option>',5)]),512),[[w,s.value.status]])])])]),e("div",me,[e("h2",pe,[l(n,{name:"chart-bar",class:"h-6 w-6 mr-2 text-blue-600"}),t[23]||(t[23]=d(" Progresso "))]),e("div",fe,[e("div",null,[t[24]||(t[24]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Score Attuale (%) ",-1)),e("div",be,[r(e("input",{"onUpdate:modelValue":t[6]||(t[6]=i=>s.value.current_score=i),type:"range",min:"0",max:"100",class:"flex-1"},null,512),[[p,s.value.current_score,void 0,{number:!0}]]),e("span",ge,c(s.value.current_score)+"%",1)]),e("div",ve,[e("div",{class:"h-2 rounded-full bg-blue-600 transition-all duration-300",style:H({width:s.value.current_score+"%"})},null,4)])]),e("div",null,[t[26]||(t[26]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Team Lead ",-1)),r(e("select",{"onUpdate:modelValue":t[7]||(t[7]=i=>s.value.team_lead_id=i),class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"},t[25]||(t[25]=[e("option",{value:""},"Seleziona Team Lead",-1),e("option",{value:"1"},"Mario Rossi",-1),e("option",{value:"2"},"Laura Bianchi",-1),e("option",{value:"3"},"Giuseppe Verdi",-1)]),512),[[w,s.value.team_lead_id]])])])]),e("div",xe,[e("div",{class:V([$(s.value.status),"rounded-lg p-4"])},[e("div",ye,[l(n,{name:U(s.value.status),class:"h-6 w-6 mr-3"},null,8,["name"]),e("div",null,[e("h3",_e,c(N(s.value.status)),1),e("p",ze,c(T(s.value.status)),1)])])],2)]),e("div",we,[e("div",Se,[e("span",ke," Ultima modifica: "+c(I(o.value.updated_at)),1)]),e("div",he,[l(_,{to:`/app/certifications/${o.value.id}`,class:"btn-secondary"},{default:v(()=>t[27]||(t[27]=[d(" Annulla ")])),_:1,__:[27]},8,["to"]),e("button",{type:"submit",disabled:!h.value||u.value,class:"btn-primary flex items-center"},[l(n,{name:u.value?"arrow-path":"check",size:"sm",class:V([{"animate-spin":u.value},"mr-2"])},null,8,["name","class"]),d(" "+c(u.value?"Salvataggio...":"Salva Modifiche"),1)],8,Ce)])])],32)])])):C("",!0)])}}};export{Ne as default};
