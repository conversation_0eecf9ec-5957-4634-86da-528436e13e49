import{_ as w,f as S,H as D,d as C}from"./app.js";import{b as a,j as r,l as e,e as x,h,A as z,B as n,C as u,t as c,g as f,F as V,q as M,v as k,S as O,P as b,f as U}from"./vendor.js";const F={name:"EditPollModal",components:{HeroIcon:D,LoadingSpinner:S},props:{poll:{type:Object,required:!0}},emits:["close","updated"],data(){return{isSubmitting:!1,form:{title:"",description:"",options:[{text:""},{text:""}],endDate:"",multipleChoice:!1,anonymous:!1,showResults:!0,allowComments:!0}}},computed:{isFormValid(){const i=this.form.title.trim().length>0,t=this.form.options.length>=2&&this.form.options.every(m=>m.text.trim().length>0),d=!this.form.endDate||new Date(this.form.endDate)>new Date;return i&&t&&d},minDateTime(){const i=new Date;return i.setMinutes(i.getMinutes()+30),i.toISOString().slice(0,16)}},watch:{poll:{handler(){this.initializeForm()},immediate:!0}},methods:{initializeForm(){var i;this.poll&&(this.form={title:this.poll.title||"",description:this.poll.description||"",options:((i=this.poll.options)==null?void 0:i.map(t=>({text:t.text||t})))||[{text:""},{text:""}],endDate:this.poll.endDate?new Date(this.poll.endDate).toISOString().slice(0,16):"",multipleChoice:this.poll.multipleChoice||!1,anonymous:this.poll.anonymous||!1,showResults:this.poll.showResults!==void 0?this.poll.showResults:!0,allowComments:this.poll.allowComments!==void 0?this.poll.allowComments:!0})},addOption(){this.form.options.length<10&&this.form.options.push({text:""})},removeOption(i){this.form.options.length>2&&this.form.options.splice(i,1)},async submitPoll(){if(!(!this.isFormValid||this.isSubmitting)){this.isSubmitting=!0;try{const i={...this.form,options:this.form.options.map(m=>m.text.trim()),endDate:this.form.endDate?new Date(this.form.endDate).toISOString():null},d=await C().updatePoll({id:this.poll.id,...i});this.$emit("updated",d),this.closeModal()}catch(i){console.error("Errore durante l'aggiornamento del sondaggio:",i)}finally{this.isSubmitting=!1}}},closeModal(){this.$emit("close"),setTimeout(()=>{this.initializeForm(),this.isSubmitting=!1},300)}}},I={class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},P={class:"card w-full max-w-3xl mx-4 max-h-[90vh] overflow-y-auto"},R={class:"flex items-center justify-between p-6 border-b"},T={class:"text-right text-xs text-gray-500 dark:text-gray-400 mt-1"},E={class:"text-right text-xs text-gray-500 dark:text-gray-400 mt-1"},j={class:"space-y-3"},B={class:"flex-1"},L=["onUpdate:modelValue","placeholder"],q=["onClick"],A={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},H=["min"],N={class:"space-y-3"},_={class:"flex items-center"},G={class:"flex items-center"},J={class:"flex items-center"},K={class:"flex justify-end space-x-3 pt-4 border-t dark:border-gray-600"},Q=["disabled"];function W(i,t,d,m,s,l){const p=h("HeroIcon"),v=h("LoadingSpinner");return r(),a("div",I,[e("div",P,[e("div",R,[t[11]||(t[11]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white"}," Modifica Sondaggio ",-1)),e("button",{onClick:t[0]||(t[0]=(...o)=>l.closeModal&&l.closeModal(...o)),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"},[x(p,{name:"x-mark",size:"md"})])]),e("form",{onSubmit:t[10]||(t[10]=z((...o)=>l.submitPoll&&l.submitPoll(...o),["prevent"])),class:"p-6 space-y-6"},[e("div",null,[t[12]||(t[12]=e("label",{for:"title",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Titolo * ",-1)),n(e("input",{id:"title","onUpdate:modelValue":t[1]||(t[1]=o=>s.form.title=o),type:"text",required:"",maxlength:"200",class:"input w-full",placeholder:"Inserisci il titolo del sondaggio"},null,512),[[u,s.form.title]]),e("div",T,c(s.form.title.length)+"/200 ",1)]),e("div",null,[t[13]||(t[13]=e("label",{for:"description",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Descrizione ",-1)),n(e("textarea",{id:"description","onUpdate:modelValue":t[2]||(t[2]=o=>s.form.description=o),rows:"3",maxlength:"1000",class:"input-field resize-vertical",placeholder:"Descrizione opzionale del sondaggio"},null,512),[[u,s.form.description]]),e("div",E,c(s.form.description.length)+"/1000 ",1)]),e("div",null,[t[15]||(t[15]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3"}," Opzioni di risposta * ",-1)),e("div",j,[(r(!0),a(V,null,M(s.form.options,(o,g)=>(r(),a("div",{key:g,class:"flex items-center space-x-3"},[e("div",B,[n(e("input",{"onUpdate:modelValue":y=>o.text=y,type:"text",placeholder:`Opzione ${g+1}`,maxlength:"200",class:"input w-full",required:""},null,8,L),[[u,o.text]])]),s.form.options.length>2?(r(),a("button",{key:0,type:"button",onClick:y=>l.removeOption(g),class:"text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 p-1"},[x(p,{name:"trash",size:"sm"})],8,q)):f("",!0)]))),128))]),s.form.options.length<10?(r(),a("button",{key:0,type:"button",onClick:t[3]||(t[3]=(...o)=>l.addOption&&l.addOption(...o)),class:"btn-secondary mt-3"},[x(p,{name:"plus",size:"sm",class:"mr-2"}),t[14]||(t[14]=k(" Aggiungi opzione "))])):f("",!0),t[16]||(t[16]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400 mt-2"}," Minimo 2 opzioni, massimo 10 ",-1))]),e("div",A,[e("div",null,[t[17]||(t[17]=e("label",{for:"endDate",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Data di scadenza ",-1)),n(e("input",{id:"endDate","onUpdate:modelValue":t[4]||(t[4]=o=>s.form.endDate=o),type:"datetime-local",min:l.minDateTime,class:"input w-full"},null,8,H),[[u,s.form.endDate]]),t[18]||(t[18]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400 mt-1"}," Lascia vuoto per sondaggio senza scadenza ",-1))]),e("div",null,[t[20]||(t[20]=e("label",{for:"multipleChoice",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Tipo di voto ",-1)),n(e("select",{id:"multipleChoice","onUpdate:modelValue":t[5]||(t[5]=o=>s.form.multipleChoice=o),class:"input w-full"},t[19]||(t[19]=[e("option",{value:!1},"Scelta singola",-1),e("option",{value:!0},"Scelta multipla",-1)]),512),[[O,s.form.multipleChoice]])])]),e("div",N,[t[24]||(t[24]=e("h4",{class:"text-sm font-medium text-gray-700 dark:text-gray-300"},"Opzioni avanzate",-1)),e("div",_,[n(e("input",{id:"anonymous","onUpdate:modelValue":t[6]||(t[6]=o=>s.form.anonymous=o),type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded dark:border-gray-600 dark:bg-gray-700"},null,512),[[b,s.form.anonymous]]),t[21]||(t[21]=e("label",{for:"anonymous",class:"ml-2 block text-sm text-gray-700 dark:text-gray-300"}," Voto anonimo ",-1))]),e("div",G,[n(e("input",{id:"showResults","onUpdate:modelValue":t[7]||(t[7]=o=>s.form.showResults=o),type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded dark:border-gray-600 dark:bg-gray-700"},null,512),[[b,s.form.showResults]]),t[22]||(t[22]=e("label",{for:"showResults",class:"ml-2 block text-sm text-gray-700 dark:text-gray-300"}," Mostra risultati in tempo reale ",-1))]),e("div",J,[n(e("input",{id:"allowComments","onUpdate:modelValue":t[8]||(t[8]=o=>s.form.allowComments=o),type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded dark:border-gray-600 dark:bg-gray-700"},null,512),[[b,s.form.allowComments]]),t[23]||(t[23]=e("label",{for:"allowComments",class:"ml-2 block text-sm text-gray-700 dark:text-gray-300"}," Permetti commenti ",-1))])]),e("div",K,[e("button",{type:"button",onClick:t[9]||(t[9]=(...o)=>l.closeModal&&l.closeModal(...o)),class:"btn-secondary"}," Annulla "),e("button",{type:"submit",disabled:s.isSubmitting||!l.isFormValid,class:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed flex items-center"},[s.isSubmitting?(r(),U(v,{key:0,class:"mr-2",size:"sm"})):f("",!0),k(" "+c(s.isSubmitting?"Salvataggio...":"Aggiorna Sondaggio"),1)],8,Q)])],32)])])}const Z=w(F,[["render",W],["__scopeId","data-v-b30ac5a8"]]);export{Z as E};
