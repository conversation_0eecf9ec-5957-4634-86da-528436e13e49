const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index.es2.js","assets/vendor.js","assets/PageHeader.js","assets/app.js","assets/index.css","assets/useToast.js","assets/marked.esm.js"])))=>i.map(i=>d[i]);
import{b as qr,e as jn,l as Wt,g as po,h as go,B as mo,S as pl,O as gl,C as Hu,v as ml,F as vl,q as bl,p as Wu,n as cs,t as hr,A as Vu,r as Fi,c as Gu,o as Yu,j as Dr}from"./vendor.js";import{_ as Ju}from"./PageHeader.js";import{i as So,_ as Xu,H as Ku}from"./app.js";import{u as Zu}from"./useToast.js";import{m as hs}from"./marked.esm.js";function de(n){"@babel/helpers - typeof";return de=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},de(n)}var er=Uint8Array,br=Uint16Array,qo=Int32Array,Ls=new er([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),Ns=new er([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),Po=new er([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),Dl=function(n,e){for(var r=new br(31),a=0;a<31;++a)r[a]=e+=1<<n[a-1];for(var l=new qo(r[30]),a=1;a<30;++a)for(var s=r[a];s<r[a+1];++s)l[s]=s-r[a]<<5|a;return{b:r,r:l}},Rl=Dl(Ls,2),zl=Rl.b,ko=Rl.r;zl[28]=258,ko[258]=28;var Ul=Dl(Ns,0),Qu=Ul.b,yl=Ul.r,Io=new br(32768);for(var xe=0;xe<32768;++xe){var On=(xe&43690)>>1|(xe&21845)<<1;On=(On&52428)>>2|(On&13107)<<2,On=(On&61680)>>4|(On&3855)<<4,Io[xe]=((On&65280)>>8|(On&255)<<8)>>1}var nn=function(n,e,r){for(var a=n.length,l=0,s=new br(e);l<a;++l)n[l]&&++s[n[l]-1];var c=new br(e);for(l=1;l<e;++l)c[l]=c[l-1]+s[l-1]<<1;var h;if(r){h=new br(1<<e);var f=15-e;for(l=0;l<a;++l)if(n[l])for(var g=l<<4|n[l],b=e-n[l],w=c[n[l]-1]++<<b,S=w|(1<<b)-1;w<=S;++w)h[Io[w]>>f]=g}else for(h=new br(a),l=0;l<a;++l)n[l]&&(h[l]=Io[c[n[l]-1]++]>>15-n[l]);return h},En=new er(288);for(var xe=0;xe<144;++xe)En[xe]=8;for(var xe=144;xe<256;++xe)En[xe]=9;for(var xe=256;xe<280;++xe)En[xe]=7;for(var xe=280;xe<288;++xe)En[xe]=8;var ya=new er(32);for(var xe=0;xe<32;++xe)ya[xe]=5;var $u=nn(En,9,0),tc=nn(En,9,1),ec=nn(ya,5,0),rc=nn(ya,5,1),vo=function(n){for(var e=n[0],r=1;r<n.length;++r)n[r]>e&&(e=n[r]);return e},Rr=function(n,e,r){var a=e/8|0;return(n[a]|n[a+1]<<8)>>(e&7)&r},bo=function(n,e){var r=e/8|0;return(n[r]|n[r+1]<<8|n[r+2]<<16)>>(e&7)},Do=function(n){return(n+7)/8|0},Hl=function(n,e,r){return(r==null||r>n.length)&&(r=n.length),new er(n.subarray(e,r))},nc=["unexpected EOF","invalid block type","invalid length/literal","invalid distance","stream finished","no stream handler",,"no callback","invalid UTF-8 data","extra field too long","date not in range 1980-2099","filename too long","stream finishing","invalid zip data"],Ur=function(n,e,r){var a=new Error(e||nc[n]);if(a.code=n,Error.captureStackTrace&&Error.captureStackTrace(a,Ur),!r)throw a;return a},ic=function(n,e,r,a){var l=n.length,s=0;if(!l||e.f&&!e.l)return r||new er(0);var c=!r,h=c||e.i!=2,f=e.i;c&&(r=new er(l*3));var g=function(Lt){var It=r.length;if(Lt>It){var St=new er(Math.max(It*2,Lt));St.set(r),r=St}},b=e.f||0,w=e.p||0,S=e.b||0,p=e.l,M=e.d,I=e.m,q=e.n,k=l*8;do{if(!p){b=Rr(n,w,1);var E=Rr(n,w+1,3);if(w+=3,E)if(E==1)p=tc,M=rc,I=9,q=5;else if(E==2){var yt=Rr(n,w,31)+257,tt=Rr(n,w+10,15)+4,U=yt+Rr(n,w+5,31)+1;w+=14;for(var nt=new er(U),pt=new er(19),L=0;L<tt;++L)pt[Po[L]]=Rr(n,w+L*3,7);w+=tt*3;for(var A=vo(pt),B=(1<<A)-1,D=nn(pt,A,1),L=0;L<U;){var et=D[Rr(n,w,B)];w+=et&15;var J=et>>4;if(J<16)nt[L++]=J;else{var rt=0,ct=0;for(J==16?(ct=3+Rr(n,w,3),w+=2,rt=nt[L-1]):J==17?(ct=3+Rr(n,w,7),w+=3):J==18&&(ct=11+Rr(n,w,127),w+=7);ct--;)nt[L++]=rt}}var X=nt.subarray(0,yt),at=nt.subarray(yt);I=vo(X),q=vo(at),p=nn(X,I,1),M=nn(at,q,1)}else Ur(1);else{var J=Do(w)+4,ot=n[J-4]|n[J-3]<<8,ut=J+ot;if(ut>l){f&&Ur(0);break}h&&g(S+ot),r.set(n.subarray(J,ut),S),e.b=S+=ot,e.p=w=ut*8,e.f=b;continue}if(w>k){f&&Ur(0);break}}h&&g(S+131072);for(var ft=(1<<I)-1,Ft=(1<<q)-1,N=w;;N=w){var rt=p[bo(n,w)&ft],C=rt>>4;if(w+=rt&15,w>k){f&&Ur(0);break}if(rt||Ur(2),C<256)r[S++]=C;else if(C==256){N=w,p=null;break}else{var O=C-254;if(C>264){var L=C-257,R=Ls[L];O=Rr(n,w,(1<<R)-1)+zl[L],w+=R}var G=M[bo(n,w)&Ft],$=G>>4;G||Ur(3),w+=G&15;var at=Qu[$];if($>3){var R=Ns[$];at+=bo(n,w)&(1<<R)-1,w+=R}if(w>k){f&&Ur(0);break}h&&g(S+131072);var it=S+O;if(S<at){var st=s-at,Nt=Math.min(at,it);for(st+S<0&&Ur(3);S<Nt;++S)r[S]=a[st+S]}for(;S<it;++S)r[S]=r[S-at]}}e.l=p,e.p=N,e.b=S,e.f=b,p&&(b=1,e.m=I,e.d=M,e.n=q)}while(!b);return S!=r.length&&c?Hl(r,0,S):r.subarray(0,S)},vn=function(n,e,r){r<<=e&7;var a=e/8|0;n[a]|=r,n[a+1]|=r>>8},ma=function(n,e,r){r<<=e&7;var a=e/8|0;n[a]|=r,n[a+1]|=r>>8,n[a+2]|=r>>16},yo=function(n,e){for(var r=[],a=0;a<n.length;++a)n[a]&&r.push({s:a,f:n[a]});var l=r.length,s=r.slice();if(!l)return{t:Vl,l:0};if(l==1){var c=new er(r[0].s+1);return c[r[0].s]=1,{t:c,l:1}}r.sort(function(ut,yt){return ut.f-yt.f}),r.push({s:-1,f:25001});var h=r[0],f=r[1],g=0,b=1,w=2;for(r[0]={s:-1,f:h.f+f.f,l:h,r:f};b!=l-1;)h=r[r[g].f<r[w].f?g++:w++],f=r[g!=b&&r[g].f<r[w].f?g++:w++],r[b++]={s:-1,f:h.f+f.f,l:h,r:f};for(var S=s[0].s,a=1;a<l;++a)s[a].s>S&&(S=s[a].s);var p=new br(S+1),M=Fo(r[b-1],p,0);if(M>e){var a=0,I=0,q=M-e,k=1<<q;for(s.sort(function(yt,tt){return p[tt.s]-p[yt.s]||yt.f-tt.f});a<l;++a){var E=s[a].s;if(p[E]>e)I+=k-(1<<M-p[E]),p[E]=e;else break}for(I>>=q;I>0;){var J=s[a].s;p[J]<e?I-=1<<e-p[J]++-1:++a}for(;a>=0&&I;--a){var ot=s[a].s;p[ot]==e&&(--p[ot],++I)}M=e}return{t:new er(p),l:M}},Fo=function(n,e,r){return n.s==-1?Math.max(Fo(n.l,e,r+1),Fo(n.r,e,r+1)):e[n.s]=r},wl=function(n){for(var e=n.length;e&&!n[--e];);for(var r=new br(++e),a=0,l=n[0],s=1,c=function(f){r[a++]=f},h=1;h<=e;++h)if(n[h]==l&&h!=e)++s;else{if(!l&&s>2){for(;s>138;s-=138)c(32754);s>2&&(c(s>10?s-11<<5|28690:s-3<<5|12305),s=0)}else if(s>3){for(c(l),--s;s>6;s-=6)c(8304);s>2&&(c(s-3<<5|8208),s=0)}for(;s--;)c(l);s=1,l=n[h]}return{c:r.subarray(0,a),n:e}},va=function(n,e){for(var r=0,a=0;a<e.length;++a)r+=n[a]*e[a];return r},Wl=function(n,e,r){var a=r.length,l=Do(e+2);n[l]=a&255,n[l+1]=a>>8,n[l+2]=n[l]^255,n[l+3]=n[l+1]^255;for(var s=0;s<a;++s)n[l+s+4]=r[s];return(l+4+a)*8},xl=function(n,e,r,a,l,s,c,h,f,g,b){vn(e,b++,r),++l[256];for(var w=yo(l,15),S=w.t,p=w.l,M=yo(s,15),I=M.t,q=M.l,k=wl(S),E=k.c,J=k.n,ot=wl(I),ut=ot.c,yt=ot.n,tt=new br(19),U=0;U<E.length;++U)++tt[E[U]&31];for(var U=0;U<ut.length;++U)++tt[ut[U]&31];for(var nt=yo(tt,7),pt=nt.t,L=nt.l,A=19;A>4&&!pt[Po[A-1]];--A);var B=g+5<<3,D=va(l,En)+va(s,ya)+c,et=va(l,S)+va(s,I)+c+14+3*A+va(tt,pt)+2*tt[16]+3*tt[17]+7*tt[18];if(f>=0&&B<=D&&B<=et)return Wl(e,b,n.subarray(f,f+g));var rt,ct,X,at;if(vn(e,b,1+(et<D)),b+=2,et<D){rt=nn(S,p,0),ct=S,X=nn(I,q,0),at=I;var ft=nn(pt,L,0);vn(e,b,J-257),vn(e,b+5,yt-1),vn(e,b+10,A-4),b+=14;for(var U=0;U<A;++U)vn(e,b+3*U,pt[Po[U]]);b+=3*A;for(var Ft=[E,ut],N=0;N<2;++N)for(var C=Ft[N],U=0;U<C.length;++U){var O=C[U]&31;vn(e,b,ft[O]),b+=pt[O],O>15&&(vn(e,b,C[U]>>5&127),b+=C[U]>>12)}}else rt=$u,ct=En,X=ec,at=ya;for(var U=0;U<h;++U){var R=a[U];if(R>255){var O=R>>18&31;ma(e,b,rt[O+257]),b+=ct[O+257],O>7&&(vn(e,b,R>>23&31),b+=Ls[O]);var G=R&31;ma(e,b,X[G]),b+=at[G],G>3&&(ma(e,b,R>>5&8191),b+=Ns[G])}else ma(e,b,rt[R]),b+=ct[R]}return ma(e,b,rt[256]),b+ct[256]},ac=new qo([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),Vl=new er(0),sc=function(n,e,r,a,l,s){var c=s.z||n.length,h=new er(a+c+5*(1+Math.ceil(c/7e3))+l),f=h.subarray(a,h.length-l),g=s.l,b=(s.r||0)&7;if(e){b&&(f[0]=s.r>>3);for(var w=ac[e-1],S=w>>13,p=w&8191,M=(1<<r)-1,I=s.p||new br(32768),q=s.h||new br(M+1),k=Math.ceil(r/3),E=2*k,J=function(Ut){return(n[Ut]^n[Ut+1]<<k^n[Ut+2]<<E)&M},ot=new qo(25e3),ut=new br(288),yt=new br(32),tt=0,U=0,nt=s.i||0,pt=0,L=s.w||0,A=0;nt+2<c;++nt){var B=J(nt),D=nt&32767,et=q[B];if(I[D]=et,q[B]=D,L<=nt){var rt=c-nt;if((tt>7e3||pt>24576)&&(rt>423||!g)){b=xl(n,f,0,ot,ut,yt,U,pt,A,nt-A,b),pt=tt=U=0,A=nt;for(var ct=0;ct<286;++ct)ut[ct]=0;for(var ct=0;ct<30;++ct)yt[ct]=0}var X=2,at=0,ft=p,Ft=D-et&32767;if(rt>2&&B==J(nt-Ft))for(var N=Math.min(S,rt)-1,C=Math.min(32767,nt),O=Math.min(258,rt);Ft<=C&&--ft&&D!=et;){if(n[nt+X]==n[nt+X-Ft]){for(var R=0;R<O&&n[nt+R]==n[nt+R-Ft];++R);if(R>X){if(X=R,at=Ft,R>N)break;for(var G=Math.min(Ft,R-2),$=0,ct=0;ct<G;++ct){var it=nt-Ft+ct&32767,st=I[it],Nt=it-st&32767;Nt>$&&($=Nt,et=it)}}}D=et,et=I[D],Ft+=D-et&32767}if(at){ot[pt++]=268435456|ko[X]<<18|yl[at];var Lt=ko[X]&31,It=yl[at]&31;U+=Ls[Lt]+Ns[It],++ut[257+Lt],++yt[It],L=nt+X,++tt}else ot[pt++]=n[nt],++ut[n[nt]]}}for(nt=Math.max(nt,L);nt<c;++nt)ot[pt++]=n[nt],++ut[n[nt]];b=xl(n,f,g,ot,ut,yt,U,pt,A,nt-A,b),g||(s.r=b&7|f[b/8|0]<<3,b-=7,s.h=q,s.p=I,s.i=nt,s.w=L)}else{for(var nt=s.w||0;nt<c+g;nt+=65535){var St=nt+65535;St>=c&&(f[b/8|0]=g,St=c),b=Wl(f,b+1,n.subarray(nt,St))}s.i=c}return Hl(h,0,a+Do(b)+l)},Gl=function(){var n=1,e=0;return{p:function(r){for(var a=n,l=e,s=r.length|0,c=0;c!=s;){for(var h=Math.min(c+2655,s);c<h;++c)l+=a+=r[c];a=(a&65535)+15*(a>>16),l=(l&65535)+15*(l>>16)}n=a,e=l},d:function(){return n%=65521,e%=65521,(n&255)<<24|(n&65280)<<8|(e&255)<<8|e>>8}}},oc=function(n,e,r,a,l){if(!l&&(l={l:1},e.dictionary)){var s=e.dictionary.subarray(-32768),c=new er(s.length+n.length);c.set(s),c.set(n,s.length),n=c,l.w=s.length}return sc(n,e.level==null?6:e.level,e.mem==null?l.l?Math.ceil(Math.max(8,Math.min(13,Math.log(n.length)))*1.5):20:12+e.mem,r,a,l)},Yl=function(n,e,r){for(;r;++e)n[e]=r,r>>>=8},lc=function(n,e){var r=e.level,a=r==0?0:r<6?1:r==9?3:2;if(n[0]=120,n[1]=a<<6|(e.dictionary&&32),n[1]|=31-(n[0]<<8|n[1])%31,e.dictionary){var l=Gl();l.p(e.dictionary),Yl(n,2,l.d())}},uc=function(n,e){return((n[0]&15)!=8||n[0]>>4>7||(n[0]<<8|n[1])%31)&&Ur(6,"invalid zlib data"),(n[1]>>5&1)==1&&Ur(6,"invalid zlib data: "+(n[1]&32?"need":"unexpected")+" dictionary"),(n[1]>>3&4)+2};function Co(n,e){e||(e={});var r=Gl();r.p(n);var a=oc(n,e,e.dictionary?6:2,4);return lc(a,e),Yl(a,a.length-4,r.d()),a}function cc(n,e){return ic(n.subarray(uc(n),-4),{i:2},e,e)}var hc=typeof TextDecoder<"u"&&new TextDecoder,fc=0;try{hc.decode(Vl,{stream:!0}),fc=1}catch{}var Ht=function(){return typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:this}();function wo(){Ht.console&&typeof Ht.console.log=="function"&&Ht.console.log.apply(Ht.console,arguments)}var be={log:wo,warn:function(n){Ht.console&&(typeof Ht.console.warn=="function"?Ht.console.warn.apply(Ht.console,arguments):wo.call(null,arguments))},error:function(n){Ht.console&&(typeof Ht.console.error=="function"?Ht.console.error.apply(Ht.console,arguments):wo(n))}};function xo(n,e,r){var a=new XMLHttpRequest;a.open("GET",n),a.responseType="blob",a.onload=function(){Zn(a.response,e,r)},a.onerror=function(){be.error("could not download file")},a.send()}function Ll(n){var e=new XMLHttpRequest;e.open("HEAD",n,!1);try{e.send()}catch{}return e.status>=200&&e.status<=299}function fs(n){try{n.dispatchEvent(new MouseEvent("click"))}catch{var e=document.createEvent("MouseEvents");e.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),n.dispatchEvent(e)}}var ba,jo,Zn=Ht.saveAs||((typeof window>"u"?"undefined":de(window))!=="object"||window!==Ht?function(){}:typeof HTMLAnchorElement<"u"&&"download"in HTMLAnchorElement.prototype?function(n,e,r){var a=Ht.URL||Ht.webkitURL,l=document.createElement("a");e=e||n.name||"download",l.download=e,l.rel="noopener",typeof n=="string"?(l.href=n,l.origin!==location.origin?Ll(l.href)?xo(n,e,r):fs(l,l.target="_blank"):fs(l)):(l.href=a.createObjectURL(n),setTimeout(function(){a.revokeObjectURL(l.href)},4e4),setTimeout(function(){fs(l)},0))}:"msSaveOrOpenBlob"in navigator?function(n,e,r){if(e=e||n.name||"download",typeof n=="string")if(Ll(n))xo(n,e,r);else{var a=document.createElement("a");a.href=n,a.target="_blank",setTimeout(function(){fs(a)})}else navigator.msSaveOrOpenBlob(function(l,s){return s===void 0?s={autoBom:!1}:de(s)!=="object"&&(be.warn("Deprecated: Expected third argument to be a object"),s={autoBom:!s}),s.autoBom&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(l.type)?new Blob(["\uFEFF",l],{type:l.type}):l}(n,r),e)}:function(n,e,r,a){if((a=a||open("","_blank"))&&(a.document.title=a.document.body.innerText="downloading..."),typeof n=="string")return xo(n,e,r);var l=n.type==="application/octet-stream",s=/constructor/i.test(Ht.HTMLElement)||Ht.safari,c=/CriOS\/[\d]+/.test(navigator.userAgent);if((c||l&&s)&&(typeof FileReader>"u"?"undefined":de(FileReader))==="object"){var h=new FileReader;h.onloadend=function(){var b=h.result;b=c?b:b.replace(/^data:[^;]*;/,"data:attachment/file;"),a?a.location.href=b:location=b,a=null},h.readAsDataURL(n)}else{var f=Ht.URL||Ht.webkitURL,g=f.createObjectURL(n);a?a.location=g:location.href=g,a=null,setTimeout(function(){f.revokeObjectURL(g)},4e4)}});/**
 * A class to parse color values
 * <AUTHOR> Stefanov <<EMAIL>>
 * {@link   http://www.phpied.com/rgb-color-parser-in-javascript/}
 * @license Use it if you like it
 */function Jl(n){var e;n=n||"",this.ok=!1,n.charAt(0)=="#"&&(n=n.substr(1,6)),n={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dodgerblue:"1e90ff",feldspar:"d19275",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslateblue:"8470ff",lightslategray:"778899",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"00ff00",limegreen:"32cd32",linen:"faf0e6",magenta:"ff00ff",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370d8",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"d87093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",red:"ff0000",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",violetred:"d02090",wheat:"f5deb3",white:"ffffff",whitesmoke:"f5f5f5",yellow:"ffff00",yellowgreen:"9acd32"}[n=(n=n.replace(/ /g,"")).toLowerCase()]||n;for(var r=[{re:/^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,example:["rgb(123, 234, 45)","rgb(255,234,245)"],process:function(h){return[parseInt(h[1]),parseInt(h[2]),parseInt(h[3])]}},{re:/^(\w{2})(\w{2})(\w{2})$/,example:["#00ff00","336699"],process:function(h){return[parseInt(h[1],16),parseInt(h[2],16),parseInt(h[3],16)]}},{re:/^(\w{1})(\w{1})(\w{1})$/,example:["#fb0","f0f"],process:function(h){return[parseInt(h[1]+h[1],16),parseInt(h[2]+h[2],16),parseInt(h[3]+h[3],16)]}}],a=0;a<r.length;a++){var l=r[a].re,s=r[a].process,c=l.exec(n);c&&(e=s(c),this.r=e[0],this.g=e[1],this.b=e[2],this.ok=!0)}this.r=this.r<0||isNaN(this.r)?0:this.r>255?255:this.r,this.g=this.g<0||isNaN(this.g)?0:this.g>255?255:this.g,this.b=this.b<0||isNaN(this.b)?0:this.b>255?255:this.b,this.toRGB=function(){return"rgb("+this.r+", "+this.g+", "+this.b+")"},this.toHex=function(){var h=this.r.toString(16),f=this.g.toString(16),g=this.b.toString(16);return h.length==1&&(h="0"+h),f.length==1&&(f="0"+f),g.length==1&&(g="0"+g),"#"+h+f+g}}/**
 * @license
 * Joseph Myers does not specify a particular license for his work.
 *
 * Author: Joseph Myers
 * Accessed from: http://www.myersdaily.org/joseph/javascript/md5.js
 *
 * Modified by: Owen Leong
 */function Lo(n,e){var r=n[0],a=n[1],l=n[2],s=n[3];r=Ze(r,a,l,s,e[0],7,-680876936),s=Ze(s,r,a,l,e[1],12,-389564586),l=Ze(l,s,r,a,e[2],17,606105819),a=Ze(a,l,s,r,e[3],22,-**********),r=Ze(r,a,l,s,e[4],7,-176418897),s=Ze(s,r,a,l,e[5],12,**********),l=Ze(l,s,r,a,e[6],17,-**********),a=Ze(a,l,s,r,e[7],22,-45705983),r=Ze(r,a,l,s,e[8],7,**********),s=Ze(s,r,a,l,e[9],12,-**********),l=Ze(l,s,r,a,e[10],17,-42063),a=Ze(a,l,s,r,e[11],22,-**********),r=Ze(r,a,l,s,e[12],7,**********),s=Ze(s,r,a,l,e[13],12,-40341101),l=Ze(l,s,r,a,e[14],17,-**********),r=Qe(r,a=Ze(a,l,s,r,e[15],22,**********),l,s,e[1],5,-165796510),s=Qe(s,r,a,l,e[6],9,-**********),l=Qe(l,s,r,a,e[11],14,643717713),a=Qe(a,l,s,r,e[0],20,-373897302),r=Qe(r,a,l,s,e[5],5,-701558691),s=Qe(s,r,a,l,e[10],9,38016083),l=Qe(l,s,r,a,e[15],14,-660478335),a=Qe(a,l,s,r,e[4],20,-405537848),r=Qe(r,a,l,s,e[9],5,568446438),s=Qe(s,r,a,l,e[14],9,-1019803690),l=Qe(l,s,r,a,e[3],14,-187363961),a=Qe(a,l,s,r,e[8],20,1163531501),r=Qe(r,a,l,s,e[13],5,-1444681467),s=Qe(s,r,a,l,e[2],9,-51403784),l=Qe(l,s,r,a,e[7],14,1735328473),r=$e(r,a=Qe(a,l,s,r,e[12],20,-1926607734),l,s,e[5],4,-378558),s=$e(s,r,a,l,e[8],11,-2022574463),l=$e(l,s,r,a,e[11],16,1839030562),a=$e(a,l,s,r,e[14],23,-35309556),r=$e(r,a,l,s,e[1],4,-1530992060),s=$e(s,r,a,l,e[4],11,1272893353),l=$e(l,s,r,a,e[7],16,-155497632),a=$e(a,l,s,r,e[10],23,-1094730640),r=$e(r,a,l,s,e[13],4,681279174),s=$e(s,r,a,l,e[0],11,-358537222),l=$e(l,s,r,a,e[3],16,-722521979),a=$e(a,l,s,r,e[6],23,76029189),r=$e(r,a,l,s,e[9],4,-640364487),s=$e(s,r,a,l,e[12],11,-421815835),l=$e(l,s,r,a,e[15],16,530742520),r=tr(r,a=$e(a,l,s,r,e[2],23,-995338651),l,s,e[0],6,-198630844),s=tr(s,r,a,l,e[7],10,1126891415),l=tr(l,s,r,a,e[14],15,-1416354905),a=tr(a,l,s,r,e[5],21,-57434055),r=tr(r,a,l,s,e[12],6,1700485571),s=tr(s,r,a,l,e[3],10,-1894986606),l=tr(l,s,r,a,e[10],15,-1051523),a=tr(a,l,s,r,e[1],21,-2054922799),r=tr(r,a,l,s,e[8],6,1873313359),s=tr(s,r,a,l,e[15],10,-30611744),l=tr(l,s,r,a,e[6],15,-1560198380),a=tr(a,l,s,r,e[13],21,1309151649),r=tr(r,a,l,s,e[4],6,-145523070),s=tr(s,r,a,l,e[11],10,-1120210379),l=tr(l,s,r,a,e[2],15,718787259),a=tr(a,l,s,r,e[9],21,-343485551),n[0]=Bn(r,n[0]),n[1]=Bn(a,n[1]),n[2]=Bn(l,n[2]),n[3]=Bn(s,n[3])}function As(n,e,r,a,l,s){return e=Bn(Bn(e,n),Bn(a,s)),Bn(e<<l|e>>>32-l,r)}function Ze(n,e,r,a,l,s,c){return As(e&r|~e&a,n,e,l,s,c)}function Qe(n,e,r,a,l,s,c){return As(e&a|r&~a,n,e,l,s,c)}function $e(n,e,r,a,l,s,c){return As(e^r^a,n,e,l,s,c)}function tr(n,e,r,a,l,s,c){return As(r^(e|~a),n,e,l,s,c)}function Xl(n){var e,r=n.length,a=[1732584193,-271733879,-1732584194,271733878];for(e=64;e<=n.length;e+=64)Lo(a,dc(n.substring(e-64,e)));n=n.substring(e-64);var l=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(e=0;e<n.length;e++)l[e>>2]|=n.charCodeAt(e)<<(e%4<<3);if(l[e>>2]|=128<<(e%4<<3),e>55)for(Lo(a,l),e=0;e<16;e++)l[e]=0;return l[14]=8*r,Lo(a,l),a}function dc(n){var e,r=[];for(e=0;e<64;e+=4)r[e>>2]=n.charCodeAt(e)+(n.charCodeAt(e+1)<<8)+(n.charCodeAt(e+2)<<16)+(n.charCodeAt(e+3)<<24);return r}ba=Ht.atob.bind(Ht),jo=Ht.btoa.bind(Ht);var Nl="0123456789abcdef".split("");function pc(n){for(var e="",r=0;r<4;r++)e+=Nl[n>>8*r+4&15]+Nl[n>>8*r&15];return e}function gc(n){return String.fromCharCode((255&n)>>0,(65280&n)>>8,(16711680&n)>>16,(**********&n)>>24)}function Oo(n){return Xl(n).map(gc).join("")}var mc=function(n){for(var e=0;e<n.length;e++)n[e]=pc(n[e]);return n.join("")}(Xl("hello"))!="5d41402abc4b2a76b9719d911017c592";function Bn(n,e){if(mc){var r=(65535&n)+(65535&e);return(n>>16)+(e>>16)+(r>>16)<<16|65535&r}return n+e&**********}/**
 * @license
 * FPDF is released under a permissive license: there is no usage restriction.
 * You may embed it freely in your application (commercial or not), with or
 * without modifications.
 *
 * Reference: http://www.fpdf.org/en/script/script37.php
 */function Mo(n,e){var r,a,l,s;if(n!==r){for(var c=(l=n,s=1+(256/n.length>>0),new Array(s+1).join(l)),h=[],f=0;f<256;f++)h[f]=f;var g=0;for(f=0;f<256;f++){var b=h[f];g=(g+b+c.charCodeAt(f))%256,h[f]=h[g],h[g]=b}r=n,a=h}else h=a;var w=e.length,S=0,p=0,M="";for(f=0;f<w;f++)p=(p+(b=h[S=(S+1)%256]))%256,h[S]=h[p],h[p]=b,c=h[(h[S]+h[p])%256],M+=String.fromCharCode(e.charCodeAt(f)^c);return M}/**
 * @license
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 * Author: Owen Leong (@owenl131)
 * Date: 15 Oct 2020
 * References:
 * https://www.cs.cmu.edu/~dst/Adobe/Gallery/anon21jul01-pdf-encryption.txt
 * https://github.com/foliojs/pdfkit/blob/master/lib/security.js
 * http://www.fpdf.org/en/script/script37.php
 */var Al={print:4,modify:8,copy:16,"annot-forms":32};function Oi(n,e,r,a){this.v=1,this.r=2;var l=192;n.forEach(function(h){if(Al.perm!==void 0)throw new Error("Invalid permission: "+h);l+=Al[h]}),this.padding="(¿N^NuAd\0NVÿú\b..\0¶Ðh>/\f©þdSiz";var s=(e+this.padding).substr(0,32),c=(r+this.padding).substr(0,32);this.O=this.processOwnerPassword(s,c),this.P=-(1+(255^l)),this.encryptionKey=Oo(s+this.O+this.lsbFirstWord(this.P)+this.hexToBytes(a)).substr(0,5),this.U=Mo(this.encryptionKey,this.padding)}function Mi(n){if(/[^\u0000-\u00ff]/.test(n))throw new Error("Invalid PDF Name Object: "+n+", Only accept ASCII characters.");for(var e="",r=n.length,a=0;a<r;a++){var l=n.charCodeAt(a);l<33||l===35||l===37||l===40||l===41||l===47||l===60||l===62||l===91||l===93||l===123||l===125||l>126?e+="#"+("0"+l.toString(16)).slice(-2):e+=n[a]}return e}function _l(n){if(de(n)!=="object")throw new Error("Invalid Context passed to initialize PubSub (jsPDF-module)");var e={};this.subscribe=function(r,a,l){if(l=l||!1,typeof r!="string"||typeof a!="function"||typeof l!="boolean")throw new Error("Invalid arguments passed to PubSub.subscribe (jsPDF-module)");e.hasOwnProperty(r)||(e[r]={});var s=Math.random().toString(35);return e[r][s]=[a,!!l],s},this.unsubscribe=function(r){for(var a in e)if(e[a][r])return delete e[a][r],Object.keys(e[a]).length===0&&delete e[a],!0;return!1},this.publish=function(r){if(e.hasOwnProperty(r)){var a=Array.prototype.slice.call(arguments,1),l=[];for(var s in e[r]){var c=e[r][s];try{c[0].apply(n,a)}catch(h){Ht.console&&be.error("jsPDF PubSub Error",h.message,h)}c[1]&&l.push(s)}l.length&&l.forEach(this.unsubscribe)}},this.getTopics=function(){return e}}function ws(n){if(!(this instanceof ws))return new ws(n);var e="opacity,stroke-opacity".split(",");for(var r in n)n.hasOwnProperty(r)&&e.indexOf(r)>=0&&(this[r]=n[r]);this.id="",this.objectNumber=-1}function Kl(n,e){this.gState=n,this.matrix=e,this.id="",this.objectNumber=-1}function Qn(n,e,r,a,l){if(!(this instanceof Qn))return new Qn(n,e,r,a,l);this.type=n==="axial"?2:3,this.coords=e,this.colors=r,Kl.call(this,a,l)}function Bi(n,e,r,a,l){if(!(this instanceof Bi))return new Bi(n,e,r,a,l);this.boundingBox=n,this.xStep=e,this.yStep=r,this.stream="",this.cloneIndex=0,Kl.call(this,a,l)}function zt(n){var e,r=typeof arguments[0]=="string"?arguments[0]:"p",a=arguments[1],l=arguments[2],s=arguments[3],c=[],h=1,f=16,g="S",b=null;de(n=n||{})==="object"&&(r=n.orientation,a=n.unit||a,l=n.format||l,s=n.compress||n.compressPdf||s,(b=n.encryption||null)!==null&&(b.userPassword=b.userPassword||"",b.ownerPassword=b.ownerPassword||"",b.userPermissions=b.userPermissions||[]),h=typeof n.userUnit=="number"?Math.abs(n.userUnit):1,n.precision!==void 0&&(e=n.precision),n.floatPrecision!==void 0&&(f=n.floatPrecision),g=n.defaultPathOperation||"S"),c=n.filters||(s===!0?["FlateEncode"]:c),a=a||"mm",r=(""+(r||"P")).toLowerCase();var w=n.putOnlyUsedFonts||!1,S={},p={internal:{},__private__:{}};p.__private__.PubSub=_l;var M="1.3",I=p.__private__.getPdfVersion=function(){return M};p.__private__.setPdfVersion=function(o){M=o};var q={a0:[2383.94,3370.39],a1:[1683.78,2383.94],a2:[1190.55,1683.78],a3:[841.89,1190.55],a4:[595.28,841.89],a5:[419.53,595.28],a6:[297.64,419.53],a7:[209.76,297.64],a8:[147.4,209.76],a9:[104.88,147.4],a10:[73.7,104.88],b0:[2834.65,4008.19],b1:[2004.09,2834.65],b2:[1417.32,2004.09],b3:[1000.63,1417.32],b4:[708.66,1000.63],b5:[498.9,708.66],b6:[354.33,498.9],b7:[249.45,354.33],b8:[175.75,249.45],b9:[124.72,175.75],b10:[87.87,124.72],c0:[2599.37,3676.54],c1:[1836.85,2599.37],c2:[1298.27,1836.85],c3:[918.43,1298.27],c4:[649.13,918.43],c5:[459.21,649.13],c6:[323.15,459.21],c7:[229.61,323.15],c8:[161.57,229.61],c9:[113.39,161.57],c10:[79.37,113.39],dl:[311.81,623.62],letter:[612,792],"government-letter":[576,756],legal:[612,1008],"junior-legal":[576,360],ledger:[1224,792],tabloid:[792,1224],"credit-card":[153,243]};p.__private__.getPageFormats=function(){return q};var k=p.__private__.getPageFormat=function(o){return q[o]};l=l||"a4";var E={COMPAT:"compat",ADVANCED:"advanced"},J=E.COMPAT;function ot(){this.saveGraphicsState(),T(new Rt(jt,0,0,-jt,0,wn()*jt).toString()+" cm"),this.setFontSize(this.getFontSize()/jt),g="n",J=E.ADVANCED}function ut(){this.restoreGraphicsState(),g="S",J=E.COMPAT}var yt=p.__private__.combineFontStyleAndFontWeight=function(o,v){if(o=="bold"&&v=="normal"||o=="bold"&&v==400||o=="normal"&&v=="italic"||o=="bold"&&v=="italic")throw new Error("Invalid Combination of fontweight and fontstyle");return v&&(o=v==400||v==="normal"?o==="italic"?"italic":"normal":v!=700&&v!=="bold"||o!=="normal"?(v==700?"bold":v)+""+o:"bold"),o};p.advancedAPI=function(o){var v=J===E.COMPAT;return v&&ot.call(this),typeof o!="function"||(o(this),v&&ut.call(this)),this},p.compatAPI=function(o){var v=J===E.ADVANCED;return v&&ut.call(this),typeof o!="function"||(o(this),v&&ot.call(this)),this},p.isAdvancedAPI=function(){return J===E.ADVANCED};var tt,U=function(o){if(J!==E.ADVANCED)throw new Error(o+" is only available in 'advanced' API mode. You need to call advancedAPI() first.")},nt=p.roundToPrecision=p.__private__.roundToPrecision=function(o,v){var j=e||v;if(isNaN(o)||isNaN(j))throw new Error("Invalid argument passed to jsPDF.roundToPrecision");return o.toFixed(j).replace(/0+$/,"")};tt=p.hpf=p.__private__.hpf=typeof f=="number"?function(o){if(isNaN(o))throw new Error("Invalid argument passed to jsPDF.hpf");return nt(o,f)}:f==="smart"?function(o){if(isNaN(o))throw new Error("Invalid argument passed to jsPDF.hpf");return nt(o,o>-1&&o<1?16:5)}:function(o){if(isNaN(o))throw new Error("Invalid argument passed to jsPDF.hpf");return nt(o,16)};var pt=p.f2=p.__private__.f2=function(o){if(isNaN(o))throw new Error("Invalid argument passed to jsPDF.f2");return nt(o,2)},L=p.__private__.f3=function(o){if(isNaN(o))throw new Error("Invalid argument passed to jsPDF.f3");return nt(o,3)},A=p.scale=p.__private__.scale=function(o){if(isNaN(o))throw new Error("Invalid argument passed to jsPDF.scale");return J===E.COMPAT?o*jt:J===E.ADVANCED?o:void 0},B=function(o){return J===E.COMPAT?wn()-o:J===E.ADVANCED?o:void 0},D=function(o){return A(B(o))};p.__private__.setPrecision=p.setPrecision=function(o){typeof parseInt(o,10)=="number"&&(e=parseInt(o,10))};var et,rt="00000000000000000000000000000000",ct=p.__private__.getFileId=function(){return rt},X=p.__private__.setFileId=function(o){return rt=o!==void 0&&/^[a-fA-F0-9]{32}$/.test(o)?o.toUpperCase():rt.split("").map(function(){return"ABCDEF0123456789".charAt(Math.floor(16*Math.random()))}).join(""),b!==null&&(Xe=new Oi(b.userPermissions,b.userPassword,b.ownerPassword,rt)),rt};p.setFileId=function(o){return X(o),this},p.getFileId=function(){return ct()};var at=p.__private__.convertDateToPDFDate=function(o){var v=o.getTimezoneOffset(),j=v<0?"+":"-",z=Math.floor(Math.abs(v/60)),K=Math.abs(v%60),ht=[j,O(z),"'",O(K),"'"].join("");return["D:",o.getFullYear(),O(o.getMonth()+1),O(o.getDate()),O(o.getHours()),O(o.getMinutes()),O(o.getSeconds()),ht].join("")},ft=p.__private__.convertPDFDateToDate=function(o){var v=parseInt(o.substr(2,4),10),j=parseInt(o.substr(6,2),10)-1,z=parseInt(o.substr(8,2),10),K=parseInt(o.substr(10,2),10),ht=parseInt(o.substr(12,2),10),wt=parseInt(o.substr(14,2),10);return new Date(v,j,z,K,ht,wt,0)},Ft=p.__private__.setCreationDate=function(o){var v;if(o===void 0&&(o=new Date),o instanceof Date)v=at(o);else{if(!/^D:(20[0-2][0-9]|203[0-7]|19[7-9][0-9])(0[0-9]|1[0-2])([0-2][0-9]|3[0-1])(0[0-9]|1[0-9]|2[0-3])(0[0-9]|[1-5][0-9])(0[0-9]|[1-5][0-9])(\+0[0-9]|\+1[0-4]|-0[0-9]|-1[0-1])'(0[0-9]|[1-5][0-9])'?$/.test(o))throw new Error("Invalid argument passed to jsPDF.setCreationDate");v=o}return et=v},N=p.__private__.getCreationDate=function(o){var v=et;return o==="jsDate"&&(v=ft(et)),v};p.setCreationDate=function(o){return Ft(o),this},p.getCreationDate=function(o){return N(o)};var C,O=p.__private__.padd2=function(o){return("0"+parseInt(o)).slice(-2)},R=p.__private__.padd2Hex=function(o){return("00"+(o=o.toString())).substr(o.length)},G=0,$=[],it=[],st=0,Nt=[],Lt=[],It=!1,St=it,Ut=function(){G=0,st=0,it=[],$=[],Nt=[],on=Be(),Fr=Be()};p.__private__.setCustomOutputDestination=function(o){It=!0,St=o};var dt=function(o){It||(St=o)};p.__private__.resetCustomOutputDestination=function(){It=!1,St=it};var T=p.__private__.out=function(o){return o=o.toString(),st+=o.length+1,St.push(o),St},Zt=p.__private__.write=function(o){return T(arguments.length===1?o.toString():Array.prototype.join.call(arguments," "))},Et=p.__private__.getArrayBuffer=function(o){for(var v=o.length,j=new ArrayBuffer(v),z=new Uint8Array(j);v--;)z[v]=o.charCodeAt(v);return j},xt=[["Helvetica","helvetica","normal","WinAnsiEncoding"],["Helvetica-Bold","helvetica","bold","WinAnsiEncoding"],["Helvetica-Oblique","helvetica","italic","WinAnsiEncoding"],["Helvetica-BoldOblique","helvetica","bolditalic","WinAnsiEncoding"],["Courier","courier","normal","WinAnsiEncoding"],["Courier-Bold","courier","bold","WinAnsiEncoding"],["Courier-Oblique","courier","italic","WinAnsiEncoding"],["Courier-BoldOblique","courier","bolditalic","WinAnsiEncoding"],["Times-Roman","times","normal","WinAnsiEncoding"],["Times-Bold","times","bold","WinAnsiEncoding"],["Times-Italic","times","italic","WinAnsiEncoding"],["Times-BoldItalic","times","bolditalic","WinAnsiEncoding"],["ZapfDingbats","zapfdingbats","normal",null],["Symbol","symbol","normal",null]];p.__private__.getStandardFonts=function(){return xt};var At=n.fontSize||16;p.__private__.setFontSize=p.setFontSize=function(o){return At=J===E.ADVANCED?o/jt:o,this};var Ct,kt=p.__private__.getFontSize=p.getFontSize=function(){return J===E.COMPAT?At:At*jt},Tt=n.R2L||!1;p.__private__.setR2L=p.setR2L=function(o){return Tt=o,this},p.__private__.getR2L=p.getR2L=function(){return Tt};var Yt,te=p.__private__.setZoomMode=function(o){var v=[void 0,null,"fullwidth","fullheight","fullpage","original"];if(/^(?:\d+\.\d*|\d*\.\d+|\d+)%$/.test(o))Ct=o;else if(isNaN(o)){if(v.indexOf(o)===-1)throw new Error('zoom must be Integer (e.g. 2), a percentage Value (e.g. 300%) or fullwidth, fullheight, fullpage, original. "'+o+'" is not recognized.');Ct=o}else Ct=parseInt(o,10)};p.__private__.getZoomMode=function(){return Ct};var ee,ae=p.__private__.setPageMode=function(o){if([void 0,null,"UseNone","UseOutlines","UseThumbs","FullScreen"].indexOf(o)==-1)throw new Error('Page mode must be one of UseNone, UseOutlines, UseThumbs, or FullScreen. "'+o+'" is not recognized.');Yt=o};p.__private__.getPageMode=function(){return Yt};var pe=p.__private__.setLayoutMode=function(o){if([void 0,null,"continuous","single","twoleft","tworight","two"].indexOf(o)==-1)throw new Error('Layout mode must be one of continuous, single, twoleft, tworight. "'+o+'" is not recognized.');ee=o};p.__private__.getLayoutMode=function(){return ee},p.__private__.setDisplayMode=p.setDisplayMode=function(o,v,j){return te(o),pe(v),ae(j),this};var Vt={title:"",subject:"",author:"",keywords:"",creator:""};p.__private__.getDocumentProperty=function(o){if(Object.keys(Vt).indexOf(o)===-1)throw new Error("Invalid argument passed to jsPDF.getDocumentProperty");return Vt[o]},p.__private__.getDocumentProperties=function(){return Vt},p.__private__.setDocumentProperties=p.setProperties=p.setDocumentProperties=function(o){for(var v in Vt)Vt.hasOwnProperty(v)&&o[v]&&(Vt[v]=o[v]);return this},p.__private__.setDocumentProperty=function(o,v){if(Object.keys(Vt).indexOf(o)===-1)throw new Error("Invalid arguments passed to jsPDF.setDocumentProperty");return Vt[o]=v};var re,jt,Je,oe,Pr,me={},Le={},Vr=[],ue={},qn={},Ae={},kr={},sn=null,_e=0,Jt=[],ce=new _l(p),Dn=n.hotfixes||[],Ve={},Gr={},Yr=[],Rt=function o(v,j,z,K,ht,wt){if(!(this instanceof o))return new o(v,j,z,K,ht,wt);isNaN(v)&&(v=1),isNaN(j)&&(j=0),isNaN(z)&&(z=0),isNaN(K)&&(K=1),isNaN(ht)&&(ht=0),isNaN(wt)&&(wt=0),this._matrix=[v,j,z,K,ht,wt]};Object.defineProperty(Rt.prototype,"sx",{get:function(){return this._matrix[0]},set:function(o){this._matrix[0]=o}}),Object.defineProperty(Rt.prototype,"shy",{get:function(){return this._matrix[1]},set:function(o){this._matrix[1]=o}}),Object.defineProperty(Rt.prototype,"shx",{get:function(){return this._matrix[2]},set:function(o){this._matrix[2]=o}}),Object.defineProperty(Rt.prototype,"sy",{get:function(){return this._matrix[3]},set:function(o){this._matrix[3]=o}}),Object.defineProperty(Rt.prototype,"tx",{get:function(){return this._matrix[4]},set:function(o){this._matrix[4]=o}}),Object.defineProperty(Rt.prototype,"ty",{get:function(){return this._matrix[5]},set:function(o){this._matrix[5]=o}}),Object.defineProperty(Rt.prototype,"a",{get:function(){return this._matrix[0]},set:function(o){this._matrix[0]=o}}),Object.defineProperty(Rt.prototype,"b",{get:function(){return this._matrix[1]},set:function(o){this._matrix[1]=o}}),Object.defineProperty(Rt.prototype,"c",{get:function(){return this._matrix[2]},set:function(o){this._matrix[2]=o}}),Object.defineProperty(Rt.prototype,"d",{get:function(){return this._matrix[3]},set:function(o){this._matrix[3]=o}}),Object.defineProperty(Rt.prototype,"e",{get:function(){return this._matrix[4]},set:function(o){this._matrix[4]=o}}),Object.defineProperty(Rt.prototype,"f",{get:function(){return this._matrix[5]},set:function(o){this._matrix[5]=o}}),Object.defineProperty(Rt.prototype,"rotation",{get:function(){return Math.atan2(this.shx,this.sx)}}),Object.defineProperty(Rt.prototype,"scaleX",{get:function(){return this.decompose().scale.sx}}),Object.defineProperty(Rt.prototype,"scaleY",{get:function(){return this.decompose().scale.sy}}),Object.defineProperty(Rt.prototype,"isIdentity",{get:function(){return this.sx===1&&this.shy===0&&this.shx===0&&this.sy===1&&this.tx===0&&this.ty===0}}),Rt.prototype.join=function(o){return[this.sx,this.shy,this.shx,this.sy,this.tx,this.ty].map(tt).join(o)},Rt.prototype.multiply=function(o){var v=o.sx*this.sx+o.shy*this.shx,j=o.sx*this.shy+o.shy*this.sy,z=o.shx*this.sx+o.sy*this.shx,K=o.shx*this.shy+o.sy*this.sy,ht=o.tx*this.sx+o.ty*this.shx+this.tx,wt=o.tx*this.shy+o.ty*this.sy+this.ty;return new Rt(v,j,z,K,ht,wt)},Rt.prototype.decompose=function(){var o=this.sx,v=this.shy,j=this.shx,z=this.sy,K=this.tx,ht=this.ty,wt=Math.sqrt(o*o+v*v),Ot=(o/=wt)*j+(v/=wt)*z;j-=o*Ot,z-=v*Ot;var qt=Math.sqrt(j*j+z*z);return Ot/=qt,o*(z/=qt)<v*(j/=qt)&&(o=-o,v=-v,Ot=-Ot,wt=-wt),{scale:new Rt(wt,0,0,qt,0,0),translate:new Rt(1,0,0,1,K,ht),rotate:new Rt(o,v,-v,o,0,0),skew:new Rt(1,0,Ot,1,0,0)}},Rt.prototype.toString=function(o){return this.join(" ")},Rt.prototype.inversed=function(){var o=this.sx,v=this.shy,j=this.shx,z=this.sy,K=this.tx,ht=this.ty,wt=1/(o*z-v*j),Ot=z*wt,qt=-v*wt,Qt=-j*wt,Xt=o*wt;return new Rt(Ot,qt,Qt,Xt,-Ot*K-Qt*ht,-qt*K-Xt*ht)},Rt.prototype.applyToPoint=function(o){var v=o.x*this.sx+o.y*this.shx+this.tx,j=o.x*this.shy+o.y*this.sy+this.ty;return new pi(v,j)},Rt.prototype.applyToRectangle=function(o){var v=this.applyToPoint(o),j=this.applyToPoint(new pi(o.x+o.w,o.y+o.h));return new Ki(v.x,v.y,j.x-v.x,j.y-v.y)},Rt.prototype.clone=function(){var o=this.sx,v=this.shy,j=this.shx,z=this.sy,K=this.tx,ht=this.ty;return new Rt(o,v,j,z,K,ht)},p.Matrix=Rt;var Ir=p.matrixMult=function(o,v){return v.multiply(o)},Jr=new Rt(1,0,0,1,0,0);p.unitMatrix=p.identityMatrix=Jr;var ar=function(o,v){if(!qn[o]){var j=(v instanceof Qn?"Sh":"P")+(Object.keys(ue).length+1).toString(10);v.id=j,qn[o]=j,ue[j]=v,ce.publish("addPattern",v)}};p.ShadingPattern=Qn,p.TilingPattern=Bi,p.addShadingPattern=function(o,v){return U("addShadingPattern()"),ar(o,v),this},p.beginTilingPattern=function(o){U("beginTilingPattern()"),qa(o.boundingBox[0],o.boundingBox[1],o.boundingBox[2]-o.boundingBox[0],o.boundingBox[3]-o.boundingBox[1],o.matrix)},p.endTilingPattern=function(o,v){U("endTilingPattern()"),v.stream=Lt[C].join(`
`),ar(o,v),ce.publish("endTilingPattern",v),Yr.pop().restore()};var De=p.__private__.newObject=function(){var o=Be();return dr(o,!0),o},Be=p.__private__.newObjectDeferred=function(){return G++,$[G]=function(){return st},G},dr=function(o,v){return v=typeof v=="boolean"&&v,$[o]=st,v&&T(o+" 0 obj"),o},ei=p.__private__.newAdditionalObject=function(){var o={objId:Be(),content:""};return Nt.push(o),o},on=Be(),Fr=Be(),Cr=p.__private__.decodeColorString=function(o){var v=o.split(" ");if(v.length!==2||v[1]!=="g"&&v[1]!=="G")v.length===5&&(v[4]==="k"||v[4]==="K")&&(v=[(1-v[0])*(1-v[3]),(1-v[1])*(1-v[3]),(1-v[2])*(1-v[3]),"r"]);else{var j=parseFloat(v[0]);v=[j,j,j,"r"]}for(var z="#",K=0;K<3;K++)z+=("0"+Math.floor(255*parseFloat(v[K])).toString(16)).slice(-2);return z},jr=p.__private__.encodeColorString=function(o){var v;typeof o=="string"&&(o={ch1:o});var j=o.ch1,z=o.ch2,K=o.ch3,ht=o.ch4,wt=o.pdfColorType==="draw"?["G","RG","K"]:["g","rg","k"];if(typeof j=="string"&&j.charAt(0)!=="#"){var Ot=new Jl(j);if(Ot.ok)j=Ot.toHex();else if(!/^\d*\.?\d*$/.test(j))throw new Error('Invalid color "'+j+'" passed to jsPDF.encodeColorString.')}if(typeof j=="string"&&/^#[0-9A-Fa-f]{3}$/.test(j)&&(j="#"+j[1]+j[1]+j[2]+j[2]+j[3]+j[3]),typeof j=="string"&&/^#[0-9A-Fa-f]{6}$/.test(j)){var qt=parseInt(j.substr(1),16);j=qt>>16&255,z=qt>>8&255,K=255&qt}if(z===void 0||ht===void 0&&j===z&&z===K)if(typeof j=="string")v=j+" "+wt[0];else switch(o.precision){case 2:v=pt(j/255)+" "+wt[0];break;case 3:default:v=L(j/255)+" "+wt[0]}else if(ht===void 0||de(ht)==="object"){if(ht&&!isNaN(ht.a)&&ht.a===0)return v=["1.","1.","1.",wt[1]].join(" ");if(typeof j=="string")v=[j,z,K,wt[1]].join(" ");else switch(o.precision){case 2:v=[pt(j/255),pt(z/255),pt(K/255),wt[1]].join(" ");break;default:case 3:v=[L(j/255),L(z/255),L(K/255),wt[1]].join(" ")}}else if(typeof j=="string")v=[j,z,K,ht,wt[2]].join(" ");else switch(o.precision){case 2:v=[pt(j),pt(z),pt(K),pt(ht),wt[2]].join(" ");break;case 3:default:v=[L(j),L(z),L(K),L(ht),wt[2]].join(" ")}return v},Xr=p.__private__.getFilters=function(){return c},yr=p.__private__.putStream=function(o){var v=(o=o||{}).data||"",j=o.filters||Xr(),z=o.alreadyAppliedFilters||[],K=o.addLength1||!1,ht=v.length,wt=o.objectId,Ot=function(Ke){return Ke};if(b!==null&&wt===void 0)throw new Error("ObjectId must be passed to putStream for file encryption");b!==null&&(Ot=Xe.encryptor(wt,0));var qt={};j===!0&&(j=["FlateEncode"]);var Qt=o.additionalKeyValues||[],Xt=(qt=zt.API.processDataByFilters!==void 0?zt.API.processDataByFilters(v,j):{data:v,reverseChain:[]}).reverseChain+(Array.isArray(z)?z.join(" "):z.toString());if(qt.data.length!==0&&(Qt.push({key:"Length",value:qt.data.length}),K===!0&&Qt.push({key:"Length1",value:ht})),Xt.length!=0)if(Xt.split("/").length-1==1)Qt.push({key:"Filter",value:Xt});else{Qt.push({key:"Filter",value:"["+Xt+"]"});for(var ie=0;ie<Qt.length;ie+=1)if(Qt[ie].key==="DecodeParms"){for(var Ne=[],Se=0;Se<qt.reverseChain.split("/").length-1;Se+=1)Ne.push("null");Ne.push(Qt[ie].value),Qt[ie].value="["+Ne.join(" ")+"]"}}T("<<");for(var Ee=0;Ee<Qt.length;Ee++)T("/"+Qt[Ee].key+" "+Qt[Ee].value);T(">>"),qt.data.length!==0&&(T("stream"),T(Ot(qt.data)),T("endstream"))},Kr=p.__private__.putPage=function(o){var v=o.number,j=o.data,z=o.objId,K=o.contentsObjId;dr(z,!0),T("<</Type /Page"),T("/Parent "+o.rootDictionaryObjId+" 0 R"),T("/Resources "+o.resourceDictionaryObjId+" 0 R"),T("/MediaBox ["+parseFloat(tt(o.mediaBox.bottomLeftX))+" "+parseFloat(tt(o.mediaBox.bottomLeftY))+" "+tt(o.mediaBox.topRightX)+" "+tt(o.mediaBox.topRightY)+"]"),o.cropBox!==null&&T("/CropBox ["+tt(o.cropBox.bottomLeftX)+" "+tt(o.cropBox.bottomLeftY)+" "+tt(o.cropBox.topRightX)+" "+tt(o.cropBox.topRightY)+"]"),o.bleedBox!==null&&T("/BleedBox ["+tt(o.bleedBox.bottomLeftX)+" "+tt(o.bleedBox.bottomLeftY)+" "+tt(o.bleedBox.topRightX)+" "+tt(o.bleedBox.topRightY)+"]"),o.trimBox!==null&&T("/TrimBox ["+tt(o.trimBox.bottomLeftX)+" "+tt(o.trimBox.bottomLeftY)+" "+tt(o.trimBox.topRightX)+" "+tt(o.trimBox.topRightY)+"]"),o.artBox!==null&&T("/ArtBox ["+tt(o.artBox.bottomLeftX)+" "+tt(o.artBox.bottomLeftY)+" "+tt(o.artBox.topRightX)+" "+tt(o.artBox.topRightY)+"]"),typeof o.userUnit=="number"&&o.userUnit!==1&&T("/UserUnit "+o.userUnit),ce.publish("putPage",{objId:z,pageContext:Jt[v],pageNumber:v,page:j}),T("/Contents "+K+" 0 R"),T(">>"),T("endobj");var ht=j.join(`
`);return J===E.ADVANCED&&(ht+=`
Q`),dr(K,!0),yr({data:ht,filters:Xr(),objectId:K}),T("endobj"),z},Rn=p.__private__.putPages=function(){var o,v,j=[];for(o=1;o<=_e;o++)Jt[o].objId=Be(),Jt[o].contentsObjId=Be();for(o=1;o<=_e;o++)j.push(Kr({number:o,data:Lt[o],objId:Jt[o].objId,contentsObjId:Jt[o].contentsObjId,mediaBox:Jt[o].mediaBox,cropBox:Jt[o].cropBox,bleedBox:Jt[o].bleedBox,trimBox:Jt[o].trimBox,artBox:Jt[o].artBox,userUnit:Jt[o].userUnit,rootDictionaryObjId:on,resourceDictionaryObjId:Fr}));dr(on,!0),T("<</Type /Pages");var z="/Kids [";for(v=0;v<_e;v++)z+=j[v]+" 0 R ";T(z+"]"),T("/Count "+_e),T(">>"),T("endobj"),ce.publish("postPutPages")},ri=function(o){ce.publish("putFont",{font:o,out:T,newObject:De,putStream:yr}),o.isAlreadyPutted!==!0&&(o.objectNumber=De(),T("<<"),T("/Type /Font"),T("/BaseFont /"+Mi(o.postScriptName)),T("/Subtype /Type1"),typeof o.encoding=="string"&&T("/Encoding /"+o.encoding),T("/FirstChar 32"),T("/LastChar 255"),T(">>"),T("endobj"))},ni=function(){for(var o in me)me.hasOwnProperty(o)&&(w===!1||w===!0&&S.hasOwnProperty(o))&&ri(me[o])},ii=function(o){o.objectNumber=De();var v=[];v.push({key:"Type",value:"/XObject"}),v.push({key:"Subtype",value:"/Form"}),v.push({key:"BBox",value:"["+[tt(o.x),tt(o.y),tt(o.x+o.width),tt(o.y+o.height)].join(" ")+"]"}),v.push({key:"Matrix",value:"["+o.matrix.toString()+"]"});var j=o.pages[1].join(`
`);yr({data:j,additionalKeyValues:v,objectId:o.objectNumber}),T("endobj")},ai=function(){for(var o in Ve)Ve.hasOwnProperty(o)&&ii(Ve[o])},wa=function(o,v){var j,z=[],K=1/(v-1);for(j=0;j<1;j+=K)z.push(j);if(z.push(1),o[0].offset!=0){var ht={offset:0,color:o[0].color};o.unshift(ht)}if(o[o.length-1].offset!=1){var wt={offset:1,color:o[o.length-1].color};o.push(wt)}for(var Ot="",qt=0,Qt=0;Qt<z.length;Qt++){for(j=z[Qt];j>o[qt+1].offset;)qt++;var Xt=o[qt].offset,ie=(j-Xt)/(o[qt+1].offset-Xt),Ne=o[qt].color,Se=o[qt+1].color;Ot+=R(Math.round((1-ie)*Ne[0]+ie*Se[0]).toString(16))+R(Math.round((1-ie)*Ne[1]+ie*Se[1]).toString(16))+R(Math.round((1-ie)*Ne[2]+ie*Se[2]).toString(16))}return Ot.trim()},_s=function(o,v){v||(v=21);var j=De(),z=wa(o.colors,v),K=[];K.push({key:"FunctionType",value:"0"}),K.push({key:"Domain",value:"[0.0 1.0]"}),K.push({key:"Size",value:"["+v+"]"}),K.push({key:"BitsPerSample",value:"8"}),K.push({key:"Range",value:"[0.0 1.0 0.0 1.0 0.0 1.0]"}),K.push({key:"Decode",value:"[0.0 1.0 0.0 1.0 0.0 1.0]"}),yr({data:z,additionalKeyValues:K,alreadyAppliedFilters:["/ASCIIHexDecode"],objectId:j}),T("endobj"),o.objectNumber=De(),T("<< /ShadingType "+o.type),T("/ColorSpace /DeviceRGB");var ht="/Coords ["+tt(parseFloat(o.coords[0]))+" "+tt(parseFloat(o.coords[1]))+" ";o.type===2?ht+=tt(parseFloat(o.coords[2]))+" "+tt(parseFloat(o.coords[3])):ht+=tt(parseFloat(o.coords[2]))+" "+tt(parseFloat(o.coords[3]))+" "+tt(parseFloat(o.coords[4]))+" "+tt(parseFloat(o.coords[5])),T(ht+="]"),o.matrix&&T("/Matrix ["+o.matrix.toString()+"]"),T("/Function "+j+" 0 R"),T("/Extend [true true]"),T(">>"),T("endobj")},Ss=function(o,v){var j=Be(),z=De();v.push({resourcesOid:j,objectOid:z}),o.objectNumber=z;var K=[];K.push({key:"Type",value:"/Pattern"}),K.push({key:"PatternType",value:"1"}),K.push({key:"PaintType",value:"1"}),K.push({key:"TilingType",value:"1"}),K.push({key:"BBox",value:"["+o.boundingBox.map(tt).join(" ")+"]"}),K.push({key:"XStep",value:tt(o.xStep)}),K.push({key:"YStep",value:tt(o.yStep)}),K.push({key:"Resources",value:j+" 0 R"}),o.matrix&&K.push({key:"Matrix",value:"["+o.matrix.toString()+"]"}),yr({data:o.stream,additionalKeyValues:K,objectId:o.objectNumber}),T("endobj")},si=function(o){var v;for(v in ue)ue.hasOwnProperty(v)&&(ue[v]instanceof Qn?_s(ue[v]):ue[v]instanceof Bi&&Ss(ue[v],o))},xa=function(o){for(var v in o.objectNumber=De(),T("<<"),o)switch(v){case"opacity":T("/ca "+pt(o[v]));break;case"stroke-opacity":T("/CA "+pt(o[v]))}T(">>"),T("endobj")},Ps=function(){var o;for(o in Ae)Ae.hasOwnProperty(o)&&xa(Ae[o])},Ri=function(){for(var o in T("/XObject <<"),Ve)Ve.hasOwnProperty(o)&&Ve[o].objectNumber>=0&&T("/"+o+" "+Ve[o].objectNumber+" 0 R");ce.publish("putXobjectDict"),T(">>")},ks=function(){Xe.oid=De(),T("<<"),T("/Filter /Standard"),T("/V "+Xe.v),T("/R "+Xe.r),T("/U <"+Xe.toHexString(Xe.U)+">"),T("/O <"+Xe.toHexString(Xe.O)+">"),T("/P "+Xe.P),T(">>"),T("endobj")},La=function(){for(var o in T("/Font <<"),me)me.hasOwnProperty(o)&&(w===!1||w===!0&&S.hasOwnProperty(o))&&T("/"+o+" "+me[o].objectNumber+" 0 R");T(">>")},Is=function(){if(Object.keys(ue).length>0){for(var o in T("/Shading <<"),ue)ue.hasOwnProperty(o)&&ue[o]instanceof Qn&&ue[o].objectNumber>=0&&T("/"+o+" "+ue[o].objectNumber+" 0 R");ce.publish("putShadingPatternDict"),T(">>")}},oi=function(o){if(Object.keys(ue).length>0){for(var v in T("/Pattern <<"),ue)ue.hasOwnProperty(v)&&ue[v]instanceof p.TilingPattern&&ue[v].objectNumber>=0&&ue[v].objectNumber<o&&T("/"+v+" "+ue[v].objectNumber+" 0 R");ce.publish("putTilingPatternDict"),T(">>")}},Fs=function(){if(Object.keys(Ae).length>0){var o;for(o in T("/ExtGState <<"),Ae)Ae.hasOwnProperty(o)&&Ae[o].objectNumber>=0&&T("/"+o+" "+Ae[o].objectNumber+" 0 R");ce.publish("putGStateDict"),T(">>")}},Ie=function(o){dr(o.resourcesOid,!0),T("<<"),T("/ProcSet [/PDF /Text /ImageB /ImageC /ImageI]"),La(),Is(),oi(o.objectOid),Fs(),Ri(),T(">>"),T("endobj")},Na=function(){var o=[];ni(),Ps(),ai(),si(o),ce.publish("putResources"),o.forEach(Ie),Ie({resourcesOid:Fr,objectOid:Number.MAX_SAFE_INTEGER}),ce.publish("postPutResources")},Aa=function(){ce.publish("putAdditionalObjects");for(var o=0;o<Nt.length;o++){var v=Nt[o];dr(v.objId,!0),T(v.content),T("endobj")}ce.publish("postPutAdditionalObjects")},_a=function(o){Le[o.fontName]=Le[o.fontName]||{},Le[o.fontName][o.fontStyle]=o.id},zi=function(o,v,j,z,K){var ht={id:"F"+(Object.keys(me).length+1).toString(10),postScriptName:o,fontName:v,fontStyle:j,encoding:z,isStandardFont:K||!1,metadata:{}};return ce.publish("addFont",{font:ht,instance:this}),me[ht.id]=ht,_a(ht),ht.id},Cs=function(o){for(var v=0,j=xt.length;v<j;v++){var z=zi.call(this,o[v][0],o[v][1],o[v][2],xt[v][3],!0);w===!1&&(S[z]=!0);var K=o[v][0].split("-");_a({id:z,fontName:K[0],fontStyle:K[1]||""})}ce.publish("addFonts",{fonts:me,dictionary:Le})},Or=function(o){return o.foo=function(){try{return o.apply(this,arguments)}catch(z){var v=z.stack||"";~v.indexOf(" at ")&&(v=v.split(" at ")[1]);var j="Error in function "+v.split(`
`)[0].split("<")[0]+": "+z.message;if(!Ht.console)throw new Error(j);Ht.console.error(j,z),Ht.alert&&alert(j)}},o.foo.bar=o,o.foo},li=function(o,v){var j,z,K,ht,wt,Ot,qt,Qt,Xt;if(K=(v=v||{}).sourceEncoding||"Unicode",wt=v.outputEncoding,(v.autoencode||wt)&&me[re].metadata&&me[re].metadata[K]&&me[re].metadata[K].encoding&&(ht=me[re].metadata[K].encoding,!wt&&me[re].encoding&&(wt=me[re].encoding),!wt&&ht.codePages&&(wt=ht.codePages[0]),typeof wt=="string"&&(wt=ht[wt]),wt)){for(qt=!1,Ot=[],j=0,z=o.length;j<z;j++)(Qt=wt[o.charCodeAt(j)])?Ot.push(String.fromCharCode(Qt)):Ot.push(o[j]),Ot[j].charCodeAt(0)>>8&&(qt=!0);o=Ot.join("")}for(j=o.length;qt===void 0&&j!==0;)o.charCodeAt(j-1)>>8&&(qt=!0),j--;if(!qt)return o;for(Ot=v.noBOM?[]:[254,255],j=0,z=o.length;j<z;j++){if((Xt=(Qt=o.charCodeAt(j))>>8)>>8)throw new Error("Character at position "+j+" of string '"+o+"' exceeds 16bits. Cannot be encoded into UCS-2 BE");Ot.push(Xt),Ot.push(Qt-(Xt<<8))}return String.fromCharCode.apply(void 0,Ot)},sr=p.__private__.pdfEscape=p.pdfEscape=function(o,v){return li(o,v).replace(/\\/g,"\\\\").replace(/\(/g,"\\(").replace(/\)/g,"\\)")},Ui=p.__private__.beginPage=function(o){Lt[++_e]=[],Jt[_e]={objId:0,contentsObjId:0,userUnit:Number(h),artBox:null,bleedBox:null,cropBox:null,trimBox:null,mediaBox:{bottomLeftX:0,bottomLeftY:0,topRightX:Number(o[0]),topRightY:Number(o[1])}},Pa(_e),dt(Lt[C])},Sa=function(o,v){var j,z,K;switch(r=v||r,typeof o=="string"&&(j=k(o.toLowerCase()),Array.isArray(j)&&(z=j[0],K=j[1])),Array.isArray(o)&&(z=o[0]*jt,K=o[1]*jt),isNaN(z)&&(z=l[0],K=l[1]),(z>14400||K>14400)&&(be.warn("A page in a PDF can not be wider or taller than 14400 userUnit. jsPDF limits the width/height to 14400"),z=Math.min(14400,z),K=Math.min(14400,K)),l=[z,K],r.substr(0,1)){case"l":K>z&&(l=[K,z]);break;case"p":z>K&&(l=[K,z])}Ui(l),Oa(Gi),T(Mr),Ji!==0&&T(Ji+" J"),Xi!==0&&T(Xi+" j"),ce.publish("addPage",{pageNumber:_e})},js=function(o){o>0&&o<=_e&&(Lt.splice(o,1),Jt.splice(o,1),_e--,C>_e&&(C=_e),this.setPage(C))},Pa=function(o){o>0&&o<=_e&&(C=o)},Os=p.__private__.getNumberOfPages=p.getNumberOfPages=function(){return Lt.length-1},ka=function(o,v,j){var z,K=void 0;return j=j||{},o=o!==void 0?o:me[re].fontName,v=v!==void 0?v:me[re].fontStyle,z=o.toLowerCase(),Le[z]!==void 0&&Le[z][v]!==void 0?K=Le[z][v]:Le[o]!==void 0&&Le[o][v]!==void 0?K=Le[o][v]:j.disableWarning===!1&&be.warn("Unable to look up font label for font '"+o+"', '"+v+"'. Refer to getFontList() for available fonts."),K||j.noFallback||(K=Le.times[v])==null&&(K=Le.times.normal),K},Ms=p.__private__.putInfo=function(){var o=De(),v=function(z){return z};for(var j in b!==null&&(v=Xe.encryptor(o,0)),T("<<"),T("/Producer ("+sr(v("jsPDF "+zt.version))+")"),Vt)Vt.hasOwnProperty(j)&&Vt[j]&&T("/"+j.substr(0,1).toUpperCase()+j.substr(1)+" ("+sr(v(Vt[j]))+")");T("/CreationDate ("+sr(v(et))+")"),T(">>"),T("endobj")},Hi=p.__private__.putCatalog=function(o){var v=(o=o||{}).rootDictionaryObjId||on;switch(De(),T("<<"),T("/Type /Catalog"),T("/Pages "+v+" 0 R"),Ct||(Ct="fullwidth"),Ct){case"fullwidth":T("/OpenAction [3 0 R /FitH null]");break;case"fullheight":T("/OpenAction [3 0 R /FitV null]");break;case"fullpage":T("/OpenAction [3 0 R /Fit]");break;case"original":T("/OpenAction [3 0 R /XYZ null null 1]");break;default:var j=""+Ct;j.substr(j.length-1)==="%"&&(Ct=parseInt(Ct)/100),typeof Ct=="number"&&T("/OpenAction [3 0 R /XYZ null null "+pt(Ct)+"]")}switch(ee||(ee="continuous"),ee){case"continuous":T("/PageLayout /OneColumn");break;case"single":T("/PageLayout /SinglePage");break;case"two":case"twoleft":T("/PageLayout /TwoColumnLeft");break;case"tworight":T("/PageLayout /TwoColumnRight")}Yt&&T("/PageMode /"+Yt),ce.publish("putCatalog"),T(">>"),T("endobj")},Bs=p.__private__.putTrailer=function(){T("trailer"),T("<<"),T("/Size "+(G+1)),T("/Root "+G+" 0 R"),T("/Info "+(G-1)+" 0 R"),b!==null&&T("/Encrypt "+Xe.oid+" 0 R"),T("/ID [ <"+rt+"> <"+rt+"> ]"),T(">>")},Es=p.__private__.putHeader=function(){T("%PDF-"+M),T("%ºß¬à")},Ts=p.__private__.putXRef=function(){var o="0000000000";T("xref"),T("0 "+(G+1)),T("0000000000 65535 f ");for(var v=1;v<=G;v++)typeof $[v]=="function"?T((o+$[v]()).slice(-10)+" 00000 n "):$[v]!==void 0?T((o+$[v]).slice(-10)+" 00000 n "):T("0000000000 00000 n ")},ln=p.__private__.buildDocument=function(){Ut(),dt(it),ce.publish("buildDocument"),Es(),Rn(),Aa(),Na(),b!==null&&ks(),Ms(),Hi();var o=st;return Ts(),Bs(),T("startxref"),T(""+o),T("%%EOF"),dt(Lt[C]),it.join(`
`)},ui=p.__private__.getBlob=function(o){return new Blob([Et(o)],{type:"application/pdf"})},ci=p.output=p.__private__.output=Or(function(o,v){switch(typeof(v=v||{})=="string"?v={filename:v}:v.filename=v.filename||"generated.pdf",o){case void 0:return ln();case"save":p.save(v.filename);break;case"arraybuffer":return Et(ln());case"blob":return ui(ln());case"bloburi":case"bloburl":if(Ht.URL!==void 0&&typeof Ht.URL.createObjectURL=="function")return Ht.URL&&Ht.URL.createObjectURL(ui(ln()))||void 0;be.warn("bloburl is not supported by your system, because URL.createObjectURL is not supported by your browser.");break;case"datauristring":case"dataurlstring":var j="",z=ln();try{j=jo(z)}catch{j=jo(unescape(encodeURIComponent(z)))}return"data:application/pdf;filename="+v.filename+";base64,"+j;case"pdfobjectnewwindow":if(Object.prototype.toString.call(Ht)==="[object Window]"){var K="https://cdnjs.cloudflare.com/ajax/libs/pdfobject/2.1.1/pdfobject.min.js",ht=' integrity="sha512-4ze/a9/4jqu+tX9dfOqJYSvyYd5M6qum/3HpCLr+/Jqf0whc37VUbkpNGHR7/8pSnCFw47T1fmIpwBV7UySh3g==" crossorigin="anonymous"';v.pdfObjectUrl&&(K=v.pdfObjectUrl,ht="");var wt='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><script src="'+K+'"'+ht+'><\/script><script >PDFObject.embed("'+this.output("dataurlstring")+'", '+JSON.stringify(v)+");<\/script></body></html>",Ot=Ht.open();return Ot!==null&&Ot.document.write(wt),Ot}throw new Error("The option pdfobjectnewwindow just works in a browser-environment.");case"pdfjsnewwindow":if(Object.prototype.toString.call(Ht)==="[object Window]"){var qt='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><iframe id="pdfViewer" src="'+(v.pdfJsUrl||"examples/PDF.js/web/viewer.html")+"?file=&downloadName="+v.filename+'" width="500px" height="400px" /></body></html>',Qt=Ht.open();if(Qt!==null){Qt.document.write(qt);var Xt=this;Qt.document.documentElement.querySelector("#pdfViewer").onload=function(){Qt.document.title=v.filename,Qt.document.documentElement.querySelector("#pdfViewer").contentWindow.PDFViewerApplication.open(Xt.output("bloburl"))}}return Qt}throw new Error("The option pdfjsnewwindow just works in a browser-environment.");case"dataurlnewwindow":if(Object.prototype.toString.call(Ht)!=="[object Window]")throw new Error("The option dataurlnewwindow just works in a browser-environment.");var ie='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><iframe src="'+this.output("datauristring",v)+'"></iframe></body></html>',Ne=Ht.open();if(Ne!==null&&(Ne.document.write(ie),Ne.document.title=v.filename),Ne||typeof safari>"u")return Ne;break;case"datauri":case"dataurl":return Ht.document.location.href=this.output("datauristring",v);default:return null}}),Ia=function(o){return Array.isArray(Dn)===!0&&Dn.indexOf(o)>-1};switch(a){case"pt":jt=1;break;case"mm":jt=72/25.4;break;case"cm":jt=72/2.54;break;case"in":jt=72;break;case"px":jt=Ia("px_scaling")==1?.75:96/72;break;case"pc":case"em":jt=12;break;case"ex":jt=6;break;default:if(typeof a!="number")throw new Error("Invalid unit: "+a);jt=a}var Xe=null;Ft(),X();var qs=function(o){return b!==null?Xe.encryptor(o,0):function(v){return v}},Fa=p.__private__.getPageInfo=p.getPageInfo=function(o){if(isNaN(o)||o%1!=0)throw new Error("Invalid argument passed to jsPDF.getPageInfo");return{objId:Jt[o].objId,pageNumber:o,pageContext:Jt[o]}},Gt=p.__private__.getPageInfoByObjId=function(o){if(isNaN(o)||o%1!=0)throw new Error("Invalid argument passed to jsPDF.getPageInfoByObjId");for(var v in Jt)if(Jt[v].objId===o)break;return Fa(v)},Ds=p.__private__.getCurrentPageInfo=p.getCurrentPageInfo=function(){return{objId:Jt[C].objId,pageNumber:C,pageContext:Jt[C]}};p.addPage=function(){return Sa.apply(this,arguments),this},p.setPage=function(){return Pa.apply(this,arguments),dt.call(this,Lt[C]),this},p.insertPage=function(o){return this.addPage(),this.movePage(C,o),this},p.movePage=function(o,v){var j,z;if(o>v){j=Lt[o],z=Jt[o];for(var K=o;K>v;K--)Lt[K]=Lt[K-1],Jt[K]=Jt[K-1];Lt[v]=j,Jt[v]=z,this.setPage(v)}else if(o<v){j=Lt[o],z=Jt[o];for(var ht=o;ht<v;ht++)Lt[ht]=Lt[ht+1],Jt[ht]=Jt[ht+1];Lt[v]=j,Jt[v]=z,this.setPage(v)}return this},p.deletePage=function(){return js.apply(this,arguments),this},p.__private__.text=p.text=function(o,v,j,z,K){var ht,wt,Ot,qt,Qt,Xt,ie,Ne,Se,Ee=(z=z||{}).scope||this;if(typeof o=="number"&&typeof v=="number"&&(typeof j=="string"||Array.isArray(j))){var Ke=j;j=v,v=o,o=Ke}if(arguments[3]instanceof Rt?(U("The transform parameter of text() with a Matrix value"),Se=K):(Ot=arguments[4],qt=arguments[5],de(ie=arguments[3])==="object"&&ie!==null||(typeof Ot=="string"&&(qt=Ot,Ot=null),typeof ie=="string"&&(qt=ie,ie=null),typeof ie=="number"&&(Ot=ie,ie=null),z={flags:ie,angle:Ot,align:qt})),isNaN(v)||isNaN(j)||o==null)throw new Error("Invalid arguments passed to jsPDF.text");if(o.length===0)return Ee;var ze="",Br=!1,pr=typeof z.lineHeightFactor=="number"?z.lineHeightFactor:Un,$r=Ee.internal.scaleFactor;function Da(ye){return ye=ye.split("	").join(Array(z.TabLen||9).join(" ")),sr(ye,ie)}function ta(ye){for(var we,Ce=ye.concat(),Re=[],dn=Ce.length;dn--;)typeof(we=Ce.shift())=="string"?Re.push(we):Array.isArray(ye)&&(we.length===1||we[1]===void 0&&we[2]===void 0)?Re.push(we[0]):Re.push([we[0],we[1],we[2]]);return Re}function ea(ye,we){var Ce;if(typeof ye=="string")Ce=we(ye)[0];else if(Array.isArray(ye)){for(var Re,dn,ua=ye.concat(),_i=[],Wa=ua.length;Wa--;)typeof(Re=ua.shift())=="string"?_i.push(we(Re)[0]):Array.isArray(Re)&&typeof Re[0]=="string"&&(dn=we(Re[0],Re[1],Re[2]),_i.push([dn[0],dn[1],dn[2]]));Ce=_i}return Ce}var mi=!1,ra=!0;if(typeof o=="string")mi=!0;else if(Array.isArray(o)){var na=o.concat();wt=[];for(var vi,Ge=na.length;Ge--;)(typeof(vi=na.shift())!="string"||Array.isArray(vi)&&typeof vi[0]!="string")&&(ra=!1);mi=ra}if(mi===!1)throw new Error('Type of text must be string or Array. "'+o+'" is not recognized.');typeof o=="string"&&(o=o.match(/[\r?\n]/)?o.split(/\r\n|\r|\n/g):[o]);var bi=At/Ee.internal.scaleFactor,yi=bi*(pr-1);switch(z.baseline){case"bottom":j-=yi;break;case"top":j+=bi-yi;break;case"hanging":j+=bi-2*yi;break;case"middle":j+=bi/2-yi}if((Xt=z.maxWidth||0)>0&&(typeof o=="string"?o=Ee.splitTextToSize(o,Xt):Object.prototype.toString.call(o)==="[object Array]"&&(o=o.reduce(function(ye,we){return ye.concat(Ee.splitTextToSize(we,Xt))},[]))),ht={text:o,x:v,y:j,options:z,mutex:{pdfEscape:sr,activeFontKey:re,fonts:me,activeFontSize:At}},ce.publish("preProcessText",ht),o=ht.text,Ot=(z=ht.options).angle,!(Se instanceof Rt)&&Ot&&typeof Ot=="number"){Ot*=Math.PI/180,z.rotationDirection===0&&(Ot=-Ot),J===E.ADVANCED&&(Ot=-Ot);var wi=Math.cos(Ot),ia=Math.sin(Ot);Se=new Rt(wi,ia,-ia,wi,0,0)}else Ot&&Ot instanceof Rt&&(Se=Ot);J!==E.ADVANCED||Se||(Se=Jr),(Qt=z.charSpace||di)!==void 0&&(ze+=tt(A(Qt))+` Tc
`,this.setCharSpace(this.getCharSpace()||0)),(Ne=z.horizontalScale)!==void 0&&(ze+=tt(100*Ne)+` Tz
`),z.lang;var or=-1,Xs=z.renderingMode!==void 0?z.renderingMode:z.stroke,aa=Ee.internal.getCurrentPageInfo().pageContext;switch(Xs){case 0:case!1:case"fill":or=0;break;case 1:case!0:case"stroke":or=1;break;case 2:case"fillThenStroke":or=2;break;case 3:case"invisible":or=3;break;case 4:case"fillAndAddForClipping":or=4;break;case 5:case"strokeAndAddPathForClipping":or=5;break;case 6:case"fillThenStrokeAndAddToPathForClipping":or=6;break;case 7:case"addToPathForClipping":or=7}var Ra=aa.usedRenderingMode!==void 0?aa.usedRenderingMode:-1;or!==-1?ze+=or+` Tr
`:Ra!==-1&&(ze+=`0 Tr
`),or!==-1&&(aa.usedRenderingMode=or),qt=z.align||"left";var wr,xi=At*pr,za=Ee.internal.pageSize.getWidth(),Ua=me[re];Qt=z.charSpace||di,Xt=z.maxWidth||0,ie=Object.assign({autoencode:!0,noBOM:!0},z.flags);var xn=[],Vn=function(ye){return Ee.getStringUnitWidth(ye,{font:Ua,charSpace:Qt,fontSize:At,doKerning:!1})*At/$r};if(Object.prototype.toString.call(o)==="[object Array]"){var lr;wt=ta(o),qt!=="left"&&(wr=wt.map(Vn));var rr,Ln=0;if(qt==="right"){v-=wr[0],o=[],Ge=wt.length;for(var cn=0;cn<Ge;cn++)cn===0?(rr=Qr(v),lr=un(j)):(rr=A(Ln-wr[cn]),lr=-xi),o.push([wt[cn],rr,lr]),Ln=wr[cn]}else if(qt==="center"){v-=wr[0]/2,o=[],Ge=wt.length;for(var hn=0;hn<Ge;hn++)hn===0?(rr=Qr(v),lr=un(j)):(rr=A((Ln-wr[hn])/2),lr=-xi),o.push([wt[hn],rr,lr]),Ln=wr[hn]}else if(qt==="left"){o=[],Ge=wt.length;for(var Li=0;Li<Ge;Li++)o.push(wt[Li])}else if(qt==="justify"&&Ua.encoding==="Identity-H"){o=[],Ge=wt.length,Xt=Xt!==0?Xt:za;for(var fn=0,Fe=0;Fe<Ge;Fe++)if(lr=Fe===0?un(j):-xi,rr=Fe===0?Qr(v):fn,Fe<Ge-1){var sa=A((Xt-wr[Fe])/(wt[Fe].split(" ").length-1)),nr=wt[Fe].split(" ");o.push([nr[0]+" ",rr,lr]),fn=0;for(var xr=1;xr<nr.length;xr++){var Ni=(Vn(nr[xr-1]+" "+nr[xr])-Vn(nr[xr]))*$r+sa;xr==nr.length-1?o.push([nr[xr],Ni,0]):o.push([nr[xr]+" ",Ni,0]),fn-=Ni}}else o.push([wt[Fe],rr,lr]);o.push(["",fn,0])}else{if(qt!=="justify")throw new Error('Unrecognized alignment option, use "left", "center", "right" or "justify".');for(o=[],Ge=wt.length,Xt=Xt!==0?Xt:za,Fe=0;Fe<Ge;Fe++)lr=Fe===0?un(j):-xi,rr=Fe===0?Qr(v):0,Fe<Ge-1?xn.push(tt(A((Xt-wr[Fe])/(wt[Fe].split(" ").length-1)))):xn.push(0),o.push([wt[Fe],rr,lr])}}var Ha=typeof z.R2L=="boolean"?z.R2L:Tt;Ha===!0&&(o=ea(o,function(ye,we,Ce){return[ye.split("").reverse().join(""),we,Ce]})),ht={text:o,x:v,y:j,options:z,mutex:{pdfEscape:sr,activeFontKey:re,fonts:me,activeFontSize:At}},ce.publish("postProcessText",ht),o=ht.text,Br=ht.mutex.isHex||!1;var oa=me[re].encoding;oa!=="WinAnsiEncoding"&&oa!=="StandardEncoding"||(o=ea(o,function(ye,we,Ce){return[Da(ye),we,Ce]})),wt=ta(o),o=[];for(var Gn,Yn,Nn,Jn=0,Ai=1,Xn=Array.isArray(wt[0])?Ai:Jn,An="",la=function(ye,we,Ce){var Re="";return Ce instanceof Rt?(Ce=typeof z.angle=="number"?Ir(Ce,new Rt(1,0,0,1,ye,we)):Ir(new Rt(1,0,0,1,ye,we),Ce),J===E.ADVANCED&&(Ce=Ir(new Rt(1,0,0,-1,0,0),Ce)),Re=Ce.join(" ")+` Tm
`):Re=tt(ye)+" "+tt(we)+` Td
`,Re},Lr=0;Lr<wt.length;Lr++){switch(An="",Xn){case Ai:Nn=(Br?"<":"(")+wt[Lr][0]+(Br?">":")"),Gn=parseFloat(wt[Lr][1]),Yn=parseFloat(wt[Lr][2]);break;case Jn:Nn=(Br?"<":"(")+wt[Lr]+(Br?">":")"),Gn=Qr(v),Yn=un(j)}xn!==void 0&&xn[Lr]!==void 0&&(An=xn[Lr]+` Tw
`),Lr===0?o.push(An+la(Gn,Yn,Se)+Nn):Xn===Jn?o.push(An+Nn):Xn===Ai&&o.push(An+la(Gn,Yn,Se)+Nn)}o=Xn===Jn?o.join(` Tj
T* `):o.join(` Tj
`),o+=` Tj
`;var Nr=`BT
/`;return Nr+=re+" "+At+` Tf
`,Nr+=tt(At*pr)+` TL
`,Nr+=Hn+`
`,Nr+=ze,Nr+=o,T(Nr+="ET"),S[re]=!0,Ee};var Rs=p.__private__.clip=p.clip=function(o){return T(o==="evenodd"?"W*":"W"),this};p.clipEvenOdd=function(){return Rs("evenodd")},p.__private__.discardPath=p.discardPath=function(){return T("n"),this};var Zr=p.__private__.isValidStyle=function(o){var v=!1;return[void 0,null,"S","D","F","DF","FD","f","f*","B","B*","n"].indexOf(o)!==-1&&(v=!0),v};p.__private__.setDefaultPathOperation=p.setDefaultPathOperation=function(o){return Zr(o)&&(g=o),this};var Ca=p.__private__.getStyle=p.getStyle=function(o){var v=g;switch(o){case"D":case"S":v="S";break;case"F":v="f";break;case"FD":case"DF":v="B";break;case"f":case"f*":case"B":case"B*":v=o}return v},ja=p.close=function(){return T("h"),this};p.stroke=function(){return T("S"),this},p.fill=function(o){return hi("f",o),this},p.fillEvenOdd=function(o){return hi("f*",o),this},p.fillStroke=function(o){return hi("B",o),this},p.fillStrokeEvenOdd=function(o){return hi("B*",o),this};var hi=function(o,v){de(v)==="object"?Us(v,o):T(o)},Wi=function(o){o===null||J===E.ADVANCED&&o===void 0||(o=Ca(o),T(o))};function zs(o,v,j,z,K){var ht=new Bi(v||this.boundingBox,j||this.xStep,z||this.yStep,this.gState,K||this.matrix);ht.stream=this.stream;var wt=o+"$$"+this.cloneIndex+++"$$";return ar(wt,ht),ht}var Us=function(o,v){var j=qn[o.key],z=ue[j];if(z instanceof Qn)T("q"),T(Hs(v)),z.gState&&p.setGState(z.gState),T(o.matrix.toString()+" cm"),T("/"+j+" sh"),T("Q");else if(z instanceof Bi){var K=new Rt(1,0,0,-1,0,wn());o.matrix&&(K=K.multiply(o.matrix||Jr),j=zs.call(z,o.key,o.boundingBox,o.xStep,o.yStep,K).id),T("q"),T("/Pattern cs"),T("/"+j+" scn"),z.gState&&p.setGState(z.gState),T(v),T("Q")}},Hs=function(o){switch(o){case"f":case"F":return"W n";case"f*":return"W* n";case"B":return"W S";case"B*":return"W* S";case"S":return"W S";case"n":return"W n"}},Vi=p.moveTo=function(o,v){return T(tt(A(o))+" "+tt(D(v))+" m"),this},zn=p.lineTo=function(o,v){return T(tt(A(o))+" "+tt(D(v))+" l"),this},bn=p.curveTo=function(o,v,j,z,K,ht){return T([tt(A(o)),tt(D(v)),tt(A(j)),tt(D(z)),tt(A(K)),tt(D(ht)),"c"].join(" ")),this};p.__private__.line=p.line=function(o,v,j,z,K){if(isNaN(o)||isNaN(v)||isNaN(j)||isNaN(z)||!Zr(K))throw new Error("Invalid arguments passed to jsPDF.line");return J===E.COMPAT?this.lines([[j-o,z-v]],o,v,[1,1],K||"S"):this.lines([[j-o,z-v]],o,v,[1,1]).stroke()},p.__private__.lines=p.lines=function(o,v,j,z,K,ht){var wt,Ot,qt,Qt,Xt,ie,Ne,Se,Ee,Ke,ze,Br;if(typeof o=="number"&&(Br=j,j=v,v=o,o=Br),z=z||[1,1],ht=ht||!1,isNaN(v)||isNaN(j)||!Array.isArray(o)||!Array.isArray(z)||!Zr(K)||typeof ht!="boolean")throw new Error("Invalid arguments passed to jsPDF.lines");for(Vi(v,j),wt=z[0],Ot=z[1],Qt=o.length,Ke=v,ze=j,qt=0;qt<Qt;qt++)(Xt=o[qt]).length===2?(Ke=Xt[0]*wt+Ke,ze=Xt[1]*Ot+ze,zn(Ke,ze)):(ie=Xt[0]*wt+Ke,Ne=Xt[1]*Ot+ze,Se=Xt[2]*wt+Ke,Ee=Xt[3]*Ot+ze,Ke=Xt[4]*wt+Ke,ze=Xt[5]*Ot+ze,bn(ie,Ne,Se,Ee,Ke,ze));return ht&&ja(),Wi(K),this},p.path=function(o){for(var v=0;v<o.length;v++){var j=o[v],z=j.c;switch(j.op){case"m":Vi(z[0],z[1]);break;case"l":zn(z[0],z[1]);break;case"c":bn.apply(this,z);break;case"h":ja()}}return this},p.__private__.rect=p.rect=function(o,v,j,z,K){if(isNaN(o)||isNaN(v)||isNaN(j)||isNaN(z)||!Zr(K))throw new Error("Invalid arguments passed to jsPDF.rect");return J===E.COMPAT&&(z=-z),T([tt(A(o)),tt(D(v)),tt(A(j)),tt(A(z)),"re"].join(" ")),Wi(K),this},p.__private__.triangle=p.triangle=function(o,v,j,z,K,ht,wt){if(isNaN(o)||isNaN(v)||isNaN(j)||isNaN(z)||isNaN(K)||isNaN(ht)||!Zr(wt))throw new Error("Invalid arguments passed to jsPDF.triangle");return this.lines([[j-o,z-v],[K-j,ht-z],[o-K,v-ht]],o,v,[1,1],wt,!0),this},p.__private__.roundedRect=p.roundedRect=function(o,v,j,z,K,ht,wt){if(isNaN(o)||isNaN(v)||isNaN(j)||isNaN(z)||isNaN(K)||isNaN(ht)||!Zr(wt))throw new Error("Invalid arguments passed to jsPDF.roundedRect");var Ot=4/3*(Math.SQRT2-1);return K=Math.min(K,.5*j),ht=Math.min(ht,.5*z),this.lines([[j-2*K,0],[K*Ot,0,K,ht-ht*Ot,K,ht],[0,z-2*ht],[0,ht*Ot,-K*Ot,ht,-K,ht],[2*K-j,0],[-K*Ot,0,-K,-ht*Ot,-K,-ht],[0,2*ht-z],[0,-ht*Ot,K*Ot,-ht,K,-ht]],o+K,v,[1,1],wt,!0),this},p.__private__.ellipse=p.ellipse=function(o,v,j,z,K){if(isNaN(o)||isNaN(v)||isNaN(j)||isNaN(z)||!Zr(K))throw new Error("Invalid arguments passed to jsPDF.ellipse");var ht=4/3*(Math.SQRT2-1)*j,wt=4/3*(Math.SQRT2-1)*z;return Vi(o+j,v),bn(o+j,v-wt,o+ht,v-z,o,v-z),bn(o-ht,v-z,o-j,v-wt,o-j,v),bn(o-j,v+wt,o-ht,v+z,o,v+z),bn(o+ht,v+z,o+j,v+wt,o+j,v),Wi(K),this},p.__private__.circle=p.circle=function(o,v,j,z){if(isNaN(o)||isNaN(v)||isNaN(j)||!Zr(z))throw new Error("Invalid arguments passed to jsPDF.circle");return this.ellipse(o,v,j,j,z)},p.setFont=function(o,v,j){return j&&(v=yt(v,j)),re=ka(o,v,{disableWarning:!1}),this};var Ws=p.__private__.getFont=p.getFont=function(){return me[ka.apply(p,arguments)]};p.__private__.getFontList=p.getFontList=function(){var o,v,j={};for(o in Le)if(Le.hasOwnProperty(o))for(v in j[o]=[],Le[o])Le[o].hasOwnProperty(v)&&j[o].push(v);return j},p.addFont=function(o,v,j,z,K){var ht=["StandardEncoding","MacRomanEncoding","Identity-H","WinAnsiEncoding"];return arguments[3]&&ht.indexOf(arguments[3])!==-1?K=arguments[3]:arguments[3]&&ht.indexOf(arguments[3])==-1&&(j=yt(j,z)),K=K||"Identity-H",zi.call(this,o,v,j,K)};var Un,Gi=n.lineWidth||.200025,fi=p.__private__.getLineWidth=p.getLineWidth=function(){return Gi},Oa=p.__private__.setLineWidth=p.setLineWidth=function(o){return Gi=o,T(tt(A(o))+" w"),this};p.__private__.setLineDash=zt.API.setLineDash=zt.API.setLineDashPattern=function(o,v){if(o=o||[],v=v||0,isNaN(v)||!Array.isArray(o))throw new Error("Invalid arguments passed to jsPDF.setLineDash");return o=o.map(function(j){return tt(A(j))}).join(" "),v=tt(A(v)),T("["+o+"] "+v+" d"),this};var Ma=p.__private__.getLineHeight=p.getLineHeight=function(){return At*Un};p.__private__.getLineHeight=p.getLineHeight=function(){return At*Un};var Ba=p.__private__.setLineHeightFactor=p.setLineHeightFactor=function(o){return typeof(o=o||1.15)=="number"&&(Un=o),this},Ea=p.__private__.getLineHeightFactor=p.getLineHeightFactor=function(){return Un};Ba(n.lineHeight);var Qr=p.__private__.getHorizontalCoordinate=function(o){return A(o)},un=p.__private__.getVerticalCoordinate=function(o){return J===E.ADVANCED?o:Jt[C].mediaBox.topRightY-Jt[C].mediaBox.bottomLeftY-A(o)},Vs=p.__private__.getHorizontalCoordinateString=p.getHorizontalCoordinateString=function(o){return tt(Qr(o))},yn=p.__private__.getVerticalCoordinateString=p.getVerticalCoordinateString=function(o){return tt(un(o))},Mr=n.strokeColor||"0 G";p.__private__.getStrokeColor=p.getDrawColor=function(){return Cr(Mr)},p.__private__.setStrokeColor=p.setDrawColor=function(o,v,j,z){return Mr=jr({ch1:o,ch2:v,ch3:j,ch4:z,pdfColorType:"draw",precision:2}),T(Mr),this};var Yi=n.fillColor||"0 g";p.__private__.getFillColor=p.getFillColor=function(){return Cr(Yi)},p.__private__.setFillColor=p.setFillColor=function(o,v,j,z){return Yi=jr({ch1:o,ch2:v,ch3:j,ch4:z,pdfColorType:"fill",precision:2}),T(Yi),this};var Hn=n.textColor||"0 g",Gs=p.__private__.getTextColor=p.getTextColor=function(){return Cr(Hn)};p.__private__.setTextColor=p.setTextColor=function(o,v,j,z){return Hn=jr({ch1:o,ch2:v,ch3:j,ch4:z,pdfColorType:"text",precision:3}),this};var di=n.charSpace,Ys=p.__private__.getCharSpace=p.getCharSpace=function(){return parseFloat(di||0)};p.__private__.setCharSpace=p.setCharSpace=function(o){if(isNaN(o))throw new Error("Invalid argument passed to jsPDF.setCharSpace");return di=o,this};var Ji=0;p.CapJoinStyles={0:0,butt:0,but:0,miter:0,1:1,round:1,rounded:1,circle:1,2:2,projecting:2,project:2,square:2,bevel:2},p.__private__.setLineCap=p.setLineCap=function(o){var v=p.CapJoinStyles[o];if(v===void 0)throw new Error("Line cap style of '"+o+"' is not recognized. See or extend .CapJoinStyles property for valid styles");return Ji=v,T(v+" J"),this};var Xi=0;p.__private__.setLineJoin=p.setLineJoin=function(o){var v=p.CapJoinStyles[o];if(v===void 0)throw new Error("Line join style of '"+o+"' is not recognized. See or extend .CapJoinStyles property for valid styles");return Xi=v,T(v+" j"),this},p.__private__.setLineMiterLimit=p.__private__.setMiterLimit=p.setLineMiterLimit=p.setMiterLimit=function(o){if(o=o||0,isNaN(o))throw new Error("Invalid argument passed to jsPDF.setLineMiterLimit");return T(tt(A(o))+" M"),this},p.GState=ws,p.setGState=function(o){(o=typeof o=="string"?Ae[kr[o]]:Ta(null,o)).equals(sn)||(T("/"+o.id+" gs"),sn=o)};var Ta=function(o,v){if(!o||!kr[o]){var j=!1;for(var z in Ae)if(Ae.hasOwnProperty(z)&&Ae[z].equals(v)){j=!0;break}if(j)v=Ae[z];else{var K="GS"+(Object.keys(Ae).length+1).toString(10);Ae[K]=v,v.id=K}return o&&(kr[o]=v.id),ce.publish("addGState",v),v}};p.addGState=function(o,v){return Ta(o,v),this},p.saveGraphicsState=function(){return T("q"),Vr.push({key:re,size:At,color:Hn}),this},p.restoreGraphicsState=function(){T("Q");var o=Vr.pop();return re=o.key,At=o.size,Hn=o.color,sn=null,this},p.setCurrentTransformationMatrix=function(o){return T(o.toString()+" cm"),this},p.comment=function(o){return T("#"+o),this};var pi=function(o,v){var j=o||0;Object.defineProperty(this,"x",{enumerable:!0,get:function(){return j},set:function(ht){isNaN(ht)||(j=parseFloat(ht))}});var z=v||0;Object.defineProperty(this,"y",{enumerable:!0,get:function(){return z},set:function(ht){isNaN(ht)||(z=parseFloat(ht))}});var K="pt";return Object.defineProperty(this,"type",{enumerable:!0,get:function(){return K},set:function(ht){K=ht.toString()}}),this},Ki=function(o,v,j,z){pi.call(this,o,v),this.type="rect";var K=j||0;Object.defineProperty(this,"w",{enumerable:!0,get:function(){return K},set:function(wt){isNaN(wt)||(K=parseFloat(wt))}});var ht=z||0;return Object.defineProperty(this,"h",{enumerable:!0,get:function(){return ht},set:function(wt){isNaN(wt)||(ht=parseFloat(wt))}}),this},Zi=function(){this.page=_e,this.currentPage=C,this.pages=Lt.slice(0),this.pagesContext=Jt.slice(0),this.x=Je,this.y=oe,this.matrix=Pr,this.width=Wn(C),this.height=wn(C),this.outputDestination=St,this.id="",this.objectNumber=-1};Zi.prototype.restore=function(){_e=this.page,C=this.currentPage,Jt=this.pagesContext,Lt=this.pages,Je=this.x,oe=this.y,Pr=this.matrix,Qi(C,this.width),$i(C,this.height),St=this.outputDestination};var qa=function(o,v,j,z,K){Yr.push(new Zi),_e=C=0,Lt=[],Je=o,oe=v,Pr=K,Ui([j,z])},Js=function(o){if(Gr[o])Yr.pop().restore();else{var v=new Zi,j="Xo"+(Object.keys(Ve).length+1).toString(10);v.id=j,Gr[o]=j,Ve[j]=v,ce.publish("addFormObject",v),Yr.pop().restore()}};for(var gi in p.beginFormObject=function(o,v,j,z,K){return qa(o,v,j,z,K),this},p.endFormObject=function(o){return Js(o),this},p.doFormObject=function(o,v){var j=Ve[Gr[o]];return T("q"),T(v.toString()+" cm"),T("/"+j.id+" Do"),T("Q"),this},p.getFormObject=function(o){var v=Ve[Gr[o]];return{x:v.x,y:v.y,width:v.width,height:v.height,matrix:v.matrix}},p.save=function(o,v){return o=o||"generated.pdf",(v=v||{}).returnPromise=v.returnPromise||!1,v.returnPromise===!1?(Zn(ui(ln()),o),typeof Zn.unload=="function"&&Ht.setTimeout&&setTimeout(Zn.unload,911),this):new Promise(function(j,z){try{var K=Zn(ui(ln()),o);typeof Zn.unload=="function"&&Ht.setTimeout&&setTimeout(Zn.unload,911),j(K)}catch(ht){z(ht.message)}})},zt.API)zt.API.hasOwnProperty(gi)&&(gi==="events"&&zt.API.events.length?function(o,v){var j,z,K;for(K=v.length-1;K!==-1;K--)j=v[K][0],z=v[K][1],o.subscribe.apply(o,[j].concat(typeof z=="function"?[z]:z))}(ce,zt.API.events):p[gi]=zt.API[gi]);var Wn=p.getPageWidth=function(o){return(Jt[o=o||C].mediaBox.topRightX-Jt[o].mediaBox.bottomLeftX)/jt},Qi=p.setPageWidth=function(o,v){Jt[o].mediaBox.topRightX=v*jt+Jt[o].mediaBox.bottomLeftX},wn=p.getPageHeight=function(o){return(Jt[o=o||C].mediaBox.topRightY-Jt[o].mediaBox.bottomLeftY)/jt},$i=p.setPageHeight=function(o,v){Jt[o].mediaBox.topRightY=v*jt+Jt[o].mediaBox.bottomLeftY};return p.internal={pdfEscape:sr,getStyle:Ca,getFont:Ws,getFontSize:kt,getCharSpace:Ys,getTextColor:Gs,getLineHeight:Ma,getLineHeightFactor:Ea,getLineWidth:fi,write:Zt,getHorizontalCoordinate:Qr,getVerticalCoordinate:un,getCoordinateString:Vs,getVerticalCoordinateString:yn,collections:{},newObject:De,newAdditionalObject:ei,newObjectDeferred:Be,newObjectDeferredBegin:dr,getFilters:Xr,putStream:yr,events:ce,scaleFactor:jt,pageSize:{getWidth:function(){return Wn(C)},setWidth:function(o){Qi(C,o)},getHeight:function(){return wn(C)},setHeight:function(o){$i(C,o)}},encryptionOptions:b,encryption:Xe,getEncryptor:qs,output:ci,getNumberOfPages:Os,pages:Lt,out:T,f2:pt,f3:L,getPageInfo:Fa,getPageInfoByObjId:Gt,getCurrentPageInfo:Ds,getPDFVersion:I,Point:pi,Rectangle:Ki,Matrix:Rt,hasHotfix:Ia},Object.defineProperty(p.internal.pageSize,"width",{get:function(){return Wn(C)},set:function(o){Qi(C,o)},enumerable:!0,configurable:!0}),Object.defineProperty(p.internal.pageSize,"height",{get:function(){return wn(C)},set:function(o){$i(C,o)},enumerable:!0,configurable:!0}),Cs.call(p,xt),re="F1",Sa(l,r),ce.publish("initialized"),p}Oi.prototype.lsbFirstWord=function(n){return String.fromCharCode(n>>0&255,n>>8&255,n>>16&255,n>>24&255)},Oi.prototype.toHexString=function(n){return n.split("").map(function(e){return("0"+(255&e.charCodeAt(0)).toString(16)).slice(-2)}).join("")},Oi.prototype.hexToBytes=function(n){for(var e=[],r=0;r<n.length;r+=2)e.push(String.fromCharCode(parseInt(n.substr(r,2),16)));return e.join("")},Oi.prototype.processOwnerPassword=function(n,e){return Mo(Oo(e).substr(0,5),n)},Oi.prototype.encryptor=function(n,e){var r=Oo(this.encryptionKey+String.fromCharCode(255&n,n>>8&255,n>>16&255,255&e,e>>8&255)).substr(0,10);return function(a){return Mo(r,a)}},ws.prototype.equals=function(n){var e,r="id,objectNumber,equals";if(!n||de(n)!==de(this))return!1;var a=0;for(e in this)if(!(r.indexOf(e)>=0)){if(this.hasOwnProperty(e)&&!n.hasOwnProperty(e)||this[e]!==n[e])return!1;a++}for(e in n)n.hasOwnProperty(e)&&r.indexOf(e)<0&&a--;return a===0},zt.API={events:[]},zt.version="3.0.1";var ke=zt.API,Ro=1,ti=function(n){return n.replace(/\\/g,"\\\\").replace(/\(/g,"\\(").replace(/\)/g,"\\)")},Ci=function(n){return n.replace(/\\\\/g,"\\").replace(/\\\(/g,"(").replace(/\\\)/g,")")},Kt=function(n){return n.toFixed(2)},Mn=function(n){return n.toFixed(5)};ke.__acroform__={};var fr=function(n,e){n.prototype=Object.create(e.prototype),n.prototype.constructor=n},Sl=function(n){return n*Ro},en=function(n){var e=new Ql,r=Bt.internal.getHeight(n)||0,a=Bt.internal.getWidth(n)||0;return e.BBox=[0,0,Number(Kt(a)),Number(Kt(r))],e},vc=ke.__acroform__.setBit=function(n,e){if(n=n||0,e=e||0,isNaN(n)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.setBit");return n|=1<<e},bc=ke.__acroform__.clearBit=function(n,e){if(n=n||0,e=e||0,isNaN(n)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.clearBit");return n&=~(1<<e)},yc=ke.__acroform__.getBit=function(n,e){if(isNaN(n)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.getBit");return n&1<<e?1:0},je=ke.__acroform__.getBitForPdf=function(n,e){if(isNaN(n)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.getBitForPdf");return yc(n,e-1)},Oe=ke.__acroform__.setBitForPdf=function(n,e){if(isNaN(n)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.setBitForPdf");return vc(n,e-1)},Me=ke.__acroform__.clearBitForPdf=function(n,e){if(isNaN(n)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.clearBitForPdf");return bc(n,e-1)},wc=ke.__acroform__.calculateCoordinates=function(n,e){var r=e.internal.getHorizontalCoordinate,a=e.internal.getVerticalCoordinate,l=n[0],s=n[1],c=n[2],h=n[3],f={};return f.lowerLeft_X=r(l)||0,f.lowerLeft_Y=a(s+h)||0,f.upperRight_X=r(l+c)||0,f.upperRight_Y=a(s)||0,[Number(Kt(f.lowerLeft_X)),Number(Kt(f.lowerLeft_Y)),Number(Kt(f.upperRight_X)),Number(Kt(f.upperRight_Y))]},xc=function(n){if(n.appearanceStreamContent)return n.appearanceStreamContent;if(n.V||n.DV){var e=[],r=n._V||n.DV,a=Bo(n,r),l=n.scope.internal.getFont(n.fontName,n.fontStyle).id;e.push("/Tx BMC"),e.push("q"),e.push("BT"),e.push(n.scope.__private__.encodeColorString(n.color)),e.push("/"+l+" "+Kt(a.fontSize)+" Tf"),e.push("1 0 0 1 0 0 Tm"),e.push(a.text),e.push("ET"),e.push("Q"),e.push("EMC");var s=en(n);return s.scope=n.scope,s.stream=e.join(`
`),s}},Bo=function(n,e){var r=n.fontSize===0?n.maxFontSize:n.fontSize,a={text:"",fontSize:""},l=(e=(e=e.substr(0,1)=="("?e.substr(1):e).substr(e.length-1)==")"?e.substr(0,e.length-1):e).split(" ");l=n.multiline?l.map(function(L){return L.split(`
`)}):l.map(function(L){return[L]});var s=r,c=Bt.internal.getHeight(n)||0;c=c<0?-c:c;var h=Bt.internal.getWidth(n)||0;h=h<0?-h:h;var f=function(L,A,B){if(L+1<l.length){var D=A+" "+l[L+1][0];return ds(D,n,B).width<=h-4}return!1};s++;t:for(;s>0;){e="",s--;var g,b,w=ds("3",n,s).height,S=n.multiline?c-s:(c-w)/2,p=S+=2,M=0,I=0,q=0;if(s<=0){e=`(...) Tj
`,e+="% Width of Text: "+ds(e,n,s=12).width+", FieldWidth:"+h+`
`;break}for(var k="",E=0,J=0;J<l.length;J++)if(l.hasOwnProperty(J)){var ot=!1;if(l[J].length!==1&&q!==l[J].length-1){if((w+2)*(E+2)+2>c)continue t;k+=l[J][q],ot=!0,I=J,J--}else{k=(k+=l[J][q]+" ").substr(k.length-1)==" "?k.substr(0,k.length-1):k;var ut=parseInt(J),yt=f(ut,k,s),tt=J>=l.length-1;if(yt&&!tt){k+=" ",q=0;continue}if(yt||tt){if(tt)I=ut;else if(n.multiline&&(w+2)*(E+2)+2>c)continue t}else{if(!n.multiline||(w+2)*(E+2)+2>c)continue t;I=ut}}for(var U="",nt=M;nt<=I;nt++){var pt=l[nt];if(n.multiline){if(nt===I){U+=pt[q]+" ",q=(q+1)%pt.length;continue}if(nt===M){U+=pt[pt.length-1]+" ";continue}}U+=pt[0]+" "}switch(U=U.substr(U.length-1)==" "?U.substr(0,U.length-1):U,b=ds(U,n,s).width,n.textAlign){case"right":g=h-b-2;break;case"center":g=(h-b)/2;break;case"left":default:g=2}e+=Kt(g)+" "+Kt(p)+` Td
`,e+="("+ti(U)+`) Tj
`,e+=-Kt(g)+` 0 Td
`,p=-(s+2),b=0,M=ot?I:I+1,E++,k=""}break}return a.text=e,a.fontSize=s,a},ds=function(n,e,r){var a=e.scope.internal.getFont(e.fontName,e.fontStyle),l=e.scope.getStringUnitWidth(n,{font:a,fontSize:parseFloat(r),charSpace:0})*parseFloat(r);return{height:e.scope.getStringUnitWidth("3",{font:a,fontSize:parseFloat(r),charSpace:0})*parseFloat(r)*1.5,width:l}},Lc={fields:[],xForms:[],acroFormDictionaryRoot:null,printedOut:!1,internal:null,isInitialized:!1},Nc=function(n,e){var r={type:"reference",object:n};e.internal.getPageInfo(n.page).pageContext.annotations.find(function(a){return a.type===r.type&&a.object===r.object})===void 0&&e.internal.getPageInfo(n.page).pageContext.annotations.push(r)},Ac=function(n,e){for(var r in n)if(n.hasOwnProperty(r)){var a=r,l=n[r];e.internal.newObjectDeferredBegin(l.objId,!0),de(l)==="object"&&typeof l.putStream=="function"&&l.putStream(),delete n[a]}},_c=function(n,e){if(e.scope=n,n.internal!==void 0&&(n.internal.acroformPlugin===void 0||n.internal.acroformPlugin.isInitialized===!1)){if(Hr.FieldNum=0,n.internal.acroformPlugin=JSON.parse(JSON.stringify(Lc)),n.internal.acroformPlugin.acroFormDictionaryRoot)throw new Error("Exception while creating AcroformDictionary");Ro=n.internal.scaleFactor,n.internal.acroformPlugin.acroFormDictionaryRoot=new $l,n.internal.acroformPlugin.acroFormDictionaryRoot.scope=n,n.internal.acroformPlugin.acroFormDictionaryRoot._eventID=n.internal.events.subscribe("postPutResources",function(){(function(r){r.internal.events.unsubscribe(r.internal.acroformPlugin.acroFormDictionaryRoot._eventID),delete r.internal.acroformPlugin.acroFormDictionaryRoot._eventID,r.internal.acroformPlugin.printedOut=!0})(n)}),n.internal.events.subscribe("buildDocument",function(){(function(r){r.internal.acroformPlugin.acroFormDictionaryRoot.objId=void 0;var a=r.internal.acroformPlugin.acroFormDictionaryRoot.Fields;for(var l in a)if(a.hasOwnProperty(l)){var s=a[l];s.objId=void 0,s.hasAnnotation&&Nc(s,r)}})(n)}),n.internal.events.subscribe("putCatalog",function(){(function(r){if(r.internal.acroformPlugin.acroFormDictionaryRoot===void 0)throw new Error("putCatalogCallback: Root missing.");r.internal.write("/AcroForm "+r.internal.acroformPlugin.acroFormDictionaryRoot.objId+" 0 R")})(n)}),n.internal.events.subscribe("postPutPages",function(r){(function(a,l){var s=!a;for(var c in a||(l.internal.newObjectDeferredBegin(l.internal.acroformPlugin.acroFormDictionaryRoot.objId,!0),l.internal.acroformPlugin.acroFormDictionaryRoot.putStream()),a=a||l.internal.acroformPlugin.acroFormDictionaryRoot.Kids)if(a.hasOwnProperty(c)){var h=a[c],f=[],g=h.Rect;if(h.Rect&&(h.Rect=wc(h.Rect,l)),l.internal.newObjectDeferredBegin(h.objId,!0),h.DA=Bt.createDefaultAppearanceStream(h),de(h)==="object"&&typeof h.getKeyValueListForStream=="function"&&(f=h.getKeyValueListForStream()),h.Rect=g,h.hasAppearanceStream&&!h.appearanceStreamContent){var b=xc(h);f.push({key:"AP",value:"<</N "+b+">>"}),l.internal.acroformPlugin.xForms.push(b)}if(h.appearanceStreamContent){var w="";for(var S in h.appearanceStreamContent)if(h.appearanceStreamContent.hasOwnProperty(S)){var p=h.appearanceStreamContent[S];if(w+="/"+S+" ",w+="<<",Object.keys(p).length>=1||Array.isArray(p)){for(var c in p)if(p.hasOwnProperty(c)){var M=p[c];typeof M=="function"&&(M=M.call(l,h)),w+="/"+c+" "+M+" ",l.internal.acroformPlugin.xForms.indexOf(M)>=0||l.internal.acroformPlugin.xForms.push(M)}}else typeof(M=p)=="function"&&(M=M.call(l,h)),w+="/"+c+" "+M,l.internal.acroformPlugin.xForms.indexOf(M)>=0||l.internal.acroformPlugin.xForms.push(M);w+=">>"}f.push({key:"AP",value:`<<
`+w+">>"})}l.internal.putStream({additionalKeyValues:f,objectId:h.objId}),l.internal.out("endobj")}s&&Ac(l.internal.acroformPlugin.xForms,l)})(r,n)}),n.internal.acroformPlugin.isInitialized=!0}},Zl=ke.__acroform__.arrayToPdfArray=function(n,e,r){var a=function(c){return c};if(Array.isArray(n)){for(var l="[",s=0;s<n.length;s++)switch(s!==0&&(l+=" "),de(n[s])){case"boolean":case"number":case"object":l+=n[s].toString();break;case"string":n[s].substr(0,1)!=="/"?(e!==void 0&&r&&(a=r.internal.getEncryptor(e)),l+="("+ti(a(n[s].toString()))+")"):l+=n[s].toString()}return l+="]"}throw new Error("Invalid argument passed to jsPDF.__acroform__.arrayToPdfArray")},No=function(n,e,r){var a=function(l){return l};return e!==void 0&&r&&(a=r.internal.getEncryptor(e)),(n=n||"").toString(),n="("+ti(a(n))+")"},rn=function(){this._objId=void 0,this._scope=void 0,Object.defineProperty(this,"objId",{get:function(){if(this._objId===void 0){if(this.scope===void 0)return;this._objId=this.scope.internal.newObjectDeferred()}return this._objId},set:function(n){this._objId=n}}),Object.defineProperty(this,"scope",{value:this._scope,writable:!0})};rn.prototype.toString=function(){return this.objId+" 0 R"},rn.prototype.putStream=function(){var n=this.getKeyValueListForStream();this.scope.internal.putStream({data:this.stream,additionalKeyValues:n,objectId:this.objId}),this.scope.internal.out("endobj")},rn.prototype.getKeyValueListForStream=function(){var n=[],e=Object.getOwnPropertyNames(this).filter(function(s){return s!="content"&&s!="appearanceStreamContent"&&s!="scope"&&s!="objId"&&s.substring(0,1)!="_"});for(var r in e)if(Object.getOwnPropertyDescriptor(this,e[r]).configurable===!1){var a=e[r],l=this[a];l&&(Array.isArray(l)?n.push({key:a,value:Zl(l,this.objId,this.scope)}):l instanceof rn?(l.scope=this.scope,n.push({key:a,value:l.objId+" 0 R"})):typeof l!="function"&&n.push({key:a,value:l}))}return n};var Ql=function(){rn.call(this),Object.defineProperty(this,"Type",{value:"/XObject",configurable:!1,writable:!0}),Object.defineProperty(this,"Subtype",{value:"/Form",configurable:!1,writable:!0}),Object.defineProperty(this,"FormType",{value:1,configurable:!1,writable:!0});var n,e=[];Object.defineProperty(this,"BBox",{configurable:!1,get:function(){return e},set:function(r){e=r}}),Object.defineProperty(this,"Resources",{value:"2 0 R",configurable:!1,writable:!0}),Object.defineProperty(this,"stream",{enumerable:!1,configurable:!0,set:function(r){n=r.trim()},get:function(){return n||null}})};fr(Ql,rn);var $l=function(){rn.call(this);var n,e=[];Object.defineProperty(this,"Kids",{enumerable:!1,configurable:!0,get:function(){return e.length>0?e:void 0}}),Object.defineProperty(this,"Fields",{enumerable:!1,configurable:!1,get:function(){return e}}),Object.defineProperty(this,"DA",{enumerable:!1,configurable:!1,get:function(){if(n){var r=function(a){return a};return this.scope&&(r=this.scope.internal.getEncryptor(this.objId)),"("+ti(r(n))+")"}},set:function(r){n=r}})};fr($l,rn);var Hr=function n(){rn.call(this);var e=4;Object.defineProperty(this,"F",{enumerable:!1,configurable:!1,get:function(){return e},set:function(k){if(isNaN(k))throw new Error('Invalid value "'+k+'" for attribute F supplied.');e=k}}),Object.defineProperty(this,"showWhenPrinted",{enumerable:!0,configurable:!0,get:function(){return!!je(e,3)},set:function(k){k?this.F=Oe(e,3):this.F=Me(e,3)}});var r=0;Object.defineProperty(this,"Ff",{enumerable:!1,configurable:!1,get:function(){return r},set:function(k){if(isNaN(k))throw new Error('Invalid value "'+k+'" for attribute Ff supplied.');r=k}});var a=[];Object.defineProperty(this,"Rect",{enumerable:!1,configurable:!1,get:function(){if(a.length!==0)return a},set:function(k){a=k!==void 0?k:[]}}),Object.defineProperty(this,"x",{enumerable:!0,configurable:!0,get:function(){return!a||isNaN(a[0])?0:a[0]},set:function(k){a[0]=k}}),Object.defineProperty(this,"y",{enumerable:!0,configurable:!0,get:function(){return!a||isNaN(a[1])?0:a[1]},set:function(k){a[1]=k}}),Object.defineProperty(this,"width",{enumerable:!0,configurable:!0,get:function(){return!a||isNaN(a[2])?0:a[2]},set:function(k){a[2]=k}}),Object.defineProperty(this,"height",{enumerable:!0,configurable:!0,get:function(){return!a||isNaN(a[3])?0:a[3]},set:function(k){a[3]=k}});var l="";Object.defineProperty(this,"FT",{enumerable:!0,configurable:!1,get:function(){return l},set:function(k){switch(k){case"/Btn":case"/Tx":case"/Ch":case"/Sig":l=k;break;default:throw new Error('Invalid value "'+k+'" for attribute FT supplied.')}}});var s=null;Object.defineProperty(this,"T",{enumerable:!0,configurable:!1,get:function(){if(!s||s.length<1){if(this instanceof xs)return;s="FieldObject"+n.FieldNum++}var k=function(E){return E};return this.scope&&(k=this.scope.internal.getEncryptor(this.objId)),"("+ti(k(s))+")"},set:function(k){s=k.toString()}}),Object.defineProperty(this,"fieldName",{configurable:!0,enumerable:!0,get:function(){return s},set:function(k){s=k}});var c="helvetica";Object.defineProperty(this,"fontName",{enumerable:!0,configurable:!0,get:function(){return c},set:function(k){c=k}});var h="normal";Object.defineProperty(this,"fontStyle",{enumerable:!0,configurable:!0,get:function(){return h},set:function(k){h=k}});var f=0;Object.defineProperty(this,"fontSize",{enumerable:!0,configurable:!0,get:function(){return f},set:function(k){f=k}});var g=void 0;Object.defineProperty(this,"maxFontSize",{enumerable:!0,configurable:!0,get:function(){return g===void 0?50/Ro:g},set:function(k){g=k}});var b="black";Object.defineProperty(this,"color",{enumerable:!0,configurable:!0,get:function(){return b},set:function(k){b=k}});var w="/F1 0 Tf 0 g";Object.defineProperty(this,"DA",{enumerable:!0,configurable:!1,get:function(){if(!(!w||this instanceof xs||this instanceof $n))return No(w,this.objId,this.scope)},set:function(k){k=k.toString(),w=k}});var S=null;Object.defineProperty(this,"DV",{enumerable:!1,configurable:!1,get:function(){if(S)return this instanceof We?S:No(S,this.objId,this.scope)},set:function(k){k=k.toString(),S=this instanceof We?k:k.substr(0,1)==="("?Ci(k.substr(1,k.length-2)):Ci(k)}}),Object.defineProperty(this,"defaultValue",{enumerable:!0,configurable:!0,get:function(){return this instanceof We?Ci(S.substr(1,S.length-1)):S},set:function(k){k=k.toString(),S=this instanceof We?"/"+k:k}});var p=null;Object.defineProperty(this,"_V",{enumerable:!1,configurable:!1,get:function(){if(p)return p},set:function(k){this.V=k}}),Object.defineProperty(this,"V",{enumerable:!1,configurable:!1,get:function(){if(p)return this instanceof We?p:No(p,this.objId,this.scope)},set:function(k){k=k.toString(),p=this instanceof We?k:k.substr(0,1)==="("?Ci(k.substr(1,k.length-2)):Ci(k)}}),Object.defineProperty(this,"value",{enumerable:!0,configurable:!0,get:function(){return this instanceof We?Ci(p.substr(1,p.length-1)):p},set:function(k){k=k.toString(),p=this instanceof We?"/"+k:k}}),Object.defineProperty(this,"hasAnnotation",{enumerable:!0,configurable:!0,get:function(){return this.Rect}}),Object.defineProperty(this,"Type",{enumerable:!0,configurable:!1,get:function(){return this.hasAnnotation?"/Annot":null}}),Object.defineProperty(this,"Subtype",{enumerable:!0,configurable:!1,get:function(){return this.hasAnnotation?"/Widget":null}});var M,I=!1;Object.defineProperty(this,"hasAppearanceStream",{enumerable:!0,configurable:!0,get:function(){return I},set:function(k){k=!!k,I=k}}),Object.defineProperty(this,"page",{enumerable:!0,configurable:!0,get:function(){if(M)return M},set:function(k){M=k}}),Object.defineProperty(this,"readOnly",{enumerable:!0,configurable:!0,get:function(){return!!je(this.Ff,1)},set:function(k){k?this.Ff=Oe(this.Ff,1):this.Ff=Me(this.Ff,1)}}),Object.defineProperty(this,"required",{enumerable:!0,configurable:!0,get:function(){return!!je(this.Ff,2)},set:function(k){k?this.Ff=Oe(this.Ff,2):this.Ff=Me(this.Ff,2)}}),Object.defineProperty(this,"noExport",{enumerable:!0,configurable:!0,get:function(){return!!je(this.Ff,3)},set:function(k){k?this.Ff=Oe(this.Ff,3):this.Ff=Me(this.Ff,3)}});var q=null;Object.defineProperty(this,"Q",{enumerable:!0,configurable:!1,get:function(){if(q!==null)return q},set:function(k){if([0,1,2].indexOf(k)===-1)throw new Error('Invalid value "'+k+'" for attribute Q supplied.');q=k}}),Object.defineProperty(this,"textAlign",{get:function(){var k;switch(q){case 0:default:k="left";break;case 1:k="center";break;case 2:k="right"}return k},configurable:!0,enumerable:!0,set:function(k){switch(k){case"right":case 2:q=2;break;case"center":case 1:q=1;break;case"left":case 0:default:q=0}}})};fr(Hr,rn);var Ei=function(){Hr.call(this),this.FT="/Ch",this.V="()",this.fontName="zapfdingbats";var n=0;Object.defineProperty(this,"TI",{enumerable:!0,configurable:!1,get:function(){return n},set:function(r){n=r}}),Object.defineProperty(this,"topIndex",{enumerable:!0,configurable:!0,get:function(){return n},set:function(r){n=r}});var e=[];Object.defineProperty(this,"Opt",{enumerable:!0,configurable:!1,get:function(){return Zl(e,this.objId,this.scope)},set:function(r){var a,l;l=[],typeof(a=r)=="string"&&(l=function(s,c,h){h||(h=1);for(var f,g=[];f=c.exec(s);)g.push(f[h]);return g}(a,/\((.*?)\)/g)),e=l}}),this.getOptions=function(){return e},this.setOptions=function(r){e=r,this.sort&&e.sort()},this.addOption=function(r){r=(r=r||"").toString(),e.push(r),this.sort&&e.sort()},this.removeOption=function(r,a){for(a=a||!1,r=(r=r||"").toString();e.indexOf(r)!==-1&&(e.splice(e.indexOf(r),1),a!==!1););},Object.defineProperty(this,"combo",{enumerable:!0,configurable:!0,get:function(){return!!je(this.Ff,18)},set:function(r){r?this.Ff=Oe(this.Ff,18):this.Ff=Me(this.Ff,18)}}),Object.defineProperty(this,"edit",{enumerable:!0,configurable:!0,get:function(){return!!je(this.Ff,19)},set:function(r){this.combo===!0&&(r?this.Ff=Oe(this.Ff,19):this.Ff=Me(this.Ff,19))}}),Object.defineProperty(this,"sort",{enumerable:!0,configurable:!0,get:function(){return!!je(this.Ff,20)},set:function(r){r?(this.Ff=Oe(this.Ff,20),e.sort()):this.Ff=Me(this.Ff,20)}}),Object.defineProperty(this,"multiSelect",{enumerable:!0,configurable:!0,get:function(){return!!je(this.Ff,22)},set:function(r){r?this.Ff=Oe(this.Ff,22):this.Ff=Me(this.Ff,22)}}),Object.defineProperty(this,"doNotSpellCheck",{enumerable:!0,configurable:!0,get:function(){return!!je(this.Ff,23)},set:function(r){r?this.Ff=Oe(this.Ff,23):this.Ff=Me(this.Ff,23)}}),Object.defineProperty(this,"commitOnSelChange",{enumerable:!0,configurable:!0,get:function(){return!!je(this.Ff,27)},set:function(r){r?this.Ff=Oe(this.Ff,27):this.Ff=Me(this.Ff,27)}}),this.hasAppearanceStream=!1};fr(Ei,Hr);var Ti=function(){Ei.call(this),this.fontName="helvetica",this.combo=!1};fr(Ti,Ei);var qi=function(){Ti.call(this),this.combo=!0};fr(qi,Ti);var ms=function(){qi.call(this),this.edit=!0};fr(ms,qi);var We=function(){Hr.call(this),this.FT="/Btn",Object.defineProperty(this,"noToggleToOff",{enumerable:!0,configurable:!0,get:function(){return!!je(this.Ff,15)},set:function(r){r?this.Ff=Oe(this.Ff,15):this.Ff=Me(this.Ff,15)}}),Object.defineProperty(this,"radio",{enumerable:!0,configurable:!0,get:function(){return!!je(this.Ff,16)},set:function(r){r?this.Ff=Oe(this.Ff,16):this.Ff=Me(this.Ff,16)}}),Object.defineProperty(this,"pushButton",{enumerable:!0,configurable:!0,get:function(){return!!je(this.Ff,17)},set:function(r){r?this.Ff=Oe(this.Ff,17):this.Ff=Me(this.Ff,17)}}),Object.defineProperty(this,"radioIsUnison",{enumerable:!0,configurable:!0,get:function(){return!!je(this.Ff,26)},set:function(r){r?this.Ff=Oe(this.Ff,26):this.Ff=Me(this.Ff,26)}});var n,e={};Object.defineProperty(this,"MK",{enumerable:!1,configurable:!1,get:function(){var r=function(s){return s};if(this.scope&&(r=this.scope.internal.getEncryptor(this.objId)),Object.keys(e).length!==0){var a,l=[];for(a in l.push("<<"),e)l.push("/"+a+" ("+ti(r(e[a]))+")");return l.push(">>"),l.join(`
`)}},set:function(r){de(r)==="object"&&(e=r)}}),Object.defineProperty(this,"caption",{enumerable:!0,configurable:!0,get:function(){return e.CA||""},set:function(r){typeof r=="string"&&(e.CA=r)}}),Object.defineProperty(this,"AS",{enumerable:!1,configurable:!1,get:function(){return n},set:function(r){n=r}}),Object.defineProperty(this,"appearanceState",{enumerable:!0,configurable:!0,get:function(){return n.substr(1,n.length-1)},set:function(r){n="/"+r}})};fr(We,Hr);var vs=function(){We.call(this),this.pushButton=!0};fr(vs,We);var Di=function(){We.call(this),this.radio=!0,this.pushButton=!1;var n=[];Object.defineProperty(this,"Kids",{enumerable:!0,configurable:!1,get:function(){return n},set:function(e){n=e!==void 0?e:[]}})};fr(Di,We);var xs=function(){var n,e;Hr.call(this),Object.defineProperty(this,"Parent",{enumerable:!1,configurable:!1,get:function(){return n},set:function(l){n=l}}),Object.defineProperty(this,"optionName",{enumerable:!1,configurable:!0,get:function(){return e},set:function(l){e=l}});var r,a={};Object.defineProperty(this,"MK",{enumerable:!1,configurable:!1,get:function(){var l=function(h){return h};this.scope&&(l=this.scope.internal.getEncryptor(this.objId));var s,c=[];for(s in c.push("<<"),a)c.push("/"+s+" ("+ti(l(a[s]))+")");return c.push(">>"),c.join(`
`)},set:function(l){de(l)==="object"&&(a=l)}}),Object.defineProperty(this,"caption",{enumerable:!0,configurable:!0,get:function(){return a.CA||""},set:function(l){typeof l=="string"&&(a.CA=l)}}),Object.defineProperty(this,"AS",{enumerable:!1,configurable:!1,get:function(){return r},set:function(l){r=l}}),Object.defineProperty(this,"appearanceState",{enumerable:!0,configurable:!0,get:function(){return r.substr(1,r.length-1)},set:function(l){r="/"+l}}),this.caption="l",this.appearanceState="Off",this._AppearanceType=Bt.RadioButton.Circle,this.appearanceStreamContent=this._AppearanceType.createAppearanceStream(this.optionName)};fr(xs,Hr),Di.prototype.setAppearance=function(n){if(!("createAppearanceStream"in n)||!("getCA"in n))throw new Error("Couldn't assign Appearance to RadioButton. Appearance was Invalid!");for(var e in this.Kids)if(this.Kids.hasOwnProperty(e)){var r=this.Kids[e];r.appearanceStreamContent=n.createAppearanceStream(r.optionName),r.caption=n.getCA()}},Di.prototype.createOption=function(n){var e=new xs;return e.Parent=this,e.optionName=n,this.Kids.push(e),Sc.call(this.scope,e),e};var bs=function(){We.call(this),this.fontName="zapfdingbats",this.caption="3",this.appearanceState="On",this.value="On",this.textAlign="center",this.appearanceStreamContent=Bt.CheckBox.createAppearanceStream()};fr(bs,We);var $n=function(){Hr.call(this),this.FT="/Tx",Object.defineProperty(this,"multiline",{enumerable:!0,configurable:!0,get:function(){return!!je(this.Ff,13)},set:function(e){e?this.Ff=Oe(this.Ff,13):this.Ff=Me(this.Ff,13)}}),Object.defineProperty(this,"fileSelect",{enumerable:!0,configurable:!0,get:function(){return!!je(this.Ff,21)},set:function(e){e?this.Ff=Oe(this.Ff,21):this.Ff=Me(this.Ff,21)}}),Object.defineProperty(this,"doNotSpellCheck",{enumerable:!0,configurable:!0,get:function(){return!!je(this.Ff,23)},set:function(e){e?this.Ff=Oe(this.Ff,23):this.Ff=Me(this.Ff,23)}}),Object.defineProperty(this,"doNotScroll",{enumerable:!0,configurable:!0,get:function(){return!!je(this.Ff,24)},set:function(e){e?this.Ff=Oe(this.Ff,24):this.Ff=Me(this.Ff,24)}}),Object.defineProperty(this,"comb",{enumerable:!0,configurable:!0,get:function(){return!!je(this.Ff,25)},set:function(e){e?this.Ff=Oe(this.Ff,25):this.Ff=Me(this.Ff,25)}}),Object.defineProperty(this,"richText",{enumerable:!0,configurable:!0,get:function(){return!!je(this.Ff,26)},set:function(e){e?this.Ff=Oe(this.Ff,26):this.Ff=Me(this.Ff,26)}});var n=null;Object.defineProperty(this,"MaxLen",{enumerable:!0,configurable:!1,get:function(){return n},set:function(e){n=e}}),Object.defineProperty(this,"maxLength",{enumerable:!0,configurable:!0,get:function(){return n},set:function(e){Number.isInteger(e)&&(n=e)}}),Object.defineProperty(this,"hasAppearanceStream",{enumerable:!0,configurable:!0,get:function(){return this.V||this.DV}})};fr($n,Hr);var ys=function(){$n.call(this),Object.defineProperty(this,"password",{enumerable:!0,configurable:!0,get:function(){return!!je(this.Ff,14)},set:function(n){n?this.Ff=Oe(this.Ff,14):this.Ff=Me(this.Ff,14)}}),this.password=!0};fr(ys,$n);var Bt={CheckBox:{createAppearanceStream:function(){return{N:{On:Bt.CheckBox.YesNormal},D:{On:Bt.CheckBox.YesPushDown,Off:Bt.CheckBox.OffPushDown}}},YesPushDown:function(n){var e=en(n);e.scope=n.scope;var r=[],a=n.scope.internal.getFont(n.fontName,n.fontStyle).id,l=n.scope.__private__.encodeColorString(n.color),s=Bo(n,n.caption);return r.push("0.749023 g"),r.push("0 0 "+Kt(Bt.internal.getWidth(n))+" "+Kt(Bt.internal.getHeight(n))+" re"),r.push("f"),r.push("BMC"),r.push("q"),r.push("0 0 1 rg"),r.push("/"+a+" "+Kt(s.fontSize)+" Tf "+l),r.push("BT"),r.push(s.text),r.push("ET"),r.push("Q"),r.push("EMC"),e.stream=r.join(`
`),e},YesNormal:function(n){var e=en(n);e.scope=n.scope;var r=n.scope.internal.getFont(n.fontName,n.fontStyle).id,a=n.scope.__private__.encodeColorString(n.color),l=[],s=Bt.internal.getHeight(n),c=Bt.internal.getWidth(n),h=Bo(n,n.caption);return l.push("1 g"),l.push("0 0 "+Kt(c)+" "+Kt(s)+" re"),l.push("f"),l.push("q"),l.push("0 0 1 rg"),l.push("0 0 "+Kt(c-1)+" "+Kt(s-1)+" re"),l.push("W"),l.push("n"),l.push("0 g"),l.push("BT"),l.push("/"+r+" "+Kt(h.fontSize)+" Tf "+a),l.push(h.text),l.push("ET"),l.push("Q"),e.stream=l.join(`
`),e},OffPushDown:function(n){var e=en(n);e.scope=n.scope;var r=[];return r.push("0.749023 g"),r.push("0 0 "+Kt(Bt.internal.getWidth(n))+" "+Kt(Bt.internal.getHeight(n))+" re"),r.push("f"),e.stream=r.join(`
`),e}},RadioButton:{Circle:{createAppearanceStream:function(n){var e={D:{Off:Bt.RadioButton.Circle.OffPushDown},N:{}};return e.N[n]=Bt.RadioButton.Circle.YesNormal,e.D[n]=Bt.RadioButton.Circle.YesPushDown,e},getCA:function(){return"l"},YesNormal:function(n){var e=en(n);e.scope=n.scope;var r=[],a=Bt.internal.getWidth(n)<=Bt.internal.getHeight(n)?Bt.internal.getWidth(n)/4:Bt.internal.getHeight(n)/4;a=Number((.9*a).toFixed(5));var l=Bt.internal.Bezier_C,s=Number((a*l).toFixed(5));return r.push("q"),r.push("1 0 0 1 "+Mn(Bt.internal.getWidth(n)/2)+" "+Mn(Bt.internal.getHeight(n)/2)+" cm"),r.push(a+" 0 m"),r.push(a+" "+s+" "+s+" "+a+" 0 "+a+" c"),r.push("-"+s+" "+a+" -"+a+" "+s+" -"+a+" 0 c"),r.push("-"+a+" -"+s+" -"+s+" -"+a+" 0 -"+a+" c"),r.push(s+" -"+a+" "+a+" -"+s+" "+a+" 0 c"),r.push("f"),r.push("Q"),e.stream=r.join(`
`),e},YesPushDown:function(n){var e=en(n);e.scope=n.scope;var r=[],a=Bt.internal.getWidth(n)<=Bt.internal.getHeight(n)?Bt.internal.getWidth(n)/4:Bt.internal.getHeight(n)/4;a=Number((.9*a).toFixed(5));var l=Number((2*a).toFixed(5)),s=Number((l*Bt.internal.Bezier_C).toFixed(5)),c=Number((a*Bt.internal.Bezier_C).toFixed(5));return r.push("0.749023 g"),r.push("q"),r.push("1 0 0 1 "+Mn(Bt.internal.getWidth(n)/2)+" "+Mn(Bt.internal.getHeight(n)/2)+" cm"),r.push(l+" 0 m"),r.push(l+" "+s+" "+s+" "+l+" 0 "+l+" c"),r.push("-"+s+" "+l+" -"+l+" "+s+" -"+l+" 0 c"),r.push("-"+l+" -"+s+" -"+s+" -"+l+" 0 -"+l+" c"),r.push(s+" -"+l+" "+l+" -"+s+" "+l+" 0 c"),r.push("f"),r.push("Q"),r.push("0 g"),r.push("q"),r.push("1 0 0 1 "+Mn(Bt.internal.getWidth(n)/2)+" "+Mn(Bt.internal.getHeight(n)/2)+" cm"),r.push(a+" 0 m"),r.push(a+" "+c+" "+c+" "+a+" 0 "+a+" c"),r.push("-"+c+" "+a+" -"+a+" "+c+" -"+a+" 0 c"),r.push("-"+a+" -"+c+" -"+c+" -"+a+" 0 -"+a+" c"),r.push(c+" -"+a+" "+a+" -"+c+" "+a+" 0 c"),r.push("f"),r.push("Q"),e.stream=r.join(`
`),e},OffPushDown:function(n){var e=en(n);e.scope=n.scope;var r=[],a=Bt.internal.getWidth(n)<=Bt.internal.getHeight(n)?Bt.internal.getWidth(n)/4:Bt.internal.getHeight(n)/4;a=Number((.9*a).toFixed(5));var l=Number((2*a).toFixed(5)),s=Number((l*Bt.internal.Bezier_C).toFixed(5));return r.push("0.749023 g"),r.push("q"),r.push("1 0 0 1 "+Mn(Bt.internal.getWidth(n)/2)+" "+Mn(Bt.internal.getHeight(n)/2)+" cm"),r.push(l+" 0 m"),r.push(l+" "+s+" "+s+" "+l+" 0 "+l+" c"),r.push("-"+s+" "+l+" -"+l+" "+s+" -"+l+" 0 c"),r.push("-"+l+" -"+s+" -"+s+" -"+l+" 0 -"+l+" c"),r.push(s+" -"+l+" "+l+" -"+s+" "+l+" 0 c"),r.push("f"),r.push("Q"),e.stream=r.join(`
`),e}},Cross:{createAppearanceStream:function(n){var e={D:{Off:Bt.RadioButton.Cross.OffPushDown},N:{}};return e.N[n]=Bt.RadioButton.Cross.YesNormal,e.D[n]=Bt.RadioButton.Cross.YesPushDown,e},getCA:function(){return"8"},YesNormal:function(n){var e=en(n);e.scope=n.scope;var r=[],a=Bt.internal.calculateCross(n);return r.push("q"),r.push("1 1 "+Kt(Bt.internal.getWidth(n)-2)+" "+Kt(Bt.internal.getHeight(n)-2)+" re"),r.push("W"),r.push("n"),r.push(Kt(a.x1.x)+" "+Kt(a.x1.y)+" m"),r.push(Kt(a.x2.x)+" "+Kt(a.x2.y)+" l"),r.push(Kt(a.x4.x)+" "+Kt(a.x4.y)+" m"),r.push(Kt(a.x3.x)+" "+Kt(a.x3.y)+" l"),r.push("s"),r.push("Q"),e.stream=r.join(`
`),e},YesPushDown:function(n){var e=en(n);e.scope=n.scope;var r=Bt.internal.calculateCross(n),a=[];return a.push("0.749023 g"),a.push("0 0 "+Kt(Bt.internal.getWidth(n))+" "+Kt(Bt.internal.getHeight(n))+" re"),a.push("f"),a.push("q"),a.push("1 1 "+Kt(Bt.internal.getWidth(n)-2)+" "+Kt(Bt.internal.getHeight(n)-2)+" re"),a.push("W"),a.push("n"),a.push(Kt(r.x1.x)+" "+Kt(r.x1.y)+" m"),a.push(Kt(r.x2.x)+" "+Kt(r.x2.y)+" l"),a.push(Kt(r.x4.x)+" "+Kt(r.x4.y)+" m"),a.push(Kt(r.x3.x)+" "+Kt(r.x3.y)+" l"),a.push("s"),a.push("Q"),e.stream=a.join(`
`),e},OffPushDown:function(n){var e=en(n);e.scope=n.scope;var r=[];return r.push("0.749023 g"),r.push("0 0 "+Kt(Bt.internal.getWidth(n))+" "+Kt(Bt.internal.getHeight(n))+" re"),r.push("f"),e.stream=r.join(`
`),e}}},createDefaultAppearanceStream:function(n){var e=n.scope.internal.getFont(n.fontName,n.fontStyle).id,r=n.scope.__private__.encodeColorString(n.color);return"/"+e+" "+n.fontSize+" Tf "+r}};Bt.internal={Bezier_C:.551915024494,calculateCross:function(n){var e=Bt.internal.getWidth(n),r=Bt.internal.getHeight(n),a=Math.min(e,r);return{x1:{x:(e-a)/2,y:(r-a)/2+a},x2:{x:(e-a)/2+a,y:(r-a)/2},x3:{x:(e-a)/2,y:(r-a)/2},x4:{x:(e-a)/2+a,y:(r-a)/2+a}}}},Bt.internal.getWidth=function(n){var e=0;return de(n)==="object"&&(e=Sl(n.Rect[2])),e},Bt.internal.getHeight=function(n){var e=0;return de(n)==="object"&&(e=Sl(n.Rect[3])),e};var Sc=ke.addField=function(n){if(_c(this,n),!(n instanceof Hr))throw new Error("Invalid argument passed to jsPDF.addField.");var e;return(e=n).scope.internal.acroformPlugin.printedOut&&(e.scope.internal.acroformPlugin.printedOut=!1,e.scope.internal.acroformPlugin.acroFormDictionaryRoot=null),e.scope.internal.acroformPlugin.acroFormDictionaryRoot.Fields.push(e),n.page=n.scope.internal.getCurrentPageInfo().pageNumber,this};ke.AcroFormChoiceField=Ei,ke.AcroFormListBox=Ti,ke.AcroFormComboBox=qi,ke.AcroFormEditBox=ms,ke.AcroFormButton=We,ke.AcroFormPushButton=vs,ke.AcroFormRadioButton=Di,ke.AcroFormCheckBox=bs,ke.AcroFormTextField=$n,ke.AcroFormPasswordField=ys,ke.AcroFormAppearance=Bt,ke.AcroForm={ChoiceField:Ei,ListBox:Ti,ComboBox:qi,EditBox:ms,Button:We,PushButton:vs,RadioButton:Di,CheckBox:bs,TextField:$n,PasswordField:ys,Appearance:Bt},zt.AcroForm={ChoiceField:Ei,ListBox:Ti,ComboBox:qi,EditBox:ms,Button:We,PushButton:vs,RadioButton:Di,CheckBox:bs,TextField:$n,PasswordField:ys,Appearance:Bt};zt.AcroForm;function tu(n){return n.reduce(function(e,r,a){return e[r]=a,e},{})}(function(n){n.__addimage__={};var e="UNKNOWN",r={PNG:[[137,80,78,71]],TIFF:[[77,77,0,42],[73,73,42,0]],JPEG:[[255,216,255,224,void 0,void 0,74,70,73,70,0],[255,216,255,225,void 0,void 0,69,120,105,102,0,0],[255,216,255,219],[255,216,255,238]],JPEG2000:[[0,0,0,12,106,80,32,32]],GIF87a:[[71,73,70,56,55,97]],GIF89a:[[71,73,70,56,57,97]],WEBP:[[82,73,70,70,void 0,void 0,void 0,void 0,87,69,66,80]],BMP:[[66,77],[66,65],[67,73],[67,80],[73,67],[80,84]]},a=n.__addimage__.getImageFileTypeByImageData=function(L,A){var B,D,et,rt,ct,X=e;if((A=A||e)==="RGBA"||L.data!==void 0&&L.data instanceof Uint8ClampedArray&&"height"in L&&"width"in L)return"RGBA";if(yt(L))for(ct in r)for(et=r[ct],B=0;B<et.length;B+=1){for(rt=!0,D=0;D<et[B].length;D+=1)if(et[B][D]!==void 0&&et[B][D]!==L[D]){rt=!1;break}if(rt===!0){X=ct;break}}else for(ct in r)for(et=r[ct],B=0;B<et.length;B+=1){for(rt=!0,D=0;D<et[B].length;D+=1)if(et[B][D]!==void 0&&et[B][D]!==L.charCodeAt(D)){rt=!1;break}if(rt===!0){X=ct;break}}return X===e&&A!==e&&(X=A),X},l=function L(A){for(var B=this.internal.write,D=this.internal.putStream,et=(0,this.internal.getFilters)();et.indexOf("FlateEncode")!==-1;)et.splice(et.indexOf("FlateEncode"),1);A.objectId=this.internal.newObject();var rt=[];if(rt.push({key:"Type",value:"/XObject"}),rt.push({key:"Subtype",value:"/Image"}),rt.push({key:"Width",value:A.width}),rt.push({key:"Height",value:A.height}),A.colorSpace===q.INDEXED?rt.push({key:"ColorSpace",value:"[/Indexed /DeviceRGB "+(A.palette.length/3-1)+" "+("sMask"in A&&A.sMask!==void 0?A.objectId+2:A.objectId+1)+" 0 R]"}):(rt.push({key:"ColorSpace",value:"/"+A.colorSpace}),A.colorSpace===q.DEVICE_CMYK&&rt.push({key:"Decode",value:"[1 0 1 0 1 0 1 0]"})),rt.push({key:"BitsPerComponent",value:A.bitsPerComponent}),"decodeParameters"in A&&A.decodeParameters!==void 0&&rt.push({key:"DecodeParms",value:"<<"+A.decodeParameters+">>"}),"transparency"in A&&Array.isArray(A.transparency)){for(var ct="",X=0,at=A.transparency.length;X<at;X++)ct+=A.transparency[X]+" "+A.transparency[X]+" ";rt.push({key:"Mask",value:"["+ct+"]"})}A.sMask!==void 0&&rt.push({key:"SMask",value:A.objectId+1+" 0 R"});var ft=A.filter!==void 0?["/"+A.filter]:void 0;if(D({data:A.data,additionalKeyValues:rt,alreadyAppliedFilters:ft,objectId:A.objectId}),B("endobj"),"sMask"in A&&A.sMask!==void 0){var Ft="/Predictor "+A.predictor+" /Colors 1 /BitsPerComponent "+A.bitsPerComponent+" /Columns "+A.width,N={width:A.width,height:A.height,colorSpace:"DeviceGray",bitsPerComponent:A.bitsPerComponent,decodeParameters:Ft,data:A.sMask};"filter"in A&&(N.filter=A.filter),L.call(this,N)}if(A.colorSpace===q.INDEXED){var C=this.internal.newObject();D({data:U(new Uint8Array(A.palette)),objectId:C}),B("endobj")}},s=function(){var L=this.internal.collections.addImage_images;for(var A in L)l.call(this,L[A])},c=function(){var L,A=this.internal.collections.addImage_images,B=this.internal.write;for(var D in A)B("/I"+(L=A[D]).index,L.objectId,"0","R")},h=function(){this.internal.collections.addImage_images||(this.internal.collections.addImage_images={},this.internal.events.subscribe("putResources",s),this.internal.events.subscribe("putXobjectDict",c))},f=function(){var L=this.internal.collections.addImage_images;return h.call(this),L},g=function(){return Object.keys(this.internal.collections.addImage_images).length},b=function(L){return typeof n["process"+L.toUpperCase()]=="function"},w=function(L){return de(L)==="object"&&L.nodeType===1},S=function(L,A){if(L.nodeName==="IMG"&&L.hasAttribute("src")){var B=""+L.getAttribute("src");if(B.indexOf("data:image/")===0)return ba(unescape(B).split("base64,").pop());var D=n.loadFile(B,!0);if(D!==void 0)return D}if(L.nodeName==="CANVAS"){if(L.width===0||L.height===0)throw new Error("Given canvas must have data. Canvas width: "+L.width+", height: "+L.height);var et;switch(A){case"PNG":et="image/png";break;case"WEBP":et="image/webp";break;case"JPEG":case"JPG":default:et="image/jpeg"}return ba(L.toDataURL(et,1).split("base64,").pop())}},p=function(L){var A=this.internal.collections.addImage_images;if(A){for(var B in A)if(L===A[B].alias)return A[B]}},M=function(L,A,B){return L||A||(L=-96,A=-96),L<0&&(L=-1*B.width*72/L/this.internal.scaleFactor),A<0&&(A=-1*B.height*72/A/this.internal.scaleFactor),L===0&&(L=A*B.width/B.height),A===0&&(A=L*B.height/B.width),[L,A]},I=function(L,A,B,D,et,rt){var ct=M.call(this,B,D,et),X=this.internal.getCoordinateString,at=this.internal.getVerticalCoordinateString,ft=f.call(this);if(B=ct[0],D=ct[1],ft[et.index]=et,rt){rt*=Math.PI/180;var Ft=Math.cos(rt),N=Math.sin(rt),C=function(R){return R.toFixed(4)},O=[C(Ft),C(N),C(-1*N),C(Ft),0,0,"cm"]}this.internal.write("q"),rt?(this.internal.write([1,"0","0",1,X(L),at(A+D),"cm"].join(" ")),this.internal.write(O.join(" ")),this.internal.write([X(B),"0","0",X(D),"0","0","cm"].join(" "))):this.internal.write([X(B),"0","0",X(D),X(L),at(A+D),"cm"].join(" ")),this.isAdvancedAPI()&&this.internal.write([1,0,0,-1,0,0,"cm"].join(" ")),this.internal.write("/I"+et.index+" Do"),this.internal.write("Q")},q=n.color_spaces={DEVICE_RGB:"DeviceRGB",DEVICE_GRAY:"DeviceGray",DEVICE_CMYK:"DeviceCMYK",CAL_GREY:"CalGray",CAL_RGB:"CalRGB",LAB:"Lab",ICC_BASED:"ICCBased",INDEXED:"Indexed",PATTERN:"Pattern",SEPARATION:"Separation",DEVICE_N:"DeviceN"};n.decode={DCT_DECODE:"DCTDecode",FLATE_DECODE:"FlateDecode",LZW_DECODE:"LZWDecode",JPX_DECODE:"JPXDecode",JBIG2_DECODE:"JBIG2Decode",ASCII85_DECODE:"ASCII85Decode",ASCII_HEX_DECODE:"ASCIIHexDecode",RUN_LENGTH_DECODE:"RunLengthDecode",CCITT_FAX_DECODE:"CCITTFaxDecode"};var k=n.image_compression={NONE:"NONE",FAST:"FAST",MEDIUM:"MEDIUM",SLOW:"SLOW"},E=n.__addimage__.sHashCode=function(L){var A,B,D=0;if(typeof L=="string")for(B=L.length,A=0;A<B;A++)D=(D<<5)-D+L.charCodeAt(A),D|=0;else if(yt(L))for(B=L.byteLength/2,A=0;A<B;A++)D=(D<<5)-D+L[A],D|=0;return D},J=n.__addimage__.validateStringAsBase64=function(L){(L=L||"").toString().trim();var A=!0;return L.length===0&&(A=!1),L.length%4!=0&&(A=!1),/^[A-Za-z0-9+/]+$/.test(L.substr(0,L.length-2))===!1&&(A=!1),/^[A-Za-z0-9/][A-Za-z0-9+/]|[A-Za-z0-9+/]=|==$/.test(L.substr(-2))===!1&&(A=!1),A},ot=n.__addimage__.extractImageFromDataUrl=function(L){if(L==null||!(L=L.trim()).startsWith("data:"))return null;var A=L.indexOf(",");return A<0?null:L.substring(0,A).trim().endsWith("base64")?L.substring(A+1):null},ut=n.__addimage__.supportsArrayBuffer=function(){return typeof ArrayBuffer<"u"&&typeof Uint8Array<"u"};n.__addimage__.isArrayBuffer=function(L){return ut()&&L instanceof ArrayBuffer};var yt=n.__addimage__.isArrayBufferView=function(L){return ut()&&typeof Uint32Array<"u"&&(L instanceof Int8Array||L instanceof Uint8Array||typeof Uint8ClampedArray<"u"&&L instanceof Uint8ClampedArray||L instanceof Int16Array||L instanceof Uint16Array||L instanceof Int32Array||L instanceof Uint32Array||L instanceof Float32Array||L instanceof Float64Array)},tt=n.__addimage__.binaryStringToUint8Array=function(L){for(var A=L.length,B=new Uint8Array(A),D=0;D<A;D++)B[D]=L.charCodeAt(D);return B},U=n.__addimage__.arrayBufferToBinaryString=function(L){for(var A="",B=yt(L)?L:new Uint8Array(L),D=0;D<B.length;D+=8192)A+=String.fromCharCode.apply(null,B.subarray(D,D+8192));return A};n.addImage=function(){var L,A,B,D,et,rt,ct,X,at;if(typeof arguments[1]=="number"?(A=e,B=arguments[1],D=arguments[2],et=arguments[3],rt=arguments[4],ct=arguments[5],X=arguments[6],at=arguments[7]):(A=arguments[1],B=arguments[2],D=arguments[3],et=arguments[4],rt=arguments[5],ct=arguments[6],X=arguments[7],at=arguments[8]),de(L=arguments[0])==="object"&&!w(L)&&"imageData"in L){var ft=L;L=ft.imageData,A=ft.format||A||e,B=ft.x||B||0,D=ft.y||D||0,et=ft.w||ft.width||et,rt=ft.h||ft.height||rt,ct=ft.alias||ct,X=ft.compression||X,at=ft.rotation||ft.angle||at}var Ft=this.internal.getFilters();if(X===void 0&&Ft.indexOf("FlateEncode")!==-1&&(X="SLOW"),isNaN(B)||isNaN(D))throw new Error("Invalid coordinates passed to jsPDF.addImage");h.call(this);var N=nt.call(this,L,A,ct,X);return I.call(this,B,D,et,rt,N,at),this};var nt=function(L,A,B,D){var et,rt,ct;if(typeof L=="string"&&a(L)===e){L=unescape(L);var X=pt(L,!1);(X!==""||(X=n.loadFile(L,!0))!==void 0)&&(L=X)}if(w(L)&&(L=S(L,A)),A=a(L,A),!b(A))throw new Error("addImage does not support files of type '"+A+"', please ensure that a plugin for '"+A+"' support is added.");if(((ct=B)==null||ct.length===0)&&(B=function(at){return typeof at=="string"||yt(at)?E(at):yt(at.data)?E(at.data):null}(L)),(et=p.call(this,B))||(ut()&&(L instanceof Uint8Array||A==="RGBA"||(rt=L,L=tt(L))),et=this["process"+A.toUpperCase()](L,g.call(this),B,function(at){return at&&typeof at=="string"&&(at=at.toUpperCase()),at in n.image_compression?at:k.NONE}(D),rt)),!et)throw new Error("An unknown error occurred whilst processing the image.");return et},pt=n.__addimage__.convertBase64ToBinaryString=function(L,A){A=typeof A!="boolean"||A;var B,D="";if(typeof L=="string"){var et;B=(et=ot(L))!==null&&et!==void 0?et:L;try{D=ba(B)}catch(rt){if(A)throw J(B)?new Error("atob-Error in jsPDF.convertBase64ToBinaryString "+rt.message):new Error("Supplied Data is not a valid base64-String jsPDF.convertBase64ToBinaryString ")}}return D};n.getImageProperties=function(L){var A,B,D="";if(w(L)&&(L=S(L)),typeof L=="string"&&a(L)===e&&((D=pt(L,!1))===""&&(D=n.loadFile(L)||""),L=D),B=a(L),!b(B))throw new Error("addImage does not support files of type '"+B+"', please ensure that a plugin for '"+B+"' support is added.");if(!ut()||L instanceof Uint8Array||(L=tt(L)),!(A=this["process"+B.toUpperCase()](L)))throw new Error("An unknown error occurred whilst processing the image");return A.fileType=B,A}})(zt.API),function(n){var e=function(r){if(r!==void 0&&r!="")return!0};zt.API.events.push(["addPage",function(r){this.internal.getPageInfo(r.pageNumber).pageContext.annotations=[]}]),n.events.push(["putPage",function(r){for(var a,l,s,c=this.internal.getCoordinateString,h=this.internal.getVerticalCoordinateString,f=this.internal.getPageInfoByObjId(r.objId),g=r.pageContext.annotations,b=!1,w=0;w<g.length&&!b;w++)switch((a=g[w]).type){case"link":(e(a.options.url)||e(a.options.pageNumber))&&(b=!0);break;case"reference":case"text":case"freetext":b=!0}if(b!=0){this.internal.write("/Annots [");for(var S=0;S<g.length;S++){a=g[S];var p=this.internal.pdfEscape,M=this.internal.getEncryptor(r.objId);switch(a.type){case"reference":this.internal.write(" "+a.object.objId+" 0 R ");break;case"text":var I=this.internal.newAdditionalObject(),q=this.internal.newAdditionalObject(),k=this.internal.getEncryptor(I.objId),E=a.title||"Note";s="<</Type /Annot /Subtype /Text "+(l="/Rect ["+c(a.bounds.x)+" "+h(a.bounds.y+a.bounds.h)+" "+c(a.bounds.x+a.bounds.w)+" "+h(a.bounds.y)+"] ")+"/Contents ("+p(k(a.contents))+")",s+=" /Popup "+q.objId+" 0 R",s+=" /P "+f.objId+" 0 R",s+=" /T ("+p(k(E))+") >>",I.content=s;var J=I.objId+" 0 R";s="<</Type /Annot /Subtype /Popup "+(l="/Rect ["+c(a.bounds.x+30)+" "+h(a.bounds.y+a.bounds.h)+" "+c(a.bounds.x+a.bounds.w+30)+" "+h(a.bounds.y)+"] ")+" /Parent "+J,a.open&&(s+=" /Open true"),s+=" >>",q.content=s,this.internal.write(I.objId,"0 R",q.objId,"0 R");break;case"freetext":l="/Rect ["+c(a.bounds.x)+" "+h(a.bounds.y)+" "+c(a.bounds.x+a.bounds.w)+" "+h(a.bounds.y+a.bounds.h)+"] ";var ot=a.color||"#000000";s="<</Type /Annot /Subtype /FreeText "+l+"/Contents ("+p(M(a.contents))+")",s+=" /DS(font: Helvetica,sans-serif 12.0pt; text-align:left; color:#"+ot+")",s+=" /Border [0 0 0]",s+=" >>",this.internal.write(s);break;case"link":if(a.options.name){var ut=this.annotations._nameMap[a.options.name];a.options.pageNumber=ut.page,a.options.top=ut.y}else a.options.top||(a.options.top=0);if(l="/Rect ["+a.finalBounds.x+" "+a.finalBounds.y+" "+a.finalBounds.w+" "+a.finalBounds.h+"] ",s="",a.options.url)s="<</Type /Annot /Subtype /Link "+l+"/Border [0 0 0] /A <</S /URI /URI ("+p(M(a.options.url))+") >>";else if(a.options.pageNumber)switch(s="<</Type /Annot /Subtype /Link "+l+"/Border [0 0 0] /Dest ["+this.internal.getPageInfo(a.options.pageNumber).objId+" 0 R",a.options.magFactor=a.options.magFactor||"XYZ",a.options.magFactor){case"Fit":s+=" /Fit]";break;case"FitH":s+=" /FitH "+a.options.top+"]";break;case"FitV":a.options.left=a.options.left||0,s+=" /FitV "+a.options.left+"]";break;case"XYZ":default:var yt=h(a.options.top);a.options.left=a.options.left||0,a.options.zoom===void 0&&(a.options.zoom=0),s+=" /XYZ "+a.options.left+" "+yt+" "+a.options.zoom+"]"}s!=""&&(s+=" >>",this.internal.write(s))}}this.internal.write("]")}}]),n.createAnnotation=function(r){var a=this.internal.getCurrentPageInfo();switch(r.type){case"link":this.link(r.bounds.x,r.bounds.y,r.bounds.w,r.bounds.h,r);break;case"text":case"freetext":a.pageContext.annotations.push(r)}},n.link=function(r,a,l,s,c){var h=this.internal.getCurrentPageInfo(),f=this.internal.getCoordinateString,g=this.internal.getVerticalCoordinateString;h.pageContext.annotations.push({finalBounds:{x:f(r),y:g(a),w:f(r+l),h:g(a+s)},options:c,type:"link"})},n.textWithLink=function(r,a,l,s){var c,h,f=this.getTextWidth(r),g=this.internal.getLineHeight()/this.internal.scaleFactor;if(s.maxWidth!==void 0){h=s.maxWidth;var b=this.splitTextToSize(r,h).length;c=Math.ceil(g*b)}else h=f,c=g;return this.text(r,a,l,s),l+=.2*g,s.align==="center"&&(a-=f/2),s.align==="right"&&(a-=f),this.link(a,l-g,h,c,s),f},n.getTextWidth=function(r){var a=this.internal.getFontSize();return this.getStringUnitWidth(r)*a/this.internal.scaleFactor}}(zt.API),function(n){var e={1569:[65152],1570:[65153,65154],1571:[65155,65156],1572:[65157,65158],1573:[65159,65160],1574:[65161,65162,65163,65164],1575:[65165,65166],1576:[65167,65168,65169,65170],1577:[65171,65172],1578:[65173,65174,65175,65176],1579:[65177,65178,65179,65180],1580:[65181,65182,65183,65184],1581:[65185,65186,65187,65188],1582:[65189,65190,65191,65192],1583:[65193,65194],1584:[65195,65196],1585:[65197,65198],1586:[65199,65200],1587:[65201,65202,65203,65204],1588:[65205,65206,65207,65208],1589:[65209,65210,65211,65212],1590:[65213,65214,65215,65216],1591:[65217,65218,65219,65220],1592:[65221,65222,65223,65224],1593:[65225,65226,65227,65228],1594:[65229,65230,65231,65232],1601:[65233,65234,65235,65236],1602:[65237,65238,65239,65240],1603:[65241,65242,65243,65244],1604:[65245,65246,65247,65248],1605:[65249,65250,65251,65252],1606:[65253,65254,65255,65256],1607:[65257,65258,65259,65260],1608:[65261,65262],1609:[65263,65264,64488,64489],1610:[65265,65266,65267,65268],1649:[64336,64337],1655:[64477],1657:[64358,64359,64360,64361],1658:[64350,64351,64352,64353],1659:[64338,64339,64340,64341],1662:[64342,64343,64344,64345],1663:[64354,64355,64356,64357],1664:[64346,64347,64348,64349],1667:[64374,64375,64376,64377],1668:[64370,64371,64372,64373],1670:[64378,64379,64380,64381],1671:[64382,64383,64384,64385],1672:[64392,64393],1676:[64388,64389],1677:[64386,64387],1678:[64390,64391],1681:[64396,64397],1688:[64394,64395],1700:[64362,64363,64364,64365],1702:[64366,64367,64368,64369],1705:[64398,64399,64400,64401],1709:[64467,64468,64469,64470],1711:[64402,64403,64404,64405],1713:[64410,64411,64412,64413],1715:[64406,64407,64408,64409],1722:[64414,64415],1723:[64416,64417,64418,64419],1726:[64426,64427,64428,64429],1728:[64420,64421],1729:[64422,64423,64424,64425],1733:[64480,64481],1734:[64473,64474],1735:[64471,64472],1736:[64475,64476],1737:[64482,64483],1739:[64478,64479],1740:[64508,64509,64510,64511],1744:[64484,64485,64486,64487],1746:[64430,64431],1747:[64432,64433]},r={65247:{65154:65269,65156:65271,65160:65273,65166:65275},65248:{65154:65270,65156:65272,65160:65274,65166:65276},65165:{65247:{65248:{65258:65010}}},1617:{1612:64606,1613:64607,1614:64608,1615:64609,1616:64610}},a={1612:64606,1613:64607,1614:64608,1615:64609,1616:64610},l=[1570,1571,1573,1575];n.__arabicParser__={};var s=n.__arabicParser__.isInArabicSubstitutionA=function(I){return e[I.charCodeAt(0)]!==void 0},c=n.__arabicParser__.isArabicLetter=function(I){return typeof I=="string"&&/^[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]+$/.test(I)},h=n.__arabicParser__.isArabicEndLetter=function(I){return c(I)&&s(I)&&e[I.charCodeAt(0)].length<=2},f=n.__arabicParser__.isArabicAlfLetter=function(I){return c(I)&&l.indexOf(I.charCodeAt(0))>=0};n.__arabicParser__.arabicLetterHasIsolatedForm=function(I){return c(I)&&s(I)&&e[I.charCodeAt(0)].length>=1};var g=n.__arabicParser__.arabicLetterHasFinalForm=function(I){return c(I)&&s(I)&&e[I.charCodeAt(0)].length>=2};n.__arabicParser__.arabicLetterHasInitialForm=function(I){return c(I)&&s(I)&&e[I.charCodeAt(0)].length>=3};var b=n.__arabicParser__.arabicLetterHasMedialForm=function(I){return c(I)&&s(I)&&e[I.charCodeAt(0)].length==4},w=n.__arabicParser__.resolveLigatures=function(I){var q=0,k=r,E="",J=0;for(q=0;q<I.length;q+=1)k[I.charCodeAt(q)]!==void 0?(J++,typeof(k=k[I.charCodeAt(q)])=="number"&&(E+=String.fromCharCode(k),k=r,J=0),q===I.length-1&&(k=r,E+=I.charAt(q-(J-1)),q-=J-1,J=0)):(k=r,E+=I.charAt(q-J),q-=J,J=0);return E};n.__arabicParser__.isArabicDiacritic=function(I){return I!==void 0&&a[I.charCodeAt(0)]!==void 0};var S=n.__arabicParser__.getCorrectForm=function(I,q,k){return c(I)?s(I)===!1?-1:!g(I)||!c(q)&&!c(k)||!c(k)&&h(q)||h(I)&&!c(q)||h(I)&&f(q)||h(I)&&h(q)?0:b(I)&&c(q)&&!h(q)&&c(k)&&g(k)?3:h(I)||!c(k)?1:2:-1},p=function(I){var q=0,k=0,E=0,J="",ot="",ut="",yt=(I=I||"").split("\\s+"),tt=[];for(q=0;q<yt.length;q+=1){for(tt.push(""),k=0;k<yt[q].length;k+=1)J=yt[q][k],ot=yt[q][k-1],ut=yt[q][k+1],c(J)?(E=S(J,ot,ut),tt[q]+=E!==-1?String.fromCharCode(e[J.charCodeAt(0)][E]):J):tt[q]+=J;tt[q]=w(tt[q])}return tt.join(" ")},M=n.__arabicParser__.processArabic=n.processArabic=function(){var I,q=typeof arguments[0]=="string"?arguments[0]:arguments[0].text,k=[];if(Array.isArray(q)){var E=0;for(k=[],E=0;E<q.length;E+=1)Array.isArray(q[E])?k.push([p(q[E][0]),q[E][1],q[E][2]]):k.push([p(q[E])]);I=k}else I=p(q);return typeof arguments[0]=="string"?I:(arguments[0].text=I,arguments[0])};n.events.push(["preProcessText",M])}(zt.API),zt.API.autoPrint=function(n){var e;switch((n=n||{}).variant=n.variant||"non-conform",n.variant){case"javascript":this.addJS("print({});");break;case"non-conform":default:this.internal.events.subscribe("postPutResources",function(){e=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/S /Named"),this.internal.out("/Type /Action"),this.internal.out("/N /Print"),this.internal.out(">>"),this.internal.out("endobj")}),this.internal.events.subscribe("putCatalog",function(){this.internal.out("/OpenAction "+e+" 0 R")})}return this},function(n){var e=function(){var r=void 0;Object.defineProperty(this,"pdf",{get:function(){return r},set:function(h){r=h}});var a=150;Object.defineProperty(this,"width",{get:function(){return a},set:function(h){a=isNaN(h)||Number.isInteger(h)===!1||h<0?150:h,this.getContext("2d").pageWrapXEnabled&&(this.getContext("2d").pageWrapX=a+1)}});var l=300;Object.defineProperty(this,"height",{get:function(){return l},set:function(h){l=isNaN(h)||Number.isInteger(h)===!1||h<0?300:h,this.getContext("2d").pageWrapYEnabled&&(this.getContext("2d").pageWrapY=l+1)}});var s=[];Object.defineProperty(this,"childNodes",{get:function(){return s},set:function(h){s=h}});var c={};Object.defineProperty(this,"style",{get:function(){return c},set:function(h){c=h}}),Object.defineProperty(this,"parentNode",{})};e.prototype.getContext=function(r,a){var l;if((r=r||"2d")!=="2d")return null;for(l in a)this.pdf.context2d.hasOwnProperty(l)&&(this.pdf.context2d[l]=a[l]);return this.pdf.context2d._canvas=this,this.pdf.context2d},e.prototype.toDataURL=function(){throw new Error("toDataURL is not implemented.")},n.events.push(["initialized",function(){this.canvas=new e,this.canvas.pdf=this}])}(zt.API),function(n){var e={left:0,top:0,bottom:0,right:0},r=!1,a=function(){this.internal.__cell__===void 0&&(this.internal.__cell__={},this.internal.__cell__.padding=3,this.internal.__cell__.headerFunction=void 0,this.internal.__cell__.margins=Object.assign({},e),this.internal.__cell__.margins.width=this.getPageWidth(),l.call(this))},l=function(){this.internal.__cell__.lastCell=new s,this.internal.__cell__.pages=1},s=function(){var f=arguments[0];Object.defineProperty(this,"x",{enumerable:!0,get:function(){return f},set:function(I){f=I}});var g=arguments[1];Object.defineProperty(this,"y",{enumerable:!0,get:function(){return g},set:function(I){g=I}});var b=arguments[2];Object.defineProperty(this,"width",{enumerable:!0,get:function(){return b},set:function(I){b=I}});var w=arguments[3];Object.defineProperty(this,"height",{enumerable:!0,get:function(){return w},set:function(I){w=I}});var S=arguments[4];Object.defineProperty(this,"text",{enumerable:!0,get:function(){return S},set:function(I){S=I}});var p=arguments[5];Object.defineProperty(this,"lineNumber",{enumerable:!0,get:function(){return p},set:function(I){p=I}});var M=arguments[6];return Object.defineProperty(this,"align",{enumerable:!0,get:function(){return M},set:function(I){M=I}}),this};s.prototype.clone=function(){return new s(this.x,this.y,this.width,this.height,this.text,this.lineNumber,this.align)},s.prototype.toArray=function(){return[this.x,this.y,this.width,this.height,this.text,this.lineNumber,this.align]},n.setHeaderFunction=function(f){return a.call(this),this.internal.__cell__.headerFunction=typeof f=="function"?f:void 0,this},n.getTextDimensions=function(f,g){a.call(this);var b=(g=g||{}).fontSize||this.getFontSize(),w=g.font||this.getFont(),S=g.scaleFactor||this.internal.scaleFactor,p=0,M=0,I=0,q=this;if(!Array.isArray(f)&&typeof f!="string"){if(typeof f!="number")throw new Error("getTextDimensions expects text-parameter to be of type String or type Number or an Array of Strings.");f=String(f)}var k=g.maxWidth;k>0?typeof f=="string"?f=this.splitTextToSize(f,k):Object.prototype.toString.call(f)==="[object Array]"&&(f=f.reduce(function(J,ot){return J.concat(q.splitTextToSize(ot,k))},[])):f=Array.isArray(f)?f:[f];for(var E=0;E<f.length;E++)p<(I=this.getStringUnitWidth(f[E],{font:w})*b)&&(p=I);return p!==0&&(M=f.length),{w:p/=S,h:Math.max((M*b*this.getLineHeightFactor()-b*(this.getLineHeightFactor()-1))/S,0)}},n.cellAddPage=function(){a.call(this),this.addPage();var f=this.internal.__cell__.margins||e;return this.internal.__cell__.lastCell=new s(f.left,f.top,void 0,void 0),this.internal.__cell__.pages+=1,this};var c=n.cell=function(){var f;f=arguments[0]instanceof s?arguments[0]:new s(arguments[0],arguments[1],arguments[2],arguments[3],arguments[4],arguments[5]),a.call(this);var g=this.internal.__cell__.lastCell,b=this.internal.__cell__.padding,w=this.internal.__cell__.margins||e,S=this.internal.__cell__.tableHeaderRow,p=this.internal.__cell__.printHeaders;return g.lineNumber!==void 0&&(g.lineNumber===f.lineNumber?(f.x=(g.x||0)+(g.width||0),f.y=g.y||0):g.y+g.height+f.height+w.bottom>this.getPageHeight()?(this.cellAddPage(),f.y=w.top,p&&S&&(this.printHeaderRow(f.lineNumber,!0),f.y+=S[0].height)):f.y=g.y+g.height||f.y),f.text[0]!==void 0&&(this.rect(f.x,f.y,f.width,f.height,r===!0?"FD":void 0),f.align==="right"?this.text(f.text,f.x+f.width-b,f.y+b,{align:"right",baseline:"top"}):f.align==="center"?this.text(f.text,f.x+f.width/2,f.y+b,{align:"center",baseline:"top",maxWidth:f.width-b-b}):this.text(f.text,f.x+b,f.y+b,{align:"left",baseline:"top",maxWidth:f.width-b-b})),this.internal.__cell__.lastCell=f,this};n.table=function(f,g,b,w,S){if(a.call(this),!b)throw new Error("No data for PDF table.");var p,M,I,q,k=[],E=[],J=[],ot={},ut={},yt=[],tt=[],U=(S=S||{}).autoSize||!1,nt=S.printHeaders!==!1,pt=S.css&&S.css["font-size"]!==void 0?16*S.css["font-size"]:S.fontSize||12,L=S.margins||Object.assign({width:this.getPageWidth()},e),A=typeof S.padding=="number"?S.padding:3,B=S.headerBackgroundColor||"#c8c8c8",D=S.headerTextColor||"#000";if(l.call(this),this.internal.__cell__.printHeaders=nt,this.internal.__cell__.margins=L,this.internal.__cell__.table_font_size=pt,this.internal.__cell__.padding=A,this.internal.__cell__.headerBackgroundColor=B,this.internal.__cell__.headerTextColor=D,this.setFontSize(pt),w==null)E=k=Object.keys(b[0]),J=k.map(function(){return"left"});else if(Array.isArray(w)&&de(w[0])==="object")for(k=w.map(function(ft){return ft.name}),E=w.map(function(ft){return ft.prompt||ft.name||""}),J=w.map(function(ft){return ft.align||"left"}),p=0;p<w.length;p+=1)ut[w[p].name]=w[p].width*(19.049976/25.4);else Array.isArray(w)&&typeof w[0]=="string"&&(E=k=w,J=k.map(function(){return"left"}));if(U||Array.isArray(w)&&typeof w[0]=="string")for(p=0;p<k.length;p+=1){for(ot[q=k[p]]=b.map(function(ft){return ft[q]}),this.setFont(void 0,"bold"),yt.push(this.getTextDimensions(E[p],{fontSize:this.internal.__cell__.table_font_size,scaleFactor:this.internal.scaleFactor}).w),M=ot[q],this.setFont(void 0,"normal"),I=0;I<M.length;I+=1)yt.push(this.getTextDimensions(M[I],{fontSize:this.internal.__cell__.table_font_size,scaleFactor:this.internal.scaleFactor}).w);ut[q]=Math.max.apply(null,yt)+A+A,yt=[]}if(nt){var et={};for(p=0;p<k.length;p+=1)et[k[p]]={},et[k[p]].text=E[p],et[k[p]].align=J[p];var rt=h.call(this,et,ut);tt=k.map(function(ft){return new s(f,g,ut[ft],rt,et[ft].text,void 0,et[ft].align)}),this.setTableHeaderRow(tt),this.printHeaderRow(1,!1)}var ct=w.reduce(function(ft,Ft){return ft[Ft.name]=Ft.align,ft},{});for(p=0;p<b.length;p+=1){"rowStart"in S&&S.rowStart instanceof Function&&S.rowStart({row:p,data:b[p]},this);var X=h.call(this,b[p],ut);for(I=0;I<k.length;I+=1){var at=b[p][k[I]];"cellStart"in S&&S.cellStart instanceof Function&&S.cellStart({row:p,col:I,data:at},this),c.call(this,new s(f,g,ut[k[I]],X,at,p+2,ct[k[I]]))}}return this.internal.__cell__.table_x=f,this.internal.__cell__.table_y=g,this};var h=function(f,g){var b=this.internal.__cell__.padding,w=this.internal.__cell__.table_font_size,S=this.internal.scaleFactor;return Object.keys(f).map(function(p){var M=f[p];return this.splitTextToSize(M.hasOwnProperty("text")?M.text:M,g[p]-b-b)},this).map(function(p){return this.getLineHeightFactor()*p.length*w/S+b+b},this).reduce(function(p,M){return Math.max(p,M)},0)};n.setTableHeaderRow=function(f){a.call(this),this.internal.__cell__.tableHeaderRow=f},n.printHeaderRow=function(f,g){if(a.call(this),!this.internal.__cell__.tableHeaderRow)throw new Error("Property tableHeaderRow does not exist.");var b;if(r=!0,typeof this.internal.__cell__.headerFunction=="function"){var w=this.internal.__cell__.headerFunction(this,this.internal.__cell__.pages);this.internal.__cell__.lastCell=new s(w[0],w[1],w[2],w[3],void 0,-1)}this.setFont(void 0,"bold");for(var S=[],p=0;p<this.internal.__cell__.tableHeaderRow.length;p+=1){b=this.internal.__cell__.tableHeaderRow[p].clone(),g&&(b.y=this.internal.__cell__.margins.top||0,S.push(b)),b.lineNumber=f;var M=this.getTextColor();this.setTextColor(this.internal.__cell__.headerTextColor),this.setFillColor(this.internal.__cell__.headerBackgroundColor),c.call(this,b),this.setTextColor(M)}S.length>0&&this.setTableHeaderRow(S),this.setFont(void 0,"normal"),r=!1}}(zt.API);var eu={italic:["italic","oblique","normal"],oblique:["oblique","italic","normal"],normal:["normal","oblique","italic"]},ru=["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded"],Eo=tu(ru),nu=[100,200,300,400,500,600,700,800,900],Pc=tu(nu);function To(n){var e=n.family.replace(/"|'/g,"").toLowerCase(),r=function(s){return eu[s=s||"normal"]?s:"normal"}(n.style),a=function(s){if(!s)return 400;if(typeof s=="number")return s>=100&&s<=900&&s%100==0?s:400;if(/^\d00$/.test(s))return parseInt(s);switch(s){case"bold":return 700;case"normal":default:return 400}}(n.weight),l=function(s){return typeof Eo[s=s||"normal"]=="number"?s:"normal"}(n.stretch);return{family:e,style:r,weight:a,stretch:l,src:n.src||[],ref:n.ref||{name:e,style:[l,r,a].join(" ")}}}function Pl(n,e,r,a){var l;for(l=r;l>=0&&l<e.length;l+=a)if(n[e[l]])return n[e[l]];for(l=r;l>=0&&l<e.length;l-=a)if(n[e[l]])return n[e[l]]}var kc={"sans-serif":"helvetica",fixed:"courier",monospace:"courier",terminal:"courier",cursive:"times",fantasy:"times",serif:"times"},kl={caption:"times",icon:"times",menu:"times","message-box":"times","small-caption":"times","status-bar":"times"};function Il(n){return[n.stretch,n.style,n.weight,n.family].join(" ")}function Ic(n,e,r){for(var a=(r=r||{}).defaultFontFamily||"times",l=Object.assign({},kc,r.genericFontFamilies||{}),s=null,c=null,h=0;h<e.length;++h)if(l[(s=To(e[h])).family]&&(s.family=l[s.family]),n.hasOwnProperty(s.family)){c=n[s.family];break}if(!(c=c||n[a]))throw new Error("Could not find a font-family for the rule '"+Il(s)+"' and default family '"+a+"'.");if(c=function(f,g){if(g[f])return g[f];var b=Eo[f],w=b<=Eo.normal?-1:1,S=Pl(g,ru,b,w);if(!S)throw new Error("Could not find a matching font-stretch value for "+f);return S}(s.stretch,c),c=function(f,g){if(g[f])return g[f];for(var b=eu[f],w=0;w<b.length;++w)if(g[b[w]])return g[b[w]];throw new Error("Could not find a matching font-style for "+f)}(s.style,c),!(c=function(f,g){if(g[f])return g[f];if(f===400&&g[500])return g[500];if(f===500&&g[400])return g[400];var b=Pc[f],w=Pl(g,nu,b,f<400?-1:1);if(!w)throw new Error("Could not find a matching font-weight for value "+f);return w}(s.weight,c)))throw new Error("Failed to resolve a font for the rule '"+Il(s)+"'.");return c}function Fl(n){return n.trimLeft()}function Fc(n,e){for(var r=0;r<n.length;){if(n.charAt(r)===e)return[n.substring(0,r),n.substring(r+1)];r+=1}return null}function Cc(n){var e=n.match(/^(-[a-z_]|[a-z_])[a-z0-9_-]*/i);return e===null?null:[e[0],n.substring(e[0].length)]}var ps,Cl,jl,Ao=["times"];(function(n){var e,r,a,l,s,c,h,f,g,b=function(N){return N=N||{},this.isStrokeTransparent=N.isStrokeTransparent||!1,this.strokeOpacity=N.strokeOpacity||1,this.strokeStyle=N.strokeStyle||"#000000",this.fillStyle=N.fillStyle||"#000000",this.isFillTransparent=N.isFillTransparent||!1,this.fillOpacity=N.fillOpacity||1,this.font=N.font||"10px sans-serif",this.textBaseline=N.textBaseline||"alphabetic",this.textAlign=N.textAlign||"left",this.lineWidth=N.lineWidth||1,this.lineJoin=N.lineJoin||"miter",this.lineCap=N.lineCap||"butt",this.path=N.path||[],this.transform=N.transform!==void 0?N.transform.clone():new f,this.globalCompositeOperation=N.globalCompositeOperation||"normal",this.globalAlpha=N.globalAlpha||1,this.clip_path=N.clip_path||[],this.currentPoint=N.currentPoint||new c,this.miterLimit=N.miterLimit||10,this.lastPoint=N.lastPoint||new c,this.lineDashOffset=N.lineDashOffset||0,this.lineDash=N.lineDash||[],this.margin=N.margin||[0,0,0,0],this.prevPageLastElemOffset=N.prevPageLastElemOffset||0,this.ignoreClearRect=typeof N.ignoreClearRect!="boolean"||N.ignoreClearRect,this};n.events.push(["initialized",function(){this.context2d=new w(this),e=this.internal.f2,r=this.internal.getCoordinateString,a=this.internal.getVerticalCoordinateString,l=this.internal.getHorizontalCoordinate,s=this.internal.getVerticalCoordinate,c=this.internal.Point,h=this.internal.Rectangle,f=this.internal.Matrix,g=new b}]);var w=function(N){Object.defineProperty(this,"canvas",{get:function(){return{parentNode:!1,style:!1}}});var C=N;Object.defineProperty(this,"pdf",{get:function(){return C}});var O=!1;Object.defineProperty(this,"pageWrapXEnabled",{get:function(){return O},set:function(dt){O=!!dt}});var R=!1;Object.defineProperty(this,"pageWrapYEnabled",{get:function(){return R},set:function(dt){R=!!dt}});var G=0;Object.defineProperty(this,"posX",{get:function(){return G},set:function(dt){isNaN(dt)||(G=dt)}});var $=0;Object.defineProperty(this,"posY",{get:function(){return $},set:function(dt){isNaN(dt)||($=dt)}}),Object.defineProperty(this,"margin",{get:function(){return g.margin},set:function(dt){var T;typeof dt=="number"?T=[dt,dt,dt,dt]:((T=new Array(4))[0]=dt[0],T[1]=dt.length>=2?dt[1]:T[0],T[2]=dt.length>=3?dt[2]:T[0],T[3]=dt.length>=4?dt[3]:T[1]),g.margin=T}});var it=!1;Object.defineProperty(this,"autoPaging",{get:function(){return it},set:function(dt){it=dt}});var st=0;Object.defineProperty(this,"lastBreak",{get:function(){return st},set:function(dt){st=dt}});var Nt=[];Object.defineProperty(this,"pageBreaks",{get:function(){return Nt},set:function(dt){Nt=dt}}),Object.defineProperty(this,"ctx",{get:function(){return g},set:function(dt){dt instanceof b&&(g=dt)}}),Object.defineProperty(this,"path",{get:function(){return g.path},set:function(dt){g.path=dt}});var Lt=[];Object.defineProperty(this,"ctxStack",{get:function(){return Lt},set:function(dt){Lt=dt}}),Object.defineProperty(this,"fillStyle",{get:function(){return this.ctx.fillStyle},set:function(dt){var T;T=S(dt),this.ctx.fillStyle=T.style,this.ctx.isFillTransparent=T.a===0,this.ctx.fillOpacity=T.a,this.pdf.setFillColor(T.r,T.g,T.b,{a:T.a}),this.pdf.setTextColor(T.r,T.g,T.b,{a:T.a})}}),Object.defineProperty(this,"strokeStyle",{get:function(){return this.ctx.strokeStyle},set:function(dt){var T=S(dt);this.ctx.strokeStyle=T.style,this.ctx.isStrokeTransparent=T.a===0,this.ctx.strokeOpacity=T.a,T.a===0?this.pdf.setDrawColor(255,255,255):(T.a,this.pdf.setDrawColor(T.r,T.g,T.b))}}),Object.defineProperty(this,"lineCap",{get:function(){return this.ctx.lineCap},set:function(dt){["butt","round","square"].indexOf(dt)!==-1&&(this.ctx.lineCap=dt,this.pdf.setLineCap(dt))}}),Object.defineProperty(this,"lineWidth",{get:function(){return this.ctx.lineWidth},set:function(dt){isNaN(dt)||(this.ctx.lineWidth=dt,this.pdf.setLineWidth(dt))}}),Object.defineProperty(this,"lineJoin",{get:function(){return this.ctx.lineJoin},set:function(dt){["bevel","round","miter"].indexOf(dt)!==-1&&(this.ctx.lineJoin=dt,this.pdf.setLineJoin(dt))}}),Object.defineProperty(this,"miterLimit",{get:function(){return this.ctx.miterLimit},set:function(dt){isNaN(dt)||(this.ctx.miterLimit=dt,this.pdf.setMiterLimit(dt))}}),Object.defineProperty(this,"textBaseline",{get:function(){return this.ctx.textBaseline},set:function(dt){this.ctx.textBaseline=dt}}),Object.defineProperty(this,"textAlign",{get:function(){return this.ctx.textAlign},set:function(dt){["right","end","center","left","start"].indexOf(dt)!==-1&&(this.ctx.textAlign=dt)}});var It=null;function St(dt,T){if(It===null){var Zt=function(Et){var xt=[];return Object.keys(Et).forEach(function(At){Et[At].forEach(function(Ct){var kt=null;switch(Ct){case"bold":kt={family:At,weight:"bold"};break;case"italic":kt={family:At,style:"italic"};break;case"bolditalic":kt={family:At,weight:"bold",style:"italic"};break;case"":case"normal":kt={family:At}}kt!==null&&(kt.ref={name:At,style:Ct},xt.push(kt))})}),xt}(dt.getFontList());It=function(Et){for(var xt={},At=0;At<Et.length;++At){var Ct=To(Et[At]),kt=Ct.family,Tt=Ct.stretch,Yt=Ct.style,te=Ct.weight;xt[kt]=xt[kt]||{},xt[kt][Tt]=xt[kt][Tt]||{},xt[kt][Tt][Yt]=xt[kt][Tt][Yt]||{},xt[kt][Tt][Yt][te]=Ct}return xt}(Zt.concat(T))}return It}var Ut=null;Object.defineProperty(this,"fontFaces",{get:function(){return Ut},set:function(dt){It=null,Ut=dt}}),Object.defineProperty(this,"font",{get:function(){return this.ctx.font},set:function(dt){var T;if(this.ctx.font=dt,(T=/^\s*(?=(?:(?:[-a-z]+\s*){0,2}(italic|oblique))?)(?=(?:(?:[-a-z]+\s*){0,2}(small-caps))?)(?=(?:(?:[-a-z]+\s*){0,2}(bold(?:er)?|lighter|[1-9]00))?)(?:(?:normal|\1|\2|\3)\s*){0,3}((?:xx?-)?(?:small|large)|medium|smaller|larger|[.\d]+(?:\%|in|[cem]m|ex|p[ctx]))(?:\s*\/\s*(normal|[.\d]+(?:\%|in|[cem]m|ex|p[ctx])))?\s*([-_,\"\'\sa-z]+?)\s*$/i.exec(dt))!==null){var Zt=T[1];T[2];var Et=T[3],xt=T[4];T[5];var At=T[6],Ct=/^([.\d]+)((?:%|in|[cem]m|ex|p[ctx]))$/i.exec(xt)[2];xt=Math.floor(Ct==="px"?parseFloat(xt)*this.pdf.internal.scaleFactor:Ct==="em"?parseFloat(xt)*this.pdf.getFontSize():parseFloat(xt)*this.pdf.internal.scaleFactor),this.pdf.setFontSize(xt);var kt=function(Vt){var re,jt,Je=[],oe=Vt.trim();if(oe==="")return Ao;if(oe in kl)return[kl[oe]];for(;oe!=="";){switch(jt=null,re=(oe=Fl(oe)).charAt(0)){case'"':case"'":jt=Fc(oe.substring(1),re);break;default:jt=Cc(oe)}if(jt===null||(Je.push(jt[0]),(oe=Fl(jt[1]))!==""&&oe.charAt(0)!==","))return Ao;oe=oe.replace(/^,/,"")}return Je}(At);if(this.fontFaces){var Tt=Ic(St(this.pdf,this.fontFaces),kt.map(function(Vt){return{family:Vt,stretch:"normal",weight:Et,style:Zt}}));this.pdf.setFont(Tt.ref.name,Tt.ref.style)}else{var Yt="";(Et==="bold"||parseInt(Et,10)>=700||Zt==="bold")&&(Yt="bold"),Zt==="italic"&&(Yt+="italic"),Yt.length===0&&(Yt="normal");for(var te="",ee={arial:"Helvetica",Arial:"Helvetica",verdana:"Helvetica",Verdana:"Helvetica",helvetica:"Helvetica",Helvetica:"Helvetica","sans-serif":"Helvetica",fixed:"Courier",monospace:"Courier",terminal:"Courier",cursive:"Times",fantasy:"Times",serif:"Times"},ae=0;ae<kt.length;ae++){if(this.pdf.internal.getFont(kt[ae],Yt,{noFallback:!0,disableWarning:!0})!==void 0){te=kt[ae];break}if(Yt==="bolditalic"&&this.pdf.internal.getFont(kt[ae],"bold",{noFallback:!0,disableWarning:!0})!==void 0)te=kt[ae],Yt="bold";else if(this.pdf.internal.getFont(kt[ae],"normal",{noFallback:!0,disableWarning:!0})!==void 0){te=kt[ae],Yt="normal";break}}if(te===""){for(var pe=0;pe<kt.length;pe++)if(ee[kt[pe]]){te=ee[kt[pe]];break}}te=te===""?"Times":te,this.pdf.setFont(te,Yt)}}}}),Object.defineProperty(this,"globalCompositeOperation",{get:function(){return this.ctx.globalCompositeOperation},set:function(dt){this.ctx.globalCompositeOperation=dt}}),Object.defineProperty(this,"globalAlpha",{get:function(){return this.ctx.globalAlpha},set:function(dt){this.ctx.globalAlpha=dt}}),Object.defineProperty(this,"lineDashOffset",{get:function(){return this.ctx.lineDashOffset},set:function(dt){this.ctx.lineDashOffset=dt,Ft.call(this)}}),Object.defineProperty(this,"lineDash",{get:function(){return this.ctx.lineDash},set:function(dt){this.ctx.lineDash=dt,Ft.call(this)}}),Object.defineProperty(this,"ignoreClearRect",{get:function(){return this.ctx.ignoreClearRect},set:function(dt){this.ctx.ignoreClearRect=!!dt}})};w.prototype.setLineDash=function(N){this.lineDash=N},w.prototype.getLineDash=function(){return this.lineDash.length%2?this.lineDash.concat(this.lineDash):this.lineDash.slice()},w.prototype.fill=function(){ot.call(this,"fill",!1)},w.prototype.stroke=function(){ot.call(this,"stroke",!1)},w.prototype.beginPath=function(){this.path=[{type:"begin"}]},w.prototype.moveTo=function(N,C){if(isNaN(N)||isNaN(C))throw be.error("jsPDF.context2d.moveTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.moveTo");var O=this.ctx.transform.applyToPoint(new c(N,C));this.path.push({type:"mt",x:O.x,y:O.y}),this.ctx.lastPoint=new c(N,C)},w.prototype.closePath=function(){var N=new c(0,0),C=0;for(C=this.path.length-1;C!==-1;C--)if(this.path[C].type==="begin"&&de(this.path[C+1])==="object"&&typeof this.path[C+1].x=="number"){N=new c(this.path[C+1].x,this.path[C+1].y);break}this.path.push({type:"close"}),this.ctx.lastPoint=new c(N.x,N.y)},w.prototype.lineTo=function(N,C){if(isNaN(N)||isNaN(C))throw be.error("jsPDF.context2d.lineTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.lineTo");var O=this.ctx.transform.applyToPoint(new c(N,C));this.path.push({type:"lt",x:O.x,y:O.y}),this.ctx.lastPoint=new c(O.x,O.y)},w.prototype.clip=function(){this.ctx.clip_path=JSON.parse(JSON.stringify(this.path)),ot.call(this,null,!0)},w.prototype.quadraticCurveTo=function(N,C,O,R){if(isNaN(O)||isNaN(R)||isNaN(N)||isNaN(C))throw be.error("jsPDF.context2d.quadraticCurveTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.quadraticCurveTo");var G=this.ctx.transform.applyToPoint(new c(O,R)),$=this.ctx.transform.applyToPoint(new c(N,C));this.path.push({type:"qct",x1:$.x,y1:$.y,x:G.x,y:G.y}),this.ctx.lastPoint=new c(G.x,G.y)},w.prototype.bezierCurveTo=function(N,C,O,R,G,$){if(isNaN(G)||isNaN($)||isNaN(N)||isNaN(C)||isNaN(O)||isNaN(R))throw be.error("jsPDF.context2d.bezierCurveTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.bezierCurveTo");var it=this.ctx.transform.applyToPoint(new c(G,$)),st=this.ctx.transform.applyToPoint(new c(N,C)),Nt=this.ctx.transform.applyToPoint(new c(O,R));this.path.push({type:"bct",x1:st.x,y1:st.y,x2:Nt.x,y2:Nt.y,x:it.x,y:it.y}),this.ctx.lastPoint=new c(it.x,it.y)},w.prototype.arc=function(N,C,O,R,G,$){if(isNaN(N)||isNaN(C)||isNaN(O)||isNaN(R)||isNaN(G))throw be.error("jsPDF.context2d.arc: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.arc");if($=!!$,!this.ctx.transform.isIdentity){var it=this.ctx.transform.applyToPoint(new c(N,C));N=it.x,C=it.y;var st=this.ctx.transform.applyToPoint(new c(0,O)),Nt=this.ctx.transform.applyToPoint(new c(0,0));O=Math.sqrt(Math.pow(st.x-Nt.x,2)+Math.pow(st.y-Nt.y,2))}Math.abs(G-R)>=2*Math.PI&&(R=0,G=2*Math.PI),this.path.push({type:"arc",x:N,y:C,radius:O,startAngle:R,endAngle:G,counterclockwise:$})},w.prototype.arcTo=function(N,C,O,R,G){throw new Error("arcTo not implemented.")},w.prototype.rect=function(N,C,O,R){if(isNaN(N)||isNaN(C)||isNaN(O)||isNaN(R))throw be.error("jsPDF.context2d.rect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.rect");this.moveTo(N,C),this.lineTo(N+O,C),this.lineTo(N+O,C+R),this.lineTo(N,C+R),this.lineTo(N,C),this.lineTo(N+O,C),this.lineTo(N,C)},w.prototype.fillRect=function(N,C,O,R){if(isNaN(N)||isNaN(C)||isNaN(O)||isNaN(R))throw be.error("jsPDF.context2d.fillRect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.fillRect");if(!p.call(this)){var G={};this.lineCap!=="butt"&&(G.lineCap=this.lineCap,this.lineCap="butt"),this.lineJoin!=="miter"&&(G.lineJoin=this.lineJoin,this.lineJoin="miter"),this.beginPath(),this.rect(N,C,O,R),this.fill(),G.hasOwnProperty("lineCap")&&(this.lineCap=G.lineCap),G.hasOwnProperty("lineJoin")&&(this.lineJoin=G.lineJoin)}},w.prototype.strokeRect=function(N,C,O,R){if(isNaN(N)||isNaN(C)||isNaN(O)||isNaN(R))throw be.error("jsPDF.context2d.strokeRect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.strokeRect");M.call(this)||(this.beginPath(),this.rect(N,C,O,R),this.stroke())},w.prototype.clearRect=function(N,C,O,R){if(isNaN(N)||isNaN(C)||isNaN(O)||isNaN(R))throw be.error("jsPDF.context2d.clearRect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.clearRect");this.ignoreClearRect||(this.fillStyle="#ffffff",this.fillRect(N,C,O,R))},w.prototype.save=function(N){N=typeof N!="boolean"||N;for(var C=this.pdf.internal.getCurrentPageInfo().pageNumber,O=0;O<this.pdf.internal.getNumberOfPages();O++)this.pdf.setPage(O+1),this.pdf.internal.out("q");if(this.pdf.setPage(C),N){this.ctx.fontSize=this.pdf.internal.getFontSize();var R=new b(this.ctx);this.ctxStack.push(this.ctx),this.ctx=R}},w.prototype.restore=function(N){N=typeof N!="boolean"||N;for(var C=this.pdf.internal.getCurrentPageInfo().pageNumber,O=0;O<this.pdf.internal.getNumberOfPages();O++)this.pdf.setPage(O+1),this.pdf.internal.out("Q");this.pdf.setPage(C),N&&this.ctxStack.length!==0&&(this.ctx=this.ctxStack.pop(),this.fillStyle=this.ctx.fillStyle,this.strokeStyle=this.ctx.strokeStyle,this.font=this.ctx.font,this.lineCap=this.ctx.lineCap,this.lineWidth=this.ctx.lineWidth,this.lineJoin=this.ctx.lineJoin,this.lineDash=this.ctx.lineDash,this.lineDashOffset=this.ctx.lineDashOffset)},w.prototype.toDataURL=function(){throw new Error("toDataUrl not implemented.")};var S=function(N){var C,O,R,G;if(N.isCanvasGradient===!0&&(N=N.getColor()),!N)return{r:0,g:0,b:0,a:0,style:N};if(/transparent|rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*0+\s*\)/.test(N))C=0,O=0,R=0,G=0;else{var $=/rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/.exec(N);if($!==null)C=parseInt($[1]),O=parseInt($[2]),R=parseInt($[3]),G=1;else if(($=/rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*([\d.]+)\s*\)/.exec(N))!==null)C=parseInt($[1]),O=parseInt($[2]),R=parseInt($[3]),G=parseFloat($[4]);else{if(G=1,typeof N=="string"&&N.charAt(0)!=="#"){var it=new Jl(N);N=it.ok?it.toHex():"#000000"}N.length===4?(C=N.substring(1,2),C+=C,O=N.substring(2,3),O+=O,R=N.substring(3,4),R+=R):(C=N.substring(1,3),O=N.substring(3,5),R=N.substring(5,7)),C=parseInt(C,16),O=parseInt(O,16),R=parseInt(R,16)}}return{r:C,g:O,b:R,a:G,style:N}},p=function(){return this.ctx.isFillTransparent||this.globalAlpha==0},M=function(){return!!(this.ctx.isStrokeTransparent||this.globalAlpha==0)};w.prototype.fillText=function(N,C,O,R){if(isNaN(C)||isNaN(O)||typeof N!="string")throw be.error("jsPDF.context2d.fillText: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.fillText");if(R=isNaN(R)?void 0:R,!p.call(this)){var G=X(this.ctx.transform.rotation),$=this.ctx.transform.scaleX;A.call(this,{text:N,x:C,y:O,scale:$,angle:G,align:this.textAlign,maxWidth:R})}},w.prototype.strokeText=function(N,C,O,R){if(isNaN(C)||isNaN(O)||typeof N!="string")throw be.error("jsPDF.context2d.strokeText: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.strokeText");if(!M.call(this)){R=isNaN(R)?void 0:R;var G=X(this.ctx.transform.rotation),$=this.ctx.transform.scaleX;A.call(this,{text:N,x:C,y:O,scale:$,renderingMode:"stroke",angle:G,align:this.textAlign,maxWidth:R})}},w.prototype.measureText=function(N){if(typeof N!="string")throw be.error("jsPDF.context2d.measureText: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.measureText");var C=this.pdf,O=this.pdf.internal.scaleFactor,R=C.internal.getFontSize(),G=C.getStringUnitWidth(N)*R/C.internal.scaleFactor,$=function(it){var st=(it=it||{}).width||0;return Object.defineProperty(this,"width",{get:function(){return st}}),this};return new $({width:G*=Math.round(96*O/72*1e4)/1e4})},w.prototype.scale=function(N,C){if(isNaN(N)||isNaN(C))throw be.error("jsPDF.context2d.scale: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.scale");var O=new f(N,0,0,C,0,0);this.ctx.transform=this.ctx.transform.multiply(O)},w.prototype.rotate=function(N){if(isNaN(N))throw be.error("jsPDF.context2d.rotate: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.rotate");var C=new f(Math.cos(N),Math.sin(N),-Math.sin(N),Math.cos(N),0,0);this.ctx.transform=this.ctx.transform.multiply(C)},w.prototype.translate=function(N,C){if(isNaN(N)||isNaN(C))throw be.error("jsPDF.context2d.translate: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.translate");var O=new f(1,0,0,1,N,C);this.ctx.transform=this.ctx.transform.multiply(O)},w.prototype.transform=function(N,C,O,R,G,$){if(isNaN(N)||isNaN(C)||isNaN(O)||isNaN(R)||isNaN(G)||isNaN($))throw be.error("jsPDF.context2d.transform: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.transform");var it=new f(N,C,O,R,G,$);this.ctx.transform=this.ctx.transform.multiply(it)},w.prototype.setTransform=function(N,C,O,R,G,$){N=isNaN(N)?1:N,C=isNaN(C)?0:C,O=isNaN(O)?0:O,R=isNaN(R)?1:R,G=isNaN(G)?0:G,$=isNaN($)?0:$,this.ctx.transform=new f(N,C,O,R,G,$)};var I=function(){return this.margin[0]>0||this.margin[1]>0||this.margin[2]>0||this.margin[3]>0};w.prototype.drawImage=function(N,C,O,R,G,$,it,st,Nt){var Lt=this.pdf.getImageProperties(N),It=1,St=1,Ut=1,dt=1;R!==void 0&&st!==void 0&&(Ut=st/R,dt=Nt/G,It=Lt.width/R*st/R,St=Lt.height/G*Nt/G),$===void 0&&($=C,it=O,C=0,O=0),R!==void 0&&st===void 0&&(st=R,Nt=G),R===void 0&&st===void 0&&(st=Lt.width,Nt=Lt.height);for(var T,Zt=this.ctx.transform.decompose(),Et=X(Zt.rotate.shx),xt=new f,At=(xt=(xt=(xt=xt.multiply(Zt.translate)).multiply(Zt.skew)).multiply(Zt.scale)).applyToRectangle(new h($-C*Ut,it-O*dt,R*It,G*St)),Ct=q.call(this,At),kt=[],Tt=0;Tt<Ct.length;Tt+=1)kt.indexOf(Ct[Tt])===-1&&kt.push(Ct[Tt]);if(J(kt),this.autoPaging)for(var Yt=kt[0],te=kt[kt.length-1],ee=Yt;ee<te+1;ee++){this.pdf.setPage(ee);var ae=this.pdf.internal.pageSize.width-this.margin[3]-this.margin[1],pe=ee===1?this.posY+this.margin[0]:this.margin[0],Vt=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],re=this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2],jt=ee===1?0:Vt+(ee-2)*re;if(this.ctx.clip_path.length!==0){var Je=this.path;T=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=E(T,this.posX+this.margin[3],-jt+pe+this.ctx.prevPageLastElemOffset),ut.call(this,"fill",!0),this.path=Je}var oe=JSON.parse(JSON.stringify(At));oe=E([oe],this.posX+this.margin[3],-jt+pe+this.ctx.prevPageLastElemOffset)[0];var Pr=(ee>Yt||ee<te)&&I.call(this);Pr&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],ae,re,null).clip().discardPath()),this.pdf.addImage(N,"JPEG",oe.x,oe.y,oe.w,oe.h,null,null,Et),Pr&&this.pdf.restoreGraphicsState()}else this.pdf.addImage(N,"JPEG",At.x,At.y,At.w,At.h,null,null,Et)};var q=function(N,C,O){var R=[];C=C||this.pdf.internal.pageSize.width,O=O||this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2];var G=this.posY+this.ctx.prevPageLastElemOffset;switch(N.type){default:case"mt":case"lt":R.push(Math.floor((N.y+G)/O)+1);break;case"arc":R.push(Math.floor((N.y+G-N.radius)/O)+1),R.push(Math.floor((N.y+G+N.radius)/O)+1);break;case"qct":var $=at(this.ctx.lastPoint.x,this.ctx.lastPoint.y,N.x1,N.y1,N.x,N.y);R.push(Math.floor(($.y+G)/O)+1),R.push(Math.floor(($.y+$.h+G)/O)+1);break;case"bct":var it=ft(this.ctx.lastPoint.x,this.ctx.lastPoint.y,N.x1,N.y1,N.x2,N.y2,N.x,N.y);R.push(Math.floor((it.y+G)/O)+1),R.push(Math.floor((it.y+it.h+G)/O)+1);break;case"rect":R.push(Math.floor((N.y+G)/O)+1),R.push(Math.floor((N.y+N.h+G)/O)+1)}for(var st=0;st<R.length;st+=1)for(;this.pdf.internal.getNumberOfPages()<R[st];)k.call(this);return R},k=function(){var N=this.fillStyle,C=this.strokeStyle,O=this.font,R=this.lineCap,G=this.lineWidth,$=this.lineJoin;this.pdf.addPage(),this.fillStyle=N,this.strokeStyle=C,this.font=O,this.lineCap=R,this.lineWidth=G,this.lineJoin=$},E=function(N,C,O){for(var R=0;R<N.length;R++)switch(N[R].type){case"bct":N[R].x2+=C,N[R].y2+=O;case"qct":N[R].x1+=C,N[R].y1+=O;case"mt":case"lt":case"arc":default:N[R].x+=C,N[R].y+=O}return N},J=function(N){return N.sort(function(C,O){return C-O})},ot=function(N,C){for(var O,R,G=this.fillStyle,$=this.strokeStyle,it=this.lineCap,st=this.lineWidth,Nt=Math.abs(st*this.ctx.transform.scaleX),Lt=this.lineJoin,It=JSON.parse(JSON.stringify(this.path)),St=JSON.parse(JSON.stringify(this.path)),Ut=[],dt=0;dt<St.length;dt++)if(St[dt].x!==void 0)for(var T=q.call(this,St[dt]),Zt=0;Zt<T.length;Zt+=1)Ut.indexOf(T[Zt])===-1&&Ut.push(T[Zt]);for(var Et=0;Et<Ut.length;Et++)for(;this.pdf.internal.getNumberOfPages()<Ut[Et];)k.call(this);if(J(Ut),this.autoPaging)for(var xt=Ut[0],At=Ut[Ut.length-1],Ct=xt;Ct<At+1;Ct++){this.pdf.setPage(Ct),this.fillStyle=G,this.strokeStyle=$,this.lineCap=it,this.lineWidth=Nt,this.lineJoin=Lt;var kt=this.pdf.internal.pageSize.width-this.margin[3]-this.margin[1],Tt=Ct===1?this.posY+this.margin[0]:this.margin[0],Yt=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],te=this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2],ee=Ct===1?0:Yt+(Ct-2)*te;if(this.ctx.clip_path.length!==0){var ae=this.path;O=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=E(O,this.posX+this.margin[3],-ee+Tt+this.ctx.prevPageLastElemOffset),ut.call(this,N,!0),this.path=ae}if(R=JSON.parse(JSON.stringify(It)),this.path=E(R,this.posX+this.margin[3],-ee+Tt+this.ctx.prevPageLastElemOffset),C===!1||Ct===0){var pe=(Ct>xt||Ct<At)&&I.call(this);pe&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],kt,te,null).clip().discardPath()),ut.call(this,N,C),pe&&this.pdf.restoreGraphicsState()}this.lineWidth=st}else this.lineWidth=Nt,ut.call(this,N,C),this.lineWidth=st;this.path=It},ut=function(N,C){if((N!=="stroke"||C||!M.call(this))&&(N==="stroke"||C||!p.call(this))){for(var O,R,G=[],$=this.path,it=0;it<$.length;it++){var st=$[it];switch(st.type){case"begin":G.push({begin:!0});break;case"close":G.push({close:!0});break;case"mt":G.push({start:st,deltas:[],abs:[]});break;case"lt":var Nt=G.length;if($[it-1]&&!isNaN($[it-1].x)&&(O=[st.x-$[it-1].x,st.y-$[it-1].y],Nt>0)){for(;Nt>=0;Nt--)if(G[Nt-1].close!==!0&&G[Nt-1].begin!==!0){G[Nt-1].deltas.push(O),G[Nt-1].abs.push(st);break}}break;case"bct":O=[st.x1-$[it-1].x,st.y1-$[it-1].y,st.x2-$[it-1].x,st.y2-$[it-1].y,st.x-$[it-1].x,st.y-$[it-1].y],G[G.length-1].deltas.push(O);break;case"qct":var Lt=$[it-1].x+2/3*(st.x1-$[it-1].x),It=$[it-1].y+2/3*(st.y1-$[it-1].y),St=st.x+2/3*(st.x1-st.x),Ut=st.y+2/3*(st.y1-st.y),dt=st.x,T=st.y;O=[Lt-$[it-1].x,It-$[it-1].y,St-$[it-1].x,Ut-$[it-1].y,dt-$[it-1].x,T-$[it-1].y],G[G.length-1].deltas.push(O);break;case"arc":G.push({deltas:[],abs:[],arc:!0}),Array.isArray(G[G.length-1].abs)&&G[G.length-1].abs.push(st)}}R=C?null:N==="stroke"?"stroke":"fill";for(var Zt=!1,Et=0;Et<G.length;Et++)if(G[Et].arc)for(var xt=G[Et].abs,At=0;At<xt.length;At++){var Ct=xt[At];Ct.type==="arc"?U.call(this,Ct.x,Ct.y,Ct.radius,Ct.startAngle,Ct.endAngle,Ct.counterclockwise,void 0,C,!Zt):B.call(this,Ct.x,Ct.y),Zt=!0}else if(G[Et].close===!0)this.pdf.internal.out("h"),Zt=!1;else if(G[Et].begin!==!0){var kt=G[Et].start.x,Tt=G[Et].start.y;D.call(this,G[Et].deltas,kt,Tt),Zt=!0}R&&nt.call(this,R),C&&pt.call(this)}},yt=function(N){var C=this.pdf.internal.getFontSize()/this.pdf.internal.scaleFactor,O=C*(this.pdf.internal.getLineHeightFactor()-1);switch(this.ctx.textBaseline){case"bottom":return N-O;case"top":return N+C-O;case"hanging":return N+C-2*O;case"middle":return N+C/2-O;case"ideographic":return N;case"alphabetic":default:return N}},tt=function(N){return N+this.pdf.internal.getFontSize()/this.pdf.internal.scaleFactor*(this.pdf.internal.getLineHeightFactor()-1)};w.prototype.createLinearGradient=function(){var N=function(){};return N.colorStops=[],N.addColorStop=function(C,O){this.colorStops.push([C,O])},N.getColor=function(){return this.colorStops.length===0?"#000000":this.colorStops[0][1]},N.isCanvasGradient=!0,N},w.prototype.createPattern=function(){return this.createLinearGradient()},w.prototype.createRadialGradient=function(){return this.createLinearGradient()};var U=function(N,C,O,R,G,$,it,st,Nt){for(var Lt=rt.call(this,O,R,G,$),It=0;It<Lt.length;It++){var St=Lt[It];It===0&&(Nt?L.call(this,St.x1+N,St.y1+C):B.call(this,St.x1+N,St.y1+C)),et.call(this,N,C,St.x2,St.y2,St.x3,St.y3,St.x4,St.y4)}st?pt.call(this):nt.call(this,it)},nt=function(N){switch(N){case"stroke":this.pdf.internal.out("S");break;case"fill":this.pdf.internal.out("f")}},pt=function(){this.pdf.clip(),this.pdf.discardPath()},L=function(N,C){this.pdf.internal.out(r(N)+" "+a(C)+" m")},A=function(N){var C;switch(N.align){case"right":case"end":C="right";break;case"center":C="center";break;case"left":case"start":default:C="left"}var O=this.pdf.getTextDimensions(N.text),R=yt.call(this,N.y),G=tt.call(this,R)-O.h,$=this.ctx.transform.applyToPoint(new c(N.x,R)),it=this.ctx.transform.decompose(),st=new f;st=(st=(st=st.multiply(it.translate)).multiply(it.skew)).multiply(it.scale);for(var Nt,Lt,It,St=this.ctx.transform.applyToRectangle(new h(N.x,R,O.w,O.h)),Ut=st.applyToRectangle(new h(N.x,G,O.w,O.h)),dt=q.call(this,Ut),T=[],Zt=0;Zt<dt.length;Zt+=1)T.indexOf(dt[Zt])===-1&&T.push(dt[Zt]);if(J(T),this.autoPaging)for(var Et=T[0],xt=T[T.length-1],At=Et;At<xt+1;At++){this.pdf.setPage(At);var Ct=At===1?this.posY+this.margin[0]:this.margin[0],kt=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],Tt=this.pdf.internal.pageSize.height-this.margin[2],Yt=Tt-this.margin[0],te=this.pdf.internal.pageSize.width-this.margin[1],ee=te-this.margin[3],ae=At===1?0:kt+(At-2)*Yt;if(this.ctx.clip_path.length!==0){var pe=this.path;Nt=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=E(Nt,this.posX+this.margin[3],-1*ae+Ct),ut.call(this,"fill",!0),this.path=pe}var Vt=E([JSON.parse(JSON.stringify(Ut))],this.posX+this.margin[3],-ae+Ct+this.ctx.prevPageLastElemOffset)[0];N.scale>=.01&&(Lt=this.pdf.internal.getFontSize(),this.pdf.setFontSize(Lt*N.scale),It=this.lineWidth,this.lineWidth=It*N.scale);var re=this.autoPaging!=="text";if(re||Vt.y+Vt.h<=Tt){if(re||Vt.y>=Ct&&Vt.x<=te){var jt=re?N.text:this.pdf.splitTextToSize(N.text,N.maxWidth||te-Vt.x)[0],Je=E([JSON.parse(JSON.stringify(St))],this.posX+this.margin[3],-ae+Ct+this.ctx.prevPageLastElemOffset)[0],oe=re&&(At>Et||At<xt)&&I.call(this);oe&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],ee,Yt,null).clip().discardPath()),this.pdf.text(jt,Je.x,Je.y,{angle:N.angle,align:C,renderingMode:N.renderingMode}),oe&&this.pdf.restoreGraphicsState()}}else Vt.y<Tt&&(this.ctx.prevPageLastElemOffset+=Tt-Vt.y);N.scale>=.01&&(this.pdf.setFontSize(Lt),this.lineWidth=It)}else N.scale>=.01&&(Lt=this.pdf.internal.getFontSize(),this.pdf.setFontSize(Lt*N.scale),It=this.lineWidth,this.lineWidth=It*N.scale),this.pdf.text(N.text,$.x+this.posX,$.y+this.posY,{angle:N.angle,align:C,renderingMode:N.renderingMode,maxWidth:N.maxWidth}),N.scale>=.01&&(this.pdf.setFontSize(Lt),this.lineWidth=It)},B=function(N,C,O,R){O=O||0,R=R||0,this.pdf.internal.out(r(N+O)+" "+a(C+R)+" l")},D=function(N,C,O){return this.pdf.lines(N,C,O,null,null)},et=function(N,C,O,R,G,$,it,st){this.pdf.internal.out([e(l(O+N)),e(s(R+C)),e(l(G+N)),e(s($+C)),e(l(it+N)),e(s(st+C)),"c"].join(" "))},rt=function(N,C,O,R){for(var G=2*Math.PI,$=Math.PI/2;C>O;)C-=G;var it=Math.abs(O-C);it<G&&R&&(it=G-it);for(var st=[],Nt=R?-1:1,Lt=C;it>1e-5;){var It=Lt+Nt*Math.min(it,$);st.push(ct.call(this,N,Lt,It)),it-=Math.abs(It-Lt),Lt=It}return st},ct=function(N,C,O){var R=(O-C)/2,G=N*Math.cos(R),$=N*Math.sin(R),it=G,st=-$,Nt=it*it+st*st,Lt=Nt+it*G+st*$,It=4/3*(Math.sqrt(2*Nt*Lt)-Lt)/(it*$-st*G),St=it-It*st,Ut=st+It*it,dt=St,T=-Ut,Zt=R+C,Et=Math.cos(Zt),xt=Math.sin(Zt);return{x1:N*Math.cos(C),y1:N*Math.sin(C),x2:St*Et-Ut*xt,y2:St*xt+Ut*Et,x3:dt*Et-T*xt,y3:dt*xt+T*Et,x4:N*Math.cos(O),y4:N*Math.sin(O)}},X=function(N){return 180*N/Math.PI},at=function(N,C,O,R,G,$){var it=N+.5*(O-N),st=C+.5*(R-C),Nt=G+.5*(O-G),Lt=$+.5*(R-$),It=Math.min(N,G,it,Nt),St=Math.max(N,G,it,Nt),Ut=Math.min(C,$,st,Lt),dt=Math.max(C,$,st,Lt);return new h(It,Ut,St-It,dt-Ut)},ft=function(N,C,O,R,G,$,it,st){var Nt,Lt,It,St,Ut,dt,T,Zt,Et,xt,At,Ct,kt,Tt,Yt=O-N,te=R-C,ee=G-O,ae=$-R,pe=it-G,Vt=st-$;for(Lt=0;Lt<41;Lt++)Et=(T=(It=N+(Nt=Lt/40)*Yt)+Nt*((Ut=O+Nt*ee)-It))+Nt*(Ut+Nt*(G+Nt*pe-Ut)-T),xt=(Zt=(St=C+Nt*te)+Nt*((dt=R+Nt*ae)-St))+Nt*(dt+Nt*($+Nt*Vt-dt)-Zt),Lt==0?(At=Et,Ct=xt,kt=Et,Tt=xt):(At=Math.min(At,Et),Ct=Math.min(Ct,xt),kt=Math.max(kt,Et),Tt=Math.max(Tt,xt));return new h(Math.round(At),Math.round(Ct),Math.round(kt-At),Math.round(Tt-Ct))},Ft=function(){if(this.prevLineDash||this.ctx.lineDash.length||this.ctx.lineDashOffset){var N,C,O=(N=this.ctx.lineDash,C=this.ctx.lineDashOffset,JSON.stringify({lineDash:N,lineDashOffset:C}));this.prevLineDash!==O&&(this.pdf.setLineDash(this.ctx.lineDash,this.ctx.lineDashOffset),this.prevLineDash=O)}}})(zt.API),function(n){var e=function(s){var c,h,f,g,b,w,S,p,M,I;for(h=[],f=0,g=(s+=c="\0\0\0\0".slice(s.length%4||4)).length;g>f;f+=4)(b=(s.charCodeAt(f)<<24)+(s.charCodeAt(f+1)<<16)+(s.charCodeAt(f+2)<<8)+s.charCodeAt(f+3))!==0?(w=(b=((b=((b=((b=(b-(I=b%85))/85)-(M=b%85))/85)-(p=b%85))/85)-(S=b%85))/85)%85,h.push(w+33,S+33,p+33,M+33,I+33)):h.push(122);return function(q,k){for(var E=k;E>0;E--)q.pop()}(h,c.length),String.fromCharCode.apply(String,h)+"~>"},r=function(s){var c,h,f,g,b,w=String,S="length",p=255,M="charCodeAt",I="slice",q="replace";for(s[I](-2),s=s[I](0,-2)[q](/\s/g,"")[q]("z","!!!!!"),f=[],g=0,b=(s+=c="uuuuu"[I](s[S]%5||5))[S];b>g;g+=5)h=52200625*(s[M](g)-33)+614125*(s[M](g+1)-33)+7225*(s[M](g+2)-33)+85*(s[M](g+3)-33)+(s[M](g+4)-33),f.push(p&h>>24,p&h>>16,p&h>>8,p&h);return function(k,E){for(var J=E;J>0;J--)k.pop()}(f,c[S]),w.fromCharCode.apply(w,f)},a=function(s){var c=new RegExp(/^([0-9A-Fa-f]{2})+$/);if((s=s.replace(/\s/g,"")).indexOf(">")!==-1&&(s=s.substr(0,s.indexOf(">"))),s.length%2&&(s+="0"),c.test(s)===!1)return"";for(var h="",f=0;f<s.length;f+=2)h+=String.fromCharCode("0x"+(s[f]+s[f+1]));return h},l=function(s){for(var c=new Uint8Array(s.length),h=s.length;h--;)c[h]=s.charCodeAt(h);return s=(c=Co(c)).reduce(function(f,g){return f+String.fromCharCode(g)},"")};n.processDataByFilters=function(s,c){var h=0,f=s||"",g=[];for(typeof(c=c||[])=="string"&&(c=[c]),h=0;h<c.length;h+=1)switch(c[h]){case"ASCII85Decode":case"/ASCII85Decode":f=r(f),g.push("/ASCII85Encode");break;case"ASCII85Encode":case"/ASCII85Encode":f=e(f),g.push("/ASCII85Decode");break;case"ASCIIHexDecode":case"/ASCIIHexDecode":f=a(f),g.push("/ASCIIHexEncode");break;case"ASCIIHexEncode":case"/ASCIIHexEncode":f=f.split("").map(function(b){return("0"+b.charCodeAt().toString(16)).slice(-2)}).join("")+">",g.push("/ASCIIHexDecode");break;case"FlateEncode":case"/FlateEncode":f=l(f),g.push("/FlateDecode");break;default:throw new Error('The filter: "'+c[h]+'" is not implemented')}return{data:f,reverseChain:g.reverse().join(" ")}}}(zt.API),function(n){n.loadFile=function(e,r,a){return function(l,s,c){s=s!==!1,c=typeof c=="function"?c:function(){};var h=void 0;try{h=function(f,g,b){var w=new XMLHttpRequest,S=0,p=function(M){var I=M.length,q=[],k=String.fromCharCode;for(S=0;S<I;S+=1)q.push(k(255&M.charCodeAt(S)));return q.join("")};if(w.open("GET",f,!g),w.overrideMimeType("text/plain; charset=x-user-defined"),g===!1&&(w.onload=function(){w.status===200?b(p(this.responseText)):b(void 0)}),w.send(null),g&&w.status===200)return p(w.responseText)}(l,s,c)}catch{}return h}(e,r,a)},n.loadImageFile=n.loadFile}(zt.API),function(n){function e(){return(Ht.html2canvas?Promise.resolve(Ht.html2canvas):So(()=>import("./html2canvas.esm.js"),[])).catch(function(c){return Promise.reject(new Error("Could not load html2canvas: "+c))}).then(function(c){return c.default?c.default:c})}function r(){return(Ht.DOMPurify?Promise.resolve(Ht.DOMPurify):So(()=>import("./purify.es.js"),[])).catch(function(c){return Promise.reject(new Error("Could not load dompurify: "+c))}).then(function(c){return c.default?c.default:c})}var a=function(c){var h=de(c);return h==="undefined"?"undefined":h==="string"||c instanceof String?"string":h==="number"||c instanceof Number?"number":h==="function"||c instanceof Function?"function":c&&c.constructor===Array?"array":c&&c.nodeType===1?"element":h==="object"?"object":"unknown"},l=function(c,h){var f=document.createElement(c);for(var g in h.className&&(f.className=h.className),h.innerHTML&&h.dompurify&&(f.innerHTML=h.dompurify.sanitize(h.innerHTML)),h.style)f.style[g]=h.style[g];return f},s=function c(h){var f=Object.assign(c.convert(Promise.resolve()),JSON.parse(JSON.stringify(c.template))),g=c.convert(Promise.resolve(),f);return g=(g=g.setProgress(1,c,1,[c])).set(h)};(s.prototype=Object.create(Promise.prototype)).constructor=s,s.convert=function(c,h){return c.__proto__=h||s.prototype,c},s.template={prop:{src:null,container:null,overlay:null,canvas:null,img:null,pdf:null,pageSize:null,callback:function(){}},progress:{val:0,state:null,n:0,stack:[]},opt:{filename:"file.pdf",margin:[0,0,0,0],enableLinks:!0,x:0,y:0,html2canvas:{},jsPDF:{},backgroundColor:"transparent"}},s.prototype.from=function(c,h){return this.then(function(){switch(h=h||function(f){switch(a(f)){case"string":return"string";case"element":return f.nodeName.toLowerCase()==="canvas"?"canvas":"element";default:return"unknown"}}(c)){case"string":return this.then(r).then(function(f){return this.set({src:l("div",{innerHTML:c,dompurify:f})})});case"element":return this.set({src:c});case"canvas":return this.set({canvas:c});case"img":return this.set({img:c});default:return this.error("Unknown source type.")}})},s.prototype.to=function(c){switch(c){case"container":return this.toContainer();case"canvas":return this.toCanvas();case"img":return this.toImg();case"pdf":return this.toPdf();default:return this.error("Invalid target.")}},s.prototype.toContainer=function(){return this.thenList([function(){return this.prop.src||this.error("Cannot duplicate - no source HTML.")},function(){return this.prop.pageSize||this.setPageSize()}]).then(function(){var c={position:"relative",display:"inline-block",width:(typeof this.opt.width!="number"||isNaN(this.opt.width)||typeof this.opt.windowWidth!="number"||isNaN(this.opt.windowWidth)?Math.max(this.prop.src.clientWidth,this.prop.src.scrollWidth,this.prop.src.offsetWidth):this.opt.windowWidth)+"px",left:0,right:0,top:0,margin:"auto",backgroundColor:this.opt.backgroundColor},h=function f(g,b){for(var w=g.nodeType===3?document.createTextNode(g.nodeValue):g.cloneNode(!1),S=g.firstChild;S;S=S.nextSibling)b!==!0&&S.nodeType===1&&S.nodeName==="SCRIPT"||w.appendChild(f(S,b));return g.nodeType===1&&(g.nodeName==="CANVAS"?(w.width=g.width,w.height=g.height,w.getContext("2d").drawImage(g,0,0)):g.nodeName!=="TEXTAREA"&&g.nodeName!=="SELECT"||(w.value=g.value),w.addEventListener("load",function(){w.scrollTop=g.scrollTop,w.scrollLeft=g.scrollLeft},!0)),w}(this.prop.src,this.opt.html2canvas.javascriptEnabled);h.tagName==="BODY"&&(c.height=Math.max(document.body.scrollHeight,document.body.offsetHeight,document.documentElement.clientHeight,document.documentElement.scrollHeight,document.documentElement.offsetHeight)+"px"),this.prop.overlay=l("div",{className:"html2pdf__overlay",style:{position:"fixed",overflow:"hidden",zIndex:1e3,left:"-100000px",right:0,bottom:0,top:0}}),this.prop.container=l("div",{className:"html2pdf__container",style:c}),this.prop.container.appendChild(h),this.prop.container.firstChild.appendChild(l("div",{style:{clear:"both",border:"0 none transparent",margin:0,padding:0,height:0}})),this.prop.container.style.float="none",this.prop.overlay.appendChild(this.prop.container),document.body.appendChild(this.prop.overlay),this.prop.container.firstChild.style.position="relative",this.prop.container.height=Math.max(this.prop.container.firstChild.clientHeight,this.prop.container.firstChild.scrollHeight,this.prop.container.firstChild.offsetHeight)+"px"})},s.prototype.toCanvas=function(){var c=[function(){return document.body.contains(this.prop.container)||this.toContainer()}];return this.thenList(c).then(e).then(function(h){var f=Object.assign({},this.opt.html2canvas);return delete f.onrendered,h(this.prop.container,f)}).then(function(h){(this.opt.html2canvas.onrendered||function(){})(h),this.prop.canvas=h,document.body.removeChild(this.prop.overlay)})},s.prototype.toContext2d=function(){var c=[function(){return document.body.contains(this.prop.container)||this.toContainer()}];return this.thenList(c).then(e).then(function(h){var f=this.opt.jsPDF,g=this.opt.fontFaces,b=typeof this.opt.width!="number"||isNaN(this.opt.width)||typeof this.opt.windowWidth!="number"||isNaN(this.opt.windowWidth)?1:this.opt.width/this.opt.windowWidth,w=Object.assign({async:!0,allowTaint:!0,scale:b,scrollX:this.opt.scrollX||0,scrollY:this.opt.scrollY||0,backgroundColor:"#ffffff",imageTimeout:15e3,logging:!0,proxy:null,removeContainer:!0,foreignObjectRendering:!1,useCORS:!1},this.opt.html2canvas);if(delete w.onrendered,f.context2d.autoPaging=this.opt.autoPaging===void 0||this.opt.autoPaging,f.context2d.posX=this.opt.x,f.context2d.posY=this.opt.y,f.context2d.margin=this.opt.margin,f.context2d.fontFaces=g,g)for(var S=0;S<g.length;++S){var p=g[S],M=p.src.find(function(I){return I.format==="truetype"});M&&f.addFont(M.url,p.ref.name,p.ref.style)}return w.windowHeight=w.windowHeight||0,w.windowHeight=w.windowHeight==0?Math.max(this.prop.container.clientHeight,this.prop.container.scrollHeight,this.prop.container.offsetHeight):w.windowHeight,f.context2d.save(!0),h(this.prop.container,w)}).then(function(h){this.opt.jsPDF.context2d.restore(!0),(this.opt.html2canvas.onrendered||function(){})(h),this.prop.canvas=h,document.body.removeChild(this.prop.overlay)})},s.prototype.toImg=function(){return this.thenList([function(){return this.prop.canvas||this.toCanvas()}]).then(function(){var c=this.prop.canvas.toDataURL("image/"+this.opt.image.type,this.opt.image.quality);this.prop.img=document.createElement("img"),this.prop.img.src=c})},s.prototype.toPdf=function(){return this.thenList([function(){return this.toContext2d()}]).then(function(){this.prop.pdf=this.prop.pdf||this.opt.jsPDF})},s.prototype.output=function(c,h,f){return(f=f||"pdf").toLowerCase()==="img"||f.toLowerCase()==="image"?this.outputImg(c,h):this.outputPdf(c,h)},s.prototype.outputPdf=function(c,h){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).then(function(){return this.prop.pdf.output(c,h)})},s.prototype.outputImg=function(c){return this.thenList([function(){return this.prop.img||this.toImg()}]).then(function(){switch(c){case void 0:case"img":return this.prop.img;case"datauristring":case"dataurlstring":return this.prop.img.src;case"datauri":case"dataurl":return document.location.href=this.prop.img.src;default:throw'Image output type "'+c+'" is not supported.'}})},s.prototype.save=function(c){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).set(c?{filename:c}:null).then(function(){this.prop.pdf.save(this.opt.filename)})},s.prototype.doCallback=function(){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).then(function(){this.prop.callback(this.prop.pdf)})},s.prototype.set=function(c){if(a(c)!=="object")return this;var h=Object.keys(c||{}).map(function(f){if(f in s.template.prop)return function(){this.prop[f]=c[f]};switch(f){case"margin":return this.setMargin.bind(this,c.margin);case"jsPDF":return function(){return this.opt.jsPDF=c.jsPDF,this.setPageSize()};case"pageSize":return this.setPageSize.bind(this,c.pageSize);default:return function(){this.opt[f]=c[f]}}},this);return this.then(function(){return this.thenList(h)})},s.prototype.get=function(c,h){return this.then(function(){var f=c in s.template.prop?this.prop[c]:this.opt[c];return h?h(f):f})},s.prototype.setMargin=function(c){return this.then(function(){switch(a(c)){case"number":c=[c,c,c,c];case"array":if(c.length===2&&(c=[c[0],c[1],c[0],c[1]]),c.length===4)break;default:return this.error("Invalid margin array.")}this.opt.margin=c}).then(this.setPageSize)},s.prototype.setPageSize=function(c){function h(f,g){return Math.floor(f*g/72*96)}return this.then(function(){(c=c||zt.getPageSize(this.opt.jsPDF)).hasOwnProperty("inner")||(c.inner={width:c.width-this.opt.margin[1]-this.opt.margin[3],height:c.height-this.opt.margin[0]-this.opt.margin[2]},c.inner.px={width:h(c.inner.width,c.k),height:h(c.inner.height,c.k)},c.inner.ratio=c.inner.height/c.inner.width),this.prop.pageSize=c})},s.prototype.setProgress=function(c,h,f,g){return c!=null&&(this.progress.val=c),h!=null&&(this.progress.state=h),f!=null&&(this.progress.n=f),g!=null&&(this.progress.stack=g),this.progress.ratio=this.progress.val/this.progress.state,this},s.prototype.updateProgress=function(c,h,f,g){return this.setProgress(c?this.progress.val+c:null,h||null,f?this.progress.n+f:null,g?this.progress.stack.concat(g):null)},s.prototype.then=function(c,h){var f=this;return this.thenCore(c,h,function(g,b){return f.updateProgress(null,null,1,[g]),Promise.prototype.then.call(this,function(w){return f.updateProgress(null,g),w}).then(g,b).then(function(w){return f.updateProgress(1),w})})},s.prototype.thenCore=function(c,h,f){f=f||Promise.prototype.then,c&&(c=c.bind(this)),h&&(h=h.bind(this));var g=Promise.toString().indexOf("[native code]")!==-1&&Promise.name==="Promise"?this:s.convert(Object.assign({},this),Promise.prototype),b=f.call(g,c,h);return s.convert(b,this.__proto__)},s.prototype.thenExternal=function(c,h){return Promise.prototype.then.call(this,c,h)},s.prototype.thenList=function(c){var h=this;return c.forEach(function(f){h=h.thenCore(f)}),h},s.prototype.catch=function(c){c&&(c=c.bind(this));var h=Promise.prototype.catch.call(this,c);return s.convert(h,this)},s.prototype.catchExternal=function(c){return Promise.prototype.catch.call(this,c)},s.prototype.error=function(c){return this.then(function(){throw new Error(c)})},s.prototype.using=s.prototype.set,s.prototype.saveAs=s.prototype.save,s.prototype.export=s.prototype.output,s.prototype.run=s.prototype.then,zt.getPageSize=function(c,h,f){if(de(c)==="object"){var g=c;c=g.orientation,h=g.unit||h,f=g.format||f}h=h||"mm",f=f||"a4",c=(""+(c||"P")).toLowerCase();var b,w=(""+f).toLowerCase(),S={a0:[2383.94,3370.39],a1:[1683.78,2383.94],a2:[1190.55,1683.78],a3:[841.89,1190.55],a4:[595.28,841.89],a5:[419.53,595.28],a6:[297.64,419.53],a7:[209.76,297.64],a8:[147.4,209.76],a9:[104.88,147.4],a10:[73.7,104.88],b0:[2834.65,4008.19],b1:[2004.09,2834.65],b2:[1417.32,2004.09],b3:[1000.63,1417.32],b4:[708.66,1000.63],b5:[498.9,708.66],b6:[354.33,498.9],b7:[249.45,354.33],b8:[175.75,249.45],b9:[124.72,175.75],b10:[87.87,124.72],c0:[2599.37,3676.54],c1:[1836.85,2599.37],c2:[1298.27,1836.85],c3:[918.43,1298.27],c4:[649.13,918.43],c5:[459.21,649.13],c6:[323.15,459.21],c7:[229.61,323.15],c8:[161.57,229.61],c9:[113.39,161.57],c10:[79.37,113.39],dl:[311.81,623.62],letter:[612,792],"government-letter":[576,756],legal:[612,1008],"junior-legal":[576,360],ledger:[1224,792],tabloid:[792,1224],"credit-card":[153,243]};switch(h){case"pt":b=1;break;case"mm":b=72/25.4;break;case"cm":b=72/2.54;break;case"in":b=72;break;case"px":b=.75;break;case"pc":case"em":b=12;break;case"ex":b=6;break;default:throw"Invalid unit: "+h}var p,M=0,I=0;if(S.hasOwnProperty(w))M=S[w][1]/b,I=S[w][0]/b;else try{M=f[1],I=f[0]}catch{throw new Error("Invalid format: "+f)}if(c==="p"||c==="portrait")c="p",I>M&&(p=I,I=M,M=p);else{if(c!=="l"&&c!=="landscape")throw"Invalid orientation: "+c;c="l",M>I&&(p=I,I=M,M=p)}return{width:I,height:M,unit:h,k:b,orientation:c}},n.html=function(c,h){(h=h||{}).callback=h.callback||function(){},h.html2canvas=h.html2canvas||{},h.html2canvas.canvas=h.html2canvas.canvas||this.canvas,h.jsPDF=h.jsPDF||this,h.fontFaces=h.fontFaces?h.fontFaces.map(To):null;var f=new s(h);return h.worker?f:f.from(c).doCallback()}}(zt.API),zt.API.addJS=function(n){return jl=n,this.internal.events.subscribe("postPutResources",function(){ps=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/Names [(EmbeddedJS) "+(ps+1)+" 0 R]"),this.internal.out(">>"),this.internal.out("endobj"),Cl=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/S /JavaScript"),this.internal.out("/JS ("+jl+")"),this.internal.out(">>"),this.internal.out("endobj")}),this.internal.events.subscribe("putCatalog",function(){ps!==void 0&&Cl!==void 0&&this.internal.out("/Names <</JavaScript "+ps+" 0 R>>")}),this},function(n){var e;n.events.push(["postPutResources",function(){var r=this,a=/^(\d+) 0 obj$/;if(this.outline.root.children.length>0)for(var l=r.outline.render().split(/\r\n/),s=0;s<l.length;s++){var c=l[s],h=a.exec(c);if(h!=null){var f=h[1];r.internal.newObjectDeferredBegin(f,!1)}r.internal.write(c)}if(this.outline.createNamedDestinations){var g=this.internal.pages.length,b=[];for(s=0;s<g;s++){var w=r.internal.newObject();b.push(w);var S=r.internal.getPageInfo(s+1);r.internal.write("<< /D["+S.objId+" 0 R /XYZ null null null]>> endobj")}var p=r.internal.newObject();for(r.internal.write("<< /Names [ "),s=0;s<b.length;s++)r.internal.write("(page_"+(s+1)+")"+b[s]+" 0 R");r.internal.write(" ] >>","endobj"),e=r.internal.newObject(),r.internal.write("<< /Dests "+p+" 0 R"),r.internal.write(">>","endobj")}}]),n.events.push(["putCatalog",function(){this.outline.root.children.length>0&&(this.internal.write("/Outlines",this.outline.makeRef(this.outline.root)),this.outline.createNamedDestinations&&this.internal.write("/Names "+e+" 0 R"))}]),n.events.push(["initialized",function(){var r=this;r.outline={createNamedDestinations:!1,root:{children:[]}},r.outline.add=function(a,l,s){var c={title:l,options:s,children:[]};return a==null&&(a=this.root),a.children.push(c),c},r.outline.render=function(){return this.ctx={},this.ctx.val="",this.ctx.pdf=r,this.genIds_r(this.root),this.renderRoot(this.root),this.renderItems(this.root),this.ctx.val},r.outline.genIds_r=function(a){a.id=r.internal.newObjectDeferred();for(var l=0;l<a.children.length;l++)this.genIds_r(a.children[l])},r.outline.renderRoot=function(a){this.objStart(a),this.line("/Type /Outlines"),a.children.length>0&&(this.line("/First "+this.makeRef(a.children[0])),this.line("/Last "+this.makeRef(a.children[a.children.length-1]))),this.line("/Count "+this.count_r({count:0},a)),this.objEnd()},r.outline.renderItems=function(a){for(var l=this.ctx.pdf.internal.getVerticalCoordinateString,s=0;s<a.children.length;s++){var c=a.children[s];this.objStart(c),this.line("/Title "+this.makeString(c.title)),this.line("/Parent "+this.makeRef(a)),s>0&&this.line("/Prev "+this.makeRef(a.children[s-1])),s<a.children.length-1&&this.line("/Next "+this.makeRef(a.children[s+1])),c.children.length>0&&(this.line("/First "+this.makeRef(c.children[0])),this.line("/Last "+this.makeRef(c.children[c.children.length-1])));var h=this.count=this.count_r({count:0},c);if(h>0&&this.line("/Count "+h),c.options&&c.options.pageNumber){var f=r.internal.getPageInfo(c.options.pageNumber);this.line("/Dest ["+f.objId+" 0 R /XYZ 0 "+l(0)+" 0]")}this.objEnd()}for(var g=0;g<a.children.length;g++)this.renderItems(a.children[g])},r.outline.line=function(a){this.ctx.val+=a+`\r
`},r.outline.makeRef=function(a){return a.id+" 0 R"},r.outline.makeString=function(a){return"("+r.internal.pdfEscape(a)+")"},r.outline.objStart=function(a){this.ctx.val+=`\r
`+a.id+` 0 obj\r
<<\r
`},r.outline.objEnd=function(){this.ctx.val+=`>> \r
endobj\r
`},r.outline.count_r=function(a,l){for(var s=0;s<l.children.length;s++)a.count++,this.count_r(a,l.children[s]);return a.count}}])}(zt.API),function(n){var e=[192,193,194,195,196,197,198,199];n.processJPEG=function(r,a,l,s,c,h){var f,g=this.decode.DCT_DECODE,b=null;if(typeof r=="string"||this.__addimage__.isArrayBuffer(r)||this.__addimage__.isArrayBufferView(r)){switch(r=c||r,r=this.__addimage__.isArrayBuffer(r)?new Uint8Array(r):r,(f=function(w){for(var S,p=256*w.charCodeAt(4)+w.charCodeAt(5),M=w.length,I={width:0,height:0,numcomponents:1},q=4;q<M;q+=2){if(q+=p,e.indexOf(w.charCodeAt(q+1))!==-1){S=256*w.charCodeAt(q+5)+w.charCodeAt(q+6),I={width:256*w.charCodeAt(q+7)+w.charCodeAt(q+8),height:S,numcomponents:w.charCodeAt(q+9)};break}p=256*w.charCodeAt(q+2)+w.charCodeAt(q+3)}return I}(r=this.__addimage__.isArrayBufferView(r)?this.__addimage__.arrayBufferToBinaryString(r):r)).numcomponents){case 1:h=this.color_spaces.DEVICE_GRAY;break;case 4:h=this.color_spaces.DEVICE_CMYK;break;case 3:h=this.color_spaces.DEVICE_RGB}b={data:r,width:f.width,height:f.height,colorSpace:h,bitsPerComponent:8,filter:g,index:a,alias:l}}return b}}(zt.API);var ji,gs,Ol,Ml,Bl,jc=function(){var n,e,r;function a(s){var c,h,f,g,b,w,S,p,M,I,q,k,E,J;for(this.data=s,this.pos=8,this.palette=[],this.imgData=[],this.transparency={},this.animation=null,this.text={},w=null;;){switch(c=this.readUInt32(),M=(function(){var ot,ut;for(ut=[],ot=0;ot<4;++ot)ut.push(String.fromCharCode(this.data[this.pos++]));return ut}).call(this).join("")){case"IHDR":this.width=this.readUInt32(),this.height=this.readUInt32(),this.bits=this.data[this.pos++],this.colorType=this.data[this.pos++],this.compressionMethod=this.data[this.pos++],this.filterMethod=this.data[this.pos++],this.interlaceMethod=this.data[this.pos++];break;case"acTL":this.animation={numFrames:this.readUInt32(),numPlays:this.readUInt32()||1/0,frames:[]};break;case"PLTE":this.palette=this.read(c);break;case"fcTL":w&&this.animation.frames.push(w),this.pos+=4,w={width:this.readUInt32(),height:this.readUInt32(),xOffset:this.readUInt32(),yOffset:this.readUInt32()},b=this.readUInt16(),g=this.readUInt16()||100,w.delay=1e3*b/g,w.disposeOp=this.data[this.pos++],w.blendOp=this.data[this.pos++],w.data=[];break;case"IDAT":case"fdAT":for(M==="fdAT"&&(this.pos+=4,c-=4),s=(w!=null?w.data:void 0)||this.imgData,k=0;0<=c?k<c:k>c;0<=c?++k:--k)s.push(this.data[this.pos++]);break;case"tRNS":switch(this.transparency={},this.colorType){case 3:if(f=this.palette.length/3,this.transparency.indexed=this.read(c),this.transparency.indexed.length>f)throw new Error("More transparent colors than palette size");if((I=f-this.transparency.indexed.length)>0)for(E=0;0<=I?E<I:E>I;0<=I?++E:--E)this.transparency.indexed.push(255);break;case 0:this.transparency.grayscale=this.read(c)[0];break;case 2:this.transparency.rgb=this.read(c)}break;case"tEXt":S=(q=this.read(c)).indexOf(0),p=String.fromCharCode.apply(String,q.slice(0,S)),this.text[p]=String.fromCharCode.apply(String,q.slice(S+1));break;case"IEND":return w&&this.animation.frames.push(w),this.colors=(function(){switch(this.colorType){case 0:case 3:case 4:return 1;case 2:case 6:return 3}}).call(this),this.hasAlphaChannel=(J=this.colorType)===4||J===6,h=this.colors+(this.hasAlphaChannel?1:0),this.pixelBitlength=this.bits*h,this.colorSpace=(function(){switch(this.colors){case 1:return"DeviceGray";case 3:return"DeviceRGB"}}).call(this),void(this.imgData=new Uint8Array(this.imgData));default:this.pos+=c}if(this.pos+=4,this.pos>this.data.length)throw new Error("Incomplete or corrupt PNG file")}}a.prototype.read=function(s){var c,h;for(h=[],c=0;0<=s?c<s:c>s;0<=s?++c:--c)h.push(this.data[this.pos++]);return h},a.prototype.readUInt32=function(){return this.data[this.pos++]<<24|this.data[this.pos++]<<16|this.data[this.pos++]<<8|this.data[this.pos++]},a.prototype.readUInt16=function(){return this.data[this.pos++]<<8|this.data[this.pos++]},a.prototype.decodePixels=function(s){var c=this.pixelBitlength/8,h=new Uint8Array(this.width*this.height*c),f=0,g=this;if(s==null&&(s=this.imgData),s.length===0)return new Uint8Array(0);function b(w,S,p,M){var I,q,k,E,J,ot,ut,yt,tt,U,nt,pt,L,A,B,D,et,rt,ct,X,at,ft=Math.ceil((g.width-w)/p),Ft=Math.ceil((g.height-S)/M),N=g.width==ft&&g.height==Ft;for(A=c*ft,pt=N?h:new Uint8Array(A*Ft),ot=s.length,L=0,q=0;L<Ft&&f<ot;){switch(s[f++]){case 0:for(E=et=0;et<A;E=et+=1)pt[q++]=s[f++];break;case 1:for(E=rt=0;rt<A;E=rt+=1)I=s[f++],J=E<c?0:pt[q-c],pt[q++]=(I+J)%256;break;case 2:for(E=ct=0;ct<A;E=ct+=1)I=s[f++],k=(E-E%c)/c,B=L&&pt[(L-1)*A+k*c+E%c],pt[q++]=(B+I)%256;break;case 3:for(E=X=0;X<A;E=X+=1)I=s[f++],k=(E-E%c)/c,J=E<c?0:pt[q-c],B=L&&pt[(L-1)*A+k*c+E%c],pt[q++]=(I+Math.floor((J+B)/2))%256;break;case 4:for(E=at=0;at<A;E=at+=1)I=s[f++],k=(E-E%c)/c,J=E<c?0:pt[q-c],L===0?B=D=0:(B=pt[(L-1)*A+k*c+E%c],D=k&&pt[(L-1)*A+(k-1)*c+E%c]),ut=J+B-D,yt=Math.abs(ut-J),U=Math.abs(ut-B),nt=Math.abs(ut-D),tt=yt<=U&&yt<=nt?J:U<=nt?B:D,pt[q++]=(I+tt)%256;break;default:throw new Error("Invalid filter algorithm: "+s[f-1])}if(!N){var C=((S+L*M)*g.width+w)*c,O=L*A;for(E=0;E<ft;E+=1){for(var R=0;R<c;R+=1)h[C++]=pt[O++];C+=(p-1)*c}}L++}}return s=cc(s),g.interlaceMethod==1?(b(0,0,8,8),b(4,0,8,8),b(0,4,4,8),b(2,0,4,4),b(0,2,2,4),b(1,0,2,2),b(0,1,1,2)):b(0,0,1,1),h},a.prototype.decodePalette=function(){var s,c,h,f,g,b,w,S,p;for(h=this.palette,b=this.transparency.indexed||[],g=new Uint8Array((b.length||0)+h.length),f=0,s=0,c=w=0,S=h.length;w<S;c=w+=3)g[f++]=h[c],g[f++]=h[c+1],g[f++]=h[c+2],g[f++]=(p=b[s++])!=null?p:255;return g},a.prototype.copyToImageData=function(s,c){var h,f,g,b,w,S,p,M,I,q,k;if(f=this.colors,I=null,h=this.hasAlphaChannel,this.palette.length&&(I=(k=this._decodedPalette)!=null?k:this._decodedPalette=this.decodePalette(),f=4,h=!0),M=(g=s.data||s).length,w=I||c,b=S=0,f===1)for(;b<M;)p=I?4*c[b/4]:S,q=w[p++],g[b++]=q,g[b++]=q,g[b++]=q,g[b++]=h?w[p++]:255,S=p;else for(;b<M;)p=I?4*c[b/4]:S,g[b++]=w[p++],g[b++]=w[p++],g[b++]=w[p++],g[b++]=h?w[p++]:255,S=p},a.prototype.decode=function(){var s;return s=new Uint8Array(this.width*this.height*4),this.copyToImageData(s,this.decodePixels()),s};var l=function(){if(Object.prototype.toString.call(Ht)==="[object Window]"){try{e=Ht.document.createElement("canvas"),r=e.getContext("2d")}catch{return!1}return!0}return!1};return l(),n=function(s){var c;if(l()===!0)return r.width=s.width,r.height=s.height,r.clearRect(0,0,s.width,s.height),r.putImageData(s,0,0),(c=new Image).src=e.toDataURL(),c;throw new Error("This method requires a Browser with Canvas-capability.")},a.prototype.decodeFrames=function(s){var c,h,f,g,b,w,S,p;if(this.animation){for(p=[],h=b=0,w=(S=this.animation.frames).length;b<w;h=++b)c=S[h],f=s.createImageData(c.width,c.height),g=this.decodePixels(new Uint8Array(c.data)),this.copyToImageData(f,g),c.imageData=f,p.push(c.image=n(f));return p}},a.prototype.renderFrame=function(s,c){var h,f,g;return h=(f=this.animation.frames)[c],g=f[c-1],c===0&&s.clearRect(0,0,this.width,this.height),(g!=null?g.disposeOp:void 0)===1?s.clearRect(g.xOffset,g.yOffset,g.width,g.height):(g!=null?g.disposeOp:void 0)===2&&s.putImageData(g.imageData,g.xOffset,g.yOffset),h.blendOp===0&&s.clearRect(h.xOffset,h.yOffset,h.width,h.height),s.drawImage(h.image,h.xOffset,h.yOffset)},a.prototype.animate=function(s){var c,h,f,g,b,w,S=this;return h=0,w=this.animation,g=w.numFrames,f=w.frames,b=w.numPlays,(c=function(){var p,M;if(p=h++%g,M=f[p],S.renderFrame(s,p),g>1&&h/g<b)return S.animation._timeout=setTimeout(c,M.delay)})()},a.prototype.stopAnimation=function(){var s;return clearTimeout((s=this.animation)!=null?s._timeout:void 0)},a.prototype.render=function(s){var c,h;return s._png&&s._png.stopAnimation(),s._png=this,s.width=this.width,s.height=this.height,c=s.getContext("2d"),this.animation?(this.decodeFrames(c),this.animate(c)):(h=c.createImageData(this.width,this.height),this.copyToImageData(h,this.decodePixels()),c.putImageData(h,0,0))},a}();/**
 * @license
 *
 * Copyright (c) 2014 James Robb, https://github.com/jamesbrobb
 *
 * Permission is hereby granted, free of charge, to any person obtaining
 * a copy of this software and associated documentation files (the
 * "Software"), to deal in the Software without restriction, including
 * without limitation the rights to use, copy, modify, merge, publish,
 * distribute, sublicense, and/or sell copies of the Software, and to
 * permit persons to whom the Software is furnished to do so, subject to
 * the following conditions:
 *
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
 * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
 * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
 * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 * ====================================================================
 *//**
 * @license
 * (c) Dean McNamee <<EMAIL>>, 2013.
 *
 * https://github.com/deanm/omggif
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to
 * deal in the Software without restriction, including without limitation the
 * rights to use, copy, modify, merge, publish, distribute, sublicense, and/or
 * sell copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 * FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
 * IN THE SOFTWARE.
 *
 * omggif is a JavaScript implementation of a GIF 89a encoder and decoder,
 * including animation and compression.  It does not rely on any specific
 * underlying system, so should run in the browser, Node, or Plask.
 */function Oc(n){var e=0;if(n[e++]!==71||n[e++]!==73||n[e++]!==70||n[e++]!==56||(n[e++]+1&253)!=56||n[e++]!==97)throw new Error("Invalid GIF 87a/89a header.");var r=n[e++]|n[e++]<<8,a=n[e++]|n[e++]<<8,l=n[e++],s=l>>7,c=1<<(7&l)+1;n[e++],n[e++];var h=null,f=null;s&&(h=e,f=c,e+=3*c);var g=!0,b=[],w=0,S=null,p=0,M=null;for(this.width=r,this.height=a;g&&e<n.length;)switch(n[e++]){case 33:switch(n[e++]){case 255:if(n[e]!==11||n[e+1]==78&&n[e+2]==69&&n[e+3]==84&&n[e+4]==83&&n[e+5]==67&&n[e+6]==65&&n[e+7]==80&&n[e+8]==69&&n[e+9]==50&&n[e+10]==46&&n[e+11]==48&&n[e+12]==3&&n[e+13]==1&&n[e+16]==0)e+=14,M=n[e++]|n[e++]<<8,e++;else for(e+=12;;){if(!((L=n[e++])>=0))throw Error("Invalid block size");if(L===0)break;e+=L}break;case 249:if(n[e++]!==4||n[e+4]!==0)throw new Error("Invalid graphics extension block.");var I=n[e++];w=n[e++]|n[e++]<<8,S=n[e++],!(1&I)&&(S=null),p=I>>2&7,e++;break;case 254:for(;;){if(!((L=n[e++])>=0))throw Error("Invalid block size");if(L===0)break;e+=L}break;default:throw new Error("Unknown graphic control label: 0x"+n[e-1].toString(16))}break;case 44:var q=n[e++]|n[e++]<<8,k=n[e++]|n[e++]<<8,E=n[e++]|n[e++]<<8,J=n[e++]|n[e++]<<8,ot=n[e++],ut=ot>>6&1,yt=1<<(7&ot)+1,tt=h,U=f,nt=!1;ot>>7&&(nt=!0,tt=e,U=yt,e+=3*yt);var pt=e;for(e++;;){var L;if(!((L=n[e++])>=0))throw Error("Invalid block size");if(L===0)break;e+=L}b.push({x:q,y:k,width:E,height:J,has_local_palette:nt,palette_offset:tt,palette_size:U,data_offset:pt,data_length:e-pt,transparent_index:S,interlaced:!!ut,delay:w,disposal:p});break;case 59:g=!1;break;default:throw new Error("Unknown gif block: 0x"+n[e-1].toString(16))}this.numFrames=function(){return b.length},this.loopCount=function(){return M},this.frameInfo=function(A){if(A<0||A>=b.length)throw new Error("Frame index out of range.");return b[A]},this.decodeAndBlitFrameBGRA=function(A,B){var D=this.frameInfo(A),et=D.width*D.height,rt=new Uint8Array(et);El(n,D.data_offset,rt,et);var ct=D.palette_offset,X=D.transparent_index;X===null&&(X=256);var at=D.width,ft=r-at,Ft=at,N=4*(D.y*r+D.x),C=4*((D.y+D.height)*r+D.x),O=N,R=4*ft;D.interlaced===!0&&(R+=4*r*7);for(var G=8,$=0,it=rt.length;$<it;++$){var st=rt[$];if(Ft===0&&(Ft=at,(O+=R)>=C&&(R=4*ft+4*r*(G-1),O=N+(at+ft)*(G<<1),G>>=1)),st===X)O+=4;else{var Nt=n[ct+3*st],Lt=n[ct+3*st+1],It=n[ct+3*st+2];B[O++]=It,B[O++]=Lt,B[O++]=Nt,B[O++]=255}--Ft}},this.decodeAndBlitFrameRGBA=function(A,B){var D=this.frameInfo(A),et=D.width*D.height,rt=new Uint8Array(et);El(n,D.data_offset,rt,et);var ct=D.palette_offset,X=D.transparent_index;X===null&&(X=256);var at=D.width,ft=r-at,Ft=at,N=4*(D.y*r+D.x),C=4*((D.y+D.height)*r+D.x),O=N,R=4*ft;D.interlaced===!0&&(R+=4*r*7);for(var G=8,$=0,it=rt.length;$<it;++$){var st=rt[$];if(Ft===0&&(Ft=at,(O+=R)>=C&&(R=4*ft+4*r*(G-1),O=N+(at+ft)*(G<<1),G>>=1)),st===X)O+=4;else{var Nt=n[ct+3*st],Lt=n[ct+3*st+1],It=n[ct+3*st+2];B[O++]=Nt,B[O++]=Lt,B[O++]=It,B[O++]=255}--Ft}}}function El(n,e,r,a){for(var l=n[e++],s=1<<l,c=s+1,h=c+1,f=l+1,g=(1<<f)-1,b=0,w=0,S=0,p=n[e++],M=new Int32Array(4096),I=null;;){for(;b<16&&p!==0;)w|=n[e++]<<b,b+=8,p===1?p=n[e++]:--p;if(b<f)break;var q=w&g;if(w>>=f,b-=f,q!==s){if(q===c)break;for(var k=q<h?q:I,E=0,J=k;J>s;)J=M[J]>>8,++E;var ot=J;if(S+E+(k!==q?1:0)>a)return void be.log("Warning, gif stream longer than expected.");r[S++]=ot;var ut=S+=E;for(k!==q&&(r[S++]=ot),J=k;E--;)J=M[J],r[--ut]=255&J,J>>=8;I!==null&&h<4096&&(M[h++]=I<<8|ot,h>=g+1&&f<12&&(++f,g=g<<1|1)),I=q}else h=c+1,g=(1<<(f=l+1))-1,I=null}return S!==a&&be.log("Warning, gif stream shorter than expected."),r}/**
 * @license
  Copyright (c) 2008, Adobe Systems Incorporated
  All rights reserved.

  Redistribution and use in source and binary forms, with or without 
  modification, are permitted provided that the following conditions are
  met:

  * Redistributions of source code must retain the above copyright notice, 
    this list of conditions and the following disclaimer.
  
  * Redistributions in binary form must reproduce the above copyright
    notice, this list of conditions and the following disclaimer in the 
    documentation and/or other materials provided with the distribution.
  
  * Neither the name of Adobe Systems Incorporated nor the names of its 
    contributors may be used to endorse or promote products derived from 
    this software without specific prior written permission.

  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS
  IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
  THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
  PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR 
  CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
  EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
  PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
  LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
  NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
  SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
*/function _o(n){var e,r,a,l,s,c=Math.floor,h=new Array(64),f=new Array(64),g=new Array(64),b=new Array(64),w=new Array(65535),S=new Array(65535),p=new Array(64),M=new Array(64),I=[],q=0,k=7,E=new Array(64),J=new Array(64),ot=new Array(64),ut=new Array(256),yt=new Array(2048),tt=[0,1,5,6,14,15,27,28,2,4,7,13,16,26,29,42,3,8,12,17,25,30,41,43,9,11,18,24,31,40,44,53,10,19,23,32,39,45,52,54,20,22,33,38,46,51,55,60,21,34,37,47,50,56,59,61,35,36,48,49,57,58,62,63],U=[0,0,1,5,1,1,1,1,1,1,0,0,0,0,0,0,0],nt=[0,1,2,3,4,5,6,7,8,9,10,11],pt=[0,0,2,1,3,3,2,4,3,5,5,4,4,0,0,1,125],L=[1,2,3,0,4,17,5,18,33,49,65,6,19,81,97,7,34,113,20,50,129,145,161,8,35,66,177,193,21,82,209,240,36,51,98,114,130,9,10,22,23,24,25,26,37,38,39,40,41,42,52,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,225,226,227,228,229,230,231,232,233,234,241,242,243,244,245,246,247,248,249,250],A=[0,0,3,1,1,1,1,1,1,1,1,1,0,0,0,0,0],B=[0,1,2,3,4,5,6,7,8,9,10,11],D=[0,0,2,1,2,4,4,3,4,7,5,4,4,0,1,2,119],et=[0,1,2,3,17,4,5,33,49,6,18,65,81,7,97,113,19,34,50,129,8,20,66,145,161,177,193,9,35,51,82,240,21,98,114,209,10,22,36,52,225,37,241,23,24,25,26,38,39,40,41,42,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,130,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,226,227,228,229,230,231,232,233,234,242,243,244,245,246,247,248,249,250];function rt(N,C){for(var O=0,R=0,G=new Array,$=1;$<=16;$++){for(var it=1;it<=N[$];it++)G[C[R]]=[],G[C[R]][0]=O,G[C[R]][1]=$,R++,O++;O*=2}return G}function ct(N){for(var C=N[0],O=N[1]-1;O>=0;)C&1<<O&&(q|=1<<k),O--,--k<0&&(q==255?(X(255),X(0)):X(q),k=7,q=0)}function X(N){I.push(N)}function at(N){X(N>>8&255),X(255&N)}function ft(N,C,O,R,G){for(var $,it=G[0],st=G[240],Nt=function(xt,At){var Ct,kt,Tt,Yt,te,ee,ae,pe,Vt,re,jt=0;for(Vt=0;Vt<8;++Vt){Ct=xt[jt],kt=xt[jt+1],Tt=xt[jt+2],Yt=xt[jt+3],te=xt[jt+4],ee=xt[jt+5],ae=xt[jt+6];var Je=Ct+(pe=xt[jt+7]),oe=Ct-pe,Pr=kt+ae,me=kt-ae,Le=Tt+ee,Vr=Tt-ee,ue=Yt+te,qn=Yt-te,Ae=Je+ue,kr=Je-ue,sn=Pr+Le,_e=Pr-Le;xt[jt]=Ae+sn,xt[jt+4]=Ae-sn;var Jt=.707106781*(_e+kr);xt[jt+2]=kr+Jt,xt[jt+6]=kr-Jt;var ce=.382683433*((Ae=qn+Vr)-(_e=me+oe)),Dn=.5411961*Ae+ce,Ve=1.306562965*_e+ce,Gr=.707106781*(sn=Vr+me),Yr=oe+Gr,Rt=oe-Gr;xt[jt+5]=Rt+Dn,xt[jt+3]=Rt-Dn,xt[jt+1]=Yr+Ve,xt[jt+7]=Yr-Ve,jt+=8}for(jt=0,Vt=0;Vt<8;++Vt){Ct=xt[jt],kt=xt[jt+8],Tt=xt[jt+16],Yt=xt[jt+24],te=xt[jt+32],ee=xt[jt+40],ae=xt[jt+48];var Ir=Ct+(pe=xt[jt+56]),Jr=Ct-pe,ar=kt+ae,De=kt-ae,Be=Tt+ee,dr=Tt-ee,ei=Yt+te,on=Yt-te,Fr=Ir+ei,Cr=Ir-ei,jr=ar+Be,Xr=ar-Be;xt[jt]=Fr+jr,xt[jt+32]=Fr-jr;var yr=.707106781*(Xr+Cr);xt[jt+16]=Cr+yr,xt[jt+48]=Cr-yr;var Kr=.382683433*((Fr=on+dr)-(Xr=De+Jr)),Rn=.5411961*Fr+Kr,ri=1.306562965*Xr+Kr,ni=.707106781*(jr=dr+De),ii=Jr+ni,ai=Jr-ni;xt[jt+40]=ai+Rn,xt[jt+24]=ai-Rn,xt[jt+8]=ii+ri,xt[jt+56]=ii-ri,jt++}for(Vt=0;Vt<64;++Vt)re=xt[Vt]*At[Vt],p[Vt]=re>0?re+.5|0:re-.5|0;return p}(N,C),Lt=0;Lt<64;++Lt)M[tt[Lt]]=Nt[Lt];var It=M[0]-O;O=M[0],It==0?ct(R[0]):(ct(R[S[$=32767+It]]),ct(w[$]));for(var St=63;St>0&&M[St]==0;)St--;if(St==0)return ct(it),O;for(var Ut,dt=1;dt<=St;){for(var T=dt;M[dt]==0&&dt<=St;)++dt;var Zt=dt-T;if(Zt>=16){Ut=Zt>>4;for(var Et=1;Et<=Ut;++Et)ct(st);Zt&=15}$=32767+M[dt],ct(G[(Zt<<4)+S[$]]),ct(w[$]),dt++}return St!=63&&ct(it),O}function Ft(N){N=Math.min(Math.max(N,1),100),s!=N&&(function(C){for(var O=[16,11,10,16,24,40,51,61,12,12,14,19,26,58,60,55,14,13,16,24,40,57,69,56,14,17,22,29,51,87,80,62,18,22,37,56,68,109,103,77,24,35,55,64,81,104,113,92,49,64,78,87,103,121,120,101,72,92,95,98,112,100,103,99],R=0;R<64;R++){var G=c((O[R]*C+50)/100);G=Math.min(Math.max(G,1),255),h[tt[R]]=G}for(var $=[17,18,24,47,99,99,99,99,18,21,26,66,99,99,99,99,24,26,56,99,99,99,99,99,47,66,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99],it=0;it<64;it++){var st=c(($[it]*C+50)/100);st=Math.min(Math.max(st,1),255),f[tt[it]]=st}for(var Nt=[1,1.387039845,1.306562965,1.175875602,1,.785694958,.5411961,.275899379],Lt=0,It=0;It<8;It++)for(var St=0;St<8;St++)g[Lt]=1/(h[tt[Lt]]*Nt[It]*Nt[St]*8),b[Lt]=1/(f[tt[Lt]]*Nt[It]*Nt[St]*8),Lt++}(N<50?Math.floor(5e3/N):Math.floor(200-2*N)),s=N)}this.encode=function(N,C){C&&Ft(C),I=new Array,q=0,k=7,at(65496),at(65504),at(16),X(74),X(70),X(73),X(70),X(0),X(1),X(1),X(0),at(1),at(1),X(0),X(0),function(){at(65499),at(132),X(0);for(var kt=0;kt<64;kt++)X(h[kt]);X(1);for(var Tt=0;Tt<64;Tt++)X(f[Tt])}(),function(kt,Tt){at(65472),at(17),X(8),at(Tt),at(kt),X(3),X(1),X(17),X(0),X(2),X(17),X(1),X(3),X(17),X(1)}(N.width,N.height),function(){at(65476),at(418),X(0);for(var kt=0;kt<16;kt++)X(U[kt+1]);for(var Tt=0;Tt<=11;Tt++)X(nt[Tt]);X(16);for(var Yt=0;Yt<16;Yt++)X(pt[Yt+1]);for(var te=0;te<=161;te++)X(L[te]);X(1);for(var ee=0;ee<16;ee++)X(A[ee+1]);for(var ae=0;ae<=11;ae++)X(B[ae]);X(17);for(var pe=0;pe<16;pe++)X(D[pe+1]);for(var Vt=0;Vt<=161;Vt++)X(et[Vt])}(),at(65498),at(12),X(3),X(1),X(0),X(2),X(17),X(3),X(17),X(0),X(63),X(0);var O=0,R=0,G=0;q=0,k=7,this.encode.displayName="_encode_";for(var $,it,st,Nt,Lt,It,St,Ut,dt,T=N.data,Zt=N.width,Et=N.height,xt=4*Zt,At=0;At<Et;){for($=0;$<xt;){for(Lt=xt*At+$,St=-1,Ut=0,dt=0;dt<64;dt++)It=Lt+(Ut=dt>>3)*xt+(St=4*(7&dt)),At+Ut>=Et&&(It-=xt*(At+1+Ut-Et)),$+St>=xt&&(It-=$+St-xt+4),it=T[It++],st=T[It++],Nt=T[It++],E[dt]=(yt[it]+yt[st+256>>0]+yt[Nt+512>>0]>>16)-128,J[dt]=(yt[it+768>>0]+yt[st+1024>>0]+yt[Nt+1280>>0]>>16)-128,ot[dt]=(yt[it+1280>>0]+yt[st+1536>>0]+yt[Nt+1792>>0]>>16)-128;O=ft(E,g,O,e,a),R=ft(J,b,R,r,l),G=ft(ot,b,G,r,l),$+=32}At+=8}if(k>=0){var Ct=[];Ct[1]=k+1,Ct[0]=(1<<k+1)-1,ct(Ct)}return at(65497),new Uint8Array(I)},n=n||50,function(){for(var N=String.fromCharCode,C=0;C<256;C++)ut[C]=N(C)}(),e=rt(U,nt),r=rt(A,B),a=rt(pt,L),l=rt(D,et),function(){for(var N=1,C=2,O=1;O<=15;O++){for(var R=N;R<C;R++)S[32767+R]=O,w[32767+R]=[],w[32767+R][1]=O,w[32767+R][0]=R;for(var G=-(C-1);G<=-N;G++)S[32767+G]=O,w[32767+G]=[],w[32767+G][1]=O,w[32767+G][0]=C-1+G;N<<=1,C<<=1}}(),function(){for(var N=0;N<256;N++)yt[N]=19595*N,yt[N+256>>0]=38470*N,yt[N+512>>0]=7471*N+32768,yt[N+768>>0]=-11059*N,yt[N+1024>>0]=-21709*N,yt[N+1280>>0]=32768*N+8421375,yt[N+1536>>0]=-27439*N,yt[N+1792>>0]=-5329*N}(),Ft(n)}/**
 * @license
 * Copyright (c) 2017 Aras Abbasi
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */function zr(n,e){if(this.pos=0,this.buffer=n,this.datav=new DataView(n.buffer),this.is_with_alpha=!!e,this.bottom_up=!0,this.flag=String.fromCharCode(this.buffer[0])+String.fromCharCode(this.buffer[1]),this.pos+=2,["BM","BA","CI","CP","IC","PT"].indexOf(this.flag)===-1)throw new Error("Invalid BMP File");this.parseHeader(),this.parseBGR()}function Tl(n){function e(U){if(!U)throw Error("assert :P")}function r(U,nt,pt){for(var L=0;4>L;L++)if(U[nt+L]!=pt.charCodeAt(L))return!0;return!1}function a(U,nt,pt,L,A){for(var B=0;B<A;B++)U[nt+B]=pt[L+B]}function l(U,nt,pt,L){for(var A=0;A<L;A++)U[nt+A]=pt}function s(U){return new Int32Array(U)}function c(U,nt){for(var pt=[],L=0;L<U;L++)pt.push(new nt);return pt}function h(U,nt){var pt=[];return function L(A,B,D){for(var et=D[B],rt=0;rt<et&&(A.push(D.length>B+1?[]:new nt),!(D.length<B+1));rt++)L(A[rt],B+1,D)}(pt,0,U),pt}var f=function(){var U=this;function nt(t,i){for(var u=1<<i-1>>>0;t&u;)u>>>=1;return u?(t&u-1)+u:t}function pt(t,i,u,d,m){e(!(d%u));do t[i+(d-=u)]=m;while(0<d)}function L(t,i,u,d,m){if(e(2328>=m),512>=m)var y=s(512);else if((y=s(m))==null)return 0;return function(x,_,P,F,H,Z){var Q,Y,vt=_,lt=1<<P,W=s(16),V=s(16);for(e(H!=0),e(F!=null),e(x!=null),e(0<P),Y=0;Y<H;++Y){if(15<F[Y])return 0;++W[F[Y]]}if(W[0]==H)return 0;for(V[1]=0,Q=1;15>Q;++Q){if(W[Q]>1<<Q)return 0;V[Q+1]=V[Q]+W[Q]}for(Y=0;Y<H;++Y)Q=F[Y],0<F[Y]&&(Z[V[Q]++]=Y);if(V[15]==1)return(F=new A).g=0,F.value=Z[0],pt(x,vt,1,lt,F),lt;var gt,bt=-1,mt=lt-1,Mt=0,_t=1,Dt=1,Pt=1<<P;for(Y=0,Q=1,H=2;Q<=P;++Q,H<<=1){if(_t+=Dt<<=1,0>(Dt-=W[Q]))return 0;for(;0<W[Q];--W[Q])(F=new A).g=Q,F.value=Z[Y++],pt(x,vt+Mt,H,Pt,F),Mt=nt(Mt,Q)}for(Q=P+1,H=2;15>=Q;++Q,H<<=1){if(_t+=Dt<<=1,0>(Dt-=W[Q]))return 0;for(;0<W[Q];--W[Q]){if(F=new A,(Mt&mt)!=bt){for(vt+=Pt,gt=1<<(bt=Q)-P;15>bt&&!(0>=(gt-=W[bt]));)++bt,gt<<=1;lt+=Pt=1<<(gt=bt-P),x[_+(bt=Mt&mt)].g=gt+P,x[_+bt].value=vt-_-bt}F.g=Q-P,F.value=Z[Y++],pt(x,vt+(Mt>>P),H,Pt,F),Mt=nt(Mt,Q)}}return _t!=2*V[15]-1?0:lt}(t,i,u,d,m,y)}function A(){this.value=this.g=0}function B(){this.value=this.g=0}function D(){this.G=c(5,A),this.H=s(5),this.jc=this.Qb=this.qb=this.nd=0,this.pd=c(Ge,B)}function et(t,i,u,d){e(t!=null),e(i!=null),e(2147483648>d),t.Ca=254,t.I=0,t.b=-8,t.Ka=0,t.oa=i,t.pa=u,t.Jd=i,t.Yc=u+d,t.Zc=4<=d?u+d-4+1:u,$(t)}function rt(t,i){for(var u=0;0<i--;)u|=st(t,128)<<i;return u}function ct(t,i){var u=rt(t,i);return it(t)?-u:u}function X(t,i,u,d){var m,y=0;for(e(t!=null),e(i!=null),e(4294967288>d),t.Sb=d,t.Ra=0,t.u=0,t.h=0,4<d&&(d=4),m=0;m<d;++m)y+=i[u+m]<<8*m;t.Ra=y,t.bb=d,t.oa=i,t.pa=u}function at(t){for(;8<=t.u&&t.bb<t.Sb;)t.Ra>>>=8,t.Ra+=t.oa[t.pa+t.bb]<<wi-8>>>0,++t.bb,t.u-=8;O(t)&&(t.h=1,t.u=0)}function ft(t,i){if(e(0<=i),!t.h&&i<=yi){var u=C(t)&bi[i];return t.u+=i,at(t),u}return t.h=1,t.u=0}function Ft(){this.b=this.Ca=this.I=0,this.oa=[],this.pa=0,this.Jd=[],this.Yc=0,this.Zc=[],this.Ka=0}function N(){this.Ra=0,this.oa=[],this.h=this.u=this.bb=this.Sb=this.pa=0}function C(t){return t.Ra>>>(t.u&wi-1)>>>0}function O(t){return e(t.bb<=t.Sb),t.h||t.bb==t.Sb&&t.u>wi}function R(t,i){t.u=i,t.h=O(t)}function G(t){t.u>=ia&&(e(t.u>=ia),at(t))}function $(t){e(t!=null&&t.oa!=null),t.pa<t.Zc?(t.I=(t.oa[t.pa++]|t.I<<8)>>>0,t.b+=8):(e(t!=null&&t.oa!=null),t.pa<t.Yc?(t.b+=8,t.I=t.oa[t.pa++]|t.I<<8):t.Ka?t.b=0:(t.I<<=8,t.b+=8,t.Ka=1))}function it(t){return rt(t,1)}function st(t,i){var u=t.Ca;0>t.b&&$(t);var d=t.b,m=u*i>>>8,y=(t.I>>>d>m)+0;for(y?(u-=m,t.I-=m+1<<d>>>0):u=m+1,d=u,m=0;256<=d;)m+=8,d>>=8;return d=7^m+or[d],t.b-=d,t.Ca=(u<<d)-1,y}function Nt(t,i,u){t[i+0]=u>>24&255,t[i+1]=u>>16&255,t[i+2]=u>>8&255,t[i+3]=u>>0&255}function Lt(t,i){return t[i+0]<<0|t[i+1]<<8}function It(t,i){return Lt(t,i)|t[i+2]<<16}function St(t,i){return Lt(t,i)|Lt(t,i+2)<<16}function Ut(t,i){var u=1<<i;return e(t!=null),e(0<i),t.X=s(u),t.X==null?0:(t.Mb=32-i,t.Xa=i,1)}function dt(t,i){e(t!=null),e(i!=null),e(t.Xa==i.Xa),a(i.X,0,t.X,0,1<<i.Xa)}function T(){this.X=[],this.Xa=this.Mb=0}function Zt(t,i,u,d){e(u!=null),e(d!=null);var m=u[0],y=d[0];return m==0&&(m=(t*y+i/2)/i),y==0&&(y=(i*m+t/2)/t),0>=m||0>=y?0:(u[0]=m,d[0]=y,1)}function Et(t,i){return t+(1<<i)-1>>>i}function xt(t,i){return((4278255360&t)+(4278255360&i)>>>0&4278255360)+((16711935&t)+(16711935&i)>>>0&16711935)>>>0}function At(t,i){U[i]=function(u,d,m,y,x,_,P){var F;for(F=0;F<x;++F){var H=U[t](_[P+F-1],m,y+F);_[P+F]=xt(u[d+F],H)}}}function Ct(){this.ud=this.hd=this.jd=0}function kt(t,i){return((4278124286&(t^i))>>>1)+(t&i)>>>0}function Tt(t){return 0<=t&&256>t?t:0>t?0:255<t?255:void 0}function Yt(t,i){return Tt(t+(t-i+.5>>1))}function te(t,i,u){return Math.abs(i-u)-Math.abs(t-u)}function ee(t,i,u,d,m,y,x){for(d=y[x-1],u=0;u<m;++u)y[x+u]=d=xt(t[i+u],d)}function ae(t,i,u,d,m){var y;for(y=0;y<u;++y){var x=t[i+y],_=x>>8&255,P=16711935&(P=(P=16711935&x)+((_<<16)+_));d[m+y]=(4278255360&x)+P>>>0}}function pe(t,i){i.jd=t>>0&255,i.hd=t>>8&255,i.ud=t>>16&255}function Vt(t,i,u,d,m,y){var x;for(x=0;x<d;++x){var _=i[u+x],P=_>>>8,F=_,H=255&(H=(H=_>>>16)+((t.jd<<24>>24)*(P<<24>>24)>>>5));F=255&(F=(F=F+((t.hd<<24>>24)*(P<<24>>24)>>>5))+((t.ud<<24>>24)*(H<<24>>24)>>>5)),m[y+x]=(4278255360&_)+(H<<16)+F}}function re(t,i,u,d,m){U[i]=function(y,x,_,P,F,H,Z,Q,Y){for(P=Z;P<Q;++P)for(Z=0;Z<Y;++Z)F[H++]=m(_[d(y[x++])])},U[t]=function(y,x,_,P,F,H,Z){var Q=8>>y.b,Y=y.Ea,vt=y.K[0],lt=y.w;if(8>Q)for(y=(1<<y.b)-1,lt=(1<<Q)-1;x<_;++x){var W,V=0;for(W=0;W<Y;++W)W&y||(V=d(P[F++])),H[Z++]=m(vt[V&lt]),V>>=Q}else U["VP8LMapColor"+u](P,F,vt,lt,H,Z,x,_,Y)}}function jt(t,i,u,d,m){for(u=i+u;i<u;){var y=t[i++];d[m++]=y>>16&255,d[m++]=y>>8&255,d[m++]=y>>0&255}}function Je(t,i,u,d,m){for(u=i+u;i<u;){var y=t[i++];d[m++]=y>>16&255,d[m++]=y>>8&255,d[m++]=y>>0&255,d[m++]=y>>24&255}}function oe(t,i,u,d,m){for(u=i+u;i<u;){var y=(x=t[i++])>>16&240|x>>12&15,x=x>>0&240|x>>28&15;d[m++]=y,d[m++]=x}}function Pr(t,i,u,d,m){for(u=i+u;i<u;){var y=(x=t[i++])>>16&248|x>>13&7,x=x>>5&224|x>>3&31;d[m++]=y,d[m++]=x}}function me(t,i,u,d,m){for(u=i+u;i<u;){var y=t[i++];d[m++]=y>>0&255,d[m++]=y>>8&255,d[m++]=y>>16&255}}function Le(t,i,u,d,m,y){if(y==0)for(u=i+u;i<u;)Nt(d,((y=t[i++])[0]>>24|y[1]>>8&65280|y[2]<<8&16711680|y[3]<<24)>>>0),m+=32;else a(d,m,t,i,u)}function Vr(t,i){U[i][0]=U[t+"0"],U[i][1]=U[t+"1"],U[i][2]=U[t+"2"],U[i][3]=U[t+"3"],U[i][4]=U[t+"4"],U[i][5]=U[t+"5"],U[i][6]=U[t+"6"],U[i][7]=U[t+"7"],U[i][8]=U[t+"8"],U[i][9]=U[t+"9"],U[i][10]=U[t+"10"],U[i][11]=U[t+"11"],U[i][12]=U[t+"12"],U[i][13]=U[t+"13"],U[i][14]=U[t+"0"],U[i][15]=U[t+"0"]}function ue(t){return t==Qs||t==$s||t==Ka||t==to}function qn(){this.eb=[],this.size=this.A=this.fb=0}function Ae(){this.y=[],this.f=[],this.ea=[],this.F=[],this.Tc=this.Ed=this.Cd=this.Fd=this.lb=this.Db=this.Ab=this.fa=this.J=this.W=this.N=this.O=0}function kr(){this.Rd=this.height=this.width=this.S=0,this.f={},this.f.RGBA=new qn,this.f.kb=new Ae,this.sd=null}function sn(){this.width=[0],this.height=[0],this.Pd=[0],this.Qd=[0],this.format=[0]}function _e(){this.Id=this.fd=this.Md=this.hb=this.ib=this.da=this.bd=this.cd=this.j=this.v=this.Da=this.Sd=this.ob=0}function Jt(t){return alert("todo:WebPSamplerProcessPlane"),t.T}function ce(t,i){var u=t.T,d=i.ba.f.RGBA,m=d.eb,y=d.fb+t.ka*d.A,x=_r[i.ba.S],_=t.y,P=t.O,F=t.f,H=t.N,Z=t.ea,Q=t.W,Y=i.cc,vt=i.dc,lt=i.Mc,W=i.Nc,V=t.ka,gt=t.ka+t.T,bt=t.U,mt=bt+1>>1;for(V==0?x(_,P,null,null,F,H,Z,Q,F,H,Z,Q,m,y,null,null,bt):(x(i.ec,i.fc,_,P,Y,vt,lt,W,F,H,Z,Q,m,y-d.A,m,y,bt),++u);V+2<gt;V+=2)Y=F,vt=H,lt=Z,W=Q,H+=t.Rc,Q+=t.Rc,y+=2*d.A,x(_,(P+=2*t.fa)-t.fa,_,P,Y,vt,lt,W,F,H,Z,Q,m,y-d.A,m,y,bt);return P+=t.fa,t.j+gt<t.o?(a(i.ec,i.fc,_,P,bt),a(i.cc,i.dc,F,H,mt),a(i.Mc,i.Nc,Z,Q,mt),u--):1&gt||x(_,P,null,null,F,H,Z,Q,F,H,Z,Q,m,y+d.A,null,null,bt),u}function Dn(t,i,u){var d=t.F,m=[t.J];if(d!=null){var y=t.U,x=i.ba.S,_=x==Xa||x==Ka;i=i.ba.f.RGBA;var P=[0],F=t.ka;P[0]=t.T,t.Kb&&(F==0?--P[0]:(--F,m[0]-=t.width),t.j+t.ka+t.T==t.o&&(P[0]=t.o-t.j-F));var H=i.eb;F=i.fb+F*i.A,t=we(d,m[0],t.width,y,P,H,F+(_?0:3),i.A),e(u==P),t&&ue(x)&&Nr(H,F,_,y,P,i.A)}return 0}function Ve(t){var i=t.ma,u=i.ba.S,d=11>u,m=u==Ya||u==Ja||u==Xa||u==Zs||u==12||ue(u);if(i.memory=null,i.Ib=null,i.Jb=null,i.Nd=null,!ra(i.Oa,t,m?11:12))return 0;if(m&&ue(u)&&wt(),t.da)alert("todo:use_scaling");else{if(d){if(i.Ib=Jt,t.Kb){if(u=t.U+1>>1,i.memory=s(t.U+2*u),i.memory==null)return 0;i.ec=i.memory,i.fc=0,i.cc=i.ec,i.dc=i.fc+t.U,i.Mc=i.cc,i.Nc=i.dc+u,i.Ib=ce,wt()}}else alert("todo:EmitYUV");m&&(i.Jb=Dn,d&&K())}if(d&&!Ko){for(t=0;256>t;++t)Au[t]=89858*(t-128)+Qa>>Za,Pu[t]=-22014*(t-128)+Qa,Su[t]=-45773*(t-128),_u[t]=113618*(t-128)+Qa>>Za;for(t=ha;t<no;++t)i=76283*(t-16)+Qa>>Za,ku[t-ha]=pr(i,255),Iu[t-ha]=pr(i+8>>4,15);Ko=1}return 1}function Gr(t){var i=t.ma,u=t.U,d=t.T;return e(!(1&t.ka)),0>=u||0>=d?0:(u=i.Ib(t,i),i.Jb!=null&&i.Jb(t,i,u),i.Dc+=u,1)}function Yr(t){t.ma.memory=null}function Rt(t,i,u,d){return ft(t,8)!=47?0:(i[0]=ft(t,14)+1,u[0]=ft(t,14)+1,d[0]=ft(t,1),ft(t,3)!=0?0:!t.h)}function Ir(t,i){if(4>t)return t+1;var u=t-2>>1;return(2+(1&t)<<u)+ft(i,u)+1}function Jr(t,i){return 120<i?i-120:1<=(u=((u=cu[i-1])>>4)*t+(8-(15&u)))?u:1;var u}function ar(t,i,u){var d=C(u),m=t[i+=255&d].g-8;return 0<m&&(R(u,u.u+8),d=C(u),i+=t[i].value,i+=d&(1<<m)-1),R(u,u.u+t[i].g),t[i].value}function De(t,i,u){return u.g+=t.g,u.value+=t.value<<i>>>0,e(8>=u.g),t.g}function Be(t,i,u){var d=t.xc;return e((i=d==0?0:t.vc[t.md*(u>>d)+(i>>d)])<t.Wb),t.Ya[i]}function dr(t,i,u,d){var m=t.ab,y=t.c*i,x=t.C;i=x+i;var _=u,P=d;for(d=t.Ta,u=t.Ua;0<m--;){var F=t.gc[m],H=x,Z=i,Q=_,Y=P,vt=(P=d,_=u,F.Ea);switch(e(H<Z),e(Z<=F.nc),F.hc){case 2:Ra(Q,Y,(Z-H)*vt,P,_);break;case 0:var lt=H,W=Z,V=P,gt=_,bt=(Pt=F).Ea;lt==0&&(Xs(Q,Y,null,null,1,V,gt),ee(Q,Y+1,0,0,bt-1,V,gt+1),Y+=bt,gt+=bt,++lt);for(var mt=1<<Pt.b,Mt=mt-1,_t=Et(bt,Pt.b),Dt=Pt.K,Pt=Pt.w+(lt>>Pt.b)*_t;lt<W;){var le=Dt,he=Pt,se=1;for(aa(Q,Y,V,gt-bt,1,V,gt);se<bt;){var ne=(se&~Mt)+mt;ne>bt&&(ne=bt),(0,xn[le[he++]>>8&15])(Q,Y+ +se,V,gt+se-bt,ne-se,V,gt+se),se=ne}Y+=bt,gt+=bt,++lt&Mt||(Pt+=_t)}Z!=F.nc&&a(P,_-vt,P,_+(Z-H-1)*vt,vt);break;case 1:for(vt=Q,W=Y,bt=(Q=F.Ea)-(gt=Q&~(V=(Y=1<<F.b)-1)),lt=Et(Q,F.b),mt=F.K,F=F.w+(H>>F.b)*lt;H<Z;){for(Mt=mt,_t=F,Dt=new Ct,Pt=W+gt,le=W+Q;W<Pt;)pe(Mt[_t++],Dt),Vn(Dt,vt,W,Y,P,_),W+=Y,_+=Y;W<le&&(pe(Mt[_t++],Dt),Vn(Dt,vt,W,bt,P,_),W+=bt,_+=bt),++H&V||(F+=lt)}break;case 3:if(Q==P&&Y==_&&0<F.b){for(W=P,Q=vt=_+(Z-H)*vt-(gt=(Z-H)*Et(F.Ea,F.b)),Y=P,V=_,lt=[],gt=(bt=gt)-1;0<=gt;--gt)lt[gt]=Y[V+gt];for(gt=bt-1;0<=gt;--gt)W[Q+gt]=lt[gt];wr(F,H,Z,P,vt,P,_)}else wr(F,H,Z,Q,Y,P,_)}_=d,P=u}P!=u&&a(d,u,_,P,y)}function ei(t,i){var u=t.V,d=t.Ba+t.c*t.C,m=i-t.C;if(e(i<=t.l.o),e(16>=m),0<m){var y=t.l,x=t.Ta,_=t.Ua,P=y.width;if(dr(t,m,u,d),m=_=[_],e((u=t.C)<(d=i)),e(y.v<y.va),d>y.o&&(d=y.o),u<y.j){var F=y.j-u;u=y.j,m[0]+=F*P}if(u>=d?u=0:(m[0]+=4*y.v,y.ka=u-y.j,y.U=y.va-y.v,y.T=d-u,u=1),u){if(_=_[0],11>(u=t.ca).S){var H=u.f.RGBA,Z=(d=u.S,m=y.U,y=y.T,F=H.eb,H.A),Q=y;for(H=H.fb+t.Ma*H.A;0<Q--;){var Y=x,vt=_,lt=m,W=F,V=H;switch(d){case Ga:lr(Y,vt,lt,W,V);break;case Ya:rr(Y,vt,lt,W,V);break;case Qs:rr(Y,vt,lt,W,V),Nr(W,V,0,lt,1,0);break;case zo:hn(Y,vt,lt,W,V);break;case Ja:Le(Y,vt,lt,W,V,1);break;case $s:Le(Y,vt,lt,W,V,1),Nr(W,V,0,lt,1,0);break;case Xa:Le(Y,vt,lt,W,V,0);break;case Ka:Le(Y,vt,lt,W,V,0),Nr(W,V,1,lt,1,0);break;case Zs:Ln(Y,vt,lt,W,V);break;case to:Ln(Y,vt,lt,W,V),ye(W,V,lt,1,0);break;case Uo:cn(Y,vt,lt,W,V);break;default:e(0)}_+=P,H+=Z}t.Ma+=y}else alert("todo:EmitRescaledRowsYUVA");e(t.Ma<=u.height)}}t.C=i,e(t.C<=t.i)}function on(t){var i;if(0<t.ua)return 0;for(i=0;i<t.Wb;++i){var u=t.Ya[i].G,d=t.Ya[i].H;if(0<u[1][d[1]+0].g||0<u[2][d[2]+0].g||0<u[3][d[3]+0].g)return 0}return 1}function Fr(t,i,u,d,m,y){if(t.Z!=0){var x=t.qd,_=t.rd;for(e(Sn[t.Z]!=null);i<u;++i)Sn[t.Z](x,_,d,m,d,m,y),x=d,_=m,m+=y;t.qd=x,t.rd=_}}function Cr(t,i){var u=t.l.ma,d=u.Z==0||u.Z==1?t.l.j:t.C;if(d=t.C<d?d:t.C,e(i<=t.l.o),i>d){var m=t.l.width,y=u.ca,x=u.tb+m*d,_=t.V,P=t.Ba+t.c*d,F=t.gc;e(t.ab==1),e(F[0].hc==3),za(F[0],d,i,_,P,y,x),Fr(u,d,i,y,x,m)}t.C=t.Ma=i}function jr(t,i,u,d,m,y,x){var _=t.$/d,P=t.$%d,F=t.m,H=t.s,Z=u+t.$,Q=Z;m=u+d*m;var Y=u+d*y,vt=280+H.ua,lt=t.Pb?_:16777216,W=0<H.ua?H.Wa:null,V=H.wc,gt=Z<Y?Be(H,P,_):null;e(t.C<y),e(Y<=m);var bt=!1;t:for(;;){for(;bt||Z<Y;){var mt=0;if(_>=lt){var Mt=Z-u;e((lt=t).Pb),lt.wd=lt.m,lt.xd=Mt,0<lt.s.ua&&dt(lt.s.Wa,lt.s.vb),lt=_+fu}if(P&V||(gt=Be(H,P,_)),e(gt!=null),gt.Qb&&(i[Z]=gt.qb,bt=!0),!bt)if(G(F),gt.jc){mt=F,Mt=i;var _t=Z,Dt=gt.pd[C(mt)&Ge-1];e(gt.jc),256>Dt.g?(R(mt,mt.u+Dt.g),Mt[_t]=Dt.value,mt=0):(R(mt,mt.u+Dt.g-256),e(256<=Dt.value),mt=Dt.value),mt==0&&(bt=!0)}else mt=ar(gt.G[0],gt.H[0],F);if(F.h)break;if(bt||256>mt){if(!bt)if(gt.nd)i[Z]=(gt.qb|mt<<8)>>>0;else{if(G(F),bt=ar(gt.G[1],gt.H[1],F),G(F),Mt=ar(gt.G[2],gt.H[2],F),_t=ar(gt.G[3],gt.H[3],F),F.h)break;i[Z]=(_t<<24|bt<<16|mt<<8|Mt)>>>0}if(bt=!1,++Z,++P>=d&&(P=0,++_,x!=null&&_<=y&&!(_%16)&&x(t,_),W!=null))for(;Q<Z;)mt=i[Q++],W.X[(506832829*mt&**********)>>>W.Mb]=mt}else if(280>mt){if(mt=Ir(mt-256,F),Mt=ar(gt.G[4],gt.H[4],F),G(F),Mt=Jr(d,Mt=Ir(Mt,F)),F.h)break;if(Z-u<Mt||m-Z<mt)break t;for(_t=0;_t<mt;++_t)i[Z+_t]=i[Z+_t-Mt];for(Z+=mt,P+=mt;P>=d;)P-=d,++_,x!=null&&_<=y&&!(_%16)&&x(t,_);if(e(Z<=m),P&V&&(gt=Be(H,P,_)),W!=null)for(;Q<Z;)mt=i[Q++],W.X[(506832829*mt&**********)>>>W.Mb]=mt}else{if(!(mt<vt))break t;for(bt=mt-280,e(W!=null);Q<Z;)mt=i[Q++],W.X[(506832829*mt&**********)>>>W.Mb]=mt;mt=Z,e(!(bt>>>(Mt=W).Xa)),i[mt]=Mt.X[bt],bt=!0}bt||e(F.h==O(F))}if(t.Pb&&F.h&&Z<m)e(t.m.h),t.a=5,t.m=t.wd,t.$=t.xd,0<t.s.ua&&dt(t.s.vb,t.s.Wa);else{if(F.h)break t;x!=null&&x(t,_>y?y:_),t.a=0,t.$=Z-u}return 1}return t.a=3,0}function Xr(t){e(t!=null),t.vc=null,t.yc=null,t.Ya=null;var i=t.Wa;i!=null&&(i.X=null),t.vb=null,e(t!=null)}function yr(){var t=new Js;return t==null?null:(t.a=0,t.xb=Vo,Vr("Predictor","VP8LPredictors"),Vr("Predictor","VP8LPredictors_C"),Vr("PredictorAdd","VP8LPredictorsAdd"),Vr("PredictorAdd","VP8LPredictorsAdd_C"),Ra=ae,Vn=Vt,lr=jt,rr=Je,Ln=oe,cn=Pr,hn=me,U.VP8LMapColor32b=xi,U.VP8LMapColor8b=Ua,t)}function Kr(t,i,u,d,m){var y=1,x=[t],_=[i],P=d.m,F=d.s,H=null,Z=0;t:for(;;){if(u)for(;y&&ft(P,1);){var Q=x,Y=_,vt=d,lt=1,W=vt.m,V=vt.gc[vt.ab],gt=ft(W,2);if(vt.Oc&1<<gt)y=0;else{switch(vt.Oc|=1<<gt,V.hc=gt,V.Ea=Q[0],V.nc=Y[0],V.K=[null],++vt.ab,e(4>=vt.ab),gt){case 0:case 1:V.b=ft(W,3)+2,lt=Kr(Et(V.Ea,V.b),Et(V.nc,V.b),0,vt,V.K),V.K=V.K[0];break;case 3:var bt,mt=ft(W,8)+1,Mt=16<mt?0:4<mt?1:2<mt?2:3;if(Q[0]=Et(V.Ea,Mt),V.b=Mt,bt=lt=Kr(mt,1,0,vt,V.K)){var _t,Dt=mt,Pt=V,le=1<<(8>>Pt.b),he=s(le);if(he==null)bt=0;else{var se=Pt.K[0],ne=Pt.w;for(he[0]=Pt.K[0][0],_t=1;_t<1*Dt;++_t)he[_t]=xt(se[ne+_t],he[_t-1]);for(;_t<4*le;++_t)he[_t]=0;Pt.K[0]=null,Pt.K[0]=he,bt=1}}lt=bt;break;case 2:break;default:e(0)}y=lt}}if(x=x[0],_=_[0],y&&ft(P,1)&&!(y=1<=(Z=ft(P,4))&&11>=Z)){d.a=3;break t}var ve;if(ve=y)e:{var ge,$t,Te,ur=d,qe=x,cr=_,fe=Z,mr=u,vr=ur.m,Ue=ur.s,Ye=[null],ir=1,Sr=0,tn=hu[fe];r:for(;;){if(mr&&ft(vr,1)){var He=ft(vr,3)+2,gn=Et(qe,He),Kn=Et(cr,He),Si=gn*Kn;if(!Kr(gn,Kn,0,ur,Ye))break r;for(Ye=Ye[0],Ue.xc=He,ge=0;ge<Si;++ge){var Pn=Ye[ge]>>8&65535;Ye[ge]=Pn,Pn>=ir&&(ir=Pn+1)}}if(vr.h)break r;for($t=0;5>$t;++$t){var Pe=Ho[$t];!$t&&0<fe&&(Pe+=1<<fe),Sr<Pe&&(Sr=Pe)}var io=c(ir*tn,A),$o=ir,tl=c($o,D);if(tl==null)var ts=null;else e(65536>=$o),ts=tl;var fa=s(Sr);if(ts==null||fa==null||io==null){ur.a=1;break r}var es=io;for(ge=Te=0;ge<ir;++ge){var Tr=ts[ge],Pi=Tr.G,ki=Tr.H,el=0,rs=1,rl=0;for($t=0;5>$t;++$t){Pe=Ho[$t],Pi[$t]=es,ki[$t]=Te,!$t&&0<fe&&(Pe+=1<<fe);i:{var ns,ao=Pe,is=ur,da=fa,ju=es,Ou=Te,so=0,kn=is.m,Mu=ft(kn,1);if(l(da,0,0,ao),Mu){var Bu=ft(kn,1)+1,Eu=ft(kn,1),nl=ft(kn,Eu==0?1:8);da[nl]=1,Bu==2&&(da[nl=ft(kn,8)]=1);var as=1}else{var il=s(19),al=ft(kn,4)+4;if(19<al){is.a=3;var ss=0;break i}for(ns=0;ns<al;++ns)il[uu[ns]]=ft(kn,3);var oo=void 0,pa=void 0,sl=is,Tu=il,os=ao,ol=da,lo=0,In=sl.m,ll=8,ul=c(128,A);n:for(;L(ul,0,7,Tu,19);){if(ft(In,1)){var qu=2+2*ft(In,3);if((oo=2+ft(In,qu))>os)break n}else oo=os;for(pa=0;pa<os&&oo--;){G(In);var cl=ul[0+(127&C(In))];R(In,In.u+cl.g);var Ii=cl.value;if(16>Ii)ol[pa++]=Ii,Ii!=0&&(ll=Ii);else{var Du=Ii==16,hl=Ii-16,Ru=ou[hl],fl=ft(In,su[hl])+Ru;if(pa+fl>os)break n;for(var zu=Du?ll:0;0<fl--;)ol[pa++]=zu}}lo=1;break n}lo||(sl.a=3),as=lo}(as=as&&!kn.h)&&(so=L(ju,Ou,8,da,ao)),as&&so!=0?ss=so:(is.a=3,ss=0)}if(ss==0)break r;if(rs&&lu[$t]==1&&(rs=es[Te].g==0),el+=es[Te].g,Te+=ss,3>=$t){var ga,uo=fa[0];for(ga=1;ga<Pe;++ga)fa[ga]>uo&&(uo=fa[ga]);rl+=uo}}if(Tr.nd=rs,Tr.Qb=0,rs&&(Tr.qb=(Pi[3][ki[3]+0].value<<24|Pi[1][ki[1]+0].value<<16|Pi[2][ki[2]+0].value)>>>0,el==0&&256>Pi[0][ki[0]+0].value&&(Tr.Qb=1,Tr.qb+=Pi[0][ki[0]+0].value<<8)),Tr.jc=!Tr.Qb&&6>rl,Tr.jc){var ls,mn=Tr;for(ls=0;ls<Ge;++ls){var Fn=ls,Cn=mn.pd[Fn],us=mn.G[0][mn.H[0]+Fn];256<=us.value?(Cn.g=us.g+256,Cn.value=us.value):(Cn.g=0,Cn.value=0,Fn>>=De(us,8,Cn),Fn>>=De(mn.G[1][mn.H[1]+Fn],16,Cn),Fn>>=De(mn.G[2][mn.H[2]+Fn],0,Cn),De(mn.G[3][mn.H[3]+Fn],24,Cn))}}}Ue.vc=Ye,Ue.Wb=ir,Ue.Ya=ts,Ue.yc=io,ve=1;break e}ve=0}if(!(y=ve)){d.a=3;break t}if(0<Z){if(F.ua=1<<Z,!Ut(F.Wa,Z)){d.a=1,y=0;break t}}else F.ua=0;var co=d,dl=x,Uu=_,ho=co.s,fo=ho.xc;if(co.c=dl,co.i=Uu,ho.md=Et(dl,fo),ho.wc=fo==0?-1:(1<<fo)-1,u){d.xb=yu;break t}if((H=s(x*_))==null){d.a=1,y=0;break t}y=(y=jr(d,H,0,x,_,_,null))&&!P.h;break t}return y?(m!=null?m[0]=H:(e(H==null),e(u)),d.$=0,u||Xr(F)):Xr(F),y}function Rn(t,i){var u=t.c*t.i,d=u+i+16*i;return e(t.c<=i),t.V=s(d),t.V==null?(t.Ta=null,t.Ua=0,t.a=1,0):(t.Ta=t.V,t.Ua=t.Ba+u+i,1)}function ri(t,i){var u=t.C,d=i-u,m=t.V,y=t.Ba+t.c*u;for(e(i<=t.l.o);0<d;){var x=16<d?16:d,_=t.l.ma,P=t.l.width,F=P*x,H=_.ca,Z=_.tb+P*u,Q=t.Ta,Y=t.Ua;dr(t,x,m,y),Ce(Q,Y,H,Z,F),Fr(_,u,u+x,H,Z,P),d-=x,m+=x*t.c,u+=x}e(u==i),t.C=t.Ma=i}function ni(){this.ub=this.yd=this.td=this.Rb=0}function ii(){this.Kd=this.Ld=this.Ud=this.Td=this.i=this.c=0}function ai(){this.Fb=this.Bb=this.Cb=0,this.Zb=s(4),this.Lb=s(4)}function wa(){this.Yb=function(){var t=[];return function i(u,d,m){for(var y=m[d],x=0;x<y&&(u.push(m.length>d+1?[]:0),!(m.length<d+1));x++)i(u[x],d+1,m)}(t,0,[3,11]),t}()}function _s(){this.jb=s(3),this.Wc=h([4,8],wa),this.Xc=h([4,17],wa)}function Ss(){this.Pc=this.wb=this.Tb=this.zd=0,this.vd=new s(4),this.od=new s(4)}function si(){this.ld=this.La=this.dd=this.tc=0}function xa(){this.Na=this.la=0}function Ps(){this.Sc=[0,0],this.Eb=[0,0],this.Qc=[0,0],this.ia=this.lc=0}function Ri(){this.ad=s(384),this.Za=0,this.Ob=s(16),this.$b=this.Ad=this.ia=this.Gc=this.Hc=this.Dd=0}function ks(){this.uc=this.M=this.Nb=0,this.wa=Array(new si),this.Y=0,this.ya=Array(new Ri),this.aa=0,this.l=new oi}function La(){this.y=s(16),this.f=s(8),this.ea=s(8)}function Is(){this.cb=this.a=0,this.sc="",this.m=new Ft,this.Od=new ni,this.Kc=new ii,this.ed=new Ss,this.Qa=new ai,this.Ic=this.$c=this.Aa=0,this.D=new ks,this.Xb=this.Va=this.Hb=this.zb=this.yb=this.Ub=this.za=0,this.Jc=c(8,Ft),this.ia=0,this.pb=c(4,Ps),this.Pa=new _s,this.Bd=this.kc=0,this.Ac=[],this.Bc=0,this.zc=[0,0,0,0],this.Gd=Array(new La),this.Hd=0,this.rb=Array(new xa),this.sb=0,this.wa=Array(new si),this.Y=0,this.oc=[],this.pc=0,this.sa=[],this.ta=0,this.qa=[],this.ra=0,this.Ha=[],this.B=this.R=this.Ia=0,this.Ec=[],this.M=this.ja=this.Vb=this.Fc=0,this.ya=Array(new Ri),this.L=this.aa=0,this.gd=h([4,2],si),this.ga=null,this.Fa=[],this.Cc=this.qc=this.P=0,this.Gb=[],this.Uc=0,this.mb=[],this.nb=0,this.rc=[],this.Ga=this.Vc=0}function oi(){this.T=this.U=this.ka=this.height=this.width=0,this.y=[],this.f=[],this.ea=[],this.Rc=this.fa=this.W=this.N=this.O=0,this.ma="void",this.put="VP8IoPutHook",this.ac="VP8IoSetupHook",this.bc="VP8IoTeardownHook",this.ha=this.Kb=0,this.data=[],this.hb=this.ib=this.da=this.o=this.j=this.va=this.v=this.Da=this.ob=this.w=0,this.F=[],this.J=0}function Fs(){var t=new Is;return t!=null&&(t.a=0,t.sc="OK",t.cb=0,t.Xb=0,ca||(ca=_a)),t}function Ie(t,i,u){return t.a==0&&(t.a=i,t.sc=u,t.cb=0),0}function Na(t,i,u){return 3<=u&&t[i+0]==157&&t[i+1]==1&&t[i+2]==42}function Aa(t,i){if(t==null)return 0;if(t.a=0,t.sc="OK",i==null)return Ie(t,2,"null VP8Io passed to VP8GetHeaders()");var u=i.data,d=i.w,m=i.ha;if(4>m)return Ie(t,7,"Truncated header.");var y=u[d+0]|u[d+1]<<8|u[d+2]<<16,x=t.Od;if(x.Rb=!(1&y),x.td=y>>1&7,x.yd=y>>4&1,x.ub=y>>5,3<x.td)return Ie(t,3,"Incorrect keyframe parameters.");if(!x.yd)return Ie(t,4,"Frame not displayable.");d+=3,m-=3;var _=t.Kc;if(x.Rb){if(7>m)return Ie(t,7,"cannot parse picture header");if(!Na(u,d,m))return Ie(t,3,"Bad code word");_.c=16383&(u[d+4]<<8|u[d+3]),_.Td=u[d+4]>>6,_.i=16383&(u[d+6]<<8|u[d+5]),_.Ud=u[d+6]>>6,d+=7,m-=7,t.za=_.c+15>>4,t.Ub=_.i+15>>4,i.width=_.c,i.height=_.i,i.Da=0,i.j=0,i.v=0,i.va=i.width,i.o=i.height,i.da=0,i.ib=i.width,i.hb=i.height,i.U=i.width,i.T=i.height,l((y=t.Pa).jb,0,255,y.jb.length),e((y=t.Qa)!=null),y.Cb=0,y.Bb=0,y.Fb=1,l(y.Zb,0,0,y.Zb.length),l(y.Lb,0,0,y.Lb)}if(x.ub>m)return Ie(t,7,"bad partition length");et(y=t.m,u,d,x.ub),d+=x.ub,m-=x.ub,x.Rb&&(_.Ld=it(y),_.Kd=it(y)),_=t.Qa;var P,F=t.Pa;if(e(y!=null),e(_!=null),_.Cb=it(y),_.Cb){if(_.Bb=it(y),it(y)){for(_.Fb=it(y),P=0;4>P;++P)_.Zb[P]=it(y)?ct(y,7):0;for(P=0;4>P;++P)_.Lb[P]=it(y)?ct(y,6):0}if(_.Bb)for(P=0;3>P;++P)F.jb[P]=it(y)?rt(y,8):255}else _.Bb=0;if(y.Ka)return Ie(t,3,"cannot parse segment header");if((_=t.ed).zd=it(y),_.Tb=rt(y,6),_.wb=rt(y,3),_.Pc=it(y),_.Pc&&it(y)){for(F=0;4>F;++F)it(y)&&(_.vd[F]=ct(y,6));for(F=0;4>F;++F)it(y)&&(_.od[F]=ct(y,6))}if(t.L=_.Tb==0?0:_.zd?1:2,y.Ka)return Ie(t,3,"cannot parse filter header");var H=m;if(m=P=d,d=P+H,_=H,t.Xb=(1<<rt(t.m,2))-1,H<3*(F=t.Xb))u=7;else{for(P+=3*F,_-=3*F,H=0;H<F;++H){var Z=u[m+0]|u[m+1]<<8|u[m+2]<<16;Z>_&&(Z=_),et(t.Jc[+H],u,P,Z),P+=Z,_-=Z,m+=3}et(t.Jc[+F],u,P,_),u=P<d?0:5}if(u!=0)return Ie(t,u,"cannot parse partitions");for(u=rt(P=t.m,7),m=it(P)?ct(P,4):0,d=it(P)?ct(P,4):0,_=it(P)?ct(P,4):0,F=it(P)?ct(P,4):0,P=it(P)?ct(P,4):0,H=t.Qa,Z=0;4>Z;++Z){if(H.Cb){var Q=H.Zb[Z];H.Fb||(Q+=u)}else{if(0<Z){t.pb[Z]=t.pb[0];continue}Q=u}var Y=t.pb[Z];Y.Sc[0]=eo[pr(Q+m,127)],Y.Sc[1]=ro[pr(Q+0,127)],Y.Eb[0]=2*eo[pr(Q+d,127)],Y.Eb[1]=101581*ro[pr(Q+_,127)]>>16,8>Y.Eb[1]&&(Y.Eb[1]=8),Y.Qc[0]=eo[pr(Q+F,117)],Y.Qc[1]=ro[pr(Q+P,127)],Y.lc=Q+P}if(!x.Rb)return Ie(t,4,"Not a key frame.");for(it(y),x=t.Pa,u=0;4>u;++u){for(m=0;8>m;++m)for(d=0;3>d;++d)for(_=0;11>_;++_)F=st(y,vu[u][m][d][_])?rt(y,8):gu[u][m][d][_],x.Wc[u][m].Yb[d][_]=F;for(m=0;17>m;++m)x.Xc[u][m]=x.Wc[u][bu[m]]}return t.kc=it(y),t.kc&&(t.Bd=rt(y,8)),t.cb=1}function _a(t,i,u,d,m,y,x){var _=i[m].Yb[u];for(u=0;16>m;++m){if(!st(t,_[u+0]))return m;for(;!st(t,_[u+1]);)if(_=i[++m].Yb[0],u=0,m==16)return 16;var P=i[m+1].Yb;if(st(t,_[u+2])){var F=t,H=0;if(st(F,(Q=_)[(Z=u)+3]))if(st(F,Q[Z+6])){for(_=0,Z=2*(H=st(F,Q[Z+8]))+(Q=st(F,Q[Z+9+H])),H=0,Q=du[Z];Q[_];++_)H+=H+st(F,Q[_]);H+=3+(8<<Z)}else st(F,Q[Z+7])?(H=7+2*st(F,165),H+=st(F,145)):H=5+st(F,159);else H=st(F,Q[Z+4])?3+st(F,Q[Z+5]):2;_=P[2]}else H=1,_=P[1];P=x+pu[m],0>(F=t).b&&$(F);var Z,Q=F.b,Y=(Z=F.Ca>>1)-(F.I>>Q)>>31;--F.b,F.Ca+=Y,F.Ca|=1,F.I-=(Z+1&Y)<<Q,y[P]=((H^Y)-Y)*d[(0<m)+0]}return 16}function zi(t){var i=t.rb[t.sb-1];i.la=0,i.Na=0,l(t.zc,0,0,t.zc.length),t.ja=0}function Cs(t,i){if(t==null)return 0;if(i==null)return Ie(t,2,"NULL VP8Io parameter in VP8Decode().");if(!t.cb&&!Aa(t,i))return 0;if(e(t.cb),i.ac==null||i.ac(i)){i.ob&&(t.L=0);var u=$a[t.L];if(t.L==2?(t.yb=0,t.zb=0):(t.yb=i.v-u>>4,t.zb=i.j-u>>4,0>t.yb&&(t.yb=0),0>t.zb&&(t.zb=0)),t.Va=i.o+15+u>>4,t.Hb=i.va+15+u>>4,t.Hb>t.za&&(t.Hb=t.za),t.Va>t.Ub&&(t.Va=t.Ub),0<t.L){var d=t.ed;for(u=0;4>u;++u){var m;if(t.Qa.Cb){var y=t.Qa.Lb[u];t.Qa.Fb||(y+=d.Tb)}else y=d.Tb;for(m=0;1>=m;++m){var x=t.gd[u][m],_=y;if(d.Pc&&(_+=d.vd[0],m&&(_+=d.od[0])),0<(_=0>_?0:63<_?63:_)){var P=_;0<d.wb&&(P=4<d.wb?P>>2:P>>1)>9-d.wb&&(P=9-d.wb),1>P&&(P=1),x.dd=P,x.tc=2*_+P,x.ld=40<=_?2:15<=_?1:0}else x.tc=0;x.La=m}}}u=0}else Ie(t,6,"Frame setup failed"),u=t.a;if(u=u==0){if(u){t.$c=0,0<t.Aa||(t.Ic=Cu);t:{u=t.Ic,d=4*(P=t.za);var F=32*P,H=P+1,Z=0<t.L?P*(0<t.Aa?2:1):0,Q=(t.Aa==2?2:1)*P;if((x=d+832+(m=3*(16*u+$a[t.L])/2*F)+(y=t.Fa!=null&&0<t.Fa.length?t.Kc.c*t.Kc.i:0))!=x)u=0;else{if(x>t.Vb){if(t.Vb=0,t.Ec=s(x),t.Fc=0,t.Ec==null){u=Ie(t,1,"no memory during frame initialization.");break t}t.Vb=x}x=t.Ec,_=t.Fc,t.Ac=x,t.Bc=_,_+=d,t.Gd=c(F,La),t.Hd=0,t.rb=c(H+1,xa),t.sb=1,t.wa=Z?c(Z,si):null,t.Y=0,t.D.Nb=0,t.D.wa=t.wa,t.D.Y=t.Y,0<t.Aa&&(t.D.Y+=P),e(!0),t.oc=x,t.pc=_,_+=832,t.ya=c(Q,Ri),t.aa=0,t.D.ya=t.ya,t.D.aa=t.aa,t.Aa==2&&(t.D.aa+=P),t.R=16*P,t.B=8*P,P=(F=$a[t.L])*t.R,F=F/2*t.B,t.sa=x,t.ta=_+P,t.qa=t.sa,t.ra=t.ta+16*u*t.R+F,t.Ha=t.qa,t.Ia=t.ra+8*u*t.B+F,t.$c=0,_+=m,t.mb=y?x:null,t.nb=y?_:null,e(_+y<=t.Fc+t.Vb),zi(t),l(t.Ac,t.Bc,0,d),u=1}}if(u){if(i.ka=0,i.y=t.sa,i.O=t.ta,i.f=t.qa,i.N=t.ra,i.ea=t.Ha,i.Vd=t.Ia,i.fa=t.R,i.Rc=t.B,i.F=null,i.J=0,!Wa){for(u=-255;255>=u;++u)Re[255+u]=0>u?-u:u;for(u=-1020;1020>=u;++u)dn[1020+u]=-128>u?-128:127<u?127:u;for(u=-112;112>=u;++u)ua[112+u]=-16>u?-16:15<u?15:u;for(u=-255;510>=u;++u)_i[255+u]=0>u?0:255<u?255:u;Wa=1}Li=Ms,fn=js,sa=Pa,nr=Os,xr=ka,Fe=Sa,Ni=Yi,Ha=Hn,oa=Ys,Gn=Ji,Yn=Gs,Nn=di,Jn=Xi,Ai=Ta,Xn=Ea,An=Qr,la=un,Lr=Vs,Er[0]=Zr,Er[1]=Bs,Er[2]=Ds,Er[3]=Rs,Er[4]=Ca,Er[5]=hi,Er[6]=ja,Er[7]=Wi,Er[8]=Us,Er[9]=zs,_n[0]=Ia,_n[1]=Ts,_n[2]=ln,_n[3]=ui,_n[4]=Xe,_n[5]=qs,_n[6]=Fa,pn[0]=bn,pn[1]=Es,pn[2]=Hs,pn[3]=Vi,pn[4]=Un,pn[5]=Ws,pn[6]=Gi,u=1}else u=0}u&&(u=function(Y,vt){for(Y.M=0;Y.M<Y.Va;++Y.M){var lt,W=Y.Jc[Y.M&Y.Xb],V=Y.m,gt=Y;for(lt=0;lt<gt.za;++lt){var bt=V,mt=gt,Mt=mt.Ac,_t=mt.Bc+4*lt,Dt=mt.zc,Pt=mt.ya[mt.aa+lt];if(mt.Qa.Bb?Pt.$b=st(bt,mt.Pa.jb[0])?2+st(bt,mt.Pa.jb[2]):st(bt,mt.Pa.jb[1]):Pt.$b=0,mt.kc&&(Pt.Ad=st(bt,mt.Bd)),Pt.Za=!st(bt,145)+0,Pt.Za){var le=Pt.Ob,he=0;for(mt=0;4>mt;++mt){var se,ne=Dt[0+mt];for(se=0;4>se;++se){ne=mu[Mt[_t+se]][ne];for(var ve=Wo[st(bt,ne[0])];0<ve;)ve=Wo[2*ve+st(bt,ne[ve])];ne=-ve,Mt[_t+se]=ne}a(le,he,Mt,_t,4),he+=4,Dt[0+mt]=ne}}else ne=st(bt,156)?st(bt,128)?1:3:st(bt,163)?2:0,Pt.Ob[0]=ne,l(Mt,_t,ne,4),l(Dt,0,ne,4);Pt.Dd=st(bt,142)?st(bt,114)?st(bt,183)?1:3:2:0}if(gt.m.Ka)return Ie(Y,7,"Premature end-of-partition0 encountered.");for(;Y.ja<Y.za;++Y.ja){if(gt=W,bt=(V=Y).rb[V.sb-1],Mt=V.rb[V.sb+V.ja],lt=V.ya[V.aa+V.ja],_t=V.kc?lt.Ad:0)bt.la=Mt.la=0,lt.Za||(bt.Na=Mt.Na=0),lt.Hc=0,lt.Gc=0,lt.ia=0;else{var ge,$t;if(bt=Mt,Mt=gt,_t=V.Pa.Xc,Dt=V.ya[V.aa+V.ja],Pt=V.pb[Dt.$b],mt=Dt.ad,le=0,he=V.rb[V.sb-1],ne=se=0,l(mt,le,0,384),Dt.Za)var Te=0,ur=_t[3];else{ve=s(16);var qe=bt.Na+he.Na;if(qe=ca(Mt,_t[1],qe,Pt.Eb,0,ve,0),bt.Na=he.Na=(0<qe)+0,1<qe)Li(ve,0,mt,le);else{var cr=ve[0]+3>>3;for(ve=0;256>ve;ve+=16)mt[le+ve]=cr}Te=1,ur=_t[0]}var fe=15&bt.la,mr=15&he.la;for(ve=0;4>ve;++ve){var vr=1&mr;for(cr=$t=0;4>cr;++cr)fe=fe>>1|(vr=(qe=ca(Mt,ur,qe=vr+(1&fe),Pt.Sc,Te,mt,le))>Te)<<7,$t=$t<<2|(3<qe?3:1<qe?2:mt[le+0]!=0),le+=16;fe>>=4,mr=mr>>1|vr<<7,se=(se<<8|$t)>>>0}for(ur=fe,Te=mr>>4,ge=0;4>ge;ge+=2){for($t=0,fe=bt.la>>4+ge,mr=he.la>>4+ge,ve=0;2>ve;++ve){for(vr=1&mr,cr=0;2>cr;++cr)qe=vr+(1&fe),fe=fe>>1|(vr=0<(qe=ca(Mt,_t[2],qe,Pt.Qc,0,mt,le)))<<3,$t=$t<<2|(3<qe?3:1<qe?2:mt[le+0]!=0),le+=16;fe>>=2,mr=mr>>1|vr<<5}ne|=$t<<4*ge,ur|=fe<<4<<ge,Te|=(240&mr)<<ge}bt.la=ur,he.la=Te,Dt.Hc=se,Dt.Gc=ne,Dt.ia=43690&ne?0:Pt.ia,_t=!(se|ne)}if(0<V.L&&(V.wa[V.Y+V.ja]=V.gd[lt.$b][lt.Za],V.wa[V.Y+V.ja].La|=!_t),gt.Ka)return Ie(Y,7,"Premature end-of-file encountered.")}if(zi(Y),V=vt,gt=1,lt=(W=Y).D,bt=0<W.L&&W.M>=W.zb&&W.M<=W.Va,W.Aa==0)t:{if(lt.M=W.M,lt.uc=bt,ea(W,lt),gt=1,lt=($t=W.D).Nb,bt=(ne=$a[W.L])*W.R,Mt=ne/2*W.B,ve=16*lt*W.R,cr=8*lt*W.B,_t=W.sa,Dt=W.ta-bt+ve,Pt=W.qa,mt=W.ra-Mt+cr,le=W.Ha,he=W.Ia-Mt+cr,mr=(fe=$t.M)==0,se=fe>=W.Va-1,W.Aa==2&&ea(W,$t),$t.uc)for(vr=(qe=W).D.M,e(qe.D.uc),$t=qe.yb;$t<qe.Hb;++$t){Te=$t,ur=vr;var Ue=(Ye=(Pe=qe).D).Nb;ge=Pe.R;var Ye=Ye.wa[Ye.Y+Te],ir=Pe.sa,Sr=Pe.ta+16*Ue*ge+16*Te,tn=Ye.dd,He=Ye.tc;if(He!=0)if(e(3<=He),Pe.L==1)0<Te&&An(ir,Sr,ge,He+4),Ye.La&&Lr(ir,Sr,ge,He),0<ur&&Xn(ir,Sr,ge,He+4),Ye.La&&la(ir,Sr,ge,He);else{var gn=Pe.B,Kn=Pe.qa,Si=Pe.ra+8*Ue*gn+8*Te,Pn=Pe.Ha,Pe=Pe.Ia+8*Ue*gn+8*Te;Ue=Ye.ld,0<Te&&(Ha(ir,Sr,ge,He+4,tn,Ue),Gn(Kn,Si,Pn,Pe,gn,He+4,tn,Ue)),Ye.La&&(Nn(ir,Sr,ge,He,tn,Ue),Ai(Kn,Si,Pn,Pe,gn,He,tn,Ue)),0<ur&&(Ni(ir,Sr,ge,He+4,tn,Ue),oa(Kn,Si,Pn,Pe,gn,He+4,tn,Ue)),Ye.La&&(Yn(ir,Sr,ge,He,tn,Ue),Jn(Kn,Si,Pn,Pe,gn,He,tn,Ue))}}if(W.ia&&alert("todo:DitherRow"),V.put!=null){if($t=16*fe,fe=16*(fe+1),mr?(V.y=W.sa,V.O=W.ta+ve,V.f=W.qa,V.N=W.ra+cr,V.ea=W.Ha,V.W=W.Ia+cr):($t-=ne,V.y=_t,V.O=Dt,V.f=Pt,V.N=mt,V.ea=le,V.W=he),se||(fe-=ne),fe>V.o&&(fe=V.o),V.F=null,V.J=null,W.Fa!=null&&0<W.Fa.length&&$t<fe&&(V.J=$i(W,V,$t,fe-$t),V.F=W.mb,V.F==null&&V.F.length==0)){gt=Ie(W,3,"Could not decode alpha data.");break t}$t<V.j&&(ne=V.j-$t,$t=V.j,e(!(1&ne)),V.O+=W.R*ne,V.N+=W.B*(ne>>1),V.W+=W.B*(ne>>1),V.F!=null&&(V.J+=V.width*ne)),$t<fe&&(V.O+=V.v,V.N+=V.v>>1,V.W+=V.v>>1,V.F!=null&&(V.J+=V.v),V.ka=$t-V.j,V.U=V.va-V.v,V.T=fe-$t,gt=V.put(V))}lt+1!=W.Ic||se||(a(W.sa,W.ta-bt,_t,Dt+16*W.R,bt),a(W.qa,W.ra-Mt,Pt,mt+8*W.B,Mt),a(W.Ha,W.Ia-Mt,le,he+8*W.B,Mt))}if(!gt)return Ie(Y,6,"Output aborted.")}return 1}(t,i)),i.bc!=null&&i.bc(i),u&=1}return u?(t.cb=0,u):0}function Or(t,i,u,d,m){m=t[i+u+32*d]+(m>>3),t[i+u+32*d]=-256&m?0>m?0:255:m}function li(t,i,u,d,m,y){Or(t,i,0,u,d+m),Or(t,i,1,u,d+y),Or(t,i,2,u,d-y),Or(t,i,3,u,d-m)}function sr(t){return(20091*t>>16)+t}function Ui(t,i,u,d){var m,y=0,x=s(16);for(m=0;4>m;++m){var _=t[i+0]+t[i+8],P=t[i+0]-t[i+8],F=(35468*t[i+4]>>16)-sr(t[i+12]),H=sr(t[i+4])+(35468*t[i+12]>>16);x[y+0]=_+H,x[y+1]=P+F,x[y+2]=P-F,x[y+3]=_-H,y+=4,i++}for(m=y=0;4>m;++m)_=(t=x[y+0]+4)+x[y+8],P=t-x[y+8],F=(35468*x[y+4]>>16)-sr(x[y+12]),Or(u,d,0,0,_+(H=sr(x[y+4])+(35468*x[y+12]>>16))),Or(u,d,1,0,P+F),Or(u,d,2,0,P-F),Or(u,d,3,0,_-H),y++,d+=32}function Sa(t,i,u,d){var m=t[i+0]+4,y=35468*t[i+4]>>16,x=sr(t[i+4]),_=35468*t[i+1]>>16;li(u,d,0,m+x,t=sr(t[i+1]),_),li(u,d,1,m+y,t,_),li(u,d,2,m-y,t,_),li(u,d,3,m-x,t,_)}function js(t,i,u,d,m){Ui(t,i,u,d),m&&Ui(t,i+16,u,d+4)}function Pa(t,i,u,d){fn(t,i+0,u,d,1),fn(t,i+32,u,d+128,1)}function Os(t,i,u,d){var m;for(t=t[i+0]+4,m=0;4>m;++m)for(i=0;4>i;++i)Or(u,d,i,m,t)}function ka(t,i,u,d){t[i+0]&&nr(t,i+0,u,d),t[i+16]&&nr(t,i+16,u,d+4),t[i+32]&&nr(t,i+32,u,d+128),t[i+48]&&nr(t,i+48,u,d+128+4)}function Ms(t,i,u,d){var m,y=s(16);for(m=0;4>m;++m){var x=t[i+0+m]+t[i+12+m],_=t[i+4+m]+t[i+8+m],P=t[i+4+m]-t[i+8+m],F=t[i+0+m]-t[i+12+m];y[0+m]=x+_,y[8+m]=x-_,y[4+m]=F+P,y[12+m]=F-P}for(m=0;4>m;++m)x=(t=y[0+4*m]+3)+y[3+4*m],_=y[1+4*m]+y[2+4*m],P=y[1+4*m]-y[2+4*m],F=t-y[3+4*m],u[d+0]=x+_>>3,u[d+16]=F+P>>3,u[d+32]=x-_>>3,u[d+48]=F-P>>3,d+=64}function Hi(t,i,u){var d,m=i-32,y=gr,x=255-t[m-1];for(d=0;d<u;++d){var _,P=y,F=x+t[i-1];for(_=0;_<u;++_)t[i+_]=P[F+t[m+_]];i+=32}}function Bs(t,i){Hi(t,i,4)}function Es(t,i){Hi(t,i,8)}function Ts(t,i){Hi(t,i,16)}function ln(t,i){var u;for(u=0;16>u;++u)a(t,i+32*u,t,i-32,16)}function ui(t,i){var u;for(u=16;0<u;--u)l(t,i,t[i-1],16),i+=32}function ci(t,i,u){var d;for(d=0;16>d;++d)l(i,u+32*d,t,16)}function Ia(t,i){var u,d=16;for(u=0;16>u;++u)d+=t[i-1+32*u]+t[i+u-32];ci(d>>5,t,i)}function Xe(t,i){var u,d=8;for(u=0;16>u;++u)d+=t[i-1+32*u];ci(d>>4,t,i)}function qs(t,i){var u,d=8;for(u=0;16>u;++u)d+=t[i+u-32];ci(d>>4,t,i)}function Fa(t,i){ci(128,t,i)}function Gt(t,i,u){return t+2*i+u+2>>2}function Ds(t,i){var u,d=i-32;for(d=new Uint8Array([Gt(t[d-1],t[d+0],t[d+1]),Gt(t[d+0],t[d+1],t[d+2]),Gt(t[d+1],t[d+2],t[d+3]),Gt(t[d+2],t[d+3],t[d+4])]),u=0;4>u;++u)a(t,i+32*u,d,0,d.length)}function Rs(t,i){var u=t[i-1],d=t[i-1+32],m=t[i-1+64],y=t[i-1+96];Nt(t,i+0,16843009*Gt(t[i-1-32],u,d)),Nt(t,i+32,16843009*Gt(u,d,m)),Nt(t,i+64,16843009*Gt(d,m,y)),Nt(t,i+96,16843009*Gt(m,y,y))}function Zr(t,i){var u,d=4;for(u=0;4>u;++u)d+=t[i+u-32]+t[i-1+32*u];for(d>>=3,u=0;4>u;++u)l(t,i+32*u,d,4)}function Ca(t,i){var u=t[i-1+0],d=t[i-1+32],m=t[i-1+64],y=t[i-1-32],x=t[i+0-32],_=t[i+1-32],P=t[i+2-32],F=t[i+3-32];t[i+0+96]=Gt(d,m,t[i-1+96]),t[i+1+96]=t[i+0+64]=Gt(u,d,m),t[i+2+96]=t[i+1+64]=t[i+0+32]=Gt(y,u,d),t[i+3+96]=t[i+2+64]=t[i+1+32]=t[i+0+0]=Gt(x,y,u),t[i+3+64]=t[i+2+32]=t[i+1+0]=Gt(_,x,y),t[i+3+32]=t[i+2+0]=Gt(P,_,x),t[i+3+0]=Gt(F,P,_)}function ja(t,i){var u=t[i+1-32],d=t[i+2-32],m=t[i+3-32],y=t[i+4-32],x=t[i+5-32],_=t[i+6-32],P=t[i+7-32];t[i+0+0]=Gt(t[i+0-32],u,d),t[i+1+0]=t[i+0+32]=Gt(u,d,m),t[i+2+0]=t[i+1+32]=t[i+0+64]=Gt(d,m,y),t[i+3+0]=t[i+2+32]=t[i+1+64]=t[i+0+96]=Gt(m,y,x),t[i+3+32]=t[i+2+64]=t[i+1+96]=Gt(y,x,_),t[i+3+64]=t[i+2+96]=Gt(x,_,P),t[i+3+96]=Gt(_,P,P)}function hi(t,i){var u=t[i-1+0],d=t[i-1+32],m=t[i-1+64],y=t[i-1-32],x=t[i+0-32],_=t[i+1-32],P=t[i+2-32],F=t[i+3-32];t[i+0+0]=t[i+1+64]=y+x+1>>1,t[i+1+0]=t[i+2+64]=x+_+1>>1,t[i+2+0]=t[i+3+64]=_+P+1>>1,t[i+3+0]=P+F+1>>1,t[i+0+96]=Gt(m,d,u),t[i+0+64]=Gt(d,u,y),t[i+0+32]=t[i+1+96]=Gt(u,y,x),t[i+1+32]=t[i+2+96]=Gt(y,x,_),t[i+2+32]=t[i+3+96]=Gt(x,_,P),t[i+3+32]=Gt(_,P,F)}function Wi(t,i){var u=t[i+0-32],d=t[i+1-32],m=t[i+2-32],y=t[i+3-32],x=t[i+4-32],_=t[i+5-32],P=t[i+6-32],F=t[i+7-32];t[i+0+0]=u+d+1>>1,t[i+1+0]=t[i+0+64]=d+m+1>>1,t[i+2+0]=t[i+1+64]=m+y+1>>1,t[i+3+0]=t[i+2+64]=y+x+1>>1,t[i+0+32]=Gt(u,d,m),t[i+1+32]=t[i+0+96]=Gt(d,m,y),t[i+2+32]=t[i+1+96]=Gt(m,y,x),t[i+3+32]=t[i+2+96]=Gt(y,x,_),t[i+3+64]=Gt(x,_,P),t[i+3+96]=Gt(_,P,F)}function zs(t,i){var u=t[i-1+0],d=t[i-1+32],m=t[i-1+64],y=t[i-1+96];t[i+0+0]=u+d+1>>1,t[i+2+0]=t[i+0+32]=d+m+1>>1,t[i+2+32]=t[i+0+64]=m+y+1>>1,t[i+1+0]=Gt(u,d,m),t[i+3+0]=t[i+1+32]=Gt(d,m,y),t[i+3+32]=t[i+1+64]=Gt(m,y,y),t[i+3+64]=t[i+2+64]=t[i+0+96]=t[i+1+96]=t[i+2+96]=t[i+3+96]=y}function Us(t,i){var u=t[i-1+0],d=t[i-1+32],m=t[i-1+64],y=t[i-1+96],x=t[i-1-32],_=t[i+0-32],P=t[i+1-32],F=t[i+2-32];t[i+0+0]=t[i+2+32]=u+x+1>>1,t[i+0+32]=t[i+2+64]=d+u+1>>1,t[i+0+64]=t[i+2+96]=m+d+1>>1,t[i+0+96]=y+m+1>>1,t[i+3+0]=Gt(_,P,F),t[i+2+0]=Gt(x,_,P),t[i+1+0]=t[i+3+32]=Gt(u,x,_),t[i+1+32]=t[i+3+64]=Gt(d,u,x),t[i+1+64]=t[i+3+96]=Gt(m,d,u),t[i+1+96]=Gt(y,m,d)}function Hs(t,i){var u;for(u=0;8>u;++u)a(t,i+32*u,t,i-32,8)}function Vi(t,i){var u;for(u=0;8>u;++u)l(t,i,t[i-1],8),i+=32}function zn(t,i,u){var d;for(d=0;8>d;++d)l(i,u+32*d,t,8)}function bn(t,i){var u,d=8;for(u=0;8>u;++u)d+=t[i+u-32]+t[i-1+32*u];zn(d>>4,t,i)}function Ws(t,i){var u,d=4;for(u=0;8>u;++u)d+=t[i+u-32];zn(d>>3,t,i)}function Un(t,i){var u,d=4;for(u=0;8>u;++u)d+=t[i-1+32*u];zn(d>>3,t,i)}function Gi(t,i){zn(128,t,i)}function fi(t,i,u){var d=t[i-u],m=t[i+0],y=3*(m-d)+Ks[1020+t[i-2*u]-t[i+u]],x=Va[112+(y+4>>3)];t[i-u]=gr[255+d+Va[112+(y+3>>3)]],t[i+0]=gr[255+m-x]}function Oa(t,i,u,d){var m=t[i+0],y=t[i+u];return Ar[255+t[i-2*u]-t[i-u]]>d||Ar[255+y-m]>d}function Ma(t,i,u,d){return 4*Ar[255+t[i-u]-t[i+0]]+Ar[255+t[i-2*u]-t[i+u]]<=d}function Ba(t,i,u,d,m){var y=t[i-3*u],x=t[i-2*u],_=t[i-u],P=t[i+0],F=t[i+u],H=t[i+2*u],Z=t[i+3*u];return 4*Ar[255+_-P]+Ar[255+x-F]>d?0:Ar[255+t[i-4*u]-y]<=m&&Ar[255+y-x]<=m&&Ar[255+x-_]<=m&&Ar[255+Z-H]<=m&&Ar[255+H-F]<=m&&Ar[255+F-P]<=m}function Ea(t,i,u,d){var m=2*d+1;for(d=0;16>d;++d)Ma(t,i+d,u,m)&&fi(t,i+d,u)}function Qr(t,i,u,d){var m=2*d+1;for(d=0;16>d;++d)Ma(t,i+d*u,1,m)&&fi(t,i+d*u,1)}function un(t,i,u,d){var m;for(m=3;0<m;--m)Ea(t,i+=4*u,u,d)}function Vs(t,i,u,d){var m;for(m=3;0<m;--m)Qr(t,i+=4,u,d)}function yn(t,i,u,d,m,y,x,_){for(y=2*y+1;0<m--;){if(Ba(t,i,u,y,x))if(Oa(t,i,u,_))fi(t,i,u);else{var P=t,F=i,H=u,Z=P[F-2*H],Q=P[F-H],Y=P[F+0],vt=P[F+H],lt=P[F+2*H],W=27*(gt=Ks[1020+3*(Y-Q)+Ks[1020+Z-vt]])+63>>7,V=18*gt+63>>7,gt=9*gt+63>>7;P[F-3*H]=gr[255+P[F-3*H]+gt],P[F-2*H]=gr[255+Z+V],P[F-H]=gr[255+Q+W],P[F+0]=gr[255+Y-W],P[F+H]=gr[255+vt-V],P[F+2*H]=gr[255+lt-gt]}i+=d}}function Mr(t,i,u,d,m,y,x,_){for(y=2*y+1;0<m--;){if(Ba(t,i,u,y,x))if(Oa(t,i,u,_))fi(t,i,u);else{var P=t,F=i,H=u,Z=P[F-H],Q=P[F+0],Y=P[F+H],vt=Va[112+((lt=3*(Q-Z))+4>>3)],lt=Va[112+(lt+3>>3)],W=vt+1>>1;P[F-2*H]=gr[255+P[F-2*H]+W],P[F-H]=gr[255+Z+lt],P[F+0]=gr[255+Q-vt],P[F+H]=gr[255+Y-W]}i+=d}}function Yi(t,i,u,d,m,y){yn(t,i,u,1,16,d,m,y)}function Hn(t,i,u,d,m,y){yn(t,i,1,u,16,d,m,y)}function Gs(t,i,u,d,m,y){var x;for(x=3;0<x;--x)Mr(t,i+=4*u,u,1,16,d,m,y)}function di(t,i,u,d,m,y){var x;for(x=3;0<x;--x)Mr(t,i+=4,1,u,16,d,m,y)}function Ys(t,i,u,d,m,y,x,_){yn(t,i,m,1,8,y,x,_),yn(u,d,m,1,8,y,x,_)}function Ji(t,i,u,d,m,y,x,_){yn(t,i,1,m,8,y,x,_),yn(u,d,1,m,8,y,x,_)}function Xi(t,i,u,d,m,y,x,_){Mr(t,i+4*m,m,1,8,y,x,_),Mr(u,d+4*m,m,1,8,y,x,_)}function Ta(t,i,u,d,m,y,x,_){Mr(t,i+4,1,m,8,y,x,_),Mr(u,d+4,1,m,8,y,x,_)}function pi(){this.ba=new kr,this.ec=[],this.cc=[],this.Mc=[],this.Dc=this.Nc=this.dc=this.fc=0,this.Oa=new _e,this.memory=0,this.Ib="OutputFunc",this.Jb="OutputAlphaFunc",this.Nd="OutputRowFunc"}function Ki(){this.data=[],this.offset=this.kd=this.ha=this.w=0,this.na=[],this.xa=this.gb=this.Ja=this.Sa=this.P=0}function Zi(){this.nc=this.Ea=this.b=this.hc=0,this.K=[],this.w=0}function qa(){this.ua=0,this.Wa=new T,this.vb=new T,this.md=this.xc=this.wc=0,this.vc=[],this.Wb=0,this.Ya=new D,this.yc=new A}function Js(){this.xb=this.a=0,this.l=new oi,this.ca=new kr,this.V=[],this.Ba=0,this.Ta=[],this.Ua=0,this.m=new N,this.Pb=0,this.wd=new N,this.Ma=this.$=this.C=this.i=this.c=this.xd=0,this.s=new qa,this.ab=0,this.gc=c(4,Zi),this.Oc=0}function gi(){this.Lc=this.Z=this.$a=this.i=this.c=0,this.l=new oi,this.ic=0,this.ca=[],this.tb=0,this.qd=null,this.rd=0}function Wn(t,i,u,d,m,y,x){for(t=t==null?0:t[i+0],i=0;i<x;++i)m[y+i]=t+u[d+i]&255,t=m[y+i]}function Qi(t,i,u,d,m,y,x){var _;if(t==null)Wn(null,null,u,d,m,y,x);else for(_=0;_<x;++_)m[y+_]=t[i+_]+u[d+_]&255}function wn(t,i,u,d,m,y,x){if(t==null)Wn(null,null,u,d,m,y,x);else{var _,P=t[i+0],F=P,H=P;for(_=0;_<x;++_)F=H+(P=t[i+_])-F,H=u[d+_]+(-256&F?0>F?0:255:F)&255,F=P,m[y+_]=H}}function $i(t,i,u,d){var m=i.width,y=i.o;if(e(t!=null&&i!=null),0>u||0>=d||u+d>y)return null;if(!t.Cc){if(t.ga==null){var x;if(t.ga=new gi,(x=t.ga==null)||(x=i.width*i.o,e(t.Gb.length==0),t.Gb=s(x),t.Uc=0,t.Gb==null?x=0:(t.mb=t.Gb,t.nb=t.Uc,t.rc=null,x=1),x=!x),!x){x=t.ga;var _=t.Fa,P=t.P,F=t.qc,H=t.mb,Z=t.nb,Q=P+1,Y=F-1,vt=x.l;if(e(_!=null&&H!=null&&i!=null),Sn[0]=null,Sn[1]=Wn,Sn[2]=Qi,Sn[3]=wn,x.ca=H,x.tb=Z,x.c=i.width,x.i=i.height,e(0<x.c&&0<x.i),1>=F)i=0;else if(x.$a=_[P+0]>>0&3,x.Z=_[P+0]>>2&3,x.Lc=_[P+0]>>4&3,P=_[P+0]>>6&3,0>x.$a||1<x.$a||4<=x.Z||1<x.Lc||P)i=0;else if(vt.put=Gr,vt.ac=Ve,vt.bc=Yr,vt.ma=x,vt.width=i.width,vt.height=i.height,vt.Da=i.Da,vt.v=i.v,vt.va=i.va,vt.j=i.j,vt.o=i.o,x.$a)t:{e(x.$a==1),i=yr();e:for(;;){if(i==null){i=0;break t}if(e(x!=null),x.mc=i,i.c=x.c,i.i=x.i,i.l=x.l,i.l.ma=x,i.l.width=x.c,i.l.height=x.i,i.a=0,X(i.m,_,Q,Y),!Kr(x.c,x.i,1,i,null)||(i.ab==1&&i.gc[0].hc==3&&on(i.s)?(x.ic=1,_=i.c*i.i,i.Ta=null,i.Ua=0,i.V=s(_),i.Ba=0,i.V==null?(i.a=1,i=0):i=1):(x.ic=0,i=Rn(i,x.c)),!i))break e;i=1;break t}x.mc=null,i=0}else i=Y>=x.c*x.i;x=!i}if(x)return null;t.ga.Lc!=1?t.Ga=0:d=y-u}e(t.ga!=null),e(u+d<=y);t:{if(i=(_=t.ga).c,y=_.l.o,_.$a==0){if(Q=t.rc,Y=t.Vc,vt=t.Fa,P=t.P+1+u*i,F=t.mb,H=t.nb+u*i,e(P<=t.P+t.qc),_.Z!=0)for(e(Sn[_.Z]!=null),x=0;x<d;++x)Sn[_.Z](Q,Y,vt,P,F,H,i),Q=F,Y=H,H+=i,P+=i;else for(x=0;x<d;++x)a(F,H,vt,P,i),Q=F,Y=H,H+=i,P+=i;t.rc=Q,t.Vc=Y}else{if(e(_.mc!=null),i=u+d,e((x=_.mc)!=null),e(i<=x.i),x.C>=i)i=1;else if(_.ic||K(),_.ic){_=x.V,Q=x.Ba,Y=x.c;var lt=x.i,W=(vt=1,P=x.$/Y,F=x.$%Y,H=x.m,Z=x.s,x.$),V=Y*lt,gt=Y*i,bt=Z.wc,mt=W<gt?Be(Z,F,P):null;e(W<=V),e(i<=lt),e(on(Z));e:for(;;){for(;!H.h&&W<gt;){if(F&bt||(mt=Be(Z,F,P)),e(mt!=null),G(H),256>(lt=ar(mt.G[0],mt.H[0],H)))_[Q+W]=lt,++W,++F>=Y&&(F=0,++P<=i&&!(P%16)&&Cr(x,P));else{if(!(280>lt)){vt=0;break e}lt=Ir(lt-256,H);var Mt,_t=ar(mt.G[4],mt.H[4],H);if(G(H),!(W>=(_t=Jr(Y,_t=Ir(_t,H)))&&V-W>=lt)){vt=0;break e}for(Mt=0;Mt<lt;++Mt)_[Q+W+Mt]=_[Q+W+Mt-_t];for(W+=lt,F+=lt;F>=Y;)F-=Y,++P<=i&&!(P%16)&&Cr(x,P);W<gt&&F&bt&&(mt=Be(Z,F,P))}e(H.h==O(H))}Cr(x,P>i?i:P);break e}!vt||H.h&&W<V?(vt=0,x.a=H.h?5:3):x.$=W,i=vt}else i=jr(x,x.V,x.Ba,x.c,x.i,i,ri);if(!i){d=0;break t}}u+d>=y&&(t.Cc=1),d=1}if(!d)return null;if(t.Cc&&((d=t.ga)!=null&&(d.mc=null),t.ga=null,0<t.Ga))return alert("todo:WebPDequantizeLevels"),null}return t.nb+u*m}function o(t,i,u,d,m,y){for(;0<m--;){var x,_=t,P=i+(u?1:0),F=t,H=i+(u?0:3);for(x=0;x<d;++x){var Z=F[H+4*x];Z!=255&&(Z*=32897,_[P+4*x+0]=_[P+4*x+0]*Z>>23,_[P+4*x+1]=_[P+4*x+1]*Z>>23,_[P+4*x+2]=_[P+4*x+2]*Z>>23)}i+=y}}function v(t,i,u,d,m){for(;0<d--;){var y;for(y=0;y<u;++y){var x=t[i+2*y+0],_=15&(F=t[i+2*y+1]),P=4369*_,F=(240&F|F>>4)*P>>16;t[i+2*y+0]=(240&x|x>>4)*P>>16&240|(15&x|x<<4)*P>>16>>4&15,t[i+2*y+1]=240&F|_}i+=m}}function j(t,i,u,d,m,y,x,_){var P,F,H=255;for(F=0;F<m;++F){for(P=0;P<d;++P){var Z=t[i+P];y[x+4*P]=Z,H&=Z}i+=u,x+=_}return H!=255}function z(t,i,u,d,m){var y;for(y=0;y<m;++y)u[d+y]=t[i+y]>>8}function K(){Nr=o,ye=v,we=j,Ce=z}function ht(t,i,u){U[t]=function(d,m,y,x,_,P,F,H,Z,Q,Y,vt,lt,W,V,gt,bt){var mt,Mt=bt-1>>1,_t=_[P+0]|F[H+0]<<16,Dt=Z[Q+0]|Y[vt+0]<<16;e(d!=null);var Pt=3*_t+Dt+131074>>2;for(i(d[m+0],255&Pt,Pt>>16,lt,W),y!=null&&(Pt=3*Dt+_t+131074>>2,i(y[x+0],255&Pt,Pt>>16,V,gt)),mt=1;mt<=Mt;++mt){var le=_[P+mt]|F[H+mt]<<16,he=Z[Q+mt]|Y[vt+mt]<<16,se=_t+le+Dt+he+524296,ne=se+2*(le+Dt)>>3;Pt=ne+_t>>1,_t=(se=se+2*(_t+he)>>3)+le>>1,i(d[m+2*mt-1],255&Pt,Pt>>16,lt,W+(2*mt-1)*u),i(d[m+2*mt-0],255&_t,_t>>16,lt,W+(2*mt-0)*u),y!=null&&(Pt=se+Dt>>1,_t=ne+he>>1,i(y[x+2*mt-1],255&Pt,Pt>>16,V,gt+(2*mt-1)*u),i(y[x+2*mt+0],255&_t,_t>>16,V,gt+(2*mt+0)*u)),_t=le,Dt=he}1&bt||(Pt=3*_t+Dt+131074>>2,i(d[m+bt-1],255&Pt,Pt>>16,lt,W+(bt-1)*u),y!=null&&(Pt=3*Dt+_t+131074>>2,i(y[x+bt-1],255&Pt,Pt>>16,V,gt+(bt-1)*u)))}}function wt(){_r[Ga]=wu,_r[Ya]=Go,_r[zo]=xu,_r[Ja]=Yo,_r[Xa]=Jo,_r[Zs]=Xo,_r[Uo]=Lu,_r[Qs]=Go,_r[$s]=Yo,_r[Ka]=Jo,_r[to]=Xo}function Ot(t){return t&-16384?0>t?0:255:t>>Nu}function qt(t,i){return Ot((19077*t>>8)+(26149*i>>8)-14234)}function Qt(t,i,u){return Ot((19077*t>>8)-(6419*i>>8)-(13320*u>>8)+8708)}function Xt(t,i){return Ot((19077*t>>8)+(33050*i>>8)-17685)}function ie(t,i,u,d,m){d[m+0]=qt(t,u),d[m+1]=Qt(t,i,u),d[m+2]=Xt(t,i)}function Ne(t,i,u,d,m){d[m+0]=Xt(t,i),d[m+1]=Qt(t,i,u),d[m+2]=qt(t,u)}function Se(t,i,u,d,m){var y=Qt(t,i,u);i=y<<3&224|Xt(t,i)>>3,d[m+0]=248&qt(t,u)|y>>5,d[m+1]=i}function Ee(t,i,u,d,m){var y=240&Xt(t,i)|15;d[m+0]=240&qt(t,u)|Qt(t,i,u)>>4,d[m+1]=y}function Ke(t,i,u,d,m){d[m+0]=255,ie(t,i,u,d,m+1)}function ze(t,i,u,d,m){Ne(t,i,u,d,m),d[m+3]=255}function Br(t,i,u,d,m){ie(t,i,u,d,m),d[m+3]=255}function pr(t,i){return 0>t?0:t>i?i:t}function $r(t,i,u){U[t]=function(d,m,y,x,_,P,F,H,Z){for(var Q=H+(-2&Z)*u;H!=Q;)i(d[m+0],y[x+0],_[P+0],F,H),i(d[m+1],y[x+0],_[P+0],F,H+u),m+=2,++x,++P,H+=2*u;1&Z&&i(d[m+0],y[x+0],_[P+0],F,H)}}function Da(t,i,u){return u==0?t==0?i==0?6:5:i==0?4:0:u}function ta(t,i,u,d,m){switch(t>>>30){case 3:fn(i,u,d,m,0);break;case 2:Fe(i,u,d,m);break;case 1:nr(i,u,d,m)}}function ea(t,i){var u,d,m=i.M,y=i.Nb,x=t.oc,_=t.pc+40,P=t.oc,F=t.pc+584,H=t.oc,Z=t.pc+600;for(u=0;16>u;++u)x[_+32*u-1]=129;for(u=0;8>u;++u)P[F+32*u-1]=129,H[Z+32*u-1]=129;for(0<m?x[_-1-32]=P[F-1-32]=H[Z-1-32]=129:(l(x,_-32-1,127,21),l(P,F-32-1,127,9),l(H,Z-32-1,127,9)),d=0;d<t.za;++d){var Q=i.ya[i.aa+d];if(0<d){for(u=-1;16>u;++u)a(x,_+32*u-4,x,_+32*u+12,4);for(u=-1;8>u;++u)a(P,F+32*u-4,P,F+32*u+4,4),a(H,Z+32*u-4,H,Z+32*u+4,4)}var Y=t.Gd,vt=t.Hd+d,lt=Q.ad,W=Q.Hc;if(0<m&&(a(x,_-32,Y[vt].y,0,16),a(P,F-32,Y[vt].f,0,8),a(H,Z-32,Y[vt].ea,0,8)),Q.Za){var V=x,gt=_-32+16;for(0<m&&(d>=t.za-1?l(V,gt,Y[vt].y[15],4):a(V,gt,Y[vt+1].y,0,4)),u=0;4>u;u++)V[gt+128+u]=V[gt+256+u]=V[gt+384+u]=V[gt+0+u];for(u=0;16>u;++u,W<<=2)V=x,gt=_+Zo[u],Er[Q.Ob[u]](V,gt),ta(W,lt,16*+u,V,gt)}else if(V=Da(d,m,Q.Ob[0]),_n[V](x,_),W!=0)for(u=0;16>u;++u,W<<=2)ta(W,lt,16*+u,x,_+Zo[u]);for(u=Q.Gc,V=Da(d,m,Q.Dd),pn[V](P,F),pn[V](H,Z),W=lt,V=P,gt=F,255&(Q=u>>0)&&(170&Q?sa(W,256,V,gt):xr(W,256,V,gt)),Q=H,W=Z,255&(u>>=8)&&(170&u?sa(lt,320,Q,W):xr(lt,320,Q,W)),m<t.Ub-1&&(a(Y[vt].y,0,x,_+480,16),a(Y[vt].f,0,P,F+224,8),a(Y[vt].ea,0,H,Z+224,8)),u=8*y*t.B,Y=t.sa,vt=t.ta+16*d+16*y*t.R,lt=t.qa,Q=t.ra+8*d+u,W=t.Ha,V=t.Ia+8*d+u,u=0;16>u;++u)a(Y,vt+u*t.R,x,_+32*u,16);for(u=0;8>u;++u)a(lt,Q+u*t.B,P,F+32*u,8),a(W,V+u*t.B,H,Z+32*u,8)}}function mi(t,i,u,d,m,y,x,_,P){var F=[0],H=[0],Z=0,Q=P!=null?P.kd:0,Y=P??new Ki;if(t==null||12>u)return 7;Y.data=t,Y.w=i,Y.ha=u,i=[i],u=[u],Y.gb=[Y.gb];t:{var vt=i,lt=u,W=Y.gb;if(e(t!=null),e(lt!=null),e(W!=null),W[0]=0,12<=lt[0]&&!r(t,vt[0],"RIFF")){if(r(t,vt[0]+8,"WEBP")){W=3;break t}var V=St(t,vt[0]+4);if(12>V||4294967286<V){W=3;break t}if(Q&&V>lt[0]-8){W=7;break t}W[0]=V,vt[0]+=12,lt[0]-=12}W=0}if(W!=0)return W;for(V=0<Y.gb[0],u=u[0];;){t:{var gt=t;lt=i,W=u;var bt=F,mt=H,Mt=vt=[0];if((Pt=Z=[Z])[0]=0,8>W[0])W=7;else{if(!r(gt,lt[0],"VP8X")){if(St(gt,lt[0]+4)!=10){W=3;break t}if(18>W[0]){W=7;break t}var _t=St(gt,lt[0]+8),Dt=1+It(gt,lt[0]+12);if(2147483648<=Dt*(gt=1+It(gt,lt[0]+15))){W=3;break t}Mt!=null&&(Mt[0]=_t),bt!=null&&(bt[0]=Dt),mt!=null&&(mt[0]=gt),lt[0]+=18,W[0]-=18,Pt[0]=1}W=0}}if(Z=Z[0],vt=vt[0],W!=0)return W;if(lt=!!(2&vt),!V&&Z)return 3;if(y!=null&&(y[0]=!!(16&vt)),x!=null&&(x[0]=lt),_!=null&&(_[0]=0),x=F[0],vt=H[0],Z&&lt&&P==null){W=0;break}if(4>u){W=7;break}if(V&&Z||!V&&!Z&&!r(t,i[0],"ALPH")){u=[u],Y.na=[Y.na],Y.P=[Y.P],Y.Sa=[Y.Sa];t:{_t=t,W=i,V=u;var Pt=Y.gb;bt=Y.na,mt=Y.P,Mt=Y.Sa,Dt=22,e(_t!=null),e(V!=null),gt=W[0];var le=V[0];for(e(bt!=null),e(Mt!=null),bt[0]=null,mt[0]=null,Mt[0]=0;;){if(W[0]=gt,V[0]=le,8>le){W=7;break t}var he=St(_t,gt+4);if(4294967286<he){W=3;break t}var se=8+he+1&-2;if(Dt+=se,0<Pt&&Dt>Pt){W=3;break t}if(!r(_t,gt,"VP8 ")||!r(_t,gt,"VP8L")){W=0;break t}if(le[0]<se){W=7;break t}r(_t,gt,"ALPH")||(bt[0]=_t,mt[0]=gt+8,Mt[0]=he),gt+=se,le-=se}}if(u=u[0],Y.na=Y.na[0],Y.P=Y.P[0],Y.Sa=Y.Sa[0],W!=0)break}u=[u],Y.Ja=[Y.Ja],Y.xa=[Y.xa];t:if(Pt=t,W=i,V=u,bt=Y.gb[0],mt=Y.Ja,Mt=Y.xa,_t=W[0],gt=!r(Pt,_t,"VP8 "),Dt=!r(Pt,_t,"VP8L"),e(Pt!=null),e(V!=null),e(mt!=null),e(Mt!=null),8>V[0])W=7;else{if(gt||Dt){if(Pt=St(Pt,_t+4),12<=bt&&Pt>bt-12){W=3;break t}if(Q&&Pt>V[0]-8){W=7;break t}mt[0]=Pt,W[0]+=8,V[0]-=8,Mt[0]=Dt}else Mt[0]=5<=V[0]&&Pt[_t+0]==47&&!(Pt[_t+4]>>5),mt[0]=V[0];W=0}if(u=u[0],Y.Ja=Y.Ja[0],Y.xa=Y.xa[0],i=i[0],W!=0)break;if(4294967286<Y.Ja)return 3;if(_==null||lt||(_[0]=Y.xa?2:1),x=[x],vt=[vt],Y.xa){if(5>u){W=7;break}_=x,Q=vt,lt=y,t==null||5>u?t=0:5<=u&&t[i+0]==47&&!(t[i+4]>>5)?(V=[0],Pt=[0],bt=[0],X(mt=new N,t,i,u),Rt(mt,V,Pt,bt)?(_!=null&&(_[0]=V[0]),Q!=null&&(Q[0]=Pt[0]),lt!=null&&(lt[0]=bt[0]),t=1):t=0):t=0}else{if(10>u){W=7;break}_=vt,t==null||10>u||!Na(t,i+3,u-3)?t=0:(Q=t[i+0]|t[i+1]<<8|t[i+2]<<16,lt=16383&(t[i+7]<<8|t[i+6]),t=16383&(t[i+9]<<8|t[i+8]),1&Q||3<(Q>>1&7)||!(Q>>4&1)||Q>>5>=Y.Ja||!lt||!t?t=0:(x&&(x[0]=lt),_&&(_[0]=t),t=1))}if(!t||(x=x[0],vt=vt[0],Z&&(F[0]!=x||H[0]!=vt)))return 3;P!=null&&(P[0]=Y,P.offset=i-P.w,e(4294967286>i-P.w),e(P.offset==P.ha-u));break}return W==0||W==7&&Z&&P==null?(y!=null&&(y[0]|=Y.na!=null&&0<Y.na.length),d!=null&&(d[0]=x),m!=null&&(m[0]=vt),0):W}function ra(t,i,u){var d=i.width,m=i.height,y=0,x=0,_=d,P=m;if(i.Da=t!=null&&0<t.Da,i.Da&&(_=t.cd,P=t.bd,y=t.v,x=t.j,11>u||(y&=-2,x&=-2),0>y||0>x||0>=_||0>=P||y+_>d||x+P>m))return 0;if(i.v=y,i.j=x,i.va=y+_,i.o=x+P,i.U=_,i.T=P,i.da=t!=null&&0<t.da,i.da){if(!Zt(_,P,u=[t.ib],y=[t.hb]))return 0;i.ib=u[0],i.hb=y[0]}return i.ob=t!=null&&t.ob,i.Kb=t==null||!t.Sd,i.da&&(i.ob=i.ib<3*d/4&&i.hb<3*m/4,i.Kb=0),1}function na(t){if(t==null)return 2;if(11>t.S){var i=t.f.RGBA;i.fb+=(t.height-1)*i.A,i.A=-i.A}else i=t.f.kb,t=t.height,i.O+=(t-1)*i.fa,i.fa=-i.fa,i.N+=(t-1>>1)*i.Ab,i.Ab=-i.Ab,i.W+=(t-1>>1)*i.Db,i.Db=-i.Db,i.F!=null&&(i.J+=(t-1)*i.lb,i.lb=-i.lb);return 0}function vi(t,i,u,d){if(d==null||0>=t||0>=i)return 2;if(u!=null){if(u.Da){var m=u.cd,y=u.bd,x=-2&u.v,_=-2&u.j;if(0>x||0>_||0>=m||0>=y||x+m>t||_+y>i)return 2;t=m,i=y}if(u.da){if(!Zt(t,i,m=[u.ib],y=[u.hb]))return 2;t=m[0],i=y[0]}}d.width=t,d.height=i;t:{var P=d.width,F=d.height;if(t=d.S,0>=P||0>=F||!(t>=Ga&&13>t))t=2;else{if(0>=d.Rd&&d.sd==null){x=y=m=i=0;var H=(_=P*Qo[t])*F;if(11>t||(y=(F+1)/2*(i=(P+1)/2),t==12&&(x=(m=P)*F)),(F=s(H+2*y+x))==null){t=1;break t}d.sd=F,11>t?((P=d.f.RGBA).eb=F,P.fb=0,P.A=_,P.size=H):((P=d.f.kb).y=F,P.O=0,P.fa=_,P.Fd=H,P.f=F,P.N=0+H,P.Ab=i,P.Cd=y,P.ea=F,P.W=0+H+y,P.Db=i,P.Ed=y,t==12&&(P.F=F,P.J=0+H+2*y),P.Tc=x,P.lb=m)}if(i=1,m=d.S,y=d.width,x=d.height,m>=Ga&&13>m)if(11>m)t=d.f.RGBA,i&=(_=Math.abs(t.A))*(x-1)+y<=t.size,i&=_>=y*Qo[m],i&=t.eb!=null;else{t=d.f.kb,_=(y+1)/2,H=(x+1)/2,P=Math.abs(t.fa),F=Math.abs(t.Ab);var Z=Math.abs(t.Db),Q=Math.abs(t.lb),Y=Q*(x-1)+y;i&=P*(x-1)+y<=t.Fd,i&=F*(H-1)+_<=t.Cd,i=(i&=Z*(H-1)+_<=t.Ed)&P>=y&F>=_&Z>=_,i&=t.y!=null,i&=t.f!=null,i&=t.ea!=null,m==12&&(i&=Q>=y,i&=Y<=t.Tc,i&=t.F!=null)}else i=0;t=i?0:2}}return t!=0||u!=null&&u.fd&&(t=na(d)),t}var Ge=64,bi=[0,1,3,7,15,31,63,127,255,511,1023,2047,4095,8191,16383,32767,65535,131071,262143,524287,1048575,2097151,4194303,8388607,16777215],yi=24,wi=32,ia=8,or=[0,0,1,1,2,2,2,2,3,3,3,3,3,3,3,3,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7];At("Predictor0","PredictorAdd0"),U.Predictor0=function(){return **********},U.Predictor1=function(t){return t},U.Predictor2=function(t,i,u){return i[u+0]},U.Predictor3=function(t,i,u){return i[u+1]},U.Predictor4=function(t,i,u){return i[u-1]},U.Predictor5=function(t,i,u){return kt(kt(t,i[u+1]),i[u+0])},U.Predictor6=function(t,i,u){return kt(t,i[u-1])},U.Predictor7=function(t,i,u){return kt(t,i[u+0])},U.Predictor8=function(t,i,u){return kt(i[u-1],i[u+0])},U.Predictor9=function(t,i,u){return kt(i[u+0],i[u+1])},U.Predictor10=function(t,i,u){return kt(kt(t,i[u-1]),kt(i[u+0],i[u+1]))},U.Predictor11=function(t,i,u){var d=i[u+0];return 0>=te(d>>24&255,t>>24&255,(i=i[u-1])>>24&255)+te(d>>16&255,t>>16&255,i>>16&255)+te(d>>8&255,t>>8&255,i>>8&255)+te(255&d,255&t,255&i)?d:t},U.Predictor12=function(t,i,u){var d=i[u+0];return(Tt((t>>24&255)+(d>>24&255)-((i=i[u-1])>>24&255))<<24|Tt((t>>16&255)+(d>>16&255)-(i>>16&255))<<16|Tt((t>>8&255)+(d>>8&255)-(i>>8&255))<<8|Tt((255&t)+(255&d)-(255&i)))>>>0},U.Predictor13=function(t,i,u){var d=i[u-1];return(Yt((t=kt(t,i[u+0]))>>24&255,d>>24&255)<<24|Yt(t>>16&255,d>>16&255)<<16|Yt(t>>8&255,d>>8&255)<<8|Yt(t>>0&255,d>>0&255))>>>0};var Xs=U.PredictorAdd0;U.PredictorAdd1=ee,At("Predictor2","PredictorAdd2"),At("Predictor3","PredictorAdd3"),At("Predictor4","PredictorAdd4"),At("Predictor5","PredictorAdd5"),At("Predictor6","PredictorAdd6"),At("Predictor7","PredictorAdd7"),At("Predictor8","PredictorAdd8"),At("Predictor9","PredictorAdd9"),At("Predictor10","PredictorAdd10"),At("Predictor11","PredictorAdd11"),At("Predictor12","PredictorAdd12"),At("Predictor13","PredictorAdd13");var aa=U.PredictorAdd2;re("ColorIndexInverseTransform","MapARGB","32b",function(t){return t>>8&255},function(t){return t}),re("VP8LColorIndexInverseTransformAlpha","MapAlpha","8b",function(t){return t},function(t){return t>>8&255});var Ra,wr=U.ColorIndexInverseTransform,xi=U.MapARGB,za=U.VP8LColorIndexInverseTransformAlpha,Ua=U.MapAlpha,xn=U.VP8LPredictorsAdd=[];xn.length=16,(U.VP8LPredictors=[]).length=16,(U.VP8LPredictorsAdd_C=[]).length=16,(U.VP8LPredictors_C=[]).length=16;var Vn,lr,rr,Ln,cn,hn,Li,fn,Fe,sa,nr,xr,Ni,Ha,oa,Gn,Yn,Nn,Jn,Ai,Xn,An,la,Lr,Nr,ye,we,Ce,Re=s(511),dn=s(2041),ua=s(225),_i=s(767),Wa=0,Ks=dn,Va=ua,gr=_i,Ar=Re,Ga=0,Ya=1,zo=2,Ja=3,Xa=4,Zs=5,Uo=6,Qs=7,$s=8,Ka=9,to=10,su=[2,3,7],ou=[3,3,11],Ho=[280,256,256,256,40],lu=[0,1,1,1,0],uu=[17,18,0,1,2,3,4,5,16,6,7,8,9,10,11,12,13,14,15],cu=[24,7,23,25,40,6,39,41,22,26,38,42,56,5,55,57,21,27,54,58,37,43,72,4,71,73,20,28,53,59,70,74,36,44,88,69,75,52,60,3,87,89,19,29,86,90,35,45,68,76,85,91,51,61,104,2,103,105,18,30,102,106,34,46,84,92,67,77,101,107,50,62,120,1,119,121,83,93,17,31,100,108,66,78,118,122,33,47,117,123,49,63,99,109,82,94,0,116,124,65,79,16,32,98,110,48,115,125,81,95,64,114,126,97,111,80,113,127,96,112],hu=[2954,2956,2958,2962,2970,2986,3018,3082,3212,3468,3980,5004],fu=8,eo=[4,5,6,7,8,9,10,10,11,12,13,14,15,16,17,17,18,19,20,20,21,21,22,22,23,23,24,25,25,26,27,28,29,30,31,32,33,34,35,36,37,37,38,39,40,41,42,43,44,45,46,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,76,77,78,79,80,81,82,83,84,85,86,87,88,89,91,93,95,96,98,100,101,102,104,106,108,110,112,114,116,118,122,124,126,128,130,132,134,136,138,140,143,145,148,151,154,157],ro=[4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,60,62,64,66,68,70,72,74,76,78,80,82,84,86,88,90,92,94,96,98,100,102,104,106,108,110,112,114,116,119,122,125,128,131,134,137,140,143,146,149,152,155,158,161,164,167,170,173,177,181,185,189,193,197,201,205,209,213,217,221,225,229,234,239,245,249,254,259,264,269,274,279,284],ca=null,du=[[173,148,140,0],[176,155,140,135,0],[180,157,141,134,130,0],[254,254,243,230,196,177,153,140,133,130,129,0]],pu=[0,1,4,8,5,2,3,6,9,12,13,10,7,11,14,15],Wo=[-0,1,-1,2,-2,3,4,6,-3,5,-4,-5,-6,7,-7,8,-8,-9],gu=[[[[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128]],[[253,136,254,255,228,219,128,128,128,128,128],[189,129,242,255,227,213,255,219,128,128,128],[106,126,227,252,214,209,255,255,128,128,128]],[[1,98,248,255,236,226,255,255,128,128,128],[181,133,238,254,221,234,255,154,128,128,128],[78,134,202,247,198,180,255,219,128,128,128]],[[1,185,249,255,243,255,128,128,128,128,128],[184,150,247,255,236,224,128,128,128,128,128],[77,110,216,255,236,230,128,128,128,128,128]],[[1,101,251,255,241,255,128,128,128,128,128],[170,139,241,252,236,209,255,255,128,128,128],[37,116,196,243,228,255,255,255,128,128,128]],[[1,204,254,255,245,255,128,128,128,128,128],[207,160,250,255,238,128,128,128,128,128,128],[102,103,231,255,211,171,128,128,128,128,128]],[[1,152,252,255,240,255,128,128,128,128,128],[177,135,243,255,234,225,128,128,128,128,128],[80,129,211,255,194,224,128,128,128,128,128]],[[1,1,255,128,128,128,128,128,128,128,128],[246,1,255,128,128,128,128,128,128,128,128],[255,128,128,128,128,128,128,128,128,128,128]]],[[[198,35,237,223,193,187,162,160,145,155,62],[131,45,198,221,172,176,220,157,252,221,1],[68,47,146,208,149,167,221,162,255,223,128]],[[1,149,241,255,221,224,255,255,128,128,128],[184,141,234,253,222,220,255,199,128,128,128],[81,99,181,242,176,190,249,202,255,255,128]],[[1,129,232,253,214,197,242,196,255,255,128],[99,121,210,250,201,198,255,202,128,128,128],[23,91,163,242,170,187,247,210,255,255,128]],[[1,200,246,255,234,255,128,128,128,128,128],[109,178,241,255,231,245,255,255,128,128,128],[44,130,201,253,205,192,255,255,128,128,128]],[[1,132,239,251,219,209,255,165,128,128,128],[94,136,225,251,218,190,255,255,128,128,128],[22,100,174,245,186,161,255,199,128,128,128]],[[1,182,249,255,232,235,128,128,128,128,128],[124,143,241,255,227,234,128,128,128,128,128],[35,77,181,251,193,211,255,205,128,128,128]],[[1,157,247,255,236,231,255,255,128,128,128],[121,141,235,255,225,227,255,255,128,128,128],[45,99,188,251,195,217,255,224,128,128,128]],[[1,1,251,255,213,255,128,128,128,128,128],[203,1,248,255,255,128,128,128,128,128,128],[137,1,177,255,224,255,128,128,128,128,128]]],[[[253,9,248,251,207,208,255,192,128,128,128],[175,13,224,243,193,185,249,198,255,255,128],[73,17,171,221,161,179,236,167,255,234,128]],[[1,95,247,253,212,183,255,255,128,128,128],[239,90,244,250,211,209,255,255,128,128,128],[155,77,195,248,188,195,255,255,128,128,128]],[[1,24,239,251,218,219,255,205,128,128,128],[201,51,219,255,196,186,128,128,128,128,128],[69,46,190,239,201,218,255,228,128,128,128]],[[1,191,251,255,255,128,128,128,128,128,128],[223,165,249,255,213,255,128,128,128,128,128],[141,124,248,255,255,128,128,128,128,128,128]],[[1,16,248,255,255,128,128,128,128,128,128],[190,36,230,255,236,255,128,128,128,128,128],[149,1,255,128,128,128,128,128,128,128,128]],[[1,226,255,128,128,128,128,128,128,128,128],[247,192,255,128,128,128,128,128,128,128,128],[240,128,255,128,128,128,128,128,128,128,128]],[[1,134,252,255,255,128,128,128,128,128,128],[213,62,250,255,255,128,128,128,128,128,128],[55,93,255,128,128,128,128,128,128,128,128]],[[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128]]],[[[202,24,213,235,186,191,220,160,240,175,255],[126,38,182,232,169,184,228,174,255,187,128],[61,46,138,219,151,178,240,170,255,216,128]],[[1,112,230,250,199,191,247,159,255,255,128],[166,109,228,252,211,215,255,174,128,128,128],[39,77,162,232,172,180,245,178,255,255,128]],[[1,52,220,246,198,199,249,220,255,255,128],[124,74,191,243,183,193,250,221,255,255,128],[24,71,130,219,154,170,243,182,255,255,128]],[[1,182,225,249,219,240,255,224,128,128,128],[149,150,226,252,216,205,255,171,128,128,128],[28,108,170,242,183,194,254,223,255,255,128]],[[1,81,230,252,204,203,255,192,128,128,128],[123,102,209,247,188,196,255,233,128,128,128],[20,95,153,243,164,173,255,203,128,128,128]],[[1,222,248,255,216,213,128,128,128,128,128],[168,175,246,252,235,205,255,255,128,128,128],[47,116,215,255,211,212,255,255,128,128,128]],[[1,121,236,253,212,214,255,255,128,128,128],[141,84,213,252,201,202,255,219,128,128,128],[42,80,160,240,162,185,255,205,128,128,128]],[[1,1,255,128,128,128,128,128,128,128,128],[244,1,255,128,128,128,128,128,128,128,128],[238,1,255,128,128,128,128,128,128,128,128]]]],mu=[[[231,120,48,89,115,113,120,152,112],[152,179,64,126,170,118,46,70,95],[175,69,143,80,85,82,72,155,103],[56,58,10,171,218,189,17,13,152],[114,26,17,163,44,195,21,10,173],[121,24,80,195,26,62,44,64,85],[144,71,10,38,171,213,144,34,26],[170,46,55,19,136,160,33,206,71],[63,20,8,114,114,208,12,9,226],[81,40,11,96,182,84,29,16,36]],[[134,183,89,137,98,101,106,165,148],[72,187,100,130,157,111,32,75,80],[66,102,167,99,74,62,40,234,128],[41,53,9,178,241,141,26,8,107],[74,43,26,146,73,166,49,23,157],[65,38,105,160,51,52,31,115,128],[104,79,12,27,217,255,87,17,7],[87,68,71,44,114,51,15,186,23],[47,41,14,110,182,183,21,17,194],[66,45,25,102,197,189,23,18,22]],[[88,88,147,150,42,46,45,196,205],[43,97,183,117,85,38,35,179,61],[39,53,200,87,26,21,43,232,171],[56,34,51,104,114,102,29,93,77],[39,28,85,171,58,165,90,98,64],[34,22,116,206,23,34,43,166,73],[107,54,32,26,51,1,81,43,31],[68,25,106,22,64,171,36,225,114],[34,19,21,102,132,188,16,76,124],[62,18,78,95,85,57,50,48,51]],[[193,101,35,159,215,111,89,46,111],[60,148,31,172,219,228,21,18,111],[112,113,77,85,179,255,38,120,114],[40,42,1,196,245,209,10,25,109],[88,43,29,140,166,213,37,43,154],[61,63,30,155,67,45,68,1,209],[100,80,8,43,154,1,51,26,71],[142,78,78,16,255,128,34,197,171],[41,40,5,102,211,183,4,1,221],[51,50,17,168,209,192,23,25,82]],[[138,31,36,171,27,166,38,44,229],[67,87,58,169,82,115,26,59,179],[63,59,90,180,59,166,93,73,154],[40,40,21,116,143,209,34,39,175],[47,15,16,183,34,223,49,45,183],[46,17,33,183,6,98,15,32,183],[57,46,22,24,128,1,54,17,37],[65,32,73,115,28,128,23,128,205],[40,3,9,115,51,192,18,6,223],[87,37,9,115,59,77,64,21,47]],[[104,55,44,218,9,54,53,130,226],[64,90,70,205,40,41,23,26,57],[54,57,112,184,5,41,38,166,213],[30,34,26,133,152,116,10,32,134],[39,19,53,221,26,114,32,73,255],[31,9,65,234,2,15,1,118,73],[75,32,12,51,192,255,160,43,51],[88,31,35,67,102,85,55,186,85],[56,21,23,111,59,205,45,37,192],[55,38,70,124,73,102,1,34,98]],[[125,98,42,88,104,85,117,175,82],[95,84,53,89,128,100,113,101,45],[75,79,123,47,51,128,81,171,1],[57,17,5,71,102,57,53,41,49],[38,33,13,121,57,73,26,1,85],[41,10,67,138,77,110,90,47,114],[115,21,2,10,102,255,166,23,6],[101,29,16,10,85,128,101,196,26],[57,18,10,102,102,213,34,20,43],[117,20,15,36,163,128,68,1,26]],[[102,61,71,37,34,53,31,243,192],[69,60,71,38,73,119,28,222,37],[68,45,128,34,1,47,11,245,171],[62,17,19,70,146,85,55,62,70],[37,43,37,154,100,163,85,160,1],[63,9,92,136,28,64,32,201,85],[75,15,9,9,64,255,184,119,16],[86,6,28,5,64,255,25,248,1],[56,8,17,132,137,255,55,116,128],[58,15,20,82,135,57,26,121,40]],[[164,50,31,137,154,133,25,35,218],[51,103,44,131,131,123,31,6,158],[86,40,64,135,148,224,45,183,128],[22,26,17,131,240,154,14,1,209],[45,16,21,91,64,222,7,1,197],[56,21,39,155,60,138,23,102,213],[83,12,13,54,192,255,68,47,28],[85,26,85,85,128,128,32,146,171],[18,11,7,63,144,171,4,4,246],[35,27,10,146,174,171,12,26,128]],[[190,80,35,99,180,80,126,54,45],[85,126,47,87,176,51,41,20,32],[101,75,128,139,118,146,116,128,85],[56,41,15,176,236,85,37,9,62],[71,30,17,119,118,255,17,18,138],[101,38,60,138,55,70,43,26,142],[146,36,19,30,171,255,97,27,20],[138,45,61,62,219,1,81,188,64],[32,41,20,117,151,142,20,21,163],[112,19,12,61,195,128,48,4,24]]],vu=[[[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[176,246,255,255,255,255,255,255,255,255,255],[223,241,252,255,255,255,255,255,255,255,255],[249,253,253,255,255,255,255,255,255,255,255]],[[255,244,252,255,255,255,255,255,255,255,255],[234,254,254,255,255,255,255,255,255,255,255],[253,255,255,255,255,255,255,255,255,255,255]],[[255,246,254,255,255,255,255,255,255,255,255],[239,253,254,255,255,255,255,255,255,255,255],[254,255,254,255,255,255,255,255,255,255,255]],[[255,248,254,255,255,255,255,255,255,255,255],[251,255,254,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[251,254,254,255,255,255,255,255,255,255,255],[254,255,254,255,255,255,255,255,255,255,255]],[[255,254,253,255,254,255,255,255,255,255,255],[250,255,254,255,254,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[217,255,255,255,255,255,255,255,255,255,255],[225,252,241,253,255,255,254,255,255,255,255],[234,250,241,250,253,255,253,254,255,255,255]],[[255,254,255,255,255,255,255,255,255,255,255],[223,254,254,255,255,255,255,255,255,255,255],[238,253,254,254,255,255,255,255,255,255,255]],[[255,248,254,255,255,255,255,255,255,255,255],[249,254,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,255,255,255,255,255,255,255,255,255],[247,254,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[252,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,254,255,255,255,255,255,255,255,255],[253,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,253,255,255,255,255,255,255,255,255],[250,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[186,251,250,255,255,255,255,255,255,255,255],[234,251,244,254,255,255,255,255,255,255,255],[251,251,243,253,254,255,254,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[236,253,254,255,255,255,255,255,255,255,255],[251,253,253,254,254,255,255,255,255,255,255]],[[255,254,254,255,255,255,255,255,255,255,255],[254,254,254,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,255,255,255,255,255,255,255,255,255],[254,254,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[248,255,255,255,255,255,255,255,255,255,255],[250,254,252,254,255,255,255,255,255,255,255],[248,254,249,253,255,255,255,255,255,255,255]],[[255,253,253,255,255,255,255,255,255,255,255],[246,253,253,255,255,255,255,255,255,255,255],[252,254,251,254,254,255,255,255,255,255,255]],[[255,254,252,255,255,255,255,255,255,255,255],[248,254,253,255,255,255,255,255,255,255,255],[253,255,254,254,255,255,255,255,255,255,255]],[[255,251,254,255,255,255,255,255,255,255,255],[245,251,254,255,255,255,255,255,255,255,255],[253,253,254,255,255,255,255,255,255,255,255]],[[255,251,253,255,255,255,255,255,255,255,255],[252,253,254,255,255,255,255,255,255,255,255],[255,254,255,255,255,255,255,255,255,255,255]],[[255,252,255,255,255,255,255,255,255,255,255],[249,255,254,255,255,255,255,255,255,255,255],[255,255,254,255,255,255,255,255,255,255,255]],[[255,255,253,255,255,255,255,255,255,255,255],[250,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]]],bu=[0,1,2,3,6,4,5,6,6,6,6,6,6,6,6,7,0],_n=[],Er=[],pn=[],yu=1,Vo=2,Sn=[],_r=[];ht("UpsampleRgbLinePair",ie,3),ht("UpsampleBgrLinePair",Ne,3),ht("UpsampleRgbaLinePair",Br,4),ht("UpsampleBgraLinePair",ze,4),ht("UpsampleArgbLinePair",Ke,4),ht("UpsampleRgba4444LinePair",Ee,2),ht("UpsampleRgb565LinePair",Se,2);var wu=U.UpsampleRgbLinePair,xu=U.UpsampleBgrLinePair,Go=U.UpsampleRgbaLinePair,Yo=U.UpsampleBgraLinePair,Jo=U.UpsampleArgbLinePair,Xo=U.UpsampleRgba4444LinePair,Lu=U.UpsampleRgb565LinePair,Za=16,Qa=1<<Za-1,ha=-227,no=482,Nu=6,Ko=0,Au=s(256),_u=s(256),Su=s(256),Pu=s(256),ku=s(no-ha),Iu=s(no-ha);$r("YuvToRgbRow",ie,3),$r("YuvToBgrRow",Ne,3),$r("YuvToRgbaRow",Br,4),$r("YuvToBgraRow",ze,4),$r("YuvToArgbRow",Ke,4),$r("YuvToRgba4444Row",Ee,2),$r("YuvToRgb565Row",Se,2);var Zo=[0,4,8,12,128,132,136,140,256,260,264,268,384,388,392,396],$a=[0,2,8],Fu=[8,7,6,4,4,2,2,2,1,1,1,1],Cu=1;this.WebPDecodeRGBA=function(t,i,u,d,m){var y=Ya,x=new pi,_=new kr;x.ba=_,_.S=y,_.width=[_.width],_.height=[_.height];var P=_.width,F=_.height,H=new sn;if(H==null||t==null)var Z=2;else e(H!=null),Z=mi(t,i,u,H.width,H.height,H.Pd,H.Qd,H.format,null);if(Z!=0?P=0:(P!=null&&(P[0]=H.width[0]),F!=null&&(F[0]=H.height[0]),P=1),P){_.width=_.width[0],_.height=_.height[0],d!=null&&(d[0]=_.width),m!=null&&(m[0]=_.height);t:{if(d=new oi,(m=new Ki).data=t,m.w=i,m.ha=u,m.kd=1,i=[0],e(m!=null),((t=mi(m.data,m.w,m.ha,null,null,null,i,null,m))==0||t==7)&&i[0]&&(t=4),(i=t)==0){if(e(x!=null),d.data=m.data,d.w=m.w+m.offset,d.ha=m.ha-m.offset,d.put=Gr,d.ac=Ve,d.bc=Yr,d.ma=x,m.xa){if((t=yr())==null){x=1;break t}if(function(Q,Y){var vt=[0],lt=[0],W=[0];e:for(;;){if(Q==null)return 0;if(Y==null)return Q.a=2,0;if(Q.l=Y,Q.a=0,X(Q.m,Y.data,Y.w,Y.ha),!Rt(Q.m,vt,lt,W)){Q.a=3;break e}if(Q.xb=Vo,Y.width=vt[0],Y.height=lt[0],!Kr(vt[0],lt[0],1,Q,null))break e;return 1}return e(Q.a!=0),0}(t,d)){if(d=(i=vi(d.width,d.height,x.Oa,x.ba))==0){e:{d=t;r:for(;;){if(d==null){d=0;break e}if(e(d.s.yc!=null),e(d.s.Ya!=null),e(0<d.s.Wb),e((u=d.l)!=null),e((m=u.ma)!=null),d.xb!=0){if(d.ca=m.ba,d.tb=m.tb,e(d.ca!=null),!ra(m.Oa,u,Ja)){d.a=2;break r}if(!Rn(d,u.width)||u.da)break r;if((u.da||ue(d.ca.S))&&K(),11>d.ca.S||(alert("todo:WebPInitConvertARGBToYUV"),d.ca.f.kb.F!=null&&K()),d.Pb&&0<d.s.ua&&d.s.vb.X==null&&!Ut(d.s.vb,d.s.Wa.Xa)){d.a=1;break r}d.xb=0}if(!jr(d,d.V,d.Ba,d.c,d.i,u.o,ei))break r;m.Dc=d.Ma,d=1;break e}e(d.a!=0),d=0}d=!d}d&&(i=t.a)}else i=t.a}else{if((t=new Fs)==null){x=1;break t}if(t.Fa=m.na,t.P=m.P,t.qc=m.Sa,Aa(t,d)){if((i=vi(d.width,d.height,x.Oa,x.ba))==0){if(t.Aa=0,u=x.Oa,e((m=t)!=null),u!=null){if(0<(P=0>(P=u.Md)?0:100<P?255:255*P/100)){for(F=H=0;4>F;++F)12>(Z=m.pb[F]).lc&&(Z.ia=P*Fu[0>Z.lc?0:Z.lc]>>3),H|=Z.ia;H&&(alert("todo:VP8InitRandom"),m.ia=1)}m.Ga=u.Id,100<m.Ga?m.Ga=100:0>m.Ga&&(m.Ga=0)}Cs(t,d)||(i=t.a)}}else i=t.a}i==0&&x.Oa!=null&&x.Oa.fd&&(i=na(x.ba))}x=i}y=x!=0?null:11>y?_.f.RGBA.eb:_.f.kb.y}else y=null;return y};var Qo=[3,4,3,4,4,2,2,4,4,4,2,1,1]};function g(U,nt){for(var pt="",L=0;L<4;L++)pt+=String.fromCharCode(U[nt++]);return pt}function b(U,nt){return(U[nt+0]<<0|U[nt+1]<<8|U[nt+2]<<16)>>>0}function w(U,nt){return(U[nt+0]<<0|U[nt+1]<<8|U[nt+2]<<16|U[nt+3]<<24)>>>0}new f;var S=[0],p=[0],M=[],I=new f,q=n,k=function(U,nt){var pt={},L=0,A=!1,B=0,D=0;if(pt.frames=[],!function(C,O,R,G){for(var $=0;$<G;$++)if(C[O+$]!=R.charCodeAt($))return!0;return!1}(U,nt,"RIFF",4)){for(w(U,nt+=4),nt+=8;nt<U.length;){var et=g(U,nt),rt=w(U,nt+=4);nt+=4;var ct=rt+(1&rt);switch(et){case"VP8 ":case"VP8L":pt.frames[L]===void 0&&(pt.frames[L]={}),(ft=pt.frames[L]).src_off=A?D:nt-8,ft.src_size=B+rt+8,L++,A&&(A=!1,B=0,D=0);break;case"VP8X":(ft=pt.header={}).feature_flags=U[nt];var X=nt+4;ft.canvas_width=1+b(U,X),X+=3,ft.canvas_height=1+b(U,X),X+=3;break;case"ALPH":A=!0,B=ct+8,D=nt-8;break;case"ANIM":(ft=pt.header).bgcolor=w(U,nt),X=nt+4,ft.loop_count=(Ft=U)[(N=X)+0]<<0|Ft[N+1]<<8,X+=2;break;case"ANMF":var at,ft;(ft=pt.frames[L]={}).offset_x=2*b(U,nt),nt+=3,ft.offset_y=2*b(U,nt),nt+=3,ft.width=1+b(U,nt),nt+=3,ft.height=1+b(U,nt),nt+=3,ft.duration=b(U,nt),nt+=3,at=U[nt++],ft.dispose=1&at,ft.blend=at>>1&1}et!="ANMF"&&(nt+=ct)}var Ft,N;return pt}}(q,0);k.response=q,k.rgbaoutput=!0,k.dataurl=!1;var E=k.header?k.header:null,J=k.frames?k.frames:null;if(E){E.loop_counter=E.loop_count,S=[E.canvas_height],p=[E.canvas_width];for(var ot=0;ot<J.length&&J[ot].blend!=0;ot++);}var ut=J[0],yt=I.WebPDecodeRGBA(q,ut.src_off,ut.src_size,p,S);ut.rgba=yt,ut.imgwidth=p[0],ut.imgheight=S[0];for(var tt=0;tt<p[0]*S[0]*4;tt++)M[tt]=yt[tt];return this.width=p,this.height=S,this.data=M,this}(function(n){var e=function(){return typeof Co=="function"},r=function(S,p,M,I){var q=4,k=c;switch(I){case n.image_compression.FAST:q=1,k=s;break;case n.image_compression.MEDIUM:q=6,k=h;break;case n.image_compression.SLOW:q=9,k=f}S=a(S,p,M,k);var E=Co(S,{level:q});return n.__addimage__.arrayBufferToBinaryString(E)},a=function(S,p,M,I){for(var q,k,E,J=S.length/p,ot=new Uint8Array(S.length+J),ut=b(),yt=0;yt<J;yt+=1){if(E=yt*p,q=S.subarray(E,E+p),I)ot.set(I(q,M,k),E+yt);else{for(var tt,U=ut.length,nt=[];tt<U;tt+=1)nt[tt]=ut[tt](q,M,k);var pt=w(nt.concat());ot.set(nt[pt],E+yt)}k=q}return ot},l=function(S){var p=Array.apply([],S);return p.unshift(0),p},s=function(S,p){var M,I=[],q=S.length;I[0]=1;for(var k=0;k<q;k+=1)M=S[k-p]||0,I[k+1]=S[k]-M+256&255;return I},c=function(S,p,M){var I,q=[],k=S.length;q[0]=2;for(var E=0;E<k;E+=1)I=M&&M[E]||0,q[E+1]=S[E]-I+256&255;return q},h=function(S,p,M){var I,q,k=[],E=S.length;k[0]=3;for(var J=0;J<E;J+=1)I=S[J-p]||0,q=M&&M[J]||0,k[J+1]=S[J]+256-(I+q>>>1)&255;return k},f=function(S,p,M){var I,q,k,E,J=[],ot=S.length;J[0]=4;for(var ut=0;ut<ot;ut+=1)I=S[ut-p]||0,q=M&&M[ut]||0,k=M&&M[ut-p]||0,E=g(I,q,k),J[ut+1]=S[ut]-E+256&255;return J},g=function(S,p,M){if(S===p&&p===M)return S;var I=Math.abs(p-M),q=Math.abs(S-M),k=Math.abs(S+p-M-M);return I<=q&&I<=k?S:q<=k?p:M},b=function(){return[l,s,c,h,f]},w=function(S){var p=S.map(function(M){return M.reduce(function(I,q){return I+Math.abs(q)},0)});return p.indexOf(Math.min.apply(null,p))};n.processPNG=function(S,p,M,I){var q,k,E,J,ot,ut,yt,tt,U,nt,pt,L,A,B,D,et=this.decode.FLATE_DECODE,rt="";if(this.__addimage__.isArrayBuffer(S)&&(S=new Uint8Array(S)),this.__addimage__.isArrayBufferView(S)){if(S=(E=new jc(S)).imgData,k=E.bits,q=E.colorSpace,ot=E.colors,[4,6].indexOf(E.colorType)!==-1){if(E.bits===8){U=(tt=E.pixelBitlength==32?new Uint32Array(E.decodePixels().buffer):E.pixelBitlength==16?new Uint16Array(E.decodePixels().buffer):new Uint8Array(E.decodePixels().buffer)).length,pt=new Uint8Array(U*E.colors),nt=new Uint8Array(U);var ct,X=E.pixelBitlength-E.bits;for(B=0,D=0;B<U;B++){for(A=tt[B],ct=0;ct<X;)pt[D++]=A>>>ct&255,ct+=E.bits;nt[B]=A>>>ct&255}}if(E.bits===16){U=(tt=new Uint32Array(E.decodePixels().buffer)).length,pt=new Uint8Array(U*(32/E.pixelBitlength)*E.colors),nt=new Uint8Array(U*(32/E.pixelBitlength)),L=E.colors>1,B=0,D=0;for(var at=0;B<U;)A=tt[B++],pt[D++]=A>>>0&255,L&&(pt[D++]=A>>>16&255,A=tt[B++],pt[D++]=A>>>0&255),nt[at++]=A>>>16&255;k=8}I!==n.image_compression.NONE&&e()?(S=r(pt,E.width*E.colors,E.colors,I),yt=r(nt,E.width,1,I)):(S=pt,yt=nt,et=void 0)}if(E.colorType===3&&(q=this.color_spaces.INDEXED,ut=E.palette,E.transparency.indexed)){var ft=E.transparency.indexed,Ft=0;for(B=0,U=ft.length;B<U;++B)Ft+=ft[B];if((Ft/=255)===U-1&&ft.indexOf(0)!==-1)J=[ft.indexOf(0)];else if(Ft!==U){for(tt=E.decodePixels(),nt=new Uint8Array(tt.length),B=0,U=tt.length;B<U;B++)nt[B]=ft[tt[B]];yt=r(nt,E.width,1)}}var N=function(C){var O;switch(C){case n.image_compression.FAST:O=11;break;case n.image_compression.MEDIUM:O=13;break;case n.image_compression.SLOW:O=14;break;default:O=12}return O}(I);return et===this.decode.FLATE_DECODE&&(rt="/Predictor "+N+" "),rt+="/Colors "+ot+" /BitsPerComponent "+k+" /Columns "+E.width,(this.__addimage__.isArrayBuffer(S)||this.__addimage__.isArrayBufferView(S))&&(S=this.__addimage__.arrayBufferToBinaryString(S)),(yt&&this.__addimage__.isArrayBuffer(yt)||this.__addimage__.isArrayBufferView(yt))&&(yt=this.__addimage__.arrayBufferToBinaryString(yt)),{alias:M,data:S,index:p,filter:et,decodeParameters:rt,transparency:J,palette:ut,sMask:yt,predictor:N,width:E.width,height:E.height,bitsPerComponent:k,colorSpace:q}}}})(zt.API),function(n){n.processGIF89A=function(e,r,a,l){var s=new Oc(e),c=s.width,h=s.height,f=[];s.decodeAndBlitFrameRGBA(0,f);var g={data:f,width:c,height:h},b=new _o(100).encode(g,100);return n.processJPEG.call(this,b,r,a,l)},n.processGIF87A=n.processGIF89A}(zt.API),zr.prototype.parseHeader=function(){if(this.fileSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.reserved=this.datav.getUint32(this.pos,!0),this.pos+=4,this.offset=this.datav.getUint32(this.pos,!0),this.pos+=4,this.headerSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.width=this.datav.getUint32(this.pos,!0),this.pos+=4,this.height=this.datav.getInt32(this.pos,!0),this.pos+=4,this.planes=this.datav.getUint16(this.pos,!0),this.pos+=2,this.bitPP=this.datav.getUint16(this.pos,!0),this.pos+=2,this.compress=this.datav.getUint32(this.pos,!0),this.pos+=4,this.rawSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.hr=this.datav.getUint32(this.pos,!0),this.pos+=4,this.vr=this.datav.getUint32(this.pos,!0),this.pos+=4,this.colors=this.datav.getUint32(this.pos,!0),this.pos+=4,this.importantColors=this.datav.getUint32(this.pos,!0),this.pos+=4,this.bitPP===16&&this.is_with_alpha&&(this.bitPP=15),this.bitPP<15){var n=this.colors===0?1<<this.bitPP:this.colors;this.palette=new Array(n);for(var e=0;e<n;e++){var r=this.datav.getUint8(this.pos++,!0),a=this.datav.getUint8(this.pos++,!0),l=this.datav.getUint8(this.pos++,!0),s=this.datav.getUint8(this.pos++,!0);this.palette[e]={red:l,green:a,blue:r,quad:s}}}this.height<0&&(this.height*=-1,this.bottom_up=!1)},zr.prototype.parseBGR=function(){this.pos=this.offset;try{var n="bit"+this.bitPP,e=this.width*this.height*4;this.data=new Uint8Array(e),this[n]()}catch(r){be.log("bit decode error:"+r)}},zr.prototype.bit1=function(){var n,e=Math.ceil(this.width/8),r=e%4;for(n=this.height-1;n>=0;n--){for(var a=this.bottom_up?n:this.height-1-n,l=0;l<e;l++)for(var s=this.datav.getUint8(this.pos++,!0),c=a*this.width*4+8*l*4,h=0;h<8&&8*l+h<this.width;h++){var f=this.palette[s>>7-h&1];this.data[c+4*h]=f.blue,this.data[c+4*h+1]=f.green,this.data[c+4*h+2]=f.red,this.data[c+4*h+3]=255}r!==0&&(this.pos+=4-r)}},zr.prototype.bit4=function(){for(var n=Math.ceil(this.width/2),e=n%4,r=this.height-1;r>=0;r--){for(var a=this.bottom_up?r:this.height-1-r,l=0;l<n;l++){var s=this.datav.getUint8(this.pos++,!0),c=a*this.width*4+2*l*4,h=s>>4,f=15&s,g=this.palette[h];if(this.data[c]=g.blue,this.data[c+1]=g.green,this.data[c+2]=g.red,this.data[c+3]=255,2*l+1>=this.width)break;g=this.palette[f],this.data[c+4]=g.blue,this.data[c+4+1]=g.green,this.data[c+4+2]=g.red,this.data[c+4+3]=255}e!==0&&(this.pos+=4-e)}},zr.prototype.bit8=function(){for(var n=this.width%4,e=this.height-1;e>=0;e--){for(var r=this.bottom_up?e:this.height-1-e,a=0;a<this.width;a++){var l=this.datav.getUint8(this.pos++,!0),s=r*this.width*4+4*a;if(l<this.palette.length){var c=this.palette[l];this.data[s]=c.red,this.data[s+1]=c.green,this.data[s+2]=c.blue,this.data[s+3]=255}else this.data[s]=255,this.data[s+1]=255,this.data[s+2]=255,this.data[s+3]=255}n!==0&&(this.pos+=4-n)}},zr.prototype.bit15=function(){for(var n=this.width%3,e=parseInt("11111",2),r=this.height-1;r>=0;r--){for(var a=this.bottom_up?r:this.height-1-r,l=0;l<this.width;l++){var s=this.datav.getUint16(this.pos,!0);this.pos+=2;var c=(s&e)/e*255|0,h=(s>>5&e)/e*255|0,f=(s>>10&e)/e*255|0,g=s>>15?255:0,b=a*this.width*4+4*l;this.data[b]=f,this.data[b+1]=h,this.data[b+2]=c,this.data[b+3]=g}this.pos+=n}},zr.prototype.bit16=function(){for(var n=this.width%3,e=parseInt("11111",2),r=parseInt("111111",2),a=this.height-1;a>=0;a--){for(var l=this.bottom_up?a:this.height-1-a,s=0;s<this.width;s++){var c=this.datav.getUint16(this.pos,!0);this.pos+=2;var h=(c&e)/e*255|0,f=(c>>5&r)/r*255|0,g=(c>>11)/e*255|0,b=l*this.width*4+4*s;this.data[b]=g,this.data[b+1]=f,this.data[b+2]=h,this.data[b+3]=255}this.pos+=n}},zr.prototype.bit24=function(){for(var n=this.height-1;n>=0;n--){for(var e=this.bottom_up?n:this.height-1-n,r=0;r<this.width;r++){var a=this.datav.getUint8(this.pos++,!0),l=this.datav.getUint8(this.pos++,!0),s=this.datav.getUint8(this.pos++,!0),c=e*this.width*4+4*r;this.data[c]=s,this.data[c+1]=l,this.data[c+2]=a,this.data[c+3]=255}this.pos+=this.width%4}},zr.prototype.bit32=function(){for(var n=this.height-1;n>=0;n--)for(var e=this.bottom_up?n:this.height-1-n,r=0;r<this.width;r++){var a=this.datav.getUint8(this.pos++,!0),l=this.datav.getUint8(this.pos++,!0),s=this.datav.getUint8(this.pos++,!0),c=this.datav.getUint8(this.pos++,!0),h=e*this.width*4+4*r;this.data[h]=s,this.data[h+1]=l,this.data[h+2]=a,this.data[h+3]=c}},zr.prototype.getData=function(){return this.data},function(n){n.processBMP=function(e,r,a,l){var s=new zr(e,!1),c=s.width,h=s.height,f={data:s.getData(),width:c,height:h},g=new _o(100).encode(f,100);return n.processJPEG.call(this,g,r,a,l)}}(zt.API),Tl.prototype.getData=function(){return this.data},function(n){n.processWEBP=function(e,r,a,l){var s=new Tl(e),c=s.width,h=s.height,f={data:s.getData(),width:c,height:h},g=new _o(100).encode(f,100);return n.processJPEG.call(this,g,r,a,l)}}(zt.API),zt.API.processRGBA=function(n,e,r){for(var a=n.data,l=a.length,s=new Uint8Array(l/4*3),c=new Uint8Array(l/4),h=0,f=0,g=0;g<l;g+=4){var b=a[g],w=a[g+1],S=a[g+2],p=a[g+3];s[h++]=b,s[h++]=w,s[h++]=S,c[f++]=p}var M=this.__addimage__.arrayBufferToBinaryString(s);return{alpha:this.__addimage__.arrayBufferToBinaryString(c),data:M,index:e,alias:r,colorSpace:"DeviceRGB",bitsPerComponent:8,width:n.width,height:n.height}},zt.API.setLanguage=function(n){return this.internal.languageSettings===void 0&&(this.internal.languageSettings={},this.internal.languageSettings.isSubscribed=!1),{af:"Afrikaans",sq:"Albanian",ar:"Arabic (Standard)","ar-DZ":"Arabic (Algeria)","ar-BH":"Arabic (Bahrain)","ar-EG":"Arabic (Egypt)","ar-IQ":"Arabic (Iraq)","ar-JO":"Arabic (Jordan)","ar-KW":"Arabic (Kuwait)","ar-LB":"Arabic (Lebanon)","ar-LY":"Arabic (Libya)","ar-MA":"Arabic (Morocco)","ar-OM":"Arabic (Oman)","ar-QA":"Arabic (Qatar)","ar-SA":"Arabic (Saudi Arabia)","ar-SY":"Arabic (Syria)","ar-TN":"Arabic (Tunisia)","ar-AE":"Arabic (U.A.E.)","ar-YE":"Arabic (Yemen)",an:"Aragonese",hy:"Armenian",as:"Assamese",ast:"Asturian",az:"Azerbaijani",eu:"Basque",be:"Belarusian",bn:"Bengali",bs:"Bosnian",br:"Breton",bg:"Bulgarian",my:"Burmese",ca:"Catalan",ch:"Chamorro",ce:"Chechen",zh:"Chinese","zh-HK":"Chinese (Hong Kong)","zh-CN":"Chinese (PRC)","zh-SG":"Chinese (Singapore)","zh-TW":"Chinese (Taiwan)",cv:"Chuvash",co:"Corsican",cr:"Cree",hr:"Croatian",cs:"Czech",da:"Danish",nl:"Dutch (Standard)","nl-BE":"Dutch (Belgian)",en:"English","en-AU":"English (Australia)","en-BZ":"English (Belize)","en-CA":"English (Canada)","en-IE":"English (Ireland)","en-JM":"English (Jamaica)","en-NZ":"English (New Zealand)","en-PH":"English (Philippines)","en-ZA":"English (South Africa)","en-TT":"English (Trinidad & Tobago)","en-GB":"English (United Kingdom)","en-US":"English (United States)","en-ZW":"English (Zimbabwe)",eo:"Esperanto",et:"Estonian",fo:"Faeroese",fj:"Fijian",fi:"Finnish",fr:"French (Standard)","fr-BE":"French (Belgium)","fr-CA":"French (Canada)","fr-FR":"French (France)","fr-LU":"French (Luxembourg)","fr-MC":"French (Monaco)","fr-CH":"French (Switzerland)",fy:"Frisian",fur:"Friulian",gd:"Gaelic (Scots)","gd-IE":"Gaelic (Irish)",gl:"Galacian",ka:"Georgian",de:"German (Standard)","de-AT":"German (Austria)","de-DE":"German (Germany)","de-LI":"German (Liechtenstein)","de-LU":"German (Luxembourg)","de-CH":"German (Switzerland)",el:"Greek",gu:"Gujurati",ht:"Haitian",he:"Hebrew",hi:"Hindi",hu:"Hungarian",is:"Icelandic",id:"Indonesian",iu:"Inuktitut",ga:"Irish",it:"Italian (Standard)","it-CH":"Italian (Switzerland)",ja:"Japanese",kn:"Kannada",ks:"Kashmiri",kk:"Kazakh",km:"Khmer",ky:"Kirghiz",tlh:"Klingon",ko:"Korean","ko-KP":"Korean (North Korea)","ko-KR":"Korean (South Korea)",la:"Latin",lv:"Latvian",lt:"Lithuanian",lb:"Luxembourgish",mk:"North Macedonia",ms:"Malay",ml:"Malayalam",mt:"Maltese",mi:"Maori",mr:"Marathi",mo:"Moldavian",nv:"Navajo",ng:"Ndonga",ne:"Nepali",no:"Norwegian",nb:"Norwegian (Bokmal)",nn:"Norwegian (Nynorsk)",oc:"Occitan",or:"Oriya",om:"Oromo",fa:"Persian","fa-IR":"Persian/Iran",pl:"Polish",pt:"Portuguese","pt-BR":"Portuguese (Brazil)",pa:"Punjabi","pa-IN":"Punjabi (India)","pa-PK":"Punjabi (Pakistan)",qu:"Quechua",rm:"Rhaeto-Romanic",ro:"Romanian","ro-MO":"Romanian (Moldavia)",ru:"Russian","ru-MO":"Russian (Moldavia)",sz:"Sami (Lappish)",sg:"Sango",sa:"Sanskrit",sc:"Sardinian",sd:"Sindhi",si:"Singhalese",sr:"Serbian",sk:"Slovak",sl:"Slovenian",so:"Somani",sb:"Sorbian",es:"Spanish","es-AR":"Spanish (Argentina)","es-BO":"Spanish (Bolivia)","es-CL":"Spanish (Chile)","es-CO":"Spanish (Colombia)","es-CR":"Spanish (Costa Rica)","es-DO":"Spanish (Dominican Republic)","es-EC":"Spanish (Ecuador)","es-SV":"Spanish (El Salvador)","es-GT":"Spanish (Guatemala)","es-HN":"Spanish (Honduras)","es-MX":"Spanish (Mexico)","es-NI":"Spanish (Nicaragua)","es-PA":"Spanish (Panama)","es-PY":"Spanish (Paraguay)","es-PE":"Spanish (Peru)","es-PR":"Spanish (Puerto Rico)","es-ES":"Spanish (Spain)","es-UY":"Spanish (Uruguay)","es-VE":"Spanish (Venezuela)",sx:"Sutu",sw:"Swahili",sv:"Swedish","sv-FI":"Swedish (Finland)","sv-SV":"Swedish (Sweden)",ta:"Tamil",tt:"Tatar",te:"Teluga",th:"Thai",tig:"Tigre",ts:"Tsonga",tn:"Tswana",tr:"Turkish",tk:"Turkmen",uk:"Ukrainian",hsb:"Upper Sorbian",ur:"Urdu",ve:"Venda",vi:"Vietnamese",vo:"Volapuk",wa:"Walloon",cy:"Welsh",xh:"Xhosa",ji:"Yiddish",zu:"Zulu"}[n]!==void 0&&(this.internal.languageSettings.languageCode=n,this.internal.languageSettings.isSubscribed===!1&&(this.internal.events.subscribe("putCatalog",function(){this.internal.write("/Lang ("+this.internal.languageSettings.languageCode+")")}),this.internal.languageSettings.isSubscribed=!0)),this},ji=zt.API,gs=ji.getCharWidthsArray=function(n,e){var r,a,l=(e=e||{}).font||this.internal.getFont(),s=e.fontSize||this.internal.getFontSize(),c=e.charSpace||this.internal.getCharSpace(),h=e.widths?e.widths:l.metadata.Unicode.widths,f=h.fof?h.fof:1,g=e.kerning?e.kerning:l.metadata.Unicode.kerning,b=g.fof?g.fof:1,w=e.doKerning!==!1,S=0,p=n.length,M=0,I=h[0]||f,q=[];for(r=0;r<p;r++)a=n.charCodeAt(r),typeof l.metadata.widthOfString=="function"?q.push((l.metadata.widthOfGlyph(l.metadata.characterToGlyph(a))+c*(1e3/s)||0)/1e3):(S=w&&de(g[a])==="object"&&!isNaN(parseInt(g[a][M],10))?g[a][M]/b:0,q.push((h[a]||I)/f+S)),M=a;return q},Ol=ji.getStringUnitWidth=function(n,e){var r=(e=e||{}).fontSize||this.internal.getFontSize(),a=e.font||this.internal.getFont(),l=e.charSpace||this.internal.getCharSpace();return ji.processArabic&&(n=ji.processArabic(n)),typeof a.metadata.widthOfString=="function"?a.metadata.widthOfString(n,r,l)/r:gs.apply(this,arguments).reduce(function(s,c){return s+c},0)},Ml=function(n,e,r,a){for(var l=[],s=0,c=n.length,h=0;s!==c&&h+e[s]<r;)h+=e[s],s++;l.push(n.slice(0,s));var f=s;for(h=0;s!==c;)h+e[s]>a&&(l.push(n.slice(f,s)),h=0,f=s),h+=e[s],s++;return f!==s&&l.push(n.slice(f,s)),l},Bl=function(n,e,r){r||(r={});var a,l,s,c,h,f,g,b=[],w=[b],S=r.textIndent||0,p=0,M=0,I=n.split(" "),q=gs.apply(this,[" ",r])[0];if(f=r.lineIndent===-1?I[0].length+2:r.lineIndent||0){var k=Array(f).join(" "),E=[];I.map(function(ot){(ot=ot.split(/\s*\n/)).length>1?E=E.concat(ot.map(function(ut,yt){return(yt&&ut.length?`
`:"")+ut})):E.push(ot[0])}),I=E,f=Ol.apply(this,[k,r])}for(s=0,c=I.length;s<c;s++){var J=0;if(a=I[s],f&&a[0]==`
`&&(a=a.substr(1),J=1),S+p+(M=(l=gs.apply(this,[a,r])).reduce(function(ot,ut){return ot+ut},0))>e||J){if(M>e){for(h=Ml.apply(this,[a,l,e-(S+p),e]),b.push(h.shift()),b=[h.pop()];h.length;)w.push([h.shift()]);M=l.slice(a.length-(b[0]?b[0].length:0)).reduce(function(ot,ut){return ot+ut},0)}else b=[a];w.push(b),S=M+f,p=q}else b.push(a),S+=p+M,p=q}return g=f?function(ot,ut){return(ut?k:"")+ot.join(" ")}:function(ot){return ot.join(" ")},w.map(g)},ji.splitTextToSize=function(n,e,r){var a,l=(r=r||{}).fontSize||this.internal.getFontSize(),s=(function(b){if(b.widths&&b.kerning)return{widths:b.widths,kerning:b.kerning};var w=this.internal.getFont(b.fontName,b.fontStyle);return w.metadata.Unicode?{widths:w.metadata.Unicode.widths||{0:1},kerning:w.metadata.Unicode.kerning||{}}:{font:w.metadata,fontSize:this.internal.getFontSize(),charSpace:this.internal.getCharSpace()}}).call(this,r);a=Array.isArray(n)?n:String(n).split(/\r?\n/);var c=1*this.internal.scaleFactor*e/l;s.textIndent=r.textIndent?1*r.textIndent*this.internal.scaleFactor/l:0,s.lineIndent=r.lineIndent;var h,f,g=[];for(h=0,f=a.length;h<f;h++)g=g.concat(Bl.apply(this,[a[h],c,s]));return g},function(n){n.__fontmetrics__=n.__fontmetrics__||{};for(var e="klmnopqrstuvwxyz",r={},a={},l=0;l<e.length;l++)r[e[l]]="0123456789abcdef"[l],a["0123456789abcdef"[l]]=e[l];var s=function(w){return"0x"+parseInt(w,10).toString(16)},c=n.__fontmetrics__.compress=function(w){var S,p,M,I,q=["{"];for(var k in w){if(S=w[k],isNaN(parseInt(k,10))?p="'"+k+"'":(k=parseInt(k,10),p=(p=s(k).slice(2)).slice(0,-1)+a[p.slice(-1)]),typeof S=="number")S<0?(M=s(S).slice(3),I="-"):(M=s(S).slice(2),I=""),M=I+M.slice(0,-1)+a[M.slice(-1)];else{if(de(S)!=="object")throw new Error("Don't know what to do with value type "+de(S)+".");M=c(S)}q.push(p+M)}return q.push("}"),q.join("")},h=n.__fontmetrics__.uncompress=function(w){if(typeof w!="string")throw new Error("Invalid argument passed to uncompress.");for(var S,p,M,I,q={},k=1,E=q,J=[],ot="",ut="",yt=w.length-1,tt=1;tt<yt;tt+=1)(I=w[tt])=="'"?S?(M=S.join(""),S=void 0):S=[]:S?S.push(I):I=="{"?(J.push([E,M]),E={},M=void 0):I=="}"?((p=J.pop())[0][p[1]]=E,M=void 0,E=p[0]):I=="-"?k=-1:M===void 0?r.hasOwnProperty(I)?(ot+=r[I],M=parseInt(ot,16)*k,k=1,ot=""):ot+=I:r.hasOwnProperty(I)?(ut+=r[I],E[M]=parseInt(ut,16)*k,k=1,M=void 0,ut=""):ut+=I;return q},f={codePages:["WinAnsiEncoding"],WinAnsiEncoding:h("{19m8n201n9q201o9r201s9l201t9m201u8m201w9n201x9o201y8o202k8q202l8r202m9p202q8p20aw8k203k8t203t8v203u9v2cq8s212m9t15m8w15n9w2dw9s16k8u16l9u17s9z17x8y17y9y}")},g={Unicode:{Courier:f,"Courier-Bold":f,"Courier-BoldOblique":f,"Courier-Oblique":f,Helvetica:f,"Helvetica-Bold":f,"Helvetica-BoldOblique":f,"Helvetica-Oblique":f,"Times-Roman":f,"Times-Bold":f,"Times-BoldItalic":f,"Times-Italic":f}},b={Unicode:{"Courier-Oblique":h("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-BoldItalic":h("{'widths'{k3o2q4ycx2r201n3m201o6o201s2l201t2l201u2l201w3m201x3m201y3m2k1t2l2r202m2n2n3m2o3m2p5n202q6o2r1w2s2l2t2l2u3m2v3t2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w3t3x3t3y3t3z3m4k5n4l4m4m4m4n4m4o4s4p4m4q4m4r4s4s4y4t2r4u3m4v4m4w3x4x5t4y4s4z4s5k3x5l4s5m4m5n3r5o3x5p4s5q4m5r5t5s4m5t3x5u3x5v2l5w1w5x2l5y3t5z3m6k2l6l3m6m3m6n2w6o3m6p2w6q2l6r3m6s3r6t1w6u1w6v3m6w1w6x4y6y3r6z3m7k3m7l3m7m2r7n2r7o1w7p3r7q2w7r4m7s3m7t2w7u2r7v2n7w1q7x2n7y3t202l3mcl4mal2ram3man3mao3map3mar3mas2lat4uau1uav3maw3way4uaz2lbk2sbl3t'fof'6obo2lbp3tbq3mbr1tbs2lbu1ybv3mbz3mck4m202k3mcm4mcn4mco4mcp4mcq5ycr4mcs4mct4mcu4mcv4mcw2r2m3rcy2rcz2rdl4sdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek3mel3mem3men3meo3mep3meq4ser2wes2wet2weu2wev2wew1wex1wey1wez1wfl3rfm3mfn3mfo3mfp3mfq3mfr3tfs3mft3rfu3rfv3rfw3rfz2w203k6o212m6o2dw2l2cq2l3t3m3u2l17s3x19m3m}'kerning'{cl{4qu5kt5qt5rs17ss5ts}201s{201ss}201t{cks4lscmscnscoscpscls2wu2yu201ts}201x{2wu2yu}2k{201ts}2w{4qx5kx5ou5qx5rs17su5tu}2x{17su5tu5ou}2y{4qx5kx5ou5qx5rs17ss5ts}'fof'-6ofn{17sw5tw5ou5qw5rs}7t{cksclscmscnscoscps4ls}3u{17su5tu5os5qs}3v{17su5tu5os5qs}7p{17su5tu}ck{4qu5kt5qt5rs17ss5ts}4l{4qu5kt5qt5rs17ss5ts}cm{4qu5kt5qt5rs17ss5ts}cn{4qu5kt5qt5rs17ss5ts}co{4qu5kt5qt5rs17ss5ts}cp{4qu5kt5qt5rs17ss5ts}6l{4qu5ou5qw5rt17su5tu}5q{ckuclucmucnucoucpu4lu}5r{ckuclucmucnucoucpu4lu}7q{cksclscmscnscoscps4ls}6p{4qu5ou5qw5rt17sw5tw}ek{4qu5ou5qw5rt17su5tu}el{4qu5ou5qw5rt17su5tu}em{4qu5ou5qw5rt17su5tu}en{4qu5ou5qw5rt17su5tu}eo{4qu5ou5qw5rt17su5tu}ep{4qu5ou5qw5rt17su5tu}es{17ss5ts5qs4qu}et{4qu5ou5qw5rt17sw5tw}eu{4qu5ou5qw5rt17ss5ts}ev{17ss5ts5qs4qu}6z{17sw5tw5ou5qw5rs}fm{17sw5tw5ou5qw5rs}7n{201ts}fo{17sw5tw5ou5qw5rs}fp{17sw5tw5ou5qw5rs}fq{17sw5tw5ou5qw5rs}7r{cksclscmscnscoscps4ls}fs{17sw5tw5ou5qw5rs}ft{17su5tu}fu{17su5tu}fv{17su5tu}fw{17su5tu}fz{cksclscmscnscoscps4ls}}}"),"Helvetica-Bold":h("{'widths'{k3s2q4scx1w201n3r201o6o201s1w201t1w201u1w201w3m201x3m201y3m2k1w2l2l202m2n2n3r2o3r2p5t202q6o2r1s2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v2l3w3u3x3u3y3u3z3x4k6l4l4s4m4s4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3r4v4s4w3x4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v2l5w1w5x2l5y3u5z3r6k2l6l3r6m3x6n3r6o3x6p3r6q2l6r3x6s3x6t1w6u1w6v3r6w1w6x5t6y3x6z3x7k3x7l3x7m2r7n3r7o2l7p3x7q3r7r4y7s3r7t3r7u3m7v2r7w1w7x2r7y3u202l3rcl4sal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3xbq3rbr1wbs2lbu2obv3rbz3xck4s202k3rcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw1w2m2zcy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3res3ret3reu3rev3rew1wex1wey1wez1wfl3xfm3xfn3xfo3xfp3xfq3xfr3ufs3xft3xfu3xfv3xfw3xfz3r203k6o212m6o2dw2l2cq2l3t3r3u2l17s4m19m3r}'kerning'{cl{4qs5ku5ot5qs17sv5tv}201t{2ww4wy2yw}201w{2ks}201x{2ww4wy2yw}2k{201ts201xs}2w{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}2x{5ow5qs}2y{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}'fof'-6o7p{17su5tu5ot}ck{4qs5ku5ot5qs17sv5tv}4l{4qs5ku5ot5qs17sv5tv}cm{4qs5ku5ot5qs17sv5tv}cn{4qs5ku5ot5qs17sv5tv}co{4qs5ku5ot5qs17sv5tv}cp{4qs5ku5ot5qs17sv5tv}6l{17st5tt5os}17s{2kwclvcmvcnvcovcpv4lv4wwckv}5o{2kucltcmtcntcotcpt4lt4wtckt}5q{2ksclscmscnscoscps4ls4wvcks}5r{2ks4ws}5t{2kwclvcmvcnvcovcpv4lv4wwckv}eo{17st5tt5os}fu{17su5tu5ot}6p{17ss5ts}ek{17st5tt5os}el{17st5tt5os}em{17st5tt5os}en{17st5tt5os}6o{201ts}ep{17st5tt5os}es{17ss5ts}et{17ss5ts}eu{17ss5ts}ev{17ss5ts}6z{17su5tu5os5qt}fm{17su5tu5os5qt}fn{17su5tu5os5qt}fo{17su5tu5os5qt}fp{17su5tu5os5qt}fq{17su5tu5os5qt}fs{17su5tu5os5qt}ft{17su5tu5ot}7m{5os}fv{17su5tu5ot}fw{17su5tu5ot}}}"),Courier:h("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Courier-BoldOblique":h("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-Bold":h("{'widths'{k3q2q5ncx2r201n3m201o6o201s2l201t2l201u2l201w3m201x3m201y3m2k1t2l2l202m2n2n3m2o3m2p6o202q6o2r1w2s2l2t2l2u3m2v3t2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w3t3x3t3y3t3z3m4k5x4l4s4m4m4n4s4o4s4p4m4q3x4r4y4s4y4t2r4u3m4v4y4w4m4x5y4y4s4z4y5k3x5l4y5m4s5n3r5o4m5p4s5q4s5r6o5s4s5t4s5u4m5v2l5w1w5x2l5y3u5z3m6k2l6l3m6m3r6n2w6o3r6p2w6q2l6r3m6s3r6t1w6u2l6v3r6w1w6x5n6y3r6z3m7k3r7l3r7m2w7n2r7o2l7p3r7q3m7r4s7s3m7t3m7u2w7v2r7w1q7x2r7y3o202l3mcl4sal2lam3man3mao3map3mar3mas2lat4uau1yav3maw3tay4uaz2lbk2sbl3t'fof'6obo2lbp3rbr1tbs2lbu2lbv3mbz3mck4s202k3mcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw2r2m3rcy2rcz2rdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3rek3mel3mem3men3meo3mep3meq4ser2wes2wet2weu2wev2wew1wex1wey1wez1wfl3rfm3mfn3mfo3mfp3mfq3mfr3tfs3mft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3m3u2l17s4s19m3m}'kerning'{cl{4qt5ks5ot5qy5rw17sv5tv}201t{cks4lscmscnscoscpscls4wv}2k{201ts}2w{4qu5ku7mu5os5qx5ru17su5tu}2x{17su5tu5ou5qs}2y{4qv5kv7mu5ot5qz5ru17su5tu}'fof'-6o7t{cksclscmscnscoscps4ls}3u{17su5tu5os5qu}3v{17su5tu5os5qu}fu{17su5tu5ou5qu}7p{17su5tu5ou5qu}ck{4qt5ks5ot5qy5rw17sv5tv}4l{4qt5ks5ot5qy5rw17sv5tv}cm{4qt5ks5ot5qy5rw17sv5tv}cn{4qt5ks5ot5qy5rw17sv5tv}co{4qt5ks5ot5qy5rw17sv5tv}cp{4qt5ks5ot5qy5rw17sv5tv}6l{17st5tt5ou5qu}17s{ckuclucmucnucoucpu4lu4wu}5o{ckuclucmucnucoucpu4lu4wu}5q{ckzclzcmzcnzcozcpz4lz4wu}5r{ckxclxcmxcnxcoxcpx4lx4wu}5t{ckuclucmucnucoucpu4lu4wu}7q{ckuclucmucnucoucpu4lu}6p{17sw5tw5ou5qu}ek{17st5tt5qu}el{17st5tt5ou5qu}em{17st5tt5qu}en{17st5tt5qu}eo{17st5tt5qu}ep{17st5tt5ou5qu}es{17ss5ts5qu}et{17sw5tw5ou5qu}eu{17sw5tw5ou5qu}ev{17ss5ts5qu}6z{17sw5tw5ou5qu5rs}fm{17sw5tw5ou5qu5rs}fn{17sw5tw5ou5qu5rs}fo{17sw5tw5ou5qu5rs}fp{17sw5tw5ou5qu5rs}fq{17sw5tw5ou5qu5rs}7r{cktcltcmtcntcotcpt4lt5os}fs{17sw5tw5ou5qu5rs}ft{17su5tu5ou5qu}7m{5os}fv{17su5tu5ou5qu}fw{17su5tu5ou5qu}fz{cksclscmscnscoscps4ls}}}"),Symbol:h("{'widths'{k3uaw4r19m3m2k1t2l2l202m2y2n3m2p5n202q6o3k3m2s2l2t2l2v3r2w1t3m3m2y1t2z1wbk2sbl3r'fof'6o3n3m3o3m3p3m3q3m3r3m3s3m3t3m3u1w3v1w3w3r3x3r3y3r3z2wbp3t3l3m5v2l5x2l5z3m2q4yfr3r7v3k7w1o7x3k}'kerning'{'fof'-6o}}"),Helvetica:h("{'widths'{k3p2q4mcx1w201n3r201o6o201s1q201t1q201u1q201w2l201x2l201y2l2k1w2l1w202m2n2n3r2o3r2p5t202q6o2r1n2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v1w3w3u3x3u3y3u3z3r4k6p4l4m4m4m4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3m4v4m4w3r4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v1w5w1w5x1w5y2z5z3r6k2l6l3r6m3r6n3m6o3r6p3r6q1w6r3r6s3r6t1q6u1q6v3m6w1q6x5n6y3r6z3r7k3r7l3r7m2l7n3m7o1w7p3r7q3m7r4s7s3m7t3m7u3m7v2l7w1u7x2l7y3u202l3rcl4mal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3rbr1wbs2lbu2obv3rbz3xck4m202k3rcm4mcn4mco4mcp4mcq6ocr4scs4mct4mcu4mcv4mcw1w2m2ncy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3mes3ret3reu3rev3rew1wex1wey1wez1wfl3rfm3rfn3rfo3rfp3rfq3rfr3ufs3xft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3r3u1w17s4m19m3r}'kerning'{5q{4wv}cl{4qs5kw5ow5qs17sv5tv}201t{2wu4w1k2yu}201x{2wu4wy2yu}17s{2ktclucmucnu4otcpu4lu4wycoucku}2w{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}2x{17sy5ty5oy5qs}2y{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}'fof'-6o7p{17sv5tv5ow}ck{4qs5kw5ow5qs17sv5tv}4l{4qs5kw5ow5qs17sv5tv}cm{4qs5kw5ow5qs17sv5tv}cn{4qs5kw5ow5qs17sv5tv}co{4qs5kw5ow5qs17sv5tv}cp{4qs5kw5ow5qs17sv5tv}6l{17sy5ty5ow}do{17st5tt}4z{17st5tt}7s{fst}dm{17st5tt}dn{17st5tt}5o{ckwclwcmwcnwcowcpw4lw4wv}dp{17st5tt}dq{17st5tt}7t{5ow}ds{17st5tt}5t{2ktclucmucnu4otcpu4lu4wycoucku}fu{17sv5tv5ow}6p{17sy5ty5ow5qs}ek{17sy5ty5ow}el{17sy5ty5ow}em{17sy5ty5ow}en{5ty}eo{17sy5ty5ow}ep{17sy5ty5ow}es{17sy5ty5qs}et{17sy5ty5ow5qs}eu{17sy5ty5ow5qs}ev{17sy5ty5ow5qs}6z{17sy5ty5ow5qs}fm{17sy5ty5ow5qs}fn{17sy5ty5ow5qs}fo{17sy5ty5ow5qs}fp{17sy5ty5qs}fq{17sy5ty5ow5qs}7r{5ow}fs{17sy5ty5ow5qs}ft{17sv5tv5ow}7m{5ow}fv{17sv5tv5ow}fw{17sv5tv5ow}}}"),"Helvetica-BoldOblique":h("{'widths'{k3s2q4scx1w201n3r201o6o201s1w201t1w201u1w201w3m201x3m201y3m2k1w2l2l202m2n2n3r2o3r2p5t202q6o2r1s2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v2l3w3u3x3u3y3u3z3x4k6l4l4s4m4s4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3r4v4s4w3x4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v2l5w1w5x2l5y3u5z3r6k2l6l3r6m3x6n3r6o3x6p3r6q2l6r3x6s3x6t1w6u1w6v3r6w1w6x5t6y3x6z3x7k3x7l3x7m2r7n3r7o2l7p3x7q3r7r4y7s3r7t3r7u3m7v2r7w1w7x2r7y3u202l3rcl4sal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3xbq3rbr1wbs2lbu2obv3rbz3xck4s202k3rcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw1w2m2zcy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3res3ret3reu3rev3rew1wex1wey1wez1wfl3xfm3xfn3xfo3xfp3xfq3xfr3ufs3xft3xfu3xfv3xfw3xfz3r203k6o212m6o2dw2l2cq2l3t3r3u2l17s4m19m3r}'kerning'{cl{4qs5ku5ot5qs17sv5tv}201t{2ww4wy2yw}201w{2ks}201x{2ww4wy2yw}2k{201ts201xs}2w{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}2x{5ow5qs}2y{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}'fof'-6o7p{17su5tu5ot}ck{4qs5ku5ot5qs17sv5tv}4l{4qs5ku5ot5qs17sv5tv}cm{4qs5ku5ot5qs17sv5tv}cn{4qs5ku5ot5qs17sv5tv}co{4qs5ku5ot5qs17sv5tv}cp{4qs5ku5ot5qs17sv5tv}6l{17st5tt5os}17s{2kwclvcmvcnvcovcpv4lv4wwckv}5o{2kucltcmtcntcotcpt4lt4wtckt}5q{2ksclscmscnscoscps4ls4wvcks}5r{2ks4ws}5t{2kwclvcmvcnvcovcpv4lv4wwckv}eo{17st5tt5os}fu{17su5tu5ot}6p{17ss5ts}ek{17st5tt5os}el{17st5tt5os}em{17st5tt5os}en{17st5tt5os}6o{201ts}ep{17st5tt5os}es{17ss5ts}et{17ss5ts}eu{17ss5ts}ev{17ss5ts}6z{17su5tu5os5qt}fm{17su5tu5os5qt}fn{17su5tu5os5qt}fo{17su5tu5os5qt}fp{17su5tu5os5qt}fq{17su5tu5os5qt}fs{17su5tu5os5qt}ft{17su5tu5ot}7m{5os}fv{17su5tu5ot}fw{17su5tu5ot}}}"),ZapfDingbats:h("{'widths'{k4u2k1w'fof'6o}'kerning'{'fof'-6o}}"),"Courier-Bold":h("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-Italic":h("{'widths'{k3n2q4ycx2l201n3m201o5t201s2l201t2l201u2l201w3r201x3r201y3r2k1t2l2l202m2n2n3m2o3m2p5n202q5t2r1p2s2l2t2l2u3m2v4n2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w4n3x4n3y4n3z3m4k5w4l3x4m3x4n4m4o4s4p3x4q3x4r4s4s4s4t2l4u2w4v4m4w3r4x5n4y4m4z4s5k3x5l4s5m3x5n3m5o3r5p4s5q3x5r5n5s3x5t3r5u3r5v2r5w1w5x2r5y2u5z3m6k2l6l3m6m3m6n2w6o3m6p2w6q1w6r3m6s3m6t1w6u1w6v2w6w1w6x4s6y3m6z3m7k3m7l3m7m2r7n2r7o1w7p3m7q2w7r4m7s2w7t2w7u2r7v2s7w1v7x2s7y3q202l3mcl3xal2ram3man3mao3map3mar3mas2lat4wau1vav3maw4nay4waz2lbk2sbl4n'fof'6obo2lbp3mbq3obr1tbs2lbu1zbv3mbz3mck3x202k3mcm3xcn3xco3xcp3xcq5tcr4mcs3xct3xcu3xcv3xcw2l2m2ucy2lcz2ldl4mdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek3mel3mem3men3meo3mep3meq4mer2wes2wet2weu2wev2wew1wex1wey1wez1wfl3mfm3mfn3mfo3mfp3mfq3mfr4nfs3mft3mfu3mfv3mfw3mfz2w203k6o212m6m2dw2l2cq2l3t3m3u2l17s3r19m3m}'kerning'{cl{5kt4qw}201s{201sw}201t{201tw2wy2yy6q-t}201x{2wy2yy}2k{201tw}2w{7qs4qy7rs5ky7mw5os5qx5ru17su5tu}2x{17ss5ts5os}2y{7qs4qy7rs5ky7mw5os5qx5ru17su5tu}'fof'-6o6t{17ss5ts5qs}7t{5os}3v{5qs}7p{17su5tu5qs}ck{5kt4qw}4l{5kt4qw}cm{5kt4qw}cn{5kt4qw}co{5kt4qw}cp{5kt4qw}6l{4qs5ks5ou5qw5ru17su5tu}17s{2ks}5q{ckvclvcmvcnvcovcpv4lv}5r{ckuclucmucnucoucpu4lu}5t{2ks}6p{4qs5ks5ou5qw5ru17su5tu}ek{4qs5ks5ou5qw5ru17su5tu}el{4qs5ks5ou5qw5ru17su5tu}em{4qs5ks5ou5qw5ru17su5tu}en{4qs5ks5ou5qw5ru17su5tu}eo{4qs5ks5ou5qw5ru17su5tu}ep{4qs5ks5ou5qw5ru17su5tu}es{5ks5qs4qs}et{4qs5ks5ou5qw5ru17su5tu}eu{4qs5ks5qw5ru17su5tu}ev{5ks5qs4qs}ex{17ss5ts5qs}6z{4qv5ks5ou5qw5ru17su5tu}fm{4qv5ks5ou5qw5ru17su5tu}fn{4qv5ks5ou5qw5ru17su5tu}fo{4qv5ks5ou5qw5ru17su5tu}fp{4qv5ks5ou5qw5ru17su5tu}fq{4qv5ks5ou5qw5ru17su5tu}7r{5os}fs{4qv5ks5ou5qw5ru17su5tu}ft{17su5tu5qs}fu{17su5tu5qs}fv{17su5tu5qs}fw{17su5tu5qs}}}"),"Times-Roman":h("{'widths'{k3n2q4ycx2l201n3m201o6o201s2l201t2l201u2l201w2w201x2w201y2w2k1t2l2l202m2n2n3m2o3m2p5n202q6o2r1m2s2l2t2l2u3m2v3s2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v1w3w3s3x3s3y3s3z2w4k5w4l4s4m4m4n4m4o4s4p3x4q3r4r4s4s4s4t2l4u2r4v4s4w3x4x5t4y4s4z4s5k3r5l4s5m4m5n3r5o3x5p4s5q4s5r5y5s4s5t4s5u3x5v2l5w1w5x2l5y2z5z3m6k2l6l2w6m3m6n2w6o3m6p2w6q2l6r3m6s3m6t1w6u1w6v3m6w1w6x4y6y3m6z3m7k3m7l3m7m2l7n2r7o1w7p3m7q3m7r4s7s3m7t3m7u2w7v3k7w1o7x3k7y3q202l3mcl4sal2lam3man3mao3map3mar3mas2lat4wau1vav3maw3say4waz2lbk2sbl3s'fof'6obo2lbp3mbq2xbr1tbs2lbu1zbv3mbz2wck4s202k3mcm4scn4sco4scp4scq5tcr4mcs3xct3xcu3xcv3xcw2l2m2tcy2lcz2ldl4sdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek2wel2wem2wen2weo2wep2weq4mer2wes2wet2weu2wev2wew1wex1wey1wez1wfl3mfm3mfn3mfo3mfp3mfq3mfr3sfs3mft3mfu3mfv3mfw3mfz3m203k6o212m6m2dw2l2cq2l3t3m3u1w17s4s19m3m}'kerning'{cl{4qs5ku17sw5ou5qy5rw201ss5tw201ws}201s{201ss}201t{ckw4lwcmwcnwcowcpwclw4wu201ts}2k{201ts}2w{4qs5kw5os5qx5ru17sx5tx}2x{17sw5tw5ou5qu}2y{4qs5kw5os5qx5ru17sx5tx}'fof'-6o7t{ckuclucmucnucoucpu4lu5os5rs}3u{17su5tu5qs}3v{17su5tu5qs}7p{17sw5tw5qs}ck{4qs5ku17sw5ou5qy5rw201ss5tw201ws}4l{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cm{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cn{4qs5ku17sw5ou5qy5rw201ss5tw201ws}co{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cp{4qs5ku17sw5ou5qy5rw201ss5tw201ws}6l{17su5tu5os5qw5rs}17s{2ktclvcmvcnvcovcpv4lv4wuckv}5o{ckwclwcmwcnwcowcpw4lw4wu}5q{ckyclycmycnycoycpy4ly4wu5ms}5r{cktcltcmtcntcotcpt4lt4ws}5t{2ktclvcmvcnvcovcpv4lv4wuckv}7q{cksclscmscnscoscps4ls}6p{17su5tu5qw5rs}ek{5qs5rs}el{17su5tu5os5qw5rs}em{17su5tu5os5qs5rs}en{17su5qs5rs}eo{5qs5rs}ep{17su5tu5os5qw5rs}es{5qs}et{17su5tu5qw5rs}eu{17su5tu5qs5rs}ev{5qs}6z{17sv5tv5os5qx5rs}fm{5os5qt5rs}fn{17sv5tv5os5qx5rs}fo{17sv5tv5os5qx5rs}fp{5os5qt5rs}fq{5os5qt5rs}7r{ckuclucmucnucoucpu4lu5os}fs{17sv5tv5os5qx5rs}ft{17ss5ts5qs}fu{17sw5tw5qs}fv{17sw5tw5qs}fw{17ss5ts5qs}fz{ckuclucmucnucoucpu4lu5os5rs}}}"),"Helvetica-Oblique":h("{'widths'{k3p2q4mcx1w201n3r201o6o201s1q201t1q201u1q201w2l201x2l201y2l2k1w2l1w202m2n2n3r2o3r2p5t202q6o2r1n2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v1w3w3u3x3u3y3u3z3r4k6p4l4m4m4m4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3m4v4m4w3r4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v1w5w1w5x1w5y2z5z3r6k2l6l3r6m3r6n3m6o3r6p3r6q1w6r3r6s3r6t1q6u1q6v3m6w1q6x5n6y3r6z3r7k3r7l3r7m2l7n3m7o1w7p3r7q3m7r4s7s3m7t3m7u3m7v2l7w1u7x2l7y3u202l3rcl4mal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3rbr1wbs2lbu2obv3rbz3xck4m202k3rcm4mcn4mco4mcp4mcq6ocr4scs4mct4mcu4mcv4mcw1w2m2ncy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3mes3ret3reu3rev3rew1wex1wey1wez1wfl3rfm3rfn3rfo3rfp3rfq3rfr3ufs3xft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3r3u1w17s4m19m3r}'kerning'{5q{4wv}cl{4qs5kw5ow5qs17sv5tv}201t{2wu4w1k2yu}201x{2wu4wy2yu}17s{2ktclucmucnu4otcpu4lu4wycoucku}2w{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}2x{17sy5ty5oy5qs}2y{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}'fof'-6o7p{17sv5tv5ow}ck{4qs5kw5ow5qs17sv5tv}4l{4qs5kw5ow5qs17sv5tv}cm{4qs5kw5ow5qs17sv5tv}cn{4qs5kw5ow5qs17sv5tv}co{4qs5kw5ow5qs17sv5tv}cp{4qs5kw5ow5qs17sv5tv}6l{17sy5ty5ow}do{17st5tt}4z{17st5tt}7s{fst}dm{17st5tt}dn{17st5tt}5o{ckwclwcmwcnwcowcpw4lw4wv}dp{17st5tt}dq{17st5tt}7t{5ow}ds{17st5tt}5t{2ktclucmucnu4otcpu4lu4wycoucku}fu{17sv5tv5ow}6p{17sy5ty5ow5qs}ek{17sy5ty5ow}el{17sy5ty5ow}em{17sy5ty5ow}en{5ty}eo{17sy5ty5ow}ep{17sy5ty5ow}es{17sy5ty5qs}et{17sy5ty5ow5qs}eu{17sy5ty5ow5qs}ev{17sy5ty5ow5qs}6z{17sy5ty5ow5qs}fm{17sy5ty5ow5qs}fn{17sy5ty5ow5qs}fo{17sy5ty5ow5qs}fp{17sy5ty5qs}fq{17sy5ty5ow5qs}7r{5ow}fs{17sy5ty5ow5qs}ft{17sv5tv5ow}7m{5ow}fv{17sv5tv5ow}fw{17sv5tv5ow}}}")}};n.events.push(["addFont",function(w){var S=w.font,p=b.Unicode[S.postScriptName];p&&(S.metadata.Unicode={},S.metadata.Unicode.widths=p.widths,S.metadata.Unicode.kerning=p.kerning);var M=g.Unicode[S.postScriptName];M&&(S.metadata.Unicode.encoding=M,S.encoding=M.codePages[0])}])}(zt.API),function(n){var e=function(r){for(var a=r.length,l=new Uint8Array(a),s=0;s<a;s++)l[s]=r.charCodeAt(s);return l};n.API.events.push(["addFont",function(r){var a=void 0,l=r.font,s=r.instance;if(!l.isStandardFont){if(s===void 0)throw new Error("Font does not exist in vFS, import fonts or remove declaration doc.addFont('"+l.postScriptName+"').");if(typeof(a=s.existsFileInVFS(l.postScriptName)===!1?s.loadFile(l.postScriptName):s.getFileFromVFS(l.postScriptName))!="string")throw new Error("Font is not stored as string-data in vFS, import fonts or remove declaration doc.addFont('"+l.postScriptName+"').");(function(c,h){h=/^\x00\x01\x00\x00/.test(h)?e(h):e(ba(h)),c.metadata=n.API.TTFFont.open(h),c.metadata.Unicode=c.metadata.Unicode||{encoding:{},kerning:{},widths:[]},c.metadata.glyIdsUsed=[0]})(l,a)}}])}(zt),function(n){function e(){return(Ht.canvg?Promise.resolve(Ht.canvg):So(()=>import("./index.es2.js"),__vite__mapDeps([0,1,2,3,4,5,6]))).catch(function(r){return Promise.reject(new Error("Could not load canvg: "+r))}).then(function(r){return r.default?r.default:r})}zt.API.addSvgAsImage=function(r,a,l,s,c,h,f,g){if(isNaN(a)||isNaN(l))throw be.error("jsPDF.addSvgAsImage: Invalid coordinates",arguments),new Error("Invalid coordinates passed to jsPDF.addSvgAsImage");if(isNaN(s)||isNaN(c))throw be.error("jsPDF.addSvgAsImage: Invalid measurements",arguments),new Error("Invalid measurements (width and/or height) passed to jsPDF.addSvgAsImage");var b=document.createElement("canvas");b.width=s,b.height=c;var w=b.getContext("2d");w.fillStyle="#fff",w.fillRect(0,0,b.width,b.height);var S={ignoreMouse:!0,ignoreAnimation:!0,ignoreDimensions:!0},p=this;return e().then(function(M){return M.fromString(w,r,S)},function(){return Promise.reject(new Error("Could not load canvg."))}).then(function(M){return M.render(S)}).then(function(){p.addImage(b.toDataURL("image/jpeg",1),a,l,s,c,f,g)})}}(),zt.API.putTotalPages=function(n){var e,r=0;parseInt(this.internal.getFont().id.substr(1),10)<15?(e=new RegExp(n,"g"),r=this.internal.getNumberOfPages()):(e=new RegExp(this.pdfEscape16(n,this.internal.getFont()),"g"),r=this.pdfEscape16(this.internal.getNumberOfPages()+"",this.internal.getFont()));for(var a=1;a<=this.internal.getNumberOfPages();a++)for(var l=0;l<this.internal.pages[a].length;l++)this.internal.pages[a][l]=this.internal.pages[a][l].replace(e,r);return this},zt.API.viewerPreferences=function(n,e){var r;n=n||{},e=e||!1;var a,l,s,c={HideToolbar:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},HideMenubar:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},HideWindowUI:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},FitWindow:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},CenterWindow:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},DisplayDocTitle:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.4},NonFullScreenPageMode:{defaultValue:"UseNone",value:"UseNone",type:"name",explicitSet:!1,valueSet:["UseNone","UseOutlines","UseThumbs","UseOC"],pdfVersion:1.3},Direction:{defaultValue:"L2R",value:"L2R",type:"name",explicitSet:!1,valueSet:["L2R","R2L"],pdfVersion:1.3},ViewArea:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},ViewClip:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintArea:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintClip:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintScaling:{defaultValue:"AppDefault",value:"AppDefault",type:"name",explicitSet:!1,valueSet:["AppDefault","None"],pdfVersion:1.6},Duplex:{defaultValue:"",value:"none",type:"name",explicitSet:!1,valueSet:["Simplex","DuplexFlipShortEdge","DuplexFlipLongEdge","none"],pdfVersion:1.7},PickTrayByPDFSize:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.7},PrintPageRange:{defaultValue:"",value:"",type:"array",explicitSet:!1,valueSet:null,pdfVersion:1.7},NumCopies:{defaultValue:1,value:1,type:"integer",explicitSet:!1,valueSet:null,pdfVersion:1.7}},h=Object.keys(c),f=[],g=0,b=0,w=0;function S(M,I){var q,k=!1;for(q=0;q<M.length;q+=1)M[q]===I&&(k=!0);return k}if(this.internal.viewerpreferences===void 0&&(this.internal.viewerpreferences={},this.internal.viewerpreferences.configuration=JSON.parse(JSON.stringify(c)),this.internal.viewerpreferences.isSubscribed=!1),r=this.internal.viewerpreferences.configuration,n==="reset"||e===!0){var p=h.length;for(w=0;w<p;w+=1)r[h[w]].value=r[h[w]].defaultValue,r[h[w]].explicitSet=!1}if(de(n)==="object"){for(l in n)if(s=n[l],S(h,l)&&s!==void 0){if(r[l].type==="boolean"&&typeof s=="boolean")r[l].value=s;else if(r[l].type==="name"&&S(r[l].valueSet,s))r[l].value=s;else if(r[l].type==="integer"&&Number.isInteger(s))r[l].value=s;else if(r[l].type==="array"){for(g=0;g<s.length;g+=1)if(a=!0,s[g].length===1&&typeof s[g][0]=="number")f.push(String(s[g]-1));else if(s[g].length>1){for(b=0;b<s[g].length;b+=1)typeof s[g][b]!="number"&&(a=!1);a===!0&&f.push([s[g][0]-1,s[g][1]-1].join(" "))}r[l].value="["+f.join(" ")+"]"}else r[l].value=r[l].defaultValue;r[l].explicitSet=!0}}return this.internal.viewerpreferences.isSubscribed===!1&&(this.internal.events.subscribe("putCatalog",function(){var M,I=[];for(M in r)r[M].explicitSet===!0&&(r[M].type==="name"?I.push("/"+M+" /"+r[M].value):I.push("/"+M+" "+r[M].value));I.length!==0&&this.internal.write(`/ViewerPreferences
<<
`+I.join(`
`)+`
>>`)}),this.internal.viewerpreferences.isSubscribed=!0),this.internal.viewerpreferences.configuration=r,this},function(n){var e=function(){var a='<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"><rdf:Description rdf:about="" xmlns:jspdf="'+this.internal.__metadata__.namespaceuri+'"><jspdf:metadata>',l=unescape(encodeURIComponent('<x:xmpmeta xmlns:x="adobe:ns:meta/">')),s=unescape(encodeURIComponent(a)),c=unescape(encodeURIComponent(this.internal.__metadata__.metadata)),h=unescape(encodeURIComponent("</jspdf:metadata></rdf:Description></rdf:RDF>")),f=unescape(encodeURIComponent("</x:xmpmeta>")),g=s.length+c.length+h.length+l.length+f.length;this.internal.__metadata__.metadata_object_number=this.internal.newObject(),this.internal.write("<< /Type /Metadata /Subtype /XML /Length "+g+" >>"),this.internal.write("stream"),this.internal.write(l+s+c+h+f),this.internal.write("endstream"),this.internal.write("endobj")},r=function(){this.internal.__metadata__.metadata_object_number&&this.internal.write("/Metadata "+this.internal.__metadata__.metadata_object_number+" 0 R")};n.addMetadata=function(a,l){return this.internal.__metadata__===void 0&&(this.internal.__metadata__={metadata:a,namespaceuri:l||"http://jspdf.default.namespaceuri/"},this.internal.events.subscribe("putCatalog",r),this.internal.events.subscribe("postPutResources",e)),this}}(zt.API),function(n){var e=n.API,r=e.pdfEscape16=function(s,c){for(var h,f=c.metadata.Unicode.widths,g=["","0","00","000","0000"],b=[""],w=0,S=s.length;w<S;++w){if(h=c.metadata.characterToGlyph(s.charCodeAt(w)),c.metadata.glyIdsUsed.push(h),c.metadata.toUnicode[h]=s.charCodeAt(w),f.indexOf(h)==-1&&(f.push(h),f.push([parseInt(c.metadata.widthOfGlyph(h),10)])),h=="0")return b.join("");h=h.toString(16),b.push(g[4-h.length],h)}return b.join("")},a=function(s){var c,h,f,g,b,w,S;for(b=`/CIDInit /ProcSet findresource begin
12 dict begin
begincmap
/CIDSystemInfo <<
  /Registry (Adobe)
  /Ordering (UCS)
  /Supplement 0
>> def
/CMapName /Adobe-Identity-UCS def
/CMapType 2 def
1 begincodespacerange
<0000><ffff>
endcodespacerange`,f=[],w=0,S=(h=Object.keys(s).sort(function(p,M){return p-M})).length;w<S;w++)c=h[w],f.length>=100&&(b+=`
`+f.length+` beginbfchar
`+f.join(`
`)+`
endbfchar`,f=[]),s[c]!==void 0&&s[c]!==null&&typeof s[c].toString=="function"&&(g=("0000"+s[c].toString(16)).slice(-4),c=("0000"+(+c).toString(16)).slice(-4),f.push("<"+c+"><"+g+">"));return f.length&&(b+=`
`+f.length+` beginbfchar
`+f.join(`
`)+`
endbfchar
`),b+=`endcmap
CMapName currentdict /CMap defineresource pop
end
end`};e.events.push(["putFont",function(s){(function(c){var h=c.font,f=c.out,g=c.newObject,b=c.putStream;if(h.metadata instanceof n.API.TTFFont&&h.encoding==="Identity-H"){for(var w=h.metadata.Unicode.widths,S=h.metadata.subset.encode(h.metadata.glyIdsUsed,1),p="",M=0;M<S.length;M++)p+=String.fromCharCode(S[M]);var I=g();b({data:p,addLength1:!0,objectId:I}),f("endobj");var q=g();b({data:a(h.metadata.toUnicode),addLength1:!0,objectId:q}),f("endobj");var k=g();f("<<"),f("/Type /FontDescriptor"),f("/FontName /"+Mi(h.fontName)),f("/FontFile2 "+I+" 0 R"),f("/FontBBox "+n.API.PDFObject.convert(h.metadata.bbox)),f("/Flags "+h.metadata.flags),f("/StemV "+h.metadata.stemV),f("/ItalicAngle "+h.metadata.italicAngle),f("/Ascent "+h.metadata.ascender),f("/Descent "+h.metadata.decender),f("/CapHeight "+h.metadata.capHeight),f(">>"),f("endobj");var E=g();f("<<"),f("/Type /Font"),f("/BaseFont /"+Mi(h.fontName)),f("/FontDescriptor "+k+" 0 R"),f("/W "+n.API.PDFObject.convert(w)),f("/CIDToGIDMap /Identity"),f("/DW 1000"),f("/Subtype /CIDFontType2"),f("/CIDSystemInfo"),f("<<"),f("/Supplement 0"),f("/Registry (Adobe)"),f("/Ordering ("+h.encoding+")"),f(">>"),f(">>"),f("endobj"),h.objectNumber=g(),f("<<"),f("/Type /Font"),f("/Subtype /Type0"),f("/ToUnicode "+q+" 0 R"),f("/BaseFont /"+Mi(h.fontName)),f("/Encoding /"+h.encoding),f("/DescendantFonts ["+E+" 0 R]"),f(">>"),f("endobj"),h.isAlreadyPutted=!0}})(s)}]),e.events.push(["putFont",function(s){(function(c){var h=c.font,f=c.out,g=c.newObject,b=c.putStream;if(h.metadata instanceof n.API.TTFFont&&h.encoding==="WinAnsiEncoding"){for(var w=h.metadata.rawData,S="",p=0;p<w.length;p++)S+=String.fromCharCode(w[p]);var M=g();b({data:S,addLength1:!0,objectId:M}),f("endobj");var I=g();b({data:a(h.metadata.toUnicode),addLength1:!0,objectId:I}),f("endobj");var q=g();f("<<"),f("/Descent "+h.metadata.decender),f("/CapHeight "+h.metadata.capHeight),f("/StemV "+h.metadata.stemV),f("/Type /FontDescriptor"),f("/FontFile2 "+M+" 0 R"),f("/Flags 96"),f("/FontBBox "+n.API.PDFObject.convert(h.metadata.bbox)),f("/FontName /"+Mi(h.fontName)),f("/ItalicAngle "+h.metadata.italicAngle),f("/Ascent "+h.metadata.ascender),f(">>"),f("endobj"),h.objectNumber=g();for(var k=0;k<h.metadata.hmtx.widths.length;k++)h.metadata.hmtx.widths[k]=parseInt(h.metadata.hmtx.widths[k]*(1e3/h.metadata.head.unitsPerEm));f("<</Subtype/TrueType/Type/Font/ToUnicode "+I+" 0 R/BaseFont/"+Mi(h.fontName)+"/FontDescriptor "+q+" 0 R/Encoding/"+h.encoding+" /FirstChar 29 /LastChar 255 /Widths "+n.API.PDFObject.convert(h.metadata.hmtx.widths)+">>"),f("endobj"),h.isAlreadyPutted=!0}})(s)}]);var l=function(s){var c,h=s.text||"",f=s.x,g=s.y,b=s.options||{},w=s.mutex||{},S=w.pdfEscape,p=w.activeFontKey,M=w.fonts,I=p,q="",k=0,E="",J=M[I].encoding;if(M[I].encoding!=="Identity-H")return{text:h,x:f,y:g,options:b,mutex:w};for(E=h,I=p,Array.isArray(h)&&(E=h[0]),k=0;k<E.length;k+=1)M[I].metadata.hasOwnProperty("cmap")&&(c=M[I].metadata.cmap.unicode.codeMap[E[k].charCodeAt(0)]),c||E[k].charCodeAt(0)<256&&M[I].metadata.hasOwnProperty("Unicode")?q+=E[k]:q+="";var ot="";return parseInt(I.slice(1))<14||J==="WinAnsiEncoding"?ot=S(q,I).split("").map(function(ut){return ut.charCodeAt(0).toString(16)}).join(""):J==="Identity-H"&&(ot=r(q,M[I])),w.isHex=!0,{text:ot,x:f,y:g,options:b,mutex:w}};e.events.push(["postProcessText",function(s){var c=s.text||"",h=[],f={text:c,x:s.x,y:s.y,options:s.options,mutex:s.mutex};if(Array.isArray(c)){var g=0;for(g=0;g<c.length;g+=1)Array.isArray(c[g])&&c[g].length===3?h.push([l(Object.assign({},f,{text:c[g][0]})).text,c[g][1],c[g][2]]):h.push(l(Object.assign({},f,{text:c[g]})).text);s.text=h}else s.text=l(Object.assign({},f,{text:c})).text}])}(zt),function(n){var e=function(){return this.internal.vFS===void 0&&(this.internal.vFS={}),!0};n.existsFileInVFS=function(r){return e.call(this),this.internal.vFS[r]!==void 0},n.addFileToVFS=function(r,a){return e.call(this),this.internal.vFS[r]=a,this},n.getFileFromVFS=function(r){return e.call(this),this.internal.vFS[r]!==void 0?this.internal.vFS[r]:null}}(zt.API),function(n){n.__bidiEngine__=n.prototype.__bidiEngine__=function(a){var l,s,c,h,f,g,b,w=e,S=[[0,3,0,1,0,0,0],[0,3,0,1,2,2,0],[0,3,0,17,2,0,1],[0,3,5,5,4,1,0],[0,3,21,21,4,0,1],[0,3,5,5,4,2,0]],p=[[2,0,1,1,0,1,0],[2,0,1,1,0,2,0],[2,0,2,1,3,2,0],[2,0,2,33,3,1,1]],M={L:0,R:1,EN:2,AN:3,N:4,B:5,S:6},I={0:0,5:1,6:2,7:3,32:4,251:5,254:6,255:7},q=["(",")","(","<",">","<","[","]","[","{","}","{","«","»","«","‹","›","‹","⁅","⁆","⁅","⁽","⁾","⁽","₍","₎","₍","≤","≥","≤","〈","〉","〈","﹙","﹚","﹙","﹛","﹜","﹛","﹝","﹞","﹝","﹤","﹥","﹤"],k=new RegExp(/^([1-4|9]|1[0-9]|2[0-9]|3[0168]|4[04589]|5[012]|7[78]|159|16[0-9]|17[0-2]|21[569]|22[03489]|250)$/),E=!1,J=0;this.__bidiEngine__={};var ot=function(L){var A=L.charCodeAt(),B=A>>8,D=I[B];return D!==void 0?w[256*D+(255&A)]:B===252||B===253?"AL":k.test(B)?"L":B===8?"R":"N"},ut=function(L){for(var A,B=0;B<L.length;B++){if((A=ot(L.charAt(B)))==="L")return!1;if(A==="R")return!0}return!1},yt=function(L,A,B,D){var et,rt,ct,X,at=A[D];switch(at){case"L":case"R":E=!1;break;case"N":case"AN":break;case"EN":E&&(at="AN");break;case"AL":E=!0,at="R";break;case"WS":at="N";break;case"CS":D<1||D+1>=A.length||(et=B[D-1])!=="EN"&&et!=="AN"||(rt=A[D+1])!=="EN"&&rt!=="AN"?at="N":E&&(rt="AN"),at=rt===et?rt:"N";break;case"ES":at=(et=D>0?B[D-1]:"B")==="EN"&&D+1<A.length&&A[D+1]==="EN"?"EN":"N";break;case"ET":if(D>0&&B[D-1]==="EN"){at="EN";break}if(E){at="N";break}for(ct=D+1,X=A.length;ct<X&&A[ct]==="ET";)ct++;at=ct<X&&A[ct]==="EN"?"EN":"N";break;case"NSM":if(c&&!h){for(X=A.length,ct=D+1;ct<X&&A[ct]==="NSM";)ct++;if(ct<X){var ft=L[D],Ft=ft>=1425&&ft<=2303||ft===64286;if(et=A[ct],Ft&&(et==="R"||et==="AL")){at="R";break}}}at=D<1||(et=A[D-1])==="B"?"N":B[D-1];break;case"B":E=!1,l=!0,at=J;break;case"S":s=!0,at="N";break;case"LRE":case"RLE":case"LRO":case"RLO":case"PDF":E=!1;break;case"BN":at="N"}return at},tt=function(L,A,B){var D=L.split("");return B&&U(D,B,{hiLevel:J}),D.reverse(),A&&A.reverse(),D.join("")},U=function(L,A,B){var D,et,rt,ct,X,at=-1,ft=L.length,Ft=0,N=[],C=J?p:S,O=[];for(E=!1,l=!1,s=!1,et=0;et<ft;et++)O[et]=ot(L[et]);for(rt=0;rt<ft;rt++){if(X=Ft,N[rt]=yt(L,O,N,rt),D=240&(Ft=C[X][M[N[rt]]]),Ft&=15,A[rt]=ct=C[Ft][5],D>0)if(D===16){for(et=at;et<rt;et++)A[et]=1;at=-1}else at=-1;if(C[Ft][6])at===-1&&(at=rt);else if(at>-1){for(et=at;et<rt;et++)A[et]=ct;at=-1}O[rt]==="B"&&(A[rt]=0),B.hiLevel|=ct}s&&function(R,G,$){for(var it=0;it<$;it++)if(R[it]==="S"){G[it]=J;for(var st=it-1;st>=0&&R[st]==="WS";st--)G[st]=J}}(O,A,ft)},nt=function(L,A,B,D,et){if(!(et.hiLevel<L)){if(L===1&&J===1&&!l)return A.reverse(),void(B&&B.reverse());for(var rt,ct,X,at,ft=A.length,Ft=0;Ft<ft;){if(D[Ft]>=L){for(X=Ft+1;X<ft&&D[X]>=L;)X++;for(at=Ft,ct=X-1;at<ct;at++,ct--)rt=A[at],A[at]=A[ct],A[ct]=rt,B&&(rt=B[at],B[at]=B[ct],B[ct]=rt);Ft=X}Ft++}}},pt=function(L,A,B){var D=L.split(""),et={hiLevel:J};return B||(B=[]),U(D,B,et),function(rt,ct,X){if(X.hiLevel!==0&&b)for(var at,ft=0;ft<rt.length;ft++)ct[ft]===1&&(at=q.indexOf(rt[ft]))>=0&&(rt[ft]=q[at+1])}(D,B,et),nt(2,D,A,B,et),nt(1,D,A,B,et),D.join("")};return this.__bidiEngine__.doBidiReorder=function(L,A,B){if(function(et,rt){if(rt)for(var ct=0;ct<et.length;ct++)rt[ct]=ct;h===void 0&&(h=ut(et)),g===void 0&&(g=ut(et))}(L,A),c||!f||g)if(c&&f&&h^g)J=h?1:0,L=tt(L,A,B);else if(!c&&f&&g)J=h?1:0,L=pt(L,A,B),L=tt(L,A);else if(!c||h||f||g){if(c&&!f&&h^g)L=tt(L,A),h?(J=0,L=pt(L,A,B)):(J=1,L=pt(L,A,B),L=tt(L,A));else if(c&&h&&!f&&g)J=1,L=pt(L,A,B),L=tt(L,A);else if(!c&&!f&&h^g){var D=b;h?(J=1,L=pt(L,A,B),J=0,b=!1,L=pt(L,A,B),b=D):(J=0,L=pt(L,A,B),L=tt(L,A),J=1,b=!1,L=pt(L,A,B),b=D,L=tt(L,A))}}else J=0,L=pt(L,A,B);else J=h?1:0,L=pt(L,A,B);return L},this.__bidiEngine__.setOptions=function(L){L&&(c=L.isInputVisual,f=L.isOutputVisual,h=L.isInputRtl,g=L.isOutputRtl,b=L.isSymmetricSwapping)},this.__bidiEngine__.setOptions(a),this.__bidiEngine__};var e=["BN","BN","BN","BN","BN","BN","BN","BN","BN","S","B","S","WS","B","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","B","B","B","S","WS","N","N","ET","ET","ET","N","N","N","N","N","ES","CS","ES","CS","CS","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","CS","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","BN","BN","BN","BN","BN","BN","B","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","CS","N","ET","ET","ET","ET","N","N","N","N","L","N","N","BN","N","N","ET","ET","EN","EN","N","L","N","N","N","EN","L","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","N","N","N","N","N","ET","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","R","NSM","R","NSM","NSM","R","NSM","NSM","R","NSM","N","N","N","N","N","N","N","N","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","N","N","N","N","N","R","R","R","R","R","N","N","N","N","N","N","N","N","N","N","N","AN","AN","AN","AN","AN","AN","N","N","AL","ET","ET","AL","CS","AL","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AL","AL","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AN","AN","AN","AN","AN","AN","AN","AN","AN","AN","ET","AN","AN","AL","AL","AL","NSM","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AN","N","NSM","NSM","NSM","NSM","NSM","NSM","AL","AL","NSM","NSM","N","NSM","NSM","NSM","NSM","AL","AL","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","AL","AL","NSM","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AL","N","N","N","N","N","N","N","N","N","N","N","N","N","N","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","R","R","N","N","N","N","R","N","N","N","N","N","WS","WS","WS","WS","WS","WS","WS","WS","WS","WS","WS","BN","BN","BN","L","R","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","WS","B","LRE","RLE","PDF","LRO","RLO","CS","ET","ET","ET","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","CS","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","WS","BN","BN","BN","BN","BN","N","LRI","RLI","FSI","PDI","BN","BN","BN","BN","BN","BN","EN","L","N","N","EN","EN","EN","EN","EN","EN","ES","ES","N","N","N","L","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","ES","ES","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","L","L","N","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","N","N","N","N","N","R","NSM","R","R","R","R","R","R","R","R","R","R","ES","R","R","R","R","R","R","R","R","R","R","R","R","R","N","R","R","R","R","R","N","R","N","R","R","N","R","R","N","R","R","R","R","R","R","R","R","R","R","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","CS","N","CS","N","N","CS","N","N","N","N","N","N","N","N","N","ET","N","N","ES","ES","N","N","N","N","N","ET","ET","N","N","N","N","N","AL","AL","AL","AL","AL","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","N","BN","N","N","N","ET","ET","ET","N","N","N","N","N","ES","CS","ES","CS","CS","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","CS","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","L","L","L","L","L","L","N","N","L","L","L","L","L","L","N","N","L","L","L","L","L","L","N","N","L","L","L","N","N","N","ET","ET","N","N","N","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N"],r=new n.__bidiEngine__({isInputVisual:!0});n.API.events.push(["postProcessText",function(a){var l=a.text;a.x,a.y;var s=a.options||{};a.mutex,s.lang;var c=[];if(s.isInputVisual=typeof s.isInputVisual!="boolean"||s.isInputVisual,r.setOptions(s),Object.prototype.toString.call(l)==="[object Array]"){var h=0;for(c=[],h=0;h<l.length;h+=1)Object.prototype.toString.call(l[h])==="[object Array]"?c.push([r.doBidiReorder(l[h][0]),l[h][1],l[h][2]]):c.push([r.doBidiReorder(l[h])]);a.text=c}else a.text=r.doBidiReorder(l);r.setOptions({isInputVisual:!0})}])}(zt),zt.API.TTFFont=function(){function n(e){var r;if(this.rawData=e,r=this.contents=new Tn(e),this.contents.pos=4,r.readString(4)==="ttcf")throw new Error("TTCF not supported.");r.pos=0,this.parse(),this.subset=new Jc(this),this.registerTTF()}return n.open=function(e){return new n(e)},n.prototype.parse=function(){return this.directory=new Mc(this.contents),this.head=new Ec(this),this.name=new zc(this),this.cmap=new iu(this),this.toUnicode={},this.hhea=new Tc(this),this.maxp=new Uc(this),this.hmtx=new Hc(this),this.post=new Dc(this),this.os2=new qc(this),this.loca=new Yc(this),this.glyf=new Wc(this),this.ascender=this.os2.exists&&this.os2.ascender||this.hhea.ascender,this.decender=this.os2.exists&&this.os2.decender||this.hhea.decender,this.lineGap=this.os2.exists&&this.os2.lineGap||this.hhea.lineGap,this.bbox=[this.head.xMin,this.head.yMin,this.head.xMax,this.head.yMax]},n.prototype.registerTTF=function(){var e,r,a,l,s;if(this.scaleFactor=1e3/this.head.unitsPerEm,this.bbox=(function(){var c,h,f,g;for(g=[],c=0,h=(f=this.bbox).length;c<h;c++)e=f[c],g.push(Math.round(e*this.scaleFactor));return g}).call(this),this.stemV=0,this.post.exists?(a=255&(l=this.post.italic_angle),32768&(r=l>>16)&&(r=-(1+(65535^r))),this.italicAngle=+(r+"."+a)):this.italicAngle=0,this.ascender=Math.round(this.ascender*this.scaleFactor),this.decender=Math.round(this.decender*this.scaleFactor),this.lineGap=Math.round(this.lineGap*this.scaleFactor),this.capHeight=this.os2.exists&&this.os2.capHeight||this.ascender,this.xHeight=this.os2.exists&&this.os2.xHeight||0,this.familyClass=(this.os2.exists&&this.os2.familyClass||0)>>8,this.isSerif=(s=this.familyClass)===1||s===2||s===3||s===4||s===5||s===7,this.isScript=this.familyClass===10,this.flags=0,this.post.isFixedPitch&&(this.flags|=1),this.isSerif&&(this.flags|=2),this.isScript&&(this.flags|=8),this.italicAngle!==0&&(this.flags|=64),this.flags|=32,!this.cmap.unicode)throw new Error("No unicode cmap for font")},n.prototype.characterToGlyph=function(e){var r;return((r=this.cmap.unicode)!=null?r.codeMap[e]:void 0)||0},n.prototype.widthOfGlyph=function(e){var r;return r=1e3/this.head.unitsPerEm,this.hmtx.forGlyph(e).advance*r},n.prototype.widthOfString=function(e,r,a){var l,s,c,h;for(c=0,s=0,h=(e=""+e).length;0<=h?s<h:s>h;s=0<=h?++s:--s)l=e.charCodeAt(s),c+=this.widthOfGlyph(this.characterToGlyph(l))+a*(1e3/r)||0;return c*(r/1e3)},n.prototype.lineHeight=function(e,r){var a;return r==null&&(r=!1),a=r?this.lineGap:0,(this.ascender+a-this.decender)/1e3*e},n}();var Wr,Tn=function(){function n(e){this.data=e??[],this.pos=0,this.length=this.data.length}return n.prototype.readByte=function(){return this.data[this.pos++]},n.prototype.writeByte=function(e){return this.data[this.pos++]=e},n.prototype.readUInt32=function(){return 16777216*this.readByte()+(this.readByte()<<16)+(this.readByte()<<8)+this.readByte()},n.prototype.writeUInt32=function(e){return this.writeByte(e>>>24&255),this.writeByte(e>>16&255),this.writeByte(e>>8&255),this.writeByte(255&e)},n.prototype.readInt32=function(){var e;return(e=this.readUInt32())>=2147483648?e-4294967296:e},n.prototype.writeInt32=function(e){return e<0&&(e+=4294967296),this.writeUInt32(e)},n.prototype.readUInt16=function(){return this.readByte()<<8|this.readByte()},n.prototype.writeUInt16=function(e){return this.writeByte(e>>8&255),this.writeByte(255&e)},n.prototype.readInt16=function(){var e;return(e=this.readUInt16())>=32768?e-65536:e},n.prototype.writeInt16=function(e){return e<0&&(e+=65536),this.writeUInt16(e)},n.prototype.readString=function(e){var r,a;for(a=[],r=0;0<=e?r<e:r>e;r=0<=e?++r:--r)a[r]=String.fromCharCode(this.readByte());return a.join("")},n.prototype.writeString=function(e){var r,a,l;for(l=[],r=0,a=e.length;0<=a?r<a:r>a;r=0<=a?++r:--r)l.push(this.writeByte(e.charCodeAt(r)));return l},n.prototype.readShort=function(){return this.readInt16()},n.prototype.writeShort=function(e){return this.writeInt16(e)},n.prototype.readLongLong=function(){var e,r,a,l,s,c,h,f;return e=this.readByte(),r=this.readByte(),a=this.readByte(),l=this.readByte(),s=this.readByte(),c=this.readByte(),h=this.readByte(),f=this.readByte(),128&e?-1*(72057594037927940*(255^e)+281474976710656*(255^r)+1099511627776*(255^a)+4294967296*(255^l)+16777216*(255^s)+65536*(255^c)+256*(255^h)+(255^f)+1):72057594037927940*e+281474976710656*r+1099511627776*a+4294967296*l+16777216*s+65536*c+256*h+f},n.prototype.writeLongLong=function(e){var r,a;return r=Math.floor(e/4294967296),a=**********&e,this.writeByte(r>>24&255),this.writeByte(r>>16&255),this.writeByte(r>>8&255),this.writeByte(255&r),this.writeByte(a>>24&255),this.writeByte(a>>16&255),this.writeByte(a>>8&255),this.writeByte(255&a)},n.prototype.readInt=function(){return this.readInt32()},n.prototype.writeInt=function(e){return this.writeInt32(e)},n.prototype.read=function(e){var r,a;for(r=[],a=0;0<=e?a<e:a>e;a=0<=e?++a:--a)r.push(this.readByte());return r},n.prototype.write=function(e){var r,a,l,s;for(s=[],a=0,l=e.length;a<l;a++)r=e[a],s.push(this.writeByte(r));return s},n}(),Mc=function(){var n;function e(r){var a,l,s;for(this.scalarType=r.readInt(),this.tableCount=r.readShort(),this.searchRange=r.readShort(),this.entrySelector=r.readShort(),this.rangeShift=r.readShort(),this.tables={},l=0,s=this.tableCount;0<=s?l<s:l>s;l=0<=s?++l:--l)a={tag:r.readString(4),checksum:r.readInt(),offset:r.readInt(),length:r.readInt()},this.tables[a.tag]=a}return e.prototype.encode=function(r){var a,l,s,c,h,f,g,b,w,S,p,M,I;for(I in p=Object.keys(r).length,f=Math.log(2),w=16*Math.floor(Math.log(p)/f),c=Math.floor(w/f),b=16*p-w,(l=new Tn).writeInt(this.scalarType),l.writeShort(p),l.writeShort(w),l.writeShort(c),l.writeShort(b),s=16*p,g=l.pos+s,h=null,M=[],r)for(S=r[I],l.writeString(I),l.writeInt(n(S)),l.writeInt(g),l.writeInt(S.length),M=M.concat(S),I==="head"&&(h=g),g+=S.length;g%4;)M.push(0),g++;return l.write(M),a=2981146554-n(l.data),l.pos=h+8,l.writeUInt32(a),l.data},n=function(r){var a,l,s,c;for(r=au.call(r);r.length%4;)r.push(0);for(s=new Tn(r),l=0,a=0,c=r.length;a<c;a=a+=4)l+=s.readUInt32();return **********&l},e}(),Bc={}.hasOwnProperty,an=function(n,e){for(var r in e)Bc.call(e,r)&&(n[r]=e[r]);function a(){this.constructor=n}return a.prototype=e.prototype,n.prototype=new a,n.__super__=e.prototype,n};Wr=function(){function n(e){var r;this.file=e,r=this.file.directory.tables[this.tag],this.exists=!!r,r&&(this.offset=r.offset,this.length=r.length,this.parse(this.file.contents))}return n.prototype.parse=function(){},n.prototype.encode=function(){},n.prototype.raw=function(){return this.exists?(this.file.contents.pos=this.offset,this.file.contents.read(this.length)):null},n}();var Ec=function(n){function e(){return e.__super__.constructor.apply(this,arguments)}return an(e,Wr),e.prototype.tag="head",e.prototype.parse=function(r){return r.pos=this.offset,this.version=r.readInt(),this.revision=r.readInt(),this.checkSumAdjustment=r.readInt(),this.magicNumber=r.readInt(),this.flags=r.readShort(),this.unitsPerEm=r.readShort(),this.created=r.readLongLong(),this.modified=r.readLongLong(),this.xMin=r.readShort(),this.yMin=r.readShort(),this.xMax=r.readShort(),this.yMax=r.readShort(),this.macStyle=r.readShort(),this.lowestRecPPEM=r.readShort(),this.fontDirectionHint=r.readShort(),this.indexToLocFormat=r.readShort(),this.glyphDataFormat=r.readShort()},e.prototype.encode=function(r){var a;return(a=new Tn).writeInt(this.version),a.writeInt(this.revision),a.writeInt(this.checkSumAdjustment),a.writeInt(this.magicNumber),a.writeShort(this.flags),a.writeShort(this.unitsPerEm),a.writeLongLong(this.created),a.writeLongLong(this.modified),a.writeShort(this.xMin),a.writeShort(this.yMin),a.writeShort(this.xMax),a.writeShort(this.yMax),a.writeShort(this.macStyle),a.writeShort(this.lowestRecPPEM),a.writeShort(this.fontDirectionHint),a.writeShort(r),a.writeShort(this.glyphDataFormat),a.data},e}(),ql=function(){function n(e,r){var a,l,s,c,h,f,g,b,w,S,p,M,I,q,k,E,J;switch(this.platformID=e.readUInt16(),this.encodingID=e.readShort(),this.offset=r+e.readInt(),w=e.pos,e.pos=this.offset,this.format=e.readUInt16(),this.length=e.readUInt16(),this.language=e.readUInt16(),this.isUnicode=this.platformID===3&&this.encodingID===1&&this.format===4||this.platformID===0&&this.format===4,this.codeMap={},this.format){case 0:for(f=0;f<256;++f)this.codeMap[f]=e.readByte();break;case 4:for(p=e.readUInt16(),S=p/2,e.pos+=6,s=function(){var ot,ut;for(ut=[],f=ot=0;0<=S?ot<S:ot>S;f=0<=S?++ot:--ot)ut.push(e.readUInt16());return ut}(),e.pos+=2,I=function(){var ot,ut;for(ut=[],f=ot=0;0<=S?ot<S:ot>S;f=0<=S?++ot:--ot)ut.push(e.readUInt16());return ut}(),g=function(){var ot,ut;for(ut=[],f=ot=0;0<=S?ot<S:ot>S;f=0<=S?++ot:--ot)ut.push(e.readUInt16());return ut}(),b=function(){var ot,ut;for(ut=[],f=ot=0;0<=S?ot<S:ot>S;f=0<=S?++ot:--ot)ut.push(e.readUInt16());return ut}(),l=(this.length-e.pos+this.offset)/2,h=function(){var ot,ut;for(ut=[],f=ot=0;0<=l?ot<l:ot>l;f=0<=l?++ot:--ot)ut.push(e.readUInt16());return ut}(),f=k=0,J=s.length;k<J;f=++k)for(q=s[f],a=E=M=I[f];M<=q?E<=q:E>=q;a=M<=q?++E:--E)b[f]===0?c=a+g[f]:(c=h[b[f]/2+(a-M)-(S-f)]||0)!==0&&(c+=g[f]),this.codeMap[a]=65535&c}e.pos=w}return n.encode=function(e,r){var a,l,s,c,h,f,g,b,w,S,p,M,I,q,k,E,J,ot,ut,yt,tt,U,nt,pt,L,A,B,D,et,rt,ct,X,at,ft,Ft,N,C,O,R,G,$,it,st,Nt,Lt,It;switch(D=new Tn,c=Object.keys(e).sort(function(St,Ut){return St-Ut}),r){case"macroman":for(I=0,q=function(){var St=[];for(M=0;M<256;++M)St.push(0);return St}(),E={0:0},s={},et=0,at=c.length;et<at;et++)E[st=e[l=c[et]]]==null&&(E[st]=++I),s[l]={old:e[l],new:E[e[l]]},q[l]=E[e[l]];return D.writeUInt16(1),D.writeUInt16(0),D.writeUInt32(12),D.writeUInt16(0),D.writeUInt16(262),D.writeUInt16(0),D.write(q),{charMap:s,subtable:D.data,maxGlyphID:I+1};case"unicode":for(A=[],w=[],J=0,E={},a={},k=g=null,rt=0,ft=c.length;rt<ft;rt++)E[ut=e[l=c[rt]]]==null&&(E[ut]=++J),a[l]={old:ut,new:E[ut]},h=E[ut]-l,k!=null&&h===g||(k&&w.push(k),A.push(l),g=h),k=l;for(k&&w.push(k),w.push(65535),A.push(65535),pt=2*(nt=A.length),U=2*Math.pow(Math.log(nt)/Math.LN2,2),S=Math.log(U/2)/Math.LN2,tt=2*nt-U,f=[],yt=[],p=[],M=ct=0,Ft=A.length;ct<Ft;M=++ct){if(L=A[M],b=w[M],L===65535){f.push(0),yt.push(0);break}if(L-(B=a[L].new)>=32768)for(f.push(0),yt.push(2*(p.length+nt-M)),l=X=L;L<=b?X<=b:X>=b;l=L<=b?++X:--X)p.push(a[l].new);else f.push(B-L),yt.push(0)}for(D.writeUInt16(3),D.writeUInt16(1),D.writeUInt32(12),D.writeUInt16(4),D.writeUInt16(16+8*nt+2*p.length),D.writeUInt16(0),D.writeUInt16(pt),D.writeUInt16(U),D.writeUInt16(S),D.writeUInt16(tt),$=0,N=w.length;$<N;$++)l=w[$],D.writeUInt16(l);for(D.writeUInt16(0),it=0,C=A.length;it<C;it++)l=A[it],D.writeUInt16(l);for(Nt=0,O=f.length;Nt<O;Nt++)h=f[Nt],D.writeUInt16(h);for(Lt=0,R=yt.length;Lt<R;Lt++)ot=yt[Lt],D.writeUInt16(ot);for(It=0,G=p.length;It<G;It++)I=p[It],D.writeUInt16(I);return{charMap:a,subtable:D.data,maxGlyphID:J+1}}},n}(),iu=function(n){function e(){return e.__super__.constructor.apply(this,arguments)}return an(e,Wr),e.prototype.tag="cmap",e.prototype.parse=function(r){var a,l,s;for(r.pos=this.offset,this.version=r.readUInt16(),s=r.readUInt16(),this.tables=[],this.unicode=null,l=0;0<=s?l<s:l>s;l=0<=s?++l:--l)a=new ql(r,this.offset),this.tables.push(a),a.isUnicode&&this.unicode==null&&(this.unicode=a);return!0},e.encode=function(r,a){var l,s;return a==null&&(a="macroman"),l=ql.encode(r,a),(s=new Tn).writeUInt16(0),s.writeUInt16(1),l.table=s.data.concat(l.subtable),l},e}(),Tc=function(n){function e(){return e.__super__.constructor.apply(this,arguments)}return an(e,Wr),e.prototype.tag="hhea",e.prototype.parse=function(r){return r.pos=this.offset,this.version=r.readInt(),this.ascender=r.readShort(),this.decender=r.readShort(),this.lineGap=r.readShort(),this.advanceWidthMax=r.readShort(),this.minLeftSideBearing=r.readShort(),this.minRightSideBearing=r.readShort(),this.xMaxExtent=r.readShort(),this.caretSlopeRise=r.readShort(),this.caretSlopeRun=r.readShort(),this.caretOffset=r.readShort(),r.pos+=8,this.metricDataFormat=r.readShort(),this.numberOfMetrics=r.readUInt16()},e}(),qc=function(n){function e(){return e.__super__.constructor.apply(this,arguments)}return an(e,Wr),e.prototype.tag="OS/2",e.prototype.parse=function(r){if(r.pos=this.offset,this.version=r.readUInt16(),this.averageCharWidth=r.readShort(),this.weightClass=r.readUInt16(),this.widthClass=r.readUInt16(),this.type=r.readShort(),this.ySubscriptXSize=r.readShort(),this.ySubscriptYSize=r.readShort(),this.ySubscriptXOffset=r.readShort(),this.ySubscriptYOffset=r.readShort(),this.ySuperscriptXSize=r.readShort(),this.ySuperscriptYSize=r.readShort(),this.ySuperscriptXOffset=r.readShort(),this.ySuperscriptYOffset=r.readShort(),this.yStrikeoutSize=r.readShort(),this.yStrikeoutPosition=r.readShort(),this.familyClass=r.readShort(),this.panose=function(){var a,l;for(l=[],a=0;a<10;++a)l.push(r.readByte());return l}(),this.charRange=function(){var a,l;for(l=[],a=0;a<4;++a)l.push(r.readInt());return l}(),this.vendorID=r.readString(4),this.selection=r.readShort(),this.firstCharIndex=r.readShort(),this.lastCharIndex=r.readShort(),this.version>0&&(this.ascent=r.readShort(),this.descent=r.readShort(),this.lineGap=r.readShort(),this.winAscent=r.readShort(),this.winDescent=r.readShort(),this.codePageRange=function(){var a,l;for(l=[],a=0;a<2;a=++a)l.push(r.readInt());return l}(),this.version>1))return this.xHeight=r.readShort(),this.capHeight=r.readShort(),this.defaultChar=r.readShort(),this.breakChar=r.readShort(),this.maxContext=r.readShort()},e}(),Dc=function(n){function e(){return e.__super__.constructor.apply(this,arguments)}return an(e,Wr),e.prototype.tag="post",e.prototype.parse=function(r){var a,l,s;switch(r.pos=this.offset,this.format=r.readInt(),this.italicAngle=r.readInt(),this.underlinePosition=r.readShort(),this.underlineThickness=r.readShort(),this.isFixedPitch=r.readInt(),this.minMemType42=r.readInt(),this.maxMemType42=r.readInt(),this.minMemType1=r.readInt(),this.maxMemType1=r.readInt(),this.format){case 65536:break;case 131072:var c;for(l=r.readUInt16(),this.glyphNameIndex=[],c=0;0<=l?c<l:c>l;c=0<=l?++c:--c)this.glyphNameIndex.push(r.readUInt16());for(this.names=[],s=[];r.pos<this.offset+this.length;)a=r.readByte(),s.push(this.names.push(r.readString(a)));return s;case 151552:return l=r.readUInt16(),this.offsets=r.read(l);case 196608:break;case 262144:return this.map=(function(){var h,f,g;for(g=[],c=h=0,f=this.file.maxp.numGlyphs;0<=f?h<f:h>f;c=0<=f?++h:--h)g.push(r.readUInt32());return g}).call(this)}},e}(),Rc=function(n,e){this.raw=n,this.length=n.length,this.platformID=e.platformID,this.encodingID=e.encodingID,this.languageID=e.languageID},zc=function(n){function e(){return e.__super__.constructor.apply(this,arguments)}return an(e,Wr),e.prototype.tag="name",e.prototype.parse=function(r){var a,l,s,c,h,f,g,b,w,S,p;for(r.pos=this.offset,r.readShort(),a=r.readShort(),f=r.readShort(),l=[],c=0;0<=a?c<a:c>a;c=0<=a?++c:--c)l.push({platformID:r.readShort(),encodingID:r.readShort(),languageID:r.readShort(),nameID:r.readShort(),length:r.readShort(),offset:this.offset+f+r.readShort()});for(g={},c=w=0,S=l.length;w<S;c=++w)s=l[c],r.pos=s.offset,b=r.readString(s.length),h=new Rc(b,s),g[p=s.nameID]==null&&(g[p]=[]),g[s.nameID].push(h);this.strings=g,this.copyright=g[0],this.fontFamily=g[1],this.fontSubfamily=g[2],this.uniqueSubfamily=g[3],this.fontName=g[4],this.version=g[5];try{this.postscriptName=g[6][0].raw.replace(/[\x00-\x19\x80-\xff]/g,"")}catch{this.postscriptName=g[4][0].raw.replace(/[\x00-\x19\x80-\xff]/g,"")}return this.trademark=g[7],this.manufacturer=g[8],this.designer=g[9],this.description=g[10],this.vendorUrl=g[11],this.designerUrl=g[12],this.license=g[13],this.licenseUrl=g[14],this.preferredFamily=g[15],this.preferredSubfamily=g[17],this.compatibleFull=g[18],this.sampleText=g[19]},e}(),Uc=function(n){function e(){return e.__super__.constructor.apply(this,arguments)}return an(e,Wr),e.prototype.tag="maxp",e.prototype.parse=function(r){return r.pos=this.offset,this.version=r.readInt(),this.numGlyphs=r.readUInt16(),this.maxPoints=r.readUInt16(),this.maxContours=r.readUInt16(),this.maxCompositePoints=r.readUInt16(),this.maxComponentContours=r.readUInt16(),this.maxZones=r.readUInt16(),this.maxTwilightPoints=r.readUInt16(),this.maxStorage=r.readUInt16(),this.maxFunctionDefs=r.readUInt16(),this.maxInstructionDefs=r.readUInt16(),this.maxStackElements=r.readUInt16(),this.maxSizeOfInstructions=r.readUInt16(),this.maxComponentElements=r.readUInt16(),this.maxComponentDepth=r.readUInt16()},e}(),Hc=function(n){function e(){return e.__super__.constructor.apply(this,arguments)}return an(e,Wr),e.prototype.tag="hmtx",e.prototype.parse=function(r){var a,l,s,c,h,f,g;for(r.pos=this.offset,this.metrics=[],a=0,f=this.file.hhea.numberOfMetrics;0<=f?a<f:a>f;a=0<=f?++a:--a)this.metrics.push({advance:r.readUInt16(),lsb:r.readInt16()});for(s=this.file.maxp.numGlyphs-this.file.hhea.numberOfMetrics,this.leftSideBearings=function(){var b,w;for(w=[],a=b=0;0<=s?b<s:b>s;a=0<=s?++b:--b)w.push(r.readInt16());return w}(),this.widths=(function(){var b,w,S,p;for(p=[],b=0,w=(S=this.metrics).length;b<w;b++)c=S[b],p.push(c.advance);return p}).call(this),l=this.widths[this.widths.length-1],g=[],a=h=0;0<=s?h<s:h>s;a=0<=s?++h:--h)g.push(this.widths.push(l));return g},e.prototype.forGlyph=function(r){return r in this.metrics?this.metrics[r]:{advance:this.metrics[this.metrics.length-1].advance,lsb:this.leftSideBearings[r-this.metrics.length]}},e}(),au=[].slice,Wc=function(n){function e(){return e.__super__.constructor.apply(this,arguments)}return an(e,Wr),e.prototype.tag="glyf",e.prototype.parse=function(){return this.cache={}},e.prototype.glyphFor=function(r){var a,l,s,c,h,f,g,b,w,S;return r in this.cache?this.cache[r]:(c=this.file.loca,a=this.file.contents,l=c.indexOf(r),(s=c.lengthOf(r))===0?this.cache[r]=null:(a.pos=this.offset+l,h=(f=new Tn(a.read(s))).readShort(),b=f.readShort(),S=f.readShort(),g=f.readShort(),w=f.readShort(),this.cache[r]=h===-1?new Gc(f,b,S,g,w):new Vc(f,h,b,S,g,w),this.cache[r]))},e.prototype.encode=function(r,a,l){var s,c,h,f,g;for(h=[],c=[],f=0,g=a.length;f<g;f++)s=r[a[f]],c.push(h.length),s&&(h=h.concat(s.encode(l)));return c.push(h.length),{table:h,offsets:c}},e}(),Vc=function(){function n(e,r,a,l,s,c){this.raw=e,this.numberOfContours=r,this.xMin=a,this.yMin=l,this.xMax=s,this.yMax=c,this.compound=!1}return n.prototype.encode=function(){return this.raw.data},n}(),Gc=function(){function n(e,r,a,l,s){var c,h;for(this.raw=e,this.xMin=r,this.yMin=a,this.xMax=l,this.yMax=s,this.compound=!0,this.glyphIDs=[],this.glyphOffsets=[],c=this.raw;h=c.readShort(),this.glyphOffsets.push(c.pos),this.glyphIDs.push(c.readUInt16()),32&h;)c.pos+=1&h?4:2,128&h?c.pos+=8:64&h?c.pos+=4:8&h&&(c.pos+=2)}return n.prototype.encode=function(){var e,r,a;for(r=new Tn(au.call(this.raw.data)),e=0,a=this.glyphIDs.length;e<a;++e)r.pos=this.glyphOffsets[e];return r.data},n}(),Yc=function(n){function e(){return e.__super__.constructor.apply(this,arguments)}return an(e,Wr),e.prototype.tag="loca",e.prototype.parse=function(r){var a,l;return r.pos=this.offset,a=this.file.head.indexToLocFormat,this.offsets=a===0?(function(){var s,c;for(c=[],l=0,s=this.length;l<s;l+=2)c.push(2*r.readUInt16());return c}).call(this):(function(){var s,c;for(c=[],l=0,s=this.length;l<s;l+=4)c.push(r.readUInt32());return c}).call(this)},e.prototype.indexOf=function(r){return this.offsets[r]},e.prototype.lengthOf=function(r){return this.offsets[r+1]-this.offsets[r]},e.prototype.encode=function(r,a){for(var l=new Uint32Array(this.offsets.length),s=0,c=0,h=0;h<l.length;++h)if(l[h]=s,c<a.length&&a[c]==h){++c,l[h]=s;var f=this.offsets[h],g=this.offsets[h+1]-f;g>0&&(s+=g)}for(var b=new Array(4*l.length),w=0;w<l.length;++w)b[4*w+3]=255&l[w],b[4*w+2]=(65280&l[w])>>8,b[4*w+1]=(16711680&l[w])>>16,b[4*w]=(**********&l[w])>>24;return b},e}(),Jc=function(){function n(e){this.font=e,this.subset={},this.unicodes={},this.next=33}return n.prototype.generateCmap=function(){var e,r,a,l,s;for(r in l=this.font.cmap.tables[0].codeMap,e={},s=this.subset)a=s[r],e[r]=l[a];return e},n.prototype.glyphsFor=function(e){var r,a,l,s,c,h,f;for(l={},c=0,h=e.length;c<h;c++)l[s=e[c]]=this.font.glyf.glyphFor(s);for(s in r=[],l)(a=l[s])!=null&&a.compound&&r.push.apply(r,a.glyphIDs);if(r.length>0)for(s in f=this.glyphsFor(r))a=f[s],l[s]=a;return l},n.prototype.encode=function(e,r){var a,l,s,c,h,f,g,b,w,S,p,M,I,q,k;for(l in a=iu.encode(this.generateCmap(),"unicode"),c=this.glyphsFor(e),p={0:0},k=a.charMap)p[(f=k[l]).old]=f.new;for(M in S=a.maxGlyphID,c)M in p||(p[M]=S++);return b=function(E){var J,ot;for(J in ot={},E)ot[E[J]]=J;return ot}(p),w=Object.keys(b).sort(function(E,J){return E-J}),I=function(){var E,J,ot;for(ot=[],E=0,J=w.length;E<J;E++)h=w[E],ot.push(b[h]);return ot}(),s=this.font.glyf.encode(c,I,p),g=this.font.loca.encode(s.offsets,I),q={cmap:this.font.cmap.raw(),glyf:s.table,loca:g,hmtx:this.font.hmtx.raw(),hhea:this.font.hhea.raw(),maxp:this.font.maxp.raw(),post:this.font.post.raw(),name:this.font.name.raw(),head:this.font.head.encode(r)},this.font.os2.exists&&(q["OS/2"]=this.font.os2.raw()),this.font.directory.encode(q)},n}();zt.API.PDFObject=function(){var n;function e(){}return n=function(r,a){return(Array(a+1).join("0")+r).slice(-a)},e.convert=function(r){var a,l,s,c;if(Array.isArray(r))return"["+function(){var h,f,g;for(g=[],h=0,f=r.length;h<f;h++)a=r[h],g.push(e.convert(a));return g}().join(" ")+"]";if(typeof r=="string")return"/"+r;if(r!=null&&r.isString)return"("+r+")";if(r instanceof Date)return"(D:"+n(r.getUTCFullYear(),4)+n(r.getUTCMonth(),2)+n(r.getUTCDate(),2)+n(r.getUTCHours(),2)+n(r.getUTCMinutes(),2)+n(r.getUTCSeconds(),2)+"Z)";if({}.toString.call(r)==="[object Object]"){for(l in s=["<<"],r)c=r[l],s.push("/"+l+" "+e.convert(c));return s.push(">>"),s.join(`
`)}return""+r},e}();const Xc={name:"InsightsReports",components:{PageHeader:Ju,HeroIcon:Ku},setup(){const{showToast:n}=Zu(),e=Fi(!1),r=Fi([]),a=Fi(null),l=Fi(""),s=Fi(""),c=Fi(""),h=()=>{e.value=!0;try{const L=localStorage.getItem("ceo-chat-messages");if(L){const A=JSON.parse(L),B=f(A);r.value=B}else r.value=S()}catch(L){console.error("Error loading insights:",L),r.value=S()}finally{e.value=!1}},f=L=>{const A=[];let B=null,D=1;for(const et of L)et.type==="user"?B={id:D++,title:g(et.content),type:b(et.content),category:"Strategic Analysis",priority:"medium",timestamp:et.timestamp,content:"",summary:"",query_count:1,session_duration:"2-3m",tags:w(et.content)}:et.type==="assistant"&&B&&(B.content=et.content,B.summary=et.content.substring(0,200)+"...",A.push(B),B=null);return A.reverse()},g=L=>L.length>50?L.substring(0,50)+"...":L,b=L=>{const A=L.toLowerCase();return A.includes("market")||A.includes("competitor")||A.includes("mercato")||A.includes("concorrenti")?"market_analysis":A.includes("team")||A.includes("hire")||A.includes("assumere")||A.includes("talenti")?"talent_strategy":A.includes("financial")||A.includes("revenue")||A.includes("finanziario")||A.includes("ricavi")?"financial_analysis":A.includes("technology")||A.includes("innovation")||A.includes("tecnologia")||A.includes("innovazione")?"technology_innovation":"strategic_planning"},w=L=>{const A=[],B=L.toLowerCase();return(B.includes("market")||B.includes("mercato"))&&A.push("Ricerca di Mercato"),(B.includes("team")||B.includes("hire")||B.includes("assumere")||B.includes("talenti"))&&A.push("Risorse Umane"),(B.includes("revenue")||B.includes("financial")||B.includes("ricavi")||B.includes("finanziario"))&&A.push("Analisi Finanziaria"),(B.includes("technology")||B.includes("tecnologia"))&&A.push("Tecnologia"),(B.includes("strategy")||B.includes("strategia"))&&A.push("Strategia"),(B.includes("growth")||B.includes("crescita"))&&A.push("Crescita"),A.length>0?A:["Strategia Generale"]},S=()=>[{id:1,title:"Analisi di Mercato per Q1 2024",type:"market_analysis",category:"Intelligence di Mercato",priority:"high",timestamp:new Date(Date.now()-864e5),content:`📊 **MARKET ANALYSIS**

Based on current market intelligence for Software/Technology:

**Key Trends:**
• Digital transformation accelerating across all sectors
• Increased demand for AI/automation solutions
• Remote-first business models becoming standard
• Sustainability becoming a competitive advantage

**Competitive Landscape:**
• Market consolidation creating opportunities for specialized players
• New entrants focusing on niche markets
• Price competition increasing in commoditized services

**Recommendations:**
1. Focus on differentiation through specialized expertise
2. Invest in technology that enhances client value
3. Consider strategic partnerships for market expansion
4. Monitor emerging competitors in adjacent markets`,summary:"Comprehensive market analysis showing accelerating digital transformation and opportunities for specialized technology companies...",query_count:3,session_duration:"5m",tags:["Market Research","Technology","Strategy"]},{id:2,title:"Analisi Strategia di Espansione del Team",type:"talent_strategy",category:"Risorse Umane",priority:"medium",timestamp:new Date(Date.now()-1728e5),content:`👥 **TALENT STRATEGY ANALYSIS**

Based on your current business metrics:

**Current Situation:**
• Team utilization: 85-90% (optimal range)
• Project pipeline: 3-4 months visibility
• Average hiring timeline: 6-8 weeks
• Employee retention: Above industry average

**Market Conditions:**
• Talent shortage in key technical skills
• Remote work increasing candidate pool
• Salary inflation 8-12% annually
• Benefits becoming more important than salary

**Recommendation:**
**EXPAND TEAM** - Your metrics indicate sustainable growth

**Action Plan:**
1. **Immediate** (next 30 days):
   - Post job openings for 2 senior positions
   - Activate employee referral program
   - Consider contract-to-hire for urgent needs`,summary:"Analysis recommends team expansion based on current utilization rates and strong project pipeline...",query_count:2,session_duration:"3m",tags:["Human Resources","Growth","Strategy"]}],p=Gu(()=>{let L=r.value;if(l.value){const A=l.value.toLowerCase();L=L.filter(B=>B.title.toLowerCase().includes(A)||B.content.toLowerCase().includes(A)||B.category&&B.category.toLowerCase().includes(A)||B.tags&&B.tags.some(D=>D.toLowerCase().includes(A)))}return s.value&&(L=L.filter(A=>A.insight_type===s.value||A.type===s.value)),c.value&&(L=L.filter(A=>A.priority===c.value)),L}),M=L=>({market_analysis:"chart-bar",talent_strategy:"users",financial_analysis:"banknotes",technology_innovation:"cpu-chip",strategic_planning:"light-bulb"})[L]||"document-text",I=L=>({market_analysis:"text-blue-600",talent_strategy:"text-purple-600",financial_analysis:"text-green-600",technology_innovation:"text-orange-600",strategic_planning:"text-yellow-600"})[L]||"text-gray-600",q=L=>{const A={critical:"bg-red-100 text-red-800",high:"bg-orange-100 text-orange-800",medium:"bg-yellow-100 text-yellow-800",low:"bg-gray-100 text-gray-800"};return A[L]||A.medium},k=L=>L.replace(/\*\*(.*?)\*\*/g,"$1").replace(/\*(.*?)\*/g,"$1").replace(/#{1,6}\s+(.*)/g,"$1").replace(/```[\s\S]*?```/g,"[Code Block]").replace(/`([^`]+)`/g,"$1").substring(0,150)+"...",E=L=>(hs.setOptions({breaks:!0,gfm:!0}),hs(L)),J=L=>{let B=L;if(L.length>200){B=L.substring(0,200);const D=B.lastIndexOf(" ");D>200*.8&&(B=B.substring(0,D)),B+="..."}return hs.setOptions({breaks:!0,gfm:!0}),hs(B)},ot=L=>new Date(L).toLocaleDateString("it-IT",{year:"numeric",month:"long",day:"numeric"}),ut=L=>new Date(L).toLocaleTimeString("it-IT",{hour:"2-digit",minute:"2-digit"}),yt=L=>{a.value=L},tt=()=>{a.value=null},U=L=>{try{const A=new zt,B=20,D=7,et=A.internal.pageSize.width,rt=A.internal.pageSize.height,ct=et-B*2;let X=B;const at=(N,C=12,O="normal",R=[0,0,0])=>{A.setFontSize(C),A.setFont("helvetica",O),A.setTextColor(R[0],R[1],R[2]);const G=A.splitTextToSize(N,ct);for(let $=0;$<G.length;$++)X+D>rt-B&&(A.addPage(),X=B),A.text(G[$],B,X),X+=D;X+=3},ft=N=>{const C=N.split(`
`);for(const O of C){if(O.trim()===""){X+=3;continue}if(O.startsWith("### "))at(O.replace("### ",""),14,"bold",[0,0,0]);else if(O.startsWith("## "))at(O.replace("## ",""),16,"bold",[0,0,0]);else if(O.startsWith("# "))at(O.replace("# ",""),18,"bold",[0,0,0]);else if(O.includes("**")){const R=O.replace(/\*\*(.*?)\*\*/g,"$1");at(R,12,"bold",[0,0,0])}else if(O.startsWith("• ")||O.startsWith("- ")||/^\d+\./.test(O))at(O,11,"normal",[0,0,0]);else{const R=O.replace(/\*\*(.*?)\*\*/g,"$1").replace(/\*(.*?)\*/g,"$1").replace(/`([^`]+)`/g,"$1");R.trim()&&at(R,12,"normal",[0,0,0])}}};at("STRATEGIC INSIGHT REPORT",20,"bold",[52,58,64]),X+=5,at(L.title,16,"bold",[0,0,0]),X+=3,at(`Category: ${L.category}`,11,"normal",[107,114,128]),at(`Priority: ${L.priority.toUpperCase()}`,11,"normal",[107,114,128]),at(`Date: ${ot(L.timestamp)}`,11,"normal",[107,114,128]),L.tags&&L.tags.length>0&&at(`Tags: ${L.tags.join(", ")}`,11,"normal",[107,114,128]),X+=8,A.setDrawColor(229,231,235),A.line(B,X,et-B,X),X+=10,at("CONTENT",14,"bold",[52,58,64]),X+=3,ft(L.content),X+=10,A.setDrawColor(229,231,235),A.line(B,X,et-B,X),X+=8,at("Generated by DatPortal AI Assistant",10,"italic",[107,114,128]),at(`Export Date: ${new Date().toLocaleDateString("it-IT")}`,10,"italic",[107,114,128]);const Ft=`insight-${L.id}-${L.title.replace(/[^a-z0-9]/gi,"_").toLowerCase()}.pdf`;A.save(Ft),n("PDF esportato con successo","success")}catch(A){console.error("Error exporting PDF:",A),n("Errore nell'esportazione PDF","error")}},nt=L=>{if(confirm(`Sei sicuro di voler eliminare "${L.title}"? Questa azione non può essere annullata.`)){const A=r.value.findIndex(B=>B.id===L.id);if(A!==-1){r.value.splice(A,1);try{const B=localStorage.getItem("ceo-chat-messages");if(B){const et=JSON.parse(B).filter(rt=>!(rt.timestamp===L.timestamp||rt.content&&rt.content.includes(L.title)));localStorage.setItem("ceo-chat-messages",JSON.stringify(et))}}catch(B){console.error("Error updating localStorage:",B)}a.value&&a.value.id===L.id&&tt(),n("Insight eliminato con successo","success")}}},pt=()=>{l.value="",s.value="",c.value=""};return Yu(()=>{h()}),{loading:e,insights:r,filteredInsights:p,selectedInsight:a,searchQuery:l,selectedType:s,selectedPriority:c,getInsightIcon:M,getInsightIconColor:I,getPriorityClass:q,getContentPreview:k,renderMarkdown:E,renderMarkdownPreview:J,formatDate:ot,formatTime:ut,viewInsight:yt,closeInsightModal:tt,exportInsight:U,deleteInsight:nt,clearFilters:pt}}},Kc={class:"max-w-6xl mx-auto p-6"},Zc={class:"bg-white rounded-lg shadow-sm p-4 mb-6"},Qc={class:"flex flex-wrap gap-4"},$c={class:"space-y-4"},th={key:0,class:"bg-white rounded-lg shadow-sm p-8 text-center"},eh={key:1,class:"space-y-4"},rh={class:"p-6"},nh={class:"flex items-start justify-between mb-4"},ih={class:"flex-1"},ah={class:"flex items-center gap-3 mb-2"},sh={class:"text-lg font-semibold text-gray-900"},oh={class:"text-sm text-gray-600"},lh={class:"text-right"},uh={class:"text-sm text-gray-500"},ch={class:"text-xs text-gray-400"},hh={class:"mb-4"},fh=["innerHTML"],dh={key:0,class:"flex flex-wrap gap-2 mb-4"},ph={class:"flex items-center justify-between"},gh={class:"flex items-center gap-2"},mh=["onClick"],vh=["onClick"],bh=["onClick"],yh={class:"flex items-center gap-2 text-xs text-gray-500"},wh={key:2,class:"bg-white rounded-lg shadow-sm p-8 text-center"},xh={class:"flex items-center justify-between p-6 border-b border-gray-200 flex-shrink-0"},Lh={class:"flex items-center gap-3"},Nh={class:"text-xl font-semibold text-gray-900"},Ah={class:"text-sm text-gray-600"},_h={class:"p-6 overflow-y-auto flex-1 min-h-0"},Sh={class:"prose max-w-none"},Ph=["innerHTML"],kh={class:"flex items-center justify-between p-6 border-t border-gray-200 flex-shrink-0"},Ih={class:"flex items-center gap-4"},Fh={class:"text-sm text-gray-500"},Ch={class:"flex gap-2"};function jh(n,e,r,a,l,s){const c=go("PageHeader"),h=go("HeroIcon"),f=go("router-link");return Dr(),qr("div",Kc,[jn(c,{title:"Insights e Report",subtitle:"Archivio insights strategici e reportistica"}),Wt("div",Zc,[Wt("div",Qc,[mo(Wt("select",{"onUpdate:modelValue":e[0]||(e[0]=g=>a.selectedType=g),class:"border border-gray-300 rounded-lg px-3 py-2"},e[10]||(e[10]=[gl('<option value="" data-v-e433f6e0>Tutti i Tipi</option><option value="market_analysis" data-v-e433f6e0>Analisi di Mercato</option><option value="talent_strategy" data-v-e433f6e0>Strategia Talenti</option><option value="financial_analysis" data-v-e433f6e0>Analisi Finanziaria</option><option value="technology_innovation" data-v-e433f6e0>Innovazione Tecnologica</option><option value="strategic_planning" data-v-e433f6e0>Pianificazione Strategica</option>',6)]),512),[[pl,a.selectedType]]),mo(Wt("select",{"onUpdate:modelValue":e[1]||(e[1]=g=>a.selectedPriority=g),class:"border border-gray-300 rounded-lg px-3 py-2"},e[11]||(e[11]=[gl('<option value="" data-v-e433f6e0>Tutte le Priorità</option><option value="critical" data-v-e433f6e0>Critica</option><option value="high" data-v-e433f6e0>Alta</option><option value="medium" data-v-e433f6e0>Media</option><option value="low" data-v-e433f6e0>Bassa</option>',5)]),512),[[pl,a.selectedPriority]]),mo(Wt("input",{"onUpdate:modelValue":e[2]||(e[2]=g=>a.searchQuery=g),type:"text",placeholder:"Cerca insights...",class:"border border-gray-300 rounded-lg px-3 py-2 flex-1 min-w-64"},null,512),[[Hu,a.searchQuery]]),a.searchQuery||a.selectedType||a.selectedPriority?(Dr(),qr("button",{key:0,onClick:e[3]||(e[3]=(...g)=>a.clearFilters&&a.clearFilters(...g)),class:"btn-secondary"},[jn(h,{name:"x-mark",size:"sm"}),e[12]||(e[12]=ml(" Cancella "))])):po("",!0)])]),Wt("div",$c,[a.loading?(Dr(),qr("div",th,[jn(h,{name:"arrow-path",size:"lg",class:"mx-auto text-gray-400 animate-spin mb-3"}),e[13]||(e[13]=Wt("p",{class:"text-gray-500"},"Caricamento insights...",-1))])):a.filteredInsights.length>0?(Dr(),qr("div",eh,[(Dr(!0),qr(vl,null,bl(a.filteredInsights,g=>(Dr(),qr("div",{key:g.id,class:"bg-white rounded-lg shadow-sm border border-gray-200 hover:border-gray-300 transition-colors"},[Wt("div",rh,[Wt("div",nh,[Wt("div",ih,[Wt("div",ah,[jn(h,{name:a.getInsightIcon(g.insight_type||g.type),size:"sm",class:cs(a.getInsightIconColor(g.insight_type||g.type))},null,8,["name","class"]),Wt("h3",sh,hr(g.title),1),Wt("span",{class:cs(["px-2 py-1 text-xs rounded-full",a.getPriorityClass(g.priority)])},hr(g.priority.toUpperCase()),3)]),Wt("p",oh,hr(g.category),1)]),Wt("div",lh,[Wt("p",uh,hr(a.formatDate(g.timestamp)),1),Wt("p",ch,hr(a.formatTime(g.timestamp)),1)])]),Wt("div",hh,[Wt("div",{class:"text-gray-700 line-clamp-3 markdown-preview",innerHTML:a.renderMarkdownPreview(g.summary||g.content)},null,8,fh)]),g.tags&&g.tags.length>0?(Dr(),qr("div",dh,[(Dr(!0),qr(vl,null,bl(g.tags,b=>(Dr(),qr("span",{key:b,class:"inline-block bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded"},hr(b),1))),128))])):po("",!0),Wt("div",ph,[Wt("div",gh,[Wt("button",{onClick:b=>a.viewInsight(g),class:"text-blue-600 hover:text-blue-700 text-sm font-medium"}," Vedi Dettagli ",8,mh),Wt("button",{onClick:b=>a.exportInsight(g),class:"text-gray-600 hover:text-gray-700 text-sm"}," Esporta ",8,vh),Wt("button",{onClick:b=>a.deleteInsight(g),class:"text-red-600 hover:text-red-700 text-sm",title:"Elimina insight"}," Elimina ",8,bh)]),Wt("div",yh,[Wt("span",null,hr(g.query_count||1)+" query",1),e[14]||(e[14]=Wt("span",null,"•",-1)),Wt("span",null,hr(g.session_duration||"2m"),1)])])])]))),128))])):(Dr(),qr("div",wh,[jn(h,{name:"document-text",size:"lg",class:"mx-auto text-gray-400 mb-3"}),e[16]||(e[16]=Wt("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"Nessun Insight Ancora",-1)),e[17]||(e[17]=Wt("p",{class:"text-gray-600 mb-4"}," Gli insights strategici appariranno qui dopo aver eseguito sessioni di ricerca AI ",-1)),jn(f,{to:"/app/ceo/assistant",class:"btn-primary"},{default:Wu(()=>e[15]||(e[15]=[ml(" Avvia Assistente AI ")])),_:1,__:[15]})]))]),a.selectedInsight?(Dr(),qr("div",{key:0,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",onClick:e[9]||(e[9]=(...g)=>a.closeInsightModal&&a.closeInsightModal(...g))},[Wt("div",{class:"bg-white rounded-lg max-w-4xl w-full max-h-[90vh] flex flex-col",onClick:e[8]||(e[8]=Vu(()=>{},["stop"]))},[Wt("div",xh,[Wt("div",Lh,[jn(h,{name:a.getInsightIcon(a.selectedInsight.insight_type||a.selectedInsight.type),size:"sm",class:cs(a.getInsightIconColor(a.selectedInsight.insight_type||a.selectedInsight.type))},null,8,["name","class"]),Wt("div",null,[Wt("h2",Nh,hr(a.selectedInsight.title),1),Wt("p",Ah,hr(a.selectedInsight.category)+" • "+hr(a.formatDate(a.selectedInsight.timestamp)),1)])]),Wt("button",{onClick:e[4]||(e[4]=(...g)=>a.closeInsightModal&&a.closeInsightModal(...g)),class:"text-gray-400 hover:text-gray-600"},[jn(h,{name:"x-mark",size:"md"})])]),Wt("div",_h,[Wt("div",Sh,[Wt("div",{innerHTML:a.renderMarkdown(a.selectedInsight.content),class:"markdown-content"},null,8,Ph)])]),Wt("div",kh,[Wt("div",Ih,[Wt("span",{class:cs(["px-3 py-1 text-sm rounded-full",a.getPriorityClass(a.selectedInsight.priority)])},hr(a.selectedInsight.priority.toUpperCase())+" Priority ",3),Wt("span",Fh,hr(a.selectedInsight.query_count||1)+" query • "+hr(a.selectedInsight.session_duration||"2m"),1)]),Wt("div",Ch,[Wt("button",{onClick:e[5]||(e[5]=g=>a.exportInsight(a.selectedInsight)),class:"btn-secondary"}," Esporta PDF "),Wt("button",{onClick:e[6]||(e[6]=g=>a.deleteInsight(a.selectedInsight)),class:"btn-danger"}," Elimina "),Wt("button",{onClick:e[7]||(e[7]=(...g)=>a.closeInsightModal&&a.closeInsightModal(...g)),class:"btn-primary"}," Chiudi ")])])])])):po("",!0)])}const Oh=Xu(Xc,[["render",jh],["__scopeId","data-v-e433f6e0"]]),Dh=Object.freeze(Object.defineProperty({__proto__:null,default:Oh},Symbol.toStringTag,{value:"Module"}));export{Dh as I,de as _};
