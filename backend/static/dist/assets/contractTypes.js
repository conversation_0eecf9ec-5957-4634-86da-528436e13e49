const e={HOURLY:"hourly",FIXED:"fixed",RETAINER:"retainer",MILESTONE:"milestone",SUBSCRIPTION:"subscription"},o={[e.HOURLY]:"Orario",[e.FIXED]:"Prezzo Fisso",[e.RETAINER]:"Retainer Mensile",[e.MILESTONE]:"A Milestone",[e.SUBSCRIPTION]:"Abbonamento"},r={[e.HOURLY]:"Fatturazione basata su ore lavorate con tariffa oraria",[e.FIXED]:"Prezzo fisso per progetto o deliverable specifico",[e.RETAINER]:"Importo fisso mensile per servizi continuativi",[e.MILESTONE]:"Pagamenti suddivisi per milestone/fasi di progetto",[e.SUBSCRIPTION]:"Abbonamento ricorrente per servizi SaaS/licenze"},i={[e.HOURLY]:"bg-blue-100 text-blue-800",[e.FIXED]:"bg-green-100 text-green-800",[e.RETAINER]:"bg-purple-100 text-purple-800",[e.MILESTONE]:"bg-orange-100 text-orange-800",[e.SUBSCRIPTION]:"bg-indigo-100 text-indigo-800"},a=t=>o[t]||t,n=t=>i[t]||"bg-gray-100 text-gray-800",s=()=>Object.values(e).map(t=>({value:t,label:o[t],description:r[t]}));export{e as C,n as a,s as b,a as g};
