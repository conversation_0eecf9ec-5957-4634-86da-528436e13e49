import{d as ae,r as c,c as v}from"./vendor.js";import{c as i}from"./app.js";const ne=ae("funding",()=>{const u=c([]),d=c([]),p=c([]),g=c(null),E=c(null),m=c({}),w=c({}),h=c([]),y=c(!1),n=c(!1),r=c(null),_=c({}),S=c({ai_service_available:!1,perplexity_configured:!1}),b=v(()=>u.value.filter(a=>a.status==="open"&&!a.is_expired)),O=v(()=>u.value.filter(a=>a.is_deadline_approaching)),I=v(()=>d.value.filter(a=>a.created_by===a.current_user_id||a.project_manager_id===a.current_user_id)),R=v(()=>{const a={};return d.value.forEach(e=>{a[e.status]||(a[e.status]=[]),a[e.status].push(e)}),a});async function z(a={}){n.value=!0,r.value=null;try{const e=await i.get("/api/funding/opportunities",{params:a});if(e.data.success)return u.value=e.data.data.opportunities,e.data.data;throw new Error(e.data.message||"Errore nel caricamento delle opportunità")}catch(e){throw r.value=e.message,console.error("Error fetching funding opportunities:",e),e}finally{n.value=!1}}async function D(a){n.value=!0,r.value=null;try{const e=await i.get(`/api/funding/opportunities/${a}`);if(e.data.success)return g.value=e.data.data.opportunity,e.data.data.opportunity;throw new Error(e.data.message||"Opportunità non trovata")}catch(e){throw r.value=e.message,console.error("Error fetching funding opportunity:",e),e}finally{n.value=!1}}async function C(a){n.value=!0,r.value=null;try{const e=await i.post("/api/funding/opportunities",a);if(e.data.success){const t=e.data.data.opportunity;return u.value.unshift(t),t}else throw new Error(e.data.message||"Errore nella creazione dell'opportunità")}catch(e){throw r.value=e.message,console.error("Error creating funding opportunity:",e),e}finally{n.value=!1}}async function $(a,e){var t;n.value=!0,r.value=null;try{const s=await i.put(`/api/funding/opportunities/${a}`,e);if(s.data.success){const o=s.data.data.opportunity,l=u.value.findIndex(f=>f.id===a);return l!==-1&&(u.value[l]=o),((t=g.value)==null?void 0:t.id)===a&&(g.value=o),o}else throw new Error(s.data.message||"Errore nell'aggiornamento dell'opportunità")}catch(s){throw r.value=s.message,console.error("Error updating funding opportunity:",s),s}finally{n.value=!1}}async function k(a){var e;n.value=!0,r.value=null;try{const t=await i.delete(`/api/funding/opportunities/${a}`);if(t.data.success)return u.value=u.value.filter(s=>s.id!==a),((e=g.value)==null?void 0:e.id)===a&&(g.value=null),!0;throw new Error(t.data.message||"Errore nell'eliminazione dell'opportunità")}catch(t){throw r.value=t.message,console.error("Error deleting funding opportunity:",t),t}finally{n.value=!1}}async function L(a){try{const e=await i.post("/api/funding/opportunities/check-duplicate",{title:a});if(e.data.success)return e.data.data;throw new Error(e.data.message||"Errore nel controllo duplicati")}catch(e){throw console.error("Error checking duplicate opportunity:",e),e}}async function U(a={}){n.value=!0,r.value=null;try{const e=await i.get("/api/funding/applications",{params:a});if(e.data.success)return d.value=e.data.data.applications,e.data.data;throw new Error(e.data.message||"Errore nel caricamento delle candidature")}catch(e){throw r.value=e.message,console.error("Error fetching funding applications:",e),e}finally{n.value=!1}}async function M(a){n.value=!0,r.value=null;try{const e=await i.post("/api/funding/applications",a);if(e.data.success){const t=e.data.data.application;return d.value.unshift(t),t}else throw new Error(e.data.message||"Errore nella creazione della candidatura")}catch(e){throw r.value=e.message,console.error("Error creating funding application:",e),e}finally{n.value=!1}}async function N(a){n.value=!0,r.value=null;try{const e=await i.post(`/api/funding/applications/${a}/submit`);if(e.data.success){const t=e.data.data.application,s=d.value.findIndex(o=>o.id===a);return s!==-1&&(d.value[s]=t),t}else throw new Error(e.data.message||"Errore nella sottomissione della candidatura")}catch(e){throw r.value=e.message,console.error("Error submitting funding application:",e),e}finally{n.value=!1}}async function T(){n.value=!0,r.value=null;try{const a=await i.get("/api/funding/dashboard/stats");if(a.data.success)return m.value=a.data.data.stats,a.data.data.stats;throw new Error(a.data.message||"Errore nel caricamento delle statistiche")}catch(a){throw r.value=a.message,console.error("Error fetching funding dashboard stats:",a),a}finally{n.value=!1}}async function j(a=10){n.value=!0,r.value=null;try{const e=await i.get("/api/funding/dashboard/recent",{params:{limit:a}});if(e.data.success)return w.value=e.data.data,e.data.data;throw new Error(e.data.message||"Errore nel caricamento delle attività recenti")}catch(e){throw r.value=e.message,console.error("Error fetching funding recent activity:",e),e}finally{n.value=!1}}async function H(a,e=null){n.value=!0,r.value=null;try{const t={company_profile:a,search_criteria:e},s=await i.post("/api/funding/ai-search",t);if(s.data.success){const o=s.data.data;return u.value=o.opportunities||[],_.value={content:o.ai_content,citations:o.citations,stats:o.stats,search_performed:o.search_performed,model_used:o.model_used,search_timestamp:new Date().toISOString()},o}else throw new Error(s.data.message||"Errore nella ricerca AI")}catch(t){throw r.value=t.message,console.error("Error in AI search:",t),t}finally{n.value=!1}}async function P(a){n.value=!0,r.value=null;try{const e=await i.post("/api/funding/ai-suggestions",{company_profile:a});if(e.data.success){const t=e.data.data;return h.value=t.suggestions||[],t}else throw new Error(e.data.message||"Errore nel generare suggerimenti AI")}catch(e){throw r.value=e.message,console.error("Error getting AI suggestions:",e),e}finally{n.value=!1}}async function B(a,e){try{const t=await i.post("/api/funding/ai-match-score",{opportunity:a,company_profile:e});if(t.data.success)return t.data.data;throw new Error(t.data.message||"Errore nel calcolo match score")}catch(t){return console.error("Error calculating AI match score:",t),{match_score:50,insights:["Errore nel calcolo"]}}}async function F(){try{const a=await i.get("/api/funding/ai-status");if(a.data.success)return a.data.data;throw new Error(a.data.message||"Servizio AI non disponibile")}catch(a){return console.error("Error checking AI service status:",a),{ai_service_available:!1,perplexity_configured:!1,error:a.message}}}function J(a,e={}){let t=50;if(a.target_sectors&&e.sectors){const s=a.target_sectors.filter(o=>e.sectors.includes(o));t+=s.length*10}return a.status==="open"&&!a.is_expired&&(t+=20),a.is_deadline_approaching&&(t-=10),Math.min(Math.max(t,0),100)}function q(){u.value=[],d.value=[],g.value=null,E.value=null,m.value={},w.value={},n.value=!1,r.value=null}function K(a){let e=[...u.value];return a.status&&(e=e.filter(t=>t.status===a.status)),a.source_entity&&(e=e.filter(t=>{var s;return(s=t.source_entity)==null?void 0:s.toLowerCase().includes(a.source_entity.toLowerCase())})),a.geographic_scope&&(e=e.filter(t=>t.geographic_scope===a.geographic_scope)),a.max_amount&&(e=e.filter(t=>t.max_grant_amount&&t.max_grant_amount<=a.max_amount)),a.deadline_approaching&&(e=e.filter(t=>t.is_deadline_approaching)),e}async function V(a={}){n.value=!0,r.value=null;try{const e=await i.get("/api/funding/expenses",{params:a});if(e.data.success)return p.value=e.data.data.expenses,p.value;throw new Error(e.data.message||"Errore nel caricamento delle spese")}catch(e){throw r.value=e.message,console.error("Error fetching funding expenses:",e),e}finally{n.value=!1}}async function W(a){n.value=!0,r.value=null;try{const e=await i.post("/api/funding/expenses",a);if(e.data.success){const t=e.data.data.expense;return p.value.unshift(t),t}else throw new Error(e.data.message||"Errore nella creazione della spesa")}catch(e){throw r.value=e.message,console.error("Error creating funding expense:",e),e}finally{n.value=!1}}async function Y(a){n.value=!0,r.value=null;try{const e=await i.get(`/api/funding/expenses/${a}`);if(e.data.success)return e.data.data.expense;throw new Error(e.data.message||"Spesa non trovata")}catch(e){throw r.value=e.message,console.error("Error fetching funding expense:",e),e}finally{n.value=!1}}async function G(a,e){n.value=!0,r.value=null;try{const t=await i.put(`/api/funding/expenses/${a}`,e);if(t.data.success){const s=t.data.data.expense,o=p.value.findIndex(l=>l.id===a);return o!==-1&&(p.value[o]=s),s}else throw new Error(t.data.message||"Errore nell'aggiornamento della spesa")}catch(t){throw r.value=t.message,console.error("Error updating funding expense:",t),t}finally{n.value=!1}}async function Q(a){n.value=!0,r.value=null;try{const e=await i.delete(`/api/funding/expenses/${a}`);if(e.data.success)return p.value=p.value.filter(t=>t.id!==a),!0;throw new Error(e.data.message||"Errore nell'eliminazione della spesa")}catch(e){throw r.value=e.message,console.error("Error deleting funding expense:",e),e}finally{n.value=!1}}async function x(a=!1){const e="funding_ai_suggestions";try{if(!a){const o=localStorage.getItem(e);if(o){const l=JSON.parse(o),f=new Date().getTime();if(l.timestamp&&f-l.timestamp<144e5)return console.log("📱 Using cached AI suggestions"),h.value=l.data.suggestions||[],l.data}}console.log("🤖 Fetching fresh AI suggestions..."),y.value=!0;const s=await i.get("/api/funding/ai-dashboard-suggestions");if(s.data.success){const o=s.data.data;h.value=o.suggestions||[];const l={timestamp:new Date().getTime(),data:o};return localStorage.setItem(e,JSON.stringify(l)),console.log("✅ AI suggestions loaded and cached"),o}else throw new Error(s.data.message||"Errore nel caricamento suggerimenti AI")}catch(s){console.error("❌ Error fetching AI suggestions:",s),r.value=s.message||"Errore nel caricamento suggerimenti AI";const o={suggestions:[{title:"Monitora bandi PNRR aggiornati",description:"Controlla regolarmente i nuovi bandi del Piano Nazionale di Ripresa e Resilienza.",priority:"high",action:"Visita il portale PA digitale 2026",category:"ricerca"},{title:"Organizza documentazione aziendale",description:"Prepara i documenti standard per accelerare le future candidature.",priority:"medium",action:"Crea cartelle digitali per bilanci e business plan",category:"candidatura"}],generated_at:new Date().toISOString()};return h.value=o.suggestions,o}finally{y.value=!1}}async function X(){return await x(!0)}async function Z(a){n.value=!0,r.value=null;try{const e=await i.get(`/api/funding/reporting/${a}`);if(e.data.success)return e.data.data;throw new Error(e.data.message||"Errore nel caricamento dati rendicontazione")}catch(e){throw console.error("❌ Error fetching reporting data:",e),r.value=e.message||"Errore nel caricamento dati rendicontazione",e}finally{n.value=!1}}async function ee(a){try{const e=await i.get(`/api/funding/export/${a}`,{responseType:"blob"}),t=new Blob([e.data],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),s=window.URL.createObjectURL(t),o=document.createElement("a");o.href=s;const l=e.headers["content-disposition"];let f=`Rendicontazione_${a}_${new Date().toISOString().split("T")[0]}.xlsx`;if(l){const A=l.match(/filename="(.+)"/);A&&(f=A[1])}return o.download=f,document.body.appendChild(o),o.click(),document.body.removeChild(o),window.URL.revokeObjectURL(s),console.log("✅ Report exported successfully"),{success:!0,filename:f}}catch(e){throw console.error("❌ Error exporting report:",e),r.value=e.message||"Errore nell'esportazione del report",e}}return{opportunities:u,applications:d,expenses:p,currentOpportunity:g,currentApplication:E,dashboardStats:m,recentActivity:w,aiSuggestions:h,aiSuggestionsLoading:y,loading:n,error:r,aiSearchResults:_,aiServiceStatus:S,openOpportunities:b,deadlineApproachingOpportunities:O,userApplications:I,applicationsByStatus:R,fetchOpportunities:z,fetchOpportunity:D,createOpportunity:C,updateOpportunity:$,deleteOpportunity:k,checkDuplicateOpportunity:L,fetchApplications:U,createApplication:M,submitApplication:N,fetchDashboardStats:T,fetchRecentActivity:j,fetchExpenses:V,createExpense:W,fetchExpense:Y,updateExpense:G,deleteExpense:Q,searchOpportunitiesWithAI:H,getAISuggestions:P,calculateAIMatchScore:B,checkAIServiceStatus:F,fetchAIDashboardSuggestions:x,refreshAISuggestions:X,fetchReportingData:Z,exportReport:ee,calculateMatchScore:J,filterOpportunities:K,resetStore:q}});export{ne as u};
