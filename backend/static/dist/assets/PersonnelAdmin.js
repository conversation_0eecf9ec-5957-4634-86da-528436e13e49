import{_ as ee,H as n}from"./app.js";import{_ as ie}from"./PageHeader.js";import{_ as de}from"./AlertsSection.js";import{T as ue}from"./TabNavigation.js";import{r as k,c as J,w as me,o as Y,b as l,j as s,l as e,g as $,e as u,B as T,C as L,S as G,F as B,q as F,t as d,P as ne,f as N,v as P,n as q,p as X,h as se,A as Z,D as ce,E as re}from"./vendor.js";import{P as le}from"./Pagination.js";/* empty css                                                             */const ge={class:"space-y-6"},pe={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0"},ye={class:"flex-1 max-w-lg"},xe={class:"relative"},be={class:"flex items-center space-x-3"},ve=["value"],fe={class:"flex items-center space-x-2 text-sm"},ke=["disabled"],we={key:0,class:"flex justify-center items-center h-64"},he={key:1,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4"},_e={class:"flex"},$e={class:"text-sm text-red-700 dark:text-red-300 mt-1"},ze={key:2,class:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6"},Ce={class:"flex items-center justify-between"},Ee={class:"flex items-center"},Te={class:"text-sm font-medium text-blue-800 dark:text-blue-200"},De={key:0,class:"font-normal"},Ue={key:0,class:"text-xs text-blue-600 dark:text-blue-400 mt-1"},Se={key:0,class:"flex items-center space-x-2"},Pe={key:3,class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},je={class:"overflow-x-auto"},Ne={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},Me={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},Ve={class:"px-6 py-4 whitespace-nowrap"},Ae={class:"flex items-center"},Ie={class:"flex-shrink-0 w-10 h-10"},Le={class:"w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center"},Re=["src","alt"],Oe={class:"ml-4"},He={class:"text-sm font-medium text-gray-900 dark:text-white"},Be={class:"text-sm text-gray-500 dark:text-gray-400"},Fe={key:0,class:"text-xs text-gray-400 dark:text-gray-500"},Ge={class:"px-6 py-4 whitespace-nowrap"},qe={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},Je={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"},Qe={class:"space-y-1"},Ke={key:0},We={key:1},Xe={class:"px-6 py-4 whitespace-nowrap"},Ye={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},Ze={class:"flex items-center justify-end space-x-2"},et=["onClick"],tt=["onClick"],rt=["onClick"],st={key:4,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-12 text-center"},at={class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},ot={__name:"UsersManagement",emits:["user-created","user-updated","user-deleted"],setup(U,{emit:O}){const h=O,b=k([]),z=k([]),x=k(!1),i=k(null),a=k(""),C=k(""),E=k(""),o=k(!1),t=k(1),r=k(10),_=J(()=>{let v=b.value;if(a.value.trim()){const y=a.value.toLowerCase();v=v.filter(S=>S.full_name.toLowerCase().includes(y)||S.email.toLowerCase().includes(y)||S.position&&S.position.toLowerCase().includes(y)||S.department&&S.department.name.toLowerCase().includes(y))}return C.value&&(v=v.filter(y=>y.department_id==C.value)),E.value&&(v=v.filter(y=>y.role===E.value)),v}),D=J(()=>{const v=(t.value-1)*r.value,y=v+r.value;return _.value.slice(v,y)}),M=J(()=>Math.ceil(_.value.length/r.value)),R=J(()=>a.value.trim()!==""||C.value!==""||E.value!==""||o.value),j=async()=>{x.value=!0,i.value=null;try{const v=new URLSearchParams;v.append("per_page","all"),o.value||v.append("is_active","true");const y=`/api/personnel/users?${v.toString()}`;console.log("Loading users with URL:",y);const S=await fetch(y,{credentials:"include"});if(!S.ok)throw new Error(`HTTP ${S.status}: ${S.statusText}`);const w=await S.json();if(w.success)b.value=w.data.users||[];else throw new Error(w.message||"Errore nel caricamento utenti")}catch(v){console.error("Error loading users:",v),i.value=v.message}finally{x.value=!1}},c=async()=>{try{const v=await fetch("/api/personnel/departments",{credentials:"include"});if(!v.ok)throw new Error(`HTTP ${v.status}: ${v.statusText}`);const y=await v.json();y.success&&(z.value=y.data.departments||[])}catch(v){console.error("Error loading departments:",v)}},p=v=>{console.log("Edit user:",v)},m=async v=>{if(confirm(`Sei sicuro di voler resettare la password di ${v.full_name}?`))try{const y=await fetch(`/api/personnel/admin/users/${v.id}/reset-password`,{method:"POST",credentials:"include"});if(!y.ok)throw new Error(`HTTP ${y.status}: ${y.statusText}`);const S=await y.json();if(S.success)alert(`Password resettata per ${v.full_name}. Nuova password temporanea: ${S.data.temporary_password}`);else throw new Error(S.message||"Errore nel reset password")}catch(y){console.error("Error resetting password:",y),alert("Errore nel reset della password: "+y.message)}},I=async v=>{const y=v.is_active?"disattivare":"riattivare";if(confirm(`Sei sicuro di voler ${y} ${v.full_name}?`))try{if(v.is_active){const S=await fetch(`/api/personnel/admin/users/${v.id}`,{method:"DELETE",credentials:"include"});if(!S.ok)throw new Error(`HTTP ${S.status}: ${S.statusText}`);(await S.json()).success&&(v.is_active=!1,h("user-deleted",v))}else{const S=await fetch(`/api/personnel/users/${v.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({is_active:!0})});if(!S.ok)throw new Error(`HTTP ${S.status}: ${S.statusText}`);(await S.json()).success&&(v.is_active=!0,h("user-updated",v))}}catch(S){console.error("Error toggling user status:",S),alert("Errore nell'operazione: "+S.message)}},V=v=>{t.value=v},K=()=>{a.value="",C.value="",E.value="",o.value=!1,j()},Q=v=>({admin:"Admin",manager:"Manager",employee:"Dipendente"})[v]||v,W=v=>({full_time:"Tempo pieno",part_time:"Part-time",contractor:"Consulente",intern:"Stagista"})[v]||v,te=v=>v?new Date(v).toLocaleDateString("it-IT",{year:"numeric",month:"short",day:"numeric"}):"",ae=v=>{if(!v)return!1;const y=new Date,w=new Date(v)-y,f=Math.ceil(w/(1e3*60*60*24));return f<=90&&f>=0};return me([a,C,E,r],()=>{t.value=1}),Y(async()=>{await Promise.all([j(),c()])}),(v,y)=>{const S=se("router-link");return s(),l("div",ge,[e("div",pe,[e("div",ye,[e("div",xe,[u(n,{name:"search",size:"xs",class:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),T(e("input",{"onUpdate:modelValue":y[0]||(y[0]=w=>a.value=w),type:"text",placeholder:"Cerca dipendenti...",class:"pl-10 pr-4 py-2 w-full border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[L,a.value]])])]),e("div",be,[T(e("select",{"onUpdate:modelValue":y[1]||(y[1]=w=>C.value=w),class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},[y[5]||(y[5]=e("option",{value:""},"Tutti i dipartimenti",-1)),(s(!0),l(B,null,F(z.value,w=>(s(),l("option",{key:w.id,value:w.id},d(w.name),9,ve))),128))],512),[[G,C.value]]),T(e("select",{"onUpdate:modelValue":y[2]||(y[2]=w=>E.value=w),class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},y[6]||(y[6]=[e("option",{value:""},"Tutti i ruoli",-1),e("option",{value:"admin"},"Admin",-1),e("option",{value:"manager"},"Manager",-1),e("option",{value:"employee"},"Dipendente",-1)]),512),[[G,E.value]]),e("label",fe,[T(e("input",{type:"checkbox","onUpdate:modelValue":y[3]||(y[3]=w=>o.value=w),onChange:j,class:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"},null,544),[[ne,o.value]]),y[7]||(y[7]=e("span",{class:"text-gray-700 dark:text-gray-300"},"Mostra disattivati",-1))]),T(e("select",{"onUpdate:modelValue":y[4]||(y[4]=w=>r.value=w),class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},y[8]||(y[8]=[e("option",{value:10},"10 per pagina",-1),e("option",{value:20},"20 per pagina",-1),e("option",{value:50},"50 per pagina",-1),e("option",{value:100},"100 per pagina",-1)]),512),[[G,r.value]]),e("button",{onClick:j,disabled:x.value,class:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"},[x.value?(s(),N(n,{key:0,name:"loading",size:"xs",class:"animate-spin"})):(s(),N(n,{key:1,name:"refresh",size:"xs"}))],8,ke)])]),x.value&&!b.value.length?(s(),l("div",we,y[9]||(y[9]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):i.value?(s(),l("div",he,[e("div",_e,[u(n,{name:"error",size:"sm",color:"text-red-400",class:"mr-2 mt-0.5"}),e("div",null,[y[10]||(y[10]=e("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"},"Errore nel caricamento",-1)),e("p",$e,d(i.value),1)])])])):$("",!0),_.value.length>0?(s(),l("div",ze,[e("div",Ce,[e("div",Ee,[u(n,{name:"info",size:"sm",color:"text-blue-600 dark:text-blue-400",class:"mr-2"}),e("div",null,[e("p",Te,[P(d(_.value.length)+" dipendenti trovati ",1),R.value?(s(),l("span",De," (filtrati da "+d(b.value.length)+" totali) ",1)):$("",!0)]),M.value>1?(s(),l("p",Ue," Visualizzazione pagina "+d(t.value)+" di "+d(M.value)+" • "+d(r.value)+" per pagina ",1)):$("",!0)])]),R.value?(s(),l("div",Se,[e("button",{onClick:K,class:"inline-flex items-center px-3 py-1 border border-blue-300 dark:border-blue-600 text-xs font-medium rounded text-blue-700 dark:text-blue-300 bg-white dark:bg-blue-900/50 hover:bg-blue-50 dark:hover:bg-blue-900/70"},[u(n,{name:"close",size:"xs",class:"mr-1"}),y[11]||(y[11]=P(" Rimuovi Filtri "))])])):$("",!0)])])):$("",!0),D.value.length>0?(s(),l("div",Pe,[e("div",je,[e("table",Ne,[y[12]||(y[12]=e("thead",{class:"bg-gray-50 dark:bg-gray-700"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Dipendente "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Ruolo "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Dipartimento "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Contratto "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Stato "),e("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Azioni ")])],-1)),e("tbody",Me,[(s(!0),l(B,null,F(D.value,w=>{var f,g,H;return s(),l("tr",{key:w.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[e("td",Ve,[e("div",Ae,[e("div",Ie,[e("div",Le,[w.profile_image?(s(),l("img",{key:0,src:w.profile_image,alt:w.full_name,class:"w-10 h-10 rounded-full object-cover"},null,8,Re)):(s(),N(n,{key:1,name:"user",size:"sm",color:"text-gray-600 dark:text-gray-300"}))])]),e("div",Oe,[e("div",He,d(w.full_name),1),e("div",Be,d(w.email),1),w.position?(s(),l("div",Fe,d(w.position),1)):$("",!0)])])]),e("td",Ge,[e("span",{class:q(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",w.role==="admin"?"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200":w.role==="manager"?"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"])},d(Q(w.role)),3)]),e("td",qe,d(((f=w.department)==null?void 0:f.name)||"Nessun dipartimento"),1),e("td",Je,[e("div",Qe,[w.hire_date?(s(),l("div",Ke," Assunto: "+d(te(w.hire_date)),1)):$("",!0),(g=w.profile)!=null&&g.employment_type?(s(),l("div",We,d(W(w.profile.employment_type)),1)):$("",!0),(H=w.profile)!=null&&H.contract_end_date?(s(),l("div",{key:2,class:q(ae(w.profile.contract_end_date)?"text-red-600 dark:text-red-400 font-medium":"")}," Scade: "+d(te(w.profile.contract_end_date)),3)):$("",!0)])]),e("td",Xe,[e("span",{class:q(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",w.is_active?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"])},d(w.is_active?"Attivo":"Disattivato"),3)]),e("td",Ye,[e("div",Ze,[u(S,{to:`/app/personnel/${w.id}`,class:"text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300"},{default:X(()=>[u(n,{name:"eye",size:"xs"})]),_:2},1032,["to"]),e("button",{onClick:A=>p(w),class:"text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300"},[u(n,{name:"edit",size:"xs"})],8,et),e("button",{onClick:A=>m(w),class:"text-yellow-600 dark:text-yellow-400 hover:text-yellow-900 dark:hover:text-yellow-300"},[u(n,{name:"key",size:"xs"})],8,tt),e("button",{onClick:A=>I(w),class:q(w.is_active?"text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300":"text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300")},[w.is_active?(s(),N(n,{key:0,name:"ban",size:"xs"})):(s(),N(n,{key:1,name:"success",size:"xs"}))],10,rt)])])])}),128))])])]),M.value>1?(s(),N(le,{key:0,"current-page":t.value,"total-pages":M.value,total:_.value.length,"per-page":r.value,"results-label":"dipendenti",onPageChange:V},null,8,["current-page","total-pages","total","per-page"])):$("",!0)])):(s(),l("div",st,[u(n,{name:"users",size:"2xl",color:"text-gray-400",class:"mx-auto"}),y[13]||(y[13]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun dipendente trovato",-1)),e("p",at,d(R.value?"Prova a modificare i filtri di ricerca.":"Inizia creando il primo dipendente."),1)]))])}}},nt=ee(ot,[["__scopeId","data-v-57d3f2fe"]]),lt={class:"flex items-center justify-between pb-4 border-b border-gray-200 dark:border-gray-700"},it={class:"text-lg font-medium text-gray-900 dark:text-white"},dt={class:"mt-6"},ut={class:"grid grid-cols-1 gap-4"},mt={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},ct=["value"],gt=["value"],pt={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},yt={key:0,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4"},xt={class:"flex"},bt={class:"text-sm text-red-700 dark:text-red-300 mt-1"},vt={class:"flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700"},ft=["disabled"],kt={__name:"DepartmentModal",props:{department:{type:Object,default:null},managers:{type:Array,default:()=>[]},departments:{type:Array,default:()=>[]}},emits:["close","saved"],setup(U,{emit:O}){const h=U,b=O,z=k(!1),x=k(null),i=k({name:"",description:"",manager_id:"",parent_id:"",budget:null,code:""}),a=J(()=>h.department?h.departments.filter(E=>E.id!==h.department.id&&E.parent_id!==h.department.id):h.departments),C=async()=>{z.value=!0,x.value=null;try{const E={...i.value};Object.keys(E).forEach(D=>{E[D]===""&&(E[D]=null)});const o=h.department?`/api/personnel/departments/${h.department.id}`:"/api/personnel/departments",t=h.department?"PUT":"POST",r=await fetch(o,{method:t,headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(E)});if(!r.ok)throw new Error(`HTTP ${r.status}: ${r.statusText}`);const _=await r.json();if(_.success)b("saved",_.data.department);else throw new Error(_.message||"Errore nel salvataggio dipartimento")}catch(E){console.error("Error saving department:",E),x.value=E.message}finally{z.value=!1}};return Y(()=>{h.department&&(i.value={name:h.department.name||"",description:h.department.description||"",manager_id:h.department.manager_id||"",parent_id:h.department.parent_id||"",budget:h.department.budget||null,code:h.department.code||""})}),(E,o)=>(s(),l("div",{class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:o[9]||(o[9]=t=>E.$emit("close"))},[e("div",{class:"relative top-20 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:o[8]||(o[8]=Z(()=>{},["stop"]))},[e("div",lt,[e("h3",it,d(U.department?"Modifica Dipartimento":"Nuovo Dipartimento"),1),e("button",{onClick:o[0]||(o[0]=t=>E.$emit("close")),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},[u(n,{name:"close",size:"lg"})])]),e("div",dt,[e("form",{onSubmit:Z(C,["prevent"]),class:"space-y-6"},[e("div",null,[o[12]||(o[12]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-4"},"Informazioni Base",-1)),e("div",ut,[e("div",null,[o[10]||(o[10]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Nome Dipartimento * ",-1)),T(e("input",{"onUpdate:modelValue":o[1]||(o[1]=t=>i.value.name=t),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[L,i.value.name]])]),e("div",null,[o[11]||(o[11]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Descrizione ",-1)),T(e("textarea",{"onUpdate:modelValue":o[2]||(o[2]=t=>i.value.description=t),rows:"3",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[L,i.value.description]])])])]),e("div",null,[o[17]||(o[17]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-4"},"Gestione",-1)),e("div",mt,[e("div",null,[o[14]||(o[14]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Manager ",-1)),T(e("select",{"onUpdate:modelValue":o[3]||(o[3]=t=>i.value.manager_id=t),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},[o[13]||(o[13]=e("option",{value:""},"Seleziona manager",-1)),(s(!0),l(B,null,F(U.managers,t=>(s(),l("option",{key:t.id,value:t.id},d(t.full_name),9,ct))),128))],512),[[G,i.value.manager_id]])]),e("div",null,[o[16]||(o[16]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Dipartimento Padre ",-1)),T(e("select",{"onUpdate:modelValue":o[4]||(o[4]=t=>i.value.parent_id=t),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},[o[15]||(o[15]=e("option",{value:""},"Nessun padre (dipartimento principale)",-1)),(s(!0),l(B,null,F(a.value,t=>(s(),l("option",{key:t.id,value:t.id},d(t.name),9,gt))),128))],512),[[G,i.value.parent_id]])])])]),e("div",null,[o[20]||(o[20]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-4"},"Budget",-1)),e("div",pt,[e("div",null,[o[18]||(o[18]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Budget Annuale (€) ",-1)),T(e("input",{"onUpdate:modelValue":o[5]||(o[5]=t=>i.value.budget=t),type:"number",step:"100",min:"0",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[L,i.value.budget,void 0,{number:!0}]])]),e("div",null,[o[19]||(o[19]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Codice Dipartimento ",-1)),T(e("input",{"onUpdate:modelValue":o[6]||(o[6]=t=>i.value.code=t),type:"text",placeholder:"es. IT, HR, SALES",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[L,i.value.code]])])])]),x.value?(s(),l("div",yt,[e("div",xt,[u(n,{name:"error",size:"sm",color:"text-red-400",class:"mr-2 mt-0.5"}),e("div",null,[o[21]||(o[21]=e("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"},"Errore nel salvataggio",-1)),e("p",bt,d(x.value),1)])])])):$("",!0),e("div",vt,[e("button",{type:"button",onClick:o[7]||(o[7]=t=>E.$emit("close")),class:"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," Annulla "),e("button",{type:"submit",disabled:z.value,class:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"},[z.value?(s(),N(n,{key:0,name:"loading",size:"sm",color:"text-white",class:"-ml-1 mr-3 inline"})):$("",!0),P(" "+d(z.value?"Salvataggio...":U.department?"Aggiorna":"Crea"),1)],8,ft)])],32)])])]))}},wt=ee(kt,[["__scopeId","data-v-3eb204ea"]]),ht={class:"space-y-6"},_t={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},$t={class:"text-lg font-medium text-gray-900 dark:text-white flex items-center"},zt={class:"mt-4 sm:mt-0 flex items-center space-x-3"},Ct={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},Et={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},Tt={class:"p-5"},Dt={class:"flex items-center"},Ut={class:"flex-shrink-0"},St={class:"ml-5 w-0 flex-1"},Pt={class:"text-lg font-medium text-gray-900 dark:text-white"},jt={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},Nt={class:"p-5"},Mt={class:"flex items-center"},Vt={class:"flex-shrink-0"},At={class:"ml-5 w-0 flex-1"},It={class:"text-lg font-medium text-gray-900 dark:text-white"},Lt={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},Rt={class:"p-5"},Ot={class:"flex items-center"},Ht={class:"flex-shrink-0"},Bt={class:"ml-5 w-0 flex-1"},Ft={class:"text-lg font-medium text-gray-900 dark:text-white"},Gt={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},qt={class:"p-5"},Jt={class:"flex items-center"},Qt={class:"flex-shrink-0"},Kt={class:"ml-5 w-0 flex-1"},Wt={class:"text-lg font-medium text-gray-900 dark:text-white"},Xt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-4"},Yt={class:"flex flex-col lg:flex-row gap-4"},Zt={class:"flex-1"},er={class:"relative"},tr={class:"lg:w-48"},rr=["disabled"],sr={key:0,class:"flex items-center gap-2 mt-4 pt-4 border-t border-gray-200 dark:border-gray-700"},ar={key:0,class:"inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"},or={key:1,class:"inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"},nr={key:0,class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},lr={class:"overflow-x-auto"},ir={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},dr={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},ur={class:"px-6 py-4 whitespace-nowrap"},mr={class:"flex items-center"},cr={class:"flex-shrink-0 w-6 h-6"},gr={class:"w-6 h-6 bg-blue-100 dark:bg-blue-900 rounded flex items-center justify-center"},pr={class:"ml-3"},yr={class:"text-sm font-medium text-gray-900 dark:text-white"},xr={key:0,class:"text-xs text-gray-500 dark:text-gray-400 truncate max-w-xs"},br={key:1,class:"text-xs text-gray-400 dark:text-gray-500"},vr={class:"px-6 py-4 whitespace-nowrap"},fr={key:0,class:"flex items-center"},kr={class:"flex-shrink-0 w-6 h-6"},wr={class:"w-6 h-6 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center"},hr={class:"ml-2"},_r={class:"text-sm font-medium text-gray-900 dark:text-white"},$r={class:"text-xs text-gray-500 dark:text-gray-400 truncate max-w-xs"},zr={key:1,class:"text-sm text-gray-500 dark:text-gray-400"},Cr={class:"px-6 py-4 whitespace-nowrap text-center"},Er={class:"flex items-center justify-center"},Tr={class:"text-sm font-medium text-gray-900 dark:text-white"},Dr={class:"px-6 py-4 whitespace-nowrap"},Ur={class:"text-sm text-gray-900 dark:text-white"},Sr={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},Pr={class:"flex items-center justify-end space-x-2"},jr=["onClick"],Nr=["onClick","disabled"],Mr={key:1,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-12 text-center"},Vr={class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},Ar={key:0,class:"mt-6"},Ir={key:2,class:"flex justify-center items-center h-64"},Lr={__name:"DepartmentsManagement",emits:["department-created","department-updated","department-deleted"],setup(U,{emit:O}){const h=O,b=k([]),z=k([]),x=k({}),i=k(!1),a=k(""),C=k(""),E=k(!1),o=k(!1),t=k(null),r=k(1),_=k(10),D=J(()=>{let f=b.value;if(a.value.trim()){const g=a.value.toLowerCase();f=f.filter(H=>H.name.toLowerCase().includes(g)||H.description&&H.description.toLowerCase().includes(g)||H.manager&&H.manager.full_name.toLowerCase().includes(g))}return C.value&&(C.value==="root"?f=f.filter(g=>!g.parent_id):C.value==="sub"&&(f=f.filter(g=>g.parent_id))),f}),M=J(()=>a.value.trim()!==""||C.value!==""),R=J(()=>Math.ceil(D.value.length/_.value)),j=J(()=>{const f=(r.value-1)*_.value,g=f+_.value;return D.value.slice(f,g)}),c=f=>{switch(f){case"root":return"Dipartimenti principali";case"sub":return"Sotto-dipartimenti";default:return f}};let p=null;const m=()=>{clearTimeout(p),p=setTimeout(()=>{},300)},I=()=>{r.value=1},V=()=>{a.value="",C.value="",r.value=1},K=f=>{r.value=f},Q=async()=>{i.value=!0;try{const f=await fetch("/api/personnel/departments",{credentials:"include"});if(!f.ok)throw new Error(`HTTP ${f.status}: ${f.statusText}`);const g=await f.json();if(g.success)b.value=g.data.departments||[];else throw new Error(g.message||"Errore nel caricamento dipartimenti")}catch(f){console.error("Error loading departments:",f)}finally{i.value=!1}},W=async()=>{try{const f=await fetch("/api/personnel/orgchart",{credentials:"include"});if(!f.ok)throw new Error(`HTTP ${f.status}: ${f.statusText}`);const g=await f.json();if(g.success){x.value=g.data.stats||{};const H=b.value.reduce((A,oe)=>A+(oe.budget||0),0);x.value.total_budget=H}}catch(f){console.error("Error loading stats:",f)}},te=async()=>{try{const f=await fetch("/api/personnel/users?role=manager,admin",{credentials:"include"});if(!f.ok)throw new Error(`HTTP ${f.status}: ${f.statusText}`);const g=await f.json();g.success&&(z.value=g.data.users||[])}catch(f){console.error("Error loading managers:",f)}},ae=f=>{t.value={...f},o.value=!0},v=async f=>{if(f.user_count>0){alert("Impossibile eliminare un dipartimento con dipendenti assegnati");return}if(confirm(`Sei sicuro di voler eliminare il dipartimento "${f.name}"?`))try{const g=await fetch(`/api/personnel/departments/${f.id}`,{method:"DELETE",credentials:"include"});if(!g.ok)throw new Error(`HTTP ${g.status}: ${g.statusText}`);const H=await g.json();if(H.success)await Q(),await W(),h("department-deleted",f);else throw new Error(H.message||"Errore nell'eliminazione")}catch(g){console.error("Error deleting department:",g),alert("Errore nell'eliminazione: "+g.message)}},y=()=>{E.value=!1,o.value=!1,t.value=null},S=async f=>{y(),await Q(),await W(),t.value?h("department-updated",f):h("department-created",f)},w=f=>f?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(f):"€0";return Y(async()=>{await Promise.all([Q(),W(),te()])}),(f,g)=>{const H=se("router-link");return s(),l("div",ht,[e("div",_t,[e("div",null,[e("h3",$t,[u(n,{name:"building-office-2",size:"md",class:"mr-2 text-blue-600"}),g[4]||(g[4]=P(" Gestione Dipartimenti "))]),g[5]||(g[5]=e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Gestisci la struttura organizzativa aziendale ",-1))]),e("div",zt,[e("button",{onClick:g[0]||(g[0]=A=>E.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"},[u(n,{name:"plus",size:"sm",class:"mr-2"}),g[6]||(g[6]=P(" Nuovo Dipartimento "))])])]),e("div",Ct,[e("div",Et,[e("div",Tt,[e("div",Dt,[e("div",Ut,[u(n,{name:"building-office-2",size:"md",class:"text-blue-600"})]),e("div",St,[e("dl",null,[g[7]||(g[7]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Dipartimenti Totali ",-1)),e("dd",Pt,d(x.value.total_departments||0),1)])])])])]),e("div",jt,[e("div",Nt,[e("div",Mt,[e("div",Vt,[u(n,{name:"users",size:"md",class:"text-green-600"})]),e("div",At,[e("dl",null,[g[8]||(g[8]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Dipendenti Totali ",-1)),e("dd",It,d(x.value.total_employees||0),1)])])])])]),e("div",Lt,[e("div",Rt,[e("div",Ot,[e("div",Ht,[u(n,{name:"user-circle",size:"md",class:"text-purple-600"})]),e("div",Bt,[e("dl",null,[g[9]||(g[9]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Manager Assegnati ",-1)),e("dd",Ft,d(x.value.total_managers||0),1)])])])])]),e("div",Gt,[e("div",qt,[e("div",Jt,[e("div",Qt,[u(n,{name:"banknotes",size:"md",class:"text-yellow-600"})]),e("div",Kt,[e("dl",null,[g[10]||(g[10]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Budget Totale ",-1)),e("dd",Wt,d(w(x.value.total_budget||0)),1)])])])])])]),e("div",Xt,[e("div",Yt,[e("div",Zt,[e("div",er,[u(n,{name:"magnifying-glass",size:"md",class:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),T(e("input",{"onUpdate:modelValue":g[1]||(g[1]=A=>a.value=A),onInput:m,type:"text",placeholder:"Cerca dipartimenti...",class:"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,544),[[L,a.value]])])]),e("div",tr,[T(e("select",{"onUpdate:modelValue":g[2]||(g[2]=A=>C.value=A),onChange:I,class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},g[11]||(g[11]=[e("option",{value:""},"Tutti i livelli",-1),e("option",{value:"root"},"Solo dipartimenti principali",-1),e("option",{value:"sub"},"Solo sotto-dipartimenti",-1)]),544),[[G,C.value]])]),e("button",{onClick:Q,disabled:i.value,class:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 flex items-center"},[i.value?(s(),N(n,{key:0,name:"arrow-path",size:"sm",class:"mr-2 animate-spin"})):(s(),N(n,{key:1,name:"arrow-path",size:"sm",class:"mr-2"})),g[12]||(g[12]=P(" Aggiorna "))],8,rr),M.value?(s(),l("button",{key:0,onClick:V,class:"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"}," Pulisci Filtri ")):$("",!0)]),M.value?(s(),l("div",sr,[g[13]||(g[13]=e("span",{class:"text-sm text-gray-600 dark:text-gray-400"},"Filtri attivi:",-1)),a.value?(s(),l("span",ar,[u(n,{name:"magnifying-glass",size:"xs",class:"mr-1"}),P(' "'+d(a.value)+'" ',1)])):$("",!0),C.value?(s(),l("span",or,[u(n,{name:"squares-2x2",size:"xs",class:"mr-1"}),P(" "+d(c(C.value)),1)])):$("",!0)])):$("",!0)]),j.value.length>0?(s(),l("div",nr,[e("div",lr,[e("table",ir,[g[15]||(g[15]=e("thead",{class:"bg-gray-50 dark:bg-gray-700"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Dipartimento "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Manager "),e("th",{class:"px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Dipendenti "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Budget "),e("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Azioni ")])],-1)),e("tbody",dr,[(s(!0),l(B,null,F(j.value,A=>(s(),l("tr",{key:A.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"},[e("td",ur,[e("div",mr,[e("div",cr,[e("div",gr,[u(n,{name:"building-office-2",size:"xs",class:"text-blue-600 dark:text-blue-400"})])]),e("div",pr,[e("div",yr,d(A.name),1),A.description?(s(),l("div",xr,d(A.description),1)):$("",!0),A.parent?(s(),l("div",br," Sotto: "+d(A.parent.name),1)):$("",!0)])])]),e("td",vr,[A.manager?(s(),l("div",fr,[e("div",kr,[e("div",wr,[u(n,{name:"user",size:"xs",class:"text-gray-600 dark:text-gray-300"})])]),e("div",hr,[e("div",_r,d(A.manager.full_name),1),e("div",$r,d(A.manager.email),1)])])):(s(),l("span",zr,"Nessun manager"))]),e("td",Cr,[e("div",Er,[e("span",Tr,d(A.user_count||0),1),g[14]||(g[14]=e("span",{class:"text-xs text-gray-500 dark:text-gray-400 ml-1"},"dip.",-1))])]),e("td",Dr,[e("span",Ur,d(w(A.budget||0)),1)]),e("td",Sr,[e("div",Pr,[u(H,{to:`/app/personnel/departments/${A.id}`,class:"text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 transition-colors duration-200"},{default:X(()=>[u(n,{name:"eye",size:"sm"})]),_:2},1032,["to"]),e("button",{onClick:oe=>ae(A),class:"text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 transition-colors duration-200"},[u(n,{name:"pencil",size:"sm"})],8,jr),e("button",{onClick:oe=>v(A),disabled:A.user_count>0,class:q(A.user_count>0?"text-gray-400 cursor-not-allowed":"text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 transition-colors duration-200")},[u(n,{name:"trash",size:"sm"})],10,Nr)])])]))),128))])])]),R.value>1?(s(),N(le,{key:0,"current-page":r.value,"total-pages":R.value,total:D.value.length,"per-page":_.value,"results-label":"dipartimenti",onPageChange:K},null,8,["current-page","total-pages","total","per-page"])):$("",!0)])):i.value?$("",!0):(s(),l("div",Mr,[u(n,{name:"building-office-2",size:"2xl",class:"mx-auto text-gray-400"}),g[17]||(g[17]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun dipartimento trovato",-1)),e("p",Vr,d(M.value?"Prova a modificare i filtri di ricerca.":"Inizia creando il primo dipartimento."),1),M.value?$("",!0):(s(),l("div",Ar,[e("button",{onClick:g[3]||(g[3]=A=>E.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"},[u(n,{name:"plus",size:"sm",class:"mr-2"}),g[16]||(g[16]=P(" Crea Primo Dipartimento "))])]))])),i.value?(s(),l("div",Ir,g[18]||(g[18]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):$("",!0),E.value||o.value?(s(),N(wt,{key:3,department:t.value,managers:z.value,departments:b.value,onClose:y,onSaved:S},null,8,["department","managers","departments"])):$("",!0)])}}},Rr={class:"flex items-center justify-between pb-4 border-b border-gray-200 dark:border-gray-700"},Or={class:"text-lg font-medium text-gray-900 dark:text-white"},Hr={class:"mt-6"},Br={class:"flex space-x-2"},Fr=["value"],Gr={key:0,class:"mt-2"},qr={key:0,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4"},Jr={class:"flex"},Qr={class:"text-sm text-red-700 dark:text-red-300 mt-1"},Kr={class:"flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700"},Wr=["disabled"],Xr={__name:"SkillModal",props:{skill:{type:Object,default:null},categories:{type:Array,default:()=>[]}},emits:["close","saved"],setup(U,{emit:O}){const h=U,b=O,z=k(!1),x=k(null),i=k(!1),a=k(""),C=k({name:"",category:"",description:""}),E=()=>{a.value.trim()&&(C.value.category=a.value.trim(),a.value="",i.value=!1)},o=async()=>{z.value=!0,x.value=null;try{const t={...C.value};Object.keys(t).forEach(R=>{t[R]===""&&(t[R]=null)});const r=h.skill?`/api/personnel/skills/${h.skill.id}`:"/api/personnel/skills",_=h.skill?"PUT":"POST",D=await fetch(r,{method:_,headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(t)});if(!D.ok)throw new Error(`HTTP ${D.status}: ${D.statusText}`);const M=await D.json();if(M.success)b("saved",M.data.skill);else throw new Error(M.message||"Errore nel salvataggio competenza")}catch(t){console.error("Error saving skill:",t),x.value=t.message}finally{z.value=!1}};return Y(()=>{h.skill&&(C.value={name:h.skill.name||"",category:h.skill.category||"",description:h.skill.description||""})}),(t,r)=>(s(),l("div",{class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:r[8]||(r[8]=_=>t.$emit("close"))},[e("div",{class:"relative top-20 mx-auto p-5 border w-11/12 max-w-lg shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:r[7]||(r[7]=Z(()=>{},["stop"]))},[e("div",Rr,[e("h3",Or,d(U.skill?"Modifica Competenza":"Nuova Competenza"),1),e("button",{onClick:r[0]||(r[0]=_=>t.$emit("close")),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},[u(n,{name:"close",size:"lg"})])]),e("div",Hr,[e("form",{onSubmit:Z(o,["prevent"]),class:"space-y-4"},[e("div",null,[r[9]||(r[9]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Nome Competenza * ",-1)),T(e("input",{"onUpdate:modelValue":r[1]||(r[1]=_=>C.value.name=_),type:"text",required:"",placeholder:"es. JavaScript, Project Management, Design Thinking",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[L,C.value.name]])]),e("div",null,[r[11]||(r[11]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Categoria ",-1)),e("div",Br,[T(e("select",{"onUpdate:modelValue":r[2]||(r[2]=_=>C.value.category=_),class:"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},[r[10]||(r[10]=e("option",{value:""},"Seleziona categoria esistente",-1)),(s(!0),l(B,null,F(U.categories,_=>(s(),l("option",{key:_,value:_},d(_),9,Fr))),128))],512),[[G,C.value.category]]),e("button",{type:"button",onClick:r[3]||(r[3]=_=>i.value=!i.value),class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"},d(i.value?"Annulla":"Nuova"),1)]),i.value?(s(),l("div",Gr,[T(e("input",{"onUpdate:modelValue":r[4]||(r[4]=_=>a.value=_),type:"text",placeholder:"Nome nuova categoria",onBlur:E,onKeyup:ce(E,["enter"]),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,544),[[L,a.value]])])):$("",!0)]),e("div",null,[r[12]||(r[12]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Descrizione ",-1)),T(e("textarea",{"onUpdate:modelValue":r[5]||(r[5]=_=>C.value.description=_),rows:"3",placeholder:"Descrizione della competenza e come viene utilizzata...",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[L,C.value.description]])]),x.value?(s(),l("div",qr,[e("div",Jr,[u(n,{name:"error",size:"sm",color:"text-red-400",class:"mr-2 mt-0.5"}),e("div",null,[r[13]||(r[13]=e("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"},"Errore nel salvataggio",-1)),e("p",Qr,d(x.value),1)])])])):$("",!0),e("div",Kr,[e("button",{type:"button",onClick:r[6]||(r[6]=_=>t.$emit("close")),class:"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," Annulla "),e("button",{type:"submit",disabled:z.value,class:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"},[z.value?(s(),N(n,{key:0,name:"loading",size:"sm",color:"text-white",class:"-ml-1 mr-3 inline"})):$("",!0),P(" "+d(z.value?"Salvataggio...":U.skill?"Aggiorna":"Crea"),1)],8,Wr)])],32)])])]))}},Yr=ee(Xr,[["__scopeId","data-v-ef3698db"]]),Zr={class:"space-y-6"},es={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},ts={class:"mt-4 sm:mt-0 flex items-center space-x-3"},rs={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},ss={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},as={class:"p-5"},os={class:"flex items-center"},ns={class:"flex-shrink-0"},ls={class:"ml-5 w-0 flex-1"},is={class:"text-lg font-medium text-gray-900 dark:text-white"},ds={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},us={class:"p-5"},ms={class:"flex items-center"},cs={class:"flex-shrink-0"},gs={class:"ml-5 w-0 flex-1"},ps={class:"text-lg font-medium text-gray-900 dark:text-white"},ys={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},xs={class:"p-5"},bs={class:"flex items-center"},vs={class:"flex-shrink-0"},fs={class:"ml-5 w-0 flex-1"},ks={class:"text-lg font-medium text-gray-900 dark:text-white"},ws={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},hs={class:"p-5"},_s={class:"flex items-center"},$s={class:"flex-shrink-0"},zs={class:"ml-5 w-0 flex-1"},Cs={class:"text-lg font-medium text-gray-900 dark:text-white"},Es={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0"},Ts={class:"flex-1 max-w-lg"},Ds={class:"relative"},Us={class:"flex items-center space-x-3"},Ss=["value"],Ps=["disabled"],js={key:0,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"},Ns={class:"flex items-start justify-between"},Ms={class:"flex-1"},Vs={class:"text-lg font-medium text-gray-900 dark:text-white"},As={key:0,class:"text-sm text-blue-600 dark:text-blue-400 mt-1"},Is={key:1,class:"text-sm text-gray-500 dark:text-gray-400 mt-2"},Ls={class:"mt-4 flex items-center justify-between"},Rs={class:"flex items-center text-sm text-gray-500 dark:text-gray-400"},Os={class:"flex items-center space-x-2"},Hs=["onClick"],Bs=["onClick","disabled"],Fs={key:1,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-12 text-center"},Gs={class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},qs={class:"mt-6"},Js={key:2,class:"flex justify-center items-center h-64"},Qs={__name:"SkillsManagement",emits:["skill-created","skill-updated","skill-deleted"],setup(U,{emit:O}){const h=O,b=k([]),z=k([]),x=k(!1),i=k(""),a=k(""),C=k(!1),E=k(!1),o=k(null),t=J(()=>{let p=b.value;if(i.value.trim()){const m=i.value.toLowerCase();p=p.filter(I=>I.name.toLowerCase().includes(m)||I.description&&I.description.toLowerCase().includes(m)||I.category&&I.category.toLowerCase().includes(m))}return a.value&&(p=p.filter(m=>m.category===a.value)),p}),r=J(()=>b.value.reduce((p,m)=>p+(m.user_count||0),0)),_=J(()=>b.value.filter(m=>m.user_count>0).length===0?0:3.2),D=async()=>{x.value=!0;try{const p=await fetch("/api/personnel/skills",{credentials:"include"});if(!p.ok)throw new Error(`HTTP ${p.status}: ${p.statusText}`);const m=await p.json();if(m.success)b.value=m.data.skills||[],z.value=m.data.categories||[];else throw new Error(m.message||"Errore nel caricamento competenze")}catch(p){console.error("Error loading skills:",p)}finally{x.value=!1}},M=p=>{o.value={...p},E.value=!0},R=async p=>{if(p.user_count>0){alert("Impossibile eliminare una competenza assegnata a dipendenti");return}if(confirm(`Sei sicuro di voler eliminare la competenza "${p.name}"?`))try{const m=await fetch(`/api/personnel/skills/${p.id}`,{method:"DELETE",credentials:"include"});if(!m.ok)throw new Error(`HTTP ${m.status}: ${m.statusText}`);const I=await m.json();if(I.success)await D(),h("skill-deleted",p);else throw new Error(I.message||"Errore nell'eliminazione")}catch(m){console.error("Error deleting skill:",m),alert("Errore nell'eliminazione: "+m.message)}},j=()=>{C.value=!1,E.value=!1,o.value=null},c=async p=>{j(),await D(),o.value?h("skill-updated",p):h("skill-created",p)};return Y(()=>{D()}),(p,m)=>{const I=se("router-link");return s(),l("div",Zr,[e("div",es,[m[6]||(m[6]=e("div",null,[e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Gestione Competenze"),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Gestisci il catalogo delle competenze aziendali ")],-1)),e("div",ts,[u(I,{to:"/app/personnel/skills",class:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"},{default:X(()=>[u(n,{name:"external-link",size:"xs",class:"mr-2"}),m[4]||(m[4]=P(" Matrice Completa "))]),_:1,__:[4]}),e("button",{onClick:m[0]||(m[0]=V=>C.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"},[u(n,{name:"add",size:"xs",class:"mr-2"}),m[5]||(m[5]=P(" Nuova Competenza "))])])]),e("div",rs,[e("div",ss,[e("div",as,[e("div",os,[e("div",ns,[u(n,{name:"star",size:"lg",color:"text-blue-600"})]),e("div",ls,[e("dl",null,[m[7]||(m[7]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Competenze Totali ",-1)),e("dd",is,d(b.value.length),1)])])])])]),e("div",ds,[e("div",us,[e("div",ms,[e("div",cs,[u(n,{name:"tag",size:"lg",color:"text-green-600"})]),e("div",gs,[e("dl",null,[m[8]||(m[8]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Categorie ",-1)),e("dd",ps,d(z.value.length),1)])])])])]),e("div",ys,[e("div",xs,[e("div",bs,[e("div",vs,[u(n,{name:"users",size:"lg",color:"text-purple-600"})]),e("div",fs,[e("dl",null,[m[9]||(m[9]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Competenze Assegnate ",-1)),e("dd",ks,d(r.value),1)])])])])]),e("div",ws,[e("div",hs,[e("div",_s,[e("div",$s,[u(n,{name:"bolt",size:"lg",color:"text-yellow-600"})]),e("div",zs,[e("dl",null,[m[10]||(m[10]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Livello Medio ",-1)),e("dd",Cs,d(_.value.toFixed(1)),1)])])])])])]),e("div",Es,[e("div",Ts,[e("div",Ds,[u(n,{name:"search",size:"xs",class:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),T(e("input",{"onUpdate:modelValue":m[1]||(m[1]=V=>i.value=V),type:"text",placeholder:"Cerca competenze...",class:"pl-10 pr-4 py-2 w-full border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[L,i.value]])])]),e("div",Us,[T(e("select",{"onUpdate:modelValue":m[2]||(m[2]=V=>a.value=V),class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},[m[11]||(m[11]=e("option",{value:""},"Tutte le categorie",-1)),(s(!0),l(B,null,F(z.value,V=>(s(),l("option",{key:V,value:V},d(V),9,Ss))),128))],512),[[G,a.value]]),e("button",{onClick:D,disabled:x.value,class:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"},[x.value?(s(),N(n,{key:0,name:"loading",size:"xs",class:"animate-spin"})):(s(),N(n,{key:1,name:"refresh",size:"xs"}))],8,Ps)])]),t.value.length>0?(s(),l("div",js,[(s(!0),l(B,null,F(t.value,V=>(s(),l("div",{key:V.id,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6 hover:shadow-lg transition-shadow"},[e("div",Ns,[e("div",Ms,[e("h4",Vs,d(V.name),1),V.category?(s(),l("p",As,d(V.category),1)):$("",!0),V.description?(s(),l("p",Is,d(V.description),1)):$("",!0),e("div",Ls,[e("div",Rs,[u(n,{name:"users",size:"xs",class:"mr-1"}),P(" "+d(V.user_count)+" dipendenti ",1)]),e("div",Os,[e("button",{onClick:K=>M(V),class:"text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300"},[u(n,{name:"edit",size:"xs"})],8,Hs),e("button",{onClick:K=>R(V),disabled:V.user_count>0,class:q(V.user_count>0?"text-gray-400 cursor-not-allowed":"text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300")},[u(n,{name:"delete",size:"xs"})],10,Bs)])])])])]))),128))])):x.value?$("",!0):(s(),l("div",Fs,[u(n,{name:"star",size:"2xl",color:"text-gray-400",class:"mx-auto"}),m[13]||(m[13]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessuna competenza trovata",-1)),e("p",Gs,d(i.value||a.value?"Prova a modificare i filtri di ricerca.":"Inizia creando la prima competenza."),1),e("div",qs,[e("button",{onClick:m[3]||(m[3]=V=>C.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"},[u(n,{name:"add",size:"xs",class:"mr-2"}),m[12]||(m[12]=P(" Crea Prima Competenza "))])])])),x.value?(s(),l("div",Js,m[14]||(m[14]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):$("",!0),C.value||E.value?(s(),N(Yr,{key:3,skill:o.value,categories:z.value,onClose:j,onSaved:c},null,8,["skill","categories"])):$("",!0)])}}},Ks={class:"space-y-6"},Ws={key:0,class:"grid grid-cols-1 md:grid-cols-3 gap-6"},Xs={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},Ys={class:"p-5"},Zs={class:"flex items-center"},ea={class:"flex-shrink-0"},ta={class:"ml-5 w-0 flex-1"},ra={class:"text-lg font-medium text-gray-900 dark:text-white"},sa={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},aa={class:"p-5"},oa={class:"flex items-center"},na={class:"flex-shrink-0"},la={class:"ml-5 w-0 flex-1"},ia={class:"text-lg font-medium text-gray-900 dark:text-white"},da={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},ua={class:"p-5"},ma={class:"flex items-center"},ca={class:"flex-shrink-0"},ga={class:"ml-5 w-0 flex-1"},pa={class:"text-lg font-medium text-gray-900 dark:text-white"},ya={key:1,class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},xa={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},ba={class:"space-y-3"},va={class:"flex items-center"},fa={class:"text-sm text-gray-700 dark:text-gray-300"},ka={class:"flex items-center"},wa={class:"text-sm font-medium text-gray-900 dark:text-white mr-2"},ha={class:"w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2"},_a={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},$a={class:"space-y-3 max-h-64 overflow-y-auto"},za={class:"flex items-center"},Ca={class:"text-sm text-gray-700 dark:text-gray-300 truncate"},Ea={class:"flex items-center"},Ta={class:"text-sm font-medium text-gray-900 dark:text-white mr-2"},Da={class:"w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2"},Ua={key:2,class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},Sa={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Pa={class:"space-y-3"},ja={class:"flex items-center"},Na={class:"text-sm text-gray-700 dark:text-gray-300"},Ma={class:"flex items-center"},Va={class:"text-sm font-medium text-gray-900 dark:text-white mr-2"},Aa={class:"w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2"},Ia={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},La={key:0,class:"space-y-3 max-h-64 overflow-y-auto"},Ra={class:"text-sm text-gray-700 dark:text-gray-300 truncate"},Oa={class:"text-sm font-medium text-gray-900 dark:text-white"},Ha={key:1,class:"text-center py-8"},Ba={class:"flex justify-center"},Fa=["disabled"],Ga={key:3,class:"flex justify-center items-center h-64"},qa={key:4,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-12 text-center"},Ja={__name:"AnalyticsDashboard",props:{analytics:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["refresh"],setup(U,{emit:O}){const h=x=>({admin:"Admin",manager:"Manager",employee:"Dipendente"})[x]||x,b=x=>({full_time:"Tempo pieno",part_time:"Part-time",contractor:"Consulente",intern:"Stagista"})[x]||x,z=x=>x?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(x):"€0";return(x,i)=>(s(),l("div",Ks,[U.analytics?(s(),l("div",Ws,[e("div",Xs,[e("div",Ys,[e("div",Zs,[e("div",ea,[u(n,{name:"users",size:"md",color:"text-blue-600"})]),e("div",ta,[e("dl",null,[i[1]||(i[1]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Dipendenti Totali ",-1)),e("dd",ra,d(U.analytics.overview.total_users),1)])])])])]),e("div",sa,[e("div",aa,[e("div",oa,[e("div",na,[u(n,{name:"building-office",size:"md",color:"text-green-600"})]),e("div",la,[e("dl",null,[i[2]||(i[2]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Dipartimenti ",-1)),e("dd",ia,d(U.analytics.overview.total_departments),1)])])])])]),e("div",da,[e("div",ua,[e("div",ma,[e("div",ca,[u(n,{name:"user-plus",size:"md",color:"text-purple-600"})]),e("div",ga,[e("dl",null,[i[3]||(i[3]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Assunzioni Recenti (90gg) ",-1)),e("dd",pa,d(U.analytics.overview.recent_hires),1)])])])])])])):$("",!0),U.analytics?(s(),l("div",ya,[e("div",xa,[i[4]||(i[4]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Distribuzione per Ruolo",-1)),e("div",ba,[(s(!0),l(B,null,F(U.analytics.users_by_role,a=>(s(),l("div",{key:a.role,class:"flex items-center justify-between"},[e("div",va,[e("div",{class:q(["w-3 h-3 rounded-full mr-3",a.role==="admin"?"bg-red-500":a.role==="manager"?"bg-blue-500":"bg-gray-500"])},null,2),e("span",fa,d(h(a.role)),1)]),e("div",ka,[e("span",wa,d(a.count),1),e("div",ha,[e("div",{class:q(["h-2 rounded-full",a.role==="admin"?"bg-red-500":a.role==="manager"?"bg-blue-500":"bg-gray-500"]),style:re({width:`${a.count/U.analytics.overview.total_users*100}%`})},null,6)])])]))),128))])]),e("div",_a,[i[6]||(i[6]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Distribuzione per Dipartimento",-1)),e("div",$a,[(s(!0),l(B,null,F(U.analytics.users_by_department,a=>(s(),l("div",{key:a.department,class:"flex items-center justify-between"},[e("div",za,[i[5]||(i[5]=e("div",{class:"w-3 h-3 rounded-full bg-indigo-500 mr-3"},null,-1)),e("span",Ca,d(a.department),1)]),e("div",Ea,[e("span",Ta,d(a.count),1),e("div",Da,[e("div",{class:"h-2 rounded-full bg-indigo-500",style:re({width:`${a.count/U.analytics.overview.total_users*100}%`})},null,4)])])]))),128))])])])):$("",!0),U.analytics?(s(),l("div",Ua,[e("div",Sa,[i[8]||(i[8]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Tipi di Contratto",-1)),e("div",Pa,[(s(!0),l(B,null,F(U.analytics.employment_types,a=>(s(),l("div",{key:a.type,class:"flex items-center justify-between"},[e("div",ja,[i[7]||(i[7]=e("div",{class:"w-3 h-3 rounded-full bg-green-500 mr-3"},null,-1)),e("span",Na,d(b(a.type)),1)]),e("div",Ma,[e("span",Va,d(a.count),1),e("div",Aa,[e("div",{class:"h-2 rounded-full bg-green-500",style:re({width:`${a.count/U.analytics.overview.total_users*100}%`})},null,4)])])]))),128))])]),e("div",Ia,[i[10]||(i[10]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Stipendio Medio per Dipartimento",-1)),U.analytics.avg_salary_by_department.length>0?(s(),l("div",La,[(s(!0),l(B,null,F(U.analytics.avg_salary_by_department,a=>(s(),l("div",{key:a.department,class:"flex items-center justify-between"},[e("span",Ra,d(a.department),1),e("span",Oa,d(z(a.avg_salary)),1)]))),128))])):(s(),l("div",Ha,[u(n,{name:"document-text",size:"xl",color:"text-gray-400",className:"mx-auto"}),i[9]||(i[9]=e("p",{class:"mt-2 text-sm text-gray-500 dark:text-gray-400"},"Nessun dato stipendio disponibile",-1))]))])])):$("",!0),e("div",Ba,[e("button",{onClick:i[0]||(i[0]=a=>x.$emit("refresh")),disabled:U.loading,class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"},[U.loading?(s(),N(n,{key:0,name:"arrow-path",size:"sm",className:"animate-spin -ml-1 mr-3"})):(s(),N(n,{key:1,name:"arrow-path",size:"sm",className:"-ml-1 mr-3"})),i[11]||(i[11]=P(" Aggiorna Dati "))],8,Fa)]),U.loading&&!U.analytics?(s(),l("div",Ga,i[12]||(i[12]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):$("",!0),!U.loading&&!U.analytics?(s(),l("div",qa,[u(n,{name:"chart-bar",size:"xl",color:"text-gray-400",className:"mx-auto"}),i[13]||(i[13]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun dato disponibile",-1)),i[14]||(i[14]=e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},' Clicca su "Aggiorna Dati" per caricare le analytics. ',-1))])):$("",!0)]))}},Qa=ee(Ja,[["__scopeId","data-v-c78d2f47"]]),Ka={class:"space-y-6"},Wa={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Xa={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Ya={class:"flex items-center mb-4"},Za={class:"space-y-3"},eo=["disabled"],to={class:"relative"},ro=["disabled"],so={key:0,class:"mt-3 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg"},ao={class:"flex items-center justify-between mb-2"},oo={class:"text-sm text-blue-600 dark:text-blue-400"},no={class:"w-full bg-blue-200 dark:bg-blue-800 rounded-full h-2"},lo={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},io={class:"flex items-center mb-4"},uo={class:"space-y-3"},mo=["disabled"],co=["disabled"],go={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},po={class:"flex items-center mb-4"},yo={class:"space-y-3"},xo={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},bo={class:"flex items-center mb-4"},vo={class:"space-y-3"},fo=["disabled"],ko={class:"flex"},wo={key:0,class:"mt-2"},ho={__name:"BulkOperations",emits:["operation-completed"],setup(U,{emit:O}){const h=O,b=k({template:!1,import:!1,export:!1,verify:!1}),z=k({show:!1,processed:0,total:0}),x=k(null),i=k(!1),a=k(!1),C=k(!1),E=k(!1),o=k(!1),t=k(!1),r=async()=>{b.value.template=!0;try{const c=["email","first_name","last_name","phone","department_id","hire_date","employment_type","salary","role","is_active"].join(",")+`
<EMAIL>,Mario,Rossi,+39 ************,1,2024-01-15,full_time,45000,employee,true
<EMAIL>,Giulia,Bianchi,+39 ************,2,2024-02-01,part_time,30000,manager,true`,p=new Blob([c],{type:"text/csv;charset=utf-8;"}),m=document.createElement("a"),I=URL.createObjectURL(p);m.setAttribute("href",I),m.setAttribute("download","template_dipendenti.csv"),m.style.visibility="hidden",document.body.appendChild(m),m.click(),document.body.removeChild(m),R("success","Template scaricato","Il template CSV è stato scaricato con successo.")}catch(j){console.error("Error downloading template:",j),R("error","Errore download","Impossibile scaricare il template.")}finally{b.value.template=!1}},_=async j=>{const c=j.target.files[0];if(c){b.value.import=!0,z.value.show=!0,z.value.processed=0;try{const p=new FormData;p.append("file",c);const m=await fetch("/api/personnel/import",{method:"POST",credentials:"include",body:p});if(!m.ok)throw new Error(`HTTP ${m.status}: ${m.statusText}`);const I=await m.json();if(I.success)R("success","Import completato",`Importati ${I.data.imported} dipendenti su ${I.data.total} righe processate.`,I.data.errors||[]),h("operation-completed","import");else throw new Error(I.message||"Errore durante l'import")}catch(p){console.error("Error importing file:",p),R("error","Errore import",p.message)}finally{b.value.import=!1,z.value.show=!1,j.target.value=""}}},D=async j=>{b.value.export=!0;try{const p=await fetch(j==="full"?"/api/personnel/export":"/api/personnel/export/contacts",{credentials:"include"});if(!p.ok)throw new Error(`HTTP ${p.status}: ${p.statusText}`);const m=p.headers.get("content-disposition");let I=`export_${j}_${new Date().toISOString().split("T")[0]}.csv`;if(m){const W=m.match(/filename="(.+)"/);W&&(I=W[1])}const V=await p.blob(),K=window.URL.createObjectURL(V),Q=document.createElement("a");Q.href=K,Q.download=I,document.body.appendChild(Q),Q.click(),document.body.removeChild(Q),window.URL.revokeObjectURL(K),R("success","Export completato",`I dati sono stati esportati in ${I}`)}catch(c){console.error("Error exporting data:",c),R("error","Errore export",c.message)}finally{b.value.export=!1}},M=async()=>{b.value.verify=!0;try{const j=await fetch("/api/personnel/verify",{credentials:"include"});if(!j.ok)throw new Error(`HTTP ${j.status}: ${j.statusText}`);const c=await j.json();if(c.success){const p=c.data.issues||[];p.length===0?R("success","Verifica completata","Nessun problema di integrità rilevato."):R("error","Problemi rilevati",`Trovati ${p.length} problemi di integrità dati.`,p)}else throw new Error(c.message||"Errore durante la verifica")}catch(j){console.error("Error verifying data:",j),R("error","Errore verifica",j.message)}finally{b.value.verify=!1}},R=(j,c,p,m=[])=>{x.value={type:j,title:c,message:p,details:m},j==="success"&&setTimeout(()=>{x.value=null},5e3)};return(j,c)=>(s(),l("div",Ka,[c[25]||(c[25]=e("div",null,[e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Operazioni di Massa"),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Import/Export dati e operazioni bulk ")],-1)),e("div",Wa,[e("div",Xa,[e("div",Ya,[u(n,{name:"arrow-down-tray",size:"md",color:"text-green-600",className:"mr-3"}),c[10]||(c[10]=e("h4",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Import Dipendenti",-1))]),c[12]||(c[12]=e("p",{class:"text-sm text-gray-500 dark:text-gray-400 mb-4"}," Importa dipendenti da file CSV con dati contrattuali completi ",-1)),e("div",Za,[e("button",{onClick:r,disabled:b.value.template,class:"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"},[b.value.template?(s(),N(n,{key:0,name:"arrow-path",size:"sm",className:"animate-spin mr-2"})):(s(),N(n,{key:1,name:"arrow-down-tray",size:"sm",className:"mr-2"})),P(" "+d(b.value.template?"Generando...":"Scarica Template CSV"),1)],8,eo),e("div",to,[e("input",{ref:"fileInput",type:"file",accept:".csv",onChange:_,class:"hidden"},null,544),e("button",{onClick:c[0]||(c[0]=p=>j.$refs.fileInput.click()),disabled:b.value.import,class:"w-full inline-flex justify-center items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 disabled:opacity-50"},[b.value.import?(s(),N(n,{key:0,name:"arrow-path",size:"sm",className:"animate-spin mr-2"})):(s(),N(n,{key:1,name:"arrow-up-tray",size:"sm",className:"mr-2"})),P(" "+d(b.value.import?"Importando...":"Carica File CSV"),1)],8,ro)]),z.value.show?(s(),l("div",so,[e("div",ao,[c[11]||(c[11]=e("span",{class:"text-sm font-medium text-blue-800 dark:text-blue-200"},"Import in corso...",-1)),e("span",oo,d(z.value.processed)+"/"+d(z.value.total),1)]),e("div",no,[e("div",{class:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:re({width:`${z.value.processed/z.value.total*100}%`})},null,4)])])):$("",!0)])]),e("div",lo,[e("div",io,[u(n,{name:"arrow-up-tray",size:"md",color:"text-blue-600",className:"mr-3"}),c[13]||(c[13]=e("h4",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Export Dati",-1))]),c[15]||(c[15]=e("p",{class:"text-sm text-gray-500 dark:text-gray-400 mb-4"}," Esporta dati del personale in vari formati ",-1)),e("div",uo,[e("button",{onClick:c[1]||(c[1]=p=>D("full")),disabled:b.value.export,class:"w-full inline-flex justify-center items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"},[b.value.export?(s(),N(n,{key:0,name:"arrow-path",size:"sm",className:"animate-spin mr-2"})):(s(),N(n,{key:1,name:"arrow-down-tray",size:"sm",className:"mr-2"})),P(" "+d(b.value.export?"Esportando...":"Export Completo CSV"),1)],8,mo),e("button",{onClick:c[2]||(c[2]=p=>D("contacts")),disabled:b.value.export,class:"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"},[b.value.export?(s(),N(n,{key:0,name:"arrow-path",size:"sm",className:"animate-spin mr-2"})):(s(),N(n,{key:1,name:"funnel",size:"sm",className:"mr-2"})),P(" "+d(b.value.export?"Esportando...":"Export Solo Contatti"),1)],8,co),e("button",{onClick:c[3]||(c[3]=p=>i.value=!0),class:"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"},[u(n,{name:"document",size:"sm",className:"mr-2"}),c[14]||(c[14]=P(" Export Personalizzato "))])])]),e("div",go,[e("div",po,[u(n,{name:"cog-6-tooth",size:"md",color:"text-purple-600",className:"mr-3"}),c[16]||(c[16]=e("h4",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Aggiornamenti di Massa",-1))]),c[20]||(c[20]=e("p",{class:"text-sm text-gray-500 dark:text-gray-400 mb-4"}," Applica modifiche a più dipendenti contemporaneamente ",-1)),e("div",yo,[e("button",{onClick:c[4]||(c[4]=p=>a.value=!0),class:"w-full inline-flex justify-center items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700"},[u(n,{name:"building-office",size:"sm",className:"mr-2"}),c[17]||(c[17]=P(" Assegnazione Dipartimenti "))]),e("button",{onClick:c[5]||(c[5]=p=>C.value=!0),class:"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"},[u(n,{name:"star",size:"sm",className:"mr-2"}),c[18]||(c[18]=P(" Assegnazione Competenze "))]),e("button",{onClick:c[6]||(c[6]=p=>E.value=!0),class:"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"},[u(n,{name:"shield-check",size:"sm",className:"mr-2"}),c[19]||(c[19]=P(" Modifica Ruoli "))])])]),e("div",xo,[e("div",bo,[u(n,{name:"trash",size:"md",color:"text-red-600",className:"mr-3"}),c[21]||(c[21]=e("h4",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Pulizia Dati",-1))]),c[24]||(c[24]=e("p",{class:"text-sm text-gray-500 dark:text-gray-400 mb-4"}," Strumenti per la manutenzione e pulizia dei dati ",-1)),e("div",vo,[e("button",{onClick:M,disabled:b.value.verify,class:"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"},[b.value.verify?(s(),N(n,{key:0,name:"arrow-path",size:"sm",className:"animate-spin mr-2"})):(s(),N(n,{key:1,name:"check-circle",size:"sm",className:"mr-2"})),P(" "+d(b.value.verify?"Verificando...":"Verifica Integrità Dati"),1)],8,fo),e("button",{onClick:c[7]||(c[7]=p=>o.value=!0),class:"w-full inline-flex justify-center items-center px-4 py-2 border border-red-300 dark:border-red-600 shadow-sm text-sm font-medium rounded-md text-red-700 dark:text-red-300 bg-white dark:bg-gray-800 hover:bg-red-50 dark:hover:bg-red-900/20"},[u(n,{name:"user-minus",size:"sm",className:"mr-2"}),c[22]||(c[22]=P(" Rimuovi Utenti Inattivi "))]),e("button",{onClick:c[8]||(c[8]=p=>t.value=!0),class:"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"},[u(n,{name:"arrow-path",class:"w-4 h-4 mr-2"}),c[23]||(c[23]=P(" Pulizia Avanzata "))])])])]),x.value?(s(),l("div",{key:0,class:q(["mt-6 p-4 rounded-lg",x.value.type==="success"?"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800":"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800"])},[e("div",ko,[x.value.type==="success"?(s(),N(n,{key:0,name:"check-circle",class:"w-5 h-5 text-green-400 mr-2 mt-0.5"})):(s(),N(n,{key:1,name:"x-circle",class:"w-5 h-5 text-red-400 mr-2 mt-0.5"})),e("div",null,[e("h3",{class:q(["text-sm font-medium",x.value.type==="success"?"text-green-800 dark:text-green-200":"text-red-800 dark:text-red-200"])},d(x.value.title),3),e("p",{class:q(["text-sm mt-1",x.value.type==="success"?"text-green-700 dark:text-green-300":"text-red-700 dark:text-red-300"])},d(x.value.message),3),x.value.details&&x.value.details.length>0?(s(),l("div",wo,[e("ul",{class:q(["text-xs space-y-1",x.value.type==="success"?"text-green-600 dark:text-green-400":"text-red-600 dark:text-red-400"])},[(s(!0),l(B,null,F(x.value.details,p=>(s(),l("li",{key:p},"• "+d(p),1))),128))],2)])):$("",!0)]),e("button",{onClick:c[9]||(c[9]=p=>x.value=null),class:"ml-auto"},[u(n,{name:"x-mark",class:q(["w-4 h-4",x.value.type==="success"?"text-green-400":"text-red-400"])},null,8,["class"])])])],2)):$("",!0)]))}},_o={class:"flex items-center justify-between pb-4 border-b border-gray-200 dark:border-gray-700"},$o={class:"mt-6"},zo={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Co={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Eo=["value"],To={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Do={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Uo={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},So={class:"flex items-center space-x-4"},Po={class:"flex items-center"},jo={key:0,class:"mt-3"},No={key:0,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4"},Mo={class:"flex"},Vo={class:"text-sm text-red-700 dark:text-red-300 mt-1"},Ao={class:"flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700"},Io=["disabled"],Lo={__name:"CreateUserModal",emits:["close","user-created"],setup(U,{emit:O}){const h=O,b=k(!1),z=k(null),x=k([]),i=k(!0),a=k({first_name:"",last_name:"",username:"",email:"",phone:"",position:"",role:"employee",department_id:"",hire_date:"",employment_type:"full_time",work_location:"",weekly_hours:40,probation_end_date:"",contract_end_date:"",salary:null,salary_currency:"EUR",emergency_contact_name:"",emergency_contact_phone:"",emergency_contact_relationship:"",password:""}),C=async()=>{try{const o=await fetch("/api/personnel/departments",{credentials:"include"});if(!o.ok)throw new Error(`HTTP ${o.status}: ${o.statusText}`);const t=await o.json();t.success&&(x.value=t.data.departments||[])}catch(o){console.error("Error loading departments:",o)}},E=async()=>{b.value=!0,z.value=null;try{const o={...a.value};i.value&&delete o.password,Object.keys(o).forEach(_=>{o[_]===""&&(o[_]=null)});const t=await fetch("/api/personnel/admin/users",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(o)});if(!t.ok)throw new Error(`HTTP ${t.status}: ${t.statusText}`);const r=await t.json();if(r.success)h("user-created",r.data.user),i.value&&r.data.temporary_password&&alert(`Utente creato con successo!
Password temporanea: ${r.data.temporary_password}`);else throw new Error(r.message||"Errore nella creazione utente")}catch(o){console.error("Error creating user:",o),z.value=o.message}finally{b.value=!1}};return Y(()=>{C();const o=new Date().toISOString().split("T")[0];a.value.hire_date=o}),(o,t)=>(s(),l("div",{class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:t[24]||(t[24]=r=>o.$emit("close"))},[e("div",{class:"relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:t[23]||(t[23]=Z(()=>{},["stop"]))},[e("div",_o,[t[25]||(t[25]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Crea Nuovo Dipendente ",-1)),e("button",{onClick:t[0]||(t[0]=r=>o.$emit("close")),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},[u(n,{name:"close",size:"lg"})])]),e("div",$o,[e("form",{onSubmit:Z(E,["prevent"]),class:"space-y-6"},[e("div",null,[t[32]||(t[32]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-4"},"Informazioni Base",-1)),e("div",zo,[e("div",null,[t[26]||(t[26]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Nome * ",-1)),T(e("input",{"onUpdate:modelValue":t[1]||(t[1]=r=>a.value.first_name=r),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[L,a.value.first_name]])]),e("div",null,[t[27]||(t[27]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Cognome * ",-1)),T(e("input",{"onUpdate:modelValue":t[2]||(t[2]=r=>a.value.last_name=r),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[L,a.value.last_name]])]),e("div",null,[t[28]||(t[28]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Username * ",-1)),T(e("input",{"onUpdate:modelValue":t[3]||(t[3]=r=>a.value.username=r),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[L,a.value.username]])]),e("div",null,[t[29]||(t[29]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Email * ",-1)),T(e("input",{"onUpdate:modelValue":t[4]||(t[4]=r=>a.value.email=r),type:"email",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[L,a.value.email]])]),e("div",null,[t[30]||(t[30]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Telefono ",-1)),T(e("input",{"onUpdate:modelValue":t[5]||(t[5]=r=>a.value.phone=r),type:"tel",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[L,a.value.phone]])]),e("div",null,[t[31]||(t[31]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Posizione ",-1)),T(e("input",{"onUpdate:modelValue":t[6]||(t[6]=r=>a.value.position=r),type:"text",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[L,a.value.position]])])])]),e("div",null,[t[37]||(t[37]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-4"},"Ruolo e Dipartimento",-1)),e("div",Co,[e("div",null,[t[34]||(t[34]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Ruolo ",-1)),T(e("select",{"onUpdate:modelValue":t[7]||(t[7]=r=>a.value.role=r),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},t[33]||(t[33]=[e("option",{value:"employee"},"Dipendente",-1),e("option",{value:"manager"},"Manager",-1),e("option",{value:"admin"},"Admin",-1)]),512),[[G,a.value.role]])]),e("div",null,[t[36]||(t[36]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Dipartimento ",-1)),T(e("select",{"onUpdate:modelValue":t[8]||(t[8]=r=>a.value.department_id=r),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},[t[35]||(t[35]=e("option",{value:""},"Seleziona dipartimento",-1)),(s(!0),l(B,null,F(x.value,r=>(s(),l("option",{key:r.id,value:r.id},d(r.name),9,Eo))),128))],512),[[G,a.value.department_id]])])])]),e("div",null,[t[46]||(t[46]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-4"},"Informazioni Contrattuali",-1)),e("div",To,[e("div",null,[t[38]||(t[38]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Data Assunzione ",-1)),T(e("input",{"onUpdate:modelValue":t[9]||(t[9]=r=>a.value.hire_date=r),type:"date",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[L,a.value.hire_date]])]),e("div",null,[t[40]||(t[40]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Tipo Contratto ",-1)),T(e("select",{"onUpdate:modelValue":t[10]||(t[10]=r=>a.value.employment_type=r),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},t[39]||(t[39]=[e("option",{value:"full_time"},"Tempo pieno",-1),e("option",{value:"part_time"},"Part-time",-1),e("option",{value:"contractor"},"Consulente",-1),e("option",{value:"intern"},"Stagista",-1)]),512),[[G,a.value.employment_type]])]),e("div",null,[t[42]||(t[42]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Modalità Lavoro ",-1)),T(e("select",{"onUpdate:modelValue":t[11]||(t[11]=r=>a.value.work_location=r),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},t[41]||(t[41]=[e("option",{value:""},"Seleziona modalità",-1),e("option",{value:"office"},"Ufficio",-1),e("option",{value:"remote"},"Remoto",-1),e("option",{value:"hybrid"},"Ibrido",-1)]),512),[[G,a.value.work_location]])]),e("div",null,[t[43]||(t[43]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Ore Settimanali ",-1)),T(e("input",{"onUpdate:modelValue":t[12]||(t[12]=r=>a.value.weekly_hours=r),type:"number",step:"0.5",min:"0",max:"60",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[L,a.value.weekly_hours,void 0,{number:!0}]])]),e("div",null,[t[44]||(t[44]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Fine Periodo Prova ",-1)),T(e("input",{"onUpdate:modelValue":t[13]||(t[13]=r=>a.value.probation_end_date=r),type:"date",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[L,a.value.probation_end_date]])]),e("div",null,[t[45]||(t[45]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Scadenza Contratto ",-1)),T(e("input",{"onUpdate:modelValue":t[14]||(t[14]=r=>a.value.contract_end_date=r),type:"date",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[L,a.value.contract_end_date]])])])]),e("div",null,[t[50]||(t[50]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-4"},"Informazioni Economiche",-1)),e("div",Do,[e("div",null,[t[47]||(t[47]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Stipendio Annuo ",-1)),T(e("input",{"onUpdate:modelValue":t[15]||(t[15]=r=>a.value.salary=r),type:"number",step:"100",min:"0",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[L,a.value.salary,void 0,{number:!0}]])]),e("div",null,[t[49]||(t[49]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Valuta ",-1)),T(e("select",{"onUpdate:modelValue":t[16]||(t[16]=r=>a.value.salary_currency=r),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},t[48]||(t[48]=[e("option",{value:"EUR"},"EUR",-1),e("option",{value:"USD"},"USD",-1),e("option",{value:"GBP"},"GBP",-1)]),512),[[G,a.value.salary_currency]])])])]),e("div",null,[t[54]||(t[54]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-4"},"Contatto di Emergenza",-1)),e("div",Uo,[e("div",null,[t[51]||(t[51]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Nome Contatto ",-1)),T(e("input",{"onUpdate:modelValue":t[17]||(t[17]=r=>a.value.emergency_contact_name=r),type:"text",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[L,a.value.emergency_contact_name]])]),e("div",null,[t[52]||(t[52]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Telefono Emergenza ",-1)),T(e("input",{"onUpdate:modelValue":t[18]||(t[18]=r=>a.value.emergency_contact_phone=r),type:"tel",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[L,a.value.emergency_contact_phone]])]),e("div",null,[t[53]||(t[53]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Relazione ",-1)),T(e("input",{"onUpdate:modelValue":t[19]||(t[19]=r=>a.value.emergency_contact_relationship=r),type:"text",placeholder:"es. Coniuge, Genitore...",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[L,a.value.emergency_contact_relationship]])])])]),e("div",null,[t[57]||(t[57]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-4"},"Password",-1)),e("div",So,[e("label",Po,[T(e("input",{"onUpdate:modelValue":t[20]||(t[20]=r=>i.value=r),type:"checkbox",class:"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"},null,512),[[ne,i.value]]),t[55]||(t[55]=e("span",{class:"ml-2 text-sm text-gray-700 dark:text-gray-300"}," Genera password temporanea automaticamente ",-1))])]),i.value?$("",!0):(s(),l("div",jo,[t[56]||(t[56]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Password Personalizzata ",-1)),T(e("input",{"onUpdate:modelValue":t[21]||(t[21]=r=>a.value.password=r),type:"password",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[L,a.value.password]])]))]),z.value?(s(),l("div",No,[e("div",Mo,[u(n,{name:"error",size:"sm",color:"text-red-400",class:"mr-2 mt-0.5"}),e("div",null,[t[58]||(t[58]=e("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"},"Errore nella creazione",-1)),e("p",Vo,d(z.value),1)])])])):$("",!0),e("div",Ao,[e("button",{type:"button",onClick:t[22]||(t[22]=r=>o.$emit("close")),class:"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," Annulla "),e("button",{type:"submit",disabled:b.value,class:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"},[b.value?(s(),N(n,{key:0,name:"loading",size:"sm",color:"text-white",class:"-ml-1 mr-3 inline"})):$("",!0),P(" "+d(b.value?"Creazione...":"Crea Dipendente"),1)],8,Io)])],32)])])]))}},Ro=ee(Lo,[["__scopeId","data-v-e74f19e2"]]),Oo={class:"space-y-6"},Ho=["disabled"],Bo={class:"mt-6"},Fo={key:0},Go={key:1},qo={key:2},Jo={key:3},Qo={key:4},Ko={__name:"PersonnelAdmin",setup(U){const O=k(!1),h=k(null),b=k("users"),z=k(!1),x=k([{id:"users",name:"Gestione Utenti",icon:"users"},{id:"departments",name:"Dipartimenti",icon:"building-office-2"},{id:"skills",name:"Competenze",icon:"star"},{id:"analytics",name:"Analytics",icon:"chart-bar"},{id:"bulk",name:"Operazioni Bulk",icon:"list-bullet"}]),i=J(()=>{var r,_,D;if(!((r=h.value)!=null&&r.alerts))return[];const t=[];return((_=h.value.alerts.expiring_contracts)==null?void 0:_.length)>0&&t.push({id:"expiring-contracts",type:"error",title:"Contratti in Scadenza",message:`${h.value.alerts.expiring_contracts.length} contratti scadranno nei prossimi 90 giorni:`,items:h.value.alerts.expiring_contracts.map(M=>({id:M.user_id,full_name:M.full_name,end_date:M.contract_end_date,days_remaining:M.days_remaining})),maxItems:3,moreText:"altri contratti"}),((D=h.value.alerts.ending_probation)==null?void 0:D.length)>0&&t.push({id:"ending-probation",type:"warning",title:"Periodi di Prova in Scadenza",icon:"clock",message:`${h.value.alerts.ending_probation.length} periodi di prova termineranno nei prossimi 30 giorni:`,items:h.value.alerts.ending_probation.map(M=>({id:M.user_id,full_name:M.full_name,end_date:M.probation_end_date,days_remaining:M.days_remaining})),maxItems:3,moreText:"altri periodi di prova"}),t}),a=async()=>{O.value=!0;try{const t=await fetch("/api/personnel/admin/analytics",{credentials:"include"});if(!t.ok)throw new Error(`HTTP ${t.status}: ${t.statusText}`);const r=await t.json();if(r.success)h.value=r.data;else throw new Error(r.message||"Errore nel caricamento analytics")}catch(t){console.error("Error loading analytics:",t)}finally{O.value=!1}},C=async()=>{try{const t=await fetch("/api/personnel/export",{credentials:"include"});if(!t.ok)throw new Error(`HTTP ${t.status}: ${t.statusText}`);const r=await t.blob(),_=window.URL.createObjectURL(r),D=document.createElement("a");D.href=_,D.download=`personnel-export-${new Date().toISOString().split("T")[0]}.csv`,document.body.appendChild(D),D.click(),document.body.removeChild(D),window.URL.revokeObjectURL(_)}catch(t){console.error("Error exporting data:",t)}},E=t=>{z.value=!1,a()},o=t=>t?new Date(t).toLocaleDateString("it-IT",{year:"numeric",month:"short",day:"numeric"}):"";return Y(()=>{a()}),(t,r)=>{const _=se("router-link");return s(),l("div",Oo,[u(ie,{title:"Gestione Personale",subtitle:"Gestione completa del personale e dati contrattuali",icon:"cog-6-tooth"},{actions:X(()=>[e("button",{onClick:r[0]||(r[0]=D=>z.value=!0),class:"btn-primary"},[u(n,{name:"plus",size:"sm",class:"mr-2"}),r[3]||(r[3]=P(" Nuovo Dipendente "))]),u(_,{to:"/app/personnel/allocation",class:"btn-secondary"},{default:X(()=>[u(n,{name:"user-group",size:"sm",class:"mr-2"}),r[4]||(r[4]=P(" Allocazione Risorse "))]),_:1,__:[4]}),u(_,{to:"/app/personnel/orgchart",class:"btn-secondary"},{default:X(()=>[u(n,{name:"chart-bar-square",size:"sm",class:"mr-2"}),r[5]||(r[5]=P(" Organigramma "))]),_:1,__:[5]}),u(_,{to:"/app/personnel/skills",class:"btn-secondary"},{default:X(()=>[u(n,{name:"star",size:"sm",class:"mr-2"}),r[6]||(r[6]=P(" Skills Matrix "))]),_:1,__:[6]}),e("button",{onClick:C,disabled:O.value,class:"btn-secondary disabled:opacity-50"},[u(n,{name:"arrow-down-tray",size:"sm",class:"mr-2"}),r[7]||(r[7]=P(" Esporta Dati "))],8,Ho)]),_:1}),u(de,{alerts:i.value},{"alert-item":X(({item:D,alert:M})=>[e("strong",null,d(D.full_name),1),P(" - "+d(o(D.end_date))+" ("+d(D.days_remaining)+" giorni) ",1)]),_:1},8,["alerts"]),u(ue,{tabs:x.value,"active-tab":b.value,onTabChange:r[1]||(r[1]=D=>b.value=D)},null,8,["tabs","active-tab"]),e("div",Bo,[b.value==="users"?(s(),l("div",Fo,[u(nt,{onUserCreated:a,onUserUpdated:a,onUserDeleted:a})])):b.value==="departments"?(s(),l("div",Go,[u(Lr,{onDepartmentCreated:a,onDepartmentUpdated:a,onDepartmentDeleted:a})])):b.value==="skills"?(s(),l("div",qo,[u(Qs,{onSkillCreated:a,onSkillUpdated:a,onSkillDeleted:a})])):b.value==="analytics"?(s(),l("div",Jo,[u(Qa,{analytics:h.value,loading:O.value,onRefresh:a},null,8,["analytics","loading"])])):b.value==="bulk"?(s(),l("div",Qo,[u(ho,{onOperationCompleted:a})])):$("",!0)]),z.value?(s(),N(Ro,{key:0,onClose:r[2]||(r[2]=D=>z.value=!1),onUserCreated:E})):$("",!0)])}}},sn=ee(Ko,[["__scopeId","data-v-f746c373"]]);export{sn as default};
