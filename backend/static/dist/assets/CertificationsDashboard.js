import{u as A}from"./certifications.js";import{D as S}from"./DashboardTemplate.js";import{_ as I,H as N}from"./app.js";import{f as V,p as m,h as y,r as x,c as j,o as B,j as n,l as e,b as c,e as i,v as _,F as w,q as z,t as r,n as C,g as T}from"./vendor.js";const F={name:"CertificationsDashboard",components:{DashboardTemplate:S,HeroIcon:N},setup(){const f=A(),t=x(!0),u=x({}),o=x([]),p=x([]),v=j(()=>[{label:"Certificazioni Attive",value:u.value.active_certifications||0,icon:"shield-check",color:"green"},{label:"In Scadenza",value:u.value.expiring_soon||0,icon:"exclamation-triangle",color:"orange"},{label:"Compliance Score",value:`${u.value.compliance_score||0}%`,icon:"chart-bar",color:"blue"},{label:"Costo Annuale",value:`€${d(u.value.total_annual_cost||0)}`,icon:"currency-euro",color:"purple"}]),l=async()=>{try{t.value=!0;const a=await f.getOverview();u.value=a.metrics||{},o.value=a.certifications||[],p.value=a.upcoming_actions||[]}catch(a){console.error("Errore nel caricamento dashboard certificazioni:",a)}finally{t.value=!1}},d=a=>a?new Intl.NumberFormat("it-IT",{minimumFractionDigits:0,maximumFractionDigits:0}).format(a):"0",g=a=>a?new Date(a).toLocaleDateString("it-IT"):"N/A",s=a=>({active:"bg-green-100 text-green-800",expired:"bg-red-100 text-red-800",in_renewal:"bg-yellow-100 text-yellow-800",suspended:"bg-gray-100 text-gray-800"})[a]||"bg-gray-100 text-gray-800",h=a=>({active:"Attiva",expired:"Scaduta",in_renewal:"In Rinnovo",suspended:"Sospesa"})[a]||a,D=a=>({renewal_due:"exclamation-triangle",audit_scheduled:"calendar"})[a]||"info",k=a=>({high:"h-5 w-5 text-red-500",medium:"h-5 w-5 text-orange-500",low:"h-5 w-5 text-blue-500"})[a]||"h-5 w-5 text-gray-500";return B(()=>{l()}),{loading:t,metrics:u,certifications:o,upcomingActions:p,statsData:v,loadDashboardData:l,formatCurrency:d,formatDate:g,getStatusBadgeClass:s,getStatusLabel:h,getActionIcon:D,getActionIconClass:k}}},H={class:"flex space-x-2"},E={class:"space-y-6"},L={class:"bg-white rounded-lg shadow-sm"},R={class:"px-6 py-4 border-b border-gray-200 flex justify-between items-center"},P={key:0,class:"flex justify-center py-8"},q={key:1,class:"p-6 text-center text-gray-500"},G={class:"text-center py-12"},M={class:"flex justify-center space-x-3"},O={key:2,class:"divide-y divide-gray-200"},J=["onClick"],K={class:"flex items-start justify-between"},Q={class:"flex-1"},U={class:"flex items-center space-x-3"},W={class:"font-semibold text-gray-900"},X={class:"text-sm text-gray-600 mt-1"},Y={class:"flex items-center space-x-4 mt-2 text-sm text-gray-500"},Z={key:0},$={class:"flex items-center space-x-4"},ee={class:"text-center"},te={class:"text-sm font-semibold text-gray-900"},se={class:"text-center"},ae={class:"text-sm font-semibold text-gray-900"},oe={class:"bg-white rounded-lg shadow-sm"},ie={key:0,class:"p-6 text-center text-gray-500"},ne={key:1,class:"divide-y divide-gray-200"},re={class:"flex items-center space-x-4"},le={class:"flex-shrink-0"},ce={class:"flex-1"},de={class:"flex items-center justify-between"},me={class:"font-medium text-gray-900"},ue={class:"text-sm text-gray-500"},_e={class:"text-sm text-gray-600 mt-1"},xe={class:"text-xs text-gray-500 mt-1"};function fe(f,t,u,o,p,v){const l=y("HeroIcon"),d=y("router-link"),g=y("DashboardTemplate");return n(),V(g,{title:"Dashboard Certificazioni",subtitle:"Gestisci e monitora le certificazioni aziendali",stats:o.statsData,onRefresh:o.loadDashboardData},{"header-actions":m(()=>[e("div",H,[i(d,{to:"/app/certifications/readiness",class:"btn-secondary flex items-center gap-2"},{default:m(()=>[i(l,{name:"chart-bar",size:"sm"}),t[0]||(t[0]=_(" Valuta Preparazione "))]),_:1,__:[0]}),i(d,{to:"/app/certifications/catalog",class:"btn-secondary flex items-center gap-2"},{default:m(()=>[i(l,{name:"book-open",size:"sm"}),t[1]||(t[1]=_(" Esplora Catalogo "))]),_:1,__:[1]}),i(d,{to:"/app/certifications/create",class:"btn-primary flex items-center gap-2"},{default:m(()=>[i(l,{name:"plus",size:"sm"}),t[2]||(t[2]=_(" Nuova Certificazione "))]),_:1,__:[2]})])]),widget:m(()=>[e("div",E,[e("div",L,[e("div",R,[t[4]||(t[4]=e("h3",{class:"text-lg font-semibold text-gray-900"},"Certificazioni Attive",-1)),i(d,{to:"/app/certifications/list",class:"text-blue-600 hover:text-blue-800 font-medium"},{default:m(()=>t[3]||(t[3]=[_(" Vedi tutte → ")])),_:1,__:[3]})]),o.loading?(n(),c("div",P,t[5]||(t[5]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1)]))):o.certifications.length===0?(n(),c("div",q,[e("div",G,[i(l,{name:"shield-check",class:"h-12 w-12 text-gray-400 mx-auto mb-4"}),t[8]||(t[8]=e("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"Nessuna Certificazione",-1)),t[9]||(t[9]=e("p",{class:"text-gray-500 mb-6"},"Inizia subito creando la tua prima certificazione aziendale",-1)),e("div",M,[i(d,{to:"/app/certifications/catalog",class:"btn-secondary flex items-center gap-2"},{default:m(()=>[i(l,{name:"book-open",size:"sm"}),t[6]||(t[6]=_(" Esplora Catalogo "))]),_:1,__:[6]}),i(d,{to:"/app/certifications/readiness",class:"btn-primary flex items-center gap-2"},{default:m(()=>[i(l,{name:"chart-bar",size:"sm"}),t[7]||(t[7]=_(" Valuta Preparazione "))]),_:1,__:[7]})])])])):(n(),c("div",O,[(n(!0),c(w,null,z(o.certifications.slice(0,5),s=>(n(),c("div",{key:s.id,class:"p-6 hover:bg-gray-50 transition-colors cursor-pointer",onClick:h=>f.$router.push(`/app/certifications/${s.id}`)},[e("div",K,[e("div",Q,[e("div",U,[e("h4",W,r(s.standard_name),1),e("span",{class:C([o.getStatusBadgeClass(s.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},r(o.getStatusLabel(s.status)),3)]),e("p",X,r(s.certifying_body),1),e("div",Y,[e("span",null,"Scadenza: "+r(o.formatDate(s.expiry_date)),1),s.responsible_person?(n(),c("span",Z," Responsabile: "+r(s.responsible_person.name),1)):T("",!0)])]),e("div",$,[e("div",ee,[e("div",te,r(s.health_score||"N/A")+"%",1),t[10]||(t[10]=e("div",{class:"text-xs text-gray-500"},"Health Score",-1))]),e("div",se,[e("div",ae,r(o.formatDate(s.next_audit_date)),1),t[11]||(t[11]=e("div",{class:"text-xs text-gray-500"},"Prossimo Audit",-1))])])])],8,J))),128))]))]),e("div",oe,[t[12]||(t[12]=e("div",{class:"px-6 py-4 border-b border-gray-200"},[e("h3",{class:"text-lg font-semibold text-gray-900"},"Azioni in Scadenza")],-1)),o.upcomingActions.length===0?(n(),c("div",ie," Nessuna azione in scadenza ")):(n(),c("div",ne,[(n(!0),c(w,null,z(o.upcomingActions.slice(0,5),s=>(n(),c("div",{key:s.id,class:"p-6 hover:bg-gray-50 transition-colors"},[e("div",re,[e("div",le,[i(l,{name:o.getActionIcon(s.type),class:C(o.getActionIconClass(s.priority))},null,8,["name","class"])]),e("div",ce,[e("div",de,[e("h4",me,r(s.title),1),e("span",ue,r(o.formatDate(s.due_date)),1)]),e("p",_e,r(s.description),1),e("p",xe,r(s.certification_name),1)])])]))),128))]))])])]),_:1},8,["stats","onRefresh"])}const ve=I(F,[["render",fe]]);export{ve as default};
