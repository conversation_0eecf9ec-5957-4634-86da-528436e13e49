import{c as B,b as o,j as s,e as m,v as _,t as p,n as D,r as h,w as L,o as H,l as e,A as G,g as x,B as w,C as q,S as R,F as $,q as S,x as I,P as O,f as N}from"./vendor.js";import{u as K,_ as P}from"./ConfidenceBadge.js";import{H as f}from"./app.js";import{P as J}from"./Pagination.js";const Q={__name:"CategoryBadge",props:{category:{type:String,required:!0}},setup(T){const k=T,c={contracts:{label:"Contratti",icon:"document-text",classes:"bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400"},onboarding:{label:"Onboarding",icon:"user-plus",classes:"bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400"},offboarding:{label:"Offboarding",icon:"user-minus",classes:"bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400"},leave:{label:"Ferie",icon:"calendar-days",classes:"bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400"},permits:{label:"Permessi",icon:"clock",classes:"bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400"},travel:{label:"Trasferte",icon:"map-pin",classes:"bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400"},benefits:{label:"Benefit",icon:"gift",classes:"bg-pink-100 text-pink-800 dark:bg-pink-900/20 dark:text-pink-400"},tools:{label:"Strumenti",icon:"computer-desktop",classes:"bg-cyan-100 text-cyan-800 dark:bg-cyan-900/20 dark:text-cyan-400"},purchases:{label:"Acquisti",icon:"shopping-cart",classes:"bg-emerald-100 text-emerald-800 dark:bg-emerald-900/20 dark:text-emerald-400"},training:{label:"Formazione",icon:"academic-cap",classes:"bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400"}},A=B(()=>{var u;return((u=c[k.category])==null?void 0:u.label)||k.category}),d=B(()=>{var u;return((u=c[k.category])==null?void 0:u.icon)||"tag"}),b=B(()=>{var u;return((u=c[k.category])==null?void 0:u.classes)||"bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400"});return(u,C)=>(s(),o("span",{class:D(["inline-flex items-center px-2 py-1 rounded-full text-xs font-medium",b.value])},[m(f,{name:d.value,class:"h-3 w-3 mr-1"},null,8,["name"]),_(" "+p(A.value),1)],2))}},W={class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},X={class:"relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white dark:bg-gray-800"},Y={class:"flex items-center justify-between mb-6"},Z={class:"text-lg font-medium text-gray-900 dark:text-white"},ee=["value"],te={key:0,class:"flex items-center space-x-3"},ae={key:1},re={key:2},se=["value"],le=["required","placeholder","readonly"],oe={key:3,class:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4"},ne={class:"flex items-center space-x-2 mb-2"},ie={key:0,class:"text-xs text-blue-700 dark:text-blue-300"},ue={class:"list-disc list-inside space-y-1"},de={class:"flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-600"},ge=["disabled"],ce={key:0,class:"flex items-center"},be={key:1},ye={__name:"KnowledgeBaseModal",props:{entry:{type:Object,default:null}},emits:["close","save"],setup(T,{emit:k}){const c=T,A=k,d=K(),b=h(!1),u=h(null),C=h([]),a=h({title:"",category:"",content:"",useAIAssistance:!1,requirements:"",templateId:null,tags:[]}),v=h(""),y=B(()=>!!c.entry),{categoriesList:M}=d,U=B(()=>a.value.category?C.value.filter(i=>i.category===a.value.category):[]);L(()=>a.value.category,async i=>{i&&a.value.useAIAssistance&&await V(i)});async function V(i=null){const t=await d.loadTemplates(i);t.success&&(C.value=t.data)}async function z(){b.value=!0;try{a.value.tags=v.value.split(",").map(t=>t.trim()).filter(t=>t.length>0);let i;y.value?i=await d.updateKnowledgeEntry(c.entry.id,{title:a.value.title,category:a.value.category,content:a.value.content,tags:a.value.tags}):a.value.useAIAssistance?(i=await d.generateAIContent({title:a.value.title,category:a.value.category,content:a.value.content,requirements:a.value.requirements,templateId:a.value.templateId}),i.success&&(u.value=i.data,a.value.content=i.data.content)):i=await d.createKnowledgeEntry({title:a.value.title,category:a.value.category,content:a.value.content,tags:a.value.tags}),i.success&&A("save")}catch(i){console.error("Error saving knowledge entry:",i)}finally{b.value=!1}}return H(async()=>{y.value&&(a.value={title:c.entry.title,category:c.entry.category,content:c.entry.content,useAIAssistance:!1,requirements:"",templateId:null,tags:c.entry.tags||[]},v.value=a.value.tags.join(", ")),await V()}),(i,t)=>(s(),o("div",W,[e("div",X,[e("div",Y,[e("h3",Z,p(y.value?"Modifica Contenuto":"Nuovo Contenuto HR"),1),e("button",{onClick:t[0]||(t[0]=l=>i.$emit("close")),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},[m(f,{name:"x-mark",class:"h-6 w-6"})])]),e("form",{onSubmit:G(z,["prevent"]),class:"space-y-6"},[e("div",null,[t[9]||(t[9]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Titolo * ",-1)),w(e("input",{"onUpdate:modelValue":t[1]||(t[1]=l=>a.value.title=l),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Inserisci il titolo del contenuto"},null,512),[[q,a.value.title]])]),e("div",null,[t[11]||(t[11]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Categoria * ",-1)),w(e("select",{"onUpdate:modelValue":t[2]||(t[2]=l=>a.value.category=l),required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},[t[10]||(t[10]=e("option",{value:""},"Seleziona categoria",-1)),(s(!0),o($,null,S(I(M),l=>(s(),o("option",{key:l.key,value:l.key},p(l.label),9,ee))),128))],512),[[R,a.value.category]])]),y.value?x("",!0):(s(),o("div",te,[w(e("input",{id:"useAI","onUpdate:modelValue":t[3]||(t[3]=l=>a.value.useAIAssistance=l),type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,512),[[O,a.value.useAIAssistance]]),t[12]||(t[12]=e("label",{for:"useAI",class:"text-sm font-medium text-gray-700 dark:text-gray-300"}," Usa assistenza AI per generare contenuto ",-1))])),a.value.useAIAssistance&&!y.value?(s(),o("div",ae,[t[13]||(t[13]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Requisiti specifici per l'AI ",-1)),w(e("textarea",{"onUpdate:modelValue":t[4]||(t[4]=l=>a.value.requirements=l),rows:"3",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Descrivi cosa deve includere il contenuto (es: policy aziendale, CCNL, procedure specifiche...)"},null,512),[[q,a.value.requirements]])])):x("",!0),a.value.useAIAssistance&&!y.value&&C.value.length>0?(s(),o("div",re,[t[15]||(t[15]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Template (opzionale) ",-1)),w(e("select",{"onUpdate:modelValue":t[5]||(t[5]=l=>a.value.templateId=l),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},[t[14]||(t[14]=e("option",{value:""},"Nessun template",-1)),(s(!0),o($,null,S(U.value,l=>(s(),o("option",{key:l.id,value:l.id},p(l.name),9,se))),128))],512),[[R,a.value.templateId]])])):x("",!0),e("div",null,[t[16]||(t[16]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Contenuto * ",-1)),w(e("textarea",{"onUpdate:modelValue":t[6]||(t[6]=l=>a.value.content=l),required:!a.value.useAIAssistance,rows:"10",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:a.value.useAIAssistance?"Il contenuto verrà generato automaticamente dall'AI":"Inserisci il contenuto...",readonly:a.value.useAIAssistance&&!y.value},null,8,le),[[q,a.value.content]])]),e("div",null,[t[17]||(t[17]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Tag (separati da virgola) ",-1)),w(e("input",{"onUpdate:modelValue":t[7]||(t[7]=l=>v.value=l),type:"text",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"tag1, tag2, tag3"},null,512),[[q,v.value]])]),u.value?(s(),o("div",oe,[e("div",ne,[m(f,{name:"cpu-chip",class:"h-5 w-5 text-blue-600 dark:text-blue-400"}),t[18]||(t[18]=e("span",{class:"text-sm font-medium text-blue-900 dark:text-blue-300"}," Contenuto generato con AI ",-1)),m(P,{confidence:u.value.confidence},null,8,["confidence"])]),u.value.sources&&u.value.sources.length>0?(s(),o("div",ie,[t[19]||(t[19]=e("p",{class:"font-medium mb-1"},"Fonti utilizzate:",-1)),e("ul",ue,[(s(!0),o($,null,S(u.value.sources,l=>(s(),o("li",{key:l},p(l),1))),128))])])):x("",!0)])):x("",!0),e("div",de,[e("button",{type:"button",onClick:t[8]||(t[8]=l=>i.$emit("close")),class:"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"}," Annulla "),e("button",{type:"submit",disabled:b.value||!a.value.content&&!a.value.useAIAssistance,class:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"},[b.value?(s(),o("div",ce,[t[20]||(t[20]=e("div",{class:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"},null,-1)),_(" "+p(a.value.useAIAssistance&&!y.value?"Generando...":"Salvando..."),1)])):(s(),o("span",be,p(y.value?"Aggiorna":"Salva"),1))],8,ge)])],32)])]))}},me={class:"space-y-6"},pe={class:"flex justify-between items-center"},ve={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},xe={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},fe=["value"],ke={class:"bg-white dark:bg-gray-800 rounded-lg shadow"},we={class:"divide-y divide-gray-200 dark:divide-gray-700"},he={class:"flex items-start justify-between"},Ae={class:"flex-1"},Ce={class:"flex items-center space-x-3 mb-2"},Ie={class:"text-lg font-medium text-gray-900 dark:text-white"},_e={key:0,class:"flex items-center space-x-1"},$e={class:"text-gray-600 dark:text-gray-400 mb-3 line-clamp-2"},Se={class:"flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400"},qe={key:0,class:"mt-2"},Be={class:"flex flex-wrap gap-1"},Te={class:"flex items-center space-x-2 ml-4"},Ve=["onClick"],ze=["onClick"],Ee={key:0,class:"text-center py-12"},Me={key:1,class:"text-center py-12"},He={__name:"HRKnowledgeBase",setup(T){const k=K(),c=h(!1),A=h(null),d=h({category:"",search:"",page:1}),b=h(null),{knowledgeBase:u,categoriesList:C,loading:a}=k;async function v(){const g=await k.loadKnowledgeBase(d.value);g.success&&(b.value=g.data.pagination)}function y(){clearTimeout(y.timer),y.timer=setTimeout(()=>{d.value.page=1,v()},500)}function M(){d.value={category:"",search:"",page:1},v()}function U(g){A.value=g}async function V(g){if(!confirm(`Sei sicuro di voler eliminare "${g.title}"?`))return;(await k.deleteKnowledgeEntry(g.id)).success&&await v()}function z(){c.value=!1,A.value=null}async function i(){await v(),z()}function t(g){d.value.page=g,v()}function l(g,r=150){return g.length<=r?g:g.substring(0,r)+"..."}function j(g){return new Date(g).toLocaleDateString("it-IT",{year:"numeric",month:"short",day:"numeric"})}return H(()=>{v()}),(g,r)=>(s(),o("div",me,[e("div",pe,[r[5]||(r[5]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"}," Knowledge Base HR "),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Gestisci contenuti per l'assistente HR ")],-1)),e("button",{onClick:r[0]||(r[0]=n=>c.value=!0),class:"inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"},[m(f,{name:"plus",class:"h-4 w-4 mr-2"}),r[4]||(r[4]=_(" Nuovo Contenuto "))])]),e("div",ve,[e("div",xe,[e("div",null,[r[7]||(r[7]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Categoria ",-1)),w(e("select",{"onUpdate:modelValue":r[1]||(r[1]=n=>d.value.category=n),onChange:v,class:"w-full rounded-lg border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},[r[6]||(r[6]=e("option",{value:""},"Tutte le categorie",-1)),(s(!0),o($,null,S(I(C),n=>(s(),o("option",{key:n.key,value:n.key},p(n.label),9,fe))),128))],544),[[R,d.value.category]])]),e("div",null,[r[8]||(r[8]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Ricerca ",-1)),w(e("input",{"onUpdate:modelValue":r[2]||(r[2]=n=>d.value.search=n),onInput:y,type:"text",placeholder:"Cerca nei contenuti...",class:"w-full rounded-lg border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},null,544),[[q,d.value.search]])]),e("div",{class:"flex items-end"},[e("button",{onClick:M,class:"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"}," Pulisci Filtri ")])])]),e("div",ke,[e("div",we,[(s(!0),o($,null,S(I(u),n=>{var F;return s(),o("div",{key:n.id,class:"p-6 hover:bg-gray-50 dark:hover:bg-gray-700"},[e("div",he,[e("div",Ae,[e("div",Ce,[e("h3",Ie,p(n.title),1),m(Q,{category:n.category},null,8,["category"]),n.created_with_ai?(s(),o("div",_e,[m(f,{name:"cpu-chip",class:"h-4 w-4 text-purple-500"}),r[9]||(r[9]=e("span",{class:"text-xs text-purple-600 dark:text-purple-400"}," AI-Generated ",-1))])):x("",!0)]),e("p",$e,p(l(n.content)),1),e("div",Se,[e("span",null,[m(f,{name:"user",class:"h-4 w-4 inline mr-1"}),_(" "+p((F=n.creator)==null?void 0:F.full_name),1)]),e("span",null,[m(f,{name:"calendar",class:"h-4 w-4 inline mr-1"}),_(" "+p(j(n.created_at)),1)]),n.ai_confidence?(s(),N(P,{key:0,confidence:n.ai_confidence},null,8,["confidence"])):x("",!0)]),n.tags&&n.tags.length>0?(s(),o("div",qe,[e("div",Be,[(s(!0),o($,null,S(n.tags,E=>(s(),o("span",{key:E,class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300"},p(E),1))),128))])])):x("",!0)]),e("div",Te,[e("button",{onClick:E=>U(n),class:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600",title:"Modifica"},[m(f,{name:"pencil",class:"h-4 w-4"})],8,Ve),e("button",{onClick:E=>V(n),class:"p-2 text-red-400 hover:text-red-600 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20",title:"Elimina"},[m(f,{name:"trash",class:"h-4 w-4"})],8,ze)])])])}),128))]),I(u).length===0&&!I(a)?(s(),o("div",Ee,[m(f,{name:"document-text",class:"h-16 w-16 text-gray-300 dark:text-gray-600 mx-auto mb-4"}),r[11]||(r[11]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"}," Nessun contenuto trovato ",-1)),r[12]||(r[12]=e("p",{class:"text-gray-500 dark:text-gray-400 mb-6"}," Inizia creando il primo contenuto per la knowledge base HR. ",-1)),e("button",{onClick:r[3]||(r[3]=n=>c.value=!0),class:"inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700"},[m(f,{name:"plus",class:"h-4 w-4 mr-2"}),r[10]||(r[10]=_(" Crea Primo Contenuto "))])])):x("",!0),I(a)?(s(),o("div",Me,r[13]||(r[13]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"},null,-1),e("p",{class:"mt-2 text-gray-500 dark:text-gray-400"},"Caricamento...",-1)]))):x("",!0)]),b.value&&b.value.pages>1?(s(),N(J,{key:0,"current-page":b.value.page,"total-pages":b.value.pages,"total-items":b.value.total,onPageChange:t},null,8,["current-page","total-pages","total-items"])):x("",!0),c.value||A.value?(s(),N(ye,{key:1,entry:A.value,onClose:z,onSave:i},null,8,["entry"])):x("",!0)]))}};export{He as default};
