import{c as B,b as l,j as s,e as g,v as _,t as v,n as G,r as f,w as H,o as K,l as e,A as L,g as b,B as k,C as q,S as N,F as I,q as $,x as C,P as O,f as j}from"./vendor.js";import{u as P,_ as D}from"./ConfidenceBadge.js";import{H as m}from"./app.js";import{P as J}from"./Pagination.js";const Q={__name:"CategoryBadge",props:{category:{type:String,required:!0}},setup(T){const x=T,c={contracts:{label:"Contratti",icon:"document-text",classes:"bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400"},onboarding:{label:"Onboarding",icon:"user-plus",classes:"bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400"},offboarding:{label:"Offboarding",icon:"user-minus",classes:"bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400"},leave:{label:"Ferie",icon:"calendar-days",classes:"bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400"},permits:{label:"Permessi",icon:"clock",classes:"bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400"},travel:{label:"Trasferte",icon:"map-pin",classes:"bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400"},benefits:{label:"Benefit",icon:"gift",classes:"bg-pink-100 text-pink-800 dark:bg-pink-900/20 dark:text-pink-400"},tools:{label:"Strumenti",icon:"computer-desktop",classes:"bg-cyan-100 text-cyan-800 dark:bg-cyan-900/20 dark:text-cyan-400"},purchases:{label:"Acquisti",icon:"shopping-cart",classes:"bg-emerald-100 text-emerald-800 dark:bg-emerald-900/20 dark:text-emerald-400"},training:{label:"Formazione",icon:"academic-cap",classes:"bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400"}},w=B(()=>{var u;return((u=c[x.category])==null?void 0:u.label)||x.category}),d=B(()=>{var u;return((u=c[x.category])==null?void 0:u.icon)||"tag"}),y=B(()=>{var u;return((u=c[x.category])==null?void 0:u.classes)||"bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400"});return(u,i)=>(s(),l("span",{class:G(["inline-flex items-center px-2 py-1 rounded-full text-xs font-medium",y.value])},[g(m,{name:d.value,class:"h-3 w-3 mr-1"},null,8,["name"]),_(" "+v(w.value),1)],2))}},W={class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},X={class:"relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white dark:bg-gray-800"},Y={class:"flex items-center justify-between mb-6"},Z={class:"text-lg font-medium text-gray-900 dark:text-white"},ee=["value"],te={key:0,class:"flex items-center space-x-3"},ae={key:1},re={key:2},se=["value"],le={key:3,class:"flex justify-center"},ne=["disabled"],oe={key:0,class:"flex items-center"},ie={key:1},ue={class:"flex items-center justify-between mb-2"},de=["disabled"],ce=["required","placeholder"],ge={key:0,class:"mt-2 text-xs text-gray-500 dark:text-gray-400"},be={key:4,class:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4"},ye={class:"flex items-center space-x-2 mb-2"},me={key:0,class:"text-xs text-blue-700 dark:text-blue-300"},ve={class:"list-disc list-inside space-y-1"},pe={class:"flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-600"},xe=["disabled"],fe={key:0,class:"flex items-center"},ke={key:1},we={__name:"KnowledgeBaseModal",props:{entry:{type:Object,default:null}},emits:["close","save"],setup(T,{emit:x}){const c=T,w=x,d=P(),y=f(!1),u=f(!1),i=f(null),A=f([]),r=f({title:"",category:"",content:"",useAIAssistance:!1,requirements:"",templateId:null,tags:[]}),h=f(""),p=B(()=>!!c.entry),{categoriesList:E}=d,M=B(()=>r.value.category?A.value.filter(o=>o.category===r.value.category):[]);H(()=>r.value.category,async o=>{o&&r.value.useAIAssistance&&await S(o)}),H(()=>r.value.useAIAssistance,o=>{o||(i.value=null,r.value.requirements="",r.value.templateId=null)});async function S(o=null){const t=await d.loadTemplates(o);t.success&&(A.value=t.data)}async function V(){if(!(!r.value.title||!r.value.category)){u.value=!0;try{const o=await d.generateAIContent({title:r.value.title,category:r.value.category,content:r.value.content||"",requirements:r.value.requirements,templateId:r.value.templateId});o.success&&(i.value=o.data,r.value.content=o.data.content)}catch(o){console.error("Error generating AI content:",o)}finally{u.value=!1}}}async function R(){i.value=null,await V()}async function U(){var o,t;y.value=!0;try{r.value.tags=h.value.split(",").map(n=>n.trim()).filter(n=>n.length>0);let a;p.value?a=await d.updateKnowledgeEntry(c.entry.id,{title:r.value.title,category:r.value.category,content:r.value.content,tags:r.value.tags}):a=await d.createKnowledgeEntry({title:r.value.title,category:r.value.category,content:r.value.content,tags:r.value.tags,ai_generated:!!i.value,ai_confidence:(o=i.value)==null?void 0:o.confidence,ai_sources:(t=i.value)==null?void 0:t.sources}),a.success&&w("save")}catch(a){console.error("Error saving knowledge entry:",a)}finally{y.value=!1}}return K(async()=>{p.value&&(r.value={title:c.entry.title,category:c.entry.category,content:c.entry.content,useAIAssistance:!1,requirements:"",templateId:null,tags:c.entry.tags||[]},h.value=r.value.tags.join(", ")),await S()}),(o,t)=>(s(),l("div",W,[e("div",X,[e("div",Y,[e("h3",Z,v(p.value?"Modifica Contenuto":"Nuovo Contenuto HR"),1),e("button",{onClick:t[0]||(t[0]=a=>o.$emit("close")),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},[g(m,{name:"x-mark",class:"h-6 w-6"})])]),e("form",{onSubmit:L(U,["prevent"]),class:"space-y-6"},[e("div",null,[t[9]||(t[9]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Titolo * ",-1)),k(e("input",{"onUpdate:modelValue":t[1]||(t[1]=a=>r.value.title=a),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Inserisci il titolo del contenuto"},null,512),[[q,r.value.title]])]),e("div",null,[t[11]||(t[11]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Categoria * ",-1)),k(e("select",{"onUpdate:modelValue":t[2]||(t[2]=a=>r.value.category=a),required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},[t[10]||(t[10]=e("option",{value:""},"Seleziona categoria",-1)),(s(!0),l(I,null,$(C(E),a=>(s(),l("option",{key:a.key,value:a.key},v(a.label),9,ee))),128))],512),[[N,r.value.category]])]),p.value?b("",!0):(s(),l("div",te,[k(e("input",{id:"useAI","onUpdate:modelValue":t[3]||(t[3]=a=>r.value.useAIAssistance=a),type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,512),[[O,r.value.useAIAssistance]]),t[12]||(t[12]=e("label",{for:"useAI",class:"text-sm font-medium text-gray-700 dark:text-gray-300"}," Usa assistenza AI per generare contenuto ",-1))])),r.value.useAIAssistance&&!p.value?(s(),l("div",ae,[t[13]||(t[13]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Requisiti specifici per l'AI ",-1)),k(e("textarea",{"onUpdate:modelValue":t[4]||(t[4]=a=>r.value.requirements=a),rows:"3",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Descrivi cosa deve includere il contenuto (es: policy aziendale, CCNL, procedure specifiche...)"},null,512),[[q,r.value.requirements]])])):b("",!0),r.value.useAIAssistance&&!p.value&&A.value.length>0?(s(),l("div",re,[t[15]||(t[15]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Template (opzionale) ",-1)),k(e("select",{"onUpdate:modelValue":t[5]||(t[5]=a=>r.value.templateId=a),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},[t[14]||(t[14]=e("option",{value:""},"Nessun template",-1)),(s(!0),l(I,null,$(M.value,a=>(s(),l("option",{key:a.id,value:a.id},v(a.name),9,se))),128))],512),[[N,r.value.templateId]])])):b("",!0),r.value.useAIAssistance&&!p.value&&!i.value&&r.value.title&&r.value.category?(s(),l("div",le,[e("button",{type:"button",onClick:V,disabled:u.value,class:"inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium rounded-lg focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"},[g(m,{name:"cpu-chip",class:"h-5 w-5 mr-2"}),u.value?(s(),l("div",oe,t[16]||(t[16]=[e("div",{class:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"},null,-1),_(" Generando contenuto... ")]))):(s(),l("span",ie,"Genera Contenuto con AI"))],8,ne)])):b("",!0),e("div",null,[e("div",ue,[t[18]||(t[18]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Contenuto * ",-1)),r.value.useAIAssistance&&!p.value&&i.value?(s(),l("button",{key:0,type:"button",onClick:R,disabled:u.value,class:"inline-flex items-center px-3 py-1 text-sm bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded-md hover:bg-blue-200 dark:hover:bg-blue-800 disabled:opacity-50"},[g(m,{name:"arrow-path",class:"h-4 w-4 mr-1"}),t[17]||(t[17]=_(" Rigenera "))],8,de)):b("",!0)]),k(e("textarea",{"onUpdate:modelValue":t[6]||(t[6]=a=>r.value.content=a),required:!r.value.useAIAssistance||!!i.value,rows:"10",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:r.value.useAIAssistance&&!i.value?"Clicca su 'Genera Contenuto con AI' per creare una bozza automatica, poi modifica secondo le tue necessità":"Inserisci il contenuto..."},null,8,ce),[[q,r.value.content]]),r.value.useAIAssistance&&i.value?(s(),l("div",ge," 💡 Contenuto generato con AI - puoi modificarlo liberamente prima di salvare ")):b("",!0)]),e("div",null,[t[19]||(t[19]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Tag (separati da virgola) ",-1)),k(e("input",{"onUpdate:modelValue":t[7]||(t[7]=a=>h.value=a),type:"text",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"tag1, tag2, tag3"},null,512),[[q,h.value]])]),i.value?(s(),l("div",be,[e("div",ye,[g(m,{name:"cpu-chip",class:"h-5 w-5 text-blue-600 dark:text-blue-400"}),t[20]||(t[20]=e("span",{class:"text-sm font-medium text-blue-900 dark:text-blue-300"}," Contenuto generato con AI ",-1)),g(D,{confidence:i.value.confidence},null,8,["confidence"])]),i.value.sources&&i.value.sources.length>0?(s(),l("div",me,[t[21]||(t[21]=e("p",{class:"font-medium mb-1"},"Fonti utilizzate:",-1)),e("ul",ve,[(s(!0),l(I,null,$(i.value.sources,a=>(s(),l("li",{key:a},v(a),1))),128))])])):b("",!0)])):b("",!0),e("div",pe,[e("button",{type:"button",onClick:t[8]||(t[8]=a=>o.$emit("close")),class:"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"}," Annulla "),e("button",{type:"submit",disabled:y.value||!r.value.content,class:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"},[y.value?(s(),l("div",fe,t[22]||(t[22]=[e("div",{class:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"},null,-1),_(" Salvando... ")]))):(s(),l("span",ke,v(p.value?"Aggiorna":"Salva"),1))],8,xe)])],32)])]))}},he={class:"space-y-6"},_e={class:"flex justify-between items-center"},Ae={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},Ce={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},Ie=["value"],$e={class:"bg-white dark:bg-gray-800 rounded-lg shadow"},Se={class:"divide-y divide-gray-200 dark:divide-gray-700"},qe={class:"flex items-start justify-between"},Be={class:"flex-1"},Te={class:"flex items-center space-x-3 mb-2"},Ve={class:"text-lg font-medium text-gray-900 dark:text-white"},ze={key:0,class:"flex items-center space-x-1"},Ee={class:"text-gray-600 dark:text-gray-400 mb-3 line-clamp-2"},Me={class:"flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400"},Re={key:0,class:"mt-2"},Ue={class:"flex flex-wrap gap-1"},je={class:"flex items-center space-x-2 ml-4"},Ne=["onClick"],Fe=["onClick"],He={key:0,class:"text-center py-12"},Ke={key:1,class:"text-center py-12"},Oe={__name:"HRKnowledgeBase",setup(T){const x=P(),c=f(!1),w=f(null),d=f({category:"",search:"",page:1}),y=f(null),{knowledgeBase:u,categoriesList:i,loading:A}=x;async function r(){const t=await x.loadKnowledgeBase(d.value);t.success&&(y.value=t.data.pagination)}function h(){clearTimeout(h.timer),h.timer=setTimeout(()=>{d.value.page=1,r()},500)}function p(){d.value={category:"",search:"",page:1},r()}function E(t){w.value=t}async function M(t){if(!confirm(`Sei sicuro di voler eliminare "${t.title}"?`))return;(await x.deleteKnowledgeEntry(t.id)).success&&await r()}function S(){c.value=!1,w.value=null}async function V(){await r(),S()}function R(t){d.value.page=t,r()}function U(t,a=150){return t.length<=a?t:t.substring(0,a)+"..."}function o(t){return new Date(t).toLocaleDateString("it-IT",{year:"numeric",month:"short",day:"numeric"})}return K(()=>{r()}),(t,a)=>(s(),l("div",he,[e("div",_e,[a[5]||(a[5]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"}," Knowledge Base HR "),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Gestisci contenuti per l'assistente HR ")],-1)),e("button",{onClick:a[0]||(a[0]=n=>c.value=!0),class:"inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"},[g(m,{name:"plus",class:"h-4 w-4 mr-2"}),a[4]||(a[4]=_(" Nuovo Contenuto "))])]),e("div",Ae,[e("div",Ce,[e("div",null,[a[7]||(a[7]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Categoria ",-1)),k(e("select",{"onUpdate:modelValue":a[1]||(a[1]=n=>d.value.category=n),onChange:r,class:"w-full rounded-lg border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},[a[6]||(a[6]=e("option",{value:""},"Tutte le categorie",-1)),(s(!0),l(I,null,$(C(i),n=>(s(),l("option",{key:n.key,value:n.key},v(n.label),9,Ie))),128))],544),[[N,d.value.category]])]),e("div",null,[a[8]||(a[8]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Ricerca ",-1)),k(e("input",{"onUpdate:modelValue":a[2]||(a[2]=n=>d.value.search=n),onInput:h,type:"text",placeholder:"Cerca nei contenuti...",class:"w-full rounded-lg border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},null,544),[[q,d.value.search]])]),e("div",{class:"flex items-end"},[e("button",{onClick:p,class:"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"}," Pulisci Filtri ")])])]),e("div",$e,[e("div",Se,[(s(!0),l(I,null,$(C(u),n=>{var F;return s(),l("div",{key:n.id,class:"p-6 hover:bg-gray-50 dark:hover:bg-gray-700"},[e("div",qe,[e("div",Be,[e("div",Te,[e("h3",Ve,v(n.title),1),g(Q,{category:n.category},null,8,["category"]),n.created_with_ai?(s(),l("div",ze,[g(m,{name:"cpu-chip",class:"h-4 w-4 text-purple-500"}),a[9]||(a[9]=e("span",{class:"text-xs text-purple-600 dark:text-purple-400"}," AI-Generated ",-1))])):b("",!0)]),e("p",Ee,v(U(n.content)),1),e("div",Me,[e("span",null,[g(m,{name:"user",class:"h-4 w-4 inline mr-1"}),_(" "+v((F=n.creator)==null?void 0:F.full_name),1)]),e("span",null,[g(m,{name:"calendar",class:"h-4 w-4 inline mr-1"}),_(" "+v(o(n.created_at)),1)]),n.ai_confidence?(s(),j(D,{key:0,confidence:n.ai_confidence},null,8,["confidence"])):b("",!0)]),n.tags&&n.tags.length>0?(s(),l("div",Re,[e("div",Ue,[(s(!0),l(I,null,$(n.tags,z=>(s(),l("span",{key:z,class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300"},v(z),1))),128))])])):b("",!0)]),e("div",je,[e("button",{onClick:z=>E(n),class:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600",title:"Modifica"},[g(m,{name:"pencil",class:"h-4 w-4"})],8,Ne),e("button",{onClick:z=>M(n),class:"p-2 text-red-400 hover:text-red-600 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20",title:"Elimina"},[g(m,{name:"trash",class:"h-4 w-4"})],8,Fe)])])])}),128))]),C(u).length===0&&!C(A)?(s(),l("div",He,[g(m,{name:"document-text",class:"h-16 w-16 text-gray-300 dark:text-gray-600 mx-auto mb-4"}),a[11]||(a[11]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"}," Nessun contenuto trovato ",-1)),a[12]||(a[12]=e("p",{class:"text-gray-500 dark:text-gray-400 mb-6"}," Inizia creando il primo contenuto per la knowledge base HR. ",-1)),e("button",{onClick:a[3]||(a[3]=n=>c.value=!0),class:"inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700"},[g(m,{name:"plus",class:"h-4 w-4 mr-2"}),a[10]||(a[10]=_(" Crea Primo Contenuto "))])])):b("",!0),C(A)?(s(),l("div",Ke,a[13]||(a[13]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"},null,-1),e("p",{class:"mt-2 text-gray-500 dark:text-gray-400"},"Caricamento...",-1)]))):b("",!0)]),y.value&&y.value.pages>1?(s(),j(J,{key:0,"current-page":y.value.page,"total-pages":y.value.pages,"total-items":y.value.total,onPageChange:R},null,8,["current-page","total-pages","total-items"])):b("",!0),c.value||w.value?(s(),j(we,{key:1,entry:w.value,onClose:S,onSave:V},null,8,["entry"])):b("",!0)]))}};export{Oe as default};
