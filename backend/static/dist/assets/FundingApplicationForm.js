import{r as g,c as j,o as te,f as oe,p as C,u as se,s as ae,j as d,l as e,b as u,g as D,e as i,v as c,t as n,F as v,q as y,x as z,B as p,P as re,C as f,S as B}from"./vendor.js";import{u as ne}from"./funding.js";import{u as ie}from"./useFormatters.js";import{u as le}from"./useToast.js";import{_ as de,c as N,H as l}from"./app.js";import{W as ue}from"./WizardContainer.js";import"./formatters.js";const me={class:"space-y-6"},ce={class:"block text-sm font-medium text-gray-700 mb-2"},pe=["value"],ge={class:"block text-sm font-medium text-gray-700 mb-2"},be={class:"block text-sm font-medium text-gray-700 mb-2"},fe={class:"text-xs text-gray-500 mt-1"},ve={class:"block text-sm font-medium text-gray-700 mb-2"},ye={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},xe={class:"block text-sm font-medium text-gray-700 mb-2"},_e=["value"],ze={class:"block text-sm font-medium text-gray-700 mb-2"},ke=["value"],we={class:"space-y-6"},he={class:"flex items-center justify-between mb-4"},je={class:"block text-sm font-medium text-gray-700"},Ce={class:"space-y-3"},Ve={class:"flex-1 grid grid-cols-1 md:grid-cols-3 gap-3"},Se=["onUpdate:modelValue","onChange"],Ue=["value"],De=["onUpdate:modelValue"],Be=["onUpdate:modelValue"],Pe=["onClick"],Fe={key:0,class:"text-center py-8 text-gray-500"},Ie={class:"space-y-6"},qe={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Ae={class:"block text-sm font-medium text-gray-700 mb-2"},Me={class:"block text-sm font-medium text-gray-700 mb-2"},Te={class:"bg-gray-50 rounded-lg p-4"},Re={class:"space-y-2 text-sm"},Ne={class:"flex justify-between"},Ee={class:"font-medium"},Oe={class:"flex justify-between"},$e={class:"font-medium"},Je={class:"border-t border-gray-300 pt-2 flex justify-between"},He={class:"font-semibold text-lg"},Le={class:"flex justify-between text-brand-primary-600"},We={class:"font-medium"},Ge={class:"flex items-center justify-between mb-4"},Ke={class:"block text-sm font-medium text-gray-700"},Qe={class:"space-y-3"},Xe={class:"flex-1 grid grid-cols-1 md:grid-cols-2 gap-3"},Ye=["onUpdate:modelValue"],Ze=["onUpdate:modelValue"],et=["onClick"],tt={key:0,class:"text-center py-6 text-gray-500"},ot={class:"space-y-6"},st={class:"bg-gray-50 rounded-lg p-6 text-center"},at={class:"space-y-6"},rt={class:"space-y-6"},nt={class:"bg-white border border-gray-200 rounded-lg p-4"},it={class:"text-md font-medium text-gray-900 mb-3 flex items-center"},lt={class:"space-y-2 text-sm"},dt={class:"flex justify-between"},ut={class:"font-medium"},mt={class:"flex justify-between"},ct={class:"font-medium"},pt={key:0,class:"flex justify-between"},gt={class:"font-medium"},bt={key:0,class:"bg-white border border-gray-200 rounded-lg p-4"},ft={class:"text-md font-medium text-gray-900 mb-3 flex items-center"},vt={class:"space-y-2"},yt={class:"text-gray-600"},xt={class:"bg-white border border-gray-200 rounded-lg p-4"},_t={class:"text-md font-medium text-gray-900 mb-3 flex items-center"},zt={class:"space-y-2 text-sm"},kt={class:"flex justify-between"},wt={class:"font-medium"},ht={class:"flex justify-between"},jt={class:"font-medium"},Ct={class:"border-t pt-2 flex justify-between"},Vt={class:"font-semibold"},St={class:"bg-blue-50 border border-blue-200 rounded-lg p-4"},Ut={class:"flex"},Dt={__name:"FundingApplicationForm",setup(Bt){const E=se(),O=ae(),V=ne(),{formatCurrency:x}=ie(),{showToast:k}=le(),A=g([{id:"basic",title:"Informazioni Base",description:"Progetto e dettagli"},{id:"team",title:"Team",description:"Composizione e competenze"},{id:"budget",title:"Budget",description:"Finanziamenti e costi"},{id:"documents",title:"Documenti",description:"Allegati richiesti"},{id:"review",title:"Riepilogo",description:"Verifica e invio"}]),w=g(0),h=g(!1),S=g(!1),s=g({opportunity_id:"",project_title:"",project_description:"",project_duration_months:12,project_manager_id:"",linked_project_id:"",requested_amount:0,co_financing_amount:0}),b=g([]),_=g([]),P=g([]),F=g([]),I=g([]),q=g([]),$=j(()=>{var r;return(r=A.value[w.value])==null?void 0:r.id}),M=j(()=>P.value.find(r=>r.id===s.value.opportunity_id)),U=j(()=>(s.value.requested_amount||0)+(s.value.co_financing_amount||0)),J=j(()=>U.value===0?0:Math.round((s.value.requested_amount||0)/U.value*100));function T(r,t){switch(t){case"basic":return s.value.opportunity_id&&s.value.project_title&&s.value.project_description&&s.value.project_description.length>=200&&s.value.project_duration_months&&s.value.project_manager_id;case"team":return!0;case"budget":return s.value.requested_amount>0;case"documents":return!0;case"review":return S.value;default:return!1}}const H=j(()=>T(w.value,$.value));function R(){return H.value&&S.value}function L(r){w.value=r}function W(){b.value.push({user_id:"",name:"",role:"",skills:""})}function G(r){b.value.splice(r,1)}function K(r){const t=b.value[r],a=q.value.find(o=>o.id===t.user_id);a&&(t.name=`${a.first_name} ${a.last_name}`,a.skills&&(t.skills=a.skills))}function Q(){_.value.push({category:"",amount:0})}function X(r){_.value.splice(r,1)}async function Y(){h.value=!0;try{const r={...s.value,team_composition:JSON.stringify(b.value),budget_breakdown:JSON.stringify(_.value),status:"draft"};console.log("Sending application data:",r),await V.createApplication(r),k("Bozza salvata con successo","success")}catch(r){console.error("Error saving draft:",r),k("Errore nel salvataggio della bozza","error")}finally{h.value=!1}}async function Z(){if(R.value){h.value=!0;try{const r={...s.value,team_composition:JSON.stringify(b.value),budget_breakdown:JSON.stringify(_.value),status:"submitted"};console.log("Submitting application data:",r);const t=await V.createApplication(r);k("Candidatura sottomessa con successo!","success"),O.push(`/app/funding/applications/${t.application.id}`)}catch(r){console.error("Error submitting application:",r),k("Errore nella sottomissione della candidatura","error")}finally{h.value=!1}}}async function ee(){try{await V.fetchOpportunities(),P.value=V.opportunities.filter(t=>t.status==="open"&&new Date(t.application_deadline)>new Date);try{const t=await N.get("/api/personnel/users");if(t.data.success){const a=t.data.data.users||[];F.value=a.filter(o=>o.role==="manager"||o.role==="admin"||o.is_active),q.value=a.filter(o=>o.is_active)}}catch{console.warn("Could not load users, using fallback"),F.value=[{id:1,first_name:"Mario",last_name:"Rossi"},{id:2,first_name:"Laura",last_name:"Bianchi"}]}try{const t=await N.get("/api/projects");t.data.success&&(I.value=t.data.data.projects||[])}catch{console.warn("Could not load projects, using fallback"),I.value=[{id:1,name:"Progetto Alpha"},{id:2,name:"Progetto Beta"}]}const r=E.params.opportunityId;r&&(s.value.opportunity_id=parseInt(r))}catch(r){console.error("Error loading form data:",r),k("Errore nel caricamento dei dati","error")}}return te(()=>{ee()}),(r,t)=>(d(),oe(ue,{steps:A.value,"current-step-index":w.value,"onUpdate:currentStepIndex":t[9]||(t[9]=a=>w.value=a),"is-step-valid":T,"is-form-valid":R,loading:h.value,"show-save-draft":!0,"can-save-draft":!0,"submit-button-text":"Sottometti Candidatura","save-draft-text":"Salva Bozza",onSubmit:Z,onSaveDraft:Y,onStepChange:L},{"step-basic":C(()=>{var a;return[e("div",me,[t[20]||(t[20]=e("div",{class:"border-b border-gray-200 pb-4 mb-6"},[e("h2",{class:"text-lg font-semibold text-gray-900"},"Informazioni Base del Progetto"),e("p",{class:"text-sm text-gray-600 mt-1"}," Descrivi il progetto per cui stai richiedendo il finanziamento ")],-1)),e("div",null,[e("label",ce,[i(l,{name:"clipboard-document",size:"sm",class:"inline mr-2"}),t[10]||(t[10]=c(" Bando di Riferimento * "))]),p(e("select",{"onUpdate:modelValue":t[0]||(t[0]=o=>s.value.opportunity_id=o),required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary-500 focus:border-brand-primary-500"},[t[11]||(t[11]=e("option",{value:""},"Seleziona un bando",-1)),(d(!0),u(v,null,y(P.value,o=>(d(),u("option",{key:o.id,value:o.id},n(o.title)+" - "+n(o.source_entity),9,pe))),128))],512),[[B,s.value.opportunity_id]])]),e("div",null,[e("label",ge,[i(l,{name:"document-text",size:"sm",class:"inline mr-2"}),t[12]||(t[12]=c(" Titolo del Progetto * "))]),p(e("input",{"onUpdate:modelValue":t[1]||(t[1]=o=>s.value.project_title=o),type:"text",required:"",placeholder:"Es. Sviluppo di una piattaforma AI per l'automazione dei processi",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary-500 focus:border-brand-primary-500"},null,512),[[f,s.value.project_title]])]),e("div",null,[e("label",be,[i(l,{name:"document-text",size:"sm",class:"inline mr-2"}),t[13]||(t[13]=c(" Descrizione del Progetto * "))]),p(e("textarea",{"onUpdate:modelValue":t[2]||(t[2]=o=>s.value.project_description=o),required:"",rows:"6",placeholder:"Descrivi dettagliatamente il progetto, gli obiettivi, la metodologia e i risultati attesi...",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary-500 focus:border-brand-primary-500"},null,512),[[f,s.value.project_description]]),e("p",fe," Almeno 200 caratteri. Attuale: "+n(((a=s.value.project_description)==null?void 0:a.length)||0),1)]),e("div",null,[e("label",ve,[i(l,{name:"clock",size:"sm",class:"inline mr-2"}),t[14]||(t[14]=c(" Durata Progetto (mesi) * "))]),p(e("input",{"onUpdate:modelValue":t[3]||(t[3]=o=>s.value.project_duration_months=o),type:"number",min:"1",max:"60",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary-500 focus:border-brand-primary-500"},null,512),[[f,s.value.project_duration_months,void 0,{number:!0}]])]),e("div",ye,[e("div",null,[e("label",xe,[i(l,{name:"user",size:"sm",class:"inline mr-2"}),t[15]||(t[15]=c(" Project Manager * "))]),p(e("select",{"onUpdate:modelValue":t[4]||(t[4]=o=>s.value.project_manager_id=o),required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary-500 focus:border-brand-primary-500"},[t[16]||(t[16]=e("option",{value:""},"Seleziona PM",-1)),(d(!0),u(v,null,y(F.value,o=>(d(),u("option",{key:o.id,value:o.id},n(o.first_name)+" "+n(o.last_name),9,_e))),128))],512),[[B,s.value.project_manager_id]])]),e("div",null,[e("label",ze,[i(l,{name:"link",size:"sm",class:"inline mr-2"}),t[17]||(t[17]=c(" Progetto Collegato (opzionale) "))]),p(e("select",{"onUpdate:modelValue":t[5]||(t[5]=o=>s.value.linked_project_id=o),class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary-500 focus:border-brand-primary-500"},[t[18]||(t[18]=e("option",{value:""},"Nessun progetto collegato",-1)),(d(!0),u(v,null,y(I.value,o=>(d(),u("option",{key:o.id,value:o.id},n(o.name),9,ke))),128))],512),[[B,s.value.linked_project_id]]),t[19]||(t[19]=e("p",{class:"text-xs text-gray-500 mt-1"}," Per rendicontazione automatica ore e costi ",-1))])])])]}),"step-team":C(()=>[e("div",we,[t[26]||(t[26]=e("div",{class:"border-b border-gray-200 pb-4 mb-6"},[e("h2",{class:"text-lg font-semibold text-gray-900"},"Team e Competenze"),e("p",{class:"text-sm text-gray-600 mt-1"}," Definisci il team che lavorerà al progetto e le competenze richieste ")],-1)),e("div",null,[e("div",he,[e("label",je,[i(l,{name:"user-group",size:"sm",class:"inline mr-2"}),t[21]||(t[21]=c(" Composizione del Team "))]),e("button",{type:"button",onClick:W,class:"btn-secondary text-sm"},[i(l,{name:"plus",size:"sm",class:"mr-1"}),t[22]||(t[22]=c(" Aggiungi Membro "))])]),e("div",Ce,[(d(!0),u(v,null,y(b.value,(a,o)=>(d(),u("div",{key:o,class:"flex items-center space-x-3 p-3 border border-gray-200 rounded-lg"},[e("div",Ve,[p(e("select",{"onUpdate:modelValue":m=>a.user_id=m,onChange:m=>K(o),class:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary-500 focus:border-brand-primary-500"},[t[23]||(t[23]=e("option",{value:""},"Seleziona dipendente",-1)),(d(!0),u(v,null,y(q.value,m=>(d(),u("option",{key:m.id,value:m.id},n(m.first_name)+" "+n(m.last_name),9,Ue))),128))],40,Se),[[B,a.user_id]]),p(e("input",{"onUpdate:modelValue":m=>a.role=m,type:"text",placeholder:"Ruolo nel progetto",class:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary-500 focus:border-brand-primary-500"},null,8,De),[[f,a.role]]),p(e("input",{"onUpdate:modelValue":m=>a.skills=m,type:"text",placeholder:"Competenze chiave",class:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary-500 focus:border-brand-primary-500"},null,8,Be),[[f,a.skills]])]),e("button",{type:"button",onClick:m=>G(o),class:"text-red-500 hover:text-red-700 p-1"},[i(l,{name:"trash",size:"sm"})],8,Pe)]))),128))]),b.value.length===0?(d(),u("div",Fe,[i(l,{name:"user-group",size:"lg",class:"mx-auto mb-2 text-gray-300"}),t[24]||(t[24]=e("p",null,"Nessun membro del team aggiunto",-1)),t[25]||(t[25]=e("p",{class:"text-sm"},"Aggiungi i membri che lavoreranno al progetto",-1))])):D("",!0)])])]),"step-budget":C(()=>[e("div",Ie,[t[38]||(t[38]=e("div",{class:"border-b border-gray-200 pb-4 mb-6"},[e("h2",{class:"text-lg font-semibold text-gray-900"},"Budget e Finanziamenti"),e("p",{class:"text-sm text-gray-600 mt-1"}," Definisci il budget del progetto e i dettagli del finanziamento richiesto ")],-1)),e("div",qe,[e("div",null,[e("label",Ae,[i(l,{name:"currency-euro",size:"sm",class:"inline mr-2"}),t[27]||(t[27]=c(" Importo Richiesto * "))]),p(e("input",{"onUpdate:modelValue":t[6]||(t[6]=a=>s.value.requested_amount=a),type:"number",min:"0",step:"0.01",required:"",placeholder:"50000.00",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary-500 focus:border-brand-primary-500"},null,512),[[f,s.value.requested_amount,void 0,{number:!0}]])]),e("div",null,[e("label",Me,[i(l,{name:"banknotes",size:"sm",class:"inline mr-2"}),t[28]||(t[28]=c(" Co-finanziamento Aziendale "))]),p(e("input",{"onUpdate:modelValue":t[7]||(t[7]=a=>s.value.co_financing_amount=a),type:"number",min:"0",step:"0.01",placeholder:"25000.00",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary-500 focus:border-brand-primary-500"},null,512),[[f,s.value.co_financing_amount,void 0,{number:!0}]])])]),e("div",Te,[t[33]||(t[33]=e("h3",{class:"text-sm font-medium text-gray-900 mb-3"},"Riepilogo Finanziario",-1)),e("div",Re,[e("div",Ne,[t[29]||(t[29]=e("span",null,"Finanziamento richiesto:",-1)),e("span",Ee,n(z(x)(s.value.requested_amount||0)),1)]),e("div",Oe,[t[30]||(t[30]=e("span",null,"Co-finanziamento aziendale:",-1)),e("span",$e,n(z(x)(s.value.co_financing_amount||0)),1)]),e("div",Je,[t[31]||(t[31]=e("span",{class:"font-medium"},"Costo totale progetto:",-1)),e("span",He,n(z(x)(U.value)),1)]),e("div",Le,[t[32]||(t[32]=e("span",null,"Percentuale finanziamento:",-1)),e("span",We,n(J.value)+"%",1)])])]),e("div",null,[e("div",Ge,[e("label",Ke,[i(l,{name:"calculator",size:"sm",class:"inline mr-2"}),t[34]||(t[34]=c(" Ripartizione Budget (opzionale) "))]),e("button",{type:"button",onClick:Q,class:"btn-secondary text-sm"},[i(l,{name:"plus",size:"sm",class:"mr-1"}),t[35]||(t[35]=c(" Aggiungi Voce "))])]),e("div",Qe,[(d(!0),u(v,null,y(_.value,(a,o)=>(d(),u("div",{key:o,class:"flex items-center space-x-3 p-3 border border-gray-200 rounded-lg"},[e("div",Xe,[p(e("input",{"onUpdate:modelValue":m=>a.category=m,type:"text",placeholder:"Es. Personale, Attrezzature, Consulenze...",class:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary-500 focus:border-brand-primary-500"},null,8,Ye),[[f,a.category]]),p(e("input",{"onUpdate:modelValue":m=>a.amount=m,type:"number",min:"0",step:"0.01",placeholder:"0.00",class:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-primary-500 focus:border-brand-primary-500"},null,8,Ze),[[f,a.amount,void 0,{number:!0}]])]),e("button",{type:"button",onClick:m=>X(o),class:"text-red-500 hover:text-red-700 p-1"},[i(l,{name:"trash",size:"sm"})],8,et)]))),128))]),_.value.length===0?(d(),u("div",tt,[i(l,{name:"calculator",size:"lg",class:"mx-auto mb-2 text-gray-300"}),t[36]||(t[36]=e("p",null,"Nessuna voce di budget aggiunta",-1)),t[37]||(t[37]=e("p",{class:"text-sm"},"Opzionale: aggiungi dettagli sulla ripartizione del budget",-1))])):D("",!0)])])]),"step-documents":C(()=>[e("div",ot,[t[42]||(t[42]=e("div",{class:"border-b border-gray-200 pb-4 mb-6"},[e("h2",{class:"text-lg font-semibold text-gray-900"},"Documenti e Allegati"),e("p",{class:"text-sm text-gray-600 mt-1"}," Carica i documenti richiesti per completare la candidatura ")],-1)),e("div",st,[i(l,{name:"document-arrow-up",size:"xl",class:"mx-auto mb-4 text-gray-400"}),t[39]||(t[39]=e("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"Upload Documenti",-1)),t[40]||(t[40]=e("p",{class:"text-gray-600 mb-4"}," Il componente di upload documenti sarà implementato nella prossima fase ",-1)),t[41]||(t[41]=e("div",{class:"text-sm text-gray-500"},[e("p",null,"Documenti tipicamente richiesti:"),e("ul",{class:"mt-2 space-y-1"},[e("li",null,"• Visura camerale"),e("li",null,"• Bilancio ultimi 2 anni"),e("li",null,"• CV del team"),e("li",null,"• Piano di progetto dettagliato")])],-1))])])]),"step-review":C(()=>[e("div",at,[t[52]||(t[52]=e("div",{class:"border-b border-gray-200 pb-4 mb-6"},[e("h2",{class:"text-lg font-semibold text-gray-900"},"Riepilogo Candidatura"),e("p",{class:"text-sm text-gray-600 mt-1"}," Verifica tutti i dati prima di sottomettere la candidatura ")],-1)),e("div",rt,[e("div",nt,[e("h3",it,[i(l,{name:"document-text",size:"sm",class:"mr-2"}),t[43]||(t[43]=c(" Informazioni Progetto "))]),e("dl",lt,[e("div",dt,[t[44]||(t[44]=e("dt",{class:"text-gray-600"},"Titolo:",-1)),e("dd",ut,n(s.value.project_title),1)]),e("div",mt,[t[45]||(t[45]=e("dt",{class:"text-gray-600"},"Durata:",-1)),e("dd",ct,n(s.value.project_duration_months)+" mesi",1)]),M.value?(d(),u("div",pt,[t[46]||(t[46]=e("dt",{class:"text-gray-600"},"Bando:",-1)),e("dd",gt,n(M.value.title),1)])):D("",!0)])]),b.value.length>0?(d(),u("div",bt,[e("h3",ft,[i(l,{name:"user-group",size:"sm",class:"mr-2"}),c(" Team ("+n(b.value.length)+" membri) ",1)]),e("div",vt,[(d(!0),u(v,null,y(b.value,a=>(d(),u("div",{key:a.name,class:"text-sm flex justify-between"},[e("span",null,n(a.name),1),e("span",yt,n(a.role),1)]))),128))])])):D("",!0),e("div",xt,[e("h3",_t,[i(l,{name:"currency-euro",size:"sm",class:"mr-2"}),t[47]||(t[47]=c(" Riepilogo Finanziario "))]),e("dl",zt,[e("div",kt,[t[48]||(t[48]=e("dt",{class:"text-gray-600"},"Finanziamento richiesto:",-1)),e("dd",wt,n(z(x)(s.value.requested_amount||0)),1)]),e("div",ht,[t[49]||(t[49]=e("dt",{class:"text-gray-600"},"Co-finanziamento:",-1)),e("dd",jt,n(z(x)(s.value.co_financing_amount||0)),1)]),e("div",Ct,[t[50]||(t[50]=e("dt",{class:"font-medium"},"Costo totale:",-1)),e("dd",Vt,n(z(x)(U.value)),1)])])])]),e("div",St,[e("div",Ut,[p(e("input",{"onUpdate:modelValue":t[8]||(t[8]=a=>S.value=a),type:"checkbox",id:"accept-terms",class:"h-4 w-4 text-brand-primary-600 focus:ring-brand-primary-500 border-gray-300 rounded"},null,512),[[re,S.value]]),t[51]||(t[51]=e("label",{for:"accept-terms",class:"ml-3 text-sm text-gray-700"},[c(" Dichiaro di aver letto e accettato i "),e("a",{href:"#",class:"text-brand-primary-600 hover:text-brand-primary-800 underline"}," termini e condizioni "),c(" del bando e di essere consapevole che la candidatura sarà valutata secondo i criteri specificati. ")],-1))])])])]),_:1},8,["steps","current-step-index","loading"]))}},Rt=de(Dt,[["__scopeId","data-v-95904237"]]);export{Rt as default};
