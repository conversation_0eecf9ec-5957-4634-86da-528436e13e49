import{r as w,c as k,w as P,b as s,j as l,l as e,g,t as i,G as y,e as m,v as C,F as h,q as f,n as N,B as z,C as S}from"./vendor.js";import{H as b}from"./app.js";import{P as B}from"./Pagination.js";const L={class:"space-y-6"},$={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},j={class:"flex items-center justify-between mb-4"},V={class:"text-2xl font-bold text-gray-900 dark:text-white"},D={key:0,class:"text-sm text-gray-500 dark:text-gray-400 mt-1"},F={class:"flex items-center space-x-3"},M={key:0,class:"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4"},A={class:"p-5"},T={class:"flex items-center"},q={class:"flex-shrink-0"},H={class:"ml-5 w-0 flex-1"},E={class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"},G={class:"text-lg font-medium text-gray-900 dark:text-white"},I={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},O={key:0,class:"p-6 border-b border-gray-200 dark:border-gray-700"},Q={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0"},U={class:"flex-1 max-w-lg"},J={class:"relative"},K={class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},R=["placeholder"],W={class:"overflow-hidden"},X={key:0,class:"flex justify-center items-center h-64"},Y={key:1,class:"text-center py-12"},Z={class:"text-gray-500 dark:text-gray-400"},ee={key:2,class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},te={class:"bg-gray-50 dark:bg-gray-800"},ae={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},se={key:1,class:"border-t border-gray-200 dark:border-gray-700"},ie={__name:"ListPageTemplate",props:{title:{type:String,required:!0},subtitle:{type:String,default:""},data:{type:Array,default:()=>[]},columns:{type:Array,default:()=>[]},stats:{type:Array,default:()=>[]},loading:{type:Boolean,default:!1},canCreate:{type:Boolean,default:!1},createLabel:{type:String,default:"Crea Nuovo"},searchPlaceholder:{type:String,default:"Cerca..."},emptyMessage:{type:String,default:"Non ci sono elementi da visualizzare."},itemsPerPage:{type:Number,default:10},resultsLabel:{type:String,default:"elementi"},showFilters:{type:Boolean,default:!0},showPagination:{type:Boolean,default:!0}},emits:["create","row-action"],setup(t){const d=t,c=w(""),u=w(1),v=k(()=>{let r=d.data;if(c.value.trim()){const o=c.value.toLowerCase();r=r.filter(a=>Object.values(a).some(n=>String(n).toLowerCase().includes(o)))}return r}),p=k(()=>Math.ceil(v.value.length/d.itemsPerPage)),x=k(()=>{const r=(u.value-1)*d.itemsPerPage,o=r+d.itemsPerPage;return v.value.slice(r,o)}),_=r=>{u.value=r};return P(()=>d.data,()=>{u.value=1}),P(c,()=>{u.value=1}),(r,o)=>(l(),s("div",L,[e("div",$,[e("div",j,[e("div",null,[e("h1",V,i(t.title),1),t.subtitle?(l(),s("p",D,i(t.subtitle),1)):g("",!0)]),e("div",F,[y(r.$slots,"header-actions",{},()=>[t.canCreate?(l(),s("button",{key:0,onClick:o[0]||(o[0]=a=>r.$emit("create")),class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},[m(b,{name:"plus",size:"sm",class:"mr-2"}),C(" "+i(t.createLabel||"Crea Nuovo"),1)])):g("",!0)])])])]),t.stats&&t.stats.length?(l(),s("div",M,[(l(!0),s(h,null,f(t.stats,a=>(l(),s("div",{key:a.label,class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},[e("div",A,[e("div",T,[e("div",q,[m(b,{name:a.icon,size:"lg",class:N(a.iconClass||"text-gray-400")},null,8,["name","class"])]),e("div",H,[e("dl",null,[e("dt",E,i(a.label),1),e("dd",G,i(a.value),1)])])])])]))),128))])):g("",!0),e("div",I,[t.showFilters?(l(),s("div",O,[e("div",Q,[e("div",U,[e("div",J,[e("div",K,[m(b,{name:"magnifying-glass",size:"sm",class:"text-gray-400"})]),z(e("input",{"onUpdate:modelValue":o[1]||(o[1]=a=>c.value=a),type:"text",placeholder:t.searchPlaceholder||"Cerca...",class:"block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"},null,8,R),[[S,c.value]])])]),y(r.$slots,"filters")])])):g("",!0),e("div",W,[y(r.$slots,"content",{data:x.value,loading:t.loading},()=>[t.loading?(l(),s("div",X,o[2]||(o[2]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):x.value.length===0?(l(),s("div",Y,[y(r.$slots,"empty-state",{},()=>[m(b,{name:"inbox",size:"2xl",class:"mx-auto text-gray-400 mb-4"}),o[3]||(o[3]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Nessun elemento trovato",-1)),e("p",Z,i(t.emptyMessage||"Non ci sono elementi da visualizzare."),1)])])):(l(),s("table",ee,[e("thead",te,[e("tr",null,[(l(!0),s(h,null,f(t.columns,a=>(l(),s("th",{key:a.key,class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"},i(a.label),1))),128))])]),e("tbody",ae,[(l(!0),s(h,null,f(x.value,a=>(l(),s("tr",{key:a.id},[(l(!0),s(h,null,f(t.columns,n=>(l(),s("td",{key:n.key,class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},[y(r.$slots,`column-${n.key}`,{item:a,value:a[n.key]},()=>[C(i(a[n.key]),1)])]))),128))]))),128))])]))])]),t.showPagination&&p.value>1?(l(),s("div",se,[m(B,{"current-page":u.value,"total-pages":p.value,total:v.value.length,"per-page":t.itemsPerPage,"results-label":t.resultsLabel||"elementi",onPageChange:_},null,8,["current-page","total-pages","total","per-page","results-label"])])):g("",!0)])]))}};export{ie as _};
