import{r as D,c as b,w as F,b as o,j as i,l as a,F as N,q as j,g as h,n as S,f as A,t as d,A as W,G as $,e as p,v as f}from"./vendor.js";import{_ as q,H as m}from"./app.js";const H={class:"max-w-4xl mx-auto"},E={class:"mb-8"},G={class:"flex items-center justify-between"},L={class:"flex items-center space-x-4"},M={class:"flex items-center"},J=["onClick"],K={key:1},O={class:"ml-3 hidden sm:block"},P={class:"text-xs text-gray-500"},Q={class:"text-sm text-gray-500"},R={class:"bg-white rounded-lg shadow-sm border p-6"},U={class:"min-h-96"},X={class:"flex items-center justify-between pt-6 mt-8 border-t border-gray-200"},Y=["disabled"],Z={key:1},_={class:"flex space-x-3"},ee=["disabled"],te=["disabled"],ne=["disabled"],ae={__name:"WizardContainer",props:{steps:{type:Array,required:!0,validator:t=>t.every(c=>c.id&&c.title&&typeof c.id=="string")},currentStepIndex:{type:Number,default:0},isStepValid:{type:Function,default:()=>!0},isFormValid:{type:Function,default:()=>!0},loading:{type:Boolean,default:!1},canSaveDraft:{type:Boolean,default:!0},allowStepNavigation:{type:Boolean,default:!0},showSaveDraft:{type:Boolean,default:!1},nextButtonText:{type:String,default:"Avanti"},previousButtonText:{type:String,default:"Indietro"},submitButtonText:{type:String,default:"Invia"},saveDraftText:{type:String,default:"Salva Bozza"},savingText:{type:String,default:"Salvataggio..."},submittingText:{type:String,default:"Invio..."}},emits:["update:currentStepIndex","step-change","next-step","previous-step","submit","save-draft","step-navigate"],setup(t,{emit:c}){const n=t,s=c,r=D(""),v=b(()=>{var e;return(e=n.steps[n.currentStepIndex])==null?void 0:e.id}),I=b(()=>n.steps[n.currentStepIndex]),x=b(()=>n.isStepValid(n.currentStepIndex,v.value));function k(e){return e<n.currentStepIndex?"bg-brand-primary-500 text-white hover:bg-brand-primary-600":e===n.currentStepIndex?"bg-brand-primary-600 text-white":"bg-gray-300 text-gray-600 hover:bg-gray-400"}function w(e){return e<=n.currentStepIndex?"text-gray-900":"text-gray-500"}function y(e){return e<n.currentStepIndex}function B(){if(x.value&&n.currentStepIndex<n.steps.length-1){r.value="next";const e=n.currentStepIndex+1;s("update:currentStepIndex",e),s("next-step",e),s("step-change",e,v.value)}}function T(){if(n.currentStepIndex>0){r.value="previous";const e=n.currentStepIndex-1;s("update:currentStepIndex",e),s("previous-step",e),s("step-change",e,v.value)}}function C(e){var u;n.allowStepNavigation&&e<=n.currentStepIndex&&(r.value="navigate",s("update:currentStepIndex",e),s("step-navigate",e),s("step-change",e,(u=n.steps[e])==null?void 0:u.id))}function V(){n.isFormValid()&&(r.value="submit",s("submit"))}function z(){n.canSaveDraft&&(r.value="draft",s("save-draft"))}return F(()=>n.currentStepIndex,e=>{var u;s("step-change",e,(u=n.steps[e])==null?void 0:u.id)}),(e,u)=>(i(),o("div",H,[a("div",E,[a("div",G,[a("div",L,[(i(!0),o(N,null,j(t.steps,(g,l)=>(i(),o("div",{key:g.id,class:"flex items-center"},[a("div",M,[a("div",{class:S(["w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-colors duration-200 cursor-pointer",k(l)]),onClick:se=>C(l)},[y(l)?(i(),A(m,{key:0,name:"check",size:"sm",class:"text-white"})):(i(),o("span",K,d(l+1),1))],10,J),a("div",O,[a("p",{class:S(["text-sm font-medium transition-colors duration-200",w(l)])},d(g.title),3),a("p",P,d(g.description),1)])]),l<t.steps.length-1?(i(),o("div",{key:0,class:S(["hidden sm:block w-16 h-0.5 mx-4 transition-colors duration-200",y(l)?"bg-brand-primary-500":"bg-gray-300"])},null,2)):h("",!0)]))),128))]),a("div",Q,d(t.currentStepIndex+1)+" di "+d(t.steps.length),1)])]),a("div",R,[a("form",{onSubmit:W(V,["prevent"])},[a("div",U,[$(e.$slots,`step-${v.value}`,{step:I.value,stepIndex:t.currentStepIndex,isValid:x.value},void 0,!0)]),a("div",X,[t.currentStepIndex>0?(i(),o("button",{key:0,type:"button",onClick:T,class:"btn-secondary",disabled:t.loading},[p(m,{name:"arrow-left",size:"sm",class:"mr-2"}),f(" "+d(t.previousButtonText),1)],8,Y)):(i(),o("div",Z)),a("div",_,[t.showSaveDraft?(i(),o("button",{key:0,type:"button",onClick:z,class:"btn-secondary",disabled:t.loading||!t.canSaveDraft},[p(m,{name:"document",size:"sm",class:"mr-2"}),f(" "+d(t.loading&&r.value==="draft"?t.savingText:t.saveDraftText),1)],8,ee)):h("",!0),t.currentStepIndex<t.steps.length-1?(i(),o("button",{key:1,type:"button",onClick:B,class:"btn-primary",disabled:!x.value||t.loading&&r.value==="next"},[f(d(t.loading&&r.value==="next"?"Caricamento...":t.nextButtonText)+" ",1),p(m,{name:"arrow-right",size:"sm",class:"ml-2"})],8,te)):(i(),o("button",{key:2,type:"submit",class:"btn-primary",disabled:!t.isFormValid||t.loading&&r.value==="submit"},[p(m,{name:"paper-airplane",size:"sm",class:"mr-2"}),f(" "+d(t.loading&&r.value==="submit"?t.submittingText:t.submitButtonText),1)],8,ne))])])],32)])]))}},oe=q(ae,[["__scopeId","data-v-006eb006"]]);export{oe as W};
