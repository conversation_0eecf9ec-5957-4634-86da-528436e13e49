import{r as m,c as d,o as pe,b as g,f as A,g as C,e as f,p as G,l as a,B as h,v as D,S as Y,F as W,q as J,C as z,P as ve,t as j,s as ye,j as c}from"./vendor.js";import{_ as fe}from"./PageHeader.js";import{_ as he}from"./AlertsSection.js";import{T as ke}from"./TimesheetGrid.js";import{b as xe,H as k,c as p}from"./app.js";import{u as _e}from"./timesheet.js";const we={class:"space-y-6"},je={class:"flex items-center space-x-4"},Te={class:"flex items-center space-x-2"},Se={class:"text-center min-w-[150px]"},Me={class:"text-lg font-semibold text-gray-900 dark:text-white"},Ce={class:"flex items-center space-x-3"},De=["disabled","title"],Ee=["disabled"],Ie=["disabled","title"],Fe={key:1,class:"fixed inset-0 z-50 overflow-y-auto","aria-labelledby":"modal-title",role:"dialog","aria-modal":"true"},$e={class:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},Oe={class:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"},ze={class:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4"},Ve={class:"sm:flex sm:items-start"},He={class:"mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 dark:bg-blue-900 sm:mx-0 sm:h-10 sm:w-10"},Ne={class:"mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full"},Ae={class:"mt-4 space-y-4"},Pe=["value"],Be={key:0},Ue=["value"],qe={class:"grid grid-cols-2 gap-4"},Le={class:"space-y-3"},Re={class:"flex items-center"},Ge={key:0,class:"ml-6"},Ye={class:"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse"},We=["disabled"],at={__name:"TimesheetEntry",setup(Je){ye();const i=_e(),T=xe(),S=m(!1),E=m(!1),x=m(!1),b=m([]),v=m([]),o=m({project_id:"",task_id:"",date:new Date().toISOString().split("T")[0],hours:0,description:"",billable:!0,billing_rate:null}),K=m(!1),I=m(new Map),V=m(!1),P=["Gennaio","Febbraio","Marzo","Aprile","Maggio","Giugno","Luglio","Agosto","Settembre","Ottobre","Novembre","Dicembre"],F=d(()=>i.currentMonth),$=d(()=>i.currentYear),H=d(()=>i.monthlyTimesheetStatus),O=d(()=>i.error),B=d(()=>{var t,e;return((e=(t=T.user)==null?void 0:t.permissions)==null?void 0:e.canViewBilling)||!1}),_=d(()=>H.value==="confirmed"||H.value==="approved"),Q=d(()=>b.value.find(t=>t.id==o.value.project_id)),U=d(()=>o.value.project_id&&o.value.date&&o.value.hours>0),X=d(()=>O.value?[{id:"error",type:"error",title:"Errore",message:O.value,dismissible:!0}]:[]),q=d(()=>{const t=$.value,e=F.value,r=new Date(t,e,0).getDate(),s=[];for(let l=1;l<=r;l++){const n=new Date(t,e-1,l);s.push({key:`${t}-${e.toString().padStart(2,"0")}-${l.toString().padStart(2,"0")}`,label:l.toString(),sublabel:n.toLocaleDateString("it-IT",{weekday:"short"}),isWeekend:n.getDay()===0||n.getDay()===6,isToday:n.toDateString()===new Date().toDateString()})}return s}),w=d(()=>{const t=i.monthlyEntries,e=i.projectTasks;if(console.log("🔍 TimesheetEntry - monthlyEntries:",t),console.log("🔍 TimesheetEntry - projectTasks:",e),!t||!e||e.length===0)return console.log("❌ Nessun dato timesheet disponibile"),[];const r=e.map(s=>{const l={id:s.id,projectName:s.project_name,taskName:s.task_name,name:`${s.project_name}${s.task_name?` - ${s.task_name}`:""}`,projectId:s.project_id,taskId:s.task_id,assignees:s.assignees||"",hours:{},billing:{},total:0};return Object.entries(t).forEach(([n,y])=>{if(y[s.id]){const u=y[s.id],M=typeof u=="object"?u.hours:u,be=typeof u=="object"?u.billable:!1;M>0&&(l.hours[n]=M.toFixed(1),l.billing[n]=be,l.total+=M)}}),{...l,total:l.total.toFixed(1)}});return console.log("✅ TimesheetEntry - dati trasformati:",r),r}),Z=d(()=>w.value.reduce((t,e)=>t+parseFloat(e.total),0)),ee=d(()=>w.value.reduce((t,e)=>{const r=Object.entries(e.billing||{}).filter(([s,l])=>l&&e.hours[s]).reduce((s,[l])=>s+parseFloat(e.hours[l]||0),0);return t+r},0)),L=d(()=>{const t={};return q.value.forEach(e=>{t[e.key]=w.value.reduce((r,s)=>r+parseFloat(s.hours[e.key]||0),0)}),t}),te=d(()=>Object.values(L.value).reduce((t,e)=>t+e,0)),ae=d(()=>({status:H.value||"pending",totalHours:Z.value,billableHours:ee.value,pendingChanges:I.value.size})),re=async()=>{i.navigateMonth("previous"),await i.loadMonthlyData(i.currentYear,i.currentMonth)},se=async()=>{i.navigateMonth("next"),await i.loadMonthlyData(i.currentYear,i.currentMonth)},oe=(t,e)=>{o.value={project_id:t.projectId,task_id:t.taskId||"",date:e.key,hours:parseFloat(t.hours[e.key]||0),description:"",billable:t.billing&&t.billing[e.key]||!0,billing_rate:null},t.projectId&&R(t.projectId),S.value=!0},le=async(t,e,r)=>{try{const s=w.value.find(n=>n.id===t);if(!s)return;const l={user_id:T.user.id,project_id:s.projectId,task_id:s.taskId||null,date:e,hours:parseFloat(r)||0,billable:s.billing[e]||!0};if(l.hours>0){const n=await p.post("/api/timesheets",l);if(!n.data.success)throw new Error(n.data.message||"Errore durante il salvataggio")}else{const n=await p.delete("/api/timesheets/entry",{data:{user_id:l.user_id,project_id:l.project_id,task_id:l.task_id,date:l.date}});if(!n.data.success)throw new Error(n.data.message||"Errore durante l'eliminazione")}await i.loadMonthlyData()}catch(s){console.error("Error updating cell:",s),i.error=s.message||"Impossibile aggiornare le ore"}},ie=(t,e,r)=>{const s=`${t}-${e}`;r&&parseFloat(r)>0?I.value.set(s,{taskId:t,date:e,hours:parseFloat(r)}):I.value.delete(s)},ne=async t=>{if(!V.value){V.value=!0;try{const e=[];for(const[l,n]of t.entries()){const y=w.value.find(M=>M.id===n.taskId);if(!y)continue;const u={user_id:T.user.id,project_id:y.projectId,task_id:y.taskId||null,date:n.date,hours:n.hours,billable:y.billing[n.date]||!0};u.hours>0?e.push(p.post("/api/timesheets",u)):e.push(p.delete("/api/timesheets/entry",{data:{user_id:u.user_id,project_id:u.project_id,task_id:u.task_id,date:u.date}}))}const s=(await Promise.allSettled(e)).filter(l=>l.status==="rejected");s.length>0&&(console.error("Alcuni salvataggi sono falliti:",s),i.error=`${s.length} modifiche non sono state salvate`),I.value.clear(),await i.loadMonthlyData()}catch(e){console.error("Error in bulk save:",e),i.error=e.message||"Errore durante il salvataggio di massa"}finally{V.value=!1}}},N=()=>{S.value=!1,de(),v.value=[]},de=()=>{o.value={project_id:"",task_id:"",date:new Date().toISOString().split("T")[0],hours:0,description:"",billable:!0,billing_rate:null}},ue=async()=>{var t;if(o.value.task_id="",v.value=[],o.value.project_id){await R(o.value.project_id);const e=Q.value;(t=e==null?void 0:e.contract)!=null&&t.hourly_rate&&B.value&&(o.value.billing_rate=e.contract.hourly_rate)}},R=async t=>{try{const e=await p.get(`/api/tasks?project_id=${t}`);if(e.data.success){const s=(e.data.data.tasks||[]).filter(l=>l.status!=="done");v.value=s}else v.value=[]}catch(e){console.error("Error loading tasks:",e),v.value=[]}},ce=async()=>{if(U.value){E.value=!0;try{const t={user_id:T.user.id,project_id:parseInt(o.value.project_id),task_id:o.value.task_id?parseInt(o.value.task_id):null,date:o.value.date,hours:parseFloat(o.value.hours),description:o.value.description,billable:o.value.billable,billing_rate:o.value.billable&&o.value.billing_rate?parseFloat(o.value.billing_rate):null},e=await p.post("/api/timesheets",t);if(e.data.success)N(),await i.loadMonthlyData();else throw new Error(e.data.message||"Errore durante il salvataggio")}catch(t){console.error("Error saving hours:",t),i.error=t.message||"Impossibile salvare le ore"}finally{E.value=!1}}},me=async()=>{if(!x.value){x.value=!0;try{const t=`${$.value}-${F.value.toString().padStart(2,"0")}`,e=await p.post("/api/timesheets/confirm",{user_id:T.user.id,year_month:t});if(e.data.success)i.setMonthlyTimesheetStatus("confirmed");else throw new Error(e.data.message||"Errore durante la conferma del timesheet")}catch(t){console.error("Error confirming timesheet:",t),i.error=t.message||"Impossibile confermare il timesheet"}finally{x.value=!1}}},ge=async()=>{try{const t=await p.get("/api/projects");if(t.data.success){const e=t.data.data;e.projects?b.value=e.projects:e.items?b.value=e.items:Array.isArray(e)?b.value=e:b.value=[]}else b.value=[]}catch(t){console.error("Error loading user projects:",t),b.value=[]}};return pe(async()=>{await Promise.all([i.loadAvailableProjects(),i.loadMonthlyData(),ge()])}),(t,e)=>(c(),g("div",we,[O.value?(c(),A(he,{key:0,alerts:X.value},null,8,["alerts"])):C("",!0),f(fe,{title:"Le Mie Ore",subtitle:"Registra le tue ore di lavoro con la griglia mensile",icon:"clock","icon-color":"text-blue-600"},{actions:G(()=>[a("div",je,[a("div",Te,[a("button",{onClick:re,class:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-md transition-colors"},[f(k,{name:"chevron-left",size:"md"})]),a("div",Se,[a("h2",Me,j(P[F.value-1])+" "+j($.value),1)]),a("button",{onClick:se,class:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-md transition-colors"},[f(k,{name:"chevron-right",size:"md"})])]),a("div",Ce,[a("button",{onClick:e[0]||(e[0]=r=>S.value=!0),disabled:_.value,class:"inline-flex items-center px-4 py-2 bg-brand-primary-600 hover:bg-brand-primary-700 text-white rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed transition-colors",title:_.value?"Timesheet non modificabile (confermato o approvato)":"Aggiungi ore"},[f(k,{name:"plus",size:"sm",class:"mr-2"}),e[9]||(e[9]=D(" Aggiungi Ore "))],8,De),_.value?C("",!0):(c(),g("button",{key:0,onClick:me,disabled:x.value,class:"inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium disabled:opacity-50 transition-colors"},[x.value?(c(),A(k,{key:0,name:"arrow-path",size:"sm",className:"animate-spin mr-2"})):(c(),A(k,{key:1,name:"check",size:"sm",class:"mr-2"})),D(" "+j(x.value?"Confermando...":"Conferma"),1)],8,Ee))])])]),_:1}),f(ke,{title:`Le Mie Ore - ${P[F.value-1]} ${$.value}`,tasks:w.value,days:q.value,"daily-totals":L.value,"grand-total":te.value,loading:K.value,error:O.value,editable:!_.value,"show-stats":!0,"show-day-totals":!0,"show-indicators":!0,"show-legend":!0,status:ae.value,"row-header-label":"Progetto/Task","empty-message":"Nessun timesheet registrato per questo mese",onCellClick:oe,onCellUpdate:le,onBulkSave:ne,onHoursChanged:ie},{"empty-state":G(()=>[f(k,{name:"clock",size:"lg",class:"mx-auto text-gray-400 mb-4"}),e[10]||(e[10]=a("p",{class:"text-gray-500 dark:text-gray-400 mb-2"},"Nessun timesheet registrato per questo mese",-1)),a("button",{onClick:e[1]||(e[1]=r=>S.value=!0),disabled:_.value,class:"text-brand-primary-600 hover:text-brand-primary-700 dark:text-brand-primary-400 disabled:opacity-50 disabled:cursor-not-allowed text-sm font-medium",title:_.value?"Timesheet non modificabile":"Registra le tue prime ore"}," Registra le tue prime ore ",8,Ie)]),_:1},8,["title","tasks","days","daily-totals","grand-total","loading","error","editable","status"]),S.value?(c(),g("div",Fe,[a("div",$e,[a("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:N}),e[21]||(e[21]=a("span",{class:"hidden sm:inline-block sm:align-middle sm:h-screen"},"​",-1)),a("div",Oe,[a("div",ze,[a("div",Ve,[a("div",He,[f(k,{name:"clock",size:"md",color:"text-blue-600 dark:text-blue-400"})]),a("div",Ne,[e[20]||(e[20]=a("h3",{class:"text-lg leading-6 font-medium text-gray-900 dark:text-white",id:"modal-title"}," Registra Ore ",-1)),a("div",Ae,[a("div",null,[e[12]||(e[12]=a("label",{for:"project",class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},[D(" Progetto "),a("span",{class:"text-red-500"},"*")],-1)),h(a("select",{id:"project","onUpdate:modelValue":e[2]||(e[2]=r=>o.value.project_id=r),onChange:ue,class:"mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-brand-primary-500 focus:ring-brand-primary-500",required:""},[e[11]||(e[11]=a("option",{value:""},"Seleziona progetto",-1)),(c(!0),g(W,null,J(b.value,r=>(c(),g("option",{key:r.id,value:r.id},j(r.name),9,Pe))),128))],544),[[Y,o.value.project_id]])]),v.value.length>0?(c(),g("div",Be,[e[14]||(e[14]=a("label",{for:"task",class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Task (Opzionale) ",-1)),h(a("select",{id:"task","onUpdate:modelValue":e[3]||(e[3]=r=>o.value.task_id=r),class:"mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-brand-primary-500 focus:ring-brand-primary-500"},[e[13]||(e[13]=a("option",{value:""},"Nessun task specifico",-1)),(c(!0),g(W,null,J(v.value,r=>(c(),g("option",{key:r.id,value:r.id},j(r.name),9,Ue))),128))],512),[[Y,o.value.task_id]])])):C("",!0),a("div",qe,[a("div",null,[e[15]||(e[15]=a("label",{for:"date",class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},[D(" Data "),a("span",{class:"text-red-500"},"*")],-1)),h(a("input",{type:"date",id:"date","onUpdate:modelValue":e[4]||(e[4]=r=>o.value.date=r),class:"mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-brand-primary-500 focus:ring-brand-primary-500",required:""},null,512),[[z,o.value.date]])]),a("div",null,[e[16]||(e[16]=a("label",{for:"hours",class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},[D(" Ore "),a("span",{class:"text-red-500"},"*")],-1)),h(a("input",{type:"number",id:"hours","onUpdate:modelValue":e[5]||(e[5]=r=>o.value.hours=r),step:"0.5",min:"0",max:"24",class:"mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-brand-primary-500 focus:ring-brand-primary-500",required:""},null,512),[[z,o.value.hours]])])]),a("div",null,[e[17]||(e[17]=a("label",{for:"description",class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Descrizione ",-1)),h(a("textarea",{id:"description","onUpdate:modelValue":e[6]||(e[6]=r=>o.value.description=r),rows:"3",class:"mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-brand-primary-500 focus:ring-brand-primary-500",placeholder:"Descrivi l'attività svolta..."},null,512),[[z,o.value.description]])]),a("div",Le,[a("div",Re,[h(a("input",{id:"billable","onUpdate:modelValue":e[7]||(e[7]=r=>o.value.billable=r),type:"checkbox",class:"h-4 w-4 text-brand-primary-600 focus:ring-brand-primary-500 border-gray-300 dark:border-gray-600 rounded"},null,512),[[ve,o.value.billable]]),e[18]||(e[18]=a("label",{for:"billable",class:"ml-2 block text-sm text-gray-700 dark:text-gray-300"}," Ore fatturabili ",-1))]),o.value.billable&&B.value?(c(),g("div",Ge,[e[19]||(e[19]=a("label",{for:"billing_rate",class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Tariffa Oraria (€) ",-1)),h(a("input",{type:"number",id:"billing_rate","onUpdate:modelValue":e[8]||(e[8]=r=>o.value.billing_rate=r),step:"0.01",min:"0",class:"mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-brand-primary-500 focus:ring-brand-primary-500",placeholder:"Es. 45.00"},null,512),[[z,o.value.billing_rate]])])):C("",!0)])])])])]),a("div",Ye,[a("button",{type:"button",onClick:ce,disabled:!U.value||E.value,class:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-brand-primary-600 text-base font-medium text-white hover:bg-brand-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-primary-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"},j(E.value?"Salvataggio...":"Salva Ore"),9,We),a("button",{type:"button",onClick:N,class:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"}," Annulla ")])])])])):C("",!0)]))}};export{at as default};
