import{_ as f,f as b,d as x,b as h}from"./app.js";import{b as a,j as l,l as e,t as c,A as y,g as d,B as r,C as u,S as v,O as k,F as S,q as w,v as m,P as p,f as T,h as M}from"./vendor.js";const _={name:"EditTopicModal",components:{LoadingSpinner:b},props:{topic:{type:Object,default:null}},emits:["close","topic-created","topic-updated"],data(){return{isSubmitting:!1,tagsInput:"",form:{title:"",category:"",content:"",tags:[],is_pinned:!1,is_locked:!1}}},computed:{isEditing(){return!!this.topic},canModerate(){return h().hasPermission("moderate_communications")},isFormValid(){return this.form.title.trim()&&this.form.category&&this.form.content.trim()&&this.form.title.length<=200&&this.form.content.length<=5e3}},watch:{isOpen(s){s&&this.initializeForm()},topic:{handler(){this.initializeForm()},immediate:!0},tagsInput(s){s.includes(",")&&this.addTagsFromInput()}},methods:{initializeForm(){this.isEditing&&this.topic?(this.form={title:this.topic.title||"",category:this.topic.category||"",content:this.topic.content||"",tags:[...this.topic.tags||[]],is_pinned:this.topic.is_pinned||!1,is_locked:this.topic.is_locked||!1},this.tagsInput=""):(this.form={title:"",category:"",content:"",tags:[],is_pinned:!1,is_locked:!1},this.tagsInput="")},addTagsFromInput(){const s=this.tagsInput.split(",").map(t=>t.trim()).filter(t=>t&&!this.form.tags.includes(t)).slice(0,10-this.form.tags.length);this.form.tags.push(...s),this.tagsInput=""},removeTag(s){this.form.tags=this.form.tags.filter(t=>t!==s)},async submitTopic(){if(!(!this.isFormValid||this.isSubmitting)){this.tagsInput.trim()&&this.addTagsFromInput(),this.isSubmitting=!0;try{const s={...this.form,tags:this.form.tags.slice(0,10)},t=x();this.isEditing?(await t.updateTopic({id:this.topic.id,...s}),this.$emit("topic-updated")):(await t.createTopic(s),this.$emit("topic-created")),this.closeModal()}catch(s){console.error("Error saving topic:",s)}finally{this.isSubmitting=!1}}},closeModal(){this.$emit("close"),this.initializeForm()}}},F={class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},I={class:"card w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto"},C={class:"flex items-center justify-between p-6 border-b"},V={class:"text-lg font-semibold text-gray-900"},E={class:"text-right text-xs text-gray-500 mt-1"},z={class:"text-right text-xs text-gray-500 mt-1"},B={key:0,class:"flex flex-wrap gap-2 mt-2"},j=["onClick"],U={key:0,class:"border-t pt-6"},q={class:"space-y-3"},A={class:"flex items-center"},D={class:"flex items-center"},L={class:"flex justify-end space-x-3 pt-6 border-t"},N=["disabled"];function O(s,t,P,G,o,n){const g=M("LoadingSpinner");return l(),a("div",F,[e("div",I,[e("div",C,[e("h3",V,c(n.isEditing?"Modifica Topic":"Nuovo Topic"),1),e("button",{onClick:t[0]||(t[0]=(...i)=>n.closeModal&&n.closeModal(...i)),class:"text-gray-400 hover:text-gray-600 transition-colors"},t[9]||(t[9]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e("form",{onSubmit:t[8]||(t[8]=y((...i)=>n.submitTopic&&n.submitTopic(...i),["prevent"])),class:"p-6 space-y-6"},[e("div",null,[t[10]||(t[10]=e("label",{for:"title",class:"block text-sm font-medium text-gray-700 mb-2"}," Titolo * ",-1)),r(e("input",{id:"title","onUpdate:modelValue":t[1]||(t[1]=i=>o.form.title=i),type:"text",required:"",maxlength:"200",class:"input w-full",placeholder:"Inserisci il titolo del topic"},null,512),[[u,o.form.title]]),e("div",E,c(o.form.title.length)+"/200 ",1)]),e("div",null,[t[12]||(t[12]=e("label",{for:"category",class:"block text-sm font-medium text-gray-700 mb-2"}," Categoria * ",-1)),r(e("select",{id:"category","onUpdate:modelValue":t[2]||(t[2]=i=>o.form.category=i),required:"",class:"input w-full"},t[11]||(t[11]=[k('<option value="">Seleziona una categoria</option><option value="general">Generale</option><option value="announcements">Annunci</option><option value="discussions">Discussioni</option><option value="questions">Domande</option><option value="feedback">Feedback</option><option value="technical">Tecnico</option><option value="social">Sociale</option>',8)]),512),[[v,o.form.category]])]),e("div",null,[t[13]||(t[13]=e("label",{for:"content",class:"block text-sm font-medium text-gray-700 mb-2"}," Contenuto * ",-1)),r(e("textarea",{id:"content","onUpdate:modelValue":t[3]||(t[3]=i=>o.form.content=i),required:"",rows:"8",maxlength:"5000",class:"input-field resize-vertical",placeholder:"Scrivi il contenuto del topic..."},null,512),[[u,o.form.content]]),e("div",z,c(o.form.content.length)+"/5000 ",1)]),e("div",null,[t[14]||(t[14]=e("label",{for:"tags",class:"block text-sm font-medium text-gray-700 mb-2"}," Tag (opzionali) ",-1)),r(e("input",{id:"tags","onUpdate:modelValue":t[4]||(t[4]=i=>o.tagsInput=i),type:"text",class:"input w-full",placeholder:"Inserisci i tag separati da virgola (es: importante, urgente)"},null,512),[[u,o.tagsInput]]),t[15]||(t[15]=e("div",{class:"text-xs text-gray-500 mt-1"}," Separare i tag con virgole. Massimo 10 tag. ",-1)),o.form.tags.length>0?(l(),a("div",B,[(l(!0),a(S,null,w(o.form.tags,i=>(l(),a("span",{key:i,class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"},[m(c(i)+" ",1),e("button",{type:"button",onClick:H=>n.removeTag(i),class:"ml-1 text-blue-600 hover:text-blue-800"}," × ",8,j)]))),128))])):d("",!0)]),n.canModerate?(l(),a("div",U,[t[18]||(t[18]=e("h4",{class:"text-sm font-medium text-gray-700 mb-4"},"Opzioni Moderatore",-1)),e("div",q,[e("label",A,[r(e("input",{"onUpdate:modelValue":t[5]||(t[5]=i=>o.form.is_pinned=i),type:"checkbox",class:"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"},null,512),[[p,o.form.is_pinned]]),t[16]||(t[16]=e("span",{class:"ml-2 text-sm text-gray-700"},"Fissa in alto",-1))]),e("label",D,[r(e("input",{"onUpdate:modelValue":t[6]||(t[6]=i=>o.form.is_locked=i),type:"checkbox",class:"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"},null,512),[[p,o.form.is_locked]]),t[17]||(t[17]=e("span",{class:"ml-2 text-sm text-gray-700"},"Blocca risposte",-1))])])])):d("",!0),e("div",L,[e("button",{type:"button",onClick:t[7]||(t[7]=(...i)=>n.closeModal&&n.closeModal(...i)),class:"btn-secondary"}," Annulla "),e("button",{type:"submit",disabled:o.isSubmitting||!n.isFormValid,class:"btn-primary flex items-center disabled:opacity-50 disabled:cursor-not-allowed"},[o.isSubmitting?(l(),T(g,{key:0,class:"-ml-1 mr-2 h-4 w-4 text-white"})):d("",!0),m(" "+c(o.isSubmitting?"Salvataggio...":n.isEditing?"Aggiorna Topic":"Crea Topic"),1)],8,N)])],32)])])}const Q=f(_,[["render",O]]);export{Q as E};
