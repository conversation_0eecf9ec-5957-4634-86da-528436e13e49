import{d as me,r as h,c as U,b as i,l as t,g as c,f as ee,v as H,h as pe,e as J,t as l,F as f,q as _,n as q,B as m,S as C,C as j,A as te,V as se,o as ge,j as n}from"./vendor.js";import{u as oe}from"./useToast.js";import{c as E,_ as be,e as xe}from"./app.js";const ve=me("caseStudies",()=>{const{showToast:g}=oe(),e=h([]),K=h([]),s=h([]),F=h([]),x=h(!1),T=h(null),M=U(()=>e.value.length),V=U(()=>e.value.filter(d=>d.case_type==="use-case")),y=U(()=>e.value.filter(d=>d.case_type==="success-story")),B=U(()=>e.value.filter(d=>d.status==="draft")),I=U(()=>e.value.filter(d=>d.status==="approved")),G=async(d={})=>{x.value=!0;try{const u=await E.get("/api/business-intelligence/case-studies",{params:d});u.data.success&&(e.value=u.data.data.case_studies)}catch(u){g("Errore nel caricamento case studies","error"),console.error("Error fetching case studies:",u)}finally{x.value=!1}},S=async d=>{x.value=!0;try{const u=await E.get(`/api/business-intelligence/case-studies/${d}`);if(u.data.success)return T.value=u.data.data,u.data.data}catch(u){throw g("Errore nel caricamento case study","error"),console.error("Error fetching case study:",u),u}finally{x.value=!1}},N=async d=>{x.value=!0;try{const u=await E.post("/api/business-intelligence/case-studies",d);if(u.data.success)return e.value.unshift(u.data.data),g("Case study creato con successo","success"),u.data.data}catch(u){throw g("Errore nella creazione del case study","error"),console.error("Error creating case study:",u),u}finally{x.value=!1}},L=async(d,u)=>{x.value=!0;try{const w=await E.put(`/api/business-intelligence/case-studies/${d}`,u);if(w.data.success){const D=e.value.findIndex(W=>W.id===d);return D!==-1&&(e.value[D]=w.data.data),T.value=w.data.data,g("Case study aggiornato con successo","success"),w.data.data}}catch(w){throw g("Errore nell'aggiornamento del case study","error"),console.error("Error updating case study:",w),w}finally{x.value=!1}},O=async d=>{x.value=!0;try{if((await E.delete(`/api/business-intelligence/case-studies/${d}`)).data.success){const w=e.value.findIndex(D=>D.id===d);return w!==-1&&e.value.splice(w,1),g("Case study eliminato con successo","success"),!0}}catch(u){throw g("Errore nell'eliminazione del case study","error"),console.error("Error deleting case study:",u),u}finally{x.value=!1}},o=async d=>{x.value=!0;try{const u=await E.post(`/api/business-intelligence/case-studies/${d}/approve`);if(u.data.success){const w=e.value.findIndex(D=>D.id===d);return w!==-1&&(e.value[w]=u.data.data),T.value=u.data.data,g("Case study approvato con successo","success"),u.data.data}}catch(u){throw g("Errore nell'approvazione del case study","error"),console.error("Error approving case study:",u),u}finally{x.value=!1}},v=async d=>{x.value=!0;try{const u=await E.post("/api/business-intelligence/case-studies/generate-with-ai",d);if(u.data.success)return g({type:"success",title:"Case study generato con AI",message:"Il case study è stato generato con successo. Controlla il pannello AI per vedere i dettagli e applicarli.",duration:5e3}),u.data.data}catch(u){throw g({type:"error",title:"Errore nella generazione AI",message:"Si è verificato un errore durante la generazione del case study. Riprova più tardi.",duration:6e3}),console.error("Error generating case study with AI:",u),u}finally{x.value=!1}},A=async()=>{try{const d=await E.get("/api/business-intelligence/case-studies/available-projects");d.data.success&&(K.value=d.data.data.projects)}catch(d){console.error("Error fetching available projects:",d)}},k=async()=>{try{const d=await E.get("/api/business-intelligence/case-studies/sectors");d.data.success&&(s.value=d.data.data.sectors)}catch(d){console.error("Error fetching sectors:",d)}},R=async()=>{try{const d=await E.get("/api/business-intelligence/case-studies/technologies");d.data.success&&(F.value=d.data.data.technologies)}catch(d){console.error("Error fetching technologies:",d)}};return{caseStudies:e,availableProjects:K,sectors:s,technologies:F,loading:x,currentCaseStudy:T,caseStudiesCount:M,useCases:V,successStories:y,draftCaseStudies:B,approvedCaseStudies:I,fetchCaseStudies:G,getCaseStudy:S,createCaseStudy:N,updateCaseStudy:L,deleteCaseStudy:O,approveCaseStudy:o,generateCaseStudyWithAI:v,fetchAvailableProjects:A,fetchSectors:k,fetchTechnologies:R,initializeHelperData:async()=>{await Promise.all([A(),k(),R()])}}}),ye={name:"CaseStudies",setup(){const g=ve(),{isAdmin:e,isManager:K}=xe(),{showToast:s}=oe(),F=h("use-cases"),x=h(!1),T=h(!1),M=h(!1),V=h(!1),y=h(null),B=h(null),I=h({title:"",overview:"",content:"",case_type:"use-case",primary_sector:"",secondary_sectors:[],project_id:null,client_id:null,technologies:[],business_kpis:{time_reduction:null,cost_reduction:null,roi:null,efficiency_gain:null,user_satisfaction:null},implementation_duration:null,team_size:null,target_audience:"internal"}),G=h({sector:"",technology:"",budget:"",case_type:"",status:""}),S=h({mode:"project",selectedProject:"",customPrompt:"",caseType:"use-case",targetAudience:"internal",focusPoints:"",sector:""}),N={"E-commerce":"bg-green-100 text-green-800",FinTech:"bg-blue-100 text-blue-800",Healthcare:"bg-purple-100 text-purple-800",Education:"bg-yellow-100 text-yellow-800",Technology:"bg-indigo-100 text-indigo-800",Consulting:"bg-gray-100 text-gray-800",Manufacturing:"bg-orange-100 text-orange-800"},L=U(()=>{let r=g.caseStudies;return F.value==="use-cases"?r=r.filter(a=>a.case_type==="use-case"):F.value==="success-stories"&&(r=r.filter(a=>a.case_type==="success-story")),G.value.sector&&(r=r.filter(a=>a.primary_sector===G.value.sector||a.secondary_sectors&&a.secondary_sectors.includes(G.value.sector))),G.value.technology&&(r=r.filter(a=>a.technologies&&a.technologies.some(b=>b.toLowerCase().includes(G.value.technology.toLowerCase())))),G.value.status&&(r=r.filter(a=>a.status===G.value.status)),r}),O=U(()=>e.value||K.value),o=U(()=>g.availableProjects),v=U(()=>g.sectors),A=r=>{if(!r)return{};if(typeof r=="string")try{return JSON.parse(r)}catch(a){return console.warn("Error parsing business_kpis:",a),{}}return r},k=r=>new Intl.NumberFormat("it-IT").format(r),R=r=>{if(!r)return"N/A";if(r<30)return`${r} giorni`;const a=Math.round(r/30);return`${a} mes${a>1?"i":"e"}`},Z=r=>{B.value=r,V.value=!0},d=(r,a="html")=>{try{const b=A(r.business_kpis);if(a==="json"){const z=JSON.stringify(r,null,2);D(z,`case-study-${r.id}.json`,"application/json"),s("Case study esportato come JSON","success");return}if(a==="txt"){const z=u(r,b);D(z,`case-study-${r.id}.txt`,"text/plain"),s("Case study esportato come TXT","success");return}const p=w(r,b);D(p,`case-study-${r.id}.html`,"text/html"),s("Case study esportato come HTML","success")}catch(b){console.error("Export error:",b),s("Errore durante l'esportazione","error")}},u=(r,a)=>{var z;const b=new Date(r.created_at).toLocaleDateString("it-IT");let p=`CASE STUDY: ${r.title}
`;return p+=`${"=".repeat(50)}

`,p+=`Settore: ${r.primary_sector}
`,p+=`Data Creazione: ${b}
`,r.generated_by_ai&&(p+=`Generato con AI: Sì
`),p+=`Stato: ${r.status==="approved"?"Approvato":r.status==="draft"?"Bozza":r.status}

`,p+=`PANORAMICA
`,p+=`${"-".repeat(20)}
`,p+=`${r.overview}

`,r.content&&(p+=`CONTENUTO DETTAGLIATO
`,p+=`${"-".repeat(20)}
`,p+=`${r.content}

`),Object.keys(a).length&&(p+=`METRICHE DI SUCCESSO
`,p+=`${"-".repeat(20)}
`,Object.entries(a).forEach(([P,X])=>{const Y=P.replace("_"," ").replace(/\b\w/g,ce=>ce.toUpperCase()),Q=P.includes("_reduction")||P==="roi"?"%":"";p+=`${Y}: ${X}${Q}
`}),p+=`
`),(z=r.technologies)!=null&&z.length&&(p+=`TECNOLOGIE
`,p+=`${"-".repeat(20)}
`,p+=`${r.technologies.join(", ")}

`),r.implementation_duration&&(p+=`DETTAGLI PROGETTO
`,p+=`${"-".repeat(20)}
`,p+=`Durata Implementazione: ${R(r.implementation_duration)}
`,r.team_size&&(p+=`Dimensione Team: ${r.team_size} persone
`),p+=`
`),p},w=(r,a)=>{var p;const b=new Date(r.created_at).toLocaleDateString("it-IT");return`<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${r.title}</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; margin: 0; padding: 20px; background: #f8f9fa; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .header { border-bottom: 3px solid #3b82f6; padding-bottom: 20px; margin-bottom: 30px; }
        .title { color: #1f2937; font-size: 2.2em; margin: 0; font-weight: 700; }
        .meta { display: flex; gap: 20px; margin-top: 15px; font-size: 0.9em; color: #6b7280; }
        .badge { background: #3b82f6; color: white; padding: 4px 12px; border-radius: 20px; font-size: 0.8em; }
        .ai-badge { background: #8b5cf6; }
        .section { margin: 25px 0; }
        .section-title { color: #1f2937; font-size: 1.3em; font-weight: 600; margin-bottom: 15px; border-left: 4px solid #3b82f6; padding-left: 15px; }
        .overview { background: #f0f9ff; padding: 20px; border-radius: 8px; border-left: 4px solid #3b82f6; font-style: italic; }
        .content { white-space: pre-line; color: #374151; }
        .metrics { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin: 20px 0; }
        .metric { text-align: center; background: #f9fafb; padding: 20px; border-radius: 8px; border: 1px solid #e5e7eb; }
        .metric-value { font-size: 2em; font-weight: bold; color: #059669; margin-bottom: 5px; }
        .metric-label { font-size: 0.9em; color: #6b7280; text-transform: capitalize; }
        .tech-tags { display: flex; flex-wrap: wrap; gap: 8px; margin: 15px 0; }
        .tech-tag { background: #dbeafe; color: #1e40af; padding: 6px 12px; border-radius: 20px; font-size: 0.85em; }
        .footer { margin-top: 40px; padding-top: 20px; border-top: 1px solid #e5e7eb; text-align: center; color: #6b7280; font-size: 0.9em; }
        @media print { body { background: white; } .container { box-shadow: none; } }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">${r.title}</h1>
            <div class="meta">
                <span class="badge">${r.primary_sector}</span>
                <span class="badge ${r.status==="approved"?"bg-green-500":"bg-yellow-500"}">${r.status==="approved"?"Approvato":r.status==="draft"?"Bozza":r.status}</span>
                ${r.generated_by_ai?'<span class="badge ai-badge">✨ Generato con AI</span>':""}
                <span>📅 ${b}</span>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">📋 Panoramica</h2>
            <div class="overview">${r.overview}</div>
        </div>

        ${r.content?`
        <div class="section">
            <h2 class="section-title">📖 Contenuto Dettagliato</h2>
            <div class="content">${r.content}</div>
        </div>
        `:""}

        ${Object.keys(a).length?`
        <div class="section">
            <h2 class="section-title">📊 Metriche di Successo</h2>
            <div class="metrics">
                ${Object.entries(a).map(([z,P])=>{const X=z.replace("_"," ").replace(/\b\w/g,Q=>Q.toUpperCase()),Y=z.includes("_reduction")||z==="roi"?"%":"";return`
                    <div class="metric">
                        <div class="metric-value">${P}${Y}</div>
                        <div class="metric-label">${X}</div>
                    </div>
                  `}).join("")}
            </div>
        </div>
        `:""}

        ${(p=r.technologies)!=null&&p.length?`
        <div class="section">
            <h2 class="section-title">🔧 Tecnologie Utilizzate</h2>
            <div class="tech-tags">
                ${r.technologies.map(z=>`<span class="tech-tag">${z}</span>`).join("")}
            </div>
        </div>
        `:""}

        ${r.implementation_duration||r.team_size?`
        <div class="section">
            <h2 class="section-title">⚙️ Dettagli Progetto</h2>
            ${r.implementation_duration?`<p><strong>Durata Implementazione:</strong> ${R(r.implementation_duration)}</p>`:""}
            ${r.team_size?`<p><strong>Dimensione Team:</strong> ${r.team_size} persone</p>`:""}
        </div>
        `:""}

        <div class="footer">
            <p>Generato da DatPortal - Business Intelligence Dashboard</p>
            <p>Esportato il ${new Date().toLocaleDateString("it-IT")} alle ${new Date().toLocaleTimeString("it-IT")}</p>
        </div>
    </div>
</body>
</html>`},D=(r,a,b)=>{const p=new Blob([r],{type:b}),z=window.URL.createObjectURL(p),P=document.createElement("a");P.href=z,P.download=a,document.body.appendChild(P),P.click(),document.body.removeChild(P),window.URL.revokeObjectURL(z)},W=r=>{console.log("Editing case study:",r)},re=async r=>{if(confirm(`Sei sicuro di voler eliminare "${r.title}"?`))try{await g.deleteCaseStudy(r.id)}catch(a){console.error("Error deleting case study:",a)}},ne=async r=>{try{await g.approveCaseStudy(r.id),s("Case study approvato con successo","success")}catch(a){console.error("Error approving case study:",a)}},ie=async()=>{if(!O.value){s("Non hai i permessi per generare case studies","error");return}x.value=!0;try{const r={project_id:S.value.selectedProject||null,custom_prompt:S.value.customPrompt||null,case_type:S.value.caseType,target_audience:S.value.targetAudience,focus_points:S.value.focusPoints,sector:S.value.sector};if(!r.project_id&&!r.custom_prompt){s("Seleziona un progetto o inserisci un prompt personalizzato","error");return}const a=await g.generateCaseStudyWithAI(r);y.value=a,T.value=!1,S.value={selectedProject:"",customPrompt:"",caseType:"use-case",targetAudience:"internal",focusPoints:"",sector:""}}catch(r){console.error("Error generating AI content:",r)}finally{x.value=!1}},ae=()=>{if(y.value){const r=`${y.value.generated_content.title}

${y.value.generated_content.overview}

${y.value.generated_content.challenge}

${y.value.generated_content.solution}

${y.value.generated_content.results}`;navigator.clipboard.writeText(r),s("Contenuto copiato negli appunti","success")}},le=async()=>{var r;if(y.value)try{const a=y.value.generated_content,b={title:a.title,overview:a.overview,content:`${a.challenge}

${a.solution}

${a.implementation}

${a.results}`,case_type:a.case_type,primary_sector:a.primary_sector,secondary_sectors:a.secondary_sectors||[],technologies:a.technologies||[],business_kpis:a.business_kpis||{},implementation_duration:a.implementation_duration,team_size:a.team_size,target_audience:a.target_audience,generated_by_ai:!0,ai_prompt_used:y.value.ai_prompt_used,project_id:((r=y.value.source_project)==null?void 0:r.id)||null};await g.createCaseStudy(b),y.value=null,s("Case study creato e salvato con successo","success")}catch(a){console.error("Error applying AI content:",a),s("Errore nel salvataggio del case study","error")}},$=()=>{I.value={title:"",overview:"",content:"",case_type:"use-case",primary_sector:"",secondary_sectors:[],project_id:null,client_id:null,technologies:[],business_kpis:{time_reduction:null,cost_reduction:null,roi:null,efficiency_gain:null,user_satisfaction:null},implementation_duration:null,team_size:null,target_audience:"internal"}},de=r=>{const a=r.target.value;I.value.technologies=a.split(",").map(b=>b.trim()).filter(b=>b.length>0)},ue=async()=>{try{const r={};Object.keys(I.value.business_kpis).forEach(b=>{I.value.business_kpis[b]!==null&&I.value.business_kpis[b]!==""&&(r[b]=I.value.business_kpis[b])});const a={...I.value,business_kpis:r,generated_by_ai:!1,project_id:I.value.project_id||null};await g.createCaseStudy(a),M.value=!1,$(),s("Case study creato con successo","success")}catch(r){console.error("Error creating manual case study:",r),s("Errore nella creazione del case study","error")}};return ge(async()=>{await g.fetchCaseStudies(),await g.initializeHelperData()}),{activeTab:F,isGenerating:x,showAIModal:T,showCreateModal:M,showDetailsModal:V,aiGeneratedContent:y,selectedCaseStudy:B,createForm:I,filters:G,aiGenerator:S,sectorColors:N,caseStudies:g.caseStudies,availableProjects:o,sectors:v,technologies:g.technologies,loading:g.loading,filteredCaseStudies:L,canCreateCaseStudies:O,formatCurrency:k,formatDuration:R,parseBusinessKpis:A,viewDetails:Z,exportStory:d,generateTextExport:u,generateHTMLExport:w,downloadFile:D,editCaseStudy:W,deleteCaseStudy:re,approveCaseStudy:ne,generateAIContent:ie,copyToClipboard:ae,applyAIContent:le,resetCreateForm:$,updateTechnologies:de,createManualCaseStudy:ue}}},fe={class:"min-h-screen bg-gray-50"},_e={class:"bg-white shadow-sm border-b border-gray-200"},Ce={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},we={class:"flex justify-between h-16"},he={key:0,class:"flex items-center space-x-4"},ke=["disabled"],ze={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6"},Te={key:0,class:"bg-purple-50 rounded-lg shadow border border-purple-200 mb-6"},Ie={class:"px-6 py-4 border-b border-purple-200"},Ge={class:"flex items-start justify-between"},Se={class:"flex items-start space-x-3"},je={class:"flex-shrink-0"},Ae={class:"flex-1"},De={class:"text-xs text-purple-700 mt-1"},Pe={class:"px-6 py-4 space-y-4 max-h-96 overflow-y-auto"},Ee={class:"text-sm text-purple-700 font-medium"},Ue={class:"text-sm text-purple-700 bg-white p-3 rounded border max-h-32 overflow-y-auto"},Fe={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Me={key:0},Ve={class:"text-sm text-purple-700 font-semibold"},Be={key:1},Oe={class:"text-sm text-purple-700 font-semibold"},Ne={key:0},Le={class:"grid grid-cols-2 md:grid-cols-4 gap-3 bg-white p-3 rounded border"},Re={class:"text-lg font-bold text-purple-600"},Ke={class:"text-xs text-gray-500"},qe={key:1},He={class:"flex flex-wrap gap-2"},Je={class:"border-t border-purple-200 pt-4 flex justify-between"},We={class:"border-b border-gray-200 mb-6"},Xe={class:"-mb-px flex space-x-8"},Ye={key:1,class:"space-y-6"},Qe={class:"bg-white rounded-lg shadow-sm p-4"},Ze={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},$e=["value"],et=["value"],tt={key:0,class:"flex justify-center py-8"},st={key:1,class:"text-center py-8"},ot={key:2,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},rt={class:"flex items-start justify-between mb-4"},nt={class:"flex-1"},it={class:"text-lg font-medium text-gray-900 mb-2"},at={class:"text-sm text-gray-600 mb-3"},lt={class:"flex flex-col space-y-2"},dt={class:"space-y-3"},ut={key:0,class:"flex justify-between text-sm"},ct={class:"font-medium text-green-600"},mt={key:1,class:"flex justify-between text-sm"},pt={class:"font-medium"},gt={key:2,class:"flex justify-between text-sm"},bt={class:"font-medium"},xt={key:0,class:"mt-4 flex flex-wrap gap-2"},vt={key:0,class:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-600"},yt={class:"mt-4"},ft={class:"flex justify-between items-start mb-3"},_t={class:"text-xs text-gray-500"},Ct={key:0,class:"flex items-center mb-1"},wt={class:"grid grid-cols-2 gap-2"},ht=["onClick"],kt=["onClick"],zt=["onClick"],Tt=["onClick"],It={key:2,class:"space-y-6"},Gt={class:"bg-white rounded-lg shadow-sm p-4"},St={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},jt=["value"],At=["value"],Dt={key:0,class:"flex justify-center py-8"},Pt={key:1,class:"text-center py-8"},Et={key:2,class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},Ut={class:"p-6"},Ft={class:"flex items-center mb-4"},Mt={class:"w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center"},Vt={class:"text-white font-bold text-lg"},Bt={class:"ml-4"},Ot={class:"text-lg font-medium text-gray-900"},Nt={class:"text-sm text-gray-500"},Lt={class:"text-gray-600 italic mb-4"},Rt={key:0,class:"grid grid-cols-3 gap-4 mb-4"},Kt={key:0,class:"text-center"},qt={class:"text-2xl font-bold text-green-600"},Ht={key:1,class:"text-center"},Jt={class:"text-2xl font-bold text-blue-600"},Wt={key:2,class:"text-center"},Xt={class:"text-2xl font-bold text-purple-600"},Yt={class:"space-y-3"},Qt={class:"flex justify-between items-center"},Zt={class:"text-sm text-gray-500"},$t={key:0,class:"text-xs text-purple-600"},es={class:"grid grid-cols-2 gap-2"},ts=["onClick"],ss=["onClick"],os=["onClick"],rs=["onClick"],ns={key:3,class:"space-y-6"},is={class:"bg-white rounded-lg shadow-sm p-6"},as={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},ls={class:"space-y-4"},ds=["value"],us=["disabled"],cs={class:"bg-gray-50 rounded-lg p-4"},ms={key:0,class:"text-center text-gray-500 py-8"},ps={key:1,class:"space-y-4"},gs={class:"flex justify-between items-center"},bs={class:"space-x-2"},xs=["innerHTML"],vs={key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},ys={class:"relative top-10 mx-auto p-6 border max-w-4xl shadow-lg rounded-md bg-white"},fs={class:"mt-3"},_s={class:"flex justify-between items-center mb-6"},Cs={class:"text-xl font-semibold text-gray-900"},ws={key:0,class:"space-y-6"},hs={class:"flex items-center space-x-3"},ks={key:0,class:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800"},zs={class:"text-gray-700"},Ts={class:"prose max-w-none text-gray-700 whitespace-pre-line"},Is={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"},Gs={key:0,class:"bg-gray-50 p-4 rounded-lg"},Ss={class:"text-lg font-semibold text-blue-600"},js={key:1,class:"bg-gray-50 p-4 rounded-lg"},As={class:"text-lg font-semibold text-green-600"},Ds={key:2,class:"bg-gray-50 p-4 rounded-lg"},Ps={class:"text-lg font-semibold text-purple-600"},Es={key:0},Us={class:"grid grid-cols-2 md:grid-cols-4 gap-4"},Fs={class:"text-2xl font-bold text-green-600"},Ms={class:"text-sm text-gray-500 capitalize"},Vs={key:1},Bs={class:"flex flex-wrap gap-2"},Os={key:2},Ns={class:"flex flex-wrap gap-2"},Ls={class:"flex justify-between items-center pt-4 border-t"},Rs={class:"flex space-x-2"},Ks={class:"flex space-x-3"},qs={key:1,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},Hs={class:"relative top-10 mx-auto p-6 border max-w-3xl shadow-lg rounded-md bg-white"},Js={class:"mt-3"},Ws={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Xs={class:"mt-2 space-y-2"},Ys={class:"flex items-center"},Qs={class:"flex items-center"},Zs={key:0},$s=["required"],eo=["value"],to={key:1,class:"md:col-span-2"},so=["required"],oo=["value"],ro={class:"flex justify-end space-x-3 mt-6"},no=["disabled"],io={key:2,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},ao={class:"relative top-5 mx-auto p-6 border max-w-4xl shadow-lg rounded-md bg-white"},lo={class:"mt-3"},uo={class:"flex justify-between items-center mb-6"},co={class:"bg-gray-50 p-4 rounded-lg"},mo={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},po={class:"md:col-span-2"},go=["value"],bo=["value"],xo={class:"bg-gray-50 p-4 rounded-lg"},vo={class:"space-y-4"},yo={class:"bg-gray-50 p-4 rounded-lg"},fo={class:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4"},_o={class:"mb-4"},Co={class:"grid grid-cols-2 md:grid-cols-5 gap-3"},wo={class:"flex justify-end space-x-3 pt-4 border-t"},ho=["disabled"];function ko(g,e,K,s,F,x){var M,V,y,B,I,G,S,N,L,O;const T=pe("HeroIcon");return n(),i("div",fe,[t("div",_e,[t("div",Ce,[t("div",we,[e[55]||(e[55]=t("div",{class:"flex items-center"},[t("h1",{class:"text-2xl font-semibold text-gray-900"},"Case Studies & Success Stories")],-1)),s.canCreateCaseStudies?(n(),i("div",he,[t("button",{onClick:e[0]||(e[0]=o=>s.showAIModal=!0),disabled:s.isGenerating,class:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"},[s.isGenerating?(n(),ee(T,{key:0,name:"arrow-path",class:"animate-spin h-4 w-4 mr-2 inline"})):c("",!0),e[54]||(e[54]=H(" ✨ Genera con AI "))],8,ke),t("button",{onClick:e[1]||(e[1]=o=>s.showCreateModal=!0),class:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"}," + Nuovo Manuale ")])):c("",!0)])])]),t("div",ze,[s.aiGeneratedContent?(n(),i("div",Te,[t("div",Ie,[t("div",Ge,[t("div",Se,[t("div",je,[J(T,{name:"light-bulb",class:"w-5 h-5 text-purple-600"})]),t("div",Ae,[e[56]||(e[56]=t("h4",{class:"text-sm font-medium text-purple-900"}," Case Study generato con AI ",-1)),t("p",De,l(s.aiGeneratedContent.source_project?`Basato su progetto: ${s.aiGeneratedContent.source_project.name}`:"Generato da prompt personalizzato"),1)])]),t("button",{onClick:e[2]||(e[2]=(...o)=>s.applyAIContent&&s.applyAIContent(...o)),class:"inline-flex items-center px-3 py-1 text-xs font-medium text-white bg-purple-600 rounded hover:bg-purple-700"}," Applica e Salva ")])]),t("div",Pe,[t("div",null,[e[57]||(e[57]=t("h5",{class:"text-sm font-medium text-purple-900 mb-2"},"Titolo generato:",-1)),t("p",Ee,l((M=s.aiGeneratedContent.generated_content)==null?void 0:M.title),1)]),t("div",null,[e[58]||(e[58]=t("h5",{class:"text-sm font-medium text-purple-900 mb-2"},"Overview generata:",-1)),t("div",Ue,l((V=s.aiGeneratedContent.generated_content)==null?void 0:V.overview),1)]),t("div",Fe,[(y=s.aiGeneratedContent.generated_content)!=null&&y.primary_sector?(n(),i("div",Me,[e[59]||(e[59]=t("h5",{class:"text-sm font-medium text-purple-900 mb-2"},"Settore principale:",-1)),t("p",Ve,l(s.aiGeneratedContent.generated_content.primary_sector),1)])):c("",!0),(B=s.aiGeneratedContent.generated_content)!=null&&B.implementation_duration?(n(),i("div",Be,[e[60]||(e[60]=t("h5",{class:"text-sm font-medium text-purple-900 mb-2"},"Durata implementazione:",-1)),t("p",Oe,l(s.formatDuration(s.aiGeneratedContent.generated_content.implementation_duration)),1)])):c("",!0)]),(I=s.aiGeneratedContent.generated_content)!=null&&I.business_kpis&&Object.keys(s.aiGeneratedContent.generated_content.business_kpis).length?(n(),i("div",Ne,[e[61]||(e[61]=t("h5",{class:"text-sm font-medium text-purple-900 mb-2"},"KPI Business:",-1)),t("div",Le,[(n(!0),i(f,null,_(s.aiGeneratedContent.generated_content.business_kpis,(o,v)=>(n(),i("div",{key:v,class:"text-center"},[t("div",Re,l(o)+l(v.includes("_reduction")||v==="roi"?"%":""),1),t("div",Ke,l(v.replace("_"," ")),1)]))),128))])])):c("",!0),(S=(G=s.aiGeneratedContent.generated_content)==null?void 0:G.technologies)!=null&&S.length?(n(),i("div",qe,[e[62]||(e[62]=t("h5",{class:"text-sm font-medium text-purple-900 mb-2"},"Tecnologie:",-1)),t("div",He,[(n(!0),i(f,null,_(s.aiGeneratedContent.generated_content.technologies,o=>(n(),i("span",{key:o,class:"inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800"},l(o),1))),128))])])):c("",!0),t("div",Je,[t("button",{onClick:e[3]||(e[3]=o=>s.aiGeneratedContent=null),class:"text-xs text-purple-600 hover:text-purple-800"}," Nascondere anteprima AI "),t("button",{onClick:e[4]||(e[4]=(...o)=>s.copyToClipboard&&s.copyToClipboard(...o)),class:"text-xs text-purple-600 hover:text-purple-800"}," Copia contenuto ")])])])):c("",!0),t("div",We,[t("nav",Xe,[t("button",{onClick:e[5]||(e[5]=o=>s.activeTab="use-cases"),class:q([s.activeTab==="use-cases"?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300","whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"])}," Use Cases Interni ",2),t("button",{onClick:e[6]||(e[6]=o=>s.activeTab="success-stories"),class:q([s.activeTab==="success-stories"?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300","whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"])}," Success Stories ",2)])]),s.activeTab==="use-cases"?(n(),i("div",Ye,[t("div",Qe,[t("div",Ze,[t("div",null,[e[64]||(e[64]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Settore",-1)),m(t("select",{"onUpdate:modelValue":e[7]||(e[7]=o=>s.filters.sector=o),class:"w-full border border-gray-300 rounded-lg px-3 py-2"},[e[63]||(e[63]=t("option",{value:""},"Tutti i settori",-1)),(n(!0),i(f,null,_(s.sectors,o=>(n(),i("option",{key:o,value:o},l(o),9,$e))),128))],512),[[C,s.filters.sector]])]),t("div",null,[e[66]||(e[66]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Tecnologia",-1)),m(t("select",{"onUpdate:modelValue":e[8]||(e[8]=o=>s.filters.technology=o),class:"w-full border border-gray-300 rounded-lg px-3 py-2"},[e[65]||(e[65]=t("option",{value:""},"Tutte le tecnologie",-1)),(n(!0),i(f,null,_(s.technologies,o=>(n(),i("option",{key:o,value:o},l(o),9,et))),128))],512),[[C,s.filters.technology]])]),t("div",null,[e[68]||(e[68]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Stato",-1)),m(t("select",{"onUpdate:modelValue":e[9]||(e[9]=o=>s.filters.status=o),class:"w-full border border-gray-300 rounded-lg px-3 py-2"},e[67]||(e[67]=[t("option",{value:""},"Tutti gli stati",-1),t("option",{value:"draft"},"Bozza",-1),t("option",{value:"approved"},"Approvato",-1),t("option",{value:"published"},"Pubblicato",-1)]),512),[[C,s.filters.status]])])])]),s.loading?(n(),i("div",tt,e[69]||(e[69]=[t("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1)]))):s.filteredCaseStudies.length===0?(n(),i("div",st,e[70]||(e[70]=[t("p",{class:"text-gray-500"},"Nessun case study trovato con i filtri selezionati.",-1)]))):(n(),i("div",ot,[(n(!0),i(f,null,_(s.filteredCaseStudies,o=>{var v,A;return n(),i("div",{key:o.id,class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"},[t("div",rt,[t("div",nt,[t("h3",it,l(o.title),1),t("p",at,l(o.overview),1)]),t("div",lt,[t("span",{class:q([s.sectorColors[o.primary_sector]||"bg-gray-100 text-gray-800","inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},l(o.primary_sector),3),t("span",{class:q([{"bg-gray-100 text-gray-800":o.status==="draft","bg-green-100 text-green-800":o.status==="approved","bg-blue-100 text-blue-800":o.status==="published"},"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},l(o.status==="draft"?"Bozza":o.status==="approved"?"Approvato":"Pubblicato"),3)])]),t("div",dt,[(v=s.parseBusinessKpis(o.business_kpis))!=null&&v.roi?(n(),i("div",ut,[e[71]||(e[71]=t("span",{class:"text-gray-500"},"ROI:",-1)),t("span",ct,l(s.parseBusinessKpis(o.business_kpis).roi)+"%",1)])):c("",!0),o.implementation_duration?(n(),i("div",mt,[e[72]||(e[72]=t("span",{class:"text-gray-500"},"Durata:",-1)),t("span",pt,l(s.formatDuration(o.implementation_duration)),1)])):c("",!0),o.team_size?(n(),i("div",gt,[e[73]||(e[73]=t("span",{class:"text-gray-500"},"Team Size:",-1)),t("span",bt,l(o.team_size)+" persone",1)])):c("",!0)]),(A=o.technologies)!=null&&A.length?(n(),i("div",xt,[(n(!0),i(f,null,_(o.technologies.slice(0,3),k=>(n(),i("span",{key:k,class:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"},l(k),1))),128)),o.technologies.length>3?(n(),i("span",vt," +"+l(o.technologies.length-3),1)):c("",!0)])):c("",!0),t("div",yt,[t("div",ft,[t("div",_t,[o.generated_by_ai?(n(),i("div",Ct," ✨ Generato con AI ")):c("",!0),t("div",null,l(new Date(o.created_at).toLocaleDateString("it-IT")),1)])]),t("div",wt,[t("button",{onClick:k=>s.exportStory(o),class:"flex items-center justify-center px-3 py-2 text-xs font-medium text-blue-700 bg-blue-50 rounded-md hover:bg-blue-100 transition-colors"}," 📤 Esporta ",8,ht),t("button",{onClick:k=>s.viewDetails(o),class:"flex items-center justify-center px-3 py-2 text-xs font-medium text-gray-700 bg-gray-50 rounded-md hover:bg-gray-100 transition-colors"}," 👁️ Dettagli ",8,kt),s.canCreateCaseStudies&&o.status==="draft"?(n(),i("button",{key:0,onClick:k=>s.approveCaseStudy(o),class:"flex items-center justify-center px-3 py-2 text-xs font-medium text-green-700 bg-green-50 rounded-md hover:bg-green-100 transition-colors"}," ✅ Approva ",8,zt)):c("",!0),s.canCreateCaseStudies?(n(),i("button",{key:1,onClick:k=>s.deleteCaseStudy(o),class:"flex items-center justify-center px-3 py-2 text-xs font-medium text-red-700 bg-red-50 rounded-md hover:bg-red-100 transition-colors"}," 🗑️ Elimina ",8,Tt)):c("",!0)])])])}),128))]))])):c("",!0),s.activeTab==="success-stories"?(n(),i("div",It,[t("div",Gt,[t("div",St,[t("div",null,[e[75]||(e[75]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Settore",-1)),m(t("select",{"onUpdate:modelValue":e[10]||(e[10]=o=>s.filters.sector=o),class:"w-full border border-gray-300 rounded-lg px-3 py-2"},[e[74]||(e[74]=t("option",{value:""},"Tutti i settori",-1)),(n(!0),i(f,null,_(s.sectors,o=>(n(),i("option",{key:o,value:o},l(o),9,jt))),128))],512),[[C,s.filters.sector]])]),t("div",null,[e[77]||(e[77]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Tecnologia",-1)),m(t("select",{"onUpdate:modelValue":e[11]||(e[11]=o=>s.filters.technology=o),class:"w-full border border-gray-300 rounded-lg px-3 py-2"},[e[76]||(e[76]=t("option",{value:""},"Tutte le tecnologie",-1)),(n(!0),i(f,null,_(s.technologies,o=>(n(),i("option",{key:o,value:o},l(o),9,At))),128))],512),[[C,s.filters.technology]])]),t("div",null,[e[79]||(e[79]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Stato",-1)),m(t("select",{"onUpdate:modelValue":e[12]||(e[12]=o=>s.filters.status=o),class:"w-full border border-gray-300 rounded-lg px-3 py-2"},e[78]||(e[78]=[t("option",{value:""},"Tutti gli stati",-1),t("option",{value:"approved"},"Approvato",-1),t("option",{value:"published"},"Pubblicato",-1)]),512),[[C,s.filters.status]])])])]),s.loading?(n(),i("div",Dt,e[80]||(e[80]=[t("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1)]))):s.filteredCaseStudies.length===0?(n(),i("div",Pt,e[81]||(e[81]=[t("p",{class:"text-gray-500"},"Nessun success story trovato con i filtri selezionati.",-1)]))):(n(),i("div",Et,[(n(!0),i(f,null,_(s.filteredCaseStudies,o=>{var v,A;return n(),i("div",{key:o.id,class:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden"},[t("div",Ut,[t("div",Ft,[t("div",Mt,[t("span",Vt,l(((v=o.client_name)==null?void 0:v.charAt(0))||((A=o.primary_sector)==null?void 0:A.charAt(0))||"C"),1)]),t("div",Bt,[t("h3",Ot,l(o.title),1),t("p",Nt,l(o.primary_sector),1)])]),t("blockquote",Lt,' "'+l(o.overview)+'" ',1),o.business_kpis&&Object.keys(s.parseBusinessKpis(o.business_kpis)).length?(n(),i("div",Rt,[s.parseBusinessKpis(o.business_kpis).roi?(n(),i("div",Kt,[t("div",qt,l(s.parseBusinessKpis(o.business_kpis).roi)+"%",1),e[82]||(e[82]=t("div",{class:"text-xs text-gray-500"},"ROI",-1))])):c("",!0),s.parseBusinessKpis(o.business_kpis).efficiency_gain?(n(),i("div",Ht,[t("div",Jt,l(s.parseBusinessKpis(o.business_kpis).efficiency_gain)+"%",1),e[83]||(e[83]=t("div",{class:"text-xs text-gray-500"},"Efficienza",-1))])):c("",!0),s.parseBusinessKpis(o.business_kpis).time_reduction?(n(),i("div",Wt,[t("div",Xt,l(s.parseBusinessKpis(o.business_kpis).time_reduction)+"%",1),e[84]||(e[84]=t("div",{class:"text-xs text-gray-500"},"Riduzione Tempi",-1))])):c("",!0)])):c("",!0),t("div",Yt,[t("div",Qt,[t("span",Zt,l(new Date(o.created_at).toLocaleDateString("it-IT")),1),o.generated_by_ai?(n(),i("span",$t,"✨ AI")):c("",!0)]),t("div",es,[t("button",{onClick:k=>s.exportStory(o),class:"flex items-center justify-center px-3 py-2 text-xs font-medium text-blue-700 bg-blue-50 rounded-md hover:bg-blue-100 transition-colors"}," 📤 Esporta ",8,ts),t("button",{onClick:k=>s.viewDetails(o),class:"flex items-center justify-center px-3 py-2 text-xs font-medium text-gray-700 bg-gray-50 rounded-md hover:bg-gray-100 transition-colors"}," 👁️ Dettagli ",8,ss),s.canCreateCaseStudies&&o.status==="draft"?(n(),i("button",{key:0,onClick:k=>s.approveCaseStudy(o),class:"flex items-center justify-center px-3 py-2 text-xs font-medium text-green-700 bg-green-50 rounded-md hover:bg-green-100 transition-colors"}," ✅ Approva ",8,os)):c("",!0),s.canCreateCaseStudies?(n(),i("button",{key:1,onClick:k=>s.deleteCaseStudy(o),class:"flex items-center justify-center px-3 py-2 text-xs font-medium text-red-700 bg-red-50 rounded-md hover:bg-red-100 transition-colors"}," 🗑️ Elimina ",8,rs)):c("",!0)])])])])}),128))]))])):c("",!0),s.activeTab==="ai-generator"?(n(),i("div",ns,[t("div",is,[e[95]||(e[95]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"AI Content Generator",-1)),t("div",as,[t("div",ls,[t("div",null,[e[86]||(e[86]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Progetto di Riferimento",-1)),m(t("select",{"onUpdate:modelValue":e[13]||(e[13]=o=>s.aiGenerator.selectedProject=o),class:"w-full border border-gray-300 rounded-lg px-3 py-2"},[e[85]||(e[85]=t("option",{value:""},"Seleziona progetto completato",-1)),(n(!0),i(f,null,_(g.completedProjects,o=>(n(),i("option",{key:o.id,value:o.id},l(o.name)+" - "+l(o.client),9,ds))),128))],512),[[C,s.aiGenerator.selectedProject]])]),t("div",null,[e[88]||(e[88]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Tipo di Content",-1)),m(t("select",{"onUpdate:modelValue":e[14]||(e[14]=o=>s.aiGenerator.contentType=o),class:"w-full border border-gray-300 rounded-lg px-3 py-2"},e[87]||(e[87]=[t("option",{value:"use-case"},"Use Case Interno",-1),t("option",{value:"success-story"},"Success Story Marketing",-1),t("option",{value:"case-study"},"Case Study Dettagliato",-1)]),512),[[C,s.aiGenerator.contentType]])]),t("div",null,[e[90]||(e[90]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Target Audience",-1)),m(t("select",{"onUpdate:modelValue":e[15]||(e[15]=o=>s.aiGenerator.audience=o),class:"w-full border border-gray-300 rounded-lg px-3 py-2"},e[89]||(e[89]=[t("option",{value:"internal"},"Team Interno",-1),t("option",{value:"prospects"},"Prospect/Clienti",-1),t("option",{value:"partners"},"Partner/Investitori",-1)]),512),[[C,s.aiGenerator.audience]])]),t("div",null,[e[91]||(e[91]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Focus Specifici",-1)),m(t("textarea",{"onUpdate:modelValue":e[16]||(e[16]=o=>s.aiGenerator.focusPoints=o),rows:"3",placeholder:"Es: ROI, innovazione tecnologica, scalabilità...",class:"w-full border border-gray-300 rounded-lg px-3 py-2"},null,512),[[j,s.aiGenerator.focusPoints]])]),t("button",{onClick:e[17]||(e[17]=(...o)=>s.generateAIContent&&s.generateAIContent(...o)),disabled:!s.aiGenerator.selectedProject||s.isGenerating,class:"w-full px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 disabled:opacity-50"},[s.isGenerating?(n(),ee(T,{key:0,name:"arrow-path",class:"animate-spin h-4 w-4 mr-2 inline"})):c("",!0),e[92]||(e[92]=H(" Genera Content con AI "))],8,us)]),t("div",cs,[s.aiGenerator.output?(n(),i("div",ps,[t("div",gs,[e[94]||(e[94]=t("h4",{class:"font-medium text-gray-900"},"Content Generato",-1)),t("div",bs,[t("button",{onClick:e[18]||(e[18]=(...o)=>s.copyToClipboard&&s.copyToClipboard(...o)),class:"text-blue-600 hover:text-blue-800 text-sm"}," Copia "),t("button",{onClick:e[19]||(e[19]=(...o)=>g.saveContent&&g.saveContent(...o)),class:"text-green-600 hover:text-green-800 text-sm"}," Salva ")])]),t("div",{class:"prose max-w-none text-sm",innerHTML:s.aiGenerator.output},null,8,xs)])):(n(),i("div",ms,[J(T,{name:"light-bulb",class:"mx-auto h-12 w-12 text-gray-400"}),e[93]||(e[93]=t("p",{class:"mt-2"},"Il content generato dall'AI apparirà qui",-1))]))])])])])):c("",!0)]),s.showDetailsModal?(n(),i("div",vs,[t("div",ys,[t("div",fs,[t("div",_s,[t("h3",Cs,l((N=s.selectedCaseStudy)==null?void 0:N.title),1),t("button",{onClick:e[20]||(e[20]=o=>s.showDetailsModal=!1),class:"text-gray-400 hover:text-gray-600"},[J(T,{name:"x-mark",class:"w-6 h-6"})])]),s.selectedCaseStudy?(n(),i("div",ws,[t("div",hs,[t("span",{class:q([s.selectedCaseStudy.status==="approved"?"bg-green-100 text-green-800":s.selectedCaseStudy.status==="draft"?"bg-yellow-100 text-yellow-800":"bg-blue-100 text-blue-800","inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},l(s.selectedCaseStudy.status==="approved"?"Approvato":s.selectedCaseStudy.status==="draft"?"Bozza":s.selectedCaseStudy.status),3),s.selectedCaseStudy.generated_by_ai?(n(),i("span",ks," ✨ Generato con AI ")):c("",!0)]),t("div",null,[e[96]||(e[96]=t("h4",{class:"text-lg font-medium text-gray-900 mb-3"},"Panoramica",-1)),t("p",zs,l(s.selectedCaseStudy.overview),1)]),t("div",null,[e[97]||(e[97]=t("h4",{class:"text-lg font-medium text-gray-900 mb-3"},"Contenuto Dettagliato",-1)),t("div",Ts,l(s.selectedCaseStudy.content),1)]),t("div",Is,[s.selectedCaseStudy.primary_sector?(n(),i("div",Gs,[e[98]||(e[98]=t("h5",{class:"text-sm font-medium text-gray-900 mb-1"},"Settore Principale",-1)),t("p",Ss,l(s.selectedCaseStudy.primary_sector),1)])):c("",!0),s.selectedCaseStudy.implementation_duration?(n(),i("div",js,[e[99]||(e[99]=t("h5",{class:"text-sm font-medium text-gray-900 mb-1"},"Durata Implementazione",-1)),t("p",As,l(s.formatDuration(s.selectedCaseStudy.implementation_duration)),1)])):c("",!0),s.selectedCaseStudy.team_size?(n(),i("div",Ds,[e[100]||(e[100]=t("h5",{class:"text-sm font-medium text-gray-900 mb-1"},"Team Size",-1)),t("p",Ps,l(s.selectedCaseStudy.team_size)+" persone",1)])):c("",!0)]),s.selectedCaseStudy.business_kpis&&Object.keys(s.parseBusinessKpis(s.selectedCaseStudy.business_kpis)).length?(n(),i("div",Es,[e[101]||(e[101]=t("h4",{class:"text-lg font-medium text-gray-900 mb-3"},"KPI Business",-1)),t("div",Us,[(n(!0),i(f,null,_(s.parseBusinessKpis(s.selectedCaseStudy.business_kpis),(o,v)=>(n(),i("div",{key:v,class:"text-center bg-gray-50 p-3 rounded-lg"},[t("div",Fs,l(o)+l(v.includes("_reduction")||v==="roi"?"%":""),1),t("div",Ms,l(v.replace("_"," ")),1)]))),128))])])):c("",!0),(L=s.selectedCaseStudy.technologies)!=null&&L.length?(n(),i("div",Vs,[e[102]||(e[102]=t("h4",{class:"text-lg font-medium text-gray-900 mb-3"},"Tecnologie Utilizzate",-1)),t("div",Bs,[(n(!0),i(f,null,_(s.selectedCaseStudy.technologies,o=>(n(),i("span",{key:o,class:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800"},l(o),1))),128))])])):c("",!0),(O=s.selectedCaseStudy.secondary_sectors)!=null&&O.length?(n(),i("div",Os,[e[103]||(e[103]=t("h4",{class:"text-lg font-medium text-gray-900 mb-3"},"Settori Applicabili",-1)),t("div",Ns,[(n(!0),i(f,null,_(s.selectedCaseStudy.secondary_sectors,o=>(n(),i("span",{key:o,class:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800"},l(o),1))),128))])])):c("",!0),t("div",Ls,[t("div",Rs,[t("button",{onClick:e[21]||(e[21]=o=>s.exportStory(s.selectedCaseStudy,"html")),class:"px-3 py-2 text-sm font-medium text-blue-700 bg-blue-50 rounded-md hover:bg-blue-100"}," 📄 HTML "),t("button",{onClick:e[22]||(e[22]=o=>s.exportStory(s.selectedCaseStudy,"txt")),class:"px-3 py-2 text-sm font-medium text-gray-700 bg-gray-50 rounded-md hover:bg-gray-100"}," 📝 TXT "),t("button",{onClick:e[23]||(e[23]=o=>s.exportStory(s.selectedCaseStudy,"json")),class:"px-3 py-2 text-sm font-medium text-purple-700 bg-purple-50 rounded-md hover:bg-purple-100"}," 🔧 JSON ")]),t("div",Ks,[t("button",{onClick:e[24]||(e[24]=o=>s.showDetailsModal=!1),class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"}," Chiudi "),s.canCreateCaseStudies&&s.selectedCaseStudy.status==="draft"?(n(),i("button",{key:0,onClick:e[25]||(e[25]=o=>{s.approveCaseStudy(s.selectedCaseStudy),s.showDetailsModal=!1}),class:"px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-md hover:bg-green-700"}," Approva ")):c("",!0)])])])):c("",!0)])])])):c("",!0),s.showAIModal?(n(),i("div",qs,[t("div",Hs,[t("div",Js,[e[118]||(e[118]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"}," ✨ Genera Case Study con AI ",-1)),t("form",{onSubmit:e[35]||(e[35]=te((...o)=>s.generateAIContent&&s.generateAIContent(...o),["prevent"]))},[t("div",Ws,[t("div",null,[e[106]||(e[106]=t("label",{class:"block text-sm font-medium text-gray-700"}," Modalità di Generazione * ",-1)),t("div",Xs,[t("label",Ys,[m(t("input",{type:"radio","onUpdate:modelValue":e[26]||(e[26]=o=>s.aiGenerator.mode=o),value:"project",class:"mr-2"},null,512),[[se,s.aiGenerator.mode]]),e[104]||(e[104]=H(" Basato su Progetto Esistente "))]),t("label",Qs,[m(t("input",{type:"radio","onUpdate:modelValue":e[27]||(e[27]=o=>s.aiGenerator.mode=o),value:"custom",class:"mr-2"},null,512),[[se,s.aiGenerator.mode]]),e[105]||(e[105]=H(" Prompt Personalizzato "))])])]),t("div",null,[e[108]||(e[108]=t("label",{class:"block text-sm font-medium text-gray-700"}," Tipo di Case Study * ",-1)),m(t("select",{"onUpdate:modelValue":e[28]||(e[28]=o=>s.aiGenerator.caseType=o),required:"",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"},e[107]||(e[107]=[t("option",{value:"use-case"},"Use Case Interno",-1),t("option",{value:"success-story"},"Success Story Marketing",-1),t("option",{value:"case-study"},"Case Study Dettagliato",-1)]),512),[[C,s.aiGenerator.caseType]])]),s.aiGenerator.mode==="project"?(n(),i("div",Zs,[e[110]||(e[110]=t("label",{class:"block text-sm font-medium text-gray-700"}," Progetto di Riferimento * ",-1)),m(t("select",{"onUpdate:modelValue":e[29]||(e[29]=o=>s.aiGenerator.selectedProject=o),required:s.aiGenerator.mode==="project",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"},[e[109]||(e[109]=t("option",{value:""},"Seleziona progetto completato",-1)),(n(!0),i(f,null,_(s.availableProjects,o=>(n(),i("option",{key:o.id,value:o.id},l(o.name)+" - "+l(o.client_name||"Cliente N/A"),9,eo))),128))],8,$s),[[C,s.aiGenerator.selectedProject]])])):c("",!0),t("div",null,[e[112]||(e[112]=t("label",{class:"block text-sm font-medium text-gray-700"}," Target Audience ",-1)),m(t("select",{"onUpdate:modelValue":e[30]||(e[30]=o=>s.aiGenerator.targetAudience=o),class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"},e[111]||(e[111]=[t("option",{value:"internal"},"Team Interno",-1),t("option",{value:"prospects"},"Prospect/Clienti",-1),t("option",{value:"partners"},"Partner/Investitori",-1)]),512),[[C,s.aiGenerator.targetAudience]])]),s.aiGenerator.mode==="custom"?(n(),i("div",to,[e[113]||(e[113]=t("label",{class:"block text-sm font-medium text-gray-700"}," Prompt Personalizzato * ",-1)),m(t("textarea",{"onUpdate:modelValue":e[31]||(e[31]=o=>s.aiGenerator.customPrompt=o),required:s.aiGenerator.mode==="custom",rows:"6",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500",placeholder:"Descrivi in dettaglio il case study che vuoi generare: contesto, obiettivi, sfide, soluzioni, risultati attesi, tecnologie coinvolte, metriche di successo..."},null,8,so),[[j,s.aiGenerator.customPrompt]]),e[114]||(e[114]=t("p",{class:"mt-1 text-xs text-gray-500"}," Più dettagli fornisci, più preciso sarà il case study generato dall'AI ",-1))])):c("",!0),t("div",null,[e[116]||(e[116]=t("label",{class:"block text-sm font-medium text-gray-700"}," Settore di Riferimento ",-1)),m(t("select",{"onUpdate:modelValue":e[32]||(e[32]=o=>s.aiGenerator.sector=o),class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"},[e[115]||(e[115]=t("option",{value:""},"Seleziona settore...",-1)),(n(!0),i(f,null,_(s.sectors,o=>(n(),i("option",{key:o,value:o},l(o),9,oo))),128))],512),[[C,s.aiGenerator.sector]])]),t("div",null,[e[117]||(e[117]=t("label",{class:"block text-sm font-medium text-gray-700"}," Focus Specifici ",-1)),m(t("input",{"onUpdate:modelValue":e[33]||(e[33]=o=>s.aiGenerator.focusPoints=o),type:"text",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500",placeholder:"Es: ROI, innovazione tecnologica, scalabilità..."},null,512),[[j,s.aiGenerator.focusPoints]])])]),t("div",ro,[t("button",{type:"button",onClick:e[34]||(e[34]=o=>{s.showAIModal=!1,s.aiGenerator.mode="project"}),class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"}," Annulla "),t("button",{type:"submit",disabled:s.isGenerating,class:"px-4 py-2 text-sm font-medium text-white bg-purple-600 rounded-md hover:bg-purple-700 disabled:opacity-50"},l(s.isGenerating?"Generando...":"✨ Genera Case Study"),9,no)])],32)])])])):c("",!0),s.showCreateModal?(n(),i("div",io,[t("div",ao,[t("div",lo,[t("div",uo,[e[119]||(e[119]=t("h3",{class:"text-xl font-semibold text-gray-900"}," Crea Nuovo Case Study ",-1)),t("button",{onClick:e[36]||(e[36]=o=>{s.showCreateModal=!1,s.resetCreateForm()}),class:"text-gray-400 hover:text-gray-600"},[J(T,{name:"x-mark",class:"w-6 h-6"})])]),t("form",{onSubmit:e[53]||(e[53]=te((...o)=>s.createManualCaseStudy&&s.createManualCaseStudy(...o),["prevent"])),class:"space-y-6"},[t("div",co,[e[129]||(e[129]=t("h4",{class:"text-lg font-medium text-gray-900 mb-4"},"Informazioni Base",-1)),t("div",mo,[t("div",po,[e[120]||(e[120]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Titolo Case Study * ",-1)),m(t("input",{"onUpdate:modelValue":e[37]||(e[37]=o=>s.createForm.title=o),type:"text",required:"",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Es: Digitalizzazione Completa per TechCorp: +40% Efficienza"},null,512),[[j,s.createForm.title]])]),t("div",null,[e[122]||(e[122]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Tipo Case Study * ",-1)),m(t("select",{"onUpdate:modelValue":e[38]||(e[38]=o=>s.createForm.case_type=o),required:"",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"},e[121]||(e[121]=[t("option",{value:"use-case"},"Use Case Interno",-1),t("option",{value:"success-story"},"Success Story Marketing",-1),t("option",{value:"case-study"},"Case Study Dettagliato",-1)]),512),[[C,s.createForm.case_type]])]),t("div",null,[e[124]||(e[124]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Target Audience ",-1)),m(t("select",{"onUpdate:modelValue":e[39]||(e[39]=o=>s.createForm.target_audience=o),class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"},e[123]||(e[123]=[t("option",{value:"internal"},"Team Interno",-1),t("option",{value:"prospects"},"Prospect/Clienti",-1),t("option",{value:"partners"},"Partner/Investitori",-1)]),512),[[C,s.createForm.target_audience]])]),t("div",null,[e[126]||(e[126]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Settore Principale * ",-1)),m(t("select",{"onUpdate:modelValue":e[40]||(e[40]=o=>s.createForm.primary_sector=o),required:"",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"},[e[125]||(e[125]=t("option",{value:""},"Seleziona settore...",-1)),(n(!0),i(f,null,_(s.sectors,o=>(n(),i("option",{key:o,value:o},l(o),9,go))),128))],512),[[C,s.createForm.primary_sector]])]),t("div",null,[e[128]||(e[128]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Progetto di Riferimento ",-1)),m(t("select",{"onUpdate:modelValue":e[41]||(e[41]=o=>s.createForm.project_id=o),class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"},[e[127]||(e[127]=t("option",{value:""},"Nessun progetto specifico",-1)),(n(!0),i(f,null,_(s.availableProjects,o=>(n(),i("option",{key:o.id,value:o.id},l(o.name)+" - "+l(o.client_name||"Cliente N/A"),9,bo))),128))],512),[[C,s.createForm.project_id]])])])]),t("div",xo,[e[132]||(e[132]=t("h4",{class:"text-lg font-medium text-gray-900 mb-4"},"Contenuto",-1)),t("div",vo,[t("div",null,[e[130]||(e[130]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Overview/Panoramica * ",-1)),m(t("textarea",{"onUpdate:modelValue":e[42]||(e[42]=o=>s.createForm.overview=o),required:"",rows:"3",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Breve panoramica del case study in 2-3 frasi..."},null,512),[[j,s.createForm.overview]])]),t("div",null,[e[131]||(e[131]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Contenuto Dettagliato * ",-1)),m(t("textarea",{"onUpdate:modelValue":e[43]||(e[43]=o=>s.createForm.content=o),required:"",rows:"8",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Descrizione dettagliata: sfida, soluzione, implementazione, risultati..."},null,512),[[j,s.createForm.content]])])])]),t("div",yo,[e[143]||(e[143]=t("h4",{class:"text-lg font-medium text-gray-900 mb-4"},"Metriche e Dettagli",-1)),t("div",fo,[t("div",null,[e[133]||(e[133]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Durata Implementazione (giorni) ",-1)),m(t("input",{"onUpdate:modelValue":e[44]||(e[44]=o=>s.createForm.implementation_duration=o),type:"number",min:"1",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"90"},null,512),[[j,s.createForm.implementation_duration,void 0,{number:!0}]])]),t("div",null,[e[134]||(e[134]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Team Size (persone) ",-1)),m(t("input",{"onUpdate:modelValue":e[45]||(e[45]=o=>s.createForm.team_size=o),type:"number",min:"1",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"5"},null,512),[[j,s.createForm.team_size,void 0,{number:!0}]])])]),t("div",_o,[e[140]||(e[140]=t("h5",{class:"text-sm font-medium text-gray-700 mb-3"},"KPI Business (%)",-1)),t("div",Co,[t("div",null,[e[135]||(e[135]=t("label",{class:"block text-xs text-gray-500 mb-1"},"Riduzione Tempo",-1)),m(t("input",{"onUpdate:modelValue":e[46]||(e[46]=o=>s.createForm.business_kpis.time_reduction=o),type:"number",min:"0",max:"100",class:"w-full border border-gray-300 rounded px-2 py-1 text-sm",placeholder:"40"},null,512),[[j,s.createForm.business_kpis.time_reduction,void 0,{number:!0}]])]),t("div",null,[e[136]||(e[136]=t("label",{class:"block text-xs text-gray-500 mb-1"},"Riduzione Costi",-1)),m(t("input",{"onUpdate:modelValue":e[47]||(e[47]=o=>s.createForm.business_kpis.cost_reduction=o),type:"number",min:"0",max:"100",class:"w-full border border-gray-300 rounded px-2 py-1 text-sm",placeholder:"30"},null,512),[[j,s.createForm.business_kpis.cost_reduction,void 0,{number:!0}]])]),t("div",null,[e[137]||(e[137]=t("label",{class:"block text-xs text-gray-500 mb-1"},"ROI",-1)),m(t("input",{"onUpdate:modelValue":e[48]||(e[48]=o=>s.createForm.business_kpis.roi=o),type:"number",min:"0",class:"w-full border border-gray-300 rounded px-2 py-1 text-sm",placeholder:"180"},null,512),[[j,s.createForm.business_kpis.roi,void 0,{number:!0}]])]),t("div",null,[e[138]||(e[138]=t("label",{class:"block text-xs text-gray-500 mb-1"},"Guadagno Efficienza",-1)),m(t("input",{"onUpdate:modelValue":e[49]||(e[49]=o=>s.createForm.business_kpis.efficiency_gain=o),type:"number",min:"0",max:"100",class:"w-full border border-gray-300 rounded px-2 py-1 text-sm",placeholder:"45"},null,512),[[j,s.createForm.business_kpis.efficiency_gain,void 0,{number:!0}]])]),t("div",null,[e[139]||(e[139]=t("label",{class:"block text-xs text-gray-500 mb-1"},"Soddisfazione Utenti",-1)),m(t("input",{"onUpdate:modelValue":e[50]||(e[50]=o=>s.createForm.business_kpis.user_satisfaction=o),type:"number",min:"0",max:"100",class:"w-full border border-gray-300 rounded px-2 py-1 text-sm",placeholder:"95"},null,512),[[j,s.createForm.business_kpis.user_satisfaction,void 0,{number:!0}]])])])]),t("div",null,[e[141]||(e[141]=t("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Tecnologie Utilizzate ",-1)),t("input",{type:"text",placeholder:"Es: Vue.js, Python, PostgreSQL (separati da virgola)",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500",onInput:e[51]||(e[51]=(...o)=>s.updateTechnologies&&s.updateTechnologies(...o))},null,32),e[142]||(e[142]=t("p",{class:"text-xs text-gray-500 mt-1"},"Inserisci le tecnologie separate da virgole",-1))])]),t("div",wo,[t("button",{type:"button",onClick:e[52]||(e[52]=o=>{s.showCreateModal=!1,s.resetCreateForm()}),class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"}," Annulla "),t("button",{type:"submit",disabled:s.loading,class:"px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50"},l(s.loading?"Creando...":"Crea Case Study"),9,ho)])],32)])])])):c("",!0)])}const Go=be(ye,[["render",ko]]);export{Go as default};
