import{u as a}from"./jsx-runtime-BDYu3_Il.js";function f(){return a("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 261.76 226.69",fill:"currentColor",children:[a("title",{children:"Vue Logo"}),a("g",{xmlns:"http://www.w3.org/2000/svg",transform:"matrix(1.3333 0 0 -1.3333 -76.311 313.34)",children:[a("g",{transform:"translate(178.06 235.01)",fillOpacity:"0.5",children:a("path",{d:"m0 0-22.669-39.264-22.669 39.264h-75.491l98.16-170.02 98.16 170.02z"})}),a("g",{transform:"translate(178.06 235.01)","fill-opacity":"1",children:a("path",{d:"m0 0-22.669-39.264-22.669 39.264h-36.227l58.896-102.01 58.896 102.01z"})})]})]})}let c=!1;function u(r){var t;if(!r)return null;const e=[],s=3;let n=r;for(;n&&e.length<s;){const l=n.__vueParentComponent;if((t=l==null?void 0:l.type)!=null&&t.__name){const i=l.type.__name;e.some(o=>o.name===i)||e.push({name:i,type:"regular"})}let m=[];n.__vms__&&Array.isArray(n.__vms__)?m=n.__vms__:n.__vue__&&(m=[n.__vue__]);for(const i of m){if(!i||!i.$options)continue;let o=i.$options.name||i.$options.__file||i.$options._componentTag||"AnonymousComponent";o&&typeof o=="string"&&o.includes("/")&&(o=(String(o).split("/").pop()||"").replace(/\.vue$/,"")),e.some(d=>d.name===o)||e.push({name:o,type:"regular"})}n=n.parentElement}return e.length===0&&!c&&(console.warn("[stagewise/vue] No Vue installation detected on the selected element. Make sure you are running in development mode and Vue is available."),c=!0),e.length>0?e:null}function p(r){const t=u(r);return t!=null&&t[0]?{annotation:`${t[0].name}`}:{annotation:null}}function h(r){const t=r.map(e=>u(e)||[]);return t.some(e=>e.length>0)?`This is additional information on the elements that the user selected. Use this information to find the correct element in the codebase.

  ${t.map((s,n)=>`
<element index="${n+1}">
  ${s.length===0?"No Vue component as parent detected":`Vue component tree (from closest to farthest, 3 closest elements): ${s.map(l=>`{name: ${l.name}, type: ${l.type}}`).join(" child of ")}`}
</element>
    `)}
  `:null}const g={displayName:"Vue",description:"This toolbar adds additional information and metadata for apps using Vue as an UI framework",iconSvg:a(f,{}),pluginName:"vue",onContextElementHover:p,onContextElementSelect:p,onPromptSend:r=>({contextSnippets:[{promptContextName:"elements-vue-component-info",content:h(r.contextElements)}]})};export{g as VuePlugin};
