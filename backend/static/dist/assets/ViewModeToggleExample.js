import{V as g}from"./ViewModeToggle.js";import{_ as f,H as m}from"./app.js";import{r as v,b as s,l as e,e as d,v as y,t as i,O as u,F as w,q as _,j as o}from"./vendor.js";const M={class:"space-y-8"},V={class:"space-y-4"},z={class:"bg-gray-50 dark:bg-gray-800 rounded-lg p-6"},C={class:"mb-4"},T={class:"text-sm text-gray-600 dark:text-gray-400"},I={class:"space-y-4"},G={class:"bg-gray-50 dark:bg-gray-800 rounded-lg p-6"},L={class:"mb-6"},q={class:"bg-white dark:bg-gray-700 rounded-lg p-4 min-h-48"},B={key:0,class:"space-y-2"},D={key:1,class:"text-center"},N={class:"w-32 h-32 bg-brand-primary-100 rounded-full mx-auto flex items-center justify-center"},A={key:2,class:"space-y-2"},E={key:3,class:"text-center"},P={class:"space-y-2"},S={class:"flex items-center justify-between p-2 bg-gray-100 dark:bg-gray-600 rounded"},j={class:"flex items-center justify-between p-2 bg-gray-100 dark:bg-gray-600 rounded"},U={class:"space-y-4"},H={class:"bg-gray-50 dark:bg-gray-800 rounded-lg p-6"},F={class:"mb-4"},K={class:"text-sm text-gray-600 dark:text-gray-400"},O={class:"space-y-4"},R={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},J={class:"overflow-x-auto"},Q={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},W={class:"divide-y divide-gray-200 dark:divide-gray-700"},X={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white"},Y={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"},Z={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"},$={class:"px-6 py-4 text-sm text-gray-500 dark:text-gray-400"},ee={__name:"ViewModeToggleExample",setup(te){const l=v("list"),p=[{id:"list",label:"Lista",icon:"list-bullet"},{id:"grid",label:"Griglia",icon:"squares-2x2"},{id:"chart",label:"Grafico",icon:"chart-bar"}],r=v("table"),b=[{id:"table",label:"Tabella",icon:"table-cells"},{id:"chart",label:"Grafico",icon:"chart-pie"},{id:"grid",label:"Griglia",icon:"squares-plus"},{id:"list",label:"Lista",icon:"bars-3"}],n=v("kanban"),x=[{id:"kanban",label:"Kanban",icon:"view-columns"},{id:"timeline",label:"Timeline",icon:"clock"},{id:"calendar",label:"Calendario",icon:"calendar-days"},{id:"map",label:"Mappa",icon:"map"}],h=c=>{const t=x.find(a=>a.id===c);return t?t.label:c},k=[{name:"modes",type:"Array",required:!0,description:"Array di oggetti mode con id, label e icon"},{name:"activeMode",type:"String",required:!0,description:"ID della modalità attualmente attiva"}];return(c,t)=>(o(),s("div",M,[t[16]||(t[16]=e("div",null,[e("h2",{class:"text-2xl font-bold text-gray-900 dark:text-white mb-2"},"ViewModeToggle Component"),e("p",{class:"text-gray-600 dark:text-gray-400"}," Componente toggle per cambiare modalità di visualizzazione (list, grid, chart, ecc.). ")],-1)),e("section",V,[t[4]||(t[4]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white"},"Utilizzo Base",-1)),e("div",z,[e("div",C,[d(g,{modes:p,"active-mode":l.value,onModeChange:t[0]||(t[0]=a=>l.value=a)},null,8,["active-mode"])]),e("div",T,[t[3]||(t[3]=y(" Modalità selezionata: ")),e("strong",null,i(l.value),1)])])]),e("section",I,[t[11]||(t[11]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white"},"Utilizzo Avanzato - Data Visualization",-1)),e("div",G,[e("div",L,[d(g,{modes:b,"active-mode":r.value,onModeChange:t[1]||(t[1]=a=>r.value=a)},null,8,["active-mode"])]),e("div",q,[r.value==="table"?(o(),s("div",B,t[5]||(t[5]=[u('<h4 class="font-medium text-gray-900 dark:text-white" data-v-9e7a1e3d>Vista Tabella</h4><div class="grid grid-cols-3 gap-4 text-sm" data-v-9e7a1e3d><div class="font-medium" data-v-9e7a1e3d>Nome</div><div class="font-medium" data-v-9e7a1e3d>Valore</div><div class="font-medium" data-v-9e7a1e3d>Status</div><div data-v-9e7a1e3d>Item 1</div><div data-v-9e7a1e3d>100</div><div class="text-green-600" data-v-9e7a1e3d>Attivo</div><div data-v-9e7a1e3d>Item 2</div><div data-v-9e7a1e3d>75</div><div class="text-yellow-600" data-v-9e7a1e3d>Pending</div></div>',2)]))):r.value==="chart"?(o(),s("div",D,[t[6]||(t[6]=e("h4",{class:"font-medium text-gray-900 dark:text-white mb-4"},"Vista Grafico",-1)),e("div",N,[d(m,{name:"chart-pie",size:"xl",class:"text-brand-primary-600"})])])):r.value==="grid"?(o(),s("div",A,t[7]||(t[7]=[u('<h4 class="font-medium text-gray-900 dark:text-white" data-v-9e7a1e3d>Vista Griglia</h4><div class="grid grid-cols-2 md:grid-cols-3 gap-4" data-v-9e7a1e3d><div class="bg-gray-100 dark:bg-gray-600 rounded p-3 text-center" data-v-9e7a1e3d>Card 1</div><div class="bg-gray-100 dark:bg-gray-600 rounded p-3 text-center" data-v-9e7a1e3d>Card 2</div><div class="bg-gray-100 dark:bg-gray-600 rounded p-3 text-center" data-v-9e7a1e3d>Card 3</div></div>',2)]))):(o(),s("div",E,[t[10]||(t[10]=e("h4",{class:"font-medium text-gray-900 dark:text-white mb-4"},"Vista Lista",-1)),e("div",P,[e("div",S,[t[8]||(t[8]=e("span",null,"Lista Item 1",-1)),d(m,{name:"chevron-right",size:"sm"})]),e("div",j,[t[9]||(t[9]=e("span",null,"Lista Item 2",-1)),d(m,{name:"chevron-right",size:"sm"})])])]))])])]),e("section",U,[t[13]||(t[13]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white"},"Toggle con Icone Personalizzate",-1)),e("div",H,[e("div",F,[d(g,{modes:x,"active-mode":n.value,onModeChange:t[2]||(t[2]=a=>n.value=a)},null,8,["active-mode"])]),e("div",K,[t[12]||(t[12]=y(" Vista corrente: ")),e("strong",null,i(h(n.value)),1)])])]),e("section",O,[t[15]||(t[15]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white"},"Props & API",-1)),e("div",R,[e("div",J,[e("table",Q,[t[14]||(t[14]=e("thead",null,[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"},"Prop"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"},"Tipo"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"},"Richiesto"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"},"Descrizione")])],-1)),e("tbody",W,[(o(),s(w,null,_(k,a=>e("tr",{key:a.name},[e("td",X,i(a.name),1),e("td",Y,i(a.type),1),e("td",Z,i(a.required?"Sì":"No"),1),e("td",$,i(a.description),1)])),64))])])])])]),t[17]||(t[17]=e("section",{class:"space-y-4"},[e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white"},"Esempi di Codice"),e("div",{class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},[e("pre",{class:"bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto text-sm"},[e("code",null,`// Basic Usage
<ViewModeToggle
  :modes="modes"
  :active-mode="currentMode"
  @mode-change="handleModeChange"
/>

// Mode Configuration
const modes = [
  { id: 'list', label: 'Lista', icon: 'list-bullet' },
  { id: 'grid', label: 'Griglia', icon: 'squares-2x2' },
  { id: 'chart', label: 'Grafico', icon: 'chart-bar' }
]

// Event Handler
const handleModeChange = (newMode) => {
  currentMode.value = newMode
  // Update your view based on the selected mode
}`)])])],-1))]))}},ie=f(ee,[["__scopeId","data-v-9e7a1e3d"]]);export{ie as default};
