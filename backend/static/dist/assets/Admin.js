import{r as i,c as G,o as H,b as n,l as e,g as f,e as U,v as O,B as m,C as p,F as Q,q as J,t as o,A as N,S as K,O as W,P as X,j as d,n as C}from"./vendor.js";import{e as Y,H as A,c as R}from"./app.js";const Z={class:"flex flex-col md:flex-row md:items-center md:justify-between mb-8"},ee={class:"mt-4 md:mt-0"},te={class:"mb-6"},ae={class:"relative rounded-md shadow-sm"},se={class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},re={class:"bg-white dark:bg-gray-800 shadow overflow-hidden rounded-lg"},oe={key:0,class:"p-6 text-center"},le={key:1},ie={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},ne={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},de={class:"px-6 py-4 whitespace-nowrap"},ue={class:"flex items-center"},me={class:"flex-shrink-0 h-10 w-10"},ge={class:"h-10 w-10 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center"},ce={class:"text-primary-600 dark:text-primary-300 font-medium text-lg"},pe={class:"ml-4"},xe={class:"text-sm font-medium text-gray-900 dark:text-white"},ye={class:"text-sm text-gray-500 dark:text-gray-400"},ve={class:"px-6 py-4 whitespace-nowrap"},fe={class:"text-sm text-gray-900 dark:text-white"},be={class:"px-6 py-4 whitespace-nowrap"},ke={class:"px-6 py-4 whitespace-nowrap"},we={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},he=["onClick"],_e=["onClick"],Ue=["onClick"],Ce={key:0,class:"p-6 text-center"},Ae={class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},Me={key:0,class:"fixed inset-0 z-50 overflow-y-auto"},Ve={class:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},ze={class:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"},Pe={class:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4"},Ne={class:"text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4"},Re={class:"space-y-4"},Ee={class:"grid grid-cols-2 gap-4"},Le={key:0},Se={class:"flex items-center"},je={class:"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse"},Te=["disabled"],$e={key:1,class:"fixed inset-0 z-50 overflow-y-auto"},qe={class:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},De={class:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"},Be={class:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4"},Ie={class:"text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4"},Fe={key:2,class:"fixed top-4 right-4 z-50"},Ge={class:"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded shadow-md"},He={key:3,class:"fixed top-4 right-4 z-50"},Oe={class:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded shadow-md"},We={__name:"Admin",setup(Qe){const{isAdmin:E}=Y(),g=i([]),w=i(!0),b=i(""),x=i(!1),h=i(!1),k=i(!1),y=i(!1),v=i(null),c=i(""),r=i({username:"",email:"",first_name:"",last_name:"",password:"",role:"employee",is_active:!0}),u=i(""),l=i(""),M=G(()=>{if(!b.value)return g.value;const a=b.value.toLowerCase();return g.value.filter(t=>t.email.toLowerCase().includes(a)||t.username.toLowerCase().includes(a)||t.role.toLowerCase().includes(a)||t.first_name&&t.first_name.toLowerCase().includes(a)||t.last_name&&t.last_name.toLowerCase().includes(a))}),L=async()=>{var a;try{w.value=!0;const t=await R.get("/api/admin/users");t.data&&t.data.data&&t.data.data.users?g.value=t.data.data.users:t.data&&t.data.users?g.value=t.data.users:l.value=((a=t.data)==null?void 0:a.message)||"Errore nel caricamento degli utenti"}catch(t){l.value="Errore nel caricamento degli utenti",console.error("Error loading users:",t)}finally{w.value=!1}},S=a=>a.first_name?a.first_name.charAt(0).toUpperCase():a.username?a.username.charAt(0).toUpperCase():a.email.charAt(0).toUpperCase(),j=a=>a.first_name&&a.last_name?`${a.first_name} ${a.last_name}`:a.username||a.email,T=a=>({admin:"Amministratore",manager:"Manager",employee:"Dipendente",sales:"Commerciale",human_resources:"Risorse Umane"})[a]||a,$=a=>({admin:"bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200",manager:"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200",employee:"bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200",sales:"bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200",human_resources:"bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200"})[a]||"bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200",q=a=>{v.value=a,r.value={...a},y.value=!0,h.value=!0},D=a=>{v.value=a,c.value="",k.value=!0},B=async a=>{a.is_active=!a.is_active,u.value=`Utente ${a.is_active?"attivato":"disattivato"} con successo`,setTimeout(()=>u.value="",3e3)},I=async()=>{x.value=!0;try{if(y.value){const a=g.value.findIndex(t=>t.id===r.value.id);a!==-1&&(g.value[a]={...r.value}),u.value="Utente aggiornato con successo"}else{const a={...r.value,id:Math.max(...g.value.map(t=>t.id))+1};g.value.push(a),u.value="Utente creato con successo"}_(),setTimeout(()=>u.value="",3e3)}catch{l.value="Errore nel salvataggio dell'utente",setTimeout(()=>l.value="",3e3)}finally{x.value=!1}},F=async()=>{try{if(x.value=!0,!c.value||c.value.length<6){l.value="La password deve essere di almeno 6 caratteri",setTimeout(()=>l.value="",3e3);return}const a=await R.post(`/api/admin/users/${v.value.id}/reset-password`,{password:c.value});a.data.success?(u.value=a.data.message,k.value=!1,c.value=""):l.value=a.data.message||"Errore nel reset della password",setTimeout(()=>{u.value="",l.value=""},3e3)}catch{l.value="Errore nel reset della password",setTimeout(()=>l.value="",3e3)}finally{x.value=!1}},_=()=>{h.value=!1,k.value=!1,y.value=!1,v.value=null,r.value={username:"",email:"",first_name:"",last_name:"",password:"",role:"employee",is_active:!0}},V=()=>{k.value=!1,v.value=null,c.value=""};return H(()=>{E.value&&L()}),(a,t)=>{var z;return d(),n("div",null,[e("div",Z,[t[11]||(t[11]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Gestione Utenti"),e("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Gestisci gli utenti, i loro ruoli e autorizzazioni all'interno della piattaforma. ")],-1)),e("div",ee,[e("button",{onClick:t[0]||(t[0]=s=>a.showAddModal=!0),class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[U(A,{name:"plus",size:"sm",className:"mr-2"}),t[10]||(t[10]=O(" Aggiungi Utente "))])])]),e("div",te,[e("div",ae,[e("div",se,[U(A,{name:"magnifying-glass",size:"md",color:"text-gray-400"})]),m(e("input",{"onUpdate:modelValue":t[1]||(t[1]=s=>b.value=s),type:"text",class:"focus:ring-primary-500 focus:border-primary-500 block w-full pl-10 sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md",placeholder:"Cerca utenti per nome, email o ruolo..."},null,512),[[p,b.value]])])]),e("div",re,[w.value?(d(),n("div",oe,t[12]||(t[12]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto"},null,-1),e("p",{class:"mt-2 text-gray-600 dark:text-gray-400"},"Caricamento utenti...",-1)]))):(d(),n("div",le,[e("table",ie,[t[13]||(t[13]=e("thead",{class:"bg-gray-50 dark:bg-gray-700"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Utente "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Email "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Ruolo "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Stato "),e("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Azioni ")])],-1)),e("tbody",ne,[(d(!0),n(Q,null,J(M.value,s=>(d(),n("tr",{key:s.id},[e("td",de,[e("div",ue,[e("div",me,[e("div",ge,[e("span",ce,o(S(s)),1)])]),e("div",pe,[e("div",xe,o(j(s)),1),e("div",ye,o(s.username),1)])])]),e("td",ve,[e("div",fe,o(s.email),1)]),e("td",be,[e("span",{class:C([$(s.role),"px-2 inline-flex text-xs leading-5 font-semibold rounded-full"])},o(T(s.role)),3)]),e("td",ke,[e("span",{class:C([s.is_active?"bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200":"bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200","px-2 inline-flex text-xs leading-5 font-semibold rounded-full"])},o(s.is_active?"Attivo":"Inattivo"),3)]),e("td",we,[e("button",{onClick:P=>q(s),class:"text-primary-600 dark:text-primary-400 hover:text-primary-900 dark:hover:text-primary-300 mr-3"}," Modifica ",8,he),e("button",{onClick:P=>D(s),class:"text-yellow-600 dark:text-yellow-400 hover:text-yellow-900 dark:hover:text-yellow-300 mr-3"}," Reset Password ",8,_e),e("button",{onClick:P=>B(s),class:C(s.is_active?"text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300":"text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300")},o(s.is_active?"Disattiva":"Attiva"),11,Ue)])]))),128))])]),M.value.length===0?(d(),n("div",Ce,[U(A,{name:"users",size:"xl",className:"mx-auto text-gray-400"}),t[14]||(t[14]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun utente trovato",-1)),e("p",Ae,o(b.value?"Prova con criteri di ricerca diversi.":"Inizia creando il primo utente."),1)])):f("",!0)]))]),h.value?(d(),n("div",Me,[e("div",Ve,[e("div",{class:"fixed inset-0 transition-opacity",onClick:_},t[15]||(t[15]=[e("div",{class:"absolute inset-0 bg-gray-500 opacity-75"},null,-1)])),e("div",ze,[e("form",{onSubmit:N(I,["prevent"])},[e("div",Pe,[e("h3",Ne,o(y.value?"Modifica Utente":"Aggiungi Nuovo Utente"),1),e("div",Re,[e("div",null,[t[16]||(t[16]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Username",-1)),m(e("input",{"onUpdate:modelValue":t[2]||(t[2]=s=>r.value.username=s),type:"text",required:"",class:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"},null,512),[[p,r.value.username]])]),e("div",null,[t[17]||(t[17]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Email",-1)),m(e("input",{"onUpdate:modelValue":t[3]||(t[3]=s=>r.value.email=s),type:"email",required:"",class:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"},null,512),[[p,r.value.email]])]),e("div",Ee,[e("div",null,[t[18]||(t[18]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Nome",-1)),m(e("input",{"onUpdate:modelValue":t[4]||(t[4]=s=>r.value.first_name=s),type:"text",class:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"},null,512),[[p,r.value.first_name]])]),e("div",null,[t[19]||(t[19]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Cognome",-1)),m(e("input",{"onUpdate:modelValue":t[5]||(t[5]=s=>r.value.last_name=s),type:"text",class:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"},null,512),[[p,r.value.last_name]])])]),y.value?f("",!0):(d(),n("div",Le,[t[20]||(t[20]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Password",-1)),m(e("input",{"onUpdate:modelValue":t[6]||(t[6]=s=>r.value.password=s),type:"password",required:"",class:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"},null,512),[[p,r.value.password]])])),e("div",null,[t[22]||(t[22]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Ruolo",-1)),m(e("select",{"onUpdate:modelValue":t[7]||(t[7]=s=>r.value.role=s),class:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"},t[21]||(t[21]=[W('<option value="employee">Dipendente</option><option value="manager">Manager</option><option value="sales">Commerciale</option><option value="human_resources">Risorse Umane</option><option value="admin">Amministratore</option>',5)]),512),[[K,r.value.role]])]),e("div",Se,[m(e("input",{"onUpdate:modelValue":t[8]||(t[8]=s=>r.value.is_active=s),type:"checkbox",class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 rounded"},null,512),[[X,r.value.is_active]]),t[23]||(t[23]=e("label",{class:"ml-2 block text-sm text-gray-900 dark:text-white"}," Utente attivo ",-1))])])]),e("div",je,[e("button",{type:"submit",disabled:x.value,class:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50"},o(x.value?"Salvataggio...":y.value?"Aggiorna":"Crea"),9,Te),e("button",{type:"button",onClick:_,class:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"}," Annulla ")])],32)])])])):f("",!0),k.value?(d(),n("div",$e,[e("div",qe,[e("div",{class:"fixed inset-0 transition-opacity",onClick:V},t[24]||(t[24]=[e("div",{class:"absolute inset-0 bg-gray-500 opacity-75"},null,-1)])),e("div",De,[e("form",{onSubmit:N(F,["prevent"])},[e("div",Be,[e("h3",Ie," Reset Password per "+o((z=v.value)==null?void 0:z.username),1),e("div",null,[t[25]||(t[25]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Nuova Password",-1)),m(e("input",{"onUpdate:modelValue":t[9]||(t[9]=s=>c.value=s),type:"password",required:"",class:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"},null,512),[[p,c.value]])])]),e("div",{class:"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse"},[t[26]||(t[26]=e("button",{type:"submit",class:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-yellow-600 text-base font-medium text-white hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 sm:ml-3 sm:w-auto sm:text-sm"}," Reset Password ",-1)),e("button",{type:"button",onClick:V,class:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"}," Annulla ")])],32)])])])):f("",!0),u.value?(d(),n("div",Fe,[e("div",Ge,o(u.value),1)])):f("",!0),l.value?(d(),n("div",He,[e("div",Oe,o(l.value),1)])):f("",!0)])}}};export{We as default};
