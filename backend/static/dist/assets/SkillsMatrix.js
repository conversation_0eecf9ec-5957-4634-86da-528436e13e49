import{_ as M,H as B,c as I}from"./app.js";import{c as f,b as o,j as l,l as e,g as p,F as h,q as w,n as $,t as b,G as A,e as _,r as x,o as O,f as z,p as E,v as R}from"./vendor.js";import{_ as H}from"./PageHeader.js";import{_ as Q}from"./FilterBar.js";import{_ as G}from"./StatsGrid.js";import{_ as J}from"./AlertsSection.js";const K={class:"flex justify-center mb-1"},W={class:"flex space-x-0.5"},X={class:"text-center"},Y={key:1,class:"text-xs text-gray-400 dark:text-gray-600"},Z={key:0,class:"mt-1 flex justify-center space-x-1"},ee={key:0,class:"text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-1 rounded"},te=["title"],re={key:2,class:"text-xs text-blue-700 dark:text-blue-300 bg-blue-100 dark:bg-blue-900 px-1 rounded",title:"Valutato dal manager"},ae={key:3,class:"text-xs text-purple-700 dark:text-purple-300 bg-purple-100 dark:bg-purple-900 px-1 rounded",title:"Auto-valutato"},le={__name:"SkillCell",props:{userSkill:{type:Object,required:!0},skill:{type:Object,required:!0}},emits:["click"],setup(s,{emit:d}){const c=s,t=f(()=>{const i=c.userSkill.proficiency_level;if(i===0)return"bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 border border-gray-200 dark:border-gray-700";const n="hover:shadow-md transform hover:scale-105";switch(i){case 1:return`${n} bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 hover:bg-red-100 dark:hover:bg-red-900/30`;case 2:return`${n} bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 hover:bg-orange-100 dark:hover:bg-orange-900/30`;case 3:return`${n} bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 hover:bg-yellow-100 dark:hover:bg-yellow-900/30`;case 4:return`${n} bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 hover:bg-blue-100 dark:hover:bg-blue-900/30`;case 5:return`${n} bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 hover:bg-green-100 dark:hover:bg-green-900/30`;default:return`${n} bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700`}}),u=f(()=>{switch(c.userSkill.proficiency_level){case 1:return"bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200";case 2:return"bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200";case 3:return"bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200";case 4:return"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200";case 5:return"bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200";default:return"bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200"}});return(i,n)=>(l(),o("div",{onClick:n[0]||(n[0]=g=>i.$emit("click")),class:$(["skill-cell cursor-pointer rounded-lg p-2 transition-all duration-200",t.value])},[e("div",K,[e("div",W,[(l(),o(h,null,w(5,g=>e("svg",{key:g,class:$(["w-3 h-3",g<=s.userSkill.proficiency_level?"text-yellow-400":"text-gray-300 dark:text-gray-600"]),fill:"currentColor",viewBox:"0 0 20 20"},n[1]||(n[1]=[e("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"},null,-1)]),2)),64))])]),e("div",X,[s.userSkill.proficiency_level>0?(l(),o("span",{key:0,class:$(["text-xs font-medium px-1.5 py-0.5 rounded",u.value])},b(s.userSkill.proficiency_level),3)):(l(),o("span",Y,"-"))]),s.userSkill.proficiency_level>0?(l(),o("div",Z,[s.userSkill.years_experience>0?(l(),o("span",ee,b(s.userSkill.years_experience)+"y ",1)):p("",!0),s.userSkill.is_certified?(l(),o("span",{key:1,class:"text-xs text-green-700 dark:text-green-300 bg-green-100 dark:bg-green-900 px-1 rounded",title:s.userSkill.certification_name||"Certificato"}," ✓ ",8,te)):p("",!0),s.userSkill.manager_assessed?(l(),o("span",re," M ")):s.userSkill.self_assessed?(l(),o("span",ae," S ")):p("",!0)])):p("",!0)],2))}},se=M(le,[["__scopeId","data-v-4c10f004"]]),oe={class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden"},ie={class:"overflow-x-auto"},ne={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},de={class:"bg-gray-50 dark:bg-gray-700"},ce={scope:"col",class:"sticky left-0 z-10 bg-gray-50 dark:bg-gray-700 px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-r border-gray-200 dark:border-gray-600"},ue={class:"flex flex-col items-center"},ge={class:"mb-1"},be={class:"text-xs text-gray-400 dark:text-gray-500 normal-case"},me={key:0,class:"mt-1 flex items-center space-x-1"},ke={class:"text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-1 rounded"},ve={class:"text-xs text-gray-400"},ye={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},xe={class:"sticky left-0 z-10 bg-white dark:bg-gray-800 px-6 py-4 whitespace-nowrap border-r border-gray-200 dark:border-gray-600"},pe={class:"flex items-center"},_e={class:"w-10 h-10 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center mr-3"},fe={class:"text-sm font-medium text-gray-900 dark:text-white"},he={class:"text-sm text-gray-500 dark:text-gray-400"},we={class:"text-xs text-gray-400 dark:text-gray-500"},Se={class:"text-xs text-gray-500"},Ce={__name:"SkillsMatrixTable",props:{users:{type:Array,required:!0},skills:{type:Array,required:!0},userColumnLabel:{type:String,default:"Dipendente"},showSkillStats:{type:Boolean,default:!0},getUserSkill:{type:Function,default:(s,d)=>{var c;return((c=s.skills)==null?void 0:c.find(t=>t.skill_id===d))||{skill_id:d,proficiency_level:0,years_experience:0,is_certified:!1,certification_name:null,self_assessed:!1,manager_assessed:!1}}}},setup(s){return(d,c)=>(l(),o("div",oe,[e("div",ie,[e("table",ne,[e("thead",de,[e("tr",null,[e("th",ce,b(s.userColumnLabel),1),(l(!0),o(h,null,w(s.skills,t=>(l(),o("th",{key:t.id,scope:"col",class:"px-3 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider min-w-24"},[e("div",ue,[e("span",ge,b(t.name),1),e("span",be,b(t.category),1),s.showSkillStats?(l(),o("div",me,[e("span",ke,b(t.total_users),1),e("span",ve," avg: "+b(t.avg_level),1)])):p("",!0)])]))),128))])]),e("tbody",ye,[(l(!0),o(h,null,w(s.users,t=>(l(),o("tr",{key:t.user_id,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[e("td",xe,[A(d.$slots,"user-cell",{user:t},()=>[e("div",pe,[e("div",_e,[_(B,{name:"user",size:"md",class:"text-gray-500 dark:text-gray-400"})]),e("div",null,[e("div",fe,b(t.full_name),1),e("div",he,b(t.position||"Dipendente"),1),e("div",we,b(t.department||"N/A"),1)])])],!0)]),(l(!0),o(h,null,w(s.skills,u=>(l(),o("td",{key:`${t.user_id}-${u.id}`,class:"px-3 py-4 text-center"},[A(d.$slots,"skill-cell",{user:t,skill:u,userSkill:s.getUserSkill(t,u.id)},()=>{var i;return[e("div",Se,b(((i=s.getUserSkill(t,u.id))==null?void 0:i.proficiency_level)||"-"),1)]},!0)]))),128))]))),128))])])])]))}},$e=M(Ce,[["__scopeId","data-v-831bfdfc"]]),ze={class:"space-y-6"},Be=["disabled"],Me={key:1,class:"flex justify-center items-center py-12"},je={key:4,class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-12"},De={class:"text-center"},Ae={__name:"SkillsMatrix",setup(s){const d=x(!1),c=x(null),t=x([]),u=x([]),i=x(null),n=x({departments:[],categories:[]}),g=x({department_id:"",category:"",min_level:"",max_level:""}),L=f(()=>[{id:"department_id",label:"Dipartimento",value:g.value.department_id,placeholder:"Tutti i dipartimenti",options:n.value.departments.map(a=>({value:a.id,label:a.name}))},{id:"category",label:"Categoria",value:g.value.category,placeholder:"Tutte le categorie",options:n.value.categories.map(a=>({value:a,label:a}))},{id:"min_level",label:"Livello Minimo",value:g.value.min_level,placeholder:"Qualsiasi",options:[{value:"1",label:"1 - Principiante"},{value:"2",label:"2 - Base"},{value:"3",label:"3 - Intermedio"},{value:"4",label:"4 - Avanzato"},{value:"5",label:"5 - Esperto"}]},{id:"max_level",label:"Livello Massimo",value:g.value.max_level,placeholder:"Qualsiasi",options:[{value:"1",label:"1 - Principiante"},{value:"2",label:"2 - Base"},{value:"3",label:"3 - Intermedio"},{value:"4",label:"4 - Avanzato"},{value:"5",label:"5 - Esperto"}]}]),F=f(()=>{var a;return i.value?[{id:"users",label:"Dipendenti",value:i.value.total_users,format:"number",icon:"users",iconBgColor:"bg-blue-100 dark:bg-blue-900",iconColor:"text-blue-600 dark:text-blue-400"},{id:"skills",label:"Competenze",value:i.value.total_skills,format:"number",icon:"star",iconBgColor:"bg-green-100 dark:bg-green-900",iconColor:"text-green-600 dark:text-green-400"},{id:"avg_skills",label:"Media Skills/Utente",value:i.value.avg_skills_per_user,format:"number",icon:"bars-3",iconBgColor:"bg-purple-100 dark:bg-purple-900",iconColor:"text-purple-600 dark:text-purple-400"},{id:"advanced",label:"Competenze Avanzate",value:((a=i.value.skill_coverage)==null?void 0:a.advanced)||0,format:"number",icon:"arrow-trending-up",iconBgColor:"bg-orange-100 dark:bg-orange-900",iconColor:"text-orange-600 dark:text-orange-400"}]:[]}),U=f(()=>c.value?[{id:"error",type:"error",title:"Errore nel caricamento",message:c.value}]:[]),N=(a,r)=>{g.value[a]=r,S()},P=()=>{g.value={department_id:"",category:"",min_level:"",max_level:""},S()},S=async()=>{d.value=!0,c.value=null;try{const a=new URLSearchParams;Object.entries(g.value).forEach(([v,y])=>{y&&a.append(v,y)});const r=await I.get(`/api/personnel/skills-matrix?${a}`);if(r.data.success)t.value=r.data.data.matrix||[],u.value=r.data.data.skills_summary||[],i.value=r.data.data.stats||{},n.value=r.data.data.filters||{departments:[],categories:[]};else throw new Error(r.data.message||"Errore nel caricamento della matrice competenze")}catch(a){console.error("Error loading skills matrix:",a),c.value=a.message}finally{d.value=!1}},j=(a,r)=>a.skills.find(v=>v.skill_id===r)||{skill_id:r,proficiency_level:0,years_experience:0,is_certified:!1,certification_name:null,self_assessed:!1,manager_assessed:!1},T=(a,r)=>{console.log("Skill cell clicked:",a.full_name,r.name)},V=()=>{if(!t.value.length||!u.value.length)return;const a=["Dipendente","Dipartimento","Posizione",...u.value.map(k=>k.name)],r=t.value.map(k=>{const C=u.value.map(q=>j(k,q.id).proficiency_level||0);return[k.full_name,k.department||"",k.position||"",...C]}),v=[a,...r].map(k=>k.map(C=>`"${C}"`).join(",")).join(`
`),y=new Blob([v],{type:"text/csv;charset=utf-8;"}),m=document.createElement("a"),D=URL.createObjectURL(y);m.setAttribute("href",D),m.setAttribute("download",`skills-matrix-${new Date().toISOString().split("T")[0]}.csv`),m.style.visibility="hidden",document.body.appendChild(m),m.click(),document.body.removeChild(m)};return O(()=>{S()}),(a,r)=>(l(),o("div",ze,[_(H,{title:"Matrice Competenze",subtitle:"Panoramica delle competenze del team con livelli di proficiency",icon:"star","icon-color":"text-green-600"},{actions:E(()=>[e("button",{onClick:V,disabled:d.value,class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"},[_(B,{name:"arrow-down-tray",size:"sm",class:"mr-2"}),r[0]||(r[0]=R(" Esporta CSV "))],8,Be)]),_:1}),_(Q,{"select-filters":L.value,onFilterChange:N,onClearFilters:P},null,8,["select-filters"]),i.value?(l(),z(G,{key:0,stats:F.value},null,8,["stats"])):p("",!0),d.value?(l(),o("div",Me,r[1]||(r[1]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):c.value?(l(),z(J,{key:2,alerts:U.value},null,8,["alerts"])):t.value.length>0?(l(),z($e,{key:3,users:t.value,skills:u.value,"get-user-skill":j,"user-column-label":"Dipendente","show-skill-stats":!0},{"skill-cell":E(({user:v,skill:y,userSkill:m})=>[_(se,{"user-skill":m,skill:y,onClick:D=>T(v,y)},null,8,["user-skill","skill","onClick"])]),_:1},8,["users","skills"])):d.value?p("",!0):(l(),o("div",je,[e("div",De,[_(B,{name:"light-bulb",size:"2xl",class:"mx-auto text-gray-400"}),r[2]||(r[2]=e("h3",{class:"mt-4 text-lg font-medium text-gray-900 dark:text-white"},"Nessuna competenza trovata",-1)),r[3]||(r[3]=e("p",{class:"mt-2 text-sm text-gray-500 dark:text-gray-400"}," Prova a modificare i filtri o aggiungi competenze ai dipendenti ",-1))])]))]))}},Ve=M(Ae,[["__scopeId","data-v-1e6018ae"]]);export{Ve as default};
