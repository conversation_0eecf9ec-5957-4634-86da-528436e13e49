import{h as S,b as l,g as w,j as i,l as t,e as x,p as g,v as P,t as r,F as I,q as M,s as U,r as p,c as C,o as K,Q as z,R as mt,z as gt,f as pt,E as xt,n as E}from"./vendor.js";import{_ as q,H as A,c as h,b as _t,d as ht}from"./app.js";import{D as vt}from"./DashboardTemplate.js";import{u as yt}from"./useToast.js";const ft={name:"BillingWidget",props:{userRole:{type:String,default:"employee"}},components:{HeroIcon:A},setup(j){const o=U(),{showToast:f}=yt(),n=p(!1),c=p({total_unbilled_amount:0,total_unbilled_hours:0,clients_with_unbilled_count:0,top_unbilled_clients:[],recent_pre_invoices:[]}),b=C(()=>["admin","manager"].includes(j.userRole)&&(c.value.total_unbilled_amount>0||c.value.clients_with_unbilled_count>0)),_=d=>new Intl.NumberFormat("it-IT",{minimumFractionDigits:0,maximumFractionDigits:0}).format(d||0),y=async()=>{n.value=!0;try{const d=await h.get("/api/pre-invoices/dashboard/stats");d.data.success&&(c.value=d.data.data)}catch(d){console.error("Errore nel caricamento statistiche fatturazione:",d)}finally{n.value=!1}},m=async d=>{try{o.push(`/app/invoicing/pre-invoices/new?client_id=${d.id}`)}catch{f({type:"error",title:"Errore",message:"Si è verificato un errore durante la navigazione",duration:4e3})}};return K(()=>{["admin","manager"].includes(j.userRole)&&y()}),{loading:n,stats:c,showWidget:b,formatCurrency:_,quickGenerateForClient:m}}},bt={key:0,class:"billing-widget bg-white rounded-lg shadow-md p-6"},kt={class:"flex items-center justify-between mb-4"},wt={class:"flex items-center"},Ct={class:"p-2 bg-green-100 rounded-lg mr-3"},jt={key:0,class:"bg-amber-50 border border-amber-200 rounded-lg p-3 mb-4"},Dt={class:"flex items-center"},$t={class:"grid grid-cols-3 gap-4 mb-6"},Et={class:"text-center"},Ft={class:"text-2xl font-bold text-gray-900"},Bt={class:"text-center"},Tt={class:"text-2xl font-bold text-gray-900"},zt={class:"text-center"},St={class:"text-2xl font-bold text-gray-900"},Pt={key:1},It={class:"space-y-2"},Mt={class:"flex-1"},At={class:"font-medium text-gray-900"},Nt={class:"text-sm text-gray-500"},Rt={class:"flex items-center space-x-2"},Vt={class:"font-medium text-gray-900"},Ht=["onClick"],Lt={key:2,class:"text-center py-4"},Wt={key:3,class:"text-center py-4"};function Ut(j,o,f,n,c,b){const _=S("HeroIcon"),y=S("router-link");return n.showWidget?(i(),l("div",bt,[t("div",kt,[t("div",wt,[t("div",Ct,[x(_,{name:"currency-dollar",size:"lg",color:"text-green-600"})]),o[0]||(o[0]=t("div",null,[t("h3",{class:"text-lg font-semibold text-gray-900"},"Fatturazione Pendente"),t("p",{class:"text-sm text-gray-500"},"Ore non fatturate")],-1))]),x(y,{to:"/app/invoicing",class:"text-blue-600 hover:text-blue-800 text-sm font-medium"},{default:g(()=>o[1]||(o[1]=[P(" Gestisci tutto → ")])),_:1,__:[1]})]),n.stats.total_unbilled_amount>5e3?(i(),l("div",jt,[t("div",Dt,[x(_,{name:"warning",size:"sm",color:"text-amber-600",class:"mr-2"}),o[2]||(o[2]=t("span",{class:"text-amber-800 text-sm font-medium"}," Alto importo non fatturato! Considera di generare le fatture. ",-1))])])):w("",!0),t("div",$t,[t("div",Et,[t("div",Ft," €"+r(n.formatCurrency(n.stats.total_unbilled_amount)),1),o[3]||(o[3]=t("div",{class:"text-sm text-gray-500"},"Da fatturare",-1))]),t("div",Bt,[t("div",Tt,r(n.stats.total_unbilled_hours)+"h ",1),o[4]||(o[4]=t("div",{class:"text-sm text-gray-500"},"Ore lavorate",-1))]),t("div",zt,[t("div",St,r(n.stats.clients_with_unbilled_count),1),o[5]||(o[5]=t("div",{class:"text-sm text-gray-500"},"Clienti",-1))])]),n.stats.top_unbilled_clients&&n.stats.top_unbilled_clients.length>0?(i(),l("div",Pt,[o[6]||(o[6]=t("h4",{class:"text-sm font-medium text-gray-700 mb-3"},"Top Clienti da Fatturare",-1)),t("div",It,[(i(!0),l(I,null,M(n.stats.top_unbilled_clients.slice(0,3),m=>(i(),l("div",{key:m.id,class:"flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"},[t("div",Mt,[t("div",At,r(m.name),1),t("div",Nt,r(m.unbilled_hours)+"h",1)]),t("div",Rt,[t("span",Vt," €"+r(n.formatCurrency(m.unbilled_amount)),1),t("button",{onClick:d=>n.quickGenerateForClient(m),class:"px-3 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700 transition-colors"}," Fattura ",8,Ht)])]))),128))])])):n.loading?w("",!0):(i(),l("div",Lt,[x(_,{name:"success",size:"2xl",color:"text-gray-400",class:"mx-auto mb-2"}),o[7]||(o[7]=t("p",{class:"text-gray-500 text-sm"},"Nessuna ora da fatturare al momento",-1))])),n.loading?(i(),l("div",Wt,o[8]||(o[8]=[t("div",{class:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"},null,-1),t("p",{class:"text-gray-500 text-sm mt-2"},"Caricamento dati fatturazione...",-1)]))):w("",!0)])):w("",!0)}const Kt=q(ft,[["render",Ut],["__scopeId","data-v-72f30c40"]]),qt={class:"h-64 flex items-center justify-center"},Gt={class:"h-64 flex items-center justify-center"},Ot={class:"flex justify-between items-start"},Qt={class:"flex-1"},Zt={class:"text-sm font-medium text-gray-900 dark:text-white"},Jt={class:"text-xs text-gray-500 dark:text-gray-400"},Xt={class:"mt-2 flex justify-between items-center"},Yt={class:"text-xs text-gray-500 dark:text-gray-400"},te={class:"flex items-start space-x-3"},ee={class:"flex-shrink-0"},se=["innerHTML"],ae={class:"flex-1 min-w-0"},re={class:"text-sm font-medium text-gray-900 dark:text-white"},oe={class:"text-xs text-gray-500 dark:text-gray-400"},ne={class:"text-xs text-gray-400 dark:text-gray-500"},ie={class:"flex justify-between items-start"},le={class:"flex-1"},ce={class:"text-sm font-medium text-gray-900 dark:text-white"},de={class:"text-xs text-gray-500 dark:text-gray-400"},ue={class:"text-right"},me={class:"text-sm font-bold text-gray-900 dark:text-white"},ge={class:"text-xs text-gray-500"},pe={class:"mt-2"},xe={class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"},_e={class:"text-xs text-gray-500 dark:text-gray-400 mt-1"},he={class:"mb-6"},ve={class:"flex items-center justify-between mb-3"},ye={key:0,class:"text-center py-4 text-gray-500 dark:text-gray-400 text-sm"},fe={key:1,class:"space-y-3"},be={class:"flex items-start space-x-2"},ke={class:"flex-shrink-0 mt-1"},we={class:"flex-1 min-w-0"},Ce={class:"text-sm font-medium text-gray-900 dark:text-white line-clamp-2"},je={class:"flex items-center space-x-2 mt-1"},De={class:"text-xs text-gray-500 dark:text-gray-400"},$e={key:0,class:"text-xs text-gray-500 dark:text-gray-400"},Ee={class:"flex items-center justify-between mb-3"},Fe={key:0,class:"text-center py-4 text-gray-500 dark:text-gray-400 text-sm"},Be={key:1,class:"space-y-3"},Te={class:"flex items-start space-x-2"},ze={class:"flex-shrink-0 mt-1"},Se={class:"flex-1 min-w-0"},Pe={class:"text-sm font-medium text-gray-900 dark:text-white line-clamp-2"},Ie={class:"mt-1"},Me={class:"text-xs text-gray-500 dark:text-gray-400"},Ae={key:0,class:"text-xs text-gray-500 dark:text-gray-400 ml-2"},Ne={__name:"Dashboard",setup(j){z.register(...mt),U();const o=_t();ht();const f=p(!1),n=p("7"),c=p({}),b=p([]),_=p([]),y=p([]),m=p([]),d=p([]),N=C(()=>{var e;return((e=o.user)==null?void 0:e.role)||"employee"}),G=C(()=>[{value:"7",label:"Ultimi 7 giorni"},{value:"30",label:"Ultimo mese"},{value:"90",label:"Ultimi 3 mesi"}]),O=C(()=>{var e,a,u,s,v,k,L,W;return[{id:"projects",title:"Progetti Attivi",value:((e=c.value.projects)==null?void 0:e.active)||0,subtitle:`di ${((a=c.value.projects)==null?void 0:a.total)||0} totali`,icon:"folder",color:"primary",link:"/app/projects?status=active"},{id:"clients",title:"Clienti",value:((u=c.value.team)==null?void 0:u.clients)||0,icon:"users",color:"secondary",link:"/app/crm/clients"},{id:"tasks",title:"Task Pendenti",value:((s=c.value.tasks)==null?void 0:s.pending)||0,subtitle:`${((v=c.value.tasks)==null?void 0:v.overdue)||0} in ritardo`,icon:"clock",color:((k=c.value.tasks)==null?void 0:k.overdue)>0?"red":"yellow",link:"/app/tasks?status=pending"},{id:"team",title:"Team Members",value:((L=c.value.team)==null?void 0:L.users)||0,subtitle:`${((W=c.value.team)==null?void 0:W.departments)||0} dipartimenti`,icon:"user-group",color:"blue",link:"/app/personnel"}]}),Q=C(()=>[{id:"project-status",title:"Stato Progetti",type:"doughnut"},{id:"task-status",title:"Stato Attività",type:"bar"}]),D=p(null),$=p(null);let F=null,B=null;const Z=e=>{n.value=e,T()},J=async()=>{try{const e=await h.get("/api/dashboard/stats");c.value=e.data.data}catch(e){console.error("Error fetching dashboard stats:",e),c.value={}}},X=async()=>{try{const e=await h.get(`/api/dashboard/upcoming-tasks?days=${n.value}&limit=5`);b.value=e.data.data.tasks}catch(e){console.error("Error fetching upcoming tasks:",e),b.value=[]}},Y=async()=>{try{const e=await h.get("/api/dashboard/recent-activities?limit=5");_.value=e.data.data.activities}catch(e){console.error("Error fetching recent activities:",e),_.value=[]}},tt=async()=>{try{const e=await h.get("/api/dashboard/kpis?limit=3");y.value=e.data.data.kpis}catch(e){console.error("Error fetching KPIs:",e),y.value=[]}},et=async()=>{var e,a;try{const u=await h.get("/api/communication/company",{params:{per_page:3,published_only:!0}});m.value=((a=(e=u.data.data)==null?void 0:e.communications)==null?void 0:a.slice(0,3))||[]}catch(u){console.error("Error fetching recent news:",u),m.value=[]}},st=async()=>{var e;try{const u=((e=(await h.get("/api/communication/events",{params:{per_page:3,upcoming_only:!0}})).data.data)==null?void 0:e.events)||[],s=new Date;d.value=u.filter(v=>new Date(v.start_time||v.event_time)>s).sort((v,k)=>new Date(v.start_time||v.event_time)-new Date(k.start_time||k.event_time)).slice(0,3)}catch(a){console.error("Error fetching upcoming events:",a),d.value=[]}},R=async()=>{try{const e=await h.get("/api/dashboard/charts/project-status");at(e.data.data.chart)}catch(e){console.error("Error fetching project chart:",e)}},V=async()=>{try{const e=await h.get("/api/dashboard/charts/task-status");rt(e.data.data.chart)}catch(e){console.error("Error fetching task chart:",e)}},at=e=>{if(!D.value)return;const a=D.value.getContext("2d");F&&F.destroy(),F=new z(a,{type:"doughnut",data:{labels:e.labels,datasets:[{data:e.data,backgroundColor:["#3B82F6","#10B981","#F59E0B","#EF4444","#8B5CF6"],borderWidth:2,borderColor:"#ffffff"}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom",labels:{padding:20,usePointStyle:!0}}}}})},rt=e=>{if(!$.value)return;const a=$.value.getContext("2d");B&&B.destroy(),B=new z(a,{type:"bar",data:{labels:e.labels,datasets:[{label:"Tasks",data:e.data,backgroundColor:["#60A5FA","#34D399","#FBBF24","#F87171"],borderColor:["#3B82F6","#10B981","#F59E0B","#EF4444"],borderWidth:1}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1}},scales:{y:{beginAtZero:!0,ticks:{stepSize:1}}}}})},T=async()=>{f.value=!0;try{await Promise.all([J(),X(),Y(),tt(),et(),st(),R(),V()])}finally{f.value=!1}},H=e=>new Date(e).toLocaleDateString("it-IT"),ot=e=>{const a=new Date(e),s=Math.floor((new Date-a)/(1e3*60));return s<60?`${s} minuti fa`:s<1440?`${Math.floor(s/60)} ore fa`:`${Math.floor(s/1440)} giorni fa`},nt=e=>new Date(e).toLocaleDateString("it-IT",{weekday:"short",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),it=e=>{const a={high:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",medium:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",low:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"};return a[e]||a.medium},lt=e=>{const a={todo:"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300","in-progress":"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",review:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",done:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"};return a[e]||a.todo},ct=e=>{const a={task:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
      </svg>`,timesheet:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>`,event:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
      </svg>`};return a[e]||a.task},dt=e=>{const a={task:"bg-blue-100 text-blue-600",timesheet:"bg-green-100 text-green-600",event:"bg-purple-100 text-purple-600"};return a[e]||a.task},ut=e=>e>=90?"bg-green-500":e>=70?"bg-yellow-500":"bg-red-500";return K(async()=>{await T(),await gt(),D.value&&$.value&&(await R(),await V())}),(e,a)=>{const u=S("router-link");return i(),pt(vt,{title:"Dashboard",subtitle:"Benvenuto! Ecco una panoramica delle attività della tua azienda.",loading:f.value,stats:O.value,charts:Q.value,"recent-items":b.value,activities:_.value,kpis:y.value,"selected-period":n.value,"period-options":G.value,"recent-items-title":"Attività in Scadenza","recent-items-empty-message":"Nessuna attività in scadenza","recent-items-link":"/app/tasks","recent-items-link-text":"Vedi tutte le attività","activities-title":"Attività Recenti","activities-empty-message":"Nessuna attività recente","kpis-title":"KPIs Principali",onRefresh:T,onPeriodChange:Z},{widget:g(()=>[x(Kt,{"user-role":N.value},null,8,["user-role"])]),"chart-project-status":g(()=>[t("div",qt,[t("canvas",{ref_key:"projectChart",ref:D,class:"w-full h-full"},null,512)])]),"chart-task-status":g(()=>[t("div",Gt,[t("canvas",{ref_key:"taskChart",ref:$,class:"w-full h-full"},null,512)])]),"recent-item":g(({item:s})=>[t("div",Ot,[t("div",Qt,[t("h3",Zt,r(s.name),1),t("p",Jt,r(s.project_name),1)]),t("span",{class:E(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",it(s.priority)])},r(s.priority),3)]),t("div",Xt,[t("span",Yt," Scadenza: "+r(H(s.due_date)),1),t("span",{class:E(["inline-flex items-center px-2 py-1 rounded-full text-xs font-medium",lt(s.status)])},r(s.status),3)])]),"activity-item":g(({activity:s})=>[t("div",te,[t("div",ee,[t("div",{class:E(["w-8 h-8 rounded-full flex items-center justify-center",dt(s.type)])},[t("div",{class:"w-4 h-4",innerHTML:ct(s.type)},null,8,se)],2)]),t("div",ae,[t("p",re,r(s.title),1),t("p",oe,r(s.description),1),t("p",ne,r(ot(s.timestamp)),1)])])]),"kpi-item":g(({kpi:s})=>[t("div",ie,[t("div",le,[t("h3",ce,r(s.name),1),t("p",de,r(s.description),1)]),t("div",ue,[t("p",me,r(s.current_value)+r(s.unit),1),t("p",ge," Target: "+r(s.target_value)+r(s.unit),1)])]),t("div",pe,[t("div",xe,[t("div",{class:E(["h-2 rounded-full",ut(s.performance_percentage)]),style:xt({width:Math.min(s.performance_percentage,100)+"%"})},null,6)]),t("p",_e,r(Math.round(s.performance_percentage))+"% del target",1)])]),sidebar:g(()=>[t("div",he,[t("div",ve,[a[1]||(a[1]=t("h3",{class:"text-sm font-medium text-gray-900 dark:text-white"},"News Recenti",-1)),x(u,{to:"/app/communications/news",class:"text-xs text-brand-primary-600 dark:text-brand-primary-400 hover:text-brand-primary-500"},{default:g(()=>a[0]||(a[0]=[P(" Vedi tutte ")])),_:1,__:[0]})]),m.value.length===0?(i(),l("div",ye," Nessuna news recente ")):(i(),l("div",fe,[(i(!0),l(I,null,M(m.value,s=>(i(),l("div",{key:s.id,class:"border-b border-gray-200 dark:border-gray-700 pb-3 last:border-b-0 last:pb-0"},[x(u,{to:`/app/communications/news/${s.id}`,class:"block hover:bg-gray-50 dark:hover:bg-gray-700 -m-1 p-1 rounded"},{default:g(()=>[t("div",be,[t("div",ke,[x(A,{name:"newspaper",size:"xs",class:"text-blue-500"})]),t("div",we,[t("h4",Ce,r(s.title),1),t("div",je,[t("span",De,r(H(s.created_at)),1),s.author?(i(),l("span",$e," • "+r(s.author.first_name)+" "+r(s.author.last_name),1)):w("",!0)])])])]),_:2},1032,["to"])]))),128))]))]),t("div",null,[t("div",Ee,[a[3]||(a[3]=t("h3",{class:"text-sm font-medium text-gray-900 dark:text-white"},"Prossimi Eventi",-1)),x(u,{to:"/app/communications/events",class:"text-xs text-brand-primary-600 dark:text-brand-primary-400 hover:text-brand-primary-500"},{default:g(()=>a[2]||(a[2]=[P(" Vedi tutti ")])),_:1,__:[2]})]),d.value.length===0?(i(),l("div",Fe," Nessun evento in programma ")):(i(),l("div",Be,[(i(!0),l(I,null,M(d.value,s=>(i(),l("div",{key:s.id,class:"border-b border-gray-200 dark:border-gray-700 pb-3 last:border-b-0 last:pb-0"},[x(u,{to:`/app/communications/events/${s.id}`,class:"block hover:bg-gray-50 dark:hover:bg-gray-700 -m-1 p-1 rounded"},{default:g(()=>[t("div",Te,[t("div",ze,[x(A,{name:"calendar",size:"xs",class:"text-purple-500"})]),t("div",Se,[t("h4",Pe,r(s.title),1),t("div",Ie,[t("span",Me,r(nt(s.start_time)),1),s.location?(i(),l("span",Ae," • "+r(s.location),1)):w("",!0)])])])]),_:2},1032,["to"])]))),128))]))])]),_:1},8,["loading","stats","charts","recent-items","activities","kpis","selected-period","period-options"])}}},We=q(Ne,[["__scopeId","data-v-312290f1"]]);export{We as default};
