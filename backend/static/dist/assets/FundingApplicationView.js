import{r as k,c,o as dt,b as r,e as d,l as s,t as o,g as i,p as lt,n as b,F as w,q as A,v as g,x as v,u as ct,s as mt,j as n}from"./vendor.js";import{u as pt}from"./funding.js";import{u as vt}from"./useFormatters.js";import{_ as gt}from"./PageHeader.js";import{_ as _t,f as yt,H as m}from"./app.js";import{S as H}from"./StatusBadge.js";import"./formatters.js";const xt={key:0,class:"flex justify-center items-center min-h-64"},bt={key:1,class:"bg-white rounded-lg shadow-sm border p-6"},ft={class:"text-center py-12"},ht={class:"text-gray-600"},kt={key:2},wt={key:0,class:"mb-6"},At={class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},jt={class:"lg:col-span-2 space-y-6"},Ct={class:"bg-white rounded-lg shadow-sm border p-6"},St={class:"prose prose-sm max-w-none"},Ft={class:"text-gray-700 whitespace-pre-wrap"},zt={key:0,class:"bg-white rounded-lg shadow-sm border p-6"},$t={class:"space-y-3"},Dt={class:"text-sm font-medium text-gray-900"},Bt={class:"text-sm text-gray-600"},Et={key:1,class:"bg-white rounded-lg shadow-sm border p-6"},Pt={class:"space-y-3"},It={class:"text-sm text-gray-900"},Ot={class:"text-sm font-medium text-gray-900"},qt={key:2,class:"bg-white rounded-lg shadow-sm border p-6"},Nt={class:"prose prose-sm max-w-none"},Rt={class:"text-gray-700 whitespace-pre-wrap"},Vt={key:3,class:"bg-red-50 border border-red-200 rounded-lg p-6"},Tt={class:"prose prose-sm max-w-none"},Lt={class:"text-red-700 whitespace-pre-wrap"},Jt={class:"space-y-6"},Ut={class:"bg-white rounded-lg shadow-sm border p-6"},Ht={class:"space-y-4"},Mt={class:"flex items-center"},Gt={key:0},Kt={class:"flex items-center text-sm font-medium text-gray-500 mb-1"},Qt={class:"text-sm text-gray-900"},Wt={key:1},Xt={class:"flex items-center text-sm font-medium text-gray-500 mb-1"},Yt={class:"text-sm text-gray-900"},Zt={key:2},te={class:"text-sm text-gray-900"},ee={class:"bg-white rounded-lg shadow-sm border p-6"},se={class:"space-y-4"},ae={class:"text-sm text-gray-900"},re={key:0},ne={class:"text-sm text-gray-900"},oe={class:"text-sm text-gray-900"},ie={key:1},ue={class:"text-sm font-semibold text-green-600"},de={key:2},le={class:"text-sm text-gray-900"},ce={class:"bg-white rounded-lg shadow-sm border p-6"},me={class:"space-y-4"},pe={key:0},ve={class:"flex items-center text-sm font-medium text-gray-500 mb-1"},ge={class:"text-sm text-gray-900"},_e={key:1},ye={class:"text-sm text-gray-900"},xe={key:2},be={class:"text-sm text-gray-900"},fe={key:0,class:"bg-white rounded-lg shadow-sm border p-6"},he={class:"space-y-4"},ke={class:"text-sm text-gray-900"},we={key:0},Ae={class:"text-sm text-gray-900"},je={key:1},Ce={class:"flex items-center text-sm font-medium text-gray-500 mb-1"},Se={class:"text-sm text-gray-900"},Fe={key:1,class:"bg-white rounded-lg shadow-sm border p-6"},ze={class:"space-y-3"},$e={class:"flex items-center"},De={class:"text-sm text-gray-900"},Be={__name:"FundingApplicationView",setup(Ee){const M=ct(),x=mt(),j=pt(),{formatDate:f,formatCurrency:_}=vt(),h=k(!1),y=k(null),e=k(null),G=c(()=>{if(!e.value)return"View application details and status";const a=e.value.opportunity||{};return a.source_entity&&a.title?`${a.source_entity} - ${a.title}`:"Application Details"}),K=c(()=>{var a,t;return((a=e.value)==null?void 0:a.status)==="draft"||((t=e.value)==null?void 0:t.status)==="pending"}),Q=c(()=>{if(!e.value)return 0;const a=e.value.requested_amount||0,t=e.value.co_financing_amount||0;return a+t}),C=c(()=>{var a;if(!((a=e.value)!=null&&a.team_composition))return[];if(typeof e.value.team_composition!="string")return Array.isArray(e.value.team_composition)?e.value.team_composition:[];try{const t=JSON.parse(e.value.team_composition);return Array.isArray(t)?t:[]}catch(t){return console.error("Error parsing team_composition:",t),[]}}),S=c(()=>{var a;if(!((a=e.value)!=null&&a.budget_breakdown))return[];if(typeof e.value.budget_breakdown!="string"){const t=e.value.budget_breakdown;return Array.isArray(t)?t:Object.entries(t).map(([l,p])=>({category:l,amount:p}))}try{const t=JSON.parse(e.value.budget_breakdown);return t?Array.isArray(t)?t:Object.entries(t).map(([l,p])=>({category:l,amount:p})):[]}catch(t){return console.error("Error parsing budget_breakdown:",t),[]}}),W=c(()=>{var a;return((a=e.value)==null?void 0:a.documents_checklist)&&F.value.length>0}),F=c(()=>{var a;if(!((a=e.value)!=null&&a.documents_checklist))return[];if(typeof e.value.documents_checklist!="string"){const t=e.value.documents_checklist;return Array.isArray(t)?t:Object.entries(t).map(([l,p])=>({name:l,status:p}))}try{const t=JSON.parse(e.value.documents_checklist);return t?Array.isArray(t)?t:Object.entries(t).map(([l,p])=>({name:l,status:p})):[]}catch(t){return console.error("Error parsing documents_checklist:",t),[]}}),X=c(()=>{var l;if(!((l=e.value)!=null&&l.status))return"bg-gray-50 border-gray-200";const a=e.value.status,t="border";switch(a){case"draft":return`${t} bg-gray-50 border-gray-200`;case"submitted":return`${t} bg-blue-50 border-blue-200`;case"under_evaluation":return`${t} bg-yellow-50 border-yellow-200`;case"approved":return`${t} bg-green-50 border-green-200`;case"rejected":return`${t} bg-red-50 border-red-200`;case"funded":return`${t} bg-emerald-50 border-emerald-200`;default:return`${t} bg-gray-50 border-gray-200`}}),Y=c(()=>{var t;if(!((t=e.value)!=null&&t.status))return"document-text";switch(e.value.status){case"draft":return"pencil-square";case"submitted":return"paper-airplane";case"under_evaluation":return"clock";case"approved":return"check-circle";case"rejected":return"exclamation-triangle";case"funded":return"currency-euro";default:return"document-text"}}),Z=c(()=>{var t;if(!((t=e.value)!=null&&t.status))return"text-gray-500";switch(e.value.status){case"draft":return"text-gray-500";case"submitted":return"text-blue-500";case"under_evaluation":return"text-yellow-500";case"approved":return"text-green-500";case"rejected":return"text-red-500";case"funded":return"text-emerald-500";default:return"text-gray-500"}}),tt=c(()=>{var t;if(!((t=e.value)!=null&&t.status))return"text-gray-800";switch(e.value.status){case"draft":return"text-gray-800";case"submitted":return"text-blue-800";case"under_evaluation":return"text-yellow-800";case"approved":return"text-green-800";case"rejected":return"text-red-800";case"funded":return"text-emerald-800";default:return"text-gray-800"}}),et=c(()=>{var t;if(!((t=e.value)!=null&&t.status))return"text-gray-600";switch(e.value.status){case"draft":return"text-gray-600";case"submitted":return"text-blue-600";case"under_evaluation":return"text-yellow-600";case"approved":return"text-green-600";case"rejected":return"text-red-600";case"funded":return"text-emerald-600";default:return"text-gray-600"}}),st=c(()=>{var t;if(!((t=e.value)!=null&&t.status))return"Unknown Status";switch(e.value.status){case"draft":return"Draft Application";case"submitted":return"Application Submitted";case"under_evaluation":return"Under Evaluation";case"approved":return"Application Approved";case"rejected":return"Application Rejected";case"funded":return"Funding Awarded";default:return"Unknown Status"}}),at=c(()=>{var t;if(!((t=e.value)!=null&&t.status))return"";switch(e.value.status){case"draft":return"Application is being prepared and can be edited";case"submitted":return"Application has been submitted and is awaiting review";case"under_evaluation":return"Application is currently being evaluated";case"approved":return"Application has been approved for funding";case"rejected":return"Application was not successful";case"funded":return"Funding has been awarded and project can begin";default:return""}});async function rt(){const a=M.params.id;if(!a){y.value="No application ID provided";return}h.value=!0,y.value=null;try{await j.fetchApplications();const t=j.applications.find(l=>l.id===parseInt(a));if(!t){y.value="Application not found",e.value=null;return}console.log("Application loaded:",t),e.value=t}catch(t){console.error("Failed to load application:",t),y.value=t.message||"Failed to load application",e.value=null}finally{h.value=!1}}function nt(){x.push("/app/funding/dashboard")}function ot(){e.value&&x.push(`/app/funding/applications/${e.value.id}/edit`)}function z(){var a,t;(t=(a=e.value)==null?void 0:a.linked_project)!=null&&t.id&&x.push(`/app/projects/${e.value.linked_project.id}`)}function it(){var a,t;(t=(a=e.value)==null?void 0:a.opportunity)!=null&&t.id&&x.push(`/app/funding/opportunities/${e.value.opportunity.id}`)}function ut(){var a;(a=e.value)!=null&&a.id&&x.push(`/app/funding/reporting?applicationId=${e.value.id}`)}return dt(()=>{rt()}),(a,t)=>{var l,p,$,D,B,E,P,I,O,q,N,R,V,T,L,J;return h.value?(n(),r("div",xt,[d(yt)])):y.value?(n(),r("div",bt,[s("div",ft,[d(m,{name:"exclamation-triangle",size:"xl",class:"text-red-400 mx-auto mb-4"}),t[0]||(t[0]=s("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"Error Loading Application",-1)),s("p",ht,o(y.value),1)])])):(n(),r("div",kt,[d(gt,{title:((l=e.value)==null?void 0:l.project_title)||"Funding Application",subtitle:G.value},{actions:lt(()=>{var u,U;return[s("button",{onClick:nt,class:"btn-secondary"},[d(m,{name:"arrow-left",size:"sm"}),t[1]||(t[1]=g(" Back to Dashboard "))]),K.value?(n(),r("button",{key:0,onClick:ot,class:"btn-secondary"},[d(m,{name:"pencil-square",size:"sm"}),t[2]||(t[2]=g(" Edit "))])):i("",!0),(u=e.value)!=null&&u.linked_project_id?(n(),r("button",{key:1,onClick:z,class:"btn-secondary"},[d(m,{name:"eye",size:"sm"}),t[3]||(t[3]=g(" Progetto "))])):i("",!0),(U=e.value)!=null&&U.linked_project_id?(n(),r("button",{key:2,onClick:ut,class:"btn-primary"},[d(m,{name:"document-chart-bar",size:"sm"}),t[4]||(t[4]=g(" Rendicontazione "))])):i("",!0)]}),_:1},8,["title","subtitle"]),e.value?(n(),r("div",wt,[s("div",{class:b([X.value,"rounded-lg p-4 flex items-center"])},[d(m,{name:Y.value,size:"sm",class:b([Z.value,"mr-3"])},null,8,["name","class"]),s("div",null,[s("p",{class:b(["text-sm font-medium",tt.value])},o(st.value),3),s("p",{class:b(["text-sm",et.value])},o(at.value),3)])],2)])):i("",!0),s("div",At,[s("div",jt,[s("div",Ct,[t[5]||(t[5]=s("h2",{class:"text-lg font-semibold text-gray-900 mb-4"},"Project Description",-1)),s("div",St,[s("p",Ft,o((p=e.value)==null?void 0:p.project_description),1)])]),C.value.length?(n(),r("div",zt,[t[6]||(t[6]=s("h2",{class:"text-lg font-semibold text-gray-900 mb-4"},"Team Composition",-1)),s("div",$t,[(n(!0),r(w,null,A(C.value,u=>(n(),r("div",{key:u.name||u.role,class:"flex items-center p-3 border border-gray-200 rounded-lg"},[d(m,{name:"user-group",size:"sm",class:"text-gray-400 mr-3"}),s("div",null,[s("p",Dt,o(u.name||u.role),1),s("p",Bt,o(u.role||u.description),1)])]))),128))])])):i("",!0),S.value.length?(n(),r("div",Et,[t[7]||(t[7]=s("h2",{class:"text-lg font-semibold text-gray-900 mb-4"},"Budget Breakdown",-1)),s("div",Pt,[(n(!0),r(w,null,A(S.value,u=>(n(),r("div",{key:u.category,class:"flex items-center justify-between"},[s("div",It,o(u.category),1),s("div",Ot,o(u.amount?v(_)(u.amount):v(_)(0)),1)]))),128))])])):i("",!0),($=e.value)!=null&&$.evaluation_feedback?(n(),r("div",qt,[t[8]||(t[8]=s("h2",{class:"text-lg font-semibold text-gray-900 mb-4"},"Evaluation Feedback",-1)),s("div",Nt,[s("p",Rt,o(e.value.evaluation_feedback),1)])])):i("",!0),(D=e.value)!=null&&D.rejection_reason?(n(),r("div",Vt,[t[9]||(t[9]=s("h2",{class:"text-lg font-semibold text-red-900 mb-4"},"Rejection Reason",-1)),s("div",Tt,[s("p",Lt,o(e.value.rejection_reason),1)])])):i("",!0)]),s("div",Jt,[s("div",Ut,[t[14]||(t[14]=s("h2",{class:"text-lg font-semibold text-gray-900 mb-4"},"Application Status",-1)),s("dl",Ht,[s("div",null,[t[10]||(t[10]=s("dt",{class:"text-sm font-medium text-gray-500 mb-1"},"Current Status",-1)),s("dd",Mt,[d(H,{status:(B=e.value)==null?void 0:B.status,type:"funding_application"},null,8,["status"])])]),(E=e.value)!=null&&E.submission_date?(n(),r("div",Gt,[s("dt",Kt,[d(m,{name:"calendar-days",size:"sm",class:"mr-2"}),t[11]||(t[11]=g(" Submitted "))]),s("dd",Qt,o(v(f)(e.value.submission_date)),1)])):i("",!0),(P=e.value)!=null&&P.approval_date?(n(),r("div",Wt,[s("dt",Xt,[d(m,{name:"check-circle",size:"sm",class:"mr-2"}),t[12]||(t[12]=g(" Approved "))]),s("dd",Yt,o(v(f)(e.value.approval_date)),1)])):i("",!0),(I=e.value)!=null&&I.priority_score?(n(),r("div",Zt,[t[13]||(t[13]=s("dt",{class:"text-sm font-medium text-gray-500 mb-1"},"Priority Score",-1)),s("dd",te,o(e.value.priority_score)+"/10 ",1)])):i("",!0)])]),s("div",ee,[t[20]||(t[20]=s("h2",{class:"text-lg font-semibold text-gray-900 mb-4"},"Financial Information",-1)),s("dl",se,[s("div",null,[t[15]||(t[15]=s("dt",{class:"text-sm font-medium text-gray-500 mb-1"},"Requested Amount",-1)),s("dd",ae,o((O=e.value)!=null&&O.requested_amount?v(_)(e.value.requested_amount):v(_)(0)),1)]),(q=e.value)!=null&&q.co_financing_amount?(n(),r("div",re,[t[16]||(t[16]=s("dt",{class:"text-sm font-medium text-gray-500 mb-1"},"Co-financing Amount",-1)),s("dd",ne,o(v(_)(e.value.co_financing_amount)),1)])):i("",!0),s("div",null,[t[17]||(t[17]=s("dt",{class:"text-sm font-medium text-gray-500 mb-1"},"Total Project Cost",-1)),s("dd",oe,o(v(_)(Q.value)),1)]),(N=e.value)!=null&&N.approved_amount?(n(),r("div",ie,[t[18]||(t[18]=s("dt",{class:"text-sm font-medium text-green-600 mb-1"}," Approved Amount ",-1)),s("dd",ue,o(v(_)(e.value.approved_amount)),1)])):i("",!0),(R=e.value)!=null&&R.funding_percentage?(n(),r("div",de,[t[19]||(t[19]=s("dt",{class:"text-sm font-medium text-gray-500 mb-1"},"Funding Percentage",-1)),s("dd",le,o(e.value.funding_percentage)+"% ",1)])):i("",!0)])]),s("div",ce,[t[24]||(t[24]=s("h2",{class:"text-lg font-semibold text-gray-900 mb-4"},"Project Information",-1)),s("dl",me,[(V=e.value)!=null&&V.project_duration_months?(n(),r("div",pe,[s("dt",ve,[d(m,{name:"clock",size:"sm",class:"mr-2"}),t[21]||(t[21]=g(" Duration "))]),s("dd",ge,o(e.value.project_duration_months)+" months ",1)])):i("",!0),(T=e.value)!=null&&T.project_manager?(n(),r("div",_e,[t[22]||(t[22]=s("dt",{class:"text-sm font-medium text-gray-500 mb-1"},"Project Manager",-1)),s("dd",ye,o(e.value.project_manager.first_name)+" "+o(e.value.project_manager.last_name),1)])):i("",!0),(L=e.value)!=null&&L.linked_project?(n(),r("div",xe,[t[23]||(t[23]=s("dt",{class:"text-sm font-medium text-gray-500 mb-1"},"Linked Project",-1)),s("dd",be,[s("button",{onClick:z,class:"text-blue-600 hover:text-blue-800 underline"},o(e.value.linked_project.name),1)])])):i("",!0)])]),(J=e.value)!=null&&J.opportunity?(n(),r("div",fe,[t[28]||(t[28]=s("h2",{class:"text-lg font-semibold text-gray-900 mb-4"},"Funding Opportunity",-1)),s("dl",he,[s("div",null,[t[25]||(t[25]=s("dt",{class:"text-sm font-medium text-gray-500 mb-1"},"Title",-1)),s("dd",ke,[s("button",{onClick:it,class:"text-blue-600 hover:text-blue-800 underline"},o(e.value.opportunity.title),1)])]),e.value.opportunity.source_entity?(n(),r("div",we,[t[26]||(t[26]=s("dt",{class:"text-sm font-medium text-gray-500 mb-1"},"Source Entity",-1)),s("dd",Ae,o(e.value.opportunity.source_entity),1)])):i("",!0),e.value.opportunity.deadline?(n(),r("div",je,[s("dt",Ce,[d(m,{name:"calendar-days",size:"sm",class:"mr-2"}),t[27]||(t[27]=g(" Deadline "))]),s("dd",Se,o(v(f)(e.value.opportunity.deadline)),1)])):i("",!0)])])):i("",!0),W.value?(n(),r("div",Fe,[t[29]||(t[29]=s("h2",{class:"text-lg font-semibold text-gray-900 mb-4"},"Documents",-1)),s("div",ze,[(n(!0),r(w,null,A(F.value,u=>(n(),r("div",{key:u.name,class:"flex items-center justify-between"},[s("div",$e,[d(m,{name:"document-text",size:"sm",class:"text-gray-400 mr-2"}),s("span",De,o(u.name),1)]),d(H,{status:u.status,size:"sm"},null,8,["status"])]))),128))])])):i("",!0)])])]))}}},Te=_t(Be,[["__scopeId","data-v-5e3d79a7"]]);export{Te as default};
