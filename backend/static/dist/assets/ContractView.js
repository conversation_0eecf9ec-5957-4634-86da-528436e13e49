import{r as g,c as D,u as J,o as K,b as o,e as u,l as t,g as m,p as b,t as d,h as Q,n as W,x as z,F as M,q as B,A as X,B as C,C as j,v,s as Y,j as n}from"./vendor.js";import{u as Z}from"./crm.js";import{u as tt}from"./useToast.js";import{H as S}from"./app.js";import{S as T}from"./StatusBadge.js";import{_ as et}from"./PageHeader.js";import{_ as at}from"./StatsGrid.js";import{a as rt,g as st}from"./contractTypes.js";const ot={class:"min-h-screen bg-gray-50 dark:bg-gray-900"},nt={key:0,class:"flex space-x-4"},lt={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},dt={key:0,class:"bg-white dark:bg-gray-800 rounded-lg shadow p-8"},it={key:1,class:"space-y-6"},ut={class:"bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden"},ct={class:"p-6"},gt={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},mt={class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},vt={class:"space-y-2 text-sm"},pt={class:"flex justify-between"},yt={class:"flex justify-between"},bt={class:"font-medium text-gray-900 dark:text-white"},xt={class:"flex justify-between"},ft={class:"flex justify-between"},_t={class:"space-y-2 text-sm"},kt={key:0},ht={class:"flex justify-between"},wt={class:"font-medium text-gray-900 dark:text-white"},Ct={key:0,class:"flex justify-between"},jt={class:"font-medium text-gray-900 dark:text-white"},Dt={key:1,class:"flex justify-between"},Tt={class:"font-medium text-gray-900 dark:text-white"},It={class:"flex justify-between"},Vt={class:"font-medium text-gray-900 dark:text-white"},zt={class:"flex justify-between"},Mt={class:"font-medium text-gray-900 dark:text-white"},Bt={key:0,class:"mt-6"},St={class:"text-gray-700 dark:text-gray-300 whitespace-pre-wrap"},$t={class:"bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden"},Nt={class:"px-6 py-4 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600"},Pt={class:"text-lg font-medium text-gray-900 dark:text-white"},Ft={class:"p-6"},Rt={key:0,class:"space-y-4"},qt={class:"flex justify-between items-start"},Et={class:"font-medium text-gray-900 dark:text-white"},Lt={key:0,class:"text-sm text-gray-600 dark:text-gray-400 mt-1"},Ot={class:"mt-2"},Ht={key:1,class:"text-center py-8 text-gray-500 dark:text-gray-400"},At={class:"bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden"},Ut={class:"px-6 py-4 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600"},Gt={class:"text-lg font-medium text-gray-900 dark:text-white"},Jt={class:"p-6"},Kt={key:0,class:"space-y-4"},Qt={class:"flex justify-between items-start"},Wt={class:"font-medium text-gray-900 dark:text-white"},Xt={class:"text-sm text-gray-600 dark:text-gray-400"},Yt={class:"mt-2 flex items-center space-x-4"},Zt={class:"text-sm font-medium text-gray-900 dark:text-white"},te={key:1,class:"text-center py-8 text-gray-500 dark:text-gray-400"},ee={key:2,class:"bg-white dark:bg-gray-800 rounded-lg shadow p-8"},ae={class:"text-center"},re={key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 dark:bg-gray-900 dark:bg-opacity-70 overflow-y-auto h-full w-full z-50"},se={class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800 dark:border-gray-600"},oe={class:"mt-3"},ne=["placeholder"],le=["placeholder"],de={class:"grid grid-cols-2 gap-4"},ie={class:"flex justify-end space-x-3 pt-4"},ue=["disabled"],ce={key:0,class:"flex items-center"},ge={key:1},he={__name:"ContractView",setup(me){const $=J(),N=Y(),{showToast:P}=tt(),F=Z(),_=g(!1),k=g(!1),a=g(null),p=g([]),x=g([]),h=g(!1),l=g({name:"",description:"",start_date:"",end_date:""}),R=D(()=>parseInt($.params.id)),q=D(()=>a.value&&!p.value.length),E=D(()=>{if(!a.value)return[];const s=[];if(a.value.contract_type==="hourly"?(s.push({name:"Tariffa Oraria",value:`€${f(a.value.hourly_rate)}`,subtitle:"per ora",icon:"clock"}),a.value.budget_hours&&s.push({name:"Budget Ore",value:a.value.budget_hours,subtitle:"ore totali",icon:"calendar"})):a.value.budget_amount&&s.push({name:"Budget Totale",value:`€${f(a.value.budget_amount)}`,subtitle:"valore contratto",icon:"banknotes"}),s.push({name:"Progetti",value:p.value.length,subtitle:"collegati",icon:"folder"}),s.push({name:"Fatture",value:x.value.length,subtitle:"generate",icon:"document-text"}),a.value.start_date){const e=new Date(a.value.start_date),i=a.value.end_date?new Date(a.value.end_date):new Date,c=Math.abs(i-e),y=Math.ceil(c/(1e3*60*60*24));s.push({name:"Durata",value:y,subtitle:"giorni",icon:"calendar-days"})}return s}),L=async()=>{try{_.value=!0;const s=await fetch(`/api/contracts/${R.value}`);if(s.ok){const i=(await s.json()).data;a.value={...i,budget_amount:i.total_budget},a.value&&(l.value.name=a.value.title,l.value.description=a.value.description||"",l.value.start_date=a.value.start_date||new Date().toISOString().split("T")[0],l.value.end_date=a.value.end_date||"")}else throw new Error("Contratto non trovato")}catch(s){console.error("Error loading contract:",s),P("Errore nel caricamento del contratto","error")}finally{_.value=!1}},O=async()=>{var s,e,i;try{(s=a.value)!=null&&s.projects&&(p.value=a.value.projects);const c=await fetch(`/api/invoices/?client_id=${(e=a.value)==null?void 0:e.client_id}&limit=5`);if(c.ok){const y=await c.json();x.value=((i=y.data)==null?void 0:i.invoices)||[]}}catch(c){console.error("Error loading related data:",c)}},H=async()=>{try{k.value=!0;const s={name:l.value.name,description:l.value.description,start_date:l.value.start_date,end_date:l.value.end_date,status:"planning"},e=await F.convertContractToProject(a.value.id,s);h.value=!1,N.push(`/app/projects/${e.project.id}`)}catch(s){console.error("Error converting to project:",s)}finally{k.value=!1}},A=()=>{h.value=!1,a.value&&(l.value.name=a.value.title,l.value.description=a.value.description||"",l.value.start_date=a.value.start_date||new Date().toISOString().split("T")[0],l.value.end_date=a.value.end_date||"")},f=s=>new Intl.NumberFormat("it-IT").format(s||0),w=s=>s?new Date(s).toLocaleDateString("it-IT"):"N/A",U=st,G=rt;return K(async()=>{await L(),a.value&&await O()}),(s,e)=>{var c,y,I,V;const i=Q("router-link");return n(),o("div",ot,[u(et,{title:((c=a.value)==null?void 0:c.contract_number)||"Caricamento...",subtitle:"Dettaglio contratto",breadcrumbs:[{name:"CRM",href:"/app/crm"},{name:"Contratti",href:"/app/crm/contracts"},{name:((y=a.value)==null?void 0:y.contract_number)||"Dettaglio",href:"#",current:!0}],loading:_.value},{actions:b(()=>[a.value?(n(),o("div",nt,[u(i,{to:`/app/crm/contracts/${a.value.id}/edit`,class:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"},{default:b(()=>[u(S,{name:"pencil",class:"w-4 h-4 mr-2"}),e[5]||(e[5]=v(" Modifica "))]),_:1,__:[5]},8,["to"]),a.value.status==="active"&&q.value?(n(),o("button",{key:0,onClick:e[0]||(e[0]=r=>h.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 dark:bg-green-700 dark:hover:bg-green-800 transition-colors"},[u(S,{name:"arrow-up-tray",class:"w-4 h-4 mr-2"}),e[6]||(e[6]=v(" Converti in Progetto "))])):m("",!0)])):m("",!0)]),_:1},8,["title","breadcrumbs","loading"]),t("div",lt,[_.value?(n(),o("div",dt,e[7]||(e[7]=[t("div",{class:"flex justify-center"},[t("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})],-1)]))):a.value?(n(),o("div",it,[u(at,{stats:E.value},null,8,["stats"]),t("div",ut,[e[19]||(e[19]=t("div",{class:"px-6 py-4 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600"},[t("h2",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Informazioni Generali")],-1)),t("div",ct,[t("div",gt,[t("div",null,[t("h3",mt,d(a.value.title),1),t("div",vt,[t("div",pt,[e[8]||(e[8]=t("span",{class:"text-gray-500 dark:text-gray-400"},"Cliente:",-1)),u(i,{to:`/app/crm/clients/${a.value.client_id}`,class:"font-medium text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors"},{default:b(()=>{var r;return[v(d(((r=a.value.client)==null?void 0:r.name)||"N/A"),1)]}),_:1},8,["to"])]),t("div",yt,[e[9]||(e[9]=t("span",{class:"text-gray-500 dark:text-gray-400"},"Numero Contratto:",-1)),t("span",bt,d(a.value.contract_number),1)]),t("div",xt,[e[10]||(e[10]=t("span",{class:"text-gray-500 dark:text-gray-400"},"Tipo:",-1)),t("span",{class:W(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",z(G)(a.value.contract_type)])},d(z(U)(a.value.contract_type)),3)]),t("div",ft,[e[11]||(e[11]=t("span",{class:"text-gray-500 dark:text-gray-400"},"Stato:",-1)),u(T,{status:a.value.status,type:"contract"},null,8,["status"])])])]),t("div",null,[e[17]||(e[17]=t("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-3"},"Dettagli Finanziari",-1)),t("div",_t,[a.value.contract_type==="hourly"?(n(),o("div",kt,[t("div",ht,[e[12]||(e[12]=t("span",{class:"text-gray-500 dark:text-gray-400"},"Tariffa Oraria:",-1)),t("span",wt,"€"+d(f(a.value.hourly_rate))+"/ora",1)]),a.value.budget_hours?(n(),o("div",Ct,[e[13]||(e[13]=t("span",{class:"text-gray-500 dark:text-gray-400"},"Budget Ore:",-1)),t("span",jt,d(a.value.budget_hours)+" ore",1)])):m("",!0)])):a.value.budget_amount?(n(),o("div",Dt,[e[14]||(e[14]=t("span",{class:"text-gray-500 dark:text-gray-400"},"Budget Totale:",-1)),t("span",Tt,"€"+d(f(a.value.budget_amount)),1)])):m("",!0),t("div",It,[e[15]||(e[15]=t("span",{class:"text-gray-500 dark:text-gray-400"},"Data Inizio:",-1)),t("span",Vt,d(w(a.value.start_date)),1)]),t("div",zt,[e[16]||(e[16]=t("span",{class:"text-gray-500 dark:text-gray-400"},"Data Fine:",-1)),t("span",Mt,d(a.value.end_date?w(a.value.end_date):"Indeterminata"),1)])])])]),a.value.description?(n(),o("div",Bt,[e[18]||(e[18]=t("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-2"},"Descrizione",-1)),t("p",St,d(a.value.description),1)])):m("",!0)])]),t("div",$t,[t("div",Nt,[t("h2",Pt,"Progetti Collegati ("+d(p.value.length)+")",1)]),t("div",Ft,[p.value.length>0?(n(),o("div",Rt,[(n(!0),o(M,null,B(p.value,r=>(n(),o("div",{key:r.id,class:"border border-gray-200 dark:border-gray-600 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"},[t("div",qt,[t("div",null,[t("h4",Et,d(r.name),1),r.description?(n(),o("p",Lt,d(r.description),1)):m("",!0),t("div",Ot,[u(T,{status:r.status,type:"project"},null,8,["status"])])]),u(i,{to:`/app/projects/${r.id}`,class:"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm font-medium transition-colors"},{default:b(()=>e[20]||(e[20]=[v(" Visualizza ")])),_:2,__:[20]},1032,["to"])])]))),128))])):(n(),o("div",Ht,e[21]||(e[21]=[t("svg",{class:"w-12 h-12 mx-auto mb-4 text-gray-300 dark:text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})],-1),t("p",null,"Nessun progetto collegato",-1),t("p",{class:"text-sm"},"I progetti creati da questo contratto appariranno qui",-1)])))])]),t("div",At,[t("div",Ut,[t("h2",Gt,"Fatture Recenti ("+d(x.value.length)+")",1)]),t("div",Jt,[x.value.length>0?(n(),o("div",Kt,[(n(!0),o(M,null,B(x.value,r=>(n(),o("div",{key:r.id,class:"border border-gray-200 dark:border-gray-600 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"},[t("div",Qt,[t("div",null,[t("h4",Wt,d(r.invoice_number),1),t("p",Xt,d(w(r.issue_date))+" - "+d(w(r.due_date)),1),t("div",Yt,[u(T,{status:r.status,type:"invoice"},null,8,["status"]),t("span",Zt,"€"+d(f(r.total_amount)),1)])]),u(i,{to:`/app/invoices/${r.id}`,class:"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm font-medium transition-colors"},{default:b(()=>e[22]||(e[22]=[v(" Visualizza ")])),_:2,__:[22]},1032,["to"])])]))),128))])):(n(),o("div",te,e[23]||(e[23]=[t("svg",{class:"w-12 h-12 mx-auto mb-4 text-gray-300 dark:text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),t("p",null,"Nessuna fattura",-1),t("p",{class:"text-sm"},"Le fatture generate per questo contratto appariranno qui",-1)])))])])])):(n(),o("div",ee,[t("div",ae,[e[25]||(e[25]=t("svg",{class:"w-16 h-16 mx-auto mb-4 text-gray-300 dark:text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})],-1)),e[26]||(e[26]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},"Contratto non trovato",-1)),e[27]||(e[27]=t("p",{class:"text-gray-500 dark:text-gray-400 mb-4"},"Il contratto richiesto non esiste o non è accessibile",-1)),u(i,{to:"/app/crm/contracts",class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 transition-colors"},{default:b(()=>e[24]||(e[24]=[v(" Torna alla Lista Contratti ")])),_:1,__:[24]})])]))]),h.value?(n(),o("div",re,[t("div",se,[t("div",oe,[e[33]||(e[33]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Converti in Progetto",-1)),t("form",{onSubmit:X(H,["prevent"]),class:"space-y-4"},[t("div",null,[e[28]||(e[28]=t("label",{for:"project_name",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Nome Progetto * ",-1)),C(t("input",{id:"project_name","onUpdate:modelValue":e[1]||(e[1]=r=>l.value.name=r),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",placeholder:((I=a.value)==null?void 0:I.title)||""},null,8,ne),[[j,l.value.name]])]),t("div",null,[e[29]||(e[29]=t("label",{for:"project_description",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Descrizione ",-1)),C(t("textarea",{id:"project_description","onUpdate:modelValue":e[2]||(e[2]=r=>l.value.description=r),rows:"3",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",placeholder:((V=a.value)==null?void 0:V.description)||""},null,8,le),[[j,l.value.description]])]),t("div",de,[t("div",null,[e[30]||(e[30]=t("label",{for:"project_start",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Data Inizio ",-1)),C(t("input",{id:"project_start","onUpdate:modelValue":e[3]||(e[3]=r=>l.value.start_date=r),type:"date",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"},null,512),[[j,l.value.start_date]])]),t("div",null,[e[31]||(e[31]=t("label",{for:"project_end",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Data Fine ",-1)),C(t("input",{id:"project_end","onUpdate:modelValue":e[4]||(e[4]=r=>l.value.end_date=r),type:"date",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"},null,512),[[j,l.value.end_date]])])]),t("div",ie,[t("button",{type:"button",onClick:A,class:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"}," Annulla "),t("button",{type:"submit",disabled:k.value,class:"px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 disabled:opacity-50 dark:bg-green-700 dark:hover:bg-green-800 transition-colors"},[k.value?(n(),o("span",ce,e[32]||(e[32]=[t("div",{class:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"},null,-1),v(" Conversione... ")]))):(n(),o("span",ge,"Crea Progetto"))],8,ue)])],32)])])])):m("",!0)])}}};export{he as default};
