import{H as l}from"./app.js";import{b as o,j as a,F as g,q as x,l as t,g as m,n as d,e as u,t as n}from"./vendor.js";const p={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"},_={class:"flex items-center"},y={class:"ml-4"},b={class:"text-sm font-medium text-gray-600 dark:text-gray-400"},h={class:"text-2xl font-semibold text-gray-900 dark:text-white"},f={key:0,class:"text-xs text-gray-500 dark:text-gray-400 mt-1"},k={key:0,class:"mt-4 flex items-center"},w={class:"text-xs text-gray-500 dark:text-gray-400 ml-2"},N={__name:"StatsGrid",props:{stats:{type:Array,required:!0,validator:c=>c.every(s=>s.id&&s.label&&s.value!==void 0&&s.icon)}},setup(c){const s=(r,i)=>{if(!i)return r;switch(i){case"hours":return`${r}h`;case"percentage":return`${r}%`;case"currency":return new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(r);case"number":return new Intl.NumberFormat("it-IT").format(r);default:return r}};return(r,i)=>(a(),o("div",p,[(a(!0),o(g,null,x(c.stats,e=>(a(),o("div",{key:e.id,class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},[t("div",_,[t("div",{class:d(["p-2 rounded-lg",e.iconBgColor||"bg-blue-100 dark:bg-blue-900"])},[u(l,{name:e.icon,size:"md",class:d(e.iconColor||"text-blue-600 dark:text-blue-400")},null,8,["name","class"])],2),t("div",y,[t("p",b,n(e.label),1),t("p",h,n(s(e.value,e.format)),1),e.subtitle?(a(),o("p",f,n(e.subtitle),1)):m("",!0)])]),e.trend?(a(),o("div",k,[u(l,{name:e.trend.direction==="up"?"arrow-trending-up":"arrow-trending-down",size:"sm",class:d(e.trend.direction==="up"?"text-green-500":"text-red-500")},null,8,["name","class"]),t("span",{class:d(["text-sm ml-1",e.trend.direction==="up"?"text-green-600":"text-red-600"])},n(e.trend.value)+n(e.trend.format||"%"),3),t("span",w,n(e.trend.period||"vs mese scorso"),1)])):m("",!0)]))),128))]))}};export{N as _};
