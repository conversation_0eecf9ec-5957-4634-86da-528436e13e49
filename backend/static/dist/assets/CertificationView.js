import{r as z,c as D,o as Q,h as ie,b as d,j as r,l as e,e as a,p as P,v as u,g as p,t as o,n as S,E as ne,F as G,q as H,B as O,C as U,u as oe,s as le,f as re}from"./vendor.js";import{u as J}from"./certifications.js";import{u as K}from"./useToast.js";import{H as l}from"./app.js";import{_ as de}from"./PageHeader.js";import{_ as ce}from"./Breadcrumb.js";const ue={class:"certification-projects"},me={class:"flex justify-between items-center mb-6"},fe={class:"flex space-x-3"},pe={key:0,class:"flex justify-center py-8"},ve={key:1,class:"space-y-6"},ge={key:0,class:"grid grid-cols-1 md:grid-cols-4 gap-4"},xe={class:"bg-blue-50 rounded-lg p-4"},be={class:"flex items-center justify-between"},_e={class:"text-blue-900 text-2xl font-bold"},ye={class:"bg-green-50 rounded-lg p-4"},he={class:"flex items-center justify-between"},we={class:"text-green-900 text-2xl font-bold"},$e={class:"bg-purple-50 rounded-lg p-4"},ze={class:"flex items-center justify-between"},ke={class:"text-purple-900 text-2xl font-bold"},Ce={class:"bg-orange-50 rounded-lg p-4"},Se={class:"flex items-center justify-between"},je={class:"text-orange-900 text-xl font-bold"},Ae={key:1,class:"bg-blue-50 border border-blue-200 rounded-lg p-6"},Pe={class:"flex items-center justify-between mb-4"},De={class:"text-lg font-semibold text-blue-900 flex items-center"},Ie={class:"text-blue-700"},Ne={class:"flex space-x-3"},Te={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},Re={class:"text-sm font-medium text-blue-900"},Me={class:"text-sm font-medium text-blue-900"},Be={class:"text-sm font-medium text-blue-900"},Ee={class:"mt-4"},Le={class:"flex items-center justify-between text-sm text-blue-700 mb-1"},Ve={class:"w-full bg-blue-200 rounded-full h-2"},qe={key:0,class:"mt-4 p-3 bg-blue-100 rounded"},Fe={class:"text-blue-800 text-sm"},Oe={key:2,class:"space-y-4"},Ue={class:"space-y-3"},Ge={class:"flex items-center justify-between"},He={class:"flex-1"},Qe={class:"flex items-center space-x-3"},Je={class:"font-medium text-gray-900"},Ke={class:"text-sm text-gray-600"},We={class:"flex items-center space-x-4"},Xe={class:"text-center"},Ye={class:"text-center"},Ze={class:"text-sm font-semibold text-gray-900"},et={class:"text-center"},tt={class:"text-sm font-semibold text-gray-900"},st={key:3,class:"text-center py-12"},it={class:"flex justify-center space-x-3"},at={key:4,class:"bg-gray-50 rounded-lg p-4"},nt={class:"flex flex-wrap gap-3"},ot={key:0,class:"btn-warning btn-sm flex items-center gap-2"},lt={class:"btn-secondary btn-sm flex items-center gap-2"},rt={__name:"CertificationProjects",props:{certification:{type:Object,required:!0}},setup(y){const R=y,_=J(),{addToast:A}=K(),j=z(!1),x=z(null),v=D(()=>{var f;return(f=x.value)!=null&&f.projects&&x.value.projects.find(i=>i.is_current)||null}),b=D(()=>{var f;return(f=x.value)!=null&&f.projects?x.value.projects.filter(i=>!i.is_current):[]}),k=async()=>{j.value=!0;try{const f=await _.fetchCertificationProjects(R.certification.id);f.success&&(x.value=f.data)}catch{A("Errore nel caricamento progetti","error")}finally{j.value=!1}},m=f=>f?new Date(f).toLocaleDateString("it-IT"):null,s=f=>f?new Intl.NumberFormat("it-IT").format(f):"0",N=f=>({planning:"In Pianificazione",active:"Attivo",in_progress:"In Corso",on_hold:"In Pausa",completed:"Completato",cancelled:"Annullato"})[f]||f,T=f=>({planning:"bg-blue-100 text-blue-800",active:"bg-green-100 text-green-800",in_progress:"bg-yellow-100 text-yellow-800",on_hold:"bg-orange-100 text-orange-800",completed:"bg-purple-100 text-purple-800",cancelled:"bg-red-100 text-red-800"})[f]||"bg-gray-100 text-gray-800";return Q(()=>{k()}),(f,i)=>{var I;const h=ie("router-link");return r(),d("div",ue,[e("div",me,[i[1]||(i[1]=e("h3",{class:"text-lg font-semibold text-gray-900"},"Progetti Collegati",-1)),e("div",fe,[a(h,{to:`/app/certifications/${y.certification.id}/create-project`,class:"btn-primary btn-sm flex items-center gap-2"},{default:P(()=>[a(l,{name:"plus",size:"sm"}),i[0]||(i[0]=u(" Nuovo Progetto "))]),_:1,__:[0]},8,["to"])])]),j.value?(r(),d("div",pe,i[2]||(i[2]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"},null,-1)]))):(r(),d("div",ve,[(I=x.value)!=null&&I.stats?(r(),d("div",ge,[e("div",xe,[e("div",be,[e("div",null,[i[3]||(i[3]=e("p",{class:"text-blue-600 text-sm font-medium"},"Progetti Totali",-1)),e("p",_e,o(x.value.stats.total_projects),1)]),a(l,{name:"folder-open",class:"h-8 w-8 text-blue-400"})])]),e("div",ye,[e("div",he,[e("div",null,[i[4]||(i[4]=e("p",{class:"text-green-600 text-sm font-medium"},"Progetti Attivi",-1)),e("p",we,o(x.value.stats.active_projects),1)]),a(l,{name:"play",class:"h-8 w-8 text-green-400"})])]),e("div",$e,[e("div",ze,[e("div",null,[i[5]||(i[5]=e("p",{class:"text-purple-600 text-sm font-medium"},"Completati",-1)),e("p",ke,o(x.value.stats.completed_projects),1)]),a(l,{name:"check-circle",class:"h-8 w-8 text-purple-400"})])]),e("div",Ce,[e("div",Se,[e("div",null,[i[6]||(i[6]=e("p",{class:"text-orange-600 text-sm font-medium"},"Budget Totale",-1)),e("p",je,"€"+o(s(x.value.stats.total_budget)),1)]),a(l,{name:"currency-euro",class:"h-8 w-8 text-orange-400"})])])])):p("",!0),v.value?(r(),d("div",Ae,[e("div",Pe,[e("div",null,[e("h4",De,[a(l,{name:"star",class:"h-5 w-5 mr-2 text-blue-600"}),i[7]||(i[7]=u(" Progetto Attuale "))]),e("p",Ie,o(v.value.name),1)]),e("div",Ne,[a(h,{to:`/app/projects/${v.value.id}`,class:"btn-secondary btn-sm"},{default:P(()=>i[8]||(i[8]=[u(" Gestisci Progetto → ")])),_:1,__:[8]},8,["to"])])]),e("div",Te,[e("div",null,[i[9]||(i[9]=e("dt",{class:"text-sm text-blue-600"},"Stato",-1)),e("dd",null,[e("span",{class:S(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",T(v.value.status)])},o(N(v.value.status)),3)])]),e("div",null,[i[10]||(i[10]=e("dt",{class:"text-sm text-blue-600"},"Inizio",-1)),e("dd",Re,o(m(v.value.start_date)||"Non definito"),1)]),e("div",null,[i[11]||(i[11]=e("dt",{class:"text-sm text-blue-600"},"Fine Prevista",-1)),e("dd",Me,o(m(v.value.end_date)||"Non definita"),1)]),e("div",null,[i[12]||(i[12]=e("dt",{class:"text-sm text-blue-600"},"Budget",-1)),e("dd",Be,"€"+o(s(v.value.budget)),1)])]),e("div",Ee,[e("div",Le,[i[13]||(i[13]=e("span",null,"Progresso",-1)),e("span",null,o(v.value.completion_percentage||0)+"%",1)]),e("div",Ve,[e("div",{class:"h-2 rounded-full bg-blue-600 transition-all duration-300",style:ne({width:(v.value.completion_percentage||0)+"%"})},null,4)])]),v.value.description?(r(),d("div",qe,[e("p",Fe,o(v.value.description),1)])):p("",!0)])):p("",!0),b.value.length>0?(r(),d("div",Oe,[i[17]||(i[17]=e("h4",{class:"text-lg font-semibold text-gray-900"},"Storico Progetti",-1)),e("div",Ue,[(r(!0),d(G,null,H(b.value,$=>(r(),d("div",{key:$.id,class:"bg-white border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors"},[e("div",Ge,[e("div",He,[e("div",Qe,[a(l,{name:"folder",class:"h-5 w-5 text-gray-400"}),e("div",null,[e("h5",Je,o($.name),1),e("p",Ke,o($.description||"Nessuna descrizione"),1)])])]),e("div",We,[e("div",Xe,[e("span",{class:S(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",T($.status)])},o(N($.status)),3)]),e("div",Ye,[e("div",Ze,"€"+o(s($.budget)),1),i[14]||(i[14]=e("div",{class:"text-xs text-gray-500"},"Budget",-1))]),e("div",et,[e("div",tt,o(m($.end_date)||"N/A"),1),i[15]||(i[15]=e("div",{class:"text-xs text-gray-500"},"Completato",-1))]),a(h,{to:`/app/projects/${$.id}`,class:"btn-secondary btn-sm flex items-center gap-1"},{default:P(()=>[a(l,{name:"eye",size:"xs"}),i[16]||(i[16]=u(" Dettagli "))]),_:2,__:[16]},1032,["to"])])])]))),128))])])):p("",!0),!v.value&&b.value.length===0?(r(),d("div",st,[a(l,{name:"folder-open",class:"h-12 w-12 text-gray-400 mx-auto mb-4"}),i[20]||(i[20]=e("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"Nessun Progetto Collegato",-1)),i[21]||(i[21]=e("p",{class:"text-gray-500 mb-6"}," Questa certificazione non ha ancora progetti collegati. Crea un progetto per gestire le attività di preparazione o rinnovo. ",-1)),e("div",it,[a(h,{to:"/app/projects/create",class:"btn-secondary flex items-center gap-2"},{default:P(()=>[a(l,{name:"folder-plus",size:"sm"}),i[18]||(i[18]=u(" Nuovo Progetto "))]),_:1,__:[18]}),a(h,{to:`/app/certifications/${y.certification.id}/create-project`,class:"btn-primary flex items-center gap-2"},{default:P(()=>[a(l,{name:"plus",size:"sm"}),i[19]||(i[19]=u(" Progetto Certificazione "))]),_:1,__:[19]},8,["to"])])])):p("",!0),v.value||b.value.length>0?(r(),d("div",at,[i[25]||(i[25]=e("h4",{class:"font-medium text-gray-900 mb-3"},"Azioni Rapide",-1)),e("div",nt,[y.certification.is_expiring_soon?(r(),d("button",ot,[a(l,{name:"exclamation-triangle",size:"sm"}),i[22]||(i[22]=u(" Avvia Rinnovo "))])):p("",!0),a(h,{to:"/app/projects",class:"btn-secondary btn-sm flex items-center gap-2"},{default:P(()=>[a(l,{name:"folder-open",size:"sm"}),i[23]||(i[23]=u(" Tutti i Progetti "))]),_:1,__:[23]}),e("button",lt,[a(l,{name:"document-text",size:"sm"}),i[24]||(i[24]=u(" Report Progetti "))])])])):p("",!0)]))])}}},dt={class:"certification-status-manager"},ct={key:0,class:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6"},ut={class:"flex items-center justify-between"},mt={class:"font-semibold text-blue-900 flex items-center"},ft={class:"text-blue-700 text-sm mt-1"},pt={class:"font-medium"},vt={class:"flex space-x-2"},gt={key:1,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},xt={class:"bg-white rounded-lg p-6 max-w-md w-full mx-4"},bt={class:"flex items-center mb-4"},_t={class:"text-gray-600 mb-6"},yt={class:"space-y-4 mb-6"},ht={class:"text-xs text-gray-500 mt-1"},wt={class:"flex justify-end space-x-3"},$t=["disabled"],zt=["disabled"],kt={key:2,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},Ct={class:"bg-white rounded-lg p-6 max-w-md w-full mx-4"},St={class:"flex items-center mb-4"},jt={class:"text-gray-600 mb-6"},At={key:0,class:"text-orange-600"},Pt={class:"flex justify-end space-x-3"},Dt=["disabled"],It=["disabled"],Nt={key:3,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},Tt={class:"bg-white rounded-lg p-6 max-w-md w-full mx-4"},Rt={class:"flex items-center mb-4"},Mt={class:"text-gray-600 mb-6"},Bt={class:"flex justify-end space-x-3"},Et=["disabled"],Lt=["disabled"],Vt={__name:"CertificationStatusManager",props:{certification:{type:Object,required:!0}},emits:["status-updated"],setup(y,{emit:R}){const _=y,A=R,j=J(),{addToast:x}=K(),v=z(!1),b=z(!1),k=z(!1),m=z(!1),s=z({issue_date:new Date().toISOString().split("T")[0],certificate_number:"",certifying_body:"",expiry_date:""}),N=D(()=>["planning","active","in_renewal"].includes(_.certification.status)),T=D(()=>_.certification.status==="planning"),f=D(()=>_.certification.status==="active"),i=D(()=>["active","in_renewal"].includes(_.certification.status)),h=C=>({planning:"In Pianificazione",active:"Attiva",in_renewal:"In Rinnovo",expired:"Scaduta",suspended:"Sospesa"})[C]||C,I=async()=>{if(!s.value.issue_date){x("Data di rilascio richiesta","error");return}m.value=!0;try{const C={status:"active",issue_date:s.value.issue_date,certificate_number:s.value.certificate_number,certifying_body:s.value.certifying_body,health_score:100};s.value.expiry_date&&(C.expiry_date=s.value.expiry_date),await j.updateCertificationStatus(_.certification.id,C),x("Certificazione marcata come attiva!","success"),E(),A("status-updated")}catch{x("Errore nell'attivazione della certificazione","error")}finally{m.value=!1}},$=async()=>{m.value=!0;try{await j.updateCertificationStatus(_.certification.id,{status:"in_renewal"}),x("Processo di rinnovo avviato","success"),b.value=!1,A("status-updated")}catch{x("Errore nell'avvio del rinnovo","error")}finally{m.value=!1}},B=async()=>{m.value=!0;try{await j.updateCertificationStatus(_.certification.id,{status:"suspended",health_score:0}),x("Certificazione sospesa","success"),k.value=!1,A("status-updated")}catch{x("Errore nella sospensione della certificazione","error")}finally{m.value=!1}},E=()=>{v.value=!1,s.value={issue_date:new Date().toISOString().split("T")[0],certificate_number:"",certifying_body:"",expiry_date:""}};return Q(()=>{_.certification.certificate_number&&(s.value.certificate_number=_.certification.certificate_number),_.certification.certifying_body&&(s.value.certifying_body=_.certification.certifying_body)}),(C,n)=>{var L,V,q,F;return r(),d("div",dt,[N.value?(r(),d("div",ct,[e("div",ut,[e("div",null,[e("h4",mt,[a(l,{name:"arrow-path",class:"h-5 w-5 mr-2"}),n[9]||(n[9]=u(" Gestione Status Certificazione "))]),e("p",ft,[n[10]||(n[10]=u(" Status attuale: ")),e("span",pt,o(h(y.certification.status)),1)])]),e("div",vt,[T.value?(r(),d("button",{key:0,onClick:n[0]||(n[0]=w=>v.value=!0),class:"btn-success btn-sm flex items-center gap-2"},[a(l,{name:"check-circle",size:"sm"}),n[11]||(n[11]=u(" Marca come Attiva "))])):p("",!0),f.value?(r(),d("button",{key:1,onClick:n[1]||(n[1]=w=>b.value=!0),class:"btn-warning btn-sm flex items-center gap-2"},[a(l,{name:"arrow-path",size:"sm"}),n[12]||(n[12]=u(" Avvia Rinnovo "))])):p("",!0),i.value?(r(),d("button",{key:2,onClick:n[2]||(n[2]=w=>k.value=!0),class:"btn-danger btn-sm flex items-center gap-2"},[a(l,{name:"pause",size:"sm"}),n[13]||(n[13]=u(" Sospendi "))])):p("",!0)])])])):p("",!0),v.value?(r(),d("div",gt,[e("div",xt,[e("div",bt,[a(l,{name:"check-circle",class:"h-6 w-6 text-green-500 mr-3"}),n[14]||(n[14]=e("h3",{class:"text-lg font-semibold text-gray-900"},"Marca Certificazione come Attiva",-1))]),e("p",_t,[n[15]||(n[15]=u(" Confermi che la certificazione ")),e("strong",null,o((L=y.certification.standard)==null?void 0:L.name),1),n[16]||(n[16]=u(" è stata ottenuta? "))]),e("div",yt,[e("div",null,[n[17]||(n[17]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Data Rilascio * ",-1)),O(e("input",{"onUpdate:modelValue":n[3]||(n[3]=w=>s.value.issue_date=w),type:"date",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500",required:""},null,512),[[U,s.value.issue_date]])]),e("div",null,[n[18]||(n[18]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Numero Certificato ",-1)),O(e("input",{"onUpdate:modelValue":n[4]||(n[4]=w=>s.value.certificate_number=w),type:"text",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500",placeholder:"es. ISO9001-2024-001"},null,512),[[U,s.value.certificate_number]])]),e("div",null,[n[19]||(n[19]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Ente Certificatore ",-1)),O(e("input",{"onUpdate:modelValue":n[5]||(n[5]=w=>s.value.certifying_body=w),type:"text",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500",placeholder:"es. Bureau Veritas, DNV, TÜV SÜD"},null,512),[[U,s.value.certifying_body]])]),e("div",null,[n[20]||(n[20]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"}," Data Scadenza ",-1)),O(e("input",{"onUpdate:modelValue":n[6]||(n[6]=w=>s.value.expiry_date=w),type:"date",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"},null,512),[[U,s.value.expiry_date]]),e("p",ht," Se non specificata, sarà calcolata automaticamente ("+o(((V=y.certification.standard)==null?void 0:V.typical_validity_years)||3)+" anni) ",1)])]),e("div",wt,[e("button",{onClick:E,class:"btn-secondary",disabled:m.value}," Annulla ",8,$t),e("button",{onClick:I,class:"btn-success flex items-center",disabled:m.value||!s.value.issue_date},[a(l,{name:m.value?"arrow-path":"check",size:"sm",class:S([{"animate-spin":m.value},"mr-2"])},null,8,["name","class"]),u(" "+o(m.value?"Attivazione...":"Attiva Certificazione"),1)],8,zt)])])])):p("",!0),b.value?(r(),d("div",kt,[e("div",Ct,[e("div",St,[a(l,{name:"arrow-path",class:"h-6 w-6 text-orange-500 mr-3"}),n[21]||(n[21]=e("h3",{class:"text-lg font-semibold text-gray-900"},"Avvia Processo di Rinnovo",-1))]),e("p",jt,[n[22]||(n[22]=u(" Vuoi avviare il processo di rinnovo per ")),e("strong",null,o((q=y.certification.standard)==null?void 0:q.name),1),n[23]||(n[23]=u("? ")),y.certification.days_to_expiry?(r(),d("span",At," (Scade tra "+o(y.certification.days_to_expiry)+" giorni) ",1)):p("",!0)]),e("div",Pt,[e("button",{onClick:n[7]||(n[7]=w=>b.value=!1),class:"btn-secondary",disabled:m.value}," Annulla ",8,Dt),e("button",{onClick:$,class:"btn-warning flex items-center",disabled:m.value},[a(l,{name:(m.value,"arrow-path"),size:"sm",class:S([{"animate-spin":m.value},"mr-2"])},null,8,["name","class"]),u(" "+o(m.value?"Avvio...":"Avvia Rinnovo"),1)],8,It)])])])):p("",!0),k.value?(r(),d("div",Nt,[e("div",Tt,[e("div",Rt,[a(l,{name:"pause",class:"h-6 w-6 text-red-500 mr-3"}),n[24]||(n[24]=e("h3",{class:"text-lg font-semibold text-gray-900"},"Sospendi Certificazione",-1))]),e("p",Mt,[n[25]||(n[25]=u(" Sei sicuro di voler sospendere ")),e("strong",null,o((F=y.certification.standard)==null?void 0:F.name),1),n[26]||(n[26]=u("? La certificazione non sarà più considerata attiva. "))]),e("div",Bt,[e("button",{onClick:n[8]||(n[8]=w=>k.value=!1),class:"btn-secondary",disabled:m.value}," Annulla ",8,Et),e("button",{onClick:B,class:"btn-danger flex items-center",disabled:m.value},[a(l,{name:m.value?"arrow-path":"pause",size:"sm",class:S([{"animate-spin":m.value},"mr-2"])},null,8,["name","class"]),u(" "+o(m.value?"Sospensione...":"Sospendi"),1)],8,Lt)])])])):p("",!0)])}}},qt={class:"certification-view"},Ft={key:0,class:"flex justify-center items-center py-12"},Ot={key:1,class:"bg-red-50 border border-red-200 rounded-lg p-4 mb-6"},Ut={class:"flex items-center"},Gt={class:"text-red-800"},Ht={key:2,class:"space-y-6"},Qt={class:"bg-white rounded-lg shadow-sm"},Jt={class:"flex border-b border-gray-200"},Kt=["onClick"],Wt={class:"p-6"},Xt={key:0,class:"space-y-6"},Yt={class:"flex items-center justify-between"},Zt={class:"flex items-center"},es={class:"font-semibold"},ts={class:"text-sm opacity-90"},ss={class:"text-right"},is={class:"text-lg font-bold"},as={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},ns={class:"bg-blue-50 rounded-lg p-4"},os={class:"flex items-center justify-between"},ls={class:"text-blue-900 text-lg font-bold"},rs={class:"bg-green-50 rounded-lg p-4"},ds={class:"flex items-center justify-between"},cs={class:"text-green-900 text-lg font-bold"},us={class:"bg-orange-50 rounded-lg p-4"},ms={class:"flex items-center justify-between"},fs={class:"text-orange-900 text-lg font-bold"},ps={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},vs={class:"bg-gray-50 rounded-lg p-4"},gs={class:"font-semibold text-gray-900 mb-4 flex items-center"},xs={class:"space-y-3"},bs={class:"text-gray-900"},_s={class:"text-gray-900"},ys={key:0},hs={class:"text-gray-900"},ws={class:"text-gray-900"},$s={class:"bg-gray-50 rounded-lg p-4"},zs={class:"font-semibold text-gray-900 mb-4 flex items-center"},ks={class:"space-y-3"},Cs={key:0},Ss={class:"text-gray-900"},js={class:"text-gray-900"},As={key:1},Ps={key:2,class:"space-y-4"},Ds={class:"flex justify-between items-center"},Is={class:"btn-primary btn-sm flex items-center gap-2"},Ns={key:0,class:"space-y-3"},Ts={class:"flex items-center justify-between"},Rs={class:"flex-1"},Ms={class:"flex items-center space-x-3"},Bs={class:"font-medium text-gray-900"},Es={class:"text-sm text-gray-600"},Ls={key:0,class:"ml-2"},Vs={class:"flex items-center space-x-4"},qs={class:"text-center"},Fs={key:0,class:"text-center"},Os={class:"text-sm font-semibold text-gray-900"},Us={key:1,class:"text-center"},Gs={class:"text-sm font-semibold text-gray-900"},Hs={key:1,class:"text-center py-8"},Qs={class:"btn-primary flex items-center gap-2 mx-auto"},Js={key:3,class:"space-y-4"},Ks={class:"flex justify-between items-center"},Ws={class:"btn-primary btn-sm flex items-center gap-2"},Xs={class:"bg-orange-50 border border-orange-200 rounded-lg p-4"},Ys={class:"space-y-2"},Zs={class:"flex justify-between"},ei={class:"font-medium"},ti={class:"flex justify-between"},si={class:"font-medium"},ii={class:"flex justify-between"},ai={class:"bg-gray-50 border border-gray-200 rounded-lg p-4"},ni={class:"flex items-center"},oi={key:4,class:"space-y-4"},li={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},ri={class:"bg-green-50 rounded-lg p-4"},di={class:"flex items-center justify-between"},ci={class:"text-green-900 text-2xl font-bold"},ui={class:"bg-blue-50 rounded-lg p-4"},mi={class:"flex items-center justify-between"},fi={class:"text-blue-900 text-2xl font-bold"},pi={class:"bg-purple-50 rounded-lg p-4"},vi={class:"flex items-center justify-between"},gi={class:"text-purple-900 text-lg font-bold"},xi={class:"bg-gray-50 border border-gray-200 rounded-lg p-4"},bi={class:"flex items-center"},_i={key:3,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},yi={class:"bg-white rounded-lg p-6 max-w-md w-full mx-4"},hi={class:"flex items-center mb-4"},wi={class:"text-gray-600 mb-6"},$i={class:"flex justify-end space-x-3"},zi=["disabled"],ki=["disabled"],Ni={__name:"CertificationView",setup(y){const R=le(),_=oe(),A=J(),{addToast:j}=K(),x=z(!1),v=z(null),b=z("overview"),k=z(!1),m=z(!1),s=D(()=>A.currentCertification),N=D(()=>{var c,t,M;return[{name:"Certificazioni",href:"/app/certifications/dashboard"},{name:"Lista",href:"/app/certifications/list"},{name:((t=(c=s.value)==null?void 0:c.standard)==null?void 0:t.name)||((M=s.value)==null?void 0:M.standard_name)||"Dettagli",href:null}]}),T=[{id:"overview",name:"Panoramica",icon:"eye"},{id:"projects",name:"Storico Progetti",icon:"folder-open"},{id:"audits",name:"Audit",icon:"clipboard-document-check"},{id:"renewals",name:"Scadenze & Rinnovi",icon:"calendar"},{id:"analytics",name:"Analytics",icon:"chart-bar"}],f=async()=>{const c=_.params.id;if(!c){v.value="ID certificazione non valido";return}x.value=!0,v.value=null;try{await A.fetchCertification(parseInt(c))}catch(t){v.value=t.message||"Errore nel caricamento della certificazione"}finally{x.value=!1}},i=async()=>{if(s.value){m.value=!0;try{await A.deleteCertification(s.value.id),j("Certificazione eliminata con successo","success"),R.push("/app/certifications/list")}catch{j("Errore nell'eliminazione della certificazione","error")}finally{m.value=!1,k.value=!1}}},h=c=>c?new Date(c).toLocaleDateString("it-IT"):null,I=c=>c?new Intl.NumberFormat("it-IT").format(c):"0",$=c=>({quality:"Qualità",security:"Sicurezza",environmental:"Ambientale",privacy:"Privacy",regulatory:"Normativo"})[c]||c||"Non specificata",B=c=>({draft:"Bozza",active:"Attiva",in_progress:"In Corso",completed:"Completata",expired:"Scaduta",suspended:"Sospesa",deleted:"Eliminata"})[c]||c||"Sconosciuto",E=c=>({draft:"La certificazione è in fase di preparazione",active:"Certificazione attiva e valida",in_progress:"Processo di certificazione in corso",completed:"Certificazione ottenuta con successo",expired:"Certificazione scaduta, necessario rinnovo",suspended:"Certificazione temporaneamente sospesa",deleted:"Certificazione eliminata dal sistema"})[c]||"Stato non definito",C=c=>({draft:"document-text",active:"check-circle",in_progress:"arrow-path",completed:"trophy",expired:"exclamation-triangle",suspended:"pause-circle",deleted:"x-circle"})[c]||"question-mark-circle",n=c=>({draft:"bg-gray-100 text-gray-800",active:"bg-green-100 text-green-800",in_progress:"bg-blue-100 text-blue-800",completed:"bg-purple-100 text-purple-800",expired:"bg-red-100 text-red-800",suspended:"bg-yellow-100 text-yellow-800",deleted:"bg-gray-100 text-gray-800"})[c]||"bg-gray-100 text-gray-800",L=c=>({low:"Bassa",medium:"Media",high:"Alta",critical:"Critica"})[c]||c||"Non definita",V=c=>({initial:"Audit Iniziale",surveillance:"Audit di Sorveglianza",renewal:"Audit di Rinnovo",special:"Audit Speciale"})[c]||c||"Audit",q=c=>({initial:"rocket-launch",surveillance:"eye",renewal:"arrow-path",special:"exclamation-triangle"})[c]||"clipboard-document-check",F=c=>({scheduled:"text-blue-600",in_progress:"text-yellow-600",completed:"text-green-600",cancelled:"text-gray-600"})[c]||"text-gray-600",w=c=>({scheduled:"Programmato",in_progress:"In Corso",completed:"Completato",cancelled:"Annullato"})[c]||c||"Sconosciuto",ae=c=>({scheduled:"bg-blue-100 text-blue-800",in_progress:"bg-yellow-100 text-yellow-800",completed:"bg-green-100 text-green-800",cancelled:"bg-gray-100 text-gray-800"})[c]||"bg-gray-100 text-gray-800";return Q(()=>{f()}),(c,t)=>{var W,X,Y,Z,ee,te,se;const M=ie("router-link");return r(),d("div",qt,[a(ce,{items:N.value},null,8,["items"]),a(de,{title:((X=(W=s.value)==null?void 0:W.standard)==null?void 0:X.name)||((Y=s.value)==null?void 0:Y.standard_name)||"Caricamento...",subtitle:s.value?`${$((Z=s.value.standard)==null?void 0:Z.category)} • ${B(s.value.status)}`:"Dettagli certificazione aziendale",icon:"shield-check","icon-color":"text-purple-600"},{actions:P(()=>[s.value?(r(),re(M,{key:0,to:`/app/certifications/${s.value.id}/edit`,class:"btn-secondary flex items-center gap-2"},{default:P(()=>[a(l,{name:"pencil",size:"sm"}),t[2]||(t[2]=u(" Modifica "))]),_:1,__:[2]},8,["to"])):p("",!0),s.value&&s.value.status!=="deleted"?(r(),d("button",{key:1,onClick:t[0]||(t[0]=g=>k.value=!0),class:"btn-danger flex items-center gap-2"},[a(l,{name:"trash",size:"sm"}),t[3]||(t[3]=u(" Elimina "))])):p("",!0),a(M,{to:"/app/certifications/dashboard",class:"btn-secondary flex items-center gap-2"},{default:P(()=>[a(l,{name:"arrow-left",size:"sm"}),t[4]||(t[4]=u(" Dashboard "))]),_:1,__:[4]})]),_:1},8,["title","subtitle"]),x.value?(r(),d("div",Ft,t[5]||(t[5]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"},null,-1),e("span",{class:"ml-3 text-gray-600"},"Caricamento certificazione...",-1)]))):v.value?(r(),d("div",Ot,[e("div",Ut,[a(l,{name:"exclamation-triangle",class:"h-5 w-5 text-red-400 mr-2"}),e("span",Gt,o(v.value),1)])])):s.value?(r(),d("div",Ht,[a(Vt,{certification:s.value,onStatusUpdated:f},null,8,["certification"]),e("div",Qt,[e("nav",Jt,[(r(),d(G,null,H(T,g=>e("button",{key:g.id,onClick:Ci=>b.value=g.id,class:S(["px-6 py-3 text-sm font-medium border-b-2 transition-colors",b.value===g.id?"border-purple-500 text-purple-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"])},[a(l,{name:g.icon,size:"sm",class:"inline mr-2"},null,8,["name"]),u(" "+o(g.name),1)],10,Kt)),64))]),e("div",Wt,[b.value==="overview"?(r(),d("div",Xt,[e("div",{class:S([n(s.value.status),"rounded-lg p-4"])},[e("div",Yt,[e("div",Zt,[a(l,{name:C(s.value.status),class:"h-6 w-6 mr-3"},null,8,["name"]),e("div",null,[e("h3",es,o(B(s.value.status)),1),e("p",ts,o(E(s.value.status)),1)])]),e("div",ss,[e("div",is,o(s.value.current_score||0)+"%",1),t[6]||(t[6]=e("div",{class:"text-sm opacity-90"},"Score Attuale",-1))])])],2),e("div",as,[e("div",ns,[e("div",os,[e("div",null,[t[7]||(t[7]=e("p",{class:"text-blue-600 text-sm font-medium"},"Data Target",-1)),e("p",ls,o(h(s.value.target_date)||"Non definita"),1)]),a(l,{name:"calendar",class:"h-8 w-8 text-blue-400"})])]),e("div",rs,[e("div",ds,[e("div",null,[t[8]||(t[8]=e("p",{class:"text-green-600 text-sm font-medium"},"Budget",-1)),e("p",cs," €"+o(I(s.value.estimated_budget)||"0"),1)]),a(l,{name:"currency-euro",class:"h-8 w-8 text-green-400"})])]),e("div",us,[e("div",ms,[e("div",null,[t[9]||(t[9]=e("p",{class:"text-orange-600 text-sm font-medium"},"Priorità",-1)),e("p",fs,o(L(s.value.priority)),1)]),a(l,{name:"flag",class:"h-8 w-8 text-orange-400"})])])]),e("div",ps,[e("div",vs,[e("h3",gs,[a(l,{name:"information-circle",class:"h-5 w-5 mr-2"}),t[10]||(t[10]=u(" Informazioni Base "))]),e("div",xs,[e("div",null,[t[11]||(t[11]=e("label",{class:"text-sm font-medium text-gray-600"},"Standard",-1)),e("p",bs,o(s.value.standard_code),1)]),e("div",null,[t[12]||(t[12]=e("label",{class:"text-sm font-medium text-gray-600"},"Categoria",-1)),e("p",_s,o($(s.value.category)),1)]),s.value.description?(r(),d("div",ys,[t[13]||(t[13]=e("label",{class:"text-sm font-medium text-gray-600"},"Descrizione",-1)),e("p",hs,o(s.value.description),1)])):p("",!0),e("div",null,[t[14]||(t[14]=e("label",{class:"text-sm font-medium text-gray-600"},"Data Creazione",-1)),e("p",ws,o(h(s.value.created_at)),1)])])]),e("div",$s,[e("h3",zs,[a(l,{name:"users",class:"h-5 w-5 mr-2"}),t[15]||(t[15]=u(" Team "))]),e("div",ks,[s.value.team_lead_id?(r(),d("div",Cs,[t[16]||(t[16]=e("label",{class:"text-sm font-medium text-gray-600"},"Team Lead",-1)),e("p",Ss,"Team Lead ID: "+o(s.value.team_lead_id),1)])):p("",!0),e("div",null,[t[17]||(t[17]=e("label",{class:"text-sm font-medium text-gray-600"},"Progetto Collegato",-1)),e("p",js,o(s.value.project_id?`Progetto #${s.value.project_id}`:"Nessun progetto collegato"),1)])])])])])):b.value==="projects"?(r(),d("div",As,[a(rt,{certification:s.value},null,8,["certification"])])):b.value==="audits"?(r(),d("div",Ps,[e("div",Ds,[t[19]||(t[19]=e("h3",{class:"text-lg font-semibold text-gray-900"},"Audit e Verifiche",-1)),e("button",Is,[a(l,{name:"plus",size:"sm"}),t[18]||(t[18]=u(" Nuovo Audit "))])]),s.value.audit_events&&s.value.audit_events.length>0?(r(),d("div",Ns,[(r(!0),d(G,null,H(s.value.audit_events,g=>(r(),d("div",{key:g.id,class:"bg-white border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors"},[e("div",Ts,[e("div",Rs,[e("div",Ms,[a(l,{name:q(g.audit_type),class:S([F(g.status),"h-5 w-5"])},null,8,["name","class"]),e("div",null,[e("h4",Bs,o(V(g.audit_type)),1),e("p",Es,[u(o(h(g.planned_date))+" ",1),g.lead_auditor?(r(),d("span",Ls,"• "+o(g.lead_auditor),1)):p("",!0)])])])]),e("div",Vs,[e("div",qs,[e("div",{class:S([ae(g.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},o(w(g.status)),3)]),g.overall_score?(r(),d("div",Fs,[e("div",Os,o(g.overall_score)+"%",1),t[20]||(t[20]=e("div",{class:"text-xs text-gray-500"},"Score",-1))])):p("",!0),g.major_findings||g.minor_findings?(r(),d("div",Us,[e("div",Gs,o((g.major_findings||0)+(g.minor_findings||0)),1),t[21]||(t[21]=e("div",{class:"text-xs text-gray-500"},"Findings",-1))])):p("",!0)])])]))),128))])):(r(),d("div",Hs,[a(l,{name:"clipboard-document-check",class:"h-12 w-12 text-gray-400 mx-auto mb-4"}),t[23]||(t[23]=e("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"Nessun Audit Registrato",-1)),t[24]||(t[24]=e("p",{class:"text-gray-500 mb-4"},"Non sono stati ancora programmati audit per questa certificazione",-1)),e("button",Qs,[a(l,{name:"plus",size:"sm"}),t[22]||(t[22]=u(" Programma Primo Audit "))])]))])):b.value==="renewals"?(r(),d("div",Js,[e("div",Ks,[t[26]||(t[26]=e("h3",{class:"text-lg font-semibold text-gray-900"},"Scadenze & Rinnovi",-1)),e("button",Ws,[a(l,{name:"plus",size:"sm"}),t[25]||(t[25]=u(" Pianifica Rinnovo "))])]),e("div",Xs,[t[30]||(t[30]=e("h4",{class:"font-semibold text-orange-900 mb-3"},"Timeline Certificazione",-1)),e("div",Ys,[e("div",Zs,[t[27]||(t[27]=e("span",{class:"text-orange-700"},"Data Rilascio:",-1)),e("span",ei,o(h(s.value.issue_date)),1)]),e("div",ti,[t[28]||(t[28]=e("span",{class:"text-orange-700"},"Data Scadenza:",-1)),e("span",si,o(h(s.value.expiry_date)),1)]),e("div",ii,[t[29]||(t[29]=e("span",{class:"text-orange-700"},"Giorni Rimanenti:",-1)),e("span",{class:S(["font-medium",s.value.days_to_expiry<=90?"text-red-600":"text-green-600"])},o(s.value.days_to_expiry)+" giorni ",3)])])]),e("div",ai,[e("div",ni,[a(l,{name:"clock",class:"h-5 w-5 text-gray-600 mr-2"}),t[31]||(t[31]=e("span",{class:"text-gray-700"},"Gestione completa rinnovi in sviluppo.",-1))])])])):b.value==="analytics"?(r(),d("div",oi,[t[36]||(t[36]=e("h3",{class:"text-lg font-semibold text-gray-900"},"Analytics & Metriche",-1)),e("div",li,[e("div",ri,[e("div",di,[e("div",null,[t[32]||(t[32]=e("p",{class:"text-green-600 text-sm font-medium"},"Health Score",-1)),e("p",ci,o(s.value.health_score||0)+"%",1)]),a(l,{name:"heart",class:"h-8 w-8 text-green-400"})])]),e("div",ui,[e("div",mi,[e("div",null,[t[33]||(t[33]=e("p",{class:"text-blue-600 text-sm font-medium"},"Readiness Score",-1)),e("p",fi,o(s.value.readiness_score||0)+"%",1)]),a(l,{name:"check-circle",class:"h-8 w-8 text-blue-400"})])]),e("div",pi,[e("div",vi,[e("div",null,[t[34]||(t[34]=e("p",{class:"text-purple-600 text-sm font-medium"},"Costo Annuale",-1)),e("p",gi,"€"+o(I(s.value.annual_maintenance_cost)),1)]),a(l,{name:"currency-euro",class:"h-8 w-8 text-purple-400"})])])]),e("div",xi,[e("div",bi,[a(l,{name:"chart-bar",class:"h-5 w-5 text-gray-600 mr-2"}),t[35]||(t[35]=e("span",{class:"text-gray-700"},"Analytics avanzate e report in sviluppo.",-1))])])])):p("",!0)])])])):p("",!0),k.value?(r(),d("div",_i,[e("div",yi,[e("div",hi,[a(l,{name:"exclamation-triangle",class:"h-6 w-6 text-red-500 mr-3"}),t[37]||(t[37]=e("h3",{class:"text-lg font-semibold text-gray-900"},"Conferma Eliminazione",-1))]),e("p",wi,' Sei sicuro di voler eliminare la certificazione "'+o(((te=(ee=s.value)==null?void 0:ee.standard)==null?void 0:te.name)||((se=s.value)==null?void 0:se.standard_name))+'"? Questa azione non può essere annullata. ',1),e("div",$i,[e("button",{onClick:t[1]||(t[1]=g=>k.value=!1),class:"btn-secondary",disabled:m.value}," Annulla ",8,zi),e("button",{onClick:i,class:"btn-danger flex items-center",disabled:m.value},[a(l,{name:m.value?"arrow-path":"trash",size:"sm",class:S([{"animate-spin":m.value},"mr-2"])},null,8,["name","class"]),u(" "+o(m.value?"Eliminazione...":"Elimina"),1)],8,ki)])])])):p("",!0)])}}};export{Ni as default};
