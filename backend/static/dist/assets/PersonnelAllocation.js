import{b as J,H as Q}from"./app.js";import{_ as W}from"./PageHeader.js";import{_ as Y}from"./StatsGrid.js";import{_ as Z}from"./FilterBar.js";import{c as y,b as i,j as s,l,g as f,n as z,t as c,F as S,q as N,E as ee,G as D,r as u,o as te,e as T,f as F,p as I,x as ae}from"./vendor.js";import{_ as le}from"./DataTable.js";import{u as re}from"./useFormatters.js";import"./formatters.js";const oe={class:"border border-gray-200 dark:border-gray-700 rounded-lg p-4"},se={class:"flex items-center justify-between mb-4"},ne={class:"flex items-center"},ie={class:"ml-3"},ce={class:"text-lg font-medium text-gray-900 dark:text-white"},ue={class:"text-sm text-gray-500 dark:text-gray-400"},de={class:"text-right"},me={class:"space-y-3"},ge={class:"flex justify-between text-sm mb-1"},ye={class:"text-gray-600 dark:text-gray-400"},he={class:"text-gray-900 dark:text-white"},ve={class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"},_e={key:0,class:"mt-4"},pe={class:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},be={class:"space-y-2"},fe={class:"text-gray-600 dark:text-gray-400"},xe={class:"flex items-center space-x-2"},ke={class:"text-gray-900 dark:text-white"},Ce={class:"text-gray-500 dark:text-gray-400"},we={key:1,class:"mt-4"},ze={key:2,class:"mt-4 flex justify-end space-x-2"},Ae={__name:"PersonCard",props:{person:{type:Object,required:!0},metrics:{type:Array,default:()=>[]},avatarBgColor:{type:String,default:"bg-blue-100 dark:bg-blue-900"},avatarTextColor:{type:String,default:"text-blue-600 dark:text-blue-400"},projectsTitle:{type:String,default:"Progetti Attivi"}},setup(n){const x=n,v=y(()=>x.person.name?x.person.name.split(" ").map(r=>r[0]).join("").toUpperCase():"??"),j=r=>r>100?"text-red-600 dark:text-red-400":r>=90?"text-yellow-600 dark:text-yellow-400":r>=70?"text-green-600 dark:text-green-400":"text-gray-600 dark:text-gray-400",h=(r,d)=>{if(!d)return r;switch(d){case"hours":return`${r}h`;case"percentage":return`${r}%`;case"currency":return new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(r);case"number":return new Intl.NumberFormat("it-IT").format(r);default:return r}};return(r,d)=>(s(),i("div",oe,[l("div",se,[l("div",ne,[l("div",{class:z(["w-10 h-10 rounded-full flex items-center justify-center",n.avatarBgColor||"bg-blue-100 dark:bg-blue-900"])},[l("span",{class:z(["text-sm font-medium",n.avatarTextColor||"text-blue-600 dark:text-blue-400"])},c(v.value),3)],2),l("div",ie,[l("h4",ce,c(n.person.name),1),l("p",ue,c(n.person.role||"Nessun ruolo"),1)])]),l("div",de,[d[0]||(d[0]=l("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Utilizzo",-1)),l("div",{class:z(["text-lg font-semibold",j(n.person.utilizationPercentage)])},c(Math.round(n.person.utilizationPercentage||0))+"% ",3)])]),l("div",me,[(s(!0),i(S,null,N(n.metrics,o=>(s(),i("div",{key:o.id},[l("div",ge,[l("span",ye,c(o.label),1),l("span",he,c(h(o.value,o.format)),1)]),l("div",ve,[l("div",{class:z(["h-2 rounded-full",o.color||"bg-blue-500"]),style:ee({width:Math.min(o.percentage,100)+"%"})},null,6)])]))),128))]),n.person.projects&&n.person.projects.length>0?(s(),i("div",_e,[l("h5",pe,c(n.projectsTitle||"Progetti Attivi"),1),l("div",be,[(s(!0),i(S,null,N(n.person.projects,o=>(s(),i("div",{key:o.id,class:"flex justify-between items-center text-sm"},[l("span",fe,c(o.name),1),l("div",xe,[l("span",ke,c(o.percentage)+"%",1),l("span",Ce," ("+c(h(o.hours,"hours"))+") ",1)])]))),128))])])):f("",!0),r.$slots.content?(s(),i("div",we,[D(r.$slots,"content",{person:n.person})])):f("",!0),r.$slots.actions?(s(),i("div",ze,[D(r.$slots,"actions",{person:n.person})])):f("",!0)]))}},je={class:"personnel-allocation space-y-6"},Pe={key:0,class:"flex justify-center items-center h-64"},$e={key:1,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6"},Te={class:"flex"},Fe={class:"text-red-800 dark:text-red-200"},Se={key:3,class:"space-y-6"},Ne={key:0,class:"bg-white dark:bg-gray-800 rounded-lg shadow"},Ee={class:"p-6"},Be={class:"space-y-4"},He={__name:"PersonnelAllocation",setup(n){const x=J(),{calculations:v,getVarianceClass:j}=re(),h=u(!1),r=u(null),d=u("current-month"),o=u(""),k=u(""),A=u(!1),P=u("summary"),g=u([]),R=u([]),E=u([]),B=u([]),b=y(()=>{if(!g.value.length)return{totalCapacity:0,totalAllocated:0,totalActual:0,utilizationPercentage:0,utilizationClass:"bg-gray-100 dark:bg-gray-700",utilizationIconClass:"text-gray-600 dark:text-gray-400"};const a=g.value.reduce((C,w)=>C+w.capacity_hours,0),e=g.value.reduce((C,w)=>C+w.allocated_hours,0),t=g.value.reduce((C,w)=>C+w.actual_hours,0),m=v.utilization(t,a,0);let _="bg-gray-100 dark:bg-gray-700",p="text-gray-600 dark:text-gray-400";return m>100?(_="bg-red-100 dark:bg-red-900",p="text-red-600 dark:text-red-400"):m>=90?(_="bg-yellow-100 dark:bg-yellow-900",p="text-yellow-600 dark:text-yellow-400"):m>=70&&(_="bg-green-100 dark:bg-green-900",p="text-green-600 dark:text-green-400"),{totalCapacity:a,totalAllocated:e,totalActual:t,utilizationPercentage:m,utilizationClass:_,utilizationIconClass:p}}),V=y(()=>[{id:"capacity",label:"Capacità Totale",value:b.value.totalCapacity,format:"hours",icon:"clock",iconBgColor:"bg-blue-100 dark:bg-blue-900",iconColor:"text-blue-600 dark:text-blue-400"},{id:"allocated",label:"Allocato",value:b.value.totalAllocated,format:"hours",icon:"check-circle",iconBgColor:"bg-green-100 dark:bg-green-900",iconColor:"text-green-600 dark:text-green-400"},{id:"actual",label:"Effettivo",value:b.value.totalActual,format:"hours",icon:"clock",iconBgColor:"bg-yellow-100 dark:bg-yellow-900",iconColor:"text-yellow-600 dark:text-yellow-400"},{id:"utilization",label:"Utilizzo",value:b.value.utilizationPercentage,format:"percentage",icon:"chart-bar",iconBgColor:b.value.utilizationClass,iconColor:b.value.utilizationIconClass}]),M=y(()=>[{id:"period",label:"Periodo",value:d.value,placeholder:"Seleziona periodo",options:[{value:"current-month",label:"Mese Corrente"},{value:"current-quarter",label:"Trimestre Corrente"},{value:"current-year",label:"Anno Corrente"},{value:"next-quarter",label:"Prossimo Trimestre"}]},{id:"department",label:"Dipartimento",value:o.value,placeholder:"Tutti i Dipartimenti",options:E.value.map(a=>({value:a.id,label:a.name,count:a.user_count}))},{id:"project",label:"Progetto",value:k.value,placeholder:"Tutti i Progetti",options:B.value.map(a=>({value:a.id,label:a.name,count:a.allocated_users}))}]),U=y(()=>[{id:"onlyAllocated",label:"Solo allocati",value:A.value}]),q=y(()=>[{id:"viewMode",value:P.value,defaultValue:"summary",options:[{value:"summary",label:"Riepilogo"},{value:"detailed",label:"Dettagliato"}]}]),O=y(()=>g.value.map(a=>{var e;return{id:a.user_id,name:a.user_name,role:a.role,utilizationPercentage:a.utilization_percentage,projects:((e=a.projects)==null?void 0:e.map(t=>({id:t.project_id,name:t.project_name,percentage:t.allocation_percentage,hours:t.allocated_hours})))||[],metrics:[{id:"capacity",label:"Capacità",value:a.capacity_hours,format:"hours",percentage:100,color:"bg-gray-400"},{id:"allocated",label:"Allocato",value:a.allocated_hours,format:"hours",percentage:v.percentage(a.allocated_hours,a.capacity_hours,1),color:"bg-blue-500"},{id:"actual",label:"Effettivo",value:a.actual_hours,format:"hours",percentage:v.percentage(a.actual_hours,a.capacity_hours,1),color:"bg-green-500"}]}})),H=y(()=>{const a=[];return!g.value||g.value.length===0||g.value.forEach(e=>{a.push({user_id:e.user_id||e.id,user_name:e.user_name||e.name,department:e.department||e.department_name||"N/A",project_name:e.project_name||"Nessun progetto specifico",project_period:e.project_period||"N/A",role:e.role||"N/A",allocation_percentage:e.allocation_percentage||e.utilization_percentage||0,planned_hours:e.allocated_hours||e.planned_hours||0,actual_hours:e.actual_hours||0,variance:v.variance(e.actual_hours,e.allocated_hours||e.planned_hours)}),e.projects&&e.projects.length>0&&e.projects.forEach(t=>{a.push({user_id:e.user_id||e.id,user_name:e.user_name||e.name,department:e.department||e.department_name||"N/A",project_name:t.project_name||t.name,project_period:t.period||"N/A",role:e.role||"N/A",allocation_percentage:t.allocation_percentage||t.percentage||0,planned_hours:t.allocated_hours||t.hours||0,actual_hours:t.actual_hours||0,variance:v.variance(t.actual_hours,t.allocated_hours||t.hours)})})}),a}),K=y(()=>[{key:"user_name",label:"Persona",type:"avatar",subKey:"department"},{key:"project_name",label:"Progetto",type:"text",subKey:"project_period"},{key:"role",label:"Ruolo",type:"text"},{key:"allocation_percentage",label:"Allocazione",type:"progress",format:"percentage",progressColor:"bg-blue-500"},{key:"planned_hours",label:"Ore Pianificate",type:"text",format:"hours"},{key:"actual_hours",label:"Ore Effettive",type:"text",format:"hours"},{key:"variance",label:"Varianza",type:"text"}]),L=async()=>{var a,e;try{const t=await fetch("/api/allocation-filters",{headers:{"Content-Type":"application/json","X-CSRFToken":x.csrfToken}});if(t.ok){const m=await t.json();E.value=((a=m.data)==null?void 0:a.departments)||[],B.value=((e=m.data)==null?void 0:e.projects)||[]}}catch(t){console.error("Error loading filters:",t)}},$=async()=>{var a,e;h.value=!0,r.value=null;try{const t=new URLSearchParams({period:d.value});o.value&&t.append("department_id",o.value),k.value&&t.append("project_id",k.value),A.value&&t.append("only_allocated","true");const m=`/api/allocation-analysis?${t.toString()}`;console.log("Calling API:",m);const _=await fetch(m,{headers:{"Content-Type":"application/json","X-CSRFToken":x.csrfToken}});if(!_.ok)throw new Error("Errore nel caricamento dei dati di allocazione");const p=await _.json();g.value=((a=p.data)==null?void 0:a.summary)||[],R.value=((e=p.data)==null?void 0:e.detailed)||[]}catch(t){r.value=t.message,console.error("Error loading allocation data:",t)}finally{h.value=!1}},X=(a,e)=>{switch(a){case"period":d.value=e;break;case"department":o.value=e;break;case"project":k.value=e;break;case"onlyAllocated":A.value=e;break;case"viewMode":P.value=e;return}$()},G=()=>{d.value="current-month",o.value="",k.value="",A.value=!1,$()};return te(async()=>{await L(),await $()}),(a,e)=>(s(),i("div",je,[T(W,{title:"Allocazione Risorse",subtitle:"Analisi temporale e confronto pianificato vs effettivo",icon:"user-group","icon-color":"text-blue-600"},{actions:I(()=>[T(Z,{"select-filters":M.value,"checkbox-filters":U.value,"toggle-filters":q.value,onFilterChange:X,onClearFilters:G},null,8,["select-filters","checkbox-filters","toggle-filters"])]),_:1}),h.value?(s(),i("div",Pe,e[0]||(e[0]=[l("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):r.value?(s(),i("div",$e,[l("div",Te,[T(Q,{name:"x-circle",size:"md",class:"text-red-400 mr-2"}),l("p",Fe,c(r.value),1)])])):f("",!0),!h.value&&!r.value?(s(),F(Y,{key:2,stats:V.value},null,8,["stats"])):f("",!0),!h.value&&!r.value?(s(),i("div",Se,[P.value==="summary"?(s(),i("div",Ne,[e[1]||(e[1]=l("div",{class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},[l("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Riepilogo per Persona")],-1)),l("div",Ee,[l("div",Be,[(s(!0),i(S,null,N(O.value,t=>(s(),F(Ae,{key:t.id,person:t,metrics:t.metrics},null,8,["person","metrics"]))),128))])])])):(s(),F(le,{key:1,title:"Vista Dettagliata",columns:K.value,data:H.value,"row-key":t=>`${t.user_id}-${t.project_name}`,"empty-message":"Nessuna allocazione trovata"},{"cell-variance":I(({value:t})=>[l("span",{class:z(["text-sm font-medium",ae(j)(t)])},c(t>0?"+":"")+c(t)+"h ",3)]),_:1},8,["columns","data","row-key"]))])):f("",!0)]))}};export{He as default};
