import{b as d,g as h,j as a,l as e,t as g,A as q,B as x,C as k,S as A,O as be,P as U,F as T,q as P,v as S,r as D,c as w,o as xe,e as p,f as B,p as Y,x as N,s as ye,n as R}from"./vendor.js";import{_ as Q,d as W,b as he,H as b}from"./app.js";import{u as we}from"./useFormatters.js";import{_ as ke}from"./ListPageTemplate.js";import{S as De}from"./StatusBadge.js";import{E as _e}from"./EventEditModal.js";import{C as Ce}from"./ConfirmationModal.js";import"./formatters.js";import"./Pagination.js";const Se={name:"CreateEventModal",props:{isOpen:{type:Boolean,default:!1},event:{type:Object,default:null}},emits:["close","created","updated"],data(){return{isSubmitting:!1,tagsInput:"",form:{title:"",description:"",category:"",priority:"medium",startDate:"",endDate:"",location:"",maxParticipants:null,requiresRegistration:!0,registrationDeadline:"",isPublic:!0,sendReminders:!0,allowComments:!0,tags:[]}}},computed:{isEditing(){return!!this.event},isFormValid(){const c=this.form.title.trim()&&this.form.description.trim()&&this.form.category&&this.form.startDate&&this.form.location.trim(),t=new Date(this.form.startDate)>new Date&&(!this.form.endDate||new Date(this.form.endDate)>new Date(this.form.startDate)),f=!this.form.requiresRegistration||!this.form.registrationDeadline||new Date(this.form.registrationDeadline)<new Date(this.form.startDate);return c&&t&&f},minDateTime(){const c=new Date;return c.setHours(c.getHours()+1),c.toISOString().slice(0,16)},parsedTags(){return this.tagsInput.trim()?this.tagsInput.split(",").map(c=>c.trim()).filter(c=>c.length>0).slice(0,5):[]}},watch:{isOpen(c){c&&this.initializeForm()},event:{handler(){this.isOpen&&this.initializeForm()},immediate:!0},parsedTags(c){this.form.tags=c}},methods:{initializeForm(){this.isEditing&&this.event?(this.form={title:this.event.title||"",description:this.event.description||"",category:this.event.category||"",priority:this.event.priority||"medium",startDate:this.event.startDate?new Date(this.event.startDate).toISOString().slice(0,16):"",endDate:this.event.endDate?new Date(this.event.endDate).toISOString().slice(0,16):"",location:this.event.location||"",maxParticipants:this.event.maxParticipants||null,requiresRegistration:this.event.requiresRegistration!==void 0?this.event.requiresRegistration:!0,registrationDeadline:this.event.registrationDeadline?new Date(this.event.registrationDeadline).toISOString().slice(0,16):"",isPublic:this.event.isPublic!==void 0?this.event.isPublic:!0,sendReminders:this.event.sendReminders!==void 0?this.event.sendReminders:!0,allowComments:this.event.allowComments!==void 0?this.event.allowComments:!0,tags:this.event.tags||[]},this.tagsInput=this.event.tags?this.event.tags.join(", "):""):(this.form={title:"",description:"",category:"",priority:"medium",startDate:"",endDate:"",location:"",maxParticipants:null,requiresRegistration:!0,registrationDeadline:"",isPublic:!0,sendReminders:!0,allowComments:!0,tags:[]},this.tagsInput="")},async submitEvent(){if(!(!this.isFormValid||this.isSubmitting)){this.isSubmitting=!0;try{const c={...this.form,tags:this.parsedTags,startDate:new Date(this.form.startDate).toISOString(),endDate:this.form.endDate?new Date(this.form.endDate).toISOString():null,registrationDeadline:this.form.registrationDeadline?new Date(this.form.registrationDeadline).toISOString():null},t=W();let f;this.isEditing?(f=await t.updateEvent({id:this.event.id,data:c}),this.$emit("updated",f)):(f=await t.createEvent(c),this.$emit("created",f)),this.closeModal()}catch(c){console.error("Errore durante il salvataggio dell'evento:",c)}finally{this.isSubmitting=!1}}},closeModal(){this.$emit("close"),setTimeout(()=>{this.initializeForm(),this.isSubmitting=!1},300)}}},ze={key:0,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},Ee={class:"bg-white rounded-lg shadow-xl w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto"},Me={class:"flex items-center justify-between p-6 border-b"},Te={class:"text-lg font-semibold text-gray-900"},Ie={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Re={class:"md:col-span-2"},Pe={class:"text-right text-xs text-gray-500 mt-1"},Ve={class:"text-right text-xs text-gray-500 mt-1"},Oe={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Fe=["min"],Ue=["min"],Ne={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},qe={class:"space-y-3"},Ae={class:"flex items-center"},je={class:"flex items-center"},Le={class:"flex items-center"},Be={class:"flex items-center"},Ye={key:0},He=["min","max"],Ge={key:0,class:"flex flex-wrap gap-2 mt-2"},$e={class:"flex justify-end space-x-3 pt-4 border-t"},Qe=["disabled"],We={key:0,class:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"};function Je(c,t,f,j,n,v){return f.isOpen?(a(),d("div",ze,[e("div",Ee,[e("div",Me,[e("h3",Te,g(v.isEditing?"Modifica Evento":"Nuovo Evento"),1),e("button",{onClick:t[0]||(t[0]=(...r)=>v.closeModal&&v.closeModal(...r)),class:"text-gray-400 hover:text-gray-600 transition-colors"},t[17]||(t[17]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e("form",{onSubmit:t[16]||(t[16]=q((...r)=>v.submitEvent&&v.submitEvent(...r),["prevent"])),class:"p-6 space-y-6"},[e("div",Ie,[e("div",Re,[t[18]||(t[18]=e("label",{for:"title",class:"block text-sm font-medium text-gray-700 mb-2"}," Titolo * ",-1)),x(e("input",{id:"title","onUpdate:modelValue":t[1]||(t[1]=r=>n.form.title=r),type:"text",required:"",maxlength:"200",class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Inserisci il titolo dell'evento"},null,512),[[k,n.form.title]]),e("div",Pe,g(n.form.title.length)+"/200 ",1)]),e("div",null,[t[20]||(t[20]=e("label",{for:"category",class:"block text-sm font-medium text-gray-700 mb-2"}," Categoria * ",-1)),x(e("select",{id:"category","onUpdate:modelValue":t[2]||(t[2]=r=>n.form.category=r),required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},t[19]||(t[19]=[be('<option value="" data-v-63cb66d9>Seleziona una categoria</option><option value="meeting" data-v-63cb66d9>Meeting</option><option value="training" data-v-63cb66d9>Formazione</option><option value="social" data-v-63cb66d9>Evento sociale</option><option value="conference" data-v-63cb66d9>Conferenza</option><option value="workshop" data-v-63cb66d9>Workshop</option><option value="team-building" data-v-63cb66d9>Team Building</option><option value="presentation" data-v-63cb66d9>Presentazione</option><option value="celebration" data-v-63cb66d9>Celebrazione</option><option value="other" data-v-63cb66d9>Altro</option>',10)]),512),[[A,n.form.category]])]),e("div",null,[t[22]||(t[22]=e("label",{for:"priority",class:"block text-sm font-medium text-gray-700 mb-2"}," Priorità ",-1)),x(e("select",{id:"priority","onUpdate:modelValue":t[3]||(t[3]=r=>n.form.priority=r),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},t[21]||(t[21]=[e("option",{value:"low"},"Bassa",-1),e("option",{value:"medium"},"Media",-1),e("option",{value:"high"},"Alta",-1),e("option",{value:"urgent"},"Urgente",-1)]),512),[[A,n.form.priority]])])]),e("div",null,[t[23]||(t[23]=e("label",{for:"description",class:"block text-sm font-medium text-gray-700 mb-2"}," Descrizione * ",-1)),x(e("textarea",{id:"description","onUpdate:modelValue":t[4]||(t[4]=r=>n.form.description=r),required:"",rows:"4",maxlength:"2000",class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-vertical",placeholder:"Descrivi l'evento, l'agenda e le informazioni importanti"},null,512),[[k,n.form.description]]),e("div",Ve,g(n.form.description.length)+"/2000 ",1)]),e("div",Oe,[e("div",null,[t[24]||(t[24]=e("label",{for:"startDate",class:"block text-sm font-medium text-gray-700 mb-2"}," Data e ora inizio * ",-1)),x(e("input",{id:"startDate","onUpdate:modelValue":t[5]||(t[5]=r=>n.form.startDate=r),type:"datetime-local",required:"",min:v.minDateTime,class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},null,8,Fe),[[k,n.form.startDate]])]),e("div",null,[t[25]||(t[25]=e("label",{for:"endDate",class:"block text-sm font-medium text-gray-700 mb-2"}," Data e ora fine ",-1)),x(e("input",{id:"endDate","onUpdate:modelValue":t[6]||(t[6]=r=>n.form.endDate=r),type:"datetime-local",min:n.form.startDate,class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},null,8,Ue),[[k,n.form.endDate]])])]),e("div",Ne,[e("div",null,[t[26]||(t[26]=e("label",{for:"location",class:"block text-sm font-medium text-gray-700 mb-2"}," Luogo * ",-1)),x(e("input",{id:"location","onUpdate:modelValue":t[7]||(t[7]=r=>n.form.location=r),type:"text",required:"",maxlength:"200",class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Sala riunioni, indirizzo, link online, etc."},null,512),[[k,n.form.location]])]),e("div",null,[t[27]||(t[27]=e("label",{for:"maxParticipants",class:"block text-sm font-medium text-gray-700 mb-2"}," Numero massimo partecipanti ",-1)),x(e("input",{id:"maxParticipants","onUpdate:modelValue":t[8]||(t[8]=r=>n.form.maxParticipants=r),type:"number",min:"1",max:"1000",class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Lascia vuoto per illimitato"},null,512),[[k,n.form.maxParticipants,void 0,{number:!0}]])])]),e("div",qe,[t[32]||(t[32]=e("h4",{class:"text-sm font-medium text-gray-700"},"Opzioni evento",-1)),e("div",Ae,[x(e("input",{id:"requiresRegistration","onUpdate:modelValue":t[9]||(t[9]=r=>n.form.requiresRegistration=r),type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,512),[[U,n.form.requiresRegistration]]),t[28]||(t[28]=e("label",{for:"requiresRegistration",class:"ml-2 block text-sm text-gray-700"}," Richiede registrazione ",-1))]),e("div",je,[x(e("input",{id:"isPublic","onUpdate:modelValue":t[10]||(t[10]=r=>n.form.isPublic=r),type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,512),[[U,n.form.isPublic]]),t[29]||(t[29]=e("label",{for:"isPublic",class:"ml-2 block text-sm text-gray-700"}," Evento pubblico (visibile a tutti) ",-1))]),e("div",Le,[x(e("input",{id:"sendReminders","onUpdate:modelValue":t[11]||(t[11]=r=>n.form.sendReminders=r),type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,512),[[U,n.form.sendReminders]]),t[30]||(t[30]=e("label",{for:"sendReminders",class:"ml-2 block text-sm text-gray-700"}," Invia promemoria automatici ",-1))]),e("div",Be,[x(e("input",{id:"allowComments","onUpdate:modelValue":t[12]||(t[12]=r=>n.form.allowComments=r),type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,512),[[U,n.form.allowComments]]),t[31]||(t[31]=e("label",{for:"allowComments",class:"ml-2 block text-sm text-gray-700"}," Permetti commenti ",-1))])]),n.form.requiresRegistration?(a(),d("div",Ye,[t[33]||(t[33]=e("label",{for:"registrationDeadline",class:"block text-sm font-medium text-gray-700 mb-2"}," Scadenza registrazione ",-1)),x(e("input",{id:"registrationDeadline","onUpdate:modelValue":t[13]||(t[13]=r=>n.form.registrationDeadline=r),type:"datetime-local",min:v.minDateTime,max:n.form.startDate,class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},null,8,He),[[k,n.form.registrationDeadline]]),t[34]||(t[34]=e("div",{class:"text-xs text-gray-500 mt-1"}," Lascia vuoto per permettere registrazioni fino all'inizio dell'evento ",-1))])):h("",!0),e("div",null,[t[35]||(t[35]=e("label",{for:"tags",class:"block text-sm font-medium text-gray-700 mb-2"}," Tags (opzionale) ",-1)),x(e("input",{id:"tags","onUpdate:modelValue":t[14]||(t[14]=r=>n.tagsInput=r),type:"text",class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Inserisci i tag separati da virgola (es: importante, team, formazione)"},null,512),[[k,n.tagsInput]]),t[36]||(t[36]=e("div",{class:"text-xs text-gray-500 mt-1"}," Separare i tag con virgole. Massimo 5 tag. ",-1)),v.parsedTags.length>0?(a(),d("div",Ge,[(a(!0),d(T,null,P(v.parsedTags,r=>(a(),d("span",{key:r,class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"},g(r),1))),128))])):h("",!0)]),e("div",$e,[e("button",{type:"button",onClick:t[15]||(t[15]=(...r)=>v.closeModal&&v.closeModal(...r)),class:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," Annulla "),e("button",{type:"submit",disabled:n.isSubmitting||!v.isFormValid,class:"px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"},[n.isSubmitting?(a(),d("svg",We,t[37]||(t[37]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):h("",!0),S(" "+g(n.isSubmitting?"Salvataggio...":v.isEditing?"Aggiorna":"Crea Evento"),1)],8,Qe)])],32)])])):h("",!0)}const Ke=Q(Se,[["render",Je],["__scopeId","data-v-63cb66d9"]]),Xe={class:"grid grid-cols-1 gap-5 sm:grid-cols-4"},Ze={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},et={class:"p-5"},tt={class:"flex items-center"},st={class:"flex-shrink-0"},ot={class:"ml-5 w-0 flex-1"},it={class:"text-lg font-medium text-gray-900 dark:text-white"},rt={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},nt={class:"p-5"},at={class:"flex items-center"},lt={class:"flex-shrink-0"},dt={class:"ml-5 w-0 flex-1"},ut={class:"text-lg font-medium text-gray-900 dark:text-white"},mt={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},gt={class:"p-5"},ct={class:"flex items-center"},ft={class:"flex-shrink-0"},pt={class:"ml-5 w-0 flex-1"},vt={class:"text-lg font-medium text-gray-900 dark:text-white"},bt={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},xt={class:"p-5"},yt={class:"flex items-center"},ht={class:"flex-shrink-0"},wt={class:"ml-5 w-0 flex-1"},kt={class:"text-lg font-medium text-gray-900 dark:text-white"},Dt={class:"flex items-center justify-between"},_t={class:"flex space-x-4"},Ct={class:"flex rounded-md shadow-sm"},St={key:0,class:"flex justify-center items-center h-64"},zt={key:1,class:"text-center py-12"},Et={key:2,class:"p-6"},Mt={class:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6"},Tt=["onClick"],It={class:"p-6"},Rt={class:"flex items-start justify-between mb-3"},Pt={class:"text-lg font-semibold text-gray-900 dark:text-white line-clamp-2"},Vt={class:"text-gray-600 dark:text-gray-400 text-sm line-clamp-2 mb-4"},Ot={class:"space-y-2 mb-4"},Ft={class:"flex items-center text-sm text-gray-500 dark:text-gray-400"},Ut={key:0,class:"flex items-center text-sm text-gray-500 dark:text-gray-400"},Nt={class:"flex items-center text-sm text-gray-500 dark:text-gray-400"},qt={key:0},At={key:0,class:"mb-4"},jt={key:0,class:"inline-flex items-center px-3 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full"},Lt=["onClick"],Bt={key:2,class:"inline-flex items-center px-3 py-1 bg-gray-100 text-gray-800 text-xs font-medium rounded-full"},Yt={class:"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-4"},Ht={class:"flex items-center space-x-2"},Gt={class:"flex justify-end space-x-2"},$t=["onClick"],Qt=["onClick"],Wt={key:3,class:"p-6"},Jt={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Kt={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},Xt={class:"flex items-center justify-between"},Zt={class:"text-lg font-medium text-gray-900 dark:text-white"},es={class:"flex items-center space-x-1"},ts={class:"p-6"},ss={class:"grid grid-cols-7 gap-1 mb-2"},os={class:"grid grid-cols-7 gap-1"},is={class:"space-y-1"},rs=["onClick","title"],ns={__name:"EventsIndex",setup(c){const t=ye(),f=W(),j=he(),{formatDate:n}=we(),v=D(!1),r=D(!1),V=D(!1),_=D(null),O=D(""),F=D(""),z=D("list"),C=D(new Date),J=w(()=>j.hasPermission("PERMISSION_MANAGE_COMMUNICATION")),K=w(()=>j.hasPermission("PERMISSION_MANAGE_COMMUNICATION")),X=w(()=>f.events.filter(o=>E(o)==="upcoming").length),Z=w(()=>f.events.filter(o=>E(o)==="completed").length),ee=w(()=>f.events.reduce((o,s)=>o+(s.registered_count||0),0)),L=w(()=>{let o=f.events;if(O.value){const s=new Date,l=new Date(s.getFullYear(),s.getMonth(),s.getDate()),u=new Date(l);u.setDate(l.getDate()-l.getDay());const y=new Date(l.getFullYear(),l.getMonth(),1);o=o.filter(i=>{const m=new Date(i.start_time||i.event_time),I=new Date(m.getFullYear(),m.getMonth(),m.getDate());switch(O.value){case"upcoming":return m>s;case"today":return I.getTime()===l.getTime();case"this_week":return m>=u&&m<new Date(u.getTime()+7*24*60*60*1e3);case"this_month":return m>=y&&m.getMonth()===s.getMonth();case"past":return m<s;default:return!0}})}return F.value&&(o=o.filter(s=>s.event_type===F.value)),o}),te=w(()=>C.value.toLocaleDateString("it-IT",{month:"long"})),se=w(()=>C.value.getFullYear()),oe=w(()=>["Dom","Lun","Mar","Mer","Gio","Ven","Sab"]),ie=w(()=>{const o=C.value.getFullYear(),s=C.value.getMonth(),l=new Date(o,s,1),u=new Date(o,s+1,0),y=new Date(l);y.setDate(y.getDate()-l.getDay());const i=new Date(u),m=6-u.getDay();i.setDate(i.getDate()+m);const I=[],M=new Date(y),ve=new Date;for(;M<=i;)I.push({date:new Date(M),isCurrentMonth:M.getMonth()===s,isToday:M.toDateString()===ve.toDateString()}),M.setDate(M.getDate()+1);return I}),E=o=>{const s=new Date,l=new Date(o.start_time||o.event_time);return l<s?"completed":l>s?"upcoming":"ongoing"},re=o=>{const s=new Date(o.start_time||o.event_time),l=o.end_time?new Date(o.end_time):null,u=s.toLocaleDateString("it-IT",{weekday:"short",year:"numeric",month:"short",day:"numeric"}),y=s.toLocaleTimeString("it-IT",{hour:"2-digit",minute:"2-digit"});if(l&&l.toDateString()===s.toDateString()){const i=l.toLocaleTimeString("it-IT",{hour:"2-digit",minute:"2-digit"});return`${u} ${y} - ${i}`}return`${u} ${y}`},H=o=>f.getUserEventRegistration(o.id)!==null,ne=o=>H(o)?!1:o.max_participants?(o.registered_count||0)<o.max_participants:!0,ae=async o=>{var s,l,u;try{await f.registerForEvent(o.id),alert("Registrazione completata con successo!")}catch(y){console.error("Errore nella registrazione all'evento:",y);let i="Errore nella registrazione all'evento";((s=y.response)==null?void 0:s.status)===400?i=((l=y.response.data)==null?void 0:l.message)||"Registrazione non valida":((u=y.response)==null?void 0:u.status)===500?i="Errore del server. Riprova più tardi.":navigator.onLine||(i="Connessione internet non disponibile"),alert(i)}},G=o=>{t.push(`/app/communications/events/${o.id}`)},le=o=>{_.value=o,r.value=!0},de=o=>{_.value=o,V.value=!0},ue=o=>{v.value=!1},me=o=>{r.value=!1,_.value=null},ge=async()=>{try{await f.deleteEvent(_.value.id),V.value=!1,_.value=null}catch(o){console.error("Errore nell'eliminazione dell'evento:",o)}},$=o=>{const s=new Date(C.value);s.setMonth(s.getMonth()+o),C.value=s},ce=()=>{C.value=new Date},fe=o=>L.value.filter(s=>{const l=new Date(s.start_time||s.event_time);return l.getDate()===o.getDate()&&l.getMonth()===o.getMonth()&&l.getFullYear()===o.getFullYear()}),pe=o=>new Date(o.start_time||o.event_time).toLocaleTimeString("it-IT",{hour:"2-digit",minute:"2-digit"});return xe(async()=>{try{await f.fetchEvents()}catch(o){console.error("Errore nel caricamento degli eventi:",o)}}),(o,s)=>{var l;return a(),d(T,null,[p(ke,{title:"Eventi Aziendali",subtitle:"Gestione e partecipazione agli eventi aziendali",data:N(f).events,loading:N(f).loading.events,"can-create":J.value,"create-label":"Nuovo Evento","search-placeholder":"Cerca eventi...","empty-message":"Nessun evento programmato","results-label":"eventi",onCreate:s[6]||(s[6]=u=>v.value=!0)},{stats:Y(()=>[e("div",Xe,[e("div",Ze,[e("div",et,[e("div",tt,[e("div",st,[p(b,{name:"calendar-days",size:"lg",class:"text-blue-500"})]),e("div",ot,[e("dl",null,[s[10]||(s[10]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"},"Totali",-1)),e("dd",it,g(N(f).events.length),1)])])])])]),e("div",rt,[e("div",nt,[e("div",at,[e("div",lt,[p(b,{name:"clock",size:"lg",class:"text-green-500"})]),e("div",dt,[e("dl",null,[s[11]||(s[11]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"},"Prossimi",-1)),e("dd",ut,g(X.value),1)])])])])]),e("div",mt,[e("div",gt,[e("div",ct,[e("div",ft,[p(b,{name:"check-circle",size:"lg",class:"text-purple-500"})]),e("div",pt,[e("dl",null,[s[12]||(s[12]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"},"Completati",-1)),e("dd",vt,g(Z.value),1)])])])])]),e("div",bt,[e("div",xt,[e("div",yt,[e("div",ht,[p(b,{name:"users",size:"lg",class:"text-yellow-500"})]),e("div",wt,[e("dl",null,[s[13]||(s[13]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"},"Partecipanti",-1)),e("dd",kt,g(ee.value),1)])])])])])])]),filters:Y(()=>[e("div",Dt,[e("div",_t,[x(e("select",{"onUpdate:modelValue":s[0]||(s[0]=u=>O.value=u),class:"block w-40 pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},s[14]||(s[14]=[e("option",{value:""},"Tutti i periodi",-1),e("option",{value:"upcoming"},"Prossimi",-1),e("option",{value:"today"},"Oggi",-1),e("option",{value:"this_week"},"Questa settimana",-1),e("option",{value:"this_month"},"Questo mese",-1),e("option",{value:"past"},"Passati",-1)]),512),[[A,O.value]]),x(e("select",{"onUpdate:modelValue":s[1]||(s[1]=u=>F.value=u),class:"block w-32 pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},s[15]||(s[15]=[e("option",{value:""},"Tutti i tipi",-1),e("option",{value:"meeting"},"Riunioni",-1),e("option",{value:"training"},"Formazione",-1),e("option",{value:"social"},"Sociale",-1),e("option",{value:"announcement"},"Annunci",-1)]),512),[[A,F.value]])]),e("div",Ct,[e("button",{onClick:s[2]||(s[2]=u=>z.value="list"),class:R([z.value==="list"?"bg-blue-600 text-white":"bg-white text-gray-700 hover:bg-gray-50 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600","px-3 py-2 text-sm font-medium border border-gray-300 dark:border-gray-600 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500"])},[p(b,{name:"list-bullet",size:"sm",class:"mr-1"}),s[16]||(s[16]=S(" Lista "))],2),e("button",{onClick:s[3]||(s[3]=u=>z.value="calendar"),class:R([z.value==="calendar"?"bg-blue-600 text-white":"bg-white text-gray-700 hover:bg-gray-50 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600","px-3 py-2 text-sm font-medium border border-l-0 border-gray-300 dark:border-gray-600 rounded-r-md focus:outline-none focus:ring-2 focus:ring-blue-500"])},[p(b,{name:"calendar",size:"sm",class:"mr-1"}),s[17]||(s[17]=S(" Calendario "))],2)])])]),content:Y(({data:u,loading:y})=>[y?(a(),d("div",St,s[18]||(s[18]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):L.value.length===0?(a(),d("div",zt,[p(b,{name:"calendar-days",size:"2xl",class:"mx-auto text-gray-400 mb-4"}),s[19]||(s[19]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Nessun evento trovato",-1)),s[20]||(s[20]=e("p",{class:"text-gray-500 dark:text-gray-400"},"Non ci sono eventi da visualizzare con i filtri selezionati.",-1))])):z.value==="list"?(a(),d("div",Et,[e("div",Mt,[(a(!0),d(T,null,P(L.value,i=>(a(),d("div",{key:i.id,class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden hover:shadow-lg transition-shadow cursor-pointer",onClick:m=>G(i)},[e("div",It,[e("div",Rt,[e("h3",Pt,g(i.title),1),p(De,{status:E(i),size:"sm"},null,8,["status"])]),e("p",Vt,g(i.description),1),e("div",Ot,[e("div",Ft,[p(b,{name:"calendar",size:"xs",class:"mr-2"}),e("span",null,g(re(i)),1)]),i.location?(a(),d("div",Ut,[p(b,{name:"map-pin",size:"xs",class:"mr-2"}),e("span",null,g(i.location),1)])):h("",!0),e("div",Nt,[p(b,{name:"users",size:"xs",class:"mr-2"}),e("span",null,g(i.registered_count||0)+" partecipanti",1),i.max_participants?(a(),d("span",qt," / "+g(i.max_participants),1)):h("",!0)])]),E(i)==="upcoming"?(a(),d("div",At,[H(i)?(a(),d("div",jt,[p(b,{name:"check",size:"xs",class:"mr-1"}),s[21]||(s[21]=S(" Iscritto "))])):ne(i)?(a(),d("button",{key:1,onClick:q(m=>ae(i),["stop"]),class:"inline-flex items-center px-3 py-1 border border-green-300 shadow-sm text-xs font-medium rounded text-green-700 bg-white hover:bg-green-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"},[p(b,{name:"plus",size:"xs",class:"mr-1"}),s[22]||(s[22]=S(" Iscriviti "))],8,Lt)):(a(),d("span",Bt," Completo "))])):h("",!0),e("div",Yt,[e("div",Ht,[p(b,{name:"user",size:"xs"}),e("span",null,g(i.organizer_name||"Organizzatore"),1)]),e("span",null,g(N(n)(i.created_at)),1)]),e("div",Gt,[e("button",{onClick:q(m=>le(i),["stop"]),class:"inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},[p(b,{name:"pencil",size:"xs",class:"mr-1"}),s[23]||(s[23]=S(" Modifica "))],8,$t),K.value?(a(),d("button",{key:0,onClick:q(m=>de(i),["stop"]),class:"inline-flex items-center px-3 py-1 border border-red-300 shadow-sm text-xs font-medium rounded text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"},[p(b,{name:"trash",size:"xs",class:"mr-1"}),s[24]||(s[24]=S(" Elimina "))],8,Qt)):h("",!0)])])],8,Tt))),128))])])):z.value==="calendar"?(a(),d("div",Wt,[e("div",Jt,[e("div",Kt,[e("div",Xt,[e("h3",Zt,g(te.value)+" "+g(se.value),1),e("div",es,[e("button",{onClick:s[4]||(s[4]=i=>$(-1)),class:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},[p(b,{name:"chevron-left",size:"sm"})]),e("button",{onClick:ce,class:"px-3 py-1 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded hover:bg-gray-50 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-600"}," Oggi "),e("button",{onClick:s[5]||(s[5]=i=>$(1)),class:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},[p(b,{name:"chevron-right",size:"sm"})])])])]),e("div",ts,[e("div",ss,[(a(!0),d(T,null,P(oe.value,i=>(a(),d("div",{key:i,class:"p-2 text-sm font-medium text-gray-500 dark:text-gray-400 text-center"},g(i),1))),128))]),e("div",os,[(a(!0),d(T,null,P(ie.value,i=>(a(),d("div",{key:`${i.date.getTime()}-${i.isCurrentMonth}`,class:R(["min-h-[100px] p-1 border border-gray-200 dark:border-gray-700",i.isCurrentMonth?"bg-white dark:bg-gray-800":"bg-gray-50 dark:bg-gray-900",i.isToday?"ring-2 ring-blue-500":""])},[e("div",{class:R(["text-sm font-medium p-1",i.isCurrentMonth?"text-gray-900 dark:text-white":"text-gray-400 dark:text-gray-600",i.isToday?"text-blue-600 dark:text-blue-400 font-bold":""])},g(i.date.getDate()),3),e("div",is,[(a(!0),d(T,null,P(fe(i.date),m=>(a(),d("div",{key:m.id,onClick:I=>G(m),class:R(["text-xs p-1 rounded cursor-pointer truncate",E(m)==="upcoming"?"bg-blue-100 text-blue-800 hover:bg-blue-200":E(m)==="ongoing"?"bg-green-100 text-green-800 hover:bg-green-200":"bg-gray-100 text-gray-800 hover:bg-gray-200"]),title:m.title},g(pe(m))+" "+g(m.title),11,rs))),128))])],2))),128))])])])])):h("",!0)]),_:1},8,["data","loading","can-create"]),v.value?(a(),B(Ke,{key:0,onClose:s[7]||(s[7]=u=>v.value=!1),onCreated:ue})):h("",!0),r.value?(a(),B(_e,{key:1,event:_.value,onClose:s[8]||(s[8]=u=>r.value=!1),onUpdated:me},null,8,["event"])):h("",!0),V.value?(a(),B(Ce,{key:2,title:"Elimina Evento",message:`Sei sicuro di voler eliminare l'evento '${(l=_.value)==null?void 0:l.title}'?`,"confirm-text":"Elimina","confirm-class":"bg-red-600 hover:bg-red-700",onConfirm:ge,onCancel:s[9]||(s[9]=u=>V.value=!1)},null,8,["message"])):h("",!0)],64)}}},vs=Q(ns,[["__scopeId","data-v-73a620b7"]]);export{vs as default};
