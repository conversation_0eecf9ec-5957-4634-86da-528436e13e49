import{r as k,c as w,o as ee,b as l,l as e,g,f as M,v as T,e as b,t as o,B as m,S as D,F as y,q as v,O as te,C as f,A as re,j as a,n as I}from"./vendor.js";import{H as c,c as A}from"./app.js";const se={class:"min-h-screen bg-gray-50 dark:bg-gray-900"},ae={class:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700"},le={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},oe={class:"flex justify-between h-16"},de={class:"flex items-center space-x-4"},ne=["disabled"],ie={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6"},ue={class:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8"},ge={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},ce={class:"flex items-center"},xe={class:"flex-shrink-0"},pe={class:"ml-4"},me={class:"text-2xl font-bold text-gray-900 dark:text-white"},be={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},ye={class:"flex items-center"},ve={class:"flex-shrink-0"},ke={class:"ml-4"},_e={class:"text-2xl font-bold text-gray-900 dark:text-white"},fe={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},he={class:"flex items-center"},we={class:"flex-shrink-0"},Ce={class:"ml-4"},Se={class:"text-2xl font-bold text-gray-900 dark:text-white"},Te={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},De={class:"flex items-center"},ze={class:"flex-shrink-0"},Ve={class:"ml-4"},Oe={class:"text-2xl font-bold text-gray-900 dark:text-white"},Me={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6"},Ie={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},Ae=["value"],je=["value"],qe={key:0,class:"flex justify-center py-12"},Be={key:1,class:"text-center py-12"},Ge={key:2,class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},Re={class:"flex items-start justify-between mb-4"},Ue={class:"flex-1"},Ne={class:"flex items-center space-x-2 mb-2"},Fe={class:"text-lg font-semibold text-gray-900 dark:text-white"},Le={class:"text-sm text-gray-600 dark:text-gray-400 mb-3"},Pe={class:"flex items-center space-x-2"},Ee={class:"text-right"},He={class:"mb-4"},$e={class:"flex justify-between items-center mb-2"},Je={class:"text-lg font-bold text-green-600 dark:text-green-400"},Ke={class:"flex justify-between items-center text-sm text-gray-500 dark:text-gray-400"},Qe={key:0,class:"mb-4"},We={class:"flex flex-wrap gap-1"},Xe={key:0,class:"inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400"},Ye={key:1,class:"mb-4"},Ze={class:"text-sm text-gray-600 dark:text-gray-400 space-y-1"},et={key:0,class:"text-xs text-gray-500 dark:text-gray-400"},tt={key:2,class:"mb-4"},rt={class:"grid grid-cols-2 gap-2"},st={class:"font-medium text-green-800 dark:text-green-300"},at={class:"flex justify-between items-center pt-4 border-t border-gray-200 dark:border-gray-700"},lt={class:"text-xs text-gray-500 dark:text-gray-400"},ot={class:"flex space-x-2"},dt=["onClick"],nt=["onClick"],it=["onClick"],ut={key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},gt={class:"relative top-10 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-2/3 shadow-lg rounded-md bg-white dark:bg-gray-800 max-h-screen overflow-y-auto"},ct={class:"mt-3"},xt={class:"flex items-center justify-between mb-4"},pt={class:"bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4"},mt={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},bt=["value"],yt={class:"bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4"},vt={class:"space-y-4"},kt={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},_t={key:0,class:"bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 rounded-lg p-6 border border-purple-200 dark:border-purple-800"},ft={class:"flex items-center mb-4"},ht={class:"space-y-4"},wt={class:"text-purple-800 dark:text-purple-200 font-medium"},Ct={class:"text-purple-700 dark:text-purple-300 text-sm"},St={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},Tt={class:"text-center p-3 bg-white dark:bg-gray-800 rounded border"},Dt={class:"text-lg font-bold text-purple-600 dark:text-purple-400"},zt={class:"text-center p-3 bg-white dark:bg-gray-800 rounded border"},Vt={class:"text-lg font-bold text-purple-600 dark:text-purple-400"},Ot={class:"text-center p-3 bg-white dark:bg-gray-800 rounded border"},Mt={class:"text-lg font-bold text-purple-600 dark:text-purple-400"},It={key:0},At={class:"flex flex-wrap gap-1"},jt={key:1},qt={class:"text-sm text-purple-700 dark:text-purple-300 space-y-1"},Bt={class:"flex justify-end space-x-3 pt-4"},Gt=["disabled"],Ft={__name:"TechnicalOffer",setup(Rt){const h=k(!1),C=k(!1),x=k([]),z=k([]),S=k(!1),i=k(null),n=k({competency:"",sector:"",status:"",search:""}),u=k({competency_id:"",target_sector:"",custom_requirements:"",budget_min:null,budget_max:null,preferred_duration:null}),j=w(()=>{let s=x.value;if(n.value.competency&&(s=s.filter(t=>{var d;return((d=t.core_competency)==null?void 0:d.id)==n.value.competency})),n.value.sector&&(s=s.filter(t=>t.target_sector===n.value.sector)),n.value.status&&(s=s.filter(t=>t.status===n.value.status)),n.value.search){const t=n.value.search.toLowerCase();s=s.filter(d=>{var _;return d.title.toLowerCase().includes(t)||((_=d.description)==null?void 0:_.toLowerCase().includes(t))})}return s.sort((t,d)=>new Date(d.created_at)-new Date(t.created_at))}),U=w(()=>[...new Set(x.value.map(t=>t.target_sector).filter(Boolean))].sort()),N=w(()=>x.value.filter(s=>s.generated_by_ai).length),F=w(()=>x.value.length===0?0:x.value.reduce((t,d)=>{const _=(d.estimated_cost_min+d.estimated_cost_max)/2;return t+(_||0)},0)/x.value.length),L=w(()=>{if(x.value.length===0)return 0;const s=x.value.reduce((t,d)=>t+(d.estimated_duration_days||0),0);return Math.round(s/x.value.length)}),V=async()=>{h.value=!0;try{const[s,t]=await Promise.all([A.get("/api/business-intelligence/technical-offers"),A.get("/api/business-intelligence/core-competencies")]);s.data.success&&(x.value=s.data.data.offers),t.data.success&&(z.value=t.data.data.competencies)}catch(s){console.error("Error fetching data:",s)}finally{h.value=!1}},P=async()=>{if(i.value){await E();return}C.value=!0;try{const s=await A.post("/api/business-intelligence/technical-offers/generate-ai",u.value);s.data.success&&(i.value=s.data.data)}catch(s){console.error("Error generating offer:",s)}finally{C.value=!1}},E=async()=>{console.log("Saving offer:",i.value),O(),await V()},H=s=>{console.log("Edit offer:",s)},$=s=>{var t;u.value={competency_id:((t=s.core_competency)==null?void 0:t.id)||"",target_sector:s.target_sector||"",custom_requirements:s.description||"",budget_min:s.estimated_cost_min,budget_max:s.estimated_cost_max,preferred_duration:s.estimated_duration_days},S.value=!0},J=s=>{console.log("View details for:",s)},O=()=>{S.value=!1,i.value=null,u.value={competency_id:"",target_sector:"",custom_requirements:"",budget_min:null,budget_max:null,preferred_duration:null}},K=s=>new Intl.NumberFormat("it-IT").format(s),Q=s=>new Date(s).toLocaleDateString("it-IT"),q=s=>!s||typeof s!="object"?0:Object.values(s).reduce((t,d)=>t+(d||0),0),W=s=>{const t=["bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300","bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300","bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300","bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300"],d=s.length%t.length;return t[d]},X=s=>({Technology:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",Healthcare:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",Finance:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",Retail:"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",Manufacturing:"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"})[s]||"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300",Y=s=>({draft:"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300",ready:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",sent:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",accepted:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"})[s]||"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300",Z=s=>({draft:"Bozza",ready:"Pronta",sent:"Inviata",accepted:"Accettata"})[s]||s;return ee(()=>{V()}),(s,t)=>{var d,_;return a(),l("div",se,[e("div",ae,[e("div",le,[e("div",oe,[t[12]||(t[12]=e("div",{class:"flex items-center"},[e("h1",{class:"text-2xl font-semibold text-gray-900 dark:text-white"},"Offerta Tecnica")],-1)),e("div",de,[e("button",{onClick:t[0]||(t[0]=r=>S.value=!0),class:"px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-lg hover:from-purple-700 hover:to-blue-700 transition-all"}," ✨ Genera con AI "),e("button",{onClick:V,disabled:h.value,class:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 disabled:opacity-50 transition-colors"},[h.value?(a(),M(c,{key:0,name:"arrow-path",class:"animate-spin h-4 w-4 mr-2 inline"})):g("",!0),t[11]||(t[11]=T(" Aggiorna "))],8,ne)])])])]),e("div",ie,[e("div",ue,[e("div",ge,[e("div",ce,[e("div",xe,[b(c,{name:"document-text",class:"h-8 w-8 text-blue-600"})]),e("div",pe,[e("div",me,o(x.value.length),1),t[13]||(t[13]=e("div",{class:"text-sm text-gray-600 dark:text-gray-400"},"Offerte Totali",-1))])])]),e("div",be,[e("div",ye,[e("div",ve,[b(c,{name:"sparkles",class:"h-8 w-8 text-purple-600"})]),e("div",ke,[e("div",_e,o(N.value),1),t[14]||(t[14]=e("div",{class:"text-sm text-gray-600 dark:text-gray-400"},"Generate con AI",-1))])])]),e("div",fe,[e("div",he,[e("div",we,[b(c,{name:"currency-euro",class:"h-8 w-8 text-green-600"})]),e("div",Ce,[e("div",Se,"€"+o(K(F.value)),1),t[15]||(t[15]=e("div",{class:"text-sm text-gray-600 dark:text-gray-400"},"Valore Medio",-1))])])]),e("div",Te,[e("div",De,[e("div",ze,[b(c,{name:"clock",class:"h-8 w-8 text-yellow-600"})]),e("div",Ve,[e("div",Oe,o(L.value),1),t[16]||(t[16]=e("div",{class:"text-sm text-gray-600 dark:text-gray-400"},"Durata Media (giorni)",-1))])])])]),e("div",Me,[e("div",Ie,[e("div",null,[t[18]||(t[18]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Competenza Core",-1)),m(e("select",{"onUpdate:modelValue":t[1]||(t[1]=r=>n.value.competency=r),class:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},[t[17]||(t[17]=e("option",{value:""},"Tutte le competenze",-1)),(a(!0),l(y,null,v(z.value,r=>(a(),l("option",{key:r.id,value:r.id},o(r.name),9,Ae))),128))],512),[[D,n.value.competency]])]),e("div",null,[t[20]||(t[20]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Settore Target",-1)),m(e("select",{"onUpdate:modelValue":t[2]||(t[2]=r=>n.value.sector=r),class:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},[t[19]||(t[19]=e("option",{value:""},"Tutti i settori",-1)),(a(!0),l(y,null,v(U.value,r=>(a(),l("option",{key:r,value:r},o(r),9,je))),128))],512),[[D,n.value.sector]])]),e("div",null,[t[22]||(t[22]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Stato",-1)),m(e("select",{"onUpdate:modelValue":t[3]||(t[3]=r=>n.value.status=r),class:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},t[21]||(t[21]=[te('<option value="">Tutti gli stati</option><option value="draft">Bozza</option><option value="ready">Pronta</option><option value="sent">Inviata</option><option value="accepted">Accettata</option>',5)]),512),[[D,n.value.status]])]),e("div",null,[t[23]||(t[23]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Ricerca",-1)),m(e("input",{"onUpdate:modelValue":t[4]||(t[4]=r=>n.value.search=r),type:"text",placeholder:"Cerca offerte...",class:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},null,512),[[f,n.value.search]])])])]),h.value?(a(),l("div",qe,t[24]||(t[24]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1)]))):j.value.length===0?(a(),l("div",Be,[b(c,{name:"document-text",class:"h-12 w-12 text-gray-400 mx-auto mb-4"}),t[25]||(t[25]=e("p",{class:"text-gray-500 dark:text-gray-400"},"Nessuna offerta trovata con i filtri selezionati.",-1))])):(a(),l("div",Ge,[(a(!0),l(y,null,v(j.value,r=>{var B,G,R;return a(),l("div",{key:r.id,class:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6 hover:shadow-lg transition-shadow"},[e("div",Re,[e("div",Ue,[e("div",Ne,[e("h3",Fe,o(r.title),1),r.generated_by_ai?(a(),M(c,{key:0,name:"sparkles",class:"h-4 w-4 text-purple-500",title:"Generata con AI"})):g("",!0)]),e("p",Le,o(r.description),1),e("div",Pe,[r.core_competency?(a(),l("span",{key:0,class:I([W(r.core_competency.name),"inline-flex items-center px-2 py-1 rounded text-xs font-medium"])},o(r.core_competency.name),3)):g("",!0),r.target_sector?(a(),l("span",{key:1,class:I([X(r.target_sector),"inline-flex items-center px-2 py-1 rounded text-xs font-medium"])},o(r.target_sector),3)):g("",!0)])]),e("div",Ee,[e("span",{class:I([Y(r.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},o(Z(r.status)),3)])]),e("div",He,[e("div",$e,[t[26]||(t[26]=e("span",{class:"text-sm font-medium text-gray-700 dark:text-gray-300"},"Range Costi",-1)),e("span",Je,o(r.estimated_cost_range),1)]),e("div",Ke,[e("span",null,"Durata: "+o(r.estimated_duration_days||"N/A")+" giorni",1),e("span",null,"Team: "+o(q(r.team_composition))+" persone",1)])]),(B=r.technology_stack)!=null&&B.length?(a(),l("div",Qe,[t[27]||(t[27]=e("h4",{class:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Stack Tecnologico",-1)),e("div",We,[(a(!0),l(y,null,v(r.technology_stack.slice(0,4),p=>(a(),l("span",{key:p,class:"inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-300"},o(p),1))),128)),r.technology_stack.length>4?(a(),l("span",Xe," +"+o(r.technology_stack.length-4),1)):g("",!0)])])):g("",!0),(G=r.deliverables)!=null&&G.length?(a(),l("div",Ye,[t[28]||(t[28]=e("h4",{class:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Deliverable Principali",-1)),e("ul",Ze,[(a(!0),l(y,null,v(r.deliverables.slice(0,3),p=>(a(),l("li",{key:p,class:"flex items-center"},[b(c,{name:"check",class:"h-3 w-3 text-green-500 mr-2 flex-shrink-0"}),T(" "+o(p),1)]))),128)),r.deliverables.length>3?(a(),l("li",et," +"+o(r.deliverables.length-3)+" altri deliverable ",1)):g("",!0)])])):g("",!0),(R=r.success_metrics)!=null&&R.length?(a(),l("div",tt,[t[29]||(t[29]=e("h4",{class:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Metriche di Successo",-1)),e("div",rt,[(a(!0),l(y,null,v(r.success_metrics.slice(0,4),p=>(a(),l("div",{key:p,class:"text-xs text-center p-2 bg-green-50 dark:bg-green-900/20 rounded border border-green-200 dark:border-green-800"},[e("div",st,o(p),1)]))),128))])])):g("",!0),e("div",at,[e("div",lt," Creata: "+o(Q(r.created_at)),1),e("div",ot,[e("button",{onClick:p=>H(r),class:"text-blue-600 hover:text-blue-800 text-sm font-medium"}," Modifica ",8,dt),e("button",{onClick:p=>$(r),class:"text-purple-600 hover:text-purple-800 text-sm font-medium"}," Duplica ",8,nt),e("button",{onClick:p=>J(r),class:"text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200 text-sm font-medium"}," Dettagli ",8,it)])])])}),128))]))]),S.value?(a(),l("div",ut,[e("div",gt,[e("div",ct,[e("div",xt,[t[30]||(t[30]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Genera Offerta Tecnica con AI",-1)),e("button",{onClick:O,class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},[b(c,{name:"x-mark",class:"h-6 w-6"})])]),e("form",{onSubmit:re(P,["prevent"]),class:"space-y-6"},[e("div",pt,[t[34]||(t[34]=e("h4",{class:"text-sm font-medium text-gray-900 dark:text-white mb-4"},"1. Informazioni Base",-1)),e("div",mt,[e("div",null,[t[32]||(t[32]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Competenza Core",-1)),m(e("select",{"onUpdate:modelValue":t[5]||(t[5]=r=>u.value.competency_id=r),required:"",class:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},[t[31]||(t[31]=e("option",{value:""},"Seleziona competenza",-1)),(a(!0),l(y,null,v(z.value,r=>(a(),l("option",{key:r.id,value:r.id},o(r.name),9,bt))),128))],512),[[D,u.value.competency_id]])]),e("div",null,[t[33]||(t[33]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Settore Target",-1)),m(e("input",{"onUpdate:modelValue":t[6]||(t[6]=r=>u.value.target_sector=r),type:"text",required:"",placeholder:"es. Healthcare, Finance, Retail",class:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},null,512),[[f,u.value.target_sector]])])])]),e("div",yt,[t[39]||(t[39]=e("h4",{class:"text-sm font-medium text-gray-900 dark:text-white mb-4"},"2. Requisiti Specifici",-1)),e("div",vt,[e("div",null,[t[35]||(t[35]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Requisiti Personalizzati",-1)),m(e("textarea",{"onUpdate:modelValue":t[7]||(t[7]=r=>u.value.custom_requirements=r),rows:"4",placeholder:"Descrivi le esigenze specifiche del cliente, tecnologie richieste, vincoli particolari...",class:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},null,512),[[f,u.value.custom_requirements]])]),e("div",kt,[e("div",null,[t[36]||(t[36]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Budget Range Min (€)",-1)),m(e("input",{"onUpdate:modelValue":t[8]||(t[8]=r=>u.value.budget_min=r),type:"number",min:"0",class:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},null,512),[[f,u.value.budget_min,void 0,{number:!0}]])]),e("div",null,[t[37]||(t[37]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Budget Range Max (€)",-1)),m(e("input",{"onUpdate:modelValue":t[9]||(t[9]=r=>u.value.budget_max=r),type:"number",min:"0",class:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},null,512),[[f,u.value.budget_max,void 0,{number:!0}]])]),e("div",null,[t[38]||(t[38]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Durata Preferita (giorni)",-1)),m(e("input",{"onUpdate:modelValue":t[10]||(t[10]=r=>u.value.preferred_duration=r),type:"number",min:"1",class:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},null,512),[[f,u.value.preferred_duration,void 0,{number:!0}]])])])])]),i.value?(a(),l("div",_t,[e("div",ft,[b(c,{name:"sparkles",class:"h-5 w-5 text-purple-600 mr-2"}),t[40]||(t[40]=e("h4",{class:"text-sm font-medium text-purple-900 dark:text-purple-100"},"Offerta Generata con AI",-1))]),e("div",ht,[e("div",null,[t[41]||(t[41]=e("h5",{class:"text-sm font-medium text-purple-900 dark:text-purple-100 mb-2"},"Titolo:",-1)),e("p",wt,o(i.value.title),1)]),e("div",null,[t[42]||(t[42]=e("h5",{class:"text-sm font-medium text-purple-900 dark:text-purple-100 mb-2"},"Descrizione:",-1)),e("p",Ct,o(i.value.description),1)]),e("div",St,[e("div",Tt,[e("div",Dt,o(i.value.estimated_cost_range),1),t[43]||(t[43]=e("div",{class:"text-xs text-purple-700 dark:text-purple-300"},"Costo Stimato",-1))]),e("div",zt,[e("div",Vt,o(i.value.estimated_duration_days)+" giorni",1),t[44]||(t[44]=e("div",{class:"text-xs text-purple-700 dark:text-purple-300"},"Durata",-1))]),e("div",Ot,[e("div",Mt,o(q(i.value.team_composition)),1),t[45]||(t[45]=e("div",{class:"text-xs text-purple-700 dark:text-purple-300"},"Team Size",-1))])]),(d=i.value.technology_stack)!=null&&d.length?(a(),l("div",It,[t[46]||(t[46]=e("h5",{class:"text-sm font-medium text-purple-900 dark:text-purple-100 mb-2"},"Stack Tecnologico:",-1)),e("div",At,[(a(!0),l(y,null,v(i.value.technology_stack,r=>(a(),l("span",{key:r,class:"inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-purple-100 dark:bg-purple-800 text-purple-800 dark:text-purple-200"},o(r),1))),128))])])):g("",!0),(_=i.value.deliverables)!=null&&_.length?(a(),l("div",jt,[t[47]||(t[47]=e("h5",{class:"text-sm font-medium text-purple-900 dark:text-purple-100 mb-2"},"Deliverable:",-1)),e("ul",qt,[(a(!0),l(y,null,v(i.value.deliverables.slice(0,5),r=>(a(),l("li",{key:r,class:"flex items-center"},[b(c,{name:"check",class:"h-3 w-3 text-purple-500 mr-2 flex-shrink-0"}),T(" "+o(r),1)]))),128))])])):g("",!0)])])):g("",!0),e("div",Bt,[e("button",{type:"button",onClick:O,class:"px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-200 dark:bg-gray-600 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-500"}," Annulla "),e("button",{type:"submit",disabled:C.value,class:"px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-lg hover:from-purple-700 hover:to-blue-700 disabled:opacity-50"},[C.value?(a(),M(c,{key:0,name:"arrow-path",class:"animate-spin h-4 w-4 mr-2 inline"})):g("",!0),T(" "+o(i.value?"Salva Offerta":"Genera con AI"),1)],8,Gt)])],32)])])])):g("",!0)])}}};export{Ft as default};
