import{l as Ne,x as $t,_ as Ae,k as De,H as ot,K as Re,b as Xr,u as d,J as $a,E as Ut,G as Ta}from"./jsx-runtime-BDYu3_Il.js";import{N as Aa,o as Pa}from"./vendor.js";var at,re,Vr,An,Tt=0,co=[],se=Ne,Pn=se.__b,Rn=se.__r,In=se.diffed,Ln=se.__c,On=se.unmount,Fn=se.__;function Lt(t,e){se.__h&&se.__h(re,t,Tt||e),Tt=0;var r=re.__H||(re.__H={__:[],__h:[]});return t>=r.__.length&&r.__.push({}),r.__[t]}function U(t){return Tt=1,fn(po,t)}function fn(t,e,r){var n=Lt(at++,2);if(n.t=t,!n.__c&&(n.__=[r?r(e):po(void 0,e),function(s){var c=n.__N?n.__N[0]:n.__[0],l=n.t(c,s);c!==l&&(n.__N=[l,n.__[1]],n.__c.setState({}))}],n.__c=re,!re.__f)){var o=function(s,c,l){if(!n.__c.__H)return!0;var u=n.__c.__H.__.filter(function(h){return!!h.__c});if(u.every(function(h){return!h.__N}))return!a||a.call(this,s,c,l);var p=n.__c.props!==s;return u.forEach(function(h){if(h.__N){var w=h.__[0];h.__=h.__N,h.__N=void 0,w!==h.__[0]&&(p=!0)}}),a&&a.call(this,s,c,l)||p};re.__f=!0;var a=re.shouldComponentUpdate,i=re.componentWillUpdate;re.componentWillUpdate=function(s,c,l){if(this.__e){var u=a;a=void 0,o(s,c,l),a=u}i&&i.call(this,s,c,l)},re.shouldComponentUpdate=o}return n.__N||n.__}function ne(t,e){var r=Lt(at++,3);!se.__s&&wn(r.__H,e)&&(r.__=t,r.u=e,re.__H.__h.push(r))}function Ot(t,e){var r=Lt(at++,4);!se.__s&&wn(r.__H,e)&&(r.__=t,r.u=e,re.__h.push(r))}function V(t){return Tt=5,le(function(){return{current:t}},[])}function lo(t,e,r){Tt=6,Ot(function(){if(typeof t=="function"){var n=t(e());return function(){t(null),n&&typeof n=="function"&&n()}}if(t)return t.current=e(),function(){return t.current=null}},r==null?r:r.concat(t))}function le(t,e){var r=Lt(at++,7);return wn(r.__H,e)&&(r.__=t(),r.__H=e,r.__h=t),r.__}function S(t,e){return Tt=8,le(function(){return t},e)}function ke(t){var e=re.context[t.__c],r=Lt(at++,9);return r.c=t,e?(r.__==null&&(r.__=!0,e.sub(re)),e.props.value):t.__}function uo(t,e){se.useDebugValue&&se.useDebugValue(e?e(t):t)}function lr(){var t=Lt(at++,11);if(!t.__){for(var e=re.__v;e!==null&&!e.__m&&e.__!==null;)e=e.__;var r=e.__m||(e.__m=[0,0]);t.__="P"+r[0]+"-"+r[1]++}return t.__}function Ra(){for(var t;t=co.shift();)if(t.__P&&t.__H)try{t.__H.__h.forEach(kr),t.__H.__h.forEach(Jr),t.__H.__h=[]}catch(e){t.__H.__h=[],se.__e(e,t.__v)}}se.__b=function(t){re=null,Pn&&Pn(t)},se.__=function(t,e){t&&e.__k&&e.__k.__m&&(t.__m=e.__k.__m),Fn&&Fn(t,e)},se.__r=function(t){Rn&&Rn(t),at=0;var e=(re=t.__c).__H;e&&(Vr===re?(e.__h=[],re.__h=[],e.__.forEach(function(r){r.__N&&(r.__=r.__N),r.u=r.__N=void 0})):(e.__h.forEach(kr),e.__h.forEach(Jr),e.__h=[],at=0)),Vr=re},se.diffed=function(t){In&&In(t);var e=t.__c;e&&e.__H&&(e.__H.__h.length&&(co.push(e)!==1&&An===se.requestAnimationFrame||((An=se.requestAnimationFrame)||Ia)(Ra)),e.__H.__.forEach(function(r){r.u&&(r.__H=r.u),r.u=void 0})),Vr=re=null},se.__c=function(t,e){e.some(function(r){try{r.__h.forEach(kr),r.__h=r.__h.filter(function(n){return!n.__||Jr(n)})}catch(n){e.some(function(o){o.__h&&(o.__h=[])}),e=[],se.__e(n,r.__v)}}),Ln&&Ln(t,e)},se.unmount=function(t){On&&On(t);var e,r=t.__c;r&&r.__H&&(r.__H.__.forEach(function(n){try{kr(n)}catch(o){e=o}}),r.__H=void 0,e&&se.__e(e,r.__v))};var jn=typeof requestAnimationFrame=="function";function Ia(t){var e,r=function(){clearTimeout(n),jn&&cancelAnimationFrame(e),setTimeout(t)},n=setTimeout(r,100);jn&&(e=requestAnimationFrame(r))}function kr(t){var e=re,r=t.__c;typeof r=="function"&&(t.__c=void 0,r()),re=e}function Jr(t){var e=re;t.__c=t.__(),re=e}function wn(t,e){return!t||t.length!==e.length||e.some(function(r,n){return r!==t[n]})}function po(t,e){return typeof e=="function"?e(t):e}function mo(t,e){for(var r in e)t[r]=e[r];return t}function Qr(t,e){for(var r in t)if(r!=="__source"&&!(r in e))return!0;for(var n in e)if(n!=="__source"&&t[n]!==e[n])return!0;return!1}function ho(t,e){var r=e(),n=U({t:{__:r,u:e}}),o=n[0].t,a=n[1];return Ot(function(){o.__=r,o.u=e,Hr(o)&&a({t:o})},[t,r,e]),ne(function(){return Hr(o)&&a({t:o}),t(function(){Hr(o)&&a({t:o})})},[t]),r}function Hr(t){var e,r,n=t.u,o=t.__;try{var a=n();return!((e=o)===(r=a)&&(e!==0||1/e==1/r)||e!=e&&r!=r)}catch{return!0}}function go(t){t()}function fo(t){return t}function wo(){return[!1,go]}var vo=Ot;function en(t,e){this.props=t,this.context=e}function La(t,e){function r(o){var a=this.props.ref,i=a==o.ref;return!i&&a&&(a.call?a(null):a.current=null),e?!e(this.props,o)||!i:Qr(this.props,o)}function n(o){return this.shouldComponentUpdate=r,Ae(t,o)}return n.displayName="Memo("+(t.displayName||t.name)+")",n.prototype.isReactComponent=!0,n.__f=!0,n}(en.prototype=new $t).isPureReactComponent=!0,en.prototype.shouldComponentUpdate=function(t,e){return Qr(this.props,t)||Qr(this.state,e)};var Bn=Ne.__b;Ne.__b=function(t){t.type&&t.type.__f&&t.ref&&(t.props.ref=t.ref,t.ref=null),Bn&&Bn(t)};var Oa=typeof Symbol<"u"&&Symbol.for&&Symbol.for("react.forward_ref")||3911;function dr(t){function e(r){var n=mo({},r);return delete n.ref,t(n,r.ref||null)}return e.$$typeof=Oa,e.render=e,e.prototype.isReactComponent=e.__f=!0,e.displayName="ForwardRef("+(t.displayName||t.name)+")",e}var Dn=function(t,e){return t==null?null:ot(ot(t).map(e))},Fa={map:Dn,forEach:Dn,count:function(t){return t?ot(t).length:0},only:function(t){var e=ot(t);if(e.length!==1)throw"Children.only";return e[0]},toArray:ot},ja=Ne.__e;Ne.__e=function(t,e,r,n){if(t.then){for(var o,a=e;a=a.__;)if((o=a.__c)&&o.__c)return e.__e==null&&(e.__e=r.__e,e.__k=r.__k),o.__c(t,e)}ja(t,e,r,n)};var Vn=Ne.unmount;function bo(t,e,r){return t&&(t.__c&&t.__c.__H&&(t.__c.__H.__.forEach(function(n){typeof n.__c=="function"&&n.__c()}),t.__c.__H=null),(t=mo({},t)).__c!=null&&(t.__c.__P===r&&(t.__c.__P=e),t.__c.__e=!0,t.__c=null),t.__k=t.__k&&t.__k.map(function(n){return bo(n,e,r)})),t}function yo(t,e,r){return t&&r&&(t.__v=null,t.__k=t.__k&&t.__k.map(function(n){return yo(n,e,r)}),t.__c&&t.__c.__P===e&&(t.__e&&r.appendChild(t.__e),t.__c.__e=!0,t.__c.__P=r)),t}function Cr(){this.__u=0,this.o=null,this.__b=null}function xo(t){var e=t.__.__c;return e&&e.__a&&e.__a(t)}function Ba(t){var e,r,n;function o(a){if(e||(e=t()).then(function(i){r=i.default||i},function(i){n=i}),n)throw n;if(!r)throw e;return Ae(r,a)}return o.displayName="Lazy",o.__f=!0,o}function Vt(){this.i=null,this.l=null}Ne.unmount=function(t){var e=t.__c;e&&e.__R&&e.__R(),e&&32&t.__u&&(t.type=null),Vn&&Vn(t)},(Cr.prototype=new $t).__c=function(t,e){var r=e.__c,n=this;n.o==null&&(n.o=[]),n.o.push(r);var o=xo(n.__v),a=!1,i=function(){a||(a=!0,r.__R=null,o?o(s):s())};r.__R=i;var s=function(){if(!--n.__u){if(n.state.__a){var c=n.state.__a;n.__v.__k[0]=yo(c,c.__c.__P,c.__c.__O)}var l;for(n.setState({__a:n.__b=null});l=n.o.pop();)l.forceUpdate()}};n.__u++||32&e.__u||n.setState({__a:n.__b=n.__v.__k[0]}),t.then(i,i)},Cr.prototype.componentWillUnmount=function(){this.o=[]},Cr.prototype.render=function(t,e){if(this.__b){if(this.__v.__k){var r=document.createElement("div"),n=this.__v.__k[0].__c;this.__v.__k[0]=bo(this.__b,r,n.__O=n.__P)}this.__b=null}var o=e.__a&&Ae(De,null,t.fallback);return o&&(o.__u&=-33),[Ae(De,null,e.__a?null:t.children),o]};var Hn=function(t,e,r){if(++r[1]===r[0]&&t.l.delete(e),t.props.revealOrder&&(t.props.revealOrder[0]!=="t"||!t.l.size))for(r=t.i;r;){for(;r.length>3;)r.pop()();if(r[1]<r[0])break;t.i=r=r[2]}};function Da(t){return this.getChildContext=function(){return t.context},t.children}function Va(t){var e=this,r=t.h;if(e.componentWillUnmount=function(){Ut(null,e.v),e.v=null,e.h=null},e.h&&e.h!==r&&e.componentWillUnmount(),!e.v){for(var n=e.__v;n!==null&&!n.__m&&n.__!==null;)n=n.__;e.h=r,e.v={nodeType:1,parentNode:r,childNodes:[],__k:{__m:n.__m},contains:function(){return!0},appendChild:function(o){this.childNodes.push(o),e.h.appendChild(o)},insertBefore:function(o,a){this.childNodes.push(o),e.h.insertBefore(o,a)},removeChild:function(o){this.childNodes.splice(this.childNodes.indexOf(o)>>>1,1),e.h.removeChild(o)}}}Ut(Ae(Da,{context:e.context},t.__v),e.v)}function Ha(t,e){var r=Ae(Va,{__v:t,h:e});return r.containerInfo=e,r}(Vt.prototype=new $t).__a=function(t){var e=this,r=xo(e.__v),n=e.l.get(t);return n[0]++,function(o){var a=function(){e.props.revealOrder?(n.push(o),Hn(e,t,n)):o()};r?r(a):a()}},Vt.prototype.render=function(t){this.i=null,this.l=new Map;var e=ot(t.children);t.revealOrder&&t.revealOrder[0]==="b"&&e.reverse();for(var r=e.length;r--;)this.l.set(e[r],this.i=[1,0,this.i]);return t.children},Vt.prototype.componentDidUpdate=Vt.prototype.componentDidMount=function(){var t=this;this.l.forEach(function(e,r){Hn(t,r,e)})};var _o=typeof Symbol<"u"&&Symbol.for&&Symbol.for("react.element")||60103,Wa=/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|image(!S)|letter|lighting|marker(?!H|W|U)|overline|paint|pointer|shape|stop|strikethrough|stroke|text(?!L)|transform|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/,Za=/^on(Ani|Tra|Tou|BeforeInp|Compo)/,qa=/[A-Z0-9]/g,Ua=typeof document<"u",Ga=function(t){return(typeof Symbol<"u"&&typeof Symbol()=="symbol"?/fil|che|rad/:/fil|che|ra/).test(t)};function Ya(t,e,r){return e.__k==null&&(e.textContent=""),Ut(t,e),typeof r=="function"&&r(),t?t.__c:null}function Ka(t,e,r){return Ta(t,e),typeof r=="function"&&r(),t?t.__c:null}$t.prototype.isReactComponent={},["componentWillMount","componentWillReceiveProps","componentWillUpdate"].forEach(function(t){Object.defineProperty($t.prototype,t,{configurable:!0,get:function(){return this["UNSAFE_"+t]},set:function(e){Object.defineProperty(this,t,{configurable:!0,writable:!0,value:e})}})});var Wn=Ne.event;function Xa(){}function Ja(){return this.cancelBubble}function Qa(){return this.defaultPrevented}Ne.event=function(t){return Wn&&(t=Wn(t)),t.persist=Xa,t.isPropagationStopped=Ja,t.isDefaultPrevented=Qa,t.nativeEvent=t};var vn,ei={enumerable:!1,configurable:!0,get:function(){return this.class}},Zn=Ne.vnode;Ne.vnode=function(t){typeof t.type=="string"&&function(e){var r=e.props,n=e.type,o={},a=n.indexOf("-")===-1;for(var i in r){var s=r[i];if(!(i==="value"&&"defaultValue"in r&&s==null||Ua&&i==="children"&&n==="noscript"||i==="class"||i==="className")){var c=i.toLowerCase();i==="defaultValue"&&"value"in r&&r.value==null?i="value":i==="download"&&s===!0?s="":c==="translate"&&s==="no"?s=!1:c[0]==="o"&&c[1]==="n"?c==="ondoubleclick"?i="ondblclick":c!=="onchange"||n!=="input"&&n!=="textarea"||Ga(r.type)?c==="onfocus"?i="onfocusin":c==="onblur"?i="onfocusout":Za.test(i)&&(i=c):c=i="oninput":a&&Wa.test(i)?i=i.replace(qa,"-$&").toLowerCase():s===null&&(s=void 0),c==="oninput"&&o[i=c]&&(i="oninputCapture"),o[i]=s}}n=="select"&&o.multiple&&Array.isArray(o.value)&&(o.value=ot(r.children).forEach(function(l){l.props.selected=o.value.indexOf(l.props.value)!=-1})),n=="select"&&o.defaultValue!=null&&(o.value=ot(r.children).forEach(function(l){l.props.selected=o.multiple?o.defaultValue.indexOf(l.props.value)!=-1:o.defaultValue==l.props.value})),r.class&&!r.className?(o.class=r.class,Object.defineProperty(o,"className",ei)):(r.className&&!r.class||r.class&&r.className)&&(o.class=o.className=r.className),e.props=o}(t),t.$$typeof=_o,Zn&&Zn(t)};var qn=Ne.__r;Ne.__r=function(t){qn&&qn(t),vn=t.__c};var Un=Ne.diffed;Ne.diffed=function(t){Un&&Un(t);var e=t.props,r=t.__e;r!=null&&t.type==="textarea"&&"value"in e&&e.value!==r.value&&(r.value=e.value==null?"":e.value),vn=null};var ti={ReactCurrentDispatcher:{current:{readContext:function(t){return vn.__n[t.__c].props.value},useCallback:S,useContext:ke,useDebugValue:uo,useDeferredValue:fo,useEffect:ne,useId:lr,useImperativeHandle:lo,useInsertionEffect:vo,useLayoutEffect:Ot,useMemo:le,useReducer:fn,useRef:V,useState:U,useSyncExternalStore:ho,useTransition:wo}}};function ri(t){return Ae.bind(null,t)}function ur(t){return!!t&&t.$$typeof===_o}function ni(t){return ur(t)&&t.type===De}function oi(t){return!!t&&!!t.displayName&&(typeof t.displayName=="string"||t.displayName instanceof String)&&t.displayName.startsWith("Memo(")}function ko(t){return ur(t)?$a.apply(null,arguments):t}function ai(t){return!!t.__k&&(Ut(null,t),!0)}function ii(t){return t&&(t.base||t.nodeType===1&&t)||null}var si=function(t,e){return t(e)},ci=function(t,e){return t(e)},li=De,di=ur,bn={useState:U,useId:lr,useReducer:fn,useEffect:ne,useLayoutEffect:Ot,useInsertionEffect:vo,useTransition:wo,useDeferredValue:fo,useSyncExternalStore:ho,startTransition:go,useRef:V,useImperativeHandle:lo,useMemo:le,useCallback:S,useContext:ke,useDebugValue:uo,version:"18.3.1",Children:Fa,render:Ya,hydrate:Ka,unmountComponentAtNode:ai,createPortal:Ha,createElement:Ae,createContext:Re,createFactory:ri,cloneElement:ko,createRef:Xr,Fragment:De,isValidElement:ur,isElement:di,isFragment:ni,isMemo:oi,findDOMNode:ii,Component:$t,PureComponent:en,memo:La,forwardRef:dr,flushSync:ci,unstable_batchedUpdates:si,StrictMode:li,Suspense:Cr,SuspenseList:Vt,lazy:Ba,__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:ti},ui=(t=16)=>Math.random().toString(36).substring(2,t+2),pi={maxReconnectAttempts:5,reconnectDelay:1e3,requestTimeout:3e4},mi=class{constructor(t={}){this.ws=null,this.pendingRequests=new Map,this.reconnectAttempts=0,this.methods={},this.isIntentionalClose=!1,this.options={...pi,...t}}register(t){Object.entries(t).forEach(([e,r])=>{this.methods[e]={handler:r}})}callMethod(t,e,r){if(!this.ws)throw new Error("WebSocket is not connected");const n=ui(),o={id:n,messageType:"request",method:t,payload:e};return new Promise((a,i)=>{var s;const c=setTimeout(()=>{this.pendingRequests.delete(n),i(new Error(`Request timed out: ${t}`))},this.options.requestTimeout);this.pendingRequests.set(n,{resolve:a,reject:i,timeout:c,onUpdate:r}),(s=this.ws)==null||s.send(JSON.stringify(o))})}setupWebSocketHandlers(t){t.onmessage=e=>{try{const r=JSON.parse(e.data);this.handleMessage(r)}catch(r){console.error("Error handling WebSocket message:",r)}},t.onclose=()=>{this.handleDisconnect()},t.onerror=e=>{console.error("WebSocket error:",e)}}handleMessage(t){const{messageType:e,id:r}=t;switch(e){case"request":this.handleRequest(t);break;case"response":this.handleResponse(r,t.payload);break;case"update":this.handleUpdate(r,t.payload);break;case"error":this.handleError(r,t.error.message);break;default:console.warn(`Unknown message type: ${e}`)}}async handleRequest(t){const{id:e,method:r,payload:n}=t;if(!r){this.sendError(e,"Method name is required");return}const o=this.methods[r];if(!o){this.sendError(e,`Method not found: ${r}`);return}try{const a=s=>{this.sendUpdate(e,r,s)},i=await o.handler(n,a);this.sendResponse(e,r,i)}catch(a){this.sendError(e,a instanceof Error?a.message:String(a))}}handleResponse(t,e){const r=this.pendingRequests.get(t);if(!r){console.warn(`Received response for unknown request ID: ${t}`);return}clearTimeout(r.timeout),this.pendingRequests.delete(t),r.resolve(e)}handleUpdate(t,e){const r=this.pendingRequests.get(t);if(!r||!r.onUpdate){console.warn(`Received update for unknown request ID: ${t}`);return}r.onUpdate(e)}handleError(t,e){const r=this.pendingRequests.get(t);if(!r){console.warn(`Received error for unknown request ID: ${t}`);return}clearTimeout(r.timeout),this.pendingRequests.delete(t),r.reject(new Error(e))}sendResponse(t,e,r){if(!this.ws)throw new Error("WebSocket is not connected");const n={id:t,messageType:"response",method:e,payload:r};this.ws.send(JSON.stringify(n))}sendUpdate(t,e,r){if(!this.ws)throw new Error("WebSocket is not connected");const n={id:t,messageType:"update",method:e,payload:r};this.ws.send(JSON.stringify(n))}sendError(t,e){if(!this.ws)throw new Error("WebSocket is not connected");const r={id:t,messageType:"error",error:{message:e}};this.ws.send(JSON.stringify(r))}handleDisconnect(){if(this.isIntentionalClose){console.log("WebSocket closed intentionally, not attempting to reconnect"),this.clearPendingRequests(new Error("Connection closed by user"));return}this.reconnectAttempts<this.options.maxReconnectAttempts?(this.reconnectAttempts++,console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.options.maxReconnectAttempts})...`),setTimeout(()=>this.reconnect(),this.options.reconnectDelay*this.reconnectAttempts)):(console.error("Max reconnection attempts reached"),this.clearPendingRequests(new Error("Connection closed")))}clearPendingRequests(t){this.pendingRequests.forEach(({reject:e})=>{e(t)}),this.pendingRequests.clear()}async close(){this.isIntentionalClose=!0,this.ws&&(this.ws.close(),this.ws=null),this.clearPendingRequests(new Error("Connection closed by user"))}};function Ct(t,e,r,n=!1){const o=t.safeParse(e);if(!o.success){const a=new Error(`Validation failed for ${r}: ${o.error.message}`);if(n)return console.error(a),e;throw a}return o.data}var hi=class{constructor(t,e){this.bridge=t,this.contract=e,this.call=new Proxy({},{get:(r,n)=>(o,a)=>this.callMethod(n,o,a)})}async callMethod(t,e,r){const n=this.contract.consumes[t];if(!n)throw new Error(`Method ${String(t)} not found in contract`);const o=Ct(n.request,e,`request for method ${String(t)}`),a=r!=null&&r.onUpdate&&n.update?s=>{var c;if(n.update)try{const l=Ct(n.update,s,`update for method ${String(t)}`,!0);(c=r.onUpdate)==null||c.call(r,l)}catch(l){console.error("Update validation failed:",l)}}:void 0,i=await this.bridge.callMethod(t,o,a);return Ct(n.response,i,`response for method ${String(t)}`)}register(t){const e={};for(const[r,n]of Object.entries(t)){const o=this.contract.serves[r];if(!o)throw new Error(`Method ${r} not found in contract`);e[r]=async(a,i)=>{const s=Ct(o.request,a,`request for method ${r}`),c=o.update&&i?u=>{if(o.update)try{const p=Ct(o.update,u,`update for method ${r}`,!0);i(p)}catch(p){console.error("Update validation failed:",p)}}:void 0,l=await n(s,{sendUpdate:c});return Ct(o.response,l,`response for method ${r}`)}}this.bridge.register(e)}async close(){await this.bridge.close()}},gi=class extends mi{constructor(t,e){super(e),this.reconnectTimer=null,this.url=t}call(t,e,r){return this.callMethod(t,e,r)}reconnect(){this.reconnectTimer&&clearTimeout(this.reconnectTimer),this.reconnectTimer=setTimeout(async()=>{try{await this.connect()}catch{this.reconnect()}},this.options.reconnectDelay)}connect(){return new Promise((t,e)=>{try{const r=new window.WebSocket(this.url);r.onopen=()=>{this.ws=r,this.setupWebSocketHandlers(r),t()},r.onerror=()=>{e(new Error("Failed to connect to WebSocket server"))}}catch(r){e(r)}})}},fi=class extends hi{constructor(t,e,r){super(new gi(t,r),{serves:e.client||{},consumes:e.server||{}})}connect(){return this.bridge.connect()}};function Co(t,e,r){return new fi(t,e,r)}var H;(function(t){t.assertEqual=o=>o;function e(o){}t.assertIs=e;function r(o){throw new Error}t.assertNever=r,t.arrayToEnum=o=>{const a={};for(const i of o)a[i]=i;return a},t.getValidEnumValues=o=>{const a=t.objectKeys(o).filter(s=>typeof o[o[s]]!="number"),i={};for(const s of a)i[s]=o[s];return t.objectValues(i)},t.objectValues=o=>t.objectKeys(o).map(function(a){return o[a]}),t.objectKeys=typeof Object.keys=="function"?o=>Object.keys(o):o=>{const a=[];for(const i in o)Object.prototype.hasOwnProperty.call(o,i)&&a.push(i);return a},t.find=(o,a)=>{for(const i of o)if(a(i))return i},t.isInteger=typeof Number.isInteger=="function"?o=>Number.isInteger(o):o=>typeof o=="number"&&isFinite(o)&&Math.floor(o)===o;function n(o,a=" | "){return o.map(i=>typeof i=="string"?`'${i}'`:i).join(a)}t.joinValues=n,t.jsonStringifyReplacer=(o,a)=>typeof a=="bigint"?a.toString():a})(H||(H={}));var tn;(function(t){t.mergeShapes=(e,r)=>({...e,...r})})(tn||(tn={}));const b=H.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),Ye=t=>{switch(typeof t){case"undefined":return b.undefined;case"string":return b.string;case"number":return isNaN(t)?b.nan:b.number;case"boolean":return b.boolean;case"function":return b.function;case"bigint":return b.bigint;case"symbol":return b.symbol;case"object":return Array.isArray(t)?b.array:t===null?b.null:t.then&&typeof t.then=="function"&&t.catch&&typeof t.catch=="function"?b.promise:typeof Map<"u"&&t instanceof Map?b.map:typeof Set<"u"&&t instanceof Set?b.set:typeof Date<"u"&&t instanceof Date?b.date:b.object;default:return b.unknown}},g=H.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]),wi=t=>JSON.stringify(t,null,2).replace(/"([^"]+)":/g,"$1:");class Te extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=n=>{this.issues=[...this.issues,n]},this.addIssues=(n=[])=>{this.issues=[...this.issues,...n]};const r=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,r):this.__proto__=r,this.name="ZodError",this.issues=e}format(e){const r=e||function(a){return a.message},n={_errors:[]},o=a=>{for(const i of a.issues)if(i.code==="invalid_union")i.unionErrors.map(o);else if(i.code==="invalid_return_type")o(i.returnTypeError);else if(i.code==="invalid_arguments")o(i.argumentsError);else if(i.path.length===0)n._errors.push(r(i));else{let s=n,c=0;for(;c<i.path.length;){const l=i.path[c];c===i.path.length-1?(s[l]=s[l]||{_errors:[]},s[l]._errors.push(r(i))):s[l]=s[l]||{_errors:[]},s=s[l],c++}}};return o(this),n}static assert(e){if(!(e instanceof Te))throw new Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,H.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(e=r=>r.message){const r={},n=[];for(const o of this.issues)o.path.length>0?(r[o.path[0]]=r[o.path[0]]||[],r[o.path[0]].push(e(o))):n.push(e(o));return{formErrors:n,fieldErrors:r}}get formErrors(){return this.flatten()}}Te.create=t=>new Te(t);const At=(t,e)=>{let r;switch(t.code){case g.invalid_type:t.received===b.undefined?r="Required":r=`Expected ${t.expected}, received ${t.received}`;break;case g.invalid_literal:r=`Invalid literal value, expected ${JSON.stringify(t.expected,H.jsonStringifyReplacer)}`;break;case g.unrecognized_keys:r=`Unrecognized key(s) in object: ${H.joinValues(t.keys,", ")}`;break;case g.invalid_union:r="Invalid input";break;case g.invalid_union_discriminator:r=`Invalid discriminator value. Expected ${H.joinValues(t.options)}`;break;case g.invalid_enum_value:r=`Invalid enum value. Expected ${H.joinValues(t.options)}, received '${t.received}'`;break;case g.invalid_arguments:r="Invalid function arguments";break;case g.invalid_return_type:r="Invalid function return type";break;case g.invalid_date:r="Invalid date";break;case g.invalid_string:typeof t.validation=="object"?"includes"in t.validation?(r=`Invalid input: must include "${t.validation.includes}"`,typeof t.validation.position=="number"&&(r=`${r} at one or more positions greater than or equal to ${t.validation.position}`)):"startsWith"in t.validation?r=`Invalid input: must start with "${t.validation.startsWith}"`:"endsWith"in t.validation?r=`Invalid input: must end with "${t.validation.endsWith}"`:H.assertNever(t.validation):t.validation!=="regex"?r=`Invalid ${t.validation}`:r="Invalid";break;case g.too_small:t.type==="array"?r=`Array must contain ${t.exact?"exactly":t.inclusive?"at least":"more than"} ${t.minimum} element(s)`:t.type==="string"?r=`String must contain ${t.exact?"exactly":t.inclusive?"at least":"over"} ${t.minimum} character(s)`:t.type==="number"?r=`Number must be ${t.exact?"exactly equal to ":t.inclusive?"greater than or equal to ":"greater than "}${t.minimum}`:t.type==="date"?r=`Date must be ${t.exact?"exactly equal to ":t.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(t.minimum))}`:r="Invalid input";break;case g.too_big:t.type==="array"?r=`Array must contain ${t.exact?"exactly":t.inclusive?"at most":"less than"} ${t.maximum} element(s)`:t.type==="string"?r=`String must contain ${t.exact?"exactly":t.inclusive?"at most":"under"} ${t.maximum} character(s)`:t.type==="number"?r=`Number must be ${t.exact?"exactly":t.inclusive?"less than or equal to":"less than"} ${t.maximum}`:t.type==="bigint"?r=`BigInt must be ${t.exact?"exactly":t.inclusive?"less than or equal to":"less than"} ${t.maximum}`:t.type==="date"?r=`Date must be ${t.exact?"exactly":t.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(t.maximum))}`:r="Invalid input";break;case g.custom:r="Invalid input";break;case g.invalid_intersection_types:r="Intersection results could not be merged";break;case g.not_multiple_of:r=`Number must be a multiple of ${t.multipleOf}`;break;case g.not_finite:r="Number must be finite";break;default:r=e.defaultError,H.assertNever(t)}return{message:r}};let So=At;function vi(t){So=t}function Sr(){return So}const Nr=t=>{const{data:e,path:r,errorMaps:n,issueData:o}=t,a=[...r,...o.path||[]],i={...o,path:a};if(o.message!==void 0)return{...o,path:a,message:o.message};let s="";const c=n.filter(l=>!!l).slice().reverse();for(const l of c)s=l(i,{data:e,defaultError:s}).message;return{...o,path:a,message:s}},bi=[];function v(t,e){const r=Sr(),n=Nr({issueData:e,data:t.data,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,r,r===At?void 0:At].filter(o=>!!o)});t.common.issues.push(n)}class _e{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(e,r){const n=[];for(const o of r){if(o.status==="aborted")return T;o.status==="dirty"&&e.dirty(),n.push(o.value)}return{status:e.value,value:n}}static async mergeObjectAsync(e,r){const n=[];for(const o of r){const a=await o.key,i=await o.value;n.push({key:a,value:i})}return _e.mergeObjectSync(e,n)}static mergeObjectSync(e,r){const n={};for(const o of r){const{key:a,value:i}=o;if(a.status==="aborted"||i.status==="aborted")return T;a.status==="dirty"&&e.dirty(),i.status==="dirty"&&e.dirty(),a.value!=="__proto__"&&(typeof i.value<"u"||o.alwaysSet)&&(n[a.value]=i.value)}return{status:e.value,value:n}}}const T=Object.freeze({status:"aborted"}),Er=t=>({status:"dirty",value:t}),Se=t=>({status:"valid",value:t}),rn=t=>t.status==="aborted",nn=t=>t.status==="dirty",yt=t=>t.status==="valid",Gt=t=>typeof Promise<"u"&&t instanceof Promise;function zr(t,e,r,n){if(typeof e=="function"?t!==e||!0:!e.has(t))throw new TypeError("Cannot read private member from an object whose class did not declare it");return e.get(t)}function No(t,e,r,n,o){if(typeof e=="function"?t!==e||!0:!e.has(t))throw new TypeError("Cannot write private member to an object whose class did not declare it");return e.set(t,r),r}var C;(function(t){t.errToObj=e=>typeof e=="string"?{message:e}:e||{},t.toString=e=>typeof e=="string"?e:e==null?void 0:e.message})(C||(C={}));var Ht,Wt;class He{constructor(e,r,n,o){this._cachedPath=[],this.parent=e,this.data=r,this._path=n,this._key=o}get path(){return this._cachedPath.length||(this._key instanceof Array?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const Gn=(t,e)=>{if(yt(e))return{success:!0,data:e.value};if(!t.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const r=new Te(t.common.issues);return this._error=r,this._error}}};function I(t){if(!t)return{};const{errorMap:e,invalid_type_error:r,required_error:n,description:o}=t;if(e&&(r||n))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return e?{errorMap:e,description:o}:{errorMap:(a,i)=>{var s,c;const{message:l}=t;return a.code==="invalid_enum_value"?{message:l??i.defaultError}:typeof i.data>"u"?{message:(s=l??n)!==null&&s!==void 0?s:i.defaultError}:a.code!=="invalid_type"?{message:i.defaultError}:{message:(c=l??r)!==null&&c!==void 0?c:i.defaultError}},description:o}}class L{get description(){return this._def.description}_getType(e){return Ye(e.data)}_getOrReturnCtx(e,r){return r||{common:e.parent.common,data:e.data,parsedType:Ye(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new _e,ctx:{common:e.parent.common,data:e.data,parsedType:Ye(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){const r=this._parse(e);if(Gt(r))throw new Error("Synchronous parse encountered promise.");return r}_parseAsync(e){const r=this._parse(e);return Promise.resolve(r)}parse(e,r){const n=this.safeParse(e,r);if(n.success)return n.data;throw n.error}safeParse(e,r){var n;const o={common:{issues:[],async:(n=r==null?void 0:r.async)!==null&&n!==void 0?n:!1,contextualErrorMap:r==null?void 0:r.errorMap},path:(r==null?void 0:r.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:Ye(e)},a=this._parseSync({data:e,path:o.path,parent:o});return Gn(o,a)}"~validate"(e){var r,n;const o={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:Ye(e)};if(!this["~standard"].async)try{const a=this._parseSync({data:e,path:[],parent:o});return yt(a)?{value:a.value}:{issues:o.common.issues}}catch(a){!((n=(r=a==null?void 0:a.message)===null||r===void 0?void 0:r.toLowerCase())===null||n===void 0)&&n.includes("encountered")&&(this["~standard"].async=!0),o.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:o}).then(a=>yt(a)?{value:a.value}:{issues:o.common.issues})}async parseAsync(e,r){const n=await this.safeParseAsync(e,r);if(n.success)return n.data;throw n.error}async safeParseAsync(e,r){const n={common:{issues:[],contextualErrorMap:r==null?void 0:r.errorMap,async:!0},path:(r==null?void 0:r.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:Ye(e)},o=this._parse({data:e,path:n.path,parent:n}),a=await(Gt(o)?o:Promise.resolve(o));return Gn(n,a)}refine(e,r){const n=o=>typeof r=="string"||typeof r>"u"?{message:r}:typeof r=="function"?r(o):r;return this._refinement((o,a)=>{const i=e(o),s=()=>a.addIssue({code:g.custom,...n(o)});return typeof Promise<"u"&&i instanceof Promise?i.then(c=>c?!0:(s(),!1)):i?!0:(s(),!1)})}refinement(e,r){return this._refinement((n,o)=>e(n)?!0:(o.addIssue(typeof r=="function"?r(n,o):r),!1))}_refinement(e){return new Oe({schema:this,typeName:$.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:r=>this["~validate"](r)}}optional(){return Ve.create(this,this._def)}nullable(){return lt.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return Le.create(this)}promise(){return Rt.create(this,this._def)}or(e){return Jt.create([this,e],this._def)}and(e){return Qt.create(this,e,this._def)}transform(e){return new Oe({...I(this._def),schema:this,typeName:$.ZodEffects,effect:{type:"transform",transform:e}})}default(e){const r=typeof e=="function"?e:()=>e;return new or({...I(this._def),innerType:this,defaultValue:r,typeName:$.ZodDefault})}brand(){return new yn({typeName:$.ZodBranded,type:this,...I(this._def)})}catch(e){const r=typeof e=="function"?e:()=>e;return new ar({...I(this._def),innerType:this,catchValue:r,typeName:$.ZodCatch})}describe(e){const r=this.constructor;return new r({...this._def,description:e})}pipe(e){return pr.create(this,e)}readonly(){return ir.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const yi=/^c[^\s-]{8,}$/i,xi=/^[0-9a-z]+$/,_i=/^[0-9A-HJKMNP-TV-Z]{26}$/i,ki=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,Ci=/^[a-z0-9_-]{21}$/i,Si=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,Ni=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,Ei=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,zi="^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$";let Wr;const Mi=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,$i=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,Ti=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,Ai=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,Pi=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,Ri=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,Eo="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",Ii=new RegExp(`^${Eo}$`);function zo(t){let e="[0-5]\\d";t.precision?e=`${e}\\.\\d{${t.precision}}`:t.precision==null&&(e=`${e}(\\.\\d+)?`);const r=t.precision?"+":"?";return`([01]\\d|2[0-3]):[0-5]\\d(:${e})${r}`}function Li(t){return new RegExp(`^${zo(t)}$`)}function Mo(t){let e=`${Eo}T${zo(t)}`;const r=[];return r.push(t.local?"Z?":"Z"),t.offset&&r.push("([+-]\\d{2}:?\\d{2})"),e=`${e}(${r.join("|")})`,new RegExp(`^${e}$`)}function Oi(t,e){return!!((e==="v4"||!e)&&Mi.test(t)||(e==="v6"||!e)&&Ti.test(t))}function Fi(t,e){if(!Si.test(t))return!1;try{const[r]=t.split("."),n=r.replace(/-/g,"+").replace(/_/g,"/").padEnd(r.length+(4-r.length%4)%4,"="),o=JSON.parse(atob(n));return!(typeof o!="object"||o===null||!o.typ||!o.alg||e&&o.alg!==e)}catch{return!1}}function ji(t,e){return!!((e==="v4"||!e)&&$i.test(t)||(e==="v6"||!e)&&Ai.test(t))}class Ie extends L{_parse(e){if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==b.string){const o=this._getOrReturnCtx(e);return v(o,{code:g.invalid_type,expected:b.string,received:o.parsedType}),T}const r=new _e;let n;for(const o of this._def.checks)if(o.kind==="min")e.data.length<o.value&&(n=this._getOrReturnCtx(e,n),v(n,{code:g.too_small,minimum:o.value,type:"string",inclusive:!0,exact:!1,message:o.message}),r.dirty());else if(o.kind==="max")e.data.length>o.value&&(n=this._getOrReturnCtx(e,n),v(n,{code:g.too_big,maximum:o.value,type:"string",inclusive:!0,exact:!1,message:o.message}),r.dirty());else if(o.kind==="length"){const a=e.data.length>o.value,i=e.data.length<o.value;(a||i)&&(n=this._getOrReturnCtx(e,n),a?v(n,{code:g.too_big,maximum:o.value,type:"string",inclusive:!0,exact:!0,message:o.message}):i&&v(n,{code:g.too_small,minimum:o.value,type:"string",inclusive:!0,exact:!0,message:o.message}),r.dirty())}else if(o.kind==="email")Ei.test(e.data)||(n=this._getOrReturnCtx(e,n),v(n,{validation:"email",code:g.invalid_string,message:o.message}),r.dirty());else if(o.kind==="emoji")Wr||(Wr=new RegExp(zi,"u")),Wr.test(e.data)||(n=this._getOrReturnCtx(e,n),v(n,{validation:"emoji",code:g.invalid_string,message:o.message}),r.dirty());else if(o.kind==="uuid")ki.test(e.data)||(n=this._getOrReturnCtx(e,n),v(n,{validation:"uuid",code:g.invalid_string,message:o.message}),r.dirty());else if(o.kind==="nanoid")Ci.test(e.data)||(n=this._getOrReturnCtx(e,n),v(n,{validation:"nanoid",code:g.invalid_string,message:o.message}),r.dirty());else if(o.kind==="cuid")yi.test(e.data)||(n=this._getOrReturnCtx(e,n),v(n,{validation:"cuid",code:g.invalid_string,message:o.message}),r.dirty());else if(o.kind==="cuid2")xi.test(e.data)||(n=this._getOrReturnCtx(e,n),v(n,{validation:"cuid2",code:g.invalid_string,message:o.message}),r.dirty());else if(o.kind==="ulid")_i.test(e.data)||(n=this._getOrReturnCtx(e,n),v(n,{validation:"ulid",code:g.invalid_string,message:o.message}),r.dirty());else if(o.kind==="url")try{new URL(e.data)}catch{n=this._getOrReturnCtx(e,n),v(n,{validation:"url",code:g.invalid_string,message:o.message}),r.dirty()}else o.kind==="regex"?(o.regex.lastIndex=0,o.regex.test(e.data)||(n=this._getOrReturnCtx(e,n),v(n,{validation:"regex",code:g.invalid_string,message:o.message}),r.dirty())):o.kind==="trim"?e.data=e.data.trim():o.kind==="includes"?e.data.includes(o.value,o.position)||(n=this._getOrReturnCtx(e,n),v(n,{code:g.invalid_string,validation:{includes:o.value,position:o.position},message:o.message}),r.dirty()):o.kind==="toLowerCase"?e.data=e.data.toLowerCase():o.kind==="toUpperCase"?e.data=e.data.toUpperCase():o.kind==="startsWith"?e.data.startsWith(o.value)||(n=this._getOrReturnCtx(e,n),v(n,{code:g.invalid_string,validation:{startsWith:o.value},message:o.message}),r.dirty()):o.kind==="endsWith"?e.data.endsWith(o.value)||(n=this._getOrReturnCtx(e,n),v(n,{code:g.invalid_string,validation:{endsWith:o.value},message:o.message}),r.dirty()):o.kind==="datetime"?Mo(o).test(e.data)||(n=this._getOrReturnCtx(e,n),v(n,{code:g.invalid_string,validation:"datetime",message:o.message}),r.dirty()):o.kind==="date"?Ii.test(e.data)||(n=this._getOrReturnCtx(e,n),v(n,{code:g.invalid_string,validation:"date",message:o.message}),r.dirty()):o.kind==="time"?Li(o).test(e.data)||(n=this._getOrReturnCtx(e,n),v(n,{code:g.invalid_string,validation:"time",message:o.message}),r.dirty()):o.kind==="duration"?Ni.test(e.data)||(n=this._getOrReturnCtx(e,n),v(n,{validation:"duration",code:g.invalid_string,message:o.message}),r.dirty()):o.kind==="ip"?Oi(e.data,o.version)||(n=this._getOrReturnCtx(e,n),v(n,{validation:"ip",code:g.invalid_string,message:o.message}),r.dirty()):o.kind==="jwt"?Fi(e.data,o.alg)||(n=this._getOrReturnCtx(e,n),v(n,{validation:"jwt",code:g.invalid_string,message:o.message}),r.dirty()):o.kind==="cidr"?ji(e.data,o.version)||(n=this._getOrReturnCtx(e,n),v(n,{validation:"cidr",code:g.invalid_string,message:o.message}),r.dirty()):o.kind==="base64"?Pi.test(e.data)||(n=this._getOrReturnCtx(e,n),v(n,{validation:"base64",code:g.invalid_string,message:o.message}),r.dirty()):o.kind==="base64url"?Ri.test(e.data)||(n=this._getOrReturnCtx(e,n),v(n,{validation:"base64url",code:g.invalid_string,message:o.message}),r.dirty()):H.assertNever(o);return{status:r.value,value:e.data}}_regex(e,r,n){return this.refinement(o=>e.test(o),{validation:r,code:g.invalid_string,...C.errToObj(n)})}_addCheck(e){return new Ie({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...C.errToObj(e)})}url(e){return this._addCheck({kind:"url",...C.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...C.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...C.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...C.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...C.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...C.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...C.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...C.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...C.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...C.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...C.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...C.errToObj(e)})}datetime(e){var r,n;return typeof e=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:typeof(e==null?void 0:e.precision)>"u"?null:e==null?void 0:e.precision,offset:(r=e==null?void 0:e.offset)!==null&&r!==void 0?r:!1,local:(n=e==null?void 0:e.local)!==null&&n!==void 0?n:!1,...C.errToObj(e==null?void 0:e.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return typeof e=="string"?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:typeof(e==null?void 0:e.precision)>"u"?null:e==null?void 0:e.precision,...C.errToObj(e==null?void 0:e.message)})}duration(e){return this._addCheck({kind:"duration",...C.errToObj(e)})}regex(e,r){return this._addCheck({kind:"regex",regex:e,...C.errToObj(r)})}includes(e,r){return this._addCheck({kind:"includes",value:e,position:r==null?void 0:r.position,...C.errToObj(r==null?void 0:r.message)})}startsWith(e,r){return this._addCheck({kind:"startsWith",value:e,...C.errToObj(r)})}endsWith(e,r){return this._addCheck({kind:"endsWith",value:e,...C.errToObj(r)})}min(e,r){return this._addCheck({kind:"min",value:e,...C.errToObj(r)})}max(e,r){return this._addCheck({kind:"max",value:e,...C.errToObj(r)})}length(e,r){return this._addCheck({kind:"length",value:e,...C.errToObj(r)})}nonempty(e){return this.min(1,C.errToObj(e))}trim(){return new Ie({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new Ie({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new Ie({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>e.kind==="datetime")}get isDate(){return!!this._def.checks.find(e=>e.kind==="date")}get isTime(){return!!this._def.checks.find(e=>e.kind==="time")}get isDuration(){return!!this._def.checks.find(e=>e.kind==="duration")}get isEmail(){return!!this._def.checks.find(e=>e.kind==="email")}get isURL(){return!!this._def.checks.find(e=>e.kind==="url")}get isEmoji(){return!!this._def.checks.find(e=>e.kind==="emoji")}get isUUID(){return!!this._def.checks.find(e=>e.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(e=>e.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(e=>e.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(e=>e.kind==="cuid2")}get isULID(){return!!this._def.checks.find(e=>e.kind==="ulid")}get isIP(){return!!this._def.checks.find(e=>e.kind==="ip")}get isCIDR(){return!!this._def.checks.find(e=>e.kind==="cidr")}get isBase64(){return!!this._def.checks.find(e=>e.kind==="base64")}get isBase64url(){return!!this._def.checks.find(e=>e.kind==="base64url")}get minLength(){let e=null;for(const r of this._def.checks)r.kind==="min"&&(e===null||r.value>e)&&(e=r.value);return e}get maxLength(){let e=null;for(const r of this._def.checks)r.kind==="max"&&(e===null||r.value<e)&&(e=r.value);return e}}Ie.create=t=>{var e;return new Ie({checks:[],typeName:$.ZodString,coerce:(e=t==null?void 0:t.coerce)!==null&&e!==void 0?e:!1,...I(t)})};function Bi(t,e){const r=(t.toString().split(".")[1]||"").length,n=(e.toString().split(".")[1]||"").length,o=r>n?r:n,a=parseInt(t.toFixed(o).replace(".","")),i=parseInt(e.toFixed(o).replace(".",""));return a%i/Math.pow(10,o)}class it extends L{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==b.number){const o=this._getOrReturnCtx(e);return v(o,{code:g.invalid_type,expected:b.number,received:o.parsedType}),T}let r;const n=new _e;for(const o of this._def.checks)o.kind==="int"?H.isInteger(e.data)||(r=this._getOrReturnCtx(e,r),v(r,{code:g.invalid_type,expected:"integer",received:"float",message:o.message}),n.dirty()):o.kind==="min"?(o.inclusive?e.data<o.value:e.data<=o.value)&&(r=this._getOrReturnCtx(e,r),v(r,{code:g.too_small,minimum:o.value,type:"number",inclusive:o.inclusive,exact:!1,message:o.message}),n.dirty()):o.kind==="max"?(o.inclusive?e.data>o.value:e.data>=o.value)&&(r=this._getOrReturnCtx(e,r),v(r,{code:g.too_big,maximum:o.value,type:"number",inclusive:o.inclusive,exact:!1,message:o.message}),n.dirty()):o.kind==="multipleOf"?Bi(e.data,o.value)!==0&&(r=this._getOrReturnCtx(e,r),v(r,{code:g.not_multiple_of,multipleOf:o.value,message:o.message}),n.dirty()):o.kind==="finite"?Number.isFinite(e.data)||(r=this._getOrReturnCtx(e,r),v(r,{code:g.not_finite,message:o.message}),n.dirty()):H.assertNever(o);return{status:n.value,value:e.data}}gte(e,r){return this.setLimit("min",e,!0,C.toString(r))}gt(e,r){return this.setLimit("min",e,!1,C.toString(r))}lte(e,r){return this.setLimit("max",e,!0,C.toString(r))}lt(e,r){return this.setLimit("max",e,!1,C.toString(r))}setLimit(e,r,n,o){return new it({...this._def,checks:[...this._def.checks,{kind:e,value:r,inclusive:n,message:C.toString(o)}]})}_addCheck(e){return new it({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:C.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:C.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:C.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:C.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:C.toString(e)})}multipleOf(e,r){return this._addCheck({kind:"multipleOf",value:e,message:C.toString(r)})}finite(e){return this._addCheck({kind:"finite",message:C.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:C.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:C.toString(e)})}get minValue(){let e=null;for(const r of this._def.checks)r.kind==="min"&&(e===null||r.value>e)&&(e=r.value);return e}get maxValue(){let e=null;for(const r of this._def.checks)r.kind==="max"&&(e===null||r.value<e)&&(e=r.value);return e}get isInt(){return!!this._def.checks.find(e=>e.kind==="int"||e.kind==="multipleOf"&&H.isInteger(e.value))}get isFinite(){let e=null,r=null;for(const n of this._def.checks){if(n.kind==="finite"||n.kind==="int"||n.kind==="multipleOf")return!0;n.kind==="min"?(r===null||n.value>r)&&(r=n.value):n.kind==="max"&&(e===null||n.value<e)&&(e=n.value)}return Number.isFinite(r)&&Number.isFinite(e)}}it.create=t=>new it({checks:[],typeName:$.ZodNumber,coerce:(t==null?void 0:t.coerce)||!1,...I(t)});class st extends L{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==b.bigint)return this._getInvalidInput(e);let r;const n=new _e;for(const o of this._def.checks)o.kind==="min"?(o.inclusive?e.data<o.value:e.data<=o.value)&&(r=this._getOrReturnCtx(e,r),v(r,{code:g.too_small,type:"bigint",minimum:o.value,inclusive:o.inclusive,message:o.message}),n.dirty()):o.kind==="max"?(o.inclusive?e.data>o.value:e.data>=o.value)&&(r=this._getOrReturnCtx(e,r),v(r,{code:g.too_big,type:"bigint",maximum:o.value,inclusive:o.inclusive,message:o.message}),n.dirty()):o.kind==="multipleOf"?e.data%o.value!==BigInt(0)&&(r=this._getOrReturnCtx(e,r),v(r,{code:g.not_multiple_of,multipleOf:o.value,message:o.message}),n.dirty()):H.assertNever(o);return{status:n.value,value:e.data}}_getInvalidInput(e){const r=this._getOrReturnCtx(e);return v(r,{code:g.invalid_type,expected:b.bigint,received:r.parsedType}),T}gte(e,r){return this.setLimit("min",e,!0,C.toString(r))}gt(e,r){return this.setLimit("min",e,!1,C.toString(r))}lte(e,r){return this.setLimit("max",e,!0,C.toString(r))}lt(e,r){return this.setLimit("max",e,!1,C.toString(r))}setLimit(e,r,n,o){return new st({...this._def,checks:[...this._def.checks,{kind:e,value:r,inclusive:n,message:C.toString(o)}]})}_addCheck(e){return new st({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:C.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:C.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:C.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:C.toString(e)})}multipleOf(e,r){return this._addCheck({kind:"multipleOf",value:e,message:C.toString(r)})}get minValue(){let e=null;for(const r of this._def.checks)r.kind==="min"&&(e===null||r.value>e)&&(e=r.value);return e}get maxValue(){let e=null;for(const r of this._def.checks)r.kind==="max"&&(e===null||r.value<e)&&(e=r.value);return e}}st.create=t=>{var e;return new st({checks:[],typeName:$.ZodBigInt,coerce:(e=t==null?void 0:t.coerce)!==null&&e!==void 0?e:!1,...I(t)})};class Yt extends L{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==b.boolean){const r=this._getOrReturnCtx(e);return v(r,{code:g.invalid_type,expected:b.boolean,received:r.parsedType}),T}return Se(e.data)}}Yt.create=t=>new Yt({typeName:$.ZodBoolean,coerce:(t==null?void 0:t.coerce)||!1,...I(t)});class xt extends L{_parse(e){if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==b.date){const o=this._getOrReturnCtx(e);return v(o,{code:g.invalid_type,expected:b.date,received:o.parsedType}),T}if(isNaN(e.data.getTime())){const o=this._getOrReturnCtx(e);return v(o,{code:g.invalid_date}),T}const r=new _e;let n;for(const o of this._def.checks)o.kind==="min"?e.data.getTime()<o.value&&(n=this._getOrReturnCtx(e,n),v(n,{code:g.too_small,message:o.message,inclusive:!0,exact:!1,minimum:o.value,type:"date"}),r.dirty()):o.kind==="max"?e.data.getTime()>o.value&&(n=this._getOrReturnCtx(e,n),v(n,{code:g.too_big,message:o.message,inclusive:!0,exact:!1,maximum:o.value,type:"date"}),r.dirty()):H.assertNever(o);return{status:r.value,value:new Date(e.data.getTime())}}_addCheck(e){return new xt({...this._def,checks:[...this._def.checks,e]})}min(e,r){return this._addCheck({kind:"min",value:e.getTime(),message:C.toString(r)})}max(e,r){return this._addCheck({kind:"max",value:e.getTime(),message:C.toString(r)})}get minDate(){let e=null;for(const r of this._def.checks)r.kind==="min"&&(e===null||r.value>e)&&(e=r.value);return e!=null?new Date(e):null}get maxDate(){let e=null;for(const r of this._def.checks)r.kind==="max"&&(e===null||r.value<e)&&(e=r.value);return e!=null?new Date(e):null}}xt.create=t=>new xt({checks:[],coerce:(t==null?void 0:t.coerce)||!1,typeName:$.ZodDate,...I(t)});class Mr extends L{_parse(e){if(this._getType(e)!==b.symbol){const r=this._getOrReturnCtx(e);return v(r,{code:g.invalid_type,expected:b.symbol,received:r.parsedType}),T}return Se(e.data)}}Mr.create=t=>new Mr({typeName:$.ZodSymbol,...I(t)});class Kt extends L{_parse(e){if(this._getType(e)!==b.undefined){const r=this._getOrReturnCtx(e);return v(r,{code:g.invalid_type,expected:b.undefined,received:r.parsedType}),T}return Se(e.data)}}Kt.create=t=>new Kt({typeName:$.ZodUndefined,...I(t)});class Xt extends L{_parse(e){if(this._getType(e)!==b.null){const r=this._getOrReturnCtx(e);return v(r,{code:g.invalid_type,expected:b.null,received:r.parsedType}),T}return Se(e.data)}}Xt.create=t=>new Xt({typeName:$.ZodNull,...I(t)});class Pt extends L{constructor(){super(...arguments),this._any=!0}_parse(e){return Se(e.data)}}Pt.create=t=>new Pt({typeName:$.ZodAny,...I(t)});class bt extends L{constructor(){super(...arguments),this._unknown=!0}_parse(e){return Se(e.data)}}bt.create=t=>new bt({typeName:$.ZodUnknown,...I(t)});class Ke extends L{_parse(e){const r=this._getOrReturnCtx(e);return v(r,{code:g.invalid_type,expected:b.never,received:r.parsedType}),T}}Ke.create=t=>new Ke({typeName:$.ZodNever,...I(t)});class $r extends L{_parse(e){if(this._getType(e)!==b.undefined){const r=this._getOrReturnCtx(e);return v(r,{code:g.invalid_type,expected:b.void,received:r.parsedType}),T}return Se(e.data)}}$r.create=t=>new $r({typeName:$.ZodVoid,...I(t)});class Le extends L{_parse(e){const{ctx:r,status:n}=this._processInputParams(e),o=this._def;if(r.parsedType!==b.array)return v(r,{code:g.invalid_type,expected:b.array,received:r.parsedType}),T;if(o.exactLength!==null){const i=r.data.length>o.exactLength.value,s=r.data.length<o.exactLength.value;(i||s)&&(v(r,{code:i?g.too_big:g.too_small,minimum:s?o.exactLength.value:void 0,maximum:i?o.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:o.exactLength.message}),n.dirty())}if(o.minLength!==null&&r.data.length<o.minLength.value&&(v(r,{code:g.too_small,minimum:o.minLength.value,type:"array",inclusive:!0,exact:!1,message:o.minLength.message}),n.dirty()),o.maxLength!==null&&r.data.length>o.maxLength.value&&(v(r,{code:g.too_big,maximum:o.maxLength.value,type:"array",inclusive:!0,exact:!1,message:o.maxLength.message}),n.dirty()),r.common.async)return Promise.all([...r.data].map((i,s)=>o.type._parseAsync(new He(r,i,r.path,s)))).then(i=>_e.mergeArray(n,i));const a=[...r.data].map((i,s)=>o.type._parseSync(new He(r,i,r.path,s)));return _e.mergeArray(n,a)}get element(){return this._def.type}min(e,r){return new Le({...this._def,minLength:{value:e,message:C.toString(r)}})}max(e,r){return new Le({...this._def,maxLength:{value:e,message:C.toString(r)}})}length(e,r){return new Le({...this._def,exactLength:{value:e,message:C.toString(r)}})}nonempty(e){return this.min(1,e)}}Le.create=(t,e)=>new Le({type:t,minLength:null,maxLength:null,exactLength:null,typeName:$.ZodArray,...I(e)});function Et(t){if(t instanceof ce){const e={};for(const r in t.shape){const n=t.shape[r];e[r]=Ve.create(Et(n))}return new ce({...t._def,shape:()=>e})}else return t instanceof Le?new Le({...t._def,type:Et(t.element)}):t instanceof Ve?Ve.create(Et(t.unwrap())):t instanceof lt?lt.create(Et(t.unwrap())):t instanceof We?We.create(t.items.map(e=>Et(e))):t}class ce extends L{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;const e=this._def.shape(),r=H.objectKeys(e);return this._cached={shape:e,keys:r}}_parse(e){if(this._getType(e)!==b.object){const c=this._getOrReturnCtx(e);return v(c,{code:g.invalid_type,expected:b.object,received:c.parsedType}),T}const{status:r,ctx:n}=this._processInputParams(e),{shape:o,keys:a}=this._getCached(),i=[];if(!(this._def.catchall instanceof Ke&&this._def.unknownKeys==="strip"))for(const c in n.data)a.includes(c)||i.push(c);const s=[];for(const c of a){const l=o[c],u=n.data[c];s.push({key:{status:"valid",value:c},value:l._parse(new He(n,u,n.path,c)),alwaysSet:c in n.data})}if(this._def.catchall instanceof Ke){const c=this._def.unknownKeys;if(c==="passthrough")for(const l of i)s.push({key:{status:"valid",value:l},value:{status:"valid",value:n.data[l]}});else if(c==="strict")i.length>0&&(v(n,{code:g.unrecognized_keys,keys:i}),r.dirty());else if(c!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const c=this._def.catchall;for(const l of i){const u=n.data[l];s.push({key:{status:"valid",value:l},value:c._parse(new He(n,u,n.path,l)),alwaysSet:l in n.data})}}return n.common.async?Promise.resolve().then(async()=>{const c=[];for(const l of s){const u=await l.key,p=await l.value;c.push({key:u,value:p,alwaysSet:l.alwaysSet})}return c}).then(c=>_e.mergeObjectSync(r,c)):_e.mergeObjectSync(r,s)}get shape(){return this._def.shape()}strict(e){return C.errToObj,new ce({...this._def,unknownKeys:"strict",...e!==void 0?{errorMap:(r,n)=>{var o,a,i,s;const c=(i=(a=(o=this._def).errorMap)===null||a===void 0?void 0:a.call(o,r,n).message)!==null&&i!==void 0?i:n.defaultError;return r.code==="unrecognized_keys"?{message:(s=C.errToObj(e).message)!==null&&s!==void 0?s:c}:{message:c}}}:{}})}strip(){return new ce({...this._def,unknownKeys:"strip"})}passthrough(){return new ce({...this._def,unknownKeys:"passthrough"})}extend(e){return new ce({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new ce({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:$.ZodObject})}setKey(e,r){return this.augment({[e]:r})}catchall(e){return new ce({...this._def,catchall:e})}pick(e){const r={};return H.objectKeys(e).forEach(n=>{e[n]&&this.shape[n]&&(r[n]=this.shape[n])}),new ce({...this._def,shape:()=>r})}omit(e){const r={};return H.objectKeys(this.shape).forEach(n=>{e[n]||(r[n]=this.shape[n])}),new ce({...this._def,shape:()=>r})}deepPartial(){return Et(this)}partial(e){const r={};return H.objectKeys(this.shape).forEach(n=>{const o=this.shape[n];e&&!e[n]?r[n]=o:r[n]=o.optional()}),new ce({...this._def,shape:()=>r})}required(e){const r={};return H.objectKeys(this.shape).forEach(n=>{if(e&&!e[n])r[n]=this.shape[n];else{let o=this.shape[n];for(;o instanceof Ve;)o=o._def.innerType;r[n]=o}}),new ce({...this._def,shape:()=>r})}keyof(){return $o(H.objectKeys(this.shape))}}ce.create=(t,e)=>new ce({shape:()=>t,unknownKeys:"strip",catchall:Ke.create(),typeName:$.ZodObject,...I(e)});ce.strictCreate=(t,e)=>new ce({shape:()=>t,unknownKeys:"strict",catchall:Ke.create(),typeName:$.ZodObject,...I(e)});ce.lazycreate=(t,e)=>new ce({shape:t,unknownKeys:"strip",catchall:Ke.create(),typeName:$.ZodObject,...I(e)});class Jt extends L{_parse(e){const{ctx:r}=this._processInputParams(e),n=this._def.options;function o(a){for(const s of a)if(s.result.status==="valid")return s.result;for(const s of a)if(s.result.status==="dirty")return r.common.issues.push(...s.ctx.common.issues),s.result;const i=a.map(s=>new Te(s.ctx.common.issues));return v(r,{code:g.invalid_union,unionErrors:i}),T}if(r.common.async)return Promise.all(n.map(async a=>{const i={...r,common:{...r.common,issues:[]},parent:null};return{result:await a._parseAsync({data:r.data,path:r.path,parent:i}),ctx:i}})).then(o);{let a;const i=[];for(const c of n){const l={...r,common:{...r.common,issues:[]},parent:null},u=c._parseSync({data:r.data,path:r.path,parent:l});if(u.status==="valid")return u;u.status==="dirty"&&!a&&(a={result:u,ctx:l}),l.common.issues.length&&i.push(l.common.issues)}if(a)return r.common.issues.push(...a.ctx.common.issues),a.result;const s=i.map(c=>new Te(c));return v(r,{code:g.invalid_union,unionErrors:s}),T}}get options(){return this._def.options}}Jt.create=(t,e)=>new Jt({options:t,typeName:$.ZodUnion,...I(e)});const rt=t=>t instanceof tr?rt(t.schema):t instanceof Oe?rt(t.innerType()):t instanceof rr?[t.value]:t instanceof ct?t.options:t instanceof nr?H.objectValues(t.enum):t instanceof or?rt(t._def.innerType):t instanceof Kt?[void 0]:t instanceof Xt?[null]:t instanceof Ve?[void 0,...rt(t.unwrap())]:t instanceof lt?[null,...rt(t.unwrap())]:t instanceof yn||t instanceof ir?rt(t.unwrap()):t instanceof ar?rt(t._def.innerType):[];class Or extends L{_parse(e){const{ctx:r}=this._processInputParams(e);if(r.parsedType!==b.object)return v(r,{code:g.invalid_type,expected:b.object,received:r.parsedType}),T;const n=this.discriminator,o=r.data[n],a=this.optionsMap.get(o);return a?r.common.async?a._parseAsync({data:r.data,path:r.path,parent:r}):a._parseSync({data:r.data,path:r.path,parent:r}):(v(r,{code:g.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[n]}),T)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,r,n){const o=new Map;for(const a of r){const i=rt(a.shape[e]);if(!i.length)throw new Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(const s of i){if(o.has(s))throw new Error(`Discriminator property ${String(e)} has duplicate value ${String(s)}`);o.set(s,a)}}return new Or({typeName:$.ZodDiscriminatedUnion,discriminator:e,options:r,optionsMap:o,...I(n)})}}function on(t,e){const r=Ye(t),n=Ye(e);if(t===e)return{valid:!0,data:t};if(r===b.object&&n===b.object){const o=H.objectKeys(e),a=H.objectKeys(t).filter(s=>o.indexOf(s)!==-1),i={...t,...e};for(const s of a){const c=on(t[s],e[s]);if(!c.valid)return{valid:!1};i[s]=c.data}return{valid:!0,data:i}}else if(r===b.array&&n===b.array){if(t.length!==e.length)return{valid:!1};const o=[];for(let a=0;a<t.length;a++){const i=t[a],s=e[a],c=on(i,s);if(!c.valid)return{valid:!1};o.push(c.data)}return{valid:!0,data:o}}else return r===b.date&&n===b.date&&+t==+e?{valid:!0,data:t}:{valid:!1}}class Qt extends L{_parse(e){const{status:r,ctx:n}=this._processInputParams(e),o=(a,i)=>{if(rn(a)||rn(i))return T;const s=on(a.value,i.value);return s.valid?((nn(a)||nn(i))&&r.dirty(),{status:r.value,value:s.data}):(v(n,{code:g.invalid_intersection_types}),T)};return n.common.async?Promise.all([this._def.left._parseAsync({data:n.data,path:n.path,parent:n}),this._def.right._parseAsync({data:n.data,path:n.path,parent:n})]).then(([a,i])=>o(a,i)):o(this._def.left._parseSync({data:n.data,path:n.path,parent:n}),this._def.right._parseSync({data:n.data,path:n.path,parent:n}))}}Qt.create=(t,e,r)=>new Qt({left:t,right:e,typeName:$.ZodIntersection,...I(r)});class We extends L{_parse(e){const{status:r,ctx:n}=this._processInputParams(e);if(n.parsedType!==b.array)return v(n,{code:g.invalid_type,expected:b.array,received:n.parsedType}),T;if(n.data.length<this._def.items.length)return v(n,{code:g.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),T;!this._def.rest&&n.data.length>this._def.items.length&&(v(n,{code:g.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),r.dirty());const o=[...n.data].map((a,i)=>{const s=this._def.items[i]||this._def.rest;return s?s._parse(new He(n,a,n.path,i)):null}).filter(a=>!!a);return n.common.async?Promise.all(o).then(a=>_e.mergeArray(r,a)):_e.mergeArray(r,o)}get items(){return this._def.items}rest(e){return new We({...this._def,rest:e})}}We.create=(t,e)=>{if(!Array.isArray(t))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new We({items:t,typeName:$.ZodTuple,rest:null,...I(e)})};class er extends L{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:r,ctx:n}=this._processInputParams(e);if(n.parsedType!==b.object)return v(n,{code:g.invalid_type,expected:b.object,received:n.parsedType}),T;const o=[],a=this._def.keyType,i=this._def.valueType;for(const s in n.data)o.push({key:a._parse(new He(n,s,n.path,s)),value:i._parse(new He(n,n.data[s],n.path,s)),alwaysSet:s in n.data});return n.common.async?_e.mergeObjectAsync(r,o):_e.mergeObjectSync(r,o)}get element(){return this._def.valueType}static create(e,r,n){return r instanceof L?new er({keyType:e,valueType:r,typeName:$.ZodRecord,...I(n)}):new er({keyType:Ie.create(),valueType:e,typeName:$.ZodRecord,...I(r)})}}class Tr extends L{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:r,ctx:n}=this._processInputParams(e);if(n.parsedType!==b.map)return v(n,{code:g.invalid_type,expected:b.map,received:n.parsedType}),T;const o=this._def.keyType,a=this._def.valueType,i=[...n.data.entries()].map(([s,c],l)=>({key:o._parse(new He(n,s,n.path,[l,"key"])),value:a._parse(new He(n,c,n.path,[l,"value"]))}));if(n.common.async){const s=new Map;return Promise.resolve().then(async()=>{for(const c of i){const l=await c.key,u=await c.value;if(l.status==="aborted"||u.status==="aborted")return T;(l.status==="dirty"||u.status==="dirty")&&r.dirty(),s.set(l.value,u.value)}return{status:r.value,value:s}})}else{const s=new Map;for(const c of i){const l=c.key,u=c.value;if(l.status==="aborted"||u.status==="aborted")return T;(l.status==="dirty"||u.status==="dirty")&&r.dirty(),s.set(l.value,u.value)}return{status:r.value,value:s}}}}Tr.create=(t,e,r)=>new Tr({valueType:e,keyType:t,typeName:$.ZodMap,...I(r)});class _t extends L{_parse(e){const{status:r,ctx:n}=this._processInputParams(e);if(n.parsedType!==b.set)return v(n,{code:g.invalid_type,expected:b.set,received:n.parsedType}),T;const o=this._def;o.minSize!==null&&n.data.size<o.minSize.value&&(v(n,{code:g.too_small,minimum:o.minSize.value,type:"set",inclusive:!0,exact:!1,message:o.minSize.message}),r.dirty()),o.maxSize!==null&&n.data.size>o.maxSize.value&&(v(n,{code:g.too_big,maximum:o.maxSize.value,type:"set",inclusive:!0,exact:!1,message:o.maxSize.message}),r.dirty());const a=this._def.valueType;function i(c){const l=new Set;for(const u of c){if(u.status==="aborted")return T;u.status==="dirty"&&r.dirty(),l.add(u.value)}return{status:r.value,value:l}}const s=[...n.data.values()].map((c,l)=>a._parse(new He(n,c,n.path,l)));return n.common.async?Promise.all(s).then(c=>i(c)):i(s)}min(e,r){return new _t({...this._def,minSize:{value:e,message:C.toString(r)}})}max(e,r){return new _t({...this._def,maxSize:{value:e,message:C.toString(r)}})}size(e,r){return this.min(e,r).max(e,r)}nonempty(e){return this.min(1,e)}}_t.create=(t,e)=>new _t({valueType:t,minSize:null,maxSize:null,typeName:$.ZodSet,...I(e)});class zt extends L{constructor(){super(...arguments),this.validate=this.implement}_parse(e){const{ctx:r}=this._processInputParams(e);if(r.parsedType!==b.function)return v(r,{code:g.invalid_type,expected:b.function,received:r.parsedType}),T;function n(s,c){return Nr({data:s,path:r.path,errorMaps:[r.common.contextualErrorMap,r.schemaErrorMap,Sr(),At].filter(l=>!!l),issueData:{code:g.invalid_arguments,argumentsError:c}})}function o(s,c){return Nr({data:s,path:r.path,errorMaps:[r.common.contextualErrorMap,r.schemaErrorMap,Sr(),At].filter(l=>!!l),issueData:{code:g.invalid_return_type,returnTypeError:c}})}const a={errorMap:r.common.contextualErrorMap},i=r.data;if(this._def.returns instanceof Rt){const s=this;return Se(async function(...c){const l=new Te([]),u=await s._def.args.parseAsync(c,a).catch(h=>{throw l.addIssue(n(c,h)),l}),p=await Reflect.apply(i,this,u);return await s._def.returns._def.type.parseAsync(p,a).catch(h=>{throw l.addIssue(o(p,h)),l})})}else{const s=this;return Se(function(...c){const l=s._def.args.safeParse(c,a);if(!l.success)throw new Te([n(c,l.error)]);const u=Reflect.apply(i,this,l.data),p=s._def.returns.safeParse(u,a);if(!p.success)throw new Te([o(u,p.error)]);return p.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new zt({...this._def,args:We.create(e).rest(bt.create())})}returns(e){return new zt({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,r,n){return new zt({args:e||We.create([]).rest(bt.create()),returns:r||bt.create(),typeName:$.ZodFunction,...I(n)})}}class tr extends L{get schema(){return this._def.getter()}_parse(e){const{ctx:r}=this._processInputParams(e);return this._def.getter()._parse({data:r.data,path:r.path,parent:r})}}tr.create=(t,e)=>new tr({getter:t,typeName:$.ZodLazy,...I(e)});class rr extends L{_parse(e){if(e.data!==this._def.value){const r=this._getOrReturnCtx(e);return v(r,{received:r.data,code:g.invalid_literal,expected:this._def.value}),T}return{status:"valid",value:e.data}}get value(){return this._def.value}}rr.create=(t,e)=>new rr({value:t,typeName:$.ZodLiteral,...I(e)});function $o(t,e){return new ct({values:t,typeName:$.ZodEnum,...I(e)})}class ct extends L{constructor(){super(...arguments),Ht.set(this,void 0)}_parse(e){if(typeof e.data!="string"){const r=this._getOrReturnCtx(e),n=this._def.values;return v(r,{expected:H.joinValues(n),received:r.parsedType,code:g.invalid_type}),T}if(zr(this,Ht)||No(this,Ht,new Set(this._def.values)),!zr(this,Ht).has(e.data)){const r=this._getOrReturnCtx(e),n=this._def.values;return v(r,{received:r.data,code:g.invalid_enum_value,options:n}),T}return Se(e.data)}get options(){return this._def.values}get enum(){const e={};for(const r of this._def.values)e[r]=r;return e}get Values(){const e={};for(const r of this._def.values)e[r]=r;return e}get Enum(){const e={};for(const r of this._def.values)e[r]=r;return e}extract(e,r=this._def){return ct.create(e,{...this._def,...r})}exclude(e,r=this._def){return ct.create(this.options.filter(n=>!e.includes(n)),{...this._def,...r})}}Ht=new WeakMap;ct.create=$o;class nr extends L{constructor(){super(...arguments),Wt.set(this,void 0)}_parse(e){const r=H.getValidEnumValues(this._def.values),n=this._getOrReturnCtx(e);if(n.parsedType!==b.string&&n.parsedType!==b.number){const o=H.objectValues(r);return v(n,{expected:H.joinValues(o),received:n.parsedType,code:g.invalid_type}),T}if(zr(this,Wt)||No(this,Wt,new Set(H.getValidEnumValues(this._def.values))),!zr(this,Wt).has(e.data)){const o=H.objectValues(r);return v(n,{received:n.data,code:g.invalid_enum_value,options:o}),T}return Se(e.data)}get enum(){return this._def.values}}Wt=new WeakMap;nr.create=(t,e)=>new nr({values:t,typeName:$.ZodNativeEnum,...I(e)});class Rt extends L{unwrap(){return this._def.type}_parse(e){const{ctx:r}=this._processInputParams(e);if(r.parsedType!==b.promise&&r.common.async===!1)return v(r,{code:g.invalid_type,expected:b.promise,received:r.parsedType}),T;const n=r.parsedType===b.promise?r.data:Promise.resolve(r.data);return Se(n.then(o=>this._def.type.parseAsync(o,{path:r.path,errorMap:r.common.contextualErrorMap})))}}Rt.create=(t,e)=>new Rt({type:t,typeName:$.ZodPromise,...I(e)});class Oe extends L{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===$.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){const{status:r,ctx:n}=this._processInputParams(e),o=this._def.effect||null,a={addIssue:i=>{v(n,i),i.fatal?r.abort():r.dirty()},get path(){return n.path}};if(a.addIssue=a.addIssue.bind(a),o.type==="preprocess"){const i=o.transform(n.data,a);if(n.common.async)return Promise.resolve(i).then(async s=>{if(r.value==="aborted")return T;const c=await this._def.schema._parseAsync({data:s,path:n.path,parent:n});return c.status==="aborted"?T:c.status==="dirty"||r.value==="dirty"?Er(c.value):c});{if(r.value==="aborted")return T;const s=this._def.schema._parseSync({data:i,path:n.path,parent:n});return s.status==="aborted"?T:s.status==="dirty"||r.value==="dirty"?Er(s.value):s}}if(o.type==="refinement"){const i=s=>{const c=o.refinement(s,a);if(n.common.async)return Promise.resolve(c);if(c instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return s};if(n.common.async===!1){const s=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});return s.status==="aborted"?T:(s.status==="dirty"&&r.dirty(),i(s.value),{status:r.value,value:s.value})}else return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(s=>s.status==="aborted"?T:(s.status==="dirty"&&r.dirty(),i(s.value).then(()=>({status:r.value,value:s.value}))))}if(o.type==="transform")if(n.common.async===!1){const i=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});if(!yt(i))return i;const s=o.transform(i.value,a);if(s instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:r.value,value:s}}else return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(i=>yt(i)?Promise.resolve(o.transform(i.value,a)).then(s=>({status:r.value,value:s})):i);H.assertNever(o)}}Oe.create=(t,e,r)=>new Oe({schema:t,typeName:$.ZodEffects,effect:e,...I(r)});Oe.createWithPreprocess=(t,e,r)=>new Oe({schema:e,effect:{type:"preprocess",transform:t},typeName:$.ZodEffects,...I(r)});class Ve extends L{_parse(e){return this._getType(e)===b.undefined?Se(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}Ve.create=(t,e)=>new Ve({innerType:t,typeName:$.ZodOptional,...I(e)});class lt extends L{_parse(e){return this._getType(e)===b.null?Se(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}lt.create=(t,e)=>new lt({innerType:t,typeName:$.ZodNullable,...I(e)});class or extends L{_parse(e){const{ctx:r}=this._processInputParams(e);let n=r.data;return r.parsedType===b.undefined&&(n=this._def.defaultValue()),this._def.innerType._parse({data:n,path:r.path,parent:r})}removeDefault(){return this._def.innerType}}or.create=(t,e)=>new or({innerType:t,typeName:$.ZodDefault,defaultValue:typeof e.default=="function"?e.default:()=>e.default,...I(e)});class ar extends L{_parse(e){const{ctx:r}=this._processInputParams(e),n={...r,common:{...r.common,issues:[]}},o=this._def.innerType._parse({data:n.data,path:n.path,parent:{...n}});return Gt(o)?o.then(a=>({status:"valid",value:a.status==="valid"?a.value:this._def.catchValue({get error(){return new Te(n.common.issues)},input:n.data})})):{status:"valid",value:o.status==="valid"?o.value:this._def.catchValue({get error(){return new Te(n.common.issues)},input:n.data})}}removeCatch(){return this._def.innerType}}ar.create=(t,e)=>new ar({innerType:t,typeName:$.ZodCatch,catchValue:typeof e.catch=="function"?e.catch:()=>e.catch,...I(e)});class Ar extends L{_parse(e){if(this._getType(e)!==b.nan){const r=this._getOrReturnCtx(e);return v(r,{code:g.invalid_type,expected:b.nan,received:r.parsedType}),T}return{status:"valid",value:e.data}}}Ar.create=t=>new Ar({typeName:$.ZodNaN,...I(t)});const Di=Symbol("zod_brand");class yn extends L{_parse(e){const{ctx:r}=this._processInputParams(e),n=r.data;return this._def.type._parse({data:n,path:r.path,parent:r})}unwrap(){return this._def.type}}class pr extends L{_parse(e){const{status:r,ctx:n}=this._processInputParams(e);if(n.common.async)return(async()=>{const o=await this._def.in._parseAsync({data:n.data,path:n.path,parent:n});return o.status==="aborted"?T:o.status==="dirty"?(r.dirty(),Er(o.value)):this._def.out._parseAsync({data:o.value,path:n.path,parent:n})})();{const o=this._def.in._parseSync({data:n.data,path:n.path,parent:n});return o.status==="aborted"?T:o.status==="dirty"?(r.dirty(),{status:"dirty",value:o.value}):this._def.out._parseSync({data:o.value,path:n.path,parent:n})}}static create(e,r){return new pr({in:e,out:r,typeName:$.ZodPipeline})}}class ir extends L{_parse(e){const r=this._def.innerType._parse(e),n=o=>(yt(o)&&(o.value=Object.freeze(o.value)),o);return Gt(r)?r.then(o=>n(o)):n(r)}unwrap(){return this._def.innerType}}ir.create=(t,e)=>new ir({innerType:t,typeName:$.ZodReadonly,...I(e)});function Yn(t,e){const r=typeof t=="function"?t(e):typeof t=="string"?{message:t}:t;return typeof r=="string"?{message:r}:r}function To(t,e={},r){return t?Pt.create().superRefine((n,o)=>{var a,i;const s=t(n);if(s instanceof Promise)return s.then(c=>{var l,u;if(!c){const p=Yn(e,n),h=(u=(l=p.fatal)!==null&&l!==void 0?l:r)!==null&&u!==void 0?u:!0;o.addIssue({code:"custom",...p,fatal:h})}});if(!s){const c=Yn(e,n),l=(i=(a=c.fatal)!==null&&a!==void 0?a:r)!==null&&i!==void 0?i:!0;o.addIssue({code:"custom",...c,fatal:l})}}):Pt.create()}const Vi={object:ce.lazycreate};var $;(function(t){t.ZodString="ZodString",t.ZodNumber="ZodNumber",t.ZodNaN="ZodNaN",t.ZodBigInt="ZodBigInt",t.ZodBoolean="ZodBoolean",t.ZodDate="ZodDate",t.ZodSymbol="ZodSymbol",t.ZodUndefined="ZodUndefined",t.ZodNull="ZodNull",t.ZodAny="ZodAny",t.ZodUnknown="ZodUnknown",t.ZodNever="ZodNever",t.ZodVoid="ZodVoid",t.ZodArray="ZodArray",t.ZodObject="ZodObject",t.ZodUnion="ZodUnion",t.ZodDiscriminatedUnion="ZodDiscriminatedUnion",t.ZodIntersection="ZodIntersection",t.ZodTuple="ZodTuple",t.ZodRecord="ZodRecord",t.ZodMap="ZodMap",t.ZodSet="ZodSet",t.ZodFunction="ZodFunction",t.ZodLazy="ZodLazy",t.ZodLiteral="ZodLiteral",t.ZodEnum="ZodEnum",t.ZodEffects="ZodEffects",t.ZodNativeEnum="ZodNativeEnum",t.ZodOptional="ZodOptional",t.ZodNullable="ZodNullable",t.ZodDefault="ZodDefault",t.ZodCatch="ZodCatch",t.ZodPromise="ZodPromise",t.ZodBranded="ZodBranded",t.ZodPipeline="ZodPipeline",t.ZodReadonly="ZodReadonly"})($||($={}));const Hi=(t,e={message:`Input not instance of ${t.name}`})=>To(r=>r instanceof t,e),Ao=Ie.create,Po=it.create,Wi=Ar.create,Zi=st.create,Ro=Yt.create,qi=xt.create,Ui=Mr.create,Gi=Kt.create,Yi=Xt.create,Ki=Pt.create,Xi=bt.create,Ji=Ke.create,Qi=$r.create,es=Le.create,ts=ce.create,rs=ce.strictCreate,ns=Jt.create,os=Or.create,as=Qt.create,is=We.create,ss=er.create,cs=Tr.create,ls=_t.create,ds=zt.create,us=tr.create,ps=rr.create,ms=ct.create,hs=nr.create,gs=Rt.create,Kn=Oe.create,fs=Ve.create,ws=lt.create,vs=Oe.createWithPreprocess,bs=pr.create,ys=()=>Ao().optional(),xs=()=>Po().optional(),_s=()=>Ro().optional(),ks={string:t=>Ie.create({...t,coerce:!0}),number:t=>it.create({...t,coerce:!0}),boolean:t=>Yt.create({...t,coerce:!0}),bigint:t=>st.create({...t,coerce:!0}),date:t=>xt.create({...t,coerce:!0})},Cs=T;var Q=Object.freeze({__proto__:null,defaultErrorMap:At,setErrorMap:vi,getErrorMap:Sr,makeIssue:Nr,EMPTY_PATH:bi,addIssueToContext:v,ParseStatus:_e,INVALID:T,DIRTY:Er,OK:Se,isAborted:rn,isDirty:nn,isValid:yt,isAsync:Gt,get util(){return H},get objectUtil(){return tn},ZodParsedType:b,getParsedType:Ye,ZodType:L,datetimeRegex:Mo,ZodString:Ie,ZodNumber:it,ZodBigInt:st,ZodBoolean:Yt,ZodDate:xt,ZodSymbol:Mr,ZodUndefined:Kt,ZodNull:Xt,ZodAny:Pt,ZodUnknown:bt,ZodNever:Ke,ZodVoid:$r,ZodArray:Le,ZodObject:ce,ZodUnion:Jt,ZodDiscriminatedUnion:Or,ZodIntersection:Qt,ZodTuple:We,ZodRecord:er,ZodMap:Tr,ZodSet:_t,ZodFunction:zt,ZodLazy:tr,ZodLiteral:rr,ZodEnum:ct,ZodNativeEnum:nr,ZodPromise:Rt,ZodEffects:Oe,ZodTransformer:Oe,ZodOptional:Ve,ZodNullable:lt,ZodDefault:or,ZodCatch:ar,ZodNaN:Ar,BRAND:Di,ZodBranded:yn,ZodPipeline:pr,ZodReadonly:ir,custom:To,Schema:L,ZodSchema:L,late:Vi,get ZodFirstPartyTypeKind(){return $},coerce:ks,any:Ki,array:es,bigint:Zi,boolean:Ro,date:qi,discriminatedUnion:os,effect:Kn,enum:ms,function:ds,instanceof:Hi,intersection:as,lazy:us,literal:ps,map:cs,nan:Wi,nativeEnum:hs,never:Ji,null:Yi,nullable:ws,number:Po,object:ts,oboolean:_s,onumber:xs,optional:fs,ostring:ys,pipeline:bs,preprocess:vs,promise:gs,record:ss,set:ls,strictObject:rs,string:Ao,symbol:Ui,transformer:Kn,tuple:is,undefined:Gi,union:ns,unknown:Xi,void:Qi,NEVER:Cs,ZodIssueCode:g,quotelessJson:wi,ZodError:Te}),Ss=5746,Ns="/ping/stagewise",Es="stagewise",Io={server:{getSessionInfo:{request:Q.object({}),response:Q.object({sessionId:Q.string().optional(),appName:Q.string().describe('The name of the application, e.g. "VS Code" or "Cursor"'),displayName:Q.string().describe("Human-readable window identifier for UI display"),port:Q.number().describe("Port number this VS Code instance is running on")}),update:Q.object({})},triggerAgentPrompt:{request:Q.object({sessionId:Q.string().optional(),prompt:Q.string(),model:Q.string().optional().describe("The model to use for the agent prompt"),files:Q.array(Q.string()).optional().describe("Link project files to the agent prompt"),mode:Q.enum(["agent","ask","manual"]).optional().describe("The mode to use for the agent prompt"),images:Q.array(Q.string()).optional().describe("Upload files like images, videos, etc.")}),response:Q.object({sessionId:Q.string().optional(),result:Q.object({success:Q.boolean(),error:Q.string().optional(),errorCode:Q.enum(["session_mismatch"]).optional(),output:Q.string().optional()})}),update:Q.object({sessionId:Q.string().optional(),updateText:Q.string()})}}};const zs=2;async function Ms(t=10,e=300){const r=[];let n=0;for(let o=0;o<t;o++){const a=Ss+o;try{const i=new AbortController,s=setTimeout(()=>i.abort(),e),c=await fetch(`http://localhost:${a}${Ns}`,{signal:i.signal});if(clearTimeout(s),n=0,c.ok&&await c.text()===Es)try{const l=Co(`ws://localhost:${a}`,Io);await l.connect();const u=await l.call.getSessionInfo({},{onUpdate:()=>{}});r.push(u),await l.close()}catch(l){console.warn(`Failed to get session info from port ${a}:`,l)}else continue}catch{if(n++,n>=zs){console.warn("⬆️⬆️⬆️ Those two errors are expected! (Everything is fine, they are part of stagewise's discovery mechanism!) ✅");break}continue}}return r.length===0&&console.warn("No IDE windows found, please start an IDE with the stagewise extension installed! ❌"),r}const $s=()=>typeof window<"u"&&window.location&&window.location.port||"80",an=()=>`ide-selected-session-id-on-browser-port-${$s()}`,Xn=()=>{try{return localStorage.getItem(an())||void 0}catch{return}},wr=t=>{try{t?localStorage.setItem(an(),t):localStorage.removeItem(an())}catch{}},Lo=Re({windows:[],isDiscovering:!1,discoveryError:null,selectedSession:void 0,shouldPromptWindowSelection:!1,setShouldPromptWindowSelection:()=>{},discover:async()=>{},selectSession:()=>{},refreshSession:async()=>{},appName:void 0});function Ts({children:t}){const[e,r]=U([]),[n,o]=U(!1),[a,i]=U(null),[s,c]=U(Xn()),[l,u]=U(!1),p=async()=>{o(!0),i(null);try{const k=await Ms();r(k);const R=Xn();if(k.length===1){const O=k[0];(!R||R!==O.sessionId)&&(c(O.sessionId),wr(O.sessionId)),u(!1)}else{const O=k.length>1&&!R||R&&!k.some(G=>G.sessionId===R);u(O),O&&(c(void 0),wr(void 0))}}catch(k){i(k instanceof Error?k.message:"Failed to discover windows")}finally{o(!1)}},h=k=>{if(!k||k===""){wr(void 0),c(void 0);return}c(k),wr(k),k&&u(!1)},w=async()=>{s&&await p()};ne(()=>{p()},[]);const y=e.find(k=>k.sessionId===s),f={windows:e,isDiscovering:n,discoveryError:a,selectedSession:y,shouldPromptWindowSelection:l,setShouldPromptWindowSelection:u,discover:p,selectSession:h,refreshSession:w,appName:y==null?void 0:y.appName};return d(Lo.Provider,{value:f,children:t})}function dt(){return ke(Lo)}const Oo=Re({bridge:null,isConnecting:!1,error:null});function As({children:t}){const[e,r]=U({bridge:null,isConnecting:!0,error:null}),{selectedSession:n}=dt(),o=V(null),a=S(async i=>{o.current&&await o.current.close();try{const s=Co(`ws://localhost:${i}`,Io);await s.connect(),o.current=s,r({bridge:s,isConnecting:!1,error:null})}catch(s){o.current=null,r({bridge:null,isConnecting:!1,error:s instanceof Error?s:new Error(String(s))})}},[]);return ne(()=>{n&&a(n.port)},[n,a]),d(Oo.Provider,{value:e,children:t})}function Fo(){const t=ke(Oo);if(!t)throw new Error("useSRPCBridge must be used within an SRPCBridgeProvider");return t}const jo=Re({config:void 0});function Ps({children:t,config:e}){const r=le(()=>({config:e}),[e]);return d(jo.Provider,{value:r,children:t})}function Rs(){return ke(jo)}const Bo=Re({plugins:[],toolbarContext:{sendPrompt:()=>{}}});function Is({children:t}){const{bridge:e}=Fo(),{selectedSession:r}=dt(),{config:n}=Rs(),o=(n==null?void 0:n.plugins)||[],a=le(()=>({sendPrompt:async c=>{if(!e)throw new Error("No connection to the agent");return await e.call.triggerAgentPrompt(typeof c=="string"?{prompt:c,...r&&{sessionId:r.sessionId}}:{prompt:c.prompt,model:c.model,files:c.files,images:c.images,mode:c.mode,...r&&{sessionId:r.sessionId}},{onUpdate:l=>{}})}}),[e,r]),i=V(!1);ne(()=>{i.current||(i.current=!0,o.forEach(c=>{var l;(l=c.onLoad)==null||l.call(c,a)}))},[o,a]);const s=le(()=>({plugins:o,toolbarContext:a}),[o,a]);return d(Bo.Provider,{value:s,children:t})}function Fr(){return ke(Bo)}function Do(t){var e,r,n="";if(typeof t=="string"||typeof t=="number")n+=t;else if(typeof t=="object")if(Array.isArray(t)){var o=t.length;for(e=0;e<o;e++)t[e]&&(r=Do(t[e]))&&(n&&(n+=" "),n+=r)}else for(r in t)t[r]&&(n&&(n+=" "),n+=r);return n}function Ls(){for(var t,e,r=0,n="",o=arguments.length;r<o;r++)(t=arguments[r])&&(e=Do(t))&&(n&&(n+=" "),n+=e);return n}const xn="-",Os=t=>{const e=js(t),{conflictingClassGroups:r,conflictingClassGroupModifiers:n}=t;return{getClassGroupId:o=>{const a=o.split(xn);return a[0]===""&&a.length!==1&&a.shift(),Vo(a,e)||Fs(o)},getConflictingClassGroupIds:(o,a)=>{const i=r[o]||[];return a&&n[o]?[...i,...n[o]]:i}}},Vo=(t,e)=>{var r;if(t.length===0)return e.classGroupId;const n=t[0],o=e.nextPart.get(n),a=o?Vo(t.slice(1),o):void 0;if(a)return a;if(e.validators.length===0)return;const i=t.join(xn);return(r=e.validators.find(({validator:s})=>s(i)))==null?void 0:r.classGroupId},Jn=/^\[(.+)\]$/,Fs=t=>{if(Jn.test(t)){const e=Jn.exec(t)[1],r=e==null?void 0:e.substring(0,e.indexOf(":"));if(r)return"arbitrary.."+r}},js=t=>{const{theme:e,classGroups:r}=t,n={nextPart:new Map,validators:[]};for(const o in r)sn(r[o],n,o,e);return n},sn=(t,e,r,n)=>{t.forEach(o=>{if(typeof o=="string"){const a=o===""?e:Qn(e,o);a.classGroupId=r;return}if(typeof o=="function"){if(Bs(o)){sn(o(n),e,r,n);return}e.validators.push({validator:o,classGroupId:r});return}Object.entries(o).forEach(([a,i])=>{sn(i,Qn(e,a),r,n)})})},Qn=(t,e)=>{let r=t;return e.split(xn).forEach(n=>{r.nextPart.has(n)||r.nextPart.set(n,{nextPart:new Map,validators:[]}),r=r.nextPart.get(n)}),r},Bs=t=>t.isThemeGetter,Ds=t=>{if(t<1)return{get:()=>{},set:()=>{}};let e=0,r=new Map,n=new Map;const o=(a,i)=>{r.set(a,i),e++,e>t&&(e=0,n=r,r=new Map)};return{get(a){let i=r.get(a);if(i!==void 0)return i;if((i=n.get(a))!==void 0)return o(a,i),i},set(a,i){r.has(a)?r.set(a,i):o(a,i)}}},cn="!",ln=":",Vs=ln.length,Hs=t=>{const{prefix:e,experimentalParseClassName:r}=t;let n=o=>{const a=[];let i=0,s=0,c=0,l;for(let y=0;y<o.length;y++){let f=o[y];if(i===0&&s===0){if(f===ln){a.push(o.slice(c,y)),c=y+Vs;continue}if(f==="/"){l=y;continue}}f==="["?i++:f==="]"?i--:f==="("?s++:f===")"&&s--}const u=a.length===0?o:o.substring(c),p=Ws(u),h=p!==u,w=l&&l>c?l-c:void 0;return{modifiers:a,hasImportantModifier:h,baseClassName:p,maybePostfixModifierPosition:w}};if(e){const o=e+ln,a=n;n=i=>i.startsWith(o)?a(i.substring(o.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:i,maybePostfixModifierPosition:void 0}}if(r){const o=n;n=a=>r({className:a,parseClassName:o})}return n},Ws=t=>t.endsWith(cn)?t.substring(0,t.length-1):t.startsWith(cn)?t.substring(1):t,Zs=t=>{const e=Object.fromEntries(t.orderSensitiveModifiers.map(r=>[r,!0]));return r=>{if(r.length<=1)return r;const n=[];let o=[];return r.forEach(a=>{a[0]==="["||e[a]?(n.push(...o.sort(),a),o=[]):o.push(a)}),n.push(...o.sort()),n}},qs=t=>({cache:Ds(t.cacheSize),parseClassName:Hs(t),sortModifiers:Zs(t),...Os(t)}),Us=/\s+/,Gs=(t,e)=>{const{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:o,sortModifiers:a}=e,i=[],s=t.trim().split(Us);let c="";for(let l=s.length-1;l>=0;l-=1){const u=s[l],{isExternal:p,modifiers:h,hasImportantModifier:w,baseClassName:y,maybePostfixModifierPosition:f}=r(u);if(p){c=u+(c.length>0?" "+c:c);continue}let k=!!f,R=n(k?y.substring(0,f):y);if(!R){if(!k){c=u+(c.length>0?" "+c:c);continue}if(R=n(y),!R){c=u+(c.length>0?" "+c:c);continue}k=!1}const O=a(h).join(":"),G=w?O+cn:O,de=G+R;if(i.includes(de))continue;i.push(de);const oe=o(R,k);for(let we=0;we<oe.length;++we){const ie=oe[we];i.push(G+ie)}c=u+(c.length>0?" "+c:c)}return c};function Ys(){let t=0,e,r,n="";for(;t<arguments.length;)(e=arguments[t++])&&(r=Ho(e))&&(n&&(n+=" "),n+=r);return n}const Ho=t=>{if(typeof t=="string")return t;let e,r="";for(let n=0;n<t.length;n++)t[n]&&(e=Ho(t[n]))&&(r&&(r+=" "),r+=e);return r};function eo(t,...e){let r,n,o,a=i;function i(c){const l=e.reduce((u,p)=>p(u),t());return r=qs(l),n=r.cache.get,o=r.cache.set,a=s,s(c)}function s(c){const l=n(c);if(l)return l;const u=Gs(c,r);return o(c,u),u}return function(){return a(Ys.apply(null,arguments))}}const fe=t=>{const e=r=>r[t]||[];return e.isThemeGetter=!0,e},Wo=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Zo=/^\((?:(\w[\w-]*):)?(.+)\)$/i,Ks=/^\d+\/\d+$/,Xs=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Js=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Qs=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,ec=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,tc=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,St=t=>Ks.test(t),B=t=>!!t&&!Number.isNaN(Number(t)),tt=t=>!!t&&Number.isInteger(Number(t)),Zr=t=>t.endsWith("%")&&B(t.slice(0,-1)),Ge=t=>Xs.test(t),rc=()=>!0,nc=t=>Js.test(t)&&!Qs.test(t),qo=()=>!1,oc=t=>ec.test(t),ac=t=>tc.test(t),ic=t=>!x(t)&&!_(t),sc=t=>Ft(t,Yo,qo),x=t=>Wo.test(t),ft=t=>Ft(t,Ko,nc),qr=t=>Ft(t,pc,B),to=t=>Ft(t,Uo,qo),cc=t=>Ft(t,Go,ac),vr=t=>Ft(t,Xo,oc),_=t=>Zo.test(t),Dt=t=>jt(t,Ko),lc=t=>jt(t,mc),ro=t=>jt(t,Uo),dc=t=>jt(t,Yo),uc=t=>jt(t,Go),br=t=>jt(t,Xo,!0),Ft=(t,e,r)=>{const n=Wo.exec(t);return n?n[1]?e(n[1]):r(n[2]):!1},jt=(t,e,r=!1)=>{const n=Zo.exec(t);return n?n[1]?e(n[1]):r:!1},Uo=t=>t==="position"||t==="percentage",Go=t=>t==="image"||t==="url",Yo=t=>t==="length"||t==="size"||t==="bg-size",Ko=t=>t==="length",pc=t=>t==="number",mc=t=>t==="family-name",Xo=t=>t==="shadow",no=()=>{const t=fe("color"),e=fe("font"),r=fe("text"),n=fe("font-weight"),o=fe("tracking"),a=fe("leading"),i=fe("breakpoint"),s=fe("container"),c=fe("spacing"),l=fe("radius"),u=fe("shadow"),p=fe("inset-shadow"),h=fe("text-shadow"),w=fe("drop-shadow"),y=fe("blur"),f=fe("perspective"),k=fe("aspect"),R=fe("ease"),O=fe("animate"),G=()=>["auto","avoid","all","avoid-page","page","left","right","column"],de=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],oe=()=>[...de(),_,x],we=()=>["auto","hidden","clip","visible","scroll"],ie=()=>["auto","contain","none"],M=()=>[_,x,c],ve=()=>[St,"full","auto",...M()],Fe=()=>[tt,"none","subgrid",_,x],je=()=>["auto",{span:["full",tt,_,x]},tt,_,x],Xe=()=>[tt,"auto",_,x],D=()=>["auto","min","max","fr",_,x],F=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],ee=()=>["start","end","center","stretch","center-safe","end-safe"],W=()=>["auto",...M()],ge=()=>[St,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...M()],E=()=>[t,_,x],P=()=>[...de(),ro,to,{position:[_,x]}],J=()=>["no-repeat",{repeat:["","x","y","space","round"]}],K=()=>["auto","cover","contain",dc,sc,{size:[_,x]}],z=()=>[Zr,Dt,ft],N=()=>["","none","full",l,_,x],j=()=>["",B,Dt,ft],q=()=>["solid","dashed","dotted","double"],ue=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],A=()=>[B,Zr,ro,to],Z=()=>["","none",y,_,x],ae=()=>["none",B,_,x],te=()=>["none",B,_,x],be=()=>[B,_,x],ze=()=>[St,"full",...M()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[Ge],breakpoint:[Ge],color:[rc],container:[Ge],"drop-shadow":[Ge],ease:["in","out","in-out"],font:[ic],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[Ge],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[Ge],shadow:[Ge],spacing:["px",B],text:[Ge],"text-shadow":[Ge],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",St,x,_,k]}],container:["container"],columns:[{columns:[B,x,_,s]}],"break-after":[{"break-after":G()}],"break-before":[{"break-before":G()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:oe()}],overflow:[{overflow:we()}],"overflow-x":[{"overflow-x":we()}],"overflow-y":[{"overflow-y":we()}],overscroll:[{overscroll:ie()}],"overscroll-x":[{"overscroll-x":ie()}],"overscroll-y":[{"overscroll-y":ie()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:ve()}],"inset-x":[{"inset-x":ve()}],"inset-y":[{"inset-y":ve()}],start:[{start:ve()}],end:[{end:ve()}],top:[{top:ve()}],right:[{right:ve()}],bottom:[{bottom:ve()}],left:[{left:ve()}],visibility:["visible","invisible","collapse"],z:[{z:[tt,"auto",_,x]}],basis:[{basis:[St,"full","auto",s,...M()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[B,St,"auto","initial","none",x]}],grow:[{grow:["",B,_,x]}],shrink:[{shrink:["",B,_,x]}],order:[{order:[tt,"first","last","none",_,x]}],"grid-cols":[{"grid-cols":Fe()}],"col-start-end":[{col:je()}],"col-start":[{"col-start":Xe()}],"col-end":[{"col-end":Xe()}],"grid-rows":[{"grid-rows":Fe()}],"row-start-end":[{row:je()}],"row-start":[{"row-start":Xe()}],"row-end":[{"row-end":Xe()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":D()}],"auto-rows":[{"auto-rows":D()}],gap:[{gap:M()}],"gap-x":[{"gap-x":M()}],"gap-y":[{"gap-y":M()}],"justify-content":[{justify:[...F(),"normal"]}],"justify-items":[{"justify-items":[...ee(),"normal"]}],"justify-self":[{"justify-self":["auto",...ee()]}],"align-content":[{content:["normal",...F()]}],"align-items":[{items:[...ee(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...ee(),{baseline:["","last"]}]}],"place-content":[{"place-content":F()}],"place-items":[{"place-items":[...ee(),"baseline"]}],"place-self":[{"place-self":["auto",...ee()]}],p:[{p:M()}],px:[{px:M()}],py:[{py:M()}],ps:[{ps:M()}],pe:[{pe:M()}],pt:[{pt:M()}],pr:[{pr:M()}],pb:[{pb:M()}],pl:[{pl:M()}],m:[{m:W()}],mx:[{mx:W()}],my:[{my:W()}],ms:[{ms:W()}],me:[{me:W()}],mt:[{mt:W()}],mr:[{mr:W()}],mb:[{mb:W()}],ml:[{ml:W()}],"space-x":[{"space-x":M()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":M()}],"space-y-reverse":["space-y-reverse"],size:[{size:ge()}],w:[{w:[s,"screen",...ge()]}],"min-w":[{"min-w":[s,"screen","none",...ge()]}],"max-w":[{"max-w":[s,"screen","none","prose",{screen:[i]},...ge()]}],h:[{h:["screen",...ge()]}],"min-h":[{"min-h":["screen","none",...ge()]}],"max-h":[{"max-h":["screen",...ge()]}],"font-size":[{text:["base",r,Dt,ft]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[n,_,qr]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",Zr,x]}],"font-family":[{font:[lc,x,e]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[o,_,x]}],"line-clamp":[{"line-clamp":[B,"none",_,qr]}],leading:[{leading:[a,...M()]}],"list-image":[{"list-image":["none",_,x]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",_,x]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:E()}],"text-color":[{text:E()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...q(),"wavy"]}],"text-decoration-thickness":[{decoration:[B,"from-font","auto",_,ft]}],"text-decoration-color":[{decoration:E()}],"underline-offset":[{"underline-offset":[B,"auto",_,x]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:M()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",_,x]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",_,x]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:P()}],"bg-repeat":[{bg:J()}],"bg-size":[{bg:K()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},tt,_,x],radial:["",_,x],conic:[tt,_,x]},uc,cc]}],"bg-color":[{bg:E()}],"gradient-from-pos":[{from:z()}],"gradient-via-pos":[{via:z()}],"gradient-to-pos":[{to:z()}],"gradient-from":[{from:E()}],"gradient-via":[{via:E()}],"gradient-to":[{to:E()}],rounded:[{rounded:N()}],"rounded-s":[{"rounded-s":N()}],"rounded-e":[{"rounded-e":N()}],"rounded-t":[{"rounded-t":N()}],"rounded-r":[{"rounded-r":N()}],"rounded-b":[{"rounded-b":N()}],"rounded-l":[{"rounded-l":N()}],"rounded-ss":[{"rounded-ss":N()}],"rounded-se":[{"rounded-se":N()}],"rounded-ee":[{"rounded-ee":N()}],"rounded-es":[{"rounded-es":N()}],"rounded-tl":[{"rounded-tl":N()}],"rounded-tr":[{"rounded-tr":N()}],"rounded-br":[{"rounded-br":N()}],"rounded-bl":[{"rounded-bl":N()}],"border-w":[{border:j()}],"border-w-x":[{"border-x":j()}],"border-w-y":[{"border-y":j()}],"border-w-s":[{"border-s":j()}],"border-w-e":[{"border-e":j()}],"border-w-t":[{"border-t":j()}],"border-w-r":[{"border-r":j()}],"border-w-b":[{"border-b":j()}],"border-w-l":[{"border-l":j()}],"divide-x":[{"divide-x":j()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":j()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...q(),"hidden","none"]}],"divide-style":[{divide:[...q(),"hidden","none"]}],"border-color":[{border:E()}],"border-color-x":[{"border-x":E()}],"border-color-y":[{"border-y":E()}],"border-color-s":[{"border-s":E()}],"border-color-e":[{"border-e":E()}],"border-color-t":[{"border-t":E()}],"border-color-r":[{"border-r":E()}],"border-color-b":[{"border-b":E()}],"border-color-l":[{"border-l":E()}],"divide-color":[{divide:E()}],"outline-style":[{outline:[...q(),"none","hidden"]}],"outline-offset":[{"outline-offset":[B,_,x]}],"outline-w":[{outline:["",B,Dt,ft]}],"outline-color":[{outline:E()}],shadow:[{shadow:["","none",u,br,vr]}],"shadow-color":[{shadow:E()}],"inset-shadow":[{"inset-shadow":["none",p,br,vr]}],"inset-shadow-color":[{"inset-shadow":E()}],"ring-w":[{ring:j()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:E()}],"ring-offset-w":[{"ring-offset":[B,ft]}],"ring-offset-color":[{"ring-offset":E()}],"inset-ring-w":[{"inset-ring":j()}],"inset-ring-color":[{"inset-ring":E()}],"text-shadow":[{"text-shadow":["none",h,br,vr]}],"text-shadow-color":[{"text-shadow":E()}],opacity:[{opacity:[B,_,x]}],"mix-blend":[{"mix-blend":[...ue(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ue()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[B]}],"mask-image-linear-from-pos":[{"mask-linear-from":A()}],"mask-image-linear-to-pos":[{"mask-linear-to":A()}],"mask-image-linear-from-color":[{"mask-linear-from":E()}],"mask-image-linear-to-color":[{"mask-linear-to":E()}],"mask-image-t-from-pos":[{"mask-t-from":A()}],"mask-image-t-to-pos":[{"mask-t-to":A()}],"mask-image-t-from-color":[{"mask-t-from":E()}],"mask-image-t-to-color":[{"mask-t-to":E()}],"mask-image-r-from-pos":[{"mask-r-from":A()}],"mask-image-r-to-pos":[{"mask-r-to":A()}],"mask-image-r-from-color":[{"mask-r-from":E()}],"mask-image-r-to-color":[{"mask-r-to":E()}],"mask-image-b-from-pos":[{"mask-b-from":A()}],"mask-image-b-to-pos":[{"mask-b-to":A()}],"mask-image-b-from-color":[{"mask-b-from":E()}],"mask-image-b-to-color":[{"mask-b-to":E()}],"mask-image-l-from-pos":[{"mask-l-from":A()}],"mask-image-l-to-pos":[{"mask-l-to":A()}],"mask-image-l-from-color":[{"mask-l-from":E()}],"mask-image-l-to-color":[{"mask-l-to":E()}],"mask-image-x-from-pos":[{"mask-x-from":A()}],"mask-image-x-to-pos":[{"mask-x-to":A()}],"mask-image-x-from-color":[{"mask-x-from":E()}],"mask-image-x-to-color":[{"mask-x-to":E()}],"mask-image-y-from-pos":[{"mask-y-from":A()}],"mask-image-y-to-pos":[{"mask-y-to":A()}],"mask-image-y-from-color":[{"mask-y-from":E()}],"mask-image-y-to-color":[{"mask-y-to":E()}],"mask-image-radial":[{"mask-radial":[_,x]}],"mask-image-radial-from-pos":[{"mask-radial-from":A()}],"mask-image-radial-to-pos":[{"mask-radial-to":A()}],"mask-image-radial-from-color":[{"mask-radial-from":E()}],"mask-image-radial-to-color":[{"mask-radial-to":E()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":de()}],"mask-image-conic-pos":[{"mask-conic":[B]}],"mask-image-conic-from-pos":[{"mask-conic-from":A()}],"mask-image-conic-to-pos":[{"mask-conic-to":A()}],"mask-image-conic-from-color":[{"mask-conic-from":E()}],"mask-image-conic-to-color":[{"mask-conic-to":E()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:P()}],"mask-repeat":[{mask:J()}],"mask-size":[{mask:K()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",_,x]}],filter:[{filter:["","none",_,x]}],blur:[{blur:Z()}],brightness:[{brightness:[B,_,x]}],contrast:[{contrast:[B,_,x]}],"drop-shadow":[{"drop-shadow":["","none",w,br,vr]}],"drop-shadow-color":[{"drop-shadow":E()}],grayscale:[{grayscale:["",B,_,x]}],"hue-rotate":[{"hue-rotate":[B,_,x]}],invert:[{invert:["",B,_,x]}],saturate:[{saturate:[B,_,x]}],sepia:[{sepia:["",B,_,x]}],"backdrop-filter":[{"backdrop-filter":["","none",_,x]}],"backdrop-blur":[{"backdrop-blur":Z()}],"backdrop-brightness":[{"backdrop-brightness":[B,_,x]}],"backdrop-contrast":[{"backdrop-contrast":[B,_,x]}],"backdrop-grayscale":[{"backdrop-grayscale":["",B,_,x]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[B,_,x]}],"backdrop-invert":[{"backdrop-invert":["",B,_,x]}],"backdrop-opacity":[{"backdrop-opacity":[B,_,x]}],"backdrop-saturate":[{"backdrop-saturate":[B,_,x]}],"backdrop-sepia":[{"backdrop-sepia":["",B,_,x]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":M()}],"border-spacing-x":[{"border-spacing-x":M()}],"border-spacing-y":[{"border-spacing-y":M()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",_,x]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[B,"initial",_,x]}],ease:[{ease:["linear","initial",R,_,x]}],delay:[{delay:[B,_,x]}],animate:[{animate:["none",O,_,x]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[f,_,x]}],"perspective-origin":[{"perspective-origin":oe()}],rotate:[{rotate:ae()}],"rotate-x":[{"rotate-x":ae()}],"rotate-y":[{"rotate-y":ae()}],"rotate-z":[{"rotate-z":ae()}],scale:[{scale:te()}],"scale-x":[{"scale-x":te()}],"scale-y":[{"scale-y":te()}],"scale-z":[{"scale-z":te()}],"scale-3d":["scale-3d"],skew:[{skew:be()}],"skew-x":[{"skew-x":be()}],"skew-y":[{"skew-y":be()}],transform:[{transform:[_,x,"","none","gpu","cpu"]}],"transform-origin":[{origin:oe()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:ze()}],"translate-x":[{"translate-x":ze()}],"translate-y":[{"translate-y":ze()}],"translate-z":[{"translate-z":ze()}],"translate-none":["translate-none"],accent:[{accent:E()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:E()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",_,x]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":M()}],"scroll-mx":[{"scroll-mx":M()}],"scroll-my":[{"scroll-my":M()}],"scroll-ms":[{"scroll-ms":M()}],"scroll-me":[{"scroll-me":M()}],"scroll-mt":[{"scroll-mt":M()}],"scroll-mr":[{"scroll-mr":M()}],"scroll-mb":[{"scroll-mb":M()}],"scroll-ml":[{"scroll-ml":M()}],"scroll-p":[{"scroll-p":M()}],"scroll-px":[{"scroll-px":M()}],"scroll-py":[{"scroll-py":M()}],"scroll-ps":[{"scroll-ps":M()}],"scroll-pe":[{"scroll-pe":M()}],"scroll-pt":[{"scroll-pt":M()}],"scroll-pr":[{"scroll-pr":M()}],"scroll-pb":[{"scroll-pb":M()}],"scroll-pl":[{"scroll-pl":M()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",_,x]}],fill:[{fill:["none",...E()]}],"stroke-w":[{stroke:[B,Dt,ft,qr]}],stroke:[{stroke:["none",...E()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},hc=(t,{cacheSize:e,prefix:r,experimentalParseClassName:n,extend:o={},override:a={}})=>(Zt(t,"cacheSize",e),Zt(t,"prefix",r),Zt(t,"experimentalParseClassName",n),yr(t.theme,a.theme),yr(t.classGroups,a.classGroups),yr(t.conflictingClassGroups,a.conflictingClassGroups),yr(t.conflictingClassGroupModifiers,a.conflictingClassGroupModifiers),Zt(t,"orderSensitiveModifiers",a.orderSensitiveModifiers),xr(t.theme,o.theme),xr(t.classGroups,o.classGroups),xr(t.conflictingClassGroups,o.conflictingClassGroups),xr(t.conflictingClassGroupModifiers,o.conflictingClassGroupModifiers),Jo(t,o,"orderSensitiveModifiers"),t),Zt=(t,e,r)=>{r!==void 0&&(t[e]=r)},yr=(t,e)=>{if(e)for(const r in e)Zt(t,r,e[r])},xr=(t,e)=>{if(e)for(const r in e)Jo(t,e,r)},Jo=(t,e,r)=>{const n=e[r];n!==void 0&&(t[r]=t[r]?t[r].concat(n):n)},gc=(t,...e)=>typeof t=="function"?eo(no,t,...e):eo(()=>hc(no(),t),...e),sr="stagewise-companion-anchor";function fc(t,e){return document.elementsFromPoint(t,e).find(r=>r.nodeName!=="STAGEWISE-COMPANION-ANCHOR"&&!r.closest(sr)&&!r.closest("svg")&&wc(r,t,e))||document.body}const wc=(t,e,r)=>{const n=t.getBoundingClientRect(),o=e>n.left&&e<n.left+n.width,a=r>n.top&&r<n.top+n.height;return o&&a};var Pr=(t=>(t[t.ESC=0]="ESC",t[t.CTRL_ALT_C=1]="CTRL_ALT_C",t))(Pr||{});const dn={0:{keyComboDefault:"Esc",keyComboMac:"esc",isEventMatching:t=>t.code==="Escape"},1:{keyComboDefault:"Ctrl+Alt+C",keyComboMac:"⌘+⌥+C",isEventMatching:t=>t.code==="KeyC"&&(t.ctrlKey||t.metaKey)&&t.altKey}},vc=gc({extend:{classGroups:{"bg-image":["bg-gradient","bg-gradient-light-1","bg-gradient-light-2","bg-gradient-light-3"]}}});function he(...t){return vc(Ls(t))}const Ur=(t=16)=>Math.random().toString(36).substring(2,t+2);function wt({children:t,alwaysFullHeight:e=!1}){return d("section",{className:he("flex max-h-full min-h-48 flex-col items-stretch justify-start rounded-2xl border border-border/30 bg-zinc-50/80 p-4 shadow-md backdrop-blur-md",e&&"h-full"),children:t})}wt.Header=function({title:t,description:e}){return d("header",{className:"mb-3 flex flex-col gap-1 text-zinc-950",children:[t&&d("h3",{className:"font-semibold text-lg ",children:t}),e&&d("p",{className:"font-medium text-zinc-600",children:e})]})};wt.Content=function({children:t}){return d("div",{className:"-mx-4 flex flex-col gap-2 overflow-y-auto border-border/30 border-t px-4 pt-4 text-zinc-950",children:t})};wt.Footer=function({children:t}){return d("footer",{className:"flex flex-row items-end justify-end gap-2 text-sm text-zinc-600",children:t})};const bc='/*! tailwindcss v4.1.5 | MIT License | https://tailwindcss.com */@layer properties{@supports ((-webkit-hyphens:none) and (not (margin-trim:inline))) or ((-moz-orient:inline) and (not (color:rgb(from red r g b)))){stagewise-companion-anchor *,stagewise-companion-anchor :before,stagewise-companion-anchor :after,stagewise-companion-anchor ::backdrop{--tw-scale-x:1;--tw-scale-y:1;--tw-scale-z:1;--tw-rotate-x:initial;--tw-rotate-y:initial;--tw-rotate-z:initial;--tw-skew-x:initial;--tw-skew-y:initial;--tw-space-y-reverse:0;--tw-space-x-reverse:0;--tw-divide-y-reverse:0;--tw-border-style:solid;--tw-gradient-position:initial;--tw-gradient-from:#0000;--tw-gradient-via:#0000;--tw-gradient-to:#0000;--tw-gradient-stops:initial;--tw-gradient-via-stops:initial;--tw-gradient-from-position:0%;--tw-gradient-via-position:50%;--tw-gradient-to-position:100%;--tw-font-weight:initial;--tw-shadow:0 0 #0000;--tw-shadow-color:initial;--tw-shadow-alpha:100%;--tw-inset-shadow:0 0 #0000;--tw-inset-shadow-color:initial;--tw-inset-shadow-alpha:100%;--tw-ring-color:initial;--tw-ring-shadow:0 0 #0000;--tw-inset-ring-color:initial;--tw-inset-ring-shadow:0 0 #0000;--tw-ring-inset:initial;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-offset-shadow:0 0 #0000;--tw-outline-style:solid;--tw-blur:initial;--tw-brightness:initial;--tw-contrast:initial;--tw-grayscale:initial;--tw-hue-rotate:initial;--tw-invert:initial;--tw-opacity:initial;--tw-saturate:initial;--tw-sepia:initial;--tw-drop-shadow:initial;--tw-drop-shadow-color:initial;--tw-drop-shadow-alpha:100%;--tw-drop-shadow-size:initial;--tw-backdrop-blur:initial;--tw-backdrop-brightness:initial;--tw-backdrop-contrast:initial;--tw-backdrop-grayscale:initial;--tw-backdrop-hue-rotate:initial;--tw-backdrop-invert:initial;--tw-backdrop-opacity:initial;--tw-backdrop-saturate:initial;--tw-backdrop-sepia:initial;--tw-duration:initial;--tw-ease:initial}}}@layer theme{stagewise-companion-anchor{--font-sans:ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";--font-mono:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace;--color-red-100:oklch(93.6% .032 17.717);--color-red-200:oklch(88.5% .062 18.334);--color-red-500:oklch(63.7% .237 25.331);--color-red-600:oklch(57.7% .245 27.325);--color-red-700:oklch(50.5% .213 27.518);--color-orange-50:oklch(98% .016 73.684);--color-orange-100:oklch(95.4% .038 75.164);--color-orange-200:oklch(90.1% .076 70.697);--color-orange-300:oklch(83.7% .128 66.29);--color-orange-500:oklch(70.5% .213 47.604);--color-orange-600:oklch(64.6% .222 41.116);--color-orange-700:oklch(55.3% .195 38.402);--color-orange-800:oklch(47% .157 37.304);--color-amber-50:oklch(98.7% .022 95.277);--color-amber-800:oklch(47.3% .137 46.201);--color-yellow-500:oklch(79.5% .184 86.047);--color-green-500:oklch(72.3% .219 149.579);--color-green-600:oklch(62.7% .194 149.214);--color-teal-500:oklch(70.4% .14 182.503);--color-sky-600:oklch(58.8% .158 241.966);--color-sky-700:oklch(50% .134 242.749);--color-blue-50:oklch(97% .014 254.604);--color-blue-100:oklch(93.2% .032 255.585);--color-blue-200:oklch(88.2% .059 254.128);--color-blue-300:oklch(80.9% .105 251.813);--color-blue-500:oklch(62.3% .214 259.815);--color-blue-600:oklch(54.6% .245 262.881);--color-blue-700:oklch(48.8% .243 264.376);--color-blue-800:oklch(42.4% .199 265.638);--color-indigo-700:oklch(45.7% .24 277.023);--color-indigo-950:oklch(25.7% .09 281.288);--color-violet-700:oklch(49.1% .27 292.581);--color-purple-500:oklch(62.7% .265 303.9);--color-fuchsia-700:oklch(51.8% .253 323.949);--color-pink-500:oklch(65.6% .241 354.308);--color-rose-600:oklch(58.6% .253 17.585);--color-zinc-50:oklch(98.5% 0 0);--color-zinc-100:oklch(96.7% .001 286.375);--color-zinc-300:oklch(87.1% .006 286.286);--color-zinc-400:oklch(70.5% .015 286.067);--color-zinc-500:oklch(55.2% .016 285.938);--color-zinc-600:oklch(44.2% .017 285.786);--color-zinc-700:oklch(37% .013 285.805);--color-zinc-900:oklch(21% .006 285.885);--color-zinc-950:oklch(14.1% .005 285.823);--color-black:#000;--color-white:#fff;--spacing:.25rem;--text-xs:.75rem;--text-xs--line-height:calc(1/.75);--text-sm:.875rem;--text-sm--line-height:calc(1.25/.875);--text-base:1rem;--text-base--line-height: 1.5 ;--text-lg:1.125rem;--text-lg--line-height:calc(1.75/1.125);--font-weight-normal:400;--font-weight-medium:500;--font-weight-semibold:600;--font-weight-bold:700;--radius-md:.375rem;--radius-lg:.5rem;--radius-2xl:1rem;--radius-3xl:1.5rem;--drop-shadow-xs:0 1px 1px #0000000d;--drop-shadow-md:0 3px 3px #0000001f;--drop-shadow-xl:0 9px 7px #0000001a;--ease-out:cubic-bezier(0,0,.2,1);--animate-spin:spin 1s linear infinite;--animate-pulse:pulse 2s cubic-bezier(.4,0,.6,1)infinite;--blur-md:12px;--default-transition-duration:.15s;--default-transition-timing-function:cubic-bezier(.4,0,.2,1);--default-font-family:var(--font-sans);--default-mono-font-family:var(--font-mono);--color-background:var(--color-white);--color-foreground:var(--color-zinc-950);--color-border:var(--color-zinc-500)}}@layer base{stagewise-companion-anchor *,stagewise-companion-anchor :after,stagewise-companion-anchor :before,stagewise-companion-anchor ::backdrop{box-sizing:border-box;border:0 solid;margin:0;padding:0}stagewise-companion-anchor ::file-selector-button{box-sizing:border-box;border:0 solid;margin:0;padding:0}:where(stagewise-companion-anchor),stagewise-companion-anchor{-webkit-text-size-adjust:100%;-moz-tab-size:4;-o-tab-size:4;tab-size:4;line-height:1.5;font-family:var(--default-font-family,ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji");font-feature-settings:var(--default-font-feature-settings,normal);font-variation-settings:var(--default-font-variation-settings,normal);-webkit-tap-highlight-color:transparent}stagewise-companion-anchor hr{height:0;color:inherit;border-top-width:1px}stagewise-companion-anchor abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}stagewise-companion-anchor h1,stagewise-companion-anchor h2,stagewise-companion-anchor h3,stagewise-companion-anchor h4,stagewise-companion-anchor h5,stagewise-companion-anchor h6{font-size:inherit;font-weight:inherit}stagewise-companion-anchor a{color:inherit;-webkit-text-decoration:inherit;text-decoration:inherit}stagewise-companion-anchor b,stagewise-companion-anchor strong{font-weight:bolder}stagewise-companion-anchor code,stagewise-companion-anchor kbd,stagewise-companion-anchor samp,stagewise-companion-anchor pre{font-family:var(--default-mono-font-family,ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace);font-feature-settings:var(--default-mono-font-feature-settings,normal);font-variation-settings:var(--default-mono-font-variation-settings,normal);font-size:1em}stagewise-companion-anchor small{font-size:80%}stagewise-companion-anchor sub,stagewise-companion-anchor sup{vertical-align:baseline;font-size:75%;line-height:0;position:relative}stagewise-companion-anchor sub{bottom:-.25em}stagewise-companion-anchor sup{top:-.5em}stagewise-companion-anchor table{text-indent:0;border-color:inherit;border-collapse:collapse}stagewise-companion-anchor :-moz-focusring{outline:auto}stagewise-companion-anchor progress{vertical-align:baseline}stagewise-companion-anchor summary{display:list-item}stagewise-companion-anchor ol,stagewise-companion-anchor ul,stagewise-companion-anchor menu{list-style:none}stagewise-companion-anchor img,stagewise-companion-anchor svg,stagewise-companion-anchor video,stagewise-companion-anchor canvas,stagewise-companion-anchor audio,stagewise-companion-anchor iframe,stagewise-companion-anchor embed,stagewise-companion-anchor object{vertical-align:middle;display:block}stagewise-companion-anchor img,stagewise-companion-anchor video{max-width:100%;height:auto}stagewise-companion-anchor button,stagewise-companion-anchor input,stagewise-companion-anchor select,stagewise-companion-anchor optgroup,stagewise-companion-anchor textarea{font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;color:inherit;opacity:1;background-color:#0000;border-radius:0}stagewise-companion-anchor ::file-selector-button{font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;color:inherit;opacity:1;background-color:#0000;border-radius:0}stagewise-companion-anchor :where(select:is([multiple],[size])) optgroup{font-weight:bolder}stagewise-companion-anchor :where(select:is([multiple],[size])) optgroup option{padding-inline-start:20px}stagewise-companion-anchor ::file-selector-button{margin-inline-end:4px}stagewise-companion-anchor ::-moz-placeholder{opacity:1}stagewise-companion-anchor ::placeholder{opacity:1}@supports (not (-webkit-appearance:-apple-pay-button)) or (contain-intrinsic-size:1px){stagewise-companion-anchor ::-moz-placeholder{color:currentColor}stagewise-companion-anchor ::placeholder{color:currentColor}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor ::-moz-placeholder{color:color-mix(in oklab,currentcolor 50%,transparent)}stagewise-companion-anchor ::placeholder{color:color-mix(in oklab,currentcolor 50%,transparent)}}}stagewise-companion-anchor textarea{resize:vertical}stagewise-companion-anchor ::-webkit-search-decoration{-webkit-appearance:none}stagewise-companion-anchor ::-webkit-date-and-time-value{min-height:1lh;text-align:inherit}stagewise-companion-anchor ::-webkit-datetime-edit{display:inline-flex}stagewise-companion-anchor ::-webkit-datetime-edit-fields-wrapper{padding:0}stagewise-companion-anchor ::-webkit-datetime-edit{padding-block:0}stagewise-companion-anchor ::-webkit-datetime-edit-year-field{padding-block:0}stagewise-companion-anchor ::-webkit-datetime-edit-month-field{padding-block:0}stagewise-companion-anchor ::-webkit-datetime-edit-day-field{padding-block:0}stagewise-companion-anchor ::-webkit-datetime-edit-hour-field{padding-block:0}stagewise-companion-anchor ::-webkit-datetime-edit-minute-field{padding-block:0}stagewise-companion-anchor ::-webkit-datetime-edit-second-field{padding-block:0}stagewise-companion-anchor ::-webkit-datetime-edit-millisecond-field{padding-block:0}stagewise-companion-anchor ::-webkit-datetime-edit-meridiem-field{padding-block:0}stagewise-companion-anchor :-moz-ui-invalid{box-shadow:none}stagewise-companion-anchor button,stagewise-companion-anchor input:where([type=button],[type=reset],[type=submit]){-webkit-appearance:button;-moz-appearance:button;appearance:button}stagewise-companion-anchor ::file-selector-button{-webkit-appearance:button;-moz-appearance:button;appearance:button}stagewise-companion-anchor ::-webkit-inner-spin-button{height:auto}stagewise-companion-anchor ::-webkit-outer-spin-button{height:auto}stagewise-companion-anchor [hidden]:where(:not([hidden=until-found])){display:none!important}stagewise-companion-anchor stagewise-companion-anchor *{min-width:0;min-height:0;position:relative}}@layer components{stagewise-companion-anchor .chat-loading-gradient{background:linear-gradient(#f8fafccc,#f8fafccc) padding-box padding-box,linear-gradient(45deg,#8b5cf6,#06b6d4,#8b5cf6) 0 0/400% 400% border-box;border:2px solid #0000;animation:2s infinite gradient-animation}stagewise-companion-anchor .chat-success-border{animation:2s ease-out blink-green-fade}stagewise-companion-anchor .chat-error-border{animation:1s ease-out blink-red-fade}@keyframes blink-green-fade{0%,50%{box-shadow:0 0 0 2px #22c55eb3}to{box-shadow:0 0 0 2px #22c55e00}}@keyframes blink-red-fade{0%,50%{box-shadow:0 0 0 2px #ef4444}to{box-shadow:0 0 0 2px #ef444400}}}@layer utilities{stagewise-companion-anchor .pointer-events-auto{pointer-events:auto!important}stagewise-companion-anchor .pointer-events-none{pointer-events:none!important}stagewise-companion-anchor .visible{visibility:visible!important}stagewise-companion-anchor .absolute{position:absolute!important}stagewise-companion-anchor .fixed{position:fixed!important}stagewise-companion-anchor .relative{position:relative!important}stagewise-companion-anchor .inset-0{inset:calc(var(--spacing)*0)!important}stagewise-companion-anchor .inset-4{inset:calc(var(--spacing)*4)!important}stagewise-companion-anchor .top-0{top:calc(var(--spacing)*0)!important}stagewise-companion-anchor .top-0\\.5{top:calc(var(--spacing)*.5)!important}stagewise-companion-anchor .top-1\\/2{top:50%!important}stagewise-companion-anchor .top-\\[-20\\%\\]{top:-20%!important}stagewise-companion-anchor .top-\\[25\\%\\]{top:25%!important}stagewise-companion-anchor .right-0{right:calc(var(--spacing)*0)!important}stagewise-companion-anchor .right-1\\/2{right:50%!important}stagewise-companion-anchor .right-\\[100\\%\\]{right:100%!important}stagewise-companion-anchor .bottom-0{bottom:calc(var(--spacing)*0)!important}stagewise-companion-anchor .bottom-1\\/2{bottom:50%!important}stagewise-companion-anchor .bottom-3{bottom:calc(var(--spacing)*3)!important}stagewise-companion-anchor .left-0{left:calc(var(--spacing)*0)!important}stagewise-companion-anchor .left-0\\.5{left:calc(var(--spacing)*.5)!important}stagewise-companion-anchor .left-1\\/2{left:50%!important}stagewise-companion-anchor .left-3{left:calc(var(--spacing)*3)!important}stagewise-companion-anchor .left-\\[-10\\%\\]{left:-10%!important}stagewise-companion-anchor .left-\\[25\\%\\]{left:25%!important}stagewise-companion-anchor .left-\\[100\\%\\]{left:100%!important}stagewise-companion-anchor .z-20{z-index:20!important}stagewise-companion-anchor .z-50{z-index:50!important}stagewise-companion-anchor .container{width:100%!important}@media (min-width:40rem){stagewise-companion-anchor .container{max-width:40rem!important}}@media (min-width:48rem){stagewise-companion-anchor .container{max-width:48rem!important}}@media (min-width:64rem){stagewise-companion-anchor .container{max-width:64rem!important}}@media (min-width:80rem){stagewise-companion-anchor .container{max-width:80rem!important}}@media (min-width:96rem){stagewise-companion-anchor .container{max-width:96rem!important}}stagewise-companion-anchor .-mx-4{margin-inline:calc(var(--spacing)*-4)!important}stagewise-companion-anchor .my-2{margin-block:calc(var(--spacing)*2)!important}stagewise-companion-anchor .mt-1{margin-top:calc(var(--spacing)*1)!important}stagewise-companion-anchor .mt-2{margin-top:calc(var(--spacing)*2)!important}stagewise-companion-anchor .mb-2{margin-bottom:calc(var(--spacing)*2)!important}stagewise-companion-anchor .mb-3{margin-bottom:calc(var(--spacing)*3)!important}stagewise-companion-anchor .block{display:block!important}stagewise-companion-anchor .contents{display:contents!important}stagewise-companion-anchor .flex{display:flex!important}stagewise-companion-anchor .hidden{display:none!important}stagewise-companion-anchor .inline{display:inline!important}stagewise-companion-anchor .aspect-square{aspect-ratio:1!important}stagewise-companion-anchor .size-0{width:calc(var(--spacing)*0)!important;height:calc(var(--spacing)*0)!important}stagewise-companion-anchor .size-1\\.5{width:calc(var(--spacing)*1.5)!important;height:calc(var(--spacing)*1.5)!important}stagewise-companion-anchor .size-2\\/3{width:66.6667%!important;height:66.6667%!important}stagewise-companion-anchor .size-3{width:calc(var(--spacing)*3)!important;height:calc(var(--spacing)*3)!important}stagewise-companion-anchor .size-4{width:calc(var(--spacing)*4)!important;height:calc(var(--spacing)*4)!important}stagewise-companion-anchor .size-4\\.5{width:calc(var(--spacing)*4.5)!important;height:calc(var(--spacing)*4.5)!important}stagewise-companion-anchor .size-5{width:calc(var(--spacing)*5)!important;height:calc(var(--spacing)*5)!important}stagewise-companion-anchor .size-6{width:calc(var(--spacing)*6)!important;height:calc(var(--spacing)*6)!important}stagewise-companion-anchor .size-8{width:calc(var(--spacing)*8)!important;height:calc(var(--spacing)*8)!important}stagewise-companion-anchor .size-9{width:calc(var(--spacing)*9)!important;height:calc(var(--spacing)*9)!important}stagewise-companion-anchor .size-9\\/12{width:75%!important;height:75%!important}stagewise-companion-anchor .size-12{width:calc(var(--spacing)*12)!important;height:calc(var(--spacing)*12)!important}stagewise-companion-anchor .size-\\[120\\%\\]{width:120%!important;height:120%!important}stagewise-companion-anchor .size-full{width:100%!important;height:100%!important}stagewise-companion-anchor .h-0{height:calc(var(--spacing)*0)!important}stagewise-companion-anchor .h-3{height:calc(var(--spacing)*3)!important}stagewise-companion-anchor .h-5{height:calc(var(--spacing)*5)!important}stagewise-companion-anchor .h-8{height:calc(var(--spacing)*8)!important}stagewise-companion-anchor .h-9\\.5{height:calc(var(--spacing)*9.5)!important}stagewise-companion-anchor .h-12{height:calc(var(--spacing)*12)!important}stagewise-companion-anchor .h-16{height:calc(var(--spacing)*16)!important}stagewise-companion-anchor .h-24{height:calc(var(--spacing)*24)!important}stagewise-companion-anchor .h-\\[50\\%\\]{height:50%!important}stagewise-companion-anchor .h-\\[120\\%\\]{height:120%!important}stagewise-companion-anchor .h-\\[calc\\(100vh-32px\\)\\]{height:calc(100vh - 32px)!important}stagewise-companion-anchor .h-\\[calc-size\\(auto\\)\\]{height:calc-size(auto)!important}stagewise-companion-anchor .h-\\[calc-size\\(auto\\,size\\)\\]{height:calc-size(auto,size)!important}stagewise-companion-anchor .h-auto{height:auto!important}stagewise-companion-anchor .h-full{height:100%!important}stagewise-companion-anchor .h-screen{height:100vh!important}stagewise-companion-anchor .max-h-full{max-height:100%!important}stagewise-companion-anchor .min-h-0{min-height:calc(var(--spacing)*0)!important}stagewise-companion-anchor .min-h-48{min-height:calc(var(--spacing)*48)!important}stagewise-companion-anchor .w-8{width:calc(var(--spacing)*8)!important}stagewise-companion-anchor .w-9\\.5{width:calc(var(--spacing)*9.5)!important}stagewise-companion-anchor .w-96{width:calc(var(--spacing)*96)!important}stagewise-companion-anchor .w-\\[50\\%\\]{width:50%!important}stagewise-companion-anchor .w-auto{width:auto!important}stagewise-companion-anchor .w-fit{width:-moz-fit-content!important;width:fit-content!important}stagewise-companion-anchor .w-full{width:100%!important}stagewise-companion-anchor .w-max{width:-moz-max-content!important;width:max-content!important}stagewise-companion-anchor .w-screen{width:100vw!important}stagewise-companion-anchor .max-w-8{max-width:calc(var(--spacing)*8)!important}stagewise-companion-anchor .max-w-90{max-width:calc(var(--spacing)*90)!important}stagewise-companion-anchor .max-w-\\[40vw\\]{max-width:40vw!important}stagewise-companion-anchor .max-w-full{max-width:100%!important}stagewise-companion-anchor .min-w-0{min-width:calc(var(--spacing)*0)!important}stagewise-companion-anchor .min-w-3{min-width:calc(var(--spacing)*3)!important}stagewise-companion-anchor .min-w-24{min-width:calc(var(--spacing)*24)!important}stagewise-companion-anchor .flex-1{flex:1!important}stagewise-companion-anchor .flex-shrink-0,stagewise-companion-anchor .shrink-0{flex-shrink:0!important}stagewise-companion-anchor .origin-bottom{transform-origin:bottom!important}stagewise-companion-anchor .origin-bottom-left{transform-origin:0 100%!important}stagewise-companion-anchor .origin-bottom-right{transform-origin:100% 100%!important}stagewise-companion-anchor .origin-center{transform-origin:50%!important}stagewise-companion-anchor .origin-top{transform-origin:top!important}stagewise-companion-anchor .origin-top-left{transform-origin:0 0!important}stagewise-companion-anchor .origin-top-right{transform-origin:100% 0!important}stagewise-companion-anchor .scale-25{--tw-scale-x:25%!important;--tw-scale-y:25%!important;--tw-scale-z:25%!important;scale:var(--tw-scale-x)var(--tw-scale-y)!important}stagewise-companion-anchor .scale-50{--tw-scale-x:50%!important;--tw-scale-y:50%!important;--tw-scale-z:50%!important;scale:var(--tw-scale-x)var(--tw-scale-y)!important}stagewise-companion-anchor .scale-100{--tw-scale-x:100%!important;--tw-scale-y:100%!important;--tw-scale-z:100%!important;scale:var(--tw-scale-x)var(--tw-scale-y)!important}stagewise-companion-anchor .transform{transform:var(--tw-rotate-x,)var(--tw-rotate-y,)var(--tw-rotate-z,)var(--tw-skew-x,)var(--tw-skew-y,)!important}stagewise-companion-anchor .animate-pulse{animation:var(--animate-pulse)!important}stagewise-companion-anchor .animate-spin{animation:var(--animate-spin)!important}stagewise-companion-anchor .cursor-copy{cursor:copy!important}stagewise-companion-anchor .cursor-not-allowed{cursor:not-allowed!important}stagewise-companion-anchor .cursor-pointer{cursor:pointer!important}stagewise-companion-anchor .resize{resize:both!important}stagewise-companion-anchor .resize-none{resize:none!important}stagewise-companion-anchor .snap-start{scroll-snap-align:start!important}stagewise-companion-anchor .list-inside{list-style-position:inside!important}stagewise-companion-anchor .list-decimal{list-style-type:decimal!important}stagewise-companion-anchor .flex-col{flex-direction:column!important}stagewise-companion-anchor .flex-col-reverse{flex-direction:column-reverse!important}stagewise-companion-anchor .flex-row{flex-direction:row!important}stagewise-companion-anchor .flex-wrap{flex-wrap:wrap!important}stagewise-companion-anchor .items-center{align-items:center!important}stagewise-companion-anchor .items-end{align-items:flex-end!important}stagewise-companion-anchor .items-start{align-items:flex-start!important}stagewise-companion-anchor .items-stretch{align-items:stretch!important}stagewise-companion-anchor .justify-between{justify-content:space-between!important}stagewise-companion-anchor .justify-center{justify-content:center!important}stagewise-companion-anchor .justify-end{justify-content:flex-end!important}stagewise-companion-anchor .justify-start{justify-content:flex-start!important}stagewise-companion-anchor .gap-0\\.5{gap:calc(var(--spacing)*.5)!important}stagewise-companion-anchor .gap-1{gap:calc(var(--spacing)*1)!important}stagewise-companion-anchor .gap-2{gap:calc(var(--spacing)*2)!important}stagewise-companion-anchor .gap-3{gap:calc(var(--spacing)*3)!important}stagewise-companion-anchor :where(.space-y-1>:not(:last-child)){--tw-space-y-reverse:0!important;margin-block-start:calc(calc(var(--spacing)*1)*var(--tw-space-y-reverse))!important;margin-block-end:calc(calc(var(--spacing)*1)*calc(1 - var(--tw-space-y-reverse)))!important}stagewise-companion-anchor :where(.space-y-2>:not(:last-child)){--tw-space-y-reverse:0!important;margin-block-start:calc(calc(var(--spacing)*2)*var(--tw-space-y-reverse))!important;margin-block-end:calc(calc(var(--spacing)*2)*calc(1 - var(--tw-space-y-reverse)))!important}stagewise-companion-anchor :where(.space-y-3>:not(:last-child)){--tw-space-y-reverse:0!important;margin-block-start:calc(calc(var(--spacing)*3)*var(--tw-space-y-reverse))!important;margin-block-end:calc(calc(var(--spacing)*3)*calc(1 - var(--tw-space-y-reverse)))!important}stagewise-companion-anchor :where(.space-y-4>:not(:last-child)){--tw-space-y-reverse:0!important;margin-block-start:calc(calc(var(--spacing)*4)*var(--tw-space-y-reverse))!important;margin-block-end:calc(calc(var(--spacing)*4)*calc(1 - var(--tw-space-y-reverse)))!important}stagewise-companion-anchor :where(.space-x-2>:not(:last-child)){--tw-space-x-reverse:0!important;margin-inline-start:calc(calc(var(--spacing)*2)*var(--tw-space-x-reverse))!important;margin-inline-end:calc(calc(var(--spacing)*2)*calc(1 - var(--tw-space-x-reverse)))!important}stagewise-companion-anchor :where(.divide-y>:not(:last-child)){--tw-divide-y-reverse:0!important;border-bottom-style:var(--tw-border-style)!important;border-top-style:var(--tw-border-style)!important;border-top-width:calc(1px*var(--tw-divide-y-reverse))!important;border-bottom-width:calc(1px*calc(1 - var(--tw-divide-y-reverse)))!important}stagewise-companion-anchor :where(.divide-y-reverse>:not(:last-child)){--tw-divide-y-reverse:1!important}stagewise-companion-anchor :where(.divide-blue-200>:not(:last-child)){border-color:var(--color-blue-200)!important}stagewise-companion-anchor :where(.divide-border\\/20>:not(:last-child)){border-color:#71717b33!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor :where(.divide-border\\/20>:not(:last-child)){border-color:color-mix(in oklab,var(--color-border)20%,transparent)!important}}stagewise-companion-anchor :where(.divide-orange-200>:not(:last-child)){border-color:var(--color-orange-200)!important}stagewise-companion-anchor .truncate{text-overflow:ellipsis!important;white-space:nowrap!important;overflow:hidden!important}stagewise-companion-anchor .overflow-hidden{overflow:hidden!important}stagewise-companion-anchor .overflow-visible{overflow:visible!important}stagewise-companion-anchor .overflow-y-auto{overflow-y:auto!important}stagewise-companion-anchor .rounded{border-radius:.25rem!important}stagewise-companion-anchor .rounded-2xl{border-radius:var(--radius-2xl)!important}stagewise-companion-anchor .rounded-full{border-radius:3.40282e38px!important}stagewise-companion-anchor .rounded-lg{border-radius:var(--radius-lg)!important}stagewise-companion-anchor .rounded-md{border-radius:var(--radius-md)!important}stagewise-companion-anchor .rounded-t-3xl{border-top-left-radius:var(--radius-3xl)!important;border-top-right-radius:var(--radius-3xl)!important}stagewise-companion-anchor .rounded-t-lg{border-top-left-radius:var(--radius-lg)!important;border-top-right-radius:var(--radius-lg)!important}stagewise-companion-anchor .rounded-b-3xl{border-bottom-right-radius:var(--radius-3xl)!important;border-bottom-left-radius:var(--radius-3xl)!important}stagewise-companion-anchor .rounded-b-lg{border-bottom-right-radius:var(--radius-lg)!important;border-bottom-left-radius:var(--radius-lg)!important}stagewise-companion-anchor .border{border-style:var(--tw-border-style)!important;border-width:1px!important}stagewise-companion-anchor .border-2{border-style:var(--tw-border-style)!important;border-width:2px!important}stagewise-companion-anchor .border-t{border-top-style:var(--tw-border-style)!important;border-top-width:1px!important}stagewise-companion-anchor .border-solid{--tw-border-style:solid!important;border-style:solid!important}stagewise-companion-anchor .border-blue-200{border-color:var(--color-blue-200)!important}stagewise-companion-anchor .border-blue-300{border-color:var(--color-blue-300)!important}stagewise-companion-anchor .border-blue-500{border-color:var(--color-blue-500)!important}stagewise-companion-anchor .border-blue-600\\/80{border-color:#155dfccc!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .border-blue-600\\/80{border-color:color-mix(in oklab,var(--color-blue-600)80%,transparent)!important}}stagewise-companion-anchor .border-border\\/30{border-color:#71717b4d!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .border-border\\/30{border-color:color-mix(in oklab,var(--color-border)30%,transparent)!important}}stagewise-companion-anchor .border-green-500{border-color:var(--color-green-500)!important}stagewise-companion-anchor .border-green-600\\/80{border-color:#00a544cc!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .border-green-600\\/80{border-color:color-mix(in oklab,var(--color-green-600)80%,transparent)!important}}stagewise-companion-anchor .border-orange-200{border-color:var(--color-orange-200)!important}stagewise-companion-anchor .border-orange-300{border-color:var(--color-orange-300)!important}stagewise-companion-anchor .border-orange-500{border-color:var(--color-orange-500)!important}stagewise-companion-anchor .border-pink-500{border-color:var(--color-pink-500)!important}stagewise-companion-anchor .border-purple-500{border-color:var(--color-purple-500)!important}stagewise-companion-anchor .border-red-200{border-color:var(--color-red-200)!important}stagewise-companion-anchor .border-red-500{border-color:var(--color-red-500)!important}stagewise-companion-anchor .border-transparent{border-color:#0000!important}stagewise-companion-anchor .border-yellow-500{border-color:var(--color-yellow-500)!important}stagewise-companion-anchor .border-zinc-300{border-color:var(--color-zinc-300)!important}stagewise-companion-anchor .border-zinc-500{border-color:var(--color-zinc-500)!important}stagewise-companion-anchor .bg-amber-50{background-color:var(--color-amber-50)!important}stagewise-companion-anchor .bg-background\\/60{background-color:#fff9!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-background\\/60{background-color:color-mix(in oklab,var(--color-background)60%,transparent)!important}}stagewise-companion-anchor .bg-blue-50{background-color:var(--color-blue-50)!important}stagewise-companion-anchor .bg-blue-50\\/90{background-color:#eff6ffe6!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-blue-50\\/90{background-color:color-mix(in oklab,var(--color-blue-50)90%,transparent)!important}}stagewise-companion-anchor .bg-blue-100\\/80{background-color:#dbeafecc!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-blue-100\\/80{background-color:color-mix(in oklab,var(--color-blue-100)80%,transparent)!important}}stagewise-companion-anchor .bg-blue-500{background-color:var(--color-blue-500)!important}stagewise-companion-anchor .bg-blue-600{background-color:var(--color-blue-600)!important}stagewise-companion-anchor .bg-blue-600\\/20{background-color:#155dfc33!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-blue-600\\/20{background-color:color-mix(in oklab,var(--color-blue-600)20%,transparent)!important}}stagewise-companion-anchor .bg-green-500{background-color:var(--color-green-500)!important}stagewise-companion-anchor .bg-green-600\\/5{background-color:#00a5440d!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-green-600\\/5{background-color:color-mix(in oklab,var(--color-green-600)5%,transparent)!important}}stagewise-companion-anchor .bg-orange-50\\/90{background-color:#fff7ede6!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-orange-50\\/90{background-color:color-mix(in oklab,var(--color-orange-50)90%,transparent)!important}}stagewise-companion-anchor .bg-orange-100\\/80{background-color:#ffedd5cc!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-orange-100\\/80{background-color:color-mix(in oklab,var(--color-orange-100)80%,transparent)!important}}stagewise-companion-anchor .bg-orange-500{background-color:var(--color-orange-500)!important}stagewise-companion-anchor .bg-orange-600{background-color:var(--color-orange-600)!important}stagewise-companion-anchor .bg-pink-500{background-color:var(--color-pink-500)!important}stagewise-companion-anchor .bg-purple-500{background-color:var(--color-purple-500)!important}stagewise-companion-anchor .bg-red-100{background-color:var(--color-red-100)!important}stagewise-companion-anchor .bg-red-500{background-color:var(--color-red-500)!important}stagewise-companion-anchor .bg-rose-600{background-color:var(--color-rose-600)!important}stagewise-companion-anchor .bg-transparent{background-color:#0000!important}stagewise-companion-anchor .bg-white{background-color:var(--color-white)!important}stagewise-companion-anchor .bg-white\\/40{background-color:#fff6!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-white\\/40{background-color:color-mix(in oklab,var(--color-white)40%,transparent)!important}}stagewise-companion-anchor .bg-white\\/80{background-color:#fffc!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-white\\/80{background-color:color-mix(in oklab,var(--color-white)80%,transparent)!important}}stagewise-companion-anchor .bg-white\\/90{background-color:#ffffffe6!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-white\\/90{background-color:color-mix(in oklab,var(--color-white)90%,transparent)!important}}stagewise-companion-anchor .bg-yellow-500{background-color:var(--color-yellow-500)!important}stagewise-companion-anchor .bg-zinc-50\\/80{background-color:#fafafacc!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-zinc-50\\/80{background-color:color-mix(in oklab,var(--color-zinc-50)80%,transparent)!important}}stagewise-companion-anchor .bg-zinc-300{background-color:var(--color-zinc-300)!important}stagewise-companion-anchor .bg-zinc-500{background-color:var(--color-zinc-500)!important}stagewise-companion-anchor .bg-zinc-500\\/10{background-color:#71717b1a!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-zinc-500\\/10{background-color:color-mix(in oklab,var(--color-zinc-500)10%,transparent)!important}}stagewise-companion-anchor .bg-zinc-500\\/40{background-color:#71717b66!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-zinc-500\\/40{background-color:color-mix(in oklab,var(--color-zinc-500)40%,transparent)!important}}stagewise-companion-anchor .bg-zinc-700\\/80{background-color:#3f3f46cc!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-zinc-700\\/80{background-color:color-mix(in oklab,var(--color-zinc-700)80%,transparent)!important}}stagewise-companion-anchor .bg-gradient-to-tr{--tw-gradient-position:to top right in oklab!important;background-image:linear-gradient(var(--tw-gradient-stops))!important}stagewise-companion-anchor .bg-\\[radial-gradient\\(circle\\,rgba\\(55\\,48\\,163\\,0\\)_55\\%\\,rgba\\(55\\,48\\,163\\,0\\.35\\)_73\\%\\)\\]{background-image:radial-gradient(circle,#3730a300 55%,#3730a359 73%)!important}stagewise-companion-anchor .bg-\\[radial-gradient\\(circle\\,rgba\\(219\\,39\\,119\\,0\\.2\\)_0\\%\\,rgba\\(219\\,39\\,119\\,0\\)_100\\%\\)\\]{background-image:radial-gradient(circle,#db277733,#db277700)!important}stagewise-companion-anchor .bg-\\[radial-gradient\\(circle\\,rgba\\(255\\,255\\,255\\,0\\)_60\\%\\,rgba\\(255\\,255\\,255\\,0\\.2\\)_70\\%\\)\\]{background-image:radial-gradient(circle,#fff0 60%,#fff3 70%)!important}stagewise-companion-anchor .from-blue-600{--tw-gradient-from:var(--color-blue-600)!important;--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))!important}stagewise-companion-anchor .from-indigo-700{--tw-gradient-from:var(--color-indigo-700)!important;--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))!important}stagewise-companion-anchor .from-orange-600{--tw-gradient-from:var(--color-orange-600)!important;--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))!important}stagewise-companion-anchor .from-sky-700{--tw-gradient-from:var(--color-sky-700)!important;--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))!important}stagewise-companion-anchor .via-blue-500{--tw-gradient-via:var(--color-blue-500)!important;--tw-gradient-via-stops:var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-via)var(--tw-gradient-via-position),var(--tw-gradient-to)var(--tw-gradient-to-position)!important;--tw-gradient-stops:var(--tw-gradient-via-stops)!important}stagewise-companion-anchor .to-fuchsia-700{--tw-gradient-to:var(--color-fuchsia-700)!important;--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))!important}stagewise-companion-anchor .to-red-600{--tw-gradient-to:var(--color-red-600)!important;--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))!important}stagewise-companion-anchor .to-sky-600{--tw-gradient-to:var(--color-sky-600)!important;--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))!important}stagewise-companion-anchor .to-teal-500{--tw-gradient-to:var(--color-teal-500)!important;--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))!important}stagewise-companion-anchor .fill-current{fill:currentColor!important}stagewise-companion-anchor .fill-white{fill:var(--color-white)!important}stagewise-companion-anchor .fill-zinc-500\\/50{fill:#71717b80!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .fill-zinc-500\\/50{fill:color-mix(in oklab,var(--color-zinc-500)50%,transparent)!important}}stagewise-companion-anchor .fill-zinc-950{fill:var(--color-zinc-950)!important}stagewise-companion-anchor .stroke-black\\/30{stroke:#0000004d!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .stroke-black\\/30{stroke:color-mix(in oklab,var(--color-black)30%,transparent)!important}}stagewise-companion-anchor .stroke-none{stroke:none!important}stagewise-companion-anchor .stroke-white{stroke:var(--color-white)!important}stagewise-companion-anchor .stroke-zinc-950{stroke:var(--color-zinc-950)!important}stagewise-companion-anchor .stroke-1{stroke-width:1px!important}stagewise-companion-anchor .p-0\\.5{padding:calc(var(--spacing)*.5)!important}stagewise-companion-anchor .p-1{padding:calc(var(--spacing)*1)!important}stagewise-companion-anchor .p-2{padding:calc(var(--spacing)*2)!important}stagewise-companion-anchor .p-3{padding:calc(var(--spacing)*3)!important}stagewise-companion-anchor .p-4{padding:calc(var(--spacing)*4)!important}stagewise-companion-anchor .px-0\\.5{padding-inline:calc(var(--spacing)*.5)!important}stagewise-companion-anchor .px-1{padding-inline:calc(var(--spacing)*1)!important}stagewise-companion-anchor .px-2{padding-inline:calc(var(--spacing)*2)!important}stagewise-companion-anchor .px-3{padding-inline:calc(var(--spacing)*3)!important}stagewise-companion-anchor .px-4{padding-inline:calc(var(--spacing)*4)!important}stagewise-companion-anchor .py-0{padding-block:calc(var(--spacing)*0)!important}stagewise-companion-anchor .py-0\\.5{padding-block:calc(var(--spacing)*.5)!important}stagewise-companion-anchor .py-2{padding-block:calc(var(--spacing)*2)!important}stagewise-companion-anchor .pt-2{padding-top:calc(var(--spacing)*2)!important}stagewise-companion-anchor .pt-4{padding-top:calc(var(--spacing)*4)!important}stagewise-companion-anchor .pr-6{padding-right:calc(var(--spacing)*6)!important}stagewise-companion-anchor .pb-4{padding-bottom:calc(var(--spacing)*4)!important}stagewise-companion-anchor .pl-2{padding-left:calc(var(--spacing)*2)!important}stagewise-companion-anchor .text-base{font-size:var(--text-base)!important;line-height:var(--tw-leading,var(--text-base--line-height))!important}stagewise-companion-anchor .text-lg{font-size:var(--text-lg)!important;line-height:var(--tw-leading,var(--text-lg--line-height))!important}stagewise-companion-anchor .text-sm{font-size:var(--text-sm)!important;line-height:var(--tw-leading,var(--text-sm--line-height))!important}stagewise-companion-anchor .text-xs{font-size:var(--text-xs)!important;line-height:var(--tw-leading,var(--text-xs--line-height))!important}stagewise-companion-anchor .text-\\[0\\.5em\\]{font-size:.5em!important}stagewise-companion-anchor .font-bold{--tw-font-weight:var(--font-weight-bold)!important;font-weight:var(--font-weight-bold)!important}stagewise-companion-anchor .font-medium{--tw-font-weight:var(--font-weight-medium)!important;font-weight:var(--font-weight-medium)!important}stagewise-companion-anchor .font-normal{--tw-font-weight:var(--font-weight-normal)!important;font-weight:var(--font-weight-normal)!important}stagewise-companion-anchor .font-semibold{--tw-font-weight:var(--font-weight-semibold)!important;font-weight:var(--font-weight-semibold)!important}stagewise-companion-anchor .text-amber-800{color:var(--color-amber-800)!important}stagewise-companion-anchor .text-blue-500{color:var(--color-blue-500)!important}stagewise-companion-anchor .text-blue-600{color:var(--color-blue-600)!important}stagewise-companion-anchor .text-blue-700{color:var(--color-blue-700)!important}stagewise-companion-anchor .text-blue-800{color:var(--color-blue-800)!important}stagewise-companion-anchor .text-foreground{color:var(--color-foreground)!important}stagewise-companion-anchor .text-indigo-700{color:var(--color-indigo-700)!important}stagewise-companion-anchor .text-orange-600{color:var(--color-orange-600)!important}stagewise-companion-anchor .text-orange-700{color:var(--color-orange-700)!important}stagewise-companion-anchor .text-orange-800{color:var(--color-orange-800)!important}stagewise-companion-anchor .text-red-600{color:var(--color-red-600)!important}stagewise-companion-anchor .text-red-700{color:var(--color-red-700)!important}stagewise-companion-anchor .text-transparent{color:#0000!important}stagewise-companion-anchor .text-violet-700{color:var(--color-violet-700)!important}stagewise-companion-anchor .text-white{color:var(--color-white)!important}stagewise-companion-anchor .text-zinc-500{color:var(--color-zinc-500)!important}stagewise-companion-anchor .text-zinc-600{color:var(--color-zinc-600)!important}stagewise-companion-anchor .text-zinc-700{color:var(--color-zinc-700)!important}stagewise-companion-anchor .text-zinc-950{color:var(--color-zinc-950)!important}stagewise-companion-anchor .opacity-0{opacity:0!important}stagewise-companion-anchor .opacity-20{opacity:.2!important}stagewise-companion-anchor .opacity-30{opacity:.3!important}stagewise-companion-anchor .opacity-80{opacity:.8!important}stagewise-companion-anchor .opacity-100{opacity:1!important}stagewise-companion-anchor .shadow{--tw-shadow:0 1px 3px 0 var(--tw-shadow-color,#0000001a),0 1px 2px -1px var(--tw-shadow-color,#0000001a)!important;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)!important}stagewise-companion-anchor .shadow-lg{--tw-shadow:0 10px 15px -3px var(--tw-shadow-color,#0000001a),0 4px 6px -4px var(--tw-shadow-color,#0000001a)!important;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)!important}stagewise-companion-anchor .shadow-md{--tw-shadow:0 4px 6px -1px var(--tw-shadow-color,#0000001a),0 2px 4px -2px var(--tw-shadow-color,#0000001a)!important;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)!important}stagewise-companion-anchor .shadow-sm{--tw-shadow:0 1px 3px 0 var(--tw-shadow-color,#0000001a),0 1px 2px -1px var(--tw-shadow-color,#0000001a)!important;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)!important}stagewise-companion-anchor .ring{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(1px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor)!important;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)!important}stagewise-companion-anchor .shadow-black\\/50{--tw-shadow-color:#00000080!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .shadow-black\\/50{--tw-shadow-color:color-mix(in oklab,color-mix(in oklab,var(--color-black)50%,transparent)var(--tw-shadow-alpha),transparent)!important}}stagewise-companion-anchor .ring-transparent{--tw-ring-color:transparent!important}stagewise-companion-anchor .ring-zinc-950\\/20{--tw-ring-color:#09090b33!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .ring-zinc-950\\/20{--tw-ring-color:color-mix(in oklab,var(--color-zinc-950)20%,transparent)!important}}stagewise-companion-anchor .outline{outline-style:var(--tw-outline-style)!important;outline-width:1px!important}stagewise-companion-anchor .blur{--tw-blur:blur(8px)!important;filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)!important}stagewise-companion-anchor .blur-md{--tw-blur:blur(var(--blur-md))!important;filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)!important}stagewise-companion-anchor .blur-none{--tw-blur: !important;filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)!important}stagewise-companion-anchor .drop-shadow-md{--tw-drop-shadow-size:drop-shadow(0 3px 3px var(--tw-drop-shadow-color,#0000001f))!important;--tw-drop-shadow:drop-shadow(var(--drop-shadow-md))!important;filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)!important}stagewise-companion-anchor .drop-shadow-xl{--tw-drop-shadow-size:drop-shadow(0 9px 7px var(--tw-drop-shadow-color,#0000001a))!important;--tw-drop-shadow:drop-shadow(var(--drop-shadow-xl))!important;filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)!important}stagewise-companion-anchor .drop-shadow-xs{--tw-drop-shadow-size:drop-shadow(0 1px 1px var(--tw-drop-shadow-color,#0000000d))!important;--tw-drop-shadow:drop-shadow(var(--drop-shadow-xs))!important;filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)!important}stagewise-companion-anchor .drop-shadow-black{--tw-drop-shadow-color:#000!important;--tw-drop-shadow:var(--tw-drop-shadow-size)!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .drop-shadow-black{--tw-drop-shadow-color:color-mix(in oklab,var(--color-black)var(--tw-drop-shadow-alpha),transparent)!important}}stagewise-companion-anchor .drop-shadow-indigo-950{--tw-drop-shadow-color:oklch(25.7% .09 281.288)!important;--tw-drop-shadow:var(--tw-drop-shadow-size)!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .drop-shadow-indigo-950{--tw-drop-shadow-color:color-mix(in oklab,var(--color-indigo-950)var(--tw-drop-shadow-alpha),transparent)!important}}stagewise-companion-anchor .filter{filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)!important}stagewise-companion-anchor .backdrop-blur{--tw-backdrop-blur:blur(8px)!important;-webkit-backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)!important;backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)!important}stagewise-companion-anchor .backdrop-blur-md{--tw-backdrop-blur:blur(var(--blur-md))!important;-webkit-backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)!important;backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)!important}stagewise-companion-anchor .backdrop-saturate-150{--tw-backdrop-saturate:saturate(150%)!important;-webkit-backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)!important;backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)!important}stagewise-companion-anchor .transition{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to,opacity,box-shadow,transform,translate,scale,rotate,filter,-webkit-backdrop-filter,backdrop-filter,display,visibility,content-visibility,overlay,pointer-events!important;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function))!important;transition-duration:var(--tw-duration,var(--default-transition-duration))!important}stagewise-companion-anchor .transition-all{transition-property:all!important;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function))!important;transition-duration:var(--tw-duration,var(--default-transition-duration))!important}stagewise-companion-anchor .transition-colors{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to!important;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function))!important;transition-duration:var(--tw-duration,var(--default-transition-duration))!important}stagewise-companion-anchor .duration-0{--tw-duration:0s!important;transition-duration:0s!important}stagewise-companion-anchor .duration-100{--tw-duration:.1s!important;transition-duration:.1s!important}stagewise-companion-anchor .duration-150{--tw-duration:.15s!important;transition-duration:.15s!important}stagewise-companion-anchor .duration-300{--tw-duration:.3s!important;transition-duration:.3s!important}stagewise-companion-anchor .duration-500{--tw-duration:.5s!important;transition-duration:.5s!important}stagewise-companion-anchor .ease-out{--tw-ease:var(--ease-out)!important;transition-timing-function:var(--ease-out)!important}stagewise-companion-anchor .outline-none{--tw-outline-style:none!important;outline-style:none!important}stagewise-companion-anchor .select-none{-webkit-user-select:none!important;-moz-user-select:none!important;user-select:none!important}stagewise-companion-anchor :is(.\\*\\:size-full>*){width:100%!important;height:100%!important}stagewise-companion-anchor .placeholder\\:text-zinc-400::-moz-placeholder{color:var(--color-zinc-400)!important}stagewise-companion-anchor .placeholder\\:text-zinc-400::placeholder{color:var(--color-zinc-400)!important}stagewise-companion-anchor .placeholder\\:text-zinc-950\\/50::-moz-placeholder{color:#09090b80!important}stagewise-companion-anchor .placeholder\\:text-zinc-950\\/50::placeholder{color:#09090b80!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .placeholder\\:text-zinc-950\\/50::-moz-placeholder{color:color-mix(in oklab,var(--color-zinc-950)50%,transparent)!important}stagewise-companion-anchor .placeholder\\:text-zinc-950\\/50::placeholder{color:color-mix(in oklab,var(--color-zinc-950)50%,transparent)!important}}stagewise-companion-anchor .placeholder\\:text-zinc-950\\/70::-moz-placeholder{color:#09090bb3!important}stagewise-companion-anchor .placeholder\\:text-zinc-950\\/70::placeholder{color:#09090bb3!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .placeholder\\:text-zinc-950\\/70::-moz-placeholder{color:color-mix(in oklab,var(--color-zinc-950)70%,transparent)!important}stagewise-companion-anchor .placeholder\\:text-zinc-950\\/70::placeholder{color:color-mix(in oklab,var(--color-zinc-950)70%,transparent)!important}}@media (hover:hover){stagewise-companion-anchor .hover\\:border-red-600\\/80:hover{border-color:#e40014cc!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .hover\\:border-red-600\\/80:hover{border-color:color-mix(in oklab,var(--color-red-600)80%,transparent)!important}}stagewise-companion-anchor .hover\\:bg-blue-200\\/80:hover{background-color:#bedbffcc!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .hover\\:bg-blue-200\\/80:hover{background-color:color-mix(in oklab,var(--color-blue-200)80%,transparent)!important}}stagewise-companion-anchor .hover\\:bg-orange-200:hover{background-color:var(--color-orange-200)!important}stagewise-companion-anchor .hover\\:bg-orange-700:hover{background-color:var(--color-orange-700)!important}stagewise-companion-anchor .hover\\:bg-red-600\\/20:hover{background-color:#e4001433!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .hover\\:bg-red-600\\/20:hover{background-color:color-mix(in oklab,var(--color-red-600)20%,transparent)!important}}stagewise-companion-anchor .hover\\:bg-zinc-500\\/20:hover{background-color:#71717b33!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .hover\\:bg-zinc-500\\/20:hover{background-color:color-mix(in oklab,var(--color-zinc-500)20%,transparent)!important}}stagewise-companion-anchor .hover\\:bg-zinc-950\\/5:hover{background-color:#09090b0d!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .hover\\:bg-zinc-950\\/5:hover{background-color:color-mix(in oklab,var(--color-zinc-950)5%,transparent)!important}}stagewise-companion-anchor .hover\\:bg-zinc-950\\/10:hover{background-color:#09090b1a!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .hover\\:bg-zinc-950\\/10:hover{background-color:color-mix(in oklab,var(--color-zinc-950)10%,transparent)!important}}stagewise-companion-anchor .hover\\:text-orange-800:hover{color:var(--color-orange-800)!important}stagewise-companion-anchor .hover\\:text-white:hover{color:var(--color-white)!important}stagewise-companion-anchor .hover\\:underline:hover{text-decoration-line:underline!important}stagewise-companion-anchor .hover\\:opacity-100:hover{opacity:1!important}stagewise-companion-anchor .hover\\:shadow-lg:hover{--tw-shadow:0 10px 15px -3px var(--tw-shadow-color,#0000001a),0 4px 6px -4px var(--tw-shadow-color,#0000001a)!important;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)!important}}stagewise-companion-anchor .focus\\:border-blue-500:focus{border-color:var(--color-blue-500)!important}stagewise-companion-anchor .focus\\:border-zinc-500:focus{border-color:var(--color-zinc-500)!important}stagewise-companion-anchor .focus\\:text-zinc-900:focus{color:var(--color-zinc-900)!important}stagewise-companion-anchor .focus\\:outline-none:focus{--tw-outline-style:none!important;outline-style:none!important}stagewise-companion-anchor .disabled\\:opacity-50:disabled{opacity:.5!important}stagewise-companion-anchor .data-focus\\:outline-none[data-focus]{--tw-outline-style:none!important;outline-style:none!important}stagewise-companion-anchor .animate-shake{animation:.5s ease-in-out 2 shake}}@keyframes shake{0%,to{transform:translate(0)}10%,30%,50%,70%,90%{transform:translate(-2px)}20%,40%,60%,80%{transform:translate(2px)}}@keyframes gradient-animation{0%{background-position:0%}50%{background-position:100%}to{background-position:0%}}stagewise-companion-anchor stagewise-companion-anchor{all:initial;interpolate-size:allow-keywords;transform:translate(0);color:var(--color-zinc-950)!important;letter-spacing:normal!important;text-rendering:auto!important;font-family:Inter,Noto Color Emoji,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,SF Compact,SF Pro,Helvetica Neue,sans-serif!important;font-weight:400!important;line-height:normal!important}@supports (font-variation-settings:normal){stagewise-companion-anchor stagewise-companion-anchor{font-optical-sizing:auto!important;font-family:InterVariable,Noto Color Emoji,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,SF Compact,SF Pro,Helvetica Neue,sans-serif!important}}stagewise-companion-anchor #headlessui-portal-root{z-index:50!important;width:100vw!important;height:100vh!important;position:fixed!important}stagewise-companion-anchor #headlessui-portal-root>*{pointer-events:auto!important}@property --tw-scale-x{syntax:"*";inherits:false;initial-value:1}@property --tw-scale-y{syntax:"*";inherits:false;initial-value:1}@property --tw-scale-z{syntax:"*";inherits:false;initial-value:1}@property --tw-rotate-x{syntax:"*";inherits:false}@property --tw-rotate-y{syntax:"*";inherits:false}@property --tw-rotate-z{syntax:"*";inherits:false}@property --tw-skew-x{syntax:"*";inherits:false}@property --tw-skew-y{syntax:"*";inherits:false}@property --tw-space-y-reverse{syntax:"*";inherits:false;initial-value:0}@property --tw-space-x-reverse{syntax:"*";inherits:false;initial-value:0}@property --tw-divide-y-reverse{syntax:"*";inherits:false;initial-value:0}@property --tw-border-style{syntax:"*";inherits:false;initial-value:solid}@property --tw-gradient-position{syntax:"*";inherits:false}@property --tw-gradient-from{syntax:"<color>";inherits:false;initial-value:#0000}@property --tw-gradient-via{syntax:"<color>";inherits:false;initial-value:#0000}@property --tw-gradient-to{syntax:"<color>";inherits:false;initial-value:#0000}@property --tw-gradient-stops{syntax:"*";inherits:false}@property --tw-gradient-via-stops{syntax:"*";inherits:false}@property --tw-gradient-from-position{syntax:"<length-percentage>";inherits:false;initial-value:0%}@property --tw-gradient-via-position{syntax:"<length-percentage>";inherits:false;initial-value:50%}@property --tw-gradient-to-position{syntax:"<length-percentage>";inherits:false;initial-value:100%}@property --tw-font-weight{syntax:"*";inherits:false}@property --tw-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-shadow-color{syntax:"*";inherits:false}@property --tw-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-inset-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-inset-shadow-color{syntax:"*";inherits:false}@property --tw-inset-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-ring-color{syntax:"*";inherits:false}@property --tw-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-inset-ring-color{syntax:"*";inherits:false}@property --tw-inset-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-ring-inset{syntax:"*";inherits:false}@property --tw-ring-offset-width{syntax:"<length>";inherits:false;initial-value:0}@property --tw-ring-offset-color{syntax:"*";inherits:false;initial-value:#fff}@property --tw-ring-offset-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-outline-style{syntax:"*";inherits:false;initial-value:solid}@property --tw-blur{syntax:"*";inherits:false}@property --tw-brightness{syntax:"*";inherits:false}@property --tw-contrast{syntax:"*";inherits:false}@property --tw-grayscale{syntax:"*";inherits:false}@property --tw-hue-rotate{syntax:"*";inherits:false}@property --tw-invert{syntax:"*";inherits:false}@property --tw-opacity{syntax:"*";inherits:false}@property --tw-saturate{syntax:"*";inherits:false}@property --tw-sepia{syntax:"*";inherits:false}@property --tw-drop-shadow{syntax:"*";inherits:false}@property --tw-drop-shadow-color{syntax:"*";inherits:false}@property --tw-drop-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-drop-shadow-size{syntax:"*";inherits:false}@property --tw-backdrop-blur{syntax:"*";inherits:false}@property --tw-backdrop-brightness{syntax:"*";inherits:false}@property --tw-backdrop-contrast{syntax:"*";inherits:false}@property --tw-backdrop-grayscale{syntax:"*";inherits:false}@property --tw-backdrop-hue-rotate{syntax:"*";inherits:false}@property --tw-backdrop-invert{syntax:"*";inherits:false}@property --tw-backdrop-opacity{syntax:"*";inherits:false}@property --tw-backdrop-saturate{syntax:"*";inherits:false}@property --tw-backdrop-sepia{syntax:"*";inherits:false}@property --tw-duration{syntax:"*";inherits:false}@property --tw-ease{syntax:"*";inherits:false}@keyframes spin{to{transform:rotate(360deg)}}@keyframes pulse{50%{opacity:.5}}';function yc(t){const e={},r=["id","class","name","type","href","src","alt","for","placeholder"],n=[];for(let o=0;o<t.attributes.length;o++){const a=t.attributes[o];a.name.startsWith("data-")?n.push({name:a.name,value:a.value}):(r.includes(a.name.toLowerCase())||a.name.toLowerCase()!=="style")&&(e[a.name]=a.value)}return n.forEach(o=>{e[o.name]=o.value}),e}function xc(t,e){var r;let n=`<element index="${e+1}">
`;n+=`  <tag>${t.tagName.toLowerCase()}</tag>
`;const o=t.id;o&&(n+=`  <id>${o}</id>
`);const a=Array.from(t.classList).join(", ");a&&(n+=`  <classes>${a}</classes>
`);const i=yc(t);if(Object.keys(i).length>0){n+=`  <attributes>
`;for(const[c,l]of Object.entries(i))(c.toLowerCase()!=="class"||!a)&&(n+=`    <${c}>${l}</${c}>
`);n+=`  </attributes>
`}const s=(r=t.innerText)==null?void 0:r.trim();if(s&&(n+=`  <text>${s.length>100?`${s.substring(0,100)}...`:s}</text>
`),n+=`  <structural_context>
`,t.parentElement){const c=t.parentElement;n+=`    <parent>
`,n+=`      <tag>${c.tagName.toLowerCase()}</tag>
`,c.id&&(n+=`      <id>${c.id}</id>
`);const l=Array.from(c.classList).join(", ");l&&(n+=`      <classes>${l}</classes>
`),n+=`    </parent>
`}else n+=`    <parent>No parent element found (likely root or disconnected)</parent>
`;n+=`  </structural_context>
`;try{const c=window.getComputedStyle(t),l={color:c.color,backgroundColor:c.backgroundColor,fontSize:c.fontSize,fontWeight:c.fontWeight,display:c.display};n+=`  <styles>
`;for(const[u,p]of Object.entries(l))n+=`    <${u}>${p}</${u}>
`;n+=`  </styles>
`}catch{n+=`  <styles>Could not retrieve computed styles</styles>
`}return n+=`</element>
`,n}function _c(t,e,r,n){const o=n.map(i=>`
      <plugin_contexts>
<${i.pluginName}>
${i.contextSnippets.map(s=>`    <${s.promptContextName}>${s.content}</${s.promptContextName}>`).join(`
`)}
</${i.pluginName}>
</plugin_contexts>
`.trim()).join(`
`);if(!t||t.length===0)return`
    <request>
      <user_goal>${e}</user_goal>
      <url>${r}</url>
  <context>No specific element was selected on the page. Please analyze the page code in general or ask for clarification.</context>
  ${o}
</request>`.trim();let a="";return t.forEach((i,s)=>{a+=xc(i,s)}),`
<request>
  <user_goal>${e}</user_goal>
  <url>${r}</url>
  <selected_elements>
    ${a.trim()}
  </selected_elements>
  ${o}
</request>`.trim()}const Qo=Re(null),ea="stgws:companion";function kc(){try{const t=sessionStorage.getItem(ea);return t?JSON.parse(t):{}}catch(t){return console.error("Failed to load state from storage:",t),{}}}function Cc(t){try{sessionStorage.setItem(ea,JSON.stringify(t))}catch(e){console.error("Failed to save state to storage:",e)}}function Sc({children:t}){const[e,r]=U(()=>{const h=kc();return{appBlockRequestList:[],appUnblockRequestList:[],lastBlockRequestNumber:0,lastUnblockRequestNumber:0,isMainAppBlocked:!1,toolbarBoxRef:Xr(),minimized:h.minimized??!1,requestMainAppBlock:()=>0,requestMainAppUnblock:()=>0,discardMainAppBlock:()=>{},discardMainAppUnblock:()=>{},setToolbarBoxRef:()=>{},unsetToolbarBoxRef:()=>{},minimize:()=>{},expand:()=>{}}});ne(()=>{Cc({minimized:e.minimized})},[e.minimized]);const n=S(()=>{let h=0;return r(w=>(h=w.lastBlockRequestNumber+1,{...w,appBlockRequestList:[...w.appBlockRequestList,h],lastBlockRequestNumber:h,isMainAppBlocked:w.appUnblockRequestList.length===0})),h},[]),o=S(()=>{let h=0;return r(w=>(h=w.lastUnblockRequestNumber+1,{...w,appUnblockRequestList:[...w.appUnblockRequestList,h],lastUnblockRequestNumber:h,isMainAppBlocked:!1})),h},[]),a=S(h=>{r(w=>{const y=w.appBlockRequestList.filter(f=>f!==h);return{...w,appBlockRequestList:y,isMainAppBlocked:y.length>0&&w.appUnblockRequestList.length===0}})},[]),i=S(h=>{r(w=>{const y=w.appUnblockRequestList.filter(f=>f!==h);return{...w,appUnblockRequestList:y,isMainAppBlocked:w.appBlockRequestList.length>0&&y.length===0}})},[]),s=S(h=>{r(w=>({...w,toolbarBoxRef:h}))},[]),c=S(()=>{r(h=>({...h,toolbarBoxRef:Xr()}))},[]),l=S(()=>{r(h=>({...h,minimized:!0}))},[]),u=S(()=>{r(h=>({...h,minimized:!1}))},[]),p={requestMainAppBlock:n,requestMainAppUnblock:o,discardMainAppBlock:a,discardMainAppUnblock:i,isMainAppBlocked:e.isMainAppBlocked,toolbarBoxRef:e.toolbarBoxRef,setToolbarBoxRef:s,unsetToolbarBoxRef:c,minimized:e.minimized,minimize:l,expand:u};return d(Qo.Provider,{value:p,children:t})}function _n(){const t=ke(Qo);if(!t)throw new Error("useAppState must be used within an AppStateProvider");return t}const ta=Re({chats:[],currentChatId:null,createChat:()=>"",deleteChat:()=>{},setCurrentChat:()=>{},setChatInput:()=>{},addChatDomContext:()=>{},removeChatDomContext:()=>{},addMessage:()=>{},chatAreaState:"hidden",setChatAreaState:()=>{},isPromptCreationActive:!1,startPromptCreation:()=>{},stopPromptCreation:()=>{},promptState:"idle",resetPromptState:()=>{}}),Nc=({children:t})=>{const[e,r]=U([{id:"new_chat",messages:[],title:"New chat",inputValue:"",domContextElements:[]}]),[n,o]=U("new_chat"),[a,i]=U("hidden"),[s,c]=U(!1),[l,u]=U("idle"),p=S(()=>{u("idle")},[]),{minimized:h}=_n(),{selectedSession:w,setShouldPromptWindowSelection:y,windows:f}=dt();ne(()=>{h&&(c(!1),i("hidden"))},[h]);const{bridge:k}=Fo(),R=S(()=>{const D=Ur(),F={id:D,title:null,messages:[],inputValue:"",domContextElements:[]};return r(ee=>[...ee,F]),o(D),D},[]),O=S(D=>{r(F=>{const ee=F.filter(W=>W.id!==D);return ee.length===0?[{id:"new_chat",messages:[],title:"New chat",inputValue:"",domContextElements:[]}]:ee}),n===D&&r(F=>(o(F[0].id),F))},[n]),G=S(D=>{o(D)},[]),de=S((D,F)=>{r(ee=>ee.map(W=>W.id===D?{...W,inputValue:F}:W))},[]),{plugins:oe}=Fr(),we=S(()=>{c(!0),a==="hidden"&&i("compact"),oe.forEach(D=>{var F;(F=D.onPromptingStart)==null||F.call(D)})},[a]),ie=S(()=>{c(!1),u("idle"),r(D=>D.map(F=>F.id===n?{...F,domContextElements:[]}:F)),a==="compact"&&i("hidden"),oe.forEach(D=>{var F;(F=D.onPromptingAbort)==null||F.call(D)})},[n,a]),M=S(D=>{i(D),D==="hidden"&&ie()},[i,ie]),ve=S((D,F)=>{const ee=oe.filter(W=>W.onContextElementSelect);r(W=>W.map(ge=>ge.id===D?{...ge,domContextElements:[...ge.domContextElements,{element:F,pluginContext:ee.map(E=>{var P;return{pluginName:E.pluginName,context:(P=E.onContextElementSelect)==null?void 0:P.call(E,F)}})}]}:ge))},[oe]),Fe=S((D,F)=>{r(ee=>ee.map(W=>W.id===D?{...W,domContextElements:W.domContextElements.filter(ge=>ge.element!==F)}:W))},[]),je=S(async(D,F,ee=!1)=>{if(!F.trim()||l==="loading")return;const W=e.find(z=>z.id===D);u("loading");const ge=[],E=oe.map(async z=>{var N;const j={id:Ur(),text:F,contextElements:(W==null?void 0:W.domContextElements.map(Z=>Z.element))||[],sentByPlugin:ee},q=await((N=z.onPromptSend)==null?void 0:N.call(z,j));if(!q||!q.contextSnippets||q.contextSnippets.length===0)return null;const ue=q.contextSnippets.map(async Z=>{const ae=typeof Z.content=="string"?Z.content:await Z.content();return{promptContextName:Z.promptContextName,content:ae}}),A=await Promise.all(ue);return A.length>0?{pluginName:z.pluginName,contextSnippets:A}:null});(await Promise.all(E)).forEach(z=>{z&&ge.push(z)});const P=_c(W==null?void 0:W.domContextElements.map(z=>z.element),F,window.location.href,ge),J={id:Ur(),content:F.trim(),sender:"user",type:"regular",timestamp:new Date};async function K(){if(k)try{const z=await k.call.triggerAgentPrompt({prompt:P,sessionId:w==null?void 0:w.sessionId},{onUpdate:N=>{}});z.result.success?(setTimeout(()=>{u("success")},1e3),r(N=>N.map(j=>j.id===D?{...j,inputValue:""}:j))):(z.result.errorCode&&z.result.errorCode==="session_mismatch"&&y(!0),u("error"),setTimeout(()=>{u("idle"),c(!1),r(N=>N.map(j=>j.id===D?{...j,inputValue:""}:j))},300))}catch{u("error"),setTimeout(()=>{u("idle"),c(!1),r(z=>z.map(N=>N.id===D?{...N,inputValue:""}:N))},300)}else y(!0),u("error"),setTimeout(()=>{u("idle"),c(!1),r(z=>z.map(N=>N.id===D?{...N,inputValue:""}:N))},300)}K(),a==="hidden"&&i("compact"),r(z=>z.map(N=>N.id===D?{...N,messages:[...N.messages,J],inputValue:F.trim(),domContextElements:[]}:N))},[a,k,e,c,i,w,l,u,oe]),Xe={chats:e,currentChatId:n,createChat:R,deleteChat:O,setCurrentChat:G,setChatInput:de,addMessage:je,chatAreaState:a,setChatAreaState:M,isPromptCreationActive:s,startPromptCreation:we,stopPromptCreation:ie,addChatDomContext:ve,removeChatDomContext:Fe,promptState:l,resetPromptState:p};return d(ta.Provider,{value:Xe,children:t})};function mr(){const t=ke(ta);if(!t)throw new Error("useChatState must be used within a ChatStateProvider");return t}function Ec({children:t,config:e}){return d(Ps,{config:e,children:d(Ts,{children:d(As,{children:d(Is,{children:d(Nc,{children:t})})})})})}function Rr(t,e,r,n=window){ne(()=>{if(!(typeof window>"u")&&n)return n.addEventListener(t,e,r),()=>n.removeEventListener(t,e,r)},[t,e,n,r])}function zc(){const{startPromptCreation:t,stopPromptCreation:e,isPromptCreationActive:r}=mr(),n=le(()=>({[Pr.CTRL_ALT_C]:()=>r?!1:(t(),!0),[Pr.ESC]:()=>r?(e(),!0):!1}),[t,e,r]),o=S(a=>{for(const[i,s]of Object.entries(dn))if(s.isEventMatching(a)){n[i]()&&(a.preventDefault(),a.stopPropagation());break}},[n]);return Rr("keydown",o,{capture:!0}),null}const ra=typeof document<"u"?bn.useLayoutEffect:()=>{};function Mc(t){const e=V(null);return ra(()=>{e.current=t},[t]),S((...r)=>{const n=e.current;return n==null?void 0:n(...r)},[])}const ut=t=>{var e;return(e=t==null?void 0:t.ownerDocument)!==null&&e!==void 0?e:document},vt=t=>t&&"window"in t&&t.window===t?t:ut(t).defaultView||window;function na(t,e){return e&&t?t.contains(e):!1}const un=(t=document)=>t.activeElement;function oa(t){return t.target}function $c(t){var e;return typeof window>"u"||window.navigator==null?!1:((e=window.navigator.userAgentData)===null||e===void 0?void 0:e.brands.some(r=>t.test(r.brand)))||t.test(window.navigator.userAgent)}function Tc(t){var e;return typeof window<"u"&&window.navigator!=null?t.test(((e=window.navigator.userAgentData)===null||e===void 0?void 0:e.platform)||window.navigator.platform):!1}function aa(t){let e=null;return()=>(e==null&&(e=t()),e)}const Ac=aa(function(){return Tc(/^Mac/i)}),Pc=aa(function(){return $c(/Android/i)});function ia(){let t=V(new Map),e=S((o,a,i,s)=>{let c=s!=null&&s.once?(...l)=>{t.current.delete(i),i(...l)}:i;t.current.set(i,{type:a,eventTarget:o,fn:c,options:s}),o.addEventListener(a,c,s)},[]),r=S((o,a,i,s)=>{var c;let l=((c=t.current.get(i))===null||c===void 0?void 0:c.fn)||i;o.removeEventListener(a,l,s),t.current.delete(i)},[]),n=S(()=>{t.current.forEach((o,a)=>{r(o.eventTarget,o.type,a,o.options)})},[r]);return ne(()=>n,[n]),{addGlobalListener:e,removeGlobalListener:r,removeAllGlobalListeners:n}}function Rc(t){return t.mozInputSource===0&&t.isTrusted?!0:Pc()&&t.pointerType?t.type==="click"&&t.buttons===1:t.detail===0&&!t.pointerType}function sa(t){let e=t;return e.nativeEvent=t,e.isDefaultPrevented=()=>e.defaultPrevented,e.isPropagationStopped=()=>e.cancelBubble,e.persist=()=>{},e}function Ic(t,e){Object.defineProperty(t,"target",{value:e}),Object.defineProperty(t,"currentTarget",{value:e})}function ca(t){let e=V({isFocused:!1,observer:null});ra(()=>{const n=e.current;return()=>{n.observer&&(n.observer.disconnect(),n.observer=null)}},[]);let r=Mc(n=>{t==null||t(n)});return S(n=>{if(n.target instanceof HTMLButtonElement||n.target instanceof HTMLInputElement||n.target instanceof HTMLTextAreaElement||n.target instanceof HTMLSelectElement){e.current.isFocused=!0;let o=n.target,a=i=>{if(e.current.isFocused=!1,o.disabled){let s=sa(i);r(s)}e.current.observer&&(e.current.observer.disconnect(),e.current.observer=null)};o.addEventListener("focusout",a,{once:!0}),e.current.observer=new MutationObserver(()=>{if(e.current.isFocused&&o.disabled){var i;(i=e.current.observer)===null||i===void 0||i.disconnect();let s=o===document.activeElement?null:document.activeElement;o.dispatchEvent(new FocusEvent("blur",{relatedTarget:s})),o.dispatchEvent(new FocusEvent("focusout",{bubbles:!0,relatedTarget:s}))}}),e.current.observer.observe(o,{attributes:!0,attributeFilter:["disabled"]})}},[r])}let Lc=!1,hr=null,pn=new Set,qt=new Map,kt=!1,mn=!1;const Oc={Tab:!0,Escape:!0};function kn(t,e){for(let r of pn)r(t,e)}function Fc(t){return!(t.metaKey||!Ac()&&t.altKey||t.ctrlKey||t.key==="Control"||t.key==="Shift"||t.key==="Meta")}function Ir(t){kt=!0,Fc(t)&&(hr="keyboard",kn("keyboard",t))}function Mt(t){hr="pointer",(t.type==="mousedown"||t.type==="pointerdown")&&(kt=!0,kn("pointer",t))}function la(t){Rc(t)&&(kt=!0,hr="virtual")}function da(t){t.target===window||t.target===document||Lc||!t.isTrusted||(!kt&&!mn&&(hr="virtual",kn("virtual",t)),kt=!1,mn=!1)}function ua(){kt=!1,mn=!0}function hn(t){if(typeof window>"u"||qt.get(vt(t)))return;const e=vt(t),r=ut(t);let n=e.HTMLElement.prototype.focus;e.HTMLElement.prototype.focus=function(){kt=!0,n.apply(this,arguments)},r.addEventListener("keydown",Ir,!0),r.addEventListener("keyup",Ir,!0),r.addEventListener("click",la,!0),e.addEventListener("focus",da,!0),e.addEventListener("blur",ua,!1),typeof PointerEvent<"u"&&(r.addEventListener("pointerdown",Mt,!0),r.addEventListener("pointermove",Mt,!0),r.addEventListener("pointerup",Mt,!0)),e.addEventListener("beforeunload",()=>{pa(t)},{once:!0}),qt.set(e,{focus:n})}const pa=(t,e)=>{const r=vt(t),n=ut(t);e&&n.removeEventListener("DOMContentLoaded",e),qt.has(r)&&(r.HTMLElement.prototype.focus=qt.get(r).focus,n.removeEventListener("keydown",Ir,!0),n.removeEventListener("keyup",Ir,!0),n.removeEventListener("click",la,!0),r.removeEventListener("focus",da,!0),r.removeEventListener("blur",ua,!1),typeof PointerEvent<"u"&&(n.removeEventListener("pointerdown",Mt,!0),n.removeEventListener("pointermove",Mt,!0),n.removeEventListener("pointerup",Mt,!0)),qt.delete(r))};function jc(t){const e=ut(t);let r;return e.readyState!=="loading"?hn(t):(r=()=>{hn(t)},e.addEventListener("DOMContentLoaded",r)),()=>pa(t,r)}typeof document<"u"&&jc();function ma(){return hr!=="pointer"}const Bc=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]);function Dc(t,e,r){let n=ut(r==null?void 0:r.target);const o=typeof window<"u"?vt(r==null?void 0:r.target).HTMLInputElement:HTMLInputElement,a=typeof window<"u"?vt(r==null?void 0:r.target).HTMLTextAreaElement:HTMLTextAreaElement,i=typeof window<"u"?vt(r==null?void 0:r.target).HTMLElement:HTMLElement,s=typeof window<"u"?vt(r==null?void 0:r.target).KeyboardEvent:KeyboardEvent;return t=t||n.activeElement instanceof o&&!Bc.has(n.activeElement.type)||n.activeElement instanceof a||n.activeElement instanceof i&&n.activeElement.isContentEditable,!(t&&e==="keyboard"&&r instanceof s&&!Oc[r.key])}function Vc(t,e,r){hn(),ne(()=>{let n=(o,a)=>{Dc(!!(r!=null&&r.isTextInput),o,a)&&t(ma())};return pn.add(n),()=>{pn.delete(n)}},e)}function Hc(t){let{isDisabled:e,onFocus:r,onBlur:n,onFocusChange:o}=t;const a=S(c=>{if(c.target===c.currentTarget)return n&&n(c),o&&o(!1),!0},[n,o]),i=ca(a),s=S(c=>{const l=ut(c.target),u=l?un(l):un();c.target===c.currentTarget&&u===oa(c.nativeEvent)&&(r&&r(c),o&&o(!0),i(c))},[o,r,i]);return{focusProps:{onFocus:!e&&(r||o||n)?s:void 0,onBlur:!e&&(n||o)?a:void 0}}}function Wc(t){let{isDisabled:e,onBlurWithin:r,onFocusWithin:n,onFocusWithinChange:o}=t,a=V({isFocusWithin:!1}),{addGlobalListener:i,removeAllGlobalListeners:s}=ia(),c=S(p=>{p.currentTarget.contains(p.target)&&a.current.isFocusWithin&&!p.currentTarget.contains(p.relatedTarget)&&(a.current.isFocusWithin=!1,s(),r&&r(p),o&&o(!1))},[r,o,a,s]),l=ca(c),u=S(p=>{if(!p.currentTarget.contains(p.target))return;const h=ut(p.target),w=un(h);if(!a.current.isFocusWithin&&w===oa(p.nativeEvent)){n&&n(p),o&&o(!0),a.current.isFocusWithin=!0,l(p);let y=p.currentTarget;i(h,"focus",f=>{if(a.current.isFocusWithin&&!na(y,f.target)){let k=new h.defaultView.FocusEvent("blur",{relatedTarget:f.target});Ic(k,y);let R=sa(k);c(R)}},{capture:!0})}},[n,o,l,i,c]);return e?{focusWithinProps:{onFocus:void 0,onBlur:void 0}}:{focusWithinProps:{onFocus:u,onBlur:c}}}let gn=!1,Gr=0;function Zc(){gn=!0,setTimeout(()=>{gn=!1},50)}function oo(t){t.pointerType==="touch"&&Zc()}function qc(){if(!(typeof document>"u"))return typeof PointerEvent<"u"&&document.addEventListener("pointerup",oo),Gr++,()=>{Gr--,!(Gr>0)&&(typeof PointerEvent<"u"&&document.removeEventListener("pointerup",oo))}}function ha(t){let{onHoverStart:e,onHoverChange:r,onHoverEnd:n,isDisabled:o}=t,[a,i]=U(!1),s=V({isHovered:!1,ignoreEmulatedMouseEvents:!1,pointerType:"",target:null}).current;ne(qc,[]);let{addGlobalListener:c,removeAllGlobalListeners:l}=ia(),{hoverProps:u,triggerHoverEnd:p}=le(()=>{let h=(f,k)=>{if(s.pointerType=k,o||k==="touch"||s.isHovered||!f.currentTarget.contains(f.target))return;s.isHovered=!0;let R=f.currentTarget;s.target=R,c(ut(f.target),"pointerover",O=>{s.isHovered&&s.target&&!na(s.target,O.target)&&w(O,O.pointerType)},{capture:!0}),e&&e({type:"hoverstart",target:R,pointerType:k}),r&&r(!0),i(!0)},w=(f,k)=>{let R=s.target;s.pointerType="",s.target=null,!(k==="touch"||!s.isHovered||!R)&&(s.isHovered=!1,l(),n&&n({type:"hoverend",target:R,pointerType:k}),r&&r(!1),i(!1))},y={};return typeof PointerEvent<"u"&&(y.onPointerEnter=f=>{gn&&f.pointerType==="mouse"||h(f,f.pointerType)},y.onPointerLeave=f=>{!o&&f.currentTarget.contains(f.target)&&w(f,f.pointerType)}),{hoverProps:y,triggerHoverEnd:w}},[e,r,n,o,s,c,l]);return ne(()=>{o&&p({currentTarget:s.target},s.pointerType)},[o]),{hoverProps:u,isHovered:a}}function ga(t={}){let{autoFocus:e=!1,isTextInput:r,within:n}=t,o=V({isFocused:!1,isFocusVisible:e||ma()}),[a,i]=U(!1),[s,c]=U(()=>o.current.isFocused&&o.current.isFocusVisible),l=S(()=>c(o.current.isFocused&&o.current.isFocusVisible),[]),u=S(w=>{o.current.isFocused=w,i(w),l()},[l]);Vc(w=>{o.current.isFocusVisible=w,l()},[],{isTextInput:r});let{focusProps:p}=Hc({isDisabled:n,onFocusChange:u}),{focusWithinProps:h}=Wc({isDisabled:!n,onFocusWithinChange:u});return{isFocused:a,isFocusVisible:s,focusProps:n?h:p}}var Uc=Object.defineProperty,Gc=(t,e,r)=>e in t?Uc(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Yr=(t,e,r)=>(Gc(t,typeof e!="symbol"?e+"":e,r),r);let Yc=class{constructor(){Yr(this,"current",this.detect()),Yr(this,"handoffState","pending"),Yr(this,"currentId",0)}set(t){this.current!==t&&(this.handoffState="pending",this.currentId=0,this.current=t)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return this.current==="server"}get isClient(){return this.current==="client"}detect(){return typeof window>"u"||typeof document>"u"?"server":"client"}handoff(){this.handoffState==="pending"&&(this.handoffState="complete")}get isHandoffComplete(){return this.handoffState==="complete"}},fa=new Yc;function Kc(t){var e,r;return fa.isServer?null:t?"ownerDocument"in t?t.ownerDocument:"current"in t?(r=(e=t.current)==null?void 0:e.ownerDocument)!=null?r:document:null:document}function Xc(t){typeof queueMicrotask=="function"?queueMicrotask(t):Promise.resolve().then(t).catch(e=>setTimeout(()=>{throw e}))}function wa(){let t=[],e={addEventListener(r,n,o,a){return r.addEventListener(n,o,a),e.add(()=>r.removeEventListener(n,o,a))},requestAnimationFrame(...r){let n=requestAnimationFrame(...r);return e.add(()=>cancelAnimationFrame(n))},nextFrame(...r){return e.requestAnimationFrame(()=>e.requestAnimationFrame(...r))},setTimeout(...r){let n=setTimeout(...r);return e.add(()=>clearTimeout(n))},microTask(...r){let n={current:!0};return Xc(()=>{n.current&&r[0]()}),e.add(()=>{n.current=!1})},style(r,n,o){let a=r.style.getPropertyValue(n);return Object.assign(r.style,{[n]:o}),this.add(()=>{Object.assign(r.style,{[n]:a})})},group(r){let n=wa();return r(n),this.add(()=>n.dispose())},add(r){return t.includes(r)||t.push(r),()=>{let n=t.indexOf(r);if(n>=0)for(let o of t.splice(n,1))o()}},dispose(){for(let r of t.splice(0))r()}};return e}function Jc(){let[t]=U(wa);return ne(()=>()=>t.dispose(),[t]),t}let Cn=(t,e)=>{fa.isServer?ne(t,e):Ot(t,e)};function Qc(t){let e=V(t);return Cn(()=>{e.current=t},[t]),e}let Lr=function(t){let e=Qc(t);return bn.useCallback((...r)=>e.current(...r),[e])};function el(t){let e=t.width/2,r=t.height/2;return{top:t.clientY-r,right:t.clientX+e,bottom:t.clientY+r,left:t.clientX-e}}function tl(t,e){return!(!t||!e||t.right<e.left||t.left>e.right||t.bottom<e.top||t.top>e.bottom)}function rl({disabled:t=!1}={}){let e=V(null),[r,n]=U(!1),o=Jc(),a=Lr(()=>{e.current=null,n(!1),o.dispose()}),i=Lr(s=>{if(o.dispose(),e.current===null){e.current=s.currentTarget,n(!0);{let c=Kc(s.currentTarget);o.addEventListener(c,"pointerup",a,!1),o.addEventListener(c,"pointermove",l=>{if(e.current){let u=el(l);n(tl(u,e.current.getBoundingClientRect()))}},!1),o.addEventListener(c,"pointercancel",a,!1)}}});return{pressed:r,pressProps:t?{}:{onPointerDown:i,onPointerUp:a,onClick:a}}}let nl=Re(void 0);function jr(){return ke(nl)}function ao(...t){return Array.from(new Set(t.flatMap(e=>typeof e=="string"?e.split(" "):[]))).filter(Boolean).join(" ")}function va(t,e,...r){if(t in e){let o=e[t];return typeof o=="function"?o(...r):o}let n=new Error(`Tried to handle "${t}" but there is no handler defined. Only defined handlers are: ${Object.keys(e).map(o=>`"${o}"`).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(n,va),n}var ol=(t=>(t[t.None=0]="None",t[t.RenderStrategy=1]="RenderStrategy",t[t.Static=2]="Static",t))(ol||{}),al=(t=>(t[t.Unmount=0]="Unmount",t[t.Hidden=1]="Hidden",t))(al||{});function Br(){let t=sl();return S(e=>il({mergeRefs:t,...e}),[t])}function il({ourProps:t,theirProps:e,slot:r,defaultTag:n,features:o,visible:a=!0,name:i,mergeRefs:s}){s=s??cl;let c=ba(e,t);if(a)return _r(c,r,n,i,s);let l=o??0;if(l&2){let{static:u=!1,...p}=c;if(u)return _r(p,r,n,i,s)}if(l&1){let{unmount:u=!0,...p}=c;return va(u?0:1,{0(){return null},1(){return _r({...p,hidden:!0,style:{display:"none"}},r,n,i,s)}})}return _r(c,r,n,i,s)}function _r(t,e={},r,n,o){let{as:a=r,children:i,refName:s="ref",...c}=Kr(t,["unmount","static"]),l=t.ref!==void 0?{[s]:t.ref}:{},u=typeof i=="function"?i(e):i;"className"in c&&c.className&&typeof c.className=="function"&&(c.className=c.className(e)),c["aria-labelledby"]&&c["aria-labelledby"]===c.id&&(c["aria-labelledby"]=void 0);let p={};if(e){let h=!1,w=[];for(let[y,f]of Object.entries(e))typeof f=="boolean"&&(h=!0),f===!0&&w.push(y.replace(/([A-Z])/g,k=>`-${k.toLowerCase()}`));if(h){p["data-headlessui-state"]=w.join(" ");for(let y of w)p[`data-${y}`]=""}}if(a===De&&(Object.keys(Nt(c)).length>0||Object.keys(Nt(p)).length>0))if(!ur(u)||Array.isArray(u)&&u.length>1){if(Object.keys(Nt(c)).length>0)throw new Error(['Passing props on "Fragment"!',"",`The current component <${n} /> is rendering a "Fragment".`,"However we need to passthrough the following props:",Object.keys(Nt(c)).concat(Object.keys(Nt(p))).map(h=>`  - ${h}`).join(`
`),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map(h=>`  - ${h}`).join(`
`)].join(`
`))}else{let h=u.props,w=h==null?void 0:h.className,y=typeof w=="function"?(...R)=>ao(w(...R),c.className):ao(w,c.className),f=y?{className:y}:{},k=ba(u.props,Nt(Kr(c,["ref"])));for(let R in p)R in k&&delete p[R];return ko(u,Object.assign({},k,p,l,{ref:o(ll(u),l.ref)},f))}return Ae(a,Object.assign({},Kr(c,["ref"]),a!==De&&l,a!==De&&p),u)}function sl(){let t=V([]),e=S(r=>{for(let n of t.current)n!=null&&(typeof n=="function"?n(r):n.current=r)},[]);return(...r)=>{if(!r.every(n=>n==null))return t.current=r,e}}function cl(...t){return t.every(e=>e==null)?void 0:e=>{for(let r of t)r!=null&&(typeof r=="function"?r(e):r.current=e)}}function ba(...t){if(t.length===0)return{};if(t.length===1)return t[0];let e={},r={};for(let n of t)for(let o in n)o.startsWith("on")&&typeof n[o]=="function"?(r[o]!=null||(r[o]=[]),r[o].push(n[o])):e[o]=n[o];if(e.disabled||e["aria-disabled"])for(let n in r)/^(on(?:Click|Pointer|Mouse|Key)(?:Down|Up|Press)?)$/.test(n)&&(r[n]=[o=>{var a;return(a=o==null?void 0:o.preventDefault)==null?void 0:a.call(o)}]);for(let n in r)Object.assign(e,{[n](o,...a){let i=r[n];for(let s of i){if((o instanceof Event||(o==null?void 0:o.nativeEvent)instanceof Event)&&o.defaultPrevented)return;s(o,...a)}}});return e}function ya(...t){if(t.length===0)return{};if(t.length===1)return t[0];let e={},r={};for(let n of t)for(let o in n)o.startsWith("on")&&typeof n[o]=="function"?(r[o]!=null||(r[o]=[]),r[o].push(n[o])):e[o]=n[o];for(let n in r)Object.assign(e,{[n](...o){let a=r[n];for(let i of a)i==null||i(...o)}});return e}function Dr(t){var e;return Object.assign(dr(t),{displayName:(e=t.displayName)!=null?e:t.name})}function Nt(t){let e=Object.assign({},t);for(let r in e)e[r]===void 0&&delete e[r];return e}function Kr(t,e=[]){let r=Object.assign({},t);for(let n of e)n in r&&delete r[n];return r}function ll(t){return bn.version.split(".")[0]>="19"?t.props.ref:t.ref}let dl="button";function ul(t,e){var r;let n=jr(),{disabled:o=n||!1,autoFocus:a=!1,...i}=t,{isFocusVisible:s,focusProps:c}=ga({autoFocus:a}),{isHovered:l,hoverProps:u}=ha({isDisabled:o}),{pressed:p,pressProps:h}=rl({disabled:o}),w=ya({ref:e,type:(r=i.type)!=null?r:"button",disabled:o||void 0,autoFocus:a},c,u,h),y=le(()=>({disabled:o,hover:l,focus:s,active:p,autofocus:a}),[o,l,s,p,a]);return Br()({ourProps:w,theirProps:i,slot:y,defaultTag:dl,name:"Button"})}let Sn=Dr(ul),pl=Re(void 0);function xa(){return ke(pl)}let ml=Symbol();function _a(...t){let e=V(t);ne(()=>{e.current=t},[t]);let r=Lr(n=>{for(let o of e.current)o!=null&&(typeof o=="function"?o(n):o.current=n)});return t.every(n=>n==null||(n==null?void 0:n[ml]))?void 0:r}let Nn=Re(null);Nn.displayName="DescriptionContext";function ka(){let t=ke(Nn);if(t===null){let e=new Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(e,ka),e}return t}function hl(){var t,e;return(e=(t=ke(Nn))==null?void 0:t.value)!=null?e:void 0}let gl="p";function fl(t,e){let r=lr(),n=jr(),{id:o=`headlessui-description-${r}`,...a}=t,i=ka(),s=_a(e);Cn(()=>i.register(o),[o,i.register]);let c=n||!1,l=le(()=>({...i.slot,disabled:c}),[i.slot,c]),u={ref:s,...i.props,id:o};return Br()({ourProps:u,theirProps:a,slot:l,defaultTag:gl,name:i.name||"Description"})}let wl=Dr(fl);Object.assign(wl,{});let En=Re(null);En.displayName="LabelContext";function Ca(){let t=ke(En);if(t===null){let e=new Error("You used a <Label /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(e,Ca),e}return t}function vl(t){var e,r,n;let o=(r=(e=ke(En))==null?void 0:e.value)!=null?r:void 0;return((n=void 0)!=null?n:0)>0?[o,...t].filter(Boolean).join(" "):o}let bl="label";function yl(t,e){var r;let n=lr(),o=Ca(),a=xa(),i=jr(),{id:s=`headlessui-label-${n}`,htmlFor:c=a??((r=o.props)==null?void 0:r.htmlFor),passive:l=!1,...u}=t,p=_a(e);Cn(()=>o.register(s),[s,o.register]);let h=Lr(k=>{let R=k.currentTarget;if(R instanceof HTMLLabelElement&&k.preventDefault(),o.props&&"onClick"in o.props&&typeof o.props.onClick=="function"&&o.props.onClick(k),R instanceof HTMLLabelElement){let O=document.getElementById(R.htmlFor);if(O){let G=O.getAttribute("disabled");if(G==="true"||G==="")return;let de=O.getAttribute("aria-disabled");if(de==="true"||de==="")return;(O instanceof HTMLInputElement&&(O.type==="radio"||O.type==="checkbox")||O.role==="radio"||O.role==="checkbox"||O.role==="switch")&&O.click(),O.focus({preventScroll:!0})}}}),w=i||!1,y=le(()=>({...o.slot,disabled:w}),[o.slot,w]),f={ref:p,...o.props,id:s,htmlFor:c,onClick:h};return l&&("onClick"in f&&(delete f.htmlFor,delete f.onClick),"onClick"in u&&delete u.onClick),Br()({ourProps:f,theirProps:u,slot:y,defaultTag:c?bl:"div",name:o.name||"Label"})}let xl=Dr(yl);Object.assign(xl,{});let _l="textarea";function kl(t,e){let r=lr(),n=xa(),o=jr(),{id:a=n||`headlessui-textarea-${r}`,disabled:i=o||!1,autoFocus:s=!1,invalid:c=!1,...l}=t,u=vl(),p=hl(),{isFocused:h,focusProps:w}=ga({autoFocus:s}),{isHovered:y,hoverProps:f}=ha({isDisabled:i}),k=ya({ref:e,id:a,"aria-labelledby":u,"aria-describedby":p,"aria-invalid":c?"true":void 0,disabled:i||void 0,autoFocus:s},w,f),R=le(()=>({disabled:i,invalid:c,hover:y,focus:h,autofocus:s}),[i,c,y,h,s]);return Br()({ourProps:k,theirProps:l,slot:R,defaultTag:_l,name:"Textarea"})}let Cl=Dr(kl);/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sl=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Nl=t=>t.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,n)=>n?n.toUpperCase():r.toLowerCase()),io=t=>{const e=Nl(t);return e.charAt(0).toUpperCase()+e.slice(1)},Sa=(...t)=>t.filter((e,r,n)=>!!e&&e.trim()!==""&&n.indexOf(e)===r).join(" ").trim(),El=t=>{for(const e in t)if(e.startsWith("aria-")||e==="role"||e==="title")return!0};/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var zl={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ml=dr(({color:t="currentColor",size:e=24,strokeWidth:r=2,absoluteStrokeWidth:n,className:o="",children:a,iconNode:i,...s},c)=>Ae("svg",{ref:c,...zl,width:e,height:e,stroke:t,strokeWidth:n?Number(r)*24/Number(e):r,className:Sa("lucide",o),...!a&&!El(s)&&{"aria-hidden":"true"},...s},[...i.map(([l,u])=>Ae(l,u)),...Array.isArray(a)?a:[a]]));/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ze=(t,e)=>{const r=dr(({className:n,...o},a)=>Ae(Ml,{ref:a,iconNode:e,className:Sa(`lucide-${Sl(io(t))}`,`lucide-${t}`,n),...o}));return r.displayName=io(t),r};/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $l=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],Tl=Ze("chevron-down",$l);/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Al=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],Pl=Ze("chevron-up",Al);/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rl=[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]],Il=Ze("message-circle",Rl);/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ll=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]],Ol=Ze("plus",Ll);/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fl=[["path",{d:"M15.39 4.39a1 1 0 0 0 1.68-.474 2.5 2.5 0 1 1 3.014 3.015 1 1 0 0 0-.474 1.68l1.683 1.682a2.414 2.414 0 0 1 0 3.414L19.61 15.39a1 1 0 0 1-1.68-.474 2.5 2.5 0 1 0-3.014 3.015 1 1 0 0 1 .474 1.68l-1.683 1.682a2.414 2.414 0 0 1-3.414 0L8.61 19.61a1 1 0 0 0-1.68.474 2.5 2.5 0 1 1-3.014-3.015 1 1 0 0 0 .474-1.68l-1.683-1.682a2.414 2.414 0 0 1 0-3.414L4.39 8.61a1 1 0 0 1 1.68.474 2.5 2.5 0 1 0 3.014-3.015 1 1 0 0 1-.474-1.68l1.683-1.682a2.414 2.414 0 0 1 3.414 0z",key:"w46dr5"}]],jl=Ze("puzzle",Fl);/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bl=[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]],Bt=Ze("refresh-cw",Bl);/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dl=[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]],Vl=Ze("send",Dl);/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hl=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],Wl=Ze("settings",Hl);/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zl=[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]],ql=Ze("trash-2",Zl);/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ul=[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}],["path",{d:"M5 12.859a10 10 0 0 1 5.17-2.69",key:"1dl1wf"}],["path",{d:"M19 12.859a10 10 0 0 0-2.007-1.523",key:"4k23kn"}],["path",{d:"M2 8.82a15 15 0 0 1 4.177-2.643",key:"1grhjp"}],["path",{d:"M22 8.82a15 15 0 0 0-11.288-3.764",key:"z3jwby"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]],Na=Ze("wifi-off",Ul),Gl={"Amazon Silk":"amazon_silk","Android Browser":"android",Bada:"bada",BlackBerry:"blackberry",Chrome:"chrome",Chromium:"chromium",Electron:"electron",Epiphany:"epiphany",Firefox:"firefox",Focus:"focus",Generic:"generic","Google Search":"google_search",Googlebot:"googlebot","Internet Explorer":"ie","K-Meleon":"k_meleon",Maxthon:"maxthon","Microsoft Edge":"edge","MZ Browser":"mz","NAVER Whale Browser":"naver",Opera:"opera","Opera Coast":"opera_coast",PhantomJS:"phantomjs",Puffin:"puffin",QupZilla:"qupzilla",QQ:"qq",QQLite:"qqlite",Safari:"safari",Sailfish:"sailfish","Samsung Internet for Android":"samsung_internet",SeaMonkey:"seamonkey",Sleipnir:"sleipnir",Swing:"swing",Tizen:"tizen","UC Browser":"uc",Vivaldi:"vivaldi","WebOS Browser":"webos",WeChat:"wechat","Yandex Browser":"yandex",Roku:"roku"},Ea={amazon_silk:"Amazon Silk",android:"Android Browser",bada:"Bada",blackberry:"BlackBerry",chrome:"Chrome",chromium:"Chromium",electron:"Electron",epiphany:"Epiphany",firefox:"Firefox",focus:"Focus",generic:"Generic",googlebot:"Googlebot",google_search:"Google Search",ie:"Internet Explorer",k_meleon:"K-Meleon",maxthon:"Maxthon",edge:"Microsoft Edge",mz:"MZ Browser",naver:"NAVER Whale Browser",opera:"Opera",opera_coast:"Opera Coast",phantomjs:"PhantomJS",puffin:"Puffin",qupzilla:"QupZilla",qq:"QQ Browser",qqlite:"QQ Browser Lite",safari:"Safari",sailfish:"Sailfish",samsung_internet:"Samsung Internet for Android",seamonkey:"SeaMonkey",sleipnir:"Sleipnir",swing:"Swing",tizen:"Tizen",uc:"UC Browser",vivaldi:"Vivaldi",webos:"WebOS Browser",wechat:"WeChat",yandex:"Yandex Browser"},me={tablet:"tablet",mobile:"mobile",desktop:"desktop",tv:"tv"},Ce={WindowsPhone:"Windows Phone",Windows:"Windows",MacOS:"macOS",iOS:"iOS",Android:"Android",WebOS:"WebOS",BlackBerry:"BlackBerry",Bada:"Bada",Tizen:"Tizen",Linux:"Linux",ChromeOS:"Chrome OS",PlayStation4:"PlayStation 4",Roku:"Roku"},nt={EdgeHTML:"EdgeHTML",Blink:"Blink",Trident:"Trident",Presto:"Presto",Gecko:"Gecko",WebKit:"WebKit"};class m{static getFirstMatch(e,r){const n=r.match(e);return n&&n.length>0&&n[1]||""}static getSecondMatch(e,r){const n=r.match(e);return n&&n.length>1&&n[2]||""}static matchAndReturnConst(e,r,n){if(e.test(r))return n}static getWindowsVersionName(e){switch(e){case"NT":return"NT";case"XP":return"XP";case"NT 5.0":return"2000";case"NT 5.1":return"XP";case"NT 5.2":return"2003";case"NT 6.0":return"Vista";case"NT 6.1":return"7";case"NT 6.2":return"8";case"NT 6.3":return"8.1";case"NT 10.0":return"10";default:return}}static getMacOSVersionName(e){const r=e.split(".").splice(0,2).map(n=>parseInt(n,10)||0);if(r.push(0),r[0]===10)switch(r[1]){case 5:return"Leopard";case 6:return"Snow Leopard";case 7:return"Lion";case 8:return"Mountain Lion";case 9:return"Mavericks";case 10:return"Yosemite";case 11:return"El Capitan";case 12:return"Sierra";case 13:return"High Sierra";case 14:return"Mojave";case 15:return"Catalina";default:return}}static getAndroidVersionName(e){const r=e.split(".").splice(0,2).map(n=>parseInt(n,10)||0);if(r.push(0),!(r[0]===1&&r[1]<5)){if(r[0]===1&&r[1]<6)return"Cupcake";if(r[0]===1&&r[1]>=6)return"Donut";if(r[0]===2&&r[1]<2)return"Eclair";if(r[0]===2&&r[1]===2)return"Froyo";if(r[0]===2&&r[1]>2)return"Gingerbread";if(r[0]===3)return"Honeycomb";if(r[0]===4&&r[1]<1)return"Ice Cream Sandwich";if(r[0]===4&&r[1]<4)return"Jelly Bean";if(r[0]===4&&r[1]>=4)return"KitKat";if(r[0]===5)return"Lollipop";if(r[0]===6)return"Marshmallow";if(r[0]===7)return"Nougat";if(r[0]===8)return"Oreo";if(r[0]===9)return"Pie"}}static getVersionPrecision(e){return e.split(".").length}static compareVersions(e,r,n=!1){const o=m.getVersionPrecision(e),a=m.getVersionPrecision(r);let i=Math.max(o,a),s=0;const c=m.map([e,r],l=>{const u=i-m.getVersionPrecision(l),p=l+new Array(u+1).join(".0");return m.map(p.split("."),h=>new Array(20-h.length).join("0")+h).reverse()});for(n&&(s=i-Math.min(o,a)),i-=1;i>=s;){if(c[0][i]>c[1][i])return 1;if(c[0][i]===c[1][i]){if(i===s)return 0;i-=1}else if(c[0][i]<c[1][i])return-1}}static map(e,r){const n=[];let o;if(Array.prototype.map)return Array.prototype.map.call(e,r);for(o=0;o<e.length;o+=1)n.push(r(e[o]));return n}static find(e,r){let n,o;if(Array.prototype.find)return Array.prototype.find.call(e,r);for(n=0,o=e.length;n<o;n+=1){const a=e[n];if(r(a,n))return a}}static assign(e,...r){const n=e;let o,a;if(Object.assign)return Object.assign(e,...r);for(o=0,a=r.length;o<a;o+=1){const i=r[o];typeof i=="object"&&i!==null&&Object.keys(i).forEach(s=>{n[s]=i[s]})}return e}static getBrowserAlias(e){return Gl[e]}static getBrowserTypeByAlias(e){return Ea[e]||""}}const X=/version\/(\d+(\.?_?\d+)+)/i,Yl=[{test:[/googlebot/i],describe(t){const e={name:"Googlebot"},r=m.getFirstMatch(/googlebot\/(\d+(\.\d+))/i,t)||m.getFirstMatch(X,t);return r&&(e.version=r),e}},{test:[/opera/i],describe(t){const e={name:"Opera"},r=m.getFirstMatch(X,t)||m.getFirstMatch(/(?:opera)[\s/](\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/opr\/|opios/i],describe(t){const e={name:"Opera"},r=m.getFirstMatch(/(?:opr|opios)[\s/](\S+)/i,t)||m.getFirstMatch(X,t);return r&&(e.version=r),e}},{test:[/SamsungBrowser/i],describe(t){const e={name:"Samsung Internet for Android"},r=m.getFirstMatch(X,t)||m.getFirstMatch(/(?:SamsungBrowser)[\s/](\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/Whale/i],describe(t){const e={name:"NAVER Whale Browser"},r=m.getFirstMatch(X,t)||m.getFirstMatch(/(?:whale)[\s/](\d+(?:\.\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/MZBrowser/i],describe(t){const e={name:"MZ Browser"},r=m.getFirstMatch(/(?:MZBrowser)[\s/](\d+(?:\.\d+)+)/i,t)||m.getFirstMatch(X,t);return r&&(e.version=r),e}},{test:[/focus/i],describe(t){const e={name:"Focus"},r=m.getFirstMatch(/(?:focus)[\s/](\d+(?:\.\d+)+)/i,t)||m.getFirstMatch(X,t);return r&&(e.version=r),e}},{test:[/swing/i],describe(t){const e={name:"Swing"},r=m.getFirstMatch(/(?:swing)[\s/](\d+(?:\.\d+)+)/i,t)||m.getFirstMatch(X,t);return r&&(e.version=r),e}},{test:[/coast/i],describe(t){const e={name:"Opera Coast"},r=m.getFirstMatch(X,t)||m.getFirstMatch(/(?:coast)[\s/](\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/opt\/\d+(?:.?_?\d+)+/i],describe(t){const e={name:"Opera Touch"},r=m.getFirstMatch(/(?:opt)[\s/](\d+(\.?_?\d+)+)/i,t)||m.getFirstMatch(X,t);return r&&(e.version=r),e}},{test:[/yabrowser/i],describe(t){const e={name:"Yandex Browser"},r=m.getFirstMatch(/(?:yabrowser)[\s/](\d+(\.?_?\d+)+)/i,t)||m.getFirstMatch(X,t);return r&&(e.version=r),e}},{test:[/ucbrowser/i],describe(t){const e={name:"UC Browser"},r=m.getFirstMatch(X,t)||m.getFirstMatch(/(?:ucbrowser)[\s/](\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/Maxthon|mxios/i],describe(t){const e={name:"Maxthon"},r=m.getFirstMatch(X,t)||m.getFirstMatch(/(?:Maxthon|mxios)[\s/](\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/epiphany/i],describe(t){const e={name:"Epiphany"},r=m.getFirstMatch(X,t)||m.getFirstMatch(/(?:epiphany)[\s/](\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/puffin/i],describe(t){const e={name:"Puffin"},r=m.getFirstMatch(X,t)||m.getFirstMatch(/(?:puffin)[\s/](\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/sleipnir/i],describe(t){const e={name:"Sleipnir"},r=m.getFirstMatch(X,t)||m.getFirstMatch(/(?:sleipnir)[\s/](\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/k-meleon/i],describe(t){const e={name:"K-Meleon"},r=m.getFirstMatch(X,t)||m.getFirstMatch(/(?:k-meleon)[\s/](\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/micromessenger/i],describe(t){const e={name:"WeChat"},r=m.getFirstMatch(/(?:micromessenger)[\s/](\d+(\.?_?\d+)+)/i,t)||m.getFirstMatch(X,t);return r&&(e.version=r),e}},{test:[/qqbrowser/i],describe(t){const e={name:/qqbrowserlite/i.test(t)?"QQ Browser Lite":"QQ Browser"},r=m.getFirstMatch(/(?:qqbrowserlite|qqbrowser)[/](\d+(\.?_?\d+)+)/i,t)||m.getFirstMatch(X,t);return r&&(e.version=r),e}},{test:[/msie|trident/i],describe(t){const e={name:"Internet Explorer"},r=m.getFirstMatch(/(?:msie |rv:)(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/\sedg\//i],describe(t){const e={name:"Microsoft Edge"},r=m.getFirstMatch(/\sedg\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/edg([ea]|ios)/i],describe(t){const e={name:"Microsoft Edge"},r=m.getSecondMatch(/edg([ea]|ios)\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/vivaldi/i],describe(t){const e={name:"Vivaldi"},r=m.getFirstMatch(/vivaldi\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/seamonkey/i],describe(t){const e={name:"SeaMonkey"},r=m.getFirstMatch(/seamonkey\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/sailfish/i],describe(t){const e={name:"Sailfish"},r=m.getFirstMatch(/sailfish\s?browser\/(\d+(\.\d+)?)/i,t);return r&&(e.version=r),e}},{test:[/silk/i],describe(t){const e={name:"Amazon Silk"},r=m.getFirstMatch(/silk\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/phantom/i],describe(t){const e={name:"PhantomJS"},r=m.getFirstMatch(/phantomjs\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/slimerjs/i],describe(t){const e={name:"SlimerJS"},r=m.getFirstMatch(/slimerjs\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe(t){const e={name:"BlackBerry"},r=m.getFirstMatch(X,t)||m.getFirstMatch(/blackberry[\d]+\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/(web|hpw)[o0]s/i],describe(t){const e={name:"WebOS Browser"},r=m.getFirstMatch(X,t)||m.getFirstMatch(/w(?:eb)?[o0]sbrowser\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/bada/i],describe(t){const e={name:"Bada"},r=m.getFirstMatch(/dolfin\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/tizen/i],describe(t){const e={name:"Tizen"},r=m.getFirstMatch(/(?:tizen\s?)?browser\/(\d+(\.?_?\d+)+)/i,t)||m.getFirstMatch(X,t);return r&&(e.version=r),e}},{test:[/qupzilla/i],describe(t){const e={name:"QupZilla"},r=m.getFirstMatch(/(?:qupzilla)[\s/](\d+(\.?_?\d+)+)/i,t)||m.getFirstMatch(X,t);return r&&(e.version=r),e}},{test:[/firefox|iceweasel|fxios/i],describe(t){const e={name:"Firefox"},r=m.getFirstMatch(/(?:firefox|iceweasel|fxios)[\s/](\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/electron/i],describe(t){const e={name:"Electron"},r=m.getFirstMatch(/(?:electron)\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/MiuiBrowser/i],describe(t){const e={name:"Miui"},r=m.getFirstMatch(/(?:MiuiBrowser)[\s/](\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/chromium/i],describe(t){const e={name:"Chromium"},r=m.getFirstMatch(/(?:chromium)[\s/](\d+(\.?_?\d+)+)/i,t)||m.getFirstMatch(X,t);return r&&(e.version=r),e}},{test:[/chrome|crios|crmo/i],describe(t){const e={name:"Chrome"},r=m.getFirstMatch(/(?:chrome|crios|crmo)\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/GSA/i],describe(t){const e={name:"Google Search"},r=m.getFirstMatch(/(?:GSA)\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test(t){const e=!t.test(/like android/i),r=t.test(/android/i);return e&&r},describe(t){const e={name:"Android Browser"},r=m.getFirstMatch(X,t);return r&&(e.version=r),e}},{test:[/playstation 4/i],describe(t){const e={name:"PlayStation 4"},r=m.getFirstMatch(X,t);return r&&(e.version=r),e}},{test:[/safari|applewebkit/i],describe(t){const e={name:"Safari"},r=m.getFirstMatch(X,t);return r&&(e.version=r),e}},{test:[/.*/i],describe(t){const e=/^(.*)\/(.*) /,r=/^(.*)\/(.*)[ \t]\((.*)/,n=t.search("\\(")!==-1?r:e;return{name:m.getFirstMatch(n,t),version:m.getSecondMatch(n,t)}}}],Kl=[{test:[/Roku\/DVP/],describe(t){const e=m.getFirstMatch(/Roku\/DVP-(\d+\.\d+)/i,t);return{name:Ce.Roku,version:e}}},{test:[/windows phone/i],describe(t){const e=m.getFirstMatch(/windows phone (?:os)?\s?(\d+(\.\d+)*)/i,t);return{name:Ce.WindowsPhone,version:e}}},{test:[/windows /i],describe(t){const e=m.getFirstMatch(/Windows ((NT|XP)( \d\d?.\d)?)/i,t),r=m.getWindowsVersionName(e);return{name:Ce.Windows,version:e,versionName:r}}},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe(t){const e={name:Ce.iOS},r=m.getSecondMatch(/(Version\/)(\d[\d.]+)/,t);return r&&(e.version=r),e}},{test:[/macintosh/i],describe(t){const e=m.getFirstMatch(/mac os x (\d+(\.?_?\d+)+)/i,t).replace(/[_\s]/g,"."),r=m.getMacOSVersionName(e),n={name:Ce.MacOS,version:e};return r&&(n.versionName=r),n}},{test:[/(ipod|iphone|ipad)/i],describe(t){const e=m.getFirstMatch(/os (\d+([_\s]\d+)*) like mac os x/i,t).replace(/[_\s]/g,".");return{name:Ce.iOS,version:e}}},{test(t){const e=!t.test(/like android/i),r=t.test(/android/i);return e&&r},describe(t){const e=m.getFirstMatch(/android[\s/-](\d+(\.\d+)*)/i,t),r=m.getAndroidVersionName(e),n={name:Ce.Android,version:e};return r&&(n.versionName=r),n}},{test:[/(web|hpw)[o0]s/i],describe(t){const e=m.getFirstMatch(/(?:web|hpw)[o0]s\/(\d+(\.\d+)*)/i,t),r={name:Ce.WebOS};return e&&e.length&&(r.version=e),r}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe(t){const e=m.getFirstMatch(/rim\stablet\sos\s(\d+(\.\d+)*)/i,t)||m.getFirstMatch(/blackberry\d+\/(\d+([_\s]\d+)*)/i,t)||m.getFirstMatch(/\bbb(\d+)/i,t);return{name:Ce.BlackBerry,version:e}}},{test:[/bada/i],describe(t){const e=m.getFirstMatch(/bada\/(\d+(\.\d+)*)/i,t);return{name:Ce.Bada,version:e}}},{test:[/tizen/i],describe(t){const e=m.getFirstMatch(/tizen[/\s](\d+(\.\d+)*)/i,t);return{name:Ce.Tizen,version:e}}},{test:[/linux/i],describe(){return{name:Ce.Linux}}},{test:[/CrOS/],describe(){return{name:Ce.ChromeOS}}},{test:[/PlayStation 4/],describe(t){const e=m.getFirstMatch(/PlayStation 4[/\s](\d+(\.\d+)*)/i,t);return{name:Ce.PlayStation4,version:e}}}],Xl=[{test:[/googlebot/i],describe(){return{type:"bot",vendor:"Google"}}},{test:[/huawei/i],describe(t){const e=m.getFirstMatch(/(can-l01)/i,t)&&"Nova",r={type:me.mobile,vendor:"Huawei"};return e&&(r.model=e),r}},{test:[/nexus\s*(?:7|8|9|10).*/i],describe(){return{type:me.tablet,vendor:"Nexus"}}},{test:[/ipad/i],describe(){return{type:me.tablet,vendor:"Apple",model:"iPad"}}},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe(){return{type:me.tablet,vendor:"Apple",model:"iPad"}}},{test:[/kftt build/i],describe(){return{type:me.tablet,vendor:"Amazon",model:"Kindle Fire HD 7"}}},{test:[/silk/i],describe(){return{type:me.tablet,vendor:"Amazon"}}},{test:[/tablet(?! pc)/i],describe(){return{type:me.tablet}}},{test(t){const e=t.test(/ipod|iphone/i),r=t.test(/like (ipod|iphone)/i);return e&&!r},describe(t){const e=m.getFirstMatch(/(ipod|iphone)/i,t);return{type:me.mobile,vendor:"Apple",model:e}}},{test:[/nexus\s*[0-6].*/i,/galaxy nexus/i],describe(){return{type:me.mobile,vendor:"Nexus"}}},{test:[/[^-]mobi/i],describe(){return{type:me.mobile}}},{test(t){return t.getBrowserName(!0)==="blackberry"},describe(){return{type:me.mobile,vendor:"BlackBerry"}}},{test(t){return t.getBrowserName(!0)==="bada"},describe(){return{type:me.mobile}}},{test(t){return t.getBrowserName()==="windows phone"},describe(){return{type:me.mobile,vendor:"Microsoft"}}},{test(t){const e=Number(String(t.getOSVersion()).split(".")[0]);return t.getOSName(!0)==="android"&&e>=3},describe(){return{type:me.tablet}}},{test(t){return t.getOSName(!0)==="android"},describe(){return{type:me.mobile}}},{test(t){return t.getOSName(!0)==="macos"},describe(){return{type:me.desktop,vendor:"Apple"}}},{test(t){return t.getOSName(!0)==="windows"},describe(){return{type:me.desktop}}},{test(t){return t.getOSName(!0)==="linux"},describe(){return{type:me.desktop}}},{test(t){return t.getOSName(!0)==="playstation 4"},describe(){return{type:me.tv}}},{test(t){return t.getOSName(!0)==="roku"},describe(){return{type:me.tv}}}],Jl=[{test(t){return t.getBrowserName(!0)==="microsoft edge"},describe(t){if(/\sedg\//i.test(t))return{name:nt.Blink};const e=m.getFirstMatch(/edge\/(\d+(\.?_?\d+)+)/i,t);return{name:nt.EdgeHTML,version:e}}},{test:[/trident/i],describe(t){const e={name:nt.Trident},r=m.getFirstMatch(/trident\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test(t){return t.test(/presto/i)},describe(t){const e={name:nt.Presto},r=m.getFirstMatch(/presto\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test(t){const e=t.test(/gecko/i),r=t.test(/like gecko/i);return e&&!r},describe(t){const e={name:nt.Gecko},r=m.getFirstMatch(/gecko\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/(apple)?webkit\/537\.36/i],describe(){return{name:nt.Blink}}},{test:[/(apple)?webkit/i],describe(t){const e={name:nt.WebKit},r=m.getFirstMatch(/webkit\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}}];class so{constructor(e,r=!1){if(e==null||e==="")throw new Error("UserAgent parameter can't be empty");this._ua=e,this.parsedResult={},r!==!0&&this.parse()}getUA(){return this._ua}test(e){return e.test(this._ua)}parseBrowser(){this.parsedResult.browser={};const e=m.find(Yl,r=>{if(typeof r.test=="function")return r.test(this);if(r.test instanceof Array)return r.test.some(n=>this.test(n));throw new Error("Browser's test function is not valid")});return e&&(this.parsedResult.browser=e.describe(this.getUA())),this.parsedResult.browser}getBrowser(){return this.parsedResult.browser?this.parsedResult.browser:this.parseBrowser()}getBrowserName(e){return e?String(this.getBrowser().name).toLowerCase()||"":this.getBrowser().name||""}getBrowserVersion(){return this.getBrowser().version}getOS(){return this.parsedResult.os?this.parsedResult.os:this.parseOS()}parseOS(){this.parsedResult.os={};const e=m.find(Kl,r=>{if(typeof r.test=="function")return r.test(this);if(r.test instanceof Array)return r.test.some(n=>this.test(n));throw new Error("Browser's test function is not valid")});return e&&(this.parsedResult.os=e.describe(this.getUA())),this.parsedResult.os}getOSName(e){const{name:r}=this.getOS();return e?String(r).toLowerCase()||"":r||""}getOSVersion(){return this.getOS().version}getPlatform(){return this.parsedResult.platform?this.parsedResult.platform:this.parsePlatform()}getPlatformType(e=!1){const{type:r}=this.getPlatform();return e?String(r).toLowerCase()||"":r||""}parsePlatform(){this.parsedResult.platform={};const e=m.find(Xl,r=>{if(typeof r.test=="function")return r.test(this);if(r.test instanceof Array)return r.test.some(n=>this.test(n));throw new Error("Browser's test function is not valid")});return e&&(this.parsedResult.platform=e.describe(this.getUA())),this.parsedResult.platform}getEngine(){return this.parsedResult.engine?this.parsedResult.engine:this.parseEngine()}getEngineName(e){return e?String(this.getEngine().name).toLowerCase()||"":this.getEngine().name||""}parseEngine(){this.parsedResult.engine={};const e=m.find(Jl,r=>{if(typeof r.test=="function")return r.test(this);if(r.test instanceof Array)return r.test.some(n=>this.test(n));throw new Error("Browser's test function is not valid")});return e&&(this.parsedResult.engine=e.describe(this.getUA())),this.parsedResult.engine}parse(){return this.parseBrowser(),this.parseOS(),this.parsePlatform(),this.parseEngine(),this}getResult(){return m.assign({},this.parsedResult)}satisfies(e){const r={};let n=0;const o={};let a=0;if(Object.keys(e).forEach(i=>{const s=e[i];typeof s=="string"?(o[i]=s,a+=1):typeof s=="object"&&(r[i]=s,n+=1)}),n>0){const i=Object.keys(r),s=m.find(i,l=>this.isOS(l));if(s){const l=this.satisfies(r[s]);if(l!==void 0)return l}const c=m.find(i,l=>this.isPlatform(l));if(c){const l=this.satisfies(r[c]);if(l!==void 0)return l}}if(a>0){const i=Object.keys(o),s=m.find(i,c=>this.isBrowser(c,!0));if(s!==void 0)return this.compareVersion(o[s])}}isBrowser(e,r=!1){const n=this.getBrowserName().toLowerCase();let o=e.toLowerCase();const a=m.getBrowserTypeByAlias(o);return r&&a&&(o=a.toLowerCase()),o===n}compareVersion(e){let r=[0],n=e,o=!1;const a=this.getBrowserVersion();if(typeof a=="string")return e[0]===">"||e[0]==="<"?(n=e.substr(1),e[1]==="="?(o=!0,n=e.substr(2)):r=[],e[0]===">"?r.push(1):r.push(-1)):e[0]==="="?n=e.substr(1):e[0]==="~"&&(o=!0,n=e.substr(1)),r.indexOf(m.compareVersions(a,n,o))>-1}isOS(e){return this.getOSName(!0)===String(e).toLowerCase()}isPlatform(e){return this.getPlatformType(!0)===String(e).toLowerCase()}isEngine(e){return this.getEngineName(!0)===String(e).toLowerCase()}is(e,r=!1){return this.isBrowser(e,r)||this.isOS(e)||this.isPlatform(e)}some(e=[]){return e.some(r=>this.is(r))}}/*!
 * Bowser - a browser detector
 * https://github.com/lancedikson/bowser
 * MIT License | (c) Dustin Diaz 2012-2015
 * MIT License | (c) Denis Demchenko 2015-2019
 */class Ql{static getParser(e,r=!1){if(typeof e!="string")throw new Error("UserAgent should be a string");return new so(e,r)}static parse(e){return new so(e).getResult()}static get BROWSER_MAP(){return Ea}static get ENGINE_MAP(){return nt}static get OS_MAP(){return Ce}static get PLATFORMS_MAP(){return me}}const ed=()=>le(()=>{{const t=Ql.parse(window.navigator.userAgent);return{browser:t.browser,engine:t.engine,os:t.os}}},[]);function td(t){return ed().os.name.toLowerCase().includes("mac")?dn[t].keyComboMac:dn[t].keyComboDefault}function rd(){const t=mr(),[e,r]=U(!1),n=le(()=>t.chats.find(f=>f.id===t.currentChatId),[t.chats,t.currentChatId]),o=le(()=>(n==null?void 0:n.inputValue)||"",[n==null?void 0:n.inputValue]),a=S(f=>{t.setChatInput(t.currentChatId,f)},[t.setChatInput,t.currentChatId]),i=S(()=>{!n||!o.trim()||t.addMessage(n.id,o)},[n,o,t.addMessage]),s=S(f=>{f.key==="Enter"&&!f.shiftKey&&!e&&(f.preventDefault(),i())},[i,e]),c=S(()=>{r(!0)},[]),l=S(()=>{r(!1)},[]),u=V(null);ne(()=>{var f,k,R;const O=()=>{var G;return(G=u.current)==null?void 0:G.focus()};return t.isPromptCreationActive?((f=u.current)==null||f.focus(),(k=u.current)==null||k.addEventListener("blur",O)):(R=u.current)==null||R.blur(),()=>{var G;(G=u.current)==null||G.removeEventListener("blur",O)}},[t.isPromptCreationActive]);const p=le(()=>he("flex size-8 items-center justify-center rounded-full bg-transparent p-1 text-zinc-950 opacity-20 transition-all duration-150",o.length>0&&"bg-blue-600 text-white opacity-100",t.promptState==="loading"&&"cursor-not-allowed bg-zinc-300 text-zinc-500 opacity-30"),[o.length,t.promptState]),h=le(()=>he("h-full w-full flex-1 resize-none bg-transparent text-zinc-950 transition-all duration-150 placeholder:text-zinc-950/50 focus:outline-none",t.promptState==="loading"&&"text-zinc-500 placeholder:text-zinc-400"),[t.promptState]),w=le(()=>{const f="flex h-24 w-full flex-1 flex-row items-end gap-1 rounded-2xl p-4 text-sm text-zinc-950 shadow-md backdrop-blur transition-all duration-150 placeholder:text-zinc-950/70";switch(t.promptState){case"loading":return he(f,"border-2 border-transparent bg-zinc-50/80","chat-loading-gradient");case"success":return he(f,"border-2 border-transparent bg-zinc-50/80","chat-success-border");case"error":return he(f,"border-2 border-transparent bg-zinc-50/80","chat-error-border animate-shake");default:return he(f,"border border-border/30 bg-zinc-50/80")}},[t.promptState]),y=td(Pr.CTRL_ALT_C);return d("div",{className:w,onClick:()=>t.startPromptCreation(),role:"button",tabIndex:0,children:[d(Cl,{ref:u,className:h,value:o,onChange:f=>a(f.currentTarget.value),onKeyDown:s,onCompositionStart:c,onCompositionEnd:l,placeholder:t.isPromptCreationActive?t.promptState==="loading"?"Processing...":"Enter prompt...":`What do you want to change? (${y})`,disabled:t.promptState==="loading"}),d(Sn,{className:p,disabled:o.length===0||t.promptState==="loading",onClick:i,children:d(Vl,{className:"size-4"})})]})}const zn=Re(null),nd=({containerRef:t,children:e,snapAreas:r,onDragStart:n,onDragEnd:o})=>{const[a,i]=U({top:0,left:0,right:0,bottom:0});ne(()=>{if(!t.current)return;const y=()=>{if(t.current){const k=t.current.getBoundingClientRect();i({top:k.top,left:k.left,right:k.right,bottom:k.bottom})}};y();const f=new ResizeObserver(y);return f.observe(t.current),window.addEventListener("resize",y),()=>{t.current&&f.unobserve(t.current),f.disconnect(),window.removeEventListener("resize",y)}},[t]);const s=V(new Set),c=V(new Set),l=S(y=>(s.current.add(y),()=>s.current.delete(y)),[]),u=S(y=>(c.current.add(y),()=>c.current.delete(y)),[]),p=S(()=>{n&&n(),s.current.forEach(y=>y())},[n]),h=S(()=>{o&&o(),c.current.forEach(y=>y())},[o]),w={borderLocation:a,snapAreas:r,registerDragStart:l,registerDragEnd:u,emitDragStart:p,emitDragEnd:h};return d(zn.Provider,{value:w,children:e})};function od(t){const e=ke(zn),r=V(e);ne(()=>{r.current=e},[e]);const n=V(null),o=V(null),[a,i]=U(null),[s,c]=U(null),l=V(null),u=V(null),p=V(null),h=V(!1),w=V(t.initialRelativeCenter),[y,f]=U(null),{startThreshold:k=3,areaSnapThreshold:R=60,onDragStart:O,onDragEnd:G,initialSnapArea:de,springStiffness:oe=.2,springDampness:we=.55}=t,ie=V(null),M=V({x:0,y:0}),ve=V(!1);ne(()=>{if(de&&e&&e.borderLocation&&e.snapAreas&&e.snapAreas[de]&&!h.current){const{top:P,left:J,right:K,bottom:z}=e.borderLocation,N=K-J,j=z-P,q={topLeft:{x:J,y:P},topRight:{x:K,y:P},bottomLeft:{x:J,y:z},bottomRight:{x:K,y:z}}[de];if(q&&N>0&&j>0){const ue=(q.x-J)/N,A=(q.y-P)/j;w.current={x:ue,y:A}}else q&&console.warn("useDraggable: Container for initialSnapArea has zero width or height. Cannot calculate relative center from snap area. Falling back to initialRelativeCenter or undefined.")}},[de,e]);function Fe(P){const{top:J,left:K,right:z,bottom:N}=P,j=(K+z)/2;return{topLeft:{x:K,y:J},topCenter:{x:j,y:J},topRight:{x:z,y:J},bottomLeft:{x:K,y:N},bottomCenter:{x:j,y:N},bottomRight:{x:z,y:N}}}const je=S(()=>{var P,J;const K=n.current;if(!K)return;const z=K.offsetWidth,N=K.offsetHeight,j=K.offsetParent;let q=0,ue=0,A=window.innerWidth,Z=window.innerHeight;if(j){const Y=j.getBoundingClientRect();q=Y.left,ue=Y.top,A=j.offsetWidth||window.innerWidth,Z=j.offsetHeight||window.innerHeight}let ae=null,te=null;const be=w.current;let ze=null,Je=null;const Qe=r.current;let qe=!0,Ue=!0;if(h.current&&l.current&&p.current&&Qe&&Qe.borderLocation&&Qe.snapAreas){const Y={x:p.current.x-l.current.x,y:p.current.y-l.current.y},mt=Fe(Qe.borderLocation);let pe=Number.POSITIVE_INFINITY,xe=null,ht=null;for(const gt in Qe.snapAreas)if(Qe.snapAreas[gt]){const fr=mt[gt];if(!fr)continue;const Tn=Math.hypot(fr.x-Y.x,fr.y-Y.y);Tn<pe&&(pe=Tn,xe=gt,ht=fr)}xe&&ht&&pe<=R&&(ze=xe,Je=ht),Ue=(Y.x-q)/A<=.5,qe=(Y.y-ue)/Z<=.5}if(h.current&&Je)ae=Je.x,te=Je.y,f(ze),Ue=(Je.x-q)/A<=.5,qe=(Je.y-ue)/Z<=.5;else if(h.current&&l.current&&p.current)ae=p.current.x-l.current.x,te=p.current.y-l.current.y,f(null),Ue=(ae-q)/A<=.5,qe=(te-ue)/Z<=.5;else{if(be&&A>0&&Z>0){if(qe=be.y<=.5,Ue=be.x<=.5,Ue){const Y=A*be.x;ae=q+Y}else{const Y=A*(1-be.x);ae=q+A-Y}if(qe){const Y=Z*be.y;te=ue+Y}else{const Y=Z*(1-be.y);te=ue+Z-Y}}else{!((P=n.current)!=null&&P.style.left)&&!((J=n.current)!=null&&J.style.top)&&console.warn("useDraggable: Cannot determine position. Parent has no dimensions or initialRelativeCenter was not effectively set.");return}f(null)}if(ae===null||te===null)return;const{borderLocation:Me}=r.current||{borderLocation:void 0};if(Me&&z>0&&N>0){const Y=Me.right-Me.left,mt=Me.bottom-Me.top;let pe=ae,xe=te;if(z>=Y)pe=Me.left+Y/2;else{const ht=Me.left+z/2,gt=Me.right-z/2;pe=Math.max(ht,Math.min(pe,gt))}if(N>=mt)xe=Me.top+mt/2;else{const ht=Me.top+N/2,gt=Me.bottom-N/2;xe=Math.max(ht,Math.min(xe,gt))}ae=pe,te=xe}if(!ie.current){ie.current={x:ae,y:te},M.current={x:0,y:0};const Y=ae-z/2,mt=te-N/2,pe=K.style;if(pe.right="",pe.bottom="",pe.left="",pe.top="",Ue){const xe=Y-q;pe.left=A>0?`${(xe/A*100).toFixed(2)}%`:"0px",pe.right=""}else{const xe=q+A-(Y+z);pe.right=A>0?`${(xe/A*100).toFixed(2)}%`:"0px",pe.left=""}if(qe){const xe=mt-ue;pe.top=Z>0?`${(xe/Z*100).toFixed(2)}%`:"0px",pe.bottom=""}else{const xe=ue+Z-(mt+N);pe.bottom=Z>0?`${(xe/Z*100).toFixed(2)}%`:"0px",pe.top=""}ve.current=!0;return}if(!ve.current){ve.current=!0;return}const Ee=ie.current,ye=M.current,pt=ae-Ee.x,$e=te-Ee.y,Be=oe*pt-we*ye.x,gr=oe*$e-we*ye.y;ye.x+=Be,ye.y+=gr,Ee.x+=ye.x,Ee.y+=ye.y;const et=.5;Math.abs(pt)<et&&Math.abs($e)<et&&Math.abs(ye.x)<et&&Math.abs(ye.y)<et&&(Ee.x=ae,Ee.y=te,ye.x=0,ye.y=0),ie.current={...Ee},M.current={...ye};const Mn=Ee.x-z/2,$n=Ee.y-N/2,Pe=K.style;if(Pe.right="",Pe.bottom="",Pe.left="",Pe.top="",Ue){const Y=Mn-q;Pe.left=A>0?`${(Y/A*100).toFixed(2)}%`:"0px",Pe.right=""}else{const Y=q+A-(Mn+z);Pe.right=A>0?`${(Y/A*100).toFixed(2)}%`:"0px",Pe.left=""}if(qe){const Y=$n-ue;Pe.top=Z>0?`${(Y/Z*100).toFixed(2)}%`:"0px",Pe.bottom=""}else{const Y=ue+Z-($n+N);Pe.bottom=Z>0?`${(Y/Z*100).toFixed(2)}%`:"0px",Pe.top=""}(Math.abs(Ee.x-ae)>et||Math.abs(Ee.y-te)>et||Math.abs(ye.x)>et||Math.abs(ye.y)>et||h.current)&&requestAnimationFrame(je)},[R,oe,we]),[Xe,D]=U(!1),F=S(P=>{var J;if(h.current){G&&G(),(J=r.current)!=null&&J.emitDragEnd&&r.current.emitDragEnd(),D(!0),setTimeout(()=>D(!1),0);const K=n.current,z=r.current;if(K&&z&&z.borderLocation){const N=K.offsetWidth,j=K.offsetHeight,q=K.offsetParent;let ue=0,A=0,Z=window.innerWidth,ae=window.innerHeight;if(q){const $e=q.getBoundingClientRect();ue=$e.left,A=$e.top,Z=q.offsetWidth||window.innerWidth,ae=q.offsetHeight||window.innerHeight}let te=0,be=0;p.current&&l.current?(te=p.current.x-l.current.x,be=p.current.y-l.current.y):ie.current&&(te=ie.current.x,be=ie.current.y);const ze=z.borderLocation,Je=ze.left+N/2,Qe=ze.right-N/2,qe=ze.top+j/2,Ue=ze.bottom-j/2;te=Math.max(Je,Math.min(te,Qe)),be=Math.max(qe,Math.min(be,Ue));const Me=Fe(ze);let Ee=Number.POSITIVE_INFINITY,ye=null,pt=null;for(const $e in z.snapAreas)if(z.snapAreas[$e]){const Be=Me[$e];if(!Be)continue;const gr=Math.hypot(Be.x-te,Be.y-be);gr<Ee&&(Ee=gr,ye=$e,pt=Be)}if(ye&&pt){f(ye);const $e=(pt.x-ue)/Z,Be=(pt.y-A)/ae;w.current={x:$e,y:Be}}else{f(null);const $e=(te-ue)/Z,Be=(be-A)/ae;w.current={x:$e,y:Be}}}}u.current=null,h.current=!1,window.removeEventListener("mousemove",ee,{capture:!0}),window.removeEventListener("mouseup",F,{capture:!0}),n.current&&(n.current.style.userSelect=""),document.body.style.userSelect="",document.body.style.cursor=""},[G]),ee=S(P=>{var J;u.current&&(Math.hypot(P.clientX-u.current.x,P.clientY-u.current.y)>k&&!h.current&&(h.current=!0,n.current&&(n.current.style.userSelect="none"),document.body.style.userSelect="none",document.body.style.cursor="grabbing",O&&O(),(J=r.current)!=null&&J.emitDragStart&&r.current.emitDragStart(),requestAnimationFrame(je)),p.current={x:P.clientX,y:P.clientY})},[k,O,je]),W=S(P=>{if(P.button!==0)return;const J=o.current,K=n.current;if(J){if(!J.contains(P.target)&&P.target!==J)return}else if(K){if(!K.contains(P.target)&&P.target!==K)return}else{console.error("Draggable element or handle ref not set in mouseDownHandler");return}if(u.current={x:P.clientX,y:P.clientY},!n.current){console.error("Draggable element ref not set in mouseDownHandler");return}const z=n.current.getBoundingClientRect(),N=z.left+z.width/2,j=z.top+z.height/2;l.current={x:P.clientX-N,y:P.clientY-j},window.addEventListener("mousemove",ee,{capture:!0}),window.addEventListener("mouseup",F,{capture:!0})},[ee,F]);ne(()=>{const P=s||a;return P&&P.addEventListener("mousedown",W),()=>{P&&P.removeEventListener("mousedown",W),h.current&&(G&&G(),h.current=!1,a&&(a.style.userSelect=""),document.body.style.userSelect="",document.body.style.cursor="",window.removeEventListener("mousemove",ee,{capture:!0}),window.removeEventListener("mouseup",F,{capture:!0}))}},[a,s,W,G,ee,F]),ne(()=>{n.current&&e&&e.borderLocation&&w.current&&!h.current&&!ve.current&&requestAnimationFrame(()=>{n.current&&je()})},[a,e,t.initialRelativeCenter,de,je]);const ge=S(P=>{i(P),n.current=P},[]),E=S(P=>{c(P),o.current=P},[]);return{draggableRef:ge,handleRef:E,position:{snapArea:y,isTopHalf:w.current?w.current.y<=.5:!0,isLeftHalf:w.current?w.current.x<=.5:!0},wasDragged:Xe}}function cr({children:t}){return d("div",{className:"fade-in slide-in-from-right-2 flex max-h-sm max-w-full animate-in snap-start flex-col items-center justify-between gap-1 py-0.5",children:t})}function ad(t){return d("div",{className:"relative flex w-full shrink-0 items-center justify-center",children:[t.children,t.badgeContent&&d("div",{className:he("bg-blue-600 text-white",t.badgeClassName,"pointer-events-none absolute right-0 bottom-0 flex h-3 w-max min-w-3 max-w-8 select-none items-center justify-center truncate rounded-full px-0.5 font-semibold text-[0.5em]"),children:t.badgeContent}),t.statusDot&&d("div",{className:he("bg-rose-600",t.statusDotClassName,"pointer-events-none absolute top-0 right-0 size-1.5 rounded-full")})]})}const It=dr(({badgeContent:t,badgeClassName:e,statusDot:r,statusDotClassName:n,tooltipHint:o,variant:a="default",active:i,...s},c)=>{const l=d(Sn,{ref:c,...s,className:he("flex items-center justify-center rounded-full p-1 text-zinc-950 ring ring-transparent transition-all duration-150 hover:bg-zinc-950/5",a==="default"?"size-8":"h-8 rounded-full",i&&"bg-white/40 ring-zinc-950/20",s.className)});return d(ad,{badgeContent:t,badgeClassName:e,statusDot:r,statusDotClassName:n,children:l})});It.displayName="ToolbarButton";const id=({color:t="default",loading:e=!1,loadingSpeed:r="slow",...n})=>{const o={default:"fill-stagewise-700 stroke-none",black:"fill-zinc-950 stroke-none",white:"fill-white stroke-none",zinc:"fill-zinc-500/50 stroke-none",current:"fill-current stroke-none",gradient:"fill-white stroke-black/30 stroke-1"};return d("div",{className:`relative ${t==="gradient"?"overflow-hidden rounded-full":"overflow-visible"} ${n.className||""} ${e?"drop-shadow-xl":""} aspect-square`,children:[t==="gradient"&&d("div",{className:"absolute inset-0",children:[d("div",{className:"absolute inset-0 size-full bg-gradient-to-tr from-indigo-700 via-blue-500 to-teal-500"}),d("div",{className:"absolute top-1/2 left-1/2 size-9/12 bg-[radial-gradient(circle,rgba(219,39,119,0.2)_0%,rgba(219,39,119,0)_100%)]"}),d("div",{className:"absolute right-1/2 bottom-1/2 size-full bg-[radial-gradient(circle,rgba(219,39,119,0.2)_0%,rgba(219,39,119,0)_100%)]"}),d("div",{className:"absolute top-0 left-[-10%] size-[120%] bg-[radial-gradient(circle,rgba(255,255,255,0)_60%,rgba(255,255,255,0.2)_70%)]"}),d("div",{className:"absolute top-[-20%] left-0 h-[120%] w-full bg-[radial-gradient(circle,rgba(55,48,163,0)_55%,rgba(55,48,163,0.35)_73%)]"})]}),d("svg",{className:`absolute overflow-visible ${t==="gradient"?"top-[25%] left-[25%] h-[50%] w-[50%] drop-shadow-indigo-950 drop-shadow-xs":"top-0 left-0 h-full w-full"}`,viewBox:"0 0 2048 2048",children:[d("title",{children:"stagewise"}),d("ellipse",{className:o[t]+(e?" animate-pulse":""),id:"path3",ry:"624",rx:"624",cy:"1024",cx:"1024"})]}),d("svg",{className:`absolute overflow-visible ${t==="gradient"?"top-[25%] left-[25%] h-[50%] w-[50%]":"top-0 left-0 h-full w-full"}`,viewBox:"0 0 2048 2048",children:d("path",{id:"path4",className:`origin-center ${o[t]}${e?r==="fast"?" animate-spin-fast":" animate-spin-slow":""}`,d:"M 1024 0 A 1024 1024 0 0 0 0 1024 A 1024 1024 0 0 0 1024 2048 L 1736 2048 L 1848 2048 C 1958.7998 2048 2048 1958.7998 2048 1848 L 2048 1736 L 2048 1024 A 1024 1024 0 0 0 1024 0 z M 1024.9414 200 A 824 824 0 0 1 1848.9414 1024 A 824 824 0 0 1 1024.9414 1848 A 824 824 0 0 1 200.94141 1024 A 824 824 0 0 1 1024.9414 200 z "})})]})},sd=({onOpenPanel:t,isActive:e=!1})=>d(cr,{children:d(It,{onClick:t,active:e,children:d(Wl,{className:"size-4"})})}),cd=({onClose:t})=>d(wt,{children:[d(wt.Header,{title:"Settings"}),d(wt.Content,{children:d(ld,{})}),d(wt.Content,{children:d(dd,{})})]}),ld=()=>{const{windows:t,isDiscovering:e,discoveryError:r,discover:n,selectedSession:o,selectSession:a}=dt(),i=l=>{const u=l.target,p=u.value===""?void 0:u.value;a(p)},{appName:s}=dt(),c=()=>{n()};return d("div",{className:"space-y-4 pb-4",children:[d("div",{children:[d("label",{htmlFor:"session-select",className:"mb-2 block font-medium text-sm text-zinc-700",children:["IDE Window ",s&&`(${s})`]}),d("div",{className:"flex w-full items-center space-x-2",children:[d("select",{id:"session-select",value:(o==null?void 0:o.sessionId)||"",onChange:i,className:"h-8 min-w-0 flex-1 rounded-lg border border-zinc-300 bg-zinc-500/10 px-3 text-sm backdrop-saturate-150 focus:border-zinc-500 focus:outline-none",disabled:e,children:[d("option",{value:"",disabled:!0,children:t.length>0?"Select an IDE window...":"No windows available"}),t.map(l=>d("option",{value:l.sessionId,children:[l.displayName," - Port ",l.port]},l.sessionId))]}),d("button",{type:"button",onClick:c,disabled:e,className:"flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-lg bg-zinc-500/10 backdrop-saturate-150 transition-colors hover:bg-zinc-500/20 disabled:opacity-50",title:"Refresh window list",children:d(Bt,{className:`size-4 ${e?"animate-spin":""}`})})]}),r&&d("p",{className:"mt-1 text-red-600 text-sm",children:["Error discovering windows: ",r]}),!e&&t.length===0&&!r&&d("p",{className:"mt-1 text-sm text-zinc-500",children:"No IDE windows found. Make sure the Stagewise extension is installed and running."})]}),o&&d("div",{className:"rounded-lg bg-blue-50 p-3",children:[d("p",{className:"text-blue-800 text-sm",children:[d("strong",{children:"Selected:"})," ",o.displayName]}),d("p",{className:"mt-1 text-blue-600 text-xs",children:["Session ID: ",o.sessionId.substring(0,8),"..."]})]}),!o&&t.length>0&&d("div",{className:"rounded-lg bg-amber-50 p-3",children:d("p",{className:"text-amber-800 text-sm",children:[d("strong",{children:"No window selected:"})," Please select an IDE window above to connect."]})})]})},dd=()=>d("div",{className:"space-y-2 text-xs text-zinc-700",children:[d("div",{className:"my-2 flex flex-wrap items-center gap-3",children:[d("a",{href:"https://github.com/stagewise-io/stagewise",target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-1 text-blue-700 hover:underline",title:"GitHub Repository",children:[d("svg",{width:"16",height:"16",fill:"currentColor",viewBox:"0 0 24 24",children:d("path",{d:"M12 .5C5.73.5.5 5.73.5 12c0 5.08 3.29 9.39 7.86 10.91.58.11.79-.25.79-.56 0-.28-.01-1.02-.02-2-3.2.7-3.88-1.54-3.88-1.54-.53-1.34-1.3-1.7-1.3-1.7-1.06-.72.08-.71.08-.71 1.17.08 1.78 1.2 1.78 1.2 1.04 1.78 2.73 1.27 3.4.97.11-.75.41-1.27.74-1.56-2.56-.29-5.26-1.28-5.26-5.7 0-1.26.45-2.29 1.19-3.1-.12-.29-.52-1.46.11-3.05 0 0 .98-.31 3.2 1.18a11.1 11.1 0 0 1 2.92-.39c.99 0 1.99.13 2.92.39 2.22-1.49 3.2-1.18 3.2-1.18.63 1.59.23 2.76.11 3.05.74.81 1.19 1.84 1.19 3.1 0 4.43-2.7 5.41-5.27 5.7.42.36.79 1.08.79 2.18 0 1.57-.01 2.84-.01 3.23 0 .31.21.68.8.56C20.71 21.39 24 17.08 24 12c0-6.27-5.23-11.5-12-11.5z"})}),"GitHub"]}),d("a",{href:"https://discord.gg/gkdGsDYaKA",target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-1 text-indigo-700 hover:underline",title:"Join our Discord",children:[d("svg",{width:"16",height:"16",fill:"currentColor",viewBox:"0 0 24 24",children:d("path",{d:"M20.317 4.369A19.791 19.791 0 0 0 16.885 3.2a.117.117 0 0 0-.124.06c-.537.96-1.13 2.22-1.552 3.2a18.524 18.524 0 0 0-5.418 0c-.423-.98-1.016-2.24-1.553-3.2a.117.117 0 0 0-.124-.06A19.736 19.736 0 0 0 3.683 4.369a.105.105 0 0 0-.047.043C.533 9.043-.32 13.579.099 18.057a.12.12 0 0 0 .045.083c1.934 1.426 3.81 2.288 5.671 2.857a.116.116 0 0 0 .127-.043c.438-.602.827-1.24 1.165-1.908a.112.112 0 0 0-.062-.158c-.619-.234-1.205-.52-1.77-.853a.117.117 0 0 1-.012-.194c.119-.09.238-.183.353-.277a.112.112 0 0 1 .114-.013c3.747 1.71 7.789 1.71 11.533 0a.112.112 0 0 1 .115.012c.115.094.234.188.353.278a.117.117 0 0 1-.012.194c-.565.333-1.151.619-1.77.853a.112.112 0 0 0-.062.158c.34.668.728 1.306 1.165 1.908a.115.115 0 0 0 .127.043c1.861-.569 3.737-1.431 5.671-2.857a.12.12 0 0 0 .045-.083c.5-5.177-.838-9.673-3.636-13.645a.105.105 0 0 0-.047-.043zM8.02 15.331c-1.183 0-2.156-1.085-2.156-2.419 0-1.333.955-2.418 2.156-2.418 1.21 0 2.175 1.095 2.156 2.418 0 1.334-.955 2.419-2.156 2.419zm7.96 0c-1.183 0-2.156-1.085-2.156-2.419 0-1.333.955-2.418 2.156-2.418 1.21 0 2.175 1.095 2.156 2.418 0 1.334-.946 2.419-2.156 2.419z"})}),"Discord"]}),d("a",{href:"https://marketplace.visualstudio.com/items?itemName=stagewise.stagewise-vscode-extension",target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-1 text-violet-700 hover:underline",title:"VS Code Marketplace",children:[d("svg",{width:"16",height:"16",fill:"currentColor",viewBox:"0 0 24 24",children:d("path",{d:"M21.805 2.29a2.25 2.25 0 0 0-2.45-.49l-7.5 3.25a2.25 2.25 0 0 0-1.31 2.06v1.13l-5.13 2.22a2.25 2.25 0 0 0-1.31 2.06v3.5a2.25 2.25 0 0 0 1.31 2.06l5.13 2.22v1.13a2.25 2.25 0 0 0 1.31 2.06l7.5 3.25a2.25 2.25 0 0 0 2.45-.49A2.25 2.25 0 0 0 23 20.25V3.75a2.25 2.25 0 0 0-1.195-1.46zM12 20.25v-16.5l7.5 3.25v10l-7.5 3.25z"})}),"VS Code Marketplace"]})]}),d("div",{className:"mt-2",children:[d("span",{className:"font-semibold",children:"Contact:"})," ",d("a",{href:"mailto:<EMAIL>",className:"text-blue-700 hover:underline",children:"<EMAIL>"})]}),d("div",{className:"mt-2 text-zinc-500",children:d("span",{children:["Licensed under AGPL v3."," ",d("a",{href:"https://github.com/stagewise-io/stagewise/blob/main/LICENSE",target:"_blank",rel:"noopener noreferrer",className:"hover:underline",children:"View license"})]})})]});function ud({discover:t,discoveryError:e}){return d("div",{className:"rounded-lg border border-orange-200 bg-orange-50/90 p-4 shadow-lg backdrop-blur",children:[d("div",{className:"mb-3 flex items-center gap-3",children:[d(Na,{className:"size-5 text-orange-600"}),d("h3",{className:"font-semibold text-orange-800",children:"Not Connected"})]}),d("div",{className:"space-y-3 text-orange-700 text-sm",children:[d("p",{children:"The stagewise toolbar isn't connected to any IDE window."}),e&&d("div",{className:"rounded border border-red-200 bg-red-100 p-2 text-red-700",children:[d("strong",{children:"Error:"})," ",e]}),d("div",{className:"space-y-2",children:[d("p",{className:"font-medium",children:"To connect:"}),d("ol",{className:"list-inside list-decimal space-y-1 pl-2 text-xs",children:[d("li",{children:"Open your IDE (Cursor, Windsurf, etc.)"}),d("li",{children:"Install the stagewise extension"}),d("li",{children:"Make sure the extension is active"}),d("li",{children:"Click refresh below"})]})]}),d("button",{type:"button",onClick:t,className:"flex w-full items-center justify-center gap-2 rounded-md bg-orange-600 px-3 py-2 font-medium text-sm text-white transition-colors hover:bg-orange-700",children:[d(Bt,{className:"size-4"}),"Retry Connection"]}),d("div",{className:"border-orange-200 border-t pt-2",children:d("a",{href:"https://marketplace.visualstudio.com/items?itemName=stagewise.stagewise-vscode-extension",target:"_blank",rel:"noopener noreferrer",className:"text-orange-600 text-xs hover:text-orange-800 hover:underline",children:"Get VS Code Extension →"})})]})]})}function pd(){return d("div",{className:"rounded-lg border border-blue-200 bg-blue-50/90 p-4 shadow-lg backdrop-blur",children:[d("div",{className:"mb-3 flex items-center gap-3",children:[d(Bt,{className:"size-5 animate-spin text-blue-600"}),d("h3",{className:"font-semibold text-blue-800",children:"Connecting..."})]}),d("div",{className:"text-blue-700 text-sm",children:d("p",{children:["Looking for active agent instances...",d("br",{}),d("span",{className:"text-blue-500 text-xs",children:"VS Code, Cursor, Windsurf ..."})]})})]})}function md(){const{windows:t,isDiscovering:e,discoveryError:r,discover:n,selectedSession:o,selectSession:a,appName:i}=dt(),s=l=>{const u=l.target,p=u.value===""?void 0:u.value;a(p)},c=()=>{n()};return d("div",{className:"rounded-lg border border-blue-200 bg-blue-50/90 p-4 shadow-lg backdrop-blur",children:[d("div",{className:"mb-3",children:d("h3",{className:"font-semibold text-blue-800",children:"Select IDE Window"})}),d("div",{className:"space-y-3",children:[d("div",{children:[d("label",{htmlFor:"window-selection-select",className:"mb-2 block font-medium text-blue-700 text-sm",children:["IDE Window ",i&&`(${i})`]}),d("div",{className:"flex w-full items-center space-x-2",children:[d("select",{id:"window-selection-select",value:(o==null?void 0:o.sessionId)||"",onChange:s,className:"h-8 min-w-0 flex-1 rounded-lg border border-blue-300 bg-white/80 px-3 text-sm backdrop-saturate-150 focus:border-blue-500 focus:outline-none",disabled:e,children:[d("option",{value:"",disabled:!0,children:t.length>0?"Select an IDE window...":"No windows available"}),t.map(l=>d("option",{value:l.sessionId,children:[l.displayName," - Port ",l.port]},l.sessionId))]}),d("button",{type:"button",onClick:c,disabled:e,className:"flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-lg bg-blue-100/80 backdrop-saturate-150 transition-colors hover:bg-blue-200/80 disabled:opacity-50",title:"Refresh window list",children:d(Bt,{className:`size-4 text-blue-600 ${e?"animate-spin":""}`})})]}),r&&d("p",{className:"mt-1 text-red-600 text-sm",children:["Error discovering windows: ",r]}),!e&&t.length===0&&!r&&d("p",{className:"mt-1 text-blue-600 text-sm",children:"No IDE windows found. Make sure the Stagewise extension is installed and running."})]}),o&&d("div",{className:"rounded-lg bg-blue-100/80 p-3",children:[d("p",{className:"text-blue-800 text-sm",children:[d("strong",{children:"Selected:"})," ",o.displayName]}),d("p",{className:"mt-1 text-blue-600 text-xs",children:["Session ID: ",o.sessionId.substring(0,8),"..."]})]}),!o&&d("div",{className:"rounded-lg border border-blue-200 bg-white/90 p-3",children:d("p",{className:"text-blue-800 text-sm",children:[d("strong",{children:"No window selected:"})," Please select an IDE window above to connect."]})})]})]})}function hd({handleButtonClick:t,pluginBox:e,setPluginBox:r,openPanel:n,setOpenPanel:o,chatState:a}){const i=Fr().plugins.filter(s=>s.onActionClick);return d(De,{children:[d(sd,{onOpenPanel:()=>o(n==="settings"?null:"settings"),isActive:n==="settings"}),i.length>0&&d(cr,{children:i.map(s=>d(It,{onClick:t(()=>{(e==null?void 0:e.pluginName)!==s.pluginName?s.onActionClick()&&r({component:s.onActionClick(),pluginName:s.pluginName}):r(null)}),active:(e==null?void 0:e.pluginName)===s.pluginName,children:s.iconSvg?d("span",{className:"size-4 stroke-zinc-950 text-zinc-950 *:size-full",children:s.iconSvg}):d(jl,{className:"size-4"})},s.pluginName))}),d(cr,{children:d(It,{onClick:t(()=>a.isPromptCreationActive?a.stopPromptCreation():a.startPromptCreation()),active:a.isPromptCreationActive,children:d(Il,{className:"size-4 stroke-zinc-950"})})})]})}function gd(){const{discover:t,isDiscovering:e}=dt();return d(cr,{children:d(It,{onClick:e?void 0:()=>t(),className:he(e?"text-blue-700":"text-orange-700 hover:bg-orange-200"),children:d(Bt,{className:he("size-4",e&&"animate-spin")})})})}function fd(){const t=ke(zn),e=t==null?void 0:t.borderLocation,r=!!e&&e.right-e.left>0&&e.bottom-e.top>0,n=od({startThreshold:10,initialSnapArea:"bottomRight"}),{windows:o,isDiscovering:a,discoveryError:i,discover:s,shouldPromptWindowSelection:c}=dt(),l=o.length>0,[u,p]=U(null),[h,w]=U(null),y=mr(),{minimized:f,minimize:k,expand:R}=_n();ne(()=>{f&&(p(null),w(null))},[f]);const O=ve=>Fe=>{if(n.wasDragged){Fe.preventDefault(),Fe.stopPropagation();return}ve()};if(!r)return null;const G=a,de=!l&&!a,oe=l,we=c&&oe,ie=G?{border:"border-blue-300",bg:"bg-blue-100/80",divideBorder:"divide-blue-200",buttonBg:"from-blue-600 to-sky-600",buttonColor:"text-blue-700"}:de?{border:"border-orange-300",bg:"bg-orange-100/80",divideBorder:"divide-orange-200",buttonBg:"from-orange-600 to-red-600",buttonColor:"text-orange-700"}:{border:"border-border/30",bg:"bg-zinc-50/80",divideBorder:"divide-border/20",buttonBg:"from-sky-700 to-fuchsia-700",buttonColor:"stroke-zinc-950"},M=()=>G?d(Bt,{className:"size-4 animate-spin text-white"}):de?d(Na,{className:"size-4 text-white"}):d(id,{className:"size-4.5",color:"white"});return d("div",{ref:n.draggableRef,className:"absolute p-0.5",children:[d("div",{className:he("absolute flex h-[calc(100vh-32px)] w-96 max-w-[40vw] items-stretch justify-end transition-all duration-300 ease-out",n.position.isTopHalf?"top-0 flex-col-reverse":"bottom-0 flex-col",n.position.isLeftHalf?"left-[100%]":"right-[100%]"),children:[d("div",{className:he("flex min-h-0 flex-1 origin-bottom-right flex-col items-stretch px-2 transition-all duration-300 ease-out",(u||h==="settings"||!oe||we)&&!f?"pointer-events-auto scale-100 opacity-100 blur-none":"pointer-events-none h-0 scale-50 opacity-0 blur-md",n.position.isTopHalf?"justify-start":"justify-end",n.position.isTopHalf?n.position.isLeftHalf?"origin-top-left":"origin-top-right":n.position.isLeftHalf?"origin-bottom-left":"origin-bottom-right"),children:[G&&d(pd,{}),de&&d(ud,{discover:s,discoveryError:i}),we&&d(md,{}),oe&&h==="settings"&&!we&&d(cd,{onClose:()=>w(null)}),oe&&!we&&(u==null?void 0:u.component)]}),oe&&d("div",{className:he("z-20 w-full px-2 transition-all duration-300 ease-out",y.isPromptCreationActive&&!f?"pointer-events-auto scale-100 opacity-100 blur-none":"pointer-events-none h-0 scale-50 opacity-0 blur-md",n.position.isTopHalf?"mb-2":"mt-2",n.position.isTopHalf?n.position.isLeftHalf?"origin-top-left":"origin-top-right":n.position.isLeftHalf?"origin-bottom-left":"origin-bottom-right"),children:d(rd,{})})]}),d("div",{ref:n.handleRef,className:he("pointer-events-auto z-50 rounded-full border px-0.5 shadow-md backdrop-blur transition-all duration-300 ease-out",ie.border,ie.bg,n.position.isTopHalf?"flex-col-reverse divide-y-reverse":"flex-col",f?"h-9.5 w-9.5":"h-[calc-size(auto,size)] h-auto w-auto"),children:[d(Sn,{onClick:()=>R(),className:he("absolute right-0 left-0 z-50 flex size-9 origin-center cursor-pointer items-center justify-center rounded-full bg-gradient-to-tr transition-all duration-300 ease-out",ie.buttonBg,f?"pointer-events-auto scale-100 opacity-100 blur-none":"pointer-events-none scale-25 opacity-0 blur-md",n.position.isTopHalf?"top-0":"bottom-0"),children:M()}),d("div",{className:he("flex h-[calc-size(auto)] scale-100 items-center justify-center divide-y transition-all duration-300 ease-out",ie.divideBorder,n.position.isTopHalf?"origin-top flex-col-reverse divide-y-reverse":"origin-bottom flex-col",f&&"pointer-events-none h-0 scale-50 opacity-0 blur-md"),children:[oe?d(hd,{handleButtonClick:O,pluginBox:u,setPluginBox:p,openPanel:h,setOpenPanel:w,chatState:y}):d(gd,{}),d(cr,{children:d(It,{onClick:O(()=>k()),className:he("h-5",ie.buttonColor,n.position.isTopHalf?"rounded-t-3xl rounded-b-lg":"rounded-t-lg rounded-b-3xl"),children:n.position.isTopHalf?d(Pl,{className:"size-4"}):d(Tl,{className:"size-4"})})})]})]})]})}function wd(){const t=V(null);return d("div",{className:"absolute size-full",children:d("div",{className:"absolute inset-4",ref:t,children:d(nd,{containerRef:t,snapAreas:{topLeft:!0,topRight:!0,bottomLeft:!0,bottomRight:!0,topCenter:!0,bottomCenter:!0},children:d(fd,{})})})})}function vd(t){const e=V(null),r=S(a=>{if(a.target.closest(".companion"))return;const i=fc(a.clientX,a.clientY);t.ignoreList.includes(i)||e.current!==i&&(e.current=i,t.onElementHovered(i))},[t]),n=S(()=>{e.current=null,t.onElementUnhovered()},[t]),o=S(()=>{e.current&&(t.ignoreList.includes(e.current)||t.onElementSelected(e.current))},[t]);return d("div",{className:"pointer-events-auto fixed inset-0 h-screen w-screen cursor-copy",onMouseMove:r,onMouseLeave:n,onClick:o,role:"button",tabIndex:0})}function za(){const[t,e]=U({width:window.innerWidth,height:window.innerHeight}),r=S(()=>e({width:window.innerWidth,height:window.innerHeight}),[]);return Rr("resize",r),t}function Ma(t,e){const r=V(void 0),n=le(()=>1e3/e,[e]),o=V(0),a=S(i=>{i-o.current>=n&&(t(),o.current=i),r.current=requestAnimationFrame(a)},[t,n]);ne(()=>(r.current=requestAnimationFrame(a),()=>{r.current&&(cancelAnimationFrame(r.current),r.current=void 0)}),[e,a])}function bd({refElement:t,...e}){const r=V(null),n=za(),{plugins:o}=Fr(),a=le(()=>t?o.filter(s=>s.onContextElementSelect).map(s=>{var c;return{pluginName:s.pluginName,context:(c=s.onContextElementSelect)==null?void 0:c.call(s,t)}}):[],[t]),i=S(()=>{if(r.current)if(t){const s=t.getBoundingClientRect();r.current.style.top=`${s.top-2}px`,r.current.style.left=`${s.left-2}px`,r.current.style.width=`${s.width+4}px`,r.current.style.height=`${s.height+4}px`,r.current.style.display=void 0}else r.current.style.height="0px",r.current.style.width="0px",r.current.style.top=`${n.height/2}px`,r.current.style.left=`${n.width/2}px`,r.current.style.display="none"},[t,n.height,n.width]);return Ma(i,30),d("div",{...e,className:"fixed flex items-center justify-center rounded-lg border-2 border-blue-600/80 bg-blue-600/20 text-white transition-all duration-100",style:{zIndex:1e3},ref:r,children:[d("div",{className:"absolute top-0.5 left-0.5 flex w-full flex-row items-start justify-start gap-1",children:[d("div",{className:"flex flex-row items-center justify-center gap-0.5 overflow-hidden rounded-md bg-zinc-700/80 px-1 py-0 font-medium text-white text-xs",children:d("span",{className:"truncate",children:t.tagName.toLowerCase()})}),a.filter(s=>s.context.annotation).map(s=>{var c;return d("div",{className:"flex flex-row items-center justify-center gap-0.5 overflow-hidden rounded-md bg-zinc-700/80 px-1 py-0 font-medium text-white text-xs",children:[d("span",{className:"size-3 shrink-0 stroke-white text-white *:size-full",children:(c=o.find(l=>l.pluginName===s.pluginName))==null?void 0:c.iconSvg}),d("span",{className:"truncate",children:s.context.annotation})]})})]}),d(Ol,{className:"size-6 drop-shadow-black drop-shadow-md"})]})}function yd({refElement:t,...e}){const r=V(null),n=za(),o=S(()=>{if(r.current)if(t){const c=t.getBoundingClientRect();r.current.style.top=`${c.top}px`,r.current.style.left=`${c.left}px`,r.current.style.width=`${c.width}px`,r.current.style.height=`${c.height}px`,r.current.style.display=void 0}else r.current.style.height="0px",r.current.style.width="0px",r.current.style.top=`${n.height/2}px`,r.current.style.left=`${n.width/2}px`,r.current.style.display="none"},[t,n.height,n.width]);Ma(o,30);const a=mr(),i=S(()=>{a.removeChatDomContext(a.currentChatId,t)},[a,t]),{plugins:s}=Fr();return d("div",{...e,className:"pointer-events-auto fixed flex cursor-pointer items-center justify-center rounded-lg border-2 border-green-600/80 bg-green-600/5 text-transparent transition-all duration-0 hover:border-red-600/80 hover:bg-red-600/20 hover:text-white",ref:r,onClick:i,role:"button",tabIndex:0,children:[d("div",{className:"absolute top-0.5 left-0.5 flex w-full flex-row items-start justify-start gap-1",children:[d("div",{className:"flex flex-row items-center justify-center gap-0.5 overflow-hidden rounded-md bg-zinc-700/80 px-1 py-0 font-medium text-white text-xs",children:d("span",{className:"truncate",children:t.tagName.toLowerCase()})}),e.pluginContext.filter(c=>c.context.annotation).map(c=>{var l;return d("div",{className:"flex flex-row items-center justify-center gap-0.5 overflow-hidden rounded-md bg-zinc-700/80 px-1 py-0 font-medium text-white text-xs",children:[d("span",{className:"size-3 shrink-0 stroke-white text-white *:size-full",children:(l=s.find(u=>u.pluginName===c.pluginName))==null?void 0:l.iconSvg}),d("span",{className:"truncate",children:c.context.annotation})]})})]}),d(ql,{className:"size-6 drop-shadow-black drop-shadow-md"})]})}function xd(){const{chats:t,currentChatId:e,addChatDomContext:r,isPromptCreationActive:n,promptState:o}=mr(),a=le(()=>t.find(p=>p.id===e),[e,t]),i=n&&o!=="loading",s=le(()=>(a==null?void 0:a.domContextElements)||[],[a]),[c,l]=U(null),u=S(p=>{r(e,p)},[r,e]);return i?d(De,{children:[c&&d(bd,{refElement:c}),d(vd,{ignoreList:s.map(p=>p.element),onElementHovered:l,onElementSelected:u,onElementUnhovered:()=>l(null)}),s.map(p=>d(yd,{refElement:p.element,pluginContext:p.pluginContext}))]}):null}function _d(){return d("div",{className:he("fixed inset-0 h-screen w-screen"),children:[d(xd,{}),d(wd,{})]})}function kd(){const t=V(!1);return ne(()=>{const e=HTMLElement.prototype.focus;return HTMLElement.prototype.focus=function(...r){const n=this.getRootNode();!(n instanceof ShadowRoot&&n.host instanceof HTMLElement&&n.host.nodeName==="STAGEWISE-COMPANION-ANCHOR")&&t.current||e.apply(this,r)},()=>{HTMLElement.prototype.focus=e}},[]),Rr("focusin",e=>{e.target.localName===sr&&(t.current=!0)},{capture:!0}),Rr("focusout",e=>{e.target.localName===sr&&(t.current=!1)},{capture:!0}),null}function Cd({children:t}){return t}function Sd(){const{isMainAppBlocked:t}=_n();return d("div",{className:he("fixed inset-0 h-screen w-screen",t?"pointer-events-auto":"pointer-events-none"),role:"button",tabIndex:0})}function Nd(t){return d(Sc,{children:[d(kd,{}),d(Sd,{}),d(Ec,{config:t,children:[d(zc,{}),d(Cd,{children:d(_d,{})})]})]})}function Ed(t){if(!document.body)throw new Error("stagewise companion cannot find document.body");if(document.body.querySelector(sr))throw console.warn("A stagewise companion anchor already exists. Aborting this instance."),new Error("A stagewise companion anchor already exists.");const e=document.createElement(sr);e.style.position="fixed",e.style.top="0px",e.style.left="0px",e.style.right="0px",e.style.bottom="0px",e.style.pointerEvents="none",e.style.zIndex="2147483647";const r=a=>{a.stopPropagation()};e.onclick=r,e.onmousedown=r,e.onmouseup=r,e.onmousemove=r,e.ondblclick=r,e.oncontextmenu=r,e.onwheel=r,e.onfocus=r,e.onblur=r,document.body.appendChild(e);const n=document.createElement("link");n.rel="stylesheet",n.href="https://rsms.me/inter/inter.css",document.head.appendChild(n);const o=document.createElement("style");o.append(document.createTextNode(bc)),document.head.appendChild(o),Ut(Ae(Nd,t),e)}const $d=Aa({__name:"StagewiseToolbar",props:{config:{},enabled:{type:Boolean,default:()=>!1}},setup(t){const e=t;return Pa(()=>{e.enabled&&Ed(e.config)}),(r,n)=>null}});export{$d as StagewiseToolbar};
