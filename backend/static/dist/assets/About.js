import{u as v,H as g,g as p}from"./app.js";import{c as _,o as y,b as o,l as t,g as n,t as s,x as l,F as b,q as f,e as h,j as a}from"./vendor.js";const k={class:"py-16 bg-white"},w={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},S={class:"text-center"},C={class:"text-4xl font-extrabold text-gray-900 sm:text-5xl"},T={class:"mt-4 text-xl text-gray-600"},B={key:0,class:"mt-16"},D={class:"max-w-3xl mx-auto"},N={class:"text-3xl font-bold text-gray-900 text-center mb-8"},V={class:"text-lg text-gray-700 leading-relaxed"},j={class:"mt-16 grid grid-cols-1 md:grid-cols-2 gap-12"},F={key:0,class:"bg-gray-50 p-8 rounded-lg"},H={class:"text-2xl font-bold text-gray-900 mb-4"},I={class:"text-gray-700"},L={key:1,class:"bg-gray-50 p-8 rounded-lg"},q={class:"text-2xl font-bold text-gray-900 mb-4"},z={class:"text-gray-700"},A={key:1,class:"mt-16"},E={class:"text-center mb-12"},M={class:"text-3xl font-bold text-gray-900"},G={class:"mt-4 text-xl text-gray-600"},J={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},K={class:"w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4"},O={class:"text-lg font-semibold text-gray-900"},P={key:2,class:"mt-16"},Q={class:"text-center"},R={class:"text-3xl font-bold text-gray-900"},U={class:"mt-4 text-xl text-gray-600"},W={class:"mt-8 inline-flex items-center px-6 py-3 bg-primary-50 rounded-lg"},X={class:"text-primary-900 font-medium"},et={__name:"About",setup(Y){const i=v(),x=_(()=>i.config||{}),e=_(()=>{var r;return((r=x.value.pages)==null?void 0:r.about)||{}}),c=_(()=>x.value.company||{});return y(()=>{i.loadConfig()}),(r,Z)=>{var m,u;return a(),o("div",k,[t("div",w,[t("div",S,[t("h1",C,s(((m=e.value.hero)==null?void 0:m.title)||"Chi Siamo"),1),t("p",T,s(((u=e.value.hero)==null?void 0:u.subtitle)||"La nostra storia e i nostri valori"),1)]),e.value.story_section?(a(),o("div",B,[t("div",D,[t("h2",N,s(e.value.story_section.title),1),t("p",V,s(l(i).interpolateText(e.value.story_section.content)),1)])])):n("",!0),t("div",j,[e.value.mission_section?(a(),o("div",F,[t("h3",H,s(e.value.mission_section.title),1),t("p",I,s(l(i).interpolateText(e.value.mission_section.content)),1)])):n("",!0),e.value.vision_section?(a(),o("div",L,[t("h3",q,s(e.value.vision_section.title),1),t("p",z,s(l(i).interpolateText(e.value.vision_section.content)),1)])):n("",!0)]),e.value.expertise_section&&c.value.expertise?(a(),o("div",A,[t("div",E,[t("h2",M,s(e.value.expertise_section.title),1),t("p",G,s(e.value.expertise_section.subtitle),1)]),t("div",J,[(a(!0),o(b,null,f(c.value.expertise,d=>(a(),o("div",{key:d,class:"bg-white p-6 rounded-lg shadow-sm border border-gray-200 text-center"},[t("div",K,[h(g,{name:l(p)(d),class:"w-6 h-6 text-primary-600"},null,8,["name"])]),t("h3",O,s(d),1)]))),128))])])):n("",!0),e.value.team_section?(a(),o("div",P,[t("div",Q,[t("h2",R,s(e.value.team_section.title),1),t("p",U,s(e.value.team_section.subtitle),1),t("div",W,[h(g,{name:"users",class:"w-5 h-5 text-primary-600 mr-2"}),t("span",X,s(c.value.team_size),1)])])])):n("",!0)])])}}};export{et as default};
