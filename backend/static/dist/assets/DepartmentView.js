import{r as p,c as h,o as j,u as C,w as D,b as n,g as x,l as e,e as o,t as d,p as m,s as E,h as $,j as i,v as I,F as k,q as w,n as P}from"./vendor.js";import{u as V}from"./personnel.js";import{_ as M,e as B,H as l}from"./app.js";import{_ as q}from"./PageHeader.js";import{T as F}from"./TabContainer.js";const H={class:"department-view"},R={key:0,class:"flex justify-center items-center h-64"},S={key:1,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6"},A={class:"flex"},U={class:"text-sm text-red-700 dark:text-red-300 mt-1"},L={key:2,class:"space-y-6"},G={class:"flex items-center space-x-6"},J={key:0,class:"flex items-center text-white"},K={class:"text-sm"},O={class:"flex items-center text-white"},Q={class:"text-sm"},W={key:1,class:"flex items-center text-white"},X={class:"text-sm"},Y={key:0,class:"space-y-4"},Z={class:"flex items-center justify-between"},ee={class:"flex items-center space-x-4"},te={class:"w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center"},se={class:"font-medium text-gray-900 dark:text-white"},ae={class:"text-sm text-gray-500 dark:text-gray-400"},re={class:"text-sm text-gray-500 dark:text-gray-400"},ne={class:"flex items-center space-x-2"},oe={key:1,class:"text-center py-8"},ie={key:0,class:"space-y-4"},de={class:"flex items-center justify-between"},le={class:"flex items-center space-x-4"},ce={class:"w-10 h-10 bg-brand-primary-100 dark:bg-brand-primary-900 rounded-lg flex items-center justify-center"},me={class:"font-medium text-gray-900 dark:text-white"},ue={class:"text-sm text-gray-500 dark:text-gray-400"},pe={key:1,class:"text-center py-8"},xe={__name:"DepartmentView",setup(ye){const f=C();E(),V();const{hasPermission:y}=B(),a=p(null),v=p(!1),u=p(null),g=p(!1),b=p("employees"),N=h(()=>{try{return y.value&&typeof y.value=="function"?y.value("manage_users"):!1}catch(r){return console.warn("Permission check failed:",r),!1}}),z=h(()=>{var r,t,c,s;return[{id:"employees",name:"Dipendenti",icon:"users",count:((t=(r=a.value)==null?void 0:r.employees)==null?void 0:t.length)||0},{id:"subdepartments",name:"Sotto-dipartimenti",icon:"building-office-2",count:((s=(c=a.value)==null?void 0:c.subdepartments)==null?void 0:s.length)||0}]}),T=r=>new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(r),_=async r=>{v.value=!0,u.value=null;try{const t=await fetch(`/api/personnel/departments/${r}`,{credentials:"include"});if(!t.ok)throw new Error(`HTTP ${t.status}: ${t.statusText}`);const c=await t.json();if(c.success)a.value=c.data.department;else throw new Error(c.message||"Errore nel caricamento del dipartimento")}catch(t){console.error("Error fetching department:",t),u.value=t.message}finally{v.value=!1}};return j(async()=>{const r=f.params.id;if(!r){u.value="ID dipartimento non specificato";return}await _(r)}),D(()=>f.params.id,async r=>{r&&await _(r)}),(r,t)=>{const c=$("router-link");return i(),n("div",H,[v.value?(i(),n("div",R,t[2]||(t[2]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-brand-primary-600"},null,-1)]))):u.value?(i(),n("div",S,[e("div",A,[o(l,{name:"exclamation-triangle",size:"sm",color:"text-red-400",className:"mr-2 mt-0.5"}),e("div",null,[t[3]||(t[3]=e("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"},"Errore nel caricamento del dipartimento",-1)),e("p",U,d(u.value),1)])])])):a.value?(i(),n("div",L,[o(q,{title:a.value.name,subtitle:a.value.description,icon:"building-office-2","icon-color":"text-brand-primary-600","show-back":!0,"back-route":"/app/personnel/departments",gradient:!0},{metadata:m(()=>[e("div",G,[a.value.manager?(i(),n("div",J,[o(l,{name:"user",size:"sm",className:"mr-2"}),e("span",K,"Manager: "+d(a.value.manager.full_name),1)])):x("",!0),e("div",O,[o(l,{name:"users",size:"sm",className:"mr-2"}),e("span",Q,d(a.value.employee_count)+" dipendenti",1)]),a.value.budget?(i(),n("div",W,[o(l,{name:"currency-euro",size:"sm",className:"mr-2"}),e("span",X,"Budget: "+d(T(a.value.budget)),1)])):x("",!0)])]),actions:m(()=>[N.value?(i(),n("button",{key:0,onClick:t[0]||(t[0]=s=>g.value=!g.value),class:"bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center"},[o(l,{name:"pencil",size:"sm",className:"mr-2"}),I(" "+d(g.value?"Annulla":"Modifica"),1)])):x("",!0)]),_:1},8,["title","subtitle"]),o(F,{tabs:z.value,"active-tab":b.value,"onUpdate:activeTab":t[1]||(t[1]=s=>b.value=s)},{employees:m(()=>[a.value.employees&&a.value.employees.length>0?(i(),n("div",Y,[(i(!0),n(k,null,w(a.value.employees,s=>(i(),n("div",{key:s.id,class:"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"},[e("div",Z,[e("div",ee,[e("div",te,[o(l,{name:"user",size:"sm",color:"text-gray-600 dark:text-gray-300"})]),e("div",null,[e("h4",se,d(s.full_name),1),e("p",ae,d(s.position||"Posizione non specificata"),1),e("p",re,d(s.email),1)])]),e("div",ne,[e("span",{class:P(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",s.is_active?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"])},d(s.is_active?"Attivo":"Inattivo"),3),o(c,{to:`/app/personnel/${s.id}`,class:"text-brand-primary-600 dark:text-brand-primary-400 hover:text-brand-primary-900 dark:hover:text-brand-primary-300"},{default:m(()=>[o(l,{name:"eye",size:"sm"})]),_:2},1032,["to"])])])]))),128))])):(i(),n("div",oe,[o(l,{name:"users",size:"2xl",color:"text-gray-400",className:"mx-auto mb-4"}),t[4]||(t[4]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun dipendente",-1)),t[5]||(t[5]=e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"Non ci sono dipendenti assegnati a questo dipartimento.",-1))]))]),subdepartments:m(()=>[a.value.subdepartments&&a.value.subdepartments.length>0?(i(),n("div",ie,[(i(!0),n(k,null,w(a.value.subdepartments,s=>(i(),n("div",{key:s.id,class:"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"},[e("div",de,[e("div",le,[e("div",ce,[o(l,{name:"building-office-2",size:"sm",color:"text-brand-primary-600 dark:text-brand-primary-400"})]),e("div",null,[e("h4",me,d(s.name),1),e("p",ue,d(s.employee_count)+" dipendenti",1)])]),o(c,{to:`/app/personnel/departments/${s.id}`,class:"text-brand-primary-600 dark:text-brand-primary-400 hover:text-brand-primary-900 dark:hover:text-brand-primary-300"},{default:m(()=>[o(l,{name:"eye",size:"sm"})]),_:2},1032,["to"])])]))),128))])):(i(),n("div",pe,[o(l,{name:"building-office-2",size:"2xl",color:"text-gray-400",className:"mx-auto mb-4"}),t[6]||(t[6]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun sotto-dipartimento",-1)),t[7]||(t[7]=e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"Non ci sono sotto-dipartimenti per questo dipartimento.",-1))]))]),_:1},8,["tabs","active-tab"])])):x("",!0)])}}},he=M(xe,[["__scopeId","data-v-a8507baf"]]);export{he as default};
