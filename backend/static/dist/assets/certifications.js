import{d as f}from"./vendor.js";import{c as o}from"./app.js";const d=f("certifications",{state:()=>({certifications:[],currentCertification:null,standardsCatalog:{},overview:null,loading:!1,error:null,catalogLastFetched:null,catalogCacheDuration:5*60*1e3}),getters:{activeCertifications:e=>e.certifications.filter(t=>t.status==="active"),expiringCertifications:e=>e.certifications.filter(t=>t.is_expiring_soon),certificationsByCategory:e=>{const t={};return e.certifications.forEach(r=>{const i=r.category||"other";t[i]||(t[i]=[]),t[i].push(r)}),t},standardsByCategory:e=>e.standardsCatalog.catalog||{},availableCategories:e=>e.standardsCatalog.categories||[]},actions:{async getOverview(){var e,t,r;this.loading=!0,this.error=null;try{console.log("🔍 [Certifications] Calling /api/certifications/overview");const i=await o.get("/api/certifications/overview");if(console.log("📥 [Certifications] Raw response:",i),console.log("📊 [Certifications] Response status:",i.status),console.log("📊 [Certifications] Response data:",i.data),console.log("📊 [Certifications] Response success:",(e=i.data)==null?void 0:e.success),i.data.success)return console.log("✅ [Certifications] Overview loaded successfully:",i.data.data),this.overview=i.data.data,i.data.data;throw console.error("❌ [Certifications] API returned success=false:",i.data.message),new Error(i.data.message||"Errore nel caricamento overview")}catch(i){throw console.error("💥 [Certifications] Overview error:",i),console.error("💥 [Certifications] Error response:",i.response),console.error("💥 [Certifications] Error status:",(t=i.response)==null?void 0:t.status),console.error("💥 [Certifications] Error data:",(r=i.response)==null?void 0:r.data),this.error=i.message,i}finally{this.loading=!1}},async fetchCertifications(e={}){this.loading=!0,this.error=null;try{const t=new URLSearchParams;Object.entries(e).forEach(([i,s])=>{s!=null&&s!==""&&t.append(i,s)});const r=await o.get(`/api/certifications?${t}`);if(r.data.success)return this.certifications=r.data.data,r.data;throw new Error(r.data.message||"Errore nel caricamento certificazioni")}catch(t){throw this.error=t.message,console.error("Errore caricamento certificazioni:",t),t}finally{this.loading=!1}},async fetchCertification(e){this.loading=!0,this.error=null;try{const t=await o.get(`/api/certifications/${e}`);if(t.data.success)return this.currentCertification=t.data.data,t.data.data;throw new Error(t.data.message||"Certificazione non trovata")}catch(t){throw this.error=t.message,console.error("Errore caricamento certificazione:",t),t}finally{this.loading=!1}},async fetchStandardsCatalog(e=!1){var i,s,n;const t=Date.now(),r=!this.catalogLastFetched||t-this.catalogLastFetched>this.catalogCacheDuration;if(!e&&!r&&Object.keys(this.standardsCatalog).length>0)return console.log("🔄 [Certifications] Using cached catalog data"),this.standardsCatalog;this.loading=!0,this.error=null;try{console.log("🔍 [Certifications] Fetching fresh catalog data from API");const a=await o.get("/api/certifications/standards/catalog");if(console.log("📥 [Certifications] Catalog raw response:",a),console.log("📊 [Certifications] Catalog response status:",a.status),console.log("📊 [Certifications] Catalog response data:",a.data),console.log("📊 [Certifications] Catalog response success:",(i=a.data)==null?void 0:i.success),a.data.success)return console.log("✅ [Certifications] Catalog loaded successfully:",a.data.data),this.standardsCatalog=a.data.data,this.catalogLastFetched=t,a.data.data;throw console.error("❌ [Certifications] Catalog API returned success=false:",a.data.message),new Error(a.data.message||"Errore nel caricamento catalogo")}catch(a){throw console.error("💥 [Certifications] Catalog error:",a),console.error("💥 [Certifications] Catalog error response:",a.response),console.error("💥 [Certifications] Catalog error status:",(s=a.response)==null?void 0:s.status),console.error("💥 [Certifications] Catalog error data:",(n=a.response)==null?void 0:n.data),this.error=a.message,a}finally{this.loading=!1}},async createCertification(e){this.loading=!0,this.error=null;try{const t=await o.post("/api/certifications",e);if(t.data.success)return await this.fetchCertifications(),t.data.data;throw new Error(t.data.message||"Errore nella creazione certificazione")}catch(t){throw this.error=t.message,console.error("Errore creazione certificazione:",t),t}finally{this.loading=!1}},async updateCertification(e,t){this.loading=!0,this.error=null;try{const r=await o.put(`/api/certifications/${e}`,t);if(r.data.success)return this.currentCertification&&this.currentCertification.id===e&&await this.fetchCertification(e),await this.fetchCertifications(),r.data.data;throw new Error(r.data.message||"Errore nell'aggiornamento certificazione")}catch(r){throw this.error=r.message,console.error("Errore aggiornamento certificazione:",r),r}finally{this.loading=!1}},async deleteCertification(e){var t,r,i,s,n;this.loading=!0,this.error=null;try{console.log(`🗑️ [Certifications] Deleting certification with ID: ${e}`),console.log(`🗑️ [Certifications] API endpoint: /api/certifications/${e}`),console.log("🗑️ [Certifications] Request method: DELETE");const a=await o.delete(`/api/certifications/${e}`);if(console.log("🗑️ [Certifications] Delete response:",a),console.log("🗑️ [Certifications] Response status:",a.status),console.log("🗑️ [Certifications] Response data:",a.data),console.log("🗑️ [Certifications] Response success:",(t=a.data)==null?void 0:t.success),a.data.success){console.log(`✅ [Certifications] Certification ${e} deleted successfully`);const c=this.certifications.length;return this.certifications=this.certifications.filter(l=>l.id!==e),console.log(`🔄 [Certifications] Removed from local state. Before: ${c}, After: ${this.certifications.length}`),this.currentCertification&&this.currentCertification.id===e&&(console.log(`🔄 [Certifications] Clearing currentCertification (was ID: ${e})`),this.currentCertification=null),!0}else throw console.error("❌ [Certifications] Delete API returned success=false:",(r=a.data)==null?void 0:r.message),new Error(a.data.message||"Errore nell'eliminazione certificazione")}catch(a){throw console.error(`💥 [Certifications] Error deleting certification ${e}:`,a),console.error("💥 [Certifications] Error details:",{message:a.message,status:(i=a.response)==null?void 0:i.status,statusText:(s=a.response)==null?void 0:s.statusText,data:(n=a.response)==null?void 0:n.data,config:a.config}),this.error=a.message,a}finally{this.loading=!1}},async syncCatalog(){this.loading=!0,this.error=null;try{const e=await o.post("/api/certifications/sync-catalog");if(e.data.success)return await this.fetchStandardsCatalog(!0),!0;throw new Error(e.data.message||"Errore nella sincronizzazione catalogo")}catch(e){throw this.error=e.message,console.error("Errore sincronizzazione catalogo:",e),e}finally{this.loading=!1}},async updateReadinessTask(e,t){this.loading=!0,this.error=null;try{const r=await o.put(`/api/certifications/readiness-tasks/${e}`,t);if(r.data.success)return this.currentCertification&&await this.fetchCertification(this.currentCertification.id),r.data.data;throw new Error(r.data.message||"Errore nell'aggiornamento task")}catch(r){throw this.error=r.message,console.error("Errore aggiornamento task:",r),r}finally{this.loading=!1}},async createAudit(e,t){this.loading=!0,this.error=null;try{const r=await o.post(`/api/certifications/${e}/audits`,t);if(r.data.success)return this.currentCertification&&this.currentCertification.id===e&&await this.fetchCertification(e),r.data.data;throw new Error(r.data.message||"Errore nella creazione audit")}catch(r){throw this.error=r.message,console.error("Errore creazione audit:",r),r}finally{this.loading=!1}},resetState(){this.certifications=[],this.currentCertification=null,this.standardsCatalog={},this.overview=null,this.error=null,this.catalogLastFetched=null},clearError(){this.error=null},async fetchPlatformCompliance(e){try{console.log(`🔍 [Certifications] Fetching platform compliance for ${e}`);const t=await o.get(`/api/certifications/platform-compliance/${e}`);return console.log("📊 [Certifications] Platform compliance response:",t.data),t.data}catch(t){throw console.error("Errore nel caricamento platform compliance:",t),t}},async fetchAIInsights(e){try{console.log(`🤖 [Certifications] Fetching AI insights for ${e}`);const t=await o.post("/api/certifications/ai-insights",{standard_code:e});return console.log("🧠 [Certifications] AI insights response:",t.data),t.data}catch(t){throw console.error("Errore nel caricamento AI insights:",t),t}},async fetchAvailableProjects(){try{console.log("📋 [Certifications] Fetching available projects");const e=await o.get("/api/certifications/available-projects");return console.log("📋 [Certifications] Available projects response:",e.data),e.data}catch(e){throw console.error("Errore nel caricamento progetti disponibili:",e),e}},async createCertificationWithAI(e){var t,r,i,s,n;try{console.log("📋 [Certifications] Creating certification with AI:",e),console.log("📋 [Certifications] API endpoint: /api/certifications/create-certification"),console.log("📋 [Certifications] Request method: POST");const a=await o.post("/api/certifications/create-certification",e);if(console.log("✅ [Certifications] Raw API response:",a),console.log("✅ [Certifications] Response status:",a.status),console.log("✅ [Certifications] Response data:",a.data),console.log("✅ [Certifications] Response success:",(t=a.data)==null?void 0:t.success),!((r=a.data)!=null&&r.success))throw console.error("❌ [Certifications] API returned success=false:",(i=a.data)==null?void 0:i.message),new Error(((s=a.data)==null?void 0:s.message)||"Creazione fallita");console.log("✅ [Certifications] Certification created successfully with ID:",(n=a.data.data)==null?void 0:n.id);try{console.log("🔄 [Certifications] Attempting to refresh certifications list..."),await this.fetchCertifications(),console.log("✅ [Certifications] Certifications list refreshed successfully")}catch(c){console.warn("⚠️ [Certifications] Could not refresh certifications list:",c),console.warn("⚠️ [Certifications] Fetch error details:",{message:c.message,stack:c.stack,response:c.response})}return a.data}catch(a){throw console.error("💥 [Certifications] Error in createCertificationWithAI:",a),console.error("💥 [Certifications] Error details:",{message:a.message,stack:a.stack,response:a.response}),a}},async fetchCertificationProjects(e){try{console.log(`📋 [Certifications] Fetching projects for certification ${e}`);const t=await o.get(`/api/certifications/${e}/projects`);return console.log("📋 [Certifications] Certification projects response:",t.data),t.data}catch(t){throw console.error("Errore nel caricamento progetti certificazione:",t),t}},async updateCertificationStatus(e,t){this.loading=!0,this.error=null;try{console.log(`📊 [Certifications] Updating status for certification ${e}:`,t);const r=await o.put(`/api/certifications/${e}/status`,t);if(console.log("📊 [Certifications] Status update response:",r.data),r.data.success)return this.currentCertification&&this.currentCertification.id===e&&await this.fetchCertification(e),await this.fetchCertifications(),r.data;throw new Error(r.data.message||"Errore nell'aggiornamento dello status")}catch(r){throw this.error=r.message,console.error("Errore aggiornamento status certificazione:",r),r}finally{this.loading=!1}}}});export{d as u};
