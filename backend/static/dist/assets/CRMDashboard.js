import{u as F}from"./crm.js";import{D as $}from"./DashboardTemplate.js";import{_ as E,H as p}from"./app.js";import{c as i,o as H,f as L,p as u,h as Y,j as l,l as t,b as d,F as _,q as k,n as w,t as n,e as c,g as f,v as m,E as q}from"./vendor.js";import"./useToast.js";const O={class:"flex space-x-2"},Q={class:"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8"},G={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},J={class:"relative h-96"},K={class:"h-full overflow-y-auto"},U={class:"space-y-2"},W={class:"flex items-center space-x-2 min-w-0 flex-1"},X={class:"text-sm font-medium text-gray-900 dark:text-white truncate"},Z={class:"text-right flex-shrink-0 ml-2"},tt={class:"text-sm font-bold text-gray-900 dark:text-white"},et={class:"text-xs text-gray-500 dark:text-gray-400"},st={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},ot={class:"relative h-96"},rt={class:"h-full overflow-y-auto"},nt={key:0,class:"text-center py-8 text-gray-500 dark:text-gray-400"},at={key:1,class:"space-y-3"},it={class:"flex-shrink-0"},lt={class:"flex-1 min-w-0"},ct={class:"text-sm font-medium text-gray-900 dark:text-white"},dt={key:0,class:"text-xs text-gray-500 dark:text-gray-400"},ut={key:1,class:"text-xs text-gray-400 dark:text-gray-500"},pt={key:0,class:"flex-shrink-0"},mt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},gt={class:"relative h-96"},xt={class:"space-y-4"},ft={class:"p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"},yt={class:"flex justify-between items-center mb-2"},ht={class:"text-lg font-bold text-green-600"},bt={class:"w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2"},vt={class:"p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"},_t={class:"flex justify-between items-center"},kt={class:"text-lg font-bold text-blue-600"},wt={class:"p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"},Ct={class:"flex justify-between items-center"},Mt={class:"text-lg font-bold text-purple-600"},Dt={class:"p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"},Pt={class:"flex justify-between items-center"},Vt={class:"text-lg font-bold text-yellow-600"},jt={__name:"CRMDashboard",setup(St){const r=F(),C=i(()=>r.clientsCount),M=i(()=>r.activeProposals.length),D=i(()=>r.totalPipelineValue),g=i(()=>r.pipelineStats),P=i(()=>[{id:"clients",title:"Clienti Totali",value:C.value,icon:"users",color:"blue",link:"/app/crm/clients"},{id:"proposals",title:"Proposte Attive",value:M.value,icon:"document-text",color:"green",link:"/app/crm/proposals"},{id:"pipeline",title:"Valore Pipeline",value:`€${x(D.value)}`,icon:"currency-euro",color:"yellow"},{id:"conversion",title:"Tasso Conversione",value:`${g.value.conversionRate}%`,icon:"chart-bar",color:"purple"}]),V=[{status:"draft",name:"Bozza",color:"bg-gray-400"},{status:"sent",name:"Inviata",color:"bg-blue-400"},{status:"negotiating",name:"Negoziazione",color:"bg-yellow-400"},{status:"accepted",name:"Accettata",color:"bg-green-400"},{status:"rejected",name:"Rifiutata",color:"bg-red-400"}],y=i(()=>r.recentActivities.filter(s=>["client","proposal","contract"].includes(s.type)).map(s=>({id:s.id,title:s.title,description:s.description,timestamp:s.timestamp,type:B(s.type),status:s.status,icon:A(s.type),link:s.link}))),j=i(()=>{const s=r.proposals||[];if(s.length===0)return 0;const e=s.reduce((a,o)=>a+(o.value||0),0);return Math.round(e/s.length)}),S=i(()=>{const s=r.proposals||[],e=new Date,a=e.getMonth(),o=e.getFullYear();return s.filter(b=>{const v=new Date(b.created_at);return v.getMonth()===a&&v.getFullYear()===o}).length}),z=i(()=>(r.contracts||[]).filter(e=>["active","signed"].includes(e.status)).length),x=s=>new Intl.NumberFormat("it-IT").format(s||0),I=s=>r.proposalsByStatus[s]||0,R=s=>r.pipelineValueByStatus[s]||0,A=s=>({client:"users",proposal:"document-text",contract:"document-check"})[s]||"clock",B=s=>({client:"user",proposal:"document",contract:"document"})[s]||"system",T=s=>{const e={project:"bg-blue-500",task:"bg-green-500",user:"bg-purple-500",system:"bg-gray-500",timesheet:"bg-indigo-500",document:"bg-yellow-500"};return e[s]||e.system},N=s=>{if(!s)return"";const e=new Date(s),o=Math.floor((new Date-e)/(1e3*60*60));return o<1?"Ora":o<24?`${o}h fa`:o<48?"Ieri":e.toLocaleDateString("it-IT",{year:"numeric",month:"short",day:"numeric"})},h=async()=>{try{await r.fetchDashboardData()}catch(s){console.error("Error refreshing CRM dashboard:",s)}};return H(()=>{h()}),(s,e)=>{const a=Y("router-link");return l(),L($,{title:"Dashboard CRM",subtitle:"Panoramica delle attività commerciali e pipeline di vendita",stats:P.value,charts:[],activities:[],"show-refresh-button":"",onRefresh:h},{"header-actions":u(()=>[t("div",O,[c(a,{to:"/app/crm/clients/create",class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"},{default:u(()=>[c(p,{name:"plus",size:"sm",class:"mr-1"}),e[0]||(e[0]=m(" Cliente "))]),_:1,__:[0]}),c(a,{to:"/app/crm/proposals/create",class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors"},{default:u(()=>[c(p,{name:"document-text",size:"sm",class:"mr-1"}),e[1]||(e[1]=m(" Proposta "))]),_:1,__:[1]}),c(a,{to:"/app/crm/contracts",class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors"},{default:u(()=>[c(p,{name:"document-check",size:"sm",class:"mr-1"}),e[2]||(e[2]=m(" Contratto "))]),_:1,__:[2]})])]),widget:u(()=>[t("div",Q,[t("div",G,[e[3]||(e[3]=t("div",{class:"flex items-center justify-between mb-4"},[t("h2",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Pipeline di Vendita")],-1)),t("div",J,[t("div",K,[t("div",U,[(l(),d(_,null,k(V,(o,b)=>t("div",{key:o.status,class:"flex items-center justify-between px-3 py-2 bg-gray-50 dark:bg-gray-700 rounded-md"},[t("div",W,[t("div",{class:w(["w-2 h-2 rounded-full flex-shrink-0",o.color])},null,2),t("span",X,n(o.name),1)]),t("div",Z,[t("div",tt,n(I(o.status)),1),t("div",et," €"+n(x(R(o.status))),1)])])),64))])])])]),t("div",st,[e[7]||(e[7]=t("div",{class:"flex items-center justify-between mb-4"},[t("h2",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Attività Recenti")],-1)),t("div",ot,[t("div",rt,[y.value.length===0?(l(),d("div",nt,[c(p,{name:"clock",size:"xl",class:"mx-auto mb-2"}),e[4]||(e[4]=t("p",{class:"text-sm"},"Nessuna attività recente",-1)),e[5]||(e[5]=t("p",{class:"text-xs mt-1"},"Le attività CRM appariranno qui",-1))])):(l(),d("div",at,[(l(!0),d(_,null,k(y.value,o=>(l(),d("div",{key:o.id,class:"flex items-start space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"},[t("div",it,[t("div",{class:w(["w-8 h-8 rounded-full flex items-center justify-center",T(o.type)])},[c(p,{name:o.icon||"clock",size:"sm",color:"text-white"},null,8,["name"])],2)]),t("div",lt,[t("p",ct,n(o.title),1),o.description?(l(),d("p",dt,n(o.description),1)):f("",!0),o.timestamp?(l(),d("p",ut,n(N(o.timestamp)),1)):f("",!0)]),o.link?(l(),d("div",pt,[c(a,{to:o.link,class:"text-xs text-brand-primary-600 hover:text-brand-primary-500 dark:text-brand-primary-400"},{default:u(()=>e[6]||(e[6]=[m(" Dettagli ")])),_:2,__:[6]},1032,["to"])])):f("",!0)]))),128))]))])])]),t("div",mt,[e[15]||(e[15]=t("div",{class:"flex items-center justify-between mb-4"},[t("h2",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Metriche Chiave")],-1)),t("div",gt,[t("div",xt,[t("div",ft,[t("div",yt,[e[8]||(e[8]=t("span",{class:"text-sm font-medium text-gray-900 dark:text-white"},"Tasso Conversione",-1)),t("span",ht,n(g.value.conversionRate)+"%",1)]),t("div",bt,[t("div",{class:"bg-green-500 h-2 rounded-full transition-all duration-300",style:q({width:`${g.value.conversionRate}%`})},null,4)])]),t("div",vt,[t("div",_t,[e[9]||(e[9]=t("span",{class:"text-sm font-medium text-gray-900 dark:text-white"},"Valore Medio",-1)),t("span",kt,"€"+n(x(j.value)),1)]),e[10]||(e[10]=t("p",{class:"text-xs text-gray-500 dark:text-gray-400 mt-1"},"Per proposta",-1))]),t("div",wt,[t("div",Ct,[e[11]||(e[11]=t("span",{class:"text-sm font-medium text-gray-900 dark:text-white"},"Questo Mese",-1)),t("span",Mt,n(S.value),1)]),e[12]||(e[12]=t("p",{class:"text-xs text-gray-500 dark:text-gray-400 mt-1"},"Nuove proposte",-1))]),t("div",Dt,[t("div",Pt,[e[13]||(e[13]=t("span",{class:"text-sm font-medium text-gray-900 dark:text-white"},"Contratti Attivi",-1)),t("span",Vt,n(z.value),1)]),e[14]||(e[14]=t("p",{class:"text-xs text-gray-500 dark:text-gray-400 mt-1"},"In corso",-1))])])])])])]),_:1},8,["stats"])}}},Tt=E(jt,[["__scopeId","data-v-c2bc1afa"]]);export{Tt as default};
