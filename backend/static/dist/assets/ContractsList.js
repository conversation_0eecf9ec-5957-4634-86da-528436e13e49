import{r as m,c as h,w as q,o as H,f as N,p as g,s as G,h as O,j as i,l as e,e as c,g as z,t as l,v as p,b as d,F as M,q as A,n as J,x as K,B as w,S as C}from"./vendor.js";import{u as Q}from"./crm.js";import{u as W}from"./useToast.js";import{S as X}from"./StatusBadge.js";import{g as Y,a as Z}from"./contractTypes.js";import{_ as tt}from"./ListPageTemplate.js";import{H as x}from"./app.js";import"./Pagination.js";const et={class:"flex space-x-4"},at=["value"],rt={class:"overflow-x-auto"},ot={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},st={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},nt={class:"px-6 py-4 whitespace-nowrap"},it={class:"text-sm font-medium text-gray-900 dark:text-white"},lt={class:"text-sm text-gray-500 dark:text-gray-400"},dt={class:"px-6 py-4 whitespace-nowrap"},ut={class:"text-sm text-gray-900 dark:text-white"},ct={class:"px-6 py-4 whitespace-nowrap"},gt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},pt={key:0},xt={key:0,class:"text-xs text-gray-500 dark:text-gray-400"},yt={key:1},mt={key:2,class:"text-gray-400 dark:text-gray-500"},bt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},vt={key:0,class:"text-xs text-gray-500 dark:text-gray-400"},ft={key:1,class:"text-xs text-gray-500 dark:text-gray-400"},kt={class:"px-6 py-4 whitespace-nowrap"},_t={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},ht={class:"flex justify-end space-x-2"},wt=["onClick"],Ct={class:"text-center py-12"},Tt={class:"text-gray-500 dark:text-gray-400 mb-4"},Lt={__name:"ContractsList",setup(St){const F=G(),b=Q(),{showToast:v}=W(),f=m(!1),u=m([]),T=m([]),a=m({client_id:"",contract_type:"",status:""}),I=[{key:"contract_number",label:"Contratto"},{key:"client",label:"Cliente"},{key:"contract_type",label:"Tipo"},{key:"budget_amount",label:"Valore"},{key:"start_date",label:"Periodo"},{key:"status",label:"Stato"},{key:"actions",label:"Azioni"}],L=h(()=>{const r=u.value.reduce((t,s)=>s.contract_type==="hourly"&&s.budget_hours?t+s.hourly_rate*s.budget_hours:t+(s.budget_amount||0),0);return[{label:"Totale Contratti",value:u.value.length,icon:"document-text",iconClass:"text-blue-500"},{label:"Contratti Attivi",value:u.value.filter(t=>t.status==="active").length,icon:"check-circle",iconClass:"text-green-500"},{label:"Valore Totale",value:`€${k(r)}`,icon:"currency-euro",iconClass:"text-purple-500"},{label:"Completati",value:u.value.filter(t=>t.status==="completed").length,icon:"archive-box",iconClass:"text-gray-500"}]}),$=h(()=>{let r=u.value;return a.value.client_id&&(r=r.filter(t=>t.client_id===parseInt(a.value.client_id))),a.value.contract_type&&(r=r.filter(t=>t.contract_type===a.value.contract_type)),a.value.status&&(r=r.filter(t=>t.status===a.value.status)),r}),S=h(()=>a.value.client_id||a.value.contract_type||a.value.status),E=async()=>{var r;try{f.value=!0;const t=new URLSearchParams;a.value.client_id&&t.append("client_id",a.value.client_id),a.value.contract_type&&t.append("type",a.value.contract_type),a.value.status&&t.append("status",a.value.status);const s=t.toString(),n=`/api/contracts/${s?"?"+s:""}`,o=await fetch(n);if(o.ok){const y=await o.json();u.value=(((r=y.data)==null?void 0:r.contracts)||[]).map(_=>({..._,budget_amount:_.total_budget}))}else throw new Error("Errore nel caricamento contratti")}catch(t){console.error("Error loading contracts:",t),v("Errore nel caricamento dei contratti","error")}finally{f.value=!1}},B=async()=>{b.clients.length===0&&await b.fetchClients(),T.value=b.clients},D=()=>{F.push("/app/crm/contracts/new")},P=()=>{a.value={client_id:"",contract_type:"",status:""}},R=async r=>{if(confirm("Sei sicuro di voler eliminare questo contratto?"))try{if((await fetch(`/api/contracts/${r}`,{method:"DELETE"})).ok)u.value=u.value.filter(s=>s.id!==r),v("Contratto eliminato con successo","success");else throw new Error("Errore nella eliminazione")}catch(t){console.error("Error deleting contract:",t),v("Errore nell'eliminazione del contratto","error")}},k=r=>new Intl.NumberFormat("it-IT").format(r||0),V=r=>r?new Date(r).toLocaleDateString("it-IT"):"N/A",U=Y,j=r=>{const t=Z(r);return{"bg-blue-100 text-blue-800":"bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400","bg-green-100 text-green-800":"bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400","bg-purple-100 text-purple-800":"bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400","bg-orange-100 text-orange-800":"bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400","bg-indigo-100 text-indigo-800":"bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400","bg-gray-100 text-gray-800":"bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400"}[t]||t};return q(()=>[a.value.client_id,a.value.contract_type,a.value.status],()=>{E()},{deep:!0}),H(async()=>{await Promise.all([E(),B()])}),(r,t)=>{const s=O("router-link");return i(),N(tt,{title:"Contratti",subtitle:"Gestione contratti e accordi commerciali",data:$.value,columns:I,stats:L.value,loading:f.value,"can-create":!0,"create-label":"Nuovo Contratto","search-placeholder":"Titolo, numero contratto...","empty-message":"Inizia creando il tuo primo contratto","results-label":"contratti",onCreate:D},{filters:g(()=>[e("div",et,[e("div",null,[t[4]||(t[4]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Cliente",-1)),w(e("select",{"onUpdate:modelValue":t[0]||(t[0]=n=>a.value.client_id=n),class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},[t[3]||(t[3]=e("option",{value:""},"Tutti i clienti",-1)),(i(!0),d(M,null,A(T.value,n=>(i(),d("option",{key:n.id,value:n.id},l(n.name),9,at))),128))],512),[[C,a.value.client_id]])]),e("div",null,[t[6]||(t[6]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Tipo",-1)),w(e("select",{"onUpdate:modelValue":t[1]||(t[1]=n=>a.value.contract_type=n),class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},t[5]||(t[5]=[e("option",{value:""},"Tutti i tipi",-1),e("option",{value:"hourly"},"Orario",-1),e("option",{value:"fixed"},"Fisso",-1),e("option",{value:"retainer"},"Retainer",-1)]),512),[[C,a.value.contract_type]])]),e("div",null,[t[8]||(t[8]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Stato",-1)),w(e("select",{"onUpdate:modelValue":t[2]||(t[2]=n=>a.value.status=n),class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},t[7]||(t[7]=[e("option",{value:""},"Tutti gli stati",-1),e("option",{value:"active"},"Attivo",-1),e("option",{value:"completed"},"Completato",-1),e("option",{value:"cancelled"},"Cancellato",-1)]),512),[[C,a.value.status]])]),e("div",{class:"flex items-end"},[e("button",{onClick:P,class:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-md"}," Reset Filtri ")])])]),content:g(({data:n})=>[e("div",rt,[e("table",ot,[t[12]||(t[12]=e("thead",{class:"bg-gray-50 dark:bg-gray-800"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"}," Contratto "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"}," Cliente "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"}," Tipo "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"}," Valore "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"}," Periodo "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"}," Stato "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"}," Azioni ")])],-1)),e("tbody",st,[(i(!0),d(M,null,A(n,o=>{var y;return i(),d("tr",{key:o.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[e("td",nt,[e("div",null,[e("div",it,l(o.contract_number),1),e("div",lt,l(o.title),1)])]),e("td",dt,[e("div",ut,l(((y=o.client)==null?void 0:y.name)||"N/A"),1)]),e("td",ct,[e("span",{class:J(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",j(o.contract_type)])},l(K(U)(o.contract_type)),3)]),e("td",gt,[o.contract_type==="hourly"?(i(),d("div",pt,[p(" €"+l(k(o.hourly_rate))+"/ora ",1),o.budget_hours?(i(),d("div",xt," Max: "+l(o.budget_hours)+"h ",1)):z("",!0)])):o.budget_amount?(i(),d("div",yt," €"+l(k(o.budget_amount)),1)):(i(),d("div",mt,"N/A"))]),e("td",bt,[e("div",null,l(V(o.start_date)),1),o.end_date?(i(),d("div",vt," al "+l(V(o.end_date)),1)):(i(),d("div",ft," Indeterminato "))]),e("td",kt,[c(X,{status:o.status,type:"contract"},null,8,["status"])]),e("td",_t,[e("div",ht,[c(s,{to:`/app/crm/contracts/${o.id}`,class:"inline-flex items-center px-3 py-1 text-xs font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 dark:text-blue-400 dark:bg-blue-900/20 dark:hover:bg-blue-900/30 transition-colors"},{default:g(()=>[c(x,{name:"eye",class:"w-3 h-3 mr-1"}),t[9]||(t[9]=p(" Visualizza "))]),_:2,__:[9]},1032,["to"]),c(s,{to:`/app/crm/contracts/${o.id}/edit`,class:"inline-flex items-center px-3 py-1 text-xs font-medium rounded-md text-gray-700 bg-gray-100 hover:bg-gray-200 dark:text-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 transition-colors"},{default:g(()=>[c(x,{name:"pencil",class:"w-3 h-3 mr-1"}),t[10]||(t[10]=p(" Modifica "))]),_:2,__:[10]},1032,["to"]),e("button",{onClick:_=>R(o.id),class:"inline-flex items-center px-3 py-1 text-xs font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 dark:text-red-400 dark:bg-red-900/20 dark:hover:bg-red-900/30 transition-colors"},[c(x,{name:"trash",class:"w-3 h-3 mr-1"}),t[11]||(t[11]=p(" Elimina "))],8,wt)])])])}),128))])])])]),"empty-state":g(()=>[e("div",Ct,[c(x,{name:"document-text",class:"w-16 h-16 mx-auto mb-4 text-gray-300 dark:text-gray-600"}),t[14]||(t[14]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},"Nessun contratto trovato",-1)),e("p",Tt,l(S.value?"Prova a modificare i filtri di ricerca":"Inizia creando il tuo primo contratto"),1),S.value?z("",!0):(i(),N(s,{key:0,to:"/app/crm/contracts/new",class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},{default:g(()=>[c(x,{name:"plus",class:"w-4 h-4 mr-2"}),t[13]||(t[13]=p(" Crea Primo Contratto "))]),_:1,__:[13]}))])]),_:1},8,["data","stats","loading"])}}};export{Lt as default};
