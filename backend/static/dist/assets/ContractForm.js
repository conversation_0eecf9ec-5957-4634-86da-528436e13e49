import{r as _,c as w,u as D,o as M,b as n,e as O,l as r,g,A as R,B as u,S as p,F as m,q as z,t as c,C as d,x as A,s as F,v as B,j as i}from"./vendor.js";import{u as L}from"./crm.js";import{u as j}from"./useToast.js";import{b as Y,C as b}from"./contractTypes.js";import{_ as P}from"./PageHeader.js";import"./app.js";const $={class:"min-h-screen bg-gray-50 dark:bg-gray-900"},H={class:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},X={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},J={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},G=["value"],K={key:0,class:"mt-1 text-sm text-red-600 dark:text-red-400"},Q=["value"],W={key:0,class:"mt-1 text-sm text-red-600 dark:text-red-400"},Z={class:"md:col-span-2"},ee={key:0,class:"mt-1 text-sm text-red-600 dark:text-red-400"},te={class:"md:col-span-2"},re={key:0,class:"border-t border-gray-200 dark:border-gray-700 pt-8"},ae={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},oe={key:1},le={class:"border-t border-gray-200 dark:border-gray-700 pt-8"},ne={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},ue={class:"flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700"},ie=["disabled"],se={key:0,class:"flex items-center"},de={key:1},be={key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 dark:bg-gray-900 dark:bg-opacity-70 flex items-center justify-center z-50"},xe={__name:"ContractForm",setup(ge){const y=D(),v=F(),q=L(),{showToast:f}=j(),x=_(!1),k=_(!1),o=_({}),t=_({client_id:"",title:"",description:"",contract_type:"",contract_number:"",status:"active",hourly_rate:null,budget_hours:null,budget_amount:null,retainer_amount:null,retainer_frequency:"",milestone_amount:null,milestone_count:null,subscription_amount:null,subscription_frequency:"",start_date:"",end_date:""}),s=w(()=>y.params.id&&y.params.id!=="new"),C=w(()=>s.value?parseInt(y.params.id):null),I=w(()=>q.clients||[]),T=w(()=>Y()),V=async()=>{if(s.value)try{k.value=!0;const l=await fetch(`/api/contracts/${C.value}`);if(l.ok){const a=(await l.json()).data;t.value={client_id:a.client_id||"",title:a.title||"",description:a.description||"",contract_type:a.contract_type||"",contract_number:a.contract_number||"",status:a.status||"active",hourly_rate:a.hourly_rate||null,budget_hours:a.budget_hours||null,budget_amount:a.total_budget||null,retainer_amount:a.retainer_amount||null,retainer_frequency:a.retainer_frequency||"",milestone_amount:a.milestone_amount||null,milestone_count:a.milestone_count||null,subscription_amount:a.subscription_amount||null,subscription_frequency:a.subscription_frequency||"",start_date:a.start_date||"",end_date:a.end_date||""}}else throw new Error("Contratto non trovato")}catch(l){console.error("Error loading contract:",l),f("Errore nel caricamento del contratto","error"),v.push("/app/crm/contracts")}finally{k.value=!1}},E=async()=>{q.clients.length===0&&await q.fetchClients()},U=()=>{if(o.value={},t.value.client_id||(o.value.client_id="Seleziona un cliente"),t.value.title.trim()||(o.value.title="Il titolo del contratto è obbligatorio"),t.value.contract_type||(o.value.contract_type="Seleziona il tipo di contratto"),t.value.contract_type===b.HOURLY?(!t.value.hourly_rate||t.value.hourly_rate<=0)&&(o.value.hourly_rate="Inserisci una tariffa oraria valida"):t.value.contract_type===b.FIXED?(!t.value.budget_amount||t.value.budget_amount<=0)&&(o.value.budget_amount="Inserisci un importo valido"):t.value.contract_type===b.RETAINER?((!t.value.retainer_amount||t.value.retainer_amount<=0)&&(o.value.retainer_amount="Inserisci un importo retainer valido"),t.value.retainer_frequency||(o.value.retainer_frequency="Seleziona la frequenza")):t.value.contract_type===b.MILESTONE?((!t.value.milestone_amount||t.value.milestone_amount<=0)&&(o.value.milestone_amount="Inserisci un importo per milestone valido"),(!t.value.milestone_count||t.value.milestone_count<=0)&&(o.value.milestone_count="Inserisci un numero di milestone valido")):t.value.contract_type===b.SUBSCRIPTION&&((!t.value.subscription_amount||t.value.subscription_amount<=0)&&(o.value.subscription_amount="Inserisci un importo abbonamento valido"),t.value.subscription_frequency||(o.value.subscription_frequency="Seleziona la frequenza")),t.value.start_date&&t.value.end_date){const l=new Date(t.value.start_date),e=new Date(t.value.end_date);l>e&&(o.value.dates="La data di inizio non può essere successiva alla data di fine")}return Object.keys(o.value).length===0},N=async()=>{if(!U()){f("Correggi gli errori nel form","error");return}try{x.value=!0;const l={client_id:parseInt(t.value.client_id),title:t.value.title,description:t.value.description,contract_type:t.value.contract_type,contract_number:t.value.contract_number||void 0,status:t.value.status,start_date:t.value.start_date||null,end_date:t.value.end_date||null};t.value.contract_type===b.HOURLY?(l.hourly_rate=t.value.hourly_rate,l.budget_hours=t.value.budget_hours||null):t.value.contract_type===b.FIXED?l.total_budget=t.value.budget_amount:t.value.contract_type===b.RETAINER?(l.retainer_amount=t.value.retainer_amount,l.retainer_frequency=t.value.retainer_frequency):t.value.contract_type===b.MILESTONE?(l.milestone_amount=t.value.milestone_amount,l.milestone_count=t.value.milestone_count):t.value.contract_type===b.SUBSCRIPTION&&(l.subscription_amount=t.value.subscription_amount,l.subscription_frequency=t.value.subscription_frequency);const e=s.value?`/api/contracts/${C.value}`:"/api/contracts/",a=s.value?"PUT":"POST",S=await fetch(e,{method:a,headers:{"Content-Type":"application/json"},body:JSON.stringify(l)});if(S.ok){const h=await S.json();f(s.value?"Contratto aggiornato con successo":"Contratto creato con successo","success"),s.value?v.push(`/app/crm/contracts/${C.value}`):v.push(`/app/crm/contracts/${h.data.id}`)}else{const h=await S.json();throw new Error(h.message||"Errore nel salvataggio")}}catch(l){console.error("Error saving contract:",l),f(s.value?"Errore nell'aggiornamento del contratto":"Errore nella creazione del contratto","error")}finally{x.value=!1}};return M(async()=>{await E(),y.query.client_id&&!s.value&&(t.value.client_id=parseInt(y.query.client_id)),s.value||(t.value.start_date=new Date().toISOString().split("T")[0]),s.value&&await V()}),(l,e)=>(i(),n("div",$,[O(P,{title:s.value?"Modifica Contratto":"Nuovo Contratto",subtitle:s.value?"Aggiorna i dettagli del contratto":"Crea un nuovo contratto commerciale",breadcrumbs:[{name:"CRM",href:"/app/crm"},{name:"Contratti",href:"/app/crm/contracts"},{name:s.value?"Modifica":"Nuovo",href:"#",current:!0}],loading:k.value},null,8,["title","subtitle","breadcrumbs","loading"]),r("div",H,[r("div",X,[r("form",{onSubmit:R(N,["prevent"]),class:"p-6 space-y-8"},[r("div",null,[e[28]||(e[28]=r("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},"Informazioni Base",-1)),e[29]||(e[29]=r("p",{class:"text-sm text-gray-600 dark:text-gray-400 mb-6"},"Dati principali del contratto",-1)),r("div",J,[r("div",null,[e[19]||(e[19]=r("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Cliente * ",-1)),u(r("select",{"onUpdate:modelValue":e[0]||(e[0]=a=>t.value.client_id=a),required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},[e[18]||(e[18]=r("option",{value:""},"Seleziona cliente",-1)),(i(!0),n(m,null,z(I.value,a=>(i(),n("option",{key:a.id,value:a.id},c(a.name),9,G))),128))],512),[[p,t.value.client_id]]),o.value.client_id?(i(),n("p",K,c(o.value.client_id),1)):g("",!0)]),r("div",null,[e[21]||(e[21]=r("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Tipo Contratto * ",-1)),u(r("select",{"onUpdate:modelValue":e[1]||(e[1]=a=>t.value.contract_type=a),required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},[e[20]||(e[20]=r("option",{value:""},"Seleziona tipo",-1)),(i(!0),n(m,null,z(T.value,a=>(i(),n("option",{key:a.value,value:a.value},c(a.label),9,Q))),128))],512),[[p,t.value.contract_type]]),o.value.contract_type?(i(),n("p",W,c(o.value.contract_type),1)):g("",!0)]),r("div",Z,[e[22]||(e[22]=r("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Titolo Contratto * ",-1)),u(r("input",{"onUpdate:modelValue":e[2]||(e[2]=a=>t.value.title=a),type:"text",required:"",placeholder:"Es. Sviluppo piattaforma e-commerce",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},null,512),[[d,t.value.title]]),o.value.title?(i(),n("p",ee,c(o.value.title),1)):g("",!0)]),r("div",null,[e[23]||(e[23]=r("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Numero Contratto ",-1)),u(r("input",{"onUpdate:modelValue":e[3]||(e[3]=a=>t.value.contract_number=a),type:"text",placeholder:"Auto-generato se vuoto",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},null,512),[[d,t.value.contract_number]]),e[24]||(e[24]=r("p",{class:"mt-1 text-xs text-gray-500 dark:text-gray-400"}," Lascia vuoto per auto-generazione (YYYY-NNNN) ",-1))]),r("div",null,[e[26]||(e[26]=r("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Stato ",-1)),u(r("select",{"onUpdate:modelValue":e[4]||(e[4]=a=>t.value.status=a),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},e[25]||(e[25]=[r("option",{value:"active"},"Attivo",-1),r("option",{value:"completed"},"Completato",-1),r("option",{value:"cancelled"},"Cancellato",-1)]),512),[[p,t.value.status]])]),r("div",te,[e[27]||(e[27]=r("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Descrizione ",-1)),u(r("textarea",{"onUpdate:modelValue":e[5]||(e[5]=a=>t.value.description=a),rows:"4",placeholder:"Descrivi i dettagli del contratto, scope, deliverable, etc...",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},null,512),[[d,t.value.description]])])])]),t.value.contract_type?(i(),n("div",re,[e[42]||(e[42]=r("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},"Dettagli Finanziari",-1)),e[43]||(e[43]=r("p",{class:"text-sm text-gray-600 dark:text-gray-400 mb-6"},"Configurazione economica del contratto",-1)),r("div",ae,[t.value.contract_type==="hourly"?(i(),n(m,{key:0},[r("div",null,[e[30]||(e[30]=r("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Tariffa Oraria (€) * ",-1)),u(r("input",{"onUpdate:modelValue":e[6]||(e[6]=a=>t.value.hourly_rate=a),type:"number",step:"0.01",min:"0",required:"",placeholder:"0.00",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},null,512),[[d,t.value.hourly_rate,void 0,{number:!0}]])]),r("div",null,[e[31]||(e[31]=r("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Budget Ore (opzionale) ",-1)),u(r("input",{"onUpdate:modelValue":e[7]||(e[7]=a=>t.value.budget_hours=a),type:"number",step:"0.5",min:"0",placeholder:"0",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},null,512),[[d,t.value.budget_hours,void 0,{number:!0}]]),e[32]||(e[32]=r("p",{class:"mt-1 text-xs text-gray-500 dark:text-gray-400"}," Massimo ore fatturabili ",-1))])],64)):t.value.contract_type==="fixed"?(i(),n("div",oe,[e[33]||(e[33]=r("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Importo Fisso (€) * ",-1)),u(r("input",{"onUpdate:modelValue":e[8]||(e[8]=a=>t.value.budget_amount=a),type:"number",step:"0.01",min:"0",required:"",placeholder:"0.00",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},null,512),[[d,t.value.budget_amount,void 0,{number:!0}]])])):t.value.contract_type==="retainer"?(i(),n(m,{key:2},[r("div",null,[e[34]||(e[34]=r("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Importo Retainer (€) * ",-1)),u(r("input",{"onUpdate:modelValue":e[9]||(e[9]=a=>t.value.retainer_amount=a),type:"number",step:"0.01",min:"0",required:"",placeholder:"0.00",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},null,512),[[d,t.value.retainer_amount,void 0,{number:!0}]])]),r("div",null,[e[36]||(e[36]=r("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Frequenza * ",-1)),u(r("select",{"onUpdate:modelValue":e[10]||(e[10]=a=>t.value.retainer_frequency=a),required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},e[35]||(e[35]=[r("option",{value:""},"Seleziona frequenza",-1),r("option",{value:"monthly"},"Mensile",-1),r("option",{value:"quarterly"},"Trimestrale",-1),r("option",{value:"yearly"},"Annuale",-1)]),512),[[p,t.value.retainer_frequency]])])],64)):t.value.contract_type==="milestone"?(i(),n(m,{key:3},[r("div",null,[e[37]||(e[37]=r("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Valore per Milestone (€) * ",-1)),u(r("input",{"onUpdate:modelValue":e[11]||(e[11]=a=>t.value.milestone_amount=a),type:"number",step:"0.01",min:"0",required:"",placeholder:"0.00",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},null,512),[[d,t.value.milestone_amount,void 0,{number:!0}]])]),r("div",null,[e[38]||(e[38]=r("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Numero Milestone * ",-1)),u(r("input",{"onUpdate:modelValue":e[12]||(e[12]=a=>t.value.milestone_count=a),type:"number",min:"1",required:"",placeholder:"1",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},null,512),[[d,t.value.milestone_count,void 0,{number:!0}]])])],64)):t.value.contract_type==="subscription"?(i(),n(m,{key:4},[r("div",null,[e[39]||(e[39]=r("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Importo Abbonamento (€) * ",-1)),u(r("input",{"onUpdate:modelValue":e[13]||(e[13]=a=>t.value.subscription_amount=a),type:"number",step:"0.01",min:"0",required:"",placeholder:"0.00",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},null,512),[[d,t.value.subscription_amount,void 0,{number:!0}]])]),r("div",null,[e[41]||(e[41]=r("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Frequenza * ",-1)),u(r("select",{"onUpdate:modelValue":e[14]||(e[14]=a=>t.value.subscription_frequency=a),required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},e[40]||(e[40]=[r("option",{value:""},"Seleziona frequenza",-1),r("option",{value:"monthly"},"Mensile",-1),r("option",{value:"yearly"},"Annuale",-1)]),512),[[p,t.value.subscription_frequency]])])],64)):g("",!0)])])):g("",!0),r("div",le,[e[47]||(e[47]=r("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},"Periodo Contratto",-1)),e[48]||(e[48]=r("p",{class:"text-sm text-gray-600 dark:text-gray-400 mb-6"},"Date di validità del contratto",-1)),r("div",ne,[r("div",null,[e[44]||(e[44]=r("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Data Inizio * ",-1)),u(r("input",{"onUpdate:modelValue":e[15]||(e[15]=a=>t.value.start_date=a),type:"date",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},null,512),[[d,t.value.start_date]])]),r("div",null,[e[45]||(e[45]=r("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Data Fine (opzionale) ",-1)),u(r("input",{"onUpdate:modelValue":e[16]||(e[16]=a=>t.value.end_date=a),type:"date",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},null,512),[[d,t.value.end_date]]),e[46]||(e[46]=r("p",{class:"mt-1 text-xs text-gray-500 dark:text-gray-400"}," Lascia vuoto per contratto a tempo indeterminato ",-1))])])]),r("div",ue,[r("button",{type:"button",onClick:e[17]||(e[17]=a=>A(v).push("/app/crm/contracts")),class:"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"}," Annulla "),r("button",{type:"submit",disabled:x.value,class:"px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-md text-sm font-medium transition-colors duration-200"},[x.value?(i(),n("span",se,e[49]||(e[49]=[r("svg",{class:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",fill:"none",viewBox:"0 0 24 24"},[r("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),r("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),B(" Salvataggio... ")]))):(i(),n("span",de,c(s.value?"Aggiorna Contratto":"Crea Contratto"),1))],8,ie)])],32)])]),k.value?(i(),n("div",be,e[50]||(e[50]=[r("div",{class:"bg-white dark:bg-gray-800 rounded-lg p-6"},[r("div",{class:"flex items-center"},[r("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-4"}),r("span",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Caricamento...")])],-1)]))):g("",!0)]))}};export{xe as default};
