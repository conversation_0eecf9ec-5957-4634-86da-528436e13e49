import{_ as i,H as l}from"./app.js";/* empty css                                                             */import{b as s,j as a,l as c,F as b,q as g,n as o,f as u,g as d,v as m,t as n}from"./vendor.js";const x={class:"border-b border-gray-200 dark:border-gray-700"},y={class:"-mb-px flex space-x-8"},p=["onClick","disabled"],k={__name:"TabNavigation",props:{tabs:{type:Array,required:!0,validator:r=>r.every(t=>t.id&&t.name)},activeTab:{type:String,required:!0}},emits:["tab-change"],setup(r){return(t,v)=>(a(),s("div",x,[c("nav",y,[(a(!0),s(b,null,g(r.tabs,e=>(a(),s("button",{key:e.id,onClick:_=>!e.disabled&&t.$emit("tab-change",e.id),disabled:e.disabled,class:o(["py-2 px-1 border-b-2 font-medium text-sm flex items-center transition-colors duration-200",e.disabled?"border-transparent text-gray-400 dark:text-gray-600 cursor-not-allowed opacity-50":r.activeTab===e.id?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600 cursor-pointer"])},[e.icon?(a(),u(l,{key:0,name:e.icon,class:o(["w-5 h-5 mr-2",e.disabled?"text-gray-400":""])},null,8,["name","class"])):d("",!0),m(" "+n(e.name||e.label)+" ",1),e.badge?(a(),s("span",{key:1,class:o(["ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",r.activeTab===e.id?"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"])},n(e.badge),3)):d("",!0)],10,p))),128))])]))}},N=i(k,[["__scopeId","data-v-eb669053"]]);export{N as T};
