import{r as d,c as p,o as X,w as Y,b as i,g as S,l as t,e as c,t as r,B as y,S as V,C,F as Z,q as tt,A as j,j as l,n as I,x as A}from"./vendor.js";import{u as et}from"./timesheet.js";import{b as at,H as g}from"./app.js";const M=m=>new Date(m).toLocaleDateString("it-IT"),st=m=>{switch(m){case"approved":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";case"submitted":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";case"confirmed":return"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";case"rejected":return"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";default:return"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"}},rt=m=>{switch(m){case"approved":return"Approvato";case"submitted":return"In Attesa";case"confirmed":return"Confermato";case"rejected":return"Rifiutato";default:return"Bozza"}},dt={class:"space-y-6"},ot={key:0,class:"bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800 rounded-md p-4"},it={class:"flex"},lt={class:"flex-shrink-0"},nt={class:"ml-3"},ut={class:"text-sm text-red-800 dark:text-red-200"},ct={class:"ml-auto pl-3"},gt={class:"-mx-1.5 -my-1.5"},mt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},xt={class:"flex justify-between items-center"},pt={class:"flex space-x-3"},yt=["disabled"],vt=["disabled"],bt=["disabled"],ft={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-4"},kt={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},ht={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},wt={class:"overflow-x-auto"},_t={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},Rt={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},Tt={class:"px-6 py-4 whitespace-nowrap"},St={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},Ct={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},Dt={class:"px-6 py-4 text-sm text-gray-900 dark:text-white max-w-xs truncate"},zt={class:"px-6 py-4 whitespace-nowrap"},At={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},Mt={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},Ft=["onClick"],Vt=["onClick"],jt={key:2,class:"text-gray-400 dark:text-gray-500"},It={key:0,class:"text-center py-8"},Nt={class:"mx-auto h-12 w-12 text-gray-400"},$t={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},Pt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Ut={class:"flex items-center"},qt={class:"flex-shrink-0"},Wt={class:"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center"},Bt={class:"ml-5 w-0 flex-1"},Et={class:"text-lg font-medium text-gray-900 dark:text-white"},Lt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Ot={class:"flex items-center"},Qt={class:"flex-shrink-0"},Ht={class:"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center"},Gt={class:"ml-5 w-0 flex-1"},Jt={class:"text-lg font-medium text-gray-900 dark:text-white"},Kt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Xt={class:"flex items-center"},Yt={class:"flex-shrink-0"},Zt={class:"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center"},te={class:"ml-5 w-0 flex-1"},ee={class:"text-lg font-medium text-gray-900 dark:text-white"},ae={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},se={class:"flex items-center"},re={class:"flex-shrink-0"},de={class:"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center"},oe={class:"ml-5 w-0 flex-1"},ie={class:"text-lg font-medium text-gray-900 dark:text-white"},le={class:"mt-3"},ne={class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},ue={class:"grid grid-cols-1 gap-4"},ce={class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},ge=["required","placeholder"],me={class:"flex justify-end space-x-3 mt-6"},xe=["disabled"],be={__name:"TimesheetRequests",setup(m){const n=et();at();const v=d([]),b=d(!1),k=d(!1),h=d(!1),u=d(""),x=d({vacation:{remaining:0,total:0},leave:{used:0,total:0},smartworking:{used:0}}),w=d(""),_=d(""),R=d(""),o=d({start_date:"",end_date:"",notes:""}),F=p(()=>n.error),N=p(()=>{var a;return((a=x.value.vacation)==null?void 0:a.remaining)||0}),$=p(()=>{var a;return((a=x.value.leave)==null?void 0:a.used)||0}),P=p(()=>{var a;return((a=x.value.leave)==null?void 0:a.total)||0}),U=p(()=>{var a;return((a=x.value.smartworking)==null?void 0:a.used)||0}),q=p(()=>Array.isArray(v.value)?v.value.filter(a=>a.status==="pending").length:0),D=async()=>{try{const a=await n.loadTimeOffQuotas();x.value=a,console.log("Time-off quotas loaded:",a)}catch(a){console.error("Failed to load time-off quotas:",a),x.value={vacation:{remaining:0,total:0},leave:{used:0,total:0},smartworking:{used:0}},n.setError("Impossibile caricare i dati delle quote di ferie e permessi. Verranno mostrati valori predefiniti.")}},f=async()=>{b.value=!0;try{const a={request_type:w.value,status:_.value,start_date:R.value};v.value=await n.loadTimeOffRequests(a)}finally{b.value=!1}},z=a=>{u.value=a,o.value={start_date:"",end_date:"",notes:""},k.value=!0},T=()=>{k.value=!1,u.value=""},W=()=>{switch(u.value){case"vacation":return"Richiesta Ferie";case"leave":return"Richiesta Permesso";case"smartworking":return"Richiesta Smart Working";default:return"Nuova Richiesta"}},B=async()=>{h.value=!0;try{const a={type:u.value,...o.value};await n.createTimeOffRequest(a)?(await f(),await D(),T()):(T(),window.scrollTo({top:0,behavior:"smooth"}))}finally{h.value=!1}},E=a=>{u.value=a.type,o.value={start_date:a.start_date,end_date:a.end_date,notes:a.notes||""},k.value=!0},L=async a=>{if(!confirm("Sei sicuro di voler eliminare questa richiesta?"))return;await n.deleteTimeOffRequest(a)&&(await f(),await D())},O=a=>`${M(a.start_date)} - ${M(a.end_date)}`,Q=a=>`${a.duration_days||0} giorni`,H=a=>{switch(a){case"vacation":return"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";case"leave":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";case"smartworking":return"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200";default:return"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"}},G=a=>{switch(a){case"vacation":return"Ferie";case"leave":return"Permesso";case"smartworking":return"Smart Working";default:return"Altro"}},J=()=>{n.clearError()};return X(()=>{f(),D()}),Y([w,_,R],()=>{f()}),(a,e)=>(l(),i("div",dt,[F.value?(l(),i("div",ot,[t("div",it,[t("div",lt,[c(g,{name:"exclamation-triangle",size:"md",color:"text-red-400"})]),t("div",nt,[t("p",ut,r(F.value),1)]),t("div",ct,[t("div",gt,[t("button",{onClick:J,class:"inline-flex bg-red-50 dark:bg-red-900/50 rounded-md p-1.5 text-red-500 hover:bg-red-100 dark:hover:bg-red-900"},[c(g,{name:"x-mark",size:"sm"})])])])])])):S("",!0),t("div",mt,[t("div",xt,[e[10]||(e[10]=t("div",null,[t("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Richieste"),t("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Gestisci le tue richieste di ferie, permessi e smart working ")],-1)),t("div",pt,[t("button",{onClick:e[0]||(e[0]=s=>z("vacation")),disabled:b.value,"data-testid":"vacation-button",class:"bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-md text-sm font-medium"}," Richiedi Ferie ",8,yt),t("button",{onClick:e[1]||(e[1]=s=>z("leave")),disabled:b.value,"data-testid":"leave-button",class:"bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white px-4 py-2 rounded-md text-sm font-medium"}," Richiedi Permesso ",8,vt),t("button",{onClick:e[2]||(e[2]=s=>z("smartworking")),disabled:b.value,class:"bg-purple-600 hover:bg-purple-700 disabled:bg-purple-400 text-white px-4 py-2 rounded-md text-sm font-medium"}," Smart Working ",8,bt)])])]),t("div",ft,[t("div",kt,[t("div",null,[e[12]||(e[12]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Tipo Richiesta ",-1)),y(t("select",{"onUpdate:modelValue":e[3]||(e[3]=s=>w.value=s),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},e[11]||(e[11]=[t("option",{value:""},"Tutti i tipi",-1),t("option",{value:"vacation"},"Ferie",-1),t("option",{value:"leave"},"Permessi",-1),t("option",{value:"smartworking"},"Smart Working",-1)]),512),[[V,w.value]])]),t("div",null,[e[14]||(e[14]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Stato ",-1)),y(t("select",{"onUpdate:modelValue":e[4]||(e[4]=s=>_.value=s),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},e[13]||(e[13]=[t("option",{value:""},"Tutti gli stati",-1),t("option",{value:"pending"},"In Attesa",-1),t("option",{value:"approved"},"Approvato",-1),t("option",{value:"rejected"},"Rifiutato",-1)]),512),[[V,_.value]])]),t("div",null,[e[15]||(e[15]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Da Data ",-1)),y(t("input",{"onUpdate:modelValue":e[5]||(e[5]=s=>R.value=s),type:"date",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[C,R.value]])]),t("div",{class:"flex items-end"},[t("button",{onClick:f,class:"w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium"}," Filtra ")])])]),t("div",ht,[e[19]||(e[19]=t("div",{class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},[t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Le Mie Richieste ")],-1)),t("div",wt,[t("table",_t,[e[16]||(e[16]=t("thead",{class:"bg-gray-50 dark:bg-gray-700"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Tipo "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Periodo "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Durata "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Motivo "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Stato "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Richiesta il "),t("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Azioni ")])],-1)),t("tbody",Rt,[(l(!0),i(Z,null,tt(v.value,s=>(l(),i("tr",{key:s.id},[t("td",Tt,[t("span",{class:I(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",H(s.type)])},r(G(s.type)),3)]),t("td",St,r(O(s)),1),t("td",Ct,r(Q(s)),1),t("td",Dt,r(s.notes||"N/A"),1),t("td",zt,[t("span",{class:I(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",A(st)(s.status)])},r(A(rt)(s.status)),3)]),t("td",At,r(A(M)(s.created_at)),1),t("td",Mt,[s.status==="pending"?(l(),i("button",{key:0,onClick:K=>E(s),class:"text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 mr-3"}," Modifica ",8,Ft)):S("",!0),s.status==="pending"?(l(),i("button",{key:1,onClick:K=>L(s.id),class:"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"}," Elimina ",8,Vt)):(l(),i("span",jt,r(s.status==="approved"?"Approvata":"Rifiutata"),1))])]))),128))])]),v.value.length===0?(l(),i("div",It,[t("div",Nt,[c(g,{name:"document",size:"xl"})]),e[17]||(e[17]=t("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessuna richiesta",-1)),e[18]||(e[18]=t("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Non hai ancora effettuato richieste per il periodo selezionato. ",-1))])):S("",!0)])]),t("div",$t,[t("div",Pt,[t("div",Ut,[t("div",qt,[t("div",Wt,[c(g,{name:"calendar",size:"md",color:"text-white"})])]),t("div",Bt,[t("dl",null,[e[20]||(e[20]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Ferie Rimanenti ",-1)),t("dd",Et,r(N.value)+" giorni ",1)])])])]),t("div",Lt,[t("div",Ot,[t("div",Qt,[t("div",Ht,[c(g,{name:"clock",size:"md",color:"text-white"})])]),t("div",Gt,[t("dl",null,[e[21]||(e[21]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Permessi Usati ",-1)),t("dd",Jt,r($.value)+" / "+r(P.value)+" giorni ",1)])])])]),t("div",Kt,[t("div",Xt,[t("div",Yt,[t("div",Zt,[c(g,{name:"user",size:"md",color:"text-white"})])]),t("div",te,[t("dl",null,[e[22]||(e[22]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Smart Working ",-1)),t("dd",ee,r(U.value)+" giorni ",1)])])])]),t("div",ae,[t("div",se,[t("div",re,[t("div",de,[c(g,{name:"document-text",size:"md",color:"text-white"})])]),t("div",oe,[t("dl",null,[e[23]||(e[23]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," In Attesa ",-1)),t("dd",ie,r(q.value),1)])])])])]),k.value?(l(),i("div",{key:1,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:T},[t("div",{class:"relative top-20 mx-auto p-5 border w-[500px] shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:e[9]||(e[9]=j(()=>{},["stop"]))},[t("div",le,[t("h3",ne,r(W()),1),t("form",{onSubmit:j(B,["prevent"])},[t("div",ue,[t("div",null,[e[24]||(e[24]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Data Inizio ",-1)),y(t("input",{"onUpdate:modelValue":e[6]||(e[6]=s=>o.value.start_date=s),type:"date",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[C,o.value.start_date]])]),t("div",null,[e[25]||(e[25]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Data Fine ",-1)),y(t("input",{"onUpdate:modelValue":e[7]||(e[7]=s=>o.value.end_date=s),type:"date",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[C,o.value.end_date]])]),t("div",null,[t("label",ce,r(u.value==="smartworking"?"Note (opzionale)":"Motivo"),1),y(t("textarea",{"onUpdate:modelValue":e[8]||(e[8]=s=>o.value.notes=s),rows:"3",required:u.value!=="smartworking",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500",placeholder:u.value==="smartworking"?"Note aggiuntive...":"Descrivi il motivo della richiesta..."},null,8,ge),[[C,o.value.notes]])])]),t("div",me,[t("button",{type:"button",onClick:T,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md"}," Annulla "),t("button",{type:"submit",disabled:h.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50"},r(h.value?"Invio...":"Invia Richiesta"),9,xe)])],32)])])])):S("",!0)]))}};export{be as default};
