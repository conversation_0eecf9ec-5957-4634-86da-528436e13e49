import{d as ae,r as u,c as h}from"./vendor.js";import{u as re}from"./useToast.js";import{c as l}from"./app.js";const le=ae("crm",()=>{const{showToast:o}=re(),p=u([]),v=u([]),n=u([]),f=u([]),z=u([]),r=u(!1),m=u(null),E=u(null),y=u(null),w=u({totalValue:0,conversionRate:0,avgCycleTime:0,proposalsByStatus:{}}),I=h(()=>p.value.length),C=h(()=>n.value.filter(t=>["draft","sent","negotiating"].includes(t.status))),P=h(()=>C.value.reduce((t,e)=>t+(e.value||0),0)),D=h(()=>{const t={};return n.value.forEach(e=>{t[e.status]=(t[e.status]||0)+1}),t}),A=h(()=>{const t={};return n.value.forEach(e=>{const a=e.status;t[a]=(t[a]||0)+(e.value||0)}),t}),j=async(t={})=>{r.value=!0;try{const e=await l.get("/api/clients",{params:t});e.data.success&&(p.value=e.data.data.clients)}catch(e){o("Errore nel caricamento clienti","error"),console.error("Error fetching clients:",e)}finally{r.value=!1}},T=async t=>{r.value=!0;try{const e=await l.get(`/api/clients/${t}`);if(e.data.success)return m.value=e.data.data.client,e.data.data.client}catch(e){o("Errore nel caricamento cliente","error"),console.error("Error fetching client:",e)}finally{r.value=!1}},V=async t=>{r.value=!0;try{const e=await l.post("/api/clients",t);if(e.data.success)return p.value.push(e.data.data),o("Cliente creato con successo","success"),e.data.data}catch(e){throw o("Errore nella creazione cliente","error"),console.error("Error creating client:",e),e}finally{r.value=!1}},b=async(t,e)=>{r.value=!0;try{const a=await l.put(`/api/clients/${t}`,e);if(a.data.success){const c=p.value.findIndex(s=>s.id===t);return c!==-1&&(p.value[c]=a.data.data),m.value=a.data.data,o("Cliente aggiornato con successo","success"),a.data.data}}catch(a){throw o("Errore nell'aggiornamento cliente","error"),console.error("Error updating client:",a),a}finally{r.value=!1}},R=async t=>{r.value=!0;try{(await l.delete(`/api/clients/${t}`)).data.success&&(p.value=p.value.filter(a=>a.id!==t),o("Cliente eliminato con successo","success"))}catch(e){throw o("Errore nell'eliminazione cliente","error"),console.error("Error deleting client:",e),e}finally{r.value=!1}},B=async(t=null)=>{r.value=!0;try{const e=t?{client_id:t}:{},a=await l.get("/api/contacts",{params:e});a.data.success&&(v.value=a.data.data.contacts)}catch(e){o("Errore nel caricamento contatti","error"),console.error("Error fetching contacts:",e)}finally{r.value=!1}},L=async t=>{r.value=!0;try{const e=await l.post("/api/contacts",t);if(e.data.success)return v.value.push(e.data.data),o("Contatto creato con successo","success"),e.data.data}catch(e){throw o("Errore nella creazione contatto","error"),console.error("Error creating contact:",e),e}finally{r.value=!1}},M=async(t,e)=>{r.value=!0;try{const a=await l.put(`/api/contacts/${t}`,e);if(a.data.success){const c=v.value.findIndex(s=>s.id===t);return c!==-1&&(v.value[c]=a.data.data),o("Contatto aggiornato con successo","success"),a.data.data}}catch(a){throw o("Errore nell'aggiornamento contatto","error"),console.error("Error updating contact:",a),a}finally{r.value=!1}},F=async t=>{r.value=!0;try{(await l.delete(`/api/contacts/${t}`)).data.success&&(v.value=v.value.filter(a=>a.id!==t),o("Contatto eliminato con successo","success"))}catch(e){throw o("Errore nell'eliminazione contatto","error"),console.error("Error deleting contact:",e),e}finally{r.value=!1}},_=async(t={})=>{r.value=!0;try{const e=await l.get("/api/proposals",{params:t});e.data.success&&(n.value=e.data.data.proposals,g())}catch(e){o("Errore nel caricamento proposte","error"),console.error("Error fetching proposals:",e)}finally{r.value=!1}},O=async t=>{r.value=!0;try{const e=await l.get(`/api/proposals/${t}`);if(e.data.success)return E.value=e.data.data,e.data.data}catch(e){o("Errore nel caricamento proposta","error"),console.error("Error fetching proposal:",e)}finally{r.value=!1}},W=async t=>{r.value=!0;try{const e=await l.post("/api/proposals",t);if(e.data.success)return n.value.push(e.data.data),g(),o("Proposta creata con successo","success"),e.data.data}catch(e){throw o("Errore nella creazione proposta","error"),console.error("Error creating proposal:",e),e}finally{r.value=!1}},k=async(t,e)=>{r.value=!0;try{const a=await l.put(`/api/proposals/${t}`,e);if(a.data.success){const c=n.value.findIndex(s=>s.id===t);return c!==-1&&(n.value[c]=a.data.data),E.value=a.data.data,g(),a.data.data}}catch(a){throw console.error("Error updating proposal:",a),a}finally{r.value=!1}},q=async(t,e)=>{r.value=!0;try{if((await l.patch(`/api/proposals/${t}/status`,{status:e})).data.success){const c=n.value.findIndex(s=>s.id===t);c!==-1&&(n.value[c]={...n.value[c],status:e}),g(),o("Stato proposta aggiornato","success")}}catch(a){throw o("Errore nell'aggiornamento stato","error"),console.error("Error updating proposal status:",a),a}finally{r.value=!1}},G=async t=>{r.value=!0;try{(await l.delete(`/api/proposals/${t}`)).data.success&&(n.value=n.value.filter(a=>a.id!==t),g(),o("Proposta eliminata con successo","success"))}catch(e){throw o("Errore nell'eliminazione proposta","error"),console.error("Error deleting proposal:",e),e}finally{r.value=!1}},H=async t=>{r.value=!0;try{const e=await l.post("/api/proposals/generate-ai",t);if(e.data.success)return o({type:"success",title:"Proposta generata con AI",message:"La proposta è stata generata con successo. Controlla il pannello AI per vedere i dettagli e applicarli al form.",duration:5e3}),e.data.data}catch(e){throw o({type:"error",title:"Errore nella generazione AI",message:"Si è verificato un errore durante la generazione della proposta. Riprova più tardi.",duration:6e3}),console.error("Error generating proposal with AI:",e),e}finally{r.value=!1}},J=async(t,e={})=>{var a,c;r.value=!0;try{const s=await l.post(`/api/proposals/${t}/convert-to-project`,e);if(s.data.success){const i=n.value.findIndex(d=>d.id===t);return i!==-1&&(n.value[i]={...n.value[i],converted_to_project:!0,project_id:s.data.data.project.id}),o({type:"success",title:"Progetto creato con successo",message:`La proposta è stata convertita nel progetto "${s.data.data.project.name}". Vai alla sezione progetti per gestirlo.`,duration:6e3}),s.data.data}}catch(s){throw o({type:"error",title:"Errore nella conversione",message:((c=(a=s.response)==null?void 0:a.data)==null?void 0:c.message)||"Si è verificato un errore durante la conversione della proposta.",duration:6e3}),console.error("Error converting proposal to project:",s),s}finally{r.value=!1}},K=async(t,e={})=>{var a,c;r.value=!0;try{const s=await l.post(`/api/proposals/${t}/create-contract`,e);if(s.data.success){const i=n.value.findIndex(d=>d.id===t);return i!==-1&&(n.value[i]={...n.value[i],converted_to_contract:!0,contract_id:s.data.data.contract.id}),f.value.length>0&&f.value.unshift(s.data.data.contract),o({type:"success",title:"Contratto creato con successo",message:`La proposta è stata convertita nel contratto "${s.data.data.contract.title}". Ora puoi creare un progetto dal contratto.`,duration:6e3}),s.data.data}}catch(s){throw o({type:"error",title:"Errore nella creazione contratto",message:((c=(a=s.response)==null?void 0:a.data)==null?void 0:c.message)||"Si è verificato un errore durante la creazione del contratto.",duration:6e3}),console.error("Error creating contract from proposal:",s),s}finally{r.value=!1}},$=async(t={})=>{r.value=!0;try{const e=await l.get("/api/contracts",{params:t});e.data.success&&(f.value=e.data.data.contracts)}catch(e){o("Errore nel caricamento contratti","error"),console.error("Error fetching contracts:",e)}finally{r.value=!1}},N=async t=>{r.value=!0;try{const e=await l.get(`/api/contracts/${t}`);if(e.data.success)return y.value=e.data.data,e.data.data}catch(e){o("Errore nel caricamento contratto","error"),console.error("Error fetching contract:",e)}finally{r.value=!1}},Q=async(t,e={})=>{var a,c,s;r.value=!0;try{const i=await l.post(`/api/contracts/${t}/create-project`,e);if(i.data.success){const d=f.value.findIndex(te=>te.id===t);return d!==-1&&(f.value[d]={...f.value[d],converted_to_project:!0,project_id:i.data.data.project.id}),((a=y.value)==null?void 0:a.id)===t&&(y.value={...y.value,converted_to_project:!0,project_id:i.data.data.project.id}),o({type:"success",title:"Progetto creato con successo",message:`Il contratto è stato convertito nel progetto "${i.data.data.project.name}". Vai alla sezione progetti per gestirlo.`,duration:6e3}),i.data.data}}catch(i){throw o({type:"error",title:"Errore nella conversione",message:((s=(c=i.response)==null?void 0:c.data)==null?void 0:s.message)||"Si è verificato un errore durante la conversione del contratto.",duration:6e3}),console.error("Error converting contract to project:",i),i}finally{r.value=!1}},g=()=>{const t=n.value.reduce((e,a)=>(e[a.status]=(e[a.status]||0)+1,e),{});w.value={totalValue:P.value,conversionRate:U(),avgCycleTime:X(),proposalsByStatus:t}},U=()=>{const t=n.value.length,e=n.value.filter(a=>a.status==="accepted").length;return t>0?Math.round(e/t*100):0},X=()=>{const t=n.value.filter(a=>["accepted","rejected"].includes(a.status)&&a.sent_date);if(t.length===0)return 0;const e=t.reduce((a,c)=>{const s=new Date(c.sent_date),i=new Date(c.updated_at),d=Math.ceil((i-s)/(1e3*60*60*24));return a+d},0);return Math.round(e/t.length)},Y=()=>{m.value=null},Z=()=>{E.value=null},ee=()=>{y.value=null},x=u([]),S=async(t=10)=>{try{const e=await l.get("/api/dashboard/recent-activities",{params:{limit:t}});e.data.success&&(x.value=e.data.data.activities)}catch(e){console.error("Error fetching recent activities:",e),o("Errore nel caricamento attività recenti","error")}};return{clients:p,contacts:v,proposals:n,contracts:f,invoices:z,loading:r,currentClient:m,currentProposal:E,currentContract:y,pipelineStats:w,clientsCount:I,activeProposals:C,totalPipelineValue:P,proposalsByStatus:D,pipelineValueByStatus:A,recentActivities:x,fetchClients:j,getClient:T,createClient:V,updateClient:b,deleteClient:R,fetchContacts:B,createContact:L,updateContact:M,deleteContact:F,fetchProposals:_,getProposal:O,createProposal:W,updateProposal:k,updateProposalStatus:q,deleteProposal:G,generateProposalWithAI:H,convertProposalToProject:J,createContractFromProposal:K,fetchContracts:$,getContract:N,convertContractToProject:Q,updatePipelineStats:g,fetchDashboardData:async()=>{try{await Promise.all([j(),_(),$(),S(10)]),g()}catch(t){console.error("Error fetching dashboard data:",t),o("Errore nel caricamento dei dati dashboard","error")}},fetchRecentActivities:S,resetCurrentClient:Y,resetCurrentProposal:Z,resetCurrentContract:ee}});export{le as u};
