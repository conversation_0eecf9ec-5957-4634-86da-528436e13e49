import{r as u,c as y,o as A,b as P,e as o,l as r,p as s,s as H,h as M,j as x,v as f,B as R,C as U,f as G,g as z,n as O,t as d}from"./vendor.js";import{u as q}from"./useToast.js";import{c as h,H as p}from"./app.js";import{_ as J}from"./PageHeader.js";import{_ as K}from"./FilterBar.js";import{_ as Q}from"./StatsGrid.js";import{_ as W}from"./DataTable.js";import{P as X}from"./Pagination.js";import"./formatters.js";const Y={class:"py-6"},Z={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6"},ee={class:"min-w-[300px]"},te={class:"relative"},ae={class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},re={class:"flex items-center justify-between"},oe={class:"text-lg font-medium text-gray-900 dark:text-white"},se={class:"text-sm font-medium text-gray-900 dark:text-white"},le={class:"text-sm text-gray-900 dark:text-white"},ne={class:"text-sm text-gray-900 dark:text-white"},ie={class:"flex items-center justify-end space-x-2"},ce=["onClick"],ue={class:"text-center py-12"},de={class:"mt-6"},we={__name:"PreInvoicesList",setup(pe){H();const{showToast:v}=q(),_=u(!1),c=u([]),k=u([]),l=u({page:1,pages:1,per_page:50,total:0,has_next:!1,has_prev:!1}),m=u({total_count:0,draft_count:0,ready_count:0,total_amount:0}),n=u({client_id:"",status:"",start_date:"",end_date:"",search:""}),S=y(()=>[{id:"client_id",label:"Cliente",placeholder:"Tutti i clienti",value:n.value.client_id,options:k.value.map(e=>({value:e.id,label:e.name}))},{id:"status",label:"Stato",placeholder:"Tutti gli stati",value:n.value.status,options:[{value:"draft",label:"Bozza"},{value:"ready",label:"Pronta"},{value:"sent_external",label:"Inviata esternamente"},{value:"invoiced",label:"Fatturata"}]},{id:"start_date",label:"Da data",placeholder:"Data inizio",value:n.value.start_date,type:"date",options:[]},{id:"end_date",label:"A data",placeholder:"Data fine",value:n.value.end_date,type:"date",options:[]}]),I=[{key:"pre_invoice_number",label:"Numero",type:"text"},{key:"client",label:"Cliente",type:"text"},{key:"period",label:"Periodo",type:"text"},{key:"status",label:"Stato",type:"badge"},{key:"total_amount",label:"Importo",type:"text",format:"currency"},{key:"created_at",label:"Data creazione",type:"text",format:"date"},{key:"actions",label:"Azioni",type:"text"}],T=y(()=>[{id:"total",label:"Totale pre-fatture",value:m.value.total_count||0,icon:"document-text",iconBgColor:"bg-blue-100 dark:bg-blue-900",iconColor:"text-blue-600 dark:text-blue-400"},{id:"draft",label:"In bozza",value:m.value.draft_count||0,icon:"exclamation-circle",iconBgColor:"bg-yellow-100 dark:bg-yellow-900",iconColor:"text-yellow-600 dark:text-yellow-400"},{id:"ready",label:"Pronte",value:m.value.ready_count||0,icon:"check-circle",iconBgColor:"bg-green-100 dark:bg-green-900",iconColor:"text-green-600 dark:text-green-400"},{id:"amount",label:"Valore totale",value:m.value.total_amount||0,format:"currency",icon:"currency-euro",iconBgColor:"bg-purple-100 dark:bg-purple-900",iconColor:"text-purple-600 dark:text-purple-400"}]),B=y(()=>l.value.page),w=e=>new Date(e).toLocaleDateString("it-IT"),D=e=>{const t={draft:"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300",ready:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",sent_external:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",invoiced:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"};return t[e]||t.draft},N=(e,t)=>{n.value[e]=t,b()},$=()=>{n.value={client_id:"",status:"",start_date:"",end_date:"",search:""},b()},g=async(e=1)=>{_.value=!0;try{const t=new URLSearchParams({page:e.toString(),per_page:l.value.per_page.toString()});Object.keys(n.value).forEach(a=>{n.value[a]&&t.append(a,n.value[a])});const i=await h.get(`/api/pre-invoices/?${t}`);i.data.success&&(c.value=i.data.data.pre_invoices,l.value=i.data.data.pagination,F())}catch(t){console.error("Errore nel caricamento pre-fatture:",t),v({type:"error",title:"Errore",message:"Impossibile caricare le pre-fatture",duration:4e3})}finally{_.value=!1}},E=async()=>{try{const e=await h.get("/api/clients/");e.data.success&&(k.value=e.data.data.clients||[])}catch(e){console.error("Errore nel caricamento clienti:",e)}},F=()=>{m.value={total_count:c.value.length,draft_count:c.value.filter(e=>e.status==="draft").length,ready_count:c.value.filter(e=>e.status==="ready").length,total_amount:c.value.reduce((e,t)=>e+t.total_amount,0)}},b=()=>{l.value.page=1,g(1)};let C;const V=()=>{clearTimeout(C),C=setTimeout(()=>{b()},500)},j=e=>{e>=1&&e<=l.value.pages&&g(e)},L=async e=>{if(confirm(`Sei sicuro di voler eliminare la pre-fattura ${e.pre_invoice_number}?`))try{(await h.delete(`/api/pre-invoices/${e.id}`)).data.success&&(v({type:"success",title:"Successo",message:"Pre-fattura eliminata con successo",duration:3e3}),g(B.value))}catch(t){console.error("Errore nell'eliminazione pre-fattura:",t),v({type:"error",title:"Errore",message:"Impossibile eliminare la pre-fattura",duration:4e3})}};return A(()=>{E(),g()}),(e,t)=>{const i=M("router-link");return x(),P("div",Y,[o(J,{title:"Pre-fatture",subtitle:"Gestione pre-fatturazione italiana con calcoli fiscali automatici",icon:"document-text","icon-color":"text-purple-600"},{actions:s(()=>[o(i,{to:"/app/invoicing/pre-invoices/new",class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},{default:s(()=>[o(p,{name:"plus",class:"w-4 h-4 mr-2"}),t[1]||(t[1]=f(" Nuova Pre-fattura "))]),_:1,__:[1]})]),_:1}),r("div",Z,[o(K,{"select-filters":S.value,onFilterChange:N,onClearFilters:$},{"additional-filters":s(()=>[r("div",ee,[r("div",te,[R(r("input",{"onUpdate:modelValue":t[0]||(t[0]=a=>n.value.search=a),onInput:V,type:"text",placeholder:"Cerca per numero pre-fattura...",class:"w-full pl-10 pr-4 py-2 rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,544),[[U,n.value.search]]),r("div",ae,[o(p,{name:"magnifying-glass",class:"h-5 w-5 text-gray-400"})])])])]),_:1},8,["select-filters"])]),o(Q,{stats:T.value,class:"mb-6"},null,8,["stats"]),o(W,{title:"Pre-fatture",columns:I,data:c.value,loading:_.value,"empty-message":"Non ci sono pre-fatture che corrispondono ai filtri selezionati.","row-key":"id"},{header:s(()=>[r("div",re,[r("h3",oe," Pre-fatture ("+d(l.value.total||0)+") ",1)])]),"cell-pre_invoice_number":s(({row:a})=>[r("div",se,d(a.pre_invoice_number),1)]),"cell-client":s(({row:a})=>[r("div",le,d(a.client.name),1)]),"cell-period":s(({row:a})=>[r("div",ne,d(w(a.billing_period_start))+" - "+d(w(a.billing_period_end)),1)]),"cell-status":s(({row:a})=>[r("span",{class:O(["inline-flex px-2 py-1 text-xs font-semibold rounded-full",D(a.status)])},d(a.display_status),3)]),"cell-actions":s(({row:a})=>[r("div",ie,[o(i,{to:`/app/invoicing/pre-invoices/${a.id}`,class:"inline-flex items-center px-3 py-1 text-xs font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 dark:text-blue-400 dark:bg-blue-900/20 dark:hover:bg-blue-900/30 transition-colors"},{default:s(()=>[o(p,{name:"eye",class:"w-3 h-3 mr-1"}),t[2]||(t[2]=f(" Visualizza "))]),_:2,__:[2]},1032,["to"]),a.status==="draft"?(x(),P("button",{key:0,onClick:me=>L(a),class:"inline-flex items-center px-3 py-1 text-xs font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 dark:text-red-400 dark:bg-red-900/20 dark:hover:bg-red-900/30 transition-colors"},[o(p,{name:"trash",class:"w-3 h-3 mr-1"}),t[3]||(t[3]=f(" Elimina "))],8,ce)):z("",!0)])]),empty:s(()=>[r("div",ue,[o(p,{name:"document-text",class:"mx-auto h-12 w-12 text-gray-400"}),t[5]||(t[5]=r("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessuna pre-fattura",-1)),t[6]||(t[6]=r("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Non ci sono pre-fatture che corrispondono ai filtri selezionati. ",-1)),r("div",de,[o(i,{to:"/app/invoicing/pre-invoices/new",class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},{default:s(()=>[o(p,{name:"plus",class:"w-4 h-4 mr-2"}),t[4]||(t[4]=f(" Crea la prima pre-fattura "))]),_:1,__:[4]})])])]),footer:s(()=>[l.value.pages>1?(x(),G(X,{key:0,"current-page":l.value.page,"total-pages":l.value.pages,total:l.value.total,"per-page":l.value.per_page,onPageChange:j},null,8,["current-page","total-pages","total","per-page"])):z("",!0)]),_:1},8,["data","loading"])])}}};export{we as default};
