import{r as v,c as y,u as V,o as N,b as i,e as a,p as _,l as e,g as m,t as l,x as u,n as $,v as d,s as A,h as T,j as n}from"./vendor.js";import{u as j}from"./funding.js";import{u as B}from"./useToast.js";import{b as M,g as x}from"./formatters.js";import{_ as F}from"./PageHeader.js";import{f as q,H as r}from"./app.js";import{S as k}from"./StatusBadge.js";const H={class:"funding-expense-view"},L={class:"flex gap-3"},R={key:0,class:"flex justify-center py-12"},P={key:1,class:"max-w-4xl mx-auto space-y-6"},U={class:"bg-white rounded-lg shadow-sm border p-6"},G={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},J={class:"text-gray-900"},K={class:"text-xl font-semibold text-gray-900"},O={class:"text-gray-900"},Q={class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"},W={key:0,class:"mt-6"},X={class:"text-gray-900 p-3 bg-gray-50 rounded-md"},Y={key:0,class:"bg-white rounded-lg shadow-sm border p-6"},Z={class:"flex items-start justify-between"},ee={class:"text-lg font-medium text-gray-900 mb-2"},te={class:"flex items-center gap-4 text-sm text-gray-600"},se={class:"bg-white rounded-lg shadow-sm border p-6"},ae={class:"grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-600"},le={key:0},oe={key:2,class:"text-center py-12"},ie={class:"bg-white rounded-lg shadow-sm border p-8 max-w-md mx-auto"},be={__name:"FundingExpenseView",setup(ne){const b=V(),w=A(),h=j(),{showToast:z}=B(),p=v(!1),s=v(null),C=y(()=>parseInt(b.params.id)),S=y(()=>[{name:"Funding",href:"/app/funding"},{name:"Spese",href:"/app/funding/expenses"},{name:"Dettaglio",href:b.path}]);function E(o){return{personnel:"Personale",equipment:"Attrezzature",travel:"Viaggi e trasferte",external:"Servizi esterni",materials:"Materiali",other:"Altro"}[o]||o}function c(){var o;(o=s.value)!=null&&o.application&&w.push(`/app/funding/applications/${s.value.application.id}`)}async function D(){p.value=!0;try{s.value=await h.fetchExpense(C.value)}catch(o){z("error","Errore nel caricamento della spesa"),console.error("Error loading expense:",o)}finally{p.value=!1}}return N(()=>{D()}),(o,t)=>{const I=T("router-link");return n(),i("div",H,[a(F,{title:"Dettaglio Spesa",subtitle:s.value?s.value.description:"Caricamento...",breadcrumbs:S.value},{actions:_(()=>{var g,f;return[e("div",L,[a(I,{to:`/app/funding/expenses/${(g=s.value)==null?void 0:g.id}/edit`,class:"inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 bg-white rounded-md hover:bg-gray-50"},{default:_(()=>[a(r,{name:"pencil",size:"sm",class:"mr-2"}),t[1]||(t[1]=d(" Modifica "))]),_:1,__:[1]},8,["to"]),(f=s.value)!=null&&f.application?(n(),i("button",{key:0,onClick:c,class:"inline-flex items-center px-4 py-2 bg-brand-primary-600 text-white rounded-md hover:bg-brand-primary-700"},[a(r,{name:"document-text",size:"sm",class:"mr-2"}),t[2]||(t[2]=d(" Vai alla Candidatura "))])):m("",!0)])]}),_:1},8,["subtitle","breadcrumbs"]),p.value?(n(),i("div",R,[a(q),t[3]||(t[3]=e("span",{class:"ml-3 text-gray-600"},"Caricamento...",-1))])):s.value?(n(),i("div",P,[e("div",U,[t[11]||(t[11]=e("h2",{class:"text-xl font-semibold text-gray-900 mb-4"},"Informazioni Spesa",-1)),e("div",G,[e("div",null,[t[4]||(t[4]=e("label",{class:"block text-sm font-medium text-gray-500 mb-1"},"Descrizione",-1)),e("p",J,l(s.value.description),1)]),e("div",null,[t[5]||(t[5]=e("label",{class:"block text-sm font-medium text-gray-500 mb-1"},"Importo",-1)),e("p",K,"€"+l(u(M)(s.value.amount)),1)]),e("div",null,[t[6]||(t[6]=e("label",{class:"block text-sm font-medium text-gray-500 mb-1"},"Data Spesa",-1)),e("p",O,l(u(x)(s.value.expense_date)),1)]),e("div",null,[t[7]||(t[7]=e("label",{class:"block text-sm font-medium text-gray-500 mb-1"},"Categoria",-1)),e("span",Q,l(E(s.value.category)),1)]),e("div",null,[t[8]||(t[8]=e("label",{class:"block text-sm font-medium text-gray-500 mb-1"},"Stato Approvazione",-1)),a(k,{status:s.value.approval_status,type:"approval"},null,8,["status"])]),e("div",null,[t[9]||(t[9]=e("label",{class:"block text-sm font-medium text-gray-500 mb-1"},"Eleggibilità",-1)),e("span",{class:$(["inline-flex items-center px-2 py-1 rounded-full text-xs font-medium",s.value.is_eligible?"bg-green-100 text-green-800":"bg-red-100 text-red-800"])},[a(r,{name:s.value.is_eligible?"check-circle":"x-circle",size:"xs",class:"mr-1"},null,8,["name"]),d(" "+l(s.value.is_eligible?"Eleggibile":"Non Eleggibile"),1)],2)])]),s.value.notes?(n(),i("div",W,[t[10]||(t[10]=e("label",{class:"block text-sm font-medium text-gray-500 mb-2"},"Note",-1)),e("p",X,l(s.value.notes),1)])):m("",!0)]),s.value.application?(n(),i("div",Y,[t[13]||(t[13]=e("h2",{class:"text-xl font-semibold text-gray-900 mb-4"},"Candidatura Collegata",-1)),e("div",Z,[e("div",null,[e("h3",ee,l(s.value.application.project_title),1),e("div",te,[e("span",null,"ID: "+l(s.value.application.id),1),a(k,{status:s.value.application.status,type:"application"},null,8,["status"])])]),e("button",{onClick:c,class:"inline-flex items-center px-3 py-2 border border-gray-300 text-gray-700 bg-white rounded-md hover:bg-gray-50"},[a(r,{name:"arrow-top-right-on-square",size:"sm",class:"mr-1"}),t[12]||(t[12]=d(" Apri "))])])])):m("",!0),e("div",se,[t[16]||(t[16]=e("h2",{class:"text-xl font-semibold text-gray-900 mb-4"},"Informazioni Sistema",-1)),e("div",ae,[e("div",null,[t[14]||(t[14]=e("label",{class:"block font-medium mb-1"},"Data Creazione",-1)),e("p",null,l(u(x)(s.value.created_at)),1)]),s.value.updated_at?(n(),i("div",le,[t[15]||(t[15]=e("label",{class:"block font-medium mb-1"},"Ultima Modifica",-1)),e("p",null,l(u(x)(s.value.updated_at)),1)])):m("",!0)])])])):(n(),i("div",oe,[e("div",ie,[a(r,{name:"exclamation-triangle",size:"lg",class:"mx-auto text-gray-400 mb-4"}),t[18]||(t[18]=e("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"Spesa non trovata",-1)),t[19]||(t[19]=e("p",{class:"text-gray-600 mb-6"},"La spesa richiesta non esiste o non hai i permessi per visualizzarla",-1)),e("button",{onClick:t[0]||(t[0]=g=>o.$router.go(-1)),class:"inline-flex items-center px-4 py-2 bg-brand-primary-600 text-white rounded-md hover:bg-brand-primary-700"},[a(r,{name:"arrow-left",size:"sm",class:"mr-2"}),t[17]||(t[17]=d(" Torna indietro "))])])]))])}}};export{be as default};
