import{r as k,c as m,o as $,f as E,p as i,s as M,h as j,j as n,l as e,e as l,v as y,b as g,F as v,q as h,g as q,t as o,x as w,n as H,B as _,S as C}from"./vendor.js";import{u as U}from"./crm.js";import{u as G}from"./useToast.js";import{g as O,a as R}from"./industries.js";import{_ as J}from"./ListPageTemplate.js";import{H as x}from"./app.js";import"./Pagination.js";const K={class:"flex space-x-4"},Q=["value"],W={class:"overflow-x-auto"},X={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},Y={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},Z={class:"px-6 py-4 whitespace-nowrap"},ee={class:"text-sm font-medium text-gray-900 dark:text-white"},te={key:0,class:"text-sm text-gray-500 dark:text-gray-400"},ae=["href"],re={class:"px-6 py-4 whitespace-nowrap"},se={class:"text-sm text-gray-900 dark:text-white"},le={class:"px-6 py-4 whitespace-nowrap"},oe={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"},ie={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"},ne={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},de={class:"flex justify-end space-x-2"},ue=["onClick"],ce={class:"text-center py-12"},he={__name:"ClientsList",setup(ge){const S=M(),p=U(),{showToast:f}=G(),d=k(""),u=k(""),z=m(()=>p.loading),c=m(()=>p.clients),I=m(()=>[{label:"Totale Clienti",value:c.value.length,icon:"users",iconClass:"text-blue-500"},{label:"Lead Attivi",value:c.value.filter(a=>a.status==="lead").length,icon:"arrow-trending-up",iconClass:"text-yellow-500"},{label:"Prospect",value:c.value.filter(a=>a.status==="prospect").length,icon:"eye",iconClass:"text-blue-500"},{label:"Clienti Attivi",value:c.value.filter(a=>a.status==="client").length,icon:"check-circle",iconClass:"text-green-500"}]),L=[{key:"name",label:"Cliente"},{key:"industry",label:"Settore"},{key:"status",label:"Stato"},{key:"contacts_count",label:"Contatti"},{key:"created_at",label:"Data Creazione"},{key:"actions",label:"Azioni"}],N=m(()=>{let a=c.value;return d.value&&(a=a.filter(t=>t.industry===d.value)),u.value&&(a=a.filter(t=>t.status===u.value)),a}),D=async()=>{await p.fetchClients()},T=()=>{S.push("/app/crm/clients/new")},V=async a=>{if(confirm("Sei sicuro di voler eliminare questo cliente?"))try{await p.deleteClient(a),f("Cliente eliminato con successo","success")}catch{f("Errore nell'eliminazione del cliente","error")}},A=()=>{d.value="",u.value=""},B=a=>({lead:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400",prospect:"bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400",client:"bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",inactive:"bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400"})[a]||"bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400",P=a=>({lead:"Lead",prospect:"Prospect",client:"Cliente",inactive:"Inattivo"})[a]||a,F=a=>a?new Date(a).toLocaleDateString("it-IT"):"N/A";return $(()=>{D()}),(a,t)=>{const b=j("router-link");return n(),E(J,{title:"Clienti",subtitle:"Gestione clienti e informazioni aziendali",data:N.value,columns:L,stats:I.value,loading:z.value,"can-create":!0,"create-label":"Nuovo Cliente","search-placeholder":"Nome cliente...","empty-message":"Inizia creando il tuo primo cliente","results-label":"clienti",onCreate:T},{filters:i(()=>[e("div",K,[e("div",null,[t[3]||(t[3]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Settore",-1)),_(e("select",{"onUpdate:modelValue":t[0]||(t[0]=s=>d.value=s),class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},[t[2]||(t[2]=e("option",{value:""},"Tutti i settori",-1)),(n(!0),g(v,null,h(w(R)(),s=>(n(),g("option",{key:s.value,value:s.value},o(s.label),9,Q))),128))],512),[[C,d.value]])]),e("div",null,[t[5]||(t[5]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Stato",-1)),_(e("select",{"onUpdate:modelValue":t[1]||(t[1]=s=>u.value=s),class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},t[4]||(t[4]=[e("option",{value:""},"Tutti gli stati",-1),e("option",{value:"lead"},"Lead",-1),e("option",{value:"prospect"},"Prospect",-1),e("option",{value:"client"},"Cliente",-1),e("option",{value:"inactive"},"Inattivo",-1)]),512),[[C,u.value]])]),e("div",{class:"flex items-end"},[e("button",{onClick:A,class:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-md"}," Pulisci Filtri ")])])]),content:i(({data:s})=>[e("div",W,[e("table",X,[t[9]||(t[9]=e("thead",{class:"bg-gray-50 dark:bg-gray-800"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"}," Cliente "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"}," Settore "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"}," Stato "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"}," Contatti "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"}," Data Creazione "),e("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"}," Azioni ")])],-1)),e("tbody",Y,[(n(!0),g(v,null,h(s,r=>(n(),g("tr",{key:r.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[e("td",Z,[e("div",null,[e("div",ee,o(r.name),1),r.website?(n(),g("div",te,[e("a",{href:r.website,target:"_blank",class:"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"},o(r.website),9,ae)])):q("",!0)])]),e("td",re,[e("span",se,o(w(O)(r.industry)),1)]),e("td",le,[e("span",{class:H([B(r.status),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},o(P(r.status)),3)]),e("td",oe,o(r.contacts_count||0)+" contatti ",1),e("td",ie,o(F(r.created_at)),1),e("td",ne,[e("div",de,[l(b,{to:`/app/crm/clients/${r.id}`,class:"inline-flex items-center px-3 py-1 text-xs font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 dark:text-blue-400 dark:bg-blue-900/20 dark:hover:bg-blue-900/30 transition-colors"},{default:i(()=>[l(x,{name:"eye",class:"w-3 h-3 mr-1"}),t[6]||(t[6]=y(" Visualizza "))]),_:2,__:[6]},1032,["to"]),l(b,{to:`/app/crm/clients/${r.id}/edit`,class:"inline-flex items-center px-3 py-1 text-xs font-medium rounded-md text-gray-700 bg-gray-100 hover:bg-gray-200 dark:text-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 transition-colors"},{default:i(()=>[l(x,{name:"pencil",class:"w-3 h-3 mr-1"}),t[7]||(t[7]=y(" Modifica "))]),_:2,__:[7]},1032,["to"]),e("button",{onClick:xe=>V(r.id),class:"inline-flex items-center px-3 py-1 text-xs font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 dark:text-red-400 dark:bg-red-900/20 dark:hover:bg-red-900/30 transition-colors"},[l(x,{name:"trash",class:"w-3 h-3 mr-1"}),t[8]||(t[8]=y(" Elimina "))],8,ue)])])]))),128))])])])]),"empty-state":i(()=>[e("div",ce,[l(x,{name:"users",class:"w-16 h-16 mx-auto mb-4 text-gray-300 dark:text-gray-600"}),t[11]||(t[11]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},"Nessun cliente trovato",-1)),t[12]||(t[12]=e("p",{class:"text-gray-500 dark:text-gray-400 mb-4"},"Inizia creando il tuo primo cliente",-1)),l(b,{to:"/app/crm/clients/new",class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},{default:i(()=>[l(x,{name:"plus",class:"w-4 h-4 mr-2"}),t[10]||(t[10]=y(" Crea Primo Cliente "))]),_:1,__:[10]})])]),_:1},8,["data","stats","loading"])}}};export{he as default};
