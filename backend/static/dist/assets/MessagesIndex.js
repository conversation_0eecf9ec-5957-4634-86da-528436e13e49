import{r as C,c as R,w as te,o as Q,y as de,b as d,j as o,l as e,e as b,f as K,g as w,B as F,W as ce,n as J,p as P,t as c,F as X,q as se,G as ue,X as ge,z as q,C as ee,v as D,A as B,x as A,S as me}from"./vendor.js";import{H as _,f as ae,_ as G,d as Y,b as W}from"./app.js";import{u as Z}from"./useFormatters.js";import{_ as fe}from"./ListPageTemplate.js";import{P as ve}from"./Pagination.js";import{C as ye}from"./ConfirmationModal.js";import"./formatters.js";function xe(n,k=300){const v=C(null);return function(...h){v.value&&clearTimeout(v.value),v.value=setTimeout(()=>{n.apply(this,h)},k)}}const be={class:"relative"},pe=["type","placeholder","disabled"],he={key:0,class:"absolute z-[9999] mt-1 w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 overflow-y-auto"},ke={key:0,class:"p-4 text-center"},_e={class:"ml-2 text-sm text-gray-500 dark:text-gray-400"},we={key:1,class:"p-4 text-center text-sm text-gray-500 dark:text-gray-400"},$e={key:2,class:"divide-y divide-gray-200 dark:divide-gray-600"},Me=["onClick","onMouseenter"],Ce={class:"flex items-center space-x-3"},Se={key:0,class:"h-8 w-8 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center"},je=["src","alt"],Ie={key:1,class:"text-xs font-medium text-gray-700 dark:text-gray-300"},ze={class:"flex-1 min-w-0"},Ne={class:"text-sm font-medium text-gray-900 dark:text-white truncate"},Ee={key:0,class:"text-xs text-gray-500 dark:text-gray-400 truncate"},Re={__name:"AutocompleteInput",props:{modelValue:{type:[String,Number,Object],default:""},options:{type:Array,default:()=>[]},labelKey:{type:String,default:"label"},valueKey:{type:String,default:"value"},descriptionKey:{type:String,default:"description"},placeholder:{type:String,default:"Cerca..."},icon:{type:String,default:null},type:{type:String,default:"text"},disabled:{type:Boolean,default:!1},clearable:{type:Boolean,default:!0},loading:{type:Boolean,default:!1},loadingText:{type:String,default:"Caricamento..."},noResultsText:{type:String,default:"Nessun risultato trovato"},minSearchLength:{type:Number,default:0},filterOnClient:{type:Boolean,default:!0}},emits:["update:modelValue","search","select","focus","blur"],setup(n,{expose:k,emit:v}){const h=n,$=v,g=C(""),f=C(!1),r=C(-1),m=C(null),y=C(null),i=R(()=>{if(!h.filterOnClient||!g.value||g.value.length<h.minSearchLength)return h.options;const l=g.value.toLowerCase();return h.options.filter(M=>{var S,L;const a=((S=M[h.labelKey])==null?void 0:S.toLowerCase())||"",p=((L=M[h.descriptionKey])==null?void 0:L.toLowerCase())||"";return a.includes(l)||p.includes(l)})}),I=l=>l[h.valueKey]||l.id||JSON.stringify(l),U=l=>l?l.split(" ").map(M=>M.charAt(0)).join("").toUpperCase().slice(0,2):"",u=l=>{const M=l.target.value;g.value=M,r.value=-1,M.length>=h.minSearchLength?(f.value=!0,$("search",M)):f.value=!1,$("update:modelValue",M)},x=()=>{(i.value.length>0||h.loading)&&(f.value=!0),$("focus")},E=()=>{setTimeout(()=>{f.value=!1,r.value=-1},150),$("blur")},O=l=>{var M;if(f.value)switch(l.key){case"ArrowDown":l.preventDefault(),r.value=Math.min(r.value+1,i.value.length-1);break;case"ArrowUp":l.preventDefault(),r.value=Math.max(r.value-1,-1);break;case"Enter":l.preventDefault(),r.value>=0&&r.value<i.value.length&&j(i.value[r.value]);break;case"Escape":f.value=!1,r.value=-1,(M=m.value)==null||M.blur();break}},j=l=>{g.value=l[h.labelKey],f.value=!1,r.value=-1,$("update:modelValue",l[h.valueKey]||l),$("select",l),q(()=>{var M;(M=m.value)==null||M.blur()})},T=()=>{g.value="",f.value=!1,r.value=-1,$("update:modelValue",""),$("search",""),q(()=>{var l;(l=m.value)==null||l.focus()})},V=l=>{y.value&&!y.value.contains(l.target)&&(f.value=!1,r.value=-1)};return te(()=>h.modelValue,l=>{typeof l=="string"?g.value=l:l&&typeof l=="object"&&(g.value=l[h.labelKey]||"")},{immediate:!0}),Q(()=>{document.addEventListener("click",V)}),de(()=>{document.removeEventListener("click",V)}),k({focus:()=>{var l;return(l=m.value)==null?void 0:l.focus()},blur:()=>{var l;return(l=m.value)==null?void 0:l.blur()},clear:T}),(l,M)=>(o(),d("div",{class:"relative",ref_key:"autocompleteContainer",ref:y},[e("div",be,[n.icon?(o(),K(_,{key:0,name:n.icon,size:"sm",class:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none"},null,8,["name"])):w("",!0),F(e("input",{ref_key:"inputRef",ref:m,"onUpdate:modelValue":M[0]||(M[0]=a=>g.value=a),type:n.type,placeholder:n.placeholder,disabled:n.disabled,class:J(["block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500",n.icon?"pl-10":"",n.disabled?"opacity-50 cursor-not-allowed":""]),onInput:u,onFocus:x,onBlur:E,onKeydown:O,autocomplete:"off"},null,42,pe),[[ce,g.value]]),g.value&&n.clearable&&!n.disabled?(o(),d("button",{key:1,onClick:T,type:"button",class:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},[b(_,{name:"x-mark",size:"sm"})])):w("",!0)]),b(ge,{"enter-active-class":"transition ease-out duration-100","enter-from-class":"transform opacity-0 scale-95","enter-to-class":"transform opacity-100 scale-100","leave-active-class":"transition ease-in duration-75","leave-from-class":"transform opacity-100 scale-100","leave-to-class":"transform opacity-0 scale-95"},{default:P(()=>[f.value&&(i.value.length>0||n.loading||n.noResultsText)?(o(),d("div",he,[n.loading?(o(),d("div",ke,[b(ae,{size:"sm"}),e("span",_e,c(n.loadingText),1)])):i.value.length===0&&n.noResultsText?(o(),d("div",we,c(n.noResultsText),1)):(o(),d("div",$e,[(o(!0),d(X,null,se(i.value,(a,p)=>(o(),d("div",{key:I(a),class:J(["p-3 cursor-pointer transition-colors duration-150",p===r.value?"bg-blue-50 dark:bg-blue-900/30":"hover:bg-gray-50 dark:hover:bg-gray-700"]),onClick:S=>j(a),onMouseenter:S=>r.value=p},[ue(l.$slots,"option",{option:a,index:p},()=>[e("div",Ce,[a.avatar?(o(),d("div",Se,[a.avatar?(o(),d("img",{key:0,src:a.avatar,alt:a[n.labelKey],class:"h-8 w-8 rounded-full"},null,8,je)):(o(),d("span",Ie,c(U(a[n.labelKey])),1))])):w("",!0),e("div",ze,[e("p",Ne,c(a[n.labelKey]),1),a[n.descriptionKey]?(o(),d("p",Ee,c(a[n.descriptionKey]),1)):w("",!0)])])])],42,Me))),128))]))])):w("",!0)]),_:3})],512))}},De={class:"message-composer"},Ae={key:0},Ue={class:"relative"},Ke={class:"absolute bottom-2 right-2 text-xs text-gray-400"},Oe={class:"flex items-center justify-between"},Te={class:"flex items-center space-x-2"},Ve={class:"flex items-center space-x-2"},Pe=["disabled"],Le={__name:"MessageComposer",props:{recipientId:{type:[String,Number],required:!0},recipientName:{type:String,required:!0},conversationId:{type:[String,Number],default:null},initialSubject:{type:String,default:""},placeholder:{type:String,default:"Scrivi il tuo messaggio..."}},emits:["message-sent","error"],setup(n,{emit:k}){const v=n,h=k,$=Y(),g=C(v.initialSubject),f=C(""),r=C(!!v.initialSubject),m=C(!1),y=C(null),i=async()=>{if(!(!f.value.trim()||m.value)){m.value=!0;try{const u={recipient_id:v.recipientId,content:f.value.trim(),subject:g.value.trim()||null,conversation_id:v.conversationId||null},x=await $.sendMessage(u);I(),h("message-sent",x),await q(),y.value&&y.value.focus()}catch(u){console.error("Errore nell'invio del messaggio:",u),h("error",u)}finally{m.value=!1}}},I=()=>{f.value="",g.value=v.initialSubject,r.value=!!v.initialSubject},U=u=>{(u.ctrlKey||u.metaKey)&&u.key==="Enter"&&(u.preventDefault(),i())};return q(()=>{y.value&&y.value.focus()}),(u,x)=>(o(),d("div",De,[e("form",{onSubmit:B(i,["prevent"]),class:"space-y-4"},[r.value||g.value?(o(),d("div",Ae,[x[3]||(x[3]=e("label",{for:"subject",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Oggetto (opzionale) ",-1)),F(e("input",{id:"subject","onUpdate:modelValue":x[0]||(x[0]=E=>g.value=E),type:"text",placeholder:"Inserisci l'oggetto del messaggio...",class:"input-field",maxlength:"200"},null,512),[[ee,g.value]])])):w("",!0),e("div",null,[x[4]||(x[4]=e("label",{for:"content",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Messaggio ",-1)),e("div",Ue,[F(e("textarea",{id:"content","onUpdate:modelValue":x[1]||(x[1]=E=>f.value=E),ref_key:"contentTextarea",ref:y,placeholder:"Scrivi il tuo messaggio...",rows:"3",class:"input-field resize-none",maxlength:"2000",onKeydown:U,required:""},null,544),[[ee,f.value]]),e("div",Ke,c(f.value.length)+"/2000 ",1)])]),e("div",Oe,[e("div",Te,[e("button",{type:"button",onClick:x[2]||(x[2]=E=>r.value=!r.value),class:"text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"},[b(_,{name:"tag",size:"sm",class:"mr-1"}),D(" "+c(r.value?"Nascondi oggetto":"Aggiungi oggetto"),1)])]),e("div",Ve,[f.value||g.value?(o(),d("button",{key:0,type:"button",onClick:I,class:"btn-secondary text-xs px-3 py-1.5"}," Cancella ")):w("",!0),e("button",{type:"submit",disabled:!f.value.trim()||m.value,class:"btn-primary text-xs px-3 py-1.5 disabled:opacity-50 disabled:cursor-not-allowed"},[m.value?(o(),K(ae,{key:0,size:"sm",class:"mr-2"})):(o(),K(_,{key:1,name:"paper-airplane",size:"sm",class:"mr-2"})),x[5]||(x[5]=D(" Invia "))],8,Pe)])])],32)]))}},re=G(Le,[["__scopeId","data-v-1325e40a"]]),Be={class:"fixed inset-0 z-50 overflow-y-auto","aria-labelledby":"modal-title",role:"dialog","aria-modal":"true"},Fe={class:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},qe={class:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-visible shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"},Ge={class:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4"},We={class:"flex items-center justify-between mb-4"},He={key:0,class:"space-y-4"},Je={class:"flex items-center space-x-3"},Xe={class:"h-8 w-8 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center"},Qe={class:"text-xs font-medium text-gray-700 dark:text-gray-300"},Ye={class:"flex-1 min-w-0"},Ze={class:"text-sm font-medium text-gray-900 dark:text-white truncate"},et={class:"text-xs text-gray-500 dark:text-gray-400 truncate"},tt={key:0,class:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-md p-3"},st={class:"flex items-center space-x-3"},at={class:"h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-800 flex items-center justify-center"},rt={class:"text-xs font-medium text-blue-700 dark:text-blue-300"},ot={class:"flex-1"},nt={class:"text-sm font-medium text-blue-900 dark:text-blue-100"},lt={class:"text-xs text-blue-700 dark:text-blue-300"},it={key:1,class:"space-y-4"},dt={class:"bg-gray-50 dark:bg-gray-700 rounded-md p-3"},ct={class:"flex items-center space-x-3"},ut={class:"h-8 w-8 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center"},gt={class:"text-xs font-medium text-gray-700 dark:text-gray-300"},mt={class:"text-sm font-medium text-gray-900 dark:text-white"},ft={class:"text-xs text-gray-500 dark:text-gray-400"},vt={class:"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse"},yt=["disabled"],xt={__name:"NewMessageModal",emits:["close","message-sent"],setup(n,{emit:k}){const v=k,h=Y(),$=W(),g=C(1),f=C(""),r=C(null),m=C(!1),y=C([]),i=R(()=>{var a;return(a=$.user)==null?void 0:a.id}),I=R(()=>y.value.map(a=>({...a,roleAndDepartment:`${a.role} - ${a.department}`}))),U=xe(a=>{a&&a.length>=2&&E(a)},300),u=a=>a.split(" ").map(p=>p.charAt(0)).join("").toUpperCase().slice(0,2),x=async()=>{m.value=!0;try{const a=await h.fetchUsers();y.value=a.filter(p=>p.id!==i.value)}catch(a){console.error("Errore nel caricamento degli utenti:",a)}finally{m.value=!1}},E=async(a=f.value)=>{if(!(!a||a.length<2)){m.value=!0;try{const p=await h.searchUsers({query:a,exclude_self:!0,current_user_id:i.value});y.value=p}catch(p){console.error("Errore nella ricerca degli utenti:",p)}finally{m.value=!1}}},O=a=>{U(a)},j=a=>{r.value=a,f.value=a.name},T=()=>{r.value&&(g.value=2)},V=()=>{g.value=1},l=a=>{v("message-sent",a.conversation_id)},M=a=>{console.error("Errore nell'invio del messaggio:",a)};return Q(()=>{x()}),(a,p)=>(o(),d("div",Be,[e("div",Fe,[e("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity","aria-hidden":"true",onClick:p[0]||(p[0]=S=>a.$emit("close"))}),e("div",qe,[e("div",Ge,[e("div",We,[p[5]||(p[5]=e("h3",{class:"text-lg leading-6 font-medium text-gray-900 dark:text-white",id:"modal-title"}," Nuovo Messaggio ",-1)),e("button",{onClick:p[1]||(p[1]=S=>a.$emit("close")),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},[b(_,{name:"x-mark",size:"md"})])]),g.value===1?(o(),d("div",He,[e("div",null,[p[6]||(p[6]=e("label",{for:"recipient-search",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Destinatario ",-1)),b(Re,{modelValue:f.value,"onUpdate:modelValue":p[2]||(p[2]=S=>f.value=S),options:I.value,"label-key":"name","value-key":"id","description-key":"roleAndDepartment",placeholder:"Cerca un collega...",icon:"magnifying-glass",loading:m.value,"loading-text":"Caricamento utenti...","no-results-text":"Nessun utente trovato","min-search-length":2,"filter-on-client":!1,onSearch:O,onSelect:j},{option:P(({option:S})=>[e("div",Je,[e("div",Xe,[e("span",Qe,c(u(S.name)),1)]),e("div",Ye,[e("p",Ze,c(S.name),1),e("p",et,c(S.role)+" - "+c(S.department),1)])])]),_:1},8,["modelValue","options","loading"])]),r.value?(o(),d("div",tt,[e("div",st,[e("div",at,[e("span",rt,c(u(r.value.name)),1)]),e("div",ot,[e("p",nt,c(r.value.name),1),e("p",lt,c(r.value.role)+" - "+c(r.value.department),1)]),e("button",{onClick:p[3]||(p[3]=S=>r.value=null),class:"text-blue-400 hover:text-blue-600 dark:text-blue-300 dark:hover:text-blue-100"},[b(_,{name:"x-mark",size:"sm"})])])])):w("",!0)])):w("",!0),g.value===2?(o(),d("div",it,[e("div",dt,[e("div",ct,[e("div",ut,[e("span",gt,c(u(r.value.name)),1)]),e("div",null,[e("p",mt," A: "+c(r.value.name),1),e("p",ft,c(r.value.role)+" - "+c(r.value.department),1)])])]),b(re,{"recipient-id":r.value.id,"recipient-name":r.value.name,onMessageSent:l,onError:M},null,8,["recipient-id","recipient-name"])])):w("",!0)]),e("div",vt,[g.value===1?(o(),d("button",{key:0,onClick:T,disabled:!r.value,class:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"}," Continua ",8,yt)):w("",!0),g.value===2?(o(),d("button",{key:1,onClick:V,class:"w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm dark:bg-gray-600 dark:text-gray-300 dark:border-gray-500 dark:hover:bg-gray-500"}," Indietro ")):w("",!0),e("button",{onClick:p[4]||(p[4]=S=>a.$emit("close")),class:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm dark:bg-gray-600 dark:text-gray-300 dark:border-gray-500 dark:hover:bg-gray-500"}," Chiudi ")])])])]))}},bt={class:"fixed inset-0 z-50 overflow-y-auto","aria-labelledby":"modal-title",role:"dialog","aria-modal":"true"},pt={class:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},ht={class:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full"},kt={class:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 border-b border-gray-200 dark:border-gray-700"},_t={class:"flex items-center justify-between"},wt={class:"flex items-center space-x-3"},$t={class:"h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center"},Mt={class:"text-lg leading-6 font-medium text-gray-900 dark:text-white",id:"modal-title"},Ct={class:"text-sm text-gray-500 dark:text-gray-400"},St={class:"bg-gray-50 dark:bg-gray-700 px-4 py-3 border-b border-gray-200 dark:border-gray-600"},jt={class:"bg-white dark:bg-gray-800 rounded p-3 text-sm"},It={class:"flex items-center justify-between mb-2"},zt={class:"font-medium text-gray-900 dark:text-white"},Nt={class:"text-xs text-gray-500 dark:text-gray-400"},Et={key:0,class:"font-medium text-gray-800 dark:text-gray-200 mb-2"},Rt={class:"text-gray-600 dark:text-gray-400 line-clamp-3"},Dt={class:"bg-white dark:bg-gray-800 px-4 py-5 sm:p-6"},At={class:"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse"},Ut={__name:"ReplyMessageModal",props:{originalMessage:{type:Object,required:!0}},emits:["close","sent"],setup(n,{emit:k}){const v=n,h=k,$=W(),{formatDate:g}=Z(),f=R(()=>{var u;return(u=$.user)==null?void 0:u.id}),r=()=>v.originalMessage.sender_id===f.value,m=()=>{var u,x;return r()?v.originalMessage.recipient_name||((u=v.originalMessage.recipient)==null?void 0:u.full_name)||"Destinatario sconosciuto":v.originalMessage.sender_name||((x=v.originalMessage.sender)==null?void 0:x.full_name)||"Mittente sconosciuto"},y=()=>r()?v.originalMessage.recipient_id:v.originalMessage.sender_id,i=()=>{const u=v.originalMessage.subject||"";return u.toLowerCase().startsWith("re:")?u:u?`Re: ${u}`:"Re: "},I=u=>{h("sent",u)},U=u=>{console.error("Errore nell'invio della risposta:",u)};return(u,x)=>(o(),d("div",bt,[e("div",pt,[e("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity","aria-hidden":"true",onClick:x[0]||(x[0]=E=>u.$emit("close"))}),e("div",ht,[e("div",kt,[e("div",_t,[e("div",wt,[e("div",$t,[b(_,{name:"arrow-uturn-left",size:"md",class:"text-gray-500"})]),e("div",null,[e("h3",Mt," Rispondi a "+c(m()),1),e("p",Ct," Re: "+c(n.originalMessage.subject||"Nessun oggetto"),1)])]),e("button",{onClick:x[1]||(x[1]=E=>u.$emit("close")),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},[b(_,{name:"x-mark",size:"md"})])])]),e("div",St,[x[3]||(x[3]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400 mb-2"}," Messaggio originale: ",-1)),e("div",jt,[e("div",It,[e("span",zt,c(m()),1),e("span",Nt,c(A(g)(n.originalMessage.sent_at)),1)]),n.originalMessage.subject?(o(),d("div",Et,c(n.originalMessage.subject),1)):w("",!0),e("div",Rt,c(n.originalMessage.body||n.originalMessage.content),1)])]),e("div",Dt,[b(re,{"recipient-id":y(),"recipient-name":m(),"conversation-id":n.originalMessage.conversation_id,"initial-subject":i(),placeholder:"Scrivi la tua risposta...",onMessageSent:I,onError:U},null,8,["recipient-id","recipient-name","conversation-id","initial-subject"])]),e("div",At,[e("button",{onClick:x[2]||(x[2]=E=>u.$emit("close")),class:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm dark:bg-gray-600 dark:text-gray-300 dark:border-gray-500 dark:hover:bg-gray-500"}," Annulla ")])])])]))}},Kt=G(Ut,[["__scopeId","data-v-63223329"]]),Ot={class:"fixed inset-0 z-50 overflow-y-auto","aria-labelledby":"modal-title",role:"dialog","aria-modal":"true"},Tt={class:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},Vt={class:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full"},Pt={class:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 border-b border-gray-200 dark:border-gray-700"},Lt={class:"flex items-center justify-between"},Bt={class:"flex items-center space-x-3"},Ft={class:"h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center"},qt={class:"text-lg leading-6 font-medium text-gray-900 dark:text-white",id:"modal-title"},Gt={class:"text-sm text-gray-500 dark:text-gray-400"},Wt={class:"bg-white dark:bg-gray-800 px-4 py-5 sm:p-6"},Ht={key:0,class:"mb-4"},Jt={class:"text-lg font-medium text-gray-900 dark:text-white"},Xt={class:"prose prose-sm max-w-none dark:prose-invert"},Qt={class:"text-gray-700 dark:text-gray-300 whitespace-pre-wrap"},Yt={class:"mt-6 pt-4 border-t border-gray-200 dark:border-gray-700"},Zt={class:"flex items-center justify-between text-sm text-gray-500 dark:text-gray-400"},es={class:"flex items-center space-x-4"},ts={class:"flex items-center"},ss={key:0,class:"flex items-center"},as={key:1,class:"flex items-center"},rs={key:2,class:"flex items-center"},os={class:"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse"},ns={__name:"ViewMessageModal",props:{message:{type:Object,required:!0}},emits:["close","reply","delete"],setup(n,{emit:k}){const v=n,h=W(),{formatDate:$}=Z(),g=R(()=>{var y;return(y=h.user)==null?void 0:y.id}),f=R(()=>h.hasPermission("PERMISSION_MANAGE_COMMUNICATION")),r=()=>v.message.sender_id===g.value,m=()=>{var y,i;return r()?v.message.recipient_name||((y=v.message.recipient)==null?void 0:y.full_name)||"Destinatario sconosciuto":v.message.sender_name||((i=v.message.sender)==null?void 0:i.full_name)||"Mittente sconosciuto"};return(y,i)=>(o(),d("div",Ot,[e("div",Tt,[e("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity","aria-hidden":"true",onClick:i[0]||(i[0]=I=>y.$emit("close"))}),e("div",Vt,[e("div",Pt,[e("div",Lt,[e("div",Bt,[e("div",Ft,[b(_,{name:"user",size:"md",class:"text-gray-500"})]),e("div",null,[e("h3",qt,c(m()),1),e("p",Gt,c(A($)(n.message.sent_at)),1)])]),e("button",{onClick:i[1]||(i[1]=I=>y.$emit("close")),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},[b(_,{name:"x-mark",size:"md"})])])]),e("div",Wt,[n.message.subject?(o(),d("div",Ht,[e("h4",Jt,c(n.message.subject),1)])):w("",!0),e("div",Xt,[e("div",Qt,c(n.message.body||n.message.content),1)]),e("div",Yt,[e("div",Zt,[e("div",es,[e("span",ts,[b(_,{name:"clock",size:"xs",class:"mr-1"}),D(" "+c(A($)(n.message.sent_at)),1)]),r()?(o(),d("span",ss,[b(_,{name:"paper-airplane",size:"xs",class:"mr-1"}),i[5]||(i[5]=D(" Inviato da te "))])):n.message.is_read?(o(),d("span",as,[b(_,{name:"check",size:"xs",class:"mr-1"}),i[6]||(i[6]=D(" Letto "))])):(o(),d("span",rs,[b(_,{name:"envelope",size:"xs",class:"mr-1"}),i[7]||(i[7]=D(" Non letto "))]))])])])]),e("div",os,[r()?w("",!0):(o(),d("button",{key:0,onClick:i[2]||(i[2]=I=>y.$emit("reply",n.message)),class:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"},[b(_,{name:"arrow-uturn-left",size:"sm",class:"mr-2"}),i[8]||(i[8]=D(" Rispondi "))])),f.value?(o(),d("button",{key:1,onClick:i[3]||(i[3]=I=>y.$emit("delete",n.message)),class:"mt-3 w-full inline-flex justify-center rounded-md border border-red-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-red-700 hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm dark:bg-gray-600 dark:text-red-400 dark:border-red-500 dark:hover:bg-gray-500"},[b(_,{name:"trash",size:"sm",class:"mr-2"}),i[9]||(i[9]=D(" Elimina "))])):w("",!0),e("button",{onClick:i[4]||(i[4]=I=>y.$emit("close")),class:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm dark:bg-gray-600 dark:text-gray-300 dark:border-gray-500 dark:hover:bg-gray-500"}," Chiudi ")])])])]))}},ls=G(ns,[["__scopeId","data-v-c779d278"]]),is={class:"grid grid-cols-1 gap-5 sm:grid-cols-4"},ds={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},cs={class:"p-5"},us={class:"flex items-center"},gs={class:"flex-shrink-0"},ms={class:"ml-5 w-0 flex-1"},fs={class:"text-lg font-medium text-gray-900 dark:text-white"},vs={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},ys={class:"p-5"},xs={class:"flex items-center"},bs={class:"flex-shrink-0"},ps={class:"ml-5 w-0 flex-1"},hs={class:"text-lg font-medium text-gray-900 dark:text-white"},ks={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},_s={class:"p-5"},ws={class:"flex items-center"},$s={class:"flex-shrink-0"},Ms={class:"ml-5 w-0 flex-1"},Cs={class:"text-lg font-medium text-gray-900 dark:text-white"},Ss={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},js={class:"p-5"},Is={class:"flex items-center"},zs={class:"flex-shrink-0"},Ns={class:"ml-5 w-0 flex-1"},Es={class:"text-lg font-medium text-gray-900 dark:text-white"},Rs={class:"flex space-x-4"},Ds={key:0,class:"flex justify-center items-center h-64"},As={key:1,class:"text-center py-12"},Us={key:2,class:"p-6"},Ks={class:"space-y-4"},Os=["onClick"],Ts={class:"flex items-start justify-between"},Vs={class:"flex items-start space-x-4 flex-1 min-w-0"},Ps={class:"flex-shrink-0"},Ls={class:"w-10 h-10 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center"},Bs={class:"flex-1 min-w-0"},Fs={class:"flex items-center justify-between mb-2"},qs={class:"flex items-center space-x-2"},Gs={class:"text-sm font-semibold text-gray-900 dark:text-white"},Ws={key:0,class:"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800"},Hs={key:1,class:"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800"},Js={class:"text-xs text-gray-500 dark:text-gray-400"},Xs={class:"text-base font-medium text-gray-900 dark:text-white mb-2"},Qs={class:"text-gray-600 dark:text-gray-400 text-sm line-clamp-2"},Ys={class:"ml-4 flex space-x-2"},Zs=["onClick"],ea=["onClick"],ta=["onClick"],sa={key:0,class:"mt-8 flex justify-center"},aa={__name:"MessagesIndex",setup(n){const k=Y(),v=W(),{formatDate:h}=Z(),$=C(!1),g=C(!1),f=C(!1),r=C(!1),m=C(null),y=C(""),i=R(()=>v.hasPermission("PERMISSION_VIEW_COMMUNICATION")),I=R(()=>v.hasPermission("PERMISSION_MANAGE_COMMUNICATION")),U=R(()=>{var s;return(s=v.user)==null?void 0:s.id}),u=R(()=>k.messages.filter(s=>!s.is_read&&!j(s)).length),x=R(()=>k.messages.filter(s=>j(s)).length),E=R(()=>k.messages.filter(s=>!j(s)).length),O=R(()=>{let s=k.messages;switch(y.value){case"unread":s=s.filter(t=>!t.is_read&&!j(t));break;case"sent":s=s.filter(t=>j(t));break;case"received":s=s.filter(t=>!j(t));break}return s.sort((t,N)=>new Date(N.sent_at)-new Date(t.sent_at))}),j=s=>s.sender_id===U.value,T=s=>{var t,N;return j(s)?s.recipient_name||((t=s.recipient)==null?void 0:t.full_name)||"Destinatario sconosciuto":s.sender_name||((N=s.sender)==null?void 0:N.full_name)||"Mittente sconosciuto"},V=s=>{m.value=s,f.value=!0,!s.is_read&&!j(s)&&l(s)},l=async s=>{try{console.log("Message object:",s),console.log("Message ID:",s.id),await k.markMessageAsRead(s.id)}catch(t){console.error("Errore nel segnare il messaggio come letto:",t)}},M=s=>{m.value=s,g.value=!0},a=s=>{m.value=s,r.value=!0},p=s=>{$.value=!1},S=s=>{g.value=!1,m.value=null},L=s=>{f.value=!1,m.value=s,g.value=!0},oe=s=>{f.value=!1,m.value=s,r.value=!0},ne=async()=>{try{await k.deleteMessage(m.value.id),r.value=!1,m.value=null}catch(s){console.error("Errore nell'eliminazione del messaggio:",s)}},le=async s=>{try{await k.fetchMessages({page:s,per_page:20,type:y.value||void 0})}catch(t){console.error("Errore nel caricamento della pagina:",t)}};return te(y,async(s,t)=>{if(t!==void 0)try{console.log("🔄 Filter changed from",t,"to",s),await k.fetchMessages({page:1,per_page:20,type:s||void 0})}catch(N){console.error("Errore nel caricamento dei messaggi con filtro:",N)}}),Q(async()=>{try{console.log("🚀 MessagesIndex mounted - loading with per_page: 20"),await k.fetchMessages({page:1,per_page:20,type:"all"})}catch(s){console.error("Errore nel caricamento dei messaggi:",s)}}),(s,t)=>(o(),d(X,null,[b(fe,{title:"Messaggi Privati",subtitle:"Comunicazioni dirette tra colleghi",data:A(k).messages,loading:A(k).loading.messages,"can-create":i.value,"show-pagination":!1,"create-label":"Nuovo Messaggio","search-placeholder":"Cerca messaggi...","empty-message":"Nessun messaggio","results-label":"messaggi",onCreate:t[1]||(t[1]=N=>$.value=!0)},{stats:P(()=>[e("div",is,[e("div",ds,[e("div",cs,[e("div",us,[e("div",gs,[b(_,{name:"envelope",size:"lg",class:"text-blue-500"})]),e("div",ms,[e("dl",null,[t[6]||(t[6]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"},"Totali",-1)),e("dd",fs,c(A(k).messages.length),1)])])])])]),e("div",vs,[e("div",ys,[e("div",xs,[e("div",bs,[b(_,{name:"envelope-open",size:"lg",class:"text-green-500"})]),e("div",ps,[e("dl",null,[t[7]||(t[7]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"},"Non Letti",-1)),e("dd",hs,c(u.value),1)])])])])]),e("div",ks,[e("div",_s,[e("div",ws,[e("div",$s,[b(_,{name:"paper-airplane",size:"lg",class:"text-purple-500"})]),e("div",Ms,[e("dl",null,[t[8]||(t[8]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"},"Inviati",-1)),e("dd",Cs,c(x.value),1)])])])])]),e("div",Ss,[e("div",js,[e("div",Is,[e("div",zs,[b(_,{name:"inbox-arrow-down",size:"lg",class:"text-yellow-500"})]),e("div",Ns,[e("dl",null,[t[9]||(t[9]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"},"Ricevuti",-1)),e("dd",Es,c(E.value),1)])])])])])])]),filters:P(()=>[e("div",Rs,[F(e("select",{"onUpdate:modelValue":t[0]||(t[0]=N=>y.value=N),class:"block w-40 pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},t[10]||(t[10]=[e("option",{value:""},"Tutti i messaggi",-1),e("option",{value:"unread"},"Non letti",-1),e("option",{value:"sent"},"Inviati",-1),e("option",{value:"received"},"Ricevuti",-1)]),512),[[me,y.value]])])]),content:P(({data:N,loading:ie})=>[ie?(o(),d("div",Ds,t[11]||(t[11]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):O.value.length===0?(o(),d("div",As,[b(_,{name:"envelope",size:"2xl",class:"mx-auto text-gray-400 mb-4"}),t[12]||(t[12]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Nessun messaggio trovato",-1)),t[13]||(t[13]=e("p",{class:"text-gray-500 dark:text-gray-400"},"Non ci sono messaggi da visualizzare con i filtri selezionati.",-1))])):(o(),d("div",Us,[e("div",Ks,[(o(!0),d(X,null,se(O.value,z=>(o(),d("div",{key:z.id,class:J(["bg-white dark:bg-gray-800 shadow rounded-lg p-6 hover:shadow-lg transition-shadow cursor-pointer",{"border-l-4 border-blue-500":!z.is_read&&!j(z)}]),onClick:H=>V(z)},[e("div",Ts,[e("div",Vs,[e("div",Ps,[e("div",Ls,[b(_,{name:"user",size:"md",class:"text-gray-500"})])]),e("div",Bs,[e("div",Fs,[e("div",qs,[e("h3",Gs,c(T(z)),1),j(z)?(o(),d("span",Ws,[b(_,{name:"paper-airplane",size:"xs",class:"mr-1"}),t[14]||(t[14]=D(" Inviato "))])):z.is_read?w("",!0):(o(),d("span",Hs,[b(_,{name:"envelope",size:"xs",class:"mr-1"}),t[15]||(t[15]=D(" Nuovo "))]))]),e("span",Js,c(A(h)(z.sent_at)),1)]),e("h4",Xs,c(z.subject||"Nessun oggetto"),1),e("p",Qs,c(z.body),1)])]),e("div",Ys,[!z.is_read&&!j(z)?(o(),d("button",{key:0,onClick:B(H=>l(z),["stop"]),class:"inline-flex items-center px-3 py-1 border border-green-300 shadow-sm text-xs font-medium rounded text-green-700 bg-white hover:bg-green-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"},[b(_,{name:"check",size:"xs",class:"mr-1"}),t[16]||(t[16]=D(" Segna come letto "))],8,Zs)):w("",!0),e("button",{onClick:B(H=>M(z),["stop"]),class:"inline-flex items-center px-3 py-1 border border-blue-300 shadow-sm text-xs font-medium rounded text-blue-700 bg-white hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},[b(_,{name:"arrow-uturn-left",size:"xs",class:"mr-1"}),t[17]||(t[17]=D(" Rispondi "))],8,ea),I.value?(o(),d("button",{key:1,onClick:B(H=>a(z),["stop"]),class:"inline-flex items-center px-3 py-1 border border-red-300 shadow-sm text-xs font-medium rounded text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"},[b(_,{name:"trash",size:"xs",class:"mr-1"}),t[18]||(t[18]=D(" Elimina "))],8,ta)):w("",!0)])])],10,Os))),128))]),A(k).pagination.messages.totalPages>1?(o(),d("div",sa,[b(ve,{"current-page":A(k).pagination.messages.page,"total-pages":A(k).pagination.messages.totalPages,total:A(k).pagination.messages.total,"per-page":A(k).pagination.messages.per_page||20,"results-label":"messaggi",onPageChange:le},null,8,["current-page","total-pages","total","per-page"])])):w("",!0)]))]),_:1},8,["data","loading","can-create"]),$.value?(o(),K(xt,{key:0,onClose:t[2]||(t[2]=N=>$.value=!1),onSent:p})):w("",!0),g.value?(o(),K(Kt,{key:1,"original-message":m.value,onClose:t[3]||(t[3]=N=>g.value=!1),onSent:S},null,8,["original-message"])):w("",!0),f.value?(o(),K(ls,{key:2,message:m.value,onClose:t[4]||(t[4]=N=>f.value=!1),onReply:L,onDelete:oe},null,8,["message"])):w("",!0),r.value?(o(),K(ye,{key:3,title:"Elimina Messaggio",message:"Sei sicuro di voler eliminare questo messaggio?","confirm-text":"Elimina","confirm-class":"bg-red-600 hover:bg-red-700",onConfirm:ne,onCancel:t[5]||(t[5]=N=>r.value=!1)})):w("",!0)],64))}},ua=G(aa,[["__scopeId","data-v-b557fe64"]]);export{ua as default};
