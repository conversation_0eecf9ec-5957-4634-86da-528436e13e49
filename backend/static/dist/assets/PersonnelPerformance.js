import{d as ve,r as b,c as h,o as ge,f as ye,p as P,x as w,j as d,l as e,e as L,b as u,g as R,t as c,F,q as G,n as T,E as ie,v as K,B as ce,S as de}from"./vendor.js";import{c as z,_ as fe,b as xe,H as N}from"./app.js";import{u as _e}from"./personnel.js";import{D as he}from"./DashboardTemplate.js";import{T as be}from"./TabNavigation.js";import{_ as we}from"./DataTable.js";/* empty css                                                             */import"./formatters.js";const ke=ve("performance",()=>{const k=b([]),p=b([]),A=b([]),$=b({}),_=b(null),v=b(!1),g=b(null),C=b(new Map),y=b({page:1,per_page:20,total:0,total_pages:0}),m=b({search:"",status:"",employee_id:"",year:new Date().getFullYear(),review_type:""}),I=h(()=>{let s=k.value;if(m.value.search){const o=m.value.search.toLowerCase();s=s.filter(i=>{var l,n,x,S,E;return((n=(l=i.employee)==null?void 0:l.first_name)==null?void 0:n.toLowerCase().includes(o))||((S=(x=i.employee)==null?void 0:x.last_name)==null?void 0:S.toLowerCase().includes(o))||((E=i.review_type)==null?void 0:E.toLowerCase().includes(o))})}return m.value.status&&(s=s.filter(o=>o.status===m.value.status)),m.value.employee_id&&(s=s.filter(o=>o.employee_id===parseInt(m.value.employee_id))),m.value.year&&(s=s.filter(o=>o.review_year===parseInt(m.value.year))),m.value.review_type&&(s=s.filter(o=>o.review_type===m.value.review_type)),s}),B=h(()=>{const s={};return k.value.forEach(o=>{s[o.status]||(s[o.status]=[]),s[o.status].push(o)}),s}),M=(s,o=0)=>{const i=(s==null?void 0:s.per_page)||o||10;return{page:(s==null?void 0:s.page)||1,per_page:i,total:(s==null?void 0:s.total)||o||0,total_pages:(s==null?void 0:s.total_pages)||Math.ceil(((s==null?void 0:s.total)||o||0)/i)||1}},O=async(s={})=>{var o,i;v.value=!0,g.value=null;try{const l=new URLSearchParams({employee_id:s.employee_id||m.value.employee_id||"",year:s.year||m.value.year||new Date().getFullYear()}),n=await z.get(`/api/performance/dashboard?${l}`);n.data.success&&($.value=n.data.data)}catch(l){g.value=((i=(o=l.response)==null?void 0:o.data)==null?void 0:i.message)||"Errore nel caricamento dashboard performance",console.error("Error fetching performance dashboard:",l)}finally{v.value=!1}},U=async(s={})=>{var o,i;v.value=!0,g.value=null;try{const l=new URLSearchParams({page:s.page||y.value.page,per_page:s.per_page||y.value.per_page,search:s.search||m.value.search,status:s.status||m.value.status,employee_id:s.employee_id||m.value.employee_id||"",year:s.year||m.value.year||"",review_type:s.review_type||m.value.review_type}),n=await z.get(`/api/performance/reviews?${l}`);n.data.success&&(n.data.data.reviews?(k.value=n.data.data.reviews,y.value=M(n.data.data.pagination,n.data.data.reviews.length)):Array.isArray(n.data.data)?(k.value=n.data.data,y.value=M(null,n.data.data.length)):(k.value=[],y.value=M(null,0)))}catch(l){g.value=((i=(o=l.response)==null?void 0:o.data)==null?void 0:i.message)||"Errore nel caricamento recensioni performance",console.error("Error fetching performance reviews:",l)}finally{v.value=!1}},V=async(s,o=!1)=>{var i,l;if(!o&&C.value.has(s)){const n=C.value.get(s);return _.value=n,n}v.value=!0,g.value=null;try{const n=await z.get(`/api/performance/reviews/${s}`);if(n.data.success){const x=n.data.data.review;return _.value=x,C.value.set(s,x),x}}catch(n){throw g.value=((l=(i=n.response)==null?void 0:i.data)==null?void 0:l.message)||"Errore nel caricamento review",console.error("Error fetching performance review:",n),n}finally{v.value=!1}};return{reviews:k,goals:p,templates:A,dashboardData:$,currentReview:_,loading:v,error:g,pagination:y,filters:m,filteredReviews:I,reviewsByStatus:B,fetchDashboardData:O,fetchReviews:U,fetchReview:V,fetchGoals:async(s={})=>{var o,i;v.value=!0,g.value=null;try{const l=new URLSearchParams({employee_id:s.employee_id||m.value.employee_id||"",year:s.year||m.value.year||""}),n=await z.get(`/api/performance/goals?${l}`);n.data.success&&(p.value=n.data.data.goals||[])}catch(l){g.value=((i=(o=l.response)==null?void 0:o.data)==null?void 0:i.message)||"Errore nel caricamento obiettivi",console.error("Error fetching performance goals:",l)}finally{v.value=!1}},fetchTemplates:async()=>{var s,o;v.value=!0,g.value=null;try{const i=await z.get("/api/performance/templates");i.data.success&&(A.value=i.data.data.templates||[])}catch(i){g.value=((o=(s=i.response)==null?void 0:s.data)==null?void 0:o.message)||"Errore nel caricamento template",console.error("Error fetching performance templates:",i)}finally{v.value=!1}},createReview:async s=>{var o,i;v.value=!0,g.value=null;try{const l=await z.post("/api/performance/reviews",s);if(l.data.success){const n=l.data.data.review;return k.value.unshift(n),n}}catch(l){throw g.value=((i=(o=l.response)==null?void 0:o.data)==null?void 0:i.message)||"Errore nella creazione review",console.error("Error creating performance review:",l),l}finally{v.value=!1}},updateReview:async(s,o)=>{var i,l,n;v.value=!0,g.value=null;try{const x=await z.put(`/api/performance/reviews/${s}`,o);if(x.data.success){const S=x.data.data.review,E=k.value.findIndex(q=>q.id===s);return E!==-1&&(k.value[E]=S),((i=_.value)==null?void 0:i.id)===s&&(_.value=S),C.value.set(s,S),S}}catch(x){throw g.value=((n=(l=x.response)==null?void 0:l.data)==null?void 0:n.message)||"Errore nell'aggiornamento review",console.error("Error updating performance review:",x),x}finally{v.value=!1}},createGoal:async s=>{var o,i;v.value=!0,g.value=null;try{const l=await z.post("/api/performance/goals",s);if(l.data.success){const n=l.data.data.goal;return p.value.unshift(n),n}}catch(l){throw g.value=((i=(o=l.response)==null?void 0:o.data)==null?void 0:i.message)||"Errore nella creazione obiettivo",console.error("Error creating performance goal:",l),l}finally{v.value=!1}},setFilters:s=>{m.value={...m.value,...s}},clearFilters:()=>{m.value={search:"",status:"",employee_id:"",year:new Date().getFullYear(),review_type:""}},setCurrentReview:s=>{_.value=s},clearCurrentReview:()=>{_.value=null},clearCache:()=>{C.value.clear()},refreshReview:async s=>await V(s,!0),getCachedReview:s=>C.value.get(s),$reset:()=>{k.value=[],p.value=[],A.value=[],$.value={},_.value=null,v.value=!1,g.value=null,C.value.clear(),y.value={page:1,per_page:20,total:0,total_pages:0},m.value={search:"",status:"",employee_id:"",year:new Date().getFullYear(),review_type:""}}}}),Ce={key:0,class:"flex items-center gap-2"},Re=["value"],Se={class:"flex items-center gap-2"},Pe={class:"h-64 flex items-center justify-center"},Te={class:"text-center"},ze={class:"w-32 h-32 mx-auto mb-4 relative"},Ee={class:"w-32 h-32 transform -rotate-90",viewBox:"0 0 36 36"},Le=["stroke-dasharray"],Ae={class:"absolute inset-0 flex items-center justify-center"},$e={class:"text-center"},Me={class:"text-xl font-bold text-gray-900 dark:text-white"},je={class:"space-y-2"},Ne={class:"flex items-center justify-between text-sm"},Ve={class:"font-medium"},Fe={class:"flex items-center justify-between text-sm"},Ge={class:"font-medium"},Ie={class:"flex items-center justify-between text-sm"},Be={class:"font-medium"},Oe={class:"flex justify-between items-start"},Ue={class:"flex-1"},De={class:"text-sm font-medium text-gray-900 dark:text-white"},Ye={class:"text-xs text-gray-500 dark:text-gray-400"},qe={class:"flex items-center mt-1 space-x-2"},He={class:"text-right"},Ke={key:0,class:"text-sm font-medium text-gray-900 dark:text-white"},Qe={key:1,class:"text-xs text-gray-500 dark:text-gray-400"},We={class:"flex-1 min-w-0"},Ze={class:"text-sm font-medium text-gray-900 dark:text-white"},Je={class:"text-xs text-gray-500 dark:text-gray-400"},Xe={class:"flex items-center justify-between mt-1"},et={class:"text-xs text-gray-400 dark:text-gray-500"},tt={key:0,class:"text-xs text-gray-500 dark:text-gray-400"},at={class:"flex justify-between items-start"},st={class:"flex-1"},rt={class:"text-sm font-medium text-gray-900 dark:text-white flex items-center"},ot={class:"text-xs text-gray-500 dark:text-gray-400"},lt={class:"text-right"},nt={class:"text-sm font-bold text-gray-900 dark:text-white"},it={class:"text-xs text-gray-500"},ct={class:"mt-2"},dt={class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"},ut={class:"text-xs text-gray-500 dark:text-gray-400 mt-1"},mt={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},pt={class:"p-6"},vt={key:0,class:"reviews-tab"},gt={key:0,class:"text-center py-8"},yt={key:1,class:"text-center py-8"},ft={class:"text-sm text-gray-600 dark:text-gray-400"},xt={key:2},_t={key:0,class:"flex items-center"},ht={class:"text-sm font-medium"},bt={class:"ml-2 flex"},wt={key:1,class:"text-gray-400"},kt={key:1,class:"goals-tab"},Ct={key:0,class:"text-center py-8"},Rt={key:1},St={key:0,class:"text-center py-8 text-gray-500"},Pt={key:1,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},Tt=["onClick"],zt={class:"flex justify-between items-start mb-3"},Et={class:"text-sm font-medium text-gray-900 dark:text-white"},Lt={class:"text-xs text-gray-600 dark:text-gray-400 mb-3"},At={class:"mb-2"},$t={class:"flex justify-between text-xs mb-1"},Mt={class:"font-medium text-gray-900 dark:text-white"},jt={class:"w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2"},Nt={class:"flex justify-between text-xs"},Vt={class:"font-medium text-gray-700 dark:text-gray-300"},Ft={key:2,class:"analytics-tab"},Gt={class:"text-center py-12"},It={key:3,class:"templates-tab"},Bt={key:0,class:"text-center py-8"},Ot={key:1},Ut={key:0,class:"text-center py-8 text-gray-500"},Dt={key:1,class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Yt=["onClick"],qt={class:"flex justify-between items-start mb-3"},Ht={class:"text-sm font-medium text-gray-900 dark:text-white"},Kt={key:0,class:"inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"},Qt={class:"text-xs text-gray-600 dark:text-gray-400 mb-3"},Wt={class:"flex justify-between text-xs"},Zt={class:"font-medium text-gray-700 dark:text-gray-300"},Jt={__name:"PersonnelPerformance",setup(k){const p=ke(),A=_e(),$=xe(),_=b("reviews"),v=b(""),g=b(2025),C=b("30"),y=h(()=>p.dashboardData),m=h(()=>{var t;const r=(t=$.user)==null?void 0:t.role;return r==="admin"||r==="hr"}),I=h(()=>m.value),B=h(()=>[{value:"7",label:"Ultimi 7 giorni"},{value:"30",label:"Ultimo mese"},{value:"90",label:"Ultimi 3 mesi"},{value:"365",label:"Ultimo anno"}]),M=h(()=>{var r,t,a,f,j;return[{id:"reviews",title:"Valutazioni Totali",value:((r=y.value.reviews)==null?void 0:r.total)||0,subtitle:`${((t=y.value.reviews)==null?void 0:t.pending)||0} in attesa`,icon:"clipboard-document-check",color:"blue",link:"/app/personnel/performance?tab=reviews"},{id:"goals",title:"Obiettivi Attivi",value:((a=y.value.goals)==null?void 0:a.total)||0,subtitle:`${((f=y.value.goals)==null?void 0:f.completed)||0} completati`,icon:"presentation-chart-line",color:"green",link:"/app/personnel/performance?tab=goals"},{id:"average_rating",title:"Rating Medio",value:y.value.average_rating?`${y.value.average_rating}/5`:"N/A",subtitle:"Performance team",icon:"star",color:"yellow"},{id:"completion_rate",title:"Tasso Completamento",value:(j=y.value.reviews)!=null&&j.completion_rate?`${y.value.reviews.completion_rate}%`:"N/A",subtitle:"Valutazioni 2024",icon:"chart-bar",color:"purple"}]}),O=h(()=>[{id:"performance-trends",title:"Trend Performance",type:"line"},{id:"goals-progress",title:"Stato Obiettivi",type:"donut"}]),U=h(()=>p.reviews.filter(r=>r.status==="pending"||r.due_date).slice(0,5).map(r=>{var t,a,f;return{id:r.id,title:`Valutazione ${(t=r.employee)==null?void 0:t.first_name} ${(a=r.employee)==null?void 0:a.last_name}`,description:`Anno ${r.review_year} - ${((f=r.template)==null?void 0:f.name)||"Template Standard"}`,status:r.status,priority:r.status==="pending"&&r.due_date?"high":"medium",rating:r.overall_rating,due_date:r.due_date,date:r.due_date||r.created_at}})),V=h(()=>[{id:1,title:"Valutazione completata",description:"Mario Rossi ha completato la valutazione 2024",timestamp:new Date(Date.now()-2*60*60*1e3).toISOString(),type:"review",icon:"clipboard-document-check",user_name:"Mario Rossi"},{id:2,title:"Nuovo obiettivo assegnato",description:"Certificazione AWS per Q2 2025",timestamp:new Date(Date.now()-6*60*60*1e3).toISOString(),type:"goal",icon:"presentation-chart-line",user_name:"Anna Verdi"},{id:3,title:"Feedback 360° ricevuto",description:"Feedback da 3 colleghi per Luca Bianchi",timestamp:new Date(Date.now()-24*60*60*1e3).toISOString(),type:"feedback",icon:"chat-bubble-left-right",user_name:"Luca Bianchi"}]),Q=h(()=>[{id:"avg_completion_time",name:"Tempo Medio Completamento",description:"Giorni per completare valutazione",current_value:12,target_value:10,unit:" giorni",performance_percentage:83,trend:"down"},{id:"feedback_response_rate",name:"Tasso Risposta Feedback",description:"Percentuale feedback 360° ricevuti",current_value:87,target_value:95,unit:"%",performance_percentage:92,trend:"up"},{id:"goals_on_track",name:"Obiettivi in Linea",description:"Obiettivi con progresso >70%",current_value:15,target_value:20,unit:"",performance_percentage:75,trend:"stable"}]),D=h(()=>{const r=y.value.goals;return!r||!r.total?0:Math.round(r.completed/r.total*100)}),W=h(()=>[{id:"reviews",name:"Valutazioni",icon:"clipboard-document-check",badge:p.reviews.length||"",disabled:!1},{id:"goals",name:"Obiettivi",icon:"presentation-chart-line",badge:p.goals.length||"",disabled:!1},{id:"analytics",name:"Analytics",icon:"chart-bar",disabled:!0},{id:"templates",name:"Template",icon:"document-text",badge:p.templates.length||"",disabled:!m.value}]),Z=h(()=>[{key:"employee_name",label:"Dipendente",type:"text"},{key:"review_year",label:"Anno",type:"text"},{key:"status",label:"Stato",type:"custom"},{key:"overall_rating",label:"Rating",type:"custom"},{key:"due_date",label:"Scadenza",type:"date"}]),J=h(()=>p.reviews.map(r=>{var t,a;return{...r,employee_name:`${(t=r.employee)==null?void 0:t.first_name} ${(a=r.employee)==null?void 0:a.last_name}`||"N/A"}})),Y=async()=>{await s()},X=r=>{C.value=r,Y()},ee=()=>{p.clearError()},te=(r,t)=>{console.log("Chart action:",r,t)},ae=()=>{console.log("Create new review")},se=async()=>{await s()},re=async()=>{await s()},oe=async()=>{try{const r={year:g.value,employee_id:v.value||void 0};await p.fetchReviews(r)}catch(r){console.error("Error loading reviews:",r)}},s=async()=>{try{const r={year:g.value,employee_id:v.value||void 0,period:C.value};await Promise.all([p.fetchDashboardData(r),p.fetchReviews(r),p.fetchGoals(r),p.fetchTemplates()])}catch(r){console.error("Error loading performance data:",r)}},o=r=>{console.log("Review clicked:",r)},i=r=>{console.log("Goal clicked:",r)},l=r=>{console.log("Template clicked:",r)},n=r=>{const t={draft:"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300",in_progress:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",pending:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",completed:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",approved:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"};return t[r]||t.draft},x=r=>({draft:"Bozza",in_progress:"In corso",pending:"In attesa",completed:"Completata",approved:"Approvata"})[r]||r,S=r=>{const t={high:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",medium:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",low:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"};return t[r]||t.medium},E=r=>{const t={up:"bg-green-500",down:"bg-red-500",stable:"bg-yellow-500"};return t[r]||t.stable},q=r=>{const t={up:"text-green-600 dark:text-green-400",down:"text-red-600 dark:text-red-400",stable:"text-yellow-600 dark:text-yellow-400"};return t[r]||t.stable},ue=r=>{const t={up:"↗ In crescita",down:"↘ In calo",stable:"→ Stabile"};return t[r]||t.stable},me=r=>r>=90?"bg-green-500":r>=70?"bg-yellow-500":"bg-red-500",H=r=>r?new Date(r).toLocaleDateString("it-IT",{year:"numeric",month:"short",day:"numeric"}):"",pe=r=>{if(!r)return"";const t=new Date(r),f=Math.floor((new Date-t)/(1e3*60*60));return f<1?"Ora":f<24?`${f}h fa`:f<48?"Ieri":H(r)};return ge(async()=>{m.value&&await A.fetchUsers(),await s()}),(r,t)=>(d(),ye(he,{title:"Performance Management",subtitle:"Gestione valutazioni e obiettivi del personale",loading:w(p).loading,error:w(p).error,stats:M.value,charts:O.value,"recent-items":U.value,activities:V.value,kpis:Q.value,"selected-period":C.value,"period-options":B.value,"recent-items-title":"Valutazioni Scadenza","recent-items-empty-message":"Nessuna valutazione in scadenza","recent-items-link":"/app/personnel/performance?tab=reviews&status=pending","recent-items-link-text":"Vedi tutte le valutazioni","activities-title":"Attività Recenti","activities-empty-message":"Nessuna attività recente","kpis-title":"KPIs Performance",onRefresh:Y,onPeriodChange:X,onClearError:ee,onChartAction:te},{"header-actions":P(()=>[m.value?(d(),u("div",Ce,[t[4]||(t[4]=e("label",{class:"text-sm font-medium text-gray-700 dark:text-gray-300"},"Dipendente:",-1)),ce(e("select",{"onUpdate:modelValue":t[0]||(t[0]=a=>v.value=a),onChange:se,class:"bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md py-2 pl-3 pr-10 text-sm font-medium text-gray-700 dark:text-gray-300 focus:outline-none focus:ring-brand-primary-500 focus:border-brand-primary-500"},[t[3]||(t[3]=e("option",{value:""},"Tutti i dipendenti",-1)),(d(!0),u(F,null,G(w(A).users,a=>(d(),u("option",{key:a.id,value:a.id},c(a.first_name)+" "+c(a.last_name),9,Re))),128))],544),[[de,v.value]])])):R("",!0),e("div",Se,[t[6]||(t[6]=e("label",{class:"text-sm font-medium text-gray-700 dark:text-gray-300"},"Anno:",-1)),ce(e("select",{"onUpdate:modelValue":t[1]||(t[1]=a=>g.value=a),onChange:re,class:"bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md py-2 pl-3 pr-10 text-sm font-medium text-gray-700 dark:text-gray-300 focus:outline-none focus:ring-brand-primary-500 focus:border-brand-primary-500"},t[5]||(t[5]=[e("option",{value:"2024"},"2024",-1),e("option",{value:"2025"},"2025",-1),e("option",{value:"2026"},"2026",-1)]),544),[[de,g.value]])]),I.value?(d(),u("button",{key:1,onClick:ae,class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-brand-primary-600 hover:bg-brand-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-primary-500"},[L(N,{name:"plus",size:"sm",class:"mr-2"}),t[7]||(t[7]=K(" Nuova Valutazione "))])):R("",!0)]),"chart-performance-trends":P(()=>t[8]||(t[8]=[e("div",{class:"h-64 flex items-center justify-center"},[e("div",{class:"w-full"},[e("svg",{class:"w-full h-48",viewBox:"0 0 400 200"},[e("defs",null,[e("linearGradient",{id:"performanceGradient",x1:"0%",y1:"0%",x2:"0%",y2:"100%"},[e("stop",{offset:"0%",style:{"stop-color":"#3b82f6","stop-opacity":"0.3"}}),e("stop",{offset:"100%",style:{"stop-color":"#3b82f6","stop-opacity":"0"}})])]),e("g",{stroke:"#e5e7eb","stroke-width":"1",opacity:"0.5"},[e("line",{x1:"0",y1:"40",x2:"400",y2:"40"}),e("line",{x1:"0",y1:"80",x2:"400",y2:"80"}),e("line",{x1:"0",y1:"120",x2:"400",y2:"120"}),e("line",{x1:"0",y1:"160",x2:"400",y2:"160"})]),e("path",{d:"M 0 160 L 80 140 L 160 130 L 240 115 L 320 110 L 400 105 L 400 200 L 0 200 Z",fill:"url(#performanceGradient)"}),e("path",{d:"M 0 160 L 80 140 L 160 130 L 240 115 L 320 110 L 400 105",fill:"none",stroke:"#3b82f6","stroke-width":"3","stroke-linecap":"round","stroke-linejoin":"round"}),e("g",{fill:"#3b82f6"},[e("circle",{cx:"0",cy:"160",r:"4"}),e("circle",{cx:"80",cy:"140",r:"4"}),e("circle",{cx:"160",cy:"130",r:"4"}),e("circle",{cx:"240",cy:"115",r:"4"}),e("circle",{cx:"320",cy:"110",r:"4"}),e("circle",{cx:"400",cy:"105",r:"4"})])]),e("div",{class:"mt-4 flex justify-center space-x-4 text-sm"},[e("div",{class:"flex items-center"},[e("div",{class:"w-3 h-3 bg-blue-500 rounded-full mr-2"}),e("span",{class:"text-gray-700 dark:text-gray-300"},"Performance Media")])])])],-1)])),"chart-goals-progress":P(()=>{var a,f,j,le,ne;return[e("div",Pe,[e("div",Te,[e("div",ze,[(d(),u("svg",Ee,[t[9]||(t[9]=e("path",{d:"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831",fill:"none",stroke:"#e5e7eb","stroke-width":"3"},null,-1)),e("path",{d:"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831",fill:"none",stroke:"#10b981","stroke-width":"3","stroke-dasharray":`${D.value}, 100`,"stroke-linecap":"round"},null,8,Le)])),e("div",Ae,[e("div",$e,[e("div",Me,c(D.value)+"%",1),t[10]||(t[10]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"Completati",-1))])])]),e("div",je,[e("div",Ne,[t[11]||(t[11]=e("div",{class:"flex items-center"},[e("div",{class:"w-3 h-3 bg-green-500 rounded-full mr-2"}),e("span",null,"Completati")],-1)),e("span",Ve,c(((a=y.value.goals)==null?void 0:a.completed)||0),1)]),e("div",Fe,[t[12]||(t[12]=e("div",{class:"flex items-center"},[e("div",{class:"w-3 h-3 bg-yellow-500 rounded-full mr-2"}),e("span",null,"In Corso")],-1)),e("span",Ge,c(((f=y.value.goals)==null?void 0:f.in_progress)||0),1)]),e("div",Ie,[t[13]||(t[13]=e("div",{class:"flex items-center"},[e("div",{class:"w-3 h-3 bg-gray-300 rounded-full mr-2"}),e("span",null,"Pianificati")],-1)),e("span",Be,c(((j=y.value.goals)==null?void 0:j.total)-(((le=y.value.goals)==null?void 0:le.completed)||0)-(((ne=y.value.goals)==null?void 0:ne.in_progress)||0)||0),1)])])])])]}),"recent-item":P(({item:a})=>[e("div",Oe,[e("div",Ue,[e("h3",De,c(a.title),1),e("p",Ye,c(a.description),1),e("div",qe,[e("span",{class:T(["inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium",n(a.status)])},c(x(a.status)),3),a.priority?(d(),u("span",{key:0,class:T(["inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium",S(a.priority)])},c(a.priority),3)):R("",!0)])]),e("div",He,[a.rating?(d(),u("div",Ke,c(a.rating)+"/5 ",1)):R("",!0),a.due_date?(d(),u("div",Qe," Scadenza: "+c(H(a.due_date)),1)):R("",!0)])])]),"activity-item":P(({activity:a})=>[e("div",We,[e("p",Ze,c(a.title),1),e("p",Je,c(a.description),1),e("div",Xe,[e("span",et,c(pe(a.timestamp)),1),a.user_name?(d(),u("span",tt,c(a.user_name),1)):R("",!0)])])]),"kpi-item":P(({kpi:a})=>[e("div",at,[e("div",st,[e("h3",rt,[e("div",{class:T(["w-2 h-2 rounded-full mr-2",E(a.trend)])},null,2),K(" "+c(a.name),1)]),e("p",ot,c(a.description),1)]),e("div",lt,[e("p",nt,c(a.current_value)+c(a.unit),1),e("p",it," Target: "+c(a.target_value)+c(a.unit),1),e("p",{class:T(["text-xs",q(a.trend)])},c(ue(a.trend)),3)])]),e("div",ct,[e("div",dt,[e("div",{class:T(["h-2 rounded-full transition-all duration-500",me(a.performance_percentage)]),style:ie({width:Math.min(a.performance_percentage,100)+"%"})},null,6)]),e("p",ut,c(Math.round(a.performance_percentage))+"% del target ",1)])]),footer:P(()=>[e("div",mt,[L(be,{tabs:W.value,activeTab:_.value,onTabChange:t[2]||(t[2]=a=>_.value=a),class:"border-b border-gray-200 dark:border-gray-700"},null,8,["tabs","activeTab"]),e("div",pt,[_.value==="reviews"?(d(),u("div",vt,[w(p).loading?(d(),u("div",gt,t[14]||(t[14]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"},null,-1),e("p",{class:"mt-2 text-gray-600 dark:text-gray-400"},"Caricamento valutazioni...",-1)]))):w(p).error?(d(),u("div",yt,[t[15]||(t[15]=e("div",{class:"text-red-600 mb-2"},"Errore nel caricamento:",-1)),e("div",ft,c(w(p).error),1),e("button",{onClick:oe,class:"mt-4 btn btn-sm btn-primary"}," Riprova ")])):(d(),u("div",xt,[L(we,{title:"Valutazioni Performance",columns:Z.value,data:J.value,"row-key":a=>a.id,"empty-message":"Nessuna valutazione trovata",onRowClick:o},{"cell-status":P(({value:a})=>[e("span",{class:T(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",n(a)])},c(x(a)),3)]),"cell-overall_rating":P(({value:a})=>[a?(d(),u("div",_t,[e("span",ht,c(a.toFixed(1)),1),e("div",bt,[(d(),u(F,null,G(5,f=>L(N,{key:f,name:"star",size:"sm",class:T([f<=Math.round(a)?"text-yellow-400":"text-gray-300","w-4 h-4"])},null,8,["class"])),64))])])):(d(),u("span",wt,"N/A"))]),_:1},8,["columns","data","row-key"])]))])):R("",!0),_.value==="goals"?(d(),u("div",kt,[w(p).loading?(d(),u("div",Ct,t[16]||(t[16]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto"},null,-1),e("p",{class:"mt-2 text-gray-600 dark:text-gray-400"},"Caricamento obiettivi...",-1)]))):(d(),u("div",Rt,[w(p).goals.length===0?(d(),u("div",St,[L(N,{name:"presentation-chart-line",class:"w-16 h-16 mx-auto mb-2"}),t[17]||(t[17]=e("p",null,"Nessun obiettivo trovato",-1))])):(d(),u("div",Pt,[(d(!0),u(F,null,G(w(p).goals,a=>(d(),u("div",{key:a.id,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors cursor-pointer",onClick:f=>i(a)},[e("div",zt,[e("h3",Et,c(a.title),1),e("span",{class:T(["inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium",S(a.priority)])},c(a.priority),3)]),e("p",Lt,c(a.description),1),e("div",At,[e("div",$t,[t[18]||(t[18]=e("span",{class:"text-gray-600 dark:text-gray-400"},"Progresso",-1)),e("span",Mt,c(a.progress_percentage)+"%",1)]),e("div",jt,[e("div",{class:"bg-green-500 h-2 rounded-full transition-all duration-500",style:ie({width:a.progress_percentage+"%"})},null,4)])]),e("div",Nt,[t[19]||(t[19]=e("span",{class:"text-gray-500 dark:text-gray-400"},"Scadenza:",-1)),e("span",Vt,c(H(a.target_date)),1)])],8,Tt))),128))]))]))])):R("",!0),_.value==="analytics"?(d(),u("div",Ft,[e("div",Gt,[L(N,{name:"chart-bar",class:"w-16 h-16 mx-auto mb-4 text-gray-400"}),t[20]||(t[20]=e("h3",{class:"text-lg font-medium mb-2 text-gray-900 dark:text-white"},"Analytics in Sviluppo",-1)),t[21]||(t[21]=e("p",{class:"text-gray-600 dark:text-gray-400"}," La sezione Analytics è in fase di sviluppo e sarà disponibile presto. ",-1)),t[22]||(t[22]=e("div",{class:"mt-4 text-sm text-gray-500"},[K(" Funzionalità previste: "),e("ul",{class:"mt-2 list-disc list-inside space-y-1"},[e("li",null,"Grafici di performance nel tempo"),e("li",null,"Confronto tra dipendenti"),e("li",null,"Report esportabili"),e("li",null,"Trend analysis")])],-1))])])):R("",!0),_.value==="templates"?(d(),u("div",It,[w(p).loading?(d(),u("div",Bt,t[23]||(t[23]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"},null,-1),e("p",{class:"mt-2 text-gray-600 dark:text-gray-400"},"Caricamento template...",-1)]))):(d(),u("div",Ot,[w(p).templates.length===0?(d(),u("div",Ut,[L(N,{name:"document-text",class:"w-16 h-16 mx-auto mb-2"}),t[24]||(t[24]=e("p",null,"Nessun template trovato",-1))])):(d(),u("div",Dt,[(d(!0),u(F,null,G(w(p).templates,a=>(d(),u("div",{key:a.id,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors cursor-pointer",onClick:f=>l(a)},[e("div",qt,[e("h3",Ht,c(a.name),1),a.is_default?(d(),u("span",Kt," Default ")):R("",!0)]),e("p",Qt,c(a.description),1),e("div",Wt,[t[25]||(t[25]=e("span",{class:"text-gray-500 dark:text-gray-400"},"Tipo:",-1)),e("span",Zt,c(a.template_type),1)])],8,Yt))),128))]))]))])):R("",!0)])])]),_:1},8,["loading","error","stats","charts","recent-items","activities","kpis","selected-period","period-options"]))}},na=fe(Jt,[["__scopeId","data-v-8c8d9fd0"]]);export{na as default};
