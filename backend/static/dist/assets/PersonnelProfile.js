import{r as w,c as T,w as te,b as l,j as a,l as e,g as i,e as y,v as P,t as n,n as B,A as ee,f as N,E as q,F as A,q as E,B as ae,P as le,O as se,o as re,u as oe,p as ne,s as ie}from"./vendor.js";import{u as de}from"./personnel.js";import{_ as J,H as x,c as M,u as ue,e as ce}from"./app.js";import{u as me}from"./useToast.js";import{T as ge}from"./TabContainer.js";import{P as ve}from"./Pagination.js";const ye={class:"space-y-6"},pe={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},xe={class:"flex items-center justify-between mb-4"},fe={key:1,class:"flex items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"},be={class:"flex-1"},ke={class:"text-sm font-medium text-gray-900 dark:text-white"},he={class:"text-xs text-gray-500 dark:text-gray-400"},_e={key:0,class:"mt-1"},$e={class:"flex space-x-2"},we={key:2,class:"mt-4"},Ce={class:"flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 mb-2"},je={class:"flex items-center"},ze={class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"},Le={key:0,class:"bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-6"},Pe={class:"flex items-center justify-between mb-4"},Te={class:"flex items-center"},Ie={class:"w-8 h-8 bg-purple-100 dark:bg-purple-800 rounded-full flex items-center justify-center mr-3"},Se={class:"flex items-center space-x-3"},Ae={key:0,class:"mb-4"},Ee={class:"text-sm text-purple-700 dark:text-purple-300 bg-white dark:bg-gray-800 rounded-lg p-3 border border-purple-200 dark:border-purple-700"},De={key:1,class:"mb-4"},Ve={class:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-200"},Be={key:2},Me={class:"grid grid-cols-1 md:grid-cols-2 gap-3 mb-3"},Je={class:"flex items-center space-x-2"},Ne={class:"text-sm font-medium text-gray-900 dark:text-white"},Oe={class:"flex items-center space-x-2"},Ue={key:0,class:"text-xs text-purple-600 dark:text-purple-400 bg-purple-100 dark:bg-purple-900 px-2 py-1 rounded"},Re={class:"text-xs text-gray-500 dark:text-gray-400"},qe={key:0,class:"text-center"},He={class:"text-sm text-purple-600 dark:text-purple-400 bg-purple-100 dark:bg-purple-900 px-3 py-1 rounded-full"},Fe={class:"mt-3"},Xe={class:"flex items-center justify-between mb-4"},Qe={class:"max-h-96 overflow-y-auto mb-4"},We={class:"space-y-2"},Ge=["id","value"],Ke=["for"],Ye={class:"text-sm font-medium text-gray-900 dark:text-white"},Ze={class:"text-xs text-gray-500 dark:text-gray-400"},et={key:0},tt={key:0,class:"text-xs text-gray-400 mt-1"},st={class:"flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-600"},at=["disabled"],lt={__name:"CVTab",props:{user:{type:Object,required:!0},canEdit:{type:Boolean,default:!1}},emits:["user-updated"],setup(t,{emit:b}){const h=t,r=b,{success:o,error:c,info:k}=me(),g=w(!1),u=w(!1),d=w(0),$=w(!1),p=w(!1),j=w([]),I=w(!1),D=w(null),O=w(0),L=T(()=>{var v;if(!((v=h.user.profile)!=null&&v.cv_analysis_data))return null;try{return JSON.parse(h.user.profile.cv_analysis_data)}catch(s){return console.error("Error parsing CV analysis data:",s),null}}),K=v=>v?new Date(v).toLocaleDateString("it-IT",{year:"numeric",month:"long",day:"numeric"}):"",Y=v=>({beginner:"Principiante",intermediate:"Intermedio",advanced:"Avanzato",expert:"Esperto",1:"Principiante",2:"Base",3:"Intermedio",4:"Avanzato",5:"Esperto"})[v]||"Intermedio",H=()=>{var v;(v=D.value)==null||v.click()},Z=v=>{const s=v.target.files[0];s&&U(s)},F=v=>{v.preventDefault(),$.value=!1;const s=v.dataTransfer.files;s.length>0&&U(s[0])},U=async v=>{var m;if(!["application/pdf","application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/msword","text/plain"].includes(v.type)){c("Formato file non supportato","Usa PDF, DOCX, DOC o TXT.");return}if(v.size>10*1024*1024){c("File troppo grande","Dimensione massima: 10MB.");return}g.value=!0,d.value=0;try{const f=new FormData;f.append("cv_file",v),f.append("analyze_skills","true"),f.append("auto_add_skills","false");const z=setInterval(()=>{d.value<90&&(d.value+=Math.random()*15)},200),S=await M.post(`/api/personnel/users/${h.user.id}/cv/upload`,f,{headers:{"Content-Type":"multipart/form-data"}});clearInterval(z),d.value=100;const C=S.data;if(console.log("Upload response:",C),C.success){if(g.value=!1,r("user-updated",C.data),(m=C.data.profile)!=null&&m.cv_analysis_data)try{const _=JSON.parse(C.data.profile.cv_analysis_data);console.log("Analysis data:",_),_.skills&&_.skills.length>0?o("CV caricato e analizzato con successo!",`Trovate ${_.skills.length} competenze`):o("CV caricato con successo!","Nessuna competenza estratta dall'AI")}catch(_){console.error("Error parsing analysis data:",_),o("CV caricato con successo!","Errore nel parsing dei dati AI")}else o("CV caricato con successo!","Analisi AI non disponibile"),console.log("No AI analysis data found in response. Profile data:",C.data.profile);D.value&&(D.value.value="")}else throw new Error(C.message||"Errore durante il caricamento")}catch(f){console.error("Errore durante il caricamento del CV:",f),c("Errore durante il caricamento del CV",f.message)}finally{g.value=!1,d.value=0}},X=async()=>{try{const s=(await M.get(`/api/personnel/users/${h.user.id}/cv/download`,{responseType:"blob"})).data,m=window.URL.createObjectURL(s),f=document.createElement("a");f.href=m,f.download=`CV_${h.user.full_name}.pdf`,document.body.appendChild(f),f.click(),window.URL.revokeObjectURL(m),document.body.removeChild(f)}catch(v){console.error("Errore durante il download del CV:",v),alert("Errore durante il download del CV: "+v.message)}},Q=async()=>{if(confirm("Sei sicuro di voler eliminare il CV? Questa azione non può essere annullata."))try{const s=(await M.delete(`/api/personnel/users/${h.user.id}/cv`)).data;if(s.success)r("user-updated",s.data),o("CV eliminato con successo");else throw new Error(s.message||"Errore durante l'eliminazione")}catch(v){console.error("Errore durante l'eliminazione del CV:",v),c("Errore durante l'eliminazione del CV",v.message)}},R=async()=>{if(j.value.length!==0){I.value=!0;try{const v=j.value.map(f=>{const z=L.value.skills[f];return{...z,level:W(z.level)}}),m=(await M.post(`/api/personnel/users/${h.user.id}/skills/from-cv`,{selected_skills:v})).data;if(m.success){const{total_added:f,total_skipped:z}=m.data;f>0&&o(`Aggiunte ${f} competenze al profilo!`),z>0&&k(`${z} competenze erano già presenti nel profilo`),p.value=!1,j.value=[],r("user-updated")}else throw new Error(m.message||"Errore durante l'aggiunta delle competenze")}catch(v){console.error("Errore durante l'aggiunta delle competenze:",v),c("Errore durante l'aggiunta delle competenze",v.message)}finally{I.value=!1}}},W=v=>{const s={beginner:1,intermediate:3,advanced:4,expert:5};return typeof v=="number"?Math.max(1,Math.min(5,v)):typeof v=="string"&&s[v.toLowerCase()]||3};return te(g,v=>{v||(d.value=0)}),te(u,v=>{v||(O.value=0)}),(v,s)=>{var m,f,z,S,C;return a(),l("div",ye,[e("div",pe,[e("div",xe,[s[8]||(s[8]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"CV Attuale",-1)),t.canEdit&&!g.value?(a(),l("button",{key:0,onClick:H,class:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm transition-colors duration-200"},[y(x,{name:"arrow-up-tray",size:"sm",class:"inline mr-2"}),P(" "+n((m=t.user.profile)!=null&&m.current_cv_path?"Aggiorna CV":"Carica CV"),1)])):i("",!0)]),!((f=t.user.profile)!=null&&f.current_cv_path)&&t.canEdit?(a(),l("div",{key:0,onDrop:F,onDragover:s[0]||(s[0]=ee(()=>{},["prevent"])),onDragenter:s[1]||(s[1]=ee(()=>{},["prevent"])),class:B(["border-2 border-dashed rounded-lg p-8 text-center transition-colors duration-200",$.value?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-300 dark:border-gray-600"])},[y(x,{name:"cloud-arrow-up",size:"2xl",class:"mx-auto text-gray-400"}),s[10]||(s[10]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Carica il tuo CV",-1)),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},[s[9]||(s[9]=P(" Trascina qui il file o ")),e("button",{onClick:H,class:"text-blue-600 hover:text-blue-500"},"sfoglia")]),s[11]||(s[11]=e("p",{class:"mt-1 text-xs text-gray-400"},"PDF, DOCX, DOC, TXT (max 10MB)",-1))],34)):(z=t.user.profile)!=null&&z.current_cv_path?(a(),l("div",fe,[y(x,{name:"document-text",size:"lg",class:"text-red-600 mr-3"}),e("div",be,[e("p",ke,"CV_"+n(t.user.full_name)+".pdf",1),e("p",he," Caricato il "+n(K(t.user.profile.cv_last_updated)),1),t.user.profile.cv_analysis_data?(a(),l("div",_e,s[12]||(s[12]=[e("span",{class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"}," ✨ Analisi AI completata ",-1)]))):i("",!0)]),e("div",$e,[e("button",{onClick:X,class:"text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300",title:"Scarica CV"},[y(x,{name:"arrow-down-tray",size:"md"})]),t.canEdit?(a(),l("button",{key:0,onClick:Q,class:"text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300",title:"Elimina CV"},[y(x,{name:"trash",size:"md"})])):i("",!0)])])):i("",!0),g.value||u.value?(a(),l("div",we,[e("div",Ce,[e("div",je,[u.value?(a(),N(x,{key:0,name:"arrow-path",size:"sm",class:"animate-spin -ml-1 mr-2 text-blue-600"})):i("",!0),e("span",null,n(u.value?"Analisi AI in corso...":"Caricamento in corso..."),1)]),e("span",null,n(u.value?O.value:d.value)+"%",1)]),e("div",ze,[e("div",{class:B(["h-2 rounded-full transition-all duration-300",u.value?"bg-purple-600":"bg-blue-600"]),style:q({width:(u.value?O.value:d.value)+"%"})},null,6)])])):i("",!0)]),L.value?(a(),l("div",Le,[e("div",Pe,[e("div",Te,[e("div",Ie,[y(x,{name:"bolt",size:"sm",class:"text-purple-600 dark:text-purple-300"})]),s[13]||(s[13]=e("h3",{class:"text-lg font-medium text-purple-900 dark:text-purple-100"}," Analisi AI del CV ",-1))]),e("div",Se,[t.canEdit&&((S=L.value.skills)==null?void 0:S.length)>0?(a(),l("button",{key:0,onClick:s[2]||(s[2]=_=>p.value=!0),class:"text-sm bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 rounded-md transition-colors duration-200 flex items-center"},[y(x,{name:"plus",size:"sm",class:"mr-1"}),s[14]||(s[14]=P(" Aggiungi Competenze "))])):i("",!0)])]),L.value.summary?(a(),l("div",Ae,[s[15]||(s[15]=e("h4",{class:"text-sm font-medium text-purple-800 dark:text-purple-200 mb-2"},"Profilo Professionale",-1)),e("p",Ee,n(L.value.summary),1)])):i("",!0),L.value.experience_years?(a(),l("div",De,[e("span",Ve,[y(x,{name:"clock",size:"sm",class:"mr-1"}),P(" "+n(L.value.experience_years)+" anni di esperienza ",1)])])):i("",!0),((C=L.value.skills)==null?void 0:C.length)>0?(a(),l("div",Be,[s[17]||(s[17]=e("h4",{class:"text-sm font-medium text-purple-800 dark:text-purple-200 mb-3"},"Competenze Estratte",-1)),e("div",Me,[(a(!0),l(A,null,E(L.value.skills.slice(0,8),(_,V)=>(a(),l("div",{key:V,class:"flex items-center justify-between bg-white dark:bg-gray-800 rounded-lg p-3 border border-purple-200 dark:border-purple-700"},[e("div",Je,[s[16]||(s[16]=e("div",{class:"w-2 h-2 bg-purple-500 rounded-full"},null,-1)),e("span",Ne,n(_.name),1)]),e("div",Oe,[_.category?(a(),l("span",Ue,n(_.category),1)):i("",!0),e("span",Re,n(Y(_.level)),1)])]))),128))]),L.value.skills.length>8?(a(),l("div",qe,[e("span",He," +"+n(L.value.skills.length-8)+" altre competenze disponibili ",1)])):i("",!0)])):i("",!0)])):i("",!0),e("input",{ref_key:"fileInput",ref:D,type:"file",accept:".pdf,.docx,.doc,.txt",onChange:Z,class:"hidden"},null,544),p.value?(a(),l("div",{key:1,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:s[7]||(s[7]=_=>p.value=!1)},[e("div",{class:"relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:s[6]||(s[6]=ee(()=>{},["stop"]))},[e("div",Fe,[e("div",Xe,[s[18]||(s[18]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Seleziona Competenze da Aggiungere ",-1)),e("button",{onClick:s[3]||(s[3]=_=>p.value=!1),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},[y(x,{name:"x-mark",size:"md"})])]),e("div",Qe,[e("div",We,[(a(!0),l(A,null,E(L.value.skills,(_,V)=>(a(),l("div",{key:V,class:"flex items-center p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"},[ae(e("input",{type:"checkbox",id:`skill-${V}`,"onUpdate:modelValue":s[4]||(s[4]=G=>j.value=G),value:V,class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,8,Ge),[[le,j.value]]),e("label",{for:`skill-${V}`,class:"ml-3 flex-1 cursor-pointer"},[e("div",Ye,n(_.name),1),e("div",Ze,[P(n(_.category)+" • Livello "+n(_.level||3)+" ",1),_.years_experience?(a(),l("span",et," • "+n(_.years_experience)+" anni",1)):i("",!0)]),_.context?(a(),l("div",tt,n(_.context),1)):i("",!0)],8,Ke)]))),128))])]),e("div",st,[e("button",{onClick:s[5]||(s[5]=_=>p.value=!1),class:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-md transition-colors duration-200"}," Annulla "),e("button",{onClick:R,disabled:j.value.length===0||I.value,class:"px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-md transition-colors duration-200"},n(I.value?"Aggiungendo...":`Aggiungi ${j.value.length} competenze`),9,at)])])])])):i("",!0)])}}},rt=J(lt,[["__scopeId","data-v-70b5f306"]]),ot={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},nt={class:"flex items-center justify-between mb-4"},it={class:"text-lg font-medium text-gray-900 dark:text-white flex items-center"},dt={key:0,class:"space-y-4"},ut={class:"flex items-start justify-between"},ct={class:"flex-1"},mt={class:"flex items-center space-x-3"},gt={class:"text-xl font-semibold text-gray-900 dark:text-white"},vt={key:0,class:"text-sm text-gray-600 dark:text-gray-400 mt-1"},yt={key:1,class:"text-sm text-gray-600 dark:text-gray-400 mt-2"},pt={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},xt={key:0,class:"bg-green-50 dark:bg-green-900/20 p-3 rounded-lg"},ft={class:"flex items-center"},bt={class:"text-lg font-semibold text-green-800 dark:text-green-200"},kt={class:"bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg"},ht={class:"flex items-center"},_t={class:"text-sm font-semibold text-blue-800 dark:text-blue-200"},$t={key:0,class:"bg-gray-50 dark:bg-gray-700 p-3 rounded-lg"},wt={class:"flex items-center space-x-4"},Ct={key:0,class:"flex items-center"},jt={class:"text-sm font-medium text-gray-900 dark:text-white"},zt={key:1,class:"flex items-center"},Lt={class:"text-sm font-medium text-gray-900 dark:text-white"},Pt={key:1,class:"border-t border-gray-200 dark:border-gray-700 pt-4"},Tt={class:"text-sm text-gray-600 dark:text-gray-400"},It={key:2,class:"border-t border-gray-200 dark:border-gray-700 pt-4"},St={class:"text-sm text-gray-600 dark:text-gray-400"},At={key:1,class:"text-center py-8"},Et={key:2,class:"border-t border-gray-200 dark:border-gray-700 pt-4 mt-4"},Dt={class:"space-y-2"},Vt={class:"flex items-center"},Bt={class:"text-gray-900 dark:text-white"},Mt={class:"text-gray-500 dark:text-gray-400"},Jt={__name:"JobLevelCard",props:{currentJobLevel:{type:Object,default:null},jobLevelHistory:{type:Array,default:()=>[]},showHistory:{type:Boolean,default:!0},showCompactHistory:{type:Boolean,default:!0},canAssign:{type:Boolean,default:!1}},emits:["show-history","assign-job-level"],setup(t){const b=t,h=T(()=>b.jobLevelHistory&&b.jobLevelHistory.length>1),r=u=>({dirigente:"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",quadro:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",impiegato:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",operaio:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"})[u]||"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200",o=u=>({dirigente:"Dirigente",quadro:"Quadro",impiegato:"Impiegato",operaio:"Operaio"})[u]||u,c=u=>u?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(u):"",k=u=>u?new Date(u).toLocaleDateString("it-IT",{year:"numeric",month:"long",day:"numeric"}):"",g=u=>u?new Date(u).toLocaleDateString("it-IT",{year:"2-digit",month:"short"}):"";return(u,d)=>(a(),l("div",ot,[e("div",nt,[e("h3",it,[y(x,{name:"academic-cap",size:"md",class:"text-blue-600 mr-2"}),d[2]||(d[2]=P(" Inquadramento "))]),t.showHistory&&h.value?(a(),l("button",{key:0,onClick:d[0]||(d[0]=$=>u.$emit("show-history")),class:"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm"}," Storico ")):i("",!0)]),t.currentJobLevel?(a(),l("div",dt,[e("div",ut,[e("div",ct,[e("div",mt,[e("h4",gt,n(t.currentJobLevel.job_level.name),1),e("span",{class:B(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",r(t.currentJobLevel.job_level.category)])},n(o(t.currentJobLevel.job_level.category)),3)]),t.currentJobLevel.job_level.level?(a(),l("p",vt," Livello: "+n(t.currentJobLevel.job_level.level),1)):i("",!0),t.currentJobLevel.job_level.description?(a(),l("p",yt,n(t.currentJobLevel.job_level.description),1)):i("",!0)])]),e("div",pt,[t.currentJobLevel.current_salary?(a(),l("div",xt,[e("div",ft,[y(x,{name:"banknotes",size:"sm",class:"text-green-600 mr-2"}),e("div",null,[d[3]||(d[3]=e("p",{class:"text-xs text-green-600 dark:text-green-400 font-medium"},"RAL Attuale",-1)),e("p",bt,n(c(t.currentJobLevel.current_salary)),1)])])])):i("",!0),e("div",kt,[e("div",ht,[y(x,{name:"calendar",size:"sm",class:"text-blue-600 mr-2"}),e("div",null,[d[4]||(d[4]=e("p",{class:"text-xs text-blue-600 dark:text-blue-400 font-medium"},"Dal",-1)),e("p",_t,n(k(t.currentJobLevel.start_date)),1)])])])]),t.currentJobLevel.job_level.salary_min||t.currentJobLevel.job_level.salary_max?(a(),l("div",$t,[d[7]||(d[7]=e("p",{class:"text-xs text-gray-600 dark:text-gray-400 font-medium mb-2"},"Range Retributivo per questo Inquadramento",-1)),e("div",wt,[t.currentJobLevel.job_level.salary_min?(a(),l("div",Ct,[d[5]||(d[5]=e("span",{class:"text-xs text-gray-500 dark:text-gray-400 mr-1"},"Min:",-1)),e("span",jt,n(c(t.currentJobLevel.job_level.salary_min)),1)])):i("",!0),t.currentJobLevel.job_level.salary_max?(a(),l("div",zt,[d[6]||(d[6]=e("span",{class:"text-xs text-gray-500 dark:text-gray-400 mr-1"},"Max:",-1)),e("span",Lt,n(c(t.currentJobLevel.job_level.salary_max)),1)])):i("",!0)])])):i("",!0),t.currentJobLevel.job_level.benefits?(a(),l("div",Pt,[d[8]||(d[8]=e("h5",{class:"text-sm font-medium text-gray-900 dark:text-white mb-2"},"Benefits",-1)),e("p",Tt,n(t.currentJobLevel.job_level.benefits),1)])):i("",!0),t.currentJobLevel.notes?(a(),l("div",It,[d[9]||(d[9]=e("h5",{class:"text-sm font-medium text-gray-900 dark:text-white mb-2"},"Note",-1)),e("p",St,n(t.currentJobLevel.notes),1)])):i("",!0)])):(a(),l("div",At,[y(x,{name:"academic-cap",size:"xl",class:"mx-auto text-gray-400 mb-4"}),d[11]||(d[11]=e("h4",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},"Nessun Inquadramento",-1)),d[12]||(d[12]=e("p",{class:"text-sm text-gray-600 dark:text-gray-400"}," Questo dipendente non ha ancora un inquadramento assegnato. ",-1)),t.canAssign?(a(),l("button",{key:0,onClick:d[1]||(d[1]=$=>u.$emit("assign-job-level")),class:"mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"},[y(x,{name:"plus",size:"sm",class:"mr-2"}),d[10]||(d[10]=P(" Assegna Inquadramento "))])):i("",!0)])),t.showCompactHistory&&t.jobLevelHistory&&t.jobLevelHistory.length>1?(a(),l("div",Et,[d[14]||(d[14]=e("h5",{class:"text-sm font-medium text-gray-900 dark:text-white mb-3"},"Storico Recente",-1)),e("div",Dt,[(a(!0),l(A,null,E(t.jobLevelHistory.slice(1,3),$=>(a(),l("div",{key:$.id,class:"flex items-center justify-between text-sm"},[e("div",Vt,[d[13]||(d[13]=e("div",{class:"w-2 h-2 bg-gray-400 rounded-full mr-3"},null,-1)),e("span",Bt,n($.job_level_name),1)]),e("span",Mt,n(g($.start_date))+" - "+n($.end_date?g($.end_date):"Presente"),1)]))),128))])])):i("",!0)]))}},Nt={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Ot={class:"flex items-center space-x-6"},Ut={class:"flex-shrink-0"},Rt={class:"w-24 h-24 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg border-2 border-white/30"},qt=["src","alt"],Ht={key:1,class:"w-24 h-24 bg-white/10 rounded-full flex items-center justify-center"},Ft={class:"flex-1"},Xt={class:"text-3xl font-bold text-white"},Qt={class:"text-white/90 text-lg mt-1"},Wt={class:"flex items-center space-x-3 mt-3"},Gt={key:0,class:"inline-flex items-center px-3 py-1 rounded-full text-sm bg-white/20 backdrop-blur-sm text-white border border-white/30"},Kt={class:"inline-flex items-center px-3 py-1 rounded-full text-sm bg-white/20 backdrop-blur-sm text-white border border-white/30"},Yt={class:"flex-shrink-0"},Zt={key:0,class:"px-6 py-4 bg-gray-50 dark:bg-gray-700"},es={class:"flex items-center justify-between mb-2"},ts={class:"text-sm text-gray-500 dark:text-gray-400"},ss={class:"w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2"},as={__name:"ProfileHeader",props:{user:{type:Object,required:!0},canEdit:{type:Boolean,default:!1},editMode:{type:Boolean,default:!1}},emits:["toggle-edit"],setup(t){ue();const b=T(()=>({background:"linear-gradient(135deg, var(--brand-primary-500) 0%, var(--brand-secondary-500) 100%)"})),h=T(()=>({backgroundColor:"var(--brand-primary-500)"}));return(r,o)=>(a(),l("div",Nt,[e("div",{class:"px-6 py-8 text-white",style:q(b.value)},[e("div",Ot,[e("div",Ut,[e("div",Rt,[t.user.profile_image?(a(),l("img",{key:0,src:t.user.profile_image,alt:t.user.full_name,class:"w-24 h-24 rounded-full object-cover"},null,8,qt)):(a(),l("div",Ht,[y(x,{name:"user",size:"xl",class:"text-white/80"})]))])]),e("div",Ft,[e("h1",Xt,n(t.user.full_name),1),e("p",Qt,n(t.user.position||"Posizione non specificata"),1),e("div",Wt,[t.user.department_obj?(a(),l("span",Gt,[y(x,{name:"building-office",size:"sm",class:"mr-1"}),P(" "+n(t.user.department_obj.name),1)])):i("",!0),e("span",Kt,[y(x,{name:"briefcase",size:"sm",class:"mr-1"}),P(" "+n(t.user.role),1)])])]),e("div",Yt,[t.canEdit?(a(),l("button",{key:0,onClick:o[0]||(o[0]=c=>r.$emit("toggle-edit")),class:"bg-white/20 hover:bg-white/30 backdrop-blur-sm text-white px-4 py-2 rounded-lg transition-all duration-200 border border-white/30 hover:border-white/50"},[y(x,{name:"pencil",size:"md",class:"inline mr-2"}),P(" "+n(t.editMode?"Annulla":"Modifica"),1)])):i("",!0)])])],4),t.user.profile&&t.user.profile.profile_completion!==void 0?(a(),l("div",Zt,[e("div",es,[o[1]||(o[1]=e("span",{class:"text-sm font-medium text-gray-700 dark:text-gray-300"},"Completamento Profilo",-1)),e("span",ts,n(t.user.profile.profile_completion)+"%",1)]),e("div",ss,[e("div",{class:"h-2 rounded-full transition-all duration-300",style:q({width:t.user.profile.profile_completion+"%",...h.value})},null,4)])])):i("",!0)]))}},ls={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},rs={class:"flex items-center justify-between mb-4"},os={key:0,class:"space-y-3"},ns={class:"flex items-center"},is={class:"text-gray-900 dark:text-white"},ds={key:0,class:"flex items-center"},us={class:"text-gray-900 dark:text-white"},cs={key:1,class:"flex items-center"},ms={class:"text-gray-900 dark:text-white"},gs={key:1,class:"space-y-4"},vs=["value"],ys=["value"],ps={class:"flex space-x-2"},xs=["disabled"],fs={__name:"ContactInfoCard",props:{user:{type:Object,required:!0},editMode:{type:Boolean,default:!1},editData:{type:Object,default:()=>({})},canEdit:{type:Boolean,default:!1},saving:{type:Boolean,default:!1}},emits:["toggle-edit","update-field","save","cancel"],setup(t){const b=h=>h?new Date(h).toLocaleDateString("it-IT",{year:"numeric",month:"long",day:"numeric"}):"";return(h,r)=>(a(),l("div",ls,[e("div",rs,[r[5]||(r[5]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Informazioni di Contatto",-1)),t.canEdit&&!t.editMode?(a(),l("button",{key:0,onClick:r[0]||(r[0]=o=>h.$emit("toggle-edit")),class:"text-brand-primary hover:text-brand-primary-600 transition-colors duration-200"},[y(x,{name:"pencil",size:"sm"})])):i("",!0)]),t.editMode?(a(),l("div",gs,[e("div",null,[r[6]||(r[6]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Telefono",-1)),e("input",{value:t.editData.phone,onInput:r[1]||(r[1]=o=>h.$emit("update-field","phone",o.target.value)),type:"tel",class:"brand-input w-full"},null,40,vs)]),e("div",null,[r[7]||(r[7]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Bio",-1)),e("textarea",{value:t.editData.bio,onInput:r[2]||(r[2]=o=>h.$emit("update-field","bio",o.target.value)),rows:"3",class:"brand-input w-full resize-none"},null,40,ys)]),e("div",ps,[e("button",{onClick:r[3]||(r[3]=o=>h.$emit("save")),disabled:t.saving,class:"brand-button flex-1 disabled:opacity-50 disabled:cursor-not-allowed"},n(t.saving?"Salvataggio...":"Salva"),9,xs),e("button",{onClick:r[4]||(r[4]=o=>h.$emit("cancel")),class:"flex-1 bg-gray-300 hover:bg-gray-400 dark:bg-gray-600 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-200 px-4 py-2 rounded-md transition-colors duration-200"}," Annulla ")])])):(a(),l("div",os,[e("div",ns,[y(x,{name:"envelope",size:"md",class:"text-gray-400 mr-3"}),e("span",is,n(t.user.email),1)]),t.user.phone?(a(),l("div",ds,[y(x,{name:"phone",size:"md",class:"text-gray-400 mr-3"}),e("span",us,n(t.user.phone),1)])):i("",!0),t.user.hire_date?(a(),l("div",cs,[y(x,{name:"calendar",size:"md",class:"text-gray-400 mr-3"}),e("span",ms,"Assunto il "+n(b(t.user.hire_date)),1)])):i("",!0)]))]))}},bs=J(fs,[["__scopeId","data-v-a33944ed"]]),ks={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},hs={class:"flex items-center justify-between mb-4"},_s={key:0,class:"space-y-3"},$s={class:"text-sm font-medium text-gray-900 dark:text-white"},ws={class:"flex items-center space-x-2"},Cs=["title"],js={key:0,class:"certification-badge",title:"Competenza certificata"},zs={key:0,class:"text-sm text-gray-500 dark:text-gray-400 text-center pt-2 border-t border-gray-100 dark:border-gray-700"},Ls={key:1,class:"empty-state"},Ps={__name:"SkillsOverviewCard",props:{skills:{type:Array,default:()=>[]},maxSkills:{type:Number,default:4},showViewAllButton:{type:Boolean,default:!0}},emits:["view-all"],setup(t){const b=t,h=T(()=>!b.skills||b.skills.length===0?[]:b.skills.slice(0,b.maxSkills));return(r,o)=>(a(),l("div",ks,[e("div",hs,[o[1]||(o[1]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Competenze Principali",-1)),t.showViewAllButton&&t.skills&&t.skills.length>t.maxSkills?(a(),l("button",{key:0,onClick:o[0]||(o[0]=c=>r.$emit("view-all")),class:"text-brand-primary hover:text-brand-primary-600 text-sm font-medium transition-colors duration-200"}," Vedi tutte ")):i("",!0)]),t.skills&&t.skills.length>0?(a(),l("div",_s,[(a(!0),l(A,null,E(h.value,c=>(a(),l("div",{key:c.id,class:"flex items-center justify-between group hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg p-2 -m-2 transition-colors duration-200"},[e("span",$s,n(c.name),1),e("div",ws,[e("div",{class:"flex space-x-1",title:`Livello: ${c.proficiency_level}/5`},[(a(),l(A,null,E(5,k=>e("div",{key:k,class:B(["skill-dot transition-all duration-200",k<=c.proficiency_level?"skill-dot-active":"skill-dot-inactive"])},null,2)),64))],8,Cs),c.is_certified||c.certified?(a(),l("span",js," ✓ ")):i("",!0)])]))),128)),t.skills.length>t.maxSkills?(a(),l("div",zs," +"+n(t.skills.length-t.maxSkills)+" altre competenze ",1)):i("",!0)])):(a(),l("div",Ls,o[2]||(o[2]=[se('<div class="text-center py-8" data-v-5da89557><div class="w-12 h-12 mx-auto mb-4 text-gray-300 dark:text-gray-600" data-v-5da89557><svg fill="currentColor" viewBox="0 0 24 24" data-v-5da89557><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" data-v-5da89557></path></svg></div><p class="text-gray-500 dark:text-gray-400 text-sm" data-v-5da89557>Nessuna competenza registrata</p><p class="text-gray-400 dark:text-gray-500 text-xs mt-1" data-v-5da89557>Le competenze verranno visualizzate qui una volta aggiunte</p></div>',1)])))]))}},Ts=J(Ps,[["__scopeId","data-v-5da89557"]]),Is={key:0,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6"},Ss={class:"flex items-center justify-between mb-6"},As={key:0,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"},Es={key:0,class:"hr-info-item"},Ds={class:"hr-info-value"},Vs={key:1,class:"hr-info-item"},Bs={class:"hr-info-value"},Ms={key:2,class:"hr-info-item"},Js={class:"hr-info-value"},Ns={key:3,class:"hr-info-item"},Os={class:"hr-info-value"},Us={key:4,class:"hr-info-item"},Rs={class:"hr-info-value"},qs={key:5,class:"hr-info-item"},Hs={class:"hr-info-value"},Fs={key:1,class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Xs=["value"],Qs=["value"],Ws=["value"],Gs=["value"],Ks=["value"],Ys=["value"],Zs={class:"md:col-span-2 flex space-x-2"},ea=["disabled"],ta={__name:"HRInfoCard",props:{profile:{type:Object,required:!0},editMode:{type:Boolean,default:!1},editData:{type:Object,default:()=>({})},canEdit:{type:Boolean,default:!1},saving:{type:Boolean,default:!1}},emits:["toggle-edit","update-field","save","cancel"],setup(t){const b=r=>({full_time:"Tempo Pieno",part_time:"Part Time",contractor:"Consulente",intern:"Stagista"})[r]||r,h=r=>({office:"Ufficio",remote:"Remoto",hybrid:"Ibrido"})[r]||r;return(r,o)=>t.profile?(a(),l("div",Is,[e("div",Ss,[o[9]||(o[9]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Informazioni HR",-1)),t.canEdit&&!t.editMode?(a(),l("button",{key:0,onClick:o[0]||(o[0]=c=>r.$emit("toggle-edit")),class:"text-brand-primary hover:text-brand-primary-600 transition-colors duration-200"},[y(x,{name:"pencil",size:"sm"})])):i("",!0)]),t.editMode?(a(),l("div",Fs,[e("div",null,[o[16]||(o[16]=e("label",{class:"brand-label"},"ID Dipendente",-1)),e("input",{value:t.editData.employee_id,onInput:o[1]||(o[1]=c=>r.$emit("update-field","employee_id",c.target.value)),type:"text",class:"brand-input w-full"},null,40,Xs)]),e("div",null,[o[17]||(o[17]=e("label",{class:"brand-label"},"Titolo Lavoro",-1)),e("input",{value:t.editData.job_title,onInput:o[2]||(o[2]=c=>r.$emit("update-field","job_title",c.target.value)),type:"text",class:"brand-input w-full"},null,40,Qs)]),e("div",null,[o[19]||(o[19]=e("label",{class:"brand-label"},"Tipo Contratto",-1)),e("select",{value:t.editData.employment_type,onChange:o[3]||(o[3]=c=>r.$emit("update-field","employment_type",c.target.value)),class:"brand-input w-full"},o[18]||(o[18]=[se('<option value="" data-v-f35f650b>Seleziona tipo</option><option value="full_time" data-v-f35f650b>Tempo Pieno</option><option value="part_time" data-v-f35f650b>Part Time</option><option value="contractor" data-v-f35f650b>Consulente</option><option value="intern" data-v-f35f650b>Stagista</option>',5)]),40,Ws)]),e("div",null,[o[20]||(o[20]=e("label",{class:"brand-label"},"Ore Settimanali",-1)),e("input",{value:t.editData.weekly_hours,onInput:o[4]||(o[4]=c=>r.$emit("update-field","weekly_hours",c.target.value)),type:"number",min:"1",max:"60",step:"0.5",class:"brand-input w-full"},null,40,Gs)]),e("div",null,[o[22]||(o[22]=e("label",{class:"brand-label"},"Modalità Lavoro",-1)),e("select",{value:t.editData.work_location,onChange:o[5]||(o[5]=c=>r.$emit("update-field","work_location",c.target.value)),class:"brand-input w-full"},o[21]||(o[21]=[e("option",{value:""},"Seleziona modalità",-1),e("option",{value:"office"},"Ufficio",-1),e("option",{value:"remote"},"Remoto",-1),e("option",{value:"hybrid"},"Ibrido",-1)]),40,Ks)]),e("div",null,[o[23]||(o[23]=e("label",{class:"brand-label"},"Indirizzo",-1)),e("input",{value:t.editData.address,onInput:o[6]||(o[6]=c=>r.$emit("update-field","address",c.target.value)),type:"text",class:"brand-input w-full"},null,40,Ys)]),e("div",Zs,[e("button",{onClick:o[7]||(o[7]=c=>r.$emit("save")),disabled:t.saving,class:"brand-button flex-1 disabled:opacity-50 disabled:cursor-not-allowed"},n(t.saving?"Salvataggio...":"Salva"),9,ea),e("button",{onClick:o[8]||(o[8]=c=>r.$emit("cancel")),class:"flex-1 bg-gray-300 hover:bg-gray-400 dark:bg-gray-600 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-200 px-4 py-2 rounded-md transition-colors duration-200"}," Annulla ")])])):(a(),l("div",As,[t.profile.employee_id?(a(),l("div",Es,[y(x,{name:"identification",size:"md",class:"hr-info-icon"}),e("div",null,[o[10]||(o[10]=e("span",{class:"hr-info-label"},"ID Dipendente",-1)),e("p",Ds,n(t.profile.employee_id),1)])])):i("",!0),t.profile.job_title?(a(),l("div",Vs,[y(x,{name:"briefcase",size:"md",class:"hr-info-icon"}),e("div",null,[o[11]||(o[11]=e("span",{class:"hr-info-label"},"Titolo",-1)),e("p",Bs,n(t.profile.job_title),1)])])):i("",!0),t.profile.employment_type?(a(),l("div",Ms,[y(x,{name:"document-text",size:"md",class:"hr-info-icon"}),e("div",null,[o[12]||(o[12]=e("span",{class:"hr-info-label"},"Tipo Contratto",-1)),e("p",Js,n(b(t.profile.employment_type)),1)])])):i("",!0),t.profile.weekly_hours?(a(),l("div",Ns,[y(x,{name:"clock",size:"md",class:"hr-info-icon"}),e("div",null,[o[13]||(o[13]=e("span",{class:"hr-info-label"},"Ore Settimanali",-1)),e("p",Os,n(t.profile.weekly_hours)+"h",1)])])):i("",!0),t.profile.work_location?(a(),l("div",Us,[y(x,{name:"map-pin",size:"md",class:"hr-info-icon"}),e("div",null,[o[14]||(o[14]=e("span",{class:"hr-info-label"},"Modalità Lavoro",-1)),e("p",Rs,n(h(t.profile.work_location)),1)])])):i("",!0),t.profile.address?(a(),l("div",qs,[y(x,{name:"home",size:"md",class:"hr-info-icon"}),e("div",null,[o[15]||(o[15]=e("span",{class:"hr-info-label"},"Indirizzo",-1)),e("p",Hs,n(t.profile.address),1)])])):i("",!0)]))])):i("",!0)}},sa=J(ta,[["__scopeId","data-v-f35f650b"]]),aa={class:"space-y-6"},la={key:0},ra={class:"flex items-center justify-between mb-4"},oa={class:"text-lg font-medium text-gray-900 dark:text-white"},na={class:"text-sm text-gray-500 dark:text-gray-400"},ia={class:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4"},da=["onClick"],ua={class:"flex items-start justify-between mb-3"},ca={class:"flex-1"},ma={class:"font-medium text-gray-900 dark:text-white mb-1"},ga={class:"text-sm text-gray-500 dark:text-gray-400"},va={class:"space-y-2 text-sm"},ya={key:0,class:"flex items-center text-gray-600 dark:text-gray-400"},pa={key:1,class:"flex items-center text-gray-600 dark:text-gray-400"},xa={key:2,class:"mt-3"},fa={class:"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-1"},ba={class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"},ka={key:1,class:"empty-state"},ha={class:"text-center py-12"},_a={class:"w-16 h-16 mx-auto mb-4 text-gray-300 dark:text-gray-600"},$a={__name:"ProjectsTabContent",props:{projects:{type:Array,default:()=>[]}},emits:["project-click"],setup(t){const b=t,h=T(()=>b.projects.filter(k=>k.status==="active").length),r=k=>k?new Date(k).toLocaleDateString("it-IT",{year:"numeric",month:"long",day:"numeric"}):"",o=k=>({active:"Attivo",completed:"Completato",on_hold:"In Pausa",cancelled:"Annullato"})[k]||k,c=k=>`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ml-2 ${{active:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",completed:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",on_hold:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",cancelled:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"}[k]||"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"}`;return(k,g)=>(a(),l("div",aa,[t.projects.length>0?(a(),l("div",la,[e("div",ra,[e("h3",oa," Progetti Assegnati ("+n(t.projects.length)+") ",1),e("div",na,n(h.value)+" attivi ",1)]),e("div",ia,[(a(!0),l(A,null,E(t.projects,u=>(a(),l("div",{key:u.id,class:"project-card",onClick:d=>k.$emit("project-click",u)},[e("div",ua,[e("div",ca,[e("h4",ma,n(u.name),1),e("p",ga,n(u.role||"Team Member"),1)]),e("span",{class:B(c(u.status))},n(o(u.status)),3)]),e("div",va,[u.client?(a(),l("div",ya,[y(x,{name:"building-office",size:"sm",class:"mr-2"}),P(" "+n(u.client),1)])):i("",!0),u.deadline?(a(),l("div",pa,[y(x,{name:"calendar",size:"sm",class:"mr-2"}),P(" "+n(r(u.deadline)),1)])):i("",!0),u.progress!==void 0?(a(),l("div",xa,[e("div",fa,[g[0]||(g[0]=e("span",null,"Progresso",-1)),e("span",null,n(u.progress)+"%",1)]),e("div",ba,[e("div",{class:"progress-bar h-2 rounded-full transition-all duration-300",style:q({width:u.progress+"%"})},null,4)])])):i("",!0)])],8,da))),128))])])):(a(),l("div",ka,[e("div",ha,[e("div",_a,[y(x,{name:"folder",size:"2xl"})]),g[1]||(g[1]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},"Nessun progetto",-1)),g[2]||(g[2]=e("p",{class:"text-gray-500 dark:text-gray-400"},"L'utente non è assegnato a nessun progetto.",-1))])]))]))}},wa=J($a,[["__scopeId","data-v-2c54eb20"]]),Ca={class:"space-y-6"},ja={key:0},za={class:"flex items-center justify-between mb-4"},La={class:"flex items-center space-x-4"},Pa={class:"text-lg font-medium text-gray-900 dark:text-white"},Ta={class:"text-sm text-gray-500 dark:text-gray-400"},Ia={class:"text-sm text-gray-500 dark:text-gray-400"},Sa={class:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4"},Aa=["onClick"],Ea={class:"flex items-center justify-between mb-2"},Da={class:"font-medium text-gray-900 dark:text-white"},Va={key:0,class:"certification-badge",title:"Competenza certificata"},Ba={key:0,class:"text-sm text-gray-500 dark:text-gray-400 mb-3"},Ma={class:"flex items-center justify-between"},Ja={class:"flex items-center space-x-2"},Na={class:"flex space-x-1"},Oa={class:"text-xs text-gray-500 dark:text-gray-400"},Ua={key:0,class:"text-sm text-gray-500 dark:text-gray-400"},Ra={key:1,class:"mt-3 pt-3 border-t border-gray-100 dark:border-gray-700"},qa={class:"text-xs text-gray-500 dark:text-gray-400 line-clamp-2"},Ha={key:1,class:"empty-state"},Fa={class:"text-center py-12"},Xa={__name:"SkillsTabContent",props:{skills:{type:Array,default:()=>[]},currentPage:{type:Number,default:1},perPage:{type:Number,default:6},canAddSkills:{type:Boolean,default:!1}},emits:["skill-click","page-change","add-skills"],setup(t){const b=t,h=T(()=>{if(!b.skills||b.skills.length===0)return[];const c=(b.currentPage-1)*b.perPage,k=c+b.perPage;return b.skills.slice(c,k)}),r=T(()=>b.skills?Math.ceil(b.skills.length/b.perPage):0),o=T(()=>b.skills?b.skills.filter(c=>c.is_certified||c.certified).length:0);return(c,k)=>(a(),l("div",Ca,[t.skills&&t.skills.length>0?(a(),l("div",ja,[e("div",za,[e("div",La,[e("h3",Pa," Competenze ("+n(t.skills.length)+") ",1),e("span",Ta," Pagina "+n(t.currentPage)+" di "+n(r.value),1)]),e("div",Ia,n(o.value)+" certificate ",1)]),e("div",Sa,[(a(!0),l(A,null,E(h.value,g=>(a(),l("div",{key:g.id,class:"skill-card",onClick:u=>c.$emit("skill-click",g)},[e("div",Ea,[e("h4",Da,n(g.name),1),g.is_certified||g.certified?(a(),l("span",Va," ✓ Certificato ")):i("",!0)]),g.category?(a(),l("p",Ba,n(g.category),1)):i("",!0),e("div",Ma,[e("div",Ja,[k[2]||(k[2]=e("span",{class:"text-sm text-gray-600 dark:text-gray-400"},"Livello:",-1)),e("div",Na,[(a(),l(A,null,E(5,u=>e("div",{key:u,class:B(["skill-dot",u<=g.proficiency_level?"skill-dot-active":"skill-dot-inactive"])},null,2)),64))]),e("span",Oa," ("+n(g.proficiency_level)+"/5) ",1)]),g.years_experience?(a(),l("span",Ua,n(g.years_experience)+n(g.years_experience===1?" anno":" anni"),1)):i("",!0)]),g.description?(a(),l("div",Ra,[e("p",qa,n(g.description),1)])):i("",!0)],8,Aa))),128))]),r.value>1?(a(),N(ve,{key:0,"current-page":t.currentPage,"total-pages":r.value,total:t.skills.length,"per-page":t.perPage,"results-label":"competenze",onPageChange:k[0]||(k[0]=g=>c.$emit("page-change",g)),class:"border-t border-gray-200 dark:border-gray-700 pt-4"},null,8,["current-page","total-pages","total","per-page"])):i("",!0)])):(a(),l("div",Ha,[e("div",Fa,[k[3]||(k[3]=se('<div class="w-16 h-16 mx-auto mb-4 text-gray-300 dark:text-gray-600" data-v-551a480e><svg fill="currentColor" viewBox="0 0 24 24" data-v-551a480e><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" data-v-551a480e></path></svg></div><h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2" data-v-551a480e>Nessuna competenza</h3><p class="text-gray-500 dark:text-gray-400" data-v-551a480e>Non sono state registrate competenze per questo utente.</p>',3)),t.canAddSkills?(a(),l("button",{key:0,onClick:k[1]||(k[1]=g=>c.$emit("add-skills")),class:"mt-4 brand-button"}," Aggiungi Competenze ")):i("",!0)])]))]))}},Qa=J(Xa,[["__scopeId","data-v-551a480e"]]),Wa={class:"space-y-6"},Ga={key:0},Ka={class:"flex items-center justify-between mb-4"},Ya={class:"text-lg font-medium text-gray-900 dark:text-white"},Za={class:"flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400"},el={class:"space-y-3"},tl=["onClick"],sl={class:"flex items-start justify-between"},al={class:"flex-1"},ll={class:"flex items-center space-x-3 mb-2"},rl={class:"font-medium text-gray-900 dark:text-white"},ol={key:0,class:"text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2"},nl={class:"flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400"},il={key:0,class:"flex items-center"},dl={key:1,class:"flex items-center"},ul={key:2,class:"flex items-center"},cl={key:0,class:"ml-4 text-right min-w-0"},ml={class:"text-sm font-medium text-gray-900 dark:text-white mb-1"},gl={class:"w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2"},vl={key:0,class:"mt-3 flex flex-wrap gap-1"},yl={key:0,class:"task-tag-more"},pl={key:1,class:"empty-state"},xl={class:"text-center py-12"},fl={class:"w-16 h-16 mx-auto mb-4 text-gray-300 dark:text-gray-600"},bl={__name:"TasksTabContent",props:{tasks:{type:Array,default:()=>[]}},emits:["task-click"],setup(t){const b=t,h=T(()=>b.tasks.filter(d=>d.status==="done"||d.status==="completed").length),r=T(()=>b.tasks.filter(d=>d.status==="in-progress"||d.status==="in_progress").length),o=d=>d?new Date(d).toLocaleDateString("it-IT",{year:"numeric",month:"short",day:"numeric"}):"",c=d=>({todo:"Da Fare","in-progress":"In Corso",in_progress:"In Corso",review:"In Revisione",done:"Completato",completed:"Completato",blocked:"Bloccato",cancelled:"Annullato"})[d]||d,k=d=>{const $="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium",p={todo:"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200","in-progress":"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",in_progress:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",review:"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",done:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",completed:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",blocked:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",cancelled:"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"};return`${$} ${p[d]||p.todo}`},g=d=>({low:"Bassa",medium:"Media",high:"Alta",urgent:"Urgente"})[d]||d,u=d=>{const $="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium",p={low:"bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-200",medium:"bg-yellow-100 text-yellow-700 dark:bg-yellow-900 dark:text-yellow-200",high:"bg-orange-100 text-orange-700 dark:bg-orange-900 dark:text-orange-200",urgent:"bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-200"};return`${$} ${p[d]||p.medium}`};return(d,$)=>(a(),l("div",Wa,[t.tasks.length>0?(a(),l("div",Ga,[e("div",Ka,[e("h3",Ya," Task Assegnati ("+n(t.tasks.length)+") ",1),e("div",Za,[e("span",null,n(h.value)+" completati",1),e("span",null,n(r.value)+" in corso",1)])]),e("div",el,[(a(!0),l(A,null,E(t.tasks,p=>(a(),l("div",{key:p.id,class:"task-card",onClick:j=>d.$emit("task-click",p)},[e("div",sl,[e("div",al,[e("div",ll,[e("h4",rl,n(p.name),1),e("span",{class:B(k(p.status))},n(c(p.status)),3),p.priority?(a(),l("span",{key:0,class:B(u(p.priority))},n(g(p.priority)),3)):i("",!0)]),p.description?(a(),l("p",ol,n(p.description),1)):i("",!0),e("div",nl,[p.project_name?(a(),l("div",il,[y(x,{name:"folder",size:"sm",class:"mr-1"}),P(" "+n(p.project_name),1)])):i("",!0),p.due_date?(a(),l("div",dl,[y(x,{name:"calendar",size:"sm",class:"mr-1"}),P(" Scadenza: "+n(o(p.due_date)),1)])):i("",!0),p.estimated_hours?(a(),l("div",ul,[y(x,{name:"clock",size:"sm",class:"mr-1"}),P(" "+n(p.estimated_hours)+"h stimati ",1)])):i("",!0)])]),p.progress!==void 0?(a(),l("div",cl,[e("div",ml,n(p.progress)+"% ",1),e("div",gl,[e("div",{class:"progress-bar h-2 rounded-full transition-all duration-300",style:q({width:p.progress+"%"})},null,4)])])):i("",!0)]),p.tags&&p.tags.length>0?(a(),l("div",vl,[(a(!0),l(A,null,E(p.tags.slice(0,3),j=>(a(),l("span",{key:j,class:"task-tag"},n(j),1))),128)),p.tags.length>3?(a(),l("span",yl," +"+n(p.tags.length-3),1)):i("",!0)])):i("",!0)],8,tl))),128))])])):(a(),l("div",pl,[e("div",xl,[e("div",fl,[y(x,{name:"clipboard-list",size:"2xl"})]),$[0]||($[0]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},"Nessun task",-1)),$[1]||($[1]=e("p",{class:"text-gray-500 dark:text-gray-400"},"Non sono stati assegnati task a questo utente.",-1))])]))]))}},kl=J(bl,[["__scopeId","data-v-16747482"]]),hl={class:"personnel-profile"},_l={key:0,class:"flex justify-center items-center h-64"},$l={key:1,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6"},wl={class:"flex"},Cl={class:"text-sm text-red-700 dark:text-red-300 mt-1"},jl={key:2,class:"space-y-6"},zl={class:"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6"},Ll={key:0,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6"},Pl={key:0,class:"text-gray-700 dark:text-gray-300 text-sm leading-relaxed"},Tl={key:3},Bl={__name:"PersonnelProfile",setup(t){const b=oe();ie(),de();const{hasPermission:h}=ce(),r=w(null),o=w([]),c=w([]),k=w(!1),g=w(null),u=w(!1),d=w(!1),$=w("projects"),p=w(1),j=w(6),I=w({phone:"",bio:"",employee_id:"",job_title:"",employment_type:"",work_location:"",weekly_hours:40,address:"",emergency_contact_name:"",emergency_contact_phone:"",emergency_contact_relationship:""}),D=T(()=>{if(!r.value)return!1;try{return h.value&&typeof h.value=="function"?h.value("edit_personnel_data"):!1}catch(s){return console.warn("Permission check failed:",s),!1}});T(()=>{var f;if(!((f=r.value)!=null&&f.skills))return[];const s=(p.value-1)*j.value,m=s+j.value;return r.value.skills.slice(s,m)}),T(()=>{var s;return(s=r.value)!=null&&s.skills?Math.ceil(r.value.skills.length/j.value):0});const O=T(()=>{var s,m;return[{id:"projects",name:"Progetti",count:o.value.length},{id:"tasks",name:"Task",count:c.value.length},{id:"skills",name:"Competenze",count:((m=(s=r.value)==null?void 0:s.skills)==null?void 0:m.length)||0},{id:"cv",name:"CV"}]}),L=s=>{$.value=s,s==="skills"&&(p.value=1)},K=s=>{p.value=s},Y=s=>{console.log("Project clicked:",s)},H=s=>{console.log("Task clicked:",s)},Z=s=>{console.log("Skill clicked:",s)},F=()=>{var s,m,f,z,S,C,_,V,G;r.value&&(I.value={phone:r.value.phone||"",bio:r.value.bio||"",employee_id:((s=r.value.profile)==null?void 0:s.employee_id)||"",job_title:((m=r.value.profile)==null?void 0:m.job_title)||"",employment_type:((f=r.value.profile)==null?void 0:f.employment_type)||"",work_location:((z=r.value.profile)==null?void 0:z.work_location)||"",weekly_hours:((S=r.value.profile)==null?void 0:S.weekly_hours)||40,address:((C=r.value.profile)==null?void 0:C.address)||"",emergency_contact_name:((_=r.value.profile)==null?void 0:_.emergency_contact_name)||"",emergency_contact_phone:((V=r.value.profile)==null?void 0:V.emergency_contact_phone)||"",emergency_contact_relationship:((G=r.value.profile)==null?void 0:G.emergency_contact_relationship)||""})},U=()=>{u.value=!1,F()},X=(s,m)=>{I.value[s]=m},Q=async()=>{if(r.value){d.value=!0;try{const m=(await M.put(`/api/personnel/users/${r.value.id}`,I.value)).data;if(m.success)r.value=m.data.user,u.value=!1,console.log("Profilo aggiornato con successo");else throw new Error(m.message||"Errore durante il salvataggio")}catch(s){console.error("Errore durante il salvataggio:",s),g.value=s.message}finally{d.value=!1}}},R=async s=>{var m,f,z,S;k.value=!0,g.value=null;try{const _=(await M.get(`/api/personnel/users/${s}/profile`)).data;if(console.log("🔍 API Response:",_),console.log("🔍 User data:",_.data),console.log("🔍 Current job level:",(f=(m=_.data)==null?void 0:m.user)==null?void 0:f.current_job_level),console.log("🔍 Job level history:",(S=(z=_.data)==null?void 0:z.user)==null?void 0:S.job_level_history),_.success)r.value=_.data.user,F();else throw new Error(_.message||"Errore nel caricamento del profilo")}catch(C){console.error("Error fetching user profile:",C),g.value=C.message}finally{k.value=!1}},W=async s=>{try{const f=(await M.get(`/api/tasks?assignee_id=${s}&limit=20`)).data;f.success&&(c.value=f.data.tasks||[])}catch(m){console.error("Error fetching user tasks:",m)}},v=async s=>{s?s.id&&s.profile?r.value=s:(s.cv_path&&(r.value.profile.current_cv_path=s.cv_path),s.cv_last_updated!==void 0&&(r.value.profile.cv_last_updated=s.cv_last_updated),s.analysis&&(r.value.profile.cv_analysis_data=JSON.stringify(s.analysis)),s.profile_completion!==void 0&&(r.value.profile.profile_completion=s.profile_completion),s.cv_path===null&&(r.value.profile.current_cv_path=null,r.value.profile.cv_last_updated=null,r.value.profile.cv_analysis_data=null)):await R(r.value.id)};return re(async()=>{const s=b.params.id;if(!s){g.value="ID utente non specificato";return}await R(s),r.value&&(o.value=r.value.projects||[],await W(s))}),te(()=>b.params.id,async s=>{s&&(await R(s),r.value&&(o.value=r.value.projects||[],await W(s)))}),(s,m)=>(a(),l("div",hl,[k.value?(a(),l("div",_l,m[4]||(m[4]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):g.value?(a(),l("div",$l,[e("div",wl,[y(x,{name:"x-circle",size:"md",class:"text-red-400 mr-2 mt-0.5"}),e("div",null,[m[5]||(m[5]=e("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"},"Errore nel caricamento del profilo",-1)),e("p",Cl,n(g.value),1)])])])):r.value?(a(),l("div",jl,[y(as,{user:r.value,"can-edit":D.value,"edit-mode":u.value,onToggleEdit:m[0]||(m[0]=f=>u.value=!u.value)},null,8,["user","can-edit","edit-mode"]),e("div",zl,[y(bs,{user:r.value,"edit-mode":u.value,"edit-data":I.value,"can-edit":D.value,saving:d.value,onToggleEdit:m[1]||(m[1]=f=>u.value=!u.value),onUpdateField:X,onSave:Q,onCancel:U},null,8,["user","edit-mode","edit-data","can-edit","saving"]),y(Ts,{skills:r.value.skills,"max-skills":4,"show-view-all-button":!0,onViewAll:m[2]||(m[2]=f=>L("skills"))},null,8,["skills"]),r.value.current_job_level||r.value.job_level_history?(a(),N(Jt,{key:0,"current-job-level":r.value.current_job_level,"job-level-history":r.value.job_level_history,"show-history":!1,class:"h-full"},null,8,["current-job-level","job-level-history"])):i("",!0)]),y(sa,{profile:r.value.profile,"edit-mode":u.value,"edit-data":I.value,"can-edit":D.value,saving:d.value,onToggleEdit:m[3]||(m[3]=f=>u.value=!u.value),onUpdateField:X,onSave:Q,onCancel:U},null,8,["profile","edit-mode","edit-data","can-edit","saving"]),r.value.bio||u.value?(a(),l("div",Ll,[m[6]||(m[6]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Bio",-1)),u.value?i("",!0):(a(),l("p",Pl,n(r.value.bio||"Nessuna bio disponibile"),1))])):i("",!0),y(ge,{tabs:O.value,"active-tab":$.value,onTabChange:L},{default:ne(()=>[$.value==="projects"?(a(),N(wa,{key:0,projects:o.value,onProjectClick:Y},null,8,["projects"])):i("",!0),$.value==="tasks"?(a(),N(kl,{key:1,tasks:c.value,onTaskClick:H},null,8,["tasks"])):i("",!0),$.value==="skills"?(a(),N(Qa,{key:2,skills:r.value.skills,"current-page":p.value,"per-page":j.value,onSkillClick:Z,onPageChange:K},null,8,["skills","current-page","per-page"])):i("",!0),$.value==="cv"?(a(),l("div",Tl,[y(rt,{user:r.value,"can-edit":D.value,onUserUpdated:v},null,8,["user","can-edit"])])):i("",!0)]),_:1},8,["tabs","active-tab"])])):i("",!0)]))}};export{Bl as default};
