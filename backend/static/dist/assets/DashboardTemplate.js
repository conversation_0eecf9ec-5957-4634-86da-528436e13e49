import{_ as T,H as m}from"./app.js";import{c as z,b as s,l as t,g as o,t as i,G as l,F as g,q as u,e as c,v as b,n as y,p as k,h as A,j as r,E as j}from"./vendor.js";const D={class:"py-6"},B={class:"flex flex-col md:flex-row md:items-center md:justify-between mb-8"},L={class:"text-2xl font-bold text-gray-900 dark:text-white"},E={key:0,class:"mt-1 text-sm text-gray-600 dark:text-gray-400"},M={class:"mt-4 md:mt-0 flex space-x-3"},P={key:0,class:"relative"},V=["value"],N=["value"],R=["disabled"],G={key:0,class:"mb-6 bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800 rounded-md p-4"},H={class:"flex"},O={class:"flex-shrink-0"},U={class:"ml-3"},q={class:"text-sm text-red-800 dark:text-red-200"},F={class:"ml-auto pl-3"},K={class:"-mx-1.5 -my-1.5"},J={class:"p-5"},Q={class:"flex items-center"},W={class:"flex-shrink-0"},X={class:"ml-5 w-0 flex-1"},Y={class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"},Z={class:"text-lg font-medium text-gray-900 dark:text-white"},ee={key:0,class:"text-xs text-gray-500 dark:text-gray-400"},te={key:0,class:"bg-gray-50 dark:bg-gray-700 px-5 py-3"},se={class:"text-sm"},re={key:2,class:"mb-8"},ae={key:3,class:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8"},de={class:"flex items-center justify-between mb-4"},ie={class:"text-lg font-medium text-gray-900 dark:text-white"},ne={key:0,class:"flex space-x-2"},oe=["onClick"],le={class:"relative h-64"},ce={class:"flex items-center justify-center h-full text-gray-500 dark:text-gray-400"},ge={class:"text-center"},ue={class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},me={key:0,class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},ye={class:"p-6"},he={class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},xe={key:0,class:"text-center py-8 text-gray-500 dark:text-gray-400"},be={key:1,class:"space-y-4"},fe={class:"flex justify-between items-start"},ke={class:"flex-1"},ve={class:"text-sm font-medium text-gray-900 dark:text-white"},_e={key:0,class:"text-xs text-gray-500 dark:text-gray-400"},pe={key:0,class:"mt-2"},we={class:"text-xs text-gray-500 dark:text-gray-400"},Ie={key:0,class:"bg-gray-50 dark:bg-gray-700 px-6 py-3"},Se={class:"text-sm"},$e={key:1,class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Ce={class:"p-6"},Te={class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},ze={key:0,class:"text-center py-8 text-gray-500 dark:text-gray-400"},Ae={key:1,class:"space-y-4"},je={class:"flex-shrink-0"},De={class:"flex-1 min-w-0"},Be={class:"text-sm font-medium text-gray-900 dark:text-white"},Le={key:0,class:"text-xs text-gray-500 dark:text-gray-400"},Ee={key:1,class:"text-xs text-gray-400 dark:text-gray-500"},Me={key:2,class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Pe={class:"p-6"},Ve={key:0,class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},Ne={key:1,class:"space-y-4"},Re={class:"flex justify-between items-start"},Ge={class:"flex-1"},He={class:"text-sm font-medium text-gray-900 dark:text-white"},Oe={key:0,class:"text-xs text-gray-500 dark:text-gray-400"},Ue={class:"text-right"},qe={class:"text-sm font-bold text-gray-900 dark:text-white"},Fe={key:0,class:"text-xs text-gray-500"},Ke={key:0,class:"mt-2"},Je={class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"},Qe={class:"text-xs text-gray-500 dark:text-gray-400 mt-1"},We={key:4,class:"mt-8"},Xe={__name:"DashboardTemplate",props:{title:{type:String,required:!0},subtitle:{type:String,default:""},loading:{type:Boolean,default:!1},error:{type:String,default:""},stats:{type:Array,default:()=>[]},charts:{type:Array,default:()=>[]},recentItems:{type:Array,default:()=>[]},activities:{type:Array,default:()=>[]},kpis:{type:Array,default:()=>[]},showPeriodSelector:{type:Boolean,default:!0},selectedPeriod:{type:String,default:"7"},periodOptions:{type:Array,default:()=>[{value:"7",label:"Ultimi 7 giorni"},{value:"30",label:"Ultimo mese"},{value:"90",label:"Ultimi 3 mesi"}]},showRefreshButton:{type:Boolean,default:!0},recentItemsTitle:{type:String,default:"Elementi Recenti"},recentItemsEmptyMessage:{type:String,default:"Nessun elemento recente"},recentItemsLink:{type:String,default:""},recentItemsLinkText:{type:String,default:"Vedi tutti"},activitiesTitle:{type:String,default:"Attività Recenti"},activitiesEmptyMessage:{type:String,default:"Nessuna attività recente"},kpisTitle:{type:String,default:"KPIs Principali"}},emits:["refresh","period-change","clear-error","chart-action"],setup(d){const v=d,_=z(()=>{var n;const a=((n=v.stats)==null?void 0:n.length)||0;return a<=3?"lg:grid-cols-3":"lg:grid-cols-4"}),p=a=>{const n={primary:"bg-brand-primary-500",secondary:"bg-gray-500",blue:"bg-blue-500",green:"bg-green-500",emerald:"bg-emerald-500",yellow:"bg-yellow-500",orange:"bg-orange-500",red:"bg-red-500",purple:"bg-purple-500",indigo:"bg-indigo-500"};return n[a]||n.primary},w=a=>typeof a=="number"?a.toLocaleString("it-IT"):a,I=a=>{const n={pending:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",active:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",completed:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",cancelled:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",draft:"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"};return n[a]||n.pending},S=a=>{const n={project:"bg-blue-500",task:"bg-green-500",user:"bg-purple-500",system:"bg-gray-500",timesheet:"bg-indigo-500",document:"bg-yellow-500"};return n[a]||n.system},$=a=>a>=90?"bg-green-500":a>=70?"bg-yellow-500":"bg-red-500",f=a=>a?new Date(a).toLocaleDateString("it-IT",{year:"numeric",month:"short",day:"numeric"}):"",C=a=>{if(!a)return"";const n=new Date(a),e=Math.floor((new Date-n)/(1e3*60*60));return e<1?"Ora":e<24?`${e}h fa`:e<48?"Ieri":f(a)};return(a,n)=>{const h=A("router-link");return r(),s("div",D,[t("div",B,[t("div",null,[t("h1",L,i(d.title),1),d.subtitle?(r(),s("p",E,i(d.subtitle),1)):o("",!0)]),t("div",M,[d.showPeriodSelector?(r(),s("div",P,[t("select",{value:d.selectedPeriod,onChange:n[0]||(n[0]=e=>a.$emit("period-change",e.target.value)),class:"bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md py-2 pl-3 pr-10 text-sm font-medium text-gray-700 dark:text-gray-300 focus:outline-none focus:ring-brand-primary-500 focus:border-brand-primary-500"},[(r(!0),s(g,null,u(d.periodOptions,e=>(r(),s("option",{key:e.value,value:e.value},i(e.label),9,N))),128))],40,V)])):o("",!0),l(a.$slots,"header-actions",{},void 0,!0),d.showRefreshButton?(r(),s("button",{key:1,onClick:n[1]||(n[1]=e=>a.$emit("refresh")),disabled:d.loading,type:"button",class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-brand-primary-600 hover:bg-brand-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-primary-500 disabled:opacity-50"},[c(m,{name:"arrow-path",size:"sm",class:y([{"animate-spin":d.loading},"mr-2"])},null,8,["class"]),b(" "+i(d.loading?"Aggiornamento...":"Aggiorna"),1)],8,R)):o("",!0)])]),d.error?(r(),s("div",G,[t("div",H,[t("div",O,[c(m,{name:"exclamation-triangle",size:"md",color:"text-red-400"})]),t("div",U,[t("p",q,i(d.error),1)]),t("div",F,[t("div",K,[t("button",{onClick:n[2]||(n[2]=e=>a.$emit("clear-error")),class:"inline-flex bg-red-50 dark:bg-red-900/50 rounded-md p-1.5 text-red-500 hover:bg-red-100 dark:hover:bg-red-900"},[c(m,{name:"x-mark",size:"sm"})])])])])])):o("",!0),d.stats.length>0?(r(),s("div",{key:1,class:y(["grid grid-cols-1 sm:grid-cols-2 gap-6 mb-8",_.value])},[(r(!0),s(g,null,u(d.stats,e=>(r(),s("div",{key:e.id||e.title,class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},[t("div",J,[t("div",Q,[t("div",W,[t("div",{class:y(["w-8 h-8 rounded-md flex items-center justify-center",p(e.color)])},[c(m,{name:e.icon,size:"md",color:"text-white"},null,8,["name"])],2)]),t("div",X,[t("dl",null,[t("dt",Y,i(e.title),1),t("dd",Z,i(w(e.value)),1),e.subtitle?(r(),s("dd",ee,i(e.subtitle),1)):o("",!0)])])])]),e.link?(r(),s("div",te,[t("div",se,[c(h,{to:e.link,class:"font-medium text-brand-primary-600 dark:text-brand-primary-400 hover:text-brand-primary-500"},{default:k(()=>n[3]||(n[3]=[b(" Visualizza dettagli ")])),_:2,__:[3]},1032,["to"])])])):o("",!0)]))),128))],2)):o("",!0),a.$slots.widget?(r(),s("div",re,[l(a.$slots,"widget",{},void 0,!0)])):o("",!0),d.charts.length>0?(r(),s("div",ae,[(r(!0),s(g,null,u(d.charts,e=>(r(),s("div",{key:e.id||e.title,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},[t("div",de,[t("h2",ie,i(e.title),1),e.actions?(r(),s("div",ne,[(r(!0),s(g,null,u(e.actions,x=>(r(),s("button",{key:x.id,onClick:Ye=>a.$emit("chart-action",e.id,x.id),class:"text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"},i(x.label),9,oe))),128))])):o("",!0)]),t("div",le,[l(a.$slots,`chart-${e.id}`,{chart:e},()=>[t("div",ce,[t("div",ge,[c(m,{name:"chart-bar",size:"xl",class:"mx-auto mb-2"}),n[4]||(n[4]=t("p",{class:"text-sm"},"Grafico non disponibile",-1))])])],!0)])]))),128))])):o("",!0),t("div",ue,[d.recentItems.length>0?(r(),s("div",me,[t("div",ye,[t("h2",he,i(d.recentItemsTitle),1),d.recentItems.length===0?(r(),s("div",xe,i(d.recentItemsEmptyMessage),1)):(r(),s("div",be,[(r(!0),s(g,null,u(d.recentItems,e=>(r(),s("div",{key:e.id,class:"border-b border-gray-200 dark:border-gray-700 pb-4 last:border-b-0 last:pb-0"},[l(a.$slots,"recent-item",{item:e},()=>[t("div",fe,[t("div",ke,[t("h3",ve,i(e.title),1),e.description?(r(),s("p",_e,i(e.description),1)):o("",!0)]),e.status?(r(),s("span",{key:0,class:y(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",I(e.status)])},i(e.status),3)):o("",!0)]),e.date?(r(),s("div",pe,[t("span",we,i(f(e.date)),1)])):o("",!0)],!0)]))),128))]))]),d.recentItemsLink?(r(),s("div",Ie,[t("div",Se,[c(h,{to:d.recentItemsLink,class:"font-medium text-brand-primary-600 dark:text-brand-primary-400 hover:text-brand-primary-500"},{default:k(()=>[b(i(d.recentItemsLinkText),1)]),_:1},8,["to"])])])):o("",!0)])):o("",!0),d.activities.length>0?(r(),s("div",$e,[t("div",Ce,[t("h2",Te,i(d.activitiesTitle),1),d.activities.length===0?(r(),s("div",ze,i(d.activitiesEmptyMessage),1)):(r(),s("div",Ae,[(r(!0),s(g,null,u(d.activities,e=>(r(),s("div",{key:e.id,class:"flex items-start space-x-3"},[t("div",je,[t("div",{class:y(["w-8 h-8 rounded-full flex items-center justify-center",S(e.type)])},[c(m,{name:e.icon||"clock",size:"sm",color:"text-white"},null,8,["name"])],2)]),t("div",De,[l(a.$slots,"activity-item",{activity:e},()=>[t("p",Be,i(e.title),1),e.description?(r(),s("p",Le,i(e.description),1)):o("",!0),e.timestamp?(r(),s("p",Ee,i(C(e.timestamp)),1)):o("",!0)],!0)])]))),128))]))])])):o("",!0),d.kpis.length>0||a.$slots.sidebar?(r(),s("div",Me,[t("div",Pe,[d.kpis.length>0?(r(),s("h2",Ve,i(d.kpisTitle),1)):o("",!0),d.kpis.length>0?(r(),s("div",Ne,[(r(!0),s(g,null,u(d.kpis,e=>(r(),s("div",{key:e.id,class:"border-b border-gray-200 dark:border-gray-700 pb-4 last:border-b-0 last:pb-0"},[l(a.$slots,"kpi-item",{kpi:e},()=>[t("div",Re,[t("div",Ge,[t("h3",He,i(e.name),1),e.description?(r(),s("p",Oe,i(e.description),1)):o("",!0)]),t("div",Ue,[t("p",qe,i(e.current_value)+i(e.unit),1),e.target_value?(r(),s("p",Fe," Target: "+i(e.target_value)+i(e.unit),1)):o("",!0)])]),e.performance_percentage!==void 0?(r(),s("div",Ke,[t("div",Je,[t("div",{class:y(["h-2 rounded-full",$(e.performance_percentage)]),style:j({width:Math.min(e.performance_percentage,100)+"%"})},null,6)]),t("p",Qe,i(Math.round(e.performance_percentage))+"% del target",1)])):o("",!0)],!0)]))),128))])):o("",!0),l(a.$slots,"sidebar",{},void 0,!0)])])):o("",!0)]),a.$slots.footer?(r(),s("div",We,[l(a.$slots,"footer",{},void 0,!0)])):o("",!0)])}}},tt=T(Xe,[["__scopeId","data-v-fb28955b"]]);export{tt as D};
