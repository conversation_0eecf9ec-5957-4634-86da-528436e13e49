import{r as p,c as v,o as U,b as c,l as o,A as C,t as u,B as i,C as n,P,j as y}from"./vendor.js";import{b as V}from"./app.js";const S={class:"max-w-4xl mx-auto"},T={class:"bg-white shadow rounded-lg"},j={key:0,class:"flex items-center justify-center h-64"},E={class:"flex items-center space-x-6"},A={class:"flex-shrink-0"},M={class:"h-20 w-20 rounded-full bg-primary-100 flex items-center justify-center"},R={class:"text-2xl font-medium text-primary-700"},B={class:"flex-1"},N={class:"text-lg font-medium text-gray-900"},D={class:"text-sm text-gray-500"},F={class:"text-sm text-gray-500"},z={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},I={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},X={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},q={class:"border-t border-gray-200 pt-6"},G={class:"flex items-center"},J={class:"flex justify-end space-x-3 pt-6 border-t border-gray-200"},L=["disabled"],Q={__name:"Profile",setup(O){const d=V(),b=p(!0),m=p(!1),t=p(null),s=p({first_name:"",last_name:"",email:"",phone:"",position:"",department:"",bio:"",dark_mode:!1}),x=v(()=>{var l;if(!t.value)return"U";const a=t.value.first_name||"",e=t.value.last_name||"";return(a.charAt(0)+e.charAt(0)).toUpperCase()||((l=t.value.username)==null?void 0:l.charAt(0).toUpperCase())||"U"}),k=v(()=>{var e,l;return{admin:"Amministratore",manager:"Manager",employee:"Dipendente",human_resources:"Risorse Umane",sales:"Vendite"}[(e=t.value)==null?void 0:e.role]||((l=t.value)==null?void 0:l.role)||"Utente"}),w=async()=>{try{if(d.user)t.value=d.user,f();else{const a=await fetch("/api/auth/profile",{headers:{"Content-Type":"application/json","X-CSRFToken":d.csrfToken}});if(a.ok){const e=await a.json();t.value=e.data.user,f()}}}catch(a){console.error("Error loading profile:",a)}finally{b.value=!1}},f=()=>{t.value&&(s.value={first_name:t.value.first_name||"",last_name:t.value.last_name||"",email:t.value.email||"",phone:t.value.phone||"",position:t.value.position||"",department:t.value.department||"",bio:t.value.bio||"",dark_mode:t.value.dark_mode||!1})},h=()=>{f()},_=async()=>{m.value=!0;try{const a=await fetch("/api/auth/profile",{method:"PUT",headers:{"Content-Type":"application/json","X-CSRFToken":d.csrfToken},body:JSON.stringify(s.value)});if(!a.ok)throw new Error("Errore nel salvataggio del profilo");const e=await a.json();t.value=e.data.user,await d.refreshUser(),alert("Profilo aggiornato con successo!")}catch(a){console.error("Error saving profile:",a),alert("Errore nel salvataggio del profilo")}finally{m.value=!1}};return U(()=>{w()}),(a,e)=>{var l,g;return y(),c("div",S,[o("div",T,[e[18]||(e[18]=o("div",{class:"px-6 py-4 border-b border-gray-200"},[o("h1",{class:"text-xl font-semibold text-gray-900"},"Il tuo Profilo"),o("p",{class:"mt-1 text-sm text-gray-600"},"Gestisci le informazioni del tuo account")],-1)),b.value?(y(),c("div",j,e[8]||(e[8]=[o("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1)]))):(y(),c("form",{key:1,onSubmit:C(_,["prevent"]),class:"p-6 space-y-6"},[o("div",E,[o("div",A,[o("div",M,[o("span",R,u(x.value),1)])]),o("div",B,[o("h3",N,u((l=t.value)==null?void 0:l.username),1),o("p",D,u((g=t.value)==null?void 0:g.email),1),o("p",F,"Ruolo: "+u(k.value),1)])]),o("div",z,[o("div",null,[e[9]||(e[9]=o("label",{for:"first_name",class:"block text-sm font-medium text-gray-700"},"Nome",-1)),i(o("input",{id:"first_name","onUpdate:modelValue":e[0]||(e[0]=r=>s.value.first_name=r),type:"text",class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[n,s.value.first_name]])]),o("div",null,[e[10]||(e[10]=o("label",{for:"last_name",class:"block text-sm font-medium text-gray-700"},"Cognome",-1)),i(o("input",{id:"last_name","onUpdate:modelValue":e[1]||(e[1]=r=>s.value.last_name=r),type:"text",class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[n,s.value.last_name]])])]),o("div",I,[o("div",null,[e[11]||(e[11]=o("label",{for:"email",class:"block text-sm font-medium text-gray-700"},"Email",-1)),i(o("input",{id:"email","onUpdate:modelValue":e[2]||(e[2]=r=>s.value.email=r),type:"email",class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[n,s.value.email]])]),o("div",null,[e[12]||(e[12]=o("label",{for:"phone",class:"block text-sm font-medium text-gray-700"},"Telefono",-1)),i(o("input",{id:"phone","onUpdate:modelValue":e[3]||(e[3]=r=>s.value.phone=r),type:"tel",class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[n,s.value.phone]])])]),o("div",X,[o("div",null,[e[13]||(e[13]=o("label",{for:"position",class:"block text-sm font-medium text-gray-700"},"Posizione",-1)),i(o("input",{id:"position","onUpdate:modelValue":e[4]||(e[4]=r=>s.value.position=r),type:"text",class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[n,s.value.position]])]),o("div",null,[e[14]||(e[14]=o("label",{for:"department",class:"block text-sm font-medium text-gray-700"},"Dipartimento",-1)),i(o("input",{id:"department","onUpdate:modelValue":e[5]||(e[5]=r=>s.value.department=r),type:"text",readonly:"",class:"mt-1 block w-full rounded-md border-gray-300 bg-gray-50 shadow-sm"},null,512),[[n,s.value.department]])])]),o("div",null,[e[15]||(e[15]=o("label",{for:"bio",class:"block text-sm font-medium text-gray-700"},"Bio",-1)),i(o("textarea",{id:"bio","onUpdate:modelValue":e[6]||(e[6]=r=>s.value.bio=r),rows:"3",class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500",placeholder:"Racconta qualcosa di te..."},null,512),[[n,s.value.bio]])]),o("div",q,[e[17]||(e[17]=o("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Preferenze",-1)),o("div",G,[i(o("input",{id:"dark_mode","onUpdate:modelValue":e[7]||(e[7]=r=>s.value.dark_mode=r),type:"checkbox",class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,512),[[P,s.value.dark_mode]]),e[16]||(e[16]=o("label",{for:"dark_mode",class:"ml-2 block text-sm text-gray-900"}," Modalità scura ",-1))])]),o("div",J,[o("button",{type:"button",onClick:h,class:"px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"}," Ripristina "),o("button",{type:"submit",disabled:m.value,class:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"},u(m.value?"Salvataggio...":"Salva Profilo"),9,L)])],32))])])}}};export{Q as default};
