import{b as d,j as n,l as t,t as u,e as g,h as j,A as V,B as h,C as M,g as f,F as E,q as U,v as z,S as F,P as I,f as O,r as C,c as S,o as ot,p as N,x as D,s as it,E as lt}from"./vendor.js";import{_ as B,f as nt,H as x,d as L,b as at}from"./app.js";import{u as rt}from"./useFormatters.js";import{_ as dt}from"./ListPageTemplate.js";import{S as ut}from"./StatusBadge.js";import{E as mt}from"./EditPollModal.js";import{C as ct}from"./ConfirmationModal.js";import"./formatters.js";import"./Pagination.js";const gt={name:"CreatePollModal",components:{HeroIcon:x,LoadingSpinner:nt},props:{poll:{type:Object,default:null}},emits:["close","created","updated"],data(){return{isSubmitting:!1,form:{title:"",description:"",options:[{text:""},{text:""}],endDate:"",multipleChoice:!1,anonymous:!1,showResults:!0,allowComments:!0}}},computed:{isEditing(){return!!this.poll},isFormValid(){const m=this.form.title.trim().length>0,e=this.form.options.length>=2&&this.form.options.every(y=>y.text.trim().length>0),c=!this.form.endDate||new Date(this.form.endDate)>new Date;return m&&e&&c},minDateTime(){const m=new Date;return m.setMinutes(m.getMinutes()+30),m.toISOString().slice(0,16)}},watch:{poll:{handler(){this.initializeForm()},immediate:!0}},methods:{initializeForm(){var m;this.isEditing&&this.poll?this.form={title:this.poll.title||"",description:this.poll.description||"",options:((m=this.poll.options)==null?void 0:m.map(e=>({text:e.text||e})))||[{text:""},{text:""}],endDate:this.poll.endDate?new Date(this.poll.endDate).toISOString().slice(0,16):"",multipleChoice:this.poll.multipleChoice||!1,anonymous:this.poll.anonymous||!1,showResults:this.poll.showResults!==void 0?this.poll.showResults:!0,allowComments:this.poll.allowComments!==void 0?this.poll.allowComments:!0}:this.form={title:"",description:"",options:[{text:""},{text:""}],endDate:"",multipleChoice:!1,anonymous:!1,showResults:!0,allowComments:!0}},addOption(){this.form.options.length<10&&this.form.options.push({text:""})},removeOption(m){this.form.options.length>2&&this.form.options.splice(m,1)},async submitPoll(){if(!(!this.isFormValid||this.isSubmitting)){this.isSubmitting=!0;try{const m={...this.form,options:this.form.options.map(y=>y.text.trim()),endDate:this.form.endDate?new Date(this.form.endDate).toISOString():null},c=await L().createPoll(m);this.$emit("created",c),this.closeModal()}catch(m){console.error("Errore durante il salvataggio del sondaggio:",m)}finally{this.isSubmitting=!1}}},closeModal(){this.$emit("close"),setTimeout(()=>{this.initializeForm(),this.isSubmitting=!1},300)}}},xt={class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},ft={class:"card w-full max-w-3xl mx-4 max-h-[90vh] overflow-y-auto"},pt={class:"flex items-center justify-between p-6 border-b dark:border-gray-600"},ht={class:"text-lg font-semibold text-gray-900 dark:text-white"},vt={class:"text-right text-xs text-gray-500 dark:text-gray-400 mt-1"},yt={class:"text-right text-xs text-gray-500 dark:text-gray-400 mt-1"},bt={class:"space-y-3"},_t={class:"flex-1"},kt=["onUpdate:modelValue","placeholder"],wt=["onClick"],Ct={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},St=["min"],zt={class:"space-y-3"},Dt={class:"flex items-center"},Pt={class:"flex items-center"},Mt={class:"flex items-center"},Vt={class:"flex justify-end space-x-3 pt-4 border-t dark:border-gray-600"},Et=["disabled"];function Ot(m,e,c,y,l,r){const v=j("HeroIcon"),k=j("LoadingSpinner");return n(),d("div",xt,[t("div",ft,[t("div",pt,[t("h3",ht,u(r.isEditing?"Modifica Sondaggio":"Nuovo Sondaggio"),1),t("button",{onClick:e[0]||(e[0]=(...i)=>r.closeModal&&r.closeModal(...i)),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"},[g(v,{name:"x-mark",size:"md"})])]),t("form",{onSubmit:e[10]||(e[10]=V((...i)=>r.submitPoll&&r.submitPoll(...i),["prevent"])),class:"p-6 space-y-6"},[t("div",null,[e[11]||(e[11]=t("label",{for:"title",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Titolo * ",-1)),h(t("input",{id:"title","onUpdate:modelValue":e[1]||(e[1]=i=>l.form.title=i),type:"text",required:"",maxlength:"200",class:"input w-full",placeholder:"Inserisci il titolo del sondaggio"},null,512),[[M,l.form.title]]),t("div",vt,u(l.form.title.length)+"/200 ",1)]),t("div",null,[e[12]||(e[12]=t("label",{for:"description",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Descrizione ",-1)),h(t("textarea",{id:"description","onUpdate:modelValue":e[2]||(e[2]=i=>l.form.description=i),rows:"3",maxlength:"1000",class:"input-field resize-vertical",placeholder:"Descrizione opzionale del sondaggio"},null,512),[[M,l.form.description]]),t("div",yt,u(l.form.description.length)+"/1000 ",1)]),t("div",null,[e[14]||(e[14]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3"}," Opzioni di risposta * ",-1)),t("div",bt,[(n(!0),d(E,null,U(l.form.options,(i,p)=>(n(),d("div",{key:p,class:"flex items-center space-x-3"},[t("div",_t,[h(t("input",{"onUpdate:modelValue":P=>i.text=P,type:"text",placeholder:`Opzione ${p+1}`,maxlength:"200",class:"input w-full",required:""},null,8,kt),[[M,i.text]])]),l.form.options.length>2?(n(),d("button",{key:0,type:"button",onClick:P=>r.removeOption(p),class:"text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 p-1"},[g(v,{name:"trash",size:"sm"})],8,wt)):f("",!0)]))),128))]),l.form.options.length<10?(n(),d("button",{key:0,type:"button",onClick:e[3]||(e[3]=(...i)=>r.addOption&&r.addOption(...i)),class:"btn-secondary mt-3"},[g(v,{name:"plus",size:"sm",class:"mr-2"}),e[13]||(e[13]=z(" Aggiungi opzione "))])):f("",!0),e[15]||(e[15]=t("div",{class:"text-xs text-gray-500 dark:text-gray-400 mt-2"}," Minimo 2 opzioni, massimo 10 ",-1))]),t("div",Ct,[t("div",null,[e[16]||(e[16]=t("label",{for:"endDate",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Data di scadenza ",-1)),h(t("input",{id:"endDate","onUpdate:modelValue":e[4]||(e[4]=i=>l.form.endDate=i),type:"datetime-local",min:r.minDateTime,class:"input w-full"},null,8,St),[[M,l.form.endDate]]),e[17]||(e[17]=t("div",{class:"text-xs text-gray-500 dark:text-gray-400 mt-1"}," Lascia vuoto per sondaggio senza scadenza ",-1))]),t("div",null,[e[19]||(e[19]=t("label",{for:"multipleChoice",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Tipo di voto ",-1)),h(t("select",{id:"multipleChoice","onUpdate:modelValue":e[5]||(e[5]=i=>l.form.multipleChoice=i),class:"input w-full"},e[18]||(e[18]=[t("option",{value:!1},"Scelta singola",-1),t("option",{value:!0},"Scelta multipla",-1)]),512),[[F,l.form.multipleChoice]])])]),t("div",zt,[e[23]||(e[23]=t("h4",{class:"text-sm font-medium text-gray-700 dark:text-gray-300"},"Opzioni avanzate",-1)),t("div",Dt,[h(t("input",{id:"anonymous","onUpdate:modelValue":e[6]||(e[6]=i=>l.form.anonymous=i),type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded dark:border-gray-600 dark:bg-gray-700"},null,512),[[I,l.form.anonymous]]),e[20]||(e[20]=t("label",{for:"anonymous",class:"ml-2 block text-sm text-gray-700 dark:text-gray-300"}," Voto anonimo ",-1))]),t("div",Pt,[h(t("input",{id:"showResults","onUpdate:modelValue":e[7]||(e[7]=i=>l.form.showResults=i),type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded dark:border-gray-600 dark:bg-gray-700"},null,512),[[I,l.form.showResults]]),e[21]||(e[21]=t("label",{for:"showResults",class:"ml-2 block text-sm text-gray-700 dark:text-gray-300"}," Mostra risultati in tempo reale ",-1))]),t("div",Mt,[h(t("input",{id:"allowComments","onUpdate:modelValue":e[8]||(e[8]=i=>l.form.allowComments=i),type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded dark:border-gray-600 dark:bg-gray-700"},null,512),[[I,l.form.allowComments]]),e[22]||(e[22]=t("label",{for:"allowComments",class:"ml-2 block text-sm text-gray-700 dark:text-gray-300"}," Permetti commenti ",-1))])]),t("div",Vt,[t("button",{type:"button",onClick:e[9]||(e[9]=(...i)=>r.closeModal&&r.closeModal(...i)),class:"btn-secondary"}," Annulla "),t("button",{type:"submit",disabled:l.isSubmitting||!r.isFormValid,class:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed flex items-center"},[l.isSubmitting?(n(),O(k,{key:0,class:"mr-2",size:"sm"})):f("",!0),z(" "+u(l.isSubmitting?"Salvataggio...":r.isEditing?"Aggiorna":"Crea Sondaggio"),1)],8,Et)])],32)])])}const It=B(gt,[["render",Ot],["__scopeId","data-v-b90bd55f"]]),Nt={class:"grid grid-cols-1 gap-5 sm:grid-cols-4"},Ut={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},At={class:"p-5"},Rt={class:"flex items-center"},Tt={class:"flex-shrink-0"},jt={class:"ml-5 w-0 flex-1"},Ft={class:"text-lg font-medium text-gray-900 dark:text-white"},Bt={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},Lt={class:"p-5"},$t={class:"flex items-center"},Ht={class:"flex-shrink-0"},qt={class:"ml-5 w-0 flex-1"},Gt={class:"text-lg font-medium text-gray-900 dark:text-white"},Jt={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},Kt={class:"p-5"},Qt={class:"flex items-center"},Wt={class:"flex-shrink-0"},Xt={class:"ml-5 w-0 flex-1"},Yt={class:"text-lg font-medium text-gray-900 dark:text-white"},Zt={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},te={class:"p-5"},ee={class:"flex items-center"},se={class:"flex-shrink-0"},oe={class:"ml-5 w-0 flex-1"},ie={class:"text-lg font-medium text-gray-900 dark:text-white"},le={class:"flex space-x-4"},ne={key:0,class:"flex justify-center items-center h-64"},ae={key:1,class:"text-center py-12"},re={key:2,class:"p-6"},de={class:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6"},ue=["onClick"],me={class:"p-6"},ce={class:"flex items-start justify-between mb-3"},ge={class:"text-lg font-semibold text-gray-900 dark:text-white line-clamp-2"},xe={class:"text-gray-600 dark:text-gray-400 text-sm line-clamp-2 mb-4"},fe={class:"mb-4"},pe={class:"flex justify-between text-sm text-gray-500 dark:text-gray-400 mb-2"},he={key:0},ve={class:"w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2"},ye={class:"space-y-2 mb-4"},be={class:"text-gray-600 dark:text-gray-400 truncate"},_e={class:"text-gray-500"},ke={key:0,class:"text-xs text-gray-400"},we={class:"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-4"},Ce={class:"flex items-center space-x-2"},Se={class:"flex justify-between items-center"},ze={class:"flex space-x-2"},De=["onClick"],Pe={key:1,class:"inline-flex items-center px-3 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full"},Me={class:"flex space-x-2"},Ve=["onClick"],Ee=["onClick"],Oe={__name:"PollsIndex",setup(m){const e=it(),c=L(),y=at(),{formatDate:l}=rt(),r=C(!1),v=C(!1);C(!1);const k=C(!1),i=C(null),p=C(""),P=S(()=>y.hasPermission("PERMISSION_CREATE_POLLS")),$=S(()=>y.hasPermission("PERMISSION_MANAGE_COMMUNICATION")),H=S(()=>c.polls.filter(o=>b(o)==="active").length),q=S(()=>c.polls.filter(o=>b(o)==="closed").length),G=S(()=>c.polls.reduce((o,s)=>o+(s.total_votes||0),0)),A=S(()=>{let o=c.polls;return p.value==="active"?o=o.filter(s=>b(s)==="active"):p.value==="closed"?o=o.filter(s=>b(s)==="closed"):p.value==="draft"&&(o=o.filter(s=>b(s)==="draft")),o}),b=o=>o.is_active?o.expires_at&&new Date(o.expires_at)<new Date?"closed":"active":"draft",J=o=>o.expires_at&&new Date(o.expires_at)<new Date,K=o=>o.max_participants?Math.min(100,(o.total_votes||0)/o.max_participants*100):100,R=o=>c.getUserVoteForPoll(o.id)!==null,Q=o=>{e.push(`/app/communications/polls/${o.id}`)},W=o=>{e.push(`/app/communications/polls/${o.id}`)},X=o=>{i.value=o,v.value=!0},Y=o=>{i.value=o,k.value=!0},Z=o=>{r.value=!1},tt=o=>{v.value=!1,i.value=null},et=async()=>{try{await c.deletePoll(i.value.id),k.value=!1,i.value=null}catch(o){console.error("Errore nell'eliminazione del sondaggio:",o)}};return ot(async()=>{try{await c.fetchPolls()}catch(o){console.error("Errore nel caricamento dei sondaggi:",o)}}),(o,s)=>{var T;return n(),d(E,null,[g(dt,{title:"Sondaggi Aziendali",subtitle:"Gestione e partecipazione ai sondaggi interni",data:D(c).polls,loading:D(c).loading.polls,"can-create":P.value,"create-label":"Nuovo Sondaggio","search-placeholder":"Cerca sondaggi...","empty-message":"Nessun sondaggio disponibile","results-label":"sondaggi",onCreate:s[1]||(s[1]=w=>r.value=!0)},{stats:N(()=>[t("div",Nt,[t("div",Ut,[t("div",At,[t("div",Rt,[t("div",Tt,[g(x,{name:"chart-bar",size:"lg",class:"text-green-500"})]),t("div",jt,[t("dl",null,[s[5]||(s[5]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"},"Totali",-1)),t("dd",Ft,u(D(c).polls.length),1)])])])])]),t("div",Bt,[t("div",Lt,[t("div",$t,[t("div",Ht,[g(x,{name:"play",size:"lg",class:"text-blue-500"})]),t("div",qt,[t("dl",null,[s[6]||(s[6]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"},"Attivi",-1)),t("dd",Gt,u(H.value),1)])])])])]),t("div",Jt,[t("div",Kt,[t("div",Qt,[t("div",Wt,[g(x,{name:"stop",size:"lg",class:"text-red-500"})]),t("div",Xt,[t("dl",null,[s[7]||(s[7]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"},"Chiusi",-1)),t("dd",Yt,u(q.value),1)])])])])]),t("div",Zt,[t("div",te,[t("div",ee,[t("div",se,[g(x,{name:"hand-raised",size:"lg",class:"text-purple-500"})]),t("div",oe,[t("dl",null,[s[8]||(s[8]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"},"Voti Totali",-1)),t("dd",ie,u(G.value),1)])])])])])])]),filters:N(()=>[t("div",le,[h(t("select",{"onUpdate:modelValue":s[0]||(s[0]=w=>p.value=w),class:"block w-32 pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},s[9]||(s[9]=[t("option",{value:""},"Tutti",-1),t("option",{value:"active"},"Attivi",-1),t("option",{value:"closed"},"Chiusi",-1),t("option",{value:"draft"},"Bozze",-1)]),512),[[F,p.value]])])]),content:N(({data:w,loading:st})=>[st?(n(),d("div",ne,s[10]||(s[10]=[t("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):A.value.length===0?(n(),d("div",ae,[g(x,{name:"chart-bar",size:"2xl",class:"mx-auto text-gray-400 mb-4"}),s[11]||(s[11]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Nessun sondaggio trovato",-1)),s[12]||(s[12]=t("p",{class:"text-gray-500 dark:text-gray-400"},"Non ci sono sondaggi da visualizzare con i filtri selezionati.",-1))])):(n(),d("div",re,[t("div",de,[(n(!0),d(E,null,U(A.value,a=>(n(),d("div",{key:a.id,class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden hover:shadow-lg transition-shadow cursor-pointer",onClick:_=>Q(a)},[t("div",me,[t("div",ce,[t("h3",ge,u(a.title),1),g(ut,{status:b(a),size:"sm"},null,8,["status"])]),t("p",xe,u(a.description),1),t("div",fe,[t("div",pe,[t("span",null,u(a.total_votes||0)+" voti",1),a.expires_at?(n(),d("span",he,u(J(a)?"Scaduto":`Scade ${D(l)(a.expires_at)}`),1)):f("",!0)]),t("div",ve,[t("div",{class:"bg-green-500 h-2 rounded-full transition-all duration-300",style:lt({width:`${K(a)}%`})},null,4)])]),t("div",ye,[(n(!0),d(E,null,U((a.options||[]).slice(0,3),_=>(n(),d("div",{key:_.id,class:"flex justify-between items-center text-xs"},[t("span",be,u(_.text),1),t("span",_e,u(_.votes||0),1)]))),128)),(a.options||[]).length>3?(n(),d("div",ke," +"+u((a.options||[]).length-3)+" altre opzioni ",1)):f("",!0)]),t("div",we,[t("div",Ce,[g(x,{name:"user",size:"xs"}),t("span",null,u(a.author_name||"Anonimo"),1)]),t("span",null,u(D(l)(a.created_at)),1)]),t("div",Se,[t("div",ze,[!R(a)&&b(a)==="active"?(n(),d("button",{key:0,onClick:V(_=>W(a),["stop"]),class:"inline-flex items-center px-3 py-1 border border-green-300 shadow-sm text-xs font-medium rounded text-green-700 bg-white hover:bg-green-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"},[g(x,{name:"hand-raised",size:"xs",class:"mr-1"}),s[13]||(s[13]=z(" Vota "))],8,De)):R(a)?(n(),d("span",Pe,[g(x,{name:"check",size:"xs",class:"mr-1"}),s[14]||(s[14]=z(" Votato "))])):f("",!0)]),t("div",Me,[t("button",{onClick:V(_=>X(a),["stop"]),class:"inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},[g(x,{name:"pencil",size:"xs",class:"mr-1"}),s[15]||(s[15]=z(" Modifica "))],8,Ve),$.value?(n(),d("button",{key:0,onClick:V(_=>Y(a),["stop"]),class:"inline-flex items-center px-3 py-1 border border-red-300 shadow-sm text-xs font-medium rounded text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"},[g(x,{name:"trash",size:"xs",class:"mr-1"}),s[16]||(s[16]=z(" Elimina "))],8,Ee)):f("",!0)])])])],8,ue))),128))])]))]),_:1},8,["data","loading","can-create"]),r.value?(n(),O(It,{key:0,onClose:s[2]||(s[2]=w=>r.value=!1),onCreated:Z})):f("",!0),v.value?(n(),O(mt,{key:1,poll:i.value,onClose:s[3]||(s[3]=w=>v.value=!1),onUpdated:tt},null,8,["poll"])):f("",!0),k.value?(n(),O(ct,{key:2,title:"Elimina Sondaggio",message:`Sei sicuro di voler eliminare il sondaggio '${(T=i.value)==null?void 0:T.title}'?`,"confirm-text":"Elimina","confirm-class":"bg-red-600 hover:bg-red-700",onConfirm:et,onCancel:s[4]||(s[4]=w=>k.value=!1)},null,8,["message"])):f("",!0)],64)}}},Le=B(Oe,[["__scopeId","data-v-2c0fd1b8"]]);export{Le as default};
