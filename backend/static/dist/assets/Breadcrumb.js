import{H as s}from"./app.js";import{b as r,l as u,F as x,q as p,h as f,j as t,f as i,p as d,e as n,v as o,t as m}from"./vendor.js";const h={class:"flex mb-4","aria-label":"Breadcrumb"},_={class:"inline-flex items-center space-x-1 md:space-x-3"},y={key:2,class:"inline-flex items-center text-sm font-medium text-gray-500"},B={__name:"Breadcrumb",props:{breadcrumbs:{type:Array,required:!0,default:()=>[]}},setup(c){return(b,g)=>{const l=f("router-link");return t(),r("nav",h,[u("ol",_,[(t(!0),r(x,null,p(c.breadcrumbs,(e,a)=>(t(),r("li",{key:a,class:"inline-flex items-center"},[a===0?(t(),i(l,{key:0,to:e.path,class:"inline-flex items-center text-sm font-medium text-gray-700 hover:text-brand-primary-600 transition-colors"},{default:d(()=>[n(s,{name:e.icon||"home",size:"sm",class:"mr-2"},null,8,["name"]),o(" "+m(e.name),1)]),_:2},1032,["to"])):a<c.breadcrumbs.length-1?(t(),i(l,{key:1,to:e.path,class:"inline-flex items-center text-sm font-medium text-gray-700 hover:text-brand-primary-600 transition-colors"},{default:d(()=>[n(s,{name:"chevron-right",size:"sm",class:"text-gray-400 mr-1 md:mr-2"}),o(" "+m(e.name),1)]),_:2},1032,["to"])):(t(),r("span",y,[n(s,{name:"chevron-right",size:"sm",class:"text-gray-400 mr-1 md:mr-2"}),o(" "+m(e.name),1)]))]))),128))])])}}};export{B as _};
