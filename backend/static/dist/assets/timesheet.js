import{d as T}from"./vendor.js";import{b as i}from"./app.js";const j=T("timesheet",{state:()=>({stats:{weeklyHours:0,monthlyHours:0,pendingApprovals:0,efficiency:0},recentActivities:[],pendingApprovals:[],myStatus:{status:"draft",totalHours:0,billableHours:0},currentMonth:new Date().getMonth()+1,currentYear:new Date().getFullYear(),projectTasks:[],monthlyEntries:{},monthlyTimesheetStatus:null,availableProjects:[],loading:{dashboard:!1,monthlyData:!1,saving:!1},error:null,lastFetch:{dashboard:null,monthlyData:null,projects:null},pendingTimeOffRequests:[],vacationQuota:{total:0,used:0,remaining:0},leaveQuota:{total:0,used:0},smartWorkingDays:0}),getters:{totalHours:t=>Object.values(t.monthlyEntries).reduce((r,e)=>r+Object.values(e).reduce((s,a)=>s+(a||0),0),0),billableHours:t=>Object.values(t.monthlyEntries).reduce((r,e)=>r+Object.values(e).reduce((s,a,o)=>{var c;const n=t.projectTasks[Math.floor(o/((c=t.daysInMonth)==null?void 0:c.length)||31)];return s+(n!=null&&n.billable&&a||0)},0),0),pendingHours:t=>t.myStatus.status==="submitted"?t.myStatus.totalHours:0,activeProjects:t=>t.projectTasks.length,daysInMonth:t=>{const r=new Date(t.currentYear,t.currentMonth,0).getDate();return Array.from({length:r},(e,s)=>s+1)},canApprove:()=>{var r,e;const t=i();return((r=t.user)==null?void 0:r.role)==="manager"||((e=t.user)==null?void 0:e.role)==="admin"},needsRefresh:t=>({dashboard:!t.lastFetch.dashboard||Date.now()-t.lastFetch.dashboard>3e5,monthlyData:!t.lastFetch.monthlyData||Date.now()-t.lastFetch.monthlyData>3e5,projects:!t.lastFetch.projects||Date.now()-t.lastFetch.projects>3e5})},actions:{async loadDashboardStats(){var t,r,e,s,a,o;if(!(this.loading.dashboard||!this.needsRefresh.dashboard)){this.loading.dashboard=!0,this.error=null;try{const n=i(),c=await fetch("/api/dashboard/stats",{headers:{"Content-Type":"application/json","X-CSRFToken":n.csrfToken}});if(!c.ok)throw new Error(`HTTP ${c.status}: ${c.statusText}`);const h=await c.json();this.stats={weeklyHours:((r=(t=h.data)==null?void 0:t.activities)==null?void 0:r.recent_timesheets)||0,monthlyHours:((s=(e=h.data)==null?void 0:e.activities)==null?void 0:s.recent_timesheets)||0,pendingApprovals:((o=(a=h.data)==null?void 0:a.activities)==null?void 0:o.unread_notifications)||0,efficiency:85},this.lastFetch.dashboard=Date.now()}catch(n){this.error=`Errore caricamento statistiche: ${n.message}`,console.error("Error loading dashboard stats:",n)}finally{this.loading.dashboard=!1}}},async loadRecentActivities(){try{const t=i(),r=await fetch("/api/timesheets/?per_page=5&page=1",{headers:{"Content-Type":"application/json","X-CSRFToken":t.csrfToken}});if(!r.ok)throw new Error(`HTTP ${r.status}: ${r.statusText}`);const s=(await r.json()).data||[];this.recentActivities=s.map(a=>({id:a.id,description:`${a.project_name||"Progetto"} - ${a.task_name||"Task"}`,hours:a.hours,created_at:a.created_at,date:a.date}))}catch(t){console.error("Error loading recent activities:",t),this.recentActivities=[]}},async loadPendingApprovals(){var t,r;if(this.canApprove)try{const e=i(),s=await fetch("/api/monthly-timesheets/?status=submitted",{headers:{"Content-Type":"application/json","X-CSRFToken":e.csrfToken}});if(!s.ok)throw new Error(`HTTP ${s.status}: ${s.statusText}`);const a=await s.json();console.log("🔍 loadPendingApprovals API response:",a),console.log("🔍 Extracted data:",a.data),console.log("🔍 Timesheets array:",(t=a.data)==null?void 0:t.timesheets),this.pendingApprovals=((r=a.data)==null?void 0:r.timesheets)||[],console.log("🔍 Final pendingApprovals array:",this.pendingApprovals)}catch(e){console.error("🚨 Error loading pending approvals:",e),console.log("🚨 Setting pendingApprovals to empty array"),this.pendingApprovals=[]}},async loadMyStatus(){var t,r,e;if(!this.canApprove)try{const s=i(),a=await fetch("/api/monthly-timesheets/generate",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":s.csrfToken},body:JSON.stringify({year:this.currentYear,month:this.currentMonth})});if(!a.ok)throw new Error(`HTTP ${a.status}: ${a.statusText}`);const o=await a.json();this.myStatus={status:((t=o.data)==null?void 0:t.status)||"draft",totalHours:((r=o.data)==null?void 0:r.total_hours)||0,billableHours:((e=o.data)==null?void 0:e.billable_hours)||0}}catch(s){console.error("Error loading my status:",s)}},async loadMonthlyData(t=this.currentYear,r=this.currentMonth,e=null){var s;if(this.loading.monthlyData||!this.needsRefresh.monthlyData&&t===this.currentYear&&r===this.currentMonth)return{entries:this.monthlyEntries,projects:this.projectTasks};this.loading.monthlyData=!0,this.error=null;try{const a=i(),o=new Date(t,r-1,1).toISOString().split("T")[0],n=new Date(t,r,0).toISOString().split("T")[0],c=e||((s=a.user)==null?void 0:s.id)||"",h=await fetch(`/api/timesheets?start_date=${o}&end_date=${n}&user_id=${c}`,{headers:{"Content-Type":"application/json","X-CSRFToken":a.csrfToken}});if(!h.ok)throw new Error(`HTTP ${h.status}: ${h.statusText}`);const m=await h.json();if(this.currentYear=t,this.currentMonth=r,!m.success)throw new Error(m.message||"API response indicates failure");const f=m.data||[],u={},y=new Set;return f.forEach(l=>{const d=l.date,p=`${l.project_id}-${l.task_id||"notask"}`;u[d]||(u[d]={}),u[d][p]||(u[d][p]={hours:0,billable:l.billable||!1}),typeof u[d][p]=="object"?(u[d][p].hours+=parseFloat(l.hours||0),u[d][p].billable=u[d][p].billable||l.billable||!1):u[d][p]={hours:u[d][p]+parseFloat(l.hours||0),billable:l.billable||!1},y.add(JSON.stringify({id:p,project_id:l.project_id,task_id:l.task_id||null,project_name:l.project_name||"Progetto Sconosciuto",task_name:l.task_name||"Attività Generica",billable:l.billable||!1}))}),this.monthlyEntries=u,this.projectTasks=Array.from(y).map(l=>JSON.parse(l)),await this.loadMonthlyTimesheetStatus(t,r),this.lastFetch.monthlyData=Date.now(),console.log("Loaded monthly data:",{entries:Object.keys(this.monthlyEntries).length,projects:this.projectTasks.length,totalTimesheets:f.length}),{entries:this.monthlyEntries,projects:this.projectTasks}}catch(a){return this.error=`Errore caricamento dati mensili: ${a.message}`,console.error("Error loading monthly data:",a),{entries:{},projects:[]}}finally{this.loading.monthlyData=!1}},async loadAvailableProjects(){if(this.availableProjects.length>0&&!this.needsRefresh.projects)return this.availableProjects;try{const t=i(),r=await fetch("/api/projects/",{headers:{"Content-Type":"application/json","X-CSRFToken":t.csrfToken}});if(!r.ok)throw new Error(`HTTP ${r.status}: ${r.statusText}`);const e=await r.json();let s=[];return e.data&&Array.isArray(e.data.projects)?s=e.data.projects:e.data&&Array.isArray(e.data.items)?s=e.data.items:e.data&&Array.isArray(e.data)?s=e.data:Array.isArray(e)&&(s=e),this.availableProjects=s.filter(a=>a.status==="active"||!a.status),this.lastFetch.projects=Date.now(),console.log("Loaded available projects:",this.availableProjects.length,"projects"),this.availableProjects}catch(t){return console.error("Error loading available projects:",t),this.availableProjects=[],[]}},async saveEntry(t,r,e){this.loading.saving=!0;try{const s=i(),[a,o]=t.split("-"),n=`${this.currentYear}-${this.currentMonth.toString().padStart(2,"0")}-${r.toString().padStart(2,"0")}`,c={date:n,project_id:parseInt(a),task_id:o!=="notask"?parseInt(o):null,hours:parseFloat(e),description:`Lavoro del ${n}`},h=await fetch("/api/timesheets",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":s.csrfToken},body:JSON.stringify(c)});if(!h.ok)throw new Error(`HTTP ${h.status}: ${h.statusText}`);const m=await h.json();if(!m.success)throw new Error(m.message||"Errore durante il salvataggio");const f=n;return this.monthlyEntries[f]||(this.monthlyEntries[f]={}),this.monthlyEntries[f][t]={hours:parseFloat(e)||0,billable:!1},console.log("Saved timesheet entry:",{date:n,project:a,task:o,hours:e,success:!0}),!0}catch(s){return this.error=`Errore salvataggio ore: ${s.message}`,console.error("Error saving entry:",s),!1}finally{this.loading.saving=!1}},async addProjectToTimesheet(t,r){try{const e=i(),s=await fetch("/api/timesheet-projects/",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":e.csrfToken},body:JSON.stringify({project_id:t,task_id:r})});if(!s.ok)throw new Error(`HTTP ${s.status}: ${s.statusText}`);return await this.loadMonthlyData(this.currentYear,this.currentMonth),!0}catch(e){return this.error=`Errore aggiunta progetto: ${e.message}`,console.error("Error adding project to timesheet:",e),!1}},async approveTimesheet(t){try{const r=i(),e=await fetch(`/api/monthly-timesheets/${t}/approve`,{method:"PUT",headers:{"Content-Type":"application/json","X-CSRFToken":r.csrfToken}});if(!e.ok)throw new Error(`HTTP ${e.status}: ${e.statusText}`);return await this.loadPendingApprovals(),!0}catch(r){return this.error=`Errore approvazione: ${r.message}`,console.error("Error approving timesheet:",r),!1}},navigateMonth(t){t==="next"?this.currentMonth===12?(this.currentMonth=1,this.currentYear++):this.currentMonth++:this.currentMonth===1?(this.currentMonth=12,this.currentYear--):this.currentMonth--,this.needsRefresh.monthlyData=!0,this.loadMonthlyData(this.currentYear,this.currentMonth)},async refreshAll(){this.lastFetch={dashboard:null,monthlyData:null,projects:null},await Promise.all([this.loadDashboardStats(),this.loadRecentActivities(),this.loadPendingApprovals(),this.loadMyStatus(),this.loadMonthlyData()])},async loadPendingTimesheets(t={}){var r;try{const e=i(),s=new URLSearchParams({month:t.month||new Date().getMonth()+1,year:t.year||new Date().getFullYear()});t.status&&s.append("status",t.status),t.user_id&&s.append("user_id",t.user_id),t.search&&s.append("search",t.search),t.anomalies_only&&s.append("anomalies_only","true");const a=await fetch(`/api/monthly-timesheets/?${s}`,{headers:{"Content-Type":"application/json","X-CSRFToken":e.csrfToken}});if(!a.ok)throw new Error(`HTTP ${a.status}: ${a.statusText}`);const o=await a.json();return console.log("🔍 loadPendingTimesheets API response:",o),((r=o.data)==null?void 0:r.timesheets)||[]}catch(e){return this.error=`Errore caricamento timesheet: ${e.message}`,console.error("Error loading pending timesheets:",e),[]}},async rejectTimesheet(t,r){try{const e=i(),s=await fetch(`/api/monthly-timesheets/${t}/reject`,{method:"PUT",headers:{"Content-Type":"application/json","X-CSRFToken":e.csrfToken},body:JSON.stringify({reason:r})});if(!s.ok)throw new Error(`HTTP ${s.status}: ${s.statusText}`);return!0}catch(e){return this.error=`Errore rifiuto timesheet: ${e.message}`,console.error("Error rejecting timesheet:",e),!1}},async loadAnalyticsData(t={}){try{const r=i(),e=new URLSearchParams;t.start_date&&e.append("start_date",t.start_date),t.end_date&&e.append("end_date",t.end_date),t.department_id&&e.append("department_id",t.department_id),t.project_id&&e.append("project_id",t.project_id),t.analysis_type&&e.append("analysis_type",t.analysis_type);const s=await fetch(`/api/timesheets/analytics/?${e}`,{headers:{"Content-Type":"application/json","X-CSRFToken":r.csrfToken}});if(!s.ok)throw new Error(`HTTP ${s.status}: ${s.statusText}`);const a=await s.json();if(!a.success)throw new Error(a.message||"API response indicates failure");return a.data||[]}catch(r){return this.error=`Errore caricamento analytics: ${r.message}`,console.error("Error loading analytics data:",r),[]}},async loadTeamMembers(){var t;try{const r=i(),e=await fetch("/api/personnel/users",{headers:{"Content-Type":"application/json","X-CSRFToken":r.csrfToken}});if(!e.ok)throw new Error(`HTTP ${e.status}: ${e.statusText}`);const s=await e.json();return((t=s.data)==null?void 0:t.users)||s.data||[]}catch(r){return this.error=`Errore caricamento team: ${r.message}`,console.error("Error loading team members:",r),[]}},async loadDepartments(){var t;try{const r=i(),e=await fetch("/api/personnel/departments",{headers:{"Content-Type":"application/json","X-CSRFToken":r.csrfToken}});if(!e.ok)throw new Error(`HTTP ${e.status}: ${e.statusText}`);const s=await e.json();return((t=s.data)==null?void 0:t.departments)||s.data||[]}catch(r){return console.error("Error loading departments:",r),[]}},async bulkApproveTimesheets(t){try{const r=i(),e=await Promise.allSettled(t.map(o=>this.approveTimesheet(o))),s=e.filter(o=>o.status==="fulfilled"&&o.value).length,a=e.length-s;return a>0&&(this.error=`${s} approvati, ${a} falliti`),{successful:s,failed:a}}catch(r){return this.error=`Errore approvazione multipla: ${r.message}`,{successful:0,failed:t.length}}},async bulkRejectTimesheets(t,r){try{const e=await Promise.allSettled(t.map(o=>this.rejectTimesheet(o,r))),s=e.filter(o=>o.status==="fulfilled"&&o.value).length,a=e.length-s;return a>0&&(this.error=`${s} rifiutati, ${a} falliti`),{successful:s,failed:a}}catch(e){return this.error=`Errore rifiuto multiplo: ${e.message}`,{successful:0,failed:t.length}}},async exportTimesheetData(t={}){try{const r=i(),e=new URLSearchParams;Object.keys(t).forEach(c=>{t[c]&&e.append(c,t[c])});const s=await fetch(`/api/timesheets/export/?${e}`,{headers:{"Content-Type":"application/json","X-CSRFToken":r.csrfToken}});if(!s.ok){const c=await s.json();throw new Error(c.message||`HTTP ${s.status}: ${s.statusText}`)}const a=await s.blob(),o=window.URL.createObjectURL(a),n=document.createElement("a");return n.href=o,n.download=`timesheet_export_${new Date().toISOString().split("T")[0]}.xlsx`,document.body.appendChild(n),n.click(),window.URL.revokeObjectURL(o),document.body.removeChild(n),!0}catch(r){return this.error=`Errore export: ${r.message}`,console.error("Error exporting data:",r),!1}},async loadTimesheetHistory(t={}){try{const r=i(),e=new URLSearchParams;Object.keys(t).forEach(o=>{t[o]&&e.append(o,t[o])});const s=await fetch(`/api/timesheets/?${e}`,{headers:{"Content-Type":"application/json","X-CSRFToken":r.csrfToken}});if(!s.ok)throw new Error(`HTTP ${s.status}: ${s.statusText}`);return(await s.json()).data||[]}catch(r){return this.error=`Errore caricamento storico: ${r.message}`,console.error("Error loading timesheet history:",r),[]}},async loadTimeOffRequests(t={}){try{const r=i(),e=new URLSearchParams;Object.keys(t).forEach(o=>{if(t[o]){const n=o==="request_type"?"type":o;e.append(n,t[o])}});const s=await fetch(`/api/time-off-requests/?${e}`,{headers:{"Content-Type":"application/json","X-CSRFToken":r.csrfToken}});if(!s.ok)throw new Error(`HTTP ${s.status}: ${s.statusText}`);const a=await s.json();return Array.isArray(a.data)?a.data:a.data&&Array.isArray(a.data.requests)?a.data.requests:[]}catch(r){return this.error=`Errore caricamento richieste: ${r.message}`,console.error("Error loading time off requests:",r),[]}},async createTimeOffRequest(t){try{const r=i(),e=await fetch("/api/time-off-requests/",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":r.csrfToken},body:JSON.stringify(t)});if(!e.ok){const s=await e.json();throw new Error(s.message||`HTTP ${e.status}: ${e.statusText}`)}return!0}catch(r){return this.error=`${r.message}`,console.error("Error creating time off request:",r),!1}},async deleteTimeOffRequest(t){try{const r=i(),e=await fetch(`/api/time-off-requests/${t}`,{method:"DELETE",headers:{"Content-Type":"application/json","X-CSRFToken":r.csrfToken}});if(!e.ok)throw new Error(`HTTP ${e.status}: ${e.statusText}`);return!0}catch(r){return this.error=`Errore eliminazione richiesta: ${r.message}`,console.error("Error deleting time off request:",r),!1}},async approveTimeOffRequest(t){try{const r=i(),e=await fetch(`/api/time-off-requests/${t}/approve`,{method:"PUT",headers:{"Content-Type":"application/json","X-CSRFToken":r.csrfToken}});if(!e.ok){const s=await e.json();throw new Error(s.message||`HTTP ${e.status}: ${e.statusText}`)}return!0}catch(r){return this.error=`Errore approvazione richiesta: ${r.message}`,console.error("Error approving time off request:",r),!1}},async rejectTimeOffRequest(t,r){try{const e=i(),s=await fetch(`/api/time-off-requests/${t}/reject`,{method:"PUT",headers:{"Content-Type":"application/json","X-CSRFToken":e.csrfToken},body:JSON.stringify({reason:r})});if(!s.ok){const a=await s.json();throw new Error(a.message||`HTTP ${s.status}: ${s.statusText}`)}return!0}catch(e){return this.error=`Errore rifiuto richiesta: ${e.message}`,console.error("Error rejecting time off request:",e),!1}},clearError(){this.error=null},async loadTimeOffQuotas(){var t;try{const r=i(),e=await fetch("/api/time-off-requests/quotas",{headers:{"Content-Type":"application/json","X-CSRFToken":r.csrfToken}});if(!e.ok)throw new Error(`HTTP ${e.status}: ${e.statusText}`);const a=(await e.json()).data||{vacation:{remaining:0,total:0},leave:{used:0,total:0},smartworking:{used:0}};return this.vacationQuota=a.vacation,this.leaveQuota=a.leave,this.smartWorkingDays=((t=a.smartworking)==null?void 0:t.used)||0,a}catch(r){console.error("Error loading time-off quotas:",r);const e={vacation:{remaining:0,total:0},leave:{used:0,total:0},smartworking:{used:0}};return this.vacationQuota=e.vacation,this.leaveQuota=e.leave,this.smartWorkingDays=0,e}},setMonthlyTimesheetStatus(t){this.monthlyTimesheetStatus=t},async loadMonthlyTimesheetStatus(t=this.currentYear,r=this.currentMonth){var e;try{const s=i(),a=`${t}-${r.toString().padStart(2,"0")}`;try{const o=await fetch(`/api/timesheets/status?year_month=${a}&user_id=${s.user.id}`,{headers:{"Content-Type":"application/json","X-CSRFToken":s.csrfToken}});if(!o.ok)return this.monthlyTimesheetStatus="pending","pending";const n=await o.json();n.success?this.monthlyTimesheetStatus=((e=n.data)==null?void 0:e.status)||"pending":this.monthlyTimesheetStatus="pending"}catch{this.monthlyTimesheetStatus="pending"}return this.monthlyTimesheetStatus}catch{return this.monthlyTimesheetStatus="pending","pending"}},async loadPendingTimeOffRequests(){try{const t=await this.loadTimeOffRequests({status:"pending"});return this.pendingTimeOffRequests=t,t}catch(t){return console.error("Error loading pending time-off requests:",t),this.error=`Errore caricamento richieste in attesa: ${t.message}`,this.pendingTimeOffRequests=[],[]}}}});export{j as u};
