import{b as o,j as a,l as e,t as s,n as H,g as h,e as I,v as q,p as ze,h as Re,F as W,q as Y,f as fe,c as K,E as ye,r as C,w as pe,o as be,B as U,S as le,O as xe,C as te,A as ge,H as Xe,x as Te,u as Je,i as We,U as Ye,s as Qe}from"./vendor.js";import{u as Be}from"./projects2.js";import{_ as Ce,H as A,b as $e,e as Ze,c as we}from"./app.js";import{u as et}from"./useToast.js";import{r as tt,a as Fe,b as st}from"./PlusIcon.js";const rt={class:"project-header bg-white shadow-sm rounded-lg p-6 mb-6"},at={key:0,class:"animate-pulse"},ot={key:1,class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},nt={class:"flex-1"},it={class:"flex items-center space-x-3 mb-2"},lt={class:"text-2xl font-bold text-gray-900"},dt={class:"flex flex-wrap items-center gap-4 text-sm text-gray-500"},ut={key:0},ct={key:1},mt={key:2},gt={key:3},pt={class:"mt-4 sm:mt-0 flex space-x-3"},xt={key:2,class:"text-center py-8"},yt={__name:"ProjectHeader",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["edit","delete"],setup(g){const D=p=>({planning:"bg-yellow-100 text-yellow-800",active:"bg-green-100 text-green-800",on_hold:"bg-orange-100 text-orange-800",completed:"bg-blue-100 text-blue-800",cancelled:"bg-red-100 text-red-800"})[p]||"bg-gray-100 text-gray-800",x=p=>({planning:"Pianificazione",active:"Attivo",on_hold:"In Pausa",completed:"Completato",cancelled:"Annullato"})[p]||p,P=p=>p?new Date(p).toLocaleDateString("it-IT"):"",w=p=>p?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(p):"";return(p,f)=>{const y=Re("router-link");return a(),o("div",rt,[g.loading?(a(),o("div",at,f[1]||(f[1]=[e("div",{class:"h-8 bg-gray-200 rounded w-1/3 mb-2"},null,-1),e("div",{class:"h-4 bg-gray-200 rounded w-1/2"},null,-1)]))):g.project?(a(),o("div",ot,[e("div",nt,[e("div",it,[e("h1",lt,s(g.project.name),1),e("span",{class:H(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",D(g.project.status)])},s(x(g.project.status)),3)]),e("div",dt,[g.project.client?(a(),o("span",ut,[I(A,{name:"user",class:"w-4 h-4 inline mr-1"}),q(" Cliente: "+s(g.project.client.name),1)])):h("",!0),g.project.start_date?(a(),o("span",ct,[I(A,{name:"calendar",class:"w-4 h-4 inline mr-1"}),q(" Inizio: "+s(P(g.project.start_date)),1)])):h("",!0),g.project.end_date?(a(),o("span",mt,[I(A,{name:"calendar",class:"w-4 h-4 inline mr-1"}),q(" Fine: "+s(P(g.project.end_date)),1)])):h("",!0),g.project.budget?(a(),o("span",gt,[I(A,{name:"currency-dollar",class:"w-4 h-4 inline mr-1"}),q(" Budget: "+s(w(g.project.budget)),1)])):h("",!0)])]),e("div",pt,[I(y,{to:`/app/projects/${g.project.id}/edit`,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},{default:ze(()=>[I(A,{name:"pencil",class:"w-4 h-4 mr-2"}),f[2]||(f[2]=q(" Modifica "))]),_:1,__:[2]},8,["to"]),e("button",{onClick:f[0]||(f[0]=v=>p.$emit("delete")),class:"inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"},[I(A,{name:"trash",class:"w-4 h-4 mr-2"}),f[3]||(f[3]=q(" Elimina "))])])])):(a(),o("div",xt,f[4]||(f[4]=[e("p",{class:"text-gray-500"},"Progetto non trovato",-1)])))])}}},vt=Ce(yt,[["__scopeId","data-v-e4270dbf"]]),ft={class:"tab-navigation"},bt={class:"border-b border-gray-200"},ht={class:"-mb-px flex space-x-8","aria-label":"Tabs"},_t=["onClick","aria-current"],kt={key:1,class:"ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-600"},wt={__name:"TabNavigation",props:{modelValue:{type:String,required:!0},tabs:{type:Array,required:!0,validator:g=>g.every(D=>typeof D=="object"&&D.id&&D.label)}},emits:["update:modelValue"],setup(g,{emit:D}){const x=g,P=D,w=f=>x.modelValue===f,p=f=>{P("update:modelValue",f)};return(f,y)=>(a(),o("div",ft,[e("div",bt,[e("nav",ht,[(a(!0),o(W,null,Y(g.tabs,v=>(a(),o("button",{key:v.id,onClick:u=>p(v.id),class:H(["whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2",w(v.id)?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"]),"aria-current":w(v.id)?"page":void 0},[v.icon?(a(),fe(A,{key:0,name:v.icon,size:"sm",variant:"outline"},null,8,["name"])):h("",!0),e("span",null,s(v.label),1),v.count!==void 0?(a(),o("span",kt,s(v.count),1)):h("",!0)],10,_t))),128))])])]))}},$t=Ce(wt,[["__scopeId","data-v-348b0971"]]),jt={class:"project-overview"},Tt={key:0,class:"animate-pulse space-y-4"},Ct={key:1,class:"space-y-6"},St={class:"bg-white shadow rounded-lg p-6"},Pt={key:0,class:"text-gray-600"},zt={key:1,class:"text-gray-400 italic"},It={key:0,class:"bg-purple-50 border border-purple-200 rounded-lg p-6"},Dt={class:"flex items-center justify-between mb-4"},Mt={class:"text-lg font-medium text-purple-900 flex items-center"},At={class:"grid grid-cols-2 md:grid-cols-4 gap-4"},Et={class:"text-sm font-medium text-purple-900"},Ut={class:"text-sm font-medium text-purple-900"},Vt={class:"text-sm font-medium text-purple-900"},Ft={key:0,class:"mt-4 p-3 bg-orange-100 border border-orange-300 rounded-lg"},Ot={class:"flex items-center"},Rt={class:"text-orange-800 text-sm"},Bt={key:1,class:"bg-white shadow rounded-lg p-6"},Nt={class:"flex items-center justify-between mb-4"},Lt={class:"grid grid-cols-2 md:grid-cols-4 gap-4"},qt={class:"text-sm font-medium text-gray-900"},Kt={class:"text-sm font-medium text-gray-900"},Ht={class:"text-sm font-medium text-gray-900"},Gt={key:0,class:"mt-4 pt-4 border-t border-gray-200"},Xt={class:"grid grid-cols-2 gap-4"},Jt={key:0},Wt={class:"text-sm font-medium text-gray-900"},Yt={key:1},Qt={class:"text-sm font-medium text-gray-900"},Zt={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},es={class:"bg-white shadow rounded-lg p-6"},ts={class:"flex items-center"},ss={class:"flex-shrink-0"},rs={class:"ml-5 w-0 flex-1"},as={class:"text-lg font-medium text-gray-900"},os={class:"bg-white shadow rounded-lg p-6"},ns={class:"flex items-center"},is={class:"flex-shrink-0"},ls={class:"ml-5 w-0 flex-1"},ds={class:"text-lg font-medium text-gray-900"},us={class:"bg-white shadow rounded-lg p-6"},cs={class:"flex items-center"},ms={class:"flex-shrink-0"},gs={class:"ml-5 w-0 flex-1"},ps={class:"text-lg font-medium text-gray-900"},xs={class:"bg-white shadow rounded-lg p-6"},ys={class:"flex items-center"},vs={class:"flex-shrink-0"},fs={class:"ml-5 w-0 flex-1"},bs={class:"text-lg font-medium text-gray-900"},hs={class:"bg-white shadow rounded-lg p-6"},_s={class:"w-full bg-gray-200 rounded-full h-2.5"},ks={class:"text-sm text-gray-500 mt-2"},ws={class:"bg-white shadow rounded-lg p-6"},$s={class:"space-y-4"},js={class:"flex justify-between items-center"},Ts={class:"text-sm font-medium"},Cs={class:"flex justify-between items-center"},Ss={class:"text-sm font-medium"},Ps={class:"w-full bg-gray-200 rounded-full h-3"},zs={class:"flex justify-between items-center text-sm"},Is={class:"bg-white shadow rounded-lg p-6"},Ds={class:"space-y-3"},Ms={class:"flex-shrink-0"},As=["src","alt"],Es={key:1,class:"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"},Us={class:"text-xs font-medium text-gray-600"},Vs={class:"flex-1"},Fs={class:"text-sm font-medium text-gray-900"},Os={class:"text-xs text-gray-500"},Rs={class:"text-right"},Bs={class:"text-xs text-gray-500"},Ns={key:0,class:"text-center py-4"},Ls={class:"bg-white shadow rounded-lg p-6"},qs={class:"space-y-3"},Ks={class:"flex-shrink-0"},Hs={class:"flex-1"},Gs={class:"text-sm text-gray-900"},Xs={class:"flex items-center space-x-2 mt-1"},Js={class:"text-xs text-gray-500"},Ws={class:"text-xs text-gray-500"},Ys={key:0,class:"text-center py-4"},Qs={key:2,class:"text-center py-8"},Zs={__name:"ProjectOverview",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(g){const D=g,x=K(()=>{if(!D.project||!D.project.task_count)return 0;const _=D.project.completed_tasks||0,n=D.project.task_count||1;return Math.round(_/n*100)}),P=K(()=>{var _;return((_=D.project)==null?void 0:_.team_members)||[]}),w=K(()=>{var Z,R,me;if((Z=D.project)!=null&&Z.expenses)return D.project.expenses;const _=((R=D.project)==null?void 0:R.total_hours)||0,n=(me=D.project)!=null&&me.client_daily_rate?D.project.client_daily_rate/8:50;return _*n}),p=K(()=>{var n;return(((n=D.project)==null?void 0:n.budget)||0)-w.value}),f=K(()=>{var n;const _=((n=D.project)==null?void 0:n.budget)||1;return Math.min(Math.round(w.value/_*100),100)}),y=K(()=>{const _=f.value;return _>=90?"bg-red-600":_>=75?"bg-yellow-600":"bg-green-600"}),v=K(()=>{var n;const _=p.value;return _<0?"text-red-600":_<(((n=D.project)==null?void 0:n.budget)||0)*.1?"text-yellow-600":"text-green-600"}),u=K(()=>{var _;return(_=D.project)!=null&&_.tasks?[...D.project.tasks].sort((n,Z)=>new Date(Z.updated_at)-new Date(n.updated_at)).slice(0,5).map(n=>{var Z;return{id:n.id,description:`Task "${n.name}" ${S(n.status)}`,created_at:n.updated_at,user_name:((Z=n.assignee)==null?void 0:Z.full_name)||"Non assegnato",type:O(n.status)}}):[]}),S=_=>({todo:"creato","in-progress":"in corso",review:"in revisione",done:"completato"})[_]||_,O=_=>({todo:"task_created","in-progress":"task_updated",review:"task_updated",done:"task_completed"})[_]||"task_updated",F=_=>_?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(_):"Non specificato",j=_=>_?new Date(_).toLocaleDateString("it-IT",{day:"numeric",month:"short",hour:"2-digit",minute:"2-digit"}):"",$=_=>_?_.split(" ").map(n=>n.charAt(0).toUpperCase()).slice(0,2).join(""):"??",G=_=>{const n={task_created:"bg-blue-600",task_completed:"bg-green-600",task_updated:"bg-yellow-600",comment_added:"bg-purple-600",file_uploaded:"bg-indigo-600",member_added:"bg-pink-600",default:"bg-gray-600"};return n[_]||n.default},E=_=>({fixed_price:"Prezzo Fisso",time_and_materials:"Tempo e Materiali",retainer:"Retainer",milestone:"Milestone"})[_]||_,z=_=>({draft:"Bozza",sent:"Inviato",signed:"Firmato",active:"Attivo",completed:"Completato",cancelled:"Annullato"})[_]||_,M=_=>({draft:"bg-gray-100 text-gray-800",sent:"bg-blue-100 text-blue-800",signed:"bg-green-100 text-green-800",active:"bg-green-100 text-green-800",completed:"bg-purple-100 text-purple-800",cancelled:"bg-red-100 text-red-800"})[_]||"bg-gray-100 text-gray-800",oe=_=>({active:"Attiva",planning:"In Pianificazione",expired:"Scaduta",in_renewal:"In Rinnovo",suspended:"Sospesa"})[_]||_,ee=_=>_>=90?"bg-green-100 text-green-800":_>=70?"bg-yellow-100 text-yellow-800":_>=50?"bg-orange-100 text-orange-800":"bg-red-100 text-red-800";return(_,n)=>{const Z=Re("router-link");return a(),o("div",jt,[g.loading?(a(),o("div",Tt,n[0]||(n[0]=[e("div",{class:"h-4 bg-gray-200 rounded w-3/4"},null,-1),e("div",{class:"h-4 bg-gray-200 rounded w-1/2"},null,-1),e("div",{class:"h-32 bg-gray-200 rounded"},null,-1)]))):g.project?(a(),o("div",Ct,[e("div",St,[n[1]||(n[1]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Descrizione Progetto",-1)),g.project.description?(a(),o("p",Pt,s(g.project.description),1)):(a(),o("p",zt,"Nessuna descrizione disponibile"))]),g.project.certifications&&g.project.certifications.length>0?(a(),o("div",It,[e("div",Dt,[e("h3",Mt,[I(A,{name:"shield-check",class:"h-6 w-6 mr-2 text-purple-600"}),n[2]||(n[2]=q(" Certificazione Collegata "))]),I(Z,{to:`/app/certifications/${g.project.certifications[0].id}`,class:"text-purple-600 hover:text-purple-800 text-sm font-medium"},{default:ze(()=>n[3]||(n[3]=[q(" Gestisci Certificazione → ")])),_:1,__:[3]},8,["to"])]),e("div",At,[e("div",null,[n[4]||(n[4]=e("dt",{class:"text-sm text-purple-600"},"Standard",-1)),e("dd",Et,s(g.project.certifications[0].standard_name),1)]),e("div",null,[n[5]||(n[5]=e("dt",{class:"text-sm text-purple-600"},"Stato",-1)),e("dd",Ut,s(oe(g.project.certifications[0].status)),1)]),e("div",null,[n[6]||(n[6]=e("dt",{class:"text-sm text-purple-600"},"Scadenza",-1)),e("dd",Vt,s(j(g.project.certifications[0].expiry_date)||"Non definita"),1)]),e("div",null,[n[7]||(n[7]=e("dt",{class:"text-sm text-purple-600"},"Health Score",-1)),e("dd",null,[e("span",{class:H(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",ee(g.project.certifications[0].health_score)])},s(g.project.certifications[0].health_score||0)+"% ",3)])])]),g.project.certifications[0].is_expiring_soon?(a(),o("div",Ft,[e("div",Ot,[I(A,{name:"exclamation-triangle",class:"h-5 w-5 text-orange-600 mr-2"}),e("span",Rt,[n[8]||(n[8]=e("strong",null,"Attenzione:",-1)),q(" Certificazione in scadenza tra "+s(g.project.certifications[0].days_to_expiry)+" giorni ",1)])])])):h("",!0)])):h("",!0),g.project.contract?(a(),o("div",Bt,[e("div",Nt,[n[10]||(n[10]=e("h3",{class:"text-lg font-medium text-gray-900"},"Contratto Collegato",-1)),I(Z,{to:`/app/crm/contracts/${g.project.contract.id}`,class:"text-blue-600 hover:text-blue-800 text-sm font-medium"},{default:ze(()=>n[9]||(n[9]=[q(" Gestisci Contratto → ")])),_:1,__:[9]},8,["to"])]),e("div",Lt,[e("div",null,[n[11]||(n[11]=e("dt",{class:"text-sm text-gray-500"},"Numero Contratto",-1)),e("dd",qt,s(g.project.contract.contract_number),1)]),e("div",null,[n[12]||(n[12]=e("dt",{class:"text-sm text-gray-500"},"Tipo",-1)),e("dd",Kt,s(E(g.project.contract.contract_type)),1)]),e("div",null,[n[13]||(n[13]=e("dt",{class:"text-sm text-gray-500"},"Tariffa Oraria",-1)),e("dd",Ht,s(F(g.project.contract.hourly_rate))+"/h",1)]),e("div",null,[n[14]||(n[14]=e("dt",{class:"text-sm text-gray-500"},"Stato",-1)),e("dd",null,[e("span",{class:H(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",M(g.project.contract.status)])},s(z(g.project.contract.status)),3)])])]),g.project.contract.budget_hours||g.project.contract.budget_amount?(a(),o("div",Gt,[e("div",Xt,[g.project.contract.budget_hours?(a(),o("div",Jt,[n[15]||(n[15]=e("dt",{class:"text-sm text-gray-500"},"Budget Ore",-1)),e("dd",Wt,s(g.project.contract.budget_hours)+"h",1)])):h("",!0),g.project.contract.budget_amount?(a(),o("div",Yt,[n[16]||(n[16]=e("dt",{class:"text-sm text-gray-500"},"Budget Importo",-1)),e("dd",Qt,s(F(g.project.contract.budget_amount)),1)])):h("",!0)])])):h("",!0)])):h("",!0),e("div",Zt,[e("div",es,[e("div",ts,[e("div",ss,[I(A,{name:"task",class:"h-8 w-8 text-blue-600"})]),e("div",rs,[e("dl",null,[n[17]||(n[17]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Task Totali",-1)),e("dd",as,s(g.project.task_count||0),1)])])])]),e("div",os,[e("div",ns,[e("div",is,[I(A,{name:"check-circle",class:"h-8 w-8 text-green-600"})]),e("div",ls,[e("dl",null,[n[18]||(n[18]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Task Completati",-1)),e("dd",ds,s(g.project.completed_tasks||0),1)])])])]),e("div",us,[e("div",cs,[e("div",ms,[I(A,{name:"user",class:"h-8 w-8 text-purple-600"})]),e("div",gs,[e("dl",null,[n[19]||(n[19]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Membri Team",-1)),e("dd",ps,s(g.project.team_count||0),1)])])])]),e("div",xs,[e("div",ys,[e("div",vs,[I(A,{name:"currency-dollar",class:"h-8 w-8 text-yellow-600"})]),e("div",fs,[e("dl",null,[n[20]||(n[20]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Budget",-1)),e("dd",bs,s(F(g.project.budget)),1)])])])])]),e("div",hs,[n[21]||(n[21]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Progresso Progetto",-1)),e("div",_s,[e("div",{class:"bg-blue-600 h-2.5 rounded-full transition-all duration-300",style:ye({width:`${x.value}%`})},null,4)]),e("p",ks,s(x.value)+"% completato",1)]),e("div",ws,[n[26]||(n[26]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Budget vs Spese",-1)),e("div",$s,[e("div",js,[n[22]||(n[22]=e("span",{class:"text-sm text-gray-600"},"Budget Totale",-1)),e("span",Ts,s(F(g.project.budget)),1)]),n[25]||(n[25]=e("div",{class:"w-full bg-gray-200 rounded-full h-3"},[e("div",{class:"bg-blue-600 h-3 rounded-full transition-all duration-300",style:{width:"100%"}})],-1)),e("div",Cs,[n[23]||(n[23]=e("span",{class:"text-sm text-gray-600"},"Spese Sostenute",-1)),e("span",Ss,s(F(w.value)),1)]),e("div",Ps,[e("div",{class:H(["h-3 rounded-full transition-all duration-300",y.value]),style:ye({width:f.value+"%"})},null,6)]),e("div",zs,[n[24]||(n[24]=e("span",{class:"text-gray-600"},"Rimanente",-1)),e("span",{class:H(["font-medium",v.value])},s(F(p.value)),3)])])]),e("div",Is,[n[28]||(n[28]=e("div",{class:"flex items-center justify-between mb-4"},[e("h3",{class:"text-lg font-medium text-gray-900"},"Team Members"),e("button",{class:"text-sm text-blue-600 hover:text-blue-800"},"Visualizza tutti")],-1)),e("div",Ds,[(a(!0),o(W,null,Y(P.value,R=>(a(),o("div",{key:R.id,class:"flex items-center space-x-3"},[e("div",Ms,[R.profile_image?(a(),o("img",{key:0,src:R.profile_image,alt:R.full_name,class:"w-8 h-8 rounded-full"},null,8,As)):(a(),o("div",Es,[e("span",Us,s($(R.full_name)),1)]))]),e("div",Vs,[e("p",Fs,s(R.full_name),1),e("p",Os,s(R.role||"Team Member"),1)]),e("div",Rs,[e("p",Bs,s(R.hours_worked||0)+"h",1)])]))),128)),P.value.length===0?(a(),o("div",Ns,n[27]||(n[27]=[e("p",{class:"text-gray-500"},"Nessun membro del team assegnato",-1)]))):h("",!0)])]),e("div",Ls,[n[31]||(n[31]=e("div",{class:"flex items-center justify-between mb-4"},[e("h3",{class:"text-lg font-medium text-gray-900"},"Attività Recenti"),e("button",{class:"text-sm text-blue-600 hover:text-blue-800"},"Visualizza tutte")],-1)),e("div",qs,[(a(!0),o(W,null,Y(u.value,R=>(a(),o("div",{key:R.id,class:"flex items-start space-x-3"},[e("div",Ks,[e("div",{class:H(["w-2 h-2 rounded-full mt-2",G(R.type)])},null,2)]),e("div",Hs,[e("p",Gs,s(R.description),1),e("div",Xs,[e("p",Js,s(j(R.created_at)),1),n[29]||(n[29]=e("span",{class:"text-xs text-gray-400"},"•",-1)),e("p",Ws,s(R.user_name),1)])])]))),128)),u.value.length===0?(a(),o("div",Ys,n[30]||(n[30]=[e("p",{class:"text-gray-500"},"Nessuna attività recente",-1)]))):h("",!0)])])])):(a(),o("div",Qs,n[32]||(n[32]=[e("p",{class:"text-gray-500"},"Progetto non trovato",-1)])))])}}},Oe=Ce(Zs,[["__scopeId","data-v-b0146708"]]),er={class:"space-y-6"},tr={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},sr={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},rr={class:"flex items-center justify-between"},ar={key:0,class:"flex items-center space-x-3"},or=["disabled"],nr={class:"mt-4 grid grid-cols-1 md:grid-cols-4 gap-4"},ir=["value"],lr={key:0,class:"text-xs text-red-500"},dr={class:"mt-4 flex items-center justify-between"},ur={class:"flex items-center space-x-4"},cr={class:"text-sm text-gray-500 dark:text-gray-400"},mr={key:0,class:"bg-purple-50 dark:bg-purple-900/20 rounded-lg shadow border border-purple-200 dark:border-purple-700"},gr={class:"px-6 py-4 border-b border-purple-200 dark:border-purple-700"},pr={class:"flex items-start justify-between"},xr={class:"flex items-start space-x-3"},yr={class:"flex-shrink-0"},vr={class:"flex-1"},fr={class:"text-xs text-purple-700 dark:text-purple-300 mt-1"},br={class:"flex items-center space-x-2"},hr=["disabled"],_r={class:"px-6 py-4 max-h-96 overflow-y-auto"},kr={key:0,class:"mb-4 p-3 bg-white dark:bg-gray-800 rounded border"},wr={class:"grid grid-cols-1 md:grid-cols-3 gap-3 text-xs"},$r={class:"ml-1 capitalize"},jr={class:"ml-1 capitalize"},Tr={class:"ml-1"},Cr={class:"space-y-3"},Sr={class:"flex items-start justify-between mb-2"},Pr={class:"text-sm font-medium text-gray-900 dark:text-white"},zr={class:"flex items-center space-x-2"},Ir={class:"text-xs text-gray-500 bg-gray-100 dark:bg-gray-700 px-2 py-0.5 rounded"},Dr={class:"text-xs text-gray-600 dark:text-gray-400 mb-2"},Mr={class:"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400"},Ar={class:"font-medium"},Er={key:0,class:"ml-2"},Ur={key:0,class:"text-xs text-orange-600"},Vr={key:1,class:"flex justify-center py-8"},Fr={key:2,class:"bg-red-50 border border-red-200 rounded-md p-4"},Or={class:"text-red-600"},Rr={key:3,class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Br={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},Nr={class:"col-span-3"},Lr={class:"text-sm font-medium text-gray-900 dark:text-white"},qr={key:0,class:"text-sm text-gray-500 dark:text-gray-400 truncate"},Kr={class:"col-span-2"},Hr={key:0,class:"flex items-center"},Gr={class:"h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center text-sm font-medium text-gray-700"},Xr={class:"ml-2"},Jr={class:"text-sm font-medium text-gray-900 dark:text-white"},Wr={key:1,class:"text-sm text-gray-500 dark:text-gray-400"},Yr={class:"col-span-1"},Qr={class:"col-span-1"},Zr={class:"col-span-1"},ea={key:0,class:"text-sm text-gray-900 dark:text-white"},ta={key:1,class:"text-sm text-gray-500 dark:text-gray-400"},sa={class:"col-span-2"},ra={key:0,class:"text-sm text-gray-900 dark:text-white"},aa={key:1,class:"text-sm text-gray-500 dark:text-gray-400"},oa={class:"col-span-1"},na={class:"text-sm text-gray-900 dark:text-white"},ia={key:0,class:"text-gray-500"},la={class:"col-span-1"},da={class:"flex items-center space-x-2"},ua=["onClick"],ca={key:0,class:"px-6 py-12 text-center"},ma={key:4,class:"grid grid-cols-1 md:grid-cols-4 gap-6"},ga={class:"flex items-center justify-between mb-4"},pa={class:"font-medium text-gray-900 dark:text-white"},xa={class:"bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-full px-2 py-1 text-xs"},ya={class:"space-y-3"},va=["onClick"],fa={class:"font-medium text-sm text-gray-900 dark:text-white mb-1"},ba={key:0,class:"text-xs text-gray-500 dark:text-gray-400 mb-2 line-clamp-2"},ha={class:"flex items-center justify-between"},_a={key:0,class:"h-6 w-6 rounded-full bg-gray-300 flex items-center justify-center text-xs font-medium text-gray-700"},ka={key:5,class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},wa={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},$a={class:"flex items-center justify-between"},ja={class:"flex items-center space-x-4"},Ta={class:"flex items-center space-x-2"},Ca={key:0,class:"p-6"},Sa={class:"overflow-x-auto"},Pa={class:"min-w-[1000px]"},za={class:"flex mb-4"},Ia={class:"flex-1 flex"},Da={class:"space-y-1"},Ma={class:"w-80 flex-shrink-0 px-4 py-3"},Aa={class:"flex items-center space-x-2"},Ea={class:"flex-1 min-w-0"},Ua={class:"text-sm font-medium text-gray-900 dark:text-white truncate"},Va={class:"flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400"},Fa={key:0},Oa={class:"text-xs text-gray-500 dark:text-gray-400 mt-1"},Ra={class:"flex-1 relative h-12 flex items-center"},Ba=["title"],Na={class:"truncate"},La={class:"ml-2"},qa={key:1,class:"text-center py-12"},Ka={class:"mt-3"},Ha={class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},Ga={class:"grid grid-cols-1 gap-4"},Xa={key:0,class:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded relative"},Ja={class:"grid grid-cols-2 gap-4"},Wa=["value"],Ya={key:0,class:"text-xs text-red-500"},Qa={class:"grid grid-cols-2 gap-4"},Za={class:"flex justify-end space-x-3 mt-6"},eo=["disabled"],to={key:7,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},so={class:"relative top-10 mx-auto p-6 border max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800"},ro={class:"mt-3"},ao={class:"mb-4 p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-700"},oo={class:"grid grid-cols-1 md:grid-cols-2 gap-3 text-xs text-purple-700 dark:text-purple-300"},no={key:0},io={key:1},lo={key:2},uo={key:3},co={class:"space-y-4"},mo={class:"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-3"},go={class:"flex"},po={class:"flex justify-end space-x-3 mt-6"},xo=["disabled"],yo={__name:"ProjectTasks",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(g,{expose:D}){const x=g;$e();const{hasPermission:P}=Ze(),{showToast:w}=et(),p=C([]),f=C(!1),y=C(""),v=C("list"),u=C(!1),S=C(!1),O=C(!1),F=C(!1),j=C(null),$=C({preferences:""}),G=C("weeks"),E=C(new Date),z=C([]),M=C({status:"",priority:"",assignee_id:"",search:""}),oe=C(!1),ee=C(!1),_=C(null),n=C({name:"",description:"",status:"todo",priority:"medium",assignee_id:"",start_date:"",due_date:"",estimated_hours:null}),Z=K(()=>P.value("manage_project_tasks")),R=[{value:"todo",label:"Da fare"},{value:"in-progress",label:"In corso"},{value:"review",label:"In revisione"},{value:"done",label:"Completato"}],me=K(()=>p.value.filter(d=>d.start_date&&d.due_date).sort((d,t)=>{const Q=new Date(d.start_date),N=new Date(t.start_date);return Q-N}).map(d=>{const t=Ne(d);return{...d,timeline:t}})),T=async()=>{var d,t,Q,N;if((d=x.project)!=null&&d.id){f.value=!0,y.value="";try{const L=new URLSearchParams;L.append("project_id",x.project.id),M.value.status&&L.append("status",M.value.status),M.value.priority&&L.append("priority",M.value.priority),M.value.assignee_id&&L.append("assignee_id",M.value.assignee_id),M.value.search&&L.append("search",M.value.search);const V=await we.get(`/api/tasks?${L.toString()}`);p.value=((t=V.data.data)==null?void 0:t.tasks)||V.data.tasks||[]}catch(L){y.value=((N=(Q=L.response)==null?void 0:Q.data)==null?void 0:N.message)||"Errore nel caricamento dei task",console.error("Error loading tasks:",L)}finally{f.value=!1}}},m=()=>{if(!n.value.name||n.value.name.trim()==="")return y.value="Il nome del task è obbligatorio",!1;if(n.value.start_date&&n.value.due_date){const d=new Date(n.value.start_date);if(new Date(n.value.due_date)<d)return y.value="La data di scadenza non può essere precedente alla data di inizio",!1}return!0},X=async()=>{var d,t,Q;if(y.value="",!m()){u.value=!1;return}u.value=!0;try{const N=ee.value?`/api/tasks/${_.value.id}`:"/api/tasks",L=ee.value?"put":"post",V={...n.value,project_id:x.project.id};console.log("Saving task data (pre-conversione):",JSON.stringify(V)),V.start_date&&(V.start_date=new Date(V.start_date).toISOString().split("T")[0]),V.due_date&&(V.due_date=new Date(V.due_date).toISOString().split("T")[0]),V.assignee_id===""?V.assignee_id=null:V.assignee_id&&(V.assignee_id=parseInt(V.assignee_id,10)),V.estimated_hours===""||V.estimated_hours===void 0?V.estimated_hours=null:V.estimated_hours&&(V.estimated_hours=parseFloat(V.estimated_hours)),console.log("Saving task data (post-conversione):",JSON.stringify(V));const ce=L==="put"?await we.put(N,V):await we.post(N,V);console.log("Task saved response:",ce.data),ce.data.success?(await T(),ae(),alert.value={type:"success",message:ee.value?"Task aggiornato con successo":"Task creato con successo"}):y.value=ce.data.message||"Errore durante il salvataggio del task"}catch(N){console.error("Error saving task:",N),y.value=((t=(d=N.response)==null?void 0:d.data)==null?void 0:t.message)||"Errore durante il salvataggio del task",((Q=N.response)==null?void 0:Q.status)===403&&(y.value="Non hai i permessi per eseguire questa operazione"),N.response&&console.error("Error response:",N.response.status,N.response.data)}finally{u.value=!1}},se=d=>{_.value=d,console.log("Editing task:",d),n.value={name:d.name,description:d.description||"",status:d.status,priority:d.priority,assignee_id:d.assignee_id?String(d.assignee_id):"",start_date:d.start_date?d.start_date.split("T")[0]:"",due_date:d.due_date?d.due_date.split("T")[0]:"",estimated_hours:d.estimated_hours},console.log("Form data populated:",n.value),ee.value=!0},ae=()=>{oe.value=!1,ee.value=!1,_.value=null,n.value={name:"",description:"",status:"todo",priority:"medium",assignee_id:"",start_date:"",due_date:"",estimated_hours:null},y.value=""},ne=d=>p.value.filter(t=>t.status===d),k=d=>({todo:"bg-gray-100 text-gray-800","in-progress":"bg-blue-100 text-blue-800",review:"bg-yellow-100 text-yellow-800",done:"bg-green-100 text-green-800"})[d]||"bg-gray-100 text-gray-800",b=d=>({todo:"Da fare","in-progress":"In corso",review:"In revisione",done:"Completato"})[d]||d,i=d=>({low:"bg-green-100 text-green-800",medium:"bg-yellow-100 text-yellow-800",high:"bg-orange-100 text-orange-800",urgent:"bg-red-100 text-red-800"})[d]||"bg-gray-100 text-gray-800",r=d=>({low:"Bassa",medium:"Media",high:"Alta",urgent:"Urgente"})[d]||d,c=d=>d?d.split(" ").map(t=>t.charAt(0)).join("").toUpperCase():"",B=d=>new Date(d).toLocaleDateString("it-IT");let ie;const J=()=>{clearTimeout(ie),ie=setTimeout(()=>{T()},300)},he=async()=>{var d,t,Q,N,L;if(!((d=x.project)!=null&&d.id)){w("Progetto non valido","error");return}try{O.value=!0;const V={};$.value.preferences.trim()&&(V.preferences=$.value.preferences);const ce=await we.post(`/api/tasks/${x.project.id}/generate-ai`,V);ce.data.success&&(j.value=ce.data.data,S.value=!1,$.value.preferences="",w({type:"success",title:"Task generati con AI",message:`${(t=ce.data.data.summary)==null?void 0:t.total_tasks} task generati per ${(Q=ce.data.data.summary)==null?void 0:Q.total_estimated_hours}h totali`,duration:5e3}),setTimeout(()=>{const ve=document.querySelector(".bg-purple-50");ve&&ve.scrollIntoView({behavior:"smooth"})},100))}catch(V){console.error("Error generating tasks with AI:",V),w({type:"error",title:"Errore generazione AI",message:((L=(N=V.response)==null?void 0:N.data)==null?void 0:L.message)||"Si è verificato un errore durante la generazione dei task con AI",duration:6e3})}finally{O.value=!1}},ue=async()=>{var d,t,Q,N;if(!((t=(d=j.value)==null?void 0:d.generated_tasks)!=null&&t.length)){w("Nessun task da applicare","warning");return}try{F.value=!0;const L=await we.post(`/api/tasks/${x.project.id}/apply-ai-tasks`,{tasks:j.value.generated_tasks});L.data.success&&(await T(),j.value=null,w({type:"success",title:"Task applicati con successo",message:`${L.data.data.summary.total_created} task creati nel progetto. Tutti i task hanno date di inizio e fine per la visualizzazione Gantt.`,duration:6e3}))}catch(L){console.error("Error applying AI tasks:",L),w({type:"error",title:"Errore applicazione task",message:((N=(Q=L.response)==null?void 0:Q.data)==null?void 0:N.message)||"Si è verificato un errore durante l'applicazione dei task AI",duration:6e3})}finally{F.value=!1}},re=d=>new Intl.NumberFormat("it-IT").format(d||0),Se=()=>{const d=new Date(E.value),t=[],Q=12;for(let N=0;N<Q;N++){const L=new Date(d);G.value==="weeks"?L.setDate(d.getDate()+N*7):G.value==="months"&&L.setMonth(d.getMonth()+N),t.push(L)}z.value=t},Ne=d=>{if(!z.value.length)return null;const t=new Date(d.start_date),Q=new Date(d.due_date),N=z.value[0],V=z.value[z.value.length-1]-N,ce=t-N,ve=Q-t,ke=Math.max(0,ce/V*100),je=Math.min(100-ke,ve/V*100);return{leftPercent:ke,widthPercent:Math.max(5,je)}},Le=d=>G.value==="weeks"?`${d.getDate()}/${d.getMonth()+1}`:G.value==="months"?d.toLocaleDateString("it-IT",{month:"short",year:"2-digit"}):"",qe=d=>{const t=new Date,Q=new Date(d);if(G.value==="weeks"){const N=new Date(Q),L=new Date(Q);return L.setDate(L.getDate()+6),t>=N&&t<=L}else if(G.value==="months")return Q.getMonth()===t.getMonth()&&Q.getFullYear()===t.getFullYear();return!1},Ke=()=>{const d=new Date;if(G.value==="weeks"){const t=new Date(d);t.setDate(d.getDate()-d.getDay()),E.value=t}else{const t=new Date(d.getFullYear(),d.getMonth(),1);E.value=t}Se()},He=d=>({todo:"bg-gray-400","in-progress":"bg-blue-500",review:"bg-yellow-500",done:"bg-green-500"})[d]||"bg-gray-400",Ge=d=>({todo:"bg-gray-500","in-progress":"bg-blue-600",review:"bg-yellow-600",done:"bg-green-600"})[d]||"bg-gray-500",_e=d=>({todo:0,"in-progress":50,review:75,done:100})[d.status]||0;return pe(()=>{var d;return(d=x.project)==null?void 0:d.id},d=>{d&&T()}),pe(()=>{var d;return(d=x.project)==null?void 0:d.team_members},d=>{d&&d.length>0&&console.log("Team members disponibili:",d.length)},{immediate:!0}),be(()=>{var d;(d=x.project)!=null&&d.id&&T(),Se()}),D({refresh:T}),(d,t)=>{var Q,N,L,V,ce,ve,ke,je,Ie,De,Me,Ae,Ee,Ue,Ve;return a(),o("div",er,[e("div",tr,[e("div",sr,[e("div",rr,[t[23]||(t[23]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Task del Progetto",-1)),Z.value?(a(),o("div",ar,[e("button",{onClick:t[0]||(t[0]=l=>S.value=!0),disabled:O.value,class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50"},[O.value?(a(),fe(A,{key:1,name:"arrow-path",size:"sm",class:"mr-2 animate-spin"})):(a(),fe(A,{key:0,name:"light-bulb",size:"sm",class:"mr-2"})),q(" "+s(O.value?"Generando...":"✨ Genera Task con AI"),1)],8,or),e("button",{onClick:t[1]||(t[1]=l=>oe.value=!0),class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[I(A,{name:"plus",size:"sm",class:"mr-2"}),t[22]||(t[22]=q(" Nuovo Task "))])])):h("",!0)]),e("div",nr,[e("div",null,[t[25]||(t[25]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Stato",-1)),U(e("select",{"onUpdate:modelValue":t[2]||(t[2]=l=>M.value.status=l),onChange:T,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},t[24]||(t[24]=[xe('<option value="">Tutti gli stati</option><option value="todo">Da fare</option><option value="in-progress">In corso</option><option value="review">In revisione</option><option value="done">Completato</option>',5)]),544),[[le,M.value.status]])]),e("div",null,[t[27]||(t[27]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Priorità",-1)),U(e("select",{"onUpdate:modelValue":t[3]||(t[3]=l=>M.value.priority=l),onChange:T,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},t[26]||(t[26]=[xe('<option value="">Tutte le priorità</option><option value="low">Bassa</option><option value="medium">Media</option><option value="high">Alta</option><option value="urgent">Urgente</option>',5)]),544),[[le,M.value.priority]])]),e("div",null,[t[29]||(t[29]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Assegnatario",-1)),U(e("select",{"onUpdate:modelValue":t[4]||(t[4]=l=>M.value.assignee_id=l),onChange:T,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[t[28]||(t[28]=e("option",{value:""},"Tutti",-1)),(a(!0),o(W,null,Y(((Q=x.project)==null?void 0:Q.team_members)||[],l=>(a(),o("option",{key:l.id,value:l.id},s(l.full_name),9,ir))),128))],544),[[le,M.value.assignee_id]]),(L=(N=x.project)==null?void 0:N.team_members)!=null&&L.length?h("",!0):(a(),o("span",lr," Nessun membro del team disponibile "))]),e("div",null,[t[30]||(t[30]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ricerca",-1)),U(e("input",{"onUpdate:modelValue":t[5]||(t[5]=l=>M.value.search=l),onInput:J,type:"text",placeholder:"Cerca task...",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,544),[[te,M.value.search]])])]),e("div",dr,[e("div",ur,[t[31]||(t[31]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Vista:",-1)),e("button",{onClick:t[6]||(t[6]=l=>v.value="list"),class:H([v.value==="list"?"bg-primary-100 text-primary-700":"text-gray-500 hover:text-gray-700","px-3 py-1 rounded-md text-sm font-medium"])}," Lista ",2),e("button",{onClick:t[7]||(t[7]=l=>v.value="kanban"),class:H([v.value==="kanban"?"bg-primary-100 text-primary-700":"text-gray-500 hover:text-gray-700","px-3 py-1 rounded-md text-sm font-medium"])}," Kanban ",2),e("button",{onClick:t[8]||(t[8]=l=>v.value="gantt"),class:H([v.value==="gantt"?"bg-primary-100 text-primary-700":"text-gray-500 hover:text-gray-700","px-3 py-1 rounded-md text-sm font-medium"])}," Gantt ",2)]),e("div",cr,s(p.value.length)+" task trovati ",1)])])]),j.value?(a(),o("div",mr,[e("div",gr,[e("div",pr,[e("div",xr,[e("div",yr,[I(A,{name:"light-bulb",size:"md",class:"text-purple-600 dark:text-purple-400"})]),e("div",vr,[t[32]||(t[32]=e("h4",{class:"text-sm font-medium text-purple-900 dark:text-purple-100"}," Task generati con AI ",-1)),e("p",fr,s((V=j.value.summary)==null?void 0:V.total_tasks)+" task · "+s((ce=j.value.summary)==null?void 0:ce.total_estimated_hours)+"h totali · "+s((ve=j.value.summary)==null?void 0:ve.estimated_duration_days)+" giorni stimati ",1)])]),e("div",br,[e("button",{onClick:ue,disabled:F.value,class:"inline-flex items-center px-3 py-1 text-xs font-medium text-white bg-purple-600 rounded hover:bg-purple-700 disabled:opacity-50"},s(F.value?"Applicando...":"Applica Task"),9,hr),e("button",{onClick:t[9]||(t[9]=l=>j.value=null),class:"text-purple-600 hover:text-purple-800 dark:text-purple-400 dark:hover:text-purple-200"},[I(A,{name:"x-mark",size:"sm"})])])])]),e("div",_r,[j.value.project_analysis?(a(),o("div",kr,[t[36]||(t[36]=e("h5",{class:"text-sm font-medium text-purple-900 dark:text-purple-100 mb-2"},"Analisi Progetto AI",-1)),e("div",wr,[e("div",null,[t[33]||(t[33]=e("span",{class:"font-medium text-purple-700 dark:text-purple-300"},"Complessità:",-1)),e("span",$r,s(j.value.project_analysis.complexity_level),1)]),e("div",null,[t[34]||(t[34]=e("span",{class:"font-medium text-purple-700 dark:text-purple-300"},"Metodologia:",-1)),e("span",jr,s(j.value.project_analysis.methodology_recommendation),1)]),e("div",null,[t[35]||(t[35]=e("span",{class:"font-medium text-purple-700 dark:text-purple-300"},"Ore stimate:",-1)),e("span",Tr,s(j.value.project_analysis.estimated_total_hours)+"h",1)])])])):h("",!0),e("div",Cr,[(a(!0),o(W,null,Y(j.value.generated_tasks,(l,de)=>(a(),o("div",{key:de,class:"p-3 bg-white dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-600"},[e("div",Sr,[e("h6",Pr,s(l.name),1),e("div",zr,[e("span",{class:H([i(l.priority),"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium"])},s(r(l.priority)),3),e("span",Ir,s(l.phase),1)])]),e("p",Dr,s(l.description),1),e("div",Mr,[e("div",null,[e("span",Ar,s(l.estimated_hours)+"h",1),l.start_date&&l.due_date?(a(),o("span",Er,s(B(l.start_date))+" → "+s(B(l.due_date)),1)):h("",!0)]),l.dependencies&&l.dependencies.length?(a(),o("div",Ur,s(l.dependencies.length)+" dipendenze ",1)):h("",!0)])]))),128))]),t[37]||(t[37]=e("div",{class:"mt-4 text-xs text-purple-600 dark:text-purple-400"}," 💡 I task sono ordinati logicamente e includono tutte le fasi: analisi, design, sviluppo, testing, deployment e supporto. ",-1))])])):h("",!0),f.value?(a(),o("div",Vr,t[38]||(t[38]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1)]))):h("",!0),y.value?(a(),o("div",Fr,[e("p",Or,s(y.value),1)])):h("",!0),!f.value&&v.value==="list"?(a(),o("div",Rr,[e("div",Br,[t[40]||(t[40]=xe('<div class="bg-gray-50 dark:bg-gray-700 px-6 py-3 grid grid-cols-12 gap-4 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"><div class="col-span-3">Task</div><div class="col-span-2">Assegnatario</div><div class="col-span-1">Stato</div><div class="col-span-1">Priorità</div><div class="col-span-1">Data Inizio</div><div class="col-span-2">Scadenza</div><div class="col-span-1">Ore</div><div class="col-span-1">Azioni</div></div>',1)),(a(!0),o(W,null,Y(p.value,l=>(a(),o("div",{key:l.id,class:"px-6 py-4 grid grid-cols-12 gap-4 items-center hover:bg-gray-50 dark:hover:bg-gray-700"},[e("div",Nr,[e("div",Lr,s(l.name),1),l.description?(a(),o("div",qr,s(l.description),1)):h("",!0)]),e("div",Kr,[l.assignee?(a(),o("div",Hr,[e("div",Gr,s(c(l.assignee.full_name)),1),e("div",Xr,[e("div",Jr,s(l.assignee.full_name),1)])])):(a(),o("span",Wr,"Non assegnato"))]),e("div",Yr,[e("span",{class:H([k(l.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},s(b(l.status)),3)]),e("div",Qr,[e("span",{class:H([i(l.priority),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},s(r(l.priority)),3)]),e("div",Zr,[l.start_date?(a(),o("div",ea,s(B(l.start_date)),1)):(a(),o("span",ta,"-"))]),e("div",sa,[l.due_date?(a(),o("div",ra,s(B(l.due_date)),1)):(a(),o("span",aa,"-"))]),e("div",oa,[e("div",na,[q(s(l.actual_hours||0)+"h ",1),l.estimated_hours?(a(),o("span",ia,"/ "+s(l.estimated_hours)+"h",1)):h("",!0)])]),e("div",la,[e("div",da,[e("button",{onClick:de=>se(l),class:"text-primary-600 hover:text-primary-900 text-sm"}," Modifica ",8,ua)])])]))),128)),p.value.length===0?(a(),o("div",ca,t[39]||(t[39]=[e("p",{class:"text-gray-500 dark:text-gray-400"},"Nessun task trovato",-1)]))):h("",!0)])])):h("",!0),!f.value&&v.value==="kanban"?(a(),o("div",ma,[(a(),o(W,null,Y(R,l=>e("div",{key:l.value,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4"},[e("div",ga,[e("h4",pa,s(l.label),1),e("span",xa,s(ne(l.value).length),1)]),e("div",ya,[(a(!0),o(W,null,Y(ne(l.value),de=>(a(),o("div",{key:de.id,class:"bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600 cursor-pointer hover:shadow-md transition-shadow",onClick:Pe=>se(de)},[e("div",fa,s(de.name),1),de.description?(a(),o("div",ba,s(de.description),1)):h("",!0),e("div",ha,[e("span",{class:H([i(de.priority),"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium"])},s(r(de.priority)),3),de.assignee?(a(),o("div",_a,s(c(de.assignee.full_name)),1)):h("",!0)])],8,va))),128))])])),64))])):h("",!0),!f.value&&v.value==="gantt"?(a(),o("div",ka,[e("div",wa,[e("div",$a,[t[43]||(t[43]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Diagramma di Gantt ",-1)),e("div",ja,[e("div",Ta,[t[42]||(t[42]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Vista:",-1)),U(e("select",{"onUpdate:modelValue":t[10]||(t[10]=l=>G.value=l),onChange:Se,class:"text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:border-primary-500 focus:ring-primary-500"},t[41]||(t[41]=[e("option",{value:"weeks"},"Settimane",-1),e("option",{value:"months"},"Mesi",-1)]),544),[[le,G.value]])]),e("button",{onClick:Ke,class:"px-3 py-1 text-sm bg-primary-100 text-primary-700 rounded-md hover:bg-primary-200"}," Oggi ")])])]),me.value.length>0?(a(),o("div",Ca,[e("div",Sa,[e("div",Pa,[e("div",za,[t[44]||(t[44]=e("div",{class:"w-80 flex-shrink-0 px-4 py-2 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"}," Task ",-1)),e("div",Ia,[(a(!0),o(W,null,Y(z.value,(l,de)=>(a(),o("div",{key:de,class:H(["flex-1 text-xs text-center text-gray-500 dark:text-gray-400 py-2 border-l border-gray-200 dark:border-gray-600",{"bg-blue-50 dark:bg-blue-900":qe(l)}])},s(Le(l)),3))),128))])]),e("div",Da,[(a(!0),o(W,null,Y(me.value,l=>(a(),o("div",{key:l.id,class:"flex items-center hover:bg-gray-50 dark:hover:bg-gray-700 rounded"},[e("div",Ma,[e("div",Aa,[e("div",{class:H(["w-3 h-3 rounded-full",He(l.status)])},null,2),e("div",Ea,[e("p",Ua,s(l.name),1),e("div",Va,[l.assignee?(a(),o("span",Fa,s(l.assignee.full_name),1)):h("",!0),e("span",{class:H(["inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium",i(l.priority)])},s(r(l.priority)),3)]),e("div",Oa,s(B(l.start_date))+" - "+s(B(l.due_date)),1)])])]),e("div",Ra,[l.timeline?(a(),o("div",{key:0,class:H(["absolute h-6 rounded-md flex items-center justify-between px-2 text-xs text-white font-medium shadow-sm cursor-pointer",Ge(l.status)]),style:ye({left:l.timeline.leftPercent+"%",width:l.timeline.widthPercent+"%",minWidth:"60px"}),title:`${l.name} - ${_e(l)}% completato`},[e("span",Na,s(l.name.length>15?l.name.substring(0,15)+"...":l.name),1),e("span",La,s(_e(l))+"%",1)],14,Ba)):h("",!0),l.timeline&&_e(l)>0&&_e(l)<100?(a(),o("div",{key:1,class:"absolute h-6 rounded-md bg-green-600 opacity-80",style:ye({left:l.timeline.leftPercent+"%",width:l.timeline.widthPercent*_e(l)/100+"%",minWidth:"2px"})},null,4)):h("",!0),(a(!0),o(W,null,Y(z.value,(de,Pe)=>(a(),o("div",{key:Pe,class:"absolute top-0 bottom-0 border-l border-gray-200 dark:border-gray-600",style:ye({left:Pe/z.value.length*100+"%"})},null,4))),128))])]))),128))])])]),t[45]||(t[45]=xe('<div class="mt-6 flex items-center space-x-6 text-xs"><div class="flex items-center space-x-2"><div class="w-3 h-3 bg-gray-400 rounded"></div><span class="text-gray-600 dark:text-gray-400">Da fare</span></div><div class="flex items-center space-x-2"><div class="w-3 h-3 bg-blue-500 rounded"></div><span class="text-gray-600 dark:text-gray-400">In corso</span></div><div class="flex items-center space-x-2"><div class="w-3 h-3 bg-yellow-500 rounded"></div><span class="text-gray-600 dark:text-gray-400">In revisione</span></div><div class="flex items-center space-x-2"><div class="w-3 h-3 bg-green-500 rounded"></div><span class="text-gray-600 dark:text-gray-400">Completato</span></div><div class="flex items-center space-x-2"><div class="w-0.5 h-4 bg-red-500"></div><span class="text-gray-600 dark:text-gray-400">Oggi</span></div></div>',1))])):(a(),o("div",qa,[I(A,{name:"calendar",size:"xl",class:"mx-auto text-gray-400"}),t[46]||(t[46]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun task pianificato",-1)),t[47]||(t[47]=e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"I task con date di inizio e fine appariranno nel diagramma di Gantt.",-1))]))])):h("",!0),oe.value||ee.value?(a(),o("div",{key:6,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:ae},[e("div",{class:"relative top-20 mx-auto p-5 border w-[500px] shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:t[19]||(t[19]=ge(()=>{},["stop"]))},[e("div",Ka,[e("h3",Ha,s(ee.value?"Modifica Task":"Nuovo Task"),1),e("form",{onSubmit:ge(X,["prevent"])},[e("div",Ga,[y.value?(a(),o("div",Xa,s(y.value),1)):h("",!0),e("div",null,[t[48]||(t[48]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},[q("Nome "),e("span",{class:"text-red-500"},"*")],-1)),U(e("input",{"onUpdate:modelValue":t[11]||(t[11]=l=>n.value.name=l),type:"text",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[te,n.value.name]])]),e("div",null,[t[49]||(t[49]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Descrizione",-1)),U(e("textarea",{"onUpdate:modelValue":t[12]||(t[12]=l=>n.value.description=l),rows:"3",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[te,n.value.description]])]),e("div",Ja,[e("div",null,[t[51]||(t[51]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Stato",-1)),U(e("select",{"onUpdate:modelValue":t[13]||(t[13]=l=>n.value.status=l),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},t[50]||(t[50]=[e("option",{value:"todo"},"Da fare",-1),e("option",{value:"in-progress"},"In corso",-1),e("option",{value:"review"},"In revisione",-1),e("option",{value:"done"},"Completato",-1)]),512),[[le,n.value.status]])]),e("div",null,[t[53]||(t[53]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Priorità",-1)),U(e("select",{"onUpdate:modelValue":t[14]||(t[14]=l=>n.value.priority=l),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},t[52]||(t[52]=[e("option",{value:"low"},"Bassa",-1),e("option",{value:"medium"},"Media",-1),e("option",{value:"high"},"Alta",-1),e("option",{value:"urgent"},"Urgente",-1)]),512),[[le,n.value.priority]])])]),e("div",null,[t[55]||(t[55]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Assegnatario",-1)),U(e("select",{"onUpdate:modelValue":t[15]||(t[15]=l=>n.value.assignee_id=l),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[t[54]||(t[54]=e("option",{value:""},"Non assegnato",-1)),(a(!0),o(W,null,Y(((ke=x.project)==null?void 0:ke.team_members)||[],l=>(a(),o("option",{key:l.id,value:l.id},s(l.full_name),9,Wa))),128))],512),[[le,n.value.assignee_id]]),(Ie=(je=x.project)==null?void 0:je.team_members)!=null&&Ie.length?h("",!0):(a(),o("span",Ya," Nessun membro del team disponibile "))]),e("div",Qa,[e("div",null,[t[56]||(t[56]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Data Inizio",-1)),U(e("input",{"onUpdate:modelValue":t[16]||(t[16]=l=>n.value.start_date=l),type:"date",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[te,n.value.start_date]])]),e("div",null,[t[57]||(t[57]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Scadenza",-1)),U(e("input",{"onUpdate:modelValue":t[17]||(t[17]=l=>n.value.due_date=l),type:"date",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[te,n.value.due_date]])])]),e("div",null,[t[58]||(t[58]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ore stimate",-1)),U(e("input",{"onUpdate:modelValue":t[18]||(t[18]=l=>n.value.estimated_hours=l),type:"number",step:"0.5",min:"0",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[te,n.value.estimated_hours]])])]),e("div",Za,[e("button",{type:"button",onClick:ae,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md"}," Annulla "),e("button",{type:"submit",disabled:u.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50"},s(u.value?"Salvataggio...":ee.value?"Aggiorna":"Crea"),9,eo)])],32)])])])):h("",!0),S.value?(a(),o("div",to,[e("div",so,[e("div",ro,[t[69]||(t[69]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," ✨ Genera Task con AI ",-1)),e("div",ao,[t[65]||(t[65]=e("h4",{class:"text-sm font-medium text-purple-900 dark:text-purple-100 mb-2"},"Informazioni Progetto",-1)),e("div",oo,[e("div",null,[t[59]||(t[59]=e("span",{class:"font-medium"},"Nome:",-1)),q(" "+s((De=x.project)==null?void 0:De.name),1)]),e("div",null,[t[60]||(t[60]=e("span",{class:"font-medium"},"Tipo:",-1)),q(" "+s((Me=x.project)==null?void 0:Me.project_type),1)]),(Ae=x.project)!=null&&Ae.budget?(a(),o("div",no,[t[61]||(t[61]=e("span",{class:"font-medium"},"Budget:",-1)),q(" €"+s(re(x.project.budget)),1)])):h("",!0),(Ee=x.project)!=null&&Ee.client?(a(),o("div",io,[t[62]||(t[62]=e("span",{class:"font-medium"},"Cliente:",-1)),q(" "+s(x.project.client.name),1)])):h("",!0),(Ue=x.project)!=null&&Ue.start_date?(a(),o("div",lo,[t[63]||(t[63]=e("span",{class:"font-medium"},"Inizio:",-1)),q(" "+s(B(x.project.start_date)),1)])):h("",!0),(Ve=x.project)!=null&&Ve.end_date?(a(),o("div",uo,[t[64]||(t[64]=e("span",{class:"font-medium"},"Fine:",-1)),q(" "+s(B(x.project.end_date)),1)])):h("",!0)])]),e("form",{onSubmit:ge(he,["prevent"])},[e("div",co,[e("div",null,[t[66]||(t[66]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Preferenze Generazione (opzionale) ",-1)),U(e("textarea",{"onUpdate:modelValue":t[20]||(t[20]=l=>$.value.preferences=l),rows:"4",class:"mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500",placeholder:"Eventuali preferenze specifiche per la generazione dei task (es. metodologie particolari, focus su determinate aree, vincoli temporali specifici...)"},null,512),[[te,$.value.preferences]]),t[67]||(t[67]=e("p",{class:"mt-1 text-xs text-gray-500 dark:text-gray-400"}," L'AI analizzerà automaticamente il progetto e genererà task per tutte le fasi: analisi, design, sviluppo, testing, deployment e supporto post-produzione. ",-1))]),e("div",mo,[e("div",go,[I(A,{name:"exclamation-triangle",size:"md",class:"text-yellow-400 mr-2"}),t[68]||(t[68]=e("div",{class:"text-sm"},[e("h4",{class:"font-medium text-yellow-800 dark:text-yellow-200"},"Informazione importante"),e("p",{class:"text-yellow-700 dark:text-yellow-300 text-xs mt-1"}," I task generati saranno mostrati in anteprima. Potrai rivederli prima di applicarli effettivamente al progetto. ")],-1))])])]),e("div",po,[e("button",{type:"button",onClick:t[21]||(t[21]=l=>S.value=!1),class:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 rounded-md hover:bg-gray-200 dark:hover:bg-gray-500"}," Annulla "),e("button",{type:"submit",disabled:O.value,class:"px-4 py-2 text-sm font-medium text-white bg-purple-600 rounded-md hover:bg-purple-700 disabled:opacity-50"},s(O.value?"Generando task...":"✨ Genera Task"),9,xo)])],32)])])])):h("",!0)])}}},vo={class:"space-y-6"},fo={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},bo={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},ho={class:"flex items-center justify-between"},_o={class:"flex items-center"},ko=["disabled"],wo={class:"p-6 border-b border-gray-200 dark:border-gray-700"},$o={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},jo={class:"text-center"},To={class:"text-2xl font-bold text-primary-600 dark:text-primary-400"},Co={class:"text-center"},So={class:"text-2xl font-bold text-green-600"},Po={class:"text-center"},zo={class:"text-2xl font-bold text-blue-600"},Io={class:"text-center"},Do={class:"text-2xl font-bold text-purple-600"},Mo={key:0,class:"p-6 border-b border-gray-200 dark:border-gray-700 bg-indigo-50 dark:bg-indigo-900/20"},Ao={class:"text-md font-medium text-gray-900 dark:text-white mb-4 flex items-center"},Eo={class:"space-y-2"},Uo=["onClick"],Vo={class:"p-6"},Fo={class:"space-y-4"},Oo={class:"flex items-center justify-between"},Ro={class:"flex items-center space-x-4"},Bo={class:"flex-shrink-0"},No=["src","alt"],Lo={key:1,class:"w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center"},qo={class:"text-sm font-medium text-gray-600 dark:text-gray-300"},Ko={class:"flex-1"},Ho={class:"flex items-center space-x-2"},Go={class:"text-lg font-medium text-gray-900 dark:text-white"},Xo={key:0,class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"},Jo={class:"text-sm text-gray-600 dark:text-gray-400"},Wo={class:"text-xs text-gray-500 dark:text-gray-500"},Yo={key:0,class:"text-xs text-blue-600 dark:text-blue-400 mt-1"},Qo={class:"flex items-center space-x-4"},Zo={class:"text-right"},en={class:"text-sm font-medium text-gray-900 dark:text-white"},tn={class:"text-right"},sn={class:"text-sm font-medium text-gray-900 dark:text-white"},rn={class:"text-right"},an={class:"text-sm font-medium text-gray-900 dark:text-white"},on={class:"flex items-center space-x-2"},nn=["onClick"],ln=["onClick"],dn={class:"mt-4"},un={class:"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-1"},cn={class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"},mn={key:0,class:"text-center py-8"},gn={class:"mt-6"},pn={class:"mt-3"},xn={class:"space-y-4"},yn=["value"],vn={class:"flex justify-end space-x-3 mt-6"},fn=["disabled"],bn={class:"mt-3"},hn={class:"space-y-4"},_n={class:"flex justify-end space-x-3 mt-6"},kn=["disabled"],wn={key:2,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},$n={class:"text-md font-medium text-gray-900 dark:text-white mb-4 flex items-center"},jn={class:"space-y-2"},Tn=["onClick"],Cn={__name:"ProjectTeam",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["refresh"],setup(g,{expose:D,emit:x}){const P=g,w=$e(),p=C(!1),f=C(!1),y=C(!1),v=C(null),u=C([]),S=C(!1),O=C({id:null,role:"",allocation_percentage:100}),F=C({user_id:"",role:"",allocation_percentage:100}),j=K(()=>{var b;return((b=P.project)==null?void 0:b.team_members)||[]}),$=K(()=>j.value.reduce((b,i)=>b+(i.hours_worked||0),0)),G=K(()=>j.value.length===0?0:Math.round($.value/j.value.length)),E=K(()=>j.value.filter(b=>(b.hours_worked||0)>0).length),z=b=>b?b.split(" ").map(i=>i.charAt(0).toUpperCase()).slice(0,2).join(""):"??",M=b=>{var r;return(((r=P.project)==null?void 0:r.tasks)||[]).filter(c=>c.assignee_id===b).length},oe=b=>{var r;return(((r=P.project)==null?void 0:r.tasks)||[]).filter(c=>c.assignee_id===b&&c.status==="done").length},ee=b=>{const i=M(b),r=oe(b);return i===0?0:Math.round(r/i*100)},_=b=>{const i=ee(b);return i>=80?"bg-green-600":i>=60?"bg-yellow-600":i>=40?"bg-orange-600":"bg-red-600"},n=b=>!b||b===0?"0.00":parseFloat(b).toFixed(2),Z=async()=>{var b;try{const i=await fetch("/api/personnel/users",{headers:{"Content-Type":"application/json","X-CSRFToken":w.csrfToken}});if(i.ok){const r=await i.json(),c=j.value.map(B=>B.id);u.value=(b=r.data)!=null&&b.users?r.data.users.filter(B=>!c.includes(B.id)):[]}}catch(i){console.error("Errore nel caricamento utenti:",i),u.value=[]}},R=async()=>{S.value=!0;try{const b=await fetch(`/api/projects/${P.project.id}/team`,{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":w.csrfToken},body:JSON.stringify({user_id:F.value.user_id,role:F.value.role,allocation_percentage:parseInt(F.value.allocation_percentage,10)})});if(b.ok)k("refresh"),X();else{const i=await b.json();alert(i.message||"Errore nell'aggiunta del membro")}}catch{alert("Errore nell'aggiunta del membro")}finally{S.value=!1}},me=b=>{O.value={id:b.id,role:b.role||"",allocation_percentage:b.allocation_percentage||100},f.value=!0},T=async()=>{if(O.value.id){S.value=!0;try{const b=await fetch(`/api/projects/${P.project.id}/team/${O.value.id}`,{method:"PUT",headers:{"Content-Type":"application/json","X-CSRFToken":w.csrfToken},body:JSON.stringify({role:O.value.role,allocation_percentage:parseInt(O.value.allocation_percentage,10)})});if(b.ok)k("refresh"),f.value=!1;else{const i=await b.json();alert(i.message||"Errore nella modifica del membro")}}catch{alert("Errore nella modifica del membro")}finally{S.value=!1}}},m=async b=>{if(confirm(`Rimuovere ${b.full_name} dal progetto?`))try{const i=await fetch(`/api/projects/${P.project.id}/team/${b.id}`,{method:"DELETE",headers:{"Content-Type":"application/json","X-CSRFToken":w.csrfToken}});if(i.ok)k("refresh");else{const r=await i.json();alert(r.message||"Errore nella rimozione del membro")}}catch{alert("Errore nella rimozione del membro")}},X=()=>{p.value=!1,F.value={user_id:"",role:"",allocation_percentage:100}},se=()=>{f.value=!1,O.value={id:null,role:"",allocation_percentage:100}},ae=async()=>{var b,i;if((b=P.project)!=null&&b.id){y.value=!0;try{const r=await fetch(`/api/ai-resources/analyze-allocation/${P.project.id}`,{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":w.csrfToken},body:JSON.stringify({include_suggestions:!0,analysis_depth:"detailed"})});if(r.ok){const c=await r.json();v.value=((i=c.data)==null?void 0:i.analysis)||null}}finally{y.value=!1}}},ne=async b=>{try{const i=await fetch(`/api/projects/${P.project.id}/team`,{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":w.csrfToken},body:JSON.stringify({user_id:b.user_id,role:b.role,allocation_percentage:parseInt(b.allocation,10)})});if(i.ok)k("refresh");else{const r=await i.json();alert(r.message||"Errore nell'applicazione della raccomandazione")}}catch(i){console.error(i),alert("Errore nell'applicazione della raccomandazione")}},k=x;return be(()=>{Z()}),pe(()=>p.value,b=>{b&&Z()}),pe(()=>{var b;return(b=P.project)==null?void 0:b.team_members},()=>{p.value&&Z()}),D({refresh:()=>{Z(),k("refresh")}}),(b,i)=>(a(),o("div",vo,[e("div",fo,[e("div",bo,[e("div",ho,[i[10]||(i[10]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Team del Progetto ",-1)),e("div",_o,[e("button",{onClick:ae,disabled:y.value,class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"},[I(A,{name:"sparkles",size:"sm",class:"mr-2"}),q(" "+s(y.value?"Analisi…":"Analisi AI"),1)],8,ko),e("button",{onClick:i[0]||(i[0]=r=>p.value=!0),"data-testid":"add-team-member-button",class:"ml-3 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[I(A,{name:"plus",size:"sm",class:"mr-2"}),i[9]||(i[9]=q(" Aggiungi Membro "))])])])]),e("div",wo,[e("div",$o,[e("div",jo,[e("div",To,s(j.value.length),1),i[11]||(i[11]=e("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Membri Totali",-1))]),e("div",Co,[e("div",So,s($.value)+"h",1),i[12]||(i[12]=e("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Ore Totali",-1))]),e("div",Po,[e("div",zo,s(G.value)+"h",1),i[13]||(i[13]=e("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Media per Membro",-1))]),e("div",Io,[e("div",Do,s(E.value),1),i[14]||(i[14]=e("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Membri Attivi",-1))])])]),v.value?(a(),o("div",Mo,[e("h4",Ao,[I(A,{name:"light-bulb",size:"md",class:"text-indigo-600 mr-2"}),i[15]||(i[15]=q(" Suggerimenti AI per l'allocazione del team "))]),e("ul",Eo,[(a(!0),o(W,null,Y(v.value.recommended_allocations,r=>(a(),o("li",{key:r.user_id,class:"flex items-center justify-between p-2 rounded-md bg-white dark:bg-gray-800 shadow-sm"},[e("span",null,s(r.user_name)+" – "+s(r.role)+" ("+s(r.allocation)+"%)",1),e("button",{onClick:c=>ne(r),class:"text-sm text-white bg-indigo-600 hover:bg-indigo-700 px-3 py-1 rounded-md"},"Applica",8,Uo)]))),128))])])):h("",!0),e("div",Vo,[e("div",Fo,[(a(!0),o(W,null,Y(j.value,r=>{var c,B;return a(),o("div",{key:r.id,class:"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-all duration-200"},[e("div",Oo,[e("div",Ro,[e("div",Bo,[r.profile_image?(a(),o("img",{key:0,src:r.profile_image,alt:r.full_name,class:"w-12 h-12 rounded-full"},null,8,No)):(a(),o("div",Lo,[e("span",qo,s(z(r.full_name)),1)]))]),e("div",Ko,[e("div",Ho,[e("h4",Go,s(r.full_name),1),r.id===((c=g.project)==null?void 0:c.manager_id)?(a(),o("span",Xo," Project Manager ")):h("",!0)]),e("p",Jo,s(r.role||"Team Member"),1),e("p",Wo,s(r.email),1),r.allocation_percentage?(a(),o("p",Yo," Allocazione: "+s(r.allocation_percentage)+"% ",1)):h("",!0)])]),e("div",Qo,[e("div",Zo,[e("div",en,s(n(r.hours_worked||0))+"h",1),i[16]||(i[16]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"ore lavorate",-1))]),e("div",tn,[e("div",sn,s(M(r.id)),1),i[17]||(i[17]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"task assegnati",-1))]),e("div",rn,[e("div",an,s(oe(r.id)),1),i[18]||(i[18]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"completati",-1))]),e("div",on,[e("button",{onClick:ie=>me(r),class:"p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",title:"Modifica membro"},[I(A,{name:"pencil",size:"sm"})],8,nn),r.id!==((B=g.project)==null?void 0:B.manager_id)?(a(),o("button",{key:0,onClick:ie=>m(r),"data-testid":"remove-team-member-button",class:"p-1 text-gray-400 hover:text-red-600",title:"Rimuovi dal progetto"},[I(A,{name:"trash",size:"sm"})],8,ln)):h("",!0)])])]),e("div",dn,[e("div",un,[i[19]||(i[19]=e("span",null,"Produttività",-1)),e("span",null,s(ee(r.id))+"%",1)]),e("div",cn,[e("div",{class:H(["h-2 rounded-full transition-all duration-300",_(r.id)]),style:ye({width:ee(r.id)+"%"})},null,6)])])])}),128)),j.value.length===0?(a(),o("div",mn,[I(A,{name:"users",size:"xl",class:"mx-auto text-gray-400"}),i[21]||(i[21]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun membro del team",-1)),i[22]||(i[22]=e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"Inizia aggiungendo membri al progetto.",-1)),e("div",gn,[e("button",{onClick:i[1]||(i[1]=r=>p.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"},[I(A,{name:"plus",size:"md",class:"-ml-1 mr-2"}),i[20]||(i[20]=q(" Aggiungi primo membro "))])])])):h("",!0)])])]),p.value?(a(),o("div",{key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:X},[e("div",{class:"relative top-20 mx-auto p-5 border w-[500px] shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:i[5]||(i[5]=ge(()=>{},["stop"]))},[e("div",pn,[i[28]||(i[28]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Aggiungi Membro al Team ",-1)),e("form",{onSubmit:ge(R,["prevent"])},[e("div",xn,[e("div",null,[i[24]||(i[24]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Utente",-1)),U(e("select",{"onUpdate:modelValue":i[2]||(i[2]=r=>F.value.user_id=r),required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[i[23]||(i[23]=e("option",{value:""},"Seleziona utente",-1)),(a(!0),o(W,null,Y(u.value,r=>(a(),o("option",{key:r.id,value:r.id},s(r.full_name)+" ("+s(r.email)+") ",9,yn))),128))],512),[[le,F.value.user_id]])]),e("div",null,[i[26]||(i[26]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ruolo",-1)),U(e("select",{"onUpdate:modelValue":i[3]||(i[3]=r=>F.value.role=r),required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},i[25]||(i[25]=[xe('<option value="">Seleziona ruolo</option><option value="Team Member">Team Member</option><option value="Developer">Developer</option><option value="Designer">Designer</option><option value="QA Tester">QA Tester</option><option value="Business Analyst">Business Analyst</option><option value="Technical Lead">Technical Lead</option>',7)]),512),[[le,F.value.role]])]),e("div",null,[i[27]||(i[27]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Percentuale di Allocazione",-1)),U(e("input",{"onUpdate:modelValue":i[4]||(i[4]=r=>F.value.allocation_percentage=r),type:"number",min:"0",max:"100",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[te,F.value.allocation_percentage]])])]),e("div",vn,[e("button",{type:"button",onClick:X,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md"}," Annulla "),e("button",{type:"submit",disabled:S.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50"},s(S.value?"Aggiungendo...":"Aggiungi"),9,fn)])],32)])])])):h("",!0),f.value?(a(),o("div",{key:1,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:se},[e("div",{class:"relative top-20 mx-auto p-5 border w-[500px] shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:i[8]||(i[8]=ge(()=>{},["stop"]))},[e("div",bn,[i[32]||(i[32]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Modifica Membro ",-1)),e("form",{onSubmit:ge(T,["prevent"])},[e("div",hn,[e("div",null,[i[30]||(i[30]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ruolo",-1)),U(e("select",{"onUpdate:modelValue":i[6]||(i[6]=r=>O.value.role=r),required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},i[29]||(i[29]=[xe('<option value="">Seleziona ruolo</option><option value="Team Member">Team Member</option><option value="Developer">Developer</option><option value="Designer">Designer</option><option value="QA Tester">QA Tester</option><option value="Business Analyst">Business Analyst</option><option value="Technical Lead">Technical Lead</option>',7)]),512),[[le,O.value.role]])]),e("div",null,[i[31]||(i[31]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Percentuale di Allocazione",-1)),U(e("input",{"onUpdate:modelValue":i[7]||(i[7]=r=>O.value.allocation_percentage=r),type:"number",min:"0",max:"100",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[te,O.value.allocation_percentage]])])]),e("div",_n,[e("button",{type:"button",onClick:se,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md"}," Annulla "),e("button",{type:"submit",disabled:S.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50"},s(S.value?"Aggiornando...":"Aggiorna"),9,kn)])],32)])])])):h("",!0),v.value?(a(),o("div",wn,[e("h4",$n,[I(A,{name:"light-bulb",size:"md",class:"text-indigo-600 mr-2"}),i[33]||(i[33]=q(" Suggerimenti AI per l'allocazione del team "))]),e("ul",jn,[(a(!0),o(W,null,Y(v.value.recommended_allocations,r=>(a(),o("li",{key:r.user_id,class:"flex items-center justify-between"},[e("span",null,s(r.user_name)+" – "+s(r.role)+" ("+s(r.allocation)+"%)",1),e("button",{onClick:c=>ne(r),class:"text-sm text-white bg-primary-600 hover:bg-primary-700 px-3 py-1 rounded-md"},"Applica",8,Tn)]))),128))])])):h("",!0)]))}},Sn={class:"fixed inset-0 z-50 overflow-y-auto"},Pn={class:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},zn={class:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"},In={class:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4"},Dn={class:"mb-4"},Mn={class:"text-lg font-medium text-gray-900 dark:text-white"},An={class:"space-y-4"},En={class:"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse"},Un=["disabled"],Vn={key:0},Fn={key:1},On={__name:"ExpenseModal",props:{projectId:{type:[String,Number],required:!0},expense:{type:Object,default:null}},emits:["close","saved"],setup(g,{emit:D}){const x=g,P=D,w=C(!1),p=Xe({description:"",amount:0,category:"",billing_type:"billable",status:"pending",date:new Date().toISOString().split("T")[0],notes:"",receipt_file:null}),f=async()=>{w.value=!0;try{const v=x.expense?`/api/expenses/${x.expense.id}`:`/api/projects/${x.projectId}/expenses`,u=x.expense?"PUT":"POST";(await fetch(v,{method:u,headers:{"Content-Type":"application/json"},body:JSON.stringify(p)})).ok?P("saved"):console.error("Error saving expense")}catch(v){console.error("Error saving expense:",v)}finally{w.value=!1}},y=v=>{const u=v.target.files[0];if(u){if(u.size>5*1024*1024){alert("Il file è troppo grande. Dimensione massima: 5MB"),v.target.value="";return}p.receipt_file=u}};return be(()=>{x.expense&&Object.assign(p,{description:x.expense.description,amount:x.expense.amount,category:x.expense.category,billing_type:x.expense.billing_type||"billable",status:x.expense.status||"pending",date:x.expense.date.split("T")[0],notes:x.expense.notes||""})}),(v,u)=>(a(),o("div",Sn,[e("div",Pn,[e("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:u[0]||(u[0]=S=>v.$emit("close"))}),e("div",zn,[e("form",{onSubmit:ge(f,["prevent"])},[e("div",In,[e("div",Dn,[e("h3",Mn,s(g.expense?"Modifica Spesa":"Aggiungi Spesa"),1)]),e("div",An,[e("div",null,[u[9]||(u[9]=e("label",{for:"description",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Descrizione ",-1)),U(e("input",{"onUpdate:modelValue":u[1]||(u[1]=S=>p.description=S),type:"text",id:"description",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white",placeholder:"Descrizione della spesa"},null,512),[[te,p.description]])]),e("div",null,[u[10]||(u[10]=e("label",{for:"amount",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Importo (€) ",-1)),U(e("input",{"onUpdate:modelValue":u[2]||(u[2]=S=>p.amount=S),type:"number",step:"0.01",id:"amount",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white",placeholder:"0.00"},null,512),[[te,p.amount,void 0,{number:!0}]])]),e("div",null,[u[12]||(u[12]=e("label",{for:"category",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Categoria ",-1)),U(e("select",{"onUpdate:modelValue":u[3]||(u[3]=S=>p.category=S),id:"category",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},u[11]||(u[11]=[xe('<option value="">Seleziona categoria</option><option value="licenses">📄 Licenze</option><option value="travel">✈️ Viaggi</option><option value="meals">🍽️ Pasti</option><option value="equipment">🖥️ Attrezzature</option><option value="external">🏢 Servizi Esterni</option><option value="other">📦 Altro</option>',7)]),512),[[le,p.category]])]),e("div",null,[u[14]||(u[14]=e("label",{for:"billing_type",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Tipo Fatturazione ",-1)),U(e("select",{"onUpdate:modelValue":u[4]||(u[4]=S=>p.billing_type=S),id:"billing_type",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},u[13]||(u[13]=[e("option",{value:"billable"},"💰 Fatturabile al Cliente",-1),e("option",{value:"non-billable"},"🏢 Assorbimento Interno",-1),e("option",{value:"reimbursable"},"💳 Rimborsabile",-1)]),512),[[le,p.billing_type]])]),e("div",null,[u[16]||(u[16]=e("label",{for:"status",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Stato ",-1)),U(e("select",{"onUpdate:modelValue":u[5]||(u[5]=S=>p.status=S),id:"status",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},u[15]||(u[15]=[e("option",{value:"pending"},"⏳ In Attesa di Approvazione",-1),e("option",{value:"approved"},"✅ Approvata",-1),e("option",{value:"rejected"},"❌ Rifiutata",-1)]),512),[[le,p.status]])]),e("div",null,[u[17]||(u[17]=e("label",{for:"date",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Data ",-1)),U(e("input",{"onUpdate:modelValue":u[6]||(u[6]=S=>p.date=S),type:"date",id:"date",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},null,512),[[te,p.date]])]),e("div",null,[u[18]||(u[18]=e("label",{for:"notes",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Note (opzionale) ",-1)),U(e("textarea",{"onUpdate:modelValue":u[7]||(u[7]=S=>p.notes=S),id:"notes",rows:"3",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white",placeholder:"Note aggiuntive..."},null,512),[[te,p.notes]])]),e("div",null,[u[19]||(u[19]=e("label",{for:"receipt",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Ricevuta/Scontrino ",-1)),e("input",{type:"file",id:"receipt",accept:"image/*,.pdf",onChange:y,class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary-50 file:text-primary-700 hover:file:bg-primary-100"},null,32),u[20]||(u[20]=e("p",{class:"mt-1 text-xs text-gray-500 dark:text-gray-400"}," Carica immagine o PDF della ricevuta (max 5MB) ",-1))])])]),e("div",En,[e("button",{type:"submit",disabled:w.value,class:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50"},[w.value?(a(),o("span",Vn,"Salvando...")):(a(),o("span",Fn,s(g.expense?"Aggiorna":"Salva"),1))],8,Un),e("button",{type:"button",onClick:u[8]||(u[8]=S=>v.$emit("close")),class:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"}," Annulla ")])],32)])])]))}},Rn={class:"project-expenses"},Bn={class:"space-y-6"},Nn={class:"flex justify-between items-center"},Ln={key:0,class:"text-center py-8"},qn={key:1,class:"text-center py-12"},Kn={key:2,class:"bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md"},Hn={class:"divide-y divide-gray-200 dark:divide-gray-700"},Gn={class:"flex items-center justify-between"},Xn={class:"flex-1"},Jn={class:"flex items-center"},Wn={class:"flex-shrink-0"},Yn={class:"h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center"},Qn={class:"ml-4 flex-1"},Zn={class:"flex items-center justify-between"},ei={class:"text-sm font-medium text-gray-900 dark:text-white"},ti={class:"ml-2 flex-shrink-0"},si={class:"text-sm font-medium text-gray-900 dark:text-white"},ri={class:"mt-1 flex items-center text-sm text-gray-500 dark:text-gray-400"},ai={class:"capitalize"},oi={key:0,class:"mx-2"},ni={key:1},ii={key:0,class:"flex items-center space-x-2"},li=["onClick"],di=["onClick"],ui={key:3,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4"},ci={class:"flex justify-between items-center"},mi={class:"text-lg font-bold text-gray-900 dark:text-white"},gi={__name:"ProjectExpenses",props:{project:{type:Object,required:!0},loading:{type:Boolean,default:!1}},setup(g){const D=g;Be();const x=$e(),P=C(!1),w=C([]),p=C(!1),f=C(null),y=K(()=>x.hasPermission("manage_expenses")),v=K(()=>w.value.reduce((E,z)=>E+z.amount,0)),u=async()=>{var E;if((E=D.project)!=null&&E.id){P.value=!0;try{const z=await fetch(`/api/projects/${D.project.id}/expenses`);z.ok&&(w.value=await z.json())}catch(z){console.error("Error loading expenses:",z)}finally{P.value=!1}}},S=E=>{f.value=E,p.value=!0},O=async E=>{if(confirm("Sei sicuro di voler eliminare questa spesa?"))try{(await fetch(`/api/expenses/${E}`,{method:"DELETE"})).ok&&(w.value=w.value.filter(M=>M.id!==E))}catch(z){console.error("Error deleting expense:",z)}},F=()=>{p.value=!1,f.value=null},j=()=>{F(),u()},$=E=>new Intl.NumberFormat("it-IT",{minimumFractionDigits:2,maximumFractionDigits:2}).format(E),G=E=>new Date(E).toLocaleDateString("it-IT");return be(()=>{u()}),(E,z)=>(a(),o("div",Rn,[e("div",Bn,[e("div",Nn,[z[2]||(z[2]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Spese Progetto",-1)),y.value?(a(),o("button",{key:0,onClick:z[0]||(z[0]=M=>p.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[I(Te(tt),{class:"w-4 h-4 mr-2"}),z[1]||(z[1]=q(" Aggiungi Spesa "))])):h("",!0)]),P.value?(a(),o("div",Ln,z[3]||(z[3]=[e("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1),e("p",{class:"mt-2 text-sm text-gray-500"},"Caricamento spese...",-1)]))):w.value.length===0?(a(),o("div",qn,[I(Te(Fe),{class:"mx-auto h-12 w-12 text-gray-400"}),z[4]||(z[4]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessuna spesa",-1)),z[5]||(z[5]=e("p",{class:"mt-1 text-sm text-gray-500"},"Non ci sono ancora spese registrate per questo progetto.",-1))])):(a(),o("div",Kn,[e("ul",Hn,[(a(!0),o(W,null,Y(w.value,M=>(a(),o("li",{key:M.id,class:"px-6 py-4"},[e("div",Gn,[e("div",Xn,[e("div",Jn,[e("div",Wn,[e("div",Yn,[I(Te(Fe),{class:"h-5 w-5 text-gray-600 dark:text-gray-300"})])]),e("div",Qn,[e("div",Zn,[e("p",ei,s(M.description),1),e("div",ti,[e("p",si," €"+s($(M.amount)),1)])]),e("div",ri,[I(Te(st),{class:"flex-shrink-0 mr-1.5 h-4 w-4"}),q(" "+s(G(M.date))+" ",1),z[6]||(z[6]=e("span",{class:"mx-2"},"•",-1)),e("span",ai,s(M.category),1),M.user?(a(),o("span",oi,"•")):h("",!0),M.user?(a(),o("span",ni,s(M.user.name),1)):h("",!0)])])])]),y.value?(a(),o("div",ii,[e("button",{onClick:oe=>S(M),class:"text-primary-600 hover:text-primary-900 text-sm font-medium"}," Modifica ",8,li),e("button",{onClick:oe=>O(M.id),class:"text-red-600 hover:text-red-900 text-sm font-medium"}," Elimina ",8,di)])):h("",!0)])]))),128))])])),w.value.length>0?(a(),o("div",ui,[e("div",ci,[z[7]||(z[7]=e("span",{class:"text-sm font-medium text-gray-900 dark:text-white"},"Totale Spese:",-1)),e("span",mi,"€"+s($(v.value)),1)])])):h("",!0)]),p.value?(a(),fe(On,{key:0,"project-id":E.projectId,expense:f.value,onClose:F,onSaved:j},null,8,["project-id","expense"])):h("",!0)]))}},pi={class:"project-kpi"},xi={key:0,class:"animate-pulse space-y-4"},yi={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},vi={key:1,class:"space-y-6"},fi={class:"bg-white shadow rounded-lg p-6"},bi={class:"flex items-center justify-between"},hi=["disabled"],_i={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},ki={class:"bg-white shadow rounded-lg p-6"},wi={class:"flex items-center"},$i={class:"flex-shrink-0"},ji={class:"ml-5 w-0 flex-1"},Ti={class:"text-lg font-medium text-gray-900"},Ci={class:"text-xs text-gray-500"},Si={class:"bg-white shadow rounded-lg p-6"},Pi={class:"flex items-center"},zi={class:"flex-shrink-0"},Ii={class:"ml-5 w-0 flex-1"},Di={class:"text-lg font-medium text-gray-900"},Mi={class:"bg-white shadow rounded-lg p-6"},Ai={class:"flex items-center"},Ei={class:"flex-shrink-0"},Ui={class:"ml-5 w-0 flex-1"},Vi={class:"text-lg font-medium text-gray-900"},Fi={class:"text-xs text-gray-500"},Oi={class:"bg-white shadow rounded-lg p-6"},Ri={class:"flex items-center"},Bi={class:"flex-shrink-0"},Ni={class:"ml-5 w-0 flex-1"},Li={class:"text-lg font-medium text-gray-900"},qi={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},Ki={class:"bg-white shadow rounded-lg p-6"},Hi={class:"space-y-4"},Gi={class:"flex justify-between text-sm"},Xi={class:"font-medium"},Ji={class:"w-full bg-gray-200 rounded-full h-3"},Wi={class:"flex justify-between text-sm"},Yi={class:"text-gray-600"},Qi={class:"font-medium"},Zi={class:"bg-white shadow rounded-lg p-6"},el={class:"space-y-4"},tl={class:"flex justify-between text-sm"},sl={class:"font-medium"},rl={class:"w-full bg-gray-200 rounded-full h-3"},al={class:"flex justify-between text-sm"},ol={class:"text-gray-600"},nl={class:"font-medium"},il={class:"bg-white shadow rounded-lg p-6"},ll={class:"flex items-center justify-between mb-4"},dl={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},ul={class:"text-center p-4 border rounded-lg"},cl={class:"text-xs text-gray-500"},ml={class:"text-center p-4 border rounded-lg"},gl={class:"text-xs text-gray-500"},pl={class:"text-center p-4 border rounded-lg"},xl={class:"text-xs text-gray-500"},yl={key:2,class:"text-center py-8"},vl={key:3,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},fl={class:"relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white"},bl={class:"mt-3"},hl={class:"flex items-center justify-between pb-4 border-b"},_l={class:"mt-6 space-y-6"},kl={class:"bg-gray-50 p-4 rounded-lg"},wl={class:"font-medium text-gray-900"},$l={class:"text-sm text-gray-600"},jl={class:"space-y-6"},Tl={class:"flex items-center justify-between mb-4"},Cl={class:"font-medium text-gray-900"},Sl={class:"text-sm text-gray-600"},Pl={class:"flex items-center space-x-2"},zl={class:"text-xs text-gray-500"},Il=["onClick"],Dl={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},Ml=["onUpdate:modelValue","onInput"],Al=["onUpdate:modelValue","onInput"],El=["onUpdate:modelValue","onInput"],Ul={class:"mt-4"},Vl=["onUpdate:modelValue","onInput"],Fl={class:"mt-4 flex justify-end"},Ol=["onClick","disabled"],Rl={key:1,class:"text-sm text-green-600"},Bl={class:"mt-6 pt-4 border-t flex justify-between"},Nl={class:"flex space-x-3"},Ll=["disabled"],ql={__name:"ProjectKPI",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["refresh"],setup(g,{emit:D}){const x=g,P=D,w=C(!1),p=C(!1),f=C(null),y=C({}),v=C({totalHours:0,workDays:0,totalCosts:0,costVariance:0,potentialRevenue:0,actualRevenue:0,marginPercentage:0}),u=C({budget:80,time:85,margin:15}),S=K(()=>{var r;return!((r=x.project)!=null&&r.budget)||v.value.totalCosts===0?0:Math.round(v.value.totalCosts/x.project.budget*100)}),O=K(()=>{var r;return!((r=x.project)!=null&&r.estimated_hours)||v.value.totalHours===0?0:Math.round(v.value.totalHours/x.project.estimated_hours*100)}),F=K(()=>{const r=v.value.costVariance;return r>0?"text-red-600":r<0?"text-green-600":"text-gray-600"}),j=K(()=>{const r=v.value.marginPercentage;return r>=u.value.margin?"text-green-600":r>=u.value.margin*.7?"text-yellow-600":"text-red-600"}),$=K(()=>{const r=v.value.marginPercentage;return r>=u.value.margin?"Ottimo":r>=u.value.margin*.7?"Accettabile":"Critico"}),G=K(()=>{const r=S.value;return r>=u.value.budget?"text-red-600":r>=u.value.budget*.8?"text-yellow-600":"text-green-600"}),E=K(()=>{const r=O.value;return r>=u.value.time?"text-red-600":r>=u.value.time*.8?"text-yellow-600":"text-green-600"}),z=K(()=>{const r=v.value.marginPercentage;return r>=u.value.margin?"text-green-600":r>=u.value.margin*.7?"text-yellow-600":"text-red-600"}),M=r=>new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(r||0),oe=r=>!r||r===0?"0h":`${parseFloat(r).toFixed(2)}h`,ee=r=>`${(r||0).toFixed(1)}%`,_=async()=>{var r;(r=x.project)!=null&&r.id&&n()},n=()=>{const r=x.project;r&&(v.value={totalHours:r.total_hours||0,workDays:Math.ceil((r.total_hours||0)/8),totalCosts:(r.total_hours||0)*50,costVariance:(r.total_hours||0)*50-(r.budget||0),potentialRevenue:r.budget||0,actualRevenue:r.invoiced_amount||0,marginPercentage:r.budget?(r.budget-(r.total_hours||0)*50)/r.budget*100:0})},Z=async()=>{w.value=!0;try{await _(),P("refresh")}catch(r){console.error("Error refreshing KPIs:",r)}finally{w.value=!1}},R=K(()=>{var c;const r=((c=x.project)==null?void 0:c.project_type)||"service";return me(r)}),me=r=>{const c={service:[{name:"margin_percentage",display_name:"Margine Netto %",description:"Percentuale di margine netto sul fatturato",unit:"%",target_min:25,target_max:40,warning_threshold:15},{name:"utilization_rate",display_name:"Tasso di Utilizzo %",description:"Percentuale di utilizzo del team rispetto alla capacità teorica",unit:"%",target_min:75,target_max:85,warning_threshold:60},{name:"cost_per_hour",display_name:"Costo per Ora",description:"Costo medio per ora di lavoro, inclusi tutti i costi",unit:"€",target_min:30,target_max:50,warning_threshold:60},{name:"cost_revenue_ratio",display_name:"Rapporto C/R",description:"Rapporto tra costi sostenuti e ricavi generati",unit:"ratio",target_min:.6,target_max:.75,warning_threshold:.85}]};return c[r]||c.service},T=r=>({service:"🔧 Servizio",license:"📄 Licenza",consulting:"💼 Consulenza",product:"📦 Prodotto",rd:"🔬 R&D",internal:"🏢 Interno"})[r]||"Sconosciuto",m=()=>{R.value.forEach(c=>{y.value[c.name]||(y.value[c.name]={target_min:c.target_min,target_max:c.target_max,warning_threshold:c.warning_threshold,custom_description:"",isDirty:!1,isSaved:!1})}),p.value=!0},X=()=>{p.value=!1},se=r=>{y.value[r]&&(y.value[r].isDirty=!0,y.value[r].isSaved=!1)},ae=r=>{const c=R.value.find(B=>B.name===r);c&&y.value[r]&&(y.value[r].target_min=c.target_min,y.value[r].target_max=c.target_max,y.value[r].warning_threshold=c.warning_threshold,y.value[r].custom_description="",y.value[r].isDirty=!0,y.value[r].isSaved=!1)},ne=()=>{confirm("Sei sicuro di voler ripristinare tutti i KPI ai valori di default?")&&R.value.forEach(r=>{ae(r.name)})},k=async r=>{var c;if(y.value[r]){f.value=r;try{const B=y.value[r];await new Promise(ie=>setTimeout(ie,1e3)),console.log("Saving KPI config:",{project_id:(c=x.project)==null?void 0:c.id,kpi_name:r,target_min:B.target_min,target_max:B.target_max,warning_threshold:B.warning_threshold,custom_description:B.custom_description}),y.value[r].isDirty=!1,y.value[r].isSaved=!0,setTimeout(()=>{y.value[r]&&(y.value[r].isSaved=!1)},3e3)}catch(B){console.error("Error saving KPI config:",B),alert("Errore nel salvataggio della configurazione KPI")}finally{f.value=null}}},b=async()=>{const r=R.value.filter(c=>{var B;return(B=y.value[c.name])==null?void 0:B.isDirty});for(const c of r)await k(c.name)},i=K(()=>R.value.some(r=>{var c;return(c=y.value[r.name])==null?void 0:c.isDirty}));return pe(()=>x.project,r=>{r&&_()},{immediate:!0}),be(()=>{x.project&&_()}),(r,c)=>{var B,ie;return a(),o("div",pi,[g.loading?(a(),o("div",xi,[e("div",yi,[(a(),o(W,null,Y(4,J=>e("div",{key:J,class:"bg-gray-200 rounded-lg h-24"})),64))]),c[0]||(c[0]=e("div",{class:"bg-gray-200 rounded-lg h-64"},null,-1))])):g.project?(a(),o("div",vi,[e("div",fi,[e("div",bi,[c[2]||(c[2]=e("div",null,[e("h3",{class:"text-lg font-medium text-gray-900"},"KPI Progetto"),e("p",{class:"text-sm text-gray-600"},"Dashboard metriche e performance del progetto")],-1)),e("button",{onClick:Z,disabled:w.value,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"},[I(A,{name:"arrow-path",class:H(["w-4 h-4 mr-2",{"animate-spin":w.value}])},null,8,["class"]),c[1]||(c[1]=q(" Aggiorna "))],8,hi)])]),e("div",_i,[e("div",ki,[e("div",wi,[e("div",$i,[I(A,{name:"clock",class:"h-8 w-8 text-blue-600"})]),e("div",ji,[e("dl",null,[c[3]||(c[3]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Ore Totali",-1)),e("dd",Ti,s(oe(v.value.totalHours)),1),e("dd",Ci,s(v.value.workDays)+" giorni lavorati",1)])])])]),e("div",Si,[e("div",Pi,[e("div",zi,[I(A,{name:"currency-euro",class:"h-8 w-8 text-red-600"})]),e("div",Ii,[e("dl",null,[c[4]||(c[4]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Costi Totali",-1)),e("dd",Di,s(M(v.value.totalCosts)),1),e("dd",{class:H(["text-xs",F.value])},s(M(v.value.costVariance))+" vs budget",3)])])])]),e("div",Mi,[e("div",Ai,[e("div",Ei,[I(A,{name:"trending-up",class:"h-8 w-8 text-green-600"})]),e("div",Ui,[e("dl",null,[c[5]||(c[5]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Ricavi Potenziali",-1)),e("dd",Vi,s(M(v.value.potentialRevenue)),1),e("dd",Fi,s(M(v.value.actualRevenue))+" fatturati",1)])])])]),e("div",Oi,[e("div",Ri,[e("div",Bi,[I(A,{name:"chart-bar",class:"h-8 w-8 text-purple-600"})]),e("div",Ni,[e("dl",null,[c[6]||(c[6]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Margine",-1)),e("dd",Li,s(ee(v.value.marginPercentage)),1),e("dd",{class:H(["text-xs",j.value])},s($.value),3)])])])])]),e("div",qi,[e("div",Ki,[c[8]||(c[8]=e("h4",{class:"text-lg font-medium text-gray-900 mb-4"},"Andamento Budget",-1)),e("div",Hi,[e("div",Gi,[c[7]||(c[7]=e("span",{class:"text-gray-600"},"Budget Totale",-1)),e("span",Xi,s(M(g.project.budget||0)),1)]),e("div",Ji,[e("div",{class:"bg-blue-600 h-3 rounded-full transition-all duration-300",style:ye({width:S.value+"%"})},null,4)]),e("div",Wi,[e("span",Yi,"Utilizzato: "+s(M(v.value.totalCosts)),1),e("span",Qi,s(S.value)+"%",1)])])]),e("div",Zi,[c[10]||(c[10]=e("h4",{class:"text-lg font-medium text-gray-900 mb-4"},"Andamento Tempo",-1)),e("div",el,[e("div",tl,[c[9]||(c[9]=e("span",{class:"text-gray-600"},"Ore Stimate",-1)),e("span",sl,s(oe(g.project.estimated_hours||0)),1)]),e("div",rl,[e("div",{class:"bg-green-600 h-3 rounded-full transition-all duration-300",style:ye({width:O.value+"%"})},null,4)]),e("div",al,[e("span",ol,"Lavorate: "+s(oe(v.value.totalHours)),1),e("span",nl,s(O.value)+"%",1)])])])]),e("div",il,[e("div",ll,[c[12]||(c[12]=e("h4",{class:"text-lg font-medium text-gray-900"},"Soglie KPI",-1)),e("button",{onClick:m,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"},[I(A,{name:"cog-6-tooth",class:"w-4 h-4 mr-2"}),c[11]||(c[11]=q(" Configura KPI "))])]),e("div",dl,[e("div",ul,[e("div",{class:H(["text-2xl font-bold",G.value])},s(S.value)+"% ",3),c[13]||(c[13]=e("div",{class:"text-sm text-gray-600"},"Budget Usage",-1)),e("div",cl,"Soglia: "+s(u.value.budget)+"%",1)]),e("div",ml,[e("div",{class:H(["text-2xl font-bold",E.value])},s(O.value)+"% ",3),c[14]||(c[14]=e("div",{class:"text-sm text-gray-600"},"Time Usage",-1)),e("div",gl,"Soglia: "+s(u.value.time)+"%",1)]),e("div",pl,[e("div",{class:H(["text-2xl font-bold",z.value])},s(ee(v.value.marginPercentage)),3),c[15]||(c[15]=e("div",{class:"text-sm text-gray-600"},"Margine",-1)),e("div",xl,"Soglia: "+s(u.value.margin)+"%",1)])])])])):(a(),o("div",yl,c[16]||(c[16]=[e("p",{class:"text-gray-500"},"Progetto non trovato",-1)]))),p.value?(a(),o("div",vl,[e("div",fl,[e("div",bl,[e("div",hl,[c[17]||(c[17]=e("h3",{class:"text-lg font-medium text-gray-900"},"Configurazione KPI Progetto",-1)),e("button",{onClick:X,class:"text-gray-400 hover:text-gray-600"},[I(A,{name:"x-mark",class:"w-6 h-6"})])]),e("div",_l,[e("div",kl,[e("h4",wl,s((B=g.project)==null?void 0:B.name),1),e("p",$l,"Tipo: "+s(T((ie=g.project)==null?void 0:ie.project_type)),1)]),e("div",jl,[(a(!0),o(W,null,Y(R.value,J=>{var he,ue;return a(),o("div",{key:J.name,class:"border border-gray-200 rounded-lg p-4"},[e("div",Tl,[e("div",null,[e("h5",Cl,s(J.display_name),1),e("p",Sl,s(J.description),1)]),e("div",Pl,[e("span",zl,s(J.unit),1),e("button",{onClick:re=>ae(J.name),class:"text-xs text-blue-600 hover:text-blue-800",title:"Reset ai valori di default"}," Reset ",8,Il)])]),e("div",Dl,[e("div",null,[c[18]||(c[18]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Target Minimo",-1)),U(e("input",{type:"number",step:"0.1","onUpdate:modelValue":re=>y.value[J.name].target_min=re,onInput:re=>se(J.name),class:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm"},null,40,Ml),[[te,y.value[J.name].target_min]])]),e("div",null,[c[19]||(c[19]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Target Massimo",-1)),U(e("input",{type:"number",step:"0.1","onUpdate:modelValue":re=>y.value[J.name].target_max=re,onInput:re=>se(J.name),class:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm"},null,40,Al),[[te,y.value[J.name].target_max]])]),e("div",null,[c[20]||(c[20]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Soglia Warning",-1)),U(e("input",{type:"number",step:"0.1","onUpdate:modelValue":re=>y.value[J.name].warning_threshold=re,onInput:re=>se(J.name),class:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm"},null,40,El),[[te,y.value[J.name].warning_threshold]])])]),e("div",Ul,[c[21]||(c[21]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Descrizione Personalizzata",-1)),U(e("textarea",{"onUpdate:modelValue":re=>y.value[J.name].custom_description=re,onInput:re=>se(J.name),rows:"2",class:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm",placeholder:"Descrizione specifica per questo progetto..."},null,40,Vl),[[te,y.value[J.name].custom_description]])]),e("div",Fl,[(he=y.value[J.name])!=null&&he.isDirty?(a(),o("button",{key:0,onClick:re=>k(J.name),disabled:f.value===J.name,class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50"},[f.value===J.name?(a(),fe(A,{key:0,name:"arrow-path",class:"animate-spin -ml-1 mr-2 h-4 w-4 text-white"})):h("",!0),q(" "+s(f.value===J.name?"Salvataggio...":"Salva KPI"),1)],8,Ol)):(ue=y.value[J.name])!=null&&ue.isSaved?(a(),o("span",Rl,"✓ Salvato")):h("",!0)])])}),128))])]),e("div",Bl,[e("button",{onClick:ne,class:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"}," Reset Tutti "),e("div",Nl,[e("button",{onClick:X,class:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"}," Chiudi "),e("button",{onClick:b,disabled:!i.value,class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50"}," Salva Tutto ",8,Ll)])])])])])):h("",!0)])}}},Kl={class:"space-y-6"},Hl={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},Gl={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},Xl={class:"flex items-center justify-between"},Jl={class:"flex items-center space-x-4"},Wl={class:"flex items-center space-x-2"},Yl={class:"text-sm font-medium text-gray-900 dark:text-white min-w-[80px] text-center"},Ql={class:"flex items-center space-x-2"},Zl=["value"],ed={key:0,class:"flex justify-center py-8"},td={key:1,class:"bg-red-50 border border-red-200 rounded-md p-4 m-6"},sd={class:"text-red-600"},rd={key:2,class:"p-6"},ad={class:"overflow-x-auto"},od={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},nd={class:"bg-gray-50 dark:bg-gray-700"},id={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},ld={class:"px-4 py-3 whitespace-nowrap sticky left-0 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700"},dd={class:"text-sm font-medium text-gray-900 dark:text-white"},ud={class:"text-xs text-gray-500 dark:text-gray-400"},cd=["onClick"],md={key:0,class:"flex flex-col items-center"},gd={class:"text-xs font-medium text-primary-600 dark:text-primary-400"},pd={key:0,class:"flex space-x-1 mt-1"},xd=["title"],yd={key:1,class:"text-gray-300 dark:text-gray-600"},vd={class:"px-3 py-3 text-center bg-gray-50 dark:bg-gray-700"},fd={class:"text-sm font-medium text-gray-900 dark:text-white"},bd={class:"bg-gray-100 dark:bg-gray-600 font-medium"},hd={class:"px-3 py-3 text-center text-sm font-semibold text-gray-900 dark:text-white bg-gray-100 dark:bg-gray-600"},_d={key:0,class:"text-center py-8"},kd={key:1,class:"mt-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-md"},wd={class:"mt-3"},$d={class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},jd={class:"grid grid-cols-1 gap-4"},Td=["value"],Cd={key:0,class:"border-t border-gray-200 dark:border-gray-600 pt-4"},Sd={class:"grid grid-cols-2 gap-4"},Pd={key:0},zd=["placeholder"],Id={class:"text-xs text-gray-500 dark:text-gray-400 mt-1"},Dd={class:"flex justify-end space-x-3 mt-6"},Md=["disabled"],Ad={__name:"ProjectTimesheet",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(g,{expose:D}){const x=g,P=$e(),w=C(null),p=C(!1),f=C(""),y=C(!1),v=C(new Date().getFullYear()),u=C(new Date().getMonth()+1),S=C(""),O=C(!1),F=C(!1),j=C(null),$=C({task_id:"",date:"",hours:0,description:"",billable:!0,billing_rate:null}),G=K(()=>w.value?Array.from({length:w.value.days_in_month},(T,m)=>m+1):[]),E=async()=>{var T;if((T=x.project)!=null&&T.id){p.value=!0,f.value="";try{const m=new URLSearchParams({year:v.value.toString(),month:u.value.toString()});S.value&&m.append("member_id",S.value.toString());const X=await fetch(`/api/timesheets/project/${x.project.id}/monthly?${m}`,{headers:{"Content-Type":"application/json","X-CSRFToken":P.csrfToken}});if(!X.ok)throw new Error("Errore nel caricamento del timesheet");const se=await X.json();w.value=se.data}catch(m){f.value=m.message}finally{p.value=!1}}},z=async(T,m)=>{try{const X=await fetch(`/api/timesheets/project/${x.project.id}?start_date=${m}&end_date=${m}&task_id=${T}`,{headers:{"Content-Type":"application/json","X-CSRFToken":P.csrfToken}});if(!X.ok)return null;const ne=((await X.json()).data||[]).find(k=>k.user_id===P.user.id&&k.task_id===parseInt(T)&&k.date===m);return ne?ne.id:null}catch(X){return console.error("Error finding existing timesheet entry:",X),null}},M=async()=>{y.value=!0;try{const T={...$.value,project_id:x.project.id},m=await z($.value.task_id,$.value.date);let X;if(m?X=await fetch(`/api/timesheets/${m}`,{method:"PUT",headers:{"Content-Type":"application/json","X-CSRFToken":P.csrfToken},body:JSON.stringify(T)}):X=await fetch("/api/timesheets/",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":P.csrfToken},body:JSON.stringify(T)}),!X.ok)throw new Error("Errore nel salvataggio del timesheet");await E(),ee()}catch(T){f.value=T.message}finally{y.value=!1}},oe=async(T,m)=>{var i,r,c,B;const X=w.value.tasks.find(ie=>ie.id===T);if(!X)return;j.value={taskId:T,day:m};const se=((r=(i=x.project)==null?void 0:i.contract)==null?void 0:r.hourly_rate)||null,ae=`${v.value}-${String(u.value).padStart(2,"0")}-${String(m).padStart(2,"0")}`;let ne=!!((c=x.project)!=null&&c.contract),k=se,b="";if(X.daily_hours[m]>0)try{const ie=await fetch(`/api/timesheets/project/${x.project.id}?start_date=${ae}&end_date=${ae}&task_id=${T}`,{headers:{"Content-Type":"application/json","X-CSRFToken":P.csrfToken}});if(ie.ok){const ue=((await ie.json()).data||[]).find(re=>re.user_id===P.user.id&&re.task_id===parseInt(T)&&re.date===ae);ue&&(ne=ue.billable!==void 0?ue.billable:!!((B=x.project)!=null&&B.contract),k=ue.billing_rate||se,b=ue.description||"",console.log("Loading existing entry:",{id:ue.id,billable:ue.billable,billing_rate:ue.billing_rate,description:ue.description}))}}catch(ie){console.error("Error loading existing entry:",ie)}$.value={task_id:T,date:ae,hours:X.daily_hours[m]||0,description:b,billable:ne,billing_rate:k},X.daily_hours[m]>0?F.value=!0:O.value=!0},ee=()=>{O.value=!1,F.value=!1,j.value=null,$.value={task_id:"",date:"",hours:0,description:"",billable:!0,billing_rate:null}},_=()=>{u.value===1?(u.value=12,v.value--):u.value--,E()},n=()=>{u.value===12?(u.value=1,v.value++):u.value++,E()},Z=T=>{const m=new Date;return m.getFullYear()===v.value&&m.getMonth()+1===u.value&&m.getDate()===T},R=T=>!T||T===0?"0":T%1===0?T.toString():T.toFixed(2),me=T=>T?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(T):"€0";return pe(()=>{var T;return(T=x.project)==null?void 0:T.id},T=>{T&&E()}),pe(S,()=>{E()}),be(()=>{var T;(T=x.project)!=null&&T.id&&E()}),D({refresh:E}),(T,m)=>{var X,se,ae,ne;return a(),o("div",Kl,[e("div",Hl,[e("div",Gl,[e("div",Xl,[e("div",Jl,[m[11]||(m[11]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Timesheet Dettaglio ",-1)),e("div",Wl,[e("button",{onClick:_,class:"p-1 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700"},[I(A,{name:"chevron-left",size:"md"})]),e("span",Yl,s(u.value)+"/"+s(v.value),1),e("button",{onClick:n,class:"p-1 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700"},[I(A,{name:"chevron-right",size:"md"})])]),e("div",Ql,[m[10]||(m[10]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Vista:",-1)),U(e("select",{"onUpdate:modelValue":m[0]||(m[0]=k=>S.value=k),onChange:E,class:"text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:border-primary-500 focus:ring-primary-500"},[m[9]||(m[9]=e("option",{value:""},"Tutti i membri",-1)),(a(!0),o(W,null,Y(((X=g.project)==null?void 0:X.team_members)||[],k=>(a(),o("option",{key:k.id,value:k.id},s(k.first_name)+" "+s(k.last_name),9,Zl))),128))],544),[[le,S.value]])])]),e("button",{onClick:m[1]||(m[1]=k=>O.value=!0),class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[I(A,{name:"plus",size:"sm",class:"mr-2"}),m[12]||(m[12]=q(" Aggiungi Ore "))])])]),p.value?(a(),o("div",ed,m[13]||(m[13]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1)]))):h("",!0),f.value?(a(),o("div",td,[e("p",sd,s(f.value),1)])):h("",!0),!p.value&&w.value?(a(),o("div",rd,[e("div",ad,[e("table",od,[e("thead",nd,[e("tr",null,[m[14]||(m[14]=e("th",{class:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider sticky left-0 bg-gray-50 dark:bg-gray-700"}," Task ",-1)),(a(!0),o(W,null,Y(G.value,k=>(a(),o("th",{key:k,class:H(["px-2 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider min-w-[40px]",{"bg-blue-50 dark:bg-blue-900":Z(k)}])},s(k),3))),128)),m[15]||(m[15]=e("th",{class:"px-3 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider bg-gray-50 dark:bg-gray-700"}," Tot ",-1))])]),e("tbody",id,[(a(!0),o(W,null,Y(w.value.tasks,k=>(a(),o("tr",{key:k.id},[e("td",ld,[e("div",dd,s(k.name),1),e("div",ud,s(k.workers.length?k.workers.join(", "):"Nessuno ha lavorato"),1)]),(a(!0),o(W,null,Y(G.value,b=>{var i;return a(),o("td",{key:b,class:H(["px-2 py-3 text-center min-w-[40px] cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700",{"bg-blue-50 dark:bg-blue-900":Z(b)}]),onClick:r=>oe(k.id,b)},[k.daily_hours[b]>0?(a(),o("div",md,[e("span",gd,s(R(k.daily_hours[b])),1),(i=g.project)!=null&&i.contract?(a(),o("div",pd,[e("div",{class:H(["w-1.5 h-1.5 rounded-full",k.daily_billing&&k.daily_billing[b]?"bg-green-500":"bg-gray-300"]),title:k.daily_billing&&k.daily_billing[b]?"Fatturabile":"Non fatturabile"},null,10,xd)])):h("",!0)])):(a(),o("span",yd,"-"))],10,cd)}),128)),e("td",vd,[e("span",fd,s(R(k.total_hours)),1)])]))),128)),e("tr",bd,[m[16]||(m[16]=e("td",{class:"px-4 py-3 text-sm font-semibold text-gray-900 dark:text-white sticky left-0 bg-gray-100 dark:bg-gray-600"}," TOTALE GIORNALIERO ",-1)),(a(!0),o(W,null,Y(G.value,k=>(a(),o("td",{key:k,class:H(["px-2 py-3 text-center text-sm font-semibold text-gray-900 dark:text-white",{"bg-blue-100 dark:bg-blue-800":Z(k)}])},s(R(w.value.daily_totals[k]||0)),3))),128)),e("td",hd,s(R(w.value.grand_total)),1)])])])]),w.value.tasks.length===0?(a(),o("div",_d,m[17]||(m[17]=[e("p",{class:"text-gray-500 dark:text-gray-400"},"Nessun task trovato per questo progetto",-1)]))):h("",!0),(se=g.project)!=null&&se.contract&&w.value.tasks.length>0?(a(),o("div",kd,m[18]||(m[18]=[xe('<div class="flex items-center space-x-4 text-xs text-gray-600 dark:text-gray-300"><span class="font-medium">Legenda:</span><div class="flex items-center space-x-1"><div class="w-1.5 h-1.5 rounded-full bg-green-500"></div><span>Fatturabile</span></div><div class="flex items-center space-x-1"><div class="w-1.5 h-1.5 rounded-full bg-gray-300"></div><span>Non fatturabile</span></div></div>',1)]))):h("",!0)])):h("",!0)]),O.value||F.value?(a(),o("div",{key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:ee},[e("div",{class:"relative top-20 mx-auto p-5 border w-[400px] shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:m[8]||(m[8]=ge(()=>{},["stop"]))},[e("div",wd,[e("h3",$d,s(F.value?"Modifica Ore":"Aggiungi Ore"),1),e("form",{onSubmit:ge(M,["prevent"])},[e("div",jd,[e("div",null,[m[20]||(m[20]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Task",-1)),U(e("select",{"onUpdate:modelValue":m[2]||(m[2]=k=>$.value.task_id=k),required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[m[19]||(m[19]=e("option",{value:""},"Seleziona task",-1)),(a(!0),o(W,null,Y(((ae=w.value)==null?void 0:ae.tasks)||[],k=>(a(),o("option",{key:k.id,value:k.id},s(k.name),9,Td))),128))],512),[[le,$.value.task_id]])]),e("div",null,[m[21]||(m[21]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Data",-1)),U(e("input",{"onUpdate:modelValue":m[3]||(m[3]=k=>$.value.date=k),type:"date",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[te,$.value.date]])]),e("div",null,[m[22]||(m[22]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ore",-1)),U(e("input",{"onUpdate:modelValue":m[4]||(m[4]=k=>$.value.hours=k),type:"number",step:"0.25",min:"0",max:"24",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[te,$.value.hours]])]),e("div",null,[m[23]||(m[23]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Descrizione",-1)),U(e("textarea",{"onUpdate:modelValue":m[5]||(m[5]=k=>$.value.description=k),rows:"3",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[te,$.value.description]])]),(ne=g.project)!=null&&ne.contract?(a(),o("div",Cd,[m[27]||(m[27]=e("h4",{class:"text-sm font-medium text-gray-900 dark:text-white mb-3"},"Informazioni Fatturazione",-1)),e("div",Sd,[e("div",null,[m[25]||(m[25]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Fatturabile ",-1)),U(e("select",{"onUpdate:modelValue":m[6]||(m[6]=k=>$.value.billable=k),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},m[24]||(m[24]=[e("option",{value:!0},"Sì - Fatturabile",-1),e("option",{value:!1},"No - Interno",-1)]),512),[[le,$.value.billable]])]),$.value.billable?(a(),o("div",Pd,[m[26]||(m[26]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Tariffa (€/h) ",-1)),U(e("input",{"onUpdate:modelValue":m[7]||(m[7]=k=>$.value.billing_rate=k),type:"number",step:"0.01",min:"0",placeholder:g.project.contract.hourly_rate,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,8,zd),[[te,$.value.billing_rate]]),e("p",Id," Tariffa contrattuale: "+s(me(g.project.contract.hourly_rate))+"/h ",1)])):h("",!0)])])):h("",!0)]),e("div",Dd,[e("button",{type:"button",onClick:ee,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md"}," Annulla "),e("button",{type:"submit",disabled:y.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50"},s(y.value?"Salvataggio...":F.value?"Aggiorna":"Aggiungi"),9,Md)])],32)])])])):h("",!0)])}}},Ed={class:"project-files"},Ud={class:"bg-white shadow rounded-lg p-6"},Vd={class:"border-2 border-dashed border-gray-300 rounded-lg p-6 mb-6 hover:border-gray-400 transition-colors"},Fd={class:"text-center"},Od={class:"space-y-3"},Rd={class:"flex items-center space-x-4"},Bd={class:"flex-shrink-0"},Nd={class:"flex-1"},Ld={class:"text-sm font-medium text-gray-900"},qd={class:"flex items-center space-x-4 text-xs text-gray-500"},Kd={class:"flex items-center space-x-2"},Hd={class:"text-gray-400 hover:text-gray-600",title:"Scarica"},Gd={key:0,class:"text-center py-8"},Xd={__name:"ProjectFiles",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(g){const D=g,x=K(()=>{var p;return((p=D.project)==null?void 0:p.files)||[]}),P=p=>{if(!p)return"0 B";const f=1024,y=["B","KB","MB","GB"],v=Math.floor(Math.log(p)/Math.log(f));return parseFloat((p/Math.pow(f,v)).toFixed(2))+" "+y[v]},w=p=>p?new Date(p).toLocaleDateString("it-IT"):"";return(p,f)=>(a(),o("div",Ed,[e("div",Ud,[f[2]||(f[2]=e("h3",{class:"text-lg font-medium text-gray-900 mb-6"},"File del Progetto",-1)),e("div",Vd,[e("div",Fd,[I(A,{name:"cloud-arrow-up",size:"xl",class:"mx-auto text-gray-400"}),f[0]||(f[0]=xe('<div class="mt-4"><label class="cursor-pointer"><span class="mt-2 block text-sm font-medium text-gray-900"> Trascina file qui o clicca per selezionare </span><input type="file" class="sr-only" multiple></label><p class="mt-2 text-xs text-gray-500"> PNG, JPG, PDF, DOC, XLS fino a 10MB </p></div>',1))])]),e("div",Od,[(a(!0),o(W,null,Y(x.value,y=>(a(),o("div",{key:y.id,class:"flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50"},[e("div",Rd,[e("div",Bd,[I(A,{name:"document-text",size:"lg",class:"text-gray-600"})]),e("div",Nd,[e("h4",Ld,s(y.name),1),e("div",qd,[e("span",null,s(P(y.size)),1),e("span",null,s(w(y.uploaded_at)),1)])])]),e("div",Kd,[e("button",Hd,[I(A,{name:"arrow-down-tray",size:"md"})])])]))),128)),x.value.length===0?(a(),o("div",Gd,f[1]||(f[1]=[e("p",{class:"text-gray-500"},"Nessun file caricato per questo progetto",-1)]))):h("",!0)])])]))}},Jd={class:"project-view"},Wd={class:"tab-content"},Yd={__name:"ProjectView",setup(g){const D=Be(),x=$e(),P=Je(),w=Qe(),p=C(!0),f=C("overview"),y=K(()=>D.currentProject),v=K(()=>[{id:"overview",label:"Panoramica",icon:"chart-bar"},{id:"tasks",label:"Task",icon:"clipboard-list"},{id:"team",label:"Team",icon:"users"},{id:"timesheet",label:"Timesheet",icon:"clock"},{id:"files",label:"File",icon:"file"},{id:"expenses",label:"Spese",icon:"credit-card"},{id:"kpi",label:"KPI & Analytics",icon:"trending-up"}].filter($=>!!(["overview","tasks","team","timesheet","files","expenses","kpi"].includes($.id)||$.id==="kpi"&&x.hasPermission("view_reports")||$.id==="expenses"&&x.hasPermission("manage_expenses")))),u=K(()=>({overview:Oe,tasks:yo,team:Cn,expenses:gi,kpi:ql,timesheet:Ad,files:Xd})[f.value]||Oe),S=async()=>{var j,$,G,E;p.value=!0;try{const z=P.params.id;await D.fetchProject(z,!0),console.log("Progetto caricato:",D.currentProject),console.log("Team members:",(($=(j=D.currentProject)==null?void 0:j.team_members)==null?void 0:$.length)||0),(E=(G=D.currentProject)==null?void 0:G.team_members)!=null&&E.length&&console.log("Team members:",D.currentProject.team_members.map(M=>M.full_name).join(", "))}catch(z){console.error("Error loading project:",z)}finally{p.value=!1}},O=()=>{w.push(`/projects/${P.params.id}/edit`)},F=async()=>{if(confirm("Sei sicuro di voler eliminare questo progetto?"))try{await D.deleteProject(P.params.id),w.push("/projects")}catch(j){console.error("Error deleting project:",j)}};return pe(()=>P.params.id,(j,$)=>{j&&j!==$&&S()}),pe(()=>P.hash,j=>{if(j){const $=j.replace("#","");v.value.find(G=>G.id===$)&&f.value!==$&&(f.value=$)}},{immediate:!0}),pe(f,j=>{const $=`#${j}`;P.hash!==$&&w.replace({...P,hash:$})}),be(()=>{if(P.hash){const j=P.hash.replace("#","");v.value.find($=>$.id===j)&&(f.value=j)}S()}),(j,$)=>(a(),o("div",Jd,[I(vt,{project:y.value,loading:p.value,onEdit:O,onDelete:F},null,8,["project","loading"]),I($t,{tabs:v.value,modelValue:f.value,"onUpdate:modelValue":$[0]||($[0]=G=>f.value=G),class:"mb-6"},null,8,["tabs","modelValue"]),e("div",Wd,[(a(),fe(Ye,null,[(a(),fe(We(u.value),{project:y.value,loading:p.value,onRefresh:S},null,40,["project","loading"]))],1024))])]))}},ru=Ce(Yd,[["__scopeId","data-v-86b07754"]]);export{ru as default};
