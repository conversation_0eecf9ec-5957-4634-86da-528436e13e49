import{H as T}from"./app.js";import{_ as C}from"./PageHeader.js";import{_ as w}from"./StatsGrid.js";import{_ as S}from"./AlertsSection.js";import{T as V}from"./TabNavigation.js";import{F as B}from"./FormBuilder.js";import{V as P}from"./ViewModeToggle.js";import{_ as z}from"./KanbanView.js";import{P as M}from"./ProposalCard.js";import{r as s,b as d,l as t,e as o,p as c,g as N,v as u,t as i,j as m}from"./vendor.js";/* empty css                                                             *//* empty css                                                           */const F={class:"space-y-8"},E={class:"card"},D={class:"btn-primary"},H={class:"card"},I={class:"card"},$={class:"card"},A={class:"mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg"},G={key:0},q={key:1},j={class:"card"},K={class:"card"},L={class:"mb-4"},O={class:"text-sm text-gray-600 dark:text-gray-400"},Q={class:"card"},R={class:"bg-white rounded-lg border border-gray-200 p-3 cursor-pointer hover:shadow-md transition-shadow"},Z={class:"font-medium text-gray-900 text-sm mb-2"},J={class:"text-xs text-gray-500"},U={class:"mt-2 flex justify-between items-center text-xs text-gray-400"},W={class:"card"},X={class:"max-w-sm"},pt={__name:"ComponentsExample",setup(Y){const p=s([{id:"test1",label:"Test Stat 1",value:100,format:"number",icon:"users",iconBgColor:"bg-blue-100 dark:bg-blue-900",iconColor:"text-blue-600 dark:text-blue-400"},{id:"test2",label:"Test Stat 2",value:75,format:"percentage",icon:"chart-bar",iconBgColor:"bg-green-100 dark:bg-green-900",iconColor:"text-green-600 dark:text-green-400"}]),b=s([{type:"info",title:"Informazione",items:[{text:"Questo è un alert di test"}]},{type:"success",title:"Successo",items:[{text:"Operazione completata con successo"}]}]),l=s("tab1"),x=s([{id:"tab1",name:"Tab 1",icon:"home"},{id:"tab2",name:"Tab 2",icon:"document-text"}]),n=s({name:"",type:""}),g=s([{id:"name",type:"text",label:"Nome Test",placeholder:"Inserisci nome",required:!0},{id:"type",type:"select",label:"Tipo",placeholder:"Seleziona tipo",options:[{value:"type1",label:"Tipo 1"},{value:"type2",label:"Tipo 2"}]}]),v=()=>{alert(`Form inviato: ${JSON.stringify(n.value)}`)},r=s("list"),y=s([{id:"list",label:"Lista",icon:"list-bullet"},{id:"grid",label:"Griglia",icon:"squares-2x2"},{id:"chart",label:"Grafico",icon:"chart-bar"}]),f=s([{status:"todo",name:"Da Fare",color:"bg-gray-400"},{status:"doing",name:"In Corso",color:"bg-blue-400"},{status:"done",name:"Completato",color:"bg-green-400"}]),k=s([{id:1,title:"Task 1",description:"Descrizione task 1",status:"todo",assignee:"Mario Rossi",priority:"Alta"},{id:2,title:"Task 2",description:"Descrizione task 2",status:"doing",assignee:"Giulia Bianchi",priority:"Media"},{id:3,title:"Task 3",description:"Descrizione task 3",status:"done",assignee:"Luca Verdi",priority:"Bassa"}]),_=s({id:1,title:"Proposta Sviluppo Web",value:15e3,client:{name:"Azienda Example"},creator:{first_name:"Mario",last_name:"Rossi"},created_at:"2024-01-15T10:00:00Z",expiry_date:"2024-02-15T23:59:59Z",status:"sent"}),h=()=>{alert("Proposal card clicked!")};return(tt,e)=>(m(),d("div",F,[e[15]||(e[15]=t("section",null,[t("h2",{class:"text-xl font-semibold text-gray-900 dark:text-white mb-4"},"Test Componenti Base"),t("div",{class:"card"},[t("p",{class:"text-gray-600 dark:text-gray-400"},"Se vedi questo testo, il componente funziona correttamente!")])],-1)),t("section",null,[e[4]||(e[4]=t("h2",{class:"text-xl font-semibold text-gray-900 dark:text-white mb-4"},"PageHeader Component",-1)),t("div",E,[o(C,{title:"Esempio PageHeader",subtitle:"Questo è un esempio del componente PageHeader",icon:"star"},{actions:c(()=>[t("button",D,[o(T,{name:"plus",size:"sm",class:"mr-2"}),e[3]||(e[3]=u(" Test Button "))])]),_:1})])]),t("section",null,[e[5]||(e[5]=t("h2",{class:"text-xl font-semibold text-gray-900 dark:text-white mb-4"},"StatsGrid Component",-1)),t("div",H,[o(w,{stats:p.value},null,8,["stats"])])]),t("section",null,[e[6]||(e[6]=t("h2",{class:"text-xl font-semibold text-gray-900 dark:text-white mb-4"},"AlertsSection Component",-1)),t("div",I,[o(S,{alerts:b.value},null,8,["alerts"])])]),t("section",null,[e[9]||(e[9]=t("h2",{class:"text-xl font-semibold text-gray-900 dark:text-white mb-4"},"TabNavigation Component",-1)),t("div",$,[o(V,{tabs:x.value,"active-tab":l.value,onTabChange:e[0]||(e[0]=a=>l.value=a)},null,8,["tabs","active-tab"]),t("div",A,[l.value==="tab1"?(m(),d("div",G,e[7]||(e[7]=[t("h3",{class:"font-medium text-gray-900 dark:text-white"},"Tab 1",-1),t("p",{class:"text-gray-600 dark:text-gray-400 mt-2"},"Contenuto della prima tab",-1)]))):l.value==="tab2"?(m(),d("div",q,e[8]||(e[8]=[t("h3",{class:"font-medium text-gray-900 dark:text-white"},"Tab 2",-1),t("p",{class:"text-gray-600 dark:text-gray-400 mt-2"},"Contenuto della seconda tab",-1)]))):N("",!0)])])]),t("section",null,[e[10]||(e[10]=t("h2",{class:"text-xl font-semibold text-gray-900 dark:text-white mb-4"},"FormBuilder Component",-1)),t("div",j,[o(B,{modelValue:n.value,"onUpdate:modelValue":e[1]||(e[1]=a=>n.value=a),fields:g.value,"submit-label":"Test Form",onSubmit:v},null,8,["modelValue","fields"])])]),t("section",null,[e[12]||(e[12]=t("h2",{class:"text-xl font-semibold text-gray-900 dark:text-white mb-4"},"ViewModeToggle Component",-1)),t("div",K,[t("div",L,[o(P,{modes:y.value,"active-mode":r.value,onModeChange:e[2]||(e[2]=a=>r.value=a)},null,8,["modes","active-mode"])]),t("div",O,[e[11]||(e[11]=u(" Modalità attiva: ")),t("strong",null,i(r.value),1)])])]),t("section",null,[e[13]||(e[13]=t("h2",{class:"text-xl font-semibold text-gray-900 dark:text-white mb-4"},"KanbanView Component",-1)),t("div",Q,[o(z,{title:"Esempio Kanban",stages:f.value,items:k.value,"stage-key":"status","empty-message":"Nessun elemento"},{item:c(({item:a})=>[t("div",R,[t("h4",Z,i(a.title),1),t("p",J,i(a.description),1),t("div",U,[t("span",null,i(a.assignee),1),t("span",null,i(a.priority),1)])])]),_:1},8,["stages","items"])])]),t("section",null,[e[14]||(e[14]=t("h2",{class:"text-xl font-semibold text-gray-900 dark:text-white mb-4"},"ProposalCard Component",-1)),t("div",W,[t("div",X,[o(M,{proposal:_.value,"show-menu":!1,onClick:h},null,8,["proposal"])])])])]))}};export{pt as default};
