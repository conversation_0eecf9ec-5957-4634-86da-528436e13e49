import{r as f,w as W,b as o,g as v,j as a,l as e,t as i,F as z,q as S,e as r,n as g,c as X,o as Y,f as Z,p as A,v as h,E as B,h as ee,B as te,P as se}from"./vendor.js";import{u as ae}from"./certifications.js";import{_ as oe,H as d}from"./app.js";import{_ as le}from"./PageHeader.js";import{_ as ie}from"./Breadcrumb.js";const ne={class:"p-4"},re={class:"flex items-start justify-between"},ce={class:"flex-1"},de={class:"text-sm font-medium text-gray-900 mb-2"},ue={class:"max-h-64 overflow-y-auto text-xs text-gray-600 space-y-1 font-mono"},me={__name:"DisposableMessage",props:{title:{type:String,default:"Dettagli Calcolo"},logs:{type:Array,default:()=>[]},show:{type:Boolean,default:!1},autoHide:{type:Number,default:0}},emits:["close"],setup(q,{emit:k}){const y=q,w=k,u=f(!1),n=()=>{u.value=!1,setTimeout(()=>{w("close")},300)};return W(()=>y.show,_=>{_?(u.value=!0,y.autoHide>0&&setTimeout(()=>{n()},y.autoHide)):u.value=!1},{immediate:!0}),(_,p)=>u.value?(a(),o("div",{key:0,class:g(["fixed bottom-4 right-4 max-w-md bg-white border border-gray-200 rounded-lg shadow-lg z-50 transition-all duration-300",{"transform translate-y-0 opacity-100":u.value,"transform translate-y-full opacity-0":!u.value}])},[e("div",ne,[e("div",re,[e("div",ce,[e("h3",de,i(q.title),1),e("div",ue,[(a(!0),o(z,null,S(q.logs,(c,b)=>(a(),o("div",{key:b,class:"whitespace-pre-wrap"},i(c),1))),128))])]),e("button",{onClick:n,class:"ml-4 text-gray-400 hover:text-gray-600 transition-colors"},[r(d,{name:"x-mark",class:"h-5 w-5"})])])]),e("div",{class:"px-4 pb-4"},[e("button",{onClick:n,class:"w-full px-3 py-2 text-xs bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"}," Chiudi ")])],2)):v("",!0)}},ve=oe(me,[["__scopeId","data-v-a51fbda6"]]),pe={class:"certification-readiness"},fe={class:"mb-6"},ge={class:"bg-white rounded-lg shadow-sm p-6 mb-6"},_e={key:0,class:"text-center py-8"},be={class:"flex items-center justify-center"},xe={key:1,class:"text-center py-8"},he={class:"bg-red-50 border border-red-200 rounded-md p-4 mb-4"},ye={class:"flex"},we={class:"flex-shrink-0"},ke={class:"ml-3"},Ce={class:"text-sm text-red-800"},ze={key:2,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"},Se=["onClick"],qe={class:"flex items-start justify-between"},$e={class:"flex-1"},je={class:"font-semibold text-gray-900 text-sm"},Ve={class:"text-xs text-gray-600 mt-1"},Re={key:0,class:"text-xs text-gray-500 mt-2 line-clamp-3"},Ae={key:0,class:"ml-2"},Be={key:0,class:"bg-white rounded-lg shadow-sm p-6"},Me={class:"flex items-center justify-between mb-6"},Pe={class:"text-lg font-semibold text-gray-900"},De=["disabled"],Ee={key:0},Ne={class:"space-y-2 mb-6"},Te=["id","onUpdate:modelValue"],He=["for"],Ie={key:0,class:"text-green-600 text-xs"},Oe={class:"space-y-4"},Fe={class:"bg-blue-50 rounded-lg p-4 border-2 border-blue-200"},Le={class:"flex items-center justify-between mb-2"},Ue={class:"text-sm font-medium text-blue-700 flex items-center"},Ge={class:"w-full bg-blue-200 rounded-full h-3"},Je={class:"text-xs text-blue-600 mt-2"},Ke={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Qe={class:"bg-gray-50 rounded-lg p-4"},We={class:"flex items-center justify-between mb-2"},Xe={class:"text-sm font-medium text-gray-700 flex items-center"},Ye={class:"w-full bg-gray-200 rounded-full h-2"},Ze={class:"bg-green-50 rounded-lg p-4"},et={class:"flex items-center justify-between mb-2"},tt={class:"text-sm font-medium text-green-700 flex items-center"},st={class:"flex items-center space-x-2"},at={class:"w-full bg-green-200 rounded-full h-2"},ot={class:"text-xs text-green-600 mt-2"},lt={key:1,class:"mt-6 bg-white border border-gray-200 rounded-lg p-4"},it={class:"font-medium text-gray-900 mb-4 flex items-center"},nt={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},rt={class:"space-y-2"},ct={class:"flex-1"},dt={class:"text-sm font-medium text-gray-900"},ut={class:"text-xs text-gray-500"},mt={class:"text-right"},vt={class:"text-xs text-gray-500"},pt={class:"space-y-2"},ft={class:"text-xs text-blue-700"},gt={key:0,class:"text-blue-600 font-medium mt-1"},_t={key:2,class:"grid grid-cols-1 md:grid-cols-2 gap-4 mt-6"},bt={class:"bg-blue-50 rounded-lg p-4"},xt={class:"space-y-1 text-sm text-blue-700"},ht={key:0},yt={key:1},wt={class:"bg-green-50 rounded-lg p-4"},kt={class:"text-sm text-green-700"},Ct={key:1,class:"bg-white rounded-lg shadow-sm p-12 text-center"},Rt={__name:"CertificationReadiness",setup(q){const k=ae(),y=f(!1),w=f(null),u=f(!1),n=f(null),_=f({}),p=f(0),c=f(null),b=f(0),$=f(!1),F=[{name:"Certificazioni",path:"/app/certifications/dashboard",icon:"shield-check"},{name:"Valutazione Readiness",path:"/app/certifications/readiness"}],L=X(()=>{const s=k.standardsByCategory,t=[];return Object.values(s).forEach(x=>{Object.values(x).forEach(V=>{t.push(V)})}),t}),M=async()=>{y.value=!0,w.value=null;try{await k.fetchStandardsCatalog()}catch(s){w.value=s.message}finally{y.value=!1}},U=s=>{n.value=s,_.value={},p.value=0,s.requirements&&s.requirements.forEach((t,x)=>{_.value[x]=!1})},G=()=>{var x;if(!((x=n.value)!=null&&x.requirements))return;const s=n.value.requirements.length,t=Object.values(_.value).filter(Boolean).length;p.value=Math.round(t/s*100),P()},P=()=>{if(!c.value){b.value=p.value;return}const s=c.value.platform_compliance_score||0;b.value=Math.round(p.value*.6+s*.4)},J=async()=>{if(n.value){u.value=!0;try{const s=await k.fetchPlatformCompliance(n.value.code);s.success&&(c.value=s.data,P())}catch(s){w.value=s.message}finally{u.value=!1}}},j=s=>s>=80?"text-green-600":s>=60?"text-yellow-600":s>=40?"text-orange-600":"text-red-600",R=s=>s>=80?"bg-green-500":s>=60?"bg-yellow-500":s>=40?"bg-orange-500":"bg-red-500",K=s=>s>=80?"Ottimo! Siete pronti per richiedere la certificazione.":s>=60?"Buon livello. Ancora qualche preparazione necessaria.":s>=40?"Preparazione media. Serve più lavoro sui requisiti.":"Preparazione iniziale. Molto lavoro da fare prima della certificazione.",Q=s=>s>=80?"2-4 settimane":s>=60?"1-3 mesi":s>=40?"3-6 mesi":"6-12 mesi",D=s=>new Intl.NumberFormat("it-IT").format(s);return Y(()=>{(!k.standardsCatalog.catalog||Object.keys(k.standardsCatalog.catalog).length===0)&&M()}),(s,t)=>{var V,E,N,T,H,I,O;const x=ee("router-link");return a(),o("div",pe,[r(ie,{breadcrumbs:F}),e("div",fe,[r(le,{title:"Valutazione Readiness",subtitle:"Valuta il livello di preparazione per ottenere nuove certificazioni",icon:"clipboard-document-check","icon-color":"text-purple-600"},{actions:A(()=>[r(x,{to:"/app/certifications/dashboard",class:"btn-secondary flex items-center gap-2"},{default:A(()=>[r(d,{name:"arrow-left",size:"sm"}),t[2]||(t[2]=h(" Dashboard "))]),_:1,__:[2]}),r(x,{to:"/app/certifications/catalog",class:"btn-secondary flex items-center gap-2"},{default:A(()=>[r(d,{name:"book-open",size:"sm"}),t[3]||(t[3]=h(" Catalogo "))]),_:1,__:[3]})]),_:1})]),e("div",ge,[t[6]||(t[6]=e("h2",{class:"text-lg font-semibold text-gray-900 mb-4"},"Seleziona Standard da Valutare",-1)),y.value?(a(),o("div",_e,[e("div",be,[r(d,{name:"arrow-path",size:"lg",class:"animate-spin text-brand-primary-600"})]),t[4]||(t[4]=e("p",{class:"text-gray-500 mt-3"},"Caricamento standard...",-1))])):w.value?(a(),o("div",xe,[e("div",he,[e("div",ye,[e("div",we,[r(d,{name:"exclamation-triangle",size:"md",class:"text-red-400"})]),e("div",ke,[e("p",Ce,i(w.value),1)])])]),e("button",{onClick:M,class:"btn-primary"},[r(d,{name:"arrow-path",size:"sm",class:"mr-2"}),t[5]||(t[5]=h(" Riprova "))])])):(a(),o("div",ze,[(a(!0),o(z,null,S(L.value,l=>{var m,C;return a(),o("div",{key:l.code,class:g(["border border-gray-200 rounded-lg p-4 hover:border-blue-300 cursor-pointer transition-colors",{"border-blue-500 bg-blue-50":((m=n.value)==null?void 0:m.code)===l.code}]),onClick:zt=>U(l)},[e("div",qe,[e("div",$e,[e("h3",je,i(l.name),1),e("p",Ve,i(l.category),1),l.description?(a(),o("p",Re,i(l.description),1)):v("",!0)]),((C=n.value)==null?void 0:C.code)===l.code?(a(),o("div",Ae,[r(d,{name:"check-circle",class:"h-5 w-5 text-blue-500"})])):v("",!0)])],10,Se)}),128))]))]),n.value?(a(),o("div",Be,[e("div",Me,[e("h2",Pe," Valutazione: "+i(n.value.name),1),e("button",{onClick:J,class:"btn-primary flex items-center",disabled:u.value},[r(d,{name:u.value?"arrow-path":"play",size:"sm",class:g([{"animate-spin":u.value},"mr-2"])},null,8,["name","class"]),h(" "+i(u.value?"Analisi in corso...":"Avvia Valutazione"),1)],8,De)]),n.value.requirements&&n.value.requirements.length>0?(a(),o("div",Ee,[t[7]||(t[7]=e("h3",{class:"font-medium text-gray-900 mb-3"},"Requisiti Principali",-1)),e("div",Ne,[(a(!0),o(z,null,S(n.value.requirements,(l,m)=>(a(),o("div",{key:m,class:"flex items-center p-3 border border-gray-200 rounded-lg"},[te(e("input",{type:"checkbox",id:`req-${m}`,"onUpdate:modelValue":C=>_.value[m]=C,class:"h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500",onChange:G},null,40,Te),[[se,_.value[m]]]),e("label",{for:`req-${m}`,class:"ml-3 text-sm text-gray-700 flex-1"},i(l),9,He),_.value[m]?(a(),o("span",Ie," ✓ Completato ")):v("",!0)]))),128))])])):v("",!0),e("div",Oe,[e("div",Fe,[e("div",Le,[e("span",Ue,[r(d,{name:"star",class:"h-4 w-4 mr-1"}),t[8]||(t[8]=h(" Livello di Preparazione Complessivo "))]),e("span",{class:g(["text-xl font-bold",j(b.value)])},i(b.value)+"% ",3)]),e("div",Ge,[e("div",{class:g(["h-3 rounded-full transition-all duration-300",R(b.value)]),style:B({width:b.value+"%"})},null,6)]),e("p",Je,i(K(b.value)),1)]),e("div",Ke,[e("div",Qe,[e("div",We,[e("span",Xe,[r(d,{name:"clipboard-document-check",class:"h-4 w-4 mr-1"}),t[9]||(t[9]=h(" Requisiti Standard "))]),e("span",{class:g(["text-lg font-bold",j(p.value)])},i(p.value)+"% ",3)]),e("div",Ye,[e("div",{class:g(["h-2 rounded-full transition-all duration-300",R(p.value)]),style:B({width:p.value+"%"})},null,6)]),t[10]||(t[10]=e("p",{class:"text-xs text-gray-600 mt-2"}," Completamento requisiti specifici ",-1))]),e("div",Ze,[e("div",et,[e("span",tt,[r(d,{name:"cpu-chip",class:"h-4 w-4 mr-1"}),t[11]||(t[11]=h(" Compliance DatPortal "))]),e("div",st,[e("span",{class:g(["text-lg font-bold",j(((V=c.value)==null?void 0:V.platform_compliance_score)||0)])},i(((E=c.value)==null?void 0:E.platform_compliance_score)||0)+"% ",3),(N=c.value)!=null&&N.calculation_logs?(a(),o("button",{key:0,onClick:t[0]||(t[0]=l=>$.value=!0),class:"text-blue-600 hover:text-blue-800 transition-colors",title:"Mostra dettagli calcolo"},[r(d,{name:"information-circle",class:"h-4 w-4"})])):v("",!0)])]),e("div",at,[e("div",{class:g(["h-2 rounded-full transition-all duration-300",R(((T=c.value)==null?void 0:T.platform_compliance_score)||0)]),style:B({width:(((H=c.value)==null?void 0:H.platform_compliance_score)||0)+"%"})},null,6)]),e("p",ot,i(c.value?`${c.value.total_features_analyzed} funzionalità analizzate`:'Clicca "Avvia Valutazione" per analizzare'),1)])])]),c.value?(a(),o("div",lt,[e("h3",it,[r(d,{name:"chart-bar",class:"h-5 w-5 mr-2 text-blue-600"}),t[12]||(t[12]=h(" Analisi Compliance Piattaforma DatPortal "))]),e("div",nt,[e("div",null,[t[13]||(t[13]=e("h4",{class:"text-sm font-medium text-gray-700 mb-3"},"Funzionalità Analizzate",-1)),e("div",rt,[(a(!0),o(z,null,S(c.value.compliance_details,(l,m)=>{var C;return a(),o("div",{key:m,class:"flex items-center justify-between p-2 bg-gray-50 rounded"},[e("div",ct,[e("div",dt,i(l.name),1),e("div",ut,i(((C=l.requirements_covered)==null?void 0:C.length)||0)+" requisiti coperti",1)]),e("div",mt,[e("div",{class:g(["text-sm font-bold",j(l.weighted_score)])},i(Math.round(l.weighted_score))+"% ",3),e("div",vt,"peso "+i(l.compliance_score)+"%",1)])])}),128))])]),e("div",null,[t[14]||(t[14]=e("h4",{class:"text-sm font-medium text-gray-700 mb-3"},"Raccomandazioni",-1)),e("div",pt,[(a(!0),o(z,null,S((I=c.value.recommendations)==null?void 0:I.slice(0,5),(l,m)=>(a(),o("div",{key:m,class:"flex items-start p-2 bg-blue-50 rounded"},[r(d,{name:l.type==="feature_adoption"?"arrow-trending-up":"wrench-screwdriver",class:"h-4 w-4 mr-2 text-blue-600 flex-shrink-0 mt-0.5"},null,8,["name"]),e("div",ft,[h(i(l.suggestion)+" ",1),l.potential_impact?(a(),o("div",gt," Impatto potenziale: +"+i(l.potential_impact)+"% ",1)):v("",!0)])]))),128))])])])])):v("",!0),n.value.estimated_cost?(a(),o("div",_t,[e("div",bt,[t[15]||(t[15]=e("h4",{class:"font-medium text-blue-900 mb-2"},"Costi Stimati",-1)),e("div",xt,[n.value.estimated_cost.initial?(a(),o("div",ht," Costo iniziale: €"+i(D(n.value.estimated_cost.initial)),1)):v("",!0),n.value.estimated_cost.annual?(a(),o("div",yt," Costo annuale: €"+i(D(n.value.estimated_cost.annual)),1)):v("",!0)])]),e("div",wt,[t[18]||(t[18]=e("h4",{class:"font-medium text-green-900 mb-2"},"Timeline Stimata",-1)),e("div",kt,[e("div",null,"Preparazione: "+i(Q(p.value)),1),t[16]||(t[16]=e("div",null,"Audit: 2-4 settimane",-1)),t[17]||(t[17]=e("div",null,"Certificazione: 1-2 settimane",-1))])])])):v("",!0)])):!y.value&&!w.value?(a(),o("div",Ct,[r(d,{name:"clipboard-document-check",class:"h-12 w-12 text-gray-400 mx-auto mb-4"}),t[19]||(t[19]=e("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"Seleziona uno Standard",-1)),t[20]||(t[20]=e("p",{class:"text-gray-500"},"Scegli uno standard di certificazione per iniziare la valutazione di readiness",-1))])):v("",!0),$.value?(a(),Z(ve,{key:2,show:$.value,title:"Dettagli Calcolo Compliance",logs:((O=c.value)==null?void 0:O.calculation_logs)||[],onClose:t[1]||(t[1]=l=>$.value=!1)},null,8,["show","logs"])):v("",!0)])}}};export{Rt as default};
