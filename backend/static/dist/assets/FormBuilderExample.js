import{F as g}from"./FormBuilder.js";import{_ as E,H as V}from"./app.js";import{r as l,c as y,b as s,l as e,e as n,p as A,F as D,q as L,j as d,v as P,t as u}from"./vendor.js";import"./AlertsSection.js";/* empty css                                                           */const q={class:"space-y-8"},N={class:"space-y-4"},O={class:"bg-gray-50 dark:bg-gray-800 rounded-lg p-6"},U={class:"space-y-4"},I={class:"bg-gray-50 dark:bg-gray-800 rounded-lg p-6"},M=["disabled"],R=["disabled","onClick"],j={key:0,class:"flex items-center"},Y={key:1},G={class:"space-y-4"},H={class:"bg-gray-50 dark:bg-gray-800 rounded-lg p-6"},J={class:"space-y-4"},K={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},Q={class:"overflow-x-auto"},W={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},X={class:"divide-y divide-gray-200 dark:divide-gray-700"},Z={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white"},$={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"},ee={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"},te={class:"px-6 py-4 text-sm text-gray-500 dark:text-gray-400"},ae={__name:"FormBuilderExample",setup(le){const r=l({name:"",email:"",role:""}),x=y(()=>[{id:"name",type:"text",label:"Nome Completo",placeholder:"Es. Mario Rossi",required:!0},{id:"email",type:"email",label:"Email",placeholder:"<EMAIL>",required:!0},{id:"role",type:"select",label:"Ruolo",placeholder:"Seleziona ruolo",required:!0,options:[{value:"admin",label:"Amministratore"},{value:"user",label:"Utente"},{value:"viewer",label:"Visualizzatore"}]}]),o=l({}),i=l(null),c=l(!1),m=l({title:"",description:"",budget:null,deadline:"",priority:"",tags:""}),f=y(()=>[{id:"title",type:"text",label:"Titolo Progetto",placeholder:"Es. Sviluppo nuovo portale",required:!0},{id:"description",type:"textarea",label:"Descrizione",placeholder:"Descrivi gli obiettivi del progetto...",rows:4},{id:"budget",type:"number",label:"Budget (€)",placeholder:"0",min:0,step:100},{id:"deadline",type:"text",label:"Scadenza",placeholder:"YYYY-MM-DD"},{id:"priority",type:"select",label:"Priorità",required:!0,options:[{value:"high",label:"Alta"},{value:"medium",label:"Media"},{value:"low",label:"Bassa"}]}]),h=l({}),v=l(null),p=l(!1),b=l({text_field:"",email_field:"",number_field:null,textarea_field:"",select_field:""}),_=y(()=>[{id:"text_field",type:"text",label:"Campo Testo",placeholder:"Testo normale"},{id:"email_field",type:"email",label:"Campo Email",placeholder:"<EMAIL>"},{id:"number_field",type:"number",label:"Campo Numero",placeholder:"0",min:0,max:100,step:5},{id:"textarea_field",type:"textarea",label:"Area Testo",placeholder:"Testo multiriga...",rows:3},{id:"select_field",type:"select",label:"Campo Select",placeholder:"Scegli opzione",options:[{value:"option1",label:"Opzione 1"},{value:"option2",label:"Opzione 2"},{value:"option3",label:"Opzione 3"}]}]),k=[{name:"fields",type:"Array",default:"[]",description:"Array di configurazione campi del form"},{name:"modelValue",type:"Object",default:"{}",description:"Valore reattivo del form (v-model)"},{name:"errors",type:"Object",default:"{}",description:"Oggetto con errori di validazione per campo"},{name:"globalError",type:"String",default:"null",description:"Messaggio di errore globale"},{name:"loading",type:"Boolean",default:"false",description:"Stato di caricamento del form"},{name:"submitLabel",type:"String",default:"Salva",description:"Label del bottone submit"},{name:"loadingLabel",type:"String",default:"Salvataggio...",description:"Label durante il caricamento"},{name:"cancelLabel",type:"String",default:"Annulla",description:"Label del bottone annulla"},{name:"cancelRoute",type:"String",default:"null",description:"Route per il bottone annulla"},{name:"cancelAction",type:"Function",default:"null",description:"Funzione custom per annulla"}],w=async()=>{c.value=!0,o.value={},i.value=null,r.value.name||(o.value.name="Nome obbligatorio"),r.value.email||(o.value.email="Email obbligatoria"),setTimeout(()=>{Object.keys(o.value).length===0?alert("Form base salvato con successo!"):i.value="Corrigi gli errori nei campi",c.value=!1},1500)},F=()=>{r.value={name:"",email:"",role:""},o.value={},i.value=null},S=async()=>{p.value=!0,v.value=null,setTimeout(()=>{alert("Progetto creato con successo!"),p.value=!1},2e3)},z=()=>{m.value={title:"",description:"",budget:null,deadline:"",priority:"",tags:""}},C=()=>{alert("Bozza salvata!")},B=()=>{console.log("Field Types Form:",b.value),alert("Controlla la console per vedere i valori!")};return(oe,t)=>(d(),s("div",q,[t[9]||(t[9]=e("div",null,[e("h2",{class:"text-2xl font-bold text-gray-900 dark:text-white mb-2"},"FormBuilder Component"),e("p",{class:"text-gray-600 dark:text-gray-400"}," Componente generico per la creazione di form dinamici con validazione, stati di caricamento e gestione errori. ")],-1)),e("section",N,[t[3]||(t[3]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white"},"Esempio Base",-1)),e("div",O,[n(g,{modelValue:r.value,"onUpdate:modelValue":t[0]||(t[0]=a=>r.value=a),fields:x.value,errors:o.value,"global-error":i.value,loading:c.value,"submit-label":"Salva Utente","loading-label":"Salvataggio...","cancel-label":"Annulla",onSubmit:w,onCancel:F},null,8,["modelValue","fields","errors","global-error","loading"])])]),e("section",U,[t[5]||(t[5]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white"},"Form Avanzato con Azioni Custom",-1)),e("div",I,[n(g,{modelValue:m.value,"onUpdate:modelValue":t[1]||(t[1]=a=>m.value=a),fields:f.value,errors:h.value,"global-error":v.value,loading:p.value,"submit-label":"Crea Progetto","loading-label":"Creazione in corso...",onSubmit:S},{actions:A(({loading:a,submit:T})=>[e("button",{type:"button",onClick:z,class:"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"}," Reset "),e("button",{type:"button",onClick:C,disabled:a,class:"px-4 py-2 border border-brand-primary-300 rounded-md text-sm font-medium text-brand-primary-700 bg-brand-primary-50 hover:bg-brand-primary-100 disabled:opacity-50"}," Salva Bozza ",8,M),e("button",{type:"submit",disabled:a,onClick:T,class:"px-4 py-2 bg-brand-primary-600 hover:bg-brand-primary-700 disabled:bg-brand-primary-400 text-white rounded-md text-sm font-medium transition-colors duration-200"},[a?(d(),s("span",j,[n(V,{name:"cog-6-tooth",class:"animate-spin -ml-1 mr-2 h-4 w-4 text-white"}),t[4]||(t[4]=P(" Creazione... "))])):(d(),s("span",Y,"Crea Progetto"))],8,R)]),_:1},8,["modelValue","fields","errors","global-error","loading"])])]),e("section",G,[t[6]||(t[6]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white"},"Tutti i Tipi di Campo",-1)),e("div",H,[n(g,{modelValue:b.value,"onUpdate:modelValue":t[2]||(t[2]=a=>b.value=a),fields:_.value,"submit-label":"Test Campi",onSubmit:B},null,8,["modelValue","fields"])])]),e("section",J,[t[8]||(t[8]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white"},"Props & API",-1)),e("div",K,[e("div",Q,[e("table",W,[t[7]||(t[7]=e("thead",null,[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"},"Prop"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"},"Tipo"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"},"Default"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"},"Descrizione")])],-1)),e("tbody",X,[(d(),s(D,null,L(k,a=>e("tr",{key:a.name},[e("td",Z,u(a.name),1),e("td",$,u(a.type),1),e("td",ee,u(a.default),1),e("td",te,u(a.description),1)])),64))])])])])]),t[10]||(t[10]=e("section",{class:"space-y-4"},[e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-white"},"Esempi di Codice"),e("div",{class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},[e("pre",{class:"bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto text-sm"},[e("code",null,`// Basic Usage
<FormBuilder
  v-model="form"
  :fields="formFields"
  :errors="errors"
  :loading="loading"
  submit-label="Salva"
  @submit="handleSubmit"
/>

// Field Configuration
const formFields = [
  {
    id: 'name',
    type: 'text',
    label: 'Nome',
    placeholder: 'Inserisci nome',
    required: true
  },
  {
    id: 'description',
    type: 'textarea',
    label: 'Descrizione',
    rows: 4
  },
  {
    id: 'status',
    type: 'select',
    label: 'Status',
    options: [
      { value: 'active', label: 'Attivo' },
      { value: 'inactive', label: 'Inattivo' }
    ]
  }
]`)])])],-1))]))}},ue=E(ae,[["__scopeId","data-v-96a7483b"]]);export{ue as default};
