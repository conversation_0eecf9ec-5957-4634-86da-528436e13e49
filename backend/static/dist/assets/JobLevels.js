import{r as h,c as D,w as R,b as m,j as u,l as e,t as c,e as x,A as B,B as w,C as $,S as J,O,v as V,g,n as U,F as A,q as F,o as H,f as N,p as L}from"./vendor.js";import{_ as P}from"./ListPageTemplate.js";import{H as p,_ as Q}from"./app.js";import"./Pagination.js";const G={class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},K={class:"relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white dark:bg-gray-800"},W={class:"mt-3"},X={class:"flex items-center justify-between pb-4"},Y={class:"text-lg font-medium text-gray-900 dark:text-white"},Z={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},ee={class:"flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700"},te=["disabled"],ae={key:0,class:"flex items-center"},re={key:1},se={__name:"JobLevelModal",props:{jobLevel:{type:Object,default:null}},emits:["close","saved"],setup(l,{emit:_}){const b=l,f=_,y=h(!1),a=h({name:"",description:"",category:"",level:"",salary_min:null,salary_max:null,benefits:"",requirements:""}),r=D(()=>{var n;return!!((n=b.jobLevel)!=null&&n.id)}),v=()=>{a.value={name:"",description:"",category:"",level:"",salary_min:null,salary_max:null,benefits:"",requirements:""}},C=n=>{n&&(a.value={name:n.name||"",description:n.description||"",category:n.category||"",level:n.level||"",salary_min:n.salary_min||null,salary_max:n.salary_max||null,benefits:n.benefits||"",requirements:n.requirements||""})},j=async()=>{y.value=!0;try{const n=r.value?`/api/personnel/job-levels/${b.jobLevel.id}`:"/api/personnel/job-levels",t=r.value?"PUT":"POST",i=await fetch(n,{method:t,headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(a.value)});if(!i.ok)throw new Error(`HTTP ${i.status}: ${i.statusText}`);const k=await i.json();if(k.success)f("saved",k.data.job_level);else throw new Error(k.message||"Errore nel salvataggio")}catch(n){console.error("Error saving job level:",n),alert("Errore nel salvataggio: "+n.message)}finally{y.value=!1}};return R(()=>b.jobLevel,n=>{n?C(n):v()},{immediate:!0}),(n,t)=>(u(),m("div",G,[e("div",K,[e("div",W,[e("div",X,[e("h3",Y,c(r.value?"Modifica Inquadramento":"Nuovo Inquadramento"),1),e("button",{onClick:t[0]||(t[0]=i=>n.$emit("close")),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},[x(p,{name:"x-mark",size:"md"})])]),e("form",{onSubmit:B(j,["prevent"]),class:"space-y-6"},[e("div",Z,[e("div",null,[t[10]||(t[10]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Nome Inquadramento * ",-1)),w(e("input",{"onUpdate:modelValue":t[1]||(t[1]=i=>a.value.name=i),type:"text",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"es. Senior Developer"},null,512),[[$,a.value.name]])]),e("div",null,[t[12]||(t[12]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Categoria * ",-1)),w(e("select",{"onUpdate:modelValue":t[2]||(t[2]=i=>a.value.category=i),required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},t[11]||(t[11]=[O('<option value="">Seleziona categoria</option><option value="dirigente">Dirigente</option><option value="quadro">Quadro</option><option value="impiegato">Impiegato</option><option value="operaio">Operaio</option>',5)]),512),[[J,a.value.category]])]),e("div",null,[t[13]||(t[13]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Livello ",-1)),w(e("input",{"onUpdate:modelValue":t[3]||(t[3]=i=>a.value.level=i),type:"text",class:"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"es. Junior, Senior, Lead"},null,512),[[$,a.value.level]])]),e("div",null,[t[14]||(t[14]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," RAL Minima (€) ",-1)),w(e("input",{"onUpdate:modelValue":t[4]||(t[4]=i=>a.value.salary_min=i),type:"number",min:"0",step:"1000",class:"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"25000"},null,512),[[$,a.value.salary_min,void 0,{number:!0}]])]),e("div",null,[t[15]||(t[15]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," RAL Massima (€) ",-1)),w(e("input",{"onUpdate:modelValue":t[5]||(t[5]=i=>a.value.salary_max=i),type:"number",min:"0",step:"1000",class:"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"45000"},null,512),[[$,a.value.salary_max,void 0,{number:!0}]])])]),e("div",null,[t[16]||(t[16]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Descrizione ",-1)),w(e("textarea",{"onUpdate:modelValue":t[6]||(t[6]=i=>a.value.description=i),rows:"3",class:"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Descrizione del ruolo e responsabilità"},null,512),[[$,a.value.description]])]),e("div",null,[t[17]||(t[17]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Benefits ",-1)),w(e("textarea",{"onUpdate:modelValue":t[7]||(t[7]=i=>a.value.benefits=i),rows:"3",class:"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"es. Buoni pasto, auto aziendale, assicurazione sanitaria"},null,512),[[$,a.value.benefits]])]),e("div",null,[t[18]||(t[18]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Requisiti ",-1)),w(e("textarea",{"onUpdate:modelValue":t[8]||(t[8]=i=>a.value.requirements=i),rows:"3",class:"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"es. Laurea in informatica, 3+ anni di esperienza"},null,512),[[$,a.value.requirements]])]),e("div",ee,[e("button",{type:"button",onClick:t[9]||(t[9]=i=>n.$emit("close")),class:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600"}," Annulla "),e("button",{type:"submit",disabled:y.value,class:"px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"},[y.value?(u(),m("span",ae,t[19]||(t[19]=[e("svg",{class:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",fill:"none",viewBox:"0 0 24 24"},[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),V(" Salvando... ")]))):(u(),m("span",re,c(r.value?"Aggiorna":"Crea"),1))],8,te)])],32)])])]))}},oe={class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},le={class:"relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-2/3 shadow-lg rounded-md bg-white dark:bg-gray-800"},ne={class:"mt-3"},ie={class:"flex items-center justify-between pb-4 border-b border-gray-200 dark:border-gray-700"},de={class:"flex items-center"},ue={class:"h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mr-4"},me={class:"text-lg font-medium text-gray-900 dark:text-white"},ce={key:0,class:"text-sm text-gray-500 dark:text-gray-400"},ge={class:"py-6 space-y-6"},be={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},ye={class:"flex items-center"},xe={class:"text-sm text-gray-900 dark:text-white"},ve={class:"md:col-span-2"},pe={key:0,class:"space-y-1"},fe={key:0,class:"flex items-center"},ke={class:"text-sm font-medium text-green-600"},we={key:1,class:"flex items-center"},he={class:"text-sm font-medium text-blue-600"},_e={key:1,class:"text-sm text-gray-500 dark:text-gray-400"},je={key:0},$e={class:"text-sm text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 p-3 rounded-md"},Ce={key:1},Le={class:"text-sm text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 p-3 rounded-md"},qe={key:2},ze={class:"text-sm text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 p-3 rounded-md"},Ee={key:3},Te={class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3"},Me={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3"},Ie={class:"flex items-center"},De={class:"h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mr-3"},Ve={class:"flex-1 min-w-0"},Se={class:"text-sm font-medium text-gray-900 dark:text-white truncate"},Ne={key:0,class:"text-xs text-gray-500 dark:text-gray-400 truncate"},Je={class:"pt-4 border-t border-gray-200 dark:border-gray-700"},Ue={class:"grid grid-cols-1 md:grid-cols-2 gap-4 text-xs text-gray-500 dark:text-gray-400"},Ae={key:0},Re={key:1},Be={class:"flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700"},Oe={__name:"JobLevelViewModal",props:{jobLevel:{type:Object,required:!0}},emits:["close"],setup(l){const _=a=>({dirigente:"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",quadro:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",impiegato:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",operaio:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"})[a]||"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200",b=a=>({dirigente:"Dirigente",quadro:"Quadro",impiegato:"Impiegato",operaio:"Operaio"})[a]||a,f=a=>a?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(a):"",y=a=>a?new Date(a).toLocaleDateString("it-IT",{year:"numeric",month:"long",day:"numeric"}):"";return(a,r)=>{var v,C,j,n,t,i,k,z,E,T,M,I,q,s,d;return u(),m("div",oe,[e("div",le,[e("div",ne,[e("div",ie,[e("div",de,[e("div",ue,[x(p,{name:"academic-cap",size:"md",class:"text-blue-600 dark:text-blue-400"})]),e("div",null,[e("h3",me,c((v=l.jobLevel)==null?void 0:v.name),1),(C=l.jobLevel)!=null&&C.level?(u(),m("p",ce,c(l.jobLevel.level),1)):g("",!0)])]),e("button",{onClick:r[0]||(r[0]=o=>a.$emit("close")),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},[x(p,{name:"x-mark",size:"md"})])]),e("div",ge,[e("div",be,[e("div",null,[r[2]||(r[2]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Categoria ",-1)),e("span",{class:U(["inline-flex items-center px-3 py-1 rounded-full text-sm font-medium",_((j=l.jobLevel)==null?void 0:j.category)])},c(b((n=l.jobLevel)==null?void 0:n.category)),3)]),e("div",null,[r[3]||(r[3]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Dipendenti Assegnati ",-1)),e("div",ye,[x(p,{name:"users",size:"sm",class:"text-gray-400 mr-2"}),e("span",xe,c(((t=l.jobLevel)==null?void 0:t.employees_count)||0)+" dipendenti ",1)])]),e("div",ve,[r[6]||(r[6]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Range Retributivo ",-1)),(i=l.jobLevel)!=null&&i.salary_min||(k=l.jobLevel)!=null&&k.salary_max?(u(),m("div",pe,[(z=l.jobLevel)!=null&&z.salary_min?(u(),m("div",fe,[r[4]||(r[4]=e("span",{class:"text-sm text-gray-600 dark:text-gray-400 w-16"},"Min:",-1)),e("span",ke,c(f(l.jobLevel.salary_min)),1)])):g("",!0),(E=l.jobLevel)!=null&&E.salary_max?(u(),m("div",we,[r[5]||(r[5]=e("span",{class:"text-sm text-gray-600 dark:text-gray-400 w-16"},"Max:",-1)),e("span",he,c(f(l.jobLevel.salary_max)),1)])):g("",!0)])):(u(),m("span",_e," Non specificato "))])]),(T=l.jobLevel)!=null&&T.description?(u(),m("div",je,[r[7]||(r[7]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Descrizione ",-1)),e("p",$e,c(l.jobLevel.description),1)])):g("",!0),(M=l.jobLevel)!=null&&M.requirements?(u(),m("div",Ce,[r[8]||(r[8]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Requisiti ",-1)),e("p",Le,c(l.jobLevel.requirements),1)])):g("",!0),(I=l.jobLevel)!=null&&I.benefits?(u(),m("div",qe,[r[9]||(r[9]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Benefits ",-1)),e("p",ze,c(l.jobLevel.benefits),1)])):g("",!0),(q=l.jobLevel)!=null&&q.employees&&l.jobLevel.employees.length>0?(u(),m("div",Ee,[e("label",Te," Dipendenti Attuali ("+c(l.jobLevel.employees.length)+") ",1),e("div",Me,[(u(!0),m(A,null,F(l.jobLevel.employees,o=>(u(),m("div",{key:o.id,class:"bg-gray-50 dark:bg-gray-700 p-3 rounded-md"},[e("div",Ie,[e("div",De,[x(p,{name:"user",size:"sm",class:"text-blue-600 dark:text-blue-400"})]),e("div",Ve,[e("p",Se,c(o.full_name),1),o.position?(u(),m("p",Ne,c(o.position),1)):g("",!0)])])]))),128))])])):g("",!0),e("div",Je,[e("div",Ue,[(s=l.jobLevel)!=null&&s.created_at?(u(),m("div",Ae,[r[10]||(r[10]=e("span",{class:"font-medium"},"Creato:",-1)),V(" "+c(y(l.jobLevel.created_at)),1)])):g("",!0),(d=l.jobLevel)!=null&&d.updated_at?(u(),m("div",Re,[r[11]||(r[11]=e("span",{class:"font-medium"},"Aggiornato:",-1)),V(" "+c(y(l.jobLevel.updated_at)),1)])):g("",!0)])])]),e("div",Be,[e("button",{onClick:r[1]||(r[1]=o=>a.$emit("close")),class:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600"}," Chiudi ")])])])])}}},Fe={class:"flex items-center"},He={class:"h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center mr-3"},Pe={class:"font-medium"},Qe={key:0,class:"text-sm text-gray-500"},Ge={key:0,class:"text-sm"},Ke={key:0,class:"text-green-600"},We={key:1,class:"text-blue-600"},Xe={key:1,class:"text-gray-400"},Ye={class:"flex items-center"},Ze={class:"flex items-center space-x-2"},et=["onClick"],tt=["onClick"],at=["onClick"],rt={__name:"JobLevels",setup(l){const _=h(!1),b=h([]),f=h(""),y=h(!1),a=h(!1),r=h(!1),v=h(null),C=[{key:"name",label:"Nome"},{key:"category",label:"Categoria"},{key:"salary_range",label:"Range Retributivo"},{key:"employees_count",label:"Dipendenti"},{key:"actions",label:"Azioni"}],j=D(()=>f.value?b.value.filter(s=>s.category===f.value):b.value),n=D(()=>[{label:"Totale Inquadramenti",value:b.value.length,icon:"academic-cap",iconClass:"text-blue-500"},{label:"Inquadramenti Mostrati",value:j.value.length,icon:"eye",iconClass:"text-purple-500"},{label:"Dipendenti Totali",value:b.value.reduce((s,d)=>s+(d.employees_count||0),0),icon:"users",iconClass:"text-green-500"},{label:"Categorie Attive",value:new Set(b.value.map(s=>s.category)).size,icon:"building-office",iconClass:"text-orange-500"}]),t=async()=>{_.value=!0;try{const s=await fetch("/api/personnel/job-levels",{credentials:"include"});if(!s.ok)throw new Error(`HTTP ${s.status}: ${s.statusText}`);const d=await s.json();if(d.success)b.value=d.data.job_levels;else throw new Error(d.message||"Errore nel caricamento")}catch(s){console.error("Error loading job levels:",s),alert("Errore nel caricamento degli inquadramenti: "+s.message)}finally{_.value=!1}},i=async s=>{if(s.employees_count>0){alert("Impossibile eliminare un inquadramento associato a dipendenti");return}if(confirm(`Sei sicuro di voler eliminare l'inquadramento "${s.name}"?`))try{const d=await fetch(`/api/personnel/job-levels/${s.id}`,{method:"DELETE",credentials:"include"});if(!d.ok)throw new Error(`HTTP ${d.status}: ${d.statusText}`);const o=await d.json();if(o.success)await t();else throw new Error(o.message||"Errore nell'eliminazione")}catch(d){console.error("Error deleting job level:",d),alert("Errore nell'eliminazione: "+d.message)}},k=()=>{y.value=!1,a.value=!1,v.value=null},z=async()=>{k(),await t()},E=s=>{v.value=s,r.value=!0},T=s=>{v.value=s,a.value=!0},M=s=>({dirigente:"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",quadro:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",impiegato:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",operaio:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"})[s]||"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200",I=s=>({dirigente:"Dirigente",quadro:"Quadro",impiegato:"Impiegato",operaio:"Operaio"})[s]||s,q=s=>s?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(s):"";return H(()=>{t()}),(s,d)=>(u(),m(A,null,[x(P,{title:"Gestione Inquadramenti",subtitle:"Gestione dei livelli di inquadramento aziendale",data:j.value,columns:C,stats:n.value,loading:_.value,"can-create":!0,"create-label":"Nuovo Inquadramento","search-placeholder":"Cerca inquadramenti...","results-label":"inquadramenti",onCreate:d[1]||(d[1]=o=>y.value=!0)},{filters:L(()=>[w(e("select",{"onUpdate:modelValue":d[0]||(d[0]=o=>f.value=o),class:"ml-3 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},d[3]||(d[3]=[e("option",{value:""},"Tutte le categorie",-1),e("option",{value:"dirigente"},"Dirigente",-1),e("option",{value:"quadro"},"Quadro",-1),e("option",{value:"impiegato"},"Impiegato",-1),e("option",{value:"operaio"},"Operaio",-1)]),512),[[J,f.value]])]),"column-name":L(({item:o})=>[e("div",Fe,[e("div",He,[x(p,{name:"academic-cap",size:"sm",class:"text-blue-600"})]),e("div",null,[e("div",Pe,c(o.name),1),o.level?(u(),m("div",Qe,c(o.level),1)):g("",!0)])])]),"column-category":L(({item:o})=>[e("span",{class:U(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",M(o.category)])},c(I(o.category)),3)]),"column-salary_range":L(({item:o})=>[o.salary_min||o.salary_max?(u(),m("div",Ge,[o.salary_min?(u(),m("div",Ke," Min: "+c(q(o.salary_min)),1)):g("",!0),o.salary_max?(u(),m("div",We," Max: "+c(q(o.salary_max)),1)):g("",!0)])):(u(),m("span",Xe,"Non specificato"))]),"column-employees_count":L(({item:o})=>[e("div",Ye,[x(p,{name:"users",size:"sm",class:"text-gray-400 mr-1"}),e("span",null,c(o.employees_count||0),1)])]),"column-actions":L(({item:o})=>[e("div",Ze,[e("button",{onClick:S=>E(o),class:"text-blue-600 hover:text-blue-900",title:"Visualizza"},[x(p,{name:"eye",size:"sm"})],8,et),e("button",{onClick:S=>T(o),class:"text-green-600 hover:text-green-900",title:"Modifica"},[x(p,{name:"pencil",size:"sm"})],8,tt),e("button",{onClick:S=>i(o),class:"text-red-600 hover:text-red-900",title:"Elimina"},[x(p,{name:"trash",size:"sm"})],8,at)])]),_:1},8,["data","stats","loading"]),y.value||a.value?(u(),N(se,{key:0,"job-level":v.value,onClose:k,onSaved:z},null,8,["job-level"])):g("",!0),r.value?(u(),N(Oe,{key:1,"job-level":v.value,onClose:d[2]||(d[2]=o=>r.value=!1)},null,8,["job-level"])):g("",!0)],64))}},it=Q(rt,[["__scopeId","data-v-d52ee61b"]]);export{it as default};
