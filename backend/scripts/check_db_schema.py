#!/usr/bin/env python3
"""
Script per verificare la struttura reale del database
Mostra tutte le tabelle e le loro colonne
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app import create_app
from extensions import db
from sqlalchemy import inspect, text

def check_table_columns(table_name):
    """Verifica le colonne di una tabella specifica"""
    app = create_app()
    
    with app.app_context():
        try:
            inspector = inspect(db.engine)
            
            if table_name not in inspector.get_table_names():
                print(f"❌ Tabella '{table_name}' NON TROVATA")
                return None
                
            columns = inspector.get_columns(table_name)
            print(f"\n📋 TABELLA: {table_name}")
            print("=" * 50)
            
            for col in columns:
                nullable = "NULL" if col['nullable'] else "NOT NULL"
                default = f" DEFAULT: {col.get('default', 'None')}" if col.get('default') else ""
                print(f"  {col['name']:<25} {str(col['type']):<20} {nullable}{default}")
                
            return columns
            
        except Exception as e:
            print(f"❌ Errore: {e}")
            return None

def check_all_tables():
    """Verifica tutte le tabelle nel database"""
    app = create_app()
    
    with app.app_context():
        try:
            inspector = inspect(db.engine)
            tables = sorted(inspector.get_table_names())
            
            print("🗄️  DATABASE SCHEMA COMPLETO")
            print("=" * 60)
            print(f"Totale tabelle: {len(tables)}")
            print()
            
            for table in tables:
                check_table_columns(table)
                print()
                
        except Exception as e:
            print(f"❌ Errore: {e}")

def check_specific_tables():
    """Verifica le tabelle che abbiamo modificato nei test"""
    tables_to_check = [
        'personnel_rates',
        'project_expenses', 
        'project_resources',
        'project_kpis',
        'task_dependencies',
        'forum_topics',
        'direct_messages',
        'polls',
        'contracts',
        'company_events'
    ]
    
    print("🔍 VERIFICA TABELLE MODIFICATE NEI TEST")
    print("=" * 60)
    
    for table in tables_to_check:
        check_table_columns(table)

def search_column_in_tables(column_name):
    """Cerca una colonna specifica in tutte le tabelle"""
    app = create_app()
    
    with app.app_context():
        try:
            inspector = inspect(db.engine)
            tables = inspector.get_table_names()
            found_tables = []
            
            print(f"🔍 CERCANDO COLONNA: '{column_name}'")
            print("=" * 50)
            
            for table in tables:
                columns = inspector.get_columns(table)
                for col in columns:
                    if column_name.lower() in col['name'].lower():
                        found_tables.append((table, col['name'], col['type']))
                        
            if found_tables:
                print(f"✅ Trovata in {len(found_tables)} tabelle:")
                for table, col_name, col_type in found_tables:
                    print(f"  {table}.{col_name} ({col_type})")
            else:
                print(f"❌ Colonna '{column_name}' non trovata in nessuna tabella")
                
        except Exception as e:
            print(f"❌ Errore: {e}")

def main():
    """Funzione principale"""
    if len(sys.argv) < 2:
        print("Uso:")
        print("  python check_db_schema.py all                    # Tutte le tabelle")
        print("  python check_db_schema.py specific               # Tabelle modificate")
        print("  python check_db_schema.py table <nome_tabella>   # Tabella specifica")
        print("  python check_db_schema.py search <nome_colonna>  # Cerca colonna")
        return
    
    command = sys.argv[1]
    
    if command == "all":
        check_all_tables()
    elif command == "specific":
        check_specific_tables()
    elif command == "table" and len(sys.argv) > 2:
        table_name = sys.argv[2]
        check_table_columns(table_name)
    elif command == "search" and len(sys.argv) > 2:
        column_name = sys.argv[2]
        search_column_in_tables(column_name)
    else:
        print("❌ Comando non riconosciuto")

if __name__ == "__main__":
    main()
