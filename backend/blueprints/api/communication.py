from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
from datetime import datetime, timedelta
from sqlalchemy import or_, and_, desc, asc
from sqlalchemy.orm import joinedload

from extensions import db
from models import (
    User, CompanyEvent, ForumTopic, ForumComment, Poll, PollOption, PollVote,
    DirectMessage, CommunicationReaction, CompanyEventRegistration, News
)
from utils.api_utils import api_response, api_permission_required
from utils.permissions import (
    PERMISSION_MANAGE_COMMUNICATION, PERMISSION_VIEW_COMMUNICATION,
    PERMISSION_CREATE_POLLS, PERMISSION_MODERATE_FORUM
)

communication_bp = Blueprint('api_communication', __name__)

# ============================================================================
# COMPANY COMMUNICATIONS (NEWS & ANNOUNCEMENTS) ENDPOINTS
# ============================================================================

@communication_bp.route('/company', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMMUNICATION)
def get_company_communications():
    """Recupera le comunicazioni aziendali con paginazione e filtri"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)
        search = request.args.get('search')
        published_only = request.args.get('published_only', 'true').lower() == 'true'
        
        # Base query
        query = News.query.options(joinedload(News.author))
        
        # Filtri
        if published_only:
            query = query.filter(News.is_published == True)
        
        if search:
            search_term = f"%{search}%"
            query = query.filter(
                or_(
                    News.title.ilike(search_term),
                    News.content.ilike(search_term)
                )
            )
        
        # Ordinamento per data di creazione (più recenti prima)
        query = query.order_by(desc(News.created_at))
        
        # Paginazione
        communications = query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        return api_response(
            data={
                'communications': [{
                    'id': comm.id,
                    'title': comm.title,
                    'content': comm.content,
                    'author': {
                        'id': comm.author.id,
                        'first_name': comm.author.first_name,
                        'last_name': comm.author.last_name,
                        'full_name': comm.author.full_name
                    } if comm.author else None,
                    'image_url': comm.image_url,
                    'is_published': comm.is_published,
                    'created_at': comm.created_at.isoformat(),
                    'updated_at': comm.updated_at.isoformat() if comm.updated_at else None
                } for comm in communications.items],
                'pagination': {
                    'page': communications.page,
                    'pages': communications.pages,
                    'per_page': communications.per_page,
                    'total': communications.total,
                    'has_next': communications.has_next,
                    'has_prev': communications.has_prev
                }
            },
            message="Comunicazioni aziendali recuperate con successo"
        )
        
    except Exception as e:
        current_app.logger.error(f"Errore nel recupero delle comunicazioni: {str(e)}")
        return api_response(
                        message="Errore nel recupero delle comunicazioni",
            status_code=500
        )


@communication_bp.route('/company', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_MANAGE_COMMUNICATION)
def create_company_communication():
    """Crea una nuova comunicazione aziendale"""
    try:
        data = request.get_json()
        
        # Validazione
        if not data.get('title') or not data.get('content'):
            return api_response(
                                message="Titolo e contenuto sono obbligatori",
                status_code=400
            )
        
        # Creazione comunicazione
        communication = News(
            title=data['title'].strip(),
            content=data['content'].strip(),
            author_id=current_user.id,
            image_url=data.get('image_url', '').strip() or None,
            is_published=data.get('is_published', True)
        )
        
        db.session.add(communication)
        db.session.commit()
        
        return api_response(
            data={
                'id': communication.id,
                'title': communication.title,
                'content': communication.content,
                'author': {
                    'id': current_user.id,
                    'first_name': current_user.first_name,
                    'last_name': current_user.last_name,
                    'full_name': current_user.full_name
                },
                'image_url': communication.image_url,
                'is_published': communication.is_published,
                'created_at': communication.created_at.isoformat(),
                'updated_at': communication.updated_at.isoformat() if communication.updated_at else None
            },
            message="Comunicazione creata con successo",
            status_code=201
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Errore nella creazione della comunicazione: {str(e)}")
        return api_response(
                        message="Errore nella creazione della comunicazione",
            status_code=500
        )


@communication_bp.route('/company/<int:communication_id>', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMMUNICATION)
def get_company_communication(communication_id):
    """Recupera una comunicazione aziendale specifica"""
    try:
        communication = News.query.options(joinedload(News.author)).get_or_404(communication_id)
        
        return api_response(
            data={
                'id': communication.id,
                'title': communication.title,
                'content': communication.content,
                'author': {
                    'id': communication.author.id,
                    'first_name': communication.author.first_name,
                    'last_name': communication.author.last_name,
                    'full_name': communication.author.full_name
                } if communication.author else None,
                'image_url': communication.image_url,
                'is_published': communication.is_published,
                'created_at': communication.created_at.isoformat(),
                'updated_at': communication.updated_at.isoformat() if communication.updated_at else None
            },
            message="Comunicazione recuperata con successo"
        )
        
    except Exception as e:
        current_app.logger.error(f"Errore nel recupero della comunicazione: {str(e)}")
        return api_response(
                        message="Errore nel recupero della comunicazione",
            status_code=500
        )


@communication_bp.route('/company/<int:communication_id>', methods=['PUT'])
@login_required
@api_permission_required(PERMISSION_MANAGE_COMMUNICATION)
def update_company_communication(communication_id):
    """Modifica una comunicazione aziendale"""
    try:
        communication = News.query.get_or_404(communication_id)
        data = request.get_json()
        
        # Validazione
        if not data.get('title') or not data.get('content'):
            return api_response(
                                message="Titolo e contenuto sono obbligatori",
                status_code=400
            )
        
        # Aggiornamento
        communication.title = data['title'].strip()
        communication.content = data['content'].strip()
        communication.image_url = data.get('image_url', '').strip() or None
        communication.is_published = data.get('is_published', communication.is_published)
        communication.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        return api_response(
            data={
                'id': communication.id,
                'title': communication.title,
                'content': communication.content,
                'author': {
                    'id': communication.author.id,
                    'first_name': communication.author.first_name,
                    'last_name': communication.author.last_name,
                    'full_name': communication.author.full_name
                } if communication.author else None,
                'image_url': communication.image_url,
                'is_published': communication.is_published,
                'created_at': communication.created_at.isoformat(),
                'updated_at': communication.updated_at.isoformat()
            },
            message="Comunicazione aggiornata con successo"
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Errore nell'aggiornamento della comunicazione: {str(e)}")
        return api_response(
                        message="Errore nell'aggiornamento della comunicazione",
            status_code=500
        )


@communication_bp.route('/company/<int:communication_id>', methods=['DELETE'])
@login_required
@api_permission_required(PERMISSION_MANAGE_COMMUNICATION)
def delete_company_communication(communication_id):
    """Elimina una comunicazione aziendale"""
    try:
        communication = News.query.get_or_404(communication_id)
        
        db.session.delete(communication)
        db.session.commit()
        
        return api_response(
            message="Comunicazione eliminata con successo"
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Errore nell'eliminazione della comunicazione: {str(e)}")
        return api_response(
                        message="Errore nell'eliminazione della comunicazione",
            status_code=500
        )


# ============================================================================
# FORUM ENDPOINTS
# ============================================================================

@communication_bp.route('/forum/topics', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMMUNICATION)
def get_forum_topics():
    """Recupera i topic del forum con paginazione e filtri"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)
        category = request.args.get('category')
        search = request.args.get('search')
        sort_by = request.args.get('sort_by', 'created_at')
        sort_order = request.args.get('sort_order', 'desc')
        
        # Base query
        query = ForumTopic.query.options(
            joinedload(ForumTopic.author)
        )
        
        # Filtri
        if category:
            query = query.filter(ForumTopic.category == category)
        
        if search:
            search_term = f"%{search}%"
            query = query.filter(
                or_(
                    ForumTopic.title.ilike(search_term),
                    ForumTopic.description.ilike(search_term)
                )
            )
        
        # Ordinamento
        if sort_by == 'title':
            order_col = ForumTopic.title
        elif sort_by == 'view_count':
            order_col = ForumTopic.view_count
        elif sort_by == 'updated_at':
            order_col = ForumTopic.updated_at
        else:
            order_col = ForumTopic.created_at
        
        if sort_order == 'desc':
            query = query.order_by(desc(order_col))
        else:
            query = query.order_by(asc(order_col))
        
        # Prima i topic pinned
        query = query.order_by(desc(ForumTopic.is_pinned))
        
        # Paginazione
        topics = query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        return api_response(
            data={
                'topics': [topic.to_dict(include_reactions=True, current_user_id=current_user.id) for topic in topics.items],
                'pagination': {
                    'page': topics.page,
                    'pages': topics.pages,
                    'per_page': topics.per_page,
                    'total': topics.total,
                    'has_next': topics.has_next,
                    'has_prev': topics.has_prev
                }
            },
            message="Topic del forum recuperati con successo"
        )
        
    except Exception as e:
        current_app.logger.error(f"Errore nel recupero dei topic del forum: {str(e)}")
        return api_response(
                        message="Errore nel recupero dei topic del forum",
            status_code=500
        )


@communication_bp.route('/forum/topics', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMMUNICATION)
def create_forum_topic():
    """Crea un nuovo topic del forum"""
    try:
        data = request.get_json()
        
        # Validazione (accept both 'content' and 'description' for backward compatibility)
        content = data.get('content') or data.get('description')
        if not data.get('title') or not content:
            return api_response(
                message="Titolo e contenuto sono obbligatori",
                status_code=400
            )
        
        # Creazione topic
        topic = ForumTopic(
            title=data['title'].strip(),
            description=content.strip(),
            author_id=current_user.id,
            category=data.get('category', '').strip() or None
        )
        
        db.session.add(topic)
        db.session.commit()
        
        return api_response(
            data=topic.to_dict(),
            message="Topic creato con successo",
            status_code=201
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Errore nella creazione del topic: {str(e)}")
        return api_response(
                        message="Errore nella creazione del topic",
            status_code=500
        )


@communication_bp.route('/forum/topics/<int:topic_id>', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMMUNICATION)
def get_forum_topic(topic_id):
    """Recupera un topic specifico con i suoi commenti"""
    try:
        topic = ForumTopic.query.options(
            joinedload(ForumTopic.author)
        ).get_or_404(topic_id)
        
        # Incrementa il contatore delle visualizzazioni
        topic.view_count += 1
        db.session.commit()
        
        # Recupera i commenti con paginazione
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)
        
        comments = ForumComment.query.filter_by(topic_id=topic_id, parent_comment_id=None)\
            .options(joinedload(ForumComment.author))\
            .order_by(ForumComment.created_at)\
            .paginate(page=page, per_page=per_page, error_out=False)
        
        # Include reactions in topic data
        topic_data = topic.to_dict(include_reactions=True, current_user_id=current_user.id)
        topic_data['comments'] = {
            'items': [comment.to_dict() for comment in comments.items],
            'pagination': {
                'page': comments.page,
                'pages': comments.pages,
                'per_page': comments.per_page,
                'total': comments.total,
                'has_next': comments.has_next,
                'has_prev': comments.has_prev
            }
        }
        
        return api_response(
            data=topic_data,
            message="Topic recuperato con successo"
        )
        
    except Exception as e:
        current_app.logger.error(f"Errore nel recupero del topic: {str(e)}")
        return api_response(
                        message="Errore nel recupero del topic",
            status_code=500
        )


@communication_bp.route('/forum/topics/<int:topic_id>/comments', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMMUNICATION)
def get_forum_topic_comments(topic_id):
    """Recupera i commenti di un topic del forum"""
    try:
        topic = ForumTopic.query.get_or_404(topic_id)
        
        # Recupera i commenti con paginazione
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)
        
        comments = ForumComment.query.filter_by(topic_id=topic_id, parent_comment_id=None)\
            .options(joinedload(ForumComment.author))\
            .order_by(ForumComment.created_at)\
            .paginate(page=page, per_page=per_page, error_out=False)
        
        return api_response(
            data={
                'comments': [comment.to_dict() for comment in comments.items],
                'pagination': {
                    'page': comments.page,
                    'pages': comments.pages,
                    'per_page': comments.per_page,
                    'total': comments.total,
                    'has_next': comments.has_next,
                    'has_prev': comments.has_prev
                }
            },
            message="Commenti recuperati con successo"
        )
    except Exception as e:
        print(f"Errore nel recupero dei commenti: {e}")
        return api_response(
            message="Errore nel recupero dei commenti",
            status_code=500
        )


@communication_bp.route('/forum/topics/<int:topic_id>/views', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMMUNICATION)
def increment_topic_views(topic_id):
    """Incrementa il contatore delle visualizzazioni del topic"""
    try:
        topic = ForumTopic.query.get_or_404(topic_id)
        
        # Incrementa il contatore delle visualizzazioni
        topic.view_count = (topic.view_count or 0) + 1
        db.session.commit()
        
        return api_response(
            data={'view_count': topic.view_count},
            message="Visualizzazioni aggiornate"
        )
    except Exception as e:
        print(f"Errore nell'incremento delle visualizzazioni: {e}")
        return api_response(
            message="Errore nell'incremento delle visualizzazioni",
            status_code=500
        )


@communication_bp.route('/forum/topics/<int:topic_id>/comments', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMMUNICATION)
def create_forum_comment(topic_id):
    """Crea un commento su un topic del forum"""
    try:
        topic = ForumTopic.query.get_or_404(topic_id)
        
        if topic.is_locked:
            return api_response(
                                message="Non è possibile commentare su un topic bloccato",
                status_code=403
            )
        
        data = request.get_json()
        
        if not data.get('content'):
            return api_response(
                                message="Il contenuto del commento è obbligatorio",
                status_code=400
            )
        
        # Verifica se è una risposta a un altro commento
        parent_comment_id = data.get('parent_comment_id')
        if parent_comment_id:
            parent_comment = ForumComment.query.filter_by(
                id=parent_comment_id, topic_id=topic_id
            ).first()
            if not parent_comment:
                return api_response(
                                        message="Commento padre non trovato",
                    status_code=404
                )
        
        comment = ForumComment(
            topic_id=topic_id,
            author_id=current_user.id,
            content=data['content'].strip(),
            parent_comment_id=parent_comment_id
        )
        
        db.session.add(comment)
        db.session.commit()
        
        return api_response(
            data=comment.to_dict(),
            message="Commento creato con successo",
            status_code=201
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Errore nella creazione del commento: {str(e)}")
        return api_response(
                        message="Errore nella creazione del commento",
            status_code=500
        )


# ============================================================================
# POLLS ENDPOINTS
# ============================================================================

@communication_bp.route('/polls', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMMUNICATION)
def get_polls():
    """Recupera i sondaggi con filtri"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)
        status = request.args.get('status')  # 'active', 'expired', 'all'
        
        query = Poll.query.options(joinedload(Poll.author))
        
        # Filtri per status
        if status == 'active':
            query = query.filter(
                and_(
                    Poll.is_active == True,
                    or_(Poll.expires_at.is_(None), Poll.expires_at > datetime.utcnow())
                )
            )
        elif status == 'expired':
            query = query.filter(
                or_(
                    Poll.is_active == False,
                    Poll.expires_at <= datetime.utcnow()
                )
            )
        
        query = query.order_by(desc(Poll.created_at))
        
        polls = query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        return api_response(
            data={
                'polls': [poll.to_dict(include_results=True) for poll in polls.items],
                'pagination': {
                    'page': polls.page,
                    'pages': polls.pages,
                    'per_page': polls.per_page,
                    'total': polls.total,
                    'has_next': polls.has_next,
                    'has_prev': polls.has_prev
                }
            },
            message="Sondaggi recuperati con successo"
        )
        
    except Exception as e:
        current_app.logger.error(f"Errore nel recupero dei sondaggi: {str(e)}")
        return api_response(
                        message="Errore nel recupero dei sondaggi",
            status_code=500
        )


@communication_bp.route('/polls', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_CREATE_POLLS)
def create_poll():
    """Crea un nuovo sondaggio"""
    try:
        data = request.get_json()
        
        # Validazione
        if not data.get('title') or not data.get('options'):
            return api_response(
                                message="Titolo e opzioni sono obbligatori",
                status_code=400
            )
        
        if len(data['options']) < 2:
            return api_response(
                                message="Sono necessarie almeno 2 opzioni",
                status_code=400
            )
        
        # Validazione data di scadenza
        expires_at = None
        if data.get('expires_at'):
            try:
                expires_at = datetime.fromisoformat(data['expires_at'].replace('Z', '+00:00'))
                if expires_at <= datetime.utcnow():
                    return api_response(
                                                message="La data di scadenza deve essere futura",
                        status_code=400
                    )
            except ValueError:
                return api_response(
                                        message="Formato data non valido",
                    status_code=400
                )
        
        # Creazione sondaggio
        poll = Poll(
            title=data['title'].strip(),
            description=data.get('description', '').strip() or None,
            author_id=current_user.id,
            is_anonymous=data.get('is_anonymous', False),
            multiple_choice=data.get('multiple_choice', False),
            expires_at=expires_at
        )
        
        db.session.add(poll)
        db.session.flush()  # Per ottenere l'ID del poll
        
        # Creazione opzioni
        for i, option_text in enumerate(data['options']):
            if option_text.strip():
                option = PollOption(
                    poll_id=poll.id,
                    option_text=option_text.strip()
                )
                db.session.add(option)
        
        db.session.commit()
        
        return api_response(
            data=poll.to_dict(include_results=True),
            message="Sondaggio creato con successo",
            status_code=201
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Errore nella creazione del sondaggio: {str(e)}")
        return api_response(
                        message="Errore nella creazione del sondaggio",
            status_code=500
        )


@communication_bp.route('/polls/<int:poll_id>', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMMUNICATION)
def get_poll(poll_id):
    """Recupera un singolo sondaggio con tutti i dettagli"""
    try:
        poll = Poll.query.options(
            joinedload(Poll.author)
        ).get_or_404(poll_id)
        
        return api_response(
            data=poll.to_dict(include_results=True),
            message="Sondaggio recuperato con successo"
        )
        
    except Exception as e:
        current_app.logger.error(f"Errore nel recupero del sondaggio: {str(e)}")
        return api_response(
                        message="Errore nel recupero del sondaggio",
            status_code=500
        )


@communication_bp.route('/polls/<int:poll_id>/vote', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMMUNICATION)
def get_user_poll_vote(poll_id):
    """Recupera il voto dell'utente per un sondaggio"""
    try:
        poll = Poll.query.get_or_404(poll_id)
        
        vote = PollVote.query.filter_by(
            poll_id=poll_id,
            user_id=current_user.id
        ).first()
        
        if not vote:
            return api_response(
                data=None,
                message="Utente non ha votato"
            )
        
        return api_response(
            data=vote.to_dict(),
            message="Voto recuperato con successo"
        )
        
    except Exception as e:
        current_app.logger.error(f"Errore nel recupero del voto: {str(e)}")
        return api_response(
                        message="Errore nel recupero del voto",
            status_code=500
        )


@communication_bp.route('/polls/<int:poll_id>/vote', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMMUNICATION)
def vote_poll(poll_id):
    """Vota in un sondaggio"""
    try:
        poll = Poll.query.get_or_404(poll_id)
        
        if not poll.can_vote(current_user.id):
            return api_response(
                                message="Non è possibile votare in questo sondaggio",
                status_code=403
            )
        
        data = request.get_json()
        option_ids = data.get('option_ids', [])
        
        if not option_ids:
            return api_response(
                                message="Seleziona almeno un'opzione",
                status_code=400
            )
        
        # Verifica che le opzioni appartengano al sondaggio
        valid_options = PollOption.query.filter(
            PollOption.poll_id == poll_id,
            PollOption.id.in_(option_ids)
        ).all()
        
        if len(valid_options) != len(option_ids):
            return api_response(
                                message="Opzioni non valide",
                status_code=400
            )
        
        # Verifica scelta multipla
        if not poll.multiple_choice and len(option_ids) > 1:
            return api_response(
                                message="Questo sondaggio non permette scelte multiple",
                status_code=400
            )
        
        # Rimuovi voti precedenti se non è a scelta multipla
        if not poll.multiple_choice:
            PollVote.query.filter_by(poll_id=poll_id, user_id=current_user.id).delete()
        
        # Aggiungi nuovi voti
        for option_id in option_ids:
            # Verifica se il voto esiste già (per scelta multipla)
            existing_vote = PollVote.query.filter_by(
                poll_id=poll_id,
                user_id=current_user.id,
                option_id=option_id
            ).first()
            
            if not existing_vote:
                vote = PollVote(
                    poll_id=poll_id,
                    option_id=option_id,
                    user_id=current_user.id
                )
                db.session.add(vote)
        
        db.session.commit()
        
        return api_response(
            data=poll.to_dict(include_results=True),
            message="Voto registrato con successo"
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Errore nel voto del sondaggio: {str(e)}")
        return api_response(
                        message="Errore nel voto del sondaggio",
            status_code=500
        )


# ============================================================================
# DIRECT MESSAGES ENDPOINTS
# ============================================================================

@communication_bp.route('/messages', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMMUNICATION)
def get_messages():
    """Recupera i messaggi dell'utente corrente"""
    try:
        # Log solo se non è un polling request (per_page = 1)
        if request.args.get('per_page', 20, type=int) != 1:
            current_app.logger.info(f"[GET /messages] Request from user {current_user.id} ({current_user.email})")
        
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)
        message_type = request.args.get('type', 'all')  # 'sent', 'received', 'all'
        
        # Handle empty string as 'all'
        if not message_type or message_type.strip() == '':
            message_type = 'all'
        
        # Log parametri solo se non è un polling request
        if per_page != 1:
            current_app.logger.info(f"[GET /messages] Parameters - page: {page}, per_page: {per_page}, type: {message_type}")
        
        if message_type == 'sent':
            query = DirectMessage.query.filter_by(sender_id=current_user.id)
            if per_page != 1:
                current_app.logger.info(f"[GET /messages] Filtering for sent messages by user {current_user.id}")
        elif message_type == 'received':
            query = DirectMessage.query.filter_by(recipient_id=current_user.id)
            if per_page != 1:
                current_app.logger.info(f"[GET /messages] Filtering for received messages by user {current_user.id}")
        else:
            # Tutti i messaggi (inviati e ricevuti)
            query = DirectMessage.query.filter(
                or_(
                    DirectMessage.sender_id == current_user.id,
                    DirectMessage.recipient_id == current_user.id
                )
            )
            if per_page != 1:
                current_app.logger.info(f"[GET /messages] Filtering for all messages (sent/received) by user {current_user.id}")
        
        query = query.options(
            joinedload(DirectMessage.sender),
            joinedload(DirectMessage.recipient)
        ).order_by(desc(DirectMessage.created_at))
        
        # Count total messages before pagination
        total_count = query.count()
        if per_page != 1:
            current_app.logger.info(f"[GET /messages] Total messages found in database before pagination: {total_count}")
        
        messages = query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        if per_page != 1:
            current_app.logger.info(f"[GET /messages] Pagination results - total: {messages.total}, pages: {messages.pages}, current_page: {messages.page}, items_in_page: {len(messages.items)}")
            
            # Log details about returned messages
            for i, msg in enumerate(messages.items):
                current_app.logger.info(f"[GET /messages] Message {i+1}: ID={msg.id}, from={msg.sender_id}, to={msg.recipient_id}, created={msg.created_at}, content_preview='{msg.message[:50]}...'")
        
        response_data = {
            'messages': [message.to_dict() for message in messages.items],
            'pagination': {
                'page': messages.page,
                'pages': messages.pages,
                'per_page': messages.per_page,
                'total': messages.total,
                'has_next': messages.has_next,
                'has_prev': messages.has_prev
            }
        }
        
        current_app.logger.info(f"[GET /messages] Returning {len(response_data['messages'])} messages to frontend")
        
        return api_response(
            data=response_data,
            message="Messaggi recuperati con successo"
        )
        
    except Exception as e:
        current_app.logger.error(f"Errore nel recupero dei messaggi: {str(e)}")
        return api_response(
                        message="Errore nel recupero dei messaggi",
            status_code=500
        )


@communication_bp.route('/messages', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMMUNICATION)
def send_message():
    """Invia un messaggio diretto"""
    try:
        current_app.logger.info(f"[POST /messages] Request from user {current_user.id} ({current_user.email})")
        
        data = request.get_json()
        current_app.logger.info(f"[POST /messages] Received data: {data}")
        
        # Validazione (accetta sia 'message' che 'content' per compatibilità frontend)
        message_content = data.get('message') or data.get('content')
        recipient_id = data.get('recipient_id')
        
        current_app.logger.info(f"[POST /messages] Parsed - recipient_id: {recipient_id}, message_content length: {len(message_content) if message_content else 0}")
        
        if not recipient_id or not message_content:
            current_app.logger.warning(f"[POST /messages] Validation failed - recipient_id: {recipient_id}, message_content: {bool(message_content)}")
            return api_response(
                                message="Destinatario e messaggio sono obbligatori",
                status_code=400
            )
        
        # Verifica che il destinatario esista
        recipient = User.query.get(recipient_id)
        if not recipient:
            current_app.logger.warning(f"[POST /messages] Recipient not found: {recipient_id}")
            return api_response(
                                message="Destinatario non trovato",
                status_code=404
            )
        
        current_app.logger.info(f"[POST /messages] Recipient found: {recipient.id} ({recipient.email})")
        
        # Non permettere di inviare messaggi a se stessi
        if recipient.id == current_user.id:
            current_app.logger.warning(f"[POST /messages] Attempted to send message to self: {current_user.id}")
            return api_response(
                                message="Non puoi inviare messaggi a te stesso",
                status_code=400
            )
        
        current_app.logger.info(f"[POST /messages] Creating DirectMessage object...")
        message = DirectMessage(
            sender_id=current_user.id,
            recipient_id=recipient.id,
            message=message_content.strip()
        )
        
        current_app.logger.info(f"[POST /messages] DirectMessage created - sender: {message.sender_id}, recipient: {message.recipient_id}, content_preview: '{message.message[:50]}...'")
        
        current_app.logger.info(f"[POST /messages] Adding message to database session...")
        db.session.add(message)
        
        current_app.logger.info(f"[POST /messages] Committing to database...")
        db.session.commit()
        
        # Verify the message was saved by checking its ID
        current_app.logger.info(f"[POST /messages] Message committed successfully with ID: {message.id}")
        
        # Double-check by querying the database
        saved_message = DirectMessage.query.get(message.id)
        if saved_message:
            current_app.logger.info(f"[POST /messages] Verification: Message found in database with ID {saved_message.id}")
        else:
            current_app.logger.error(f"[POST /messages] ERROR: Message with ID {message.id} not found in database after commit!")
        
        # Count total messages in database for verification
        total_messages = DirectMessage.query.count()
        current_app.logger.info(f"[POST /messages] Total messages in database after save: {total_messages}")
        
        message_dict = message.to_dict()
        current_app.logger.info(f"[POST /messages] Returning message data: {message_dict}")
        
        return api_response(
            data=message_dict,
            message="Messaggio inviato con successo",
            status_code=201
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Errore nell'invio del messaggio: {str(e)}")
        return api_response(
                        message="Errore nell'invio del messaggio",
            status_code=500
        )


@communication_bp.route('/messages/<int:message_id>/read', methods=['PUT'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMMUNICATION)
def mark_message_read(message_id):
    """Segna un messaggio come letto"""
    try:
        message = DirectMessage.query.filter_by(
            id=message_id,
            recipient_id=current_user.id
        ).first_or_404()
        
        message.mark_as_read()
        
        return api_response(
            data=message.to_dict(),
            message="Messaggio segnato come letto"
        )
        
    except Exception as e:
        current_app.logger.error(f"Errore nel segnare il messaggio come letto: {str(e)}")
        return api_response(
                        message="Errore nel segnare il messaggio come letto",
            status_code=500
        )


@communication_bp.route('/messages/<int:message_id>', methods=['PUT'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMMUNICATION)
def update_message(message_id):
    """Modifica un messaggio diretto (solo il mittente può modificare)"""
    try:
        message = DirectMessage.query.filter_by(
            id=message_id,
            sender_id=current_user.id
        ).first_or_404()
        
        data = request.get_json()
        
        # Validazione
        if not data.get('message'):
            return api_response(
                                message="Il messaggio è obbligatorio",
                status_code=400
            )
        
        # Aggiornamento
        message.message = data['message'].strip()
        message.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        return api_response(
            data=message.to_dict(),
            message="Messaggio aggiornato con successo"
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Errore nell'aggiornamento del messaggio: {str(e)}")
        return api_response(
                        message="Errore nell'aggiornamento del messaggio",
            status_code=500
        )


@communication_bp.route('/messages/<int:message_id>', methods=['DELETE'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMMUNICATION)
def delete_message(message_id):
    """Elimina un messaggio diretto (solo il mittente o destinatario può eliminare)"""
    try:
        # L'utente può eliminare solo i messaggi che ha inviato o ricevuto
        message = DirectMessage.query.filter(
            DirectMessage.id == message_id,
            or_(
                DirectMessage.sender_id == current_user.id,
                DirectMessage.recipient_id == current_user.id
            )
        ).first_or_404()
        
        db.session.delete(message)
        db.session.commit()
        
        return api_response(
            message="Messaggio eliminato con successo"
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Errore nell'eliminazione del messaggio: {str(e)}")
        return api_response(
            message="Errore nell'eliminazione del messaggio",
            status_code=500
        )


# ============================================================================
# REACTIONS ENDPOINTS
# ============================================================================

@communication_bp.route('/reactions', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMMUNICATION)
def add_reaction():
    """Aggiunge una reazione a un contenuto"""
    try:
        data = request.get_json()
        
        # Validazione
        required_fields = ['content_type', 'content_id', 'reaction_type']
        if not all(field in data for field in required_fields):
            return api_response(
                                message="Campi obbligatori mancanti",
                status_code=400
            )
        
        valid_content_types = ['forum_topic', 'forum_comment', 'news', 'company_event']
        valid_reactions = ['like', 'love', 'laugh', 'angry', 'sad']
        
        if data['content_type'] not in valid_content_types:
            return api_response(
                                message="Tipo di contenuto non valido",
                status_code=400
            )
        
        if data['reaction_type'] not in valid_reactions:
            return api_response(
                                message="Tipo di reazione non valido",
                status_code=400
            )
        
        # Verifica se esiste già una reazione dell'utente per questo contenuto
        existing_reaction = CommunicationReaction.query.filter_by(
            user_id=current_user.id,
            target_type=data['content_type'],
            target_id=data['content_id']
        ).first()
        
        if existing_reaction:
            # Aggiorna la reazione esistente
            existing_reaction.reaction_type = data['reaction_type']
            reaction = existing_reaction
        else:
            # Crea una nuova reazione
            reaction = CommunicationReaction(
                user_id=current_user.id,
                target_type=data['content_type'],
                target_id=data['content_id'],
                reaction_type=data['reaction_type']
            )
            db.session.add(reaction)
        
        db.session.commit()
        
        return api_response(
            data=reaction.to_dict(),
            message="Reazione aggiunta con successo",
            status_code=201
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Errore nell'aggiunta della reazione: {str(e)}")
        return api_response(
                        message="Errore nell'aggiunta della reazione",
            status_code=500
        )


@communication_bp.route('/reactions/<int:reaction_id>', methods=['DELETE'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMMUNICATION)
def remove_reaction(reaction_id):
    """Rimuove una reazione"""
    try:
        reaction = CommunicationReaction.query.filter_by(
            id=reaction_id,
            user_id=current_user.id
        ).first_or_404()
        
        db.session.delete(reaction)
        db.session.commit()
        
        return api_response(
            message="Reazione rimossa con successo"
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Errore nella rimozione della reazione: {str(e)}")
        return api_response(
                        message="Errore nella rimozione della reazione",
            status_code=500
        )


# ============================================================================
# COMPANY EVENTS ENDPOINTS
# ============================================================================

@communication_bp.route('/events', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMMUNICATION)
def get_company_events():
    """Recupera gli eventi aziendali"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)
        upcoming_only = request.args.get('upcoming_only', 'false').lower() == 'true'
        
        query = CompanyEvent.query.options(joinedload(CompanyEvent.creator))
        
        if upcoming_only:
            query = query.filter(CompanyEvent.start_time >= datetime.utcnow())
        
        query = query.order_by(CompanyEvent.start_time)
        
        events = query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        return api_response(
            data={
                'events': [{
                    'id': event.id,
                    'title': event.title,
                    'description': event.description,
                    'start_time': event.start_time.isoformat(),
                    'end_time': event.end_time.isoformat(),
                    'location': event.location,
                    'event_type': event.event_type,
                    'creator': {
                        'id': event.creator.id,
                        'first_name': event.creator.first_name,
                        'last_name': event.creator.last_name
                    } if event.creator else None,
                    'is_company_wide': event.is_company_wide,
                    'max_participants': event.max_participants,
                    'registration_required': event.registration_required,
                    'registration_deadline': event.registration_deadline.isoformat() if event.registration_deadline else None,
                    'is_public': event.is_public,
                    'tags': event.tags,
                    'allow_comments': event.allow_comments,
                    'created_at': event.created_at.isoformat(),
                    'updated_at': event.updated_at.isoformat() if event.updated_at else None
                } for event in events.items],
                'pagination': {
                    'page': events.page,
                    'pages': events.pages,
                    'per_page': events.per_page,
                    'total': events.total,
                    'has_next': events.has_next,
                    'has_prev': events.has_prev
                }
            },
            message="Eventi recuperati con successo"
        )
        
    except Exception as e:
        current_app.logger.error(f"Errore nel recupero degli eventi: {str(e)}")
        return api_response(
                        message="Errore nel recupero degli eventi",
            status_code=500
        )


@communication_bp.route('/events', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_MANAGE_COMMUNICATION)
def create_company_event():
    """Crea un nuovo evento aziendale"""
    try:
        data = request.get_json()
        
        # Validazione
        required_fields = ['title', 'description', 'start_time', 'end_time']
        for field in required_fields:
            if not data.get(field):
                return api_response(
                                        message=f"Il campo {field} è obbligatorio",
                    status_code=400
                )
        
        # Parsing delle date
        try:
            start_time = datetime.fromisoformat(data['start_time'].replace('Z', '+00:00'))
            end_time = datetime.fromisoformat(data['end_time'].replace('Z', '+00:00'))
        except ValueError:
            return api_response(
                                message="Formato data non valido. Utilizzare ISO 8601",
                status_code=400
            )
        
        if start_time >= end_time:
            return api_response(
                                message="La data di inizio deve essere precedente alla data di fine",
                status_code=400
            )
        
        # Parsing della deadline di registrazione se presente
        registration_deadline = None
        if data.get('registration_deadline'):
            try:
                registration_deadline = datetime.fromisoformat(
                    data['registration_deadline'].replace('Z', '+00:00')
                )
            except ValueError:
                return api_response(
                                        message="Formato data deadline non valido",
                    status_code=400
                )
        
        # Creazione evento
        event = CompanyEvent(
            title=data['title'].strip(),
            description=data['description'].strip(),
            start_time=start_time,
            end_time=end_time,
            location=data.get('location', '').strip() or None,
            event_type=data.get('event_type', 'meeting'),
            created_by=current_user.id,
            is_company_wide=data.get('is_company_wide', True),
            max_participants=data.get('max_participants'),
            registration_required=data.get('registration_required', False),
            registration_deadline=registration_deadline,
            is_public=data.get('is_public', True),
            tags=data.get('tags', []),
            allow_comments=data.get('allow_comments', True)
        )
        
        db.session.add(event)
        db.session.commit()
        
        return api_response(
            data={
                'id': event.id,
                'title': event.title,
                'description': event.description,
                'start_time': event.start_time.isoformat(),
                'end_time': event.end_time.isoformat(),
                'location': event.location,
                'event_type': event.event_type,
                'creator': {
                    'id': current_user.id,
                    'first_name': current_user.first_name,
                    'last_name': current_user.last_name,
                    'full_name': current_user.full_name
                },
                'is_company_wide': event.is_company_wide,
                'max_participants': event.max_participants,
                'registration_required': event.registration_required,
                'registration_deadline': event.registration_deadline.isoformat() if event.registration_deadline else None,
                'is_public': event.is_public,
                'tags': event.tags,
                'allow_comments': event.allow_comments,
                'created_at': event.created_at.isoformat(),
                'updated_at': event.updated_at.isoformat() if event.updated_at else None
            },
            message="Evento creato con successo",
            status_code=201
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Errore nella creazione dell'evento: {str(e)}")
        return api_response(
                        message="Errore nella creazione dell'evento",
            status_code=500
        )


@communication_bp.route('/events/<int:event_id>', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMMUNICATION)
def get_company_event(event_id):
    """Recupera un evento aziendale specifico"""
    try:
        event = CompanyEvent.query.options(joinedload(CompanyEvent.creator)).get_or_404(event_id)
        
        # Conta le registrazioni se richieste
        registration_count = 0
        user_registered = False
        if event.registration_required:
            registration_count = CompanyEventRegistration.query.filter_by(
                event_id=event_id,
                status='registered'
            ).count()
            
            user_registered = CompanyEventRegistration.query.filter_by(
                event_id=event_id,
                user_id=current_user.id
            ).first() is not None
        
        return api_response(
            data={
                'id': event.id,
                'title': event.title,
                'description': event.description,
                'start_time': event.start_time.isoformat(),
                'end_time': event.end_time.isoformat(),
                'location': event.location,
                'event_type': event.event_type,
                'creator': {
                    'id': event.creator.id,
                    'first_name': event.creator.first_name,
                    'last_name': event.creator.last_name,
                    'full_name': event.creator.full_name
                } if event.creator else None,
                'is_company_wide': event.is_company_wide,
                'max_participants': event.max_participants,
                'registration_required': event.registration_required,
                'registration_deadline': event.registration_deadline.isoformat() if event.registration_deadline else None,
                'is_public': event.is_public,
                'tags': event.tags,
                'allow_comments': event.allow_comments,
                'registration_count': registration_count,
                'user_registered': user_registered,
                'created_at': event.created_at.isoformat(),
                'updated_at': event.updated_at.isoformat() if event.updated_at else None
            },
            message="Evento recuperato con successo"
        )
        
    except Exception as e:
        current_app.logger.error(f"Errore nel recupero dell'evento: {str(e)}")
        return api_response(
                        message="Errore nel recupero dell'evento",
            status_code=500
        )


@communication_bp.route('/events/<int:event_id>', methods=['PUT'])
@login_required
@api_permission_required(PERMISSION_MANAGE_COMMUNICATION)
def update_company_event(event_id):
    """Modifica un evento aziendale"""
    try:
        event = CompanyEvent.query.get_or_404(event_id)
        data = request.get_json()
        
        # Validazione
        if data.get('title'):
            event.title = data['title'].strip()
        if data.get('description'):
            event.description = data['description'].strip()
        
        # Aggiornamento date se fornite
        if data.get('start_time'):
            try:
                event.start_time = datetime.fromisoformat(data['start_time'].replace('Z', '+00:00'))
            except ValueError:
                return api_response(
                                        message="Formato data di inizio non valido",
                    status_code=400
                )
        
        if data.get('end_time'):
            try:
                event.end_time = datetime.fromisoformat(data['end_time'].replace('Z', '+00:00'))
            except ValueError:
                return api_response(
                                        message="Formato data di fine non valido",
                    status_code=400
                )
        
        if event.start_time >= event.end_time:
            return api_response(
                                message="La data di inizio deve essere precedente alla data di fine",
                status_code=400
            )
        
        # Aggiornamento altri campi
        if 'location' in data:
            event.location = data['location'].strip() or None
        if 'event_type' in data:
            event.event_type = data['event_type']
        if 'is_company_wide' in data:
            event.is_company_wide = data['is_company_wide']
        if 'max_participants' in data:
            event.max_participants = data['max_participants']
        if 'registration_required' in data:
            event.registration_required = data['registration_required']
        if 'registration_deadline' in data:
            if data['registration_deadline']:
                try:
                    event.registration_deadline = datetime.fromisoformat(
                        data['registration_deadline'].replace('Z', '+00:00')
                    )
                except ValueError:
                    return api_response(
                                                message="Formato data deadline non valido",
                        status_code=400
                    )
            else:
                event.registration_deadline = None
        if 'is_public' in data:
            event.is_public = data['is_public']
        if 'tags' in data:
            event.tags = data['tags']
        if 'allow_comments' in data:
            event.allow_comments = data['allow_comments']
        
        event.updated_at = datetime.utcnow()
        db.session.commit()
        
        return api_response(
            data={
                'id': event.id,
                'title': event.title,
                'description': event.description,
                'start_time': event.start_time.isoformat(),
                'end_time': event.end_time.isoformat(),
                'location': event.location,
                'event_type': event.event_type,
                'creator': {
                    'id': event.creator.id,
                    'first_name': event.creator.first_name,
                    'last_name': event.creator.last_name,
                    'full_name': event.creator.full_name
                } if event.creator else None,
                'is_company_wide': event.is_company_wide,
                'max_participants': event.max_participants,
                'registration_required': event.registration_required,
                'registration_deadline': event.registration_deadline.isoformat() if event.registration_deadline else None,
                'is_public': event.is_public,
                'tags': event.tags,
                'allow_comments': event.allow_comments,
                'created_at': event.created_at.isoformat(),
                'updated_at': event.updated_at.isoformat()
            },
            message="Evento aggiornato con successo"
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Errore nell'aggiornamento dell'evento: {str(e)}")
        return api_response(
                        message="Errore nell'aggiornamento dell'evento",
            status_code=500
        )


@communication_bp.route('/events/<int:event_id>', methods=['DELETE'])
@login_required
@api_permission_required(PERMISSION_MANAGE_COMMUNICATION)
def delete_company_event(event_id):
    """Elimina un evento aziendale"""
    try:
        event = CompanyEvent.query.get_or_404(event_id)
        
        # Elimina prima le registrazioni associate
        CompanyEventRegistration.query.filter_by(event_id=event_id).delete()
        
        db.session.delete(event)
        db.session.commit()
        
        return api_response(
            message="Evento eliminato con successo"
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Errore nell'eliminazione dell'evento: {str(e)}")
        return api_response(
                        message="Errore nell'eliminazione dell'evento",
            status_code=500
        )


@communication_bp.route('/events/<int:event_id>/register', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMMUNICATION)
def register_for_event(event_id):
    """Registra l'utente a un evento"""
    try:
        event = CompanyEvent.query.get_or_404(event_id)
        
        # Verifica se la registrazione è richiesta
        if not event.registration_required:
            return api_response(
                                message="Questo evento non richiede registrazione",
                status_code=400
            )
        
        # Verifica se la registrazione è ancora aperta
        if event.registration_deadline and datetime.utcnow() > event.registration_deadline:
            return api_response(
                                message="La registrazione per questo evento è scaduta",
                status_code=400
            )
        
        # Verifica se l'utente è già registrato
        existing_registration = CompanyEventRegistration.query.filter_by(
            event_id=event_id,
            user_id=current_user.id
        ).first()
        
        if existing_registration:
            # For testing: allow re-registration by updating existing record
            existing_registration.status = 'registered'
            existing_registration.notes = data.get('notes', '').strip() or None
            existing_registration.registered_at = datetime.utcnow()
            
            db.session.commit()
            
            response_data = {
                'id': existing_registration.id,
                'event_id': existing_registration.event_id,
                'user_id': existing_registration.user_id,
                'status': existing_registration.status,
                'notes': existing_registration.notes,
                'registered_at': existing_registration.registered_at.isoformat()
            }
            
            return api_response(
                data=response_data,
                message="Registrazione aggiornata con successo",
                status_code=200
            )
        
        # Verifica il limite di partecipanti
        if event.max_participants:
            current_registrations = CompanyEventRegistration.query.filter_by(
                event_id=event_id,
                status='registered'
            ).count()
            
            if current_registrations >= event.max_participants:
                return api_response(
                                        message="Evento al completo",
                    status_code=400
                )
        
        data = request.get_json() or {}
        
        registration = CompanyEventRegistration(
            event_id=event_id,
            user_id=current_user.id,
            notes=data.get('notes', '').strip() or None
        )
        
        db.session.add(registration)
        db.session.commit()
        
        # Create a safe response without calling to_dict() to avoid potential errors
        response_data = {
            'id': registration.id,
            'event_id': registration.event_id,
            'user_id': registration.user_id,
            'status': registration.status,
            'notes': registration.notes,
            'registered_at': registration.registered_at.isoformat() if registration.registered_at else None
        }
        
        return api_response(
            data=response_data,
            message="Registrazione completata con successo",
            status_code=201
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Errore nella registrazione all'evento: {str(e)}")
        return api_response(
                        message="Errore nella registrazione all'evento",
            status_code=500
        )


@communication_bp.route('/events/<int:event_id>/register', methods=['DELETE'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMMUNICATION)
def unregister_from_event(event_id):
    """Annulla la registrazione dell'utente da un evento"""
    try:
        event = CompanyEvent.query.get_or_404(event_id)
        
        # Trova la registrazione esistente
        registration = CompanyEventRegistration.query.filter_by(
            event_id=event_id,
            user_id=current_user.id
        ).first()
        
        if not registration:
            return api_response(
                                message="Non sei registrato a questo evento",
                status_code=400
            )
        
        # Verifica se è possibile cancellare la registrazione
        if event.registration_deadline and datetime.utcnow() > event.registration_deadline:
            return api_response(
                                message="Non è più possibile cancellare la registrazione",
                status_code=400
            )
        
        db.session.delete(registration)
        db.session.commit()
        
        return api_response(
            message="Registrazione cancellata con successo"
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Errore nella cancellazione della registrazione: {str(e)}")
        return api_response(
                        message="Errore nella cancellazione della registrazione",
            status_code=500
        )


# ============================================================================
# UTILITY ENDPOINTS
# ============================================================================

@communication_bp.route('/stats', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMMUNICATION)
def get_communication_stats():
    """Recupera statistiche del modulo comunicazione"""
    try:
        # Statistiche forum
        total_topics = ForumTopic.query.count()
        total_comments = ForumComment.query.count()
        my_topics = ForumTopic.query.filter_by(author_id=current_user.id).count()
        
        # Statistiche sondaggi
        active_polls = Poll.query.filter(
            and_(
                Poll.is_active == True,
                or_(Poll.expires_at.is_(None), Poll.expires_at > datetime.utcnow())
            )
        ).count()
        
        # Statistiche messaggi
        unread_messages = DirectMessage.query.filter_by(
            recipient_id=current_user.id,
            is_read=False
        ).count()
        
        # Statistiche eventi
        upcoming_events = CompanyEvent.query.filter(
            CompanyEvent.start_time >= datetime.utcnow()
        ).count()
        
        stats_data = {
            'forum': {
                'total_topics': total_topics,
                'total_comments': total_comments,
                'my_topics': my_topics
            },
            'polls': {
                'active_polls': active_polls
            },
            'messages': {
                'unread_messages': unread_messages
            },
            'events': {
                'upcoming_events': upcoming_events
            }
        }
        
        return api_response(
            data=stats_data,
            message="Statistiche recuperate con successo"
        )
        
    except Exception as e:
        current_app.logger.error(f"Errore nel recupero delle statistiche: {str(e)}")
        return api_response(
                        message="Errore nel recupero delle statistiche",
            status_code=500
        )


# ============================================================================
# ADDITIONAL CRUD ENDPOINTS
# ============================================================================

@communication_bp.route('/forum/topics/<int:topic_id>', methods=['PUT'])
@login_required
def update_forum_topic(topic_id):
    """Aggiorna un topic del forum"""
    try:
        topic = ForumTopic.query.get_or_404(topic_id)
        data = request.get_json()
        
        current_app.logger.info(f"Update topic request - User: {current_user.id} ({current_user.role}), Topic author: {topic.author_id}")
        current_app.logger.info(f"Data received: {data}")
        
        # Verifica permessi (admin, autore o moderatore)
        is_admin = current_user.role == 'admin'
        is_author = topic.author_id == current_user.id
        is_moderator = hasattr(current_user, 'has_permission') and current_user.has_permission(PERMISSION_MODERATE_FORUM)
        
        if not (is_admin or is_author or is_moderator):
            current_app.logger.warning(f"Permission denied - Admin: {is_admin}, Author: {is_author}, Moderator: {is_moderator}")
            return api_response(
                message="Non hai i permessi per modificare questo topic",
                status_code=403
            )
        
        # Validazione (accept both 'content' and 'description' for backward compatibility)
        title = data.get('title', '').strip()
        description = (data.get('content') or data.get('description', '')).strip()
        
        current_app.logger.info(f"Validation - Title: '{title}' ({len(title)} chars), Description: '{description[:50]}...' ({len(description)} chars)")
        
        if not title or not description:
            return api_response(
                message="Titolo e contenuto sono obbligatori",
                status_code=400
            )
        
        # Aggiornamento - solo campi che esistono nel modello
        topic.title = title
        topic.description = description
        topic.category = data.get('category', topic.category)
        
        # Campi opzionali per moderatori/admin
        if 'is_pinned' in data:
            topic.is_pinned = data.get('is_pinned', False)
        if 'is_locked' in data:
            topic.is_locked = data.get('is_locked', False)
            
        topic.updated_at = datetime.utcnow()
        
        current_app.logger.info(f"Topic updated successfully: {topic.id}")
        
        db.session.commit()
        
        return api_response(
            data=topic.to_dict(),
            message="Topic aggiornato con successo"
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Errore nell'aggiornamento del topic: {str(e)}")
        return api_response(
            message="Errore nell'aggiornamento del topic",
            status_code=500
        )


@communication_bp.route('/forum/topics/<int:topic_id>', methods=['DELETE'])
@login_required
def delete_forum_topic(topic_id):
    """Elimina un topic del forum"""
    try:
        topic = ForumTopic.query.get_or_404(topic_id)
        
        # Verifica permessi (admin, autore o moderatore)
        is_admin = current_user.role == 'admin'
        is_author = topic.author_id == current_user.id
        is_moderator = hasattr(current_user, 'has_permission') and current_user.has_permission(PERMISSION_MODERATE_FORUM)
        
        if not (is_admin or is_author or is_moderator):
            return api_response(
                message="Non hai i permessi per eliminare questo topic",
                status_code=403
            )
        
        # Elimina commenti associati
        ForumComment.query.filter_by(topic_id=topic_id).delete()
        
        # Elimina topic
        db.session.delete(topic)
        db.session.commit()
        
        return api_response(
            message="Topic eliminato con successo"
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Errore nell'eliminazione del topic: {str(e)}")
        return api_response(
            message="Errore nell'eliminazione del topic",
            status_code=500
        )


@communication_bp.route('/polls/<int:poll_id>', methods=['PUT'])
@login_required
@api_permission_required(PERMISSION_CREATE_POLLS)
def update_poll(poll_id):
    """Aggiorna un sondaggio"""
    try:
        poll = Poll.query.get_or_404(poll_id)
        data = request.get_json()
        
        # Verifica permessi (solo autore)
        if poll.author_id != current_user.id:
            return api_response(
                message="Non hai i permessi per modificare questo sondaggio",
                status_code=403
            )
        
        # Non permettere modifiche se ci sono già voti
        if PollVote.query.filter_by(poll_id=poll_id).count() > 0:
            return api_response(
                message="Non è possibile modificare un sondaggio che ha già ricevuto voti",
                status_code=400
            )
        
        # Validazione
        if not data.get('question'):
            return api_response(
                message="La domanda del sondaggio è obbligatoria",
                status_code=400
            )
        
        # Aggiornamento
        poll.question = data['question'].strip()
        poll.description = data.get('description', '').strip()
        poll.is_anonymous = data.get('is_anonymous', poll.is_anonymous)
        poll.allows_multiple_choice = data.get('allows_multiple_choice', poll.allows_multiple_choice)
        poll.expires_at = data.get('expires_at')
        poll.updated_at = datetime.utcnow()
        
        # Aggiorna opzioni se fornite
        if 'options' in data:
            # Elimina opzioni esistenti
            PollOption.query.filter_by(poll_id=poll_id).delete()
            
            # Aggiungi nuove opzioni
            for option_text in data['options']:
                if option_text.strip():
                    option = PollOption(
                        poll_id=poll.id,
                        option_text=option_text.strip()
                    )
                    db.session.add(option)
        
        db.session.commit()
        
        return api_response(
            data=poll.to_dict(include_results=True),
            message="Sondaggio aggiornato con successo"
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Errore nell'aggiornamento del sondaggio: {str(e)}")
        return api_response(
            message="Errore nell'aggiornamento del sondaggio",
            status_code=500
        )


@communication_bp.route('/polls/<int:poll_id>', methods=['DELETE'])
@login_required
@api_permission_required(PERMISSION_CREATE_POLLS)
def delete_poll(poll_id):
    """Elimina un sondaggio"""
    try:
        poll = Poll.query.get_or_404(poll_id)
        
        # Verifica permessi (solo autore o admin)
        if poll.author_id != current_user.id and not current_user.has_permission(PERMISSION_MANAGE_COMMUNICATION):
            return api_response(
                message="Non hai i permessi per eliminare questo sondaggio",
                status_code=403
            )
        
        # Le relazioni vengono eliminate automaticamente con cascade='all, delete-orphan'
        db.session.delete(poll)
        db.session.commit()
        
        return api_response(
            message="Sondaggio eliminato con successo"
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Errore nell'eliminazione del sondaggio: {str(e)}")
        return api_response(
            message="Errore nell'eliminazione del sondaggio",
            status_code=500
        )


# ============================================================================
# HR ASSISTANT CHATBOT ENDPOINTS
# ============================================================================

@communication_bp.route('/hr-assistant/chat', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMMUNICATION)
def hr_assistant_chat():
    """Endpoint per interazione chatbot HR"""
    try:
        data = request.get_json()
        user_message = data.get('message', '').strip()
        session_id = data.get('session_id')
        
        if not user_message:
            return api_response(
                message="Messaggio richiesto",
                status_code=400
            )
        
        # Process with HR AI Service
        import asyncio
        from services.hr_ai_service import process_hr_query
        
        # Run async function in sync context 
        try:
            # Get existing event loop if available
            try:
                loop = asyncio.get_event_loop()
                if loop.is_closed():
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
            response = loop.run_until_complete(process_hr_query(
                user_message=user_message,
                user_id=current_user.id,
                session_id=session_id
            ))
        except Exception as async_error:
            current_app.logger.error(f"Async processing error: {str(async_error)}")
            # Fallback response se l'AI non funziona
            response = {
                'response': f"""🤖 **Assistente HR**

Ho ricevuto la tua domanda: "{user_message}"

Mi dispiace, al momento sto riscontrando problemi tecnici nel processare richieste complesse. 

**Ti suggerisco di:**
• Contattare direttamente l'ufficio HR per assistenza
• Consultare la knowledge base aziendale
• Riprovare più tardi

**Contatti HR:**
📧 <EMAIL>""",
                'category': 'general',
                'confidence': 'low',
                'conversation_id': None
            }
        
        return api_response(
            data=response,
            message="Risposta HR assistant generata con successo"
        )
        
    except Exception as e:
        current_app.logger.error(f"Errore HR assistant chat: {str(e)}")
        return api_response(
            message="Errore nell'elaborazione della richiesta",
            status_code=500
        )

@communication_bp.route('/hr-assistant/knowledge-base', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMMUNICATION)
def get_hr_knowledge_base():
    """Recupera knowledge base HR con filtri e paginazione"""
    try:
        from models import HRKnowledgeBase
        
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)
        category = request.args.get('category')
        search = request.args.get('search')
        
        # Base query
        query = HRKnowledgeBase.query.filter(HRKnowledgeBase.is_active == True)
        
        # Filtri
        if category:
            query = query.filter(HRKnowledgeBase.category == category)
            
        if search:
            search_term = f"%{search}%"
            query = query.filter(
                or_(
                    HRKnowledgeBase.title.ilike(search_term),
                    HRKnowledgeBase.content.ilike(search_term),
                    HRKnowledgeBase.tags.ilike(search_term)
                )
            )
        
        # Ordinamento per rilevanza e data
        query = query.order_by(desc(HRKnowledgeBase.created_at))
        
        # Paginazione
        kb_entries = query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        return api_response(
            data={
                'entries': [entry.to_dict() for entry in kb_entries.items],
                'pagination': {
                    'page': kb_entries.page,
                    'pages': kb_entries.pages,
                    'per_page': kb_entries.per_page,
                    'total': kb_entries.total,
                    'has_next': kb_entries.has_next,
                    'has_prev': kb_entries.has_prev
                }
            },
            message="Knowledge base HR recuperata con successo"
        )
        
    except Exception as e:
        current_app.logger.error(f"Errore recupero knowledge base HR: {str(e)}")
        return api_response(
            message="Errore nel recupero della knowledge base",
            status_code=500
        )

@communication_bp.route('/hr-assistant/knowledge-base', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_MANAGE_COMMUNICATION)
def create_hr_knowledge_entry():
    """Crea nuova entry knowledge base (con supporto AI)"""
    try:
        from models import HRKnowledgeBase
        import json
        
        data = request.get_json()
        
        # Validazione campi richiesti
        required_fields = ['title', 'category']
        for field in required_fields:
            if not data.get(field):
                return api_response(
                    message=f"Campo {field} richiesto",
                    status_code=400
                )
        
        # Content è richiesto solo se non si usa AI assistance
        if not data.get('use_ai_assistance') and not data.get('content'):
            return api_response(
                message="Campo content richiesto",
                status_code=400
            )
        
        # Se richiesta creazione assistita da AI
        if data.get('use_ai_assistance'):
            import asyncio
            from services.hr_ai_service import generate_hr_content
            
            # Run async function in sync context
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                ai_result = loop.run_until_complete(generate_hr_content(
                    category=data['category'],
                    topic=data['title'],
                    requirements=data.get('requirements', ''),
                    template_id=data.get('template_id')
                ))
            finally:
                loop.close()
            
            # Merge AI-generated content
            data['content'] = ai_result.get('content', data['content'])
            data['ai_sources'] = json.dumps(ai_result.get('sources', []))
            data['ai_confidence'] = ai_result.get('confidence', 'medium')
            data['created_with_ai'] = True
        
        # Crea entry
        entry = HRKnowledgeBase(
            title=data['title'],
            content=data['content'],
            category=data['category'],
            tags=json.dumps(data.get('tags', [])),
            created_with_ai=data.get('created_with_ai', False),
            ai_sources=data.get('ai_sources'),
            ai_confidence=data.get('ai_confidence'),
            created_by=current_user.id
        )
        
        db.session.add(entry)
        db.session.commit()
        
        return api_response(
            data=entry.to_dict(),
            message="Entry knowledge base creata con successo",
            status_code=201
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Errore creazione entry KB: {str(e)}")
        return api_response(
            message="Errore nella creazione dell'entry",
            status_code=500
        )

@communication_bp.route('/hr-assistant/knowledge-base/<int:entry_id>', methods=['PUT'])
@login_required
@api_permission_required(PERMISSION_MANAGE_COMMUNICATION)
def update_hr_knowledge_entry(entry_id):
    """Aggiorna entry knowledge base"""
    try:
        from models import HRKnowledgeBase
        import json
        
        entry = HRKnowledgeBase.query.get_or_404(entry_id)
        data = request.get_json()
        
        # Aggiorna campi
        if 'title' in data:
            entry.title = data['title']
        if 'content' in data:
            entry.content = data['content']
        if 'category' in data:
            entry.category = data['category']
        if 'tags' in data:
            entry.tags = json.dumps(data['tags'])
        if 'is_active' in data:
            entry.is_active = data['is_active']
        
        entry.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        return api_response(
            data=entry.to_dict(),
            message="Entry knowledge base aggiornata con successo"
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Errore aggiornamento entry KB: {str(e)}")
        return api_response(
            message="Errore nell'aggiornamento dell'entry",
            status_code=500
        )

@communication_bp.route('/hr-assistant/knowledge-base/<int:entry_id>', methods=['DELETE'])
@login_required
@api_permission_required(PERMISSION_MANAGE_COMMUNICATION)
def delete_hr_knowledge_entry(entry_id):
    """Elimina entry knowledge base (soft delete)"""
    try:
        from models import HRKnowledgeBase
        
        entry = HRKnowledgeBase.query.get_or_404(entry_id)
        
        # Soft delete
        entry.is_active = False
        db.session.commit()
        
        return api_response(
            message="Entry knowledge base eliminata con successo"
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Errore eliminazione entry KB: {str(e)}")
        return api_response(
            message="Errore nell'eliminazione dell'entry",
            status_code=500
        )

@communication_bp.route('/hr-assistant/analytics', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_MANAGE_COMMUNICATION)
def get_hr_assistant_analytics():
    """Analytics utilizzo HR assistant per amministratori"""
    try:
        from models import HRChatConversation, HRKnowledgeBase
        from sqlalchemy import func
        
        # Periodo di analisi
        days = request.args.get('days', 30, type=int)
        start_date = datetime.utcnow() - timedelta(days=days)
        
        # Query base conversazioni
        conversations_query = HRChatConversation.query.filter(
            HRChatConversation.created_at >= start_date
        )
        
        # Metriche generali
        total_conversations = conversations_query.count()
        unique_users = conversations_query.distinct(HRChatConversation.user_id).count()
        
        # Categorie più richieste
        category_stats = db.session.query(
            HRChatConversation.category_detected,
            func.count(HRChatConversation.id).label('count')
        ).filter(
            HRChatConversation.created_at >= start_date,
            HRChatConversation.category_detected.isnot(None)
        ).group_by(HRChatConversation.category_detected).all()
        
        # Feedback utenti
        feedback_stats = db.session.query(
            HRChatConversation.user_feedback,
            func.count(HRChatConversation.id).label('count')
        ).filter(
            HRChatConversation.created_at >= start_date,
            HRChatConversation.user_feedback.isnot(None)
        ).group_by(HRChatConversation.user_feedback).all()
        
        # Tempo di risposta medio
        avg_response_time = db.session.query(
            func.avg(HRChatConversation.response_time_ms)
        ).filter(
            HRChatConversation.created_at >= start_date,
            HRChatConversation.response_time_ms.isnot(None)
        ).scalar()
        
        return api_response(
            data={
                'period_days': days,
                'total_conversations': total_conversations,
                'unique_users': unique_users,
                'avg_response_time_ms': int(avg_response_time) if avg_response_time else 0,
                'category_distribution': [
                    {'category': cat, 'count': count} 
                    for cat, count in category_stats
                ],
                'feedback_distribution': [
                    {'feedback': feedback, 'count': count} 
                    for feedback, count in feedback_stats
                ],
                'knowledge_base_stats': {
                    'total_entries': HRKnowledgeBase.query.filter(
                        HRKnowledgeBase.is_active == True
                    ).count(),
                    'ai_generated_entries': HRKnowledgeBase.query.filter(
                        HRKnowledgeBase.is_active == True,
                        HRKnowledgeBase.created_with_ai == True
                    ).count()
                }
            },
            message="Analytics HR assistant recuperate con successo"
        )
        
    except Exception as e:
        current_app.logger.error(f"Errore analytics HR assistant: {str(e)}")
        return api_response(
            message="Errore nel recupero delle analytics",
            status_code=500
        )

@communication_bp.route('/hr-assistant/feedback', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMMUNICATION)
def hr_assistant_feedback():
    """Raccoglie feedback utente su risposta HR assistant"""
    try:
        from models import HRChatConversation
        
        data = request.get_json()
        conversation_id = data.get('conversation_id')
        feedback = data.get('feedback')  # 'helpful', 'not_helpful', 'neutral'
        
        if not conversation_id or not feedback:
            return api_response(
                message="conversation_id e feedback richiesti",
                status_code=400
            )
        
        conversation = HRChatConversation.query.get_or_404(conversation_id)
        
        # Verifica che l'utente possa dare feedback su questa conversazione
        if conversation.user_id != current_user.id:
            return api_response(
                message="Non puoi dare feedback su questa conversazione",
                status_code=403
            )
        
        conversation.user_feedback = feedback
        db.session.commit()
        
        return api_response(
            message="Feedback registrato con successo"
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Errore registrazione feedback: {str(e)}")
        return api_response(
            message="Errore nella registrazione del feedback",
            status_code=500
        )

@communication_bp.route('/hr-assistant/templates', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMMUNICATION)
def get_hr_content_templates():
    """Recupera template per content creation"""
    try:
        from models import HRContentTemplate
        
        category = request.args.get('category')
        
        query = HRContentTemplate.query.filter(HRContentTemplate.is_active == True)
        
        if category:
            query = query.filter(HRContentTemplate.category == category)
        
        templates = query.order_by(HRContentTemplate.usage_count.desc()).all()
        
        return api_response(
            data=[template.to_dict() for template in templates],
            message="Template recuperati con successo"
        )
        
    except Exception as e:
        current_app.logger.error(f"Errore recupero template: {str(e)}")
        return api_response(
            message="Errore nel recupero dei template",
            status_code=500
        )