# HR Assistant Chatbot - DatPortal Implementation Plan

## Executive Summary

Il **Sistema di Assistenza HR con AI** per DatPortal implementa un chatbot intelligente nell'area comunicazioni per supportare i dipendenti con informazioni contrattuali, procedure HR e benefit aziendali. Include gestione knowledge base con creazione contenuti assistita da AI per amministratori.

### Obiettivi Strategici
- **Knowledge Management**: Base di conoscenza centralizzata per info HR in database
- **AI-Powered Support**: Chatbot intelligente per assistenza dipendenti 24/7
- **Content Creation**: Strumenti AI per amministratori (Sonar Pro + OpenAI)
- **Pattern Consistency**: Integrazione seamless con modulo comunicazioni esistente
- **Italian PMI Focus**: Contenuti specifici per normative e procedure italiane

## DatPortal Architecture Alignment

### Backend Pattern Integration
- **Flask Blueprints**: Estensione `backend/blueprints/api/communication.py` esistente
- **API Response Standard**: `api_response()` utility per consistency
- **Permission System**: <PERSON><PERSON><PERSON><PERSON><PERSON> `PERMISSION_MANAGE_COMMUNICATION` e `PERMISSION_VIEW_COMMUNICATION`
- **Models Organization**: Nuovi modelli in `backend/models_split/hr_assistant.py`
- **Services Layer**: `backend/services/hr_ai_service.py` seguendo pattern `ceo_ai_service.py`

### Frontend Pattern Integration
- **Vue 3 + Composition API**: Integrazione in `views/communications/` esistente
- **Pinia State Management**: Store `stores/hrAssistant.js` seguendo pattern comunicazioni
- **HeroIcon System**: Icone standard (`chat-bubble-left-right`, `academic-cap`, `document-text`)
- **Design System**: TailwindCSS brand colors e componenti riutilizzabili
- **Sidebar Integration**: Aggiunta in sezione Comunicazioni esistente

### AI Services Integration
- **OpenAI Integration**: Riutilizzo pattern da `ceo_ai_service.py`
- **Sonar Pro Research**: Web search per content creation amministratori
- **Async Processing**: Pattern asincrono per performance ottimali

## Technical Architecture - DatPortal Integration

### Database Models (`backend/models_split/hr_assistant.py`)

```python
from extensions import db
from datetime import datetime
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, ForeignKey, Index
from sqlalchemy.orm import relationship

class HRKnowledgeBase(db.Model):
    """Knowledge base HR per chatbot assistant"""
    __tablename__ = 'hr_knowledge_base'
    
    id = Column(Integer, primary_key=True)
    title = Column(String(255), nullable=False)
    content = Column(Text, nullable=False)
    category = Column(String(100), nullable=False)  # contracts, onboarding, offboarding, leave, permits, travel, benefits
    tags = Column(Text)  # JSON array of tags
    is_active = Column(Boolean, default=True)
    
    # AI-assisted content creation
    created_with_ai = Column(Boolean, default=False)
    ai_sources = Column(Text)  # JSON array of web sources used
    ai_confidence = Column(String(20))  # high, medium, low
    
    # Standard fields
    created_by = Column(Integer, ForeignKey('users.id'), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    creator = relationship('User', backref='hr_knowledge_entries')
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_hr_kb_category', 'category'),
        Index('idx_hr_kb_active', 'is_active'),
        Index('idx_hr_kb_created_at', 'created_at'),
    )

class HRChatConversation(db.Model):
    """Conversazioni chatbot HR per tracking e analytics"""
    __tablename__ = 'hr_chat_conversations'
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    session_id = Column(String(100), nullable=False)  # Frontend session tracking
    
    # Conversation data
    user_message = Column(Text, nullable=False)
    bot_response = Column(Text, nullable=False)
    category_detected = Column(String(100))  # Auto-detected category
    confidence_score = Column(String(20))  # AI confidence in response
    
    # Knowledge base references used
    kb_entries_used = Column(Text)  # JSON array of KB entry IDs
    
    # Analytics fields
    response_time_ms = Column(Integer)  # Response time in milliseconds
    user_feedback = Column(String(20))  # helpful, not_helpful, neutral
    
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    user = relationship('User', backref='hr_chat_history')
    
    # Indexes
    __table_args__ = (
        Index('idx_hr_chat_user', 'user_id'),
        Index('idx_hr_chat_session', 'session_id'),
        Index('idx_hr_chat_category', 'category_detected'),
        Index('idx_hr_chat_created', 'created_at'),
    )

class HRContentTemplate(db.Model):
    """Template pre-configurati per creazione contenuti HR"""
    __tablename__ = 'hr_content_templates'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(255), nullable=False)
    category = Column(String(100), nullable=False)
    description = Column(Text)
    
    # Template structure
    prompt_template = Column(Text, nullable=False)  # Template for AI content generation
    required_fields = Column(Text)  # JSON array of required input fields
    output_format = Column(Text)  # Expected output structure
    
    # Usage tracking
    usage_count = Column(Integer, default=0)
    last_used = Column(DateTime)
    
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    __table_args__ = (
        Index('idx_hr_template_category', 'category'),
        Index('idx_hr_template_active', 'is_active'),
    )
```

### Backend API Extension (`backend/blueprints/api/communication.py`)

```python
# Aggiunte al blueprint comunicazioni esistente

# ============================================================================
# HR ASSISTANT CHATBOT ENDPOINTS
# ============================================================================

@communication_bp.route('/hr-assistant/chat', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMMUNICATION)
def hr_assistant_chat():
    """Endpoint per interazione chatbot HR"""
    try:
        data = request.get_json()
        user_message = data.get('message', '').strip()
        session_id = data.get('session_id')
        
        if not user_message:
            return api_response(
                success=False,
                message="Messaggio richiesto",
                status_code=400
            )
        
        # Process with HR AI Service
        from services.hr_ai_service import process_hr_query
        response = await process_hr_query(
            user_message=user_message,
            user_id=current_user.id,
            session_id=session_id
        )
        
        return api_response(
            data=response,
            message="Risposta HR assistant generata con successo"
        )
        
    except Exception as e:
        current_app.logger.error(f"Errore HR assistant chat: {str(e)}")
        return api_response(
            success=False,
            message="Errore nell'elaborazione della richiesta",
            status_code=500
        )

@communication_bp.route('/hr-assistant/knowledge-base', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMMUNICATION)
def get_hr_knowledge_base():
    """Recupera knowledge base HR con filtri e paginazione"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)
        category = request.args.get('category')
        search = request.args.get('search')
        
        # Base query
        query = HRKnowledgeBase.query.filter(HRKnowledgeBase.is_active == True)
        
        # Filtri
        if category:
            query = query.filter(HRKnowledgeBase.category == category)
            
        if search:
            search_term = f"%{search}%"
            query = query.filter(
                or_(
                    HRKnowledgeBase.title.ilike(search_term),
                    HRKnowledgeBase.content.ilike(search_term),
                    HRKnowledgeBase.tags.ilike(search_term)
                )
            )
        
        # Ordinamento per rilevanza e data
        query = query.order_by(desc(HRKnowledgeBase.created_at))
        
        # Paginazione
        kb_entries = query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        return api_response(
            data={
                'entries': [{
                    'id': entry.id,
                    'title': entry.title,
                    'content': entry.content,
                    'category': entry.category,
                    'tags': json.loads(entry.tags) if entry.tags else [],
                    'created_with_ai': entry.created_with_ai,
                    'ai_confidence': entry.ai_confidence,
                    'created_at': entry.created_at.isoformat(),
                    'creator': {
                        'id': entry.creator.id,
                        'full_name': entry.creator.full_name
                    } if entry.creator else None
                } for entry in kb_entries.items],
                'pagination': {
                    'page': kb_entries.page,
                    'pages': kb_entries.pages,
                    'per_page': kb_entries.per_page,
                    'total': kb_entries.total,
                    'has_next': kb_entries.has_next,
                    'has_prev': kb_entries.has_prev
                }
            },
            message="Knowledge base HR recuperata con successo"
        )
        
    except Exception as e:
        current_app.logger.error(f"Errore recupero knowledge base HR: {str(e)}")
        return api_response(
            success=False,
            message="Errore nel recupero della knowledge base",
            status_code=500
        )

@communication_bp.route('/hr-assistant/knowledge-base', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_MANAGE_COMMUNICATION)
def create_hr_knowledge_entry():
    """Crea nuova entry knowledge base (con supporto AI)"""
    try:
        data = request.get_json()
        
        # Validazione campi richiesti
        required_fields = ['title', 'content', 'category']
        for field in required_fields:
            if not data.get(field):
                return api_response(
                    success=False,
                    message=f"Campo {field} richiesto",
                    status_code=400
                )
        
        # Se richiesta creazione assistita da AI
        if data.get('use_ai_assistance'):
            from services.hr_ai_service import generate_hr_content
            ai_result = await generate_hr_content(
                category=data['category'],
                topic=data['title'],
                requirements=data.get('requirements', ''),
                template_id=data.get('template_id')
            )
            
            # Merge AI-generated content
            data['content'] = ai_result.get('content', data['content'])
            data['ai_sources'] = json.dumps(ai_result.get('sources', []))
            data['ai_confidence'] = ai_result.get('confidence', 'medium')
            data['created_with_ai'] = True
        
        # Crea entry
        entry = HRKnowledgeBase(
            title=data['title'],
            content=data['content'],
            category=data['category'],
            tags=json.dumps(data.get('tags', [])),
            created_with_ai=data.get('created_with_ai', False),
            ai_sources=data.get('ai_sources'),
            ai_confidence=data.get('ai_confidence'),
            created_by=current_user.id
        )
        
        db.session.add(entry)
        db.session.commit()
        
        return api_response(
            data={
                'id': entry.id,
                'title': entry.title,
                'category': entry.category,
                'created_with_ai': entry.created_with_ai
            },
            message="Entry knowledge base creata con successo",
            status_code=201
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Errore creazione entry KB: {str(e)}")
        return api_response(
            success=False,
            message="Errore nella creazione dell'entry",
            status_code=500
        )

@communication_bp.route('/hr-assistant/analytics', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_MANAGE_COMMUNICATION)
def get_hr_assistant_analytics():
    """Analytics utilizzo HR assistant per amministratori"""
    try:
        # Periodo di analisi
        days = request.args.get('days', 30, type=int)
        start_date = datetime.utcnow() - timedelta(days=days)
        
        # Query base conversazioni
        conversations_query = HRChatConversation.query.filter(
            HRChatConversation.created_at >= start_date
        )
        
        # Metriche generali
        total_conversations = conversations_query.count()
        unique_users = conversations_query.distinct(HRChatConversation.user_id).count()
        
        # Categorie più richieste
        from sqlalchemy import func
        category_stats = db.session.query(
            HRChatConversation.category_detected,
            func.count(HRChatConversation.id).label('count')
        ).filter(
            HRChatConversation.created_at >= start_date,
            HRChatConversation.category_detected.isnot(None)
        ).group_by(HRChatConversation.category_detected).all()
        
        # Feedback utenti
        feedback_stats = db.session.query(
            HRChatConversation.user_feedback,
            func.count(HRChatConversation.id).label('count')
        ).filter(
            HRChatConversation.created_at >= start_date,
            HRChatConversation.user_feedback.isnot(None)
        ).group_by(HRChatConversation.user_feedback).all()
        
        # Tempo di risposta medio
        avg_response_time = db.session.query(
            func.avg(HRChatConversation.response_time_ms)
        ).filter(
            HRChatConversation.created_at >= start_date,
            HRChatConversation.response_time_ms.isnot(None)
        ).scalar()
        
        return api_response(
            data={
                'period_days': days,
                'total_conversations': total_conversations,
                'unique_users': unique_users,
                'avg_response_time_ms': int(avg_response_time) if avg_response_time else 0,
                'category_distribution': [
                    {'category': cat, 'count': count} 
                    for cat, count in category_stats
                ],
                'feedback_distribution': [
                    {'feedback': feedback, 'count': count} 
                    for feedback, count in feedback_stats
                ],
                'knowledge_base_stats': {
                    'total_entries': HRKnowledgeBase.query.filter(
                        HRKnowledgeBase.is_active == True
                    ).count(),
                    'ai_generated_entries': HRKnowledgeBase.query.filter(
                        HRKnowledgeBase.is_active == True,
                        HRKnowledgeBase.created_with_ai == True
                    ).count()
                }
            },
            message="Analytics HR assistant recuperate con successo"
        )
        
    except Exception as e:
        current_app.logger.error(f"Errore analytics HR assistant: {str(e)}")
        return api_response(
            success=False,
            message="Errore nel recupero delle analytics",
            status_code=500
        )
```

### HR AI Service (`backend/services/hr_ai_service.py`)

```python
"""
HR Assistant AI Service - Knowledge management e chatbot support
Integra OpenAI e Sonar Pro per assistenza dipendenti e content creation
"""

import os
import json
import logging
import asyncio
import httpx
from datetime import datetime
from typing import Dict, List, Optional, Union
from flask import current_app
from sqlalchemy import or_, desc

from extensions import db
from models_split.hr_assistant import HRKnowledgeBase, HRChatConversation, HRContentTemplate

logger = logging.getLogger(__name__)

class HRAIService:
    """
    Servizio AI per HR Assistant module.
    Gestisce chatbot support e content creation con AI.
    """
    
    def __init__(self):
        self.openai_api_key = os.getenv('OPENAI_API_KEY')
        self.perplexity_api_key = os.getenv('PERPLEXITY_API_KEY')
        self.openai_base_url = "https://api.openai.com/v1"
        self.perplexity_base_url = "https://api.perplexity.ai"
        
        # Categorie HR supportate
        self.hr_categories = {
            'contracts': 'Informazioni Contrattuali',
            'onboarding': 'Procedure di Onboarding',
            'offboarding': 'Procedure di Offboarding', 
            'leave': 'Gestione Ferie',
            'permits': 'Permessi e Congedi',
            'travel': 'Trasferte e Rimborsi',
            'benefits': 'Benefit e Welfare'
        }

    async def process_hr_query(self, user_message: str, user_id: int, session_id: str = None) -> Dict:
        """
        Processa query HR employee e genera risposta chatbot.
        
        Args:
            user_message: Domanda del dipendente
            user_id: ID utente che fa la domanda
            session_id: ID sessione per tracking conversazione
            
        Returns:
            Dict con risposta, categoria, confidence, KB entries usate
        """
        try:
            start_time = datetime.utcnow()
            
            # Step 1: Detect categoria dalla domanda
            category = await self._detect_hr_category(user_message)
            
            # Step 2: Search knowledge base per contenuti rilevanti
            relevant_kb = await self._search_knowledge_base(user_message, category)
            
            # Step 3: Generate risposta chatbot
            bot_response = await self._generate_hr_response(
                user_message, category, relevant_kb
            )
            
            # Step 4: Store conversazione per analytics
            processing_time = (datetime.utcnow() - start_time).total_seconds() * 1000
            
            conversation = HRChatConversation(
                user_id=user_id,
                session_id=session_id or f"session_{user_id}_{int(datetime.utcnow().timestamp())}",
                user_message=user_message,
                bot_response=bot_response.get('content', ''),
                category_detected=category,
                confidence_score=bot_response.get('confidence', 'medium'),
                kb_entries_used=json.dumps([kb['id'] for kb in relevant_kb]),
                response_time_ms=int(processing_time)
            )
            
            db.session.add(conversation)
            db.session.commit()
            
            return {
                'response': bot_response.get('content'),
                'category': category,
                'confidence': bot_response.get('confidence'),
                'kb_entries_used': relevant_kb,
                'response_time_ms': int(processing_time),
                'conversation_id': conversation.id,
                'suggested_actions': bot_response.get('suggested_actions', [])
            }
            
        except Exception as e:
            logger.error(f"Error processing HR query: {str(e)}")
            return self._get_fallback_hr_response(user_message, user_id)

    async def _detect_hr_category(self, user_message: str) -> str:
        """Detect categoria HR dalla domanda usando AI."""
        try:
            categories_text = "\n".join([
                f"- {key}: {value}" for key, value in self.hr_categories.items()
            ])
            
            prompt = f"""
            Analizza la seguente domanda HR e identifica la categoria più appropriata.
            
            Domanda: "{user_message}"
            
            Categorie disponibili:
            {categories_text}
            
            Rispondi SOLO con la chiave della categoria (es: 'contracts', 'leave', etc.).
            Se non sei sicuro, usa 'general'.
            """
            
            response = await self._call_openai_simple(prompt, max_tokens=50)
            category = response.strip().lower()
            
            # Validate categoria
            if category in self.hr_categories:
                return category
            else:
                return 'general'
                
        except Exception as e:
            logger.warning(f"Category detection failed: {str(e)}")
            return 'general'

    async def _search_knowledge_base(self, query: str, category: str = None) -> List[Dict]:
        """Search knowledge base per contenuti rilevanti."""
        try:
            # Base query
            kb_query = HRKnowledgeBase.query.filter(HRKnowledgeBase.is_active == True)
            
            # Filter by categoria se specificata
            if category and category != 'general':
                kb_query = kb_query.filter(HRKnowledgeBase.category == category)
            
            # Text search in titolo e contenuto
            search_term = f"%{query}%"
            kb_query = kb_query.filter(
                or_(
                    HRKnowledgeBase.title.ilike(search_term),
                    HRKnowledgeBase.content.ilike(search_term),
                    HRKnowledgeBase.tags.ilike(search_term)
                )
            )
            
            # Ordina per rilevanza (per ora by data, in futuro similarity scoring)
            kb_entries = kb_query.order_by(desc(HRKnowledgeBase.created_at)).limit(5).all()
            
            return [{
                'id': entry.id,
                'title': entry.title,
                'content': entry.content,
                'category': entry.category,
                'relevance_score': 0.8  # Placeholder per future similarity scoring
            } for entry in kb_entries]
            
        except Exception as e:
            logger.error(f"Knowledge base search failed: {str(e)}")
            return []

    async def _generate_hr_response(self, user_message: str, category: str, kb_entries: List[Dict]) -> Dict:
        """Genera risposta HR chatbot usando KB e AI."""
        try:
            # Costruisci context dalla knowledge base
            kb_context = "\n\n".join([
                f"**{entry['title']}**\n{entry['content']}" 
                for entry in kb_entries
            ]) if kb_entries else "Nessuna informazione specifica trovata nella knowledge base."
            
            system_prompt = f"""
            Sei un assistente HR specializzato per aziende italiane PMI. 
            Fornisci risposte accurate, professionali e utili ai dipendenti.
            
            Categoria domanda: {self.hr_categories.get(category, 'Generale')}
            
            Linee guida:
            - Usa informazioni dalla knowledge base aziendale quando disponibili
            - Se non hai informazioni specifiche, suggerisci di contattare HR
            - Mantieni tono professionale ma amichevole  
            - Includi riferimenti normativi italiani quando rilevanti
            - Suggerisci azioni concrete quando possibile
            - Rispondi in italiano
            """
            
            user_prompt = f"""
            Domanda dipendente: {user_message}
            
            Informazioni dalla Knowledge Base Aziendale:
            {kb_context}
            
            Fornisci una risposta completa che:
            1. Risponda direttamente alla domanda
            2. Utilizzi le informazioni aziendali quando disponibili
            3. Includa azioni concrete da intraprendere
            4. Suggerisca quando contattare HR per approfondimenti
            
            Formato risposta in markdown con sezioni chiare.
            """
            
            response_content = await self._call_openai(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                max_tokens=1000,
                temperature=0.3
            )
            
            # Extract suggested actions
            suggested_actions = self._extract_suggested_actions(response_content)
            
            # Determine confidence based on KB matches
            confidence = 'high' if len(kb_entries) >= 2 else 'medium' if len(kb_entries) == 1 else 'low'
            
            return {
                'content': response_content,
                'confidence': confidence,
                'suggested_actions': suggested_actions
            }
            
        except Exception as e:
            logger.error(f"HR response generation failed: {str(e)}")
            return self._get_simple_fallback_response(user_message, category)

    async def generate_hr_content(self, category: str, topic: str, requirements: str = '', template_id: int = None) -> Dict:
        """
        Genera contenuto HR assistito da AI per amministratori.
        Usa Sonar Pro per research + OpenAI per content creation.
        """
        try:
            start_time = datetime.utcnow()
            
            # Step 1: Load template se specificato
            template_data = None
            if template_id:
                template = HRContentTemplate.query.get(template_id)
                if template:
                    template_data = {
                        'prompt_template': template.prompt_template,
                        'required_fields': json.loads(template.required_fields or '[]'),
                        'output_format': template.output_format
                    }
            
            # Step 2: Research web con Sonar Pro per context aggiornato
            web_research = await self._sonar_pro_hr_research(category, topic, requirements)
            
            # Step 3: Generate content con OpenAI
            content_result = await self._generate_ai_hr_content(
                category, topic, requirements, web_research, template_data
            )
            
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            
            return {
                'content': content_result.get('content'),
                'sources': web_research.get('sources', []),
                'confidence': content_result.get('confidence', 'medium'),
                'processing_time': round(processing_time, 1),
                'template_used': template_id is not None,
                'suggestions': content_result.get('suggestions', [])
            }
            
        except Exception as e:
            logger.error(f"HR content generation failed: {str(e)}")
            return self._get_content_generation_fallback(category, topic)

    async def _sonar_pro_hr_research(self, category: str, topic: str, requirements: str) -> Dict:
        """Ricerca web con Sonar Pro per context HR aggiornato."""
        if not self.perplexity_api_key:
            logger.warning("Perplexity API key not configured, using simulated research")
            return self._simulate_hr_research(category, topic)
        
        try:
            research_query = f"""
            Ricerca informazioni aggiornate per contenuto HR italiano su: {topic}
            
            Categoria: {self.hr_categories.get(category, category)}
            Requisiti specifici: {requirements}
            
            Focus su:
            - Normative italiane vigenti (Codice del Lavoro, CCNL)
            - Best practices PMI italiane
            - Procedure standard e template
            - Aspetti GDPR e privacy
            - Aggiornamenti recenti normativa lavoro
            
            Fornisci informazioni accurate e fonti verificabili.
            """
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.perplexity_base_url}/chat/completions",
                    headers={
                        "Authorization": f"Bearer {self.perplexity_api_key}",
                        "Content-Type": "application/json"
                    },
                    json={
                        "model": "llama-3.1-sonar-large-128k-online",
                        "messages": [
                            {"role": "user", "content": research_query}
                        ],
                        "max_tokens": 2000,
                        "temperature": 0.2
                    },
                    timeout=45.0
                )
                
                if response.status_code != 200:
                    logger.warning(f"Sonar Pro API error: {response.status_code}")
                    return self._simulate_hr_research(category, topic)
                
                result = response.json()
                research_content = result['choices'][0]['message']['content']
                
                # Extract sources (basic parsing)
                sources = self._extract_sources_from_research(research_content)
                
                return {
                    'content': research_content,
                    'sources': sources,
                    'research_time': datetime.utcnow().isoformat()
                }
                
        except Exception as e:
            logger.warning(f"Sonar Pro research failed: {str(e)}")
            return self._simulate_hr_research(category, topic)

    async def _generate_ai_hr_content(self, category: str, topic: str, requirements: str, 
                                    web_research: Dict, template_data: Dict = None) -> Dict:
        """Genera contenuto HR finale con OpenAI."""
        try:
            # Template prompt o standard prompt
            if template_data:
                base_prompt = template_data['prompt_template']
                system_prompt = f"""
                Sei un esperto HR per PMI italiane. Crea contenuti professionali seguendo il template fornito.
                Categoria: {self.hr_categories.get(category, category)}
                
                Template requirements: {json.dumps(template_data.get('required_fields', []))}
                Output format: {template_data.get('output_format', 'Markdown strutturato')}
                """
            else:
                system_prompt = f"""
                Sei un esperto HR per PMI italiane specializzato in {self.hr_categories.get(category, category)}.
                
                Crea contenuti HR professionali, accurati e pratici che includano:
                - Informazioni normative italiane aggiornate
                - Procedure step-by-step chiare
                - Template e esempi pratici quando appropriato
                - Riferimenti GDPR e privacy
                - Linguaggio professionale ma accessibile
                
                Output in markdown ben strutturato con sezioni logiche.
                """
                base_prompt = """
                Crea contenuto completo su: {topic}
                
                Requisiti specifici: {requirements}
                
                Research context aggiornato:
                {research_content}
                
                Struttura consigliata:
                # {topic}
                
                ## Panoramica
                ## Normativa di Riferimento  
                ## Procedure e Processi
                ## Template/Esempi
                ## FAQ Comuni
                ## Contatti e Risorse
                """
            
            user_prompt = base_prompt.format(
                topic=topic,
                requirements=requirements,
                research_content=web_research.get('content', '')
            )
            
            content = await self._call_openai(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                max_tokens=2500,
                temperature=0.4
            )
            
            # Extract suggestions for improvement
            suggestions = self._extract_content_suggestions(content, web_research)
            
            # Determine confidence based on research quality
            confidence = 'high' if web_research.get('sources') else 'medium'
            
            return {
                'content': content,
                'confidence': confidence,
                'suggestions': suggestions
            }
            
        except Exception as e:
            logger.error(f"AI content generation failed: {str(e)}")
            raise

    # Utility methods following CEO AI service patterns
    async def _call_openai(self, system_prompt: str, user_prompt: str, max_tokens: int = 1500, temperature: float = 0.7) -> str:
        """Effettua chiamata OpenAI API."""
        if not self.openai_api_key:
            raise ValueError("OpenAI API key not configured")
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.openai_base_url}/chat/completions",
                headers={
                    "Authorization": f"Bearer {self.openai_api_key}",
                    "Content-Type": "application/json"
                },
                json={
                    "model": "gpt-4o-mini",
                    "messages": [
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": user_prompt}
                    ],
                    "max_tokens": max_tokens,
                    "temperature": temperature
                },
                timeout=30.0
            )
            
            if response.status_code != 200:
                raise Exception(f"OpenAI API error: {response.status_code} - {response.text}")
            
            result = response.json()
            return result['choices'][0]['message']['content']

    async def _call_openai_simple(self, prompt: str, max_tokens: int = 100) -> str:
        """Chiamata OpenAI semplificata per task rapidi."""
        return await self._call_openai(
            system_prompt="Sei un assistente AI preciso e conciso.",
            user_prompt=prompt,
            max_tokens=max_tokens,
            temperature=0.1
        )

    def _extract_suggested_actions(self, response_content: str) -> List[str]:
        """Estrae azioni suggerite dalla risposta."""
        actions = []
        lines = response_content.split('\n')
        
        in_actions_section = False
        for line in lines:
            line = line.strip()
            
            if any(keyword in line.lower() for keyword in ['azioni', 'passaggi', 'next steps', 'cosa fare']):
                in_actions_section = True
                continue
                
            if in_actions_section and line:
                if line.startswith(('1.', '2.', '3.', '•', '-', '*')):
                    action = line
                    for prefix in ['1.', '2.', '3.', '4.', '5.', '•', '-', '*']:
                        if action.startswith(prefix):
                            action = action[len(prefix):].strip()
                            break
                    
                    if action and len(action) > 10:
                        actions.append(action)
                
                elif line.startswith('#') or line.startswith('**'):
                    in_actions_section = False
        
        return actions[:3]  # Limit to 3 actions

    def _extract_sources_from_research(self, research_content: str) -> List[str]:
        """Estrae fonti dal contenuto di ricerca."""
        sources = []
        lines = research_content.split('\n')
        
        for line in lines:
            # Look for URLs or source citations
            if 'http' in line or 'www.' in line:
                sources.append(line.strip())
            elif any(keyword in line.lower() for keyword in ['fonte:', 'source:', 'riferimento:']):
                sources.append(line.strip())
        
        return sources[:5]  # Limit sources

    def _extract_content_suggestions(self, content: str, web_research: Dict) -> List[str]:
        """Genera suggerimenti per miglioramento contenuto."""
        suggestions = []
        
        # Check content completeness
        if len(content) < 500:
            suggestions.append("Considera di espandere il contenuto con più dettagli ed esempi")
        
        # Check for normative references
        if not any(keyword in content.lower() for keyword in ['decreto', 'legge', 'ccnl', 'codice civile']):
            suggestions.append("Aggiungi riferimenti normativi specifici per maggiore autorevolezza")
        
        # Check for practical examples
        if not any(keyword in content.lower() for keyword in ['esempio', 'template', 'modello']):
            suggestions.append("Includi esempi pratici o template per facilitare l'applicazione")
        
        return suggestions

    def _simulate_hr_research(self, category: str, topic: str) -> Dict:
        """Simula ricerca HR quando Sonar Pro non è disponibile."""
        simulated_content = f"""
        Ricerca simulata per {self.hr_categories.get(category, category)} - {topic}:
        
        Riferimenti Normativi Italiani:
        - Decreto Legislativo 81/2008 (Sicurezza sul lavoro)
        - Codice Civile - Libro V (Rapporti di lavoro)
        - CCNL di riferimento settoriale
        - Decreto Legislativo 196/2003 (Privacy)
        
        Best Practices PMI:
        - Procedure standardizzate per efficienza operativa
        - Documentazione conforme normativa italiana
        - Template personalizzabili per diverse esigenze
        - Integrazione con sistemi HR digitali
        
        Nota: Questa è ricerca simulata. Per informazioni real-time,
        configurare Perplexity API key nelle variabili ambiente.
        """
        
        return {
            'content': simulated_content,
            'sources': [
                'Decreto Legislativo 81/2008',
                'Codice Civile Italiano',
                'CCNL Settoriale',
                'GDPR - Regolamento UE 2016/679'
            ],
            'research_time': datetime.utcnow().isoformat()
        }

    def _get_fallback_hr_response(self, user_message: str, user_id: int) -> Dict:
        """Risposta di fallback per errori HR chatbot."""
        return {
            'response': f"""🤖 **Assistente HR - Servizio Temporaneamente Non Disponibile**

Mi dispiace, sto riscontrando difficoltà tecniche nel elaborare la tua richiesta: "{user_message}"

**Cosa puoi fare ora:**
• Riprova tra qualche minuto
• Contatta direttamente l'ufficio HR per assistenza urgente
• Controlla la knowledge base aziendale per info rapide

**Contatti HR:**
📧 <EMAIL>
📞 +39 XXX XXXXXXX

Ti assicuro che risolveremo al più presto!""",
            'category': 'general',
            'confidence': 'low',
            'kb_entries_used': [],
            'response_time_ms': 100,
            'conversation_id': None,
            'suggested_actions': [
                'Contatta direttamente HR',
                'Riprova la richiesta più tardi',
                'Consulta knowledge base aziendale'
            ]
        }

    def _get_simple_fallback_response(self, user_message: str, category: str) -> Dict:
        """Risposta fallback semplice per errori generazione."""
        category_name = self.hr_categories.get(category, 'Generale')
        
        return {
            'content': f"""**Informazioni {category_name}**

Ho ricevuto la tua domanda su: "{user_message}"

Per questa tipologia di richiesta ({category_name}), ti consiglio di:

1. **Consultare** la documentazione aziendale disponibile
2. **Contattare** direttamente l'ufficio HR per informazioni specifiche
3. **Verificare** eventuali comunicazioni recenti sull'argomento

📞 **Contatto HR diretto:** <EMAIL>

Sarò in grado di fornirti risposte più dettagliate non appena il servizio tornerà completamente operativo.""",
            'confidence': 'low',
            'suggested_actions': [
                'Contatta HR per informazioni specifiche',
                'Consulta documentazione aziendale',
                'Riprova più tardi per assistenza completa'
            ]
        }

    def _get_content_generation_fallback(self, category: str, topic: str) -> Dict:
        """Fallback per content generation quando AI non è disponibile."""
        category_name = self.hr_categories.get(category, category)
        
        return {
            'content': f"""# {topic}

## Struttura Base per {category_name}

Questo template di base può essere utilizzato come punto di partenza per sviluppare contenuti su **{topic}**.

### Sezioni Consigliate:

1. **Panoramica**
   - Introduzione all'argomento
   - Applicabilità nel contesto aziendale

2. **Normativa di Riferimento**
   - Decreto Legislativo applicabile
   - CCNL settoriale
   - Disposizioni aziendali

3. **Procedure Operative**
   - Passaggi step-by-step
   - Responsabilità e ruoli
   - Tempistiche

4. **Documenti e Template**
   - Modulistica necessaria
   - Template di comunicazione
   - Checklist operative

5. **FAQ e Casi Comuni**
   - Domande frequenti
   - Situazioni tipiche
   - Soluzioni pratiche

### Nota Importante

Per contenuti più dettagliati e aggiornati, si consiglia di:
- Configurare i servizi AI (OpenAI + Perplexity)
- Consultare fonti normative ufficiali
- Coinvolgere esperti HR nella revisione

---
*Template generato automaticamente - Personalizzare secondo esigenze specifiche*""",
            'sources': [],
            'confidence': 'low',
            'processing_time': 0.1,
            'template_used': False,
            'suggestions': [
                'Configura servizi AI per content generation avanzata',
                'Consulta fonti normative ufficiali aggiornate', 
                'Fai revisionare da esperti HR interni'
            ]
        }


# Factory functions per integration
def get_hr_ai_service() -> HRAIService:
    """Factory function per HR AI Service."""
    return HRAIService()

async def process_hr_query(user_message: str, user_id: int, session_id: str = None) -> Dict:
    """
    Processa query HR in modo asincrono.
    Wrapper principale per l'integrazione con Flask routes.
    """
    service = get_hr_ai_service()
    return await service.process_hr_query(user_message, user_id, session_id)

async def generate_hr_content(category: str, topic: str, requirements: str = '', template_id: int = None) -> Dict:
    """
    Genera contenuto HR assistito da AI.
    Wrapper per content creation da parte degli amministratori.
    """
    service = get_hr_ai_service()
    return await service.generate_hr_content(category, topic, requirements, template_id)
```

### Frontend Implementation

#### Store (`frontend/src/stores/hrAssistant.js`)

```javascript
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '@/utils/api'

export const useHRAssistantStore = defineStore('hrAssistant', () => {
  // State
  const conversations = ref([])
  const currentSessionId = ref(null)
  const knowledgeBase = ref([])
  const templates = ref([])
  const analytics = ref({})
  const loading = ref(false)
  const error = ref(null)

  // Getters
  const currentConversation = computed(() => {
    return conversations.value.filter(c => c.sessionId === currentSessionId.value)
  })

  const categoriesList = computed(() => [
    { key: 'contracts', label: 'Informazioni Contrattuali', icon: 'document-text' },
    { key: 'onboarding', label: 'Procedure di Onboarding', icon: 'user-plus' },
    { key: 'offboarding', label: 'Procedure di Offboarding', icon: 'user-minus' },
    { key: 'leave', label: 'Gestione Ferie', icon: 'calendar-days' },
    { key: 'permits', label: 'Permessi e Congedi', icon: 'clock' },
    { key: 'travel', label: 'Trasferte e Rimborsi', icon: 'map-pin' },
    { key: 'benefits', label: 'Benefit e Welfare', icon: 'gift' }
  ])

  // Actions
  async function sendMessage(message) {
    loading.value = true
    error.value = null

    try {
      // Ensure session ID
      if (!currentSessionId.value) {
        currentSessionId.value = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      }

      // Add user message to conversation
      const userMessage = {
        id: `msg_${Date.now()}`,
        type: 'user',
        content: message,
        timestamp: new Date().toISOString(),
        sessionId: currentSessionId.value
      }
      conversations.value.push(userMessage)

      // Send to backend
      const response = await api.post('/api/communication/hr-assistant/chat', {
        message,
        session_id: currentSessionId.value
      })

      if (response.data.success) {
        // Add bot response to conversation
        const botMessage = {
          id: `msg_${Date.now() + 1}`,
          type: 'bot',
          content: response.data.data.response,
          category: response.data.data.category,
          confidence: response.data.data.confidence,
          suggestedActions: response.data.data.suggested_actions || [],
          timestamp: new Date().toISOString(),
          sessionId: currentSessionId.value,
          conversationId: response.data.data.conversation_id
        }
        conversations.value.push(botMessage)

        return { success: true, data: response.data.data }
      } else {
        error.value = response.data.message
        return { success: false, error: error.value }
      }
    } catch (err) {
      error.value = err.response?.data?.message || 'Errore di connessione'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  async function provideFeedback(conversationId, feedback) {
    try {
      const response = await api.post(`/api/communication/hr-assistant/feedback`, {
        conversation_id: conversationId,
        feedback // 'helpful', 'not_helpful', 'neutral'
      })

      if (response.data.success) {
        // Update conversation with feedback
        const message = conversations.value.find(c => c.conversationId === conversationId)
        if (message) {
          message.userFeedback = feedback
        }
        return { success: true }
      }
    } catch (err) {
      console.error('Feedback submission failed:', err)
      return { success: false }
    }
  }

  async function loadKnowledgeBase(filters = {}) {
    loading.value = true
    try {
      const params = new URLSearchParams()
      if (filters.category) params.append('category', filters.category)
      if (filters.search) params.append('search', filters.search)
      if (filters.page) params.append('page', filters.page)

      const response = await api.get(`/api/communication/hr-assistant/knowledge-base?${params}`)

      if (response.data.success) {
        knowledgeBase.value = response.data.data.entries
        return {
          success: true,
          data: response.data.data
        }
      }
    } catch (err) {
      error.value = err.response?.data?.message || 'Errore caricamento knowledge base'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  async function createKnowledgeEntry(entryData) {
    loading.value = true
    try {
      const response = await api.post('/api/communication/hr-assistant/knowledge-base', entryData)

      if (response.data.success) {
        // Refresh knowledge base
        await loadKnowledgeBase()
        return { success: true, data: response.data.data }
      } else {
        error.value = response.data.message
        return { success: false, error: error.value }
      }
    } catch (err) {
      error.value = err.response?.data?.message || 'Errore creazione entry'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  async function generateAIContent(contentParams) {
    loading.value = true
    try {
      const response = await api.post('/api/communication/hr-assistant/generate-content', {
        category: contentParams.category,
        topic: contentParams.topic,
        requirements: contentParams.requirements || '',
        template_id: contentParams.templateId || null,
        use_ai_assistance: true
      })

      if (response.data.success) {
        return { success: true, data: response.data.data }
      } else {
        error.value = response.data.message
        return { success: false, error: error.value }
      }
    } catch (err) {
      error.value = err.response?.data?.message || 'Errore generazione contenuto'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  async function loadAnalytics(days = 30) {
    loading.value = true
    try {
      const response = await api.get(`/api/communication/hr-assistant/analytics?days=${days}`)

      if (response.data.success) {
        analytics.value = response.data.data
        return { success: true, data: response.data.data }
      }
    } catch (err) {
      error.value = err.response?.data?.message || 'Errore caricamento analytics'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  function startNewSession() {
    currentSessionId.value = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    // Keep conversations for history but mark new session
  }

  function clearConversations() {
    conversations.value = []
    currentSessionId.value = null
  }

  return {
    // State
    conversations,
    currentSessionId,
    knowledgeBase,
    templates,
    analytics,
    loading,
    error,

    // Getters
    currentConversation,
    categoriesList,

    // Actions
    sendMessage,
    provideFeedback,
    loadKnowledgeBase,
    createKnowledgeEntry,
    generateAIContent,
    loadAnalytics,
    startNewSession,
    clearConversations
  }
})
```

#### Chat Interface Component (`frontend/src/components/communication/HRAssistantChat.vue`)

```vue
<template>
  <div class="flex flex-col h-full bg-white dark:bg-gray-900 rounded-lg shadow-lg">
    <!-- Header -->
    <div class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
      <div class="flex items-center space-x-3">
        <div class="flex-shrink-0">
          <HeroIcon 
            name="chat-bubble-left-right" 
            class="h-8 w-8 text-blue-600 dark:text-blue-400" 
          />
        </div>
        <div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
            Assistente HR
          </h3>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            Supporto per procedure e informazioni aziendali
          </p>
        </div>
      </div>
      
      <div class="flex items-center space-x-2">
        <button
          @click="startNewSession"
          class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800"
          title="Nuova conversazione"
        >
          <HeroIcon name="plus" class="h-5 w-5" />
        </button>
        
        <button
          @click="clearConversations"
          class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800"
          title="Cancella cronologia"
        >
          <HeroIcon name="trash" class="h-5 w-5" />
        </button>
      </div>
    </div>

    <!-- Messages Area -->
    <div 
      ref="messagesContainer"
      class="flex-1 overflow-y-auto p-4 space-y-4"
    >
      <!-- Welcome Message -->
      <div v-if="currentConversation.length === 0" class="text-center py-8">
        <HeroIcon 
          name="academic-cap" 
          class="h-16 w-16 text-gray-300 dark:text-gray-600 mx-auto mb-4" 
        />
        <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
          Ciao! Sono il tuo Assistente HR
        </h4>
        <p class="text-gray-500 dark:text-gray-400 mb-6 max-w-md mx-auto">
          Posso aiutarti con informazioni su contratti, ferie, permessi, benefit e molto altro.
        </p>
        
        <!-- Quick Actions -->
        <div class="grid grid-cols-2 md:grid-cols-3 gap-3 max-w-2xl mx-auto">
          <button
            v-for="category in categoriesList.slice(0, 6)"
            :key="category.key"
            @click="sendQuickMessage(`Vorrei informazioni su ${category.label.toLowerCase()}`)"
            class="flex items-center space-x-2 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            <HeroIcon :name="category.icon" class="h-5 w-5 text-blue-600 dark:text-blue-400" />
            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
              {{ category.label }}
            </span>
          </button>
        </div>
      </div>

      <!-- Conversation Messages -->
      <div
        v-for="message in currentConversation"
        :key="message.id"
        :class="message.type === 'user' ? 'flex justify-end' : 'flex justify-start'"
      >
        <!-- User Message -->
        <div 
          v-if="message.type === 'user'"
          class="max-w-xs lg:max-w-md bg-blue-600 text-white rounded-lg p-3"
        >
          {{ message.content }}
        </div>

        <!-- Bot Message -->
        <div
          v-else
          class="max-w-xs lg:max-w-2xl bg-gray-100 dark:bg-gray-800 rounded-lg p-4"
        >
          <!-- Bot Avatar & Category -->
          <div class="flex items-center space-x-2 mb-2">
            <HeroIcon 
              name="cpu-chip" 
              class="h-5 w-5 text-blue-600 dark:text-blue-400" 
            />
            <span class="text-xs font-medium text-gray-500 dark:text-gray-400">
              {{ getCategoryLabel(message.category) }}
            </span>
            <ConfidenceBadge 
              :confidence="message.confidence" 
              class="text-xs"
            />
          </div>

          <!-- Message Content -->
          <div 
            class="prose prose-sm dark:prose-invert max-w-none"
            v-html="formatMessageContent(message.content)"
          ></div>

          <!-- Suggested Actions -->
          <div 
            v-if="message.suggestedActions && message.suggestedActions.length > 0"
            class="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600"
          >
            <p class="text-xs font-medium text-gray-500 dark:text-gray-400 mb-2">
              Azioni suggerite:
            </p>
            <div class="space-y-1">
              <button
                v-for="(action, index) in message.suggestedActions"
                :key="index"
                @click="sendQuickMessage(action)"
                class="block w-full text-left text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 p-1 rounded hover:bg-blue-50 dark:hover:bg-blue-900/20"
              >
                • {{ action }}
              </button>
            </div>
          </div>

          <!-- Feedback -->
          <div 
            v-if="message.conversationId && !message.userFeedback"
            class="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600 flex items-center space-x-2"
          >
            <span class="text-xs text-gray-500 dark:text-gray-400">Utile?</span>
            <button
              @click="provideFeedback(message.conversationId, 'helpful')"
              class="p-1 text-green-600 hover:text-green-800 hover:bg-green-50 dark:hover:bg-green-900/20 rounded"
              title="Utile"
            >
              <HeroIcon name="hand-thumb-up" class="h-4 w-4" />
            </button>
            <button
              @click="provideFeedback(message.conversationId, 'not_helpful')"
              class="p-1 text-red-600 hover:text-red-800 hover:bg-red-50 dark:hover:bg-red-900/20 rounded"
              title="Non utile"
            >
              <HeroIcon name="hand-thumb-down" class="h-4 w-4" />
            </button>
          </div>

          <!-- Feedback Received -->
          <div 
            v-if="message.userFeedback"
            class="mt-2 text-xs text-gray-500 dark:text-gray-400"
          >
            <HeroIcon 
              :name="message.userFeedback === 'helpful' ? 'check-circle' : 'x-circle'" 
              :class="message.userFeedback === 'helpful' ? 'text-green-500' : 'text-red-500'"
              class="h-4 w-4 inline mr-1" 
            />
            Feedback ricevuto
          </div>
        </div>
      </div>

      <!-- Loading Message -->
      <div v-if="loading" class="flex justify-start">
        <div class="max-w-xs bg-gray-100 dark:bg-gray-800 rounded-lg p-4">
          <div class="flex items-center space-x-2">
            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            <span class="text-sm text-gray-500 dark:text-gray-400">Sto elaborando...</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Input Area -->
    <div class="border-t border-gray-200 dark:border-gray-700 p-4">
      <form @submit.prevent="sendMessage" class="flex space-x-3">
        <div class="flex-1 relative">
          <input
            v-model="newMessage"
            type="text"
            placeholder="Scrivi la tua domanda..."
            :disabled="loading"
            class="w-full px-4 py-2 pr-12 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
          />
          
          <!-- Character Count -->
          <div class="absolute right-3 top-2 text-xs text-gray-400">
            {{ newMessage.length }}/500
          </div>
        </div>
        
        <button
          type="submit"
          :disabled="!newMessage.trim() || loading || newMessage.length > 500"
          class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          <HeroIcon 
            name="paper-airplane" 
            class="h-5 w-5" 
          />
        </button>
      </form>
      
      <!-- Error Message -->
      <div v-if="error" class="mt-2 text-sm text-red-600 dark:text-red-400">
        {{ error }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, nextTick, onMounted } from 'vue'
import { useHRAssistantStore } from '@/stores/hrAssistant'
import HeroIcon from '@/components/common/HeroIcon.vue'
import ConfidenceBadge from '@/components/common/ConfidenceBadge.vue'

const hrAssistantStore = useHRAssistantStore()

// Local state
const newMessage = ref('')
const messagesContainer = ref(null)

// Computed
const { 
  currentConversation, 
  categoriesList, 
  loading, 
  error 
} = hrAssistantStore

// Methods
async function sendMessage() {
  if (!newMessage.value.trim() || loading) return

  const message = newMessage.value.trim()
  newMessage.value = ''

  const result = await hrAssistantStore.sendMessage(message)
  
  if (result.success) {
    await nextTick()
    scrollToBottom()
  }
}

async function sendQuickMessage(message) {
  newMessage.value = message
  await sendMessage()
}

function getCategoryLabel(categoryKey) {
  const category = categoriesList.find(c => c.key === categoryKey)
  return category?.label || 'Generale'
}

function formatMessageContent(content) {
  // Basic markdown to HTML conversion
  return content
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/`(.*?)`/g, '<code class="bg-gray-200 dark:bg-gray-700 px-1 rounded">$1</code>')
    .replace(/\n/g, '<br>')
}

async function provideFeedback(conversationId, feedback) {
  await hrAssistantStore.provideFeedback(conversationId, feedback)
}

function startNewSession() {
  hrAssistantStore.startNewSession()
}

function clearConversations() {
  if (confirm('Sei sicuro di voler cancellare tutta la cronologia delle conversazioni?')) {
    hrAssistantStore.clearConversations()
  }
}

function scrollToBottom() {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

onMounted(() => {
  scrollToBottom()
})
</script>
```

#### Knowledge Base Management (`frontend/src/views/communications/HRKnowledgeBase.vue`)

```vue
<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
          Knowledge Base HR
        </h1>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Gestisci contenuti per l'assistente HR
        </p>
      </div>
      
      <button
        @click="showCreateModal = true"
        class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
      >
        <HeroIcon name="plus" class="h-4 w-4 mr-2" />
        Nuovo Contenuto
      </button>
    </div>

    <!-- Filters -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Categoria
          </label>
          <select
            v-model="filters.category"
            @change="loadKnowledgeBase"
            class="w-full rounded-lg border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="">Tutte le categorie</option>
            <option 
              v-for="category in categoriesList"
              :key="category.key"
              :value="category.key"
            >
              {{ category.label }}
            </option>
          </select>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Ricerca
          </label>
          <input
            v-model="filters.search"
            @input="debounceSearch"
            type="text"
            placeholder="Cerca nei contenuti..."
            class="w-full rounded-lg border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
        </div>
        
        <div class="flex items-end">
          <button
            @click="clearFilters"
            class="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"
          >
            Pulisci Filtri
          </button>
        </div>
      </div>
    </div>

    <!-- Knowledge Base Entries -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
      <div class="divide-y divide-gray-200 dark:divide-gray-700">
        <div
          v-for="entry in knowledgeBase"
          :key="entry.id"
          class="p-6 hover:bg-gray-50 dark:hover:bg-gray-700"
        >
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <div class="flex items-center space-x-3 mb-2">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                  {{ entry.title }}
                </h3>
                
                <CategoryBadge :category="entry.category" />
                
                <div v-if="entry.created_with_ai" class="flex items-center space-x-1">
                  <HeroIcon name="cpu-chip" class="h-4 w-4 text-purple-500" />
                  <span class="text-xs text-purple-600 dark:text-purple-400">
                    AI-Generated
                  </span>
                </div>
              </div>
              
              <p class="text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
                {{ truncateContent(entry.content) }}
              </p>
              
              <div class="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                <span>
                  <HeroIcon name="user" class="h-4 w-4 inline mr-1" />
                  {{ entry.creator?.full_name }}
                </span>
                <span>
                  <HeroIcon name="calendar" class="h-4 w-4 inline mr-1" />
                  {{ formatDate(entry.created_at) }}
                </span>
                <ConfidenceBadge 
                  v-if="entry.ai_confidence"
                  :confidence="entry.ai_confidence" 
                />
              </div>
              
              <!-- Tags -->
              <div v-if="entry.tags && entry.tags.length > 0" class="mt-2">
                <div class="flex flex-wrap gap-1">
                  <span
                    v-for="tag in entry.tags"
                    :key="tag"
                    class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300"
                  >
                    {{ tag }}
                  </span>
                </div>
              </div>
            </div>
            
            <div class="flex items-center space-x-2 ml-4">
              <button
                @click="editEntry(entry)"
                class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600"
                title="Modifica"
              >
                <HeroIcon name="pencil" class="h-4 w-4" />
              </button>
              
              <button
                @click="deleteEntry(entry)"
                class="p-2 text-red-400 hover:text-red-600 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20"
                title="Elimina"
              >
                <HeroIcon name="trash" class="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Empty State -->
      <div v-if="knowledgeBase.length === 0 && !loading" class="text-center py-12">
        <HeroIcon name="document-text" class="h-16 w-16 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
          Nessun contenuto trovato
        </h3>
        <p class="text-gray-500 dark:text-gray-400 mb-6">
          Inizia creando il primo contenuto per la knowledge base HR.
        </p>
        <button
          @click="showCreateModal = true"
          class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700"
        >
          <HeroIcon name="plus" class="h-4 w-4 mr-2" />
          Crea Primo Contenuto
        </button>
      </div>
      
      <!-- Loading -->
      <div v-if="loading" class="text-center py-12">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
        <p class="mt-2 text-gray-500 dark:text-gray-400">Caricamento...</p>
      </div>
    </div>

    <!-- Pagination -->
    <Pagination
      v-if="pagination && pagination.pages > 1"
      :current-page="pagination.page"
      :total-pages="pagination.pages"
      :total-items="pagination.total"
      @page-change="handlePageChange"
    />

    <!-- Create/Edit Modal -->
    <KnowledgeBaseModal
      v-if="showCreateModal || editingEntry"
      :entry="editingEntry"
      @close="closeModal"
      @save="handleSave"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useHRAssistantStore } from '@/stores/hrAssistant'
import HeroIcon from '@/components/common/HeroIcon.vue'
import CategoryBadge from '@/components/common/CategoryBadge.vue'
import ConfidenceBadge from '@/components/common/ConfidenceBadge.vue'
import Pagination from '@/components/common/Pagination.vue'
import KnowledgeBaseModal from '@/components/communication/KnowledgeBaseModal.vue'

const hrAssistantStore = useHRAssistantStore()

// Local state
const showCreateModal = ref(false)
const editingEntry = ref(null)
const filters = ref({
  category: '',
  search: '',
  page: 1
})
const pagination = ref(null)

// Computed
const { knowledgeBase, categoriesList, loading } = hrAssistantStore

// Methods
async function loadKnowledgeBase() {
  const result = await hrAssistantStore.loadKnowledgeBase(filters.value)
  if (result.success) {
    pagination.value = result.data.pagination
  }
}

function debounceSearch() {
  clearTimeout(debounceSearch.timer)
  debounceSearch.timer = setTimeout(() => {
    filters.value.page = 1
    loadKnowledgeBase()
  }, 500)
}

function clearFilters() {
  filters.value = {
    category: '',
    search: '',
    page: 1
  }
  loadKnowledgeBase()
}

function editEntry(entry) {
  editingEntry.value = entry
}

async function deleteEntry(entry) {
  if (!confirm(`Sei sicuro di voler eliminare "${entry.title}"?`)) return
  
  // Implementation for delete API call
  // await hrAssistantStore.deleteKnowledgeEntry(entry.id)
  // await loadKnowledgeBase()
}

function closeModal() {
  showCreateModal.value = false
  editingEntry.value = null
}

async function handleSave() {
  await loadKnowledgeBase()
  closeModal()
}

function handlePageChange(page) {
  filters.value.page = page
  loadKnowledgeBase()
}

function truncateContent(content, limit = 150) {
  if (content.length <= limit) return content
  return content.substring(0, limit) + '...'
}

function formatDate(dateString) {
  return new Date(dateString).toLocaleDateString('it-IT', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

onMounted(() => {
  loadKnowledgeBase()
})
</script>
```

## Implementation Roadmap

### Phase 1: Foundation (1-2 settimane)
1. **Database Models**: Implementazione `hr_assistant.py` models
2. **Basic API**: Core endpoints per chat e knowledge base
3. **HR AI Service**: Servizio base con OpenAI integration
4. **Pinia Store**: Store management per frontend state

### Phase 2: Core Features (2-3 settimane)
1. **Chat Interface**: Componente chat completo con UI/UX
2. **Knowledge Base**: Management interface per amministratori
3. **Category System**: Sistema categorie HR complete
4. **Basic AI Responses**: Chatbot funzionante con KB search

### Phase 3: AI Enhancement (1-2 settimane)
1. **Sonar Pro Integration**: Web research per content creation
2. **AI Content Generation**: Tools per amministratori
3. **Template System**: Template pre-configurati per content
4. **Confidence Scoring**: Sistema scoring per qualità risposte

### Phase 4: Analytics & Optimization (1 settimana)
1. **Usage Analytics**: Dashboard utilizzo per amministratori  
2. **Feedback System**: Sistema feedback utenti per improvement
3. **Performance Optimization**: Caching e ottimizzazioni performance
4. **Testing & Polish**: Testing completo e refinement UI

### Phase 5: Integration & Launch (1 settimana)
1. **Sidebar Integration**: Aggiunta alla navigazione comunicazioni
2. **Permission Integration**: Controlli accesso esistenti
3. **Documentation**: Guide utente e amministratore
4. **Production Deployment**: Deploy e monitoring

## Success Metrics

### User Engagement
- **Daily Active Users**: Utilizzo chatbot per supporto HR
- **Query Resolution Rate**: % domande risolte senza escalation
- **User Satisfaction**: Rating medio feedback utenti (target >4.0/5.0)
- **Session Duration**: Tempo medio interazione chatbot

### Content Quality
- **Knowledge Base Growth**: Numero entries create/aggiornate
- **AI Content Usage**: % contenuti creati con assistenza AI
- **Content Accuracy**: Feedback qualità da utenti e HR team
- **Search Effectiveness**: % query matched con KB entries

### Operational Efficiency
- **HR Workload Reduction**: Riduzione % ticket HR routine
- **Response Time**: Tempo medio risposta chatbot (<3 secondi)
- **Content Creation Speed**: Tempo medio creazione content con AI
- **System Reliability**: Uptime e performance metrics

## Security & Compliance

### Data Protection
- **GDPR Compliance**: Gestione dati personali secondo normativa
- **Conversation Logging**: Storage sicuro conversazioni con retention policy
- **Access Control**: Permission-based access a KB e analytics
- **Data Encryption**: Encryption at rest e in transit per dati sensibili

### AI Safety
- **Content Moderation**: Validazione risposte AI prima delivery
- **Hallucination Prevention**: Grounding responses su KB verificata
- **Bias Monitoring**: Monitoring per bias nelle risposte AI
- **Human Oversight**: Escalation automatica per queries complesse

Questo piano implementa un sistema HR Assistant completo che si integra seamlessly con l'architettura DatPortal esistente, fornendo valore immediato ai dipendenti e tools potenti per amministratori HR.