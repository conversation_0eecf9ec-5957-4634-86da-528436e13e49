# Compliance & Audit Logging - DatPortal Implementation Plan

## Executive Summary

Il **Sistema di Compliance e Audit Logging** per DatPortal implementa un approccio **middleware-based non-invasivo** seguendo rigorosamente i pattern architetturali esistenti per garantire tracciabilità completa, compliance GDPR e controlli di sicurezza enterprise-grade.

### Obiettivi Strategici
- **Zero Refactoring**: Implementazione senza modifiche a API e modelli esistenti
- **Pattern Consistency**: Seguire architettura Flask Blueprints + Vue 3 + Pinia esistente
- **Compliance GDPR**: Gestione completa privacy e data protection per PMI italiane
- **Security Enhancement**: Controlli avanzati per ambiente enterprise
- **Performance Optimized**: Logging asincrono senza impatto prestazioni

## DatPortal Architecture Alignment

### Backend Pattern Integration
- **Flask Blueprints**: `backend/blueprints/api/compliance.py` seguendo pattern `ceo.py`
- **API Response Standard**: `api_response()` utility per consistency
- **Permission System**: `PERMISSION_VIEW_COMPLIANCE` già esistente per admin/manager
- **Models Organization**: `backend/models_split/compliance.py` seguendo pattern esistente
- **Services Layer**: `backend/services/audit_service.py` per business logic

### Frontend Pattern Integration  
- **Vue 3 + Composition API**: Seguendo pattern views/admin/ e views/ceo/
- **Pinia State Management**: Store pattern alignment con `stores/auth.js`
- **HeroIcon System**: Icone standard (`shield-check`, `document-text`, `exclamation-triangle`)
- **Design System**: TailwindCSS brand colors e componenti riutilizzabili
- **Sidebar Integration**: Aggiornamento `SidebarNavigation.vue` con sezione Compliance

### Permission & Security Alignment
- **RBAC Integration**: `PERMISSION_VIEW_COMPLIANCE` per admin/manager roles
- **Session Security**: Integration con Flask-Login esistente + CSRF protection
- **API Decorators**: `@api_permission_required()` pattern standardizzato

## Technical Architecture - DatPortal Integration

### Middleware-Based Approach Aligned with DatPortal

#### 1. Flask Request Middleware Integration
```python
# backend/middleware/audit_middleware.py
# Integration con app.py esistente senza modifiche invasive

from flask import current_app, request, g
from flask_login import current_user
from datetime import datetime
from utils.api_utils import api_response  # Usa utility esistente
from services.audit_service import audit_service  # Nuovo service layer

def init_audit_middleware(app):
    """Initialize audit middleware seguendo pattern DatPortal"""
    
    @app.before_request
    def capture_request_context():
        """Capture request data seguendo pattern Flask-Login esistente"""
        if request.endpoint and should_audit_endpoint(request.endpoint):
            g.audit_context = {
                'user_id': current_user.id if current_user.is_authenticated else None,
                'ip_address': request.remote_addr,
                'user_agent': request.user_agent.string,
                'method': request.method,
                'endpoint': request.endpoint,
                'url': request.url,
                'timestamp': datetime.utcnow(),
                'request_data': get_sanitized_request_data()
            }

@app.after_request
def log_request_audit(response):
    """Log audit trail after successful requests"""
    if (hasattr(g, 'audit_context') and 
        response.status_code < 400 and
        is_modifying_operation(request.method)):
        
        # Extract resource information from endpoint
        resource_info = extract_resource_info(request.endpoint, request.view_args)
        
        # Create audit log asynchronously
        create_audit_log_async(
            user_id=g.audit_context['user_id'],
            action=map_method_to_action(request.method),
            resource_type=resource_info.get('type'),
            resource_id=resource_info.get('id'),
            endpoint=g.audit_context['endpoint'],
            ip_address=g.audit_context['ip_address'],
            user_agent=g.audit_context['user_agent'],
            request_data=g.audit_context.get('request_data'),
            response_status=response.status_code
        )
    
    return response

def should_audit_endpoint(endpoint):
    """Determine if endpoint should be audited"""
    # Audit API endpoints and sensitive operations
    audit_patterns = [
        'api.',  # All API endpoints
        'auth.',  # Authentication operations
        'admin.'  # Administrative operations
    ]
    
    exclude_patterns = [
        'static',  # Static files
        'api.dashboard.stats',  # High-frequency read operations
        'api.communication.messages'  # Message polling
    ]
    
    if any(pattern in endpoint for pattern in exclude_patterns):
        return False
    
    return any(pattern in endpoint for pattern in audit_patterns)

def is_modifying_operation(method):
    """Check if HTTP method is a modifying operation"""
    return method in ['POST', 'PUT', 'PATCH', 'DELETE']
```

#### 2. SQLAlchemy Event Listeners
```python
# middleware/database_audit.py

from sqlalchemy import event
from sqlalchemy.orm import object_session

def setup_database_audit_listeners():
    """Setup automatic database change tracking"""
    
    @event.listens_for(db.session, 'before_commit')
    def capture_pending_changes(session):
        """Capture changes before they're committed"""
        g.pending_audit_logs = []
        
        # Track new objects
        for obj in session.new:
            if should_audit_model(obj.__class__):
                g.pending_audit_logs.append({
                    'action': 'create',
                    'resource_type': obj.__class__.__name__,
                    'resource_id': None,  # Will be set after commit
                    'object_data': serialize_object_for_audit(obj),
                    'changes': None
                })
        
        # Track modified objects
        for obj in session.dirty:
            if should_audit_model(obj.__class__) and session.is_modified(obj):
                changes = get_object_changes(obj)
                if changes:  # Only log if there are actual changes
                    g.pending_audit_logs.append({
                        'action': 'update',
                        'resource_type': obj.__class__.__name__,
                        'resource_id': getattr(obj, 'id', None),
                        'object_data': serialize_object_for_audit(obj),
                        'changes': changes
                    })
        
        # Track deleted objects
        for obj in session.deleted:
            if should_audit_model(obj.__class__):
                g.pending_audit_logs.append({
                    'action': 'delete',
                    'resource_type': obj.__class__.__name__,
                    'resource_id': getattr(obj, 'id', None),
                    'object_data': serialize_object_for_audit(obj),
                    'changes': None
                })

    @event.listens_for(db.session, 'after_commit')
    def create_audit_logs(session):
        """Create audit logs after successful commit"""
        if hasattr(g, 'pending_audit_logs'):
            for log_data in g.pending_audit_logs:
                # Update resource_id for new objects
                if log_data['action'] == 'create' and log_data['resource_id'] is None:
                    # Try to get ID from object_data
                    log_data['resource_id'] = log_data['object_data'].get('id')
                
                create_audit_log_async(**log_data)
            
            # Clean up
            delattr(g, 'pending_audit_logs')

def should_audit_model(model_class):
    """Determine if model changes should be audited"""
    # Exclude audit log model itself to prevent recursion
    excluded_models = ['AuditLog', 'Session']
    
    # Include all important business models
    audited_models = [
        'User', 'Project', 'Task', 'Client', 'Contact',
        'TimesheetEntry', 'Expense', 'Invoice', 'Contract',
        'News', 'Event', 'Message', 'Document'
    ]
    
    model_name = model_class.__name__
    
    if model_name in excluded_models:
        return False
    
    return model_name in audited_models or not model_name.startswith('_')

def get_object_changes(obj):
    """Get changes made to an object"""
    changes = {}
    state = object_session(obj).identity_map._mutable_attrs.get(obj)
    
    if state:
        for attr in state.committed_state:
            current_value = getattr(obj, attr)
            original_value = state.committed_state[attr]
            
            if current_value != original_value:
                changes[attr] = {
                    'from': serialize_value(original_value),
                    'to': serialize_value(current_value)
                }
    
    return changes if changes else None
```

### Data Models - DatPortal Integration

```python
# backend/models_split/compliance.py
# Seguendo pattern models_split/ esistente per organizzazione modulare

from extensions import db
from datetime import datetime
from models_split.base import BaseModel  # Usa base model esistente se disponibile

class AuditLog(db.Model):
    __tablename__ = 'audit_logs'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # User context
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)
    ip_address = db.Column(db.String(45))  # IPv6 compatible
    user_agent = db.Column(db.String(500))
    
    # Action details
    action = db.Column(db.String(20), nullable=False)  # create, read, update, delete
    resource_type = db.Column(db.String(50), nullable=False)
    resource_id = db.Column(db.Integer)
    endpoint = db.Column(db.String(100))
    
    # Change tracking
    changes = db.Column(db.JSON)  # Detailed change information
    object_data = db.Column(db.JSON)  # Snapshot of object state
    
    # Request context
    request_data = db.Column(db.JSON)  # Sanitized request data
    response_status = db.Column(db.Integer)
    
    # Timestamp
    timestamp = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    
    # Relationships
    user = db.relationship('User', backref='audit_logs')
    
    # Indexes for performance
    __table_args__ = (
        db.Index('idx_audit_user_timestamp', 'user_id', 'timestamp'),
        db.Index('idx_audit_resource', 'resource_type', 'resource_id'),
        db.Index('idx_audit_action_timestamp', 'action', 'timestamp'),
        db.Index('idx_audit_timestamp', 'timestamp'),
    )

class DataRetentionPolicy(db.Model):
    __tablename__ = 'data_retention_policies'
    
    id = db.Column(db.Integer, primary_key=True)
    resource_type = db.Column(db.String(50), nullable=False, unique=True)
    retention_period_days = db.Column(db.Integer, nullable=False)
    action_after_expiry = db.Column(db.String(20), default='archive')  # delete, archive, anonymize
    description = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, onupdate=datetime.utcnow)

class DataProcessingConsent(db.Model):
    __tablename__ = 'data_processing_consents'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    consent_type = db.Column(db.String(50), nullable=False)  # analytics, marketing, profiling
    granted = db.Column(db.Boolean, default=False)
    granted_at = db.Column(db.DateTime)
    revoked_at = db.Column(db.DateTime)
    ip_address = db.Column(db.String(45))
    consent_version = db.Column(db.String(20))
    legal_basis = db.Column(db.String(100))  # legitimate_interest, consent, contract
    
    # Relationships
    user = db.relationship('User', backref='data_consents')

class SecurityEvent(db.Model):
    __tablename__ = 'security_events'
    
    id = db.Column(db.Integer, primary_key=True)
    event_type = db.Column(db.String(50), nullable=False)  # failed_login, suspicious_activity
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)
    ip_address = db.Column(db.String(45), nullable=False)
    user_agent = db.Column(db.String(500))
    severity = db.Column(db.String(20), default='medium')  # low, medium, high, critical
    details = db.Column(db.JSON)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    resolved = db.Column(db.Boolean, default=False)
    resolved_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)
    resolved_at = db.Column(db.DateTime)
```

### Async Logging System

```python
# services/audit_service.py

import asyncio
from concurrent.futures import ThreadPoolExecutor
from queue import Queue
import threading
import json

class AuditService:
    def __init__(self):
        self.executor = ThreadPoolExecutor(max_workers=3, thread_name_prefix='audit')
        self.audit_queue = Queue()
        self.batch_size = 50
        self.batch_timeout = 30  # seconds
        self._start_batch_processor()
    
    def log_audit_event(self, **kwargs):
        """Add audit event to processing queue"""
        self.audit_queue.put(kwargs)
    
    def _start_batch_processor(self):
        """Start background thread for batch processing audit logs"""
        def process_batch():
            batch = []
            last_flush = time.time()
            
            while True:
                try:
                    # Get item with timeout
                    item = self.audit_queue.get(timeout=5)
                    batch.append(item)
                    
                    # Flush batch if size or time threshold reached
                    if (len(batch) >= self.batch_size or 
                        time.time() - last_flush > self.batch_timeout):
                        self._flush_batch(batch)
                        batch = []
                        last_flush = time.time()
                        
                except queue.Empty:
                    # Timeout - flush any pending items
                    if batch:
                        self._flush_batch(batch)
                        batch = []
                        last_flush = time.time()
                except Exception as e:
                    app.logger.error(f"Audit batch processor error: {e}")
        
        thread = threading.Thread(target=process_batch, daemon=True)
        thread.start()
    
    def _flush_batch(self, batch):
        """Flush batch of audit logs to database"""
        try:
            with app.app_context():
                audit_logs = []
                for item in batch:
                    audit_log = AuditLog(**item)
                    audit_logs.append(audit_log)
                
                db.session.bulk_save_objects(audit_logs)
                db.session.commit()
                
                app.logger.debug(f"Flushed {len(batch)} audit logs")
                
        except Exception as e:
            app.logger.error(f"Failed to flush audit batch: {e}")
            # Could implement retry logic here

# Global instance
audit_service = AuditService()

def create_audit_log_async(**kwargs):
    """Non-blocking audit log creation"""
    audit_service.log_audit_event(**kwargs)
```

## GDPR Compliance Features

### Data Subject Rights Implementation

```python
# blueprints/api/gdpr.py

@bp.route('/api/gdpr/data-export/<int:user_id>', methods=['POST'])
@roles_required(['admin'])
def export_user_data(user_id):
    """Export all user data for GDPR data portability"""
    user = User.query.get_or_404(user_id)
    
    # Aggregate all user data across modules
    user_data = {
        'personal_info': serialize_user_profile(user),
        'projects': serialize_user_projects(user),
        'timesheets': serialize_user_timesheets(user),
        'communications': serialize_user_communications(user),
        'audit_logs': serialize_user_audit_logs(user),
        'consents': serialize_user_consents(user)
    }
    
    # Create export file
    export_filename = f"user_data_export_{user_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    export_path = os.path.join(app.config['GDPR_EXPORT_DIR'], export_filename)
    
    with open(export_path, 'w') as f:
        json.dump(user_data, f, indent=2, default=str)
    
    # Log the export
    create_audit_log_async(
        user_id=current_user.id,
        action='export',
        resource_type='UserData',
        resource_id=user_id,
        details={'export_file': export_filename}
    )
    
    return jsonify({
        'success': True,
        'data': {
            'export_file': export_filename,
            'download_url': f'/api/gdpr/download/{export_filename}'
        }
    })

@bp.route('/api/gdpr/forget-user/<int:user_id>', methods=['DELETE'])
@roles_required(['admin'])
def forget_user(user_id):
    """Implement right to be forgotten"""
    user = User.query.get_or_404(user_id)
    
    # Create backup before deletion
    backup_data = export_user_data_internal(user)
    
    # Anonymize or delete user data based on retention policies
    anonymization_results = {
        'user_profile': anonymize_user_profile(user),
        'communications': anonymize_user_communications(user),
        'audit_logs': anonymize_user_audit_logs(user),
        'projects': handle_user_project_data(user),  # May need to keep for business purposes
        'timesheets': handle_user_timesheet_data(user)  # May need to keep for legal purposes
    }
    
    # Log the forgetting action
    create_audit_log_async(
        user_id=current_user.id,
        action='forget',
        resource_type='User',
        resource_id=user_id,
        details=anonymization_results
    )
    
    return jsonify({
        'success': True,
        'data': {
            'message': 'User data has been anonymized according to data retention policies',
            'anonymization_summary': anonymization_results
        }
    })

@bp.route('/api/gdpr/consent', methods=['POST'])
@authenticate_required
def update_consent():
    """Update user consent preferences"""
    data = request.get_json()
    consent_type = data.get('consent_type')
    granted = data.get('granted', False)
    
    # Update or create consent record
    consent = DataProcessingConsent.query.filter_by(
        user_id=current_user.id,
        consent_type=consent_type
    ).first()
    
    if consent:
        consent.granted = granted
        if granted:
            consent.granted_at = datetime.utcnow()
            consent.revoked_at = None
        else:
            consent.revoked_at = datetime.utcnow()
    else:
        consent = DataProcessingConsent(
            user_id=current_user.id,
            consent_type=consent_type,
            granted=granted,
            granted_at=datetime.utcnow() if granted else None,
            ip_address=request.remote_addr,
            consent_version='1.0'
        )
        db.session.add(consent)
    
    db.session.commit()
    
    return jsonify({
        'success': True,
        'data': {
            'consent_type': consent_type,
            'granted': granted,
            'updated_at': datetime.utcnow().isoformat()
        }
    })
```

## Security Controls

### IP-based Access Control

```python
# middleware/security_middleware.py

class IPAccessControl:
    def __init__(self, app=None):
        self.app = app
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        app.before_request(self.check_ip_access)
    
    def check_ip_access(self):
        """Check if client IP is allowed"""
        if request.endpoint in ['static', 'api.health']:
            return  # Skip check for static files and health checks
        
        client_ip = get_client_ip()
        
        # Check IP whitelist/blacklist
        if not self.is_ip_allowed(client_ip):
            self.log_security_event('ip_blocked', client_ip)
            abort(403, 'Access denied from this IP address')
    
    def is_ip_allowed(self, ip):
        """Check if IP is in allowed list"""
        # Get IP restrictions from database or config
        ip_restrictions = current_app.config.get('IP_RESTRICTIONS', {})
        
        whitelist = ip_restrictions.get('whitelist', [])
        blacklist = ip_restrictions.get('blacklist', [])
        
        # If whitelist exists, IP must be in it
        if whitelist and not any(ipaddress.ip_address(ip) in ipaddress.ip_network(allowed) 
                                for allowed in whitelist):
            return False
        
        # Check blacklist
        if blacklist and any(ipaddress.ip_address(ip) in ipaddress.ip_network(blocked) 
                            for blocked in blacklist):
            return False
        
        return True
    
    def log_security_event(self, event_type, ip):
        """Log security event"""
        security_event = SecurityEvent(
            event_type=event_type,
            ip_address=ip,
            user_agent=request.user_agent.string,
            severity='high' if event_type == 'ip_blocked' else 'medium',
            details={'endpoint': request.endpoint, 'method': request.method}
        )
        db.session.add(security_event)
        db.session.commit()

def get_client_ip():
    """Get real client IP considering proxies"""
    if request.headers.get('X-Forwarded-For'):
        return request.headers.get('X-Forwarded-For').split(',')[0].strip()
    elif request.headers.get('X-Real-IP'):
        return request.headers.get('X-Real-IP')
    else:
        return request.remote_addr
```

### Failed Login Tracking

```python
# middleware/auth_security.py

class LoginSecurityManager:
    def __init__(self):
        self.failed_attempts = {}  # Could use Redis for distributed systems
        self.lockout_threshold = 5
        self.lockout_duration = 300  # 5 minutes
    
    def track_failed_login(self, username, ip_address):
        """Track failed login attempt"""
        key = f"{username}:{ip_address}"
        current_time = time.time()
        
        if key not in self.failed_attempts:
            self.failed_attempts[key] = []
        
        # Clean old attempts
        self.failed_attempts[key] = [
            attempt for attempt in self.failed_attempts[key]
            if current_time - attempt < self.lockout_duration
        ]
        
        # Add current attempt
        self.failed_attempts[key].append(current_time)
        
        # Log security event
        self.log_failed_login(username, ip_address)
        
        # Check if account should be locked
        if len(self.failed_attempts[key]) >= self.lockout_threshold:
            self.lock_account(username, ip_address)
    
    def is_account_locked(self, username, ip_address):
        """Check if account is currently locked"""
        key = f"{username}:{ip_address}"
        current_time = time.time()
        
        if key in self.failed_attempts:
            recent_attempts = [
                attempt for attempt in self.failed_attempts[key]
                if current_time - attempt < self.lockout_duration
            ]
            return len(recent_attempts) >= self.lockout_threshold
        
        return False
    
    def clear_failed_attempts(self, username, ip_address):
        """Clear failed attempts after successful login"""
        key = f"{username}:{ip_address}"
        if key in self.failed_attempts:
            del self.failed_attempts[key]

# Integration with login route
@auth_bp.route('/login', methods=['POST'])
def login():
    data = request.get_json()
    username = data.get('username')
    password = data.get('password')
    ip_address = get_client_ip()
    
    # Check if account is locked
    if login_security.is_account_locked(username, ip_address):
        return jsonify({
            'success': False,
            'message': 'Account temporarily locked due to multiple failed attempts'
        }), 429
    
    user = User.query.filter_by(username=username).first()
    
    if user and user.check_password(password):
        # Successful login
        login_user(user)
        login_security.clear_failed_attempts(username, ip_address)
        
        # Log successful login
        create_audit_log_async(
            user_id=user.id,
            action='login',
            resource_type='Authentication',
            ip_address=ip_address
        )
        
        return jsonify({'success': True, 'user': user.to_dict()})
    else:
        # Failed login
        login_security.track_failed_login(username, ip_address)
        return jsonify({
            'success': False,
            'message': 'Invalid credentials'
        }), 401
```

## Implementation Roadmap

### Phase 1: Middleware Foundation (1-2 weeks)
**Week 1**
- [ ] Database models creation (AuditLog, SecurityEvent, DataRetentionPolicy)
- [ ] Basic Flask middleware setup
- [ ] SQLAlchemy event listeners
- [ ] Async logging service architecture

**Week 2** 
- [ ] Request/response middleware implementation
- [ ] Basic audit trail functionality
- [ ] Performance testing and optimization
- [ ] Initial security controls (IP restrictions)

### Phase 2: GDPR Compliance (2-3 weeks)
**Week 3**
- [ ] Data aggregation for export functionality
- [ ] Consent management system
- [ ] Data retention policy engine
- [ ] Right to be forgotten implementation

**Week 4**
- [ ] Data anonymization utilities
- [ ] Export/import functionality
- [ ] Compliance dashboard basic version
- [ ] API endpoints for GDPR operations

**Week 5** (if needed)
- [ ] Advanced anonymization rules
- [ ] Bulk operations for data management
- [ ] Integration testing
- [ ] Documentation and user guides

### Phase 3: Advanced Security (1-2 weeks)
**Week 6**
- [ ] Failed login tracking system
- [ ] Session security enhancements
- [ ] Two-factor authentication (optional)
- [ ] Security monitoring dashboard

**Week 7** (if needed)
- [ ] Advanced threat detection
- [ ] Security reporting
- [ ] Performance optimization
- [ ] Security audit and testing

### Phase 4: Dashboard & Reporting (1 week)
**Week 8**
- [ ] Audit log viewing interface
- [ ] Security dashboard
- [ ] Compliance reporting
- [ ] Admin security controls panel

## API Endpoints - DatPortal Blueprint Pattern

```python
# backend/blueprints/api/compliance.py
# Seguendo pattern esistente da ceo.py e admin.py

from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
from sqlalchemy import desc, and_, func
from datetime import datetime, timedelta

from models_split.compliance import AuditLog, SecurityEvent, DataProcessingConsent
from utils.api_utils import (
    api_response, handle_api_error, get_pagination_params,
    format_pagination, api_permission_required
)
from utils.permissions import PERMISSION_VIEW_COMPLIANCE
from extensions import db

# Create blueprint seguendo pattern esistente
api_compliance = Blueprint('api_compliance', __name__)

@api_compliance.route('/api/compliance/logs', methods=['GET'])
@login_required
@api_permission_required(PERMISSION_VIEW_COMPLIANCE)  # Solo admin/manager
def get_audit_logs():
    """Get audit logs with filtering - Pattern DatPortal standard"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 50, type=int)
    user_id = request.args.get('user_id', type=int)
    resource_type = request.args.get('resource_type')
    action = request.args.get('action')
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')
    
    query = AuditLog.query
    
    # Apply filters
    if user_id:
        query = query.filter(AuditLog.user_id == user_id)
    if resource_type:
        query = query.filter(AuditLog.resource_type == resource_type)
    if action:
        query = query.filter(AuditLog.action == action)
    if date_from:
        query = query.filter(AuditLog.timestamp >= datetime.fromisoformat(date_from))
    if date_to:
        query = query.filter(AuditLog.timestamp <= datetime.fromisoformat(date_to))
    
    # Order by timestamp desc
    query = query.order_by(AuditLog.timestamp.desc())
    
    # Paginate usando get_pagination_params() standard
    page, per_page = get_pagination_params()
    pagination = query.paginate(page=page, per_page=per_page, error_out=False)
    
    # Usa api_response() utility standard DatPortal
    return api_response({
        'logs': [log.to_dict() for log in pagination.items],
        'pagination': format_pagination(pagination)  # Utility DatPortal standard
    })

@api_compliance.route('/api/compliance/security-events', methods=['GET'])
@roles_required(['admin'])
def get_security_events():
    """Get security events"""
    events = SecurityEvent.query.order_by(SecurityEvent.timestamp.desc()).limit(100).all()
    return jsonify({
        'success': True,
        'data': {
            'events': [event.to_dict() for event in events]
        }
    })

@bp.route('/api/compliance/dashboard', methods=['GET'])
@roles_required(['admin'])
def get_compliance_dashboard():
    """Get compliance dashboard data"""
    # Calculate compliance metrics
    total_users = User.query.count()
    users_with_consents = db.session.query(DataProcessingConsent.user_id).distinct().count()
    recent_security_events = SecurityEvent.query.filter(
        SecurityEvent.timestamp >= datetime.utcnow() - timedelta(days=30)
    ).count()
    
    return jsonify({
        'success': True,
        'data': {
            'metrics': {
                'total_users': total_users,
                'consent_coverage': round((users_with_consents / total_users) * 100, 2) if total_users > 0 else 0,
                'recent_security_events': recent_security_events,
                'audit_logs_last_24h': AuditLog.query.filter(
                    AuditLog.timestamp >= datetime.utcnow() - timedelta(hours=24)
                ).count()
            }
        }
    })
```

## Frontend Implementation - DatPortal Vue 3 Pattern

### Pinia Store Structure
```javascript
// frontend/src/stores/compliance.js
// Seguendo pattern stores/auth.js esistente

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '@/utils/api'

export const useComplianceStore = defineStore('compliance', () => {
  // State seguendo pattern esistente
  const auditLogs = ref([])
  const securityEvents = ref([])
  const loading = ref(false)
  const error = ref(null)
  
  // Actions seguendo pattern API standard
  const fetchAuditLogs = async (filters = {}) => {
    try {
      loading.value = true
      const response = await api.get('/api/compliance/logs', { params: filters })
      
      if (response.data.success) {
        auditLogs.value = response.data.data.logs
        return response.data.data
      }
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  return {
    auditLogs,
    securityEvents,
    loading,
    error,
    fetchAuditLogs
  }
})
```

### Vue Components Structure
```vue
<!-- frontend/src/views/compliance/ComplianceDashboard.vue -->
<!-- Seguendo pattern AdminSettings.vue con tab navigation -->

<template>
  <div>
    <!-- Header seguendo pattern DatPortal -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
          Compliance & Audit
        </h1>
        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
          Gestione audit logging e compliance GDPR
        </p>
      </div>
    </div>

    <!-- Tab Navigation seguendo pattern esistente -->
    <TabContainer :tabs="complianceTabs" v-model="activeTab">
      
      <!-- Dashboard Tab -->
      <template #dashboard>
        <ComplianceDashboardContent />
      </template>
      
      <!-- Audit Logs Tab -->
      <template #logs>
        <AuditLogsTable />
      </template>
      
      <!-- Security Events Tab -->
      <template #security>
        <SecurityEventsView />
      </template>
      
    </TabContainer>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import TabContainer from '@/components/design-system/TabContainer.vue'
import { useComplianceStore } from '@/stores/compliance'

const complianceStore = useComplianceStore()
const activeTab = ref('dashboard')

const complianceTabs = [
  { id: 'dashboard', name: 'Dashboard', icon: 'chart-bar' },
  { id: 'logs', name: 'Audit Logs', icon: 'document-text' },
  { id: 'security', name: 'Security Events', icon: 'shield-check' },
  { id: 'gdpr', name: 'GDPR Tools', icon: 'lock-closed' }
]

onMounted(() => {
  complianceStore.fetchAuditLogs()
})
</script>
```

### HeroIcon Integration
- **shield-check**: Dashboard compliance, security overview
- **document-text**: Audit logs, documentation
- **exclamation-triangle**: Security alerts, warnings
- **lock-closed**: GDPR tools, data protection
- **eye**: View details, monitoring
- **download**: Export functionality

### Sidebar Navigation Update
```vue
<!-- In SidebarNavigation.vue - Aggiungere sotto Amministrazione -->
<SidebarNavItemCollapsible
  v-if="showAdminSection"
  :item="{
    name: 'Amministrazione',
    icon: 'settings',
    children: [
      { name: 'Configurazioni', path: '/app/admin/settings', icon: 'settings' },
      { name: 'Gestione Utenti', path: '/app/admin/users', icon: 'user-management' },
      { name: 'Template KPI', path: '/app/admin/kpi-templates', icon: 'reports' },
      { name: 'Compliance & Audit', path: '/app/admin/compliance', icon: 'shield-check' }  // NUOVO
    ]
  }"
  :is-collapsed="isCollapsed"
  @click="$emit('item-click')"
/>
```

### Design System Compliance
- **TailwindCSS Classes**: Usa brand colors esistenti (primary-600, gray-900, etc.)
- **Component Reuse**: FilterBar, Pagination, DataTable, StatusBadge
- **Modal System**: Seguire pattern modal esistenti per export/management
- **Toast Notifications**: useToast() composable per feedback utente

## Performance Considerations

### Database Optimization
- **Partitioning**: Partition audit_logs table by timestamp for better performance
- **Indexing**: Strategic indexes on commonly queried fields
- **Archiving**: Automatic archiving of old audit logs
- **Compression**: Use compression for audit log storage

### Async Processing
- **Queue-based**: Use Redis or database queue for audit log processing
- **Batch Processing**: Process audit logs in batches to reduce database load
- **Circuit Breaker**: Implement circuit breaker pattern for audit service failures
- **Graceful Degradation**: Continue normal operations even if audit logging fails

### Memory Management
- **Object Serialization**: Efficient serialization of large objects
- **Cleanup**: Automatic cleanup of processed audit items
- **Memory Limits**: Monitor and limit memory usage of audit processes

## Security Considerations

### Data Protection
- **Encryption**: Encrypt sensitive data in audit logs
- **Access Control**: Strict access controls on audit data
- **Data Minimization**: Only log necessary information
- **Secure Storage**: Secure storage of audit logs and exported data

### Privacy by Design
- **Anonymization**: Automatic anonymization of personal data where possible
- **Consent Tracking**: Track and respect user consent preferences
- **Data Minimization**: Collect only necessary audit information
- **Purpose Limitation**: Use audit data only for intended purposes

## Success Metrics

### Technical Metrics
- **Performance**: <100ms overhead for audit logging
- **Reliability**: 99.9% audit log success rate
- **Storage**: Efficient storage utilization for audit data
- **Compliance**: 100% coverage of required audit events

### Business Metrics
- **GDPR Readiness**: 100% compliance with GDPR requirements
- **Security Posture**: Significant reduction in security incidents
- **Audit Readiness**: Fast response to audit requests
- **Risk Mitigation**: Proactive identification of security issues

## Cost Analysis

### Development Costs
- **Backend Development**: 15-20 developer days
- **Frontend Development**: 5-10 developer days
- **Testing & QA**: 5-10 developer days
- **Documentation**: 2-3 developer days

### Operational Costs
- **Storage**: Additional database storage for audit logs
- **Processing**: Additional CPU/memory for audit processing
- **Backup**: Enhanced backup requirements for compliance data
- **Monitoring**: Additional monitoring and alerting setup

## Implementation Roadmap - DatPortal Specific

### Phase 1: Backend Foundation (5-6 giorni)
**Middleware & Models**
- [ ] `backend/middleware/audit_middleware.py`: Flask hooks integration non-invasiva
- [ ] `backend/models_split/compliance.py`: AuditLog, SecurityEvent, DataProcessingConsent models
- [ ] Database migration: `flask db migrate -m "Add compliance audit tables"`
- [ ] `backend/services/audit_service.py`: Async processing service layer

**API Blueprint**
- [ ] `backend/blueprints/api/compliance.py`: Seguendo pattern ceo.py
- [ ] Test API endpoints: `backend/tests/api/test_compliance.py`
- [ ] Permission integration: Update `utils/permissions.py` se necessario
- [ ] Blueprint registration in `app.py`

### Phase 2: Frontend Foundation (4-5 giorni)
**Pinia Store & Router**
- [ ] `frontend/src/stores/compliance.js`: State management seguendo pattern auth.js
- [ ] Router update: Aggiungere routes `/app/admin/compliance/*`
- [ ] Permission guards: Verificare `view_compliance` permission

**Base Views**
- [ ] `frontend/src/views/compliance/ComplianceDashboard.vue`: Dashboard principale
- [ ] `frontend/src/views/compliance/AuditLogs.vue`: Lista audit logs con filtri
- [ ] `frontend/src/views/compliance/SecurityEvents.vue`: Security monitoring
- [ ] Tab navigation integration: Riuso TabContainer component

### Phase 3: Sidebar & Navigation (1 giorno)
**Navigation Updates**
- [ ] `frontend/src/components/layout/SidebarNavigation.vue`: Aggiungere Compliance section
- [ ] Route guards: Verificare permissions per admin/manager access
- [ ] HeroIcon updates: shield-check, document-text, lock-closed icons

### Phase 4: Advanced Features (3-4 giorni)
**GDPR Tools**
- [ ] `frontend/src/views/compliance/GDPRManagement.vue`: Export data, consent management
- [ ] Backend API: `/api/compliance/gdpr/export`, `/api/compliance/gdpr/consent`
- [ ] Data aggregation: Cross-module user data collection
- [ ] PDF export: jsPDF integration seguendo pattern InsightsReports.vue

**Security Enhancements**
- [ ] Failed login tracking: Integration con auth.py esistente
- [ ] IP-based access control: Middleware security layer
- [ ] Security alerts: Real-time notification system

### Phase 5: Testing & Polish (2-3 giorni)
**Testing**
- [ ] Backend tests: `pytest backend/tests/api/test_compliance.py`
- [ ] Frontend tests: Component testing con Vitest
- [ ] Integration tests: E2E workflow testing
- [ ] Performance testing: Audit middleware overhead measurement

**Documentation & Deploy**
- [ ] Update CLAUDE.md: Aggiungere sezione Compliance Module
- [ ] Frontend build: `npm run build` testing
- [ ] Database migration: Production deployment checklist

### File Structure Preview
```
backend/
├── middleware/
│   ├── audit_middleware.py (NEW)
│   └── security_middleware.py (NEW)
├── models_split/
│   └── compliance.py (NEW)
├── blueprints/api/
│   └── compliance.py (NEW)
├── services/
│   └── audit_service.py (NEW)
└── tests/api/
    └── test_compliance.py (NEW)

frontend/
├── src/
│   ├── stores/
│   │   └── compliance.js (NEW)
│   ├── views/compliance/ (NEW FOLDER)
│   │   ├── ComplianceDashboard.vue
│   │   ├── AuditLogs.vue
│   │   ├── SecurityEvents.vue
│   │   └── GDPRManagement.vue
│   └── components/layout/
│       └── SidebarNavigation.vue (UPDATE)
```

### Success Criteria
- **Backend**: 100% API coverage con test, <100ms middleware overhead
- **Frontend**: Compliance dashboard funzionale con design system coerente
- **Integration**: Seamless navigation da sidebar, permission enforcement
- **GDPR**: Export completo user data, consent management operativo

## Risk Assessment

### Implementation Risks
- **Performance Impact**: Mitigation through async processing and optimization
- **Storage Growth**: Mitigation through archiving and retention policies
- **Complexity**: Mitigation through phased implementation
- **Data Quality**: Mitigation through comprehensive testing

### Compliance Risks
- **Regulatory Changes**: Stay updated with GDPR and privacy law changes
- **Data Breaches**: Implement strong security measures for audit data
- **False Positives**: Fine-tune security event detection to reduce noise
- **User Trust**: Transparent communication about data collection and use

## Conclusion - DatPortal Enterprise Readiness

L'implementazione del sistema di Compliance e Audit Logging per **DatPortal** seguendo rigorosamente i pattern architetturali esistenti garantisce:

### 🎯 Alignment Perfetto
1. **Zero Disruption**: Middleware non-invasivo senza modifiche a modelli/API esistenti
2. **Pattern Consistency**: Flask Blueprints + Vue 3 + Pinia + HeroIcon system
3. **Permission Integration**: `PERMISSION_VIEW_COMPLIANCE` già esistente per admin/manager
4. **Design System**: TailwindCSS brand colors e componenti riutilizzabili

### 🚀 Enterprise Features
1. **GDPR Compliance**: Export completo dati utente, consent management, right to be forgotten
2. **Security Enhancement**: Failed login tracking, IP restrictions, security monitoring
3. **Audit Trail**: Tracciabilità completa azioni utente cross-module
4. **Performance Optimized**: Async logging, minimal overhead (<100ms)

### 🏢 PMI Italian Market Ready
1. **Compliance GDPR**: Specifico per normative italiane e PMI requirements
2. **Scalabilità**: Pronto per crescita aziendale da startup a enterprise
3. **User Experience**: Interfaccia italiana coerente con design system DatPortal
4. **Integration**: Seamless con moduli esistenti (CEO, Personnel, Projects, CRM)

### 📈 Strategic Impact
Il modulo Compliance posiziona **DatPortal** come:
- **Enterprise-Grade Solution** per PMI innovative italiane
- **GDPR-Compliant Platform** pronta per audit e certificazioni
- **Security-First Intranet** con controlli avanzati e monitoring
- **Future-Proof Architecture** per scalabilità e nuove funzionalità

Il sistema fornisce una **base solida enterprise** mantenendo la **semplicità d'uso** caratteristica di DatPortal, rendendo la compliance un vantaggio competitivo piuttosto che un overhead operativo.