# 🚀 Test Execution Guide

Guida completa per l'esecuzione dei test e l'interpretazione dei risultati.

## 📋 Indice

- [Comandi di Esecuzione](#comandi-di-esecuzione)
- [Script Automatizzati](#script-automatizzati)
- [Coverage Reports](#coverage-reports)
- [CI/CD Integration](#cicd-integration)
- [Debugging Tests](#debugging-tests)
- [Performance Monitoring](#performance-monitoring)
- [Troubleshooting](#troubleshooting)

## ⚡ Comandi di Esecuzione

### Backend Tests

```bash
# Tutti i test backend
cd backend && python -m pytest tests/ -v

# Solo unit tests
python -m pytest tests/unit/ -v

# Solo integration tests
python -m pytest tests/integration/ -v

# Solo API tests
python -m pytest tests/api/ -v

# Con coverage
python -m pytest tests/ --cov=. --cov-report=html --cov-report=term

# Test specifico
python -m pytest tests/unit/test_models.py::TestProjectModel::test_budget_calculation -v

# Test con markers
python -m pytest -m "slow" -v  # Solo test lenti
python -m pytest -m "not slow" -v  # Escludi test lenti

# Parallel execution
python -m pytest tests/ -n auto  # Usa tutti i core disponibili
python -m pytest tests/ -n 4     # Usa 4 processi
```

### Frontend Tests

```bash
# Tutti i test frontend
cd frontend && npm run test

# Test specifici
npm run test:unit          # Solo component tests
npm run test:integration   # Solo integration tests
npm run test:e2e          # Solo E2E tests

# Watch mode per sviluppo
npm run test:watch

# Coverage
npm run test:coverage

# Test specifico file
npm run test ProjectTeam.test.js

# Test con pattern
npm run test -- --grep "should handle form submission"

# Debug mode
npm run test:debug
```

### E2E Tests

```bash
# Cypress headless
cd frontend && npx cypress run

# Cypress interactive
npx cypress open

# Specific spec
npx cypress run --spec "cypress/e2e/projects/project-creation.cy.js"

# Specific browser
npx cypress run --browser chrome
npx cypress run --browser firefox

# With video recording
npx cypress run --record --key <record-key>

# Parallel execution
npx cypress run --parallel --record --key <record-key>
```

## 🤖 Script Automatizzati

### Script Esecuzione Completa

```bash
#!/bin/bash
# scripts/run-all-tests.sh

set -e

echo "🚀 AVVIO TEST SUITE COMPLETA"
echo "=================================="

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Variables
BACKEND_DIR="backend"
FRONTEND_DIR="frontend"
TEST_RESULTS_DIR="test-results"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

mkdir -p $TEST_RESULTS_DIR

# 1. BACKEND TESTS
print_status "🔧 ESECUZIONE BACKEND TESTS"
cd $BACKEND_DIR

# Setup test database
print_status "Setup database di test..."
python -c "
from app import create_app
from extensions import db
app = create_app()
with app.app_context():
    db.create_all()
    print('✅ Database di test creato')
"

# Run tests with coverage
print_status "Esecuzione backend tests..."
python -m pytest tests/ -v \
    --cov=. \
    --cov-report=html:../$TEST_RESULTS_DIR/backend-coverage-$TIMESTAMP \
    --cov-report=term-missing \
    --junitxml=../$TEST_RESULTS_DIR/backend-results-$TIMESTAMP.xml

print_success "Backend tests completati"
cd ..

# 2. FRONTEND TESTS
print_status "🎨 ESECUZIONE FRONTEND TESTS"
cd $FRONTEND_DIR

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    print_status "Installazione dipendenze..."
    npm install
fi

# Run unit and integration tests
print_status "Esecuzione frontend tests..."
npm run test:coverage -- \
    --reporter=junit \
    --outputFile=../$TEST_RESULTS_DIR/frontend-results-$TIMESTAMP.xml

print_success "Frontend tests completati"

# 3. E2E TESTS
print_status "🌐 ESECUZIONE E2E TESTS"

# Start backend server
print_status "Avvio backend server..."
cd ../$BACKEND_DIR
python main.py &
BACKEND_PID=$!
sleep 5

cd ../$FRONTEND_DIR

# Start frontend dev server
print_status "Avvio frontend dev server..."
npm run dev &
FRONTEND_PID=$!
sleep 10

# Run E2E tests
print_status "Esecuzione E2E tests..."
npx cypress run \
    --reporter junit \
    --reporter-options "mochaFile=../$TEST_RESULTS_DIR/e2e-results-$TIMESTAMP.xml"

# Cleanup
print_status "Cleanup servers..."
kill $BACKEND_PID 2>/dev/null || true
kill $FRONTEND_PID 2>/dev/null || true

cd ..

# 4. GENERATE REPORT
print_status "📊 GENERAZIONE REPORT FINALE"
./scripts/generate-test-report.sh $TIMESTAMP

print_success "🎉 TEST SUITE COMPLETA ESEGUITA!"
print_status "📊 Risultati in: $TEST_RESULTS_DIR/"
```

### Script Generazione Report

```bash
#!/bin/bash
# scripts/generate-test-report.sh

TIMESTAMP=$1
TEST_RESULTS_DIR="test-results"

# Extract test counts from XML reports
BACKEND_TESTS=$(grep -o 'tests="[0-9]*"' $TEST_RESULTS_DIR/backend-results-$TIMESTAMP.xml | grep -o '[0-9]*' || echo "0")
FRONTEND_TESTS=$(grep -o 'tests="[0-9]*"' $TEST_RESULTS_DIR/frontend-results-$TIMESTAMP.xml | grep -o '[0-9]*' || echo "0")
E2E_TESTS=$(grep -o 'tests="[0-9]*"' $TEST_RESULTS_DIR/e2e-results-$TIMESTAMP.xml | grep -o '[0-9]*' || echo "0")

TOTAL_TESTS=$((BACKEND_TESTS + FRONTEND_TESTS + E2E_TESTS))

# Generate HTML report
cat > $TEST_RESULTS_DIR/test-report-$TIMESTAMP.html << EOF
<!DOCTYPE html>
<html>
<head>
    <title>Test Execution Report - $TIMESTAMP</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .header { background: #f8f9fa; padding: 20px; border-radius: 8px; }
        .stats { display: flex; gap: 20px; margin: 20px 0; }
        .stat-card { background: #e3f2fd; padding: 15px; border-radius: 8px; flex: 1; }
        .success { background: #e8f5e8; }
        .warning { background: #fff3cd; }
        .error { background: #f8d7da; }
        .coverage { margin: 20px 0; }
        .coverage-bar { background: #ddd; height: 20px; border-radius: 10px; overflow: hidden; }
        .coverage-fill { background: #4caf50; height: 100%; transition: width 0.3s; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧪 Test Execution Report</h1>
        <p><strong>Timestamp:</strong> $TIMESTAMP</p>
        <p><strong>Total Tests:</strong> $TOTAL_TESTS</p>
    </div>
    
    <div class="stats">
        <div class="stat-card success">
            <h3>Backend Tests</h3>
            <p><strong>$BACKEND_TESTS</strong> tests executed</p>
            <p>✅ Unit, Integration, API</p>
        </div>
        <div class="stat-card success">
            <h3>Frontend Tests</h3>
            <p><strong>$FRONTEND_TESTS</strong> tests executed</p>
            <p>✅ Component, Integration, UI</p>
        </div>
        <div class="stat-card success">
            <h3>E2E Tests</h3>
            <p><strong>$E2E_TESTS</strong> tests executed</p>
            <p>✅ User Workflows</p>
        </div>
    </div>
    
    <div class="coverage">
        <h3>📊 Coverage Reports</h3>
        <p><a href="backend-coverage-$TIMESTAMP/index.html">Backend Coverage Report</a></p>
        <p><a href="frontend-coverage-$TIMESTAMP/index.html">Frontend Coverage Report</a></p>
    </div>
    
    <div>
        <h3>📁 Test Artifacts</h3>
        <ul>
            <li><a href="backend-results-$TIMESTAMP.xml">Backend JUnit Results</a></li>
            <li><a href="frontend-results-$TIMESTAMP.xml">Frontend JUnit Results</a></li>
            <li><a href="e2e-results-$TIMESTAMP.xml">E2E JUnit Results</a></li>
        </ul>
    </div>
    
    <div>
        <h3>🎯 Validated Use Cases</h3>
        <ul>
            <li>✅ Project Management (Creation, Editing, Team, Tasks)</li>
            <li>✅ User Authentication & Authorization</li>
            <li>✅ Timesheet Management & Approval</li>
            <li>✅ Financial Tracking & KPIs</li>
            <li>✅ Team Collaboration & Communication</li>
            <li>✅ Dashboard & Analytics</li>
        </ul>
    </div>
</body>
</html>
EOF

echo "📊 Report HTML generato: $TEST_RESULTS_DIR/test-report-$TIMESTAMP.html"
```

## 📊 Coverage Reports

### Interpretazione Coverage Backend

```bash
# Genera coverage dettagliato
python -m pytest --cov=. --cov-report=html --cov-report=term-missing

# Coverage per modulo
python -m pytest --cov=models --cov-report=term
python -m pytest --cov=services --cov-report=term
python -m pytest --cov=blueprints --cov-report=term

# Coverage con soglie
python -m pytest --cov=. --cov-fail-under=80

# Coverage escludendo file
python -m pytest --cov=. --cov-omit="*/tests/*,*/migrations/*"
```

### Interpretazione Coverage Frontend

```bash
# Coverage dettagliato
npm run test:coverage

# Coverage per directory
npm run test:coverage -- --include="src/components/**"
npm run test:coverage -- --include="src/stores/**"

# Coverage con soglie
npm run test:coverage -- --coverage.thresholds.global.lines=70

# Report in formati diversi
npm run test:coverage -- --coverage.reporter=html
npm run test:coverage -- --coverage.reporter=lcov
npm run test:coverage -- --coverage.reporter=json
```

## 🔧 CI/CD Integration

### GitHub Actions Workflow

```yaml
# .github/workflows/tests.yml
name: Test Suite
on: 
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  backend-tests:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: 3.9
    
    - name: Install dependencies
      run: |
        cd backend
        pip install -r requirements.txt
        pip install pytest pytest-cov
    
    - name: Run backend tests
      run: |
        cd backend
        python -m pytest tests/ --cov=. --cov-report=xml
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v1
      with:
        file: backend/coverage.xml
        flags: backend

  frontend-tests:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Set up Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: Install dependencies
      run: |
        cd frontend
        npm ci
    
    - name: Run frontend tests
      run: |
        cd frontend
        npm run test:coverage
    
    - name: Upload coverage
      uses: codecov/codecov-action@v1
      with:
        file: frontend/coverage/lcov.info
        flags: frontend

  e2e-tests:
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests]
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Set up environment
      run: |
        # Setup backend and frontend
        cd backend && pip install -r requirements.txt &
        cd frontend && npm ci &
        wait
    
    - name: Start services
      run: |
        cd backend && python main.py &
        cd frontend && npm run dev &
        sleep 30
    
    - name: Run E2E tests
      run: |
        cd frontend
        npx cypress run --record --key ${{ secrets.CYPRESS_RECORD_KEY }}
    
    - name: Upload test artifacts
      uses: actions/upload-artifact@v2
      if: failure()
      with:
        name: cypress-screenshots
        path: frontend/cypress/screenshots
```

## 🐛 Debugging Tests

### Debug Backend Tests

```bash
# Debug con pdb
python -m pytest tests/unit/test_models.py::test_function -s --pdb

# Debug con logging
python -m pytest tests/ -s --log-cli-level=DEBUG

# Debug test specifico
python -m pytest tests/unit/test_models.py::TestProjectModel::test_budget -vvv -s

# Debug con breakpoint
# Nel codice: import pdb; pdb.set_trace()
python -m pytest tests/ -s
```

### Debug Frontend Tests

```bash
# Debug mode Vitest
npm run test:debug

# Debug specifico test
npm run test -- --reporter=verbose ProjectTeam.test.js

# Debug con browser
npm run test -- --ui

# Debug con console.log
# Nel test: console.log(wrapper.html())
npm run test -- --reporter=verbose
```

### Debug E2E Tests

```bash
# Debug mode Cypress
npx cypress open

# Debug con video
npx cypress run --record

# Debug con screenshots
npx cypress run --screenshot

# Debug specifico test
npx cypress run --spec "cypress/e2e/projects/project-creation.cy.js" --headed

# Debug con browser tools
# Nel test: cy.debug() o cy.pause()
```

## 📈 Performance Monitoring

### Metriche Test Performance

```bash
# Tempo esecuzione backend
python -m pytest tests/ --durations=10

# Tempo esecuzione frontend
npm run test -- --reporter=verbose --run

# Tempo esecuzione E2E
npx cypress run --reporter json > cypress-results.json

# Analisi performance
./scripts/analyze-test-performance.sh
```

### Ottimizzazione Test

```bash
# Parallel execution backend
python -m pytest tests/ -n auto

# Parallel execution frontend
npm run test -- --threads

# Parallel execution E2E
npx cypress run --parallel --record

# Cache dependencies
npm ci --cache .npm
pip install --cache-dir .pip-cache
```

## 🔧 Troubleshooting

### Problemi Comuni Backend

```bash
# Database connection issues
export DATABASE_URL="postgresql://user:pass@localhost/test_db"

# Import path issues
export PYTHONPATH="${PYTHONPATH}:$(pwd)/backend"

# Permission issues
chmod +x scripts/*.sh

# Clean cache
find . -name "*.pyc" -delete
find . -name "__pycache__" -delete
```

### Problemi Comuni Frontend

```bash
# Node modules issues
rm -rf node_modules package-lock.json
npm install

# Cache issues
npm run test -- --clearCache

# Memory issues
npm run test -- --maxWorkers=2

# Port conflicts
lsof -ti:3000 | xargs kill -9
```

### Problemi Comuni E2E

```bash
# Browser issues
npx cypress verify
npx cypress info

# Video/screenshot issues
rm -rf cypress/videos cypress/screenshots

# Network issues
npx cypress run --config defaultCommandTimeout=10000

# Headless issues
npx cypress run --headed --no-exit
```

---

*Guida aggiornata: 2025-01-26*
