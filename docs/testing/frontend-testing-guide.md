# 🎨 Frontend Testing Guide

Guida completa per il testing del frontend Vue.js con Vitest, Vue Test Utils e Cypress.

## 📋 Indice

- [Setup e Configurazione](#setup-e-configurazione)
- [Component Testing](#component-testing)
- [Integration Testing](#integration-testing)
- [UI Interaction Testing](#ui-interaction-testing)
- [End-to-End Testing](#end-to-end-testing)
- [Mock Strategies](#mock-strategies)
- [Best Practices](#best-practices)

## ⚙️ Setup e Configurazione

### Struttura Directory

```
frontend/src/test/
├── setup.js                    # Setup globale Vitest
├── components/
│   ├── ProjectTeam.test.js     # Test componente team
│   ├── Dashboard.test.js       # Test dashboard
│   └── ProjectEdit.test.js     # Test form progetto
├── integration/
│   ├── project-api-integration.test.js  # Test API integration
│   ├── store-integration.test.js        # Test store integration
│   └── routing-integration.test.js      # Test routing
├── ui/
│   ├── user-interactions.test.js        # Test interazioni utente
│   ├── form-validation.test.js          # Test validazione form
│   └── navigation.test.js               # Test navigazione
├── mocks/
│   ├── api-handlers.js         # Mock API con MSW
│   ├── server.js              # Setup MSW server
│   └── store-mocks.js         # Mock store Pinia
├── fixtures/
│   ├── projects.json          # Dati progetti di test
│   ├── users.json            # Dati utenti di test
│   └── dashboard.json        # Dati dashboard di test
└── utils/
    └── test-helpers.js       # Utility per test
```

### Configurazione Vitest

```javascript
// vitest.config.js
import { defineConfig } from 'vitest/config'
import vue from '@vitejs/plugin-vue'
import { fileURLToPath, URL } from 'node:url'

export default defineConfig({
  plugins: [vue()],
  test: {
    environment: 'jsdom',
    globals: true,
    setupFiles: ['./src/test/setup.js'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'src/test/',
        'cypress/',
        'dist/'
      ],
      thresholds: {
        global: {
          branches: 70,
          functions: 70,
          lines: 70,
          statements: 70
        }
      }
    }
  },
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  }
})
```

### Setup Globale

```javascript
// src/test/setup.js
import { vi } from 'vitest'
import { config } from '@vue/test-utils'
import { createPinia } from 'pinia'

// Mock global objects
global.ResizeObserver = vi.fn(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

global.fetch = vi.fn()

// Configure Vue Test Utils
config.global.plugins = [createPinia()]
config.global.stubs = {
  'router-link': { template: '<a><slot /></a>' },
  'router-view': { template: '<div><slot /></div>' },
  'HeroIcon': { template: '<svg><slot /></svg>' }
}

// Global test utilities
global.testUtils = {
  waitForUpdate: async (wrapper) => {
    await wrapper.vm.$nextTick()
    await new Promise(resolve => setTimeout(resolve, 0))
  },
  
  fillForm: async (wrapper, formData) => {
    for (const [field, value] of Object.entries(formData)) {
      const input = wrapper.find(`[data-testid="${field}"]`)
      if (input.exists()) {
        await input.setValue(value)
      }
    }
  },
  
  mockApiResponse: (data, status = 200) => ({
    ok: status >= 200 && status < 300,
    status,
    json: () => Promise.resolve(data)
  })
}
```

## 🧩 Component Testing

### Test Componenti Base

```javascript
// tests/components/ProjectTeam.test.js
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia } from 'pinia'
import ProjectTeam from '@/views/projects/components/ProjectTeam.vue'

describe('ProjectTeam Component', () => {
  let wrapper
  let mockProject

  beforeEach(() => {
    mockProject = {
      id: 1,
      name: 'Test Project',
      team_members: [
        {
          id: 1,
          full_name: 'John Doe',
          role: 'Project Manager',
          allocation_percentage: 50,
          hours_worked: 40
        },
        {
          id: 2,
          full_name: 'Jane Smith',
          role: 'Developer',
          allocation_percentage: 100,
          hours_worked: 80
        }
      ]
    }
    
    global.fetch = vi.fn()
  })

  describe('Rendering', () => {
    it('should render team members correctly', () => {
      wrapper = mount(ProjectTeam, {
        props: { project: mockProject },
        global: { plugins: [createPinia()] }
      })

      expect(wrapper.text()).toContain('John Doe')
      expect(wrapper.text()).toContain('Jane Smith')
      expect(wrapper.text()).toContain('Project Manager')
      expect(wrapper.text()).toContain('Developer')
    })

    it('should display team statistics', () => {
      wrapper = mount(ProjectTeam, {
        props: { project: mockProject },
        global: { plugins: [createPinia()] }
      })

      // Total hours: 40 + 80 = 120
      expect(wrapper.vm.totalHoursWorked).toBe(120)
      // Average: 120 / 2 = 60
      expect(wrapper.vm.averageHoursPerMember).toBe(60)
      // Active members: 2
      expect(wrapper.vm.activeMembersCount).toBe(2)
    })

    it('should show empty state when no team members', () => {
      const emptyProject = { ...mockProject, team_members: [] }
      
      wrapper = mount(ProjectTeam, {
        props: { project: emptyProject },
        global: { plugins: [createPinia()] }
      })

      expect(wrapper.find('[data-testid="empty-team-state"]').exists()).toBe(true)
      expect(wrapper.text()).toContain('No team members')
    })
  })

  describe('User Interactions', () => {
    beforeEach(() => {
      wrapper = mount(ProjectTeam, {
        props: { project: mockProject },
        global: { plugins: [createPinia()] }
      })
    })

    it('should open add member modal', async () => {
      await wrapper.find('[data-testid="add-member-button"]').trigger('click')

      expect(wrapper.vm.showAddMemberModal).toBe(true)
      expect(wrapper.find('[data-testid="add-member-modal"]').exists()).toBe(true)
    })

    it('should handle form submission', async () => {
      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ success: true })
      })

      wrapper.vm.newMemberForm = {
        user_id: '3',
        role: 'QA Tester',
        allocation_percentage: 75
      }

      await wrapper.vm.addMember()

      expect(fetch).toHaveBeenCalledWith(`/api/projects/${mockProject.id}/team`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': expect.any(String)
        },
        body: JSON.stringify({
          user_id: '3',
          role: 'QA Tester',
          allocation_percentage: 75
        })
      })
    })

    it('should handle API errors', async () => {
      fetch.mockRejectedValueOnce(new Error('Network error'))
      const alertSpy = vi.spyOn(window, 'alert').mockImplementation(() => {})

      await wrapper.vm.addMember()

      expect(alertSpy).toHaveBeenCalledWith('Errore nell\'aggiunta del membro')
      alertSpy.mockRestore()
    })
  })

  describe('Props and Events', () => {
    it('should handle loading prop', () => {
      wrapper = mount(ProjectTeam, {
        props: { 
          project: mockProject,
          loading: true 
        },
        global: { plugins: [createPinia()] }
      })

      expect(wrapper.find('[data-testid="loading-spinner"]').exists()).toBe(true)
    })

    it('should emit refresh event', async () => {
      wrapper = mount(ProjectTeam, {
        props: { project: mockProject },
        global: { plugins: [createPinia()] }
      })

      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ success: true })
      })

      await wrapper.vm.addMember()

      expect(wrapper.emitted('refresh')).toBeTruthy()
    })
  })
})
```

### Test Form Components

```javascript
// tests/components/ProjectEdit.test.js
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import ProjectEdit from '@/views/projects/ProjectEdit.vue'

describe('ProjectEdit Component', () => {
  let wrapper

  beforeEach(() => {
    wrapper = mount(ProjectEdit, {
      global: {
        plugins: [createPinia()],
        mocks: {
          $router: { push: vi.fn() },
          $route: { params: { id: 'new' } }
        }
      }
    })
  })

  describe('Form Validation', () => {
    it('should validate required fields', async () => {
      // Submit empty form
      await wrapper.find('[data-testid="save-button"]').trigger('click')

      expect(wrapper.find('[data-testid="name-error"]').text()).toContain('required')
      expect(wrapper.find('[data-testid="budget-error"]').text()).toContain('required')
    })

    it('should validate field formats', async () => {
      await wrapper.find('[data-testid="project-budget"]').setValue('-1000')
      await wrapper.find('[data-testid="project-budget"]').trigger('blur')

      expect(wrapper.find('[data-testid="budget-error"]').text()).toContain('positive')
    })

    it('should validate date ranges', async () => {
      await wrapper.find('[data-testid="project-start-date"]').setValue('2025-12-31')
      await wrapper.find('[data-testid="project-end-date"]').setValue('2025-01-01')
      await wrapper.find('[data-testid="project-end-date"]').trigger('blur')

      expect(wrapper.find('[data-testid="end-date-error"]').text()).toContain('after start date')
    })
  })

  describe('Form Submission', () => {
    it('should submit valid form', async () => {
      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ success: true, data: { id: 1 } })
      })

      await global.testUtils.fillForm(wrapper, {
        'project-name': 'Test Project',
        'project-description': 'Test description',
        'project-budget': '25000'
      })

      await wrapper.find('[data-testid="save-button"]').trigger('click')

      expect(fetch).toHaveBeenCalledWith('/api/projects', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: 'Test Project',
          description: 'Test description',
          budget: 25000
        })
      })
    })

    it('should handle submission errors', async () => {
      fetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        json: () => Promise.resolve({
          success: false,
          errors: { name: ['Name already exists'] }
        })
      })

      await wrapper.find('[data-testid="save-button"]').trigger('click')

      expect(wrapper.find('[data-testid="name-error"]').text()).toContain('already exists')
    })
  })
})
```

## 🔗 Integration Testing

### Test Store Integration

```javascript
// tests/integration/store-integration.test.js
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { useProjectsStore } from '@/stores/projects'

describe('Projects Store Integration', () => {
  let projectsStore

  beforeEach(() => {
    setActivePinia(createPinia())
    projectsStore = useProjectsStore()
    global.fetch = vi.fn()
  })

  describe('API Integration', () => {
    it('should fetch projects from API', async () => {
      const mockProjects = [
        { id: 1, name: 'Project 1' },
        { id: 2, name: 'Project 2' }
      ]

      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          data: { projects: mockProjects }
        })
      })

      await projectsStore.fetchProjects()

      expect(projectsStore.projects).toEqual(mockProjects)
      expect(projectsStore.loading).toBe(false)
      expect(projectsStore.error).toBeNull()
    })

    it('should handle API errors', async () => {
      fetch.mockRejectedValueOnce(new Error('Network error'))

      await projectsStore.fetchProjects()

      expect(projectsStore.projects).toEqual([])
      expect(projectsStore.loading).toBe(false)
      expect(projectsStore.error).toBeTruthy()
    })

    it('should create project via API', async () => {
      const newProject = { name: 'New Project', budget: 10000 }

      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          data: { ...newProject, id: 3 }
        })
      })

      const result = await projectsStore.createProject(newProject)

      expect(result.success).toBe(true)
      expect(fetch).toHaveBeenCalledWith('/api/projects', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newProject)
      })
    })
  })

  describe('State Management', () => {
    it('should update loading states correctly', async () => {
      expect(projectsStore.loading).toBe(false)

      const fetchPromise = projectsStore.fetchProjects()
      expect(projectsStore.loading).toBe(true)

      await fetchPromise
      expect(projectsStore.loading).toBe(false)
    })

    it('should filter projects correctly', () => {
      projectsStore.projects = [
        { id: 1, name: 'Alpha Project', status: 'active' },
        { id: 2, name: 'Beta Project', status: 'planning' },
        { id: 3, name: 'Gamma Task', status: 'active' }
      ]

      // Filter by status
      const activeProjects = projectsStore.getProjectsByStatus('active')
      expect(activeProjects).toHaveLength(2)

      // Search by name
      const searchResults = projectsStore.searchProjects('Project')
      expect(searchResults).toHaveLength(2)
      expect(searchResults.every(p => p.name.includes('Project'))).toBe(true)
    })
  })
})
```

## 🖱️ UI Interaction Testing

### Test User Interactions

```javascript
// tests/ui/user-interactions.test.js
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { nextTick } from 'vue'
import Dashboard from '@/views/Dashboard.vue'

describe('User Interface Interactions', () => {
  let wrapper

  beforeEach(() => {
    wrapper = mount(Dashboard, {
      global: {
        plugins: [createPinia()],
        mocks: { $router: { push: vi.fn() } }
      }
    })
  })

  describe('Navigation', () => {
    it('should handle tab navigation', async () => {
      expect(wrapper.vm.activeTab).toBe('overview')

      await wrapper.find('[data-testid="projects-tab"]').trigger('click')

      expect(wrapper.vm.activeTab).toBe('projects')
      expect(wrapper.find('[data-testid="projects-tab"]').classes()).toContain('active')
    })

    it('should update URL hash on tab change', async () => {
      await wrapper.find('[data-testid="timesheet-tab"]').trigger('click')

      expect(window.location.hash).toBe('#timesheet')
    })
  })

  describe('Modal Interactions', () => {
    it('should open and close modals', async () => {
      expect(wrapper.vm.showModal).toBe(false)

      await wrapper.find('[data-testid="open-modal-button"]').trigger('click')
      expect(wrapper.vm.showModal).toBe(true)

      await wrapper.find('[data-testid="close-modal-button"]').trigger('click')
      expect(wrapper.vm.showModal).toBe(false)
    })

    it('should close modal on escape key', async () => {
      wrapper.vm.showModal = true
      await nextTick()

      await wrapper.trigger('keydown', { key: 'Escape' })
      expect(wrapper.vm.showModal).toBe(false)
    })
  })

  describe('Dynamic Updates', () => {
    it('should update UI when data changes', async () => {
      expect(wrapper.find('[data-testid="no-data"]').exists()).toBe(true)

      wrapper.vm.dashboardData = {
        totalProjects: 5,
        activeProjects: 3
      }
      await nextTick()

      expect(wrapper.find('[data-testid="no-data"]').exists()).toBe(false)
      expect(wrapper.text()).toContain('5')
      expect(wrapper.text()).toContain('3')
    })
  })
})
```

## 🌐 End-to-End Testing

### Test Workflow Completi

```javascript
// cypress/e2e/project-workflows.cy.js
describe('Project Management Workflows', () => {
  beforeEach(() => {
    cy.login('admin', 'password')
    cy.intercept('GET', '/api/projects*', { fixture: 'projects.json' }).as('getProjects')
  })

  it('should create complete project with team and tasks', () => {
    // Navigate to projects
    cy.visit('/app/projects')
    cy.wait('@getProjects')
    
    // Create project
    cy.get('[data-testid="create-project-button"]').click()
    cy.get('[data-testid="project-name"]').type('E2E Test Project')
    cy.get('[data-testid="project-budget"]').type('50000')
    cy.get('[data-testid="save-button"]').click()
    
    // Verify creation
    cy.get('[data-testid="success-message"]').should('contain', 'created')
    cy.url().should('include', '/projects/')
    
    // Add team member
    cy.get('[data-testid="team-tab"]').click()
    cy.get('[data-testid="add-member-button"]').click()
    cy.get('[data-testid="user-select"]').select('John Doe')
    cy.get('[data-testid="role-input"]').type('Developer')
    cy.get('[data-testid="save-member-button"]').click()
    
    // Verify team member
    cy.get('[data-testid="team-member-list"]').should('contain', 'John Doe')
    
    // Add task
    cy.get('[data-testid="tasks-tab"]').click()
    cy.get('[data-testid="add-task-button"]').click()
    cy.get('[data-testid="task-title"]').type('Setup Environment')
    cy.get('[data-testid="task-assignee"]').select('John Doe')
    cy.get('[data-testid="save-task-button"]').click()
    
    // Verify task
    cy.get('[data-testid="task-list"]').should('contain', 'Setup Environment')
  })

  it('should handle project status transitions', () => {
    cy.visit('/app/projects/1')
    
    // Change status
    cy.get('[data-testid="status-dropdown"]').click()
    cy.get('[data-testid="status-active"]').click()
    cy.get('[data-testid="confirm-status-change"]').click()
    
    // Verify status change
    cy.get('[data-testid="project-status"]').should('contain', 'Active')
    cy.get('[data-testid="success-message"]').should('contain', 'updated')
  })
})

// Custom commands
Cypress.Commands.add('login', (username, password) => {
  cy.session([username, password], () => {
    cy.visit('/login')
    cy.get('[data-testid="username"]').type(username)
    cy.get('[data-testid="password"]').type(password)
    cy.get('[data-testid="login-button"]').click()
    cy.url().should('include', '/app/dashboard')
  })
})
```

## 🎭 Mock Strategies

### MSW API Mocking

```javascript
// tests/mocks/api-handlers.js
import { rest } from 'msw'

export const handlers = [
  // Projects
  rest.get('/api/projects', (req, res, ctx) => {
    const search = req.url.searchParams.get('search')
    let projects = mockProjects
    
    if (search) {
      projects = projects.filter(p => 
        p.name.toLowerCase().includes(search.toLowerCase())
      )
    }
    
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        data: { projects, pagination: { total: projects.length } }
      })
    )
  }),

  rest.post('/api/projects', (req, res, ctx) => {
    const projectData = req.body
    
    if (!projectData.name) {
      return res(
        ctx.status(400),
        ctx.json({
          success: false,
          errors: { name: ['Project name is required'] }
        })
      )
    }
    
    const newProject = {
      id: Date.now(),
      ...projectData,
      created_at: new Date().toISOString()
    }
    
    return res(
      ctx.status(201),
      ctx.json({ success: true, data: newProject })
    )
  }),

  // Error simulation
  rest.get('/api/error-test', (req, res, ctx) => {
    return res(
      ctx.status(500),
      ctx.json({ success: false, error: 'Internal server error' })
    )
  })
]
```

### Store Mocking

```javascript
// tests/mocks/store-mocks.js
import { vi } from 'vitest'

export const mockProjectsStore = {
  projects: [],
  loading: false,
  error: null,
  fetchProjects: vi.fn(),
  createProject: vi.fn(),
  updateProject: vi.fn(),
  deleteProject: vi.fn()
}

export const mockAuthStore = {
  user: { id: 1, username: 'testuser', role: 'admin' },
  isAuthenticated: true,
  csrfToken: 'mock-csrf-token',
  login: vi.fn(),
  logout: vi.fn(),
  hasPermission: vi.fn(() => true)
}
```

## 📚 Best Practices

### Test Organization

```javascript
// ✅ Buono: Struttura chiara
describe('ProjectTeam Component', () => {
  describe('Rendering', () => {
    it('should display team members correctly', () => {})
    it('should show empty state when no members', () => {})
  })
  
  describe('User Interactions', () => {
    it('should open add member modal', () => {})
    it('should handle form submission', () => {})
  })
  
  describe('API Integration', () => {
    it('should fetch team data from API', () => {})
    it('should handle API errors', () => {})
  })
})
```

### Data-testid Usage

```vue
<!-- ✅ Buono: Usa data-testid per elementi testabili -->
<template>
  <div>
    <button data-testid="add-member-button" @click="openModal">
      Add Member
    </button>
    <div data-testid="team-member-list">
      <div 
        v-for="member in teamMembers" 
        :key="member.id"
        data-testid="team-member"
      >
        {{ member.full_name }}
      </div>
    </div>
  </div>
</template>
```

### Async Testing

```javascript
// ✅ Buono: Gestione corretta async/await
it('should handle async operations', async () => {
  fetch.mockResolvedValueOnce(mockResponse)
  
  await wrapper.vm.loadData()
  await wrapper.vm.$nextTick()
  
  expect(wrapper.text()).toContain('Expected content')
})

// ✅ Buono: Wait for DOM updates
it('should update UI after state change', async () => {
  wrapper.vm.showModal = true
  await nextTick()
  
  expect(wrapper.find('[data-testid="modal"]').exists()).toBe(true)
})
```

### Mock Cleanup

```javascript
// ✅ Buono: Cleanup automatico
beforeEach(() => {
  vi.clearAllMocks()
  fetch.mockClear()
})

afterEach(() => {
  vi.restoreAllMocks()
})
```

---

*Guida aggiornata: 2025-01-26*
