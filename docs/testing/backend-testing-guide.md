# 🔧 Backend Testing Guide

Guida completa per il testing del backend Flask con pytest.

## 📋 Indice

- [Setup e Configurazione](#setup-e-configurazione)
- [Unit Testing](#unit-testing)
- [Integration Testing](#integration-testing)
- [API Testing](#api-testing)
- [Database Testing](#database-testing)
- [Mock e Fixtures](#mock-e-fixtures)
- [Best Practices](#best-practices)

## ⚙️ Setup e Configurazione

### Struttura Directory

```
backend/tests/
├── conftest.py              # Configurazione pytest e fixtures globali
├── unit/
│   ├── test_models.py       # Test modelli database
│   ├── test_utils.py        # Test funzioni utility
│   └── test_business_logic.py # Test logica business
├── integration/
│   ├── test_project_workflows.py # Test workflow completi
│   ├── test_user_management.py   # Test gestione utenti
│   └── test_timesheet_flows.py   # Test flussi timesheet
└── api/
    ├── test_auth_endpoints.py     # Test autenticazione
    ├── test_projects_api.py       # Test API progetti
    ├── test_personnel_api.py      # Test API personale
    └── test_timesheet_api.py      # Test API timesheet
```

### Configurazione pytest

```python
# conftest.py
import pytest
from app import create_app
from extensions import db
from models import User, Project, Client

@pytest.fixture(scope='session')
def app():
    """Crea app Flask per testing"""
    app = create_app(config_name='testing')
    
    with app.app_context():
        db.create_all()
        yield app
        db.drop_all()

@pytest.fixture
def client(app):
    """Client di test per API calls"""
    return app.test_client()

@pytest.fixture
def auth_headers(client):
    """Headers autenticazione per API protette"""
    # Login e ottieni token
    response = client.post('/api/auth/login', json={
        'username': 'admin',
        'password': 'password'
    })
    token = response.json['data']['token']
    
    return {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }

@pytest.fixture
def sample_user():
    """Utente di esempio per test"""
    user = User(
        username='testuser',
        email='<EMAIL>',
        first_name='Test',
        last_name='User',
        role='employee'
    )
    user.set_password('password')
    db.session.add(user)
    db.session.commit()
    return user

@pytest.fixture
def sample_project(sample_user):
    """Progetto di esempio per test"""
    client = Client(
        name='Test Client',
        email='<EMAIL>'
    )
    db.session.add(client)
    db.session.commit()
    
    project = Project(
        name='Test Project',
        description='Project for testing',
        client_id=client.id,
        budget=10000.0,
        status='active'
    )
    db.session.add(project)
    db.session.commit()
    return project
```

## 🧪 Unit Testing

### Test Modelli Database

```python
# tests/unit/test_models.py
import pytest
from datetime import date, timedelta
from models import Project, User, TimesheetEntry

class TestProjectModel:
    """Test per il modello Project"""
    
    def test_project_creation(self):
        """Test creazione progetto base"""
        project = Project(
            name='Test Project',
            description='Test description',
            budget=5000.0,
            start_date=date.today(),
            end_date=date.today() + timedelta(days=30)
        )
        
        assert project.name == 'Test Project'
        assert project.budget == 5000.0
        assert project.status == 'planning'  # Default value
        
    def test_remaining_budget_calculation(self):
        """Test calcolo budget rimanente"""
        project = Project(
            name='Budget Test',
            budget=10000.0,
            expenses=2500.0
        )
        
        assert project.remaining_budget == 7500.0
        
    def test_budget_utilization_percentage(self):
        """Test calcolo percentuale utilizzo budget"""
        project = Project(
            name='Utilization Test',
            budget=10000.0,
            expenses=3000.0
        )
        
        assert project.budget_utilization_percentage == 30.0
        
    def test_project_duration_calculation(self):
        """Test calcolo durata progetto"""
        start_date = date(2025, 1, 1)
        end_date = date(2025, 3, 31)
        
        project = Project(
            name='Duration Test',
            start_date=start_date,
            end_date=end_date
        )
        
        assert project.duration_days == 89  # 31+28+31-1
        
    def test_project_status_transitions(self):
        """Test transizioni stato progetto"""
        project = Project(name='Status Test')
        
        # Stato iniziale
        assert project.status == 'planning'
        
        # Transizione valida
        project.status = 'active'
        assert project.status == 'active'
        
        # Test validazione stati
        valid_statuses = ['planning', 'active', 'completed', 'on-hold']
        for status in valid_statuses:
            project.status = status
            assert project.status == status

class TestUserModel:
    """Test per il modello User"""
    
    def test_user_creation(self):
        """Test creazione utente"""
        user = User(
            username='testuser',
            email='<EMAIL>',
            first_name='Test',
            last_name='User'
        )
        
        assert user.username == 'testuser'
        assert user.full_name == 'Test User'
        assert user.role == 'employee'  # Default
        
    def test_password_hashing(self):
        """Test hashing password"""
        user = User(username='testuser')
        user.set_password('mypassword')
        
        assert user.password_hash is not None
        assert user.password_hash != 'mypassword'
        assert user.check_password('mypassword') is True
        assert user.check_password('wrongpassword') is False
        
    def test_user_permissions(self):
        """Test sistema permessi utente"""
        admin = User(username='admin', role='admin')
        manager = User(username='manager', role='manager')
        employee = User(username='employee', role='employee')
        
        # Admin ha tutti i permessi
        assert admin.has_permission('create_project') is True
        assert admin.has_permission('delete_user') is True
        
        # Manager ha permessi limitati
        assert manager.has_permission('create_project') is True
        assert manager.has_permission('delete_user') is False
        
        # Employee ha permessi base
        assert employee.has_permission('view_projects') is True
        assert employee.has_permission('create_project') is False
```

### Test Business Logic

```python
# tests/unit/test_business_logic.py
import pytest
from datetime import date, timedelta
from services.project_service import ProjectService
from services.timesheet_service import TimesheetService

class TestProjectService:
    """Test per la logica business dei progetti"""
    
    def test_calculate_project_profitability(self, sample_project):
        """Test calcolo profittabilità progetto"""
        # Setup dati
        sample_project.budget = 50000
        sample_project.client_daily_rate = 500
        sample_project.markup_percentage = 20
        
        # Aggiungi costi team
        team_daily_cost = 300  # Costo interno giornaliero
        project_days = 60
        
        profitability = ProjectService.calculate_profitability(
            sample_project, team_daily_cost, project_days
        )
        
        # Revenue: 500 * 60 = 30000
        # Costs: 300 * 60 = 18000  
        # Profit: 30000 - 18000 = 12000
        # Margin: 12000 / 30000 = 40%
        
        assert profitability['revenue'] == 30000
        assert profitability['costs'] == 18000
        assert profitability['profit'] == 12000
        assert profitability['margin_percentage'] == 40.0
        
    def test_project_resource_allocation(self, sample_project, sample_user):
        """Test allocazione risorse progetto"""
        allocation = ProjectService.calculate_resource_allocation(
            sample_project, sample_user, allocation_percentage=75
        )
        
        assert allocation['user_id'] == sample_user.id
        assert allocation['project_id'] == sample_project.id
        assert allocation['allocation_percentage'] == 75
        assert allocation['daily_capacity_hours'] == 6  # 75% of 8 hours

class TestTimesheetService:
    """Test per la logica business timesheet"""
    
    def test_calculate_weekly_hours(self, sample_user, sample_project):
        """Test calcolo ore settimanali"""
        # Crea entries per una settimana
        week_start = date(2025, 1, 6)  # Lunedì
        
        for i in range(5):  # Lun-Ven
            entry = TimesheetEntry(
                user_id=sample_user.id,
                project_id=sample_project.id,
                date=week_start + timedelta(days=i),
                hours=8.0
            )
            db.session.add(entry)
        db.session.commit()
        
        weekly_hours = TimesheetService.get_weekly_hours(
            sample_user.id, week_start
        )
        
        assert weekly_hours['total_hours'] == 40.0
        assert weekly_hours['working_days'] == 5
        assert weekly_hours['average_daily_hours'] == 8.0
        
    def test_overtime_calculation(self, sample_user):
        """Test calcolo straordinari"""
        # 10 ore in un giorno (2 ore di straordinario)
        overtime_day = date.today()
        
        entry = TimesheetEntry(
            user_id=sample_user.id,
            date=overtime_day,
            hours=10.0
        )
        
        overtime = TimesheetService.calculate_overtime(entry)
        
        assert overtime['regular_hours'] == 8.0
        assert overtime['overtime_hours'] == 2.0
        assert overtime['overtime_rate'] == 1.5  # 150%
```

## 🔗 Integration Testing

### Test Workflow Completi

```python
# tests/integration/test_project_workflows.py
import pytest
from datetime import date, timedelta
from models import Project, Task, TimesheetEntry, ProjectResource

class TestProjectLifecycle:
    """Test del ciclo di vita completo di un progetto"""
    
    def test_complete_project_workflow(self, client, auth_headers, sample_user):
        """Test workflow completo: creazione → team → task → timesheet → completamento"""
        
        # 1. CREAZIONE PROGETTO
        project_data = {
            'name': 'Integration Test Project',
            'description': 'Full workflow test',
            'budget': 25000.0,
            'start_date': '2025-01-01',
            'end_date': '2025-06-30',
            'client_id': 1
        }
        
        response = client.post('/api/projects', 
                             json=project_data, 
                             headers=auth_headers)
        assert response.status_code == 201
        
        project_id = response.json['data']['id']
        
        # 2. AGGIUNTA TEAM MEMBERS
        team_member_data = {
            'user_id': sample_user.id,
            'role': 'Developer',
            'allocation_percentage': 100
        }
        
        response = client.post(f'/api/projects/{project_id}/team',
                             json=team_member_data,
                             headers=auth_headers)
        assert response.status_code == 200
        
        # 3. CREAZIONE TASK
        task_data = {
            'title': 'Implement Feature X',
            'description': 'Develop and test feature X',
            'assigned_to': sample_user.id,
            'estimated_hours': 40,
            'priority': 'high',
            'due_date': '2025-02-15'
        }
        
        response = client.post(f'/api/projects/{project_id}/tasks',
                             json=task_data,
                             headers=auth_headers)
        assert response.status_code == 201
        
        task_id = response.json['data']['id']
        
        # 4. REGISTRAZIONE ORE
        timesheet_data = {
            'task_id': task_id,
            'date': '2025-01-15',
            'hours': 8.0,
            'description': 'Working on feature implementation'
        }
        
        response = client.post('/api/timesheet/entries',
                             json=timesheet_data,
                             headers=auth_headers)
        assert response.status_code == 201
        
        # 5. AGGIORNAMENTO STATO TASK
        response = client.put(f'/api/projects/{project_id}/tasks/{task_id}',
                            json={'status': 'completed'},
                            headers=auth_headers)
        assert response.status_code == 200
        
        # 6. VERIFICA STATO FINALE
        response = client.get(f'/api/projects/{project_id}',
                            headers=auth_headers)
        assert response.status_code == 200
        
        project = response.json['data']
        assert len(project['team_members']) == 1
        assert len(project['tasks']) == 1
        assert project['tasks'][0]['status'] == 'completed'
        
    def test_project_funding_workflow(self, client, auth_headers):
        """Test workflow progetto con finanziamento"""
        
        # 1. Crea opportunità finanziamento
        opportunity_data = {
            'title': 'Digital Innovation Grant',
            'source_entity': 'EU Commission',
            'max_grant_amount': 100000,
            'contribution_percentage': 70,
            'application_deadline': '2025-12-31'
        }
        
        response = client.post('/api/funding/opportunities',
                             json=opportunity_data,
                             headers=auth_headers)
        assert response.status_code == 201
        
        opportunity_id = response.json['data']['id']
        
        # 2. Crea application
        application_data = {
            'opportunity_id': opportunity_id,
            'project_title': 'AI Platform Development',
            'requested_amount': 70000,
            'project_description': 'AI-powered business platform'
        }
        
        response = client.post('/api/funding/applications',
                             json=application_data,
                             headers=auth_headers)
        assert response.status_code == 201
        
        application_id = response.json['data']['id']
        
        # 3. Approva application
        response = client.put(f'/api/funding/applications/{application_id}',
                            json={
                                'status': 'approved',
                                'approved_amount': 70000
                            },
                            headers=auth_headers)
        assert response.status_code == 200
        
        # 4. Crea progetto collegato
        project_data = {
            'name': 'AI Platform Project',
            'budget': 100000,
            'funding_source': 'public_funding',
            'funding_application_id': application_id
        }
        
        response = client.post('/api/projects',
                             json=project_data,
                             headers=auth_headers)
        assert response.status_code == 201
        
        project_id = response.json['data']['id']
        
        # 5. Crea link finanziamento
        link_data = {
            'project_id': project_id,
            'funding_application_id': application_id,
            'allocation_percentage': 70
        }
        
        response = client.post('/api/funding/project-links',
                             json=link_data,
                             headers=auth_headers)
        assert response.status_code == 201
        
        # 6. Verifica collegamento
        response = client.get(f'/api/projects/{project_id}',
                            headers=auth_headers)
        project = response.json['data']
        
        assert project['funding_source'] == 'public_funding'
        assert len(project['funding_links']) == 1
        assert project['funding_links'][0]['allocation_percentage'] == 70
```

## 🌐 API Testing

### Test Endpoint REST

```python
# tests/api/test_projects_api.py
import pytest
from models import Project

class TestProjectsAPI:
    """Test per gli endpoint API dei progetti"""
    
    def test_get_projects_list(self, client, auth_headers, sample_project):
        """Test GET /api/projects"""
        response = client.get('/api/projects', headers=auth_headers)
        
        assert response.status_code == 200
        assert response.json['success'] is True
        
        data = response.json['data']
        assert 'projects' in data
        assert 'pagination' in data
        assert len(data['projects']) >= 1
        
    def test_get_project_detail(self, client, auth_headers, sample_project):
        """Test GET /api/projects/<id>"""
        response = client.get(f'/api/projects/{sample_project.id}',
                            headers=auth_headers)
        
        assert response.status_code == 200
        
        project = response.json['data']
        assert project['id'] == sample_project.id
        assert project['name'] == sample_project.name
        assert 'team_members' in project
        assert 'tasks' in project
        
    def test_create_project_validation(self, client, auth_headers):
        """Test validazione creazione progetto"""
        # Test dati mancanti
        response = client.post('/api/projects',
                             json={},
                             headers=auth_headers)
        
        assert response.status_code == 400
        assert 'errors' in response.json
        assert 'name' in response.json['errors']
        
        # Test dati invalidi
        invalid_data = {
            'name': '',  # Nome vuoto
            'budget': -1000,  # Budget negativo
            'start_date': '2025-12-31',
            'end_date': '2025-01-01'  # End date prima di start date
        }
        
        response = client.post('/api/projects',
                             json=invalid_data,
                             headers=auth_headers)
        
        assert response.status_code == 400
        errors = response.json['errors']
        assert 'name' in errors
        assert 'budget' in errors
        assert 'end_date' in errors
        
    def test_update_project(self, client, auth_headers, sample_project):
        """Test PUT /api/projects/<id>"""
        update_data = {
            'name': 'Updated Project Name',
            'budget': 15000.0,
            'status': 'active'
        }
        
        response = client.put(f'/api/projects/{sample_project.id}',
                            json=update_data,
                            headers=auth_headers)
        
        assert response.status_code == 200
        
        updated_project = response.json['data']
        assert updated_project['name'] == 'Updated Project Name'
        assert updated_project['budget'] == 15000.0
        assert updated_project['status'] == 'active'
        
    def test_delete_project(self, client, auth_headers, sample_project):
        """Test DELETE /api/projects/<id>"""
        project_id = sample_project.id
        
        response = client.delete(f'/api/projects/{project_id}',
                               headers=auth_headers)
        
        assert response.status_code == 200
        
        # Verifica che il progetto sia stato eliminato
        response = client.get(f'/api/projects/{project_id}',
                            headers=auth_headers)
        assert response.status_code == 404
        
    def test_project_filtering(self, client, auth_headers):
        """Test filtri API progetti"""
        # Test filtro per status
        response = client.get('/api/projects?status=active',
                            headers=auth_headers)
        assert response.status_code == 200
        
        # Test filtro per client
        response = client.get('/api/projects?client_id=1',
                            headers=auth_headers)
        assert response.status_code == 200
        
        # Test ricerca per nome
        response = client.get('/api/projects?search=test',
                            headers=auth_headers)
        assert response.status_code == 200
        
    def test_project_pagination(self, client, auth_headers):
        """Test paginazione API progetti"""
        response = client.get('/api/projects?page=1&per_page=5',
                            headers=auth_headers)
        
        assert response.status_code == 200
        
        data = response.json['data']
        pagination = data['pagination']
        
        assert pagination['page'] == 1
        assert pagination['per_page'] == 5
        assert 'total' in pagination
        assert 'pages' in pagination
```

## 🗄️ Database Testing

### Test Transazioni e Rollback

```python
# tests/unit/test_database.py
import pytest
from extensions import db
from models import Project, User

class TestDatabaseOperations:
    """Test operazioni database"""
    
    def test_transaction_rollback(self, app):
        """Test rollback transazioni in caso di errore"""
        with app.app_context():
            # Inizia transazione
            project1 = Project(name='Project 1', budget=1000)
            db.session.add(project1)
            
            try:
                # Operazione che causa errore
                project2 = Project(name=None, budget=2000)  # Nome required
                db.session.add(project2)
                db.session.commit()
            except Exception:
                db.session.rollback()
                
            # Verifica che nessun progetto sia stato salvato
            assert Project.query.count() == 0
            
    def test_cascade_delete(self, app, sample_project, sample_user):
        """Test eliminazione a cascata"""
        with app.app_context():
            # Aggiungi task al progetto
            task = Task(
                project_id=sample_project.id,
                title='Test Task',
                assigned_to=sample_user.id
            )
            db.session.add(task)
            db.session.commit()
            
            task_id = task.id
            
            # Elimina progetto
            db.session.delete(sample_project)
            db.session.commit()
            
            # Verifica che anche il task sia stato eliminato
            assert Task.query.get(task_id) is None
            
    def test_unique_constraints(self, app):
        """Test vincoli di unicità"""
        with app.app_context():
            # Crea primo utente
            user1 = User(username='testuser', email='<EMAIL>')
            db.session.add(user1)
            db.session.commit()
            
            # Tenta di creare secondo utente con stesso username
            user2 = User(username='testuser', email='<EMAIL>')
            db.session.add(user2)
            
            with pytest.raises(Exception):  # Violazione constraint
                db.session.commit()
```

## 🎭 Mock e Fixtures

### Factory Pattern per Test Data

```python
# tests/factories.py
import factory
from datetime import date, timedelta
from models import User, Project, Client, Task

class UserFactory(factory.Factory):
    class Meta:
        model = User
        
    username = factory.Sequence(lambda n: f'user{n}')
    email = factory.LazyAttribute(lambda obj: f'{obj.username}@example.com')
    first_name = factory.Faker('first_name')
    last_name = factory.Faker('last_name')
    role = 'employee'

class ClientFactory(factory.Factory):
    class Meta:
        model = Client
        
    name = factory.Faker('company')
    email = factory.LazyAttribute(lambda obj: f'contact@{obj.name.lower().replace(" ", "")}.com')
    industry = factory.Faker('word')

class ProjectFactory(factory.Factory):
    class Meta:
        model = Project
        
    name = factory.Faker('catch_phrase')
    description = factory.Faker('text', max_nb_chars=200)
    budget = factory.Faker('pydecimal', left_digits=5, right_digits=2, positive=True)
    start_date = factory.LazyFunction(lambda: date.today())
    end_date = factory.LazyFunction(lambda: date.today() + timedelta(days=90))
    status = 'planning'
    client = factory.SubFactory(ClientFactory)

# Uso nelle test
def test_with_factory_data():
    project = ProjectFactory()
    assert project.name is not None
    assert project.budget > 0
    assert project.client.name is not None
```

## 📚 Best Practices

### Naming Conventions

```python
# ✅ Buono: Nomi descrittivi
def test_project_budget_calculation_with_expenses():
    """Test calcolo budget rimanente considerando le spese"""
    pass

def test_user_authentication_with_invalid_credentials():
    """Test autenticazione con credenziali non valide"""
    pass

# ❌ Cattivo: Nomi generici
def test_project():
    pass

def test_user_login():
    pass
```

### Test Organization

```python
# ✅ Buono: Organizzazione per classe
class TestProjectModel:
    """Test per il modello Project"""
    
    def test_creation(self):
        pass
        
    def test_validation(self):
        pass
        
    def test_relationships(self):
        pass

class TestProjectAPI:
    """Test per l'API dei progetti"""
    
    def test_get_list(self):
        pass
        
    def test_create(self):
        pass
        
    def test_update(self):
        pass
```

### Assertion Patterns

```python
# ✅ Buono: Assertions specifiche
assert response.status_code == 201
assert response.json['success'] is True
assert 'id' in response.json['data']
assert len(projects) == 3

# ❌ Cattivo: Assertions generiche
assert response
assert data
```

### Cleanup e Isolation

```python
# ✅ Buono: Cleanup automatico
@pytest.fixture(autouse=True)
def cleanup_database():
    """Pulisce database dopo ogni test"""
    yield
    db.session.rollback()
    for table in reversed(db.metadata.sorted_tables):
        db.session.execute(table.delete())
    db.session.commit()
```

---

*Guida aggiornata: 2025-01-26*
